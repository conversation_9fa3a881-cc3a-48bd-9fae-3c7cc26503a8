<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.internetoptions API documentation</title>
<meta name="description" content="File for setting internet options
InternetOptions: class for setting Internet options in CommServe …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.internetoptions</code></h1>
</header>
<section id="section-intro">
<p>File for setting internet options
InternetOptions: class for setting Internet options in CommServe</p>
<p><strong>init</strong>(Commcell_object)
&ndash;
initialise with object of CommCell</p>
<p><strong>repr</strong>()
&ndash;
returns the string to represent the instance of the
InternetOptions class
set_internet_gateway_client(clientname)
&ndash;
sets Internet gateway with provided client</p>
<p>set_metrics_internet_gateway()
&ndash; sets metrics server as internet gateway</p>
<p>set_no_gateway()
&ndash; Removes internet gateway</p>
<p>set_http_proxy(servername, port)&ndash; sets HTTP proxy enabled with the provided server name and port</p>
<p>disable_http_proxy()
&ndash; Removes HTTP proxy</p>
<p>set_http_authentication(username, pwd)
&ndash; sets authentication to HTTP proxy</p>
<p>disable_http_authentication()
&ndash;Removes HTTP authentication</p>
<p>refresh()
&ndash;
refresh the internet options
set_gateway_for_sendlogs
&mdash; sets internet gateway for sendlog files</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L1-L196" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for setting internet options
InternetOptions: class for setting Internet options in CommServe

 __init__(Commcell_object)      --  initialise with object of CommCell

__repr__()                      --  returns the string to represent the instance of the
                                            InternetOptions class
set_internet_gateway_client(clientname)
                                --  sets Internet gateway with provided client

set_metrics_internet_gateway()  -- sets metrics server as internet gateway

set_no_gateway()                -- Removes internet gateway

set_http_proxy(servername, port)-- sets HTTP proxy enabled with the provided server name and port

disable_http_proxy()            -- Removes HTTP proxy

set_http_authentication(username, pwd)
                                -- sets authentication to HTTP proxy

disable_http_authentication()   --Removes HTTP authentication

refresh()                       --  refresh the internet options
set_gateway_for_sendlogs    --- sets internet gateway for sendlog files

&#34;&#34;&#34;
from __future__ import absolute_import
from __future__ import unicode_literals

from base64 import b64encode

from .exception import SDKException


class InternetOptions(object):
    &#34;&#34;&#34;Class for setting Internet options in CommServe&#34;&#34;&#34;

    def __init__(self, commcell_object):
        self._commcell_object = commcell_object
        self._INTERNET = self._commcell_object._services[&#39;INTERNET_PROXY&#39;]
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the UserGroups class.&#34;&#34;&#34;
        return &#34;InternetOption class instance for Commcell with config &#39;{0}&#39;&#34;.format(
            self._internet_config
        )

    def _get_internet_config(self):
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._INTERNET
        )
        if flag:
            self._internet_config = response.json()
            if self._internet_config and &#39;config&#39; in self._internet_config:
                self._config = self._internet_config[&#39;config&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _save_config(self):
        &#34;&#34;&#34;
        updates the configuration of private Metrics
        this must be called to save the configuration changes made in this object
        Raises:
            SDKException:
                if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._INTERNET, self._internet_config
        )
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def set_internet_gateway_client(self,
                                    client_name=None,
                                    cloud_metrics=True,
                                    private_metrics=True
                                    ):
        &#34;&#34;&#34;
        set internet gateway with the client name provided.
        Args:
            client_name     (str): client to be used as internet gateway
            cloud_metrics  (bool): True to enable gateway for cloud metrics
            private_metrics(bool): True to enable gateway for Private metrics
        Raises:
            SDKException:
                if client doesnt exist in CommServe
        &#34;&#34;&#34;
        if client_name is None:
            raise SDKException(&#39;InternetOptions&#39;, &#39;101&#39;, &#39;Client name is required&#39;)
        clientid = int(self._commcell_object.clients.get(client_name).client_id)
        self._config[&#39;proxyType&#39;] = 2
        self._config[&#39;proxyClient&#39;][&#39;clientId&#39;] = clientid
        self._config[&#39;proxyClient&#39;][&#39;clientName&#39;] = client_name
        if cloud_metrics:
            self._config[&#39;useInternetGatewayPublic&#39;] = True
        else:
            self._config[&#39;useInternetGatewayPublic&#39;] = False
        if private_metrics:
            self._config[&#39;useInternetGatewayPrivate&#39;] = True
        else:
            self._config[&#39;useInternetGatewayPrivate&#39;] = False
        self._save_config()

    def set_gateway_for_sendlogs(self,
                                 client_name
                                 ):
        &#34;&#34;&#34;
        set internet gateway with the client name provided for sendlogs.
        Args:
            client_name     (str): client to be used as internet gateway
        Raises:
            SDKException:
                if client doesnt exist in CommServe
        &#34;&#34;&#34;
        clientid = int(self._commcell_object.clients.get(client_name).client_id)
        self._config[&#39;proxyType&#39;] = 2
        self._config[&#39;proxyClient&#39;][&#39;clientId&#39;] = clientid
        self._config[&#39;proxyClient&#39;][&#39;clientName&#39;] = client_name
        self._config[&#39;useInternetGatewaySendLogFile&#39;] = True
        self._save_config()

    def set_metrics_internet_gateway(self):
        &#34;&#34;&#34;sets metrics server as internet gateway&#34;&#34;&#34;
        self._config[&#39;proxyType&#39;] = 3
        self._save_config()

    def set_no_gateway(self):
        &#34;&#34;&#34;Removes internet gateway&#34;&#34;&#34;
        self._config[&#39;proxyType&#39;] = 1
        self._save_config()

    def set_http_proxy(self, servername=None, port=None):
        &#34;&#34;&#34;
        sets HTTP proxy enabled with the provided server name and port
        Args:
            servername (str): hostname or IP of the HTTP proxy server
            port (int): HTTP proxy server port
        Raises:
            SDKException:
                if proxy server name and port are empty
        &#34;&#34;&#34;
        if servername is None or port is None:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#39;proxy server name and port cannot be empty&#39;)
        self._config[&#39;useHttpProxy&#39;] = True
        self._config[&#39;proxyServer&#39;] = servername
        self._config[&#39;proxyPort&#39;] = int(port)
        self._save_config()

    def disable_http_proxy(self):
        &#34;&#34;&#34;Removes HTTP proxy&#34;&#34;&#34;
        self._config[&#39;useHttpProxy&#39;] = False
        self._save_config()

    def set_http_authentication(self, username=&#39;&#39;, pwd=&#39;&#39;):
        &#34;&#34;&#34;
        sets authentication to HTTP proxy
        Args:
            username: username for proxy server
            pwd: password for proxy server
        &#34;&#34;&#34;
        self._config[&#39;useProxyAuthentication&#39;] = True
        self._config[&#39;proxyCredentials&#39;][&#39;userName&#39;] = username
        self._config[&#39;proxyCredentials&#39;][&#39;password&#39;] = b64encode(pwd.encode()).decode()
        self._config[&#39;proxyCredentials&#39;][&#39;confirmPassword&#39;] = b64encode(pwd.encode()).decode()
        self._save_config()

    def disable_http_authentication(self):
        &#34;&#34;&#34;Removes HTTP authentication&#34;&#34;&#34;
        self._config[&#39;useProxyAuthentication&#39;] = False
        self._save_config()

    def refresh(self):
        &#34;&#34;&#34;Refresh the Internet Options.&#34;&#34;&#34;
        self._get_internet_config()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.internetoptions.InternetOptions"><code class="flex name class">
<span>class <span class="ident">InternetOptions</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for setting Internet options in CommServe</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L54-L196" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class InternetOptions(object):
    &#34;&#34;&#34;Class for setting Internet options in CommServe&#34;&#34;&#34;

    def __init__(self, commcell_object):
        self._commcell_object = commcell_object
        self._INTERNET = self._commcell_object._services[&#39;INTERNET_PROXY&#39;]
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the UserGroups class.&#34;&#34;&#34;
        return &#34;InternetOption class instance for Commcell with config &#39;{0}&#39;&#34;.format(
            self._internet_config
        )

    def _get_internet_config(self):
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._INTERNET
        )
        if flag:
            self._internet_config = response.json()
            if self._internet_config and &#39;config&#39; in self._internet_config:
                self._config = self._internet_config[&#39;config&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _save_config(self):
        &#34;&#34;&#34;
        updates the configuration of private Metrics
        this must be called to save the configuration changes made in this object
        Raises:
            SDKException:
                if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._INTERNET, self._internet_config
        )
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def set_internet_gateway_client(self,
                                    client_name=None,
                                    cloud_metrics=True,
                                    private_metrics=True
                                    ):
        &#34;&#34;&#34;
        set internet gateway with the client name provided.
        Args:
            client_name     (str): client to be used as internet gateway
            cloud_metrics  (bool): True to enable gateway for cloud metrics
            private_metrics(bool): True to enable gateway for Private metrics
        Raises:
            SDKException:
                if client doesnt exist in CommServe
        &#34;&#34;&#34;
        if client_name is None:
            raise SDKException(&#39;InternetOptions&#39;, &#39;101&#39;, &#39;Client name is required&#39;)
        clientid = int(self._commcell_object.clients.get(client_name).client_id)
        self._config[&#39;proxyType&#39;] = 2
        self._config[&#39;proxyClient&#39;][&#39;clientId&#39;] = clientid
        self._config[&#39;proxyClient&#39;][&#39;clientName&#39;] = client_name
        if cloud_metrics:
            self._config[&#39;useInternetGatewayPublic&#39;] = True
        else:
            self._config[&#39;useInternetGatewayPublic&#39;] = False
        if private_metrics:
            self._config[&#39;useInternetGatewayPrivate&#39;] = True
        else:
            self._config[&#39;useInternetGatewayPrivate&#39;] = False
        self._save_config()

    def set_gateway_for_sendlogs(self,
                                 client_name
                                 ):
        &#34;&#34;&#34;
        set internet gateway with the client name provided for sendlogs.
        Args:
            client_name     (str): client to be used as internet gateway
        Raises:
            SDKException:
                if client doesnt exist in CommServe
        &#34;&#34;&#34;
        clientid = int(self._commcell_object.clients.get(client_name).client_id)
        self._config[&#39;proxyType&#39;] = 2
        self._config[&#39;proxyClient&#39;][&#39;clientId&#39;] = clientid
        self._config[&#39;proxyClient&#39;][&#39;clientName&#39;] = client_name
        self._config[&#39;useInternetGatewaySendLogFile&#39;] = True
        self._save_config()

    def set_metrics_internet_gateway(self):
        &#34;&#34;&#34;sets metrics server as internet gateway&#34;&#34;&#34;
        self._config[&#39;proxyType&#39;] = 3
        self._save_config()

    def set_no_gateway(self):
        &#34;&#34;&#34;Removes internet gateway&#34;&#34;&#34;
        self._config[&#39;proxyType&#39;] = 1
        self._save_config()

    def set_http_proxy(self, servername=None, port=None):
        &#34;&#34;&#34;
        sets HTTP proxy enabled with the provided server name and port
        Args:
            servername (str): hostname or IP of the HTTP proxy server
            port (int): HTTP proxy server port
        Raises:
            SDKException:
                if proxy server name and port are empty
        &#34;&#34;&#34;
        if servername is None or port is None:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#39;proxy server name and port cannot be empty&#39;)
        self._config[&#39;useHttpProxy&#39;] = True
        self._config[&#39;proxyServer&#39;] = servername
        self._config[&#39;proxyPort&#39;] = int(port)
        self._save_config()

    def disable_http_proxy(self):
        &#34;&#34;&#34;Removes HTTP proxy&#34;&#34;&#34;
        self._config[&#39;useHttpProxy&#39;] = False
        self._save_config()

    def set_http_authentication(self, username=&#39;&#39;, pwd=&#39;&#39;):
        &#34;&#34;&#34;
        sets authentication to HTTP proxy
        Args:
            username: username for proxy server
            pwd: password for proxy server
        &#34;&#34;&#34;
        self._config[&#39;useProxyAuthentication&#39;] = True
        self._config[&#39;proxyCredentials&#39;][&#39;userName&#39;] = username
        self._config[&#39;proxyCredentials&#39;][&#39;password&#39;] = b64encode(pwd.encode()).decode()
        self._config[&#39;proxyCredentials&#39;][&#39;confirmPassword&#39;] = b64encode(pwd.encode()).decode()
        self._save_config()

    def disable_http_authentication(self):
        &#34;&#34;&#34;Removes HTTP authentication&#34;&#34;&#34;
        self._config[&#39;useProxyAuthentication&#39;] = False
        self._save_config()

    def refresh(self):
        &#34;&#34;&#34;Refresh the Internet Options.&#34;&#34;&#34;
        self._get_internet_config()</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.internetoptions.InternetOptions.disable_http_authentication"><code class="name flex">
<span>def <span class="ident">disable_http_authentication</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Removes HTTP authentication</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L189-L192" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_http_authentication(self):
    &#34;&#34;&#34;Removes HTTP authentication&#34;&#34;&#34;
    self._config[&#39;useProxyAuthentication&#39;] = False
    self._save_config()</code></pre>
</details>
</dd>
<dt id="cvpysdk.internetoptions.InternetOptions.disable_http_proxy"><code class="name flex">
<span>def <span class="ident">disable_http_proxy</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Removes HTTP proxy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L171-L174" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_http_proxy(self):
    &#34;&#34;&#34;Removes HTTP proxy&#34;&#34;&#34;
    self._config[&#39;useHttpProxy&#39;] = False
    self._save_config()</code></pre>
</details>
</dd>
<dt id="cvpysdk.internetoptions.InternetOptions.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the Internet Options.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L194-L196" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the Internet Options.&#34;&#34;&#34;
    self._get_internet_config()</code></pre>
</details>
</dd>
<dt id="cvpysdk.internetoptions.InternetOptions.set_gateway_for_sendlogs"><code class="name flex">
<span>def <span class="ident">set_gateway_for_sendlogs</span></span>(<span>self, client_name)</span>
</code></dt>
<dd>
<div class="desc"><p>set internet gateway with the client name provided for sendlogs.</p>
<h2 id="args">Args</h2>
<p>client_name
(str): client to be used as internet gateway</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client doesnt exist in CommServe</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L126-L142" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_gateway_for_sendlogs(self,
                             client_name
                             ):
    &#34;&#34;&#34;
    set internet gateway with the client name provided for sendlogs.
    Args:
        client_name     (str): client to be used as internet gateway
    Raises:
        SDKException:
            if client doesnt exist in CommServe
    &#34;&#34;&#34;
    clientid = int(self._commcell_object.clients.get(client_name).client_id)
    self._config[&#39;proxyType&#39;] = 2
    self._config[&#39;proxyClient&#39;][&#39;clientId&#39;] = clientid
    self._config[&#39;proxyClient&#39;][&#39;clientName&#39;] = client_name
    self._config[&#39;useInternetGatewaySendLogFile&#39;] = True
    self._save_config()</code></pre>
</details>
</dd>
<dt id="cvpysdk.internetoptions.InternetOptions.set_http_authentication"><code class="name flex">
<span>def <span class="ident">set_http_authentication</span></span>(<span>self, username='', pwd='')</span>
</code></dt>
<dd>
<div class="desc"><p>sets authentication to HTTP proxy</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>username</code></strong></dt>
<dd>username for proxy server</dd>
<dt><strong><code>pwd</code></strong></dt>
<dd>password for proxy server</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L176-L187" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_http_authentication(self, username=&#39;&#39;, pwd=&#39;&#39;):
    &#34;&#34;&#34;
    sets authentication to HTTP proxy
    Args:
        username: username for proxy server
        pwd: password for proxy server
    &#34;&#34;&#34;
    self._config[&#39;useProxyAuthentication&#39;] = True
    self._config[&#39;proxyCredentials&#39;][&#39;userName&#39;] = username
    self._config[&#39;proxyCredentials&#39;][&#39;password&#39;] = b64encode(pwd.encode()).decode()
    self._config[&#39;proxyCredentials&#39;][&#39;confirmPassword&#39;] = b64encode(pwd.encode()).decode()
    self._save_config()</code></pre>
</details>
</dd>
<dt id="cvpysdk.internetoptions.InternetOptions.set_http_proxy"><code class="name flex">
<span>def <span class="ident">set_http_proxy</span></span>(<span>self, servername=None, port=None)</span>
</code></dt>
<dd>
<div class="desc"><p>sets HTTP proxy enabled with the provided server name and port</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>servername</code></strong> :&ensp;<code>str</code></dt>
<dd>hostname or IP of the HTTP proxy server</dd>
<dt><strong><code>port</code></strong> :&ensp;<code>int</code></dt>
<dd>HTTP proxy server port</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proxy server name and port are empty</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L154-L169" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_http_proxy(self, servername=None, port=None):
    &#34;&#34;&#34;
    sets HTTP proxy enabled with the provided server name and port
    Args:
        servername (str): hostname or IP of the HTTP proxy server
        port (int): HTTP proxy server port
    Raises:
        SDKException:
            if proxy server name and port are empty
    &#34;&#34;&#34;
    if servername is None or port is None:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#39;proxy server name and port cannot be empty&#39;)
    self._config[&#39;useHttpProxy&#39;] = True
    self._config[&#39;proxyServer&#39;] = servername
    self._config[&#39;proxyPort&#39;] = int(port)
    self._save_config()</code></pre>
</details>
</dd>
<dt id="cvpysdk.internetoptions.InternetOptions.set_internet_gateway_client"><code class="name flex">
<span>def <span class="ident">set_internet_gateway_client</span></span>(<span>self, client_name=None, cloud_metrics=True, private_metrics=True)</span>
</code></dt>
<dd>
<div class="desc"><p>set internet gateway with the client name provided.</p>
<h2 id="args">Args</h2>
<p>client_name
(str): client to be used as internet gateway
cloud_metrics
(bool): True to enable gateway for cloud metrics
private_metrics(bool): True to enable gateway for Private metrics</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client doesnt exist in CommServe</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L95-L124" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_internet_gateway_client(self,
                                client_name=None,
                                cloud_metrics=True,
                                private_metrics=True
                                ):
    &#34;&#34;&#34;
    set internet gateway with the client name provided.
    Args:
        client_name     (str): client to be used as internet gateway
        cloud_metrics  (bool): True to enable gateway for cloud metrics
        private_metrics(bool): True to enable gateway for Private metrics
    Raises:
        SDKException:
            if client doesnt exist in CommServe
    &#34;&#34;&#34;
    if client_name is None:
        raise SDKException(&#39;InternetOptions&#39;, &#39;101&#39;, &#39;Client name is required&#39;)
    clientid = int(self._commcell_object.clients.get(client_name).client_id)
    self._config[&#39;proxyType&#39;] = 2
    self._config[&#39;proxyClient&#39;][&#39;clientId&#39;] = clientid
    self._config[&#39;proxyClient&#39;][&#39;clientName&#39;] = client_name
    if cloud_metrics:
        self._config[&#39;useInternetGatewayPublic&#39;] = True
    else:
        self._config[&#39;useInternetGatewayPublic&#39;] = False
    if private_metrics:
        self._config[&#39;useInternetGatewayPrivate&#39;] = True
    else:
        self._config[&#39;useInternetGatewayPrivate&#39;] = False
    self._save_config()</code></pre>
</details>
</dd>
<dt id="cvpysdk.internetoptions.InternetOptions.set_metrics_internet_gateway"><code class="name flex">
<span>def <span class="ident">set_metrics_internet_gateway</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>sets metrics server as internet gateway</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L144-L147" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_metrics_internet_gateway(self):
    &#34;&#34;&#34;sets metrics server as internet gateway&#34;&#34;&#34;
    self._config[&#39;proxyType&#39;] = 3
    self._save_config()</code></pre>
</details>
</dd>
<dt id="cvpysdk.internetoptions.InternetOptions.set_no_gateway"><code class="name flex">
<span>def <span class="ident">set_no_gateway</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Removes internet gateway</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/internetoptions.py#L149-L152" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_no_gateway(self):
    &#34;&#34;&#34;Removes internet gateway&#34;&#34;&#34;
    self._config[&#39;proxyType&#39;] = 1
    self._save_config()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.internetoptions.InternetOptions" href="#cvpysdk.internetoptions.InternetOptions">InternetOptions</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.internetoptions.InternetOptions.disable_http_authentication" href="#cvpysdk.internetoptions.InternetOptions.disable_http_authentication">disable_http_authentication</a></code></li>
<li><code><a title="cvpysdk.internetoptions.InternetOptions.disable_http_proxy" href="#cvpysdk.internetoptions.InternetOptions.disable_http_proxy">disable_http_proxy</a></code></li>
<li><code><a title="cvpysdk.internetoptions.InternetOptions.refresh" href="#cvpysdk.internetoptions.InternetOptions.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.internetoptions.InternetOptions.set_gateway_for_sendlogs" href="#cvpysdk.internetoptions.InternetOptions.set_gateway_for_sendlogs">set_gateway_for_sendlogs</a></code></li>
<li><code><a title="cvpysdk.internetoptions.InternetOptions.set_http_authentication" href="#cvpysdk.internetoptions.InternetOptions.set_http_authentication">set_http_authentication</a></code></li>
<li><code><a title="cvpysdk.internetoptions.InternetOptions.set_http_proxy" href="#cvpysdk.internetoptions.InternetOptions.set_http_proxy">set_http_proxy</a></code></li>
<li><code><a title="cvpysdk.internetoptions.InternetOptions.set_internet_gateway_client" href="#cvpysdk.internetoptions.InternetOptions.set_internet_gateway_client">set_internet_gateway_client</a></code></li>
<li><code><a title="cvpysdk.internetoptions.InternetOptions.set_metrics_internet_gateway" href="#cvpysdk.internetoptions.InternetOptions.set_metrics_internet_gateway">set_metrics_internet_gateway</a></code></li>
<li><code><a title="cvpysdk.internetoptions.InternetOptions.set_no_gateway" href="#cvpysdk.internetoptions.InternetOptions.set_no_gateway">set_no_gateway</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>