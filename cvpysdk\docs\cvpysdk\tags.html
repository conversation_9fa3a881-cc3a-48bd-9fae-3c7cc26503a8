<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.tags API documentation</title>
<meta name="description" content="File for performing tags related operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.tags</code></h1>
</header>
<section id="section-intro">
<p>File for performing tags related operations.</p>
<p>Tags: Class for representing all the tags created by the user which is logged in.</p>
<h2 id="tags">Tags</h2>
<p>Methods:</p>
<pre><code>has_tag()   --  checks whether the tag with given name exists or not

add()       --  creates a new tag to commcell

get()       --  returns a tag object for given tag name

delete()    --  deletes an entity tag from commcell

refresh()   --  refreshes the tags list
</code></pre>
<p>Properties:</p>
<pre><code>**all_tags  --  returns the dict containing all the tags and id
</code></pre>
<h2 id="tag">Tag</h2>
<p>Properties:</p>
<pre><code>**tag_id    --  returns tag id

**tag_name  --  returns tag name
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L1-L286" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for performing tags related operations.


Tags: Class for representing all the tags created by the user which is logged in.


Tags:
    Methods:

        has_tag()   --  checks whether the tag with given name exists or not

        add()       --  creates a new tag to commcell

        get()       --  returns a tag object for given tag name

        delete()    --  deletes an entity tag from commcell

        refresh()   --  refreshes the tags list

    Properties:

        **all_tags  --  returns the dict containing all the tags and id

Tag:
    Properties:

        **tag_id    --  returns tag id

        **tag_name  --  returns tag name

&#34;&#34;&#34;
from cvpysdk.exception import SDKException


class Tags:
    &#34;&#34;&#34;Class for doing operations related to entity tags from backend&#34;&#34;&#34;

    DEFAULT_TAGSET_ID = -1

    def __init__(self, commcell_object: object) -&gt; None:
        &#34;&#34;&#34;Method to initialize tags class

            Args:
                commcell_object (Commcell)  -- instance of commcell class
            Returns:
                Tags (object)               -- instance of tags class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._service = commcell_object._services
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._get_tags_url = self._service[&#39;GET_ENTITY_TAGS&#39;]
        self._create_tags_url = self._service[&#39;CREATE_ENTITY_TAGS&#39;]
        self._tags = None

        self.refresh()

    def _get_tags(self) -&gt; dict:
        &#34;&#34;&#34;Gets all the tags created by a user

            Returns:
                tags: name-id pair of tags associated to a user
                    Example:
                        {
                            &#34;tag1&#34;: 1,
                            &#34;tag2&#34;: 3,
                            &#34;tag3&#34;: 4
                        }
            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._get_tags_url)

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        resp = response.json()
        if not resp:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        self.DEFAULT_TAGSET_ID = resp[&#39;tagSetInfo&#39;][&#39;id&#39;]

        tag_dict = {}
        if &#39;tags&#39; in resp:
            for tag in resp.get(&#34;tags&#34;, []):
                tag_dict[tag.get(&#34;name&#34;).lower()] = tag.get(&#34;id&#34;)
            return tag_dict

    @property
    def all_tags(self) -&gt; dict:
        &#34;&#34;&#34;Returns a dictionary containing tags and their ID associated to a user

            Returns:
                tags: name-id pair of tags associated to a user
                    Example:
                        {
                            &#34;tag1&#34;: 1,
                            &#34;tag2&#34;: 3,
                            &#34;tag3&#34;: 4
                        }
        &#34;&#34;&#34;

        return self._tags

    def has_tag(self, tag_name: str) -&gt; bool:
        &#34;&#34;&#34;Checks if a tag exist for the logged-in user

            Args:
                tag_name (str): name of the tag to search

            Returns:
                bool: return True if tag is found, else returns false

            Raises:
                SDKException:
                    if type of the tag name argument is not string

        &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;EntityTags&#39;, &#39;101&#39;)

        return self._tags and tag_name.lower() in self._tags

    def get(self, name: str):
        &#34;&#34;&#34;Returns an instance of the Tag class for the given tag name.

            Args:
                name    (str)   --  name of the tag to get the instance of

            Returns:
                object  -   instance of the Tag class for the given tag name

            Raises:
                SDKException:
                    if type of the tag name argument is not string

                    if no tag exists with the given name

        &#34;&#34;&#34;
        if self.has_tag(name):
            name = name.lower()
            return Tag(self._commcell_object, name, self._tags[name])

        raise SDKException(&#39;EntityTags&#39;, &#39;105&#39;)

    def refresh(self) -&gt; None:
        &#34;&#34;&#34;Refresh the list of tags&#34;&#34;&#34;
        self._tags = self._get_tags()

    def add(self, tag_name: str):
        &#34;&#34;&#34;Method to add an entity tag

            Args:
                tag_name (str): entity tag name

            Returns:
                object  -   instance of the Tag class, for the newly created entity tag
        &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;EntityTags&#39;, &#39;101&#39;)

        payload = {
            &#39;container&#39;: {
                &#39;containerId&#39;: self.DEFAULT_TAGSET_ID
                },
            &#39;tags&#39;: [
                {
                    &#39;name&#39;: f&#34;{tag_name}&#34;
                }
            ]
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._service[&#39;CREATE_ENTITY_TAGS&#39;], payload)

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        response_json = response.json()
        if &#39;errList&#39; in response_json:
            err_message = response_json.get(&#39;errLogMessage&#39;)
            err_code = response_json.get(&#39;errorCode&#39;)

            if err_code or err_message:
                raise SDKException(&#39;EntityTags&#39;, &#39;103&#39;, err_message)

        self.refresh()
        return self.get(tag_name)

    def delete(self, tag_name: str) -&gt; None:
        &#34;&#34;&#34;Deletes the entity tag

        Args:
            tag_name (str): entity tag name to delete
        &#34;&#34;&#34;
        if not self.has_tag(tag_name):
            raise SDKException(&#34;EntityTags&#34;, &#39;105&#39;)

        tag_name = tag_name.lower()
        tag_id = self._tags[tag_name]

        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._service[&#39;DELETE_ENTITY_TAGS&#39;] % tag_id)

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        resp = response.json()
        err_code = 0
        err_message = &#34;&#34;
        if resp:
            err_code = resp.get(&#39;errorCode&#39;, 0)
            err_message = resp.get(&#39;errorMessage&#39;, &#34;&#34;)

        if err_code or err_message:
            raise SDKException(&#34;EntityTags&#34;, &#39;102&#39;, f&#34;Error: {err_message}&#34;)

        self.refresh()


class Tag:
    &#34;&#34;&#34;Class for performing actions related to tag &#34;&#34;&#34;

    def __init__(self, commcell_object, tag_name, tag_id=None):
        &#34;&#34;&#34;Initialise the Tag class instance.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

                tag_name            (str)       --  name of the entity tag

                tag_id              (str)       --  id of the entity tag
                    default: None

            Returns:
                object  -   instance of the tag class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._tag_name = tag_name

        if tag_id:
            self._tag_id = str(tag_id)
        else:
            self._tag_id = self._get_tag_id()

    def _get_tag_id(self) -&gt; str:
        &#34;&#34;&#34;Gets tag id based on the tag name&#34;&#34;&#34;
        tags = Tags(self._commcell_object)
        return tags.get(self._tag_name).tag_id

    @property
    def tag_id(self):
        &#34;&#34;&#34;Returns tag id&#34;&#34;&#34;
        return self._tag_id

    @property
    def tag_name(self):
        &#34;&#34;&#34;Returns tag name&#34;&#34;&#34;
        return self._tag_name</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.tags.Tag"><code class="flex name class">
<span>class <span class="ident">Tag</span></span>
<span>(</span><span>commcell_object, tag_name, tag_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing actions related to tag </p>
<p>Initialise the Tag class instance.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>tag_name
(str)
&ndash;
name of the entity tag</p>
<p>tag_id
(str)
&ndash;
id of the entity tag
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the tag class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L242-L286" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Tag:
    &#34;&#34;&#34;Class for performing actions related to tag &#34;&#34;&#34;

    def __init__(self, commcell_object, tag_name, tag_id=None):
        &#34;&#34;&#34;Initialise the Tag class instance.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

                tag_name            (str)       --  name of the entity tag

                tag_id              (str)       --  id of the entity tag
                    default: None

            Returns:
                object  -   instance of the tag class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._tag_name = tag_name

        if tag_id:
            self._tag_id = str(tag_id)
        else:
            self._tag_id = self._get_tag_id()

    def _get_tag_id(self) -&gt; str:
        &#34;&#34;&#34;Gets tag id based on the tag name&#34;&#34;&#34;
        tags = Tags(self._commcell_object)
        return tags.get(self._tag_name).tag_id

    @property
    def tag_id(self):
        &#34;&#34;&#34;Returns tag id&#34;&#34;&#34;
        return self._tag_id

    @property
    def tag_name(self):
        &#34;&#34;&#34;Returns tag name&#34;&#34;&#34;
        return self._tag_name</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.tags.Tag.tag_id"><code class="name">var <span class="ident">tag_id</span></code></dt>
<dd>
<div class="desc"><p>Returns tag id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L278-L281" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tag_id(self):
    &#34;&#34;&#34;Returns tag id&#34;&#34;&#34;
    return self._tag_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.tags.Tag.tag_name"><code class="name">var <span class="ident">tag_name</span></code></dt>
<dd>
<div class="desc"><p>Returns tag name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L283-L286" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tag_name(self):
    &#34;&#34;&#34;Returns tag name&#34;&#34;&#34;
    return self._tag_name</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.tags.Tags"><code class="flex name class">
<span>class <span class="ident">Tags</span></span>
<span>(</span><span>commcell_object: object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for doing operations related to entity tags from backend</p>
<p>Method to initialize tags class</p>
<h2 id="args">Args</h2>
<p>commcell_object (Commcell)
&ndash; instance of commcell class</p>
<h2 id="returns">Returns</h2>
<p>Tags (object)
&ndash; instance of tags class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L53-L239" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Tags:
    &#34;&#34;&#34;Class for doing operations related to entity tags from backend&#34;&#34;&#34;

    DEFAULT_TAGSET_ID = -1

    def __init__(self, commcell_object: object) -&gt; None:
        &#34;&#34;&#34;Method to initialize tags class

            Args:
                commcell_object (Commcell)  -- instance of commcell class
            Returns:
                Tags (object)               -- instance of tags class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._service = commcell_object._services
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._get_tags_url = self._service[&#39;GET_ENTITY_TAGS&#39;]
        self._create_tags_url = self._service[&#39;CREATE_ENTITY_TAGS&#39;]
        self._tags = None

        self.refresh()

    def _get_tags(self) -&gt; dict:
        &#34;&#34;&#34;Gets all the tags created by a user

            Returns:
                tags: name-id pair of tags associated to a user
                    Example:
                        {
                            &#34;tag1&#34;: 1,
                            &#34;tag2&#34;: 3,
                            &#34;tag3&#34;: 4
                        }
            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._get_tags_url)

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        resp = response.json()
        if not resp:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        self.DEFAULT_TAGSET_ID = resp[&#39;tagSetInfo&#39;][&#39;id&#39;]

        tag_dict = {}
        if &#39;tags&#39; in resp:
            for tag in resp.get(&#34;tags&#34;, []):
                tag_dict[tag.get(&#34;name&#34;).lower()] = tag.get(&#34;id&#34;)
            return tag_dict

    @property
    def all_tags(self) -&gt; dict:
        &#34;&#34;&#34;Returns a dictionary containing tags and their ID associated to a user

            Returns:
                tags: name-id pair of tags associated to a user
                    Example:
                        {
                            &#34;tag1&#34;: 1,
                            &#34;tag2&#34;: 3,
                            &#34;tag3&#34;: 4
                        }
        &#34;&#34;&#34;

        return self._tags

    def has_tag(self, tag_name: str) -&gt; bool:
        &#34;&#34;&#34;Checks if a tag exist for the logged-in user

            Args:
                tag_name (str): name of the tag to search

            Returns:
                bool: return True if tag is found, else returns false

            Raises:
                SDKException:
                    if type of the tag name argument is not string

        &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;EntityTags&#39;, &#39;101&#39;)

        return self._tags and tag_name.lower() in self._tags

    def get(self, name: str):
        &#34;&#34;&#34;Returns an instance of the Tag class for the given tag name.

            Args:
                name    (str)   --  name of the tag to get the instance of

            Returns:
                object  -   instance of the Tag class for the given tag name

            Raises:
                SDKException:
                    if type of the tag name argument is not string

                    if no tag exists with the given name

        &#34;&#34;&#34;
        if self.has_tag(name):
            name = name.lower()
            return Tag(self._commcell_object, name, self._tags[name])

        raise SDKException(&#39;EntityTags&#39;, &#39;105&#39;)

    def refresh(self) -&gt; None:
        &#34;&#34;&#34;Refresh the list of tags&#34;&#34;&#34;
        self._tags = self._get_tags()

    def add(self, tag_name: str):
        &#34;&#34;&#34;Method to add an entity tag

            Args:
                tag_name (str): entity tag name

            Returns:
                object  -   instance of the Tag class, for the newly created entity tag
        &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;EntityTags&#39;, &#39;101&#39;)

        payload = {
            &#39;container&#39;: {
                &#39;containerId&#39;: self.DEFAULT_TAGSET_ID
                },
            &#39;tags&#39;: [
                {
                    &#39;name&#39;: f&#34;{tag_name}&#34;
                }
            ]
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._service[&#39;CREATE_ENTITY_TAGS&#39;], payload)

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        response_json = response.json()
        if &#39;errList&#39; in response_json:
            err_message = response_json.get(&#39;errLogMessage&#39;)
            err_code = response_json.get(&#39;errorCode&#39;)

            if err_code or err_message:
                raise SDKException(&#39;EntityTags&#39;, &#39;103&#39;, err_message)

        self.refresh()
        return self.get(tag_name)

    def delete(self, tag_name: str) -&gt; None:
        &#34;&#34;&#34;Deletes the entity tag

        Args:
            tag_name (str): entity tag name to delete
        &#34;&#34;&#34;
        if not self.has_tag(tag_name):
            raise SDKException(&#34;EntityTags&#34;, &#39;105&#39;)

        tag_name = tag_name.lower()
        tag_id = self._tags[tag_name]

        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._service[&#39;DELETE_ENTITY_TAGS&#39;] % tag_id)

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        resp = response.json()
        err_code = 0
        err_message = &#34;&#34;
        if resp:
            err_code = resp.get(&#39;errorCode&#39;, 0)
            err_message = resp.get(&#39;errorMessage&#39;, &#34;&#34;)

        if err_code or err_message:
            raise SDKException(&#34;EntityTags&#34;, &#39;102&#39;, f&#34;Error: {err_message}&#34;)

        self.refresh()</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.tags.Tags.DEFAULT_TAGSET_ID"><code class="name">var <span class="ident">DEFAULT_TAGSET_ID</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.tags.Tags.all_tags"><code class="name">var <span class="ident">all_tags</span> : dict</code></dt>
<dd>
<div class="desc"><p>Returns a dictionary containing tags and their ID associated to a user</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>tags</code></dt>
<dd>name-id pair of tags associated to a user
Example:
{
"tag1": 1,
"tag2": 3,
"tag3": 4
}</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L110-L124" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_tags(self) -&gt; dict:
    &#34;&#34;&#34;Returns a dictionary containing tags and their ID associated to a user

        Returns:
            tags: name-id pair of tags associated to a user
                Example:
                    {
                        &#34;tag1&#34;: 1,
                        &#34;tag2&#34;: 3,
                        &#34;tag3&#34;: 4
                    }
    &#34;&#34;&#34;

    return self._tags</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.tags.Tags.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, tag_name: str)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to add an entity tag</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>tag_name</code></strong> :&ensp;<code>str</code></dt>
<dd>entity tag name</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Tag class, for the newly created entity tag</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L171-L209" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, tag_name: str):
    &#34;&#34;&#34;Method to add an entity tag

        Args:
            tag_name (str): entity tag name

        Returns:
            object  -   instance of the Tag class, for the newly created entity tag
    &#34;&#34;&#34;
    if not isinstance(tag_name, str):
        raise SDKException(&#39;EntityTags&#39;, &#39;101&#39;)

    payload = {
        &#39;container&#39;: {
            &#39;containerId&#39;: self.DEFAULT_TAGSET_ID
            },
        &#39;tags&#39;: [
            {
                &#39;name&#39;: f&#34;{tag_name}&#34;
            }
        ]
    }

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._service[&#39;CREATE_ENTITY_TAGS&#39;], payload)

    if not flag:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    response_json = response.json()
    if &#39;errList&#39; in response_json:
        err_message = response_json.get(&#39;errLogMessage&#39;)
        err_code = response_json.get(&#39;errorCode&#39;)

        if err_code or err_message:
            raise SDKException(&#39;EntityTags&#39;, &#39;103&#39;, err_message)

    self.refresh()
    return self.get(tag_name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.tags.Tags.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, tag_name: str) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the entity tag</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>tag_name</code></strong> :&ensp;<code>str</code></dt>
<dd>entity tag name to delete</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L211-L239" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, tag_name: str) -&gt; None:
    &#34;&#34;&#34;Deletes the entity tag

    Args:
        tag_name (str): entity tag name to delete
    &#34;&#34;&#34;
    if not self.has_tag(tag_name):
        raise SDKException(&#34;EntityTags&#34;, &#39;105&#39;)

    tag_name = tag_name.lower()
    tag_id = self._tags[tag_name]

    flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._service[&#39;DELETE_ENTITY_TAGS&#39;] % tag_id)

    if not flag:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    resp = response.json()
    err_code = 0
    err_message = &#34;&#34;
    if resp:
        err_code = resp.get(&#39;errorCode&#39;, 0)
        err_message = resp.get(&#39;errorMessage&#39;, &#34;&#34;)

    if err_code or err_message:
        raise SDKException(&#34;EntityTags&#34;, &#39;102&#39;, f&#34;Error: {err_message}&#34;)

    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.tags.Tags.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, name: str)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns an instance of the Tag class for the given tag name.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the tag to get the instance of</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Tag class for the given tag name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the tag name argument is not string</p>
<pre><code>if no tag exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L145-L165" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, name: str):
    &#34;&#34;&#34;Returns an instance of the Tag class for the given tag name.

        Args:
            name    (str)   --  name of the tag to get the instance of

        Returns:
            object  -   instance of the Tag class for the given tag name

        Raises:
            SDKException:
                if type of the tag name argument is not string

                if no tag exists with the given name

    &#34;&#34;&#34;
    if self.has_tag(name):
        name = name.lower()
        return Tag(self._commcell_object, name, self._tags[name])

    raise SDKException(&#39;EntityTags&#39;, &#39;105&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.tags.Tags.has_tag"><code class="name flex">
<span>def <span class="ident">has_tag</span></span>(<span>self, tag_name: str) ‑> bool</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a tag exist for the logged-in user</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>tag_name</code></strong> :&ensp;<code>str</code></dt>
<dd>name of the tag to search</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>bool</code></dt>
<dd>return True if tag is found, else returns false</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the tag name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L126-L143" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_tag(self, tag_name: str) -&gt; bool:
    &#34;&#34;&#34;Checks if a tag exist for the logged-in user

        Args:
            tag_name (str): name of the tag to search

        Returns:
            bool: return True if tag is found, else returns false

        Raises:
            SDKException:
                if type of the tag name argument is not string

    &#34;&#34;&#34;
    if not isinstance(tag_name, str):
        raise SDKException(&#39;EntityTags&#39;, &#39;101&#39;)

    return self._tags and tag_name.lower() in self._tags</code></pre>
</details>
</dd>
<dt id="cvpysdk.tags.Tags.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the list of tags</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/tags.py#L167-L169" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self) -&gt; None:
    &#34;&#34;&#34;Refresh the list of tags&#34;&#34;&#34;
    self._tags = self._get_tags()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.tags.Tag" href="#cvpysdk.tags.Tag">Tag</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.tags.Tag.tag_id" href="#cvpysdk.tags.Tag.tag_id">tag_id</a></code></li>
<li><code><a title="cvpysdk.tags.Tag.tag_name" href="#cvpysdk.tags.Tag.tag_name">tag_name</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.tags.Tags" href="#cvpysdk.tags.Tags">Tags</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.tags.Tags.DEFAULT_TAGSET_ID" href="#cvpysdk.tags.Tags.DEFAULT_TAGSET_ID">DEFAULT_TAGSET_ID</a></code></li>
<li><code><a title="cvpysdk.tags.Tags.add" href="#cvpysdk.tags.Tags.add">add</a></code></li>
<li><code><a title="cvpysdk.tags.Tags.all_tags" href="#cvpysdk.tags.Tags.all_tags">all_tags</a></code></li>
<li><code><a title="cvpysdk.tags.Tags.delete" href="#cvpysdk.tags.Tags.delete">delete</a></code></li>
<li><code><a title="cvpysdk.tags.Tags.get" href="#cvpysdk.tags.Tags.get">get</a></code></li>
<li><code><a title="cvpysdk.tags.Tags.has_tag" href="#cvpysdk.tags.Tags.has_tag">has_tag</a></code></li>
<li><code><a title="cvpysdk.tags.Tags.refresh" href="#cvpysdk.tags.Tags.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>