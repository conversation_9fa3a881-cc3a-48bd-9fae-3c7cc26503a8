<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.bigdataappssubclient API documentation</title>
<meta name="description" content="Module for operating on a Big Data Apps Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.bigdataappssubclient</code></h1>
</header>
<section id="section-intro">
<p>Module for operating on a Big Data Apps Subclient</p>
<h2 id="bigdataappssubclient">Bigdataappssubclient</h2>
<p><strong>init</strong>()
&ndash;
Just inializes all properties related to its super class</p>
<p>set_data_access_nodes(data_access_nodes) &ndash; adds the passed json object as data access
nodes for this subclient.</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/bigdataappssubclient.py#L1-L113" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
        Module for operating on a Big Data Apps Subclient

        BigDataAppsSubclient:

            __init__()                  --  Just inializes all properties related to its super class

            set_data_access_nodes(data_access_nodes) -- adds the passed json object as data access
                                                        nodes for this subclient.

&#34;&#34;&#34;

from __future__ import unicode_literals
from ..subclients.fssubclient import FileSystemSubclient
from ..exception import SDKException


class BigDataAppsSubclient(FileSystemSubclient):
    &#34;&#34;&#34;
        Derived class from FileSystemSubclient. Can perform fs subclient operations.
    &#34;&#34;&#34;
    def __new__(cls, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Object creation function for BigDataAppsSubclient which returns appropiate
        sub class object based on cluster type

        Args:
            backupset_object    (obj)   --  Backupset object associated with the
            subclient

            subclient_name      (str)   --  Subclient name

            subclient_id        (int)   --  Subclient Id

        Returns:
            object              (obj)   --  Object associated with the Bigdatapps subclient

        &#34;&#34;&#34;
        from ..subclients.splunksubclient import SplunkSubclient
        from ..subclients.index_server_subclient import IndexServerSubclient
        cluster_types = {
            16: SplunkSubclient,
            6: IndexServerSubclient
        }

        bigdata_apps_cluster_type = backupset_object._instance_object.properties. \
            get(&#39;distributedClusterInstance&#39;, {}).get(&#39;clusterType&#39;, -1)

        if bigdata_apps_cluster_type in cluster_types.keys():
            cluster_type = cluster_types[bigdata_apps_cluster_type]
            return object.__new__(cluster_type)

        return object.__new__(cls)

    def set_data_access_nodes(self, data_access_nodes):
        &#34;&#34;&#34;
            Sets the Data Access Nodes for the distributed apps subclient.
            Args :

                data_access_nodes (list) : Sets the list of client nodes passed as
                                            data access node for this distributed apps
                                            subclient

            Raise SDK Exception :

                If unable to set data access nodes property of the subclient.

        &#34;&#34;&#34;

        data_access_nodes_client_json = []
        for access_node in data_access_nodes:
            data_access_nodes_client_json.append({&#34;clientName&#34;: access_node})

        data_access_nodes_json = {
            &#34;dataAccessNodes&#34;: data_access_nodes_client_json
        }

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;dfsSubclientProp&#34;: {
                    &#34;distributedDataAccessNodes&#34;: data_access_nodes_json
                }
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._SUBCLIENT, request_json)

        output = self._process_update_response(flag, response)

        if output[0]:
            return

        o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.bigdataappssubclient.BigDataAppsSubclient"><code class="flex name class">
<span>class <span class="ident">BigDataAppsSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from FileSystemSubclient. Can perform fs subclient operations.</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/bigdataappssubclient.py#L36-L113" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class BigDataAppsSubclient(FileSystemSubclient):
    &#34;&#34;&#34;
        Derived class from FileSystemSubclient. Can perform fs subclient operations.
    &#34;&#34;&#34;
    def __new__(cls, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Object creation function for BigDataAppsSubclient which returns appropiate
        sub class object based on cluster type

        Args:
            backupset_object    (obj)   --  Backupset object associated with the
            subclient

            subclient_name      (str)   --  Subclient name

            subclient_id        (int)   --  Subclient Id

        Returns:
            object              (obj)   --  Object associated with the Bigdatapps subclient

        &#34;&#34;&#34;
        from ..subclients.splunksubclient import SplunkSubclient
        from ..subclients.index_server_subclient import IndexServerSubclient
        cluster_types = {
            16: SplunkSubclient,
            6: IndexServerSubclient
        }

        bigdata_apps_cluster_type = backupset_object._instance_object.properties. \
            get(&#39;distributedClusterInstance&#39;, {}).get(&#39;clusterType&#39;, -1)

        if bigdata_apps_cluster_type in cluster_types.keys():
            cluster_type = cluster_types[bigdata_apps_cluster_type]
            return object.__new__(cluster_type)

        return object.__new__(cls)

    def set_data_access_nodes(self, data_access_nodes):
        &#34;&#34;&#34;
            Sets the Data Access Nodes for the distributed apps subclient.
            Args :

                data_access_nodes (list) : Sets the list of client nodes passed as
                                            data access node for this distributed apps
                                            subclient

            Raise SDK Exception :

                If unable to set data access nodes property of the subclient.

        &#34;&#34;&#34;

        data_access_nodes_client_json = []
        for access_node in data_access_nodes:
            data_access_nodes_client_json.append({&#34;clientName&#34;: access_node})

        data_access_nodes_json = {
            &#34;dataAccessNodes&#34;: data_access_nodes_client_json
        }

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;dfsSubclientProp&#34;: {
                    &#34;distributedDataAccessNodes&#34;: data_access_nodes_json
                }
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._SUBCLIENT, request_json)

        output = self._process_update_response(flag, response)

        if output[0]:
            return

        o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient">FileSystemSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.index_server_subclient.IndexServerSubclient" href="index_server_subclient.html#cvpysdk.subclients.index_server_subclient.IndexServerSubclient">IndexServerSubclient</a></li>
<li><a title="cvpysdk.subclients.splunksubclient.SplunkSubclient" href="splunksubclient.html#cvpysdk.subclients.splunksubclient.SplunkSubclient">SplunkSubclient</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.bigdataappssubclient.BigDataAppsSubclient.set_data_access_nodes"><code class="name flex">
<span>def <span class="ident">set_data_access_nodes</span></span>(<span>self, data_access_nodes)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the Data Access Nodes for the distributed apps subclient.
Args :</p>
<pre><code>data_access_nodes (list) : Sets the list of client nodes passed as
                            data access node for this distributed apps
                            subclient
</code></pre>
<p>Raise SDK Exception :</p>
<pre><code>If unable to set data access nodes property of the subclient.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/bigdataappssubclient.py#L73-L113" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_data_access_nodes(self, data_access_nodes):
    &#34;&#34;&#34;
        Sets the Data Access Nodes for the distributed apps subclient.
        Args :

            data_access_nodes (list) : Sets the list of client nodes passed as
                                        data access node for this distributed apps
                                        subclient

        Raise SDK Exception :

            If unable to set data access nodes property of the subclient.

    &#34;&#34;&#34;

    data_access_nodes_client_json = []
    for access_node in data_access_nodes:
        data_access_nodes_client_json.append({&#34;clientName&#34;: access_node})

    data_access_nodes_json = {
        &#34;dataAccessNodes&#34;: data_access_nodes_client_json
    }

    request_json = {
        &#34;subClientProperties&#34;: {
            &#34;dfsSubclientProp&#34;: {
                &#34;distributedDataAccessNodes&#34;: data_access_nodes_json
            }
        }
    }

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._SUBCLIENT, request_json)

    output = self._process_update_response(flag, response)

    if output[0]:
        return

    o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;
    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient">FileSystemSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.add_comparison" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.add_comparison">add_comparison</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention">archiver_retention</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention_days" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention_days">archiver_retention_days</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_nodes" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_nodes">backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_only_archiving_candidate" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_only_archiving_candidate">backup_only_archiving_candidate</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_private_authorities" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_private_authorities">backup_private_authorities</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_queue_data" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_queue_data">backup_queue_data</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention">backup_retention</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention_days" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention_days">backup_retention_days</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_savf_file_data" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_savf_file_data">backup_savf_file_data</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_spool_file_data" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_spool_file_data">backup_spool_file_data</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_using_multiple_drives" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_using_multiple_drives">backup_using_multiple_drives</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.block_level_backup_option" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.block_level_backup_option">block_level_backup_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.catalog_acl" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.catalog_acl">catalog_acl</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.content" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.create_file_level_index_option" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.create_file_level_index_option">create_file_level_index_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.disable_content_indexing" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.disable_content_indexing">disable_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup">disk_cleanup</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup_rules" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup_rules">disk_cleanup_rules</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_content_indexing" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_content_indexing">enable_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_dc_content_indexing" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_dc_content_indexing">enable_dc_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_synclib" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_synclib">enable_synclib</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.exception_content" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.exception_content">exception_content</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.file_version" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.file_version">file_version</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.filter_content" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.filter_content">filter_content</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.find_all_versions" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.find_all_versions">find_all_versions</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.generate_signature_on_ibmi" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.generate_signature_on_ibmi">generate_signature_on_ibmi</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.global_filter_status" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.global_filter_status">global_filter_status</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_compression" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_compression">ibmi_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_dr_config" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_dr_config">ibmi_dr_config</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.impersonate_user" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.impersonate_user">impersonate_user</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_cycles_retention" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_cycles_retention">index_pruning_cycles_retention</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_days_retention" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_days_retention">index_pruning_days_retention</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_type" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_type">index_pruning_type</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_server" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.index_server">index_server</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.network_share_auto_mount" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.network_share_auto_mount">network_share_auto_mount</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.object_level_backup" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.object_level_backup">object_level_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_option" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_option">onetouch_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server">onetouch_server</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server_directory" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server_directory">onetouch_server_directory</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.other_pending_changes" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.other_pending_changes">other_pending_changes</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.pending_record_changes" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.pending_record_changes">pending_record_changes</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.pre_post_commands" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.pre_post_commands">pre_post_commands</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.preview_backedup_file" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.preview_backedup_file">preview_backedup_file</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_in_place" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_out_of_place" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.run_backup_copy" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.run_backup_copy">run_backup_copy</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.save_access_path" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.save_access_path">save_access_path</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.save_while_active_option" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.save_while_active_option">save_while_active_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.scan_type" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.scan_type">scan_type</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.software_compression" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.system_state_option" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.system_state_option">system_state_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.target_release" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.target_release">target_release</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_days" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_days">trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_option" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_option">trueup_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.update_history" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.update_history">update_history</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.use_vss" href="fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient.use_vss">use_vss</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.bigdataappssubclient.BigDataAppsSubclient" href="#cvpysdk.subclients.bigdataappssubclient.BigDataAppsSubclient">BigDataAppsSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.bigdataappssubclient.BigDataAppsSubclient.set_data_access_nodes" href="#cvpysdk.subclients.bigdataappssubclient.BigDataAppsSubclient.set_data_access_nodes">set_data_access_nodes</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>