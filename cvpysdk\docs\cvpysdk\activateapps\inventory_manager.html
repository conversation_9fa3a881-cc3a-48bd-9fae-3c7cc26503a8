<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activateapps.inventory_manager API documentation</title>
<meta name="description" content="Main file for performing operations on inventory manager app under Activate …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activateapps.inventory_manager</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing operations on inventory manager app under Activate.</p>
<p>Inventories, Inventory, Assets &amp; Asset are the four classes defined in this file</p>
<p>Inventories: class for representing all inventories in the commcell</p>
<p>Inventory: class for representing a single inventory in the commcell</p>
<p>Assets: class for representing all assets in an inventory</p>
<p>Asset: class to represent single asset in an inventory</p>
<h2 id="inventories">Inventories</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the Inventories class</p>
<p>_get_inventories()
&ndash;
Gets all inventories in the commcell</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the Inventories from the commcell</p>
<p>get_properties()
&ndash;
returns the properties for given inventory name</p>
<p>has_inventory()
&ndash;
Checks if a given inventory name exists in the commcell or not</p>
<p>get()
&ndash;
returns the Inventory class object for given inventory name</p>
<p>add()
&ndash;
add inventory to the commcell</p>
<p>delete()
&ndash;
delete inventory from the commcell</p>
<h2 id="inventory">Inventory</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the Inventory class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_inventory_properties()
&ndash;
Gets all the properties of this inventory</p>
<p>_get_schedule_object()
&ndash;
returns the schedule class object associated to this inventory</p>
<p>_get_data_source_handler_object()
&ndash;
returns the datasource and default handler object for this inventory</p>
<p>_get_permission()
&ndash;
returns the security associations for this inventory</p>
<p>refresh()
&ndash;
refresh the properties of the inventory</p>
<p>get_assets()
&ndash;
returns the Assets class object for this inventory</p>
<p>share()
&ndash;
shares inventory with other user or user group</p>
<p>start_collection()
&ndash;
starts collection job on this inventory</p>
<p>get_inventory_data()
&ndash;
returns data from inventory</p>
<h2 id="inventory-attributes">Inventory Attributes</h2>
<pre><code>**properties**              --  returns properties of the inventory

**index_server_name**       --  returns the index server name associated with this inventory

**_index_server_cloud_id**  --  returns the index server cloudid associated with this inventory

**inventory_name**          --  returns the inventory name

**inventory_id**            --  returns the inventory id

**security_associations**   --  returns the security associations blob of this inventory

**schedule**                --  returns the schedule object associated with this inventory

**data_source**             --  returns the DataSource object associated with this inventory

**handler**                 --  returns the default handler object for this inventory
</code></pre>
<h2 id="assets">Assets</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the Assets class</p>
<p>refresh()
&ndash;
refresh the assets associated with inventory</p>
<p>add()
&ndash;
adds asset to the inventory</p>
<p>get()
&ndash;
returns the instance of Asset class based on given asset name</p>
<p>has_asset()
&ndash;
returns whether given asset exists or not in inventory</p>
<p>delete()
&ndash;
deletes the asset from the inventory</p>
<p>_get_assets_properties()
&ndash;
returns the assets properties</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<h2 id="assets-attributes">Assets Attributes:</h2>
<pre><code>**assets**                  --  returns the assets details as json
</code></pre>
<h2 id="asset">Asset</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the Asset class</p>
<p>_get_properties()
&ndash;
returns the properties of the asset</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the asset associated with inventory</p>
<p>get_job_history()
&ndash;
returns the job history details of this asset</p>
<p>get_job_status()
&ndash;
returns the latest job status details of this asset</p>
<p>get_asset_prop()
&ndash;
returns the asset property value for the given property name</p>
<h2 id="asset-attributes">Asset Attributes:</h2>
<pre><code>    **asset_id**            --      returns the id of asset

    **asset_name**          --      returns the name of asset

    **asset_type**          --      returns the type of asset

    **crawl_start_time**    --      returns the last crawl start time of asset

    **asset_props**         --      returns the properties(name/value pair) of asset

    **asset_status**        --      returns the status of asset

    **inventory_id**        --      returns the inventory id of this asset
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1-L1190" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing operations on inventory manager app under Activate.

Inventories, Inventory, Assets &amp; Asset are the four classes defined in this file

Inventories: class for representing all inventories in the commcell

Inventory: class for representing a single inventory in the commcell

Assets: class for representing all assets in an inventory

Asset: class to represent single asset in an inventory

Inventories:

            __init__()                          --  initialise object of the Inventories class

            _get_inventories()                  --  Gets all inventories in the commcell

             _response_not_success()            --  parses through the exception response, and raises SDKException

             refresh()                          --  refresh the Inventories from the commcell

             get_properties()                   --  returns the properties for given inventory name

             has_inventory()                    --  Checks if a given inventory name exists in the commcell or not

             get()                              --  returns the Inventory class object for given inventory name

             add()                              --  add inventory to the commcell

             delete()                           --  delete inventory from the commcell


Inventory:

            __init__()                          --  initialise object of the Inventory class

            _response_not_success()             --  parses through the exception response, and raises SDKException

            _get_inventory_properties()         --  Gets all the properties of this inventory

             _get_schedule_object()             --  returns the schedule class object associated to this inventory

            _get_data_source_handler_object()   --  returns the datasource and default handler object for this inventory

            _get_permission()                   --  returns the security associations for this inventory

            refresh()                           --  refresh the properties of the inventory

            get_assets()                        --  returns the Assets class object for this inventory

            share()                             --  shares inventory with other user or user group

            start_collection()                  --  starts collection job on this inventory

            get_inventory_data()                --  returns data from inventory

Inventory Attributes
-----------------

    **properties**              --  returns properties of the inventory

    **index_server_name**       --  returns the index server name associated with this inventory

    **_index_server_cloud_id**  --  returns the index server cloudid associated with this inventory

    **inventory_name**          --  returns the inventory name

    **inventory_id**            --  returns the inventory id

    **security_associations**   --  returns the security associations blob of this inventory

    **schedule**                --  returns the schedule object associated with this inventory

    **data_source**             --  returns the DataSource object associated with this inventory

    **handler**                 --  returns the default handler object for this inventory

Assets:

        __init__()                          --  initialise object of the Assets class

        refresh()                           --  refresh the assets associated with inventory

        add()                               --  adds asset to the inventory

        get()                               --  returns the instance of Asset class based on given asset name

        has_asset()                         --  returns whether given asset exists or not in inventory

        delete()                            --  deletes the asset from the inventory

        _get_assets_properties()            --  returns the assets properties

         _response_not_success()            --  parses through the exception response, and raises SDKException

Assets Attributes:
----------------

    **assets**                  --  returns the assets details as json

Asset:

        __init__()                          --  initialise object of the Asset class

        _get_properties()                   --  returns the properties of the asset

        _response_not_success()             --  parses through the exception response, and raises SDKException

        refresh()                           --  refresh the asset associated with inventory

        get_job_history()                   --  returns the job history details of this asset

        get_job_status()                    --  returns the latest job status details of this asset

        get_asset_prop()                    --  returns the asset property value for the given property name


Asset Attributes:
-----------------

        **asset_id**            --      returns the id of asset

        **asset_name**          --      returns the name of asset

        **asset_type**          --      returns the type of asset

        **crawl_start_time**    --      returns the last crawl start time of asset

        **asset_props**         --      returns the properties(name/value pair) of asset

        **asset_status**        --      returns the status of asset

        **inventory_id**        --      returns the inventory id of this asset


&#34;&#34;&#34;
import copy

from ..activateapps.constants import InventoryConstants
from ..schedules import Schedules
from ..exception import SDKException


class Inventories():
    &#34;&#34;&#34;Class for representing all inventories in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Inventories class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Inventories class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._inventories = None
        self._API_INVENTORIES = self._services[&#39;EDISCOVERY_INVENTORIES&#39;]
        self._API_DELETE_INVENTORY = self._services[&#39;EDISCOVERY_INVENTORY&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_inventories(self):
        &#34;&#34;&#34;Gets all inventories from the commcell

            Args:

                None

            Return:

                list(dict)        --  list Containing inventory details dict

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        output = {}
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_INVENTORIES
        )
        if flag:
            if response.json() and &#39;inventories&#39; in response.json():
                inventories = response.json()[&#39;inventories&#39;]
                for inventory in inventories:
                    output[inventory[&#39;displayName&#39;].lower()] = inventory
                return output
            raise SDKException(&#39;Inventory&#39;, &#39;103&#39;)
        self._response_not_success(response)

    def add(self, inventory_name, index_server, name_server=None):
        &#34;&#34;&#34;Adds inventory to the commcell with given inputs

                Args:

                    inventory_name              (str)       --  Name of the inventory

                    index_server                (str)       --  Index server name

                    name_server                 (list)      --  Name server assets which needs to be added to inventory

                Returns:

                    object  --  Instance of Inventory Class

                Raises:

                    SDKException:

                            if input data type is not valid

                            if failed to add inventory

                            if Index Server doesn&#39;t exists in commcell

        &#34;&#34;&#34;
        if not isinstance(inventory_name, str) or not isinstance(index_server, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        req_json = copy.deepcopy(InventoryConstants.INVENTORY_ADD_REQUEST_JSON)
        if name_server:
            req_json[&#39;identityServers&#39;] = name_server
        req_json[&#39;name&#39;] = inventory_name
        if not self._commcell_object.index_servers.has(index_server):
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Given index server name not exists on this commcell&#34;)
        index_server_obj = self._commcell_object.index_servers.get(index_server)
        req_json[&#39;indexServer&#39;][&#39;cloudId&#39;] = index_server_obj.cloud_id
        req_json[&#39;indexServer&#39;][&#39;displayName&#39;] = index_server_obj.cloud_name
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_INVENTORIES, req_json
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to create inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                elif &#39;name&#39; in response.json():
                    inventory = response.json()[&#39;name&#39;]
                    inventory_id = response.json()[&#39;id&#39;]
                    self.refresh()
                    return Inventory(self._commcell_object, inventory_name, inventory_id)
                raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, f&#34;Failed to create inventory with response - {response.json()}&#34;)
            raise SDKException(&#39;Inventory&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def delete(self, inventory_name):
        &#34;&#34;&#34;Deletes the inventory from the commcell

                Args:

                    inventory_name      (str)       --  Inventory name to be deleted

                Returns:
                    None

                Raises:

                    SDKException:

                            if unable to find inventory

                            if failed to delete inventory

                            if input type is not valid

        &#34;&#34;&#34;
        if not isinstance(inventory_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        if not self.has_inventory(inventory_name):
            raise SDKException(&#39;Inventory&#39;, &#39;106&#39;)
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._API_DELETE_INVENTORY % self._inventories[inventory_name.lower()][&#39;id&#39;]
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to Delete inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                self.refresh()
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;107&#39;)
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the inventories associated with the commcell.&#34;&#34;&#34;
        self._inventories = self._get_inventories()

    def get_properties(self, inventory_name):
        &#34;&#34;&#34;Returns a properties of the specified Inventory

            Args:
                inventory_name (str)  --  name of the inventory

            Returns:
                dict -  properties for the given inventory name


        &#34;&#34;&#34;
        return self._inventories[inventory_name.lower()]

    def has_inventory(self, inventory_name):
        &#34;&#34;&#34;Checks if a inventory exists in the commcell with the input name.

            Args:
                inventory_name (str)  --  name of the inventory

            Returns:
                bool - boolean output to specify whether the inventory exists in the commcell or not

            Raises:
                SDKException:
                    if type of the inventory name argument is not string

        &#34;&#34;&#34;
        if not isinstance(inventory_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)

        return self._inventories and inventory_name.lower() in map(str.lower, self._inventories)

    def get(self, inventory_name):
        &#34;&#34;&#34;Returns a Inventory object for the given inventory name.

            Args:
                inventory_name (str)  --  name of the inventory

            Returns:

                obj                 -- Object of Inventory class

            Raises:

                SDKException:

                    if inventory doesn&#39;t exists in commcell

                    if inventory_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(inventory_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)

        if self.has_inventory(inventory_name):
            inventory_id = self._inventories[inventory_name.lower()][&#39;id&#39;]
            return Inventory(self._commcell_object, inventory_name, inventory_id)
        raise SDKException(&#39;Inventory&#39;, &#39;106&#39;)


class Inventory():
    &#34;&#34;&#34;Class for performing operations on a single inventory&#34;&#34;&#34;

    def __init__(self, commcell_object, inventory_name, inventory_id=None):
        &#34;&#34;&#34;Initialize an object of the Inventory class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                inventory_name     (str)        --  name of the Inventory

                inventory_id       (str)        --  id of Inventory
                    default: None

            Returns:
                object  -   instance of the Inventory class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._inventory_id = None
        self._inventory_name = inventory_name
        self._inventory_props = None
        self._index_server_name = None
        self._index_server_cloud_id = None
        self._security_associations = None
        self._schedule = None
        self._data_source = None
        self._handler = None
        self._API_GET_INVENTORY_DETAILS = self._services[&#39;EDISCOVERY_INVENTORY&#39;]
        self._API_SECURITY_ENTITY = self._services[&#39;ENTITY_SECURITY_ASSOCIATION&#39;]
        self._API_GET_DEFAULT_HANDLER = self._services[&#39;EDISCOVERY_GET_DEFAULT_HANDLER&#39;]
        self._API_PERMISSION = self._services[&#39;V4_ACTIVATE_DS_PERMISSION&#39;]
        self._API_CRAWL = self._services[&#39;V4_INVENTORY_CRAWL&#39;]

        if not inventory_id:
            self._inventory_id = self._commcell_object.activate.inventory_manager().get(inventory_name).inventory_id
        else:
            self._inventory_id = inventory_id
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_permission(self):
        &#34;&#34;&#34;returns security association blob for this inventory

                Args:

                    None

                Returns:

                    dict    --  Security association blob

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_PERMISSION % self._inventory_id
        )
        if flag:
            if response.json() and &#39;securityAssociations&#39; in response.json():
                return response.json()[&#39;securityAssociations&#39;]
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Inventory permission fetch failed&#34;)
        self._response_not_success(response)

    def _get_inventory_properties(self):
        &#34;&#34;&#34; Get inventory properties from the commcell
                Args:

                    None

                Returns:

                    dict        --  Properties of inventory

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_GET_INVENTORY_DETAILS % self._inventory_id
        )
        if flag:
            if response.json():
                inventory_props = response.json()
                self._index_server_name = inventory_props[&#39;indexServer&#39;][&#39;displayName&#39;]
                self._index_server_cloud_id = inventory_props[&#39;indexServer&#39;][&#39;cloudId&#39;]
                self._inventory_name = inventory_props[&#39;displayName&#39;]
                return inventory_props
            raise SDKException(&#39;Inventory&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the inventory details for associated object&#34;&#34;&#34;
        self._inventory_props = self._get_inventory_properties()
        self._schedule = self._get_schedule_object()
        self._data_source, self._handler = self._get_data_source_handler_object()
        self._security_associations = self._get_permission()

    def get_assets(self):
        &#34;&#34;&#34;Returns the Assets class instance for this inventory

                Args:

                    None

                Returns:

                    object      --  Instance of Assets class

        &#34;&#34;&#34;
        return Assets(self._commcell_object, self.inventory_name, self.inventory_id)

    def start_collection(self):
        &#34;&#34;&#34;Starts collection job on this inventory

                Args:

                    None

                Return:

                    None

                Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, self._API_CRAWL % self._inventory_id
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to start crawl on inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                return
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while starting collection job on inventory&#34;)
        else:
            self._response_not_success(response)

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares inventory with given user or user group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        if not isinstance(user_or_group_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(InventoryConstants.INVENTORY_SHARE_REQUEST_JSON)
        external_user = False
        association_response = None
        if ops_type == 1 and len(self.security_associations) &gt; 1:
            association_request_json = copy.deepcopy(InventoryConstants.INVENTORY_SHARE_REQUEST_JSON)
            del association_request_json[&#39;securityAssociations&#39;]
            association_request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;seaDataSourceId&#39;] = int(self.inventory_id)
            # get security blob for this data source type entity - 132
            flag, response = self._cvpysdk_obj.make_request(
                &#39;GET&#39;, self._API_SECURITY_ENTITY % (132, int(self.inventory_id)), association_request_json
            )
            if flag:
                if response.json() and &#39;securityAssociations&#39; in response.json():
                    association_response = response.json(
                    )[&#39;securityAssociations&#39;][0][&#39;securityAssociations&#39;][&#39;associations&#39;]
                else:
                    raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#39;Failed to get existing security associations&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if &#39;\\&#39; in user_or_group_name:
            external_user = True
        if is_user:
            user_obj = self._commcell_object.users.get(user_or_group_name)
            user_id = user_obj.user_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userId&#39;] = int(user_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 13
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userName&#39;] = user_or_group_name
        elif external_user:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;groupId&#39;] = 0
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 62
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
                &#39;externalGroupName&#39;] = user_or_group_name
        else:
            grp_obj = self._commcell_object.user_groups.get(user_or_group_name)
            grp_id = grp_obj.user_group_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupId&#39;] = int(grp_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 15
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
                &#39;userGroupName&#39;] = user_or_group_name

        request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;seaDataSourceId&#39;] = self.inventory_id
        request_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = ops_type

        if allow_edit_permission:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;].append(
                InventoryConstants.EDIT_CATEGORY_PERMISSION)

            # Associate existing associations to the request
        if ops_type == 1 and len(self.security_associations) &gt; 1:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;].extend(association_response)

        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, self._API_PERMISSION % self._inventory_id, request_json
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]
                if error_code != 0:
                    error_message = response.json()[&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;, error_message)
                # update association list by refreshing inventory
                self.refresh()
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;111&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_data_source_handler_object(self):
        &#34;&#34;&#34;returns the data source and handler object associated with this inventory

                Args:
                    None

                Returns:
                    obj,obj     --  Instance of DataSource object,Instance of Handler object

                Raises:

                    SDKException:

                            if failed to get datasource or handler details
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_GET_DEFAULT_HANDLER % self.inventory_id)
        if flag:
            if response.json() and &#39;handlerInfos&#39; in response.json():
                handler_list = response.json()[&#39;handlerInfos&#39;]
                if not isinstance(handler_list, list):
                    raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Failed to get Datasource/Handler details&#34;)
                handler_details = handler_list[0]
                ds_name = handler_details[&#39;dataSourceName&#39;]
                handler_name = handler_details[&#39;handlerName&#39;]
                ds_obj = self._commcell_object.datacube.datasources.get(ds_name)
                handler_obj = ds_obj.ds_handlers.get(handler_name)
                return ds_obj, handler_obj
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#39;Unknown response while fetching datasource details&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_schedule_object(self):
        &#34;&#34;&#34;Returns the schedule class object of schedule associated to this inventory

                Args:

                    None

                Returns:

                    object      --  Instance of Schedule class

                Raises:

                    SDKException:

                            if failed to find schedule
        &#34;&#34;&#34;
        return Schedules(self).get()

    def get_inventory_data(self, handler_filter=&#34;&#34;):
        &#34;&#34;&#34; Executes handler for fetching data from inventory
                Args:

                     handler_filter    (str)  -- Filter which needs to applied for handler execution

                Returns:

                    dict        --  Dictionary of values fetched from handler execution

                Raises:

                    SDKExpception:

                        if error in fetching handler data

                        if input is not valid
        &#34;&#34;&#34;
        if not isinstance(handler_filter, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        if not self._handler:
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#39;No handler object initialised&#39;)
        return self._handler.get_handler_data(handler_filter=handler_filter)

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the properties of this inventory as dict&#34;&#34;&#34;
        return self._inventory_props

    @property
    def index_server_name(self):
        &#34;&#34;&#34;Returns the index server name associated with this inventory&#34;&#34;&#34;
        return self._index_server_name

    @property
    def index_server_cloud_id(self):
        &#34;&#34;&#34;Returns the index server id associated with this inventory&#34;&#34;&#34;
        return self._index_server_cloud_id

    @property
    def inventory_id(self):
        &#34;&#34;&#34;Returns the inventory id associated with this inventory&#34;&#34;&#34;
        return self._inventory_id

    @property
    def inventory_name(self):
        &#34;&#34;&#34;Returns the inventory name associated with this inventory&#34;&#34;&#34;
        return self._inventory_name

    @property
    def security_associations(self):
        &#34;&#34;&#34;Returns the security blob associated with this inventory&#34;&#34;&#34;
        return self._security_associations

    @property
    def schedule(self):
        &#34;&#34;&#34;Returns the schedule class object for schedule associated with this inventory&#34;&#34;&#34;
        return self._schedule

    @property
    def data_source(self):
        &#34;&#34;&#34;Returns the DataSource class object for datasource associated with this inventory&#34;&#34;&#34;
        return self._data_source

    @property
    def handler(self):
        &#34;&#34;&#34;Returns the Handler class object for default handler associated with this inventory&#34;&#34;&#34;
        return self._handler


class Assets():
    &#34;&#34;&#34;Class to represent all assets in an inventory&#34;&#34;&#34;

    def __init__(self, commcell_object, inventory_name, inventory_id=None):
        &#34;&#34;&#34;Initialize an object of the Assets class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                inventory_name     (str)        --  name of the Inventory

                inventory_id       (str)        --  id of Inventory
                    default: None

            Returns:
                object  -   instance of the Assets class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._inventory_id = None
        self._inventory_name = inventory_name
        if not inventory_id:
            self._inventory_id = self._commcell_object.activate.inventory_manager().get(inventory_name).inventory_id
        else:
            self._inventory_id = inventory_id
        self._assets = None
        self._API_ASSETS = self._services[&#39;EDISCOVERY_ASSETS&#39;]
        self._API_ASSET = self._services[&#39;EDISCOVERY_ASSET&#39;]
        self.refresh()

    def _get_assets_properties(self):
        &#34;&#34;&#34;gets the assets properties from inventory

                    Args:
                        None

                    Returns:

                        dict    --  containing asset properties

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_ASSETS % self._inventory_id)
        if flag:
            if response.json() and &#39;assets&#39; in response.json():
                assets = {}
                for asset in response.json()[&#39;assets&#39;]:
                    name = asset[&#39;name&#39;].lower()
                    assets[name] = asset
                return assets
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while fetching assets on inventory&#34;)
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the assets details associated with this inventory&#34;&#34;&#34;
        self._assets = self._get_assets_properties()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def has_asset(self, asset_name):
        &#34;&#34;&#34;Checks whether given asset exists in inventory or not

                        Args:

                            asset_name      (str)       --  Name of the asset

                        Returns:

                            bool      --  true if asset exists else false
        &#34;&#34;&#34;
        return self._assets and asset_name.lower() in self._assets

    def get(self, asset_name):
        &#34;&#34;&#34;Returns the asset object

                Args:

                    asset_name      (str)       --  Name of the asset

                Returns:

                    object      -- Instance of Asset class

                Raises:

                    SDKException:

                            if input is not valid

                            if asset doesn&#39;t exists in inventory
        &#34;&#34;&#34;
        if not isinstance(asset_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        if not self.has_asset(asset_name):
            raise SDKException(&#39;Inventory&#39;, &#39;109&#39;)
        return Asset(self._commcell_object, self._inventory_name, asset_name, self._inventory_id)

    def add(self, asset_name, asset_type=InventoryConstants.AssetType.IDENTITY_SERVER, **kwargs):
        &#34;&#34;&#34;Adds asset to the inventory

                Args:

                    asset_name          (str)       --  Name of the asset

                    asset_type          (Enum)      --  type of asset (Refer to InventoryConstants.AssetType class)

                    kwargs for FILE SERVER type Asset:

                            fqdn                --  File server FQDN

                            os                  --  File Server OS type
                                                        (Default:Windows)

                            ip                  --  File server IP

                            country_code        --  Country code (ISO 3166 2-letter code)

                Returns:

                    object  --  Instance of Asset class

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to add asset to inventory

        &#34;&#34;&#34;
        if not isinstance(asset_name, str) or not isinstance(asset_type, InventoryConstants.AssetType):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        request_json = {}
        if asset_type.value == InventoryConstants.AssetType.FILE_SERVER.value:
            for prop in InventoryConstants.ASSET_FILE_SERVER_PROPERTY:
                prop_name = InventoryConstants.FIELD_PROPS_MAPPING[prop]
                default_value = &#34;&#34;
                if prop_name not in kwargs:
                    # always use asset name as file server name
                    if prop == InventoryConstants.FIELD_PROPERTY_NAME:
                        default_value = asset_name
                    if prop == InventoryConstants.FIELD_PROPERTY_OS:
                        default_value = &#34;Windows&#34;
                request_json[prop] = kwargs.get(prop_name, default_value)
        else:
            request_json = copy.deepcopy(InventoryConstants.IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON)
            request_json[&#39;identityServers&#39;] = [asset_name]

        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, self._API_ASSETS % self._inventory_id, request_json)
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to add asset to inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                self.refresh()
                return Asset(self._commcell_object, self._inventory_name, asset_name, self._inventory_id)
            raise SDKException(&#39;Inventory&#39;, &#39;108&#39;)
        self._response_not_success(response)

    def delete(self, asset_name):
        &#34;&#34;&#34;Delete the asset from the inventory

                Args:

                    asset_name      (str)       --  Name of the asset

                Returns:

                    None

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to delete the asset

                            if unable to find this asset in inventory
        &#34;&#34;&#34;

        if not isinstance(asset_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        if not self.has_asset(asset_name):
            raise SDKException(&#39;Inventory&#39;, &#39;109&#39;)
        flag, response = self._cvpysdk_obj.make_request(
            &#39;DELETE&#39;, self._API_ASSET % (self._inventory_id, self._assets[asset_name.lower()][&#39;id&#39;]))
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to delete asset from inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                self.refresh()
                return
            raise SDKException(&#39;Inventory&#39;, &#39;110&#39;)
        self._response_not_success(response)

    @property
    def assets(self):
        &#34;&#34;&#34;Returns the assets details associated with this inventory&#34;&#34;&#34;
        return self._assets


class Asset():
    &#34;&#34;&#34;Class to represent single asset in an inventory&#34;&#34;&#34;

    def __init__(self, commcell_object, inventory_name, asset_name, inventory_id=None):
        &#34;&#34;&#34;Initialize an object of the Asset class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                inventory_name     (str)        --  name of the Inventory

                asset_name          (str)       --  Name of the asset

                inventory_id       (str)        --  id of Inventory
                    default: None

            Returns:
                object  -   instance of the Asset class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._inventory_id = None
        self._inventory_name = inventory_name
        if not inventory_id:
            self._inventory_id = self._commcell_object.activate.inventory_manager().get(inventory_name).inventory_id
        else:
            self._inventory_id = inventory_id
        self._asset_name = asset_name
        self._asset_props = None
        self._asset_id = None
        self._crawl_start_time = None
        self._asset_type = None
        self._asset_status = None
        self._asset_name_values_props = None
        self._API_ASSETS = self._services[&#39;EDISCOVERY_ASSETS&#39;]
        self._API_ASSET = self._services[&#39;EDISCOVERY_ASSET&#39;]
        self._API_ASSET_JOBS = self._services[&#39;EDISCOVERY_ASSET_JOBS&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_properties(self):
        &#34;&#34;&#34;Returns the properties of this asset

                Args:

                    None

                Returns:

                    dict        -- Containing properties of asset

        &#34;&#34;&#34;

        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_ASSETS % self._inventory_id)
        if flag:
            if response.json() and &#39;assets&#39; in response.json():
                for asset in response.json()[&#39;assets&#39;]:
                    if asset[&#39;name&#39;].lower() == self._asset_name.lower():
                        self._asset_id = asset.get(&#39;id&#39;, &#34;&#34;)
                        self._crawl_start_time = asset.get(&#39;lastCollectionTime&#39;, 0)
                        self._asset_type = asset.get(&#39;type&#39;, &#34;&#34;)
                        self._asset_status = asset.get(&#39;status&#39;, &#34;NA&#34;)
                        self._asset_name = asset[&#39;name&#39;]
                        if self._asset_type == InventoryConstants.AssetType.FILE_SERVER.name:
                            flag, response = self._cvpysdk_obj.make_request(
                                &#39;GET&#39;, self._API_ASSET % (self._inventory_id, self._asset_id))
                            if flag:
                                if response.json() and &#39;properties&#39; in response.json():
                                    self._asset_name_values_props = response.json()[&#39;properties&#39;]
                        return asset
                return {}
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while fetching assets on inventory&#34;)
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the asset details associated with this&#34;&#34;&#34;
        self._asset_props = self._get_properties()

    def get_job_history(self):
        &#34;&#34;&#34;Returns the job history details of this asset

                Args:
                    None

                Returns:

                    list(dict)    --  containing job history details

                Raises:

                    SDKException:

                            if failed to get job history

                            if asset is not supported for this operation

        &#34;&#34;&#34;
        if self.asset_type != InventoryConstants.AssetType.IDENTITY_SERVER.name:
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Not supported other than Name Server asset type&#34;)
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_ASSET_JOBS % (self._inventory_id, self._asset_id))
        if flag:
            if response.json() and &#39;jobs&#39; in response.json():
                return response.json()[&#39;jobs&#39;]
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while fetching job history on inventory&#34;)
        else:
            self._response_not_success(response)

    def get_job_status(self):
        &#34;&#34;&#34;Returns the latest job status details of this asset

                Args:
                    None

                Returns:

                    str    --  last job status (Eg:- RUNNING / IDLE)

                Raises:

                    SDKException:

                            if failed to get job status

                             if asset is not supported for this operation

        &#34;&#34;&#34;
        if self.asset_type != InventoryConstants.AssetType.IDENTITY_SERVER.name:
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Not supported other than Name Server asset type&#34;)
        self.refresh()  # do refresh before getting current status
        return self.asset_status

    def get_asset_prop(self, prop_name):
        &#34;&#34;&#34;returns the property value for given property name for this asset

            Args:

                prop_name       (str)       --  Name of the property

            Returns:

                str     --  Value of the property


        &#34;&#34;&#34;
        for prop in self._asset_name_values_props:
            name = prop[&#39;name&#39;]
            if name == prop_name:
                return prop[&#39;value&#39;]
        return &#34;&#34;

    @property
    def asset_id(self):
        &#34;&#34;&#34;Returns the asset id with this asset&#34;&#34;&#34;
        return self._asset_id

    @property
    def asset_name(self):
        &#34;&#34;&#34;Returns the asset name with this asset&#34;&#34;&#34;
        return self._asset_name

    @property
    def crawl_start_time(self):
        &#34;&#34;&#34;Returns the crawl start time with this asset&#34;&#34;&#34;
        return self._crawl_start_time

    @property
    def asset_type(self):
        &#34;&#34;&#34;Returns the asset type for this asset&#34;&#34;&#34;
        return self._asset_type

    @property
    def asset_status(self):
        &#34;&#34;&#34;Returns the asset status for this asset&#34;&#34;&#34;
        return self._asset_status

    @property
    def asset_props(self):
        &#34;&#34;&#34;Returns the property values for this asset&#34;&#34;&#34;
        return self._asset_name_values_props

    @property
    def inventory_id(self):
        &#34;&#34;&#34;Returns the inventory id for this asset&#34;&#34;&#34;
        return self._inventory_id</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activateapps.inventory_manager.Asset"><code class="flex name class">
<span>class <span class="ident">Asset</span></span>
<span>(</span><span>commcell_object, inventory_name, asset_name, inventory_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent single asset in an inventory</p>
<p>Initialize an object of the Asset class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>inventory_name
(str)
&ndash;
name of the Inventory</p>
<p>asset_name
(str)
&ndash;
Name of the asset</p>
<p>inventory_id
(str)
&ndash;
id of Inventory
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Asset class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L991-L1190" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Asset():
    &#34;&#34;&#34;Class to represent single asset in an inventory&#34;&#34;&#34;

    def __init__(self, commcell_object, inventory_name, asset_name, inventory_id=None):
        &#34;&#34;&#34;Initialize an object of the Asset class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                inventory_name     (str)        --  name of the Inventory

                asset_name          (str)       --  Name of the asset

                inventory_id       (str)        --  id of Inventory
                    default: None

            Returns:
                object  -   instance of the Asset class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._inventory_id = None
        self._inventory_name = inventory_name
        if not inventory_id:
            self._inventory_id = self._commcell_object.activate.inventory_manager().get(inventory_name).inventory_id
        else:
            self._inventory_id = inventory_id
        self._asset_name = asset_name
        self._asset_props = None
        self._asset_id = None
        self._crawl_start_time = None
        self._asset_type = None
        self._asset_status = None
        self._asset_name_values_props = None
        self._API_ASSETS = self._services[&#39;EDISCOVERY_ASSETS&#39;]
        self._API_ASSET = self._services[&#39;EDISCOVERY_ASSET&#39;]
        self._API_ASSET_JOBS = self._services[&#39;EDISCOVERY_ASSET_JOBS&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_properties(self):
        &#34;&#34;&#34;Returns the properties of this asset

                Args:

                    None

                Returns:

                    dict        -- Containing properties of asset

        &#34;&#34;&#34;

        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_ASSETS % self._inventory_id)
        if flag:
            if response.json() and &#39;assets&#39; in response.json():
                for asset in response.json()[&#39;assets&#39;]:
                    if asset[&#39;name&#39;].lower() == self._asset_name.lower():
                        self._asset_id = asset.get(&#39;id&#39;, &#34;&#34;)
                        self._crawl_start_time = asset.get(&#39;lastCollectionTime&#39;, 0)
                        self._asset_type = asset.get(&#39;type&#39;, &#34;&#34;)
                        self._asset_status = asset.get(&#39;status&#39;, &#34;NA&#34;)
                        self._asset_name = asset[&#39;name&#39;]
                        if self._asset_type == InventoryConstants.AssetType.FILE_SERVER.name:
                            flag, response = self._cvpysdk_obj.make_request(
                                &#39;GET&#39;, self._API_ASSET % (self._inventory_id, self._asset_id))
                            if flag:
                                if response.json() and &#39;properties&#39; in response.json():
                                    self._asset_name_values_props = response.json()[&#39;properties&#39;]
                        return asset
                return {}
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while fetching assets on inventory&#34;)
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the asset details associated with this&#34;&#34;&#34;
        self._asset_props = self._get_properties()

    def get_job_history(self):
        &#34;&#34;&#34;Returns the job history details of this asset

                Args:
                    None

                Returns:

                    list(dict)    --  containing job history details

                Raises:

                    SDKException:

                            if failed to get job history

                            if asset is not supported for this operation

        &#34;&#34;&#34;
        if self.asset_type != InventoryConstants.AssetType.IDENTITY_SERVER.name:
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Not supported other than Name Server asset type&#34;)
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_ASSET_JOBS % (self._inventory_id, self._asset_id))
        if flag:
            if response.json() and &#39;jobs&#39; in response.json():
                return response.json()[&#39;jobs&#39;]
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while fetching job history on inventory&#34;)
        else:
            self._response_not_success(response)

    def get_job_status(self):
        &#34;&#34;&#34;Returns the latest job status details of this asset

                Args:
                    None

                Returns:

                    str    --  last job status (Eg:- RUNNING / IDLE)

                Raises:

                    SDKException:

                            if failed to get job status

                             if asset is not supported for this operation

        &#34;&#34;&#34;
        if self.asset_type != InventoryConstants.AssetType.IDENTITY_SERVER.name:
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Not supported other than Name Server asset type&#34;)
        self.refresh()  # do refresh before getting current status
        return self.asset_status

    def get_asset_prop(self, prop_name):
        &#34;&#34;&#34;returns the property value for given property name for this asset

            Args:

                prop_name       (str)       --  Name of the property

            Returns:

                str     --  Value of the property


        &#34;&#34;&#34;
        for prop in self._asset_name_values_props:
            name = prop[&#39;name&#39;]
            if name == prop_name:
                return prop[&#39;value&#39;]
        return &#34;&#34;

    @property
    def asset_id(self):
        &#34;&#34;&#34;Returns the asset id with this asset&#34;&#34;&#34;
        return self._asset_id

    @property
    def asset_name(self):
        &#34;&#34;&#34;Returns the asset name with this asset&#34;&#34;&#34;
        return self._asset_name

    @property
    def crawl_start_time(self):
        &#34;&#34;&#34;Returns the crawl start time with this asset&#34;&#34;&#34;
        return self._crawl_start_time

    @property
    def asset_type(self):
        &#34;&#34;&#34;Returns the asset type for this asset&#34;&#34;&#34;
        return self._asset_type

    @property
    def asset_status(self):
        &#34;&#34;&#34;Returns the asset status for this asset&#34;&#34;&#34;
        return self._asset_status

    @property
    def asset_props(self):
        &#34;&#34;&#34;Returns the property values for this asset&#34;&#34;&#34;
        return self._asset_name_values_props

    @property
    def inventory_id(self):
        &#34;&#34;&#34;Returns the inventory id for this asset&#34;&#34;&#34;
        return self._inventory_id</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.asset_id"><code class="name">var <span class="ident">asset_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the asset id with this asset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1157-L1160" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def asset_id(self):
    &#34;&#34;&#34;Returns the asset id with this asset&#34;&#34;&#34;
    return self._asset_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.asset_name"><code class="name">var <span class="ident">asset_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the asset name with this asset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1162-L1165" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def asset_name(self):
    &#34;&#34;&#34;Returns the asset name with this asset&#34;&#34;&#34;
    return self._asset_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.asset_props"><code class="name">var <span class="ident">asset_props</span></code></dt>
<dd>
<div class="desc"><p>Returns the property values for this asset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1182-L1185" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def asset_props(self):
    &#34;&#34;&#34;Returns the property values for this asset&#34;&#34;&#34;
    return self._asset_name_values_props</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.asset_status"><code class="name">var <span class="ident">asset_status</span></code></dt>
<dd>
<div class="desc"><p>Returns the asset status for this asset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1177-L1180" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def asset_status(self):
    &#34;&#34;&#34;Returns the asset status for this asset&#34;&#34;&#34;
    return self._asset_status</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.asset_type"><code class="name">var <span class="ident">asset_type</span></code></dt>
<dd>
<div class="desc"><p>Returns the asset type for this asset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1172-L1175" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def asset_type(self):
    &#34;&#34;&#34;Returns the asset type for this asset&#34;&#34;&#34;
    return self._asset_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.crawl_start_time"><code class="name">var <span class="ident">crawl_start_time</span></code></dt>
<dd>
<div class="desc"><p>Returns the crawl start time with this asset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1167-L1170" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def crawl_start_time(self):
    &#34;&#34;&#34;Returns the crawl start time with this asset&#34;&#34;&#34;
    return self._crawl_start_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.inventory_id"><code class="name">var <span class="ident">inventory_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the inventory id for this asset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1187-L1190" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def inventory_id(self):
    &#34;&#34;&#34;Returns the inventory id for this asset&#34;&#34;&#34;
    return self._inventory_id</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.get_asset_prop"><code class="name flex">
<span>def <span class="ident">get_asset_prop</span></span>(<span>self, prop_name)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the property value for given property name for this asset</p>
<h2 id="args">Args</h2>
<p>prop_name
(str)
&ndash;
Name of the property</p>
<h2 id="returns">Returns</h2>
<p>str
&ndash;
Value of the property</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1138-L1155" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_asset_prop(self, prop_name):
    &#34;&#34;&#34;returns the property value for given property name for this asset

        Args:

            prop_name       (str)       --  Name of the property

        Returns:

            str     --  Value of the property


    &#34;&#34;&#34;
    for prop in self._asset_name_values_props:
        name = prop[&#39;name&#39;]
        if name == prop_name:
            return prop[&#39;value&#39;]
    return &#34;&#34;</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.get_job_history"><code class="name flex">
<span>def <span class="ident">get_job_history</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the job history details of this asset</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>list(dict)
&ndash;
containing job history details</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to get job history

    if asset is not supported for this operation
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1084-L1112" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_job_history(self):
    &#34;&#34;&#34;Returns the job history details of this asset

            Args:
                None

            Returns:

                list(dict)    --  containing job history details

            Raises:

                SDKException:

                        if failed to get job history

                        if asset is not supported for this operation

    &#34;&#34;&#34;
    if self.asset_type != InventoryConstants.AssetType.IDENTITY_SERVER.name:
        raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Not supported other than Name Server asset type&#34;)
    flag, response = self._cvpysdk_obj.make_request(
        &#39;GET&#39;, self._API_ASSET_JOBS % (self._inventory_id, self._asset_id))
    if flag:
        if response.json() and &#39;jobs&#39; in response.json():
            return response.json()[&#39;jobs&#39;]
        raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while fetching job history on inventory&#34;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.get_job_status"><code class="name flex">
<span>def <span class="ident">get_job_status</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the latest job status details of this asset</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>str
&ndash;
last job status (Eg:- RUNNING / IDLE)</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to get job status

     if asset is not supported for this operation
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1114-L1136" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_job_status(self):
    &#34;&#34;&#34;Returns the latest job status details of this asset

            Args:
                None

            Returns:

                str    --  last job status (Eg:- RUNNING / IDLE)

            Raises:

                SDKException:

                        if failed to get job status

                         if asset is not supported for this operation

    &#34;&#34;&#34;
    if self.asset_type != InventoryConstants.AssetType.IDENTITY_SERVER.name:
        raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Not supported other than Name Server asset type&#34;)
    self.refresh()  # do refresh before getting current status
    return self.asset_status</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Asset.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the asset details associated with this</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L1080-L1082" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the asset details associated with this&#34;&#34;&#34;
    self._asset_props = self._get_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Assets"><code class="flex name class">
<span>class <span class="ident">Assets</span></span>
<span>(</span><span>commcell_object, inventory_name, inventory_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent all assets in an inventory</p>
<p>Initialize an object of the Assets class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>inventory_name
(str)
&ndash;
name of the Inventory</p>
<p>inventory_id
(str)
&ndash;
id of Inventory
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Assets class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L770-L988" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Assets():
    &#34;&#34;&#34;Class to represent all assets in an inventory&#34;&#34;&#34;

    def __init__(self, commcell_object, inventory_name, inventory_id=None):
        &#34;&#34;&#34;Initialize an object of the Assets class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                inventory_name     (str)        --  name of the Inventory

                inventory_id       (str)        --  id of Inventory
                    default: None

            Returns:
                object  -   instance of the Assets class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._inventory_id = None
        self._inventory_name = inventory_name
        if not inventory_id:
            self._inventory_id = self._commcell_object.activate.inventory_manager().get(inventory_name).inventory_id
        else:
            self._inventory_id = inventory_id
        self._assets = None
        self._API_ASSETS = self._services[&#39;EDISCOVERY_ASSETS&#39;]
        self._API_ASSET = self._services[&#39;EDISCOVERY_ASSET&#39;]
        self.refresh()

    def _get_assets_properties(self):
        &#34;&#34;&#34;gets the assets properties from inventory

                    Args:
                        None

                    Returns:

                        dict    --  containing asset properties

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_ASSETS % self._inventory_id)
        if flag:
            if response.json() and &#39;assets&#39; in response.json():
                assets = {}
                for asset in response.json()[&#39;assets&#39;]:
                    name = asset[&#39;name&#39;].lower()
                    assets[name] = asset
                return assets
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while fetching assets on inventory&#34;)
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the assets details associated with this inventory&#34;&#34;&#34;
        self._assets = self._get_assets_properties()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def has_asset(self, asset_name):
        &#34;&#34;&#34;Checks whether given asset exists in inventory or not

                        Args:

                            asset_name      (str)       --  Name of the asset

                        Returns:

                            bool      --  true if asset exists else false
        &#34;&#34;&#34;
        return self._assets and asset_name.lower() in self._assets

    def get(self, asset_name):
        &#34;&#34;&#34;Returns the asset object

                Args:

                    asset_name      (str)       --  Name of the asset

                Returns:

                    object      -- Instance of Asset class

                Raises:

                    SDKException:

                            if input is not valid

                            if asset doesn&#39;t exists in inventory
        &#34;&#34;&#34;
        if not isinstance(asset_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        if not self.has_asset(asset_name):
            raise SDKException(&#39;Inventory&#39;, &#39;109&#39;)
        return Asset(self._commcell_object, self._inventory_name, asset_name, self._inventory_id)

    def add(self, asset_name, asset_type=InventoryConstants.AssetType.IDENTITY_SERVER, **kwargs):
        &#34;&#34;&#34;Adds asset to the inventory

                Args:

                    asset_name          (str)       --  Name of the asset

                    asset_type          (Enum)      --  type of asset (Refer to InventoryConstants.AssetType class)

                    kwargs for FILE SERVER type Asset:

                            fqdn                --  File server FQDN

                            os                  --  File Server OS type
                                                        (Default:Windows)

                            ip                  --  File server IP

                            country_code        --  Country code (ISO 3166 2-letter code)

                Returns:

                    object  --  Instance of Asset class

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to add asset to inventory

        &#34;&#34;&#34;
        if not isinstance(asset_name, str) or not isinstance(asset_type, InventoryConstants.AssetType):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        request_json = {}
        if asset_type.value == InventoryConstants.AssetType.FILE_SERVER.value:
            for prop in InventoryConstants.ASSET_FILE_SERVER_PROPERTY:
                prop_name = InventoryConstants.FIELD_PROPS_MAPPING[prop]
                default_value = &#34;&#34;
                if prop_name not in kwargs:
                    # always use asset name as file server name
                    if prop == InventoryConstants.FIELD_PROPERTY_NAME:
                        default_value = asset_name
                    if prop == InventoryConstants.FIELD_PROPERTY_OS:
                        default_value = &#34;Windows&#34;
                request_json[prop] = kwargs.get(prop_name, default_value)
        else:
            request_json = copy.deepcopy(InventoryConstants.IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON)
            request_json[&#39;identityServers&#39;] = [asset_name]

        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, self._API_ASSETS % self._inventory_id, request_json)
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to add asset to inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                self.refresh()
                return Asset(self._commcell_object, self._inventory_name, asset_name, self._inventory_id)
            raise SDKException(&#39;Inventory&#39;, &#39;108&#39;)
        self._response_not_success(response)

    def delete(self, asset_name):
        &#34;&#34;&#34;Delete the asset from the inventory

                Args:

                    asset_name      (str)       --  Name of the asset

                Returns:

                    None

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to delete the asset

                            if unable to find this asset in inventory
        &#34;&#34;&#34;

        if not isinstance(asset_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        if not self.has_asset(asset_name):
            raise SDKException(&#39;Inventory&#39;, &#39;109&#39;)
        flag, response = self._cvpysdk_obj.make_request(
            &#39;DELETE&#39;, self._API_ASSET % (self._inventory_id, self._assets[asset_name.lower()][&#39;id&#39;]))
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to delete asset from inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                self.refresh()
                return
            raise SDKException(&#39;Inventory&#39;, &#39;110&#39;)
        self._response_not_success(response)

    @property
    def assets(self):
        &#34;&#34;&#34;Returns the assets details associated with this inventory&#34;&#34;&#34;
        return self._assets</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.inventory_manager.Assets.assets"><code class="name">var <span class="ident">assets</span></code></dt>
<dd>
<div class="desc"><p>Returns the assets details associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L985-L988" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def assets(self):
    &#34;&#34;&#34;Returns the assets details associated with this inventory&#34;&#34;&#34;
    return self._assets</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.inventory_manager.Assets.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, asset_name, asset_type=AssetType.IDENTITY_SERVER, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds asset to the inventory</p>
<h2 id="args">Args</h2>
<p>asset_name
(str)
&ndash;
Name of the asset</p>
<p>asset_type
(Enum)
&ndash;
type of asset (Refer to InventoryConstants.AssetType class)</p>
<p>kwargs for FILE SERVER type Asset:</p>
<pre><code>    fqdn                --  File server FQDN

    os                  --  File Server OS type
                                (Default:Windows)

    ip                  --  File server IP

    country_code        --  Country code (ISO 3166 2-letter code)
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
Instance of Asset class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if input is not valid

    if failed to add asset to inventory
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L880-L943" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, asset_name, asset_type=InventoryConstants.AssetType.IDENTITY_SERVER, **kwargs):
    &#34;&#34;&#34;Adds asset to the inventory

            Args:

                asset_name          (str)       --  Name of the asset

                asset_type          (Enum)      --  type of asset (Refer to InventoryConstants.AssetType class)

                kwargs for FILE SERVER type Asset:

                        fqdn                --  File server FQDN

                        os                  --  File Server OS type
                                                    (Default:Windows)

                        ip                  --  File server IP

                        country_code        --  Country code (ISO 3166 2-letter code)

            Returns:

                object  --  Instance of Asset class

            Raises:

                SDKException:

                        if input is not valid

                        if failed to add asset to inventory

    &#34;&#34;&#34;
    if not isinstance(asset_name, str) or not isinstance(asset_type, InventoryConstants.AssetType):
        raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
    request_json = {}
    if asset_type.value == InventoryConstants.AssetType.FILE_SERVER.value:
        for prop in InventoryConstants.ASSET_FILE_SERVER_PROPERTY:
            prop_name = InventoryConstants.FIELD_PROPS_MAPPING[prop]
            default_value = &#34;&#34;
            if prop_name not in kwargs:
                # always use asset name as file server name
                if prop == InventoryConstants.FIELD_PROPERTY_NAME:
                    default_value = asset_name
                if prop == InventoryConstants.FIELD_PROPERTY_OS:
                    default_value = &#34;Windows&#34;
            request_json[prop] = kwargs.get(prop_name, default_value)
    else:
        request_json = copy.deepcopy(InventoryConstants.IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON)
        request_json[&#39;identityServers&#39;] = [asset_name]

    flag, response = self._cvpysdk_obj.make_request(
        &#39;PUT&#39;, self._API_ASSETS % self._inventory_id, request_json)
    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;Inventory&#39;,
                    &#39;102&#39;,
                    f&#34;Failed to add asset to inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
            self.refresh()
            return Asset(self._commcell_object, self._inventory_name, asset_name, self._inventory_id)
        raise SDKException(&#39;Inventory&#39;, &#39;108&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Assets.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, asset_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Delete the asset from the inventory</p>
<h2 id="args">Args</h2>
<p>asset_name
(str)
&ndash;
Name of the asset</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if input is not valid

    if failed to delete the asset

    if unable to find this asset in inventory
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L945-L983" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, asset_name):
    &#34;&#34;&#34;Delete the asset from the inventory

            Args:

                asset_name      (str)       --  Name of the asset

            Returns:

                None

            Raises:

                SDKException:

                        if input is not valid

                        if failed to delete the asset

                        if unable to find this asset in inventory
    &#34;&#34;&#34;

    if not isinstance(asset_name, str):
        raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
    if not self.has_asset(asset_name):
        raise SDKException(&#39;Inventory&#39;, &#39;109&#39;)
    flag, response = self._cvpysdk_obj.make_request(
        &#39;DELETE&#39;, self._API_ASSET % (self._inventory_id, self._assets[asset_name.lower()][&#39;id&#39;]))
    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;Inventory&#39;,
                    &#39;102&#39;,
                    f&#34;Failed to delete asset from inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
            self.refresh()
            return
        raise SDKException(&#39;Inventory&#39;, &#39;110&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Assets.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, asset_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the asset object</p>
<h2 id="args">Args</h2>
<p>asset_name
(str)
&ndash;
Name of the asset</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash; Instance of Asset class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if input is not valid

    if asset doesn't exists in inventory
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L855-L878" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, asset_name):
    &#34;&#34;&#34;Returns the asset object

            Args:

                asset_name      (str)       --  Name of the asset

            Returns:

                object      -- Instance of Asset class

            Raises:

                SDKException:

                        if input is not valid

                        if asset doesn&#39;t exists in inventory
    &#34;&#34;&#34;
    if not isinstance(asset_name, str):
        raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
    if not self.has_asset(asset_name):
        raise SDKException(&#39;Inventory&#39;, &#39;109&#39;)
    return Asset(self._commcell_object, self._inventory_name, asset_name, self._inventory_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Assets.has_asset"><code class="name flex">
<span>def <span class="ident">has_asset</span></span>(<span>self, asset_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks whether given asset exists in inventory or not</p>
<h2 id="args">Args</h2>
<p>asset_name
(str)
&ndash;
Name of the asset</p>
<h2 id="returns">Returns</h2>
<p>bool
&ndash;
true if asset exists else false</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L842-L853" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_asset(self, asset_name):
    &#34;&#34;&#34;Checks whether given asset exists in inventory or not

                    Args:

                        asset_name      (str)       --  Name of the asset

                    Returns:

                        bool      --  true if asset exists else false
    &#34;&#34;&#34;
    return self._assets and asset_name.lower() in self._assets</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Assets.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the assets details associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L827-L829" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the assets details associated with this inventory&#34;&#34;&#34;
    self._assets = self._get_assets_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventories"><code class="flex name class">
<span>class <span class="ident">Inventories</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all inventories in the commcell.</p>
<p>Initializes an instance of the Inventories class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Inventories class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L163-L388" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Inventories():
    &#34;&#34;&#34;Class for representing all inventories in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Inventories class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Inventories class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._inventories = None
        self._API_INVENTORIES = self._services[&#39;EDISCOVERY_INVENTORIES&#39;]
        self._API_DELETE_INVENTORY = self._services[&#39;EDISCOVERY_INVENTORY&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_inventories(self):
        &#34;&#34;&#34;Gets all inventories from the commcell

            Args:

                None

            Return:

                list(dict)        --  list Containing inventory details dict

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        output = {}
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_INVENTORIES
        )
        if flag:
            if response.json() and &#39;inventories&#39; in response.json():
                inventories = response.json()[&#39;inventories&#39;]
                for inventory in inventories:
                    output[inventory[&#39;displayName&#39;].lower()] = inventory
                return output
            raise SDKException(&#39;Inventory&#39;, &#39;103&#39;)
        self._response_not_success(response)

    def add(self, inventory_name, index_server, name_server=None):
        &#34;&#34;&#34;Adds inventory to the commcell with given inputs

                Args:

                    inventory_name              (str)       --  Name of the inventory

                    index_server                (str)       --  Index server name

                    name_server                 (list)      --  Name server assets which needs to be added to inventory

                Returns:

                    object  --  Instance of Inventory Class

                Raises:

                    SDKException:

                            if input data type is not valid

                            if failed to add inventory

                            if Index Server doesn&#39;t exists in commcell

        &#34;&#34;&#34;
        if not isinstance(inventory_name, str) or not isinstance(index_server, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        req_json = copy.deepcopy(InventoryConstants.INVENTORY_ADD_REQUEST_JSON)
        if name_server:
            req_json[&#39;identityServers&#39;] = name_server
        req_json[&#39;name&#39;] = inventory_name
        if not self._commcell_object.index_servers.has(index_server):
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Given index server name not exists on this commcell&#34;)
        index_server_obj = self._commcell_object.index_servers.get(index_server)
        req_json[&#39;indexServer&#39;][&#39;cloudId&#39;] = index_server_obj.cloud_id
        req_json[&#39;indexServer&#39;][&#39;displayName&#39;] = index_server_obj.cloud_name
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_INVENTORIES, req_json
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to create inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                elif &#39;name&#39; in response.json():
                    inventory = response.json()[&#39;name&#39;]
                    inventory_id = response.json()[&#39;id&#39;]
                    self.refresh()
                    return Inventory(self._commcell_object, inventory_name, inventory_id)
                raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, f&#34;Failed to create inventory with response - {response.json()}&#34;)
            raise SDKException(&#39;Inventory&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def delete(self, inventory_name):
        &#34;&#34;&#34;Deletes the inventory from the commcell

                Args:

                    inventory_name      (str)       --  Inventory name to be deleted

                Returns:
                    None

                Raises:

                    SDKException:

                            if unable to find inventory

                            if failed to delete inventory

                            if input type is not valid

        &#34;&#34;&#34;
        if not isinstance(inventory_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        if not self.has_inventory(inventory_name):
            raise SDKException(&#39;Inventory&#39;, &#39;106&#39;)
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._API_DELETE_INVENTORY % self._inventories[inventory_name.lower()][&#39;id&#39;]
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to Delete inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                self.refresh()
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;107&#39;)
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the inventories associated with the commcell.&#34;&#34;&#34;
        self._inventories = self._get_inventories()

    def get_properties(self, inventory_name):
        &#34;&#34;&#34;Returns a properties of the specified Inventory

            Args:
                inventory_name (str)  --  name of the inventory

            Returns:
                dict -  properties for the given inventory name


        &#34;&#34;&#34;
        return self._inventories[inventory_name.lower()]

    def has_inventory(self, inventory_name):
        &#34;&#34;&#34;Checks if a inventory exists in the commcell with the input name.

            Args:
                inventory_name (str)  --  name of the inventory

            Returns:
                bool - boolean output to specify whether the inventory exists in the commcell or not

            Raises:
                SDKException:
                    if type of the inventory name argument is not string

        &#34;&#34;&#34;
        if not isinstance(inventory_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)

        return self._inventories and inventory_name.lower() in map(str.lower, self._inventories)

    def get(self, inventory_name):
        &#34;&#34;&#34;Returns a Inventory object for the given inventory name.

            Args:
                inventory_name (str)  --  name of the inventory

            Returns:

                obj                 -- Object of Inventory class

            Raises:

                SDKException:

                    if inventory doesn&#39;t exists in commcell

                    if inventory_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(inventory_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)

        if self.has_inventory(inventory_name):
            inventory_id = self._inventories[inventory_name.lower()][&#39;id&#39;]
            return Inventory(self._commcell_object, inventory_name, inventory_id)
        raise SDKException(&#39;Inventory&#39;, &#39;106&#39;)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.inventory_manager.Inventories.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, inventory_name, index_server, name_server=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds inventory to the commcell with given inputs</p>
<h2 id="args">Args</h2>
<p>inventory_name
(str)
&ndash;
Name of the inventory</p>
<p>index_server
(str)
&ndash;
Index server name</p>
<p>name_server
(list)
&ndash;
Name server assets which needs to be added to inventory</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
Instance of Inventory Class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if input data type is not valid

    if failed to add inventory

    if Index Server doesn't exists in commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L229-L283" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, inventory_name, index_server, name_server=None):
    &#34;&#34;&#34;Adds inventory to the commcell with given inputs

            Args:

                inventory_name              (str)       --  Name of the inventory

                index_server                (str)       --  Index server name

                name_server                 (list)      --  Name server assets which needs to be added to inventory

            Returns:

                object  --  Instance of Inventory Class

            Raises:

                SDKException:

                        if input data type is not valid

                        if failed to add inventory

                        if Index Server doesn&#39;t exists in commcell

    &#34;&#34;&#34;
    if not isinstance(inventory_name, str) or not isinstance(index_server, str):
        raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
    req_json = copy.deepcopy(InventoryConstants.INVENTORY_ADD_REQUEST_JSON)
    if name_server:
        req_json[&#39;identityServers&#39;] = name_server
    req_json[&#39;name&#39;] = inventory_name
    if not self._commcell_object.index_servers.has(index_server):
        raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Given index server name not exists on this commcell&#34;)
    index_server_obj = self._commcell_object.index_servers.get(index_server)
    req_json[&#39;indexServer&#39;][&#39;cloudId&#39;] = index_server_obj.cloud_id
    req_json[&#39;indexServer&#39;][&#39;displayName&#39;] = index_server_obj.cloud_name
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_INVENTORIES, req_json
    )
    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;Inventory&#39;,
                    &#39;102&#39;,
                    f&#34;Failed to create inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
            elif &#39;name&#39; in response.json():
                inventory = response.json()[&#39;name&#39;]
                inventory_id = response.json()[&#39;id&#39;]
                self.refresh()
                return Inventory(self._commcell_object, inventory_name, inventory_id)
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, f&#34;Failed to create inventory with response - {response.json()}&#34;)
        raise SDKException(&#39;Inventory&#39;, &#39;105&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventories.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, inventory_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the inventory from the commcell</p>
<h2 id="args">Args</h2>
<p>inventory_name
(str)
&ndash;
Inventory name to be deleted</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if unable to find inventory

    if failed to delete inventory

    if input type is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L285-L324" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, inventory_name):
    &#34;&#34;&#34;Deletes the inventory from the commcell

            Args:

                inventory_name      (str)       --  Inventory name to be deleted

            Returns:
                None

            Raises:

                SDKException:

                        if unable to find inventory

                        if failed to delete inventory

                        if input type is not valid

    &#34;&#34;&#34;
    if not isinstance(inventory_name, str):
        raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
    if not self.has_inventory(inventory_name):
        raise SDKException(&#39;Inventory&#39;, &#39;106&#39;)
    flag, response = self._cvpysdk_object.make_request(
        &#39;DELETE&#39;, self._API_DELETE_INVENTORY % self._inventories[inventory_name.lower()][&#39;id&#39;]
    )
    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;Inventory&#39;,
                    &#39;102&#39;,
                    f&#34;Failed to Delete inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
            self.refresh()
        else:
            raise SDKException(&#39;Inventory&#39;, &#39;107&#39;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventories.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, inventory_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a Inventory object for the given inventory name.</p>
<h2 id="args">Args</h2>
<p>inventory_name (str)
&ndash;
name of the inventory</p>
<h2 id="returns">Returns</h2>
<p>obj
&ndash; Object of Inventory class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if inventory doesn't exists in commcell

if inventory_name is not of type string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L362-L388" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, inventory_name):
    &#34;&#34;&#34;Returns a Inventory object for the given inventory name.

        Args:
            inventory_name (str)  --  name of the inventory

        Returns:

            obj                 -- Object of Inventory class

        Raises:

            SDKException:

                if inventory doesn&#39;t exists in commcell

                if inventory_name is not of type string


    &#34;&#34;&#34;
    if not isinstance(inventory_name, str):
        raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)

    if self.has_inventory(inventory_name):
        inventory_id = self._inventories[inventory_name.lower()][&#39;id&#39;]
        return Inventory(self._commcell_object, inventory_name, inventory_id)
    raise SDKException(&#39;Inventory&#39;, &#39;106&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventories.get_properties"><code class="name flex">
<span>def <span class="ident">get_properties</span></span>(<span>self, inventory_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a properties of the specified Inventory</p>
<h2 id="args">Args</h2>
<p>inventory_name (str)
&ndash;
name of the inventory</p>
<h2 id="returns">Returns</h2>
<p>dict -
properties for the given inventory name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L330-L341" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_properties(self, inventory_name):
    &#34;&#34;&#34;Returns a properties of the specified Inventory

        Args:
            inventory_name (str)  --  name of the inventory

        Returns:
            dict -  properties for the given inventory name


    &#34;&#34;&#34;
    return self._inventories[inventory_name.lower()]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventories.has_inventory"><code class="name flex">
<span>def <span class="ident">has_inventory</span></span>(<span>self, inventory_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a inventory exists in the commcell with the input name.</p>
<h2 id="args">Args</h2>
<p>inventory_name (str)
&ndash;
name of the inventory</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output to specify whether the inventory exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the inventory name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L343-L360" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_inventory(self, inventory_name):
    &#34;&#34;&#34;Checks if a inventory exists in the commcell with the input name.

        Args:
            inventory_name (str)  --  name of the inventory

        Returns:
            bool - boolean output to specify whether the inventory exists in the commcell or not

        Raises:
            SDKException:
                if type of the inventory name argument is not string

    &#34;&#34;&#34;
    if not isinstance(inventory_name, str):
        raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)

    return self._inventories and inventory_name.lower() in map(str.lower, self._inventories)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventories.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the inventories associated with the commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L326-L328" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the inventories associated with the commcell.&#34;&#34;&#34;
    self._inventories = self._get_inventories()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory"><code class="flex name class">
<span>class <span class="ident">Inventory</span></span>
<span>(</span><span>commcell_object, inventory_name, inventory_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on a single inventory</p>
<p>Initialize an object of the Inventory class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>inventory_name
(str)
&ndash;
name of the Inventory</p>
<p>inventory_id
(str)
&ndash;
id of Inventory
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Inventory class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L391-L767" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Inventory():
    &#34;&#34;&#34;Class for performing operations on a single inventory&#34;&#34;&#34;

    def __init__(self, commcell_object, inventory_name, inventory_id=None):
        &#34;&#34;&#34;Initialize an object of the Inventory class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                inventory_name     (str)        --  name of the Inventory

                inventory_id       (str)        --  id of Inventory
                    default: None

            Returns:
                object  -   instance of the Inventory class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._inventory_id = None
        self._inventory_name = inventory_name
        self._inventory_props = None
        self._index_server_name = None
        self._index_server_cloud_id = None
        self._security_associations = None
        self._schedule = None
        self._data_source = None
        self._handler = None
        self._API_GET_INVENTORY_DETAILS = self._services[&#39;EDISCOVERY_INVENTORY&#39;]
        self._API_SECURITY_ENTITY = self._services[&#39;ENTITY_SECURITY_ASSOCIATION&#39;]
        self._API_GET_DEFAULT_HANDLER = self._services[&#39;EDISCOVERY_GET_DEFAULT_HANDLER&#39;]
        self._API_PERMISSION = self._services[&#39;V4_ACTIVATE_DS_PERMISSION&#39;]
        self._API_CRAWL = self._services[&#39;V4_INVENTORY_CRAWL&#39;]

        if not inventory_id:
            self._inventory_id = self._commcell_object.activate.inventory_manager().get(inventory_name).inventory_id
        else:
            self._inventory_id = inventory_id
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_permission(self):
        &#34;&#34;&#34;returns security association blob for this inventory

                Args:

                    None

                Returns:

                    dict    --  Security association blob

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_PERMISSION % self._inventory_id
        )
        if flag:
            if response.json() and &#39;securityAssociations&#39; in response.json():
                return response.json()[&#39;securityAssociations&#39;]
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Inventory permission fetch failed&#34;)
        self._response_not_success(response)

    def _get_inventory_properties(self):
        &#34;&#34;&#34; Get inventory properties from the commcell
                Args:

                    None

                Returns:

                    dict        --  Properties of inventory

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_GET_INVENTORY_DETAILS % self._inventory_id
        )
        if flag:
            if response.json():
                inventory_props = response.json()
                self._index_server_name = inventory_props[&#39;indexServer&#39;][&#39;displayName&#39;]
                self._index_server_cloud_id = inventory_props[&#39;indexServer&#39;][&#39;cloudId&#39;]
                self._inventory_name = inventory_props[&#39;displayName&#39;]
                return inventory_props
            raise SDKException(&#39;Inventory&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the inventory details for associated object&#34;&#34;&#34;
        self._inventory_props = self._get_inventory_properties()
        self._schedule = self._get_schedule_object()
        self._data_source, self._handler = self._get_data_source_handler_object()
        self._security_associations = self._get_permission()

    def get_assets(self):
        &#34;&#34;&#34;Returns the Assets class instance for this inventory

                Args:

                    None

                Returns:

                    object      --  Instance of Assets class

        &#34;&#34;&#34;
        return Assets(self._commcell_object, self.inventory_name, self.inventory_id)

    def start_collection(self):
        &#34;&#34;&#34;Starts collection job on this inventory

                Args:

                    None

                Return:

                    None

                Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, self._API_CRAWL % self._inventory_id
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to start crawl on inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
                return
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while starting collection job on inventory&#34;)
        else:
            self._response_not_success(response)

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares inventory with given user or user group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        if not isinstance(user_or_group_name, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(InventoryConstants.INVENTORY_SHARE_REQUEST_JSON)
        external_user = False
        association_response = None
        if ops_type == 1 and len(self.security_associations) &gt; 1:
            association_request_json = copy.deepcopy(InventoryConstants.INVENTORY_SHARE_REQUEST_JSON)
            del association_request_json[&#39;securityAssociations&#39;]
            association_request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;seaDataSourceId&#39;] = int(self.inventory_id)
            # get security blob for this data source type entity - 132
            flag, response = self._cvpysdk_obj.make_request(
                &#39;GET&#39;, self._API_SECURITY_ENTITY % (132, int(self.inventory_id)), association_request_json
            )
            if flag:
                if response.json() and &#39;securityAssociations&#39; in response.json():
                    association_response = response.json(
                    )[&#39;securityAssociations&#39;][0][&#39;securityAssociations&#39;][&#39;associations&#39;]
                else:
                    raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#39;Failed to get existing security associations&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if &#39;\\&#39; in user_or_group_name:
            external_user = True
        if is_user:
            user_obj = self._commcell_object.users.get(user_or_group_name)
            user_id = user_obj.user_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userId&#39;] = int(user_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 13
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userName&#39;] = user_or_group_name
        elif external_user:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;groupId&#39;] = 0
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 62
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
                &#39;externalGroupName&#39;] = user_or_group_name
        else:
            grp_obj = self._commcell_object.user_groups.get(user_or_group_name)
            grp_id = grp_obj.user_group_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupId&#39;] = int(grp_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 15
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
                &#39;userGroupName&#39;] = user_or_group_name

        request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;seaDataSourceId&#39;] = self.inventory_id
        request_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = ops_type

        if allow_edit_permission:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;].append(
                InventoryConstants.EDIT_CATEGORY_PERMISSION)

            # Associate existing associations to the request
        if ops_type == 1 and len(self.security_associations) &gt; 1:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;].extend(association_response)

        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, self._API_PERMISSION % self._inventory_id, request_json
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]
                if error_code != 0:
                    error_message = response.json()[&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Inventory&#39;,
                        &#39;102&#39;, error_message)
                # update association list by refreshing inventory
                self.refresh()
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;111&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_data_source_handler_object(self):
        &#34;&#34;&#34;returns the data source and handler object associated with this inventory

                Args:
                    None

                Returns:
                    obj,obj     --  Instance of DataSource object,Instance of Handler object

                Raises:

                    SDKException:

                            if failed to get datasource or handler details
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_GET_DEFAULT_HANDLER % self.inventory_id)
        if flag:
            if response.json() and &#39;handlerInfos&#39; in response.json():
                handler_list = response.json()[&#39;handlerInfos&#39;]
                if not isinstance(handler_list, list):
                    raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Failed to get Datasource/Handler details&#34;)
                handler_details = handler_list[0]
                ds_name = handler_details[&#39;dataSourceName&#39;]
                handler_name = handler_details[&#39;handlerName&#39;]
                ds_obj = self._commcell_object.datacube.datasources.get(ds_name)
                handler_obj = ds_obj.ds_handlers.get(handler_name)
                return ds_obj, handler_obj
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#39;Unknown response while fetching datasource details&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_schedule_object(self):
        &#34;&#34;&#34;Returns the schedule class object of schedule associated to this inventory

                Args:

                    None

                Returns:

                    object      --  Instance of Schedule class

                Raises:

                    SDKException:

                            if failed to find schedule
        &#34;&#34;&#34;
        return Schedules(self).get()

    def get_inventory_data(self, handler_filter=&#34;&#34;):
        &#34;&#34;&#34; Executes handler for fetching data from inventory
                Args:

                     handler_filter    (str)  -- Filter which needs to applied for handler execution

                Returns:

                    dict        --  Dictionary of values fetched from handler execution

                Raises:

                    SDKExpception:

                        if error in fetching handler data

                        if input is not valid
        &#34;&#34;&#34;
        if not isinstance(handler_filter, str):
            raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
        if not self._handler:
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#39;No handler object initialised&#39;)
        return self._handler.get_handler_data(handler_filter=handler_filter)

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the properties of this inventory as dict&#34;&#34;&#34;
        return self._inventory_props

    @property
    def index_server_name(self):
        &#34;&#34;&#34;Returns the index server name associated with this inventory&#34;&#34;&#34;
        return self._index_server_name

    @property
    def index_server_cloud_id(self):
        &#34;&#34;&#34;Returns the index server id associated with this inventory&#34;&#34;&#34;
        return self._index_server_cloud_id

    @property
    def inventory_id(self):
        &#34;&#34;&#34;Returns the inventory id associated with this inventory&#34;&#34;&#34;
        return self._inventory_id

    @property
    def inventory_name(self):
        &#34;&#34;&#34;Returns the inventory name associated with this inventory&#34;&#34;&#34;
        return self._inventory_name

    @property
    def security_associations(self):
        &#34;&#34;&#34;Returns the security blob associated with this inventory&#34;&#34;&#34;
        return self._security_associations

    @property
    def schedule(self):
        &#34;&#34;&#34;Returns the schedule class object for schedule associated with this inventory&#34;&#34;&#34;
        return self._schedule

    @property
    def data_source(self):
        &#34;&#34;&#34;Returns the DataSource class object for datasource associated with this inventory&#34;&#34;&#34;
        return self._data_source

    @property
    def handler(self):
        &#34;&#34;&#34;Returns the Handler class object for default handler associated with this inventory&#34;&#34;&#34;
        return self._handler</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.data_source"><code class="name">var <span class="ident">data_source</span></code></dt>
<dd>
<div class="desc"><p>Returns the DataSource class object for datasource associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L759-L762" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_source(self):
    &#34;&#34;&#34;Returns the DataSource class object for datasource associated with this inventory&#34;&#34;&#34;
    return self._data_source</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.handler"><code class="name">var <span class="ident">handler</span></code></dt>
<dd>
<div class="desc"><p>Returns the Handler class object for default handler associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L764-L767" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def handler(self):
    &#34;&#34;&#34;Returns the Handler class object for default handler associated with this inventory&#34;&#34;&#34;
    return self._handler</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.index_server_cloud_id"><code class="name">var <span class="ident">index_server_cloud_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the index server id associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L734-L737" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_server_cloud_id(self):
    &#34;&#34;&#34;Returns the index server id associated with this inventory&#34;&#34;&#34;
    return self._index_server_cloud_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.index_server_name"><code class="name">var <span class="ident">index_server_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the index server name associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L729-L732" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_server_name(self):
    &#34;&#34;&#34;Returns the index server name associated with this inventory&#34;&#34;&#34;
    return self._index_server_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.inventory_id"><code class="name">var <span class="ident">inventory_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the inventory id associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L739-L742" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def inventory_id(self):
    &#34;&#34;&#34;Returns the inventory id associated with this inventory&#34;&#34;&#34;
    return self._inventory_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.inventory_name"><code class="name">var <span class="ident">inventory_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the inventory name associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L744-L747" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def inventory_name(self):
    &#34;&#34;&#34;Returns the inventory name associated with this inventory&#34;&#34;&#34;
    return self._inventory_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.properties"><code class="name">var <span class="ident">properties</span></code></dt>
<dd>
<div class="desc"><p>Returns the properties of this inventory as dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L724-L727" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def properties(self):
    &#34;&#34;&#34;Returns the properties of this inventory as dict&#34;&#34;&#34;
    return self._inventory_props</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.schedule"><code class="name">var <span class="ident">schedule</span></code></dt>
<dd>
<div class="desc"><p>Returns the schedule class object for schedule associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L754-L757" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def schedule(self):
    &#34;&#34;&#34;Returns the schedule class object for schedule associated with this inventory&#34;&#34;&#34;
    return self._schedule</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.security_associations"><code class="name">var <span class="ident">security_associations</span></code></dt>
<dd>
<div class="desc"><p>Returns the security blob associated with this inventory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L749-L752" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def security_associations(self):
    &#34;&#34;&#34;Returns the security blob associated with this inventory&#34;&#34;&#34;
    return self._security_associations</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.get_assets"><code class="name flex">
<span>def <span class="ident">get_assets</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the Assets class instance for this inventory</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
Instance of Assets class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L496-L508" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_assets(self):
    &#34;&#34;&#34;Returns the Assets class instance for this inventory

            Args:

                None

            Returns:

                object      --  Instance of Assets class

    &#34;&#34;&#34;
    return Assets(self._commcell_object, self.inventory_name, self.inventory_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.get_inventory_data"><code class="name flex">
<span>def <span class="ident">get_inventory_data</span></span>(<span>self, handler_filter='')</span>
</code></dt>
<dd>
<div class="desc"><p>Executes handler for fetching data from inventory</p>
<h2 id="args">Args</h2>
<p>handler_filter
(str)
&ndash; Filter which needs to applied for handler execution</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash;
Dictionary of values fetched from handler execution</p>
<h2 id="raises">Raises</h2>
<p>SDKExpception:</p>
<pre><code>if error in fetching handler data

if input is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L700-L722" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_inventory_data(self, handler_filter=&#34;&#34;):
    &#34;&#34;&#34; Executes handler for fetching data from inventory
            Args:

                 handler_filter    (str)  -- Filter which needs to applied for handler execution

            Returns:

                dict        --  Dictionary of values fetched from handler execution

            Raises:

                SDKExpception:

                    if error in fetching handler data

                    if input is not valid
    &#34;&#34;&#34;
    if not isinstance(handler_filter, str):
        raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
    if not self._handler:
        raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#39;No handler object initialised&#39;)
    return self._handler.get_handler_data(handler_filter=handler_filter)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the inventory details for associated object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L489-L494" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the inventory details for associated object&#34;&#34;&#34;
    self._inventory_props = self._get_inventory_properties()
    self._schedule = self._get_schedule_object()
    self._data_source, self._handler = self._get_data_source_handler_object()
    self._security_associations = self._get_permission()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.share"><code class="name flex">
<span>def <span class="ident">share</span></span>(<span>self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Shares inventory with given user or user group in commcell</p>
<h2 id="args">Args</h2>
<p>user_or_group_name
(str)
&ndash;
Name of user or group</p>
<p>is_user
(bool)
&ndash;
Denotes whether this is user or group name
default : True(User)</p>
<p>allow_edit_permission
(bool)
&ndash;
whether to give edit permission or not to user or group</p>
<p>ops_type
(int)
&ndash;
Operation type</p>
<pre><code>                                    Default : 1 (Add)

                                    Supported : 1 (Add)
                                                3 (Delete)
</code></pre>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if unable to update security associations

    if response is empty or not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L544-L647" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
    &#34;&#34;&#34;Shares inventory with given user or user group in commcell

            Args:

                user_or_group_name      (str)       --  Name of user or group

                is_user                 (bool)      --  Denotes whether this is user or group name
                                                            default : True(User)

                allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                ops_type                (int)       --  Operation type

                                                        Default : 1 (Add)

                                                        Supported : 1 (Add)
                                                                    3 (Delete)

            Returns:

                None

            Raises:

                SDKException:

                        if unable to update security associations

                        if response is empty or not success
    &#34;&#34;&#34;
    if not isinstance(user_or_group_name, str):
        raise SDKException(&#39;Inventory&#39;, &#39;101&#39;)
    request_json = copy.deepcopy(InventoryConstants.INVENTORY_SHARE_REQUEST_JSON)
    external_user = False
    association_response = None
    if ops_type == 1 and len(self.security_associations) &gt; 1:
        association_request_json = copy.deepcopy(InventoryConstants.INVENTORY_SHARE_REQUEST_JSON)
        del association_request_json[&#39;securityAssociations&#39;]
        association_request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;seaDataSourceId&#39;] = int(self.inventory_id)
        # get security blob for this data source type entity - 132
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._API_SECURITY_ENTITY % (132, int(self.inventory_id)), association_request_json
        )
        if flag:
            if response.json() and &#39;securityAssociations&#39; in response.json():
                association_response = response.json(
                )[&#39;securityAssociations&#39;][0][&#39;securityAssociations&#39;][&#39;associations&#39;]
            else:
                raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#39;Failed to get existing security associations&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    if &#39;\\&#39; in user_or_group_name:
        external_user = True
    if is_user:
        user_obj = self._commcell_object.users.get(user_or_group_name)
        user_id = user_obj.user_id
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userId&#39;] = int(user_id)
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 13
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userName&#39;] = user_or_group_name
    elif external_user:
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;groupId&#39;] = 0
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 62
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
            &#39;externalGroupName&#39;] = user_or_group_name
    else:
        grp_obj = self._commcell_object.user_groups.get(user_or_group_name)
        grp_id = grp_obj.user_group_id
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupId&#39;] = int(grp_id)
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 15
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
            &#39;userGroupName&#39;] = user_or_group_name

    request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;seaDataSourceId&#39;] = self.inventory_id
    request_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = ops_type

    if allow_edit_permission:
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;].append(
            InventoryConstants.EDIT_CATEGORY_PERMISSION)

        # Associate existing associations to the request
    if ops_type == 1 and len(self.security_associations) &gt; 1:
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;].extend(association_response)

    flag, response = self._cvpysdk_obj.make_request(
        &#39;PUT&#39;, self._API_PERMISSION % self._inventory_id, request_json
    )
    if flag:
        if response.json():
            error_code = response.json()[&#39;errorCode&#39;]
            if error_code != 0:
                error_message = response.json()[&#39;errorString&#39;]
                raise SDKException(
                    &#39;Inventory&#39;,
                    &#39;102&#39;, error_message)
            # update association list by refreshing inventory
            self.refresh()
        else:
            raise SDKException(&#39;Inventory&#39;, &#39;111&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.inventory_manager.Inventory.start_collection"><code class="name flex">
<span>def <span class="ident">start_collection</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts collection job on this inventory</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="return">Return</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to start collection job
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/inventory_manager.py#L510-L542" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def start_collection(self):
    &#34;&#34;&#34;Starts collection job on this inventory

            Args:

                None

            Return:

                None

            Raises:

                SDKException:

                        if failed to start collection job

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_obj.make_request(
        &#39;PUT&#39;, self._API_CRAWL % self._inventory_id
    )
    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;Inventory&#39;,
                    &#39;102&#39;,
                    f&#34;Failed to start crawl on inventory with error [{response.json()[&#39;errorMessage&#39;]}]&#34;)
            return
        else:
            raise SDKException(&#39;Inventory&#39;, &#39;102&#39;, &#34;Unknown response while starting collection job on inventory&#34;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#inventory-attributes">Inventory Attributes</a></li>
<li><a href="#assets-attributes">Assets Attributes:</a></li>
<li><a href="#asset-attributes">Asset Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.activateapps" href="index.html">cvpysdk.activateapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activateapps.inventory_manager.Asset" href="#cvpysdk.activateapps.inventory_manager.Asset">Asset</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.asset_id" href="#cvpysdk.activateapps.inventory_manager.Asset.asset_id">asset_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.asset_name" href="#cvpysdk.activateapps.inventory_manager.Asset.asset_name">asset_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.asset_props" href="#cvpysdk.activateapps.inventory_manager.Asset.asset_props">asset_props</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.asset_status" href="#cvpysdk.activateapps.inventory_manager.Asset.asset_status">asset_status</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.asset_type" href="#cvpysdk.activateapps.inventory_manager.Asset.asset_type">asset_type</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.crawl_start_time" href="#cvpysdk.activateapps.inventory_manager.Asset.crawl_start_time">crawl_start_time</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.get_asset_prop" href="#cvpysdk.activateapps.inventory_manager.Asset.get_asset_prop">get_asset_prop</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.get_job_history" href="#cvpysdk.activateapps.inventory_manager.Asset.get_job_history">get_job_history</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.get_job_status" href="#cvpysdk.activateapps.inventory_manager.Asset.get_job_status">get_job_status</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.inventory_id" href="#cvpysdk.activateapps.inventory_manager.Asset.inventory_id">inventory_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Asset.refresh" href="#cvpysdk.activateapps.inventory_manager.Asset.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.inventory_manager.Assets" href="#cvpysdk.activateapps.inventory_manager.Assets">Assets</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.inventory_manager.Assets.add" href="#cvpysdk.activateapps.inventory_manager.Assets.add">add</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Assets.assets" href="#cvpysdk.activateapps.inventory_manager.Assets.assets">assets</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Assets.delete" href="#cvpysdk.activateapps.inventory_manager.Assets.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Assets.get" href="#cvpysdk.activateapps.inventory_manager.Assets.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Assets.has_asset" href="#cvpysdk.activateapps.inventory_manager.Assets.has_asset">has_asset</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Assets.refresh" href="#cvpysdk.activateapps.inventory_manager.Assets.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.inventory_manager.Inventories" href="#cvpysdk.activateapps.inventory_manager.Inventories">Inventories</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventories.add" href="#cvpysdk.activateapps.inventory_manager.Inventories.add">add</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventories.delete" href="#cvpysdk.activateapps.inventory_manager.Inventories.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventories.get" href="#cvpysdk.activateapps.inventory_manager.Inventories.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventories.get_properties" href="#cvpysdk.activateapps.inventory_manager.Inventories.get_properties">get_properties</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventories.has_inventory" href="#cvpysdk.activateapps.inventory_manager.Inventories.has_inventory">has_inventory</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventories.refresh" href="#cvpysdk.activateapps.inventory_manager.Inventories.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.inventory_manager.Inventory" href="#cvpysdk.activateapps.inventory_manager.Inventory">Inventory</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.data_source" href="#cvpysdk.activateapps.inventory_manager.Inventory.data_source">data_source</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.get_assets" href="#cvpysdk.activateapps.inventory_manager.Inventory.get_assets">get_assets</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.get_inventory_data" href="#cvpysdk.activateapps.inventory_manager.Inventory.get_inventory_data">get_inventory_data</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.handler" href="#cvpysdk.activateapps.inventory_manager.Inventory.handler">handler</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.index_server_cloud_id" href="#cvpysdk.activateapps.inventory_manager.Inventory.index_server_cloud_id">index_server_cloud_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.index_server_name" href="#cvpysdk.activateapps.inventory_manager.Inventory.index_server_name">index_server_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.inventory_id" href="#cvpysdk.activateapps.inventory_manager.Inventory.inventory_id">inventory_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.inventory_name" href="#cvpysdk.activateapps.inventory_manager.Inventory.inventory_name">inventory_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.properties" href="#cvpysdk.activateapps.inventory_manager.Inventory.properties">properties</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.refresh" href="#cvpysdk.activateapps.inventory_manager.Inventory.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.schedule" href="#cvpysdk.activateapps.inventory_manager.Inventory.schedule">schedule</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.security_associations" href="#cvpysdk.activateapps.inventory_manager.Inventory.security_associations">security_associations</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.share" href="#cvpysdk.activateapps.inventory_manager.Inventory.share">share</a></code></li>
<li><code><a title="cvpysdk.activateapps.inventory_manager.Inventory.start_collection" href="#cvpysdk.activateapps.inventory_manager.Inventory.start_collection">start_collection</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>