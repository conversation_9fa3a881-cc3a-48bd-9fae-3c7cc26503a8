<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.cloudapps.onedrive_subclient API documentation</title>
<meta name="description" content="File for operating on a OneDrive Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.cloudapps.onedrive_subclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a OneDrive Subclient.</p>
<p>OneDriveSubclient is the only class defined in this file.</p>
<p>OneDriveSubclient:
Derived class from CloudAppsSubclient Base class, representing a
OneDrive subclient, and to perform operations on that subclient</p>
<h2 id="onedrivesubclient">Onedrivesubclient</h2>
<p>content()
&ndash;
gets the content of the subclient</p>
<p>groups()
&ndash;
gets the groups associated with the subclient</p>
<p>restore_out_of_place()
&ndash;
runs out-of-place restore for the subclient</p>
<p>discover()
&ndash;
runs user discovery on subclient</p>
<p>add_AD_group()
&ndash;
adds AD group to the subclient</p>
<p>add_user()
&ndash;
adds user to the subclient</p>
<p>add_users_onedrive_for_business_client()
&ndash;
Adds user to OneDrive for Business Client</p>
<p>search_for_user()
&ndash;
Searches for a specific user's details from
discovered list</p>
<p>disk_restore_onedrive_for_business_client()
&ndash;
Runs disk restore of selected users for
OneDrive for Business Client</p>
<p>out_of_place_restore_onedrive_for_business_client()
&ndash;
Runs out-of-place restore of selected users
for OneDrive for Business Client</p>
<p>in_place_restore_onedrive_syntex()
&ndash;
Runs in-place restore of selected users for Syntex OneDrive for Business Client</p>
<p>in_place_restore_onedrive_for_business_client()
&ndash;
Runs in-place restore of selected users for OneDrive for Business Client</p>
<p>_get_user_guids()
&ndash;
Retrieve GUIDs for users specified</p>
<p>_task_json_for_onedrive_backup()
&ndash;
Json for onedrive backup for selected users</p>
<p>_association_users_json()
&ndash;
user association</p>
<p>point_in_time_in_place_restore_onedrive_for_business_client()
&ndash; Runs PIT in-place restore of selected users</p>
<p>point_in_time_out_of_place_restore_onedrive_for_business_client()
&ndash; Runs PIT out of place restore of selected users</p>
<p>run_user_level_backup_onedrive_for_business_client()
&ndash;
Runs the backup for the users in users list</p>
<p>_get_user_details()
&ndash;
gets user details from discovery</p>
<p>_get_group_details()
&ndash;
gets group details from discovery</p>
<p>browse_for_content()
&ndash;
Returns the Onedrive client content i.e.
users/group information that is discovered
in auto discovery phase</p>
<p>_set_properties_to_update_site_association()
&ndash;
Updates the association properties of user</p>
<p>update_users_association_properties()
&ndash;
Updates the association properties of user</p>
<p>manage_custom_category()
&ndash;
Adds or Edits Custom category in the office 365 app</p>
<p>update_custom_categories_association_properties()
&ndash;
Updates the association properties of custom category</p>
<p>refresh_retention_stats()
&ndash;
refresh the retention stats for the client</p>
<p>refresh_client_level_stats()
&ndash;
refresh the client level stats for the client</p>
<p>get_client_level_stats()
&ndash;
Returns the client level stats for the client</p>
<p>run_trueup_for_single_user()
&ndash;
Runs the true up for a single user</p>
<p>read_trueup_results_for_single_user()
&ndash;
Reads the true up results for a single user</p>
<p>read_trueup_results_for_all_users()
&ndash;
Method to read the api call from the TrueUp and find the deleted files</p>
<p>preview_backedup_file() &ndash; Get preview content for onedrive subclient</p>
<p>run_backup_onedrive_for_business_client()
&ndash; Runs client level backup</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1-L2089" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a OneDrive Subclient.

OneDriveSubclient is the only class defined in this file.

OneDriveSubclient:    Derived class from CloudAppsSubclient Base class, representing a
OneDrive subclient, and to perform operations on that subclient

OneDriveSubclient:

    content()                                                           --  gets the content of the subclient

    groups()                                                            --  gets the groups associated with the subclient

    restore_out_of_place()                                              --  runs out-of-place restore for the subclient

    discover()                                                          --  runs user discovery on subclient

    add_AD_group()                                                      --  adds AD group to the subclient

    add_user()                                                          --  adds user to the subclient

    add_users_onedrive_for_business_client()                            --  Adds user to OneDrive for Business Client

    search_for_user()                                                   --  Searches for a specific user&#39;s details from
                                                                            discovered list

    disk_restore_onedrive_for_business_client()                         --  Runs disk restore of selected users for
                                                                            OneDrive for Business Client

    out_of_place_restore_onedrive_for_business_client()                 --  Runs out-of-place restore of selected users
                                                                            for OneDrive for Business Client

    in_place_restore_onedrive_syntex()          --  Runs in-place restore of selected users for Syntex OneDrive for Business Client

    in_place_restore_onedrive_for_business_client()               --  Runs in-place restore of selected users for OneDrive for Business Client

    _get_user_guids()                                                   --  Retrieve GUIDs for users specified

    _task_json_for_onedrive_backup()                                    --  Json for onedrive backup for selected users

    _association_users_json()                                           --  user association

    point_in_time_in_place_restore_onedrive_for_business_client()       -- Runs PIT in-place restore of selected users

    point_in_time_out_of_place_restore_onedrive_for_business_client()   -- Runs PIT out of place restore of selected users

    run_user_level_backup_onedrive_for_business_client()                --  Runs the backup for the users in users list

    _get_user_details()                                                 --   gets user details from discovery

    _get_group_details()                                                --   gets group details from discovery

    browse_for_content()                                                --  Returns the Onedrive client content i.e.
                                                                            users/group information that is discovered
                                                                            in auto discovery phase

    _set_properties_to_update_site_association()                        --   Updates the association properties of user

    update_users_association_properties()                               --   Updates the association properties of user

    manage_custom_category()                                            --   Adds or Edits Custom category in the office 365 app

    update_custom_categories_association_properties()                   --  Updates the association properties of custom category

    refresh_retention_stats()                                           --  refresh the retention stats for the client

    refresh_client_level_stats()                                        --  refresh the client level stats for the client

    get_client_level_stats()                                            --  Returns the client level stats for the client

    run_trueup_for_single_user()                                        --  Runs the true up for a single user

    read_trueup_results_for_single_user()                               --  Reads the true up results for a single user

    read_trueup_results_for_all_users()                                 --  Method to read the api call from the TrueUp and find the deleted files

    preview_backedup_file() -- Get preview content for onedrive subclient

    run_backup_onedrive_for_business_client()                           -- Runs client level backup

&#34;&#34;&#34;

from __future__ import unicode_literals

import datetime

from ...exception import SDKException
import time
from ..casubclient import CloudAppsSubclient
from ...constants import AppIDAType
from .onedrive_constants import OneDriveConstants
import re


class OneDriveSubclient(CloudAppsSubclient):
    &#34;&#34;&#34;Derived class from CloudAppsSubclient Base class, representing a OneDrive subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of File System subclient..&#34;&#34;&#34;
        super(OneDriveSubclient, self)._get_subclient_properties()
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

        content = []
        group_list = []

        for account in self._content:
            temp_account = account[&#34;cloudconnectorContent&#34;][&#34;includeAccounts&#34;]

            if temp_account[&#39;contentType&#39;] == AppIDAType.CLOUD_APP.value:
                content_dict = {
                    &#39;SMTPAddress&#39;: temp_account[&#34;contentName&#34;].split(&#34;;&#34;)[0],
                    &#39;display_name&#39;: temp_account[&#34;contentValue&#34;]
                }

                content.append(content_dict)
            if temp_account[&#39;contentType&#39;] == 135:
                group_list.append(temp_account[&#34;contentName&#34;])
        self._ca_content = content
        self._ca_groups = group_list

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;

        return {&#39;subClientProperties&#39;: self._subclient_properties}

    def _association_users_json(self, users_list):
        &#34;&#34;&#34;
            Args:
                users_list (list) : list of SMTP addresses of users
            Returns:
                users_json(list): Required details of users to backup
        &#34;&#34;&#34;
        users_json = []
        for user_smtp in users_list:
            user_details = self._get_user_details(user_smtp)
            user_info = {
                &#34;user&#34;: {
                    &#34;userGUID&#34;: user_details[0].get(&#39;user&#39;, {}).get(&#39;userGUID&#39;)
                }
            }
            users_json.append(user_info)
        return users_json

    def _task_json_for_onedrive_backup(self, users_list, custom_groups_list=[]):
        &#34;&#34;&#34;
        Json for onedrive backup for selected users

        Args:
                users_list (list) : list of SMTP addresses of users
                custom_groups_list (list) : list of custom category groups
        &#34;&#34;&#34;
        groups, _ = self.browse_for_content(discovery_type=31)
        associated_users_json = self._association_users_json(users_list)

        associated_custom_groups_json = []
        if len(custom_groups_list) != 0:
            for group in custom_groups_list:
                group_info = {
                    &#34;id&#34;: groups[group].get(&#39;id&#39;, None),
                    &#34;name&#34;: group
                }
                associated_custom_groups_json.append(group_info)

        advanced_options_dict = {
            &#39;cloudAppOptions&#39;: {
                &#39;userAccounts&#39;: associated_users_json,
                &#39;userGroups&#39;: associated_custom_groups_json
            }
        }

        selected_items = []
        for user_smtp in users_list:
            details = self._get_user_details(user_smtp)
            item = {
                &#34;itemName&#34;: details[0].get(&#39;displayName&#39;),
                &#34;itemType&#34;: &#34;User&#34;
            }
            selected_items.append(item)

        for group in custom_groups_list:
            item = {
                &#34;itemName&#34;: group,
                &#34;itemtype&#34;: &#34;Custom category&#34;
            }
            selected_items.append(item)

        common_options_dict = {
            &#34;jobMetadata&#34;: [
                {
                    &#34;selectedItems&#34;: selected_items,
                    &#34;jobOptionItems&#34;: [
                        {
                            &#34;option&#34;: &#34;Total running time&#34;,
                            &#34;value&#34;: &#34;Disabled&#34;
                        }
                    ]
                }
            ]
        }

        task_json = self._backup_json(backup_level=&#39;INCREMENTAL&#39;, incremental_backup=False, incremental_level=&#39;BEFORE_SYNTH&#39;,
                                      advanced_options=advanced_options_dict, common_backup_options=common_options_dict)
        return task_json

    def _task_json_for_backup(self,**kwargs):
        &#34;&#34;&#34;
        Json for onedrive backup
        &#34;&#34;&#34;

        items_selection_option = kwargs.get(&#39;items_selection_option&#39;, &#39;&#39;)

        common_options_dict={
            &#34;jobMetadata&#34;: [
                {
                    &#34;selectedItems&#34;: [
                  {
                    &#34;itemName&#34;: &#34;All%20users&#34;,
                    &#34;itemType&#34;: &#34;All users&#34;
                  }
                ],
                    &#34;jobOptionItems&#34;: [
                        {
                            &#34;option&#34;: &#34;Total running time&#34;,
                            &#34;value&#34;: &#34;Disabled&#34;
                        }
                    ]
                }
            ]
        }
        if items_selection_option!=&#39;&#39;:
            common_options_dict[&#34;itemsSelectionOption&#34;]=items_selection_option

        task_json = self._backup_json(backup_level=&#39;INCREMENTAL&#39;,incremental_backup=False,incremental_level=&#39;BEFORE_SYNTH&#39;,common_backup_options=common_options_dict)
        return task_json

    @property
    def content(self):
        &#34;&#34;&#34;Returns the subclient content dict&#34;&#34;&#34;
        return self._ca_content

    @property
    def groups(self):
        &#34;&#34;&#34;Returns the list of groups assigned to the subclient if any.
        Groups are assigned only if auto discovery is enabled for groups.

            Returns:

                list - list of groups associated with the subclient

        &#34;&#34;&#34;
        return self._ca_groups

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update content of a
            Cloud Apps Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient
                                              contains the account info for each user in list.

                                              example temp_content_dict={
                                                &#34;cloudconnectorContent&#34;: {
                                                  &#34;includeAccounts&#34;: {
                                                    &#34;contentValue&#34;: Automation User,
                                                    &#34;contentType&#34;: 134,
                                                    &#34;contentName&#34;: <EMAIL>
                                                     }
                                                  }
                                              }

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        content = []

        try:
            for account in subclient_content:
                temp_content_dict = {
                    &#34;cloudconnectorContent&#34;: {
                        &#34;includeAccounts&#34;: {
                            &#34;contentValue&#34;: account[&#39;display_name&#39;],
                            &#34;contentType&#34;: AppIDAType.CLOUD_APP.value,
                            &#34;contentName&#34;: account[&#39;SMTPAddress&#39;]
                        }
                    }
                }

                content.append(temp_content_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;{} not given in content&#39;.format(err))

        self._set_subclient_properties(&#34;_content&#34;, content)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            to_disk=False):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_disk             (bool)       --  If True, restore to disk will be performed

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity

        return self._instance_object.restore_out_of_place(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            to_disk=to_disk
        )

    def discover(self, discover_type=&#39;USERS&#39;):
        &#34;&#34;&#34;This method discovers the users/groups on OneDrive

                Args:

                    discover_type (str)  --  Type of discovery

                        Valid Values are

                        -   USERS
                        -   GROUPS

                        Default: USERS

                Returns:

                    List (list)  --  List of users on GSuite account

                Raises:
                    SDKException:
                        if response is empty

                        if response is not success


        &#34;&#34;&#34;

        if discover_type.upper() == &#39;USERS&#39;:
            disc_type = 10
        elif discover_type.upper() == &#39;GROUPS&#39;:
            disc_type = 5
        _get_users = self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self._instance_object.instance_id,
                                                              self._client_object.client_id,
                                                              disc_type)

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _get_users)
        if flag:
            if response.json() and &#34;scDiscoveryContent&#34; in response.json():
                self._discover_properties = response.json()[
                    &#34;scDiscoveryContent&#34;][0]

                if &#34;contentInfo&#34; in self._discover_properties:
                    self._contentInfo = self._discover_properties[&#34;contentInfo&#34;]
                return self._contentInfo
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def set_auto_discovery(self, value):
        &#34;&#34;&#34;Sets the auto discovery value for subclient.
        You can either set a RegEx value or a user group,
        depending on the auto discovery type selected at instance level.

            Args:

                value   (list)  --  List of RegEx or user groups

        &#34;&#34;&#34;

        if not isinstance(value, list):
            raise SDKException(&#39;Subclient&#39;, &#39;116&#39;)

        if not self._instance_object.auto_discovery_status:
            raise SDKException(&#39;Subclient&#39;, &#39;117&#39;)

        subclient_prop = self._subclient_properties[&#39;cloudAppsSubClientProp&#39;].copy(
        )
        if self._instance_object.auto_discovery_mode == 0:
            # RegEx based auto discovery is enabled on instance

            subclient_prop[&#39;oneDriveSubclient&#39;][&#39;regularExp&#39;] = value
            self._set_subclient_properties(
                &#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]&#34;, subclient_prop)
        else:
            # User group based auto discovery is enabled on instance
            grp_list = []
            groups = self.discover(discover_type=&#39;GROUPS&#39;)
            for item in value:
                for group in groups:
                    if group[&#39;contentName&#39;].lower() == item.lower():
                        grp_list.append({
                            &#34;cloudconnectorContent&#34;: {
                                &#34;includeAccounts&#34;: group
                            }
                        })
            self._content.extend(grp_list)
            self._set_subclient_properties(
                &#34;_subclient_properties[&#39;content&#39;]&#34;, self._content)
        self.refresh()

    def run_subclient_discovery(self):
        &#34;&#34;&#34;
            This method launches AutoDiscovery on the subclient
        &#34;&#34;&#34;

        discover_type = 15
        discover_users = self._services[&#39;GET_CLOUDAPPS_ONEDRIVE_USERS&#39;] % (self._instance_object.instance_id,
                                                                           self._client_object.client_id,
                                                                           discover_type,
                                                                           self.subclient_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, discover_users)
        if response.status_code != 200 and response.status_code != 500:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def add_AD_group(self, value):
        &#34;&#34;&#34;Adds the user group to the subclient if auto discovery type selected
            AD group at instance level.
                Args:
                    value   (list)  --  List of user groups
        &#34;&#34;&#34;
        grp_list = []
        groups = self.discover(discover_type=&#39;GROUPS&#39;)
        for item in value:
            for group in groups:
                if group[&#39;contentName&#39;].lower() == item.lower():
                    grp_list.append(group)

        contentinfo = []

        for grp in grp_list:
            info = {
                &#34;contentValue&#34;: grp[&#39;contentValue&#39;],
                &#34;contentType&#34;: grp[&#39;contentType&#39;],
                &#34;contentName&#34;: grp[&#39;contentName&#39;]
            }
            contentinfo.append(info)

        request_json = {
            &#34;App_DiscoveryContent&#34;: {
                &#34;scDiscoveryContent&#34;: [
                    {
                        &#34;scEntity&#34;: {
                            &#34;subclientId&#34;: self.subclient_id
                        },
                        &#34;contentInfo&#34;: contentinfo
                    }
                ]
            }
        }
        add_ADgroup = self._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, add_ADgroup, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def add_user(self, user_name):
        &#34;&#34;&#34;This method adds one drive user to the subclient
                Args:
                    user_name   (str)  --  Onedrive user name
        &#34;&#34;&#34;
        users = self.discover(discover_type=&#39;USERS&#39;)

        for user in users:
            if user[&#39;contentName&#39;].lower() == user_name.lower():
                user_dict = user
                break

        request_json = {
            &#34;App_DiscoveryContent&#34;: {
                &#34;scDiscoveryContent&#34;: [
                    {
                        &#34;scEntity&#34;: {
                            &#34;subclientId&#34;: self.subclient_id
                        },
                        &#34;contentInfo&#34;: [
                            {
                                &#34;contentValue&#34;: user_dict[&#39;contentValue&#39;],
                                &#34;contentType&#34;: user_dict[&#39;contentType&#39;],
                                &#34;contentName&#34;: user_dict[&#39;contentName&#39;]
                            }
                        ]
                    }
                ]
            }
        }

        add_user = self._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, add_user, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = &#39;Failed to user to the subclient\nError: &#34;{0}&#34;&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                       output_string.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def _get_subclient_users(self):
        &#34;&#34;&#34;Method to get the users in the subclient

            Returns:
                List of Users in subclient
        &#34;&#34;&#34;
        users = []
        result = self.content
        for user in result:
            users.append(user[&#39;SMTPAddress&#39;])
        return users

    @property
    def get_subclient_users(self):
        &#34;&#34;&#34;Returns the users in subclient&#34;&#34;&#34;
        return self._get_subclient_users()

    def add_ad_group_onedrive_for_business_client(self, value, plan_name):
        &#34;&#34;&#34; Adds given OneDrive group to v2 client

            Args:

                value (string) : Group name

                plan_name (str) : O365 plan name to associate with users

            Raises:

                SDKException:

                    if response is not success

                    if response is returned with errors
        &#34;&#34;&#34;
        # Get o365plan
        plan_name = plan_name.strip()
        o365_plan_object = self._commcell_object.plans.get(plan_name)
        o365_plan_id = int(o365_plan_object.plan_id)

        # Get client id
        client_id = int(self._client_object.client_id)

        groups = []
        group_response = self.search_for_group(group_id=value)
        display_name = group_response[0].get(&#39;name&#39;)
        group_id = group_response[0].get(&#39;id&#39;)

        groups.append({
            &#34;name&#34;: display_name,
            &#34;id&#34;: group_id
        })

        request_json = {
            &#34;LaunchAutoDiscovery&#34;: True,
            &#34;cloudAppAssociation&#34;: {
                &#34;accountStatus&#34;: 0,
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: client_id,
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 2,
                    &#34;groups&#34;: groups
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: o365_plan_id
                }
            }
        }

        user_associations = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, user_associations, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add group\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def add_users_onedrive_for_business_client(self, users, plan_name):
        &#34;&#34;&#34; Adds given OneDrive users to v2 client

            Args:

                users (list) : List of user&#39;s SMTP address

                plan_name (str) : O365 plan name to associate with users

            Raises:

                SDKException:

                    if response is not success

                    if response is returned with errors
        &#34;&#34;&#34;

        if not (isinstance(users, list) and isinstance(plan_name, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Get o365plan
        plan_name = plan_name.strip()
        o365_plan_object = self._commcell_object.plans.get(plan_name)
        o365_plan_id = int(o365_plan_object.plan_id)

        # Get client ID
        client_id = int(self._client_object.client_id)

        user_accounts = []

        for user_id in users:
            # Get user details
            user_response = self.search_for_user(user_id)
            display_name = user_response[0].get(&#39;displayName&#39;)
            user_guid = user_response[0].get(&#39;user&#39;).get(&#39;userGUID&#39;)
            is_auto_discovered_user = user_response[0].get(
                &#39;isAutoDiscoveredUser&#39;)
            is_super_admin = user_response[0].get(&#39;isSuperAdmin&#39;)

            user_accounts.append({
                &#34;displayName&#34;: display_name,
                &#34;isSuperAdmin&#34;: is_super_admin,
                &#34;smtpAddress&#34;: user_id,
                &#34;isAutoDiscoveredUser&#34;: is_auto_discovered_user,
                &#34;associated&#34;: False,
                &#34;commonFlags&#34;: 0,
                &#34;user&#34;: {
                    &#34;userGUID&#34;: user_guid
                }
            })

        request_json = {
            &#34;LaunchAutoDiscovery&#34;: False,
            &#34;cloudAppAssociation&#34;: {
                &#34;accountStatus&#34;: 0,
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: client_id,
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 1,
                    &#34;userAccounts&#34;: user_accounts
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: o365_plan_id
                }
            }
        }

        user_associations = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, user_associations, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add user\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def verify_discovery_onedrive_for_business_client(self):
        &#34;&#34;&#34; Verifies that discovery is complete

            Returns:

                discovery_stats (tuple):

                    discovery_status (bool): True if users are discovered else returns False

                    total_records (int):     Number of users fetched, returns -1 if discovery is not complete

            Raises:

                 SDKException:

                        if response is not success

                        if response received does not contain pagining info
        &#34;&#34;&#34;

        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        # determines the number of accounts to return in response
        page_size = 1
        discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, discover_query)

        if flag:
            no_of_records = -1
            if response and response.json():
                if &#39;pagingInfo&#39; in response.json():
                    no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                    if no_of_records &gt; 0:
                        return True, no_of_records
            return False, no_of_records
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def search_for_user(self, user_id):
        &#34;&#34;&#34; Searches for a specific user&#39;s details from discovered list

            Args:
                user_id (str) : user&#39;s SMTP address

            Returns:

                user_accounts (list): user details&#39; list fetched from discovered content
                              eg: [
                                      {
                                        &#39;displayName&#39;: &#39;&#39;,
                                        &#39;smtpAddress&#39;: &#39;&#39;,
                                        &#39;isSuperAdmin&#39;: False,
                                        &#39;isAutoDiscoveredUser&#39;: False,
                                        &#39;commonFlags&#39;: 0,
                                        &#39;user&#39;: {
                                            &#39;_type_&#39;: 13,
                                             &#39;userGUID&#39;: &#39;UserGuid&#39;
                                             }
                                       }
                                  ]

            Raises:

                SDKException:

                    if discovery is not complete

                    if invalid SMTP address is passed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        search_query = f&#39;{browse_content}&amp;search={user_id}&#39;

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

        if flag:
            if response and response.json():
                if &#39;userAccounts&#39; in response.json():
                    user_accounts = response.json().get(&#39;userAccounts&#39;, [])
                    if len(user_accounts) == 0:
                        error_string = &#39;Either discovery is not complete or user is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                    return user_accounts
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                                       &#39;Check if the user provided is valid&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def search_for_group(self, group_id):
        &#34;&#34;&#34; Searches for a specific group details from discovered list

            Args:
                group_id (str) : group name

            Returns:

                groups (list): group details&#39; list fetched from discovered content
                              eg: [
                                      {
                                         &#34;name&#34;: &#34;&#34;,
                                         &#34;id&#34;: &#34;&#34;
                                       }
                                  ]

            Raises:

                SDKException:

                    if discovery is not complete

                    if invalid SMTP address is passed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self._instance_object.instance_id,
                                                                   self._client_object.client_id,
                                                                   5))

        search_query = f&#39;{browse_content}&amp;search={group_id}&#39;

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

        if flag:
            if response and response.json():
                if &#39;groups&#39; in response.json():
                    groups = response.json().get(&#39;groups&#39;, [])
                    if len(groups) == 0:
                        error_string = &#39;Either discovery is not complete or group is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                    return groups
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                                       &#39;Check if the group provided is valid&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def disk_restore_onedrive_for_business_client(self, users, destination_client, destination_path, skip_file_permissions=False):
        &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                destination_client (str) : client where the users need to be restored
                destination_path (str) : Destination folder location
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;disk_restore&#39;: True,
            &#39;destination_path&#39;: destination_path,
            &#39;destination_client&#39;: destination_client,
            &#39;skip_file_permissions&#39;: skip_file_permissions
        }
        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)
        return self._process_restore_response(restore_json)

    def out_of_place_restore_onedrive_for_business_client(self, users, destination_path, **kwargs):
        &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                destination_path (str) : SMTP address of destination user
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, True)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)

        if overwrite and restore_as_copy:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;out_of_place&#39;: True,
            &#39;destination_path&#39;: destination_path,
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions,
            &#39;include_deleted_items&#39;: include_deleted_items
        }
        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)

        return self._process_restore_response(restore_json)

    def in_place_restore_onedrive_syntex(self, users, fast_restore_point=False):
        &#34;&#34;&#34; Runs an in-place restore job for specified users on Syntex OneDrive for business client

            Args:
                users (list)                    :  List of SMTP addresses of users
                fast_restore_point   (booL)     : Whether to use fast restore point or not
                                                  default: False


            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if restore job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        user_details = {}
        for user in users:
            user_details[user] = self.search_for_user(user)

        self._instance_object._restore_association = self._subClientEntity

        syntex_restore_items = []
        for key, value in user_details.items():
            syntex_restore_items.append({
                &#34;displayName&#34;: value[0][&#34;displayName&#34;],
                &#34;email&#34;: value[0][&#34;smtpAddress&#34;],
                &#34;guid&#34;: value[0][&#34;user&#34;][&#34;userGUID&#34;],
                &#34;rawId&#34;: value[0][&#34;user&#34;][&#34;userGUID&#34;],
                &#34;restoreType&#34;: 1
            })

        source_user_list = self._get_user_guids(users)
        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list)

        # Get the current time in UTC
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_timestamp = int(current_time.timestamp())
        current_iso_format = current_time.strftime(
            &#39;%Y-%m-%dT%H:%M:%S.%f&#39;)[:-3] + &#39;Z&#39;

        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
            &#34;msSyntexRestoreOptions&#34;] = {
            &#34;msSyntexRestoreItems&#34;: {
                &#34;listMsSyntexRestoreItems&#34;: syntex_restore_items
            },
            &#34;restoreDate&#34;: {
                &#34;time&#34;: current_timestamp,
                &#34;timeValue&#34;: current_iso_format
            },
            &#34;restorePointId&#34;: &#34;&#34;,
            &#34;restoreType&#34;: 1,
            &#34;useFastRestorePoint&#34;: fast_restore_point
        }

        return self._process_restore_response(restore_json)

    def in_place_restore_onedrive_for_business_client(self, users, **kwargs):
        &#34;&#34;&#34; Runs an in-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) :  List of SMTP addresses of users
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, True)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)
        if overwrite and restore_as_copy:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions,
            &#39;include_deleted_items&#39;: include_deleted_items
        }

        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)
        return self._process_restore_response(restore_json)

    def _get_user_guids(self, users):
        &#34;&#34;&#34; Retrieve GUIDs for users specified

            Args:
                user (list) : List of SMTP addresses of users

            Returns:
                user_guid_list (list) : list of GUIDs of specified users

            Raises:
                SDKException:
                    if user details couldn&#39;t be found in discovered data
        &#34;&#34;&#34;
        user_guid_list = []
        for user_id in users:
            user = self.search_for_user(user_id)
            if len(user) != 0 and user[0].get(&#39;user&#39;, {}).get(&#39;userGUID&#39;) is not None:
                user_guid_list.append(user[0].get(&#39;user&#39;).get(&#39;userGUID&#39;))
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                   &#39;User details not found in discovered data&#39;)
        return user_guid_list

    def process_index_retention_rules(self, index_app_type_id, index_server_client_name):
        &#34;&#34;&#34;
         Makes API call to process index retention rules

         Args:

            index_app_type_id           (int)   --   index app type id

            index_server_client_name    (str)   --  client name of index server

         Raises:

                SDKException:

                    if index server not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if self._commcell_object.clients.has_client(index_server_client_name):
            index_server_client_id = int(
                self._commcell_object.clients[index_server_client_name.lower()][&#39;id&#39;])
            request_json = {
                &#34;appType&#34;: index_app_type_id,
                &#34;indexServerClientId&#34;: index_server_client_id
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json(
                            )[&#39;response&#39;][&#39;errorString&#39;]
                            o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(
                                error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                                   self._update_response_(response.text))
        else:
            raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)

    def point_in_time_in_place_restore_onedrive_for_business_client(self, users, end_time, **kwargs):
        &#34;&#34;&#34; Runs an in-place point in time restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) :  List of SMTP addresses of users
                end_time (int) : Backup job end time
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;

        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)

        if overwrite and restore_as_copy:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions
        }

        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)

        adv_search_bkp_time_dict = {
            &#34;field&#34;: &#34;BACKUPTIME&#34;,
            &#34;fieldValues&#34;: {
                &#34;values&#34;: [
                    &#34;0&#34;,
                    str(end_time)
                ]
            },
            &#34;intraFieldOp&#34;: &#34;FTOr&#34;
        }

        add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
        add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
        add_backup_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
            &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
        add_backup_time.append(adv_search_bkp_time_dict)

        return self._instance_object._process_restore_response(restore_json)

    def point_in_time_out_of_place_restore_onedrive_for_business_client(self, users, end_time, destination_path, **kwargs):
        &#34;&#34;&#34; Runs an out-of-place point in time restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                end_time (int) : Backup job end time
                destination_path (str) : SMTP address of destination user
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)

        if overwrite and restore_as_copy:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;out_of_place&#39;: True,
            &#39;destination_path&#39;: destination_path,
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions
        }
        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)

        adv_search_bkp_time_dict = {
            &#34;field&#34;: &#34;BACKUPTIME&#34;,
            &#34;fieldValues&#34;: {
                &#34;values&#34;: [
                    &#34;0&#34;,
                    str(end_time)
                ]
            },
            &#34;intraFieldOp&#34;: &#34;FTOr&#34;
        }

        add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
        add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
        add_backup_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
            &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
        add_backup_time.append(adv_search_bkp_time_dict)

        return self._process_restore_response(restore_json)

    def run_user_level_backup_onedrive_for_business_client(self, users_list, custom_groups_list=[]):
        &#34;&#34;&#34;
        Runs the backup for the users in users list/ custom categories list
        Args:
                users_list (list) : list of SMTP addresses of users
                custom_groups_list (lis) : list of custom categories

        Returns:
                object - instance of the Job class for this backup job

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        task_json = self._task_json_for_onedrive_backup(
            users_list, custom_groups_list)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def run_backup_onedrive_for_business_client(self,**kwargs):
        &#34;&#34;&#34;
                Runs the backup


                 **kwargs (dict) : Additional parameters
                    items_selection_option (str) : Item Selection Option


                Returns:
                        object - instance of the Job class for this backup job

                Raises:
                    SDKException:
                        if response is empty

                        if response is not success

                &#34;&#34;&#34;
        items_selection_option=kwargs.get(&#39;items_selection_option&#39;, &#39;&#39;)

        kwargs = {
            &#39;items_selection_option&#39;: items_selection_option
        }

        task_json = self._task_json_for_backup(**kwargs)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def _get_user_details(self, user):
        &#34;&#34;&#34;
        gets user details from discovery
        Args:
                user (str) : SMTP address of user

        Returns:
                user_details  (dict) : User&#39;s details fetched from discovery

        Raises:
            SDKException:
                if response is empty
        &#34;&#34;&#34;
        user_details = self.search_for_user(user)
        if len(user_details) != 0:
            return user_details
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;User details not found in discovered data&#39;)

    def _get_group_details(self, group):
        &#34;&#34;&#34;
        gets group details from discovery
        Args:
                group (str) : SMTP address of group
        &#34;&#34;&#34;
        group_details = self.search_for_group(group)
        if len(group_details) != 0:
            return group_details
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;Group details not found in discovered data&#39;)

    def browse_for_content(self, discovery_type, include_deleted=False):
        &#34;&#34;&#34;Returns the Onedrive client content i.e. users/ group information that is discovered in auto discovery phase

                Args:

                    discovery_type  (int)   --  type of discovery for content
                                                For all Associated users = 1
                                                For all Associated groups = 2
                                                For all Custom category groups = 31

                    include_deleted  (bool)  -- If True, deleted items will also be included

                Returns:

                    user_dict     (dict)    --  dictionary of users properties

                    no_of_records   (int)   --  no of records

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by Onedrive On-Premise Instance

        &#34;&#34;&#34;
        if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

        self._USER_POLICY_ASSOCIATION = self._services[&#39;USER_POLICY_ASSOCIATION&#39;]
        request_json = {
            &#34;discoverByType&#34;: discovery_type,
            &#34;bIncludeDeleted&#34;: include_deleted,
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                }
            },
            &#34;searchInfo&#34;: {
                &#34;isSearch&#34;: 0,
                &#34;searchKey&#34;: &#34;&#34;
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._USER_POLICY_ASSOCIATION, request_json
        )
        if flag:
            if response and response.json():
                no_of_records = 0
                if &#39;associations&#39; in response.json():
                    no_of_records = response.json().get(&#39;associations&#39;, [{}])[0].get(&#39;pagingInfo&#39;, {}). \
                        get(&#39;totalRecords&#39;, -1)
                elif &#39;pagingInfo&#39; in response.json():
                    no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                    if no_of_records &lt;= 0:
                        return {}, no_of_records
                associations = response.json().get(&#39;associations&#39;, [{}])
                user_dict = {}
                if discovery_type == 2 or discovery_type == 31:
                    if associations:
                        for group in associations:
                            group_name = group.get(
                                &#34;groups&#34;, {}).get(&#34;name&#34;, &#34;&#34;)
                            user_dict[group_name] = {
                                &#39;accountStatus&#39;: group.get(&#34;accountStatus&#34;),
                                &#39;discoverByType&#39;: group.get(&#34;discoverByType&#34;),
                                &#39;planName&#39;: group.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;),
                                &#39;id&#39;: group.get(&#34;groups&#34;, {}).get(&#34;id&#34;, &#34;&#34;),
                                &#39;categoryNumber&#39;: group.get(&#34;groups&#34;, {}).get(&#34;categoryNumber&#34;, None)
                            }
                else:
                    if associations:
                        for user in associations:
                            user_url = user.get(&#34;userAccountInfo&#34;, {}).get(
                                &#34;smtpAddress&#34;, &#34;&#34;)
                            user_account_info = user.get(&#34;userAccountInfo&#34;, {})
                            user_dict[user_url] = {
                                &#39;userAccountInfo&#39;: user_account_info,
                                &#39;accountStatus&#39;: user.get(&#34;accountStatus&#34;),
                                &#39;discoverByType&#39;: user.get(&#34;discoverByType&#34;),
                                &#39;planName&#39;: user.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;),
                                &#39;lastBackupTime&#39;: user.get(&#34;userAccountInfo&#34;, {}).get(&#34;lastBackupJobRanTime&#34;, {}).get(
                                    &#34;time&#34;, None)
                            }
                return user_dict, no_of_records
            return {}, 0
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))

    def _set_properties_to_update_site_association(self, operation):
        &#34;&#34;&#34;Updates the association properties of user

            Args:

               operation (int)                  --  type of operation to be performed
                                                     Example: 1 - Associate
                                                              2 - Enable
                                                              3 - Disable
                                                              4 - Remove

            Raises:

            SDKException:

                if the method is called by Onedrive On-Premise Instance

        &#34;&#34;&#34;
        if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

        properties_dict = {}
        if operation == 1:
            properties_dict[&#34;accountStatus&#34;] = 0
        elif operation == 2:
            properties_dict[&#34;accountStatus&#34;] = 0
        elif operation == 3:
            properties_dict[&#34;accountStatus&#34;] = 2
        elif operation == 4:
            properties_dict[&#34;accountStatus&#34;] = 1
        return properties_dict

    def update_users_association_properties(self, operation, **kwargs):
        &#34;&#34;&#34;Updates the association properties of user

                Args:
                    operation (int)         --  type of operation to be performed
                                                 Example: 1 - Associate
                                                          2 - Enable
                                                          3 - Disable
                                                          4 - Remove

                    Additional arguments (kwargs):
                    user_accounts_list (list)   --  list of user accounts
                                                    It has all information of users

                    groups_list (list)      --  list of groups
                                                It has all information of groups

                    plan_id (int)           --  id of Office 365 plan

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by Onedrive On-Premise Instance

        &#34;&#34;&#34;
        plan_id = kwargs.get(&#39;plan_id&#39;, None)
        user_accounts_list = kwargs.get(&#39;user_accounts_list&#39;, None)
        groups_list = kwargs.get(&#39;groups_list&#39;, None)

        if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

        properties_dict = self._set_properties_to_update_site_association(
            operation)
        self._ASSOCIATE_CONTENT = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
        if user_accounts_list:
            request_json = {
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 1,
                        &#34;userAccounts&#34;: user_accounts_list
                    }
                }
            }
        if groups_list:
            request_json = {
                &#34;LaunchAutoDiscovery&#34;: True,
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 2,
                        &#34;groups&#34;: groups_list
                    }
                }
            }
        if properties_dict.get(&#39;accountStatus&#39;, None) is not None:
            request_json[&#39;cloudAppAssociation&#39;][&#39;accountStatus&#39;] = properties_dict[&#39;accountStatus&#39;]
        if plan_id:
            request_json[&#39;cloudAppAssociation&#39;][&#39;plan&#39;] = {
                &#34;planId&#34;: int(plan_id)
            }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._ASSOCIATE_CONTENT, request_json
        )
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json().get(&#39;response&#39;, {}).get(&#39;errorString&#39;, str())
                        o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                               self._update_response_(response.text))

    def manage_custom_category(self, custom_dict, action, plan=None):
        &#34;&#34;&#34;
        Adds or Edits Custom category in the office 365 app.

        Args:
            custom_dict (dict)  --  dictionary of custom category name and rule details.
                Example:
                    {
                    &#34;name&#34;:&#34;Display name contains custom&#34;
                    &#34;rules&#34;:
                        [
                            {
                            &#34;CCRuleName&#34;:&#34;User Display Name&#34;,
                            &#34;CCRuleOperator&#34;:&#34;Contains&#34;,
                            &#34;CCRuleMask&#34;:&#34;od_test_user&#34;
                            }
                        ]
                    }
            action (str)     --  Action to perform. Either &#39;add&#39; or &#39;edit&#39;.
            plan (str)       --  Name of plan to be selected for adding category.
                                //Default: None. Required for adding category.

        Raises:
            SDKException:
                if response is not success
                if response is returned with errors
        &#34;&#34;&#34;

        self.custom_counter = 0

        def get_field_number(field_name):
            &#34;&#34;&#34; Gets the indexed number for each type of field&#34;&#34;&#34;
            numbers = {
                &#34;User Display Name&#34;: 1,
                &#34;User SMTP Address&#34;: 2,
                &#34;User Geo Location&#34;: 3,
                &#34;License&#34;: 4
            }
            return numbers.get(field_name, None)

        def get_field_type(field_name):
            &#34;&#34;&#34; Returns the mapped field_type of given field_name &#34;&#34;&#34;
            types = {
                &#34;User Display Name&#34;: 5,
                &#34;User SMTP Address&#34;: 5,
                &#34;User Geo Location&#34;: 1,
                &#34;License&#34;: 1
            }
            return types.get(field_name, None)

        def get_field_operator(cc_rule_operator):
            &#34;&#34;&#34; Gets the corresponding number assigned to each operator &#34;&#34;&#34;
            operators = {
                &#34;Contains&#34;: 0,
                &#34;Regular Expression&#34;: 1,
                &#34;Starts With&#34;: 3,
                &#34;Ends With&#34;: 4,
                &#34;Equals&#34;: 1000,
                &#34;Not Equal&#34;: 1001
            }
            return operators.get(cc_rule_operator, None)

        def get_cc_rule_type(field_type):
            &#34;&#34;&#34;  Gets the type of field in English words &#34;&#34;&#34;
            if field_type == 1:
                return &#34;Generic&#34;
            elif field_type == 5:
                return &#34;String&#34;
            else:
                return &#34;Unknown&#34;

        def get_mask(cc_rule_mask, cc_rule_name):
            &#34;&#34;&#34; Gets the masked name of Custom category rule &#34;&#34;&#34;
            if cc_rule_name != &#34;User Geo Location&#34;:
                if cc_rule_name == &#34;License&#34;:
                    if cc_rule_mask != &#34;Active&#34;:
                        return &#34;ActiveRevoked&#34;
                return cc_rule_mask
            else:
                # Extract mask from the brackets in CCRuleMask
                match = re.search(r&#39;\((.*?)\)&#39;, cc_rule_mask)
                if match:
                    return match.group(1)
                else:
                    return None

        # Get o365 plan object and ID
        if action == &#39;add&#39;:
            plan_name = plan.strip()
            o365_plan_object = self._commcell_object.plans.get(plan_name)
            o365_plan_id = int(o365_plan_object.plan_id)
        else:
            # Fetch plan details for the given category in case of edit
            groups, _ = self.browse_for_content(discovery_type=31)
            plan_name = groups[custom_dict[&#39;name&#39;]].get(&#39;planName&#39;, &#34;&#34;)
            o365_plan_object = self._commcell_object.plans.get(plan_name)
            o365_plan_id = int(o365_plan_object.plan_id)
            categoryNumber = groups[custom_dict[&#39;name&#39;]].get(
                &#39;categoryNumber&#39;, None)

        # Get Instance, client, Subclient Ids
        instance_id = int(self._instance_object.instance_id)
        client_id = int(self._client_object.client_id)
        subclient_id = int(self.subclient_id)

        conditions = []
        for entry in custom_dict[&#34;rules&#34;]:
            self.custom_counter += 1
            condition = {
                &#34;uniqueId&#34;: f&#34;CC_{self.custom_counter}&#34;,
                &#34;fieldSource&#34;: &#34;OD_KnownFields&#34;,
                &#34;fieldName&#34;: entry[&#34;CCRuleName&#34;],
                &#34;fieldNumber&#34;: get_field_number(entry[&#34;CCRuleName&#34;]),
                &#34;fieldType&#34;: get_field_type(entry[&#34;CCRuleName&#34;]),
                &#34;fieldOperator&#34;: get_field_operator(entry[&#34;CCRuleOperator&#34;]),
                &#34;mask&#34;: get_mask(entry[&#34;CCRuleMask&#34;], entry[&#34;CCRuleName&#34;]),
                &#34;CCRuleName&#34;: entry[&#34;CCRuleName&#34;],
                &#34;CCRuleOperator&#34;: entry[&#34;CCRuleOperator&#34;],
                &#34;CCRuleType&#34;: get_cc_rule_type(get_field_type(entry[&#34;CCRuleName&#34;])),
                &#34;CCRuleMask&#34;: entry[&#34;CCRuleMask&#34;]
            }
            conditions.append(condition)

        req_json = {
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: subclient_id
            },
            &#34;planEntity&#34;: {
                &#34;planId&#34;: o365_plan_id,
                &#34;planName&#34;: plan_name if action == &#39;edit&#39; else &#34;&#34;
            },
            &#34;status&#34;: 0,
            &#34;categoryName&#34;: custom_dict[&#39;name&#39;],
            &#34;categoryQuery&#34;: {
                &#34;conditions&#34;: conditions
            },
            &#34;office365V2AutoDiscover&#34;: {
                &#34;launchAutoDiscover&#34;: True,
                &#34;appType&#34;: 134,
                &#34;clientId&#34;: client_id,
                &#34;instanceId&#34;: instance_id,
                &#34;instanceType&#34;: 7
            }
        }

        if action == &#39;add&#39;:
            url = self._services[&#39;CUSTOM_CATEGORY&#39;] % subclient_id
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, url, req_json)
        elif action == &#39;edit&#39;:
            url = self._services[&#39;CUSTOM_CATEGORY&#39;] % subclient_id + \
                &#34;/&#34; + str(categoryNumber)
            flag, response = self._cvpysdk_object.make_request(
                &#39;PUT&#39;, url, req_json)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#34;Invalid action. Must be either &#39;add&#39; or &#39;edit&#39;.&#34;)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to {action} group\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def update_custom_categories_association_properties(self, category_name, operation):
        &#34;&#34;&#34;
        Updates the association properties of custom category

                Args:
                    category_name (str)     --  Display name of custom category
                    operation (int)         --  type of operation to be performed
                                                 Example:
                                                          0 - Enable
                                                          1 - Remove
                                                          2 - Disable

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by Onedrive On-Premise Instance

        &#34;&#34;&#34;

        if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

        # Get Instance, client, Subclient Ids
        instance_id = int(self._instance_object.instance_id)
        client_id = int(self._client_object.client_id)
        client_name = self._client_object.client_name
        subclient_id = int(self.subclient_id)
        url = self._services[&#39;CUSTOM_CATEGORIES&#39;] % subclient_id

        # Get the category number
        groups, numberOfGroups = self.browse_for_content(discovery_type=31)
        category_number = groups[category_name].get(&#39;categoryNumber&#39;, None)

        if not category_number:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Please ensure the category name given is valid&#39;)

        request_json = {
            &#34;updateCategoryNumbers&#34;: [category_number],
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: subclient_id,
                &#34;clientName&#34;: client_name
            },
            &#34;office365V2AutoDiscover&#34;: {
                &#34;launchAutoDiscover&#34;: True,
                &#34;appType&#34;: 134,
                &#34;clientId&#34;: client_id,
                &#34;instanceId&#34;: instance_id
            },
            &#34;status&#34;: operation
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, url, request_json
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add group\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def refresh_retention_stats(self, subclient_id):
        &#34;&#34;&#34;
        refresh the retention stats for the client

        Args:
            subclient_id(int)             : subclient id of the client
        &#34;&#34;&#34;
        request_json = {
            &#34;appType&#34;: OneDriveConstants.INDEX_APP_TYPE,
            &#34;subclientId&#34;: int(subclient_id)
        }
        refresh_retention = self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, refresh_retention, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to refresh retention stats \nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
                else:
                    self.log.info(&#34;refresh retention stats successful&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def refresh_client_level_stats(self, subclient_id):
        &#34;&#34;&#34;
        refresh the client level stats for the client

        Args:
            subclient_id(int)             : subclient id of the client

        &#34;&#34;&#34;
        request_json = {
            &#34;appType&#34;: OneDriveConstants.INDEX_APP_TYPE,
            &#34;oneDriveIdxStatsReq&#34;:
                [{
                    &#34;subclientId&#34;: int(subclient_id), &#34;type&#34;: 0}]
        }
        refresh_backup_stats = self._services[&#39;OFFICE365_POPULATE_INDEX_STATS&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, refresh_backup_stats, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to refresh client level stats \nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
                else:
                    self.log.info(&#34;refresh client level stats successful&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def get_client_level_stats(self, backupset_id):
        &#34;&#34;&#34;
        Returns the client level stats for the client

        Args:
            backupset_id(int)             : backupset id of the client

        Retruns:

            response(json)                : returns the client level stats as a json response
        &#34;&#34;&#34;
        get_backup_stats = self._services[&#39;OFFICE365_OVERVIEW_STATS&#39;] % backupset_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, get_backup_stats)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to get client level stats \nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
                else:
                    self.log.info(&#34;get client level stats successful&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

        return response.json()

    def run_trueup_for_single_user(self, user, associated_plan, current_timestamp=int(time.time())):
        &#34;&#34;&#34;Runs the true up for a single user

        Args:
            user (str) -- SMTP address of user
            current_timestamp (int) -- Current timestamp, The time at which you want to run trueup, default current time
            associated_plan (str) -- Name of the plan associated with the subclient

        Raises:
            SDKException:
                if there is an error in TrueUp API call
        &#34;&#34;&#34;
        subclient_id = int(self.subclient_id)
        client_id = int(self._client_object.client_id)
        plan_name = self._subclient_properties.get(&#39;planName&#39;)
        plan_name = associated_plan.strip()
        o365_plan_object = self._commcell_object.plans.get(plan_name)
        o365_plan_id = int(o365_plan_object.plan_id)
        user_response = self.search_for_user(user)
        user_guid = user_response[0].get(&#39;user&#39;).get(&#39;userGUID&#39;)
        base_url = self._commcell_object.webconsole_hostname
        url = self._services[&#39;RUN_TRUEUP&#39;].format(base_url)
        trueup_json = {
            &#34;processinginstructioninfo&#34;: {
                &#34;formatFlags&#34;: {
                    &#34;skipIdToNameConversion&#34;: True
                }
            },
            &#34;isEnterprise&#34;: True,
            &#34;discoveryContent&#34;: [
                {
                    &#34;userAccounts&#34;: [
                        {
                            &#34;lastBackup&#34;: str(current_timestamp),
                            &#34;planId&#34;: o365_plan_id,
                            &#34;userGuid&#34;: user_guid,
                            &#34;email&#34;: user
                        }
                    ]
                }
            ],
            &#34;discoverySentTypes&#34;: [
                20
            ],
            &#34;subclientDetails&#34;: {
                &#34;instanceId&#34;: 7,
                &#34;subclientId&#34;: subclient_id,
                &#34;clientId&#34;: client_id,
                &#34;applicationId&#34;: 134
            }
        }

        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;,
                                                           url=url, payload=trueup_json)
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to run trueup with \nError: {error_message}&#39;
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def read_trueup_results_for_single_user(self, userSMTPaddress):
        &#34;&#34;&#34;Reads the true up results for a single user

        Args:
        userSMTPaddres  (str) -- the email address of the user for trueup

        Returns:
            File_names (list) -- of file names that were captured in the trueup

            [&#39;file1&#39;, &#39;file2&#39;, &#39;file3&#39;]

        Raises:
            SDKException:
                if there is an error in TrueUp API call
        &#34;&#34;&#34;

        def extract_file_name_from_json(json_data):
            &#34;&#34;&#34;Method to extract file name from json

            Args:
                json_data (dict) -- json data from the TrueUp API

            Returns:
                file_names (list) -- list of file names that were captured in the trueup
            &#34;&#34;&#34;
            file_names = []
            rows = json_data.get(&#39;rows&#39;, [])

            for row in rows:
                row_data = row.get(&#39;row&#39;, [])
                if len(row_data) &gt;= 1:  # Ensure there is at least one element
                    file_names.append(row_data[0])
            return file_names

        subclient_id = self.subclient_id
        client_id = self._client_object.client_id
        user_response = self.search_for_user(userSMTPaddress)
        user_guid = user_response[0].get(&#39;user&#39;).get(&#39;userGUID&#39;)
        base_url = self._commcell_object.webconsole_hostname
        url = self._services[&#39;READ_TRUEUP_RESULTS_USER&#39;].format(base_url)
        url = url % (subclient_id, client_id, user_guid)
        flag, response = self._cvpysdk_object.make_request(
            method=&#39;GET&#39;, url=url)
        if flag:
            File_names = extract_file_name_from_json(
                response.json())
            return File_names
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def read_trueup_results_for_all_users(self, usersSMTPaddress):
        &#34;&#34;&#34;Method to read the api call from the TrueUp and find the deleted files

        Args:
            usersSMTPaddress (list) -- of all the email addresses of the users for trueup

        Returns:
            all_deleted_files (list) -- list of all the files that were captured by trueUp

        &#34;&#34;&#34;
        all_deleted_files = []
        for user in usersSMTPaddress:
            deleted_files_for_user = self.read_trueup_results_for_single_user(
                user)
            all_deleted_files += deleted_files_for_user
        return all_deleted_files

    def preview_backedup_file(self, file_path):
        &#34;&#34;&#34;Gets the preview content for onedrive subclient.
            Params:
                file_path (str) -- file path of the file for which preview content is needed

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self._get_preview(file_path)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient"><code class="flex name class">
<span>class <span class="ident">OneDriveSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from CloudAppsSubclient Base class, representing a OneDrive subclient,
and to perform operations on that subclient.</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L113-L2089" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OneDriveSubclient(CloudAppsSubclient):
    &#34;&#34;&#34;Derived class from CloudAppsSubclient Base class, representing a OneDrive subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of File System subclient..&#34;&#34;&#34;
        super(OneDriveSubclient, self)._get_subclient_properties()
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

        content = []
        group_list = []

        for account in self._content:
            temp_account = account[&#34;cloudconnectorContent&#34;][&#34;includeAccounts&#34;]

            if temp_account[&#39;contentType&#39;] == AppIDAType.CLOUD_APP.value:
                content_dict = {
                    &#39;SMTPAddress&#39;: temp_account[&#34;contentName&#34;].split(&#34;;&#34;)[0],
                    &#39;display_name&#39;: temp_account[&#34;contentValue&#34;]
                }

                content.append(content_dict)
            if temp_account[&#39;contentType&#39;] == 135:
                group_list.append(temp_account[&#34;contentName&#34;])
        self._ca_content = content
        self._ca_groups = group_list

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;

        return {&#39;subClientProperties&#39;: self._subclient_properties}

    def _association_users_json(self, users_list):
        &#34;&#34;&#34;
            Args:
                users_list (list) : list of SMTP addresses of users
            Returns:
                users_json(list): Required details of users to backup
        &#34;&#34;&#34;
        users_json = []
        for user_smtp in users_list:
            user_details = self._get_user_details(user_smtp)
            user_info = {
                &#34;user&#34;: {
                    &#34;userGUID&#34;: user_details[0].get(&#39;user&#39;, {}).get(&#39;userGUID&#39;)
                }
            }
            users_json.append(user_info)
        return users_json

    def _task_json_for_onedrive_backup(self, users_list, custom_groups_list=[]):
        &#34;&#34;&#34;
        Json for onedrive backup for selected users

        Args:
                users_list (list) : list of SMTP addresses of users
                custom_groups_list (list) : list of custom category groups
        &#34;&#34;&#34;
        groups, _ = self.browse_for_content(discovery_type=31)
        associated_users_json = self._association_users_json(users_list)

        associated_custom_groups_json = []
        if len(custom_groups_list) != 0:
            for group in custom_groups_list:
                group_info = {
                    &#34;id&#34;: groups[group].get(&#39;id&#39;, None),
                    &#34;name&#34;: group
                }
                associated_custom_groups_json.append(group_info)

        advanced_options_dict = {
            &#39;cloudAppOptions&#39;: {
                &#39;userAccounts&#39;: associated_users_json,
                &#39;userGroups&#39;: associated_custom_groups_json
            }
        }

        selected_items = []
        for user_smtp in users_list:
            details = self._get_user_details(user_smtp)
            item = {
                &#34;itemName&#34;: details[0].get(&#39;displayName&#39;),
                &#34;itemType&#34;: &#34;User&#34;
            }
            selected_items.append(item)

        for group in custom_groups_list:
            item = {
                &#34;itemName&#34;: group,
                &#34;itemtype&#34;: &#34;Custom category&#34;
            }
            selected_items.append(item)

        common_options_dict = {
            &#34;jobMetadata&#34;: [
                {
                    &#34;selectedItems&#34;: selected_items,
                    &#34;jobOptionItems&#34;: [
                        {
                            &#34;option&#34;: &#34;Total running time&#34;,
                            &#34;value&#34;: &#34;Disabled&#34;
                        }
                    ]
                }
            ]
        }

        task_json = self._backup_json(backup_level=&#39;INCREMENTAL&#39;, incremental_backup=False, incremental_level=&#39;BEFORE_SYNTH&#39;,
                                      advanced_options=advanced_options_dict, common_backup_options=common_options_dict)
        return task_json

    def _task_json_for_backup(self,**kwargs):
        &#34;&#34;&#34;
        Json for onedrive backup
        &#34;&#34;&#34;

        items_selection_option = kwargs.get(&#39;items_selection_option&#39;, &#39;&#39;)

        common_options_dict={
            &#34;jobMetadata&#34;: [
                {
                    &#34;selectedItems&#34;: [
                  {
                    &#34;itemName&#34;: &#34;All%20users&#34;,
                    &#34;itemType&#34;: &#34;All users&#34;
                  }
                ],
                    &#34;jobOptionItems&#34;: [
                        {
                            &#34;option&#34;: &#34;Total running time&#34;,
                            &#34;value&#34;: &#34;Disabled&#34;
                        }
                    ]
                }
            ]
        }
        if items_selection_option!=&#39;&#39;:
            common_options_dict[&#34;itemsSelectionOption&#34;]=items_selection_option

        task_json = self._backup_json(backup_level=&#39;INCREMENTAL&#39;,incremental_backup=False,incremental_level=&#39;BEFORE_SYNTH&#39;,common_backup_options=common_options_dict)
        return task_json

    @property
    def content(self):
        &#34;&#34;&#34;Returns the subclient content dict&#34;&#34;&#34;
        return self._ca_content

    @property
    def groups(self):
        &#34;&#34;&#34;Returns the list of groups assigned to the subclient if any.
        Groups are assigned only if auto discovery is enabled for groups.

            Returns:

                list - list of groups associated with the subclient

        &#34;&#34;&#34;
        return self._ca_groups

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update content of a
            Cloud Apps Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient
                                              contains the account info for each user in list.

                                              example temp_content_dict={
                                                &#34;cloudconnectorContent&#34;: {
                                                  &#34;includeAccounts&#34;: {
                                                    &#34;contentValue&#34;: Automation User,
                                                    &#34;contentType&#34;: 134,
                                                    &#34;contentName&#34;: <EMAIL>
                                                     }
                                                  }
                                              }

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        content = []

        try:
            for account in subclient_content:
                temp_content_dict = {
                    &#34;cloudconnectorContent&#34;: {
                        &#34;includeAccounts&#34;: {
                            &#34;contentValue&#34;: account[&#39;display_name&#39;],
                            &#34;contentType&#34;: AppIDAType.CLOUD_APP.value,
                            &#34;contentName&#34;: account[&#39;SMTPAddress&#39;]
                        }
                    }
                }

                content.append(temp_content_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;{} not given in content&#39;.format(err))

        self._set_subclient_properties(&#34;_content&#34;, content)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            to_disk=False):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_disk             (bool)       --  If True, restore to disk will be performed

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity

        return self._instance_object.restore_out_of_place(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            to_disk=to_disk
        )

    def discover(self, discover_type=&#39;USERS&#39;):
        &#34;&#34;&#34;This method discovers the users/groups on OneDrive

                Args:

                    discover_type (str)  --  Type of discovery

                        Valid Values are

                        -   USERS
                        -   GROUPS

                        Default: USERS

                Returns:

                    List (list)  --  List of users on GSuite account

                Raises:
                    SDKException:
                        if response is empty

                        if response is not success


        &#34;&#34;&#34;

        if discover_type.upper() == &#39;USERS&#39;:
            disc_type = 10
        elif discover_type.upper() == &#39;GROUPS&#39;:
            disc_type = 5
        _get_users = self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self._instance_object.instance_id,
                                                              self._client_object.client_id,
                                                              disc_type)

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _get_users)
        if flag:
            if response.json() and &#34;scDiscoveryContent&#34; in response.json():
                self._discover_properties = response.json()[
                    &#34;scDiscoveryContent&#34;][0]

                if &#34;contentInfo&#34; in self._discover_properties:
                    self._contentInfo = self._discover_properties[&#34;contentInfo&#34;]
                return self._contentInfo
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def set_auto_discovery(self, value):
        &#34;&#34;&#34;Sets the auto discovery value for subclient.
        You can either set a RegEx value or a user group,
        depending on the auto discovery type selected at instance level.

            Args:

                value   (list)  --  List of RegEx or user groups

        &#34;&#34;&#34;

        if not isinstance(value, list):
            raise SDKException(&#39;Subclient&#39;, &#39;116&#39;)

        if not self._instance_object.auto_discovery_status:
            raise SDKException(&#39;Subclient&#39;, &#39;117&#39;)

        subclient_prop = self._subclient_properties[&#39;cloudAppsSubClientProp&#39;].copy(
        )
        if self._instance_object.auto_discovery_mode == 0:
            # RegEx based auto discovery is enabled on instance

            subclient_prop[&#39;oneDriveSubclient&#39;][&#39;regularExp&#39;] = value
            self._set_subclient_properties(
                &#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]&#34;, subclient_prop)
        else:
            # User group based auto discovery is enabled on instance
            grp_list = []
            groups = self.discover(discover_type=&#39;GROUPS&#39;)
            for item in value:
                for group in groups:
                    if group[&#39;contentName&#39;].lower() == item.lower():
                        grp_list.append({
                            &#34;cloudconnectorContent&#34;: {
                                &#34;includeAccounts&#34;: group
                            }
                        })
            self._content.extend(grp_list)
            self._set_subclient_properties(
                &#34;_subclient_properties[&#39;content&#39;]&#34;, self._content)
        self.refresh()

    def run_subclient_discovery(self):
        &#34;&#34;&#34;
            This method launches AutoDiscovery on the subclient
        &#34;&#34;&#34;

        discover_type = 15
        discover_users = self._services[&#39;GET_CLOUDAPPS_ONEDRIVE_USERS&#39;] % (self._instance_object.instance_id,
                                                                           self._client_object.client_id,
                                                                           discover_type,
                                                                           self.subclient_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, discover_users)
        if response.status_code != 200 and response.status_code != 500:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def add_AD_group(self, value):
        &#34;&#34;&#34;Adds the user group to the subclient if auto discovery type selected
            AD group at instance level.
                Args:
                    value   (list)  --  List of user groups
        &#34;&#34;&#34;
        grp_list = []
        groups = self.discover(discover_type=&#39;GROUPS&#39;)
        for item in value:
            for group in groups:
                if group[&#39;contentName&#39;].lower() == item.lower():
                    grp_list.append(group)

        contentinfo = []

        for grp in grp_list:
            info = {
                &#34;contentValue&#34;: grp[&#39;contentValue&#39;],
                &#34;contentType&#34;: grp[&#39;contentType&#39;],
                &#34;contentName&#34;: grp[&#39;contentName&#39;]
            }
            contentinfo.append(info)

        request_json = {
            &#34;App_DiscoveryContent&#34;: {
                &#34;scDiscoveryContent&#34;: [
                    {
                        &#34;scEntity&#34;: {
                            &#34;subclientId&#34;: self.subclient_id
                        },
                        &#34;contentInfo&#34;: contentinfo
                    }
                ]
            }
        }
        add_ADgroup = self._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, add_ADgroup, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def add_user(self, user_name):
        &#34;&#34;&#34;This method adds one drive user to the subclient
                Args:
                    user_name   (str)  --  Onedrive user name
        &#34;&#34;&#34;
        users = self.discover(discover_type=&#39;USERS&#39;)

        for user in users:
            if user[&#39;contentName&#39;].lower() == user_name.lower():
                user_dict = user
                break

        request_json = {
            &#34;App_DiscoveryContent&#34;: {
                &#34;scDiscoveryContent&#34;: [
                    {
                        &#34;scEntity&#34;: {
                            &#34;subclientId&#34;: self.subclient_id
                        },
                        &#34;contentInfo&#34;: [
                            {
                                &#34;contentValue&#34;: user_dict[&#39;contentValue&#39;],
                                &#34;contentType&#34;: user_dict[&#39;contentType&#39;],
                                &#34;contentName&#34;: user_dict[&#39;contentName&#39;]
                            }
                        ]
                    }
                ]
            }
        }

        add_user = self._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, add_user, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = &#39;Failed to user to the subclient\nError: &#34;{0}&#34;&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                       output_string.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def _get_subclient_users(self):
        &#34;&#34;&#34;Method to get the users in the subclient

            Returns:
                List of Users in subclient
        &#34;&#34;&#34;
        users = []
        result = self.content
        for user in result:
            users.append(user[&#39;SMTPAddress&#39;])
        return users

    @property
    def get_subclient_users(self):
        &#34;&#34;&#34;Returns the users in subclient&#34;&#34;&#34;
        return self._get_subclient_users()

    def add_ad_group_onedrive_for_business_client(self, value, plan_name):
        &#34;&#34;&#34; Adds given OneDrive group to v2 client

            Args:

                value (string) : Group name

                plan_name (str) : O365 plan name to associate with users

            Raises:

                SDKException:

                    if response is not success

                    if response is returned with errors
        &#34;&#34;&#34;
        # Get o365plan
        plan_name = plan_name.strip()
        o365_plan_object = self._commcell_object.plans.get(plan_name)
        o365_plan_id = int(o365_plan_object.plan_id)

        # Get client id
        client_id = int(self._client_object.client_id)

        groups = []
        group_response = self.search_for_group(group_id=value)
        display_name = group_response[0].get(&#39;name&#39;)
        group_id = group_response[0].get(&#39;id&#39;)

        groups.append({
            &#34;name&#34;: display_name,
            &#34;id&#34;: group_id
        })

        request_json = {
            &#34;LaunchAutoDiscovery&#34;: True,
            &#34;cloudAppAssociation&#34;: {
                &#34;accountStatus&#34;: 0,
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: client_id,
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 2,
                    &#34;groups&#34;: groups
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: o365_plan_id
                }
            }
        }

        user_associations = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, user_associations, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add group\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def add_users_onedrive_for_business_client(self, users, plan_name):
        &#34;&#34;&#34; Adds given OneDrive users to v2 client

            Args:

                users (list) : List of user&#39;s SMTP address

                plan_name (str) : O365 plan name to associate with users

            Raises:

                SDKException:

                    if response is not success

                    if response is returned with errors
        &#34;&#34;&#34;

        if not (isinstance(users, list) and isinstance(plan_name, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Get o365plan
        plan_name = plan_name.strip()
        o365_plan_object = self._commcell_object.plans.get(plan_name)
        o365_plan_id = int(o365_plan_object.plan_id)

        # Get client ID
        client_id = int(self._client_object.client_id)

        user_accounts = []

        for user_id in users:
            # Get user details
            user_response = self.search_for_user(user_id)
            display_name = user_response[0].get(&#39;displayName&#39;)
            user_guid = user_response[0].get(&#39;user&#39;).get(&#39;userGUID&#39;)
            is_auto_discovered_user = user_response[0].get(
                &#39;isAutoDiscoveredUser&#39;)
            is_super_admin = user_response[0].get(&#39;isSuperAdmin&#39;)

            user_accounts.append({
                &#34;displayName&#34;: display_name,
                &#34;isSuperAdmin&#34;: is_super_admin,
                &#34;smtpAddress&#34;: user_id,
                &#34;isAutoDiscoveredUser&#34;: is_auto_discovered_user,
                &#34;associated&#34;: False,
                &#34;commonFlags&#34;: 0,
                &#34;user&#34;: {
                    &#34;userGUID&#34;: user_guid
                }
            })

        request_json = {
            &#34;LaunchAutoDiscovery&#34;: False,
            &#34;cloudAppAssociation&#34;: {
                &#34;accountStatus&#34;: 0,
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: client_id,
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 1,
                    &#34;userAccounts&#34;: user_accounts
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: o365_plan_id
                }
            }
        }

        user_associations = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, user_associations, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add user\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def verify_discovery_onedrive_for_business_client(self):
        &#34;&#34;&#34; Verifies that discovery is complete

            Returns:

                discovery_stats (tuple):

                    discovery_status (bool): True if users are discovered else returns False

                    total_records (int):     Number of users fetched, returns -1 if discovery is not complete

            Raises:

                 SDKException:

                        if response is not success

                        if response received does not contain pagining info
        &#34;&#34;&#34;

        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        # determines the number of accounts to return in response
        page_size = 1
        discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, discover_query)

        if flag:
            no_of_records = -1
            if response and response.json():
                if &#39;pagingInfo&#39; in response.json():
                    no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                    if no_of_records &gt; 0:
                        return True, no_of_records
            return False, no_of_records
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def search_for_user(self, user_id):
        &#34;&#34;&#34; Searches for a specific user&#39;s details from discovered list

            Args:
                user_id (str) : user&#39;s SMTP address

            Returns:

                user_accounts (list): user details&#39; list fetched from discovered content
                              eg: [
                                      {
                                        &#39;displayName&#39;: &#39;&#39;,
                                        &#39;smtpAddress&#39;: &#39;&#39;,
                                        &#39;isSuperAdmin&#39;: False,
                                        &#39;isAutoDiscoveredUser&#39;: False,
                                        &#39;commonFlags&#39;: 0,
                                        &#39;user&#39;: {
                                            &#39;_type_&#39;: 13,
                                             &#39;userGUID&#39;: &#39;UserGuid&#39;
                                             }
                                       }
                                  ]

            Raises:

                SDKException:

                    if discovery is not complete

                    if invalid SMTP address is passed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        search_query = f&#39;{browse_content}&amp;search={user_id}&#39;

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

        if flag:
            if response and response.json():
                if &#39;userAccounts&#39; in response.json():
                    user_accounts = response.json().get(&#39;userAccounts&#39;, [])
                    if len(user_accounts) == 0:
                        error_string = &#39;Either discovery is not complete or user is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                    return user_accounts
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                                       &#39;Check if the user provided is valid&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def search_for_group(self, group_id):
        &#34;&#34;&#34; Searches for a specific group details from discovered list

            Args:
                group_id (str) : group name

            Returns:

                groups (list): group details&#39; list fetched from discovered content
                              eg: [
                                      {
                                         &#34;name&#34;: &#34;&#34;,
                                         &#34;id&#34;: &#34;&#34;
                                       }
                                  ]

            Raises:

                SDKException:

                    if discovery is not complete

                    if invalid SMTP address is passed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self._instance_object.instance_id,
                                                                   self._client_object.client_id,
                                                                   5))

        search_query = f&#39;{browse_content}&amp;search={group_id}&#39;

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

        if flag:
            if response and response.json():
                if &#39;groups&#39; in response.json():
                    groups = response.json().get(&#39;groups&#39;, [])
                    if len(groups) == 0:
                        error_string = &#39;Either discovery is not complete or group is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                    return groups
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                                       &#39;Check if the group provided is valid&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def disk_restore_onedrive_for_business_client(self, users, destination_client, destination_path, skip_file_permissions=False):
        &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                destination_client (str) : client where the users need to be restored
                destination_path (str) : Destination folder location
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;disk_restore&#39;: True,
            &#39;destination_path&#39;: destination_path,
            &#39;destination_client&#39;: destination_client,
            &#39;skip_file_permissions&#39;: skip_file_permissions
        }
        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)
        return self._process_restore_response(restore_json)

    def out_of_place_restore_onedrive_for_business_client(self, users, destination_path, **kwargs):
        &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                destination_path (str) : SMTP address of destination user
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, True)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)

        if overwrite and restore_as_copy:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;out_of_place&#39;: True,
            &#39;destination_path&#39;: destination_path,
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions,
            &#39;include_deleted_items&#39;: include_deleted_items
        }
        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)

        return self._process_restore_response(restore_json)

    def in_place_restore_onedrive_syntex(self, users, fast_restore_point=False):
        &#34;&#34;&#34; Runs an in-place restore job for specified users on Syntex OneDrive for business client

            Args:
                users (list)                    :  List of SMTP addresses of users
                fast_restore_point   (booL)     : Whether to use fast restore point or not
                                                  default: False


            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if restore job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        user_details = {}
        for user in users:
            user_details[user] = self.search_for_user(user)

        self._instance_object._restore_association = self._subClientEntity

        syntex_restore_items = []
        for key, value in user_details.items():
            syntex_restore_items.append({
                &#34;displayName&#34;: value[0][&#34;displayName&#34;],
                &#34;email&#34;: value[0][&#34;smtpAddress&#34;],
                &#34;guid&#34;: value[0][&#34;user&#34;][&#34;userGUID&#34;],
                &#34;rawId&#34;: value[0][&#34;user&#34;][&#34;userGUID&#34;],
                &#34;restoreType&#34;: 1
            })

        source_user_list = self._get_user_guids(users)
        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list)

        # Get the current time in UTC
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_timestamp = int(current_time.timestamp())
        current_iso_format = current_time.strftime(
            &#39;%Y-%m-%dT%H:%M:%S.%f&#39;)[:-3] + &#39;Z&#39;

        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
            &#34;msSyntexRestoreOptions&#34;] = {
            &#34;msSyntexRestoreItems&#34;: {
                &#34;listMsSyntexRestoreItems&#34;: syntex_restore_items
            },
            &#34;restoreDate&#34;: {
                &#34;time&#34;: current_timestamp,
                &#34;timeValue&#34;: current_iso_format
            },
            &#34;restorePointId&#34;: &#34;&#34;,
            &#34;restoreType&#34;: 1,
            &#34;useFastRestorePoint&#34;: fast_restore_point
        }

        return self._process_restore_response(restore_json)

    def in_place_restore_onedrive_for_business_client(self, users, **kwargs):
        &#34;&#34;&#34; Runs an in-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) :  List of SMTP addresses of users
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, True)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)
        if overwrite and restore_as_copy:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions,
            &#39;include_deleted_items&#39;: include_deleted_items
        }

        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)
        return self._process_restore_response(restore_json)

    def _get_user_guids(self, users):
        &#34;&#34;&#34; Retrieve GUIDs for users specified

            Args:
                user (list) : List of SMTP addresses of users

            Returns:
                user_guid_list (list) : list of GUIDs of specified users

            Raises:
                SDKException:
                    if user details couldn&#39;t be found in discovered data
        &#34;&#34;&#34;
        user_guid_list = []
        for user_id in users:
            user = self.search_for_user(user_id)
            if len(user) != 0 and user[0].get(&#39;user&#39;, {}).get(&#39;userGUID&#39;) is not None:
                user_guid_list.append(user[0].get(&#39;user&#39;).get(&#39;userGUID&#39;))
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                   &#39;User details not found in discovered data&#39;)
        return user_guid_list

    def process_index_retention_rules(self, index_app_type_id, index_server_client_name):
        &#34;&#34;&#34;
         Makes API call to process index retention rules

         Args:

            index_app_type_id           (int)   --   index app type id

            index_server_client_name    (str)   --  client name of index server

         Raises:

                SDKException:

                    if index server not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if self._commcell_object.clients.has_client(index_server_client_name):
            index_server_client_id = int(
                self._commcell_object.clients[index_server_client_name.lower()][&#39;id&#39;])
            request_json = {
                &#34;appType&#34;: index_app_type_id,
                &#34;indexServerClientId&#34;: index_server_client_id
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json(
                            )[&#39;response&#39;][&#39;errorString&#39;]
                            o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(
                                error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                                   self._update_response_(response.text))
        else:
            raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)

    def point_in_time_in_place_restore_onedrive_for_business_client(self, users, end_time, **kwargs):
        &#34;&#34;&#34; Runs an in-place point in time restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) :  List of SMTP addresses of users
                end_time (int) : Backup job end time
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;

        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)

        if overwrite and restore_as_copy:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions
        }

        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)

        adv_search_bkp_time_dict = {
            &#34;field&#34;: &#34;BACKUPTIME&#34;,
            &#34;fieldValues&#34;: {
                &#34;values&#34;: [
                    &#34;0&#34;,
                    str(end_time)
                ]
            },
            &#34;intraFieldOp&#34;: &#34;FTOr&#34;
        }

        add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
        add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
        add_backup_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
            &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
        add_backup_time.append(adv_search_bkp_time_dict)

        return self._instance_object._process_restore_response(restore_json)

    def point_in_time_out_of_place_restore_onedrive_for_business_client(self, users, end_time, destination_path, **kwargs):
        &#34;&#34;&#34; Runs an out-of-place point in time restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                end_time (int) : Backup job end time
                destination_path (str) : SMTP address of destination user
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)

        if overwrite and restore_as_copy:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;out_of_place&#39;: True,
            &#39;destination_path&#39;: destination_path,
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions
        }
        restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
            source_user_list, **kwargs)

        adv_search_bkp_time_dict = {
            &#34;field&#34;: &#34;BACKUPTIME&#34;,
            &#34;fieldValues&#34;: {
                &#34;values&#34;: [
                    &#34;0&#34;,
                    str(end_time)
                ]
            },
            &#34;intraFieldOp&#34;: &#34;FTOr&#34;
        }

        add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
        add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
        add_backup_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
            &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
        add_backup_time.append(adv_search_bkp_time_dict)

        return self._process_restore_response(restore_json)

    def run_user_level_backup_onedrive_for_business_client(self, users_list, custom_groups_list=[]):
        &#34;&#34;&#34;
        Runs the backup for the users in users list/ custom categories list
        Args:
                users_list (list) : list of SMTP addresses of users
                custom_groups_list (lis) : list of custom categories

        Returns:
                object - instance of the Job class for this backup job

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        task_json = self._task_json_for_onedrive_backup(
            users_list, custom_groups_list)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def run_backup_onedrive_for_business_client(self,**kwargs):
        &#34;&#34;&#34;
                Runs the backup


                 **kwargs (dict) : Additional parameters
                    items_selection_option (str) : Item Selection Option


                Returns:
                        object - instance of the Job class for this backup job

                Raises:
                    SDKException:
                        if response is empty

                        if response is not success

                &#34;&#34;&#34;
        items_selection_option=kwargs.get(&#39;items_selection_option&#39;, &#39;&#39;)

        kwargs = {
            &#39;items_selection_option&#39;: items_selection_option
        }

        task_json = self._task_json_for_backup(**kwargs)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def _get_user_details(self, user):
        &#34;&#34;&#34;
        gets user details from discovery
        Args:
                user (str) : SMTP address of user

        Returns:
                user_details  (dict) : User&#39;s details fetched from discovery

        Raises:
            SDKException:
                if response is empty
        &#34;&#34;&#34;
        user_details = self.search_for_user(user)
        if len(user_details) != 0:
            return user_details
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;User details not found in discovered data&#39;)

    def _get_group_details(self, group):
        &#34;&#34;&#34;
        gets group details from discovery
        Args:
                group (str) : SMTP address of group
        &#34;&#34;&#34;
        group_details = self.search_for_group(group)
        if len(group_details) != 0:
            return group_details
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;Group details not found in discovered data&#39;)

    def browse_for_content(self, discovery_type, include_deleted=False):
        &#34;&#34;&#34;Returns the Onedrive client content i.e. users/ group information that is discovered in auto discovery phase

                Args:

                    discovery_type  (int)   --  type of discovery for content
                                                For all Associated users = 1
                                                For all Associated groups = 2
                                                For all Custom category groups = 31

                    include_deleted  (bool)  -- If True, deleted items will also be included

                Returns:

                    user_dict     (dict)    --  dictionary of users properties

                    no_of_records   (int)   --  no of records

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by Onedrive On-Premise Instance

        &#34;&#34;&#34;
        if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

        self._USER_POLICY_ASSOCIATION = self._services[&#39;USER_POLICY_ASSOCIATION&#39;]
        request_json = {
            &#34;discoverByType&#34;: discovery_type,
            &#34;bIncludeDeleted&#34;: include_deleted,
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                }
            },
            &#34;searchInfo&#34;: {
                &#34;isSearch&#34;: 0,
                &#34;searchKey&#34;: &#34;&#34;
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._USER_POLICY_ASSOCIATION, request_json
        )
        if flag:
            if response and response.json():
                no_of_records = 0
                if &#39;associations&#39; in response.json():
                    no_of_records = response.json().get(&#39;associations&#39;, [{}])[0].get(&#39;pagingInfo&#39;, {}). \
                        get(&#39;totalRecords&#39;, -1)
                elif &#39;pagingInfo&#39; in response.json():
                    no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                    if no_of_records &lt;= 0:
                        return {}, no_of_records
                associations = response.json().get(&#39;associations&#39;, [{}])
                user_dict = {}
                if discovery_type == 2 or discovery_type == 31:
                    if associations:
                        for group in associations:
                            group_name = group.get(
                                &#34;groups&#34;, {}).get(&#34;name&#34;, &#34;&#34;)
                            user_dict[group_name] = {
                                &#39;accountStatus&#39;: group.get(&#34;accountStatus&#34;),
                                &#39;discoverByType&#39;: group.get(&#34;discoverByType&#34;),
                                &#39;planName&#39;: group.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;),
                                &#39;id&#39;: group.get(&#34;groups&#34;, {}).get(&#34;id&#34;, &#34;&#34;),
                                &#39;categoryNumber&#39;: group.get(&#34;groups&#34;, {}).get(&#34;categoryNumber&#34;, None)
                            }
                else:
                    if associations:
                        for user in associations:
                            user_url = user.get(&#34;userAccountInfo&#34;, {}).get(
                                &#34;smtpAddress&#34;, &#34;&#34;)
                            user_account_info = user.get(&#34;userAccountInfo&#34;, {})
                            user_dict[user_url] = {
                                &#39;userAccountInfo&#39;: user_account_info,
                                &#39;accountStatus&#39;: user.get(&#34;accountStatus&#34;),
                                &#39;discoverByType&#39;: user.get(&#34;discoverByType&#34;),
                                &#39;planName&#39;: user.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;),
                                &#39;lastBackupTime&#39;: user.get(&#34;userAccountInfo&#34;, {}).get(&#34;lastBackupJobRanTime&#34;, {}).get(
                                    &#34;time&#34;, None)
                            }
                return user_dict, no_of_records
            return {}, 0
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))

    def _set_properties_to_update_site_association(self, operation):
        &#34;&#34;&#34;Updates the association properties of user

            Args:

               operation (int)                  --  type of operation to be performed
                                                     Example: 1 - Associate
                                                              2 - Enable
                                                              3 - Disable
                                                              4 - Remove

            Raises:

            SDKException:

                if the method is called by Onedrive On-Premise Instance

        &#34;&#34;&#34;
        if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

        properties_dict = {}
        if operation == 1:
            properties_dict[&#34;accountStatus&#34;] = 0
        elif operation == 2:
            properties_dict[&#34;accountStatus&#34;] = 0
        elif operation == 3:
            properties_dict[&#34;accountStatus&#34;] = 2
        elif operation == 4:
            properties_dict[&#34;accountStatus&#34;] = 1
        return properties_dict

    def update_users_association_properties(self, operation, **kwargs):
        &#34;&#34;&#34;Updates the association properties of user

                Args:
                    operation (int)         --  type of operation to be performed
                                                 Example: 1 - Associate
                                                          2 - Enable
                                                          3 - Disable
                                                          4 - Remove

                    Additional arguments (kwargs):
                    user_accounts_list (list)   --  list of user accounts
                                                    It has all information of users

                    groups_list (list)      --  list of groups
                                                It has all information of groups

                    plan_id (int)           --  id of Office 365 plan

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by Onedrive On-Premise Instance

        &#34;&#34;&#34;
        plan_id = kwargs.get(&#39;plan_id&#39;, None)
        user_accounts_list = kwargs.get(&#39;user_accounts_list&#39;, None)
        groups_list = kwargs.get(&#39;groups_list&#39;, None)

        if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

        properties_dict = self._set_properties_to_update_site_association(
            operation)
        self._ASSOCIATE_CONTENT = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
        if user_accounts_list:
            request_json = {
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 1,
                        &#34;userAccounts&#34;: user_accounts_list
                    }
                }
            }
        if groups_list:
            request_json = {
                &#34;LaunchAutoDiscovery&#34;: True,
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 2,
                        &#34;groups&#34;: groups_list
                    }
                }
            }
        if properties_dict.get(&#39;accountStatus&#39;, None) is not None:
            request_json[&#39;cloudAppAssociation&#39;][&#39;accountStatus&#39;] = properties_dict[&#39;accountStatus&#39;]
        if plan_id:
            request_json[&#39;cloudAppAssociation&#39;][&#39;plan&#39;] = {
                &#34;planId&#34;: int(plan_id)
            }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._ASSOCIATE_CONTENT, request_json
        )
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json().get(&#39;response&#39;, {}).get(&#39;errorString&#39;, str())
                        o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                               self._update_response_(response.text))

    def manage_custom_category(self, custom_dict, action, plan=None):
        &#34;&#34;&#34;
        Adds or Edits Custom category in the office 365 app.

        Args:
            custom_dict (dict)  --  dictionary of custom category name and rule details.
                Example:
                    {
                    &#34;name&#34;:&#34;Display name contains custom&#34;
                    &#34;rules&#34;:
                        [
                            {
                            &#34;CCRuleName&#34;:&#34;User Display Name&#34;,
                            &#34;CCRuleOperator&#34;:&#34;Contains&#34;,
                            &#34;CCRuleMask&#34;:&#34;od_test_user&#34;
                            }
                        ]
                    }
            action (str)     --  Action to perform. Either &#39;add&#39; or &#39;edit&#39;.
            plan (str)       --  Name of plan to be selected for adding category.
                                //Default: None. Required for adding category.

        Raises:
            SDKException:
                if response is not success
                if response is returned with errors
        &#34;&#34;&#34;

        self.custom_counter = 0

        def get_field_number(field_name):
            &#34;&#34;&#34; Gets the indexed number for each type of field&#34;&#34;&#34;
            numbers = {
                &#34;User Display Name&#34;: 1,
                &#34;User SMTP Address&#34;: 2,
                &#34;User Geo Location&#34;: 3,
                &#34;License&#34;: 4
            }
            return numbers.get(field_name, None)

        def get_field_type(field_name):
            &#34;&#34;&#34; Returns the mapped field_type of given field_name &#34;&#34;&#34;
            types = {
                &#34;User Display Name&#34;: 5,
                &#34;User SMTP Address&#34;: 5,
                &#34;User Geo Location&#34;: 1,
                &#34;License&#34;: 1
            }
            return types.get(field_name, None)

        def get_field_operator(cc_rule_operator):
            &#34;&#34;&#34; Gets the corresponding number assigned to each operator &#34;&#34;&#34;
            operators = {
                &#34;Contains&#34;: 0,
                &#34;Regular Expression&#34;: 1,
                &#34;Starts With&#34;: 3,
                &#34;Ends With&#34;: 4,
                &#34;Equals&#34;: 1000,
                &#34;Not Equal&#34;: 1001
            }
            return operators.get(cc_rule_operator, None)

        def get_cc_rule_type(field_type):
            &#34;&#34;&#34;  Gets the type of field in English words &#34;&#34;&#34;
            if field_type == 1:
                return &#34;Generic&#34;
            elif field_type == 5:
                return &#34;String&#34;
            else:
                return &#34;Unknown&#34;

        def get_mask(cc_rule_mask, cc_rule_name):
            &#34;&#34;&#34; Gets the masked name of Custom category rule &#34;&#34;&#34;
            if cc_rule_name != &#34;User Geo Location&#34;:
                if cc_rule_name == &#34;License&#34;:
                    if cc_rule_mask != &#34;Active&#34;:
                        return &#34;ActiveRevoked&#34;
                return cc_rule_mask
            else:
                # Extract mask from the brackets in CCRuleMask
                match = re.search(r&#39;\((.*?)\)&#39;, cc_rule_mask)
                if match:
                    return match.group(1)
                else:
                    return None

        # Get o365 plan object and ID
        if action == &#39;add&#39;:
            plan_name = plan.strip()
            o365_plan_object = self._commcell_object.plans.get(plan_name)
            o365_plan_id = int(o365_plan_object.plan_id)
        else:
            # Fetch plan details for the given category in case of edit
            groups, _ = self.browse_for_content(discovery_type=31)
            plan_name = groups[custom_dict[&#39;name&#39;]].get(&#39;planName&#39;, &#34;&#34;)
            o365_plan_object = self._commcell_object.plans.get(plan_name)
            o365_plan_id = int(o365_plan_object.plan_id)
            categoryNumber = groups[custom_dict[&#39;name&#39;]].get(
                &#39;categoryNumber&#39;, None)

        # Get Instance, client, Subclient Ids
        instance_id = int(self._instance_object.instance_id)
        client_id = int(self._client_object.client_id)
        subclient_id = int(self.subclient_id)

        conditions = []
        for entry in custom_dict[&#34;rules&#34;]:
            self.custom_counter += 1
            condition = {
                &#34;uniqueId&#34;: f&#34;CC_{self.custom_counter}&#34;,
                &#34;fieldSource&#34;: &#34;OD_KnownFields&#34;,
                &#34;fieldName&#34;: entry[&#34;CCRuleName&#34;],
                &#34;fieldNumber&#34;: get_field_number(entry[&#34;CCRuleName&#34;]),
                &#34;fieldType&#34;: get_field_type(entry[&#34;CCRuleName&#34;]),
                &#34;fieldOperator&#34;: get_field_operator(entry[&#34;CCRuleOperator&#34;]),
                &#34;mask&#34;: get_mask(entry[&#34;CCRuleMask&#34;], entry[&#34;CCRuleName&#34;]),
                &#34;CCRuleName&#34;: entry[&#34;CCRuleName&#34;],
                &#34;CCRuleOperator&#34;: entry[&#34;CCRuleOperator&#34;],
                &#34;CCRuleType&#34;: get_cc_rule_type(get_field_type(entry[&#34;CCRuleName&#34;])),
                &#34;CCRuleMask&#34;: entry[&#34;CCRuleMask&#34;]
            }
            conditions.append(condition)

        req_json = {
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: subclient_id
            },
            &#34;planEntity&#34;: {
                &#34;planId&#34;: o365_plan_id,
                &#34;planName&#34;: plan_name if action == &#39;edit&#39; else &#34;&#34;
            },
            &#34;status&#34;: 0,
            &#34;categoryName&#34;: custom_dict[&#39;name&#39;],
            &#34;categoryQuery&#34;: {
                &#34;conditions&#34;: conditions
            },
            &#34;office365V2AutoDiscover&#34;: {
                &#34;launchAutoDiscover&#34;: True,
                &#34;appType&#34;: 134,
                &#34;clientId&#34;: client_id,
                &#34;instanceId&#34;: instance_id,
                &#34;instanceType&#34;: 7
            }
        }

        if action == &#39;add&#39;:
            url = self._services[&#39;CUSTOM_CATEGORY&#39;] % subclient_id
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, url, req_json)
        elif action == &#39;edit&#39;:
            url = self._services[&#39;CUSTOM_CATEGORY&#39;] % subclient_id + \
                &#34;/&#34; + str(categoryNumber)
            flag, response = self._cvpysdk_object.make_request(
                &#39;PUT&#39;, url, req_json)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#34;Invalid action. Must be either &#39;add&#39; or &#39;edit&#39;.&#34;)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to {action} group\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def update_custom_categories_association_properties(self, category_name, operation):
        &#34;&#34;&#34;
        Updates the association properties of custom category

                Args:
                    category_name (str)     --  Display name of custom category
                    operation (int)         --  type of operation to be performed
                                                 Example:
                                                          0 - Enable
                                                          1 - Remove
                                                          2 - Disable

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by Onedrive On-Premise Instance

        &#34;&#34;&#34;

        if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

        # Get Instance, client, Subclient Ids
        instance_id = int(self._instance_object.instance_id)
        client_id = int(self._client_object.client_id)
        client_name = self._client_object.client_name
        subclient_id = int(self.subclient_id)
        url = self._services[&#39;CUSTOM_CATEGORIES&#39;] % subclient_id

        # Get the category number
        groups, numberOfGroups = self.browse_for_content(discovery_type=31)
        category_number = groups[category_name].get(&#39;categoryNumber&#39;, None)

        if not category_number:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Please ensure the category name given is valid&#39;)

        request_json = {
            &#34;updateCategoryNumbers&#34;: [category_number],
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: subclient_id,
                &#34;clientName&#34;: client_name
            },
            &#34;office365V2AutoDiscover&#34;: {
                &#34;launchAutoDiscover&#34;: True,
                &#34;appType&#34;: 134,
                &#34;clientId&#34;: client_id,
                &#34;instanceId&#34;: instance_id
            },
            &#34;status&#34;: operation
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, url, request_json
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add group\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def refresh_retention_stats(self, subclient_id):
        &#34;&#34;&#34;
        refresh the retention stats for the client

        Args:
            subclient_id(int)             : subclient id of the client
        &#34;&#34;&#34;
        request_json = {
            &#34;appType&#34;: OneDriveConstants.INDEX_APP_TYPE,
            &#34;subclientId&#34;: int(subclient_id)
        }
        refresh_retention = self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, refresh_retention, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to refresh retention stats \nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
                else:
                    self.log.info(&#34;refresh retention stats successful&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def refresh_client_level_stats(self, subclient_id):
        &#34;&#34;&#34;
        refresh the client level stats for the client

        Args:
            subclient_id(int)             : subclient id of the client

        &#34;&#34;&#34;
        request_json = {
            &#34;appType&#34;: OneDriveConstants.INDEX_APP_TYPE,
            &#34;oneDriveIdxStatsReq&#34;:
                [{
                    &#34;subclientId&#34;: int(subclient_id), &#34;type&#34;: 0}]
        }
        refresh_backup_stats = self._services[&#39;OFFICE365_POPULATE_INDEX_STATS&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, refresh_backup_stats, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to refresh client level stats \nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
                else:
                    self.log.info(&#34;refresh client level stats successful&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def get_client_level_stats(self, backupset_id):
        &#34;&#34;&#34;
        Returns the client level stats for the client

        Args:
            backupset_id(int)             : backupset id of the client

        Retruns:

            response(json)                : returns the client level stats as a json response
        &#34;&#34;&#34;
        get_backup_stats = self._services[&#39;OFFICE365_OVERVIEW_STATS&#39;] % backupset_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, get_backup_stats)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to get client level stats \nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
                else:
                    self.log.info(&#34;get client level stats successful&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

        return response.json()

    def run_trueup_for_single_user(self, user, associated_plan, current_timestamp=int(time.time())):
        &#34;&#34;&#34;Runs the true up for a single user

        Args:
            user (str) -- SMTP address of user
            current_timestamp (int) -- Current timestamp, The time at which you want to run trueup, default current time
            associated_plan (str) -- Name of the plan associated with the subclient

        Raises:
            SDKException:
                if there is an error in TrueUp API call
        &#34;&#34;&#34;
        subclient_id = int(self.subclient_id)
        client_id = int(self._client_object.client_id)
        plan_name = self._subclient_properties.get(&#39;planName&#39;)
        plan_name = associated_plan.strip()
        o365_plan_object = self._commcell_object.plans.get(plan_name)
        o365_plan_id = int(o365_plan_object.plan_id)
        user_response = self.search_for_user(user)
        user_guid = user_response[0].get(&#39;user&#39;).get(&#39;userGUID&#39;)
        base_url = self._commcell_object.webconsole_hostname
        url = self._services[&#39;RUN_TRUEUP&#39;].format(base_url)
        trueup_json = {
            &#34;processinginstructioninfo&#34;: {
                &#34;formatFlags&#34;: {
                    &#34;skipIdToNameConversion&#34;: True
                }
            },
            &#34;isEnterprise&#34;: True,
            &#34;discoveryContent&#34;: [
                {
                    &#34;userAccounts&#34;: [
                        {
                            &#34;lastBackup&#34;: str(current_timestamp),
                            &#34;planId&#34;: o365_plan_id,
                            &#34;userGuid&#34;: user_guid,
                            &#34;email&#34;: user
                        }
                    ]
                }
            ],
            &#34;discoverySentTypes&#34;: [
                20
            ],
            &#34;subclientDetails&#34;: {
                &#34;instanceId&#34;: 7,
                &#34;subclientId&#34;: subclient_id,
                &#34;clientId&#34;: client_id,
                &#34;applicationId&#34;: 134
            }
        }

        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;,
                                                           url=url, payload=trueup_json)
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to run trueup with \nError: {error_message}&#39;
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def read_trueup_results_for_single_user(self, userSMTPaddress):
        &#34;&#34;&#34;Reads the true up results for a single user

        Args:
        userSMTPaddres  (str) -- the email address of the user for trueup

        Returns:
            File_names (list) -- of file names that were captured in the trueup

            [&#39;file1&#39;, &#39;file2&#39;, &#39;file3&#39;]

        Raises:
            SDKException:
                if there is an error in TrueUp API call
        &#34;&#34;&#34;

        def extract_file_name_from_json(json_data):
            &#34;&#34;&#34;Method to extract file name from json

            Args:
                json_data (dict) -- json data from the TrueUp API

            Returns:
                file_names (list) -- list of file names that were captured in the trueup
            &#34;&#34;&#34;
            file_names = []
            rows = json_data.get(&#39;rows&#39;, [])

            for row in rows:
                row_data = row.get(&#39;row&#39;, [])
                if len(row_data) &gt;= 1:  # Ensure there is at least one element
                    file_names.append(row_data[0])
            return file_names

        subclient_id = self.subclient_id
        client_id = self._client_object.client_id
        user_response = self.search_for_user(userSMTPaddress)
        user_guid = user_response[0].get(&#39;user&#39;).get(&#39;userGUID&#39;)
        base_url = self._commcell_object.webconsole_hostname
        url = self._services[&#39;READ_TRUEUP_RESULTS_USER&#39;].format(base_url)
        url = url % (subclient_id, client_id, user_guid)
        flag, response = self._cvpysdk_object.make_request(
            method=&#39;GET&#39;, url=url)
        if flag:
            File_names = extract_file_name_from_json(
                response.json())
            return File_names
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def read_trueup_results_for_all_users(self, usersSMTPaddress):
        &#34;&#34;&#34;Method to read the api call from the TrueUp and find the deleted files

        Args:
            usersSMTPaddress (list) -- of all the email addresses of the users for trueup

        Returns:
            all_deleted_files (list) -- list of all the files that were captured by trueUp

        &#34;&#34;&#34;
        all_deleted_files = []
        for user in usersSMTPaddress:
            deleted_files_for_user = self.read_trueup_results_for_single_user(
                user)
            all_deleted_files += deleted_files_for_user
        return all_deleted_files

    def preview_backedup_file(self, file_path):
        &#34;&#34;&#34;Gets the preview content for onedrive subclient.
            Params:
                file_path (str) -- file path of the file for which preview content is needed

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self._get_preview(file_path)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="../casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Returns the subclient content dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L261-L264" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Returns the subclient content dict&#34;&#34;&#34;
    return self._ca_content</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.get_subclient_users"><code class="name">var <span class="ident">get_subclient_users</span></code></dt>
<dd>
<div class="desc"><p>Returns the users in subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L615-L618" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def get_subclient_users(self):
    &#34;&#34;&#34;Returns the users in subclient&#34;&#34;&#34;
    return self._get_subclient_users()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.groups"><code class="name">var <span class="ident">groups</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of groups assigned to the subclient if any.
Groups are assigned only if auto discovery is enabled for groups.</p>
<pre><code>Returns:

    list - list of groups associated with the subclient
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L266-L276" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def groups(self):
    &#34;&#34;&#34;Returns the list of groups assigned to the subclient if any.
    Groups are assigned only if auto discovery is enabled for groups.

        Returns:

            list - list of groups associated with the subclient

    &#34;&#34;&#34;
    return self._ca_groups</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_AD_group"><code class="name flex">
<span>def <span class="ident">add_AD_group</span></span>(<span>self, value)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the user group to the subclient if auto discovery type selected
AD group at instance level.
Args:
value
(list)
&ndash;
List of user groups</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L504-L552" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_AD_group(self, value):
    &#34;&#34;&#34;Adds the user group to the subclient if auto discovery type selected
        AD group at instance level.
            Args:
                value   (list)  --  List of user groups
    &#34;&#34;&#34;
    grp_list = []
    groups = self.discover(discover_type=&#39;GROUPS&#39;)
    for item in value:
        for group in groups:
            if group[&#39;contentName&#39;].lower() == item.lower():
                grp_list.append(group)

    contentinfo = []

    for grp in grp_list:
        info = {
            &#34;contentValue&#34;: grp[&#39;contentValue&#39;],
            &#34;contentType&#34;: grp[&#39;contentType&#39;],
            &#34;contentName&#34;: grp[&#39;contentName&#39;]
        }
        contentinfo.append(info)

    request_json = {
        &#34;App_DiscoveryContent&#34;: {
            &#34;scDiscoveryContent&#34;: [
                {
                    &#34;scEntity&#34;: {
                        &#34;subclientId&#34;: self.subclient_id
                    },
                    &#34;contentInfo&#34;: contentinfo
                }
            ]
        }
    }
    add_ADgroup = self._services[&#39;EXECUTE_QCOMMAND&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, add_ADgroup, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_ad_group_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">add_ad_group_onedrive_for_business_client</span></span>(<span>self, value, plan_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds given OneDrive group to v2 client</p>
<h2 id="args">Args</h2>
<p>value (string) : Group name</p>
<p>plan_name (str) : O365 plan name to associate with users</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is not success

if response is returned with errors
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L620-L687" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_ad_group_onedrive_for_business_client(self, value, plan_name):
    &#34;&#34;&#34; Adds given OneDrive group to v2 client

        Args:

            value (string) : Group name

            plan_name (str) : O365 plan name to associate with users

        Raises:

            SDKException:

                if response is not success

                if response is returned with errors
    &#34;&#34;&#34;
    # Get o365plan
    plan_name = plan_name.strip()
    o365_plan_object = self._commcell_object.plans.get(plan_name)
    o365_plan_id = int(o365_plan_object.plan_id)

    # Get client id
    client_id = int(self._client_object.client_id)

    groups = []
    group_response = self.search_for_group(group_id=value)
    display_name = group_response[0].get(&#39;name&#39;)
    group_id = group_response[0].get(&#39;id&#39;)

    groups.append({
        &#34;name&#34;: display_name,
        &#34;id&#34;: group_id
    })

    request_json = {
        &#34;LaunchAutoDiscovery&#34;: True,
        &#34;cloudAppAssociation&#34;: {
            &#34;accountStatus&#34;: 0,
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: int(self.subclient_id),
                &#34;clientId&#34;: client_id,
                &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
            },
            &#34;cloudAppDiscoverinfo&#34;: {
                &#34;discoverByType&#34;: 2,
                &#34;groups&#34;: groups
            },
            &#34;plan&#34;: {
                &#34;planId&#34;: o365_plan_id
            }
        }
    }

    user_associations = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, user_associations, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to add group\nError: {error_message}&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_user"><code class="name flex">
<span>def <span class="ident">add_user</span></span>(<span>self, user_name)</span>
</code></dt>
<dd>
<div class="desc"><p>This method adds one drive user to the subclient</p>
<h2 id="args">Args</h2>
<p>user_name
(str)
&ndash;
Onedrive user name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L554-L601" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_user(self, user_name):
    &#34;&#34;&#34;This method adds one drive user to the subclient
            Args:
                user_name   (str)  --  Onedrive user name
    &#34;&#34;&#34;
    users = self.discover(discover_type=&#39;USERS&#39;)

    for user in users:
        if user[&#39;contentName&#39;].lower() == user_name.lower():
            user_dict = user
            break

    request_json = {
        &#34;App_DiscoveryContent&#34;: {
            &#34;scDiscoveryContent&#34;: [
                {
                    &#34;scEntity&#34;: {
                        &#34;subclientId&#34;: self.subclient_id
                    },
                    &#34;contentInfo&#34;: [
                        {
                            &#34;contentValue&#34;: user_dict[&#39;contentValue&#39;],
                            &#34;contentType&#34;: user_dict[&#39;contentType&#39;],
                            &#34;contentName&#34;: user_dict[&#39;contentName&#39;]
                        }
                    ]
                }
            ]
        }
    }

    add_user = self._services[&#39;EXECUTE_QCOMMAND&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, add_user, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = &#39;Failed to user to the subclient\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                   output_string.format(error_message))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_users_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">add_users_onedrive_for_business_client</span></span>(<span>self, users, plan_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds given OneDrive users to v2 client</p>
<h2 id="args">Args</h2>
<p>users (list) : List of user's SMTP address</p>
<p>plan_name (str) : O365 plan name to associate with users</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is not success

if response is returned with errors
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L689-L773" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_users_onedrive_for_business_client(self, users, plan_name):
    &#34;&#34;&#34; Adds given OneDrive users to v2 client

        Args:

            users (list) : List of user&#39;s SMTP address

            plan_name (str) : O365 plan name to associate with users

        Raises:

            SDKException:

                if response is not success

                if response is returned with errors
    &#34;&#34;&#34;

    if not (isinstance(users, list) and isinstance(plan_name, str)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    # Get o365plan
    plan_name = plan_name.strip()
    o365_plan_object = self._commcell_object.plans.get(plan_name)
    o365_plan_id = int(o365_plan_object.plan_id)

    # Get client ID
    client_id = int(self._client_object.client_id)

    user_accounts = []

    for user_id in users:
        # Get user details
        user_response = self.search_for_user(user_id)
        display_name = user_response[0].get(&#39;displayName&#39;)
        user_guid = user_response[0].get(&#39;user&#39;).get(&#39;userGUID&#39;)
        is_auto_discovered_user = user_response[0].get(
            &#39;isAutoDiscoveredUser&#39;)
        is_super_admin = user_response[0].get(&#39;isSuperAdmin&#39;)

        user_accounts.append({
            &#34;displayName&#34;: display_name,
            &#34;isSuperAdmin&#34;: is_super_admin,
            &#34;smtpAddress&#34;: user_id,
            &#34;isAutoDiscoveredUser&#34;: is_auto_discovered_user,
            &#34;associated&#34;: False,
            &#34;commonFlags&#34;: 0,
            &#34;user&#34;: {
                &#34;userGUID&#34;: user_guid
            }
        })

    request_json = {
        &#34;LaunchAutoDiscovery&#34;: False,
        &#34;cloudAppAssociation&#34;: {
            &#34;accountStatus&#34;: 0,
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: int(self.subclient_id),
                &#34;clientId&#34;: client_id,
                &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
            },
            &#34;cloudAppDiscoverinfo&#34;: {
                &#34;discoverByType&#34;: 1,
                &#34;userAccounts&#34;: user_accounts
            },
            &#34;plan&#34;: {
                &#34;planId&#34;: o365_plan_id
            }
        }
    }

    user_associations = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, user_associations, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to add user\nError: {error_message}&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.browse_for_content"><code class="name flex">
<span>def <span class="ident">browse_for_content</span></span>(<span>self, discovery_type, include_deleted=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the Onedrive client content i.e. users/ group information that is discovered in auto discovery phase</p>
<h2 id="args">Args</h2>
<p>discovery_type
(int)
&ndash;
type of discovery for content
For all Associated users = 1
For all Associated groups = 2
For all Custom category groups = 31</p>
<p>include_deleted
(bool)
&ndash; If True, deleted items will also be included</p>
<h2 id="returns">Returns</h2>
<p>user_dict
(dict)
&ndash;
dictionary of users properties</p>
<p>no_of_records
(int)
&ndash;
no of records</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success

if the method is called by Onedrive On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1386-L1477" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse_for_content(self, discovery_type, include_deleted=False):
    &#34;&#34;&#34;Returns the Onedrive client content i.e. users/ group information that is discovered in auto discovery phase

            Args:

                discovery_type  (int)   --  type of discovery for content
                                            For all Associated users = 1
                                            For all Associated groups = 2
                                            For all Custom category groups = 31

                include_deleted  (bool)  -- If True, deleted items will also be included

            Returns:

                user_dict     (dict)    --  dictionary of users properties

                no_of_records   (int)   --  no of records

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by Onedrive On-Premise Instance

    &#34;&#34;&#34;
    if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

    self._USER_POLICY_ASSOCIATION = self._services[&#39;USER_POLICY_ASSOCIATION&#39;]
    request_json = {
        &#34;discoverByType&#34;: discovery_type,
        &#34;bIncludeDeleted&#34;: include_deleted,
        &#34;cloudAppAssociation&#34;: {
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: int(self.subclient_id)
            }
        },
        &#34;searchInfo&#34;: {
            &#34;isSearch&#34;: 0,
            &#34;searchKey&#34;: &#34;&#34;
        }
    }
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._USER_POLICY_ASSOCIATION, request_json
    )
    if flag:
        if response and response.json():
            no_of_records = 0
            if &#39;associations&#39; in response.json():
                no_of_records = response.json().get(&#39;associations&#39;, [{}])[0].get(&#39;pagingInfo&#39;, {}). \
                    get(&#39;totalRecords&#39;, -1)
            elif &#39;pagingInfo&#39; in response.json():
                no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                if no_of_records &lt;= 0:
                    return {}, no_of_records
            associations = response.json().get(&#39;associations&#39;, [{}])
            user_dict = {}
            if discovery_type == 2 or discovery_type == 31:
                if associations:
                    for group in associations:
                        group_name = group.get(
                            &#34;groups&#34;, {}).get(&#34;name&#34;, &#34;&#34;)
                        user_dict[group_name] = {
                            &#39;accountStatus&#39;: group.get(&#34;accountStatus&#34;),
                            &#39;discoverByType&#39;: group.get(&#34;discoverByType&#34;),
                            &#39;planName&#39;: group.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;),
                            &#39;id&#39;: group.get(&#34;groups&#34;, {}).get(&#34;id&#34;, &#34;&#34;),
                            &#39;categoryNumber&#39;: group.get(&#34;groups&#34;, {}).get(&#34;categoryNumber&#34;, None)
                        }
            else:
                if associations:
                    for user in associations:
                        user_url = user.get(&#34;userAccountInfo&#34;, {}).get(
                            &#34;smtpAddress&#34;, &#34;&#34;)
                        user_account_info = user.get(&#34;userAccountInfo&#34;, {})
                        user_dict[user_url] = {
                            &#39;userAccountInfo&#39;: user_account_info,
                            &#39;accountStatus&#39;: user.get(&#34;accountStatus&#34;),
                            &#39;discoverByType&#39;: user.get(&#34;discoverByType&#34;),
                            &#39;planName&#39;: user.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;),
                            &#39;lastBackupTime&#39;: user.get(&#34;userAccountInfo&#34;, {}).get(&#34;lastBackupJobRanTime&#34;, {}).get(
                                &#34;time&#34;, None)
                        }
            return user_dict, no_of_records
        return {}, 0
    raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                       self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.discover"><code class="name flex">
<span>def <span class="ident">discover</span></span>(<span>self, discover_type='USERS')</span>
</code></dt>
<dd>
<div class="desc"><p>This method discovers the users/groups on OneDrive</p>
<h2 id="args">Args</h2>
<p>discover_type (str)
&ndash;
Type of discovery</p>
<pre><code>Valid Values are

-   USERS
-   GROUPS

Default: USERS
</code></pre>
<h2 id="returns">Returns</h2>
<p>List (list)
&ndash;
List of users on GSuite account</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L397-L445" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def discover(self, discover_type=&#39;USERS&#39;):
    &#34;&#34;&#34;This method discovers the users/groups on OneDrive

            Args:

                discover_type (str)  --  Type of discovery

                    Valid Values are

                    -   USERS
                    -   GROUPS

                    Default: USERS

            Returns:

                List (list)  --  List of users on GSuite account

            Raises:
                SDKException:
                    if response is empty

                    if response is not success


    &#34;&#34;&#34;

    if discover_type.upper() == &#39;USERS&#39;:
        disc_type = 10
    elif discover_type.upper() == &#39;GROUPS&#39;:
        disc_type = 5
    _get_users = self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self._instance_object.instance_id,
                                                          self._client_object.client_id,
                                                          disc_type)

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _get_users)
    if flag:
        if response.json() and &#34;scDiscoveryContent&#34; in response.json():
            self._discover_properties = response.json()[
                &#34;scDiscoveryContent&#34;][0]

            if &#34;contentInfo&#34; in self._discover_properties:
                self._contentInfo = self._discover_properties[&#34;contentInfo&#34;]
            return self._contentInfo
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.disk_restore_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">disk_restore_onedrive_for_business_client</span></span>(<span>self, users, destination_client, destination_path, skip_file_permissions=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an out-of-place restore job for specified users on OneDrive for business client
By default restore skips the files already present in destination</p>
<h2 id="args">Args</h2>
<p>users (list) : list of SMTP addresses of users
destination_client (str) : client where the users need to be restored
destination_path (str) : Destination folder location
skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L931-L954" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disk_restore_onedrive_for_business_client(self, users, destination_client, destination_path, skip_file_permissions=False):
    &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
        By default restore skips the files already present in destination

        Args:
            users (list) : list of SMTP addresses of users
            destination_client (str) : client where the users need to be restored
            destination_path (str) : Destination folder location
            skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

        Returns:
            object - instance of the Job class for this restore job
    &#34;&#34;&#34;
    self._instance_object._restore_association = self._subClientEntity
    source_user_list = self._get_user_guids(users)
    kwargs = {
        &#39;disk_restore&#39;: True,
        &#39;destination_path&#39;: destination_path,
        &#39;destination_client&#39;: destination_client,
        &#39;skip_file_permissions&#39;: skip_file_permissions
    }
    restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
        source_user_list, **kwargs)
    return self._process_restore_response(restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.get_client_level_stats"><code class="name flex">
<span>def <span class="ident">get_client_level_stats</span></span>(<span>self, backupset_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the client level stats for the client</p>
<h2 id="args">Args</h2>
<p>backupset_id(int)
: backupset id of the client</p>
<h2 id="retruns">Retruns</h2>
<p>response(json)
: returns the client level stats as a json response</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1910-L1938" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_client_level_stats(self, backupset_id):
    &#34;&#34;&#34;
    Returns the client level stats for the client

    Args:
        backupset_id(int)             : backupset id of the client

    Retruns:

        response(json)                : returns the client level stats as a json response
    &#34;&#34;&#34;
    get_backup_stats = self._services[&#39;OFFICE365_OVERVIEW_STATS&#39;] % backupset_id
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, get_backup_stats)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to get client level stats \nError: {error_message}&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
            else:
                self.log.info(&#34;get client level stats successful&#34;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))

    return response.json()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.in_place_restore_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">in_place_restore_onedrive_for_business_client</span></span>(<span>self, users, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an in-place restore job for specified users on OneDrive for business client
By default restore skips the files already present in destination</p>
<h2 id="args">Args</h2>
<p>users (list) :
List of SMTP addresses of users
**kwargs (dict) : Additional parameters
overwrite (bool) : unconditional overwrite files during restore (default: False)
restore_as_copy (bool) : restore files as copy during restore (default: False)
skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if overwrite and restore as copy file options are both selected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1064-L1102" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def in_place_restore_onedrive_for_business_client(self, users, **kwargs):
    &#34;&#34;&#34; Runs an in-place restore job for specified users on OneDrive for business client
        By default restore skips the files already present in destination

        Args:
            users (list) :  List of SMTP addresses of users
            **kwargs (dict) : Additional parameters
                overwrite (bool) : unconditional overwrite files during restore (default: False)
                restore_as_copy (bool) : restore files as copy during restore (default: False)
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:

                if overwrite and restore as copy file options are both selected
    &#34;&#34;&#34;
    overwrite = kwargs.get(&#39;overwrite&#39;, False)
    restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
    skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, True)
    include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)
    if overwrite and restore_as_copy:
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

    self._instance_object._restore_association = self._subClientEntity
    source_user_list = self._get_user_guids(users)
    kwargs = {
        &#39;overwrite&#39;: overwrite,
        &#39;restore_as_copy&#39;: restore_as_copy,
        &#39;skip_file_permissions&#39;: skip_file_permissions,
        &#39;include_deleted_items&#39;: include_deleted_items
    }

    restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
        source_user_list, **kwargs)
    return self._process_restore_response(restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.in_place_restore_onedrive_syntex"><code class="name flex">
<span>def <span class="ident">in_place_restore_onedrive_syntex</span></span>(<span>self, users, fast_restore_point=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an in-place restore job for specified users on Syntex OneDrive for business client</p>
<h2 id="args">Args</h2>
<p>users (list)
:
List of SMTP addresses of users
fast_restore_point
(booL)
: Whether to use fast restore point or not
default: False</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if restore job failed

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1000-L1062" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def in_place_restore_onedrive_syntex(self, users, fast_restore_point=False):
    &#34;&#34;&#34; Runs an in-place restore job for specified users on Syntex OneDrive for business client

        Args:
            users (list)                    :  List of SMTP addresses of users
            fast_restore_point   (booL)     : Whether to use fast restore point or not
                                              default: False


        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:

                if restore job failed

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    user_details = {}
    for user in users:
        user_details[user] = self.search_for_user(user)

    self._instance_object._restore_association = self._subClientEntity

    syntex_restore_items = []
    for key, value in user_details.items():
        syntex_restore_items.append({
            &#34;displayName&#34;: value[0][&#34;displayName&#34;],
            &#34;email&#34;: value[0][&#34;smtpAddress&#34;],
            &#34;guid&#34;: value[0][&#34;user&#34;][&#34;userGUID&#34;],
            &#34;rawId&#34;: value[0][&#34;user&#34;][&#34;userGUID&#34;],
            &#34;restoreType&#34;: 1
        })

    source_user_list = self._get_user_guids(users)
    restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
        source_user_list)

    # Get the current time in UTC
    current_time = datetime.datetime.now(datetime.timezone.utc)
    current_timestamp = int(current_time.timestamp())
    current_iso_format = current_time.strftime(
        &#39;%Y-%m-%dT%H:%M:%S.%f&#39;)[:-3] + &#39;Z&#39;

    restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
        &#34;msSyntexRestoreOptions&#34;] = {
        &#34;msSyntexRestoreItems&#34;: {
            &#34;listMsSyntexRestoreItems&#34;: syntex_restore_items
        },
        &#34;restoreDate&#34;: {
            &#34;time&#34;: current_timestamp,
            &#34;timeValue&#34;: current_iso_format
        },
        &#34;restorePointId&#34;: &#34;&#34;,
        &#34;restoreType&#34;: 1,
        &#34;useFastRestorePoint&#34;: fast_restore_point
    }

    return self._process_restore_response(restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.manage_custom_category"><code class="name flex">
<span>def <span class="ident">manage_custom_category</span></span>(<span>self, custom_dict, action, plan=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds or Edits Custom category in the office 365 app.</p>
<h2 id="args">Args</h2>
<p>custom_dict (dict)
&ndash;
dictionary of custom category name and rule details.
Example:
{
"name":"Display name contains custom"
"rules":
[
{
"CCRuleName":"User Display Name",
"CCRuleOperator":"Contains",
"CCRuleMask":"od_test_user"
}
]
}
action (str)
&ndash;
Action to perform. Either 'add' or 'edit'.
plan (str)
&ndash;
Name of plan to be selected for adding category.
//Default: None. Required for adding category.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is not success
if response is returned with errors</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1605-L1774" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def manage_custom_category(self, custom_dict, action, plan=None):
    &#34;&#34;&#34;
    Adds or Edits Custom category in the office 365 app.

    Args:
        custom_dict (dict)  --  dictionary of custom category name and rule details.
            Example:
                {
                &#34;name&#34;:&#34;Display name contains custom&#34;
                &#34;rules&#34;:
                    [
                        {
                        &#34;CCRuleName&#34;:&#34;User Display Name&#34;,
                        &#34;CCRuleOperator&#34;:&#34;Contains&#34;,
                        &#34;CCRuleMask&#34;:&#34;od_test_user&#34;
                        }
                    ]
                }
        action (str)     --  Action to perform. Either &#39;add&#39; or &#39;edit&#39;.
        plan (str)       --  Name of plan to be selected for adding category.
                            //Default: None. Required for adding category.

    Raises:
        SDKException:
            if response is not success
            if response is returned with errors
    &#34;&#34;&#34;

    self.custom_counter = 0

    def get_field_number(field_name):
        &#34;&#34;&#34; Gets the indexed number for each type of field&#34;&#34;&#34;
        numbers = {
            &#34;User Display Name&#34;: 1,
            &#34;User SMTP Address&#34;: 2,
            &#34;User Geo Location&#34;: 3,
            &#34;License&#34;: 4
        }
        return numbers.get(field_name, None)

    def get_field_type(field_name):
        &#34;&#34;&#34; Returns the mapped field_type of given field_name &#34;&#34;&#34;
        types = {
            &#34;User Display Name&#34;: 5,
            &#34;User SMTP Address&#34;: 5,
            &#34;User Geo Location&#34;: 1,
            &#34;License&#34;: 1
        }
        return types.get(field_name, None)

    def get_field_operator(cc_rule_operator):
        &#34;&#34;&#34; Gets the corresponding number assigned to each operator &#34;&#34;&#34;
        operators = {
            &#34;Contains&#34;: 0,
            &#34;Regular Expression&#34;: 1,
            &#34;Starts With&#34;: 3,
            &#34;Ends With&#34;: 4,
            &#34;Equals&#34;: 1000,
            &#34;Not Equal&#34;: 1001
        }
        return operators.get(cc_rule_operator, None)

    def get_cc_rule_type(field_type):
        &#34;&#34;&#34;  Gets the type of field in English words &#34;&#34;&#34;
        if field_type == 1:
            return &#34;Generic&#34;
        elif field_type == 5:
            return &#34;String&#34;
        else:
            return &#34;Unknown&#34;

    def get_mask(cc_rule_mask, cc_rule_name):
        &#34;&#34;&#34; Gets the masked name of Custom category rule &#34;&#34;&#34;
        if cc_rule_name != &#34;User Geo Location&#34;:
            if cc_rule_name == &#34;License&#34;:
                if cc_rule_mask != &#34;Active&#34;:
                    return &#34;ActiveRevoked&#34;
            return cc_rule_mask
        else:
            # Extract mask from the brackets in CCRuleMask
            match = re.search(r&#39;\((.*?)\)&#39;, cc_rule_mask)
            if match:
                return match.group(1)
            else:
                return None

    # Get o365 plan object and ID
    if action == &#39;add&#39;:
        plan_name = plan.strip()
        o365_plan_object = self._commcell_object.plans.get(plan_name)
        o365_plan_id = int(o365_plan_object.plan_id)
    else:
        # Fetch plan details for the given category in case of edit
        groups, _ = self.browse_for_content(discovery_type=31)
        plan_name = groups[custom_dict[&#39;name&#39;]].get(&#39;planName&#39;, &#34;&#34;)
        o365_plan_object = self._commcell_object.plans.get(plan_name)
        o365_plan_id = int(o365_plan_object.plan_id)
        categoryNumber = groups[custom_dict[&#39;name&#39;]].get(
            &#39;categoryNumber&#39;, None)

    # Get Instance, client, Subclient Ids
    instance_id = int(self._instance_object.instance_id)
    client_id = int(self._client_object.client_id)
    subclient_id = int(self.subclient_id)

    conditions = []
    for entry in custom_dict[&#34;rules&#34;]:
        self.custom_counter += 1
        condition = {
            &#34;uniqueId&#34;: f&#34;CC_{self.custom_counter}&#34;,
            &#34;fieldSource&#34;: &#34;OD_KnownFields&#34;,
            &#34;fieldName&#34;: entry[&#34;CCRuleName&#34;],
            &#34;fieldNumber&#34;: get_field_number(entry[&#34;CCRuleName&#34;]),
            &#34;fieldType&#34;: get_field_type(entry[&#34;CCRuleName&#34;]),
            &#34;fieldOperator&#34;: get_field_operator(entry[&#34;CCRuleOperator&#34;]),
            &#34;mask&#34;: get_mask(entry[&#34;CCRuleMask&#34;], entry[&#34;CCRuleName&#34;]),
            &#34;CCRuleName&#34;: entry[&#34;CCRuleName&#34;],
            &#34;CCRuleOperator&#34;: entry[&#34;CCRuleOperator&#34;],
            &#34;CCRuleType&#34;: get_cc_rule_type(get_field_type(entry[&#34;CCRuleName&#34;])),
            &#34;CCRuleMask&#34;: entry[&#34;CCRuleMask&#34;]
        }
        conditions.append(condition)

    req_json = {
        &#34;subclientEntity&#34;: {
            &#34;subclientId&#34;: subclient_id
        },
        &#34;planEntity&#34;: {
            &#34;planId&#34;: o365_plan_id,
            &#34;planName&#34;: plan_name if action == &#39;edit&#39; else &#34;&#34;
        },
        &#34;status&#34;: 0,
        &#34;categoryName&#34;: custom_dict[&#39;name&#39;],
        &#34;categoryQuery&#34;: {
            &#34;conditions&#34;: conditions
        },
        &#34;office365V2AutoDiscover&#34;: {
            &#34;launchAutoDiscover&#34;: True,
            &#34;appType&#34;: 134,
            &#34;clientId&#34;: client_id,
            &#34;instanceId&#34;: instance_id,
            &#34;instanceType&#34;: 7
        }
    }

    if action == &#39;add&#39;:
        url = self._services[&#39;CUSTOM_CATEGORY&#39;] % subclient_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, url, req_json)
    elif action == &#39;edit&#39;:
        url = self._services[&#39;CUSTOM_CATEGORY&#39;] % subclient_id + \
            &#34;/&#34; + str(categoryNumber)
        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, url, req_json)
    else:
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#34;Invalid action. Must be either &#39;add&#39; or &#39;edit&#39;.&#34;)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to {action} group\nError: {error_message}&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.out_of_place_restore_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">out_of_place_restore_onedrive_for_business_client</span></span>(<span>self, users, destination_path, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an out-of-place restore job for specified users on OneDrive for business client
By default restore skips the files already present in destination</p>
<h2 id="args">Args</h2>
<p>users (list) : list of SMTP addresses of users
destination_path (str) : SMTP address of destination user
**kwargs (dict) : Additional parameters
overwrite (bool) : unconditional overwrite files during restore (default: False)
restore_as_copy (bool) : restore files as copy during restore (default: False)
skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if overwrite and restore as copy file options are both selected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L956-L998" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def out_of_place_restore_onedrive_for_business_client(self, users, destination_path, **kwargs):
    &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
        By default restore skips the files already present in destination

        Args:
            users (list) : list of SMTP addresses of users
            destination_path (str) : SMTP address of destination user
            **kwargs (dict) : Additional parameters
                overwrite (bool) : unconditional overwrite files during restore (default: False)
                restore_as_copy (bool) : restore files as copy during restore (default: False)
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:

                if overwrite and restore as copy file options are both selected
    &#34;&#34;&#34;
    overwrite = kwargs.get(&#39;overwrite&#39;, False)
    restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
    skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, True)
    include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)

    if overwrite and restore_as_copy:
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

    self._instance_object._restore_association = self._subClientEntity
    source_user_list = self._get_user_guids(users)
    kwargs = {
        &#39;out_of_place&#39;: True,
        &#39;destination_path&#39;: destination_path,
        &#39;overwrite&#39;: overwrite,
        &#39;restore_as_copy&#39;: restore_as_copy,
        &#39;skip_file_permissions&#39;: skip_file_permissions,
        &#39;include_deleted_items&#39;: include_deleted_items
    }
    restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
        source_user_list, **kwargs)

    return self._process_restore_response(restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.point_in_time_in_place_restore_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">point_in_time_in_place_restore_onedrive_for_business_client</span></span>(<span>self, users, end_time, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an in-place point in time restore job for specified users on OneDrive for business client
By default restore skips the files already present in destination</p>
<h2 id="args">Args</h2>
<p>users (list) :
List of SMTP addresses of users
end_time (int) : Backup job end time
**kwargs (dict) : Additional parameters
overwrite (bool) : unconditional overwrite files during restore (default: False)
restore_as_copy (bool) : restore files as copy during restore (default: False)
skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if overwrite and restore as copy file options are both selected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1178-L1234" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def point_in_time_in_place_restore_onedrive_for_business_client(self, users, end_time, **kwargs):
    &#34;&#34;&#34; Runs an in-place point in time restore job for specified users on OneDrive for business client
        By default restore skips the files already present in destination

        Args:
            users (list) :  List of SMTP addresses of users
            end_time (int) : Backup job end time
            **kwargs (dict) : Additional parameters
                overwrite (bool) : unconditional overwrite files during restore (default: False)
                restore_as_copy (bool) : restore files as copy during restore (default: False)
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:

                if overwrite and restore as copy file options are both selected
    &#34;&#34;&#34;

    overwrite = kwargs.get(&#39;overwrite&#39;, False)
    restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
    skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)

    if overwrite and restore_as_copy:
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

    self._instance_object._restore_association = self._subClientEntity
    source_user_list = self._get_user_guids(users)
    kwargs = {
        &#39;overwrite&#39;: overwrite,
        &#39;restore_as_copy&#39;: restore_as_copy,
        &#39;skip_file_permissions&#39;: skip_file_permissions
    }

    restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
        source_user_list, **kwargs)

    adv_search_bkp_time_dict = {
        &#34;field&#34;: &#34;BACKUPTIME&#34;,
        &#34;fieldValues&#34;: {
            &#34;values&#34;: [
                &#34;0&#34;,
                str(end_time)
            ]
        },
        &#34;intraFieldOp&#34;: &#34;FTOr&#34;
    }

    add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
    add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
    add_backup_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
        &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
    add_backup_time.append(adv_search_bkp_time_dict)

    return self._instance_object._process_restore_response(restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.point_in_time_out_of_place_restore_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">point_in_time_out_of_place_restore_onedrive_for_business_client</span></span>(<span>self, users, end_time, destination_path, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an out-of-place point in time restore job for specified users on OneDrive for business client
By default restore skips the files already present in destination</p>
<h2 id="args">Args</h2>
<p>users (list) : list of SMTP addresses of users
end_time (int) : Backup job end time
destination_path (str) : SMTP address of destination user
**kwargs (dict) : Additional parameters
overwrite (bool) : unconditional overwrite files during restore (default: False)
restore_as_copy (bool) : restore files as copy during restore (default: False)
skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if overwrite and restore as copy file options are both selected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1236-L1294" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def point_in_time_out_of_place_restore_onedrive_for_business_client(self, users, end_time, destination_path, **kwargs):
    &#34;&#34;&#34; Runs an out-of-place point in time restore job for specified users on OneDrive for business client
        By default restore skips the files already present in destination

        Args:
            users (list) : list of SMTP addresses of users
            end_time (int) : Backup job end time
            destination_path (str) : SMTP address of destination user
            **kwargs (dict) : Additional parameters
                overwrite (bool) : unconditional overwrite files during restore (default: False)
                restore_as_copy (bool) : restore files as copy during restore (default: False)
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:

                if overwrite and restore as copy file options are both selected
    &#34;&#34;&#34;
    overwrite = kwargs.get(&#39;overwrite&#39;, False)
    restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
    skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)

    if overwrite and restore_as_copy:
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

    self._instance_object._restore_association = self._subClientEntity
    source_user_list = self._get_user_guids(users)
    kwargs = {
        &#39;out_of_place&#39;: True,
        &#39;destination_path&#39;: destination_path,
        &#39;overwrite&#39;: overwrite,
        &#39;restore_as_copy&#39;: restore_as_copy,
        &#39;skip_file_permissions&#39;: skip_file_permissions
    }
    restore_json = self._instance_object._prepare_restore_json_onedrive_for_business_client(
        source_user_list, **kwargs)

    adv_search_bkp_time_dict = {
        &#34;field&#34;: &#34;BACKUPTIME&#34;,
        &#34;fieldValues&#34;: {
            &#34;values&#34;: [
                &#34;0&#34;,
                str(end_time)
            ]
        },
        &#34;intraFieldOp&#34;: &#34;FTOr&#34;
    }

    add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
    add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
    add_backup_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
        &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
    add_backup_time.append(adv_search_bkp_time_dict)

    return self._process_restore_response(restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.preview_backedup_file"><code class="name flex">
<span>def <span class="ident">preview_backedup_file</span></span>(<span>self, file_path)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the preview content for onedrive subclient.</p>
<h2 id="params">Params</h2>
<p>file_path (str) &ndash; file path of the file for which preview content is needed</p>
<h2 id="returns">Returns</h2>
<p>html
(str)
&ndash;
html content of the preview</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if file is not found</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L2073-L2089" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def preview_backedup_file(self, file_path):
    &#34;&#34;&#34;Gets the preview content for onedrive subclient.
        Params:
            file_path (str) -- file path of the file for which preview content is needed

        Returns:
            html   (str)   --  html content of the preview

        Raises:
            SDKException:
                if file is not found

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    return self._get_preview(file_path)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.process_index_retention_rules"><code class="name flex">
<span>def <span class="ident">process_index_retention_rules</span></span>(<span>self, index_app_type_id, index_server_client_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Makes API call to process index retention rules</p>
<h2 id="args">Args</h2>
<p>index_app_type_id
(int)
&ndash;
index app type id</p>
<p>index_server_client_name
(str)
&ndash;
client name of index server</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if index server not found

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1127-L1176" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def process_index_retention_rules(self, index_app_type_id, index_server_client_name):
    &#34;&#34;&#34;
     Makes API call to process index retention rules

     Args:

        index_app_type_id           (int)   --   index app type id

        index_server_client_name    (str)   --  client name of index server

     Raises:

            SDKException:

                if index server not found

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if self._commcell_object.clients.has_client(index_server_client_name):
        index_server_client_id = int(
            self._commcell_object.clients[index_server_client_name.lower()][&#39;id&#39;])
        request_json = {
            &#34;appType&#34;: index_app_type_id,
            &#34;indexServerClientId&#34;: index_server_client_id
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
        )
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json(
                        )[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))
    else:
        raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.read_trueup_results_for_all_users"><code class="name flex">
<span>def <span class="ident">read_trueup_results_for_all_users</span></span>(<span>self, usersSMTPaddress)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to read the api call from the TrueUp and find the deleted files</p>
<h2 id="args">Args</h2>
<p>usersSMTPaddress (list) &ndash; of all the email addresses of the users for trueup</p>
<h2 id="returns">Returns</h2>
<p>all_deleted_files (list) &ndash; list of all the files that were captured by trueUp</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L2056-L2071" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def read_trueup_results_for_all_users(self, usersSMTPaddress):
    &#34;&#34;&#34;Method to read the api call from the TrueUp and find the deleted files

    Args:
        usersSMTPaddress (list) -- of all the email addresses of the users for trueup

    Returns:
        all_deleted_files (list) -- list of all the files that were captured by trueUp

    &#34;&#34;&#34;
    all_deleted_files = []
    for user in usersSMTPaddress:
        deleted_files_for_user = self.read_trueup_results_for_single_user(
            user)
        all_deleted_files += deleted_files_for_user
    return all_deleted_files</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.read_trueup_results_for_single_user"><code class="name flex">
<span>def <span class="ident">read_trueup_results_for_single_user</span></span>(<span>self, userSMTPaddress)</span>
</code></dt>
<dd>
<div class="desc"><p>Reads the true up results for a single user</p>
<p>Args:
userSMTPaddres
(str) &ndash; the email address of the user for trueup</p>
<h2 id="returns">Returns</h2>
<p>File_names (list) &ndash; of file names that were captured in the trueup</p>
<p>['file1', 'file2', 'file3']</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if there is an error in TrueUp API call</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L2005-L2054" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def read_trueup_results_for_single_user(self, userSMTPaddress):
    &#34;&#34;&#34;Reads the true up results for a single user

    Args:
    userSMTPaddres  (str) -- the email address of the user for trueup

    Returns:
        File_names (list) -- of file names that were captured in the trueup

        [&#39;file1&#39;, &#39;file2&#39;, &#39;file3&#39;]

    Raises:
        SDKException:
            if there is an error in TrueUp API call
    &#34;&#34;&#34;

    def extract_file_name_from_json(json_data):
        &#34;&#34;&#34;Method to extract file name from json

        Args:
            json_data (dict) -- json data from the TrueUp API

        Returns:
            file_names (list) -- list of file names that were captured in the trueup
        &#34;&#34;&#34;
        file_names = []
        rows = json_data.get(&#39;rows&#39;, [])

        for row in rows:
            row_data = row.get(&#39;row&#39;, [])
            if len(row_data) &gt;= 1:  # Ensure there is at least one element
                file_names.append(row_data[0])
        return file_names

    subclient_id = self.subclient_id
    client_id = self._client_object.client_id
    user_response = self.search_for_user(userSMTPaddress)
    user_guid = user_response[0].get(&#39;user&#39;).get(&#39;userGUID&#39;)
    base_url = self._commcell_object.webconsole_hostname
    url = self._services[&#39;READ_TRUEUP_RESULTS_USER&#39;].format(base_url)
    url = url % (subclient_id, client_id, user_guid)
    flag, response = self._cvpysdk_object.make_request(
        method=&#39;GET&#39;, url=url)
    if flag:
        File_names = extract_file_name_from_json(
            response.json())
        return File_names
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.refresh_client_level_stats"><code class="name flex">
<span>def <span class="ident">refresh_client_level_stats</span></span>(<span>self, subclient_id)</span>
</code></dt>
<dd>
<div class="desc"><p>refresh the client level stats for the client</p>
<h2 id="args">Args</h2>
<p>subclient_id(int)
: subclient id of the client</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1879-L1908" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh_client_level_stats(self, subclient_id):
    &#34;&#34;&#34;
    refresh the client level stats for the client

    Args:
        subclient_id(int)             : subclient id of the client

    &#34;&#34;&#34;
    request_json = {
        &#34;appType&#34;: OneDriveConstants.INDEX_APP_TYPE,
        &#34;oneDriveIdxStatsReq&#34;:
            [{
                &#34;subclientId&#34;: int(subclient_id), &#34;type&#34;: 0}]
    }
    refresh_backup_stats = self._services[&#39;OFFICE365_POPULATE_INDEX_STATS&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, refresh_backup_stats, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to refresh client level stats \nError: {error_message}&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
            else:
                self.log.info(&#34;refresh client level stats successful&#34;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.refresh_retention_stats"><code class="name flex">
<span>def <span class="ident">refresh_retention_stats</span></span>(<span>self, subclient_id)</span>
</code></dt>
<dd>
<div class="desc"><p>refresh the retention stats for the client</p>
<h2 id="args">Args</h2>
<p>subclient_id(int)
: subclient id of the client</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1851-L1877" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh_retention_stats(self, subclient_id):
    &#34;&#34;&#34;
    refresh the retention stats for the client

    Args:
        subclient_id(int)             : subclient id of the client
    &#34;&#34;&#34;
    request_json = {
        &#34;appType&#34;: OneDriveConstants.INDEX_APP_TYPE,
        &#34;subclientId&#34;: int(subclient_id)
    }
    refresh_retention = self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, refresh_retention, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to refresh retention stats \nError: {error_message}&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
            else:
                self.log.info(&#34;refresh retention stats successful&#34;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, client, destination_path, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, to_disk=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destionation location.</p>
<h2 id="args">Args</h2>
<p>client
(str/object) &ndash;
either the name of the client or
the instance of the Client</p>
<p>destination_path
(str)
&ndash;
full path of the restore location on client</p>
<p>paths
(list)
&ndash;
list of full paths of
files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_disk
(bool)
&ndash;
If True, restore to disk will be performed</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client instance</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L321-L395" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        client,
        destination_path,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        to_disk=False):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destionation location.

        Args:
            client                (str/object) --  either the name of the client or
                                                       the instance of the Client

            destination_path      (str)        --  full path of the restore location on client

            paths                 (list)       --  list of full paths of
                                                       files/folders to restore

            overwrite             (bool)       --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl  (bool)       --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time           (str)       --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_disk             (bool)       --  If True, restore to disk will be performed

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or Client instance

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    self._instance_object._restore_association = self._subClientEntity

    return self._instance_object.restore_out_of_place(
        client=client,
        destination_path=destination_path,
        paths=paths,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        to_disk=to_disk
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_backup_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">run_backup_onedrive_for_business_client</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs the backup</p>
<p>**kwargs (dict) : Additional parameters
items_selection_option (str) : Item Selection Option</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1321-L1351" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_backup_onedrive_for_business_client(self,**kwargs):
    &#34;&#34;&#34;
            Runs the backup


             **kwargs (dict) : Additional parameters
                items_selection_option (str) : Item Selection Option


            Returns:
                    object - instance of the Job class for this backup job

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

            &#34;&#34;&#34;
    items_selection_option=kwargs.get(&#39;items_selection_option&#39;, &#39;&#39;)

    kwargs = {
        &#39;items_selection_option&#39;: items_selection_option
    }

    task_json = self._task_json_for_backup(**kwargs)
    create_task = self._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, create_task, task_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_subclient_discovery"><code class="name flex">
<span>def <span class="ident">run_subclient_discovery</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>This method launches AutoDiscovery on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L489-L502" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_subclient_discovery(self):
    &#34;&#34;&#34;
        This method launches AutoDiscovery on the subclient
    &#34;&#34;&#34;

    discover_type = 15
    discover_users = self._services[&#39;GET_CLOUDAPPS_ONEDRIVE_USERS&#39;] % (self._instance_object.instance_id,
                                                                       self._client_object.client_id,
                                                                       discover_type,
                                                                       self.subclient_id)
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, discover_users)
    if response.status_code != 200 and response.status_code != 500:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_trueup_for_single_user"><code class="name flex">
<span>def <span class="ident">run_trueup_for_single_user</span></span>(<span>self, user, associated_plan, current_timestamp=1750148081)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs the true up for a single user</p>
<h2 id="args">Args</h2>
<p>user (str) &ndash; SMTP address of user
current_timestamp (int) &ndash; Current timestamp, The time at which you want to run trueup, default current time
associated_plan (str) &ndash; Name of the plan associated with the subclient</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if there is an error in TrueUp API call</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1940-L2003" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_trueup_for_single_user(self, user, associated_plan, current_timestamp=int(time.time())):
    &#34;&#34;&#34;Runs the true up for a single user

    Args:
        user (str) -- SMTP address of user
        current_timestamp (int) -- Current timestamp, The time at which you want to run trueup, default current time
        associated_plan (str) -- Name of the plan associated with the subclient

    Raises:
        SDKException:
            if there is an error in TrueUp API call
    &#34;&#34;&#34;
    subclient_id = int(self.subclient_id)
    client_id = int(self._client_object.client_id)
    plan_name = self._subclient_properties.get(&#39;planName&#39;)
    plan_name = associated_plan.strip()
    o365_plan_object = self._commcell_object.plans.get(plan_name)
    o365_plan_id = int(o365_plan_object.plan_id)
    user_response = self.search_for_user(user)
    user_guid = user_response[0].get(&#39;user&#39;).get(&#39;userGUID&#39;)
    base_url = self._commcell_object.webconsole_hostname
    url = self._services[&#39;RUN_TRUEUP&#39;].format(base_url)
    trueup_json = {
        &#34;processinginstructioninfo&#34;: {
            &#34;formatFlags&#34;: {
                &#34;skipIdToNameConversion&#34;: True
            }
        },
        &#34;isEnterprise&#34;: True,
        &#34;discoveryContent&#34;: [
            {
                &#34;userAccounts&#34;: [
                    {
                        &#34;lastBackup&#34;: str(current_timestamp),
                        &#34;planId&#34;: o365_plan_id,
                        &#34;userGuid&#34;: user_guid,
                        &#34;email&#34;: user
                    }
                ]
            }
        ],
        &#34;discoverySentTypes&#34;: [
            20
        ],
        &#34;subclientDetails&#34;: {
            &#34;instanceId&#34;: 7,
            &#34;subclientId&#34;: subclient_id,
            &#34;clientId&#34;: client_id,
            &#34;applicationId&#34;: 134
        }
    }

    flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;,
                                                       url=url, payload=trueup_json)
    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to run trueup with \nError: {error_message}&#39;
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, output_string)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_user_level_backup_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">run_user_level_backup_onedrive_for_business_client</span></span>(<span>self, users_list, custom_groups_list=[])</span>
</code></dt>
<dd>
<div class="desc"><p>Runs the backup for the users in users list/ custom categories list</p>
<h2 id="args">Args</h2>
<p>users_list (list) : list of SMTP addresses of users
custom_groups_list (lis) : list of custom categories</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1296-L1319" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_user_level_backup_onedrive_for_business_client(self, users_list, custom_groups_list=[]):
    &#34;&#34;&#34;
    Runs the backup for the users in users list/ custom categories list
    Args:
            users_list (list) : list of SMTP addresses of users
            custom_groups_list (lis) : list of custom categories

    Returns:
            object - instance of the Job class for this backup job

    Raises:
        SDKException:
            if response is empty

            if response is not success

    &#34;&#34;&#34;
    task_json = self._task_json_for_onedrive_backup(
        users_list, custom_groups_list)
    create_task = self._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, create_task, task_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.search_for_group"><code class="name flex">
<span>def <span class="ident">search_for_group</span></span>(<span>self, group_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches for a specific group details from discovered list</p>
<h2 id="args">Args</h2>
<p>group_id (str) : group name</p>
<h2 id="returns">Returns</h2>
<dl>
<dt>groups (list): group details' list fetched from discovered content</dt>
<dt><code>
eg</code></dt>
<dd>[
{
"name": "",
"id": ""
}
]</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if discovery is not complete

if invalid SMTP address is passed

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L878-L929" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def search_for_group(self, group_id):
    &#34;&#34;&#34; Searches for a specific group details from discovered list

        Args:
            group_id (str) : group name

        Returns:

            groups (list): group details&#39; list fetched from discovered content
                          eg: [
                                  {
                                     &#34;name&#34;: &#34;&#34;,
                                     &#34;id&#34;: &#34;&#34;
                                   }
                              ]

        Raises:

            SDKException:

                if discovery is not complete

                if invalid SMTP address is passed

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    browse_content = (self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               5))

    search_query = f&#39;{browse_content}&amp;search={group_id}&#39;

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

    if flag:
        if response and response.json():
            if &#39;groups&#39; in response.json():
                groups = response.json().get(&#39;groups&#39;, [])
                if len(groups) == 0:
                    error_string = &#39;Either discovery is not complete or group is not available in discovered data&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                return groups
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                                   &#39;Check if the group provided is valid&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.search_for_user"><code class="name flex">
<span>def <span class="ident">search_for_user</span></span>(<span>self, user_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches for a specific user's details from discovered list</p>
<h2 id="args">Args</h2>
<p>user_id (str) : user's SMTP address</p>
<h2 id="returns">Returns</h2>
<dl>
<dt>user_accounts (list): user details' list fetched from discovered content</dt>
<dt><code>
eg</code></dt>
<dd>[
{
'displayName': '',
'smtpAddress': '',
'isSuperAdmin': False,
'isAutoDiscoveredUser': False,
'commonFlags': 0,
'user': {
'<em>type</em>': 13,
'userGUID': 'UserGuid'
}
}
]</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if discovery is not complete

if invalid SMTP address is passed

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L818-L876" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def search_for_user(self, user_id):
    &#34;&#34;&#34; Searches for a specific user&#39;s details from discovered list

        Args:
            user_id (str) : user&#39;s SMTP address

        Returns:

            user_accounts (list): user details&#39; list fetched from discovered content
                          eg: [
                                  {
                                    &#39;displayName&#39;: &#39;&#39;,
                                    &#39;smtpAddress&#39;: &#39;&#39;,
                                    &#39;isSuperAdmin&#39;: False,
                                    &#39;isAutoDiscoveredUser&#39;: False,
                                    &#39;commonFlags&#39;: 0,
                                    &#39;user&#39;: {
                                        &#39;_type_&#39;: 13,
                                         &#39;userGUID&#39;: &#39;UserGuid&#39;
                                         }
                                   }
                              ]

        Raises:

            SDKException:

                if discovery is not complete

                if invalid SMTP address is passed

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                           self._client_object.client_id,
                                                           AppIDAType.CLOUD_APP.value))

    search_query = f&#39;{browse_content}&amp;search={user_id}&#39;

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

    if flag:
        if response and response.json():
            if &#39;userAccounts&#39; in response.json():
                user_accounts = response.json().get(&#39;userAccounts&#39;, [])
                if len(user_accounts) == 0:
                    error_string = &#39;Either discovery is not complete or user is not available in discovered data&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                return user_accounts
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                                   &#39;Check if the user provided is valid&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.set_auto_discovery"><code class="name flex">
<span>def <span class="ident">set_auto_discovery</span></span>(<span>self, value)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the auto discovery value for subclient.
You can either set a RegEx value or a user group,
depending on the auto discovery type selected at instance level.</p>
<pre><code>Args:

    value   (list)  --  List of RegEx or user groups
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L447-L487" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_auto_discovery(self, value):
    &#34;&#34;&#34;Sets the auto discovery value for subclient.
    You can either set a RegEx value or a user group,
    depending on the auto discovery type selected at instance level.

        Args:

            value   (list)  --  List of RegEx or user groups

    &#34;&#34;&#34;

    if not isinstance(value, list):
        raise SDKException(&#39;Subclient&#39;, &#39;116&#39;)

    if not self._instance_object.auto_discovery_status:
        raise SDKException(&#39;Subclient&#39;, &#39;117&#39;)

    subclient_prop = self._subclient_properties[&#39;cloudAppsSubClientProp&#39;].copy(
    )
    if self._instance_object.auto_discovery_mode == 0:
        # RegEx based auto discovery is enabled on instance

        subclient_prop[&#39;oneDriveSubclient&#39;][&#39;regularExp&#39;] = value
        self._set_subclient_properties(
            &#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]&#34;, subclient_prop)
    else:
        # User group based auto discovery is enabled on instance
        grp_list = []
        groups = self.discover(discover_type=&#39;GROUPS&#39;)
        for item in value:
            for group in groups:
                if group[&#39;contentName&#39;].lower() == item.lower():
                    grp_list.append({
                        &#34;cloudconnectorContent&#34;: {
                            &#34;includeAccounts&#34;: group
                        }
                    })
        self._content.extend(grp_list)
        self._set_subclient_properties(
            &#34;_subclient_properties[&#39;content&#39;]&#34;, self._content)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.update_custom_categories_association_properties"><code class="name flex">
<span>def <span class="ident">update_custom_categories_association_properties</span></span>(<span>self, category_name, operation)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the association properties of custom category</p>
<pre><code>    Args:
        category_name (str)     --  Display name of custom category
        operation (int)         --  type of operation to be performed
                                     Example:
                                              0 - Enable
                                              1 - Remove
                                              2 - Disable

    Raises:

        SDKException:

            if response is empty

            if response is not success

            if the method is called by Onedrive On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1776-L1849" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_custom_categories_association_properties(self, category_name, operation):
    &#34;&#34;&#34;
    Updates the association properties of custom category

            Args:
                category_name (str)     --  Display name of custom category
                operation (int)         --  type of operation to be performed
                                             Example:
                                                      0 - Enable
                                                      1 - Remove
                                                      2 - Disable

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by Onedrive On-Premise Instance

    &#34;&#34;&#34;

    if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

    # Get Instance, client, Subclient Ids
    instance_id = int(self._instance_object.instance_id)
    client_id = int(self._client_object.client_id)
    client_name = self._client_object.client_name
    subclient_id = int(self.subclient_id)
    url = self._services[&#39;CUSTOM_CATEGORIES&#39;] % subclient_id

    # Get the category number
    groups, numberOfGroups = self.browse_for_content(discovery_type=31)
    category_number = groups[category_name].get(&#39;categoryNumber&#39;, None)

    if not category_number:
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Please ensure the category name given is valid&#39;)

    request_json = {
        &#34;updateCategoryNumbers&#34;: [category_number],
        &#34;subclientEntity&#34;: {
            &#34;subclientId&#34;: subclient_id,
            &#34;clientName&#34;: client_name
        },
        &#34;office365V2AutoDiscover&#34;: {
            &#34;launchAutoDiscover&#34;: True,
            &#34;appType&#34;: 134,
            &#34;clientId&#34;: client_id,
            &#34;instanceId&#34;: instance_id
        },
        &#34;status&#34;: operation
    }

    flag, response = self._cvpysdk_object.make_request(
        &#39;PUT&#39;, url, request_json
    )

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to add group\nError: {error_message}&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.update_users_association_properties"><code class="name flex">
<span>def <span class="ident">update_users_association_properties</span></span>(<span>self, operation, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the association properties of user</p>
<h2 id="args">Args</h2>
<p>operation (int)
&ndash;
type of operation to be performed
Example: 1 - Associate
2 - Enable
3 - Disable
4 - Remove</p>
<p>Additional arguments (kwargs):
user_accounts_list (list)
&ndash;
list of user accounts
It has all information of users</p>
<p>groups_list (list)
&ndash;
list of groups
It has all information of groups</p>
<p>plan_id (int)
&ndash;
id of Office 365 plan</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success

if the method is called by Onedrive On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L1512-L1603" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_users_association_properties(self, operation, **kwargs):
    &#34;&#34;&#34;Updates the association properties of user

            Args:
                operation (int)         --  type of operation to be performed
                                             Example: 1 - Associate
                                                      2 - Enable
                                                      3 - Disable
                                                      4 - Remove

                Additional arguments (kwargs):
                user_accounts_list (list)   --  list of user accounts
                                                It has all information of users

                groups_list (list)      --  list of groups
                                            It has all information of groups

                plan_id (int)           --  id of Office 365 plan

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by Onedrive On-Premise Instance

    &#34;&#34;&#34;
    plan_id = kwargs.get(&#39;plan_id&#39;, None)
    user_accounts_list = kwargs.get(&#39;user_accounts_list&#39;, None)
    groups_list = kwargs.get(&#39;groups_list&#39;, None)

    if not self._backupset_object._instance_object.ca_instance_type.lower() == OneDriveConstants.INSTANCE.lower():
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for Onedrive On-Premise Instance&#39;)

    properties_dict = self._set_properties_to_update_site_association(
        operation)
    self._ASSOCIATE_CONTENT = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
    if user_accounts_list:
        request_json = {
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 1,
                    &#34;userAccounts&#34;: user_accounts_list
                }
            }
        }
    if groups_list:
        request_json = {
            &#34;LaunchAutoDiscovery&#34;: True,
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 2,
                    &#34;groups&#34;: groups_list
                }
            }
        }
    if properties_dict.get(&#39;accountStatus&#39;, None) is not None:
        request_json[&#39;cloudAppAssociation&#39;][&#39;accountStatus&#39;] = properties_dict[&#39;accountStatus&#39;]
    if plan_id:
        request_json[&#39;cloudAppAssociation&#39;][&#39;plan&#39;] = {
            &#34;planId&#34;: int(plan_id)
        }
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._ASSOCIATE_CONTENT, request_json
    )
    if flag:
        if response.json():
            if &#34;resp&#34; in response.json():
                error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                if error_code != 0:
                    error_string = response.json().get(&#39;response&#39;, {}).get(&#39;errorString&#39;, str())
                    o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            elif &#39;errorMessage&#39; in response.json():
                error_string = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(
                    error_string)
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
    else:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.verify_discovery_onedrive_for_business_client"><code class="name flex">
<span>def <span class="ident">verify_discovery_onedrive_for_business_client</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Verifies that discovery is complete</p>
<h2 id="returns">Returns</h2>
<p>discovery_stats (tuple):</p>
<pre><code>discovery_status (bool): True if users are discovered else returns False

total_records (int):     Number of users fetched, returns -1 if discovery is not complete
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>   if response is not success

   if response received does not contain pagining info
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/onedrive_subclient.py#L775-L816" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def verify_discovery_onedrive_for_business_client(self):
    &#34;&#34;&#34; Verifies that discovery is complete

        Returns:

            discovery_stats (tuple):

                discovery_status (bool): True if users are discovered else returns False

                total_records (int):     Number of users fetched, returns -1 if discovery is not complete

        Raises:

             SDKException:

                    if response is not success

                    if response received does not contain pagining info
    &#34;&#34;&#34;

    browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                           self._client_object.client_id,
                                                           AppIDAType.CLOUD_APP.value))

    # determines the number of accounts to return in response
    page_size = 1
    discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&#39;

    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, discover_query)

    if flag:
        no_of_records = -1
        if response and response.json():
            if &#39;pagingInfo&#39; in response.json():
                no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                if no_of_records &gt; 0:
                    return True, no_of_records
        return False, no_of_records
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="../casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.allow_multiple_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.backup" href="../../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.browse" href="../../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.data_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.deduplication_options" href="../../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.description" href="../../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.display_name" href="../../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup_at_time" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup_days" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.encryption_flag" href="../../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.exclude_from_sla" href="../../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find" href="../../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find_latest_job" href="../../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.get_ma_associated_storagepolicy" href="../../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_blocklevel_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_intelli_snap_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_on_demand_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_trueup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.last_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.list_media" href="../../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.name" href="../../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.network_agent" href="../../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.next_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.plan" href="../../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.properties" href="../../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.read_buffer_size" href="../../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.refresh" href="../../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.restore_in_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.run_content_indexing" href="../../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_backup_nodes" href="../../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.snapshot_engine_name" href="../../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.software_compression" href="../../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma_id" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_policy" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_guid" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_id" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_name" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.unset_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.update_properties" href="../../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.cloudapps" href="index.html">cvpysdk.subclients.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient">OneDriveSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_AD_group" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_AD_group">add_AD_group</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_ad_group_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_ad_group_onedrive_for_business_client">add_ad_group_onedrive_for_business_client</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_user" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_user">add_user</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_users_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.add_users_onedrive_for_business_client">add_users_onedrive_for_business_client</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.browse_for_content" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.browse_for_content">browse_for_content</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.content" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.discover" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.discover">discover</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.disk_restore_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.disk_restore_onedrive_for_business_client">disk_restore_onedrive_for_business_client</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.get_client_level_stats" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.get_client_level_stats">get_client_level_stats</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.get_subclient_users" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.get_subclient_users">get_subclient_users</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.groups" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.groups">groups</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.in_place_restore_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.in_place_restore_onedrive_for_business_client">in_place_restore_onedrive_for_business_client</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.in_place_restore_onedrive_syntex" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.in_place_restore_onedrive_syntex">in_place_restore_onedrive_syntex</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.manage_custom_category" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.manage_custom_category">manage_custom_category</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.out_of_place_restore_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.out_of_place_restore_onedrive_for_business_client">out_of_place_restore_onedrive_for_business_client</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.point_in_time_in_place_restore_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.point_in_time_in_place_restore_onedrive_for_business_client">point_in_time_in_place_restore_onedrive_for_business_client</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.point_in_time_out_of_place_restore_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.point_in_time_out_of_place_restore_onedrive_for_business_client">point_in_time_out_of_place_restore_onedrive_for_business_client</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.preview_backedup_file" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.preview_backedup_file">preview_backedup_file</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.process_index_retention_rules" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.process_index_retention_rules">process_index_retention_rules</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.read_trueup_results_for_all_users" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.read_trueup_results_for_all_users">read_trueup_results_for_all_users</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.read_trueup_results_for_single_user" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.read_trueup_results_for_single_user">read_trueup_results_for_single_user</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.refresh_client_level_stats" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.refresh_client_level_stats">refresh_client_level_stats</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.refresh_retention_stats" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.refresh_retention_stats">refresh_retention_stats</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.restore_out_of_place" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_backup_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_backup_onedrive_for_business_client">run_backup_onedrive_for_business_client</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_subclient_discovery" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_subclient_discovery">run_subclient_discovery</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_trueup_for_single_user" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_trueup_for_single_user">run_trueup_for_single_user</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_user_level_backup_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.run_user_level_backup_onedrive_for_business_client">run_user_level_backup_onedrive_for_business_client</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.search_for_group" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.search_for_group">search_for_group</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.search_for_user" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.search_for_user">search_for_user</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.set_auto_discovery" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.set_auto_discovery">set_auto_discovery</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.update_custom_categories_association_properties" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.update_custom_categories_association_properties">update_custom_categories_association_properties</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.update_users_association_properties" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.update_users_association_properties">update_users_association_properties</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.verify_discovery_onedrive_for_business_client" href="#cvpysdk.subclients.cloudapps.onedrive_subclient.OneDriveSubclient.verify_discovery_onedrive_for_business_client">verify_discovery_onedrive_for_business_client</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>