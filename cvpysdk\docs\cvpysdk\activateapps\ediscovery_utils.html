<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activateapps.ediscovery_utils API documentation</title>
<meta name="description" content="Main file for performing operations on ediscovery clients &amp; ediscovery data sources …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activateapps.ediscovery_utils</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing operations on ediscovery clients &amp; ediscovery data sources.</p>
<p>'EdiscoveryClients','EdiscoveryClientOperations' ,'EdiscoveryDataSources' , 'EdiscoveryDataSource' are the 4 classes defined in this file</p>
<p>EdiscoveryClients:
Class for getting ediscovery clients details for different apps in activate</p>
<p>EdiscoveryClientOperations : Class for performing operations on Ediscovery client</p>
<p>EdiscoveryDataSources:
Class to represent all datasources associated with ediscovery client</p>
<p>EdiscoveryDataSource:
Class to represent single data source associated with edisocvery client</p>
<h2 id="ediscoveryclients">Ediscoveryclients</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the EdiscoveryClients class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>get_ediscovery_clients()
&ndash;
returns the ediscovery clients details</p>
<p>get_ediscovery_client_group_details() -
returns the ediscovery client group details</p>
<p>get_ediscovery_projects()
&ndash;
returns the ediscovery projects details</p>
<p>add()
&ndash;
Adds ediscovery client</p>
<p>delete()
&ndash;
deletes ediscovery client</p>
<h2 id="ediscoveryclientoperations">Ediscoveryclientoperations</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the EdiscoveryClientOperations class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_associations()
&ndash;
returns the associations blob for this client</p>
<p>_do_stream_download()
&ndash;
does stream download of exported csv file to local machine</p>
<p>form_search_params()
&ndash;
returns the search params dict for searching/exporting</p>
<p>refresh()
&ndash;
refresh the ediscovery client properties</p>
<p>share()
&ndash;
shares client with given user name or group name</p>
<p>export()
&ndash;
do export to CSV on data</p>
<p>start_job()
&ndash;
starts collection job on ediscovery client</p>
<p>get_job_status()
&ndash;
returns the job status of ediscovery client job</p>
<p>get_job_history()
&ndash;
returns the job history details of this ediscovery client</p>
<p>wait_for_collection_job()
&ndash;
waits for collection job to finish</p>
<p>wait_for_export()
&ndash;
waits for export to csv operation to finish</p>
<p>get_ediscovery_client_details()
&ndash;
returns the ediscovery client details</p>
<p>get_ediscovery_project_details()
&ndash;
returns the ediscovery project properties</p>
<p>search()
&ndash;
returns the search response containing document details</p>
<p>get_handler_id()
&ndash;
returns the handler id for this Ediscovery client</p>
<p>schedule()
&ndash;
Creates or modifies the schedule associated with ediscovery client</p>
<p>do_document_task()
&ndash;
does document related tasks like consent/comment</p>
<p>configure_task()
&ndash;
does task configuration for this edisocvery client</p>
<p>task_workflow_operation()
&ndash;
calls workflow operation for task</p>
<h2 id="ediscoveryclientoperations-attributes">EdiscoveryClientOperations Attributes:</h2>
<pre><code>**associations**            --  returns the blob of associated entities for this client
</code></pre>
<h2 id="ediscoverydatasources">Ediscoverydatasources</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the EdiscoveryDataSources class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_data_sources_details()
&ndash;
returns the data sources details associated with ediscovery client</p>
<p>_get_data_source_names()
&ndash;
returns separate list of data source display names &amp; data source names
associated with client</p>
<p>_parse_client_response_for_data_source
&ndash;
returns list of values for field names for data sources
from client response</p>
<p>_get_data_source_properties()
&ndash;
parses client response and returns deta sources properties</p>
<p>_get_o365_backupset_subclient_id()
&ndash;
Get the backupset and subclient ID for a given client object</p>
<p>has_data_source()
&ndash;
checks whether given data source exists in this client or not</p>
<p>get()
&ndash;
returns the EdiscoveryDataSource class object for given data source name</p>
<p>delete()
&ndash;
deletes the given data source associated with client</p>
<p>add_fs_data_source()
&ndash;
adds file system data source</p>
<p>add_o365_sdg_data_source()
&ndash;
Adds Office365 SDG data source to a project</p>
<p>refresh()
&ndash;
refresh the data sources details associated with client</p>
<p>get_datasource_document_count()
&ndash;
returns the document count for specified data source</p>
<h2 id="ediscoverydatasources-attributes">EdiscoveryDataSources Attributes:</h2>
<pre><code>**data_sources**            --  returns the list of data sources names associated with this client

**ediscovery_client_props** --  returns the Ediscovery client properties response for associated client

**total_documents**         --  returns the total documents count from all data sources

**client_id**               --  returns associated client id for all these data sources

**client_targetapp**        --  returns the source details of client (FSO/SDG)
</code></pre>
<h2 id="ediscoverydatasource">Ediscoverydatasource</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the EdiscoveryDataSource class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_data_source_properties()
&ndash;
returns the properties of data source</p>
<p>_get_property_value()
&ndash;
returns the value for the property name</p>
<p>_form_files_list()
&ndash;
returns list of dict containing files details</p>
<p>_form_request_options()
&ndash;
returns the options for review request</p>
<p>refresh()
&ndash;
refresh the datasource properties</p>
<p>get_job_history()
&ndash;
returns the job history for this data source</p>
<p>get_active_jobs()
&ndash;
returns the active jobs for this data source</p>
<p>search()
&ndash;
returns the search response containing document details</p>
<p>export()
&ndash;
do export to CSV on data</p>
<p>wait_for_export()
&ndash;
waits for export to csv operation to finish</p>
<p>tag_items()
&ndash;
applies tag to the documents</p>
<p>review_action()
&ndash;
do review action for documents</p>
<p>start_collection()
&ndash;
starts collection job on this data source</p>
<h2 id="ediscoverydatasource-attributes">EdiscoveryDataSource Attributes:</h2>
<pre><code>**crawl_type_name**         --  returns the crawl type enum name for this data source

**crawl_type**              --  returns the crawl type for this data source

**core_id**                 --  returns the data source core id attribute

**computed_core_name**      --  returns the computed core name of this datasource

**core_name**               --  returns the core name attribute of this data source

**cloud_id**                --  returns the index server cloud id associated with this data source

**data_source_props**       --  returns dict containing data source properties

**data_source_id**          --  returns the id of data source

**data_source_type**        --  returns the type of data source

**data_source_name**        --  returns the display name of data source

**plan_id**                 --  returns the associated DC plan id

**data_source_type_id**     --  returns the data source type id value

**client_id**               --  returns the client id to which data source belongs too

**total_documents**         --  returns the total document count on this data source

**sensitive_files_count**   --  returns the total sensitive files count

**name**                    --  returns the actual name for this data source

**index_server_node_client_id** --  returns the associated Index server node client id on which the collection
                                    exists
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1-L3407" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing operations on ediscovery clients &amp; ediscovery data sources.

&#39;EdiscoveryClients&#39;,&#39;EdiscoveryClientOperations&#39; ,&#39;EdiscoveryDataSources&#39; , &#39;EdiscoveryDataSource&#39; are the 4 classes defined in this file

EdiscoveryClients:  Class for getting ediscovery clients details for different apps in activate

EdiscoveryClientOperations : Class for performing operations on Ediscovery client

EdiscoveryDataSources:  Class to represent all datasources associated with ediscovery client

EdiscoveryDataSource:   Class to represent single data source associated with edisocvery client

EdiscoveryClients:

    __init__()                          --  initialise object of the EdiscoveryClients class

    _response_not_success()             --  parses through the exception response, and raises SDKException

    get_ediscovery_clients()            --  returns the ediscovery clients details

    get_ediscovery_client_group_details() -  returns the ediscovery client group details

    get_ediscovery_projects()            --  returns the ediscovery projects details

    add()                               --  Adds ediscovery client

    delete()                            --  deletes ediscovery client

EdiscoveryClientOperations:

    __init__()                          --  initialise object of the EdiscoveryClientOperations class

    _response_not_success()             --  parses through the exception response, and raises SDKException

    _get_associations()                 --  returns the associations blob for this client

    _do_stream_download()               --  does stream download of exported csv file to local machine

    form_search_params()                --  returns the search params dict for searching/exporting

    refresh()                           --  refresh the ediscovery client properties

    share()                             --  shares client with given user name or group name

    export()                            --  do export to CSV on data

    start_job()                         --  starts collection job on ediscovery client

    get_job_status()                    --  returns the job status of ediscovery client job

    get_job_history()                   --  returns the job history details of this ediscovery client

    wait_for_collection_job()           --  waits for collection job to finish

    wait_for_export()                   --  waits for export to csv operation to finish

    get_ediscovery_client_details()     --  returns the ediscovery client details

    get_ediscovery_project_details()    --  returns the ediscovery project properties

    search()                            --  returns the search response containing document details

    get_handler_id()                    --  returns the handler id for this Ediscovery client

    schedule()                          --  Creates or modifies the schedule associated with ediscovery client

    do_document_task()                  --  does document related tasks like consent/comment

    configure_task()                    --  does task configuration for this edisocvery client

    task_workflow_operation()           --  calls workflow operation for task

EdiscoveryClientOperations Attributes:
--------------------------------------

    **associations**            --  returns the blob of associated entities for this client


EdiscoveryDataSources:

    __init__()                              --  initialise object of the EdiscoveryDataSources class

    _response_not_success()                 --  parses through the exception response, and raises SDKException

    _get_data_sources_details()             --  returns the data sources details associated with ediscovery client

    _get_data_source_names()                --  returns separate list of data source display names &amp; data source names
                                                    associated with client

    _parse_client_response_for_data_source  --  returns list of values for field names for data sources
                                                                                from client response

    _get_data_source_properties()           --  parses client response and returns deta sources properties

    _get_o365_backupset_subclient_id()      --  Get the backupset and subclient ID for a given client object

    has_data_source()                       --  checks whether given data source exists in this client or not

    get()                                   --  returns the EdiscoveryDataSource class object for given data source name

    delete()                                --  deletes the given data source associated with client

    add_fs_data_source()                    --  adds file system data source

    add_o365_sdg_data_source()              --  Adds Office365 SDG data source to a project

    refresh()                               --  refresh the data sources details associated with client

    get_datasource_document_count()         --  returns the document count for specified data source

EdiscoveryDataSources Attributes:
----------------------------------

    **data_sources**            --  returns the list of data sources names associated with this client

    **ediscovery_client_props** --  returns the Ediscovery client properties response for associated client

    **total_documents**         --  returns the total documents count from all data sources

    **client_id**               --  returns associated client id for all these data sources

    **client_targetapp**        --  returns the source details of client (FSO/SDG)

EdiscoveryDataSource:

    __init__()                              --  initialise object of the EdiscoveryDataSource class

    _response_not_success()                 --  parses through the exception response, and raises SDKException

    _get_data_source_properties()           --  returns the properties of data source

    _get_property_value()                   --  returns the value for the property name

    _form_files_list()                      --  returns list of dict containing files details

    _form_request_options()                 --  returns the options for review request

    refresh()                               --  refresh the datasource properties

    get_job_history()                       --  returns the job history for this data source

    get_active_jobs()                       --  returns the active jobs for this data source

    search()                                --  returns the search response containing document details

    export()                                --  do export to CSV on data

    wait_for_export()                       --  waits for export to csv operation to finish

    tag_items()                             --  applies tag to the documents

    review_action()                         --  do review action for documents

    start_collection()                      --  starts collection job on this data source

EdiscoveryDataSource Attributes:
---------------------------------

    **crawl_type_name**         --  returns the crawl type enum name for this data source

    **crawl_type**              --  returns the crawl type for this data source

    **core_id**                 --  returns the data source core id attribute

    **computed_core_name**      --  returns the computed core name of this datasource

    **core_name**               --  returns the core name attribute of this data source

    **cloud_id**                --  returns the index server cloud id associated with this data source

    **data_source_props**       --  returns dict containing data source properties

    **data_source_id**          --  returns the id of data source

    **data_source_type**        --  returns the type of data source

    **data_source_name**        --  returns the display name of data source

    **plan_id**                 --  returns the associated DC plan id

    **data_source_type_id**     --  returns the data source type id value

    **client_id**               --  returns the client id to which data source belongs too

    **total_documents**         --  returns the total document count on this data source

    **sensitive_files_count**   --  returns the total sensitive files count

    **name**                    --  returns the actual name for this data source

    **index_server_node_client_id** --  returns the associated Index server node client id on which the collection
                                        exists

&#34;&#34;&#34;
import copy
import time
import json
import os

from ..activateapps.entity_manager import EntityManagerTypes

from ..activateapps.constants import InventoryConstants, EdiscoveryConstants, TargetApps, RequestConstants
from ..exception import SDKException


class EdiscoveryClients():
    &#34;&#34;&#34;Class for getting ediscovery clients details for different apps in activate&#34;&#34;&#34;

    def __init__(self, commcell_object, class_object):
        &#34;&#34;&#34;Initializes an instance of the EdiscoveryClients class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                class_object        (object)    -- instance of FsoServers/FsoServerGroups/FsoServerGroup class

            Returns:
                object  -   instance of the EdiscoveryClients class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._class_obj = class_object
        self._ds_type = None
        self._client_group = None
        self._limit = None
        self._offset = None
        self._sort_by = None
        self._sort_dir = None
        self._client_group_filter = None
        self._include_doc = None
        self._ediscovery_sub_type = None
        self._API_GET_EDISCOVERY_CLIENTS = copy.deepcopy(self._services[&#39;EDISCOVERY_V2_GET_CLIENTS&#39;])
        self._API_GET_EDISCOVERY_CLIENT_GROUPS = self._services[&#39;EDISCOVERY_V2_GET_CLIENT_GROUP_DETAILS&#39;]
        self._API_GET_EDISCOVERY_CLIENTS_V1 = copy.deepcopy(self._services[&#39;EDISCOVERY_CLIENTS&#39;])
        self._API_CREATE_CLIENT = copy.deepcopy(self._services[&#39;EDISCOVERY_CREATE_CLIENT&#39;])
        self._API_DELETE_CLIENT = copy.deepcopy((self._services[&#39;EDISCOVERY_DELETE_CLIENT&#39;]))

        from .file_storage_optimization import FsoServers, FsoServerGroups, FsoServerGroup
        from .sensitive_data_governance import Projects

        if isinstance(class_object, FsoServers):
            self._ds_type = 5
            self._client_group = 0
            self._limit = 0
            self._offset = 0
            self._sort_by = 1
            self._sort_dir = 0
        elif isinstance(class_object, FsoServerGroups):
            self._ds_type = 5
            self._client_group = 1
            self._limit = 0
            self._offset = 0
            self._sort_by = 1
            self._sort_dir = 0
        elif isinstance(class_object, FsoServerGroup):
            self._ds_type = 5
            self._client_group = 0
            self._client_group_filter = class_object.server_group_id
            self._limit = 0
            self._offset = 0
            self._sort_by = 1
            self._sort_dir = 0
            self._include_doc = 1
        elif isinstance(class_object, Projects):
            self._ediscovery_sub_type = 2
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Not a supported caller for this class&#39;)

    def delete(self, client_id):
        &#34;&#34;&#34;Deletes the ediscovery client

                Args:

                    client_id (int)       --  Client id

                Returns:

                      None

                Raises:

                      SDKException:

                            if input is not valid

                            if failed to delete client

                            if response is empty or not success

        &#34;&#34;&#34;
        if not isinstance(client_id, int):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._API_DELETE_CLIENT % client_id)
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response = response.json()[&#39;response&#39;][0]
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Delete operation failed on client - {response[&#39;errorCode&#39;]}&#34;)
                return
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;120&#39;)
        self._response_not_success(response)

    def add(self, client_name, inventory_name, plan_name):
        &#34;&#34;&#34;Adds ediscovery client

                Args:

                    client_name        (str)        --  Name of the client

                    inventory_name      (str)       --  Name of inventory

                    plan_name           (str)       --  Plan name to associate with this client

                Returns:

                    int --  client id

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to create client

                            if response is empty or not success
        &#34;&#34;&#34;
        if not isinstance(client_name, str) or not isinstance(plan_name, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if not self._commcell_object.plans.has_plan(plan_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid plan name&#39;)
        plan_obj = self._commcell_object.plans.get(plan_name)
        req_json = copy.deepcopy(EdiscoveryConstants.CREATE_CLIENT_REQ_JSON)
        req_json[&#39;entity&#39;][&#39;clientName&#39;] = client_name
        req_json[&#39;clientInfo&#39;][&#39;plan&#39;][&#39;planId&#39;] = int(plan_obj.plan_id)
        if inventory_name is not None:
            if not self._commcell_object.activate.inventory_manager().has_inventory(inventory_name):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid inventory name&#39;)
            inv_obj = self._commcell_object.activate.inventory_manager().get(inventory_name)
            req_json[&#39;clientInfo&#39;][&#39;edgeDrivePseudoClientProperties&#39;][&#39;eDiscoveryInfo&#39;][&#39;inventoryDataSource&#39;][&#39;seaDataSourceId&#39;] \
                = int(inv_obj.inventory_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CREATE_CLIENT, req_json)
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response = response.json()[&#39;response&#39;]
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Add operation failed on client - {response[&#39;errorCode&#39;]}&#34;)
                if &#39;entity&#39; in response:
                    return response[&#39;entity&#39;].get(&#39;clientId&#39;, 0)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;119&#39;)
        self._response_not_success(response)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def get_ediscovery_client_group_details(self):
        &#34;&#34;&#34;returns the ediscovery client group details for the app

                Args:

                    None

                Returns:

                    dict        -- Containing client group details

                Raises;

                    SDKException:

                            if failed to get client group details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_GET_EDISCOVERY_CLIENT_GROUPS %
            (self._client_group_filter, self._include_doc))
        if flag:
            if response.json() and &#39;nodeList&#39; in response.json():
                return response.json()
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def get_ediscovery_clients(self):
        &#34;&#34;&#34;returns the ediscovery clients details for the app

                Args:

                    None

                Returns:

                    dict        -- Containing client details

                Raises;

                    SDKException:

                            if failed to get client details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        api = self._API_GET_EDISCOVERY_CLIENTS % (
            self._ds_type, self._client_group, self._limit, self._offset, self._sort_by, self._sort_dir)
        if self._client_group_filter:
            api = api + f&#34;&amp;clientGroupFilter={self._client_group_filter}&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        output = {}
        if flag:
            if response.json() and &#39;nodeList&#39; in response.json():
                for node in response.json()[&#39;nodeList&#39;]:
                    if &#39;clientEntity&#39; in node:
                        output[node[&#39;clientEntity&#39;].get(&#39;displayName&#39;, &#39;NA&#39;).lower()] = node
                return output
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def get_ediscovery_projects(self):
        &#34;&#34;&#34;returns the ediscovery projects details for the app

                Args:

                    None

                Returns:

                    dict        -- Containing project details

                Raises;

                    SDKException:

                            if failed to get project details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        if not self._ediscovery_sub_type:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Ediscovery subtype not initialized&#39;)
        api = self._API_GET_EDISCOVERY_CLIENTS_V1 % self._ediscovery_sub_type
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        output = {}
        if flag:
            if response.json() and &#39;eDiscoveryClientProp&#39; in response.json():
                projects = response.json()[&#39;eDiscoveryClientProp&#39;]
                for project in projects:
                    project[&#39;clientId&#39;] = project[&#39;eDiscoveryClient&#39;][&#39;clientId&#39;]
                    output[project[&#39;eDiscoveryClient&#39;][&#39;clientName&#39;].lower()] = project
                return output
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;117&#39;)
        self._response_not_success(response)


class EdiscoveryClientOperations():
    &#34;&#34;&#34;Class for performing operations on ediscovery client.&#34;&#34;&#34;

    def __init__(self, commcell_object, class_object):
        &#34;&#34;&#34;Initializes an instance of the EdiscoveryClientOperations class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                class_object        (object)    -- instance of Inventory/Asset/FsoServer
                                                        /EdiscoveryDataSource/EdiscoveryDataSources/FsoServerGroup class

            Returns:
                object  -   instance of the EdiscoveryClientOperations class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._class_obj = class_object
        self._type = None
        self._operation = None
        self._client_id = None
        self._data_source_id = None
        self._ds_type_names = None
        self._include_doc_count = None
        self._limit = None
        self._offset = None
        self._sort_by = None
        self._sort_dir = None
        self._app_type = None
        self._associations = None
        self._search_entity_type = None
        self._search_entity_id = None
        self._client_entity_type = 3
        self._request_type = None
        self._request_review_set_id = None
        self._request_app = None
        self._API_CRAWL = self._services[&#39;EDISCOVERY_CRAWL&#39;]
        self._API_JOBS_HISTORY = self._services[&#39;EDISCOVERY_JOBS_HISTORY&#39;]
        self._API_JOB_STATUS = self._services[&#39;EDISCOVERY_JOB_STATUS&#39;]
        self._API_CLIENT_DETAILS = self._services[&#39;EDISCOVERY_V2_GET_CLIENT_DETAILS&#39;]
        self._API_SECURITY_ENTITY = self._services[&#39;ENTITY_SECURITY_ASSOCIATION&#39;]
        self._API_SECURITY = self._services[&#39;EDISCOVERY_SECURITY_ASSOCIATION&#39;]
        self._API_SEARCH = self._services[&#39;EDISCOVERY_DYNAMIC_FEDERATED&#39;]
        self._API_GET_DEFAULT_HANDLER = self._services[&#39;EDISCOVERY_GET_DEFAULT_HANDLER&#39;]
        self._API_EXPORT = self._services[&#39;EDISCOVERY_EXPORT&#39;]
        self._API_EXPORT_STATUS = self._services[&#39;EDISCOVERY_EXPORT_STATUS&#39;]
        self._CREATE_POLICY = self._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]
        self._API_GET_EDISCOVERY_CLIENT_DETAILS_V1 = copy.deepcopy(self._services[&#39;EDISCOVERY_CLIENT_DETAILS&#39;])
        self._API_DOC_TASK = self._services[&#39;EDISCOVERY_REQUEST_DOCUMENT_MARKER&#39;]
        self._API_CONFIGURE_TASK = self._services[&#39;EDISCOVERY_CONFIGURE_TASK&#39;]
        self._API_TASK_WORKFLOW = self._services[&#39;EDICOVERY_TASK_WORKFLOW&#39;]
        from .file_storage_optimization import FsoServer, FsoServerGroup
        from .sensitive_data_governance import Project
        from .request_manager import Request

        if isinstance(class_object, FsoServer):
            self._client_id = class_object.server_id
            self._include_doc_count = 1
            self._limit = self._offset = 0
            self._sort_by = 2
            self._sort_dir = 0
            self._ds_type_names = f&#34;{EdiscoveryConstants.DS_FILE},{EdiscoveryConstants.DS_CLOUD_STORAGE}&#34;
            self._data_source_id = 0  # invoke on all data sources
            self._type = 1  # Client
            self._operation = 0
            self._app_type = 1
            self._search_entity_type = 3
            self._search_entity_id = self._client_id
        elif isinstance(class_object, EdiscoveryDatasource):
            self._search_entity_type = 132
            self._search_entity_id = class_object.data_source_id
            self._data_source_id = class_object.data_source_id
            self._type = 1  # Client
            self._operation = 2  # incremental job by default
            self._client_id = class_object.client_id
        elif isinstance(class_object, EdiscoveryDataSources):
            self._client_id = class_object.client_id
            self._include_doc_count = 1
            self._limit = self._offset = 0
            self._sort_by = 2
            self._sort_dir = 0
            if class_object.client_targetapp == TargetApps.FSO.value:
                # based on caller, set appropriate ds types supported for that
                self._ds_type_names = f&#34;{EdiscoveryConstants.DS_FILE},{EdiscoveryConstants.DS_CLOUD_STORAGE}&#34;
        elif isinstance(class_object, FsoServerGroup):
            self._search_entity_type = 28
            self._search_entity_id = class_object.server_group_id
        elif isinstance(class_object, Project):
            self._client_id = class_object.project_id
            self._app_type = 2  # for sharing, app type param
            self._search_entity_type = 188
            self._search_entity_id = class_object.project_id
        elif isinstance(class_object, Request):
            self._client_id = class_object.request_id
            self._request_type = class_object.request_type
            self._request_review_set_id = class_object.review_set_id
            self._request_app = class_object.request_app
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;refresh ediscovery client properties&#34;&#34;&#34;
        self._associations = self._get_associations()

    def schedule(self, schedule_name, pattern_json, ops_type=2):
        &#34;&#34;&#34;Creates or modifies the schedule associated with ediscovery client

                Args:

                    schedule_name       (str)       --  Schedule name

                    pattern_json        (dict)      --  Schedule pattern dict
                                                        (Refer to Create_schedule_pattern in schedule.py)

                    ops_type            (int)       --  Operation type

                                                            Default : 2 (Add)

                                                            Supported : 2 (Add/Modify)

                Raises:

                      SDKException:

                            if input is not valid

                            if failed to create/modify schedule

        &#34;&#34;&#34;
        if not isinstance(schedule_name, str) or not isinstance(pattern_json, dict):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if ops_type not in [2]:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Schedule operation type provided is not supported&#34;)
        request_json = copy.deepcopy(EdiscoveryConstants.SERVER_LEVEL_SCHEDULE_JSON)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;] = int(self._client_id)
        request_json[&#39;taskInfo&#39;][&#39;task&#39;][
            &#39;taskName&#39;] = f&#34;Cvpysdk created Schedule -{schedule_name} for Server id - {self._client_id}&#34;
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][
            &#39;subTaskName&#39;] = schedule_name
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;] = pattern_json
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;contentIndexingOption&#39;][&#39;operationType&#39;] = ops_type

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_POLICY, request_json
        )
        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    task_id = str(response.json()[&#34;taskId&#34;])
                    if task_id:
                        return

                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return
                    else:
                        raise SDKException(
                            &#39;EdiscoveryClients&#39;,
                            &#39;102&#39;,
                            f&#34;Schedule operation failed on server - {error_code} - {error_message}&#34;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def form_search_params(
            self,
            criteria=None,
            attr_list=None,
            params=None,
            query=&#34;*:*&#34;,
            key=&#34;key&#34;,
            is_separate_attr=False):
        &#34;&#34;&#34;returns the search params dict based on input

            Args:

                criteria        (str)      --  containing criteria for query

                                                    Example :

                                                        Size:[10 TO 1024]
                                                        FileName:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

                query           (str)      --   query to be performed (acts as q param in query)
                                                    default:None (Means *:*)

                key             (str)      --   key name to be used in request (default:key)

                is_separate_attr (bool)    --   specifies whether attribute list needs to formed as separate key-value

            Returns:

                dict        --  Containing searchparams details

                Example : {
                              &#34;searchParams&#34;: [
                                {
                                  &#34;key&#34;: &#34;wt&#34;,
                                  &#34;value&#34;: &#34;json&#34;
                                },
                                {
                                  &#34;key&#34;: &#34;defType&#34;,
                                  &#34;value&#34;: &#34;edismax&#34;
                                },
                                {
                                  &#34;key&#34;: &#34;q&#34;,
                                  &#34;value&#34;: &#34;*:*&#34;
                                },
                                {
                                  &#34;key&#34;: &#34;fq&#34;,
                                  &#34;value&#34;: &#34;(contentid:949c3b53ce4dd72a82b8e67039eeddef)&#34;
                                },
                                {
                                  &#34;key&#34;: &#34;fl&#34;,
                                  &#34;value&#34;: &#34;contentid,CreatedTime,Url,ClientId&#34;
                                }
                              ]
                            }
        &#34;&#34;&#34;
        search_params = copy.deepcopy(EdiscoveryConstants.DYNAMIC_FEDERATED_SEARCH_PARAMS)
        search_params[&#39;searchParams&#39;].append(
            {key: &#34;wt&#34;, &#34;value&#34;: &#34;json&#34;})
        search_params[&#39;searchParams&#39;].append(
            {key: &#34;defType&#34;, &#34;value&#34;: &#34;edismax&#34;})
        search_params[&#39;searchParams&#39;].append({key: &#34;q&#34;, &#34;value&#34;: query})
        if criteria:
            fq_dict = {
                key: &#34;fq&#34;,
                &#34;value&#34;: criteria
            }
            search_params[&#39;searchParams&#39;].append(fq_dict)
        if attr_list:
            if is_separate_attr:
                for attr in attr_list:
                    fl_dict = {
                        key: &#34;fl&#34;,
                        &#34;value&#34;: attr
                    }
                    search_params[&#39;searchParams&#39;].append(fl_dict)
            else:
                fl_list = &#39;,&#39;.join(attr_list)
                fl_dict = {
                    key: &#34;fl&#34;,
                    &#34;value&#34;: fl_list
                }
                search_params[&#39;searchParams&#39;].append(fl_dict)
        if params:
            for dkey, value in params.items():
                custom_dict = {
                    key: str(dkey),
                    &#34;value&#34;: str(value)
                }
                search_params[&#39;searchParams&#39;].append(custom_dict)
        return search_params

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _do_stream_download(self, guid, file_name, download_location):
        &#34;&#34;&#34;does stream download to file to local machine

                Args:

                    guid                (str)       --  Download GUID

                    download_location   (str)       --  path on local machine to download requested file

                    file_name           (str)       --  File name for download

                Returns:

                    Str     --  File path containing downloaded file

                Raise:

                    SDKException:

                        if failed to do stream download


        &#34;&#34;&#34;
        request = copy.deepcopy(EdiscoveryConstants.EXPORT_DOWNLOAD_REQ)
        request[&#39;responseFileName&#39;] = file_name
        for param in request[&#39;fileParams&#39;]:
            if param[&#39;id&#39;] == 2:
                param[&#39;name&#39;] = guid
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;DOWNLOAD_PACKAGE&#39;], request
        )

        if flag:
            error_list = response.json().get(&#39;errList&#39;)
            file_content = response.json().get(&#39;fileContent&#39;, {})

            if error_list:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Error: {0}&#39;.format(error_list))

            file_name = file_content.get(&#39;fileName&#39;, file_name)
            request_id = file_content[&#39;requestId&#39;]

            # full path of the file on local machine to be downloaded
            download_path = os.path.join(download_location, file_name)

            # execute request to get the stream of content
            # using request id returned in the previous response
            request[&#39;requestId&#39;] = request_id
            flag1, response1 = self._cvpysdk_object.make_request(
                &#39;POST&#39;,
                self._services[&#39;DOWNLOAD_VIA_STREAM&#39;],
                request,
                stream=True
            )

            # download chunks of 1MB each
            chunk_size = 1024 ** 2

            if flag1:
                with open(download_path, &#34;wb&#34;) as file_pointer:
                    for content in response1.iter_content(chunk_size=chunk_size):
                        file_pointer.write(content)
            else:
                self._response_not_success(response1)
        else:
            self._response_not_success(response)

        return download_path

    def get_handler_id(self, handler_name=&#34;default&#34;):
        &#34;&#34;&#34;returns the id of given handler name

                Args:

                    handler_name            (str)       --  Handler name(Default: default)

                Returns:

                    int --  Handler id

                Raises:

                    SDKException:

                        if failed to find handler

                        if response is empty

        &#34;&#34;&#34;
        if not isinstance(self._class_obj, EdiscoveryDatasource):
            return 0
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_GET_DEFAULT_HANDLER % self._data_source_id)
        if flag:
            if response.json() and &#39;handlerInfos&#39; in response.json():
                handler_list = response.json()[&#39;handlerInfos&#39;]
                if not isinstance(handler_list, list):
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Failed to get Datasource/Handler details&#34;)
                for handler in handler_list:
                    if handler[&#39;handlerName&#39;].lower() == handler_name.lower():
                        return handler[&#39;handlerId&#39;]
                else:
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;No Handler found with given name&#39;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Unknown response while fetching datasource details&#39;)
        self._response_not_success(response)

    def export(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do export to CSV on data

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - Exports all docs)

                                                    Example :

                                                        1) Size filter --&gt; Size:[10 TO 1024]
                                                        2) File name filter --&gt; FileName_idx:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                str     --  export operation token


            Raises:

                SDKException:

                        if failed to perform export

        &#34;&#34;&#34;
        if criteria and not isinstance(criteria, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        search_params = self.form_search_params(criteria=criteria, attr_list=attr_list,
                                                params=params)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_EXPORT % self.get_handler_id(), search_params
        )
        if flag:
            if response.json():
                response = response.json()
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Export failed with error : {response.get(&#39;errLogMessage&#39;,&#39;&#39;)}&#34;)
                elif &#39;customMap&#39; in response and &#39;name&#39; in response[&#39;customMap&#39;]:
                    return response[&#39;customMap&#39;][&#39;name&#39;]
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Failed to search with response - {response.json()}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;113&#39;)
        self._response_not_success(response)

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on data source and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        1) Size filter --&gt; Size:[10 TO 1024]
                                                        2) File name filter --&gt; FileName_idx:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                int,list(dict),dict    --  Containing document count, document  details &amp; facet/stats details(if any)


            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        if criteria and not isinstance(criteria, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        search_params = self.form_search_params(criteria=criteria, attr_list=attr_list,
                                                params=params)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_SEARCH % (self._search_entity_type, self._search_entity_id), search_params
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to perform search - {response.json().get(&#39;errLogMessage&#39;,&#39;&#39;)}&#34;)
                if &#39;response&#39; in response.json() and &#39;docs&#39; in response.json()[&#39;response&#39;]:
                    if &#39;facets&#39; in response.json():
                        return response.json()[&#39;response&#39;][&#39;numFound&#39;], response.json()[
                            &#39;response&#39;][&#39;docs&#39;], response.json()[&#39;facets&#39;]
                    elif &#39;stats&#39; in response.json():
                        return response.json()[&#39;response&#39;][&#39;numFound&#39;], response.json()[
                            &#39;response&#39;][&#39;docs&#39;], response.json()[&#39;stats&#39;]
                    else:
                        return response.json()[&#39;response&#39;][&#39;numFound&#39;], response.json()[&#39;response&#39;][&#39;docs&#39;], {}
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Failed to search with response - {response.json()}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;112&#39;)
        self._response_not_success(response)

    def _get_associations(self):
        &#34;&#34;&#34;returns the associations for this client

            Args:

                None

            Returns:

                Dict    --  Containing associations details

            Raises:

                SDKException:

                    if failed to find association details

        &#34;&#34;&#34;
        # if called from EdiscoveryDatasource, then no association check needed as sharing is not possible at this level
        from ..activateapps.file_storage_optimization import FsoServerGroup
        from ..activateapps.request_manager import Request
        if isinstance(
                self._class_obj,
                Request) or isinstance(
                self._class_obj,
                EdiscoveryDatasource) or isinstance(
                self._class_obj,
                FsoServerGroup):
            return {}
        association_request_json = copy.deepcopy(EdiscoveryConstants.SHARE_REQUEST_JSON)
        del association_request_json[&#39;securityAssociations&#39;]
        association_request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;clientId&#39;] = int(self._client_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_SECURITY_ENTITY %
            (self._client_entity_type, int(
                self._client_id)), association_request_json)
        if flag:
            if response.json() and &#39;securityAssociations&#39; in response.json():
                security = response.json()[&#39;securityAssociations&#39;][0][&#39;securityAssociations&#39;]
                return security.get(&#39;associations&#39;, {})
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Failed to get existing security associations&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares ediscovery client with given user or user group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        if not isinstance(user_or_group_name, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(EdiscoveryConstants.SHARE_REQUEST_JSON)
        external_user = False
        association_response = None
        if ops_type == 1:
            association_response = self._associations

        if &#39;\\&#39; in user_or_group_name:
            external_user = True
        if is_user:
            user_obj = self._commcell_object.users.get(user_or_group_name)
            user_id = user_obj.user_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userId&#39;] = int(user_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 13
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userName&#39;] = user_or_group_name
        elif external_user:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;groupId&#39;] = 0
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 62
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
                &#39;externalGroupName&#39;] = user_or_group_name
        else:
            grp_obj = self._commcell_object.user_groups.get(user_or_group_name)
            grp_id = grp_obj.user_group_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupId&#39;] = int(grp_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 15
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
                &#39;userGroupName&#39;] = user_or_group_name

        request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;clientId&#39;] = int(self._client_id)
        request_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = ops_type

        if allow_edit_permission:
            # we need to send separate association for each permission
            association_json = copy.deepcopy(request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0])
            # do copy, remove permission and add Edit
            del association_json[&#39;properties&#39;][&#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;][0]
            association_json[&#39;properties&#39;][&#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;].append(
                EdiscoveryConstants.EDIT_CATEGORY_PERMISSION)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;].append(association_json)

            # Associate existing associations to the request
        if ops_type == 1:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;].extend(association_response)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_SECURITY % self._app_type, request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response_json = response.json()[&#39;response&#39;]
                for node in response_json:
                    if &#39;errorCode&#39; in node:
                        error_code = node[&#39;errorCode&#39;]
                        if error_code != 0:
                            error_message = node.get(&#39;warningMessage&#39;, &#34;Something went wrong&#34;)
                            raise SDKException(
                                &#39;EdiscoveryClients&#39;,
                                &#39;102&#39;, error_message)
                self.refresh()
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;109&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_ediscovery_project_details(self):
        &#34;&#34;&#34;returns the ediscovery project details

                Args:

                    None

                Returns:

                    dict        -- Containing project details

                Raises;

                    SDKException:

                            if failed to get project details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        api = self._API_GET_EDISCOVERY_CLIENT_DETAILS_V1 % self._client_id
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        if flag:
            if response.json() and &#39;eDiscoveryClientProp&#39; in response.json():
                project = response.json()[&#39;eDiscoveryClientProp&#39;][0]
                return project[&#39;eDiscoveryClientInfo&#39;]
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;118&#39;)
        self._response_not_success(response)

    def get_ediscovery_client_details(self):
        &#34;&#34;&#34;returns the ediscovery client details for this client

                Args:

                    None

                Returns:

                    dict        -- Containing client details

                Raises;

                    SDKException:

                            if failed to get client details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_CLIENT_DETAILS % (
            self._client_id, self._include_doc_count, self._limit, self._offset,
            self._sort_by, self._sort_dir, self._ds_type_names))
        if flag:
            if response.json() and &#39;nodeList&#39; in response.json():
                return response.json()[&#39;nodeList&#39;][0] if len(response.json()[&#39;nodeList&#39;]) &gt; 0 else {}
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def start_job(self, wait_for_job=False, wait_time=60, is_incr=True):
        &#34;&#34;&#34;Starts job on ediscovery client

            Args:

                    wait_for_job        (bool)       --  specifies whether to wait for job to complete or not

                    wait_time           (int)        --  time interval to wait for job completion in Mins
                                                            Default : 60Mins

                    is_incr             (bool)       -- Specifies whether this is incremental or full crawl job

             Return:

                    None

            Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        if not is_incr:
            self._operation = 3  # full crawl job
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_CRAWL % (self._client_id, self._data_source_id, self._type, self._operation)
        )
        if flag:
            if response.json():
                response_json = response.json()
                if &#39;errorCode&#39; in response_json:
                    error_code = response_json[&#39;errorCode&#39;]
                    if error_code != 0:
                        error_message = response_json[&#39;errorMessage&#39;]
                        raise SDKException(
                            &#39;EdiscoveryClients&#39;,
                            &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;103&#39;)
            if not wait_for_job:
                return
            return self.wait_for_collection_job(wait_time=wait_time)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def wait_for_export(self, token, wait_time=60, download=True, download_location=os.getcwd()):
        &#34;&#34;&#34;Waits for Export to CSV to finish

                Args:

                    wait_time           (int)       --  time interval to wait for job completion in Mins
                                                            Default : 60Mins

                    token               (str)       --  Export to CSV token GUID

                    download            (bool)      --  specify whether to download exported file or not

                    download_location   (str)       --  Path where to download exported csv file
                                                                Default: Current working dir

                Return:

                    str     -- Download GUID for exported CSV file if download=false
                               File path containing exported csv file if download=true

                Raises:

                    SDKException:

                            if Export status check fails

                            if timeout happens

        &#34;&#34;&#34;
        timeout = time.time() + 60 * wait_time  # 1hr
        handler_id = self.get_handler_id()
        while True:
            if time.time() &gt; timeout:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Export job Timeout&#34;)
            flag, response = self._cvpysdk_object.make_request(
                &#39;GET&#39;, self._API_EXPORT_STATUS % (handler_id, token)
            )
            if flag:
                if response.json():
                    response = response.json()
                    if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                        raise SDKException(
                            &#39;EdiscoveryClients&#39;,
                            &#39;102&#39;,
                            f&#34;Export status check failed with error : {response.get(&#39;errLogMessage&#39;, &#39;&#39;)}&#34;)
                    elif &#39;customMap&#39; in response and response[&#39;customMap&#39;].get(&#39;name&#39;, &#39;&#39;) == &#39;statusObject&#39;:
                        value_json = json.loads(response[&#39;customMap&#39;][&#39;value&#39;])
                        if &#39;response&#39; in value_json and isinstance(
                                value_json[&#39;response&#39;],
                                dict) and value_json[&#39;response&#39;].get(
                                &#39;status&#39;,
                                &#39;&#39;) == &#39;finished&#39;:
                            if not download:
                                return value_json[&#39;response&#39;].get(&#39;downloadGuid&#39;, &#39;&#39;)
                            else:
                                return self._do_stream_download(guid=value_json[&#39;response&#39;].get(&#39;downloadGuid&#39;, &#39;&#39;),
                                                                file_name=f&#34;Cvpysdk_Activate_export_{int(time.time())}&#34;,
                                                                download_location=download_location)
                    else:
                        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;,
                                           f&#34;Failed to check export status with response - {response.json()}&#34;)
                else:
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;114&#39;)
            else:
                self._response_not_success(response)
            time.sleep(10)

    def wait_for_collection_job(self, wait_time=60):
        &#34;&#34;&#34;Waits for collection job to finish

                Args:

                    wait_time           (int)       --  time interval to wait for job completion in Mins
                                                            Default : 60Mins

                Return:

                    None

                Raises:

                    SDKException:

                            if collection job fails

                            if timeout happens

        &#34;&#34;&#34;
        timeout = time.time() + 60 * wait_time  # 1hr
        while True:
            if time.time() &gt; timeout:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Collection job Timeout&#34;)
            status = self.get_job_status()
            if int(status[&#39;state&#39;]) == InventoryConstants.CRAWL_JOB_COMPLETE_STATE:  # Finished State
                return
            elif int(status[&#39;state&#39;]) == InventoryConstants.CRAWL_JOB_COMPLETE_ERROR_STATE:  # completed with error
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Job status is marked as Completed with Error&#34;)
            # STOPPING,STOPPED,ABORTING, ABORTED,EXCEPTION,UNKNOWN,SYNCING,PENDING
            elif int(status[&#39;state&#39;]) in InventoryConstants.CRAWL_JOB_FAILED_STATE:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Job status is marked as Failed/Error/Pending&#34;)
            else:
                time.sleep(10)

    def get_job_history(self):
        &#34;&#34;&#34;Returns the job history details of ediscovery client

                Args:
                    None

                Returns:

                    list(dict)    --  containing job history details

                Raises:

                    SDKException:

                            if failed to get job history

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_JOBS_HISTORY % (self._client_id, self._type, self._data_source_id)
        )
        if flag:
            if response.json() and &#39;status&#39; in response.json():
                return response.json()[&#39;status&#39;]
            elif &#39;error&#39; in response.json():
                error = response.json()[&#39;error&#39;]
                error_code = error[&#39;errorCode&#39;]
                if error_code != 0:
                    error_message = error[&#39;errLogMessage&#39;]
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;, error_message)
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Something went wrong while fetching job history&#34;)
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;104&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_job_status(self):
        &#34;&#34;&#34;Returns the job status details of this asset

                Args:
                    None

                Returns:

                    dict    --  containing job status details

                Raises:

                    SDKException:

                            if failed to get job status

        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_JOB_STATUS % (self._client_id, self._type, self._data_source_id)
        )
        if flag:
            if response.json() and &#39;status&#39; in response.json():
                return response.json()[&#39;status&#39;]
            elif &#39;error&#39; in response.json():
                error = response.json()[&#39;error&#39;]
                error_code = error[&#39;errorCode&#39;]
                if error_code != 0:
                    error_message = error[&#39;errLogMessage&#39;]
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;, error_message)
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Something went wrong while fetching job status&#34;)
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;105&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def do_document_task(self, comment, doc_id=None, ds_id=None, consent=True, redact=False):
        &#34;&#34;&#34;does document update for consent/comment

            Args:

                doc_id          (str)       --  Document id (Mandatory in case of SDG)

                comment         (str)       --  User comment

                ds_id           (int)       --  Data SourceId (Mandatory in case of SDG)

                consent         (bool)      --  Accept or Decline (Default:True)

                redact          (bool)      --  Redact ON or OFF (only in case of export)
                                                            (Default:False)

            Returns:

                None

            Raises:

                SDKException:

                    if failed to update document

                    if input is not valid
        &#34;&#34;&#34;
        if not self._request_type or not self._request_app or not self._request_review_set_id:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Request type not set correctly&#34;)
        if self._request_app == TargetApps.SDG.name:
            if not doc_id:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    &#34;Document id is mandatory for request from SDG app&#34;)
            if not ds_id:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    &#34;DataSource id is mandatory for request from SDG app&#34;)
        if self._request_app == TargetApps.FSO.name:
            self._request_review_set_id = f&#34;FSO_{self._request_review_set_id}&#34;
        req_json = {
            &#34;nameValues&#34;: [
                {
                    &#34;name&#34;: f&#34;ConsentFor_{self._request_review_set_id}_b&#34;,
                    &#34;value&#34;: f&#34;{consent}&#34;
                },

                {
                    &#34;name&#34;: f&#34;CommentFor_{self._request_review_set_id}&#34;,
                    &#34;value&#34;: comment
                }
            ]
        }

        if self._request_app == TargetApps.SDG.name:
            req_json[&#39;nameValues&#39;].append({
                &#34;name&#34;: &#34;q&#34;,
                &#34;value&#34;: f&#34;contentid:{doc_id}&#34;
            })
            req_json[&#39;nameValues&#39;].append({
                &#34;name&#34;: &#34;datasourceId&#34;,
                &#34;value&#34;: f&#34;{ds_id}&#34;
            })
        elif self._request_app == TargetApps.FSO.name:
            req_json[&#39;nameValues&#39;].append({
                &#34;name&#34;: &#34;fq&#34;,
                &#34;value&#34;: f&#34;contentid:* AND -(ConsentFor_{self._request_review_set_id}_b:*)&#34;
            })
        if self._request_type == RequestConstants.RequestType.EXPORT.value:
            req_json[&#39;nameValues&#39;].append({
                &#34;name&#34;: f&#34;RedactMode_{self._request_review_set_id}_b&#34;,
                &#34;value&#34;: f&#34;{redact}&#34;
            })

        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._API_DOC_TASK % (self._client_id), req_json
        )
        if flag:
            if not response.json():
                return
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Something went wrong while doing document task operation - {response.json()[&#39;errorMessage&#39;]}&#34;)
            else:
                return
        self._response_not_success(response)

    def task_workflow_operation(self):
        &#34;&#34;&#34;calls workflow operation for task

                Args:
                    None

                Returns:

                    str --  Workflow job id

                Raises:

                    SDKException:

                        if failed to call task workflow
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_TASK_WORKFLOW % self._client_id)
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Something wrong while invoking task workflow operation - {response.json()[&#39;errorMessage&#39;]}&#34;)
                if &#39;jobId&#39; in response.json():
                    return response.json()[&#39;jobId&#39;]
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Workflow task failed&#34;)
        self._response_not_success(response)

    def configure_task(self, task_props):
        &#34;&#34;&#34;configures task for this edsicovery client

            Args:

                task_props      list(dict)      --  Task properties

            Returns:

                None

            Raises:

                SDKException:

                    if input is not valid

                    if failed to configure task
        &#34;&#34;&#34;
        if not isinstance(task_props, list):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        req_json = {
            &#34;taskReq&#34;: {
                &#34;tasks&#34;: [
                    {
                        &#34;taskInfo&#34;: {
                            &#34;taskId&#34;: self._client_id
                        },
                        &#34;taskProps&#34;: task_props
                    }
                ]
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CONFIGURE_TASK, req_json
        )
        if flag:
            if response.json() and &#39;msg&#39; in response.json():
                msg = response.json()[&#39;msg&#39;]
                if &#39;errorCode&#39; in msg and msg[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Something went wrong while configuring task operation - {msg[&#39;errorMessage&#39;]}&#34;)
                return
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Configure task failed&#34;)
        self._response_not_success(response)

    @property
    def associations(self):
        &#34;&#34;&#34;returns association blob for this client

            Returns:

                dict --  containing security association blob details

        &#34;&#34;&#34;
        return self._associations


class EdiscoveryDataSources():
    &#34;&#34;&#34;Class to represent all datasources associated with ediscovery client&#34;&#34;&#34;

    def __init__(self, commcell_object, class_object):
        &#34;&#34;&#34;Initializes an instance of the EdiscoveryDataSources class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                class_object        (object)    -- instance of FsoServer/FsoServers/FsoServerGroups class

            Returns:
                object  -   instance of the EdiscoveryDataSources class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._class_obj = class_object
        self._ediscovery_client_ops = None
        self._client_id = None
        self._ediscovery_client_props = None
        self._data_source_display_names = None
        self._data_source_names = None
        self._data_sources = None
        self._app_source = None
        self._app_source_sub_type = None
        self._type = None  # client entity
        self._API_DELETE = self._services[&#39;EDISCOVERY_DATA_SOURCE_DELETE&#39;]
        self._API_CREATE_DATA_SOURCE = self._services[&#39;EDISCOVERY_CREATE_DATA_SOURCE&#39;]
        self._API_GET_DATA_SOURCE_STATS = self._services[&#39;EDISCOVERY_DATA_SOURCE_STATS&#39;]

        from .file_storage_optimization import FsoServer, FsoServers, FsoServerGroups
        from .sensitive_data_governance import Project

        if isinstance(class_object, FsoServer):
            self._client_id = class_object.server_id
            self._ediscovery_client_ops = EdiscoveryClientOperations(commcell_object, class_object)
            self._app_source = TargetApps.FSO
        elif isinstance(class_object, FsoServers):
            self._app_source = TargetApps.FSO
            self._app_source_sub_type = EdiscoveryConstants.FSO_SERVERS
        elif isinstance(class_object, FsoServerGroups):
            self._app_source = TargetApps.FSO
            self._app_source_sub_type = EdiscoveryConstants.FSO_SERVER_GROUPS
        elif isinstance(class_object, Project):
            self._app_source = TargetApps.SDG
            self._client_id = class_object.project_id
            self._ediscovery_client_ops = EdiscoveryClientOperations(commcell_object, class_object)
            self._type = 1
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)

        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_data_source_properties(self, client_details):
        &#34;&#34;&#34;Parses client response and returns data sources properties

                Args:

                    client_details      (dict)      --  containing EdiscoveryClient details response

                Returns:

                    list        --  containing data source details

                Raises:

                    SDKException:

                            if input is not valid


        &#34;&#34;&#34;
        output = {}
        if not isinstance(client_details, dict):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;107&#39;)
        index = 0
        ds_name = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME)
        ds_type = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE)
        plan_id = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_PLAN_ID, field_type=&#34;int&#34;)
        subclient_id = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_SUBCLIENT_ID, field_type=&#34;int&#34;)
        crawl_type = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_CRAWL_TYPE, field_type=&#34;int&#34;)
        if &#39;childs&#39; not in client_details:
            return output
        for data_source in client_details[&#39;childs&#39;]:
            ds_id = 0
            if &#39;dsEntity&#39; in data_source:
                ds_id = data_source[&#39;dsEntity&#39;].get(EdiscoveryConstants.FIELD_DATA_SOURCE_ID, 0)
            ds_props = {
                EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME: ds_name[index],
                EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE: ds_type[index],
                EdiscoveryConstants.FIELD_PLAN_ID: plan_id[index],
                EdiscoveryConstants.FIELD_SUBCLIENT_ID: subclient_id[index],
                EdiscoveryConstants.FIELD_DATA_SOURCE_ID: ds_id,
                EdiscoveryConstants.FIELD_CRAWL_TYPE: crawl_type
            }
            output[ds_name[index].lower()] = ds_props
            index = index + 1
        return output

    def _get_data_sources_stats(self):
        &#34;&#34;&#34;returns the dict containing data source properties

            Args:

                None

            Returns:

                dict    --  containing data source properties

            Raises:

                SDKException:

                        if failed to get data source stats

                        if response is empty or not success
        &#34;&#34;&#34;
        api = self._API_GET_DATA_SOURCE_STATS % (self._client_id, self._type)
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        output = {}
        if flag:
            if response.json() and &#39;statusResp&#39; in response.json():
                status = response.json()[&#39;statusResp&#39;]
                if &#39;collections&#39; in status:
                    # Change to return all datasources in a project
                    for collection in status[&#39;collections&#39;]:
                        if &#39;datasources&#39; in collection:
                            data_sources = collection[&#39;datasources&#39;]
                            for data_source in data_sources:
                                ds_props = {
                                    EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME: data_source[EdiscoveryConstants.FIELD_DISPLAY_NAME],
                                    EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE: data_source[EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE],
                                    EdiscoveryConstants.FIELD_DATA_SOURCE_ID: data_source[EdiscoveryConstants.FIELD_DATA_SOURCE_ID_NON_SEA],
                                    EdiscoveryConstants.FIELD_DOCUMENT_COUNT: data_source.get(&#39;status&#39;, {}).get(&#39;totalcount&#39;, 0)
                                }
                                output[data_source[EdiscoveryConstants.FIELD_DISPLAY_NAME].lower()] = ds_props
                    return output
                return {}  # no data sources exists
            if response.json() and &#39;response&#39; in response.json():
                response = response.json()[&#39;response&#39;]
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Ediscovery Add client failed with error code - {response[&#39;errorCode&#39;]}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;110&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the data sources associated with edisocvery client&#34;&#34;&#34;
        if not self._client_id:
            return
        if self._app_source and self._app_source == TargetApps.SDG:
            self._ediscovery_client_props = self._ediscovery_client_ops.get_ediscovery_project_details()
            self._data_source_display_names, self._data_source_names = self._get_data_source_names(
                self._ediscovery_client_props)
            self._data_sources = self._get_data_sources_stats()
        elif self._app_source and self._app_source == TargetApps.FSO:
            self._ediscovery_client_props = self._get_data_sources_details()
            self._data_source_display_names, self._data_source_names = self._get_data_source_names(
                self._ediscovery_client_props)
            self._data_sources = self._get_data_source_properties(self._ediscovery_client_props)
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unknown App source type passed&#34;)

    def add_fs_data_source(self, server_name, data_source_name, inventory_name, plan_name,
                           source_type=EdiscoveryConstants.SourceType.BACKUP, **kwargs):
        &#34;&#34;&#34;Adds file system data source to server

                Args:

                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    inventory_name      (str)       --  Inventory name which needs to be associated

                    plan_name           (str)       --  Plan name which needs to be associated with this data source

                    source_type         (enum)      --  Source type for crawl (Live source or Backedup)
                                                                Refer EdiscoveryConstants.SourceType

                Kwargs Arguments:

                    scan_type           (str)       --  Specifies scan type when source type is for backed up data
                                                                Supported values : quick | full

                    crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

                    access_node         (str)       --  server name which needs to be used as access node in case
                                                                if server to be added is not a commvault client

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    user_name           (str)       --  User name who has access to UNC path

                    password            (str)       --  base64 encoded password to access unc path

                    enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this

                Returns:

                    obj     --  Instance of EdiscoveryDataSource class

                    None    --  if it is called to create FSO server group

                Raises:

                      SDKException:

                            if plan/inventory/index server doesn&#39;t exists

                            if failed to add FSO server data source
        &#34;&#34;&#34;
        is_commvault_client = False
        is_server_group = False
        if self._app_source_sub_type and self._app_source_sub_type == EdiscoveryConstants.FSO_SERVER_GROUPS:
            is_server_group = True
        if not self._commcell_object.activate.inventory_manager().has_inventory(inventory_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid inventory name&#39;)
        if not self._commcell_object.plans.has_plan(plan_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid plan name&#39;)
        plan_obj = self._commcell_object.plans.get(plan_name)
        if self._app_source.value not in plan_obj.content_indexing_props[&#39;targetApps&#39;]:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Plan is not marked with targetapp as FSO&#39;)
        inv_obj = self._commcell_object.activate.inventory_manager().get(inventory_name)
        request_json = copy.deepcopy(EdiscoveryConstants.ADD_FS_REQ_JSON)
        request_json[&#39;datasourceId&#39;] = inv_obj.inventory_id
        request_json[&#39;indexServerClientId&#39;] = plan_obj.content_indexing_props[&#39;analyticsIndexServer&#39;].get(&#39;clientId&#39;, 0)
        request_json[&#39;datasources&#39;][0][&#39;datasourceName&#39;] = data_source_name
        if self._app_source == TargetApps.SDG:
            request_json[&#39;clientId&#39;] = self._client_id  # project source client id
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;caconfig&#34;,
                &#34;propertyValue&#34;: &#34;[{\&#34;task\&#34;:\&#34;EntityExtractionFields\&#34;,\&#34;arguments\&#34;:[\&#34;content\&#34;]}]&#34;
            })
        # find out whether given server is commvault client or not to decide further
        inventory_resp = None
        scan_type = kwargs.get(&#39;scan_type&#39;, &#39;quick&#39;)
        if not is_server_group:
            is_commvault_client = self._commcell_object.clients.has_client(server_name)
            if not is_commvault_client:
                if (&#39;access_node&#39; not in kwargs or &#39;user_name&#39; not in kwargs or &#39;password&#39; not in kwargs):
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Access node information is missing&#34;)
                if not self._commcell_object.clients.has_client(kwargs.get(&#34;access_node&#34;)):
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Access node client is not present&#34;)
            inventory_resp = inv_obj.data_source.ds_handlers.get(
                EdiscoveryConstants.FS_SERVER_HANDLER_NAME).get_handler_data(
                handler_filter=f&#34;q=(name_idx:{server_name})&amp;rows=1&#34;)
            if inventory_resp[&#39;numFound&#39;] != 1:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    &#39;Multiple server with same name exists or no server exists in inventory&#39;)
            inventory_resp = inventory_resp[&#39;docs&#39;][0]
        # set common properties
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;enablemonitoring&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;enable_monitoring&#39;, &#34;false&#34;).lower()
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;countryCode&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;country_code&#39;, &#39;US&#39;)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;co&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;country_name&#39;, &#39;United States&#39;)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;contentid&#34;,
            &#34;propertyValue&#34;: inventory_resp[&#39;contentid&#39;] if not is_server_group else server_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;clientdisplayname&#34;,
            &#34;propertyValue&#34;: server_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;dcplanid&#34;,
            &#34;propertyValue&#34;: str(plan_obj.plan_id)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;name&#34;,
            &#34;propertyValue&#34;: inventory_resp[&#39;name&#39;] if not is_server_group else server_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;operatingSystem&#34;,
            &#34;propertyValue&#34;: inventory_resp[&#39;operatingSystem&#39;] if not is_server_group else &#34;&#34;
        })
        if is_commvault_client:
            if &#39;access_node&#39; not in kwargs:
                del request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;]
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;ClientId&#34;,
                &#34;propertyValue&#34;: str(self._commcell_object.clients.get(server_name).client_id)
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;ContentIndexingStatus&#34;,
                &#34;propertyValue&#34;: str(inventory_resp[&#39;ContentIndexingStatus&#39;])
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;BackedupStatus&#34;,
                &#34;propertyValue&#34;: str(inventory_resp[&#39;BackedupStatus&#39;])
            })
        elif is_server_group:
            del request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;]
            del request_json[&#39;indexServerClientId&#39;]
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;ClientGroupId&#34;,
                &#34;propertyValue&#34;: str(self._commcell_object.client_groups.get(server_name).clientgroup_id)
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;ContentIndexingStatus&#34;,
                &#34;propertyValue&#34;: str(0)
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;BackedupStatus&#34;,
                &#34;propertyValue&#34;: str(0)
            })

        # set crawl type and source type related params
        if source_type.value == EdiscoveryConstants.SourceType.BACKUP.value:
            if scan_type == &#39;quick&#39; and self._app_source == TargetApps.FSO:
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;crawltype&#34;,
                    &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.FILE_LEVEL_ANALYTICS.value)
                })
            else:
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;crawltype&#34;,
                    &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.BACKUP_V2.value)
                })
        else:
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;crawltype&#34;,
                &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.LIVE.value)
            })
            if not is_commvault_client or &#39;access_node&#39; in kwargs:
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;username&#34;,
                    &#34;propertyValue&#34;: kwargs.get(&#39;user_name&#39;, &#39;&#39;)
                })
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;password&#34;,
                    &#34;propertyValue&#34;: kwargs.get(&#39;password&#39;, &#39;&#39;)
                })
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;domainName&#34;,
                    &#34;propertyValue&#34;: inventory_resp[&#39;domainName&#39;]
                })
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;dNSHostName&#34;,
                    &#34;propertyValue&#34;: inventory_resp[&#39;dNSHostName&#39;]
                })
                request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;][0][&#39;clientId&#39;] = int(
                    self._commcell_object.clients.get(kwargs.get(&#39;access_node&#39;)).client_id)
                request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;][0][&#39;clientName&#39;] = kwargs.get(&#39;access_node&#39;)

            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;includedirectoriespath&#34;,
                &#34;propertyValue&#34;: &#39;,&#39;.join(kwargs.get(&#39;crawl_path&#39;, []))
            })

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CREATE_DATA_SOURCE, request_json)
        if flag:
            if response.json() and &#39;collections&#39; in response.json():
                collection = response.json()[&#39;collections&#39;][0]
                if &#39;datasources&#39; in collection:
                    data_source = collection[&#39;datasources&#39;][0]
                    # when add data source is called for new server then handle client id accordingly
                    if is_server_group:
                        # for server group, no need to refresh data sources details as we go via Server by server only
                        return
                    if not self._client_id:
                        if is_commvault_client:
                            self._client_id = inventory_resp[&#39;ClientId&#39;]
                        else:
                            self._commcell_object.clients.refresh()
                            all_clients = self._commcell_object.clients.all_clients
                            for client_name, _ in all_clients.items():
                                if client_name.lower().startswith(f&#34;{data_source_name.lower()}_&#34;):
                                    self._client_id = self._commcell_object.clients.get(client_name).client_id
                                    break
                        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)
                    self.refresh()
                    return EdiscoveryDatasource(
                        self._commcell_object,
                        data_source[&#39;datasourceId&#39;],
                        EdiscoveryConstants.DATA_SOURCE_TYPES[5], client_id=self._client_id, app_type=self._app_source)
            if response.json() and &#39;error&#39; in response.json():
                error = response.json()[&#39;error&#39;]
                if &#39;errorCode&#39; in error and error[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Creation of data source failed with error - {error[&#39;errorCode&#39;]}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;115&#39;)
        self._response_not_success(response)

    def add_o365_sdg_data_source(self, server_name, data_source_name, plan_name,
                                 datasource_type=EdiscoveryConstants.ClientType.ONEDRIVE, **kwargs):
        &#34;&#34;&#34;Adds Office365 SDG data source to a project

                Args:
                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    plan_name           (str)       --  Plan name which needs to be associated with this data source

                    datasource_type     (str)       --  Type of O365 SDG datasource (Exchange/OneDrive)

                Kwargs Arguments:

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    users               (list)      --  List of users/mailboxes to be added. If empty, all users would be added

                Returns:

                    obj     --  Instance of EdiscoveryDataSource class

                Raises:

                      SDKException:

                            if plan doesn&#39;t exists
        &#34;&#34;&#34;
        if not self._commcell_object.plans.has_plan(plan_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid plan name&#39;)
        plan_obj = self._commcell_object.plans.get(plan_name)
        if self._app_source.value not in plan_obj.content_indexing_props[&#39;targetApps&#39;]:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Plan is not marked with targetapp as SDG&#39;)
        o365_client = self._commcell_object.clients.get(server_name)
        backupset_id, subclient_id = self._get_o365_backupset_subclient_id(o365_client, datasource_type)
        request_json = copy.deepcopy(EdiscoveryConstants.ADD_O365_SDG_BACKED_UP_DS_REQ)
        request_json[&#39;clientId&#39;] = self._client_id  # project source client id
        if plan_obj.content_indexing_props.get(&#39;analyticsIndexServer&#39;, {}).get(&#39;clientId&#39;, None) is not None:
            # Only for software datasource creation, we will need this index server client ID to be set
            request_json[&#39;indexServerClientId&#39;] = plan_obj.content_indexing_props[&#39;analyticsIndexServer&#39;].get(&#39;clientId&#39;, 0)
        request_json[&#39;datasources&#39;][0][&#39;datasourceType&#39;] = datasource_type.value
        request_json[&#39;datasources&#39;][0][&#39;datasourceName&#39;] = data_source_name
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;caconfig&#34;,
            &#34;propertyValue&#34;: &#34;[{\&#34;task\&#34;:\&#34;EntityExtractionFields\&#34;,\&#34;arguments\&#34;:[\&#34;content\&#34;]}]&#34;
        })

        # set common properties
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;countryCode&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;country_code&#39;, &#39;US&#39;)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;co&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;country_name&#39;, &#39;United States&#39;)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;clientdisplayname&#34;,
            &#34;propertyValue&#34;: data_source_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;name&#34;,
            &#34;propertyValue&#34;: data_source_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;dcplanid&#34;,
            &#34;propertyValue&#34;: str(plan_obj.plan_id)
        })

        # set crawl type and source type related params
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;crawltype&#34;,
            &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.BACKUP_V2.value)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;dNSHostName&#34;,
            &#34;propertyValue&#34;: server_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;ClientId&#34;,
            &#34;propertyValue&#34;: str(o365_client.client_id)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;CAppBackupSetId&#34;,
            &#34;propertyValue&#34;: str(backupset_id)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;backedupsubclientids&#34;,
            &#34;propertyValue&#34;: str(subclient_id)
        })

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CREATE_DATA_SOURCE, request_json)
        if flag:
            if response.json() and &#39;collections&#39; in response.json():
                collection = response.json()[&#39;collections&#39;][0]
                if &#39;datasources&#39; in collection:
                    data_source = collection[&#39;datasources&#39;][0]
                    # when add data source is called for new server then handle client id accordingly
                    return EdiscoveryDatasource(
                        self._commcell_object,
                        data_source[&#39;datasourceId&#39;],
                        EdiscoveryConstants.DATA_SOURCE_TYPES[datasource_type.value], client_id=self._client_id, app_type=self._app_source)
            if response.json() and &#39;error&#39; in response.json():
                error = response.json()[&#39;error&#39;]
                if &#39;errorCode&#39; in error and error[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Creation of data source failed with error - {error[&#39;errorCode&#39;]}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;115&#39;)
        self._response_not_success(response)

    def _get_o365_backupset_subclient_id(self, client, client_type=EdiscoveryConstants.ClientType.ONEDRIVE):
        &#34;&#34;&#34;
        Get the backupset and subclient ID for a given client object
        Args:
            client(object)      --  Instance of O365 client object
            client_type(enum)   --  Type of client (OneDrive/Exchange)
        Returns:
            backupset_id(int)   --  Backupset ID of the client
            subclient_id(int)   --  Subclient ID of the client
        Raises:
            SDKException:
                if backupset or subclient doesn&#39;t exist

        &#34;&#34;&#34;
        if client_type == EdiscoveryConstants.ClientType.ONEDRIVE:
            _agent = client.agents.get(EdiscoveryConstants.ONEDRIVE_AGENT)
            _instance = _agent.instances.get(EdiscoveryConstants.ONEDRIVE_INSTANCE)
            _backupset = _instance.backupsets.get(EdiscoveryConstants.ONEDRIVE_BACKUPSET)
            _subclient = _backupset.subclients.get(EdiscoveryConstants.ONEDRIVE_SUBCLIENT)
        else:
            _agent = client.agents.get(EdiscoveryConstants.EXCHANGE_AGENT)
            _instance = _agent.instances.get(EdiscoveryConstants.EXCHANGE_INSTANCE)
            _backupset = _instance.backupsets.get(EdiscoveryConstants.EXCHANGE_BACKUPSET)
            _subclient = _backupset.subclients.get(EdiscoveryConstants.EXCHANGE_SUBCLIENT)
        if _backupset is None or _subclient is None:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;121&#39;)
        return _backupset.backupset_id, _subclient.subclient_id

    def delete(self, data_source_name):
        &#34;&#34;&#34;Deletes the given data source from client

                        Args:

                            data_source_name        (str)       --  Datasource name

                        Returns:

                            None

                        Raises:

                            SDKException:

                                    if failed to find given data source in this client

                                    if failed to delete the data source

        &#34;&#34;&#34;
        if not self.has_data_source(data_source_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;108&#39;)
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._API_DELETE % (self.get(data_source_name).data_source_id, self._client_id)
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to Delete DataSource with error [{response.json().get(&#39;errorMessage&#39;,&#39;&#39;)}]&#34;)
                self.refresh()
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;111&#39;)
        else:
            self._response_not_success(response)

    def has_data_source(self, data_source_name):
        &#34;&#34;&#34;Checks whether given data source exists in this client or not

                Args:

                    data_source_name        (str)       --  Datasource name

                Returns:

                    bool    --  True if exists else false

                Raises:

                    SDKException:

                            if failed to find given data source in this client

        &#34;&#34;&#34;
        if not isinstance(data_source_name, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        return self._data_source_display_names and data_source_name.lower() in self._data_source_display_names

    def get(self, data_source_name):
        &#34;&#34;&#34;returns EdiscoveryDataSource class object for given data source name

                Args:

                    data_source_name        (str)       --  Datasource name

                Returns:

                    obj --  Instance of EdiscoveryDataSource class

                Raises:

                    SDKException:

                            if failed to find given data source in this client

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(data_source_name, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if not self.has_data_source(data_source_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;108&#39;)
        ds_props = self._data_sources[data_source_name.lower()]
        return EdiscoveryDatasource(commcell_object=self._commcell_object,
                                    data_source_id=int(ds_props[EdiscoveryConstants.FIELD_DATA_SOURCE_ID]),
                                    data_source_type=ds_props[EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE],
                                    app_type=self._app_source, client_id=self._client_id)

    def _parse_client_response_for_data_source(self, client_details, field_name, field_type=&#34;str&#34;):
        &#34;&#34;&#34;Parses client response and returns given property from data sources as list

                Args:

                    client_details      (dict)      --  containing EdiscoveryClient details response

                    field_name          (str)       --  Field name to be fetched

                    field_type          (str)       --  Field type to be converted (Default: str)

                Returns:

                    list        --  containing field values from all data sources in response

                Raises:

                    SDKException:

                            if input is not valid


        &#34;&#34;&#34;
        output = []
        old_len = len(output)
        if not isinstance(client_details, dict):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;107&#39;)
        if &#39;childs&#39; not in client_details:
            return output
        for data_source in client_details[&#39;childs&#39;]:
            if &#39;customProperties&#39; in data_source:
                name_value_dict = data_source[&#39;customProperties&#39;][&#39;nameValues&#39;]
                for prop in name_value_dict:
                    prop_name = prop.get(&#39;name&#39;)
                    if prop_name == field_name:
                        if field_type == &#34;int&#34;:
                            output.append(int(prop.get(&#39;value&#39;, 0)))
                        else:
                            if field_name == EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME:
                                output.append(prop.get(&#39;value&#39;, &#39;NA&#39;).lower())
                            else:
                                output.append(prop.get(&#39;value&#39;, &#39;NA&#39;))
            new_len = len(output)
            if old_len == new_len:
                output.append(&#39;Not Found&#39;)
                new_len = new_len + 1
            old_len = new_len
        return output

    def _get_data_source_names(self, client_details):
        &#34;&#34;&#34;returns the list of data source display name and data source name

                Args:

                    client_details      (dict)      --  containing EdiscoveryClient details response

                Returns:

                    list,list       --  Data source display name &amp; Data Source name

                Raises:

                    SDKException:

                            if input is not valid

        &#34;&#34;&#34;
        data_sources_name = []
        data_sources_display_name = []
        if not isinstance(client_details, dict):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;107&#39;)
        if self._app_source and self._app_source == TargetApps.SDG:
            if &#39;dataSources&#39; in client_details:
                data_sources = client_details[&#39;dataSources&#39;]
                for data_source in data_sources:
                    data_sources_name.append(
                        data_source[EdiscoveryConstants.FIELD_DATA_SOURCE_NAME_SEA].lower())
                    data_sources_display_name.append(
                        data_source[EdiscoveryConstants.FIELD_DATA_SOURCE_NAME_SEA].lower())
        elif self._app_source and self._app_source == TargetApps.FSO:
            data_sources_name = self._parse_client_response_for_data_source(
                client_details, EdiscoveryConstants.FIELD_DATA_SOURCE_NAME)
            data_sources_display_name = self._parse_client_response_for_data_source(
                client_details, EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME)
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unknown App source type passed&#34;)
        return data_sources_display_name, data_sources_name

    def _get_data_sources_details(self):
        &#34;&#34;&#34;returns the data sources details associated with ediscovery client

                Args:

                    None

                Return:

                    dict    --  containing data source details

                Raises:

                        SDKException:

                                if failed to get data source details

        &#34;&#34;&#34;
        server_details = self._ediscovery_client_ops.get_ediscovery_client_details()
        return server_details

    def get_datasource_document_count(self, data_source):
        &#34;&#34;&#34;Returns the document count for given data source

                Args:

                    data_source         (str)       --  Name of the data source

                Returns:

                    int --  Document count

                Raises:

                    SDKException:

                            if data source doesn&#39;t exists

                            if failed to get document count

        &#34;&#34;&#34;
        if not isinstance(data_source, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if not self.has_data_source(data_source_name=data_source):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Data Source not exists&#34;)

        if self._app_source and self._app_source == TargetApps.SDG:
            for key, value in self._data_sources[data_source.lower()].items():
                if key == EdiscoveryConstants.FIELD_DOCUMENT_COUNT:
                    return int(value)
        else:
            ds_names = self._parse_client_response_for_data_source(
                client_details=self.ediscovery_client_props,
                field_name=EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME)
            docs = self._parse_client_response_for_data_source(
                client_details=self.ediscovery_client_props,
                field_name=EdiscoveryConstants.FIELD_DOCUMENT_COUNT,
                field_type=&#34;int&#34;)
            return docs[ds_names.index(data_source.lower())]

    @property
    def data_sources(self):
        &#34;&#34;&#34;returns the list of data sources display name associated with this client

            Returns:

                list --  Name of data sources

        &#34;&#34;&#34;
        return self._data_source_display_names

    @property
    def client_id(self):
        &#34;&#34;&#34;returns the associated client id

            Returns:

                int --  client id

        &#34;&#34;&#34;
        return self._client_id

    @property
    def client_targetapp(self):
        &#34;&#34;&#34;returns the source client targetapp

            Returns:

                str --  Target app for this data sources

        &#34;&#34;&#34;
        return self._app_source.value

    @property
    def ediscovery_client_props(self):
        &#34;&#34;&#34;Returns the associated client properties

            Returns:

                dict --  containing client properties

        &#34;&#34;&#34;
        return self._ediscovery_client_props

    @property
    def total_documents(self):
        &#34;&#34;&#34;returns the total document counts of all data sources associated with this client

            Returns:

                int --  Total crawled documents from all of these data sources

        &#34;&#34;&#34;
        total_doc = 0
        if self._app_source and self._app_source == TargetApps.SDG:
            for data_source in self._data_sources:
                for key, value in self._data_sources[data_source].items():
                    if key == EdiscoveryConstants.FIELD_DOCUMENT_COUNT:
                        total_doc = total_doc + int(value)
        else:
            total_doc = sum(
                self._parse_client_response_for_data_source(
                    client_details=self.ediscovery_client_props,
                    field_name=EdiscoveryConstants.FIELD_DOCUMENT_COUNT,
                    field_type=&#34;int&#34;))
        return total_doc


class EdiscoveryDatasource():
    &#34;&#34;&#34;Class to represent single datasource associated with ediscovery client&#34;&#34;&#34;

    def __init__(self, commcell_object, data_source_id, data_source_type, client_id, app_type=TargetApps.FSO):
        &#34;&#34;&#34;Initializes an instance of the EdiscoveryDataSource class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                data_source_id      (int)       --  Data source id

                data_source_type    (int/str)   --  Data Source type (Example : 5 for file)
                                                        Refer to EdiscoveryConstants class in activateapps\\constants.py

                client_id           (int)       --  client id where this data source belongs to

                app_type            (enum)      --  Specifies which app type these data sources belongs too
                                                        Default:FSO

            Returns:
                object  -   instance of the EdiscoveryDataSource class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._data_source_name = None
        self._data_source_actual_name = None
        self._data_source_id = None
        self._data_source_type = None
        self._data_source = None
        self._data_source_props = None
        self._client_id = client_id
        self._collection_client_id = None
        self._core_name = None
        self._computed_core_name = None
        self._cloud_id = None
        self._core_id = None
        self._crawl_type = None
        self._dc_plan_id = None
        self._data_source_entity_id = 132
        self._app_type = app_type
        self._API_DATA_SOURCE = self._services[&#39;EDISCOVERY_DATA_SOURCES&#39;]
        self._API_SEARCH = self._services[&#39;EDISCOVERY_DYNAMIC_FEDERATED&#39;]
        self._API_ACTIONS = self._services[&#39;EDISCOVERY_REVIEW_ACTIONS&#39;]
        self._API_ACTIONS_WITH_REQUEST = self._services[&#39;EDISCOVERY_REVIEW_ACTIONS_WITH_REQUEST&#39;]
        self._jobs = self._commcell_object.job_controller
        self._data_source_id = data_source_id
        if isinstance(data_source_type, int):
            self._data_source_type = EdiscoveryConstants.DATA_SOURCE_TYPES.get(data_source_type)
        else:
            self._data_source_type = data_source_type
        self.refresh()
        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_data_source_properties(self):
        &#34;&#34;&#34;returns the data source properties for this data source

            Args:

                None

            Returns:

                Dict    --  Containing data source details

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_DATA_SOURCE %
            (self._data_source_id, self._data_source_type))
        if flag:
            if response.json() and &#39;collections&#39; in response.json():
                collection = response.json()[&#39;collections&#39;][0]
                self._collection_client_id = collection.get(&#39;clientId&#39;)
                self._core_name = collection.get(&#39;coreName&#39;)
                self._computed_core_name = collection.get(&#39;computedCoreName&#39;)
                self._cloud_id = collection.get(&#39;cloudId&#39;)
                self._core_id = collection.get(&#39;coreId&#39;)
                ds_list = collection.get(&#39;datasources&#39;, [])
                if len(ds_list) == 1:
                    self._data_source_props = ds_list[0].get(&#39;properties&#39;, [])
                    # fetch crawl type from above properties fetched.
                    self._crawl_type = self._get_property_value(property_name=EdiscoveryConstants.FIELD_CRAWL_TYPE)
                    self._dc_plan_id = self._get_property_value(property_name=EdiscoveryConstants.FIELD_DC_PLAN_ID)
                    self._data_source_name = ds_list[0].get(&#39;displayName&#39;, &#39;NA&#39;)
                    self._data_source_actual_name = ds_list[0].get(&#39;datasourceName&#39;, &#39;NA&#39;)
                return collection
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;110&#39;)
        self._response_not_success(response)

    def _get_property_value(self, property_name):
        &#34;&#34;&#34;Returns the property value for property name

            Args:

                property_name       (str)       --  Name of property

            Returns:
                str --  value of property

        &#34;&#34;&#34;
        for prop in self.data_source_props:
            if &#39;propertyName&#39; in prop:
                if prop[&#39;propertyName&#39;].lower() == property_name.lower():
                    return prop[&#39;propertyValue&#39;]
        return &#34;&#34;

    def refresh(self):
        &#34;&#34;&#34;refresh the data source properties&#34;&#34;&#34;
        self._data_source = self._get_data_source_properties()

    def tag_items(
            self,
            tags,
            document_ids=None,
            ops_type=1,
            create_review=False,
            reviewers=None,
            approvers=None,
            req_name=None):
        &#34;&#34;&#34;Applies given tag to documents

            Args:

                tags            (list)      --  list of tags names which needs to be applied
                                                        Format : Tagset\\TagName
                                                        Example : DiscoveryEntity\\American

                document_ids    (list)      --  list of document content id&#39;s which needs to be tagged

                ops_type        (int)       --  Denotes operation type for tagging  (1-Add or 2-Delete)
                                                        Default : 1(Add)

                create_review   (bool)      --  Specifies whether to create review request for this tagging or not
                                                        Default:False

                reviewers       (list)      --  List of review users

                approvers       (list)      --  List of approver users

                req_name        (str)       --  Request name

            Returns:

                None if it is tagging with review request

                jobid (str) -- if it is bulk operation of tagging all items without review request

            Raises:

                SDKException:

                        if tag name doesn&#39;t exists in commcell

                        if failed to apply tag

                        if response is empty

                        if data source doesn&#39;t belongs to FSO app
        &#34;&#34;&#34;
        if self._app_type.value != TargetApps.FSO.value:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Tagging is supported only for FSO app&#34;)
        if not isinstance(tags, list):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        query = &#34;&#34;
        request_json = None
        tag_guids = []
        api = self._API_ACTIONS
        if create_review:
            api = self._API_ACTIONS_WITH_REQUEST

        tag_mgr = self._commcell_object.activate.entity_manager(EntityManagerTypes.TAGS)
        for tag in tags:
            tag_split = tag.split(&#34;\\\\&#34;)
            if not tag_mgr.has_tag_set(tag_set_name=tag_split[0]):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unable to find tagset in the commcell&#34;)
            tag_set_obj = tag_mgr.get(tag_split[0])
            if not tag_set_obj.has_tag(tag_split[1]):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unable to find tag in the tagset&#34;)
            tag_obj = tag_set_obj.get(tag_split[1])
            if not create_review:
                tag_guids.append({&#34;id&#34;: tag_obj.guid})
            else:
                tag_guids.append(tag_obj.guid)

        if not create_review:
            if document_ids:
                for doc in document_ids:
                    query = query + f&#34;(contentid:{doc}) OR &#34;
                last_char_index = query.rfind(&#34; OR &#34;)
                query = query[:last_char_index]
            else:
                query = &#34;*:*&#34;
            search_params = self._ediscovery_client_ops.form_search_params(
                query=query, key=&#34;name&#34;, params={&#34;rows&#34;: &#34;0&#34;})
            tag_request = copy.deepcopy(EdiscoveryConstants.TAGGING_ITEMS_REQUEST)
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_TAG_REQ_JSON)
            if ops_type != 1:
                tag_request[&#39;opType&#39;] = &#34;DELETE&#34;
            if not document_ids:  # bulk request
                tag_request[&#39;isAsync&#39;] = True
            tag_request[&#39;entityIds&#39;].append(self.data_source_id)
            tag_request[&#39;searchRequest&#39;] = search_params
            tag_request[&#39;tags&#39;] = tag_guids
            tag_request[&#39;dsType&#39;] = self.data_source_type_id
            request_json[&#39;taggingRequest&#39;] = tag_request
            request_json[&#39;remActionRequest&#39;][&#39;dataSourceId&#39;] = self.data_source_id
            if not document_ids:
                request_json[&#39;remActionRequest&#39;][&#39;isBulkOperation&#39;] = True

        else:
            request_json = copy.deepcopy(EdiscoveryConstants.TAGGING_ITEMS_REVIEW_REQUEST)
            if ops_type != 1:
                request_json[&#39;taggingInformation&#39;][&#39;opType&#39;] = &#34;DELETE&#34;
            request_json[&#39;files&#39;] = json.dumps(self._form_files_list(
                document_ids=document_ids,
                attr_list=EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET[self.data_source_type_id]))
            request_json[&#39;taggingInformation&#39;][&#39;tagIds&#39;] = tag_guids
            request_json[&#39;options&#39;] = str(
                self._form_request_options(
                    reviewers=reviewers,
                    approvers=approvers,
                    document_ids=document_ids,
                    req_name=req_name if req_name else f&#34;{self.data_source_name}_tag_{int(time.time())}&#34;))

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, api, request_json
        )
        if flag:
            if response.json():
                response = response.json()
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Tagging failed with error : {response.get(&#39;errorMessage&#39;, &#39;&#39;)}&#34;)
                else:
                    if not create_review:
                        # tagging without review. return the job id
                        return response[&#39;jobId&#39;]
                    return
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;113&#39;)
        self._response_not_success(response)

    def get_job_history(self, limit=50, lookup_time=2160):
        &#34;&#34;&#34;returns the job history details for this data source

            Args:

                limit       (int)           --  No of jobs to return (default: 50 rows)

                lookup_time (int)           --  list of jobs to be retrieved which are specified
                    hours older

                            default: 2160 hours (last 90 days)

        &#34;&#34;&#34;
        return self._jobs.finished_jobs(lookup_time=lookup_time,
                                        limit=limit,
                                        entity={&#34;dataSourceId&#34;: self.data_source_id})

    def get_active_jobs(self, limit=50, lookup_time=2160):
        &#34;&#34;&#34;returns the active jobs details for this data source

            Args:

                limit       (int)           --  No of jobs to return (default: 50 rows)

                lookup_time (int)           --  list of jobs to be retrieved which are started within specified
                    hours older

                            default: 2160 hours (last 90 days)

            Returns:

                    dict    -   dictionary consisting of the job IDs matching the given criteria
                                as the key, and their details as its value

        &#34;&#34;&#34;
        return self._jobs.active_jobs(lookup_time=lookup_time,
                                      limit=limit,
                                      entity={&#34;dataSourceId&#34;: self.data_source_id})

    def wait_for_export(self, token, wait_time=60, download=True, download_location=os.getcwd()):
        &#34;&#34;&#34;Waits for Export to CSV to finish

                Args:

                    wait_time           (int)       --  time interval to wait for job completion in Mins
                                                            Default : 60Mins

                    token               (str)       --  Export to CSV token GUID

                    download            (bool)      --  specify whether to download exported file or not

                    download_location   (str)       --  Path where to download exported csv file
                                                                Default: Current working dir

                Return:

                    str     -- Download GUID for exported CSV file if download=false
                               File path containing exported csv file if download=true

                Raises:

                    SDKException:

                            if Export job fails

                            if timeout happens

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.wait_for_export(token=token,
                                                           wait_time=wait_time,
                                                           download=download,
                                                           download_location=download_location)

    def export(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do export to CSV on data

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - Exports all docs)

                                                    Example :

                                                        1) Size filter --&gt; Size:[10 TO 1024]
                                                        2) File name filter --&gt; FileName_idx:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                str     --  export operation token


            Raises:

                SDKException:

                        if failed to perform export

        &#34;&#34;&#34;
        if not attr_list:
            if self.data_source_type == EdiscoveryConstants.DATA_SOURCE_TYPES[5]:
                attr_list = EdiscoveryConstants.FS_DEFAULT_EXPORT_FIELDS
        return self._ediscovery_client_ops.export(criteria=criteria, attr_list=attr_list,
                                                  params=params)

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on data source and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        1) Size filter --&gt; Size:[10 TO 1024]
                                                        2) File name filter --&gt; FileName_idx:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                list(dict),dict    --  Containing document details &amp; facet details(if any)

            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list,
                                                  params=params)

    def _form_request_options(self, req_name, reviewers, approvers, document_ids=None):
        &#34;&#34;&#34;Returns the options for review request

            Args:

                req_name        (str)       --  Request Name

                reviewers       (list)      --  List of review users

                approvers       (list)      --  List of approver users

                document_ids    (list)      --  list of document id&#39;s
                                                    Default:None

            Returns:

                dict        --  Containing options

            Raises:

                SDKException:

                        if failed to get document details

                        if failed to find user details for reviewers/approvers
        &#34;&#34;&#34;
        options = {
            &#34;Name&#34;: req_name,
            &#34;DatasetId&#34;: str(self.data_source_id),
            &#34;DatasetType&#34;: &#34;SEA_DATASOURCE_ENTITY&#34;,
            &#34;DatasetName&#34;: self.data_source_name,
            &#34;CreatedFrom&#34;: &#34;FSO&#34; if self._app_type.value == TargetApps.FSO.value else &#34;SDG&#34;,
            &#34;ClientId&#34;: str(self._client_id)
        }
        if document_ids:
            query = &#34;&#34;
            for doc in document_ids:
                query = query + f&#34;(contentid:{doc}) OR &#34;
            last_char_index = query.rfind(&#34; OR &#34;)
            query = query[:last_char_index]
            count, docs, _ = self.search(
                criteria=query, attr_list=EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET[self.data_source_type_id])
            if len(docs) != len(document_ids):
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    &#34;Unable to find document details from given list of document id&#34;)
            file_names = []
            for doc in docs:
                file_names.append(doc[&#39;FileName&#39;])
            options[&#39;ReviewCriteria&#39;] = json.dumps({
                &#34;Files&#34;: file_names
            })
        else:
            options[&#39;ReviewCriteria&#39;] = json.dumps({})
        # reviewer
        reviewers_list = []
        for user in reviewers:
            if not self._commcell_object.users.has_user((user)):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Unable to find reviewer user : {user}&#34;)
            user_obj = self._commcell_object.users.get(user)
            reviewers_list.append({&#34;id&#34;: user_obj.user_id,
                                   &#34;name&#34;: user_obj.user_name
                                   })
        options[&#39;Reviewers&#39;] = str(reviewers_list)

        # approvers
        approvers_list = []
        for user in approvers:
            if not self._commcell_object.users.has_user((user)):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Unable to find approver user : {user}&#34;)
            user_obj = self._commcell_object.users.get(user)
            approvers_list.append({&#34;id&#34;: user_obj.user_id,
                                   &#34;name&#34;: user_obj.user_name
                                   })
        options[&#39;Approvers&#39;] = str(approvers_list)
        return options

    def _form_files_list(self, document_ids, attr_list):
        &#34;&#34;&#34;returns the list of dict containing files details

                Args:

                    document_ids        (list)      --  list of document id&#39;s

                    attr_list           (set)       --  Set of fields needed to be fetched for document id

                Returns:

                      list(dict)      --  Containing file details

                Raises:

                    SDKException:

                        if failed to get document details

        &#34;&#34;&#34;
        query = &#34;&#34;
        files_list = []
        for doc in document_ids:
            query = query + f&#34;(contentid:{doc}) OR &#34;
        last_char_index = query.rfind(&#34; OR &#34;)
        query = query[:last_char_index]
        count, docs, _ = self.search(criteria=query, attr_list=attr_list)
        if len(docs) != len(document_ids):
            raise SDKException(
                &#39;EdiscoveryClients&#39;,
                &#39;102&#39;,
                &#34;Unable to find document details from given list of document id&#34;)
        for doc in docs:
            doc_dict = {
                &#34;file&#34;: doc[&#39;Url&#39;],
                &#34;dsid&#34;: str(
                    self.data_source_id),
                &#34;contentid&#34;: doc[&#39;contentid&#39;],
                &#34;CreatedTime&#34;: doc[&#39;CreatedTime&#39;],
                &#34;ClientId&#34;: doc[&#39;ClientId&#39;],
                &#34;dstype&#34;: self.data_source_type_id}
            files_list.append(doc_dict)
        return files_list

    def review_action(self, action_type, reviewers=None, approvers=None, document_ids=None, req_name=None, **kwargs):
        &#34;&#34;&#34;do review action on documents

                Args:

                    action_type         (enum)      --  Type of action to be taken
                                                        Refer to EdiscoveryConstants.ReviewActions

                    document_ids        (list)      --  list of document id&#39;s
                                                            Default:None (means all docs)

                    reviewers           (list)      --  List of review users

                    approvers           (list)      --  List of approver users

                    req_name            (str)       --  Request name

                kwargs arguments:

                    backup_delete       (bool)      --  Specifies whether to delete document from backup or not

                    destination         (str)       --  Destination UNC path for move operation

                    user_name           (str)       --  Username to access share path

                    password            (str)       --  Password for user in base64 encoded

                    create_review       (bool)      --  speicifies whether to create review or not for this action
                                                            (For Delete &amp; Move, it is TRUE always)

                    retain_month        (int)       --  no of months to set as retention

                    ignore_all_risks    (bool)      --  specifies whether it has to be ignore risk fully or not

                    ignore_risk_type    (list)      --  list of risks which needs to be ignored
                                                            Refer to EDiscoveryConstants.RiskTypes

                Returns:

                    None -- if create_review is true

                    job id -- if create_review is false

                Raises:

                    SDKException:

                        if action type is not valid

                        if failed to do review action on documents

                        if document id&#39;s not found
        &#34;&#34;&#34;
        if not isinstance(action_type, EdiscoveryConstants.ReviewActions):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if self._app_type.value == TargetApps.FSO.value and \
                action_type.value not in EdiscoveryConstants.REVIEW_ACTION_FSO_SUPPORTED:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;{action_type.value} is not supported for FSO app&#34;)
        if self._app_type.value == TargetApps.SDG.value and \
                action_type.value not in EdiscoveryConstants.REVIEW_ACTION_SDG_SUPPORTED:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;{action_type.value} is not supported for SDG app&#34;)
        attr_list = None
        api = self._API_ACTIONS
        create_review = kwargs.get(&#39;create_review&#39;, False)
        if action_type == EdiscoveryConstants.ReviewActions.DELETE or \
                action_type == EdiscoveryConstants.ReviewActions.MOVE:
            # For Delete &amp; Move, review request is compulsory
            create_review = True
        if create_review:
            if not reviewers or not approvers:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Reviewers/Approvers missing in input&#39;)
        if self.data_source_type_id not in EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Not supported data source for review action&#34;)
        attr_list = EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET[self.data_source_type_id]
        request_json = None
        # For Delete &amp; Move, review request is compulsory so non-review case is not handled for this block
        if action_type.value == EdiscoveryConstants.ReviewActions.DELETE.value:
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_DELETE_REQ_JSON)
            request_json[&#39;deleteFromBackup&#39;] = kwargs.get(&#34;backup_delete&#34;, False)
        elif action_type.value == EdiscoveryConstants.ReviewActions.MOVE.value:
            if &#39;destination&#39; not in kwargs or &#39;user_name&#39; not in kwargs or &#39;password&#39; not in kwargs:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Required params missing for move operation&#34;)
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_MOVE_REQ_JSON)
            request_json[&#39;newDestination&#39;] = kwargs.get(&#34;destination&#34;, &#39;&#39;)
            request_json[&#39;username&#39;] = kwargs.get(&#34;user_name&#34;, &#39;&#39;)
            request_json[&#39;password&#39;] = kwargs.get(&#34;password&#34;, &#39;&#39;)
        elif action_type.value == EdiscoveryConstants.ReviewActions.RETENTION.value:
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_SET_RETENTION_REQ_JSON)
            if &#39;retain_month&#39; not in kwargs:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Retention month input missing for this operation&#39;)
            if not create_review:
                request_json[&#39;setRetentionReq&#39;][&#39;numOfMonthsRemain&#39;] = kwargs.get(&#39;retain_month&#39;)
                request_json[&#39;remActionRequest&#39;][&#39;dataSourceId&#39;] = self._data_source_id
            else:
                request_json[&#39;numOfMonthsRemain&#39;] = kwargs.get(&#39;retain_month&#39;)
                request_json[&#39;dataSourceId&#39;] = self._data_source_id
                # delete unwanted keys as it is review request
                if &#39;remActionRequest&#39; in request_json:
                    del request_json[&#39;remActionRequest&#39;]
                if &#39;setRetentionReq&#39; in request_json:
                    del request_json[&#39;setRetentionReq&#39;]
        elif action_type.value == EdiscoveryConstants.ReviewActions.IGNORE.value:
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_IGNORE_FILES_REQ_JSON)
            ignore_all = kwargs.get(&#39;ignore_all_risks&#39;, False)
            if not ignore_all and &#39;ignore_risk_type&#39; not in kwargs:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Ignore risk type details missing for this operation&#39;)
            if not create_review:
                if ignore_all:
                    request_json[&#39;ignoreRisksReq&#39;][&#39;ignoreAllRisks&#39;] = True
                else:
                    request_json[&#39;ignoreRisksReq&#39;][&#39;ignoreAllRisks&#39;] = False
                    request_json[&#39;ignoreRisksReq&#39;][&#39;ignoreRiskTypeList&#39;] = kwargs.get(&#39;ignore_risk_type&#39;)
                request_json[&#39;remActionRequest&#39;][&#39;dataSourceId&#39;] = self._data_source_id
            else:
                if ignore_all:
                    request_json[&#39;ignoreAllRisks&#39;] = True
                else:
                    request_json[&#39;ignoreAllRisks&#39;] = False
                    request_json[&#39;ignoreRiskTypeList&#39;] = kwargs.get(&#39;ignore_risk_type&#39;)

                request_json[&#39;dataSourceId&#39;] = self._data_source_id
                # delete unwanted keys as it is review request
                if &#39;remActionRequest&#39; in request_json:
                    del request_json[&#39;remActionRequest&#39;]
                if &#39;ignoreRisksReq&#39; in request_json:
                    del request_json[&#39;ignoreRisksReq&#39;]

        if document_ids:
            query = &#34;&#34;
            for doc in document_ids:
                query = query + f&#34;(contentid:{doc}) OR &#34;
            last_char_index = query.rfind(&#34; OR &#34;)
            query = query[:last_char_index]
            # for non-review request, doc id need to set at search request inside remaction
            if not create_review:
                # make sure whether passed document ids are correct
                count, docs, _ = self.search(
                    criteria=f&#34;{EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS} AND {query}&#34;,
                    attr_list=EdiscoveryConstants.REVIEW_ACTION_SEARCH_FL_SET)
                if len(docs) != len(document_ids):
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        &#34;Unable to find document details from given list of document id&#34;)

                request_json[&#39;remActionRequest&#39;][&#39;searchRequest&#39;] = json.dumps(
                    self._ediscovery_client_ops.form_search_params(
                        criteria=EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS,
                        attr_list=EdiscoveryConstants.REVIEW_ACTION_SEARCH_FL_SET,
                        params={
                            &#34;start&#34;: &#34;0&#34;},
                        query=query, is_separate_attr=True))

            else:
                request_json[&#39;files&#39;] = json.dumps(
                    self._form_files_list(
                        document_ids=document_ids,
                        attr_list=attr_list))
        else:
            # bulk operation request. Delete unnecessary fields
            if &#39;files&#39; in request_json:
                del request_json[&#39;files&#39;]
            if not create_review:
                request_json[&#39;remActionRequest&#39;][&#39;searchRequest&#39;] = EdiscoveryConstants.REVIEW_ACTION_BULK_SEARCH_REQ
                request_json[&#39;remActionRequest&#39;][&#39;isBulkOperation&#39;] = True
                request_json[&#39;remActionRequest&#39;][&#39;handlerId&#39;] = self._ediscovery_client_ops.get_handler_id()
            else:
                request_json[&#39;searchRequest&#39;] = EdiscoveryConstants.REVIEW_ACTION_BULK_SEARCH_REQ
                request_json[&#39;handlerId&#39;] = self._ediscovery_client_ops.get_handler_id()
                request_json[&#39;isBulkOperation&#39;] = True

        if create_review:
            api = self._API_ACTIONS_WITH_REQUEST
            request_json[&#39;options&#39;] = json.dumps(
                self._form_request_options(
                    reviewers=reviewers,
                    approvers=approvers,
                    document_ids=document_ids,
                    req_name=req_name if req_name else f&#34;{self.data_source_name}_{action_type.name}&#34;
                                                       f&#34;_{int(time.time())}&#34;))

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, api, request_json
        )
        if flag:
            if response.json():
                response = response.json()
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Review action failed with error - {response.get(&#39;errorMsg&#39;)}&#34;)
                if &#39;jobId&#39; in response and not create_review:
                    return response[&#39;jobId&#39;]
                return
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;116&#39;)
        self._response_not_success(response)

    def start_collection(self, is_incr=True):
        &#34;&#34;&#34;Starts collection job on this data source
                Args:

                    is_incr         (bool)      --  Specifies whether to invoke incremental or full crawl job
                                                        Default:True (Incremental job)

                Return:

                    None

                Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.start_job(is_incr=is_incr)

    @property
    def data_source_name(self):
        &#34;&#34;&#34;returns the data source name

            Returns:

                str --  Name of data source

        &#34;&#34;&#34;
        return self._data_source_name

    @property
    def data_source_type(self):
        &#34;&#34;&#34;returns the data source type

            Returns:

                str --  Type of data source

        &#34;&#34;&#34;
        return self._data_source_type

    @property
    def data_source_type_id(self):
        &#34;&#34;&#34;returns the data source type id

            Returns:

                int --  data source type

        &#34;&#34;&#34;
        position = list(EdiscoveryConstants.DATA_SOURCE_TYPES.values()).index(self.data_source_type)
        return list(EdiscoveryConstants.DATA_SOURCE_TYPES.keys())[position]

    @property
    def data_source_id(self):
        &#34;&#34;&#34;returns the data source id

            Returns:

                int --  data source id

        &#34;&#34;&#34;
        return self._data_source_id

    @property
    def data_source_props(self):
        &#34;&#34;&#34;returns the data source properties

            Returns:

                dict --  data source properties

        &#34;&#34;&#34;
        return self._data_source_props

    @property
    def cloud_id(self):
        &#34;&#34;&#34;returns the index server cloudid associated with this data source

            Returns:

                int --  index server cloud id

        &#34;&#34;&#34;
        return self._cloud_id

    @property
    def core_name(self):
        &#34;&#34;&#34;returns the core name for this data source

            Returns:

                str --  core name for this data source

        &#34;&#34;&#34;
        return self._core_name

    @property
    def computed_core_name(self):
        &#34;&#34;&#34;returns the computed core name for this data source

            Returns:

                str --  Index server core name for this data source

        &#34;&#34;&#34;
        return self._computed_core_name

    @property
    def core_id(self):
        &#34;&#34;&#34;returns the core id for this data source

            Returns:

                int --  core id

        &#34;&#34;&#34;
        return self._core_id

    @property
    def crawl_type_name(self):
        &#34;&#34;&#34;returns the crawl type enum name for this data source

            Returns:

                str --  crawl type

        &#34;&#34;&#34;
        return EdiscoveryConstants.CrawlType(int(self._crawl_type)).name

    @property
    def crawl_type(self):
        &#34;&#34;&#34;returns the crawl type for this data source

            Returns:

                int --  crawl type

        &#34;&#34;&#34;
        return self._crawl_type

    @property
    def plan_id(self):
        &#34;&#34;&#34;returns the DC plan id associated

            Returns:

                int -- Data classification plan id

        &#34;&#34;&#34;
        return self._dc_plan_id

    @property
    def client_id(self):
        &#34;&#34;&#34;returns the client id associated

            Returns:

                int -- client id

        &#34;&#34;&#34;
        return self._client_id

    @property
    def total_documents(self):
        &#34;&#34;&#34;returns the total document from this data source

            Returns:

                int --  Total document count

        &#34;&#34;&#34;
        count, _, _ = self.search(criteria=EdiscoveryConstants.FIELD_IS_FILE, params={&#34;rows&#34;: &#34;0&#34;})
        return count

    @property
    def sensitive_files_count(self):
        &#34;&#34;&#34;returns the total sensitive files count on this data source

            Returns:

                int --  Sensitive files count

        &#34;&#34;&#34;
        count, _, _ = self.search(criteria=EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS,
                                  params={&#34;rows&#34;: &#34;0&#34;})
        return count

    @property
    def name(self):
        &#34;&#34;&#34;returns the actual name for this data source

            Returns:

                str --  Actual name of the datasource

        &#34;&#34;&#34;
        return self._data_source_actual_name

    @property
    def index_server_node_client_id(self):
        &#34;&#34;&#34;returns the associated Index server node client id on which the collection exists

            Returns:

                str --  Index server node client id on which the collection exists

        &#34;&#34;&#34;
        return self._collection_client_id</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations"><code class="flex name class">
<span>class <span class="ident">EdiscoveryClientOperations</span></span>
<span>(</span><span>commcell_object, class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on ediscovery client.</p>
<p>Initializes an instance of the EdiscoveryClientOperations class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>class_object
(object)
&ndash; instance of Inventory/Asset/FsoServer
/EdiscoveryDataSource/EdiscoveryDataSources/FsoServerGroup class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the EdiscoveryClientOperations class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L498-L1607" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class EdiscoveryClientOperations():
    &#34;&#34;&#34;Class for performing operations on ediscovery client.&#34;&#34;&#34;

    def __init__(self, commcell_object, class_object):
        &#34;&#34;&#34;Initializes an instance of the EdiscoveryClientOperations class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                class_object        (object)    -- instance of Inventory/Asset/FsoServer
                                                        /EdiscoveryDataSource/EdiscoveryDataSources/FsoServerGroup class

            Returns:
                object  -   instance of the EdiscoveryClientOperations class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._class_obj = class_object
        self._type = None
        self._operation = None
        self._client_id = None
        self._data_source_id = None
        self._ds_type_names = None
        self._include_doc_count = None
        self._limit = None
        self._offset = None
        self._sort_by = None
        self._sort_dir = None
        self._app_type = None
        self._associations = None
        self._search_entity_type = None
        self._search_entity_id = None
        self._client_entity_type = 3
        self._request_type = None
        self._request_review_set_id = None
        self._request_app = None
        self._API_CRAWL = self._services[&#39;EDISCOVERY_CRAWL&#39;]
        self._API_JOBS_HISTORY = self._services[&#39;EDISCOVERY_JOBS_HISTORY&#39;]
        self._API_JOB_STATUS = self._services[&#39;EDISCOVERY_JOB_STATUS&#39;]
        self._API_CLIENT_DETAILS = self._services[&#39;EDISCOVERY_V2_GET_CLIENT_DETAILS&#39;]
        self._API_SECURITY_ENTITY = self._services[&#39;ENTITY_SECURITY_ASSOCIATION&#39;]
        self._API_SECURITY = self._services[&#39;EDISCOVERY_SECURITY_ASSOCIATION&#39;]
        self._API_SEARCH = self._services[&#39;EDISCOVERY_DYNAMIC_FEDERATED&#39;]
        self._API_GET_DEFAULT_HANDLER = self._services[&#39;EDISCOVERY_GET_DEFAULT_HANDLER&#39;]
        self._API_EXPORT = self._services[&#39;EDISCOVERY_EXPORT&#39;]
        self._API_EXPORT_STATUS = self._services[&#39;EDISCOVERY_EXPORT_STATUS&#39;]
        self._CREATE_POLICY = self._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]
        self._API_GET_EDISCOVERY_CLIENT_DETAILS_V1 = copy.deepcopy(self._services[&#39;EDISCOVERY_CLIENT_DETAILS&#39;])
        self._API_DOC_TASK = self._services[&#39;EDISCOVERY_REQUEST_DOCUMENT_MARKER&#39;]
        self._API_CONFIGURE_TASK = self._services[&#39;EDISCOVERY_CONFIGURE_TASK&#39;]
        self._API_TASK_WORKFLOW = self._services[&#39;EDICOVERY_TASK_WORKFLOW&#39;]
        from .file_storage_optimization import FsoServer, FsoServerGroup
        from .sensitive_data_governance import Project
        from .request_manager import Request

        if isinstance(class_object, FsoServer):
            self._client_id = class_object.server_id
            self._include_doc_count = 1
            self._limit = self._offset = 0
            self._sort_by = 2
            self._sort_dir = 0
            self._ds_type_names = f&#34;{EdiscoveryConstants.DS_FILE},{EdiscoveryConstants.DS_CLOUD_STORAGE}&#34;
            self._data_source_id = 0  # invoke on all data sources
            self._type = 1  # Client
            self._operation = 0
            self._app_type = 1
            self._search_entity_type = 3
            self._search_entity_id = self._client_id
        elif isinstance(class_object, EdiscoveryDatasource):
            self._search_entity_type = 132
            self._search_entity_id = class_object.data_source_id
            self._data_source_id = class_object.data_source_id
            self._type = 1  # Client
            self._operation = 2  # incremental job by default
            self._client_id = class_object.client_id
        elif isinstance(class_object, EdiscoveryDataSources):
            self._client_id = class_object.client_id
            self._include_doc_count = 1
            self._limit = self._offset = 0
            self._sort_by = 2
            self._sort_dir = 0
            if class_object.client_targetapp == TargetApps.FSO.value:
                # based on caller, set appropriate ds types supported for that
                self._ds_type_names = f&#34;{EdiscoveryConstants.DS_FILE},{EdiscoveryConstants.DS_CLOUD_STORAGE}&#34;
        elif isinstance(class_object, FsoServerGroup):
            self._search_entity_type = 28
            self._search_entity_id = class_object.server_group_id
        elif isinstance(class_object, Project):
            self._client_id = class_object.project_id
            self._app_type = 2  # for sharing, app type param
            self._search_entity_type = 188
            self._search_entity_id = class_object.project_id
        elif isinstance(class_object, Request):
            self._client_id = class_object.request_id
            self._request_type = class_object.request_type
            self._request_review_set_id = class_object.review_set_id
            self._request_app = class_object.request_app
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;refresh ediscovery client properties&#34;&#34;&#34;
        self._associations = self._get_associations()

    def schedule(self, schedule_name, pattern_json, ops_type=2):
        &#34;&#34;&#34;Creates or modifies the schedule associated with ediscovery client

                Args:

                    schedule_name       (str)       --  Schedule name

                    pattern_json        (dict)      --  Schedule pattern dict
                                                        (Refer to Create_schedule_pattern in schedule.py)

                    ops_type            (int)       --  Operation type

                                                            Default : 2 (Add)

                                                            Supported : 2 (Add/Modify)

                Raises:

                      SDKException:

                            if input is not valid

                            if failed to create/modify schedule

        &#34;&#34;&#34;
        if not isinstance(schedule_name, str) or not isinstance(pattern_json, dict):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if ops_type not in [2]:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Schedule operation type provided is not supported&#34;)
        request_json = copy.deepcopy(EdiscoveryConstants.SERVER_LEVEL_SCHEDULE_JSON)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;] = int(self._client_id)
        request_json[&#39;taskInfo&#39;][&#39;task&#39;][
            &#39;taskName&#39;] = f&#34;Cvpysdk created Schedule -{schedule_name} for Server id - {self._client_id}&#34;
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][
            &#39;subTaskName&#39;] = schedule_name
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;] = pattern_json
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;contentIndexingOption&#39;][&#39;operationType&#39;] = ops_type

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_POLICY, request_json
        )
        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    task_id = str(response.json()[&#34;taskId&#34;])
                    if task_id:
                        return

                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return
                    else:
                        raise SDKException(
                            &#39;EdiscoveryClients&#39;,
                            &#39;102&#39;,
                            f&#34;Schedule operation failed on server - {error_code} - {error_message}&#34;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def form_search_params(
            self,
            criteria=None,
            attr_list=None,
            params=None,
            query=&#34;*:*&#34;,
            key=&#34;key&#34;,
            is_separate_attr=False):
        &#34;&#34;&#34;returns the search params dict based on input

            Args:

                criteria        (str)      --  containing criteria for query

                                                    Example :

                                                        Size:[10 TO 1024]
                                                        FileName:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

                query           (str)      --   query to be performed (acts as q param in query)
                                                    default:None (Means *:*)

                key             (str)      --   key name to be used in request (default:key)

                is_separate_attr (bool)    --   specifies whether attribute list needs to formed as separate key-value

            Returns:

                dict        --  Containing searchparams details

                Example : {
                              &#34;searchParams&#34;: [
                                {
                                  &#34;key&#34;: &#34;wt&#34;,
                                  &#34;value&#34;: &#34;json&#34;
                                },
                                {
                                  &#34;key&#34;: &#34;defType&#34;,
                                  &#34;value&#34;: &#34;edismax&#34;
                                },
                                {
                                  &#34;key&#34;: &#34;q&#34;,
                                  &#34;value&#34;: &#34;*:*&#34;
                                },
                                {
                                  &#34;key&#34;: &#34;fq&#34;,
                                  &#34;value&#34;: &#34;(contentid:949c3b53ce4dd72a82b8e67039eeddef)&#34;
                                },
                                {
                                  &#34;key&#34;: &#34;fl&#34;,
                                  &#34;value&#34;: &#34;contentid,CreatedTime,Url,ClientId&#34;
                                }
                              ]
                            }
        &#34;&#34;&#34;
        search_params = copy.deepcopy(EdiscoveryConstants.DYNAMIC_FEDERATED_SEARCH_PARAMS)
        search_params[&#39;searchParams&#39;].append(
            {key: &#34;wt&#34;, &#34;value&#34;: &#34;json&#34;})
        search_params[&#39;searchParams&#39;].append(
            {key: &#34;defType&#34;, &#34;value&#34;: &#34;edismax&#34;})
        search_params[&#39;searchParams&#39;].append({key: &#34;q&#34;, &#34;value&#34;: query})
        if criteria:
            fq_dict = {
                key: &#34;fq&#34;,
                &#34;value&#34;: criteria
            }
            search_params[&#39;searchParams&#39;].append(fq_dict)
        if attr_list:
            if is_separate_attr:
                for attr in attr_list:
                    fl_dict = {
                        key: &#34;fl&#34;,
                        &#34;value&#34;: attr
                    }
                    search_params[&#39;searchParams&#39;].append(fl_dict)
            else:
                fl_list = &#39;,&#39;.join(attr_list)
                fl_dict = {
                    key: &#34;fl&#34;,
                    &#34;value&#34;: fl_list
                }
                search_params[&#39;searchParams&#39;].append(fl_dict)
        if params:
            for dkey, value in params.items():
                custom_dict = {
                    key: str(dkey),
                    &#34;value&#34;: str(value)
                }
                search_params[&#39;searchParams&#39;].append(custom_dict)
        return search_params

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _do_stream_download(self, guid, file_name, download_location):
        &#34;&#34;&#34;does stream download to file to local machine

                Args:

                    guid                (str)       --  Download GUID

                    download_location   (str)       --  path on local machine to download requested file

                    file_name           (str)       --  File name for download

                Returns:

                    Str     --  File path containing downloaded file

                Raise:

                    SDKException:

                        if failed to do stream download


        &#34;&#34;&#34;
        request = copy.deepcopy(EdiscoveryConstants.EXPORT_DOWNLOAD_REQ)
        request[&#39;responseFileName&#39;] = file_name
        for param in request[&#39;fileParams&#39;]:
            if param[&#39;id&#39;] == 2:
                param[&#39;name&#39;] = guid
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;DOWNLOAD_PACKAGE&#39;], request
        )

        if flag:
            error_list = response.json().get(&#39;errList&#39;)
            file_content = response.json().get(&#39;fileContent&#39;, {})

            if error_list:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Error: {0}&#39;.format(error_list))

            file_name = file_content.get(&#39;fileName&#39;, file_name)
            request_id = file_content[&#39;requestId&#39;]

            # full path of the file on local machine to be downloaded
            download_path = os.path.join(download_location, file_name)

            # execute request to get the stream of content
            # using request id returned in the previous response
            request[&#39;requestId&#39;] = request_id
            flag1, response1 = self._cvpysdk_object.make_request(
                &#39;POST&#39;,
                self._services[&#39;DOWNLOAD_VIA_STREAM&#39;],
                request,
                stream=True
            )

            # download chunks of 1MB each
            chunk_size = 1024 ** 2

            if flag1:
                with open(download_path, &#34;wb&#34;) as file_pointer:
                    for content in response1.iter_content(chunk_size=chunk_size):
                        file_pointer.write(content)
            else:
                self._response_not_success(response1)
        else:
            self._response_not_success(response)

        return download_path

    def get_handler_id(self, handler_name=&#34;default&#34;):
        &#34;&#34;&#34;returns the id of given handler name

                Args:

                    handler_name            (str)       --  Handler name(Default: default)

                Returns:

                    int --  Handler id

                Raises:

                    SDKException:

                        if failed to find handler

                        if response is empty

        &#34;&#34;&#34;
        if not isinstance(self._class_obj, EdiscoveryDatasource):
            return 0
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_GET_DEFAULT_HANDLER % self._data_source_id)
        if flag:
            if response.json() and &#39;handlerInfos&#39; in response.json():
                handler_list = response.json()[&#39;handlerInfos&#39;]
                if not isinstance(handler_list, list):
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Failed to get Datasource/Handler details&#34;)
                for handler in handler_list:
                    if handler[&#39;handlerName&#39;].lower() == handler_name.lower():
                        return handler[&#39;handlerId&#39;]
                else:
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;No Handler found with given name&#39;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Unknown response while fetching datasource details&#39;)
        self._response_not_success(response)

    def export(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do export to CSV on data

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - Exports all docs)

                                                    Example :

                                                        1) Size filter --&gt; Size:[10 TO 1024]
                                                        2) File name filter --&gt; FileName_idx:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                str     --  export operation token


            Raises:

                SDKException:

                        if failed to perform export

        &#34;&#34;&#34;
        if criteria and not isinstance(criteria, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        search_params = self.form_search_params(criteria=criteria, attr_list=attr_list,
                                                params=params)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_EXPORT % self.get_handler_id(), search_params
        )
        if flag:
            if response.json():
                response = response.json()
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Export failed with error : {response.get(&#39;errLogMessage&#39;,&#39;&#39;)}&#34;)
                elif &#39;customMap&#39; in response and &#39;name&#39; in response[&#39;customMap&#39;]:
                    return response[&#39;customMap&#39;][&#39;name&#39;]
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Failed to search with response - {response.json()}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;113&#39;)
        self._response_not_success(response)

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on data source and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        1) Size filter --&gt; Size:[10 TO 1024]
                                                        2) File name filter --&gt; FileName_idx:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                int,list(dict),dict    --  Containing document count, document  details &amp; facet/stats details(if any)


            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        if criteria and not isinstance(criteria, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        search_params = self.form_search_params(criteria=criteria, attr_list=attr_list,
                                                params=params)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_SEARCH % (self._search_entity_type, self._search_entity_id), search_params
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to perform search - {response.json().get(&#39;errLogMessage&#39;,&#39;&#39;)}&#34;)
                if &#39;response&#39; in response.json() and &#39;docs&#39; in response.json()[&#39;response&#39;]:
                    if &#39;facets&#39; in response.json():
                        return response.json()[&#39;response&#39;][&#39;numFound&#39;], response.json()[
                            &#39;response&#39;][&#39;docs&#39;], response.json()[&#39;facets&#39;]
                    elif &#39;stats&#39; in response.json():
                        return response.json()[&#39;response&#39;][&#39;numFound&#39;], response.json()[
                            &#39;response&#39;][&#39;docs&#39;], response.json()[&#39;stats&#39;]
                    else:
                        return response.json()[&#39;response&#39;][&#39;numFound&#39;], response.json()[&#39;response&#39;][&#39;docs&#39;], {}
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Failed to search with response - {response.json()}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;112&#39;)
        self._response_not_success(response)

    def _get_associations(self):
        &#34;&#34;&#34;returns the associations for this client

            Args:

                None

            Returns:

                Dict    --  Containing associations details

            Raises:

                SDKException:

                    if failed to find association details

        &#34;&#34;&#34;
        # if called from EdiscoveryDatasource, then no association check needed as sharing is not possible at this level
        from ..activateapps.file_storage_optimization import FsoServerGroup
        from ..activateapps.request_manager import Request
        if isinstance(
                self._class_obj,
                Request) or isinstance(
                self._class_obj,
                EdiscoveryDatasource) or isinstance(
                self._class_obj,
                FsoServerGroup):
            return {}
        association_request_json = copy.deepcopy(EdiscoveryConstants.SHARE_REQUEST_JSON)
        del association_request_json[&#39;securityAssociations&#39;]
        association_request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;clientId&#39;] = int(self._client_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_SECURITY_ENTITY %
            (self._client_entity_type, int(
                self._client_id)), association_request_json)
        if flag:
            if response.json() and &#39;securityAssociations&#39; in response.json():
                security = response.json()[&#39;securityAssociations&#39;][0][&#39;securityAssociations&#39;]
                return security.get(&#39;associations&#39;, {})
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Failed to get existing security associations&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares ediscovery client with given user or user group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        if not isinstance(user_or_group_name, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(EdiscoveryConstants.SHARE_REQUEST_JSON)
        external_user = False
        association_response = None
        if ops_type == 1:
            association_response = self._associations

        if &#39;\\&#39; in user_or_group_name:
            external_user = True
        if is_user:
            user_obj = self._commcell_object.users.get(user_or_group_name)
            user_id = user_obj.user_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userId&#39;] = int(user_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 13
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userName&#39;] = user_or_group_name
        elif external_user:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;groupId&#39;] = 0
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 62
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
                &#39;externalGroupName&#39;] = user_or_group_name
        else:
            grp_obj = self._commcell_object.user_groups.get(user_or_group_name)
            grp_id = grp_obj.user_group_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupId&#39;] = int(grp_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 15
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
                &#39;userGroupName&#39;] = user_or_group_name

        request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;clientId&#39;] = int(self._client_id)
        request_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = ops_type

        if allow_edit_permission:
            # we need to send separate association for each permission
            association_json = copy.deepcopy(request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0])
            # do copy, remove permission and add Edit
            del association_json[&#39;properties&#39;][&#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;][0]
            association_json[&#39;properties&#39;][&#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;].append(
                EdiscoveryConstants.EDIT_CATEGORY_PERMISSION)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;].append(association_json)

            # Associate existing associations to the request
        if ops_type == 1:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;].extend(association_response)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_SECURITY % self._app_type, request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response_json = response.json()[&#39;response&#39;]
                for node in response_json:
                    if &#39;errorCode&#39; in node:
                        error_code = node[&#39;errorCode&#39;]
                        if error_code != 0:
                            error_message = node.get(&#39;warningMessage&#39;, &#34;Something went wrong&#34;)
                            raise SDKException(
                                &#39;EdiscoveryClients&#39;,
                                &#39;102&#39;, error_message)
                self.refresh()
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;109&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_ediscovery_project_details(self):
        &#34;&#34;&#34;returns the ediscovery project details

                Args:

                    None

                Returns:

                    dict        -- Containing project details

                Raises;

                    SDKException:

                            if failed to get project details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        api = self._API_GET_EDISCOVERY_CLIENT_DETAILS_V1 % self._client_id
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        if flag:
            if response.json() and &#39;eDiscoveryClientProp&#39; in response.json():
                project = response.json()[&#39;eDiscoveryClientProp&#39;][0]
                return project[&#39;eDiscoveryClientInfo&#39;]
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;118&#39;)
        self._response_not_success(response)

    def get_ediscovery_client_details(self):
        &#34;&#34;&#34;returns the ediscovery client details for this client

                Args:

                    None

                Returns:

                    dict        -- Containing client details

                Raises;

                    SDKException:

                            if failed to get client details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_CLIENT_DETAILS % (
            self._client_id, self._include_doc_count, self._limit, self._offset,
            self._sort_by, self._sort_dir, self._ds_type_names))
        if flag:
            if response.json() and &#39;nodeList&#39; in response.json():
                return response.json()[&#39;nodeList&#39;][0] if len(response.json()[&#39;nodeList&#39;]) &gt; 0 else {}
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def start_job(self, wait_for_job=False, wait_time=60, is_incr=True):
        &#34;&#34;&#34;Starts job on ediscovery client

            Args:

                    wait_for_job        (bool)       --  specifies whether to wait for job to complete or not

                    wait_time           (int)        --  time interval to wait for job completion in Mins
                                                            Default : 60Mins

                    is_incr             (bool)       -- Specifies whether this is incremental or full crawl job

             Return:

                    None

            Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        if not is_incr:
            self._operation = 3  # full crawl job
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_CRAWL % (self._client_id, self._data_source_id, self._type, self._operation)
        )
        if flag:
            if response.json():
                response_json = response.json()
                if &#39;errorCode&#39; in response_json:
                    error_code = response_json[&#39;errorCode&#39;]
                    if error_code != 0:
                        error_message = response_json[&#39;errorMessage&#39;]
                        raise SDKException(
                            &#39;EdiscoveryClients&#39;,
                            &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;103&#39;)
            if not wait_for_job:
                return
            return self.wait_for_collection_job(wait_time=wait_time)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def wait_for_export(self, token, wait_time=60, download=True, download_location=os.getcwd()):
        &#34;&#34;&#34;Waits for Export to CSV to finish

                Args:

                    wait_time           (int)       --  time interval to wait for job completion in Mins
                                                            Default : 60Mins

                    token               (str)       --  Export to CSV token GUID

                    download            (bool)      --  specify whether to download exported file or not

                    download_location   (str)       --  Path where to download exported csv file
                                                                Default: Current working dir

                Return:

                    str     -- Download GUID for exported CSV file if download=false
                               File path containing exported csv file if download=true

                Raises:

                    SDKException:

                            if Export status check fails

                            if timeout happens

        &#34;&#34;&#34;
        timeout = time.time() + 60 * wait_time  # 1hr
        handler_id = self.get_handler_id()
        while True:
            if time.time() &gt; timeout:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Export job Timeout&#34;)
            flag, response = self._cvpysdk_object.make_request(
                &#39;GET&#39;, self._API_EXPORT_STATUS % (handler_id, token)
            )
            if flag:
                if response.json():
                    response = response.json()
                    if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                        raise SDKException(
                            &#39;EdiscoveryClients&#39;,
                            &#39;102&#39;,
                            f&#34;Export status check failed with error : {response.get(&#39;errLogMessage&#39;, &#39;&#39;)}&#34;)
                    elif &#39;customMap&#39; in response and response[&#39;customMap&#39;].get(&#39;name&#39;, &#39;&#39;) == &#39;statusObject&#39;:
                        value_json = json.loads(response[&#39;customMap&#39;][&#39;value&#39;])
                        if &#39;response&#39; in value_json and isinstance(
                                value_json[&#39;response&#39;],
                                dict) and value_json[&#39;response&#39;].get(
                                &#39;status&#39;,
                                &#39;&#39;) == &#39;finished&#39;:
                            if not download:
                                return value_json[&#39;response&#39;].get(&#39;downloadGuid&#39;, &#39;&#39;)
                            else:
                                return self._do_stream_download(guid=value_json[&#39;response&#39;].get(&#39;downloadGuid&#39;, &#39;&#39;),
                                                                file_name=f&#34;Cvpysdk_Activate_export_{int(time.time())}&#34;,
                                                                download_location=download_location)
                    else:
                        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;,
                                           f&#34;Failed to check export status with response - {response.json()}&#34;)
                else:
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;114&#39;)
            else:
                self._response_not_success(response)
            time.sleep(10)

    def wait_for_collection_job(self, wait_time=60):
        &#34;&#34;&#34;Waits for collection job to finish

                Args:

                    wait_time           (int)       --  time interval to wait for job completion in Mins
                                                            Default : 60Mins

                Return:

                    None

                Raises:

                    SDKException:

                            if collection job fails

                            if timeout happens

        &#34;&#34;&#34;
        timeout = time.time() + 60 * wait_time  # 1hr
        while True:
            if time.time() &gt; timeout:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Collection job Timeout&#34;)
            status = self.get_job_status()
            if int(status[&#39;state&#39;]) == InventoryConstants.CRAWL_JOB_COMPLETE_STATE:  # Finished State
                return
            elif int(status[&#39;state&#39;]) == InventoryConstants.CRAWL_JOB_COMPLETE_ERROR_STATE:  # completed with error
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Job status is marked as Completed with Error&#34;)
            # STOPPING,STOPPED,ABORTING, ABORTED,EXCEPTION,UNKNOWN,SYNCING,PENDING
            elif int(status[&#39;state&#39;]) in InventoryConstants.CRAWL_JOB_FAILED_STATE:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Job status is marked as Failed/Error/Pending&#34;)
            else:
                time.sleep(10)

    def get_job_history(self):
        &#34;&#34;&#34;Returns the job history details of ediscovery client

                Args:
                    None

                Returns:

                    list(dict)    --  containing job history details

                Raises:

                    SDKException:

                            if failed to get job history

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_JOBS_HISTORY % (self._client_id, self._type, self._data_source_id)
        )
        if flag:
            if response.json() and &#39;status&#39; in response.json():
                return response.json()[&#39;status&#39;]
            elif &#39;error&#39; in response.json():
                error = response.json()[&#39;error&#39;]
                error_code = error[&#39;errorCode&#39;]
                if error_code != 0:
                    error_message = error[&#39;errLogMessage&#39;]
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;, error_message)
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Something went wrong while fetching job history&#34;)
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;104&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_job_status(self):
        &#34;&#34;&#34;Returns the job status details of this asset

                Args:
                    None

                Returns:

                    dict    --  containing job status details

                Raises:

                    SDKException:

                            if failed to get job status

        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_JOB_STATUS % (self._client_id, self._type, self._data_source_id)
        )
        if flag:
            if response.json() and &#39;status&#39; in response.json():
                return response.json()[&#39;status&#39;]
            elif &#39;error&#39; in response.json():
                error = response.json()[&#39;error&#39;]
                error_code = error[&#39;errorCode&#39;]
                if error_code != 0:
                    error_message = error[&#39;errLogMessage&#39;]
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;, error_message)
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Something went wrong while fetching job status&#34;)
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;105&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def do_document_task(self, comment, doc_id=None, ds_id=None, consent=True, redact=False):
        &#34;&#34;&#34;does document update for consent/comment

            Args:

                doc_id          (str)       --  Document id (Mandatory in case of SDG)

                comment         (str)       --  User comment

                ds_id           (int)       --  Data SourceId (Mandatory in case of SDG)

                consent         (bool)      --  Accept or Decline (Default:True)

                redact          (bool)      --  Redact ON or OFF (only in case of export)
                                                            (Default:False)

            Returns:

                None

            Raises:

                SDKException:

                    if failed to update document

                    if input is not valid
        &#34;&#34;&#34;
        if not self._request_type or not self._request_app or not self._request_review_set_id:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Request type not set correctly&#34;)
        if self._request_app == TargetApps.SDG.name:
            if not doc_id:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    &#34;Document id is mandatory for request from SDG app&#34;)
            if not ds_id:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    &#34;DataSource id is mandatory for request from SDG app&#34;)
        if self._request_app == TargetApps.FSO.name:
            self._request_review_set_id = f&#34;FSO_{self._request_review_set_id}&#34;
        req_json = {
            &#34;nameValues&#34;: [
                {
                    &#34;name&#34;: f&#34;ConsentFor_{self._request_review_set_id}_b&#34;,
                    &#34;value&#34;: f&#34;{consent}&#34;
                },

                {
                    &#34;name&#34;: f&#34;CommentFor_{self._request_review_set_id}&#34;,
                    &#34;value&#34;: comment
                }
            ]
        }

        if self._request_app == TargetApps.SDG.name:
            req_json[&#39;nameValues&#39;].append({
                &#34;name&#34;: &#34;q&#34;,
                &#34;value&#34;: f&#34;contentid:{doc_id}&#34;
            })
            req_json[&#39;nameValues&#39;].append({
                &#34;name&#34;: &#34;datasourceId&#34;,
                &#34;value&#34;: f&#34;{ds_id}&#34;
            })
        elif self._request_app == TargetApps.FSO.name:
            req_json[&#39;nameValues&#39;].append({
                &#34;name&#34;: &#34;fq&#34;,
                &#34;value&#34;: f&#34;contentid:* AND -(ConsentFor_{self._request_review_set_id}_b:*)&#34;
            })
        if self._request_type == RequestConstants.RequestType.EXPORT.value:
            req_json[&#39;nameValues&#39;].append({
                &#34;name&#34;: f&#34;RedactMode_{self._request_review_set_id}_b&#34;,
                &#34;value&#34;: f&#34;{redact}&#34;
            })

        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._API_DOC_TASK % (self._client_id), req_json
        )
        if flag:
            if not response.json():
                return
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Something went wrong while doing document task operation - {response.json()[&#39;errorMessage&#39;]}&#34;)
            else:
                return
        self._response_not_success(response)

    def task_workflow_operation(self):
        &#34;&#34;&#34;calls workflow operation for task

                Args:
                    None

                Returns:

                    str --  Workflow job id

                Raises:

                    SDKException:

                        if failed to call task workflow
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_TASK_WORKFLOW % self._client_id)
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Something wrong while invoking task workflow operation - {response.json()[&#39;errorMessage&#39;]}&#34;)
                if &#39;jobId&#39; in response.json():
                    return response.json()[&#39;jobId&#39;]
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Workflow task failed&#34;)
        self._response_not_success(response)

    def configure_task(self, task_props):
        &#34;&#34;&#34;configures task for this edsicovery client

            Args:

                task_props      list(dict)      --  Task properties

            Returns:

                None

            Raises:

                SDKException:

                    if input is not valid

                    if failed to configure task
        &#34;&#34;&#34;
        if not isinstance(task_props, list):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        req_json = {
            &#34;taskReq&#34;: {
                &#34;tasks&#34;: [
                    {
                        &#34;taskInfo&#34;: {
                            &#34;taskId&#34;: self._client_id
                        },
                        &#34;taskProps&#34;: task_props
                    }
                ]
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CONFIGURE_TASK, req_json
        )
        if flag:
            if response.json() and &#39;msg&#39; in response.json():
                msg = response.json()[&#39;msg&#39;]
                if &#39;errorCode&#39; in msg and msg[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Something went wrong while configuring task operation - {msg[&#39;errorMessage&#39;]}&#34;)
                return
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Configure task failed&#34;)
        self._response_not_success(response)

    @property
    def associations(self):
        &#34;&#34;&#34;returns association blob for this client

            Returns:

                dict --  containing security association blob details

        &#34;&#34;&#34;
        return self._associations</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.associations"><code class="name">var <span class="ident">associations</span></code></dt>
<dd>
<div class="desc"><p>returns association blob for this client</p>
<h2 id="returns">Returns</h2>
<p>dict &ndash;
containing security association blob details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1598-L1607" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associations(self):
    &#34;&#34;&#34;returns association blob for this client

        Returns:

            dict --  containing security association blob details

    &#34;&#34;&#34;
    return self._associations</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.configure_task"><code class="name flex">
<span>def <span class="ident">configure_task</span></span>(<span>self, task_props)</span>
</code></dt>
<dd>
<div class="desc"><p>configures task for this edsicovery client</p>
<h2 id="args">Args</h2>
<p>task_props
list(dict)
&ndash;
Task properties</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if input is not valid

if failed to configure task
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1550-L1596" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_task(self, task_props):
    &#34;&#34;&#34;configures task for this edsicovery client

        Args:

            task_props      list(dict)      --  Task properties

        Returns:

            None

        Raises:

            SDKException:

                if input is not valid

                if failed to configure task
    &#34;&#34;&#34;
    if not isinstance(task_props, list):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    req_json = {
        &#34;taskReq&#34;: {
            &#34;tasks&#34;: [
                {
                    &#34;taskInfo&#34;: {
                        &#34;taskId&#34;: self._client_id
                    },
                    &#34;taskProps&#34;: task_props
                }
            ]
        }
    }
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_CONFIGURE_TASK, req_json
    )
    if flag:
        if response.json() and &#39;msg&#39; in response.json():
            msg = response.json()[&#39;msg&#39;]
            if &#39;errorCode&#39; in msg and msg[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Something went wrong while configuring task operation - {msg[&#39;errorMessage&#39;]}&#34;)
            return
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Configure task failed&#34;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.do_document_task"><code class="name flex">
<span>def <span class="ident">do_document_task</span></span>(<span>self, comment, doc_id=None, ds_id=None, consent=True, redact=False)</span>
</code></dt>
<dd>
<div class="desc"><p>does document update for consent/comment</p>
<h2 id="args">Args</h2>
<p>doc_id
(str)
&ndash;
Document id (Mandatory in case of SDG)</p>
<p>comment
(str)
&ndash;
User comment</p>
<p>ds_id
(int)
&ndash;
Data SourceId (Mandatory in case of SDG)</p>
<p>consent
(bool)
&ndash;
Accept or Decline (Default:True)</p>
<p>redact
(bool)
&ndash;
Redact ON or OFF (only in case of export)
(Default:False)</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to update document

if input is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1428-L1518" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def do_document_task(self, comment, doc_id=None, ds_id=None, consent=True, redact=False):
    &#34;&#34;&#34;does document update for consent/comment

        Args:

            doc_id          (str)       --  Document id (Mandatory in case of SDG)

            comment         (str)       --  User comment

            ds_id           (int)       --  Data SourceId (Mandatory in case of SDG)

            consent         (bool)      --  Accept or Decline (Default:True)

            redact          (bool)      --  Redact ON or OFF (only in case of export)
                                                        (Default:False)

        Returns:

            None

        Raises:

            SDKException:

                if failed to update document

                if input is not valid
    &#34;&#34;&#34;
    if not self._request_type or not self._request_app or not self._request_review_set_id:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Request type not set correctly&#34;)
    if self._request_app == TargetApps.SDG.name:
        if not doc_id:
            raise SDKException(
                &#39;EdiscoveryClients&#39;,
                &#39;102&#39;,
                &#34;Document id is mandatory for request from SDG app&#34;)
        if not ds_id:
            raise SDKException(
                &#39;EdiscoveryClients&#39;,
                &#39;102&#39;,
                &#34;DataSource id is mandatory for request from SDG app&#34;)
    if self._request_app == TargetApps.FSO.name:
        self._request_review_set_id = f&#34;FSO_{self._request_review_set_id}&#34;
    req_json = {
        &#34;nameValues&#34;: [
            {
                &#34;name&#34;: f&#34;ConsentFor_{self._request_review_set_id}_b&#34;,
                &#34;value&#34;: f&#34;{consent}&#34;
            },

            {
                &#34;name&#34;: f&#34;CommentFor_{self._request_review_set_id}&#34;,
                &#34;value&#34;: comment
            }
        ]
    }

    if self._request_app == TargetApps.SDG.name:
        req_json[&#39;nameValues&#39;].append({
            &#34;name&#34;: &#34;q&#34;,
            &#34;value&#34;: f&#34;contentid:{doc_id}&#34;
        })
        req_json[&#39;nameValues&#39;].append({
            &#34;name&#34;: &#34;datasourceId&#34;,
            &#34;value&#34;: f&#34;{ds_id}&#34;
        })
    elif self._request_app == TargetApps.FSO.name:
        req_json[&#39;nameValues&#39;].append({
            &#34;name&#34;: &#34;fq&#34;,
            &#34;value&#34;: f&#34;contentid:* AND -(ConsentFor_{self._request_review_set_id}_b:*)&#34;
        })
    if self._request_type == RequestConstants.RequestType.EXPORT.value:
        req_json[&#39;nameValues&#39;].append({
            &#34;name&#34;: f&#34;RedactMode_{self._request_review_set_id}_b&#34;,
            &#34;value&#34;: f&#34;{redact}&#34;
        })

    flag, response = self._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._API_DOC_TASK % (self._client_id), req_json
    )
    if flag:
        if not response.json():
            return
        if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
            raise SDKException(
                &#39;EdiscoveryClients&#39;,
                &#39;102&#39;,
                f&#34;Something went wrong while doing document task operation - {response.json()[&#39;errorMessage&#39;]}&#34;)
        else:
            return
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.export"><code class="name flex">
<span>def <span class="ident">export</span></span>(<span>self, criteria=None, attr_list=None, params=None)</span>
</code></dt>
<dd>
<div class="desc"><p>do export to CSV on data</p>
<h2 id="args">Args</h2>
<p>criteria
(str)
&ndash;
containing criteria for query
(Default : None - Exports all docs)</p>
<pre><code>                                Example :

                                    1) Size filter --&gt; Size:[10 TO 1024]
                                    2) File name filter --&gt; FileName_idx:09_23*
</code></pre>
<p>attr_list
(set)
&ndash;
Column names to be returned in results.
Acts as 'fl' in query</p>
<p>params
(dict)
&ndash;
Any other params which needs to be passed
Example : { "start" : "0" }</p>
<h2 id="returns">Returns</h2>
<p>str
&ndash;
export operation token</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to perform export
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L887-L937" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def export(self, criteria=None, attr_list=None, params=None):
    &#34;&#34;&#34;do export to CSV on data

        Args:

            criteria        (str)      --  containing criteria for query
                                                (Default : None - Exports all docs)

                                                Example :

                                                    1) Size filter --&gt; Size:[10 TO 1024]
                                                    2) File name filter --&gt; FileName_idx:09_23*

            attr_list       (set)      --  Column names to be returned in results.
                                                 Acts as &#39;fl&#39; in query

            params          (dict)     --  Any other params which needs to be passed
                                               Example : { &#34;start&#34; : &#34;0&#34; }

        Returns:

            str     --  export operation token


        Raises:

            SDKException:

                    if failed to perform export

    &#34;&#34;&#34;
    if criteria and not isinstance(criteria, str):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    search_params = self.form_search_params(criteria=criteria, attr_list=attr_list,
                                            params=params)
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_EXPORT % self.get_handler_id(), search_params
    )
    if flag:
        if response.json():
            response = response.json()
            if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Export failed with error : {response.get(&#39;errLogMessage&#39;,&#39;&#39;)}&#34;)
            elif &#39;customMap&#39; in response and &#39;name&#39; in response[&#39;customMap&#39;]:
                return response[&#39;customMap&#39;][&#39;name&#39;]
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Failed to search with response - {response.json()}&#34;)
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;113&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.form_search_params"><code class="name flex">
<span>def <span class="ident">form_search_params</span></span>(<span>self, criteria=None, attr_list=None, params=None, query='*:*', key='key', is_separate_attr=False)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the search params dict based on input</p>
<h2 id="args">Args</h2>
<p>criteria
(str)
&ndash;
containing criteria for query</p>
<pre><code>                                Example :

                                    Size:[10 TO 1024]
                                    FileName:09_23*
</code></pre>
<p>attr_list
(set)
&ndash;
Column names to be returned in results.
Acts as 'fl' in query</p>
<p>params
(dict)
&ndash;
Any other params which needs to be passed
Example : { "start" : "0" }</p>
<p>query
(str)
&ndash;
query to be performed (acts as q param in query)
default:None (Means <em>:</em>)</p>
<p>key
(str)
&ndash;
key name to be used in request (default:key)</p>
<p>is_separate_attr (bool)
&ndash;
specifies whether attribute list needs to formed as separate key-value</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash;
Containing searchparams details</p>
<dl>
<dt><code>Example </code></dt>
<dd>{
"searchParams": [
{
"key": "wt",
"value": "json"
},
{
"key": "defType",
"value": "edismax"
},
{
"key": "q",
"value": "<em>:</em>"
},
{
"key": "fq",
"value": "(contentid:949c3b53ce4dd72a82b8e67039eeddef)"
},
{
"key": "fl",
"value": "contentid,CreatedTime,Url,ClientId"
}
]
}</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L673-L768" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def form_search_params(
        self,
        criteria=None,
        attr_list=None,
        params=None,
        query=&#34;*:*&#34;,
        key=&#34;key&#34;,
        is_separate_attr=False):
    &#34;&#34;&#34;returns the search params dict based on input

        Args:

            criteria        (str)      --  containing criteria for query

                                                Example :

                                                    Size:[10 TO 1024]
                                                    FileName:09_23*

            attr_list       (set)      --  Column names to be returned in results.
                                                 Acts as &#39;fl&#39; in query

            params          (dict)     --  Any other params which needs to be passed
                                               Example : { &#34;start&#34; : &#34;0&#34; }

            query           (str)      --   query to be performed (acts as q param in query)
                                                default:None (Means *:*)

            key             (str)      --   key name to be used in request (default:key)

            is_separate_attr (bool)    --   specifies whether attribute list needs to formed as separate key-value

        Returns:

            dict        --  Containing searchparams details

            Example : {
                          &#34;searchParams&#34;: [
                            {
                              &#34;key&#34;: &#34;wt&#34;,
                              &#34;value&#34;: &#34;json&#34;
                            },
                            {
                              &#34;key&#34;: &#34;defType&#34;,
                              &#34;value&#34;: &#34;edismax&#34;
                            },
                            {
                              &#34;key&#34;: &#34;q&#34;,
                              &#34;value&#34;: &#34;*:*&#34;
                            },
                            {
                              &#34;key&#34;: &#34;fq&#34;,
                              &#34;value&#34;: &#34;(contentid:949c3b53ce4dd72a82b8e67039eeddef)&#34;
                            },
                            {
                              &#34;key&#34;: &#34;fl&#34;,
                              &#34;value&#34;: &#34;contentid,CreatedTime,Url,ClientId&#34;
                            }
                          ]
                        }
    &#34;&#34;&#34;
    search_params = copy.deepcopy(EdiscoveryConstants.DYNAMIC_FEDERATED_SEARCH_PARAMS)
    search_params[&#39;searchParams&#39;].append(
        {key: &#34;wt&#34;, &#34;value&#34;: &#34;json&#34;})
    search_params[&#39;searchParams&#39;].append(
        {key: &#34;defType&#34;, &#34;value&#34;: &#34;edismax&#34;})
    search_params[&#39;searchParams&#39;].append({key: &#34;q&#34;, &#34;value&#34;: query})
    if criteria:
        fq_dict = {
            key: &#34;fq&#34;,
            &#34;value&#34;: criteria
        }
        search_params[&#39;searchParams&#39;].append(fq_dict)
    if attr_list:
        if is_separate_attr:
            for attr in attr_list:
                fl_dict = {
                    key: &#34;fl&#34;,
                    &#34;value&#34;: attr
                }
                search_params[&#39;searchParams&#39;].append(fl_dict)
        else:
            fl_list = &#39;,&#39;.join(attr_list)
            fl_dict = {
                key: &#34;fl&#34;,
                &#34;value&#34;: fl_list
            }
            search_params[&#39;searchParams&#39;].append(fl_dict)
    if params:
        for dkey, value in params.items():
            custom_dict = {
                key: str(dkey),
                &#34;value&#34;: str(value)
            }
            search_params[&#39;searchParams&#39;].append(custom_dict)
    return search_params</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_ediscovery_client_details"><code class="name flex">
<span>def <span class="ident">get_ediscovery_client_details</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the ediscovery client details for this client</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash; Containing client details
Raises;</p>
<pre><code>SDKException:

        if failed to get client details

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1171-L1200" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_ediscovery_client_details(self):
    &#34;&#34;&#34;returns the ediscovery client details for this client

            Args:

                None

            Returns:

                dict        -- Containing client details

            Raises;

                SDKException:

                        if failed to get client details

                        if response is empty

                        if response is not success

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_CLIENT_DETAILS % (
        self._client_id, self._include_doc_count, self._limit, self._offset,
        self._sort_by, self._sort_dir, self._ds_type_names))
    if flag:
        if response.json() and &#39;nodeList&#39; in response.json():
            return response.json()[&#39;nodeList&#39;][0] if len(response.json()[&#39;nodeList&#39;]) &gt; 0 else {}
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;106&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_ediscovery_project_details"><code class="name flex">
<span>def <span class="ident">get_ediscovery_project_details</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the ediscovery project details</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash; Containing project details
Raises;</p>
<pre><code>SDKException:

        if failed to get project details

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1140-L1169" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_ediscovery_project_details(self):
    &#34;&#34;&#34;returns the ediscovery project details

            Args:

                None

            Returns:

                dict        -- Containing project details

            Raises;

                SDKException:

                        if failed to get project details

                        if response is empty

                        if response is not success

    &#34;&#34;&#34;
    api = self._API_GET_EDISCOVERY_CLIENT_DETAILS_V1 % self._client_id
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
    if flag:
        if response.json() and &#39;eDiscoveryClientProp&#39; in response.json():
            project = response.json()[&#39;eDiscoveryClientProp&#39;][0]
            return project[&#39;eDiscoveryClientInfo&#39;]
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;118&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_handler_id"><code class="name flex">
<span>def <span class="ident">get_handler_id</span></span>(<span>self, handler_name='default')</span>
</code></dt>
<dd>
<div class="desc"><p>returns the id of given handler name</p>
<h2 id="args">Args</h2>
<p>handler_name
(str)
&ndash;
Handler name(Default: default)</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Handler id</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to find handler

if response is empty
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L850-L885" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_handler_id(self, handler_name=&#34;default&#34;):
    &#34;&#34;&#34;returns the id of given handler name

            Args:

                handler_name            (str)       --  Handler name(Default: default)

            Returns:

                int --  Handler id

            Raises:

                SDKException:

                    if failed to find handler

                    if response is empty

    &#34;&#34;&#34;
    if not isinstance(self._class_obj, EdiscoveryDatasource):
        return 0
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self._API_GET_DEFAULT_HANDLER % self._data_source_id)
    if flag:
        if response.json() and &#39;handlerInfos&#39; in response.json():
            handler_list = response.json()[&#39;handlerInfos&#39;]
            if not isinstance(handler_list, list):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Failed to get Datasource/Handler details&#34;)
            for handler in handler_list:
                if handler[&#39;handlerName&#39;].lower() == handler_name.lower():
                    return handler[&#39;handlerId&#39;]
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;No Handler found with given name&#39;)
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Unknown response while fetching datasource details&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_job_history"><code class="name flex">
<span>def <span class="ident">get_job_history</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the job history details of ediscovery client</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>list(dict)
&ndash;
containing job history details</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to get job history
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1351-L1387" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_job_history(self):
    &#34;&#34;&#34;Returns the job history details of ediscovery client

            Args:
                None

            Returns:

                list(dict)    --  containing job history details

            Raises:

                SDKException:

                        if failed to get job history

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self._API_JOBS_HISTORY % (self._client_id, self._type, self._data_source_id)
    )
    if flag:
        if response.json() and &#39;status&#39; in response.json():
            return response.json()[&#39;status&#39;]
        elif &#39;error&#39; in response.json():
            error = response.json()[&#39;error&#39;]
            error_code = error[&#39;errorCode&#39;]
            if error_code != 0:
                error_message = error[&#39;errLogMessage&#39;]
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;, error_message)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Something went wrong while fetching job history&#34;)
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;104&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_job_status"><code class="name flex">
<span>def <span class="ident">get_job_status</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the job status details of this asset</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash;
containing job status details</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to get job status
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1389-L1426" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_job_status(self):
    &#34;&#34;&#34;Returns the job status details of this asset

            Args:
                None

            Returns:

                dict    --  containing job status details

            Raises:

                SDKException:

                        if failed to get job status

    &#34;&#34;&#34;

    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self._API_JOB_STATUS % (self._client_id, self._type, self._data_source_id)
    )
    if flag:
        if response.json() and &#39;status&#39; in response.json():
            return response.json()[&#39;status&#39;]
        elif &#39;error&#39; in response.json():
            error = response.json()[&#39;error&#39;]
            error_code = error[&#39;errorCode&#39;]
            if error_code != 0:
                error_message = error[&#39;errLogMessage&#39;]
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;, error_message)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Something went wrong while fetching job status&#34;)
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;105&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>refresh ediscovery client properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L601-L603" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;refresh ediscovery client properties&#34;&#34;&#34;
    self._associations = self._get_associations()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.schedule"><code class="name flex">
<span>def <span class="ident">schedule</span></span>(<span>self, schedule_name, pattern_json, ops_type=2)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates or modifies the schedule associated with ediscovery client</p>
<h2 id="args">Args</h2>
<p>schedule_name
(str)
&ndash;
Schedule name</p>
<p>pattern_json
(dict)
&ndash;
Schedule pattern dict
(Refer to Create_schedule_pattern in schedule.py)</p>
<p>ops_type
(int)
&ndash;
Operation type</p>
<pre><code>                                    Default : 2 (Add)

                                    Supported : 2 (Add/Modify)
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if input is not valid

  if failed to create/modify schedule
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L605-L671" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def schedule(self, schedule_name, pattern_json, ops_type=2):
    &#34;&#34;&#34;Creates or modifies the schedule associated with ediscovery client

            Args:

                schedule_name       (str)       --  Schedule name

                pattern_json        (dict)      --  Schedule pattern dict
                                                    (Refer to Create_schedule_pattern in schedule.py)

                ops_type            (int)       --  Operation type

                                                        Default : 2 (Add)

                                                        Supported : 2 (Add/Modify)

            Raises:

                  SDKException:

                        if input is not valid

                        if failed to create/modify schedule

    &#34;&#34;&#34;
    if not isinstance(schedule_name, str) or not isinstance(pattern_json, dict):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    if ops_type not in [2]:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Schedule operation type provided is not supported&#34;)
    request_json = copy.deepcopy(EdiscoveryConstants.SERVER_LEVEL_SCHEDULE_JSON)
    request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;] = int(self._client_id)
    request_json[&#39;taskInfo&#39;][&#39;task&#39;][
        &#39;taskName&#39;] = f&#34;Cvpysdk created Schedule -{schedule_name} for Server id - {self._client_id}&#34;
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][
        &#39;subTaskName&#39;] = schedule_name
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;] = pattern_json
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;contentIndexingOption&#39;][&#39;operationType&#39;] = ops_type

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._CREATE_POLICY, request_json
    )
    if flag:
        if response.json():
            if &#34;taskId&#34; in response.json():
                task_id = str(response.json()[&#34;taskId&#34;])
                if task_id:
                    return

            elif &#34;errorCode&#34; in response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])
                error_message = response.json()[&#39;errorMessage&#39;]

                if error_code == &#34;0&#34;:
                    return
                else:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Schedule operation failed on server - {error_code} - {error_message}&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.search"><code class="name flex">
<span>def <span class="ident">search</span></span>(<span>self, criteria=None, attr_list=None, params=None)</span>
</code></dt>
<dd>
<div class="desc"><p>do searches on data source and returns document details</p>
<h2 id="args">Args</h2>
<p>criteria
(str)
&ndash;
containing criteria for query
(Default : None - returns all docs)</p>
<pre><code>                                Example :

                                    1) Size filter --&gt; Size:[10 TO 1024]
                                    2) File name filter --&gt; FileName_idx:09_23*
</code></pre>
<p>attr_list
(set)
&ndash;
Column names to be returned in results.
Acts as 'fl' in query</p>
<p>params
(dict)
&ndash;
Any other params which needs to be passed
Example : { "start" : "0" }</p>
<h2 id="returns">Returns</h2>
<p>int,list(dict),dict
&ndash;
Containing document count, document
details &amp; facet/stats details(if any)</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to perform search
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L939-L995" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def search(self, criteria=None, attr_list=None, params=None):
    &#34;&#34;&#34;do searches on data source and returns document details

        Args:

            criteria        (str)      --  containing criteria for query
                                                (Default : None - returns all docs)

                                                Example :

                                                    1) Size filter --&gt; Size:[10 TO 1024]
                                                    2) File name filter --&gt; FileName_idx:09_23*

            attr_list       (set)      --  Column names to be returned in results.
                                                 Acts as &#39;fl&#39; in query

            params          (dict)     --  Any other params which needs to be passed
                                               Example : { &#34;start&#34; : &#34;0&#34; }

        Returns:

            int,list(dict),dict    --  Containing document count, document  details &amp; facet/stats details(if any)


        Raises:

            SDKException:

                    if failed to perform search

    &#34;&#34;&#34;
    if criteria and not isinstance(criteria, str):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    search_params = self.form_search_params(criteria=criteria, attr_list=attr_list,
                                            params=params)
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_SEARCH % (self._search_entity_type, self._search_entity_id), search_params
    )
    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Failed to perform search - {response.json().get(&#39;errLogMessage&#39;,&#39;&#39;)}&#34;)
            if &#39;response&#39; in response.json() and &#39;docs&#39; in response.json()[&#39;response&#39;]:
                if &#39;facets&#39; in response.json():
                    return response.json()[&#39;response&#39;][&#39;numFound&#39;], response.json()[
                        &#39;response&#39;][&#39;docs&#39;], response.json()[&#39;facets&#39;]
                elif &#39;stats&#39; in response.json():
                    return response.json()[&#39;response&#39;][&#39;numFound&#39;], response.json()[
                        &#39;response&#39;][&#39;docs&#39;], response.json()[&#39;stats&#39;]
                else:
                    return response.json()[&#39;response&#39;][&#39;numFound&#39;], response.json()[&#39;response&#39;][&#39;docs&#39;], {}
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Failed to search with response - {response.json()}&#34;)
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;112&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.share"><code class="name flex">
<span>def <span class="ident">share</span></span>(<span>self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Shares ediscovery client with given user or user group in commcell</p>
<h2 id="args">Args</h2>
<p>user_or_group_name
(str)
&ndash;
Name of user or group</p>
<p>is_user
(bool)
&ndash;
Denotes whether this is user or group name
default : True(User)</p>
<p>allow_edit_permission
(bool)
&ndash;
whether to give edit permission or not to user or group</p>
<p>ops_type
(int)
&ndash;
Operation type</p>
<pre><code>                                    Default : 1 (Add)

                                    Supported : 1 (Add)
                                                3 (Delete)
</code></pre>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if unable to update security associations

    if response is empty or not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1043-L1138" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
    &#34;&#34;&#34;Shares ediscovery client with given user or user group in commcell

            Args:

                user_or_group_name      (str)       --  Name of user or group

                is_user                 (bool)      --  Denotes whether this is user or group name
                                                            default : True(User)

                allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                ops_type                (int)       --  Operation type

                                                        Default : 1 (Add)

                                                        Supported : 1 (Add)
                                                                    3 (Delete)

            Returns:

                None

            Raises:

                SDKException:

                        if unable to update security associations

                        if response is empty or not success
    &#34;&#34;&#34;
    if not isinstance(user_or_group_name, str):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    request_json = copy.deepcopy(EdiscoveryConstants.SHARE_REQUEST_JSON)
    external_user = False
    association_response = None
    if ops_type == 1:
        association_response = self._associations

    if &#39;\\&#39; in user_or_group_name:
        external_user = True
    if is_user:
        user_obj = self._commcell_object.users.get(user_or_group_name)
        user_id = user_obj.user_id
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userId&#39;] = int(user_id)
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 13
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userName&#39;] = user_or_group_name
    elif external_user:
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;groupId&#39;] = 0
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 62
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
            &#39;externalGroupName&#39;] = user_or_group_name
    else:
        grp_obj = self._commcell_object.user_groups.get(user_or_group_name)
        grp_id = grp_obj.user_group_id
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupId&#39;] = int(grp_id)
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = 15
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][
            &#39;userGroupName&#39;] = user_or_group_name

    request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;clientId&#39;] = int(self._client_id)
    request_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = ops_type

    if allow_edit_permission:
        # we need to send separate association for each permission
        association_json = copy.deepcopy(request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0])
        # do copy, remove permission and add Edit
        del association_json[&#39;properties&#39;][&#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;][0]
        association_json[&#39;properties&#39;][&#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;].append(
            EdiscoveryConstants.EDIT_CATEGORY_PERMISSION)
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;].append(association_json)

        # Associate existing associations to the request
    if ops_type == 1:
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;].extend(association_response)

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_SECURITY % self._app_type, request_json
    )
    if flag:
        if response.json() and &#39;response&#39; in response.json():
            response_json = response.json()[&#39;response&#39;]
            for node in response_json:
                if &#39;errorCode&#39; in node:
                    error_code = node[&#39;errorCode&#39;]
                    if error_code != 0:
                        error_message = node.get(&#39;warningMessage&#39;, &#34;Something went wrong&#34;)
                        raise SDKException(
                            &#39;EdiscoveryClients&#39;,
                            &#39;102&#39;, error_message)
            self.refresh()
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;109&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.start_job"><code class="name flex">
<span>def <span class="ident">start_job</span></span>(<span>self, wait_for_job=False, wait_time=60, is_incr=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts job on ediscovery client</p>
<h2 id="args">Args</h2>
<p>wait_for_job
(bool)
&ndash;
specifies whether to wait for job to complete or not</p>
<p>wait_time
(int)
&ndash;
time interval to wait for job completion in Mins
Default : 60Mins</p>
<p>is_incr
(bool)
&ndash; Specifies whether this is incremental or full crawl job
Return:</p>
<pre><code>    None
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to start collection job
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1202-L1246" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def start_job(self, wait_for_job=False, wait_time=60, is_incr=True):
    &#34;&#34;&#34;Starts job on ediscovery client

        Args:

                wait_for_job        (bool)       --  specifies whether to wait for job to complete or not

                wait_time           (int)        --  time interval to wait for job completion in Mins
                                                        Default : 60Mins

                is_incr             (bool)       -- Specifies whether this is incremental or full crawl job

         Return:

                None

        Raises:

                SDKException:

                        if failed to start collection job

    &#34;&#34;&#34;
    if not is_incr:
        self._operation = 3  # full crawl job
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self._API_CRAWL % (self._client_id, self._data_source_id, self._type, self._operation)
    )
    if flag:
        if response.json():
            response_json = response.json()
            if &#39;errorCode&#39; in response_json:
                error_code = response_json[&#39;errorCode&#39;]
                if error_code != 0:
                    error_message = response_json[&#39;errorMessage&#39;]
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;103&#39;)
        if not wait_for_job:
            return
        return self.wait_for_collection_job(wait_time=wait_time)
    response_string = self._commcell_object._update_response_(response.text)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.task_workflow_operation"><code class="name flex">
<span>def <span class="ident">task_workflow_operation</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>calls workflow operation for task</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
Workflow job id</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to call task workflow
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1520-L1548" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def task_workflow_operation(self):
    &#34;&#34;&#34;calls workflow operation for task

            Args:
                None

            Returns:

                str --  Workflow job id

            Raises:

                SDKException:

                    if failed to call task workflow
    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_TASK_WORKFLOW % self._client_id)
    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Something wrong while invoking task workflow operation - {response.json()[&#39;errorMessage&#39;]}&#34;)
            if &#39;jobId&#39; in response.json():
                return response.json()[&#39;jobId&#39;]
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Workflow task failed&#34;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.wait_for_collection_job"><code class="name flex">
<span>def <span class="ident">wait_for_collection_job</span></span>(<span>self, wait_time=60)</span>
</code></dt>
<dd>
<div class="desc"><p>Waits for collection job to finish</p>
<h2 id="args">Args</h2>
<p>wait_time
(int)
&ndash;
time interval to wait for job completion in Mins
Default : 60Mins</p>
<h2 id="return">Return</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if collection job fails

    if timeout happens
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1315-L1349" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def wait_for_collection_job(self, wait_time=60):
    &#34;&#34;&#34;Waits for collection job to finish

            Args:

                wait_time           (int)       --  time interval to wait for job completion in Mins
                                                        Default : 60Mins

            Return:

                None

            Raises:

                SDKException:

                        if collection job fails

                        if timeout happens

    &#34;&#34;&#34;
    timeout = time.time() + 60 * wait_time  # 1hr
    while True:
        if time.time() &gt; timeout:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Collection job Timeout&#34;)
        status = self.get_job_status()
        if int(status[&#39;state&#39;]) == InventoryConstants.CRAWL_JOB_COMPLETE_STATE:  # Finished State
            return
        elif int(status[&#39;state&#39;]) == InventoryConstants.CRAWL_JOB_COMPLETE_ERROR_STATE:  # completed with error
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Job status is marked as Completed with Error&#34;)
        # STOPPING,STOPPED,ABORTING, ABORTED,EXCEPTION,UNKNOWN,SYNCING,PENDING
        elif int(status[&#39;state&#39;]) in InventoryConstants.CRAWL_JOB_FAILED_STATE:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Job status is marked as Failed/Error/Pending&#34;)
        else:
            time.sleep(10)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.wait_for_export"><code class="name flex">
<span>def <span class="ident">wait_for_export</span></span>(<span>self, token, wait_time=60, download=True, download_location='C:\\Users\\<USER>\\Desktop\\Automated\\cvpysdk')</span>
</code></dt>
<dd>
<div class="desc"><p>Waits for Export to CSV to finish</p>
<h2 id="args">Args</h2>
<p>wait_time
(int)
&ndash;
time interval to wait for job completion in Mins
Default : 60Mins</p>
<p>token
(str)
&ndash;
Export to CSV token GUID</p>
<p>download
(bool)
&ndash;
specify whether to download exported file or not</p>
<p>download_location
(str)
&ndash;
Path where to download exported csv file
Default: Current working dir</p>
<h2 id="return">Return</h2>
<p>str
&ndash; Download GUID for exported CSV file if download=false
File path containing exported csv file if download=true</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if Export status check fails

    if timeout happens
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1248-L1313" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def wait_for_export(self, token, wait_time=60, download=True, download_location=os.getcwd()):
    &#34;&#34;&#34;Waits for Export to CSV to finish

            Args:

                wait_time           (int)       --  time interval to wait for job completion in Mins
                                                        Default : 60Mins

                token               (str)       --  Export to CSV token GUID

                download            (bool)      --  specify whether to download exported file or not

                download_location   (str)       --  Path where to download exported csv file
                                                            Default: Current working dir

            Return:

                str     -- Download GUID for exported CSV file if download=false
                           File path containing exported csv file if download=true

            Raises:

                SDKException:

                        if Export status check fails

                        if timeout happens

    &#34;&#34;&#34;
    timeout = time.time() + 60 * wait_time  # 1hr
    handler_id = self.get_handler_id()
    while True:
        if time.time() &gt; timeout:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Export job Timeout&#34;)
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_EXPORT_STATUS % (handler_id, token)
        )
        if flag:
            if response.json():
                response = response.json()
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Export status check failed with error : {response.get(&#39;errLogMessage&#39;, &#39;&#39;)}&#34;)
                elif &#39;customMap&#39; in response and response[&#39;customMap&#39;].get(&#39;name&#39;, &#39;&#39;) == &#39;statusObject&#39;:
                    value_json = json.loads(response[&#39;customMap&#39;][&#39;value&#39;])
                    if &#39;response&#39; in value_json and isinstance(
                            value_json[&#39;response&#39;],
                            dict) and value_json[&#39;response&#39;].get(
                            &#39;status&#39;,
                            &#39;&#39;) == &#39;finished&#39;:
                        if not download:
                            return value_json[&#39;response&#39;].get(&#39;downloadGuid&#39;, &#39;&#39;)
                        else:
                            return self._do_stream_download(guid=value_json[&#39;response&#39;].get(&#39;downloadGuid&#39;, &#39;&#39;),
                                                            file_name=f&#34;Cvpysdk_Activate_export_{int(time.time())}&#34;,
                                                            download_location=download_location)
                else:
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;,
                                       f&#34;Failed to check export status with response - {response.json()}&#34;)
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;114&#39;)
        else:
            self._response_not_success(response)
        time.sleep(10)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients"><code class="flex name class">
<span>class <span class="ident">EdiscoveryClients</span></span>
<span>(</span><span>commcell_object, class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting ediscovery clients details for different apps in activate</p>
<p>Initializes an instance of the EdiscoveryClients class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>class_object
(object)
&ndash; instance of FsoServers/FsoServerGroups/FsoServerGroup class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the EdiscoveryClients class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L224-L495" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class EdiscoveryClients():
    &#34;&#34;&#34;Class for getting ediscovery clients details for different apps in activate&#34;&#34;&#34;

    def __init__(self, commcell_object, class_object):
        &#34;&#34;&#34;Initializes an instance of the EdiscoveryClients class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                class_object        (object)    -- instance of FsoServers/FsoServerGroups/FsoServerGroup class

            Returns:
                object  -   instance of the EdiscoveryClients class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._class_obj = class_object
        self._ds_type = None
        self._client_group = None
        self._limit = None
        self._offset = None
        self._sort_by = None
        self._sort_dir = None
        self._client_group_filter = None
        self._include_doc = None
        self._ediscovery_sub_type = None
        self._API_GET_EDISCOVERY_CLIENTS = copy.deepcopy(self._services[&#39;EDISCOVERY_V2_GET_CLIENTS&#39;])
        self._API_GET_EDISCOVERY_CLIENT_GROUPS = self._services[&#39;EDISCOVERY_V2_GET_CLIENT_GROUP_DETAILS&#39;]
        self._API_GET_EDISCOVERY_CLIENTS_V1 = copy.deepcopy(self._services[&#39;EDISCOVERY_CLIENTS&#39;])
        self._API_CREATE_CLIENT = copy.deepcopy(self._services[&#39;EDISCOVERY_CREATE_CLIENT&#39;])
        self._API_DELETE_CLIENT = copy.deepcopy((self._services[&#39;EDISCOVERY_DELETE_CLIENT&#39;]))

        from .file_storage_optimization import FsoServers, FsoServerGroups, FsoServerGroup
        from .sensitive_data_governance import Projects

        if isinstance(class_object, FsoServers):
            self._ds_type = 5
            self._client_group = 0
            self._limit = 0
            self._offset = 0
            self._sort_by = 1
            self._sort_dir = 0
        elif isinstance(class_object, FsoServerGroups):
            self._ds_type = 5
            self._client_group = 1
            self._limit = 0
            self._offset = 0
            self._sort_by = 1
            self._sort_dir = 0
        elif isinstance(class_object, FsoServerGroup):
            self._ds_type = 5
            self._client_group = 0
            self._client_group_filter = class_object.server_group_id
            self._limit = 0
            self._offset = 0
            self._sort_by = 1
            self._sort_dir = 0
            self._include_doc = 1
        elif isinstance(class_object, Projects):
            self._ediscovery_sub_type = 2
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Not a supported caller for this class&#39;)

    def delete(self, client_id):
        &#34;&#34;&#34;Deletes the ediscovery client

                Args:

                    client_id (int)       --  Client id

                Returns:

                      None

                Raises:

                      SDKException:

                            if input is not valid

                            if failed to delete client

                            if response is empty or not success

        &#34;&#34;&#34;
        if not isinstance(client_id, int):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._API_DELETE_CLIENT % client_id)
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response = response.json()[&#39;response&#39;][0]
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Delete operation failed on client - {response[&#39;errorCode&#39;]}&#34;)
                return
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;120&#39;)
        self._response_not_success(response)

    def add(self, client_name, inventory_name, plan_name):
        &#34;&#34;&#34;Adds ediscovery client

                Args:

                    client_name        (str)        --  Name of the client

                    inventory_name      (str)       --  Name of inventory

                    plan_name           (str)       --  Plan name to associate with this client

                Returns:

                    int --  client id

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to create client

                            if response is empty or not success
        &#34;&#34;&#34;
        if not isinstance(client_name, str) or not isinstance(plan_name, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if not self._commcell_object.plans.has_plan(plan_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid plan name&#39;)
        plan_obj = self._commcell_object.plans.get(plan_name)
        req_json = copy.deepcopy(EdiscoveryConstants.CREATE_CLIENT_REQ_JSON)
        req_json[&#39;entity&#39;][&#39;clientName&#39;] = client_name
        req_json[&#39;clientInfo&#39;][&#39;plan&#39;][&#39;planId&#39;] = int(plan_obj.plan_id)
        if inventory_name is not None:
            if not self._commcell_object.activate.inventory_manager().has_inventory(inventory_name):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid inventory name&#39;)
            inv_obj = self._commcell_object.activate.inventory_manager().get(inventory_name)
            req_json[&#39;clientInfo&#39;][&#39;edgeDrivePseudoClientProperties&#39;][&#39;eDiscoveryInfo&#39;][&#39;inventoryDataSource&#39;][&#39;seaDataSourceId&#39;] \
                = int(inv_obj.inventory_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CREATE_CLIENT, req_json)
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response = response.json()[&#39;response&#39;]
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Add operation failed on client - {response[&#39;errorCode&#39;]}&#34;)
                if &#39;entity&#39; in response:
                    return response[&#39;entity&#39;].get(&#39;clientId&#39;, 0)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;119&#39;)
        self._response_not_success(response)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def get_ediscovery_client_group_details(self):
        &#34;&#34;&#34;returns the ediscovery client group details for the app

                Args:

                    None

                Returns:

                    dict        -- Containing client group details

                Raises;

                    SDKException:

                            if failed to get client group details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_GET_EDISCOVERY_CLIENT_GROUPS %
            (self._client_group_filter, self._include_doc))
        if flag:
            if response.json() and &#39;nodeList&#39; in response.json():
                return response.json()
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def get_ediscovery_clients(self):
        &#34;&#34;&#34;returns the ediscovery clients details for the app

                Args:

                    None

                Returns:

                    dict        -- Containing client details

                Raises;

                    SDKException:

                            if failed to get client details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        api = self._API_GET_EDISCOVERY_CLIENTS % (
            self._ds_type, self._client_group, self._limit, self._offset, self._sort_by, self._sort_dir)
        if self._client_group_filter:
            api = api + f&#34;&amp;clientGroupFilter={self._client_group_filter}&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        output = {}
        if flag:
            if response.json() and &#39;nodeList&#39; in response.json():
                for node in response.json()[&#39;nodeList&#39;]:
                    if &#39;clientEntity&#39; in node:
                        output[node[&#39;clientEntity&#39;].get(&#39;displayName&#39;, &#39;NA&#39;).lower()] = node
                return output
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def get_ediscovery_projects(self):
        &#34;&#34;&#34;returns the ediscovery projects details for the app

                Args:

                    None

                Returns:

                    dict        -- Containing project details

                Raises;

                    SDKException:

                            if failed to get project details

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        if not self._ediscovery_sub_type:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Ediscovery subtype not initialized&#39;)
        api = self._API_GET_EDISCOVERY_CLIENTS_V1 % self._ediscovery_sub_type
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        output = {}
        if flag:
            if response.json() and &#39;eDiscoveryClientProp&#39; in response.json():
                projects = response.json()[&#39;eDiscoveryClientProp&#39;]
                for project in projects:
                    project[&#39;clientId&#39;] = project[&#39;eDiscoveryClient&#39;][&#39;clientId&#39;]
                    output[project[&#39;eDiscoveryClient&#39;][&#39;clientName&#39;].lower()] = project
                return output
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;117&#39;)
        self._response_not_success(response)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, client_name, inventory_name, plan_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds ediscovery client</p>
<h2 id="args">Args</h2>
<p>client_name
(str)
&ndash;
Name of the client</p>
<p>inventory_name
(str)
&ndash;
Name of inventory</p>
<p>plan_name
(str)
&ndash;
Plan name to associate with this client</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
client id</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if input is not valid

    if failed to create client

    if response is empty or not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L327-L379" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, client_name, inventory_name, plan_name):
    &#34;&#34;&#34;Adds ediscovery client

            Args:

                client_name        (str)        --  Name of the client

                inventory_name      (str)       --  Name of inventory

                plan_name           (str)       --  Plan name to associate with this client

            Returns:

                int --  client id

            Raises:

                SDKException:

                        if input is not valid

                        if failed to create client

                        if response is empty or not success
    &#34;&#34;&#34;
    if not isinstance(client_name, str) or not isinstance(plan_name, str):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    if not self._commcell_object.plans.has_plan(plan_name):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid plan name&#39;)
    plan_obj = self._commcell_object.plans.get(plan_name)
    req_json = copy.deepcopy(EdiscoveryConstants.CREATE_CLIENT_REQ_JSON)
    req_json[&#39;entity&#39;][&#39;clientName&#39;] = client_name
    req_json[&#39;clientInfo&#39;][&#39;plan&#39;][&#39;planId&#39;] = int(plan_obj.plan_id)
    if inventory_name is not None:
        if not self._commcell_object.activate.inventory_manager().has_inventory(inventory_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid inventory name&#39;)
        inv_obj = self._commcell_object.activate.inventory_manager().get(inventory_name)
        req_json[&#39;clientInfo&#39;][&#39;edgeDrivePseudoClientProperties&#39;][&#39;eDiscoveryInfo&#39;][&#39;inventoryDataSource&#39;][&#39;seaDataSourceId&#39;] \
            = int(inv_obj.inventory_id)
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_CREATE_CLIENT, req_json)
    if flag:
        if response.json() and &#39;response&#39; in response.json():
            response = response.json()[&#39;response&#39;]
            if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Add operation failed on client - {response[&#39;errorCode&#39;]}&#34;)
            if &#39;entity&#39; in response:
                return response[&#39;entity&#39;].get(&#39;clientId&#39;, 0)
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;119&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, client_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the ediscovery client</p>
<h2 id="args">Args</h2>
<p>client_id (int)
&ndash;
Client id</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if input is not valid

  if failed to delete client

  if response is empty or not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L289-L325" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, client_id):
    &#34;&#34;&#34;Deletes the ediscovery client

            Args:

                client_id (int)       --  Client id

            Returns:

                  None

            Raises:

                  SDKException:

                        if input is not valid

                        if failed to delete client

                        if response is empty or not success

    &#34;&#34;&#34;
    if not isinstance(client_id, int):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    flag, response = self._cvpysdk_object.make_request(
        &#39;DELETE&#39;, self._API_DELETE_CLIENT % client_id)
    if flag:
        if response.json() and &#39;response&#39; in response.json():
            response = response.json()[&#39;response&#39;][0]
            if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Delete operation failed on client - {response[&#39;errorCode&#39;]}&#34;)
            return
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;120&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.get_ediscovery_client_group_details"><code class="name flex">
<span>def <span class="ident">get_ediscovery_client_group_details</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the ediscovery client group details for the app</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash; Containing client group details
Raises;</p>
<pre><code>SDKException:

        if failed to get client group details

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L392-L421" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_ediscovery_client_group_details(self):
    &#34;&#34;&#34;returns the ediscovery client group details for the app

            Args:

                None

            Returns:

                dict        -- Containing client group details

            Raises;

                SDKException:

                        if failed to get client group details

                        if response is empty

                        if response is not success

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self._API_GET_EDISCOVERY_CLIENT_GROUPS %
        (self._client_group_filter, self._include_doc))
    if flag:
        if response.json() and &#39;nodeList&#39; in response.json():
            return response.json()
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;106&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.get_ediscovery_clients"><code class="name flex">
<span>def <span class="ident">get_ediscovery_clients</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the ediscovery clients details for the app</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash; Containing client details
Raises;</p>
<pre><code>SDKException:

        if failed to get client details

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L423-L458" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_ediscovery_clients(self):
    &#34;&#34;&#34;returns the ediscovery clients details for the app

            Args:

                None

            Returns:

                dict        -- Containing client details

            Raises;

                SDKException:

                        if failed to get client details

                        if response is empty

                        if response is not success

    &#34;&#34;&#34;
    api = self._API_GET_EDISCOVERY_CLIENTS % (
        self._ds_type, self._client_group, self._limit, self._offset, self._sort_by, self._sort_dir)
    if self._client_group_filter:
        api = api + f&#34;&amp;clientGroupFilter={self._client_group_filter}&#34;
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
    output = {}
    if flag:
        if response.json() and &#39;nodeList&#39; in response.json():
            for node in response.json()[&#39;nodeList&#39;]:
                if &#39;clientEntity&#39; in node:
                    output[node[&#39;clientEntity&#39;].get(&#39;displayName&#39;, &#39;NA&#39;).lower()] = node
            return output
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;106&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.get_ediscovery_projects"><code class="name flex">
<span>def <span class="ident">get_ediscovery_projects</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the ediscovery projects details for the app</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash; Containing project details
Raises;</p>
<pre><code>SDKException:

        if failed to get project details

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L460-L495" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_ediscovery_projects(self):
    &#34;&#34;&#34;returns the ediscovery projects details for the app

            Args:

                None

            Returns:

                dict        -- Containing project details

            Raises;

                SDKException:

                        if failed to get project details

                        if response is empty

                        if response is not success

    &#34;&#34;&#34;
    if not self._ediscovery_sub_type:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Ediscovery subtype not initialized&#39;)
    api = self._API_GET_EDISCOVERY_CLIENTS_V1 % self._ediscovery_sub_type
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
    output = {}
    if flag:
        if response.json() and &#39;eDiscoveryClientProp&#39; in response.json():
            projects = response.json()[&#39;eDiscoveryClientProp&#39;]
            for project in projects:
                project[&#39;clientId&#39;] = project[&#39;eDiscoveryClient&#39;][&#39;clientId&#39;]
                output[project[&#39;eDiscoveryClient&#39;][&#39;clientName&#39;].lower()] = project
            return output
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;117&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources"><code class="flex name class">
<span>class <span class="ident">EdiscoveryDataSources</span></span>
<span>(</span><span>commcell_object, class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent all datasources associated with ediscovery client</p>
<p>Initializes an instance of the EdiscoveryDataSources class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>class_object
(object)
&ndash; instance of FsoServer/FsoServers/FsoServerGroups class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the EdiscoveryDataSources class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1610-L2477" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class EdiscoveryDataSources():
    &#34;&#34;&#34;Class to represent all datasources associated with ediscovery client&#34;&#34;&#34;

    def __init__(self, commcell_object, class_object):
        &#34;&#34;&#34;Initializes an instance of the EdiscoveryDataSources class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                class_object        (object)    -- instance of FsoServer/FsoServers/FsoServerGroups class

            Returns:
                object  -   instance of the EdiscoveryDataSources class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._class_obj = class_object
        self._ediscovery_client_ops = None
        self._client_id = None
        self._ediscovery_client_props = None
        self._data_source_display_names = None
        self._data_source_names = None
        self._data_sources = None
        self._app_source = None
        self._app_source_sub_type = None
        self._type = None  # client entity
        self._API_DELETE = self._services[&#39;EDISCOVERY_DATA_SOURCE_DELETE&#39;]
        self._API_CREATE_DATA_SOURCE = self._services[&#39;EDISCOVERY_CREATE_DATA_SOURCE&#39;]
        self._API_GET_DATA_SOURCE_STATS = self._services[&#39;EDISCOVERY_DATA_SOURCE_STATS&#39;]

        from .file_storage_optimization import FsoServer, FsoServers, FsoServerGroups
        from .sensitive_data_governance import Project

        if isinstance(class_object, FsoServer):
            self._client_id = class_object.server_id
            self._ediscovery_client_ops = EdiscoveryClientOperations(commcell_object, class_object)
            self._app_source = TargetApps.FSO
        elif isinstance(class_object, FsoServers):
            self._app_source = TargetApps.FSO
            self._app_source_sub_type = EdiscoveryConstants.FSO_SERVERS
        elif isinstance(class_object, FsoServerGroups):
            self._app_source = TargetApps.FSO
            self._app_source_sub_type = EdiscoveryConstants.FSO_SERVER_GROUPS
        elif isinstance(class_object, Project):
            self._app_source = TargetApps.SDG
            self._client_id = class_object.project_id
            self._ediscovery_client_ops = EdiscoveryClientOperations(commcell_object, class_object)
            self._type = 1
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)

        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_data_source_properties(self, client_details):
        &#34;&#34;&#34;Parses client response and returns data sources properties

                Args:

                    client_details      (dict)      --  containing EdiscoveryClient details response

                Returns:

                    list        --  containing data source details

                Raises:

                    SDKException:

                            if input is not valid


        &#34;&#34;&#34;
        output = {}
        if not isinstance(client_details, dict):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;107&#39;)
        index = 0
        ds_name = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME)
        ds_type = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE)
        plan_id = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_PLAN_ID, field_type=&#34;int&#34;)
        subclient_id = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_SUBCLIENT_ID, field_type=&#34;int&#34;)
        crawl_type = self._parse_client_response_for_data_source(
            client_details, field_name=EdiscoveryConstants.FIELD_CRAWL_TYPE, field_type=&#34;int&#34;)
        if &#39;childs&#39; not in client_details:
            return output
        for data_source in client_details[&#39;childs&#39;]:
            ds_id = 0
            if &#39;dsEntity&#39; in data_source:
                ds_id = data_source[&#39;dsEntity&#39;].get(EdiscoveryConstants.FIELD_DATA_SOURCE_ID, 0)
            ds_props = {
                EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME: ds_name[index],
                EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE: ds_type[index],
                EdiscoveryConstants.FIELD_PLAN_ID: plan_id[index],
                EdiscoveryConstants.FIELD_SUBCLIENT_ID: subclient_id[index],
                EdiscoveryConstants.FIELD_DATA_SOURCE_ID: ds_id,
                EdiscoveryConstants.FIELD_CRAWL_TYPE: crawl_type
            }
            output[ds_name[index].lower()] = ds_props
            index = index + 1
        return output

    def _get_data_sources_stats(self):
        &#34;&#34;&#34;returns the dict containing data source properties

            Args:

                None

            Returns:

                dict    --  containing data source properties

            Raises:

                SDKException:

                        if failed to get data source stats

                        if response is empty or not success
        &#34;&#34;&#34;
        api = self._API_GET_DATA_SOURCE_STATS % (self._client_id, self._type)
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        output = {}
        if flag:
            if response.json() and &#39;statusResp&#39; in response.json():
                status = response.json()[&#39;statusResp&#39;]
                if &#39;collections&#39; in status:
                    # Change to return all datasources in a project
                    for collection in status[&#39;collections&#39;]:
                        if &#39;datasources&#39; in collection:
                            data_sources = collection[&#39;datasources&#39;]
                            for data_source in data_sources:
                                ds_props = {
                                    EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME: data_source[EdiscoveryConstants.FIELD_DISPLAY_NAME],
                                    EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE: data_source[EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE],
                                    EdiscoveryConstants.FIELD_DATA_SOURCE_ID: data_source[EdiscoveryConstants.FIELD_DATA_SOURCE_ID_NON_SEA],
                                    EdiscoveryConstants.FIELD_DOCUMENT_COUNT: data_source.get(&#39;status&#39;, {}).get(&#39;totalcount&#39;, 0)
                                }
                                output[data_source[EdiscoveryConstants.FIELD_DISPLAY_NAME].lower()] = ds_props
                    return output
                return {}  # no data sources exists
            if response.json() and &#39;response&#39; in response.json():
                response = response.json()[&#39;response&#39;]
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Ediscovery Add client failed with error code - {response[&#39;errorCode&#39;]}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;110&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the data sources associated with edisocvery client&#34;&#34;&#34;
        if not self._client_id:
            return
        if self._app_source and self._app_source == TargetApps.SDG:
            self._ediscovery_client_props = self._ediscovery_client_ops.get_ediscovery_project_details()
            self._data_source_display_names, self._data_source_names = self._get_data_source_names(
                self._ediscovery_client_props)
            self._data_sources = self._get_data_sources_stats()
        elif self._app_source and self._app_source == TargetApps.FSO:
            self._ediscovery_client_props = self._get_data_sources_details()
            self._data_source_display_names, self._data_source_names = self._get_data_source_names(
                self._ediscovery_client_props)
            self._data_sources = self._get_data_source_properties(self._ediscovery_client_props)
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unknown App source type passed&#34;)

    def add_fs_data_source(self, server_name, data_source_name, inventory_name, plan_name,
                           source_type=EdiscoveryConstants.SourceType.BACKUP, **kwargs):
        &#34;&#34;&#34;Adds file system data source to server

                Args:

                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    inventory_name      (str)       --  Inventory name which needs to be associated

                    plan_name           (str)       --  Plan name which needs to be associated with this data source

                    source_type         (enum)      --  Source type for crawl (Live source or Backedup)
                                                                Refer EdiscoveryConstants.SourceType

                Kwargs Arguments:

                    scan_type           (str)       --  Specifies scan type when source type is for backed up data
                                                                Supported values : quick | full

                    crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

                    access_node         (str)       --  server name which needs to be used as access node in case
                                                                if server to be added is not a commvault client

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    user_name           (str)       --  User name who has access to UNC path

                    password            (str)       --  base64 encoded password to access unc path

                    enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this

                Returns:

                    obj     --  Instance of EdiscoveryDataSource class

                    None    --  if it is called to create FSO server group

                Raises:

                      SDKException:

                            if plan/inventory/index server doesn&#39;t exists

                            if failed to add FSO server data source
        &#34;&#34;&#34;
        is_commvault_client = False
        is_server_group = False
        if self._app_source_sub_type and self._app_source_sub_type == EdiscoveryConstants.FSO_SERVER_GROUPS:
            is_server_group = True
        if not self._commcell_object.activate.inventory_manager().has_inventory(inventory_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid inventory name&#39;)
        if not self._commcell_object.plans.has_plan(plan_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid plan name&#39;)
        plan_obj = self._commcell_object.plans.get(plan_name)
        if self._app_source.value not in plan_obj.content_indexing_props[&#39;targetApps&#39;]:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Plan is not marked with targetapp as FSO&#39;)
        inv_obj = self._commcell_object.activate.inventory_manager().get(inventory_name)
        request_json = copy.deepcopy(EdiscoveryConstants.ADD_FS_REQ_JSON)
        request_json[&#39;datasourceId&#39;] = inv_obj.inventory_id
        request_json[&#39;indexServerClientId&#39;] = plan_obj.content_indexing_props[&#39;analyticsIndexServer&#39;].get(&#39;clientId&#39;, 0)
        request_json[&#39;datasources&#39;][0][&#39;datasourceName&#39;] = data_source_name
        if self._app_source == TargetApps.SDG:
            request_json[&#39;clientId&#39;] = self._client_id  # project source client id
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;caconfig&#34;,
                &#34;propertyValue&#34;: &#34;[{\&#34;task\&#34;:\&#34;EntityExtractionFields\&#34;,\&#34;arguments\&#34;:[\&#34;content\&#34;]}]&#34;
            })
        # find out whether given server is commvault client or not to decide further
        inventory_resp = None
        scan_type = kwargs.get(&#39;scan_type&#39;, &#39;quick&#39;)
        if not is_server_group:
            is_commvault_client = self._commcell_object.clients.has_client(server_name)
            if not is_commvault_client:
                if (&#39;access_node&#39; not in kwargs or &#39;user_name&#39; not in kwargs or &#39;password&#39; not in kwargs):
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Access node information is missing&#34;)
                if not self._commcell_object.clients.has_client(kwargs.get(&#34;access_node&#34;)):
                    raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Access node client is not present&#34;)
            inventory_resp = inv_obj.data_source.ds_handlers.get(
                EdiscoveryConstants.FS_SERVER_HANDLER_NAME).get_handler_data(
                handler_filter=f&#34;q=(name_idx:{server_name})&amp;rows=1&#34;)
            if inventory_resp[&#39;numFound&#39;] != 1:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    &#39;Multiple server with same name exists or no server exists in inventory&#39;)
            inventory_resp = inventory_resp[&#39;docs&#39;][0]
        # set common properties
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;enablemonitoring&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;enable_monitoring&#39;, &#34;false&#34;).lower()
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;countryCode&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;country_code&#39;, &#39;US&#39;)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;co&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;country_name&#39;, &#39;United States&#39;)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;contentid&#34;,
            &#34;propertyValue&#34;: inventory_resp[&#39;contentid&#39;] if not is_server_group else server_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;clientdisplayname&#34;,
            &#34;propertyValue&#34;: server_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;dcplanid&#34;,
            &#34;propertyValue&#34;: str(plan_obj.plan_id)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;name&#34;,
            &#34;propertyValue&#34;: inventory_resp[&#39;name&#39;] if not is_server_group else server_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;operatingSystem&#34;,
            &#34;propertyValue&#34;: inventory_resp[&#39;operatingSystem&#39;] if not is_server_group else &#34;&#34;
        })
        if is_commvault_client:
            if &#39;access_node&#39; not in kwargs:
                del request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;]
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;ClientId&#34;,
                &#34;propertyValue&#34;: str(self._commcell_object.clients.get(server_name).client_id)
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;ContentIndexingStatus&#34;,
                &#34;propertyValue&#34;: str(inventory_resp[&#39;ContentIndexingStatus&#39;])
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;BackedupStatus&#34;,
                &#34;propertyValue&#34;: str(inventory_resp[&#39;BackedupStatus&#39;])
            })
        elif is_server_group:
            del request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;]
            del request_json[&#39;indexServerClientId&#39;]
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;ClientGroupId&#34;,
                &#34;propertyValue&#34;: str(self._commcell_object.client_groups.get(server_name).clientgroup_id)
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;ContentIndexingStatus&#34;,
                &#34;propertyValue&#34;: str(0)
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;BackedupStatus&#34;,
                &#34;propertyValue&#34;: str(0)
            })

        # set crawl type and source type related params
        if source_type.value == EdiscoveryConstants.SourceType.BACKUP.value:
            if scan_type == &#39;quick&#39; and self._app_source == TargetApps.FSO:
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;crawltype&#34;,
                    &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.FILE_LEVEL_ANALYTICS.value)
                })
            else:
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;crawltype&#34;,
                    &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.BACKUP_V2.value)
                })
        else:
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;crawltype&#34;,
                &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.LIVE.value)
            })
            if not is_commvault_client or &#39;access_node&#39; in kwargs:
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;username&#34;,
                    &#34;propertyValue&#34;: kwargs.get(&#39;user_name&#39;, &#39;&#39;)
                })
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;password&#34;,
                    &#34;propertyValue&#34;: kwargs.get(&#39;password&#39;, &#39;&#39;)
                })
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;domainName&#34;,
                    &#34;propertyValue&#34;: inventory_resp[&#39;domainName&#39;]
                })
                request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                    &#34;propertyName&#34;: &#34;dNSHostName&#34;,
                    &#34;propertyValue&#34;: inventory_resp[&#39;dNSHostName&#39;]
                })
                request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;][0][&#39;clientId&#39;] = int(
                    self._commcell_object.clients.get(kwargs.get(&#39;access_node&#39;)).client_id)
                request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;][0][&#39;clientName&#39;] = kwargs.get(&#39;access_node&#39;)

            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;includedirectoriespath&#34;,
                &#34;propertyValue&#34;: &#39;,&#39;.join(kwargs.get(&#39;crawl_path&#39;, []))
            })

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CREATE_DATA_SOURCE, request_json)
        if flag:
            if response.json() and &#39;collections&#39; in response.json():
                collection = response.json()[&#39;collections&#39;][0]
                if &#39;datasources&#39; in collection:
                    data_source = collection[&#39;datasources&#39;][0]
                    # when add data source is called for new server then handle client id accordingly
                    if is_server_group:
                        # for server group, no need to refresh data sources details as we go via Server by server only
                        return
                    if not self._client_id:
                        if is_commvault_client:
                            self._client_id = inventory_resp[&#39;ClientId&#39;]
                        else:
                            self._commcell_object.clients.refresh()
                            all_clients = self._commcell_object.clients.all_clients
                            for client_name, _ in all_clients.items():
                                if client_name.lower().startswith(f&#34;{data_source_name.lower()}_&#34;):
                                    self._client_id = self._commcell_object.clients.get(client_name).client_id
                                    break
                        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)
                    self.refresh()
                    return EdiscoveryDatasource(
                        self._commcell_object,
                        data_source[&#39;datasourceId&#39;],
                        EdiscoveryConstants.DATA_SOURCE_TYPES[5], client_id=self._client_id, app_type=self._app_source)
            if response.json() and &#39;error&#39; in response.json():
                error = response.json()[&#39;error&#39;]
                if &#39;errorCode&#39; in error and error[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Creation of data source failed with error - {error[&#39;errorCode&#39;]}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;115&#39;)
        self._response_not_success(response)

    def add_o365_sdg_data_source(self, server_name, data_source_name, plan_name,
                                 datasource_type=EdiscoveryConstants.ClientType.ONEDRIVE, **kwargs):
        &#34;&#34;&#34;Adds Office365 SDG data source to a project

                Args:
                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    plan_name           (str)       --  Plan name which needs to be associated with this data source

                    datasource_type     (str)       --  Type of O365 SDG datasource (Exchange/OneDrive)

                Kwargs Arguments:

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    users               (list)      --  List of users/mailboxes to be added. If empty, all users would be added

                Returns:

                    obj     --  Instance of EdiscoveryDataSource class

                Raises:

                      SDKException:

                            if plan doesn&#39;t exists
        &#34;&#34;&#34;
        if not self._commcell_object.plans.has_plan(plan_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid plan name&#39;)
        plan_obj = self._commcell_object.plans.get(plan_name)
        if self._app_source.value not in plan_obj.content_indexing_props[&#39;targetApps&#39;]:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Plan is not marked with targetapp as SDG&#39;)
        o365_client = self._commcell_object.clients.get(server_name)
        backupset_id, subclient_id = self._get_o365_backupset_subclient_id(o365_client, datasource_type)
        request_json = copy.deepcopy(EdiscoveryConstants.ADD_O365_SDG_BACKED_UP_DS_REQ)
        request_json[&#39;clientId&#39;] = self._client_id  # project source client id
        if plan_obj.content_indexing_props.get(&#39;analyticsIndexServer&#39;, {}).get(&#39;clientId&#39;, None) is not None:
            # Only for software datasource creation, we will need this index server client ID to be set
            request_json[&#39;indexServerClientId&#39;] = plan_obj.content_indexing_props[&#39;analyticsIndexServer&#39;].get(&#39;clientId&#39;, 0)
        request_json[&#39;datasources&#39;][0][&#39;datasourceType&#39;] = datasource_type.value
        request_json[&#39;datasources&#39;][0][&#39;datasourceName&#39;] = data_source_name
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;caconfig&#34;,
            &#34;propertyValue&#34;: &#34;[{\&#34;task\&#34;:\&#34;EntityExtractionFields\&#34;,\&#34;arguments\&#34;:[\&#34;content\&#34;]}]&#34;
        })

        # set common properties
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;countryCode&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;country_code&#39;, &#39;US&#39;)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;co&#34;,
            &#34;propertyValue&#34;: kwargs.get(&#39;country_name&#39;, &#39;United States&#39;)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;clientdisplayname&#34;,
            &#34;propertyValue&#34;: data_source_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;name&#34;,
            &#34;propertyValue&#34;: data_source_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;dcplanid&#34;,
            &#34;propertyValue&#34;: str(plan_obj.plan_id)
        })

        # set crawl type and source type related params
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;crawltype&#34;,
            &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.BACKUP_V2.value)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;dNSHostName&#34;,
            &#34;propertyValue&#34;: server_name
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;ClientId&#34;,
            &#34;propertyValue&#34;: str(o365_client.client_id)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;CAppBackupSetId&#34;,
            &#34;propertyValue&#34;: str(backupset_id)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;backedupsubclientids&#34;,
            &#34;propertyValue&#34;: str(subclient_id)
        })

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CREATE_DATA_SOURCE, request_json)
        if flag:
            if response.json() and &#39;collections&#39; in response.json():
                collection = response.json()[&#39;collections&#39;][0]
                if &#39;datasources&#39; in collection:
                    data_source = collection[&#39;datasources&#39;][0]
                    # when add data source is called for new server then handle client id accordingly
                    return EdiscoveryDatasource(
                        self._commcell_object,
                        data_source[&#39;datasourceId&#39;],
                        EdiscoveryConstants.DATA_SOURCE_TYPES[datasource_type.value], client_id=self._client_id, app_type=self._app_source)
            if response.json() and &#39;error&#39; in response.json():
                error = response.json()[&#39;error&#39;]
                if &#39;errorCode&#39; in error and error[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Creation of data source failed with error - {error[&#39;errorCode&#39;]}&#34;)
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;115&#39;)
        self._response_not_success(response)

    def _get_o365_backupset_subclient_id(self, client, client_type=EdiscoveryConstants.ClientType.ONEDRIVE):
        &#34;&#34;&#34;
        Get the backupset and subclient ID for a given client object
        Args:
            client(object)      --  Instance of O365 client object
            client_type(enum)   --  Type of client (OneDrive/Exchange)
        Returns:
            backupset_id(int)   --  Backupset ID of the client
            subclient_id(int)   --  Subclient ID of the client
        Raises:
            SDKException:
                if backupset or subclient doesn&#39;t exist

        &#34;&#34;&#34;
        if client_type == EdiscoveryConstants.ClientType.ONEDRIVE:
            _agent = client.agents.get(EdiscoveryConstants.ONEDRIVE_AGENT)
            _instance = _agent.instances.get(EdiscoveryConstants.ONEDRIVE_INSTANCE)
            _backupset = _instance.backupsets.get(EdiscoveryConstants.ONEDRIVE_BACKUPSET)
            _subclient = _backupset.subclients.get(EdiscoveryConstants.ONEDRIVE_SUBCLIENT)
        else:
            _agent = client.agents.get(EdiscoveryConstants.EXCHANGE_AGENT)
            _instance = _agent.instances.get(EdiscoveryConstants.EXCHANGE_INSTANCE)
            _backupset = _instance.backupsets.get(EdiscoveryConstants.EXCHANGE_BACKUPSET)
            _subclient = _backupset.subclients.get(EdiscoveryConstants.EXCHANGE_SUBCLIENT)
        if _backupset is None or _subclient is None:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;121&#39;)
        return _backupset.backupset_id, _subclient.subclient_id

    def delete(self, data_source_name):
        &#34;&#34;&#34;Deletes the given data source from client

                        Args:

                            data_source_name        (str)       --  Datasource name

                        Returns:

                            None

                        Raises:

                            SDKException:

                                    if failed to find given data source in this client

                                    if failed to delete the data source

        &#34;&#34;&#34;
        if not self.has_data_source(data_source_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;108&#39;)
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._API_DELETE % (self.get(data_source_name).data_source_id, self._client_id)
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Failed to Delete DataSource with error [{response.json().get(&#39;errorMessage&#39;,&#39;&#39;)}]&#34;)
                self.refresh()
            else:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;111&#39;)
        else:
            self._response_not_success(response)

    def has_data_source(self, data_source_name):
        &#34;&#34;&#34;Checks whether given data source exists in this client or not

                Args:

                    data_source_name        (str)       --  Datasource name

                Returns:

                    bool    --  True if exists else false

                Raises:

                    SDKException:

                            if failed to find given data source in this client

        &#34;&#34;&#34;
        if not isinstance(data_source_name, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        return self._data_source_display_names and data_source_name.lower() in self._data_source_display_names

    def get(self, data_source_name):
        &#34;&#34;&#34;returns EdiscoveryDataSource class object for given data source name

                Args:

                    data_source_name        (str)       --  Datasource name

                Returns:

                    obj --  Instance of EdiscoveryDataSource class

                Raises:

                    SDKException:

                            if failed to find given data source in this client

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(data_source_name, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if not self.has_data_source(data_source_name):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;108&#39;)
        ds_props = self._data_sources[data_source_name.lower()]
        return EdiscoveryDatasource(commcell_object=self._commcell_object,
                                    data_source_id=int(ds_props[EdiscoveryConstants.FIELD_DATA_SOURCE_ID]),
                                    data_source_type=ds_props[EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE],
                                    app_type=self._app_source, client_id=self._client_id)

    def _parse_client_response_for_data_source(self, client_details, field_name, field_type=&#34;str&#34;):
        &#34;&#34;&#34;Parses client response and returns given property from data sources as list

                Args:

                    client_details      (dict)      --  containing EdiscoveryClient details response

                    field_name          (str)       --  Field name to be fetched

                    field_type          (str)       --  Field type to be converted (Default: str)

                Returns:

                    list        --  containing field values from all data sources in response

                Raises:

                    SDKException:

                            if input is not valid


        &#34;&#34;&#34;
        output = []
        old_len = len(output)
        if not isinstance(client_details, dict):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;107&#39;)
        if &#39;childs&#39; not in client_details:
            return output
        for data_source in client_details[&#39;childs&#39;]:
            if &#39;customProperties&#39; in data_source:
                name_value_dict = data_source[&#39;customProperties&#39;][&#39;nameValues&#39;]
                for prop in name_value_dict:
                    prop_name = prop.get(&#39;name&#39;)
                    if prop_name == field_name:
                        if field_type == &#34;int&#34;:
                            output.append(int(prop.get(&#39;value&#39;, 0)))
                        else:
                            if field_name == EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME:
                                output.append(prop.get(&#39;value&#39;, &#39;NA&#39;).lower())
                            else:
                                output.append(prop.get(&#39;value&#39;, &#39;NA&#39;))
            new_len = len(output)
            if old_len == new_len:
                output.append(&#39;Not Found&#39;)
                new_len = new_len + 1
            old_len = new_len
        return output

    def _get_data_source_names(self, client_details):
        &#34;&#34;&#34;returns the list of data source display name and data source name

                Args:

                    client_details      (dict)      --  containing EdiscoveryClient details response

                Returns:

                    list,list       --  Data source display name &amp; Data Source name

                Raises:

                    SDKException:

                            if input is not valid

        &#34;&#34;&#34;
        data_sources_name = []
        data_sources_display_name = []
        if not isinstance(client_details, dict):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;107&#39;)
        if self._app_source and self._app_source == TargetApps.SDG:
            if &#39;dataSources&#39; in client_details:
                data_sources = client_details[&#39;dataSources&#39;]
                for data_source in data_sources:
                    data_sources_name.append(
                        data_source[EdiscoveryConstants.FIELD_DATA_SOURCE_NAME_SEA].lower())
                    data_sources_display_name.append(
                        data_source[EdiscoveryConstants.FIELD_DATA_SOURCE_NAME_SEA].lower())
        elif self._app_source and self._app_source == TargetApps.FSO:
            data_sources_name = self._parse_client_response_for_data_source(
                client_details, EdiscoveryConstants.FIELD_DATA_SOURCE_NAME)
            data_sources_display_name = self._parse_client_response_for_data_source(
                client_details, EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME)
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unknown App source type passed&#34;)
        return data_sources_display_name, data_sources_name

    def _get_data_sources_details(self):
        &#34;&#34;&#34;returns the data sources details associated with ediscovery client

                Args:

                    None

                Return:

                    dict    --  containing data source details

                Raises:

                        SDKException:

                                if failed to get data source details

        &#34;&#34;&#34;
        server_details = self._ediscovery_client_ops.get_ediscovery_client_details()
        return server_details

    def get_datasource_document_count(self, data_source):
        &#34;&#34;&#34;Returns the document count for given data source

                Args:

                    data_source         (str)       --  Name of the data source

                Returns:

                    int --  Document count

                Raises:

                    SDKException:

                            if data source doesn&#39;t exists

                            if failed to get document count

        &#34;&#34;&#34;
        if not isinstance(data_source, str):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if not self.has_data_source(data_source_name=data_source):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Data Source not exists&#34;)

        if self._app_source and self._app_source == TargetApps.SDG:
            for key, value in self._data_sources[data_source.lower()].items():
                if key == EdiscoveryConstants.FIELD_DOCUMENT_COUNT:
                    return int(value)
        else:
            ds_names = self._parse_client_response_for_data_source(
                client_details=self.ediscovery_client_props,
                field_name=EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME)
            docs = self._parse_client_response_for_data_source(
                client_details=self.ediscovery_client_props,
                field_name=EdiscoveryConstants.FIELD_DOCUMENT_COUNT,
                field_type=&#34;int&#34;)
            return docs[ds_names.index(data_source.lower())]

    @property
    def data_sources(self):
        &#34;&#34;&#34;returns the list of data sources display name associated with this client

            Returns:

                list --  Name of data sources

        &#34;&#34;&#34;
        return self._data_source_display_names

    @property
    def client_id(self):
        &#34;&#34;&#34;returns the associated client id

            Returns:

                int --  client id

        &#34;&#34;&#34;
        return self._client_id

    @property
    def client_targetapp(self):
        &#34;&#34;&#34;returns the source client targetapp

            Returns:

                str --  Target app for this data sources

        &#34;&#34;&#34;
        return self._app_source.value

    @property
    def ediscovery_client_props(self):
        &#34;&#34;&#34;Returns the associated client properties

            Returns:

                dict --  containing client properties

        &#34;&#34;&#34;
        return self._ediscovery_client_props

    @property
    def total_documents(self):
        &#34;&#34;&#34;returns the total document counts of all data sources associated with this client

            Returns:

                int --  Total crawled documents from all of these data sources

        &#34;&#34;&#34;
        total_doc = 0
        if self._app_source and self._app_source == TargetApps.SDG:
            for data_source in self._data_sources:
                for key, value in self._data_sources[data_source].items():
                    if key == EdiscoveryConstants.FIELD_DOCUMENT_COUNT:
                        total_doc = total_doc + int(value)
        else:
            total_doc = sum(
                self._parse_client_response_for_data_source(
                    client_details=self.ediscovery_client_props,
                    field_name=EdiscoveryConstants.FIELD_DOCUMENT_COUNT,
                    field_type=&#34;int&#34;))
        return total_doc</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.client_id"><code class="name">var <span class="ident">client_id</span></code></dt>
<dd>
<div class="desc"><p>returns the associated client id</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
client id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2423-L2432" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_id(self):
    &#34;&#34;&#34;returns the associated client id

        Returns:

            int --  client id

    &#34;&#34;&#34;
    return self._client_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.client_targetapp"><code class="name">var <span class="ident">client_targetapp</span></code></dt>
<dd>
<div class="desc"><p>returns the source client targetapp</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
Target app for this data sources</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2434-L2443" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_targetapp(self):
    &#34;&#34;&#34;returns the source client targetapp

        Returns:

            str --  Target app for this data sources

    &#34;&#34;&#34;
    return self._app_source.value</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.data_sources"><code class="name">var <span class="ident">data_sources</span></code></dt>
<dd>
<div class="desc"><p>returns the list of data sources display name associated with this client</p>
<h2 id="returns">Returns</h2>
<p>list &ndash;
Name of data sources</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2412-L2421" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_sources(self):
    &#34;&#34;&#34;returns the list of data sources display name associated with this client

        Returns:

            list --  Name of data sources

    &#34;&#34;&#34;
    return self._data_source_display_names</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.ediscovery_client_props"><code class="name">var <span class="ident">ediscovery_client_props</span></code></dt>
<dd>
<div class="desc"><p>Returns the associated client properties</p>
<h2 id="returns">Returns</h2>
<p>dict &ndash;
containing client properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2445-L2454" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ediscovery_client_props(self):
    &#34;&#34;&#34;Returns the associated client properties

        Returns:

            dict --  containing client properties

    &#34;&#34;&#34;
    return self._ediscovery_client_props</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.total_documents"><code class="name">var <span class="ident">total_documents</span></code></dt>
<dd>
<div class="desc"><p>returns the total document counts of all data sources associated with this client</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Total crawled documents from all of these data sources</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2456-L2477" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def total_documents(self):
    &#34;&#34;&#34;returns the total document counts of all data sources associated with this client

        Returns:

            int --  Total crawled documents from all of these data sources

    &#34;&#34;&#34;
    total_doc = 0
    if self._app_source and self._app_source == TargetApps.SDG:
        for data_source in self._data_sources:
            for key, value in self._data_sources[data_source].items():
                if key == EdiscoveryConstants.FIELD_DOCUMENT_COUNT:
                    total_doc = total_doc + int(value)
    else:
        total_doc = sum(
            self._parse_client_response_for_data_source(
                client_details=self.ediscovery_client_props,
                field_name=EdiscoveryConstants.FIELD_DOCUMENT_COUNT,
                field_type=&#34;int&#34;))
    return total_doc</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.add_fs_data_source"><code class="name flex">
<span>def <span class="ident">add_fs_data_source</span></span>(<span>self, server_name, data_source_name, inventory_name, plan_name, source_type=SourceType.BACKUP, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds file system data source to server</p>
<h2 id="args">Args</h2>
<p>server_name
(str)
&ndash;
Server name which needs to be added</p>
<p>data_source_name
(str)
&ndash;
Name for data source</p>
<p>inventory_name
(str)
&ndash;
Inventory name which needs to be associated</p>
<p>plan_name
(str)
&ndash;
Plan name which needs to be associated with this data source</p>
<p>source_type
(enum)
&ndash;
Source type for crawl (Live source or Backedup)
Refer EdiscoveryConstants.SourceType
Kwargs Arguments:</p>
<pre><code>scan_type           (str)       --  Specifies scan type when source type is for backed up data
                                            Supported values : quick | full

crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

access_node         (str)       --  server name which needs to be used as access node in case
                                            if server to be added is not a commvault client

country_name        (str)       --  country name where server is located (default: USA)

country_code        (str)       --  Country code (ISO 3166 2-letter code)

user_name           (str)       --  User name who has access to UNC path

password            (str)       --  base64 encoded password to access unc path

enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this
</code></pre>
<h2 id="returns">Returns</h2>
<p>obj
&ndash;
Instance of EdiscoveryDataSource class</p>
<p>None
&ndash;
if it is called to create FSO server group</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if plan/inventory/index server doesn't exists

  if failed to add FSO server data source
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1794-L2028" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_fs_data_source(self, server_name, data_source_name, inventory_name, plan_name,
                       source_type=EdiscoveryConstants.SourceType.BACKUP, **kwargs):
    &#34;&#34;&#34;Adds file system data source to server

            Args:

                server_name         (str)       --  Server name which needs to be added

                data_source_name    (str)       --  Name for data source

                inventory_name      (str)       --  Inventory name which needs to be associated

                plan_name           (str)       --  Plan name which needs to be associated with this data source

                source_type         (enum)      --  Source type for crawl (Live source or Backedup)
                                                            Refer EdiscoveryConstants.SourceType

            Kwargs Arguments:

                scan_type           (str)       --  Specifies scan type when source type is for backed up data
                                                            Supported values : quick | full

                crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

                access_node         (str)       --  server name which needs to be used as access node in case
                                                            if server to be added is not a commvault client

                country_name        (str)       --  country name where server is located (default: USA)

                country_code        (str)       --  Country code (ISO 3166 2-letter code)

                user_name           (str)       --  User name who has access to UNC path

                password            (str)       --  base64 encoded password to access unc path

                enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this

            Returns:

                obj     --  Instance of EdiscoveryDataSource class

                None    --  if it is called to create FSO server group

            Raises:

                  SDKException:

                        if plan/inventory/index server doesn&#39;t exists

                        if failed to add FSO server data source
    &#34;&#34;&#34;
    is_commvault_client = False
    is_server_group = False
    if self._app_source_sub_type and self._app_source_sub_type == EdiscoveryConstants.FSO_SERVER_GROUPS:
        is_server_group = True
    if not self._commcell_object.activate.inventory_manager().has_inventory(inventory_name):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid inventory name&#39;)
    if not self._commcell_object.plans.has_plan(plan_name):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid plan name&#39;)
    plan_obj = self._commcell_object.plans.get(plan_name)
    if self._app_source.value not in plan_obj.content_indexing_props[&#39;targetApps&#39;]:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Plan is not marked with targetapp as FSO&#39;)
    inv_obj = self._commcell_object.activate.inventory_manager().get(inventory_name)
    request_json = copy.deepcopy(EdiscoveryConstants.ADD_FS_REQ_JSON)
    request_json[&#39;datasourceId&#39;] = inv_obj.inventory_id
    request_json[&#39;indexServerClientId&#39;] = plan_obj.content_indexing_props[&#39;analyticsIndexServer&#39;].get(&#39;clientId&#39;, 0)
    request_json[&#39;datasources&#39;][0][&#39;datasourceName&#39;] = data_source_name
    if self._app_source == TargetApps.SDG:
        request_json[&#39;clientId&#39;] = self._client_id  # project source client id
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;caconfig&#34;,
            &#34;propertyValue&#34;: &#34;[{\&#34;task\&#34;:\&#34;EntityExtractionFields\&#34;,\&#34;arguments\&#34;:[\&#34;content\&#34;]}]&#34;
        })
    # find out whether given server is commvault client or not to decide further
    inventory_resp = None
    scan_type = kwargs.get(&#39;scan_type&#39;, &#39;quick&#39;)
    if not is_server_group:
        is_commvault_client = self._commcell_object.clients.has_client(server_name)
        if not is_commvault_client:
            if (&#39;access_node&#39; not in kwargs or &#39;user_name&#39; not in kwargs or &#39;password&#39; not in kwargs):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Access node information is missing&#34;)
            if not self._commcell_object.clients.has_client(kwargs.get(&#34;access_node&#34;)):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Access node client is not present&#34;)
        inventory_resp = inv_obj.data_source.ds_handlers.get(
            EdiscoveryConstants.FS_SERVER_HANDLER_NAME).get_handler_data(
            handler_filter=f&#34;q=(name_idx:{server_name})&amp;rows=1&#34;)
        if inventory_resp[&#39;numFound&#39;] != 1:
            raise SDKException(
                &#39;EdiscoveryClients&#39;,
                &#39;102&#39;,
                &#39;Multiple server with same name exists or no server exists in inventory&#39;)
        inventory_resp = inventory_resp[&#39;docs&#39;][0]
    # set common properties
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;enablemonitoring&#34;,
        &#34;propertyValue&#34;: kwargs.get(&#39;enable_monitoring&#39;, &#34;false&#34;).lower()
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;countryCode&#34;,
        &#34;propertyValue&#34;: kwargs.get(&#39;country_code&#39;, &#39;US&#39;)
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;co&#34;,
        &#34;propertyValue&#34;: kwargs.get(&#39;country_name&#39;, &#39;United States&#39;)
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;contentid&#34;,
        &#34;propertyValue&#34;: inventory_resp[&#39;contentid&#39;] if not is_server_group else server_name
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;clientdisplayname&#34;,
        &#34;propertyValue&#34;: server_name
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;dcplanid&#34;,
        &#34;propertyValue&#34;: str(plan_obj.plan_id)
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;name&#34;,
        &#34;propertyValue&#34;: inventory_resp[&#39;name&#39;] if not is_server_group else server_name
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;operatingSystem&#34;,
        &#34;propertyValue&#34;: inventory_resp[&#39;operatingSystem&#39;] if not is_server_group else &#34;&#34;
    })
    if is_commvault_client:
        if &#39;access_node&#39; not in kwargs:
            del request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;]
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;ClientId&#34;,
            &#34;propertyValue&#34;: str(self._commcell_object.clients.get(server_name).client_id)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;ContentIndexingStatus&#34;,
            &#34;propertyValue&#34;: str(inventory_resp[&#39;ContentIndexingStatus&#39;])
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;BackedupStatus&#34;,
            &#34;propertyValue&#34;: str(inventory_resp[&#39;BackedupStatus&#39;])
        })
    elif is_server_group:
        del request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;]
        del request_json[&#39;indexServerClientId&#39;]
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;ClientGroupId&#34;,
            &#34;propertyValue&#34;: str(self._commcell_object.client_groups.get(server_name).clientgroup_id)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;ContentIndexingStatus&#34;,
            &#34;propertyValue&#34;: str(0)
        })
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;BackedupStatus&#34;,
            &#34;propertyValue&#34;: str(0)
        })

    # set crawl type and source type related params
    if source_type.value == EdiscoveryConstants.SourceType.BACKUP.value:
        if scan_type == &#39;quick&#39; and self._app_source == TargetApps.FSO:
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;crawltype&#34;,
                &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.FILE_LEVEL_ANALYTICS.value)
            })
        else:
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;crawltype&#34;,
                &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.BACKUP_V2.value)
            })
    else:
        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;crawltype&#34;,
            &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.LIVE.value)
        })
        if not is_commvault_client or &#39;access_node&#39; in kwargs:
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;username&#34;,
                &#34;propertyValue&#34;: kwargs.get(&#39;user_name&#39;, &#39;&#39;)
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;password&#34;,
                &#34;propertyValue&#34;: kwargs.get(&#39;password&#39;, &#39;&#39;)
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;domainName&#34;,
                &#34;propertyValue&#34;: inventory_resp[&#39;domainName&#39;]
            })
            request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
                &#34;propertyName&#34;: &#34;dNSHostName&#34;,
                &#34;propertyValue&#34;: inventory_resp[&#39;dNSHostName&#39;]
            })
            request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;][0][&#39;clientId&#39;] = int(
                self._commcell_object.clients.get(kwargs.get(&#39;access_node&#39;)).client_id)
            request_json[&#39;datasources&#39;][0][&#39;accessNodes&#39;][0][&#39;clientName&#39;] = kwargs.get(&#39;access_node&#39;)

        request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
            &#34;propertyName&#34;: &#34;includedirectoriespath&#34;,
            &#34;propertyValue&#34;: &#39;,&#39;.join(kwargs.get(&#39;crawl_path&#39;, []))
        })

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_CREATE_DATA_SOURCE, request_json)
    if flag:
        if response.json() and &#39;collections&#39; in response.json():
            collection = response.json()[&#39;collections&#39;][0]
            if &#39;datasources&#39; in collection:
                data_source = collection[&#39;datasources&#39;][0]
                # when add data source is called for new server then handle client id accordingly
                if is_server_group:
                    # for server group, no need to refresh data sources details as we go via Server by server only
                    return
                if not self._client_id:
                    if is_commvault_client:
                        self._client_id = inventory_resp[&#39;ClientId&#39;]
                    else:
                        self._commcell_object.clients.refresh()
                        all_clients = self._commcell_object.clients.all_clients
                        for client_name, _ in all_clients.items():
                            if client_name.lower().startswith(f&#34;{data_source_name.lower()}_&#34;):
                                self._client_id = self._commcell_object.clients.get(client_name).client_id
                                break
                    self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)
                self.refresh()
                return EdiscoveryDatasource(
                    self._commcell_object,
                    data_source[&#39;datasourceId&#39;],
                    EdiscoveryConstants.DATA_SOURCE_TYPES[5], client_id=self._client_id, app_type=self._app_source)
        if response.json() and &#39;error&#39; in response.json():
            error = response.json()[&#39;error&#39;]
            if &#39;errorCode&#39; in error and error[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Creation of data source failed with error - {error[&#39;errorCode&#39;]}&#34;)
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;115&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.add_o365_sdg_data_source"><code class="name flex">
<span>def <span class="ident">add_o365_sdg_data_source</span></span>(<span>self, server_name, data_source_name, plan_name, datasource_type=ClientType.ONEDRIVE, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds Office365 SDG data source to a project</p>
<h2 id="args">Args</h2>
<p>server_name
(str)
&ndash;
Server name which needs to be added</p>
<p>data_source_name
(str)
&ndash;
Name for data source</p>
<p>plan_name
(str)
&ndash;
Plan name which needs to be associated with this data source</p>
<p>datasource_type
(str)
&ndash;
Type of O365 SDG datasource (Exchange/OneDrive)
Kwargs Arguments:</p>
<pre><code>country_name        (str)       --  country name where server is located (default: USA)

country_code        (str)       --  Country code (ISO 3166 2-letter code)

users               (list)      --  List of users/mailboxes to be added. If empty, all users would be added
</code></pre>
<h2 id="returns">Returns</h2>
<p>obj
&ndash;
Instance of EdiscoveryDataSource class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if plan doesn't exists
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2030-L2144" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_o365_sdg_data_source(self, server_name, data_source_name, plan_name,
                             datasource_type=EdiscoveryConstants.ClientType.ONEDRIVE, **kwargs):
    &#34;&#34;&#34;Adds Office365 SDG data source to a project

            Args:
                server_name         (str)       --  Server name which needs to be added

                data_source_name    (str)       --  Name for data source

                plan_name           (str)       --  Plan name which needs to be associated with this data source

                datasource_type     (str)       --  Type of O365 SDG datasource (Exchange/OneDrive)

            Kwargs Arguments:

                country_name        (str)       --  country name where server is located (default: USA)

                country_code        (str)       --  Country code (ISO 3166 2-letter code)

                users               (list)      --  List of users/mailboxes to be added. If empty, all users would be added

            Returns:

                obj     --  Instance of EdiscoveryDataSource class

            Raises:

                  SDKException:

                        if plan doesn&#39;t exists
    &#34;&#34;&#34;
    if not self._commcell_object.plans.has_plan(plan_name):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Invalid plan name&#39;)
    plan_obj = self._commcell_object.plans.get(plan_name)
    if self._app_source.value not in plan_obj.content_indexing_props[&#39;targetApps&#39;]:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Plan is not marked with targetapp as SDG&#39;)
    o365_client = self._commcell_object.clients.get(server_name)
    backupset_id, subclient_id = self._get_o365_backupset_subclient_id(o365_client, datasource_type)
    request_json = copy.deepcopy(EdiscoveryConstants.ADD_O365_SDG_BACKED_UP_DS_REQ)
    request_json[&#39;clientId&#39;] = self._client_id  # project source client id
    if plan_obj.content_indexing_props.get(&#39;analyticsIndexServer&#39;, {}).get(&#39;clientId&#39;, None) is not None:
        # Only for software datasource creation, we will need this index server client ID to be set
        request_json[&#39;indexServerClientId&#39;] = plan_obj.content_indexing_props[&#39;analyticsIndexServer&#39;].get(&#39;clientId&#39;, 0)
    request_json[&#39;datasources&#39;][0][&#39;datasourceType&#39;] = datasource_type.value
    request_json[&#39;datasources&#39;][0][&#39;datasourceName&#39;] = data_source_name
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;caconfig&#34;,
        &#34;propertyValue&#34;: &#34;[{\&#34;task\&#34;:\&#34;EntityExtractionFields\&#34;,\&#34;arguments\&#34;:[\&#34;content\&#34;]}]&#34;
    })

    # set common properties
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;countryCode&#34;,
        &#34;propertyValue&#34;: kwargs.get(&#39;country_code&#39;, &#39;US&#39;)
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;co&#34;,
        &#34;propertyValue&#34;: kwargs.get(&#39;country_name&#39;, &#39;United States&#39;)
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;clientdisplayname&#34;,
        &#34;propertyValue&#34;: data_source_name
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;name&#34;,
        &#34;propertyValue&#34;: data_source_name
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;dcplanid&#34;,
        &#34;propertyValue&#34;: str(plan_obj.plan_id)
    })

    # set crawl type and source type related params
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;crawltype&#34;,
        &#34;propertyValue&#34;: str(EdiscoveryConstants.CrawlType.BACKUP_V2.value)
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;dNSHostName&#34;,
        &#34;propertyValue&#34;: server_name
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;ClientId&#34;,
        &#34;propertyValue&#34;: str(o365_client.client_id)
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;CAppBackupSetId&#34;,
        &#34;propertyValue&#34;: str(backupset_id)
    })
    request_json[&#39;datasources&#39;][0][&#39;properties&#39;].append({
        &#34;propertyName&#34;: &#34;backedupsubclientids&#34;,
        &#34;propertyValue&#34;: str(subclient_id)
    })

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_CREATE_DATA_SOURCE, request_json)
    if flag:
        if response.json() and &#39;collections&#39; in response.json():
            collection = response.json()[&#39;collections&#39;][0]
            if &#39;datasources&#39; in collection:
                data_source = collection[&#39;datasources&#39;][0]
                # when add data source is called for new server then handle client id accordingly
                return EdiscoveryDatasource(
                    self._commcell_object,
                    data_source[&#39;datasourceId&#39;],
                    EdiscoveryConstants.DATA_SOURCE_TYPES[datasource_type.value], client_id=self._client_id, app_type=self._app_source)
        if response.json() and &#39;error&#39; in response.json():
            error = response.json()[&#39;error&#39;]
            if &#39;errorCode&#39; in error and error[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Creation of data source failed with error - {error[&#39;errorCode&#39;]}&#34;)
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;115&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, data_source_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the given data source from client</p>
<h2 id="args">Args</h2>
<p>data_source_name
(str)
&ndash;
Datasource name</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to find given data source in this client

    if failed to delete the data source
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2174-L2210" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, data_source_name):
    &#34;&#34;&#34;Deletes the given data source from client

                    Args:

                        data_source_name        (str)       --  Datasource name

                    Returns:

                        None

                    Raises:

                        SDKException:

                                if failed to find given data source in this client

                                if failed to delete the data source

    &#34;&#34;&#34;
    if not self.has_data_source(data_source_name):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;108&#39;)
    flag, response = self._cvpysdk_object.make_request(
        &#39;DELETE&#39;, self._API_DELETE % (self.get(data_source_name).data_source_id, self._client_id)
    )
    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            if response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Failed to Delete DataSource with error [{response.json().get(&#39;errorMessage&#39;,&#39;&#39;)}]&#34;)
            self.refresh()
        else:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;111&#39;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, data_source_name)</span>
</code></dt>
<dd>
<div class="desc"><p>returns EdiscoveryDataSource class object for given data source name</p>
<h2 id="args">Args</h2>
<p>data_source_name
(str)
&ndash;
Datasource name</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of EdiscoveryDataSource class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to find given data source in this client

    if input is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2234-L2262" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, data_source_name):
    &#34;&#34;&#34;returns EdiscoveryDataSource class object for given data source name

            Args:

                data_source_name        (str)       --  Datasource name

            Returns:

                obj --  Instance of EdiscoveryDataSource class

            Raises:

                SDKException:

                        if failed to find given data source in this client

                        if input is not valid

    &#34;&#34;&#34;
    if not isinstance(data_source_name, str):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    if not self.has_data_source(data_source_name):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;108&#39;)
    ds_props = self._data_sources[data_source_name.lower()]
    return EdiscoveryDatasource(commcell_object=self._commcell_object,
                                data_source_id=int(ds_props[EdiscoveryConstants.FIELD_DATA_SOURCE_ID]),
                                data_source_type=ds_props[EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE],
                                app_type=self._app_source, client_id=self._client_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.get_datasource_document_count"><code class="name flex">
<span>def <span class="ident">get_datasource_document_count</span></span>(<span>self, data_source)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the document count for given data source</p>
<h2 id="args">Args</h2>
<p>data_source
(str)
&ndash;
Name of the data source</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Document count</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if data source doesn't exists

    if failed to get document count
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2373-L2410" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_datasource_document_count(self, data_source):
    &#34;&#34;&#34;Returns the document count for given data source

            Args:

                data_source         (str)       --  Name of the data source

            Returns:

                int --  Document count

            Raises:

                SDKException:

                        if data source doesn&#39;t exists

                        if failed to get document count

    &#34;&#34;&#34;
    if not isinstance(data_source, str):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    if not self.has_data_source(data_source_name=data_source):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Data Source not exists&#34;)

    if self._app_source and self._app_source == TargetApps.SDG:
        for key, value in self._data_sources[data_source.lower()].items():
            if key == EdiscoveryConstants.FIELD_DOCUMENT_COUNT:
                return int(value)
    else:
        ds_names = self._parse_client_response_for_data_source(
            client_details=self.ediscovery_client_props,
            field_name=EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME)
        docs = self._parse_client_response_for_data_source(
            client_details=self.ediscovery_client_props,
            field_name=EdiscoveryConstants.FIELD_DOCUMENT_COUNT,
            field_type=&#34;int&#34;)
        return docs[ds_names.index(data_source.lower())]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.has_data_source"><code class="name flex">
<span>def <span class="ident">has_data_source</span></span>(<span>self, data_source_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks whether given data source exists in this client or not</p>
<h2 id="args">Args</h2>
<p>data_source_name
(str)
&ndash;
Datasource name</p>
<h2 id="returns">Returns</h2>
<p>bool
&ndash;
True if exists else false</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to find given data source in this client
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2212-L2232" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_data_source(self, data_source_name):
    &#34;&#34;&#34;Checks whether given data source exists in this client or not

            Args:

                data_source_name        (str)       --  Datasource name

            Returns:

                bool    --  True if exists else false

            Raises:

                SDKException:

                        if failed to find given data source in this client

    &#34;&#34;&#34;
    if not isinstance(data_source_name, str):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    return self._data_source_display_names and data_source_name.lower() in self._data_source_display_names</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the data sources associated with edisocvery client</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L1777-L1792" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the data sources associated with edisocvery client&#34;&#34;&#34;
    if not self._client_id:
        return
    if self._app_source and self._app_source == TargetApps.SDG:
        self._ediscovery_client_props = self._ediscovery_client_ops.get_ediscovery_project_details()
        self._data_source_display_names, self._data_source_names = self._get_data_source_names(
            self._ediscovery_client_props)
        self._data_sources = self._get_data_sources_stats()
    elif self._app_source and self._app_source == TargetApps.FSO:
        self._ediscovery_client_props = self._get_data_sources_details()
        self._data_source_display_names, self._data_source_names = self._get_data_source_names(
            self._ediscovery_client_props)
        self._data_sources = self._get_data_source_properties(self._ediscovery_client_props)
    else:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unknown App source type passed&#34;)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource"><code class="flex name class">
<span>class <span class="ident">EdiscoveryDatasource</span></span>
<span>(</span><span>commcell_object, data_source_id, data_source_type, client_id, app_type=TargetApps.FSO)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent single datasource associated with ediscovery client</p>
<p>Initializes an instance of the EdiscoveryDataSource class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>data_source_id
(int)
&ndash;
Data source id</p>
<p>data_source_type
(int/str)
&ndash;
Data Source type (Example : 5 for file)
Refer to EdiscoveryConstants class in activateapps\constants.py</p>
<p>client_id
(int)
&ndash;
client id where this data source belongs to</p>
<p>app_type
(enum)
&ndash;
Specifies which app type these data sources belongs too
Default:FSO</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the EdiscoveryDataSource class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2480-L3407" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class EdiscoveryDatasource():
    &#34;&#34;&#34;Class to represent single datasource associated with ediscovery client&#34;&#34;&#34;

    def __init__(self, commcell_object, data_source_id, data_source_type, client_id, app_type=TargetApps.FSO):
        &#34;&#34;&#34;Initializes an instance of the EdiscoveryDataSource class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                data_source_id      (int)       --  Data source id

                data_source_type    (int/str)   --  Data Source type (Example : 5 for file)
                                                        Refer to EdiscoveryConstants class in activateapps\\constants.py

                client_id           (int)       --  client id where this data source belongs to

                app_type            (enum)      --  Specifies which app type these data sources belongs too
                                                        Default:FSO

            Returns:
                object  -   instance of the EdiscoveryDataSource class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._data_source_name = None
        self._data_source_actual_name = None
        self._data_source_id = None
        self._data_source_type = None
        self._data_source = None
        self._data_source_props = None
        self._client_id = client_id
        self._collection_client_id = None
        self._core_name = None
        self._computed_core_name = None
        self._cloud_id = None
        self._core_id = None
        self._crawl_type = None
        self._dc_plan_id = None
        self._data_source_entity_id = 132
        self._app_type = app_type
        self._API_DATA_SOURCE = self._services[&#39;EDISCOVERY_DATA_SOURCES&#39;]
        self._API_SEARCH = self._services[&#39;EDISCOVERY_DYNAMIC_FEDERATED&#39;]
        self._API_ACTIONS = self._services[&#39;EDISCOVERY_REVIEW_ACTIONS&#39;]
        self._API_ACTIONS_WITH_REQUEST = self._services[&#39;EDISCOVERY_REVIEW_ACTIONS_WITH_REQUEST&#39;]
        self._jobs = self._commcell_object.job_controller
        self._data_source_id = data_source_id
        if isinstance(data_source_type, int):
            self._data_source_type = EdiscoveryConstants.DATA_SOURCE_TYPES.get(data_source_type)
        else:
            self._data_source_type = data_source_type
        self.refresh()
        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_data_source_properties(self):
        &#34;&#34;&#34;returns the data source properties for this data source

            Args:

                None

            Returns:

                Dict    --  Containing data source details

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._API_DATA_SOURCE %
            (self._data_source_id, self._data_source_type))
        if flag:
            if response.json() and &#39;collections&#39; in response.json():
                collection = response.json()[&#39;collections&#39;][0]
                self._collection_client_id = collection.get(&#39;clientId&#39;)
                self._core_name = collection.get(&#39;coreName&#39;)
                self._computed_core_name = collection.get(&#39;computedCoreName&#39;)
                self._cloud_id = collection.get(&#39;cloudId&#39;)
                self._core_id = collection.get(&#39;coreId&#39;)
                ds_list = collection.get(&#39;datasources&#39;, [])
                if len(ds_list) == 1:
                    self._data_source_props = ds_list[0].get(&#39;properties&#39;, [])
                    # fetch crawl type from above properties fetched.
                    self._crawl_type = self._get_property_value(property_name=EdiscoveryConstants.FIELD_CRAWL_TYPE)
                    self._dc_plan_id = self._get_property_value(property_name=EdiscoveryConstants.FIELD_DC_PLAN_ID)
                    self._data_source_name = ds_list[0].get(&#39;displayName&#39;, &#39;NA&#39;)
                    self._data_source_actual_name = ds_list[0].get(&#39;datasourceName&#39;, &#39;NA&#39;)
                return collection
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;110&#39;)
        self._response_not_success(response)

    def _get_property_value(self, property_name):
        &#34;&#34;&#34;Returns the property value for property name

            Args:

                property_name       (str)       --  Name of property

            Returns:
                str --  value of property

        &#34;&#34;&#34;
        for prop in self.data_source_props:
            if &#39;propertyName&#39; in prop:
                if prop[&#39;propertyName&#39;].lower() == property_name.lower():
                    return prop[&#39;propertyValue&#39;]
        return &#34;&#34;

    def refresh(self):
        &#34;&#34;&#34;refresh the data source properties&#34;&#34;&#34;
        self._data_source = self._get_data_source_properties()

    def tag_items(
            self,
            tags,
            document_ids=None,
            ops_type=1,
            create_review=False,
            reviewers=None,
            approvers=None,
            req_name=None):
        &#34;&#34;&#34;Applies given tag to documents

            Args:

                tags            (list)      --  list of tags names which needs to be applied
                                                        Format : Tagset\\TagName
                                                        Example : DiscoveryEntity\\American

                document_ids    (list)      --  list of document content id&#39;s which needs to be tagged

                ops_type        (int)       --  Denotes operation type for tagging  (1-Add or 2-Delete)
                                                        Default : 1(Add)

                create_review   (bool)      --  Specifies whether to create review request for this tagging or not
                                                        Default:False

                reviewers       (list)      --  List of review users

                approvers       (list)      --  List of approver users

                req_name        (str)       --  Request name

            Returns:

                None if it is tagging with review request

                jobid (str) -- if it is bulk operation of tagging all items without review request

            Raises:

                SDKException:

                        if tag name doesn&#39;t exists in commcell

                        if failed to apply tag

                        if response is empty

                        if data source doesn&#39;t belongs to FSO app
        &#34;&#34;&#34;
        if self._app_type.value != TargetApps.FSO.value:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Tagging is supported only for FSO app&#34;)
        if not isinstance(tags, list):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        query = &#34;&#34;
        request_json = None
        tag_guids = []
        api = self._API_ACTIONS
        if create_review:
            api = self._API_ACTIONS_WITH_REQUEST

        tag_mgr = self._commcell_object.activate.entity_manager(EntityManagerTypes.TAGS)
        for tag in tags:
            tag_split = tag.split(&#34;\\\\&#34;)
            if not tag_mgr.has_tag_set(tag_set_name=tag_split[0]):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unable to find tagset in the commcell&#34;)
            tag_set_obj = tag_mgr.get(tag_split[0])
            if not tag_set_obj.has_tag(tag_split[1]):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unable to find tag in the tagset&#34;)
            tag_obj = tag_set_obj.get(tag_split[1])
            if not create_review:
                tag_guids.append({&#34;id&#34;: tag_obj.guid})
            else:
                tag_guids.append(tag_obj.guid)

        if not create_review:
            if document_ids:
                for doc in document_ids:
                    query = query + f&#34;(contentid:{doc}) OR &#34;
                last_char_index = query.rfind(&#34; OR &#34;)
                query = query[:last_char_index]
            else:
                query = &#34;*:*&#34;
            search_params = self._ediscovery_client_ops.form_search_params(
                query=query, key=&#34;name&#34;, params={&#34;rows&#34;: &#34;0&#34;})
            tag_request = copy.deepcopy(EdiscoveryConstants.TAGGING_ITEMS_REQUEST)
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_TAG_REQ_JSON)
            if ops_type != 1:
                tag_request[&#39;opType&#39;] = &#34;DELETE&#34;
            if not document_ids:  # bulk request
                tag_request[&#39;isAsync&#39;] = True
            tag_request[&#39;entityIds&#39;].append(self.data_source_id)
            tag_request[&#39;searchRequest&#39;] = search_params
            tag_request[&#39;tags&#39;] = tag_guids
            tag_request[&#39;dsType&#39;] = self.data_source_type_id
            request_json[&#39;taggingRequest&#39;] = tag_request
            request_json[&#39;remActionRequest&#39;][&#39;dataSourceId&#39;] = self.data_source_id
            if not document_ids:
                request_json[&#39;remActionRequest&#39;][&#39;isBulkOperation&#39;] = True

        else:
            request_json = copy.deepcopy(EdiscoveryConstants.TAGGING_ITEMS_REVIEW_REQUEST)
            if ops_type != 1:
                request_json[&#39;taggingInformation&#39;][&#39;opType&#39;] = &#34;DELETE&#34;
            request_json[&#39;files&#39;] = json.dumps(self._form_files_list(
                document_ids=document_ids,
                attr_list=EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET[self.data_source_type_id]))
            request_json[&#39;taggingInformation&#39;][&#39;tagIds&#39;] = tag_guids
            request_json[&#39;options&#39;] = str(
                self._form_request_options(
                    reviewers=reviewers,
                    approvers=approvers,
                    document_ids=document_ids,
                    req_name=req_name if req_name else f&#34;{self.data_source_name}_tag_{int(time.time())}&#34;))

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, api, request_json
        )
        if flag:
            if response.json():
                response = response.json()
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Tagging failed with error : {response.get(&#39;errorMessage&#39;, &#39;&#39;)}&#34;)
                else:
                    if not create_review:
                        # tagging without review. return the job id
                        return response[&#39;jobId&#39;]
                    return
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;113&#39;)
        self._response_not_success(response)

    def get_job_history(self, limit=50, lookup_time=2160):
        &#34;&#34;&#34;returns the job history details for this data source

            Args:

                limit       (int)           --  No of jobs to return (default: 50 rows)

                lookup_time (int)           --  list of jobs to be retrieved which are specified
                    hours older

                            default: 2160 hours (last 90 days)

        &#34;&#34;&#34;
        return self._jobs.finished_jobs(lookup_time=lookup_time,
                                        limit=limit,
                                        entity={&#34;dataSourceId&#34;: self.data_source_id})

    def get_active_jobs(self, limit=50, lookup_time=2160):
        &#34;&#34;&#34;returns the active jobs details for this data source

            Args:

                limit       (int)           --  No of jobs to return (default: 50 rows)

                lookup_time (int)           --  list of jobs to be retrieved which are started within specified
                    hours older

                            default: 2160 hours (last 90 days)

            Returns:

                    dict    -   dictionary consisting of the job IDs matching the given criteria
                                as the key, and their details as its value

        &#34;&#34;&#34;
        return self._jobs.active_jobs(lookup_time=lookup_time,
                                      limit=limit,
                                      entity={&#34;dataSourceId&#34;: self.data_source_id})

    def wait_for_export(self, token, wait_time=60, download=True, download_location=os.getcwd()):
        &#34;&#34;&#34;Waits for Export to CSV to finish

                Args:

                    wait_time           (int)       --  time interval to wait for job completion in Mins
                                                            Default : 60Mins

                    token               (str)       --  Export to CSV token GUID

                    download            (bool)      --  specify whether to download exported file or not

                    download_location   (str)       --  Path where to download exported csv file
                                                                Default: Current working dir

                Return:

                    str     -- Download GUID for exported CSV file if download=false
                               File path containing exported csv file if download=true

                Raises:

                    SDKException:

                            if Export job fails

                            if timeout happens

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.wait_for_export(token=token,
                                                           wait_time=wait_time,
                                                           download=download,
                                                           download_location=download_location)

    def export(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do export to CSV on data

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - Exports all docs)

                                                    Example :

                                                        1) Size filter --&gt; Size:[10 TO 1024]
                                                        2) File name filter --&gt; FileName_idx:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                str     --  export operation token


            Raises:

                SDKException:

                        if failed to perform export

        &#34;&#34;&#34;
        if not attr_list:
            if self.data_source_type == EdiscoveryConstants.DATA_SOURCE_TYPES[5]:
                attr_list = EdiscoveryConstants.FS_DEFAULT_EXPORT_FIELDS
        return self._ediscovery_client_ops.export(criteria=criteria, attr_list=attr_list,
                                                  params=params)

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on data source and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        1) Size filter --&gt; Size:[10 TO 1024]
                                                        2) File name filter --&gt; FileName_idx:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                list(dict),dict    --  Containing document details &amp; facet details(if any)

            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list,
                                                  params=params)

    def _form_request_options(self, req_name, reviewers, approvers, document_ids=None):
        &#34;&#34;&#34;Returns the options for review request

            Args:

                req_name        (str)       --  Request Name

                reviewers       (list)      --  List of review users

                approvers       (list)      --  List of approver users

                document_ids    (list)      --  list of document id&#39;s
                                                    Default:None

            Returns:

                dict        --  Containing options

            Raises:

                SDKException:

                        if failed to get document details

                        if failed to find user details for reviewers/approvers
        &#34;&#34;&#34;
        options = {
            &#34;Name&#34;: req_name,
            &#34;DatasetId&#34;: str(self.data_source_id),
            &#34;DatasetType&#34;: &#34;SEA_DATASOURCE_ENTITY&#34;,
            &#34;DatasetName&#34;: self.data_source_name,
            &#34;CreatedFrom&#34;: &#34;FSO&#34; if self._app_type.value == TargetApps.FSO.value else &#34;SDG&#34;,
            &#34;ClientId&#34;: str(self._client_id)
        }
        if document_ids:
            query = &#34;&#34;
            for doc in document_ids:
                query = query + f&#34;(contentid:{doc}) OR &#34;
            last_char_index = query.rfind(&#34; OR &#34;)
            query = query[:last_char_index]
            count, docs, _ = self.search(
                criteria=query, attr_list=EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET[self.data_source_type_id])
            if len(docs) != len(document_ids):
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    &#34;Unable to find document details from given list of document id&#34;)
            file_names = []
            for doc in docs:
                file_names.append(doc[&#39;FileName&#39;])
            options[&#39;ReviewCriteria&#39;] = json.dumps({
                &#34;Files&#34;: file_names
            })
        else:
            options[&#39;ReviewCriteria&#39;] = json.dumps({})
        # reviewer
        reviewers_list = []
        for user in reviewers:
            if not self._commcell_object.users.has_user((user)):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Unable to find reviewer user : {user}&#34;)
            user_obj = self._commcell_object.users.get(user)
            reviewers_list.append({&#34;id&#34;: user_obj.user_id,
                                   &#34;name&#34;: user_obj.user_name
                                   })
        options[&#39;Reviewers&#39;] = str(reviewers_list)

        # approvers
        approvers_list = []
        for user in approvers:
            if not self._commcell_object.users.has_user((user)):
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;Unable to find approver user : {user}&#34;)
            user_obj = self._commcell_object.users.get(user)
            approvers_list.append({&#34;id&#34;: user_obj.user_id,
                                   &#34;name&#34;: user_obj.user_name
                                   })
        options[&#39;Approvers&#39;] = str(approvers_list)
        return options

    def _form_files_list(self, document_ids, attr_list):
        &#34;&#34;&#34;returns the list of dict containing files details

                Args:

                    document_ids        (list)      --  list of document id&#39;s

                    attr_list           (set)       --  Set of fields needed to be fetched for document id

                Returns:

                      list(dict)      --  Containing file details

                Raises:

                    SDKException:

                        if failed to get document details

        &#34;&#34;&#34;
        query = &#34;&#34;
        files_list = []
        for doc in document_ids:
            query = query + f&#34;(contentid:{doc}) OR &#34;
        last_char_index = query.rfind(&#34; OR &#34;)
        query = query[:last_char_index]
        count, docs, _ = self.search(criteria=query, attr_list=attr_list)
        if len(docs) != len(document_ids):
            raise SDKException(
                &#39;EdiscoveryClients&#39;,
                &#39;102&#39;,
                &#34;Unable to find document details from given list of document id&#34;)
        for doc in docs:
            doc_dict = {
                &#34;file&#34;: doc[&#39;Url&#39;],
                &#34;dsid&#34;: str(
                    self.data_source_id),
                &#34;contentid&#34;: doc[&#39;contentid&#39;],
                &#34;CreatedTime&#34;: doc[&#39;CreatedTime&#39;],
                &#34;ClientId&#34;: doc[&#39;ClientId&#39;],
                &#34;dstype&#34;: self.data_source_type_id}
            files_list.append(doc_dict)
        return files_list

    def review_action(self, action_type, reviewers=None, approvers=None, document_ids=None, req_name=None, **kwargs):
        &#34;&#34;&#34;do review action on documents

                Args:

                    action_type         (enum)      --  Type of action to be taken
                                                        Refer to EdiscoveryConstants.ReviewActions

                    document_ids        (list)      --  list of document id&#39;s
                                                            Default:None (means all docs)

                    reviewers           (list)      --  List of review users

                    approvers           (list)      --  List of approver users

                    req_name            (str)       --  Request name

                kwargs arguments:

                    backup_delete       (bool)      --  Specifies whether to delete document from backup or not

                    destination         (str)       --  Destination UNC path for move operation

                    user_name           (str)       --  Username to access share path

                    password            (str)       --  Password for user in base64 encoded

                    create_review       (bool)      --  speicifies whether to create review or not for this action
                                                            (For Delete &amp; Move, it is TRUE always)

                    retain_month        (int)       --  no of months to set as retention

                    ignore_all_risks    (bool)      --  specifies whether it has to be ignore risk fully or not

                    ignore_risk_type    (list)      --  list of risks which needs to be ignored
                                                            Refer to EDiscoveryConstants.RiskTypes

                Returns:

                    None -- if create_review is true

                    job id -- if create_review is false

                Raises:

                    SDKException:

                        if action type is not valid

                        if failed to do review action on documents

                        if document id&#39;s not found
        &#34;&#34;&#34;
        if not isinstance(action_type, EdiscoveryConstants.ReviewActions):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
        if self._app_type.value == TargetApps.FSO.value and \
                action_type.value not in EdiscoveryConstants.REVIEW_ACTION_FSO_SUPPORTED:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;{action_type.value} is not supported for FSO app&#34;)
        if self._app_type.value == TargetApps.SDG.value and \
                action_type.value not in EdiscoveryConstants.REVIEW_ACTION_SDG_SUPPORTED:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;{action_type.value} is not supported for SDG app&#34;)
        attr_list = None
        api = self._API_ACTIONS
        create_review = kwargs.get(&#39;create_review&#39;, False)
        if action_type == EdiscoveryConstants.ReviewActions.DELETE or \
                action_type == EdiscoveryConstants.ReviewActions.MOVE:
            # For Delete &amp; Move, review request is compulsory
            create_review = True
        if create_review:
            if not reviewers or not approvers:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Reviewers/Approvers missing in input&#39;)
        if self.data_source_type_id not in EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Not supported data source for review action&#34;)
        attr_list = EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET[self.data_source_type_id]
        request_json = None
        # For Delete &amp; Move, review request is compulsory so non-review case is not handled for this block
        if action_type.value == EdiscoveryConstants.ReviewActions.DELETE.value:
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_DELETE_REQ_JSON)
            request_json[&#39;deleteFromBackup&#39;] = kwargs.get(&#34;backup_delete&#34;, False)
        elif action_type.value == EdiscoveryConstants.ReviewActions.MOVE.value:
            if &#39;destination&#39; not in kwargs or &#39;user_name&#39; not in kwargs or &#39;password&#39; not in kwargs:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Required params missing for move operation&#34;)
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_MOVE_REQ_JSON)
            request_json[&#39;newDestination&#39;] = kwargs.get(&#34;destination&#34;, &#39;&#39;)
            request_json[&#39;username&#39;] = kwargs.get(&#34;user_name&#34;, &#39;&#39;)
            request_json[&#39;password&#39;] = kwargs.get(&#34;password&#34;, &#39;&#39;)
        elif action_type.value == EdiscoveryConstants.ReviewActions.RETENTION.value:
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_SET_RETENTION_REQ_JSON)
            if &#39;retain_month&#39; not in kwargs:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Retention month input missing for this operation&#39;)
            if not create_review:
                request_json[&#39;setRetentionReq&#39;][&#39;numOfMonthsRemain&#39;] = kwargs.get(&#39;retain_month&#39;)
                request_json[&#39;remActionRequest&#39;][&#39;dataSourceId&#39;] = self._data_source_id
            else:
                request_json[&#39;numOfMonthsRemain&#39;] = kwargs.get(&#39;retain_month&#39;)
                request_json[&#39;dataSourceId&#39;] = self._data_source_id
                # delete unwanted keys as it is review request
                if &#39;remActionRequest&#39; in request_json:
                    del request_json[&#39;remActionRequest&#39;]
                if &#39;setRetentionReq&#39; in request_json:
                    del request_json[&#39;setRetentionReq&#39;]
        elif action_type.value == EdiscoveryConstants.ReviewActions.IGNORE.value:
            request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_IGNORE_FILES_REQ_JSON)
            ignore_all = kwargs.get(&#39;ignore_all_risks&#39;, False)
            if not ignore_all and &#39;ignore_risk_type&#39; not in kwargs:
                raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Ignore risk type details missing for this operation&#39;)
            if not create_review:
                if ignore_all:
                    request_json[&#39;ignoreRisksReq&#39;][&#39;ignoreAllRisks&#39;] = True
                else:
                    request_json[&#39;ignoreRisksReq&#39;][&#39;ignoreAllRisks&#39;] = False
                    request_json[&#39;ignoreRisksReq&#39;][&#39;ignoreRiskTypeList&#39;] = kwargs.get(&#39;ignore_risk_type&#39;)
                request_json[&#39;remActionRequest&#39;][&#39;dataSourceId&#39;] = self._data_source_id
            else:
                if ignore_all:
                    request_json[&#39;ignoreAllRisks&#39;] = True
                else:
                    request_json[&#39;ignoreAllRisks&#39;] = False
                    request_json[&#39;ignoreRiskTypeList&#39;] = kwargs.get(&#39;ignore_risk_type&#39;)

                request_json[&#39;dataSourceId&#39;] = self._data_source_id
                # delete unwanted keys as it is review request
                if &#39;remActionRequest&#39; in request_json:
                    del request_json[&#39;remActionRequest&#39;]
                if &#39;ignoreRisksReq&#39; in request_json:
                    del request_json[&#39;ignoreRisksReq&#39;]

        if document_ids:
            query = &#34;&#34;
            for doc in document_ids:
                query = query + f&#34;(contentid:{doc}) OR &#34;
            last_char_index = query.rfind(&#34; OR &#34;)
            query = query[:last_char_index]
            # for non-review request, doc id need to set at search request inside remaction
            if not create_review:
                # make sure whether passed document ids are correct
                count, docs, _ = self.search(
                    criteria=f&#34;{EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS} AND {query}&#34;,
                    attr_list=EdiscoveryConstants.REVIEW_ACTION_SEARCH_FL_SET)
                if len(docs) != len(document_ids):
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        &#34;Unable to find document details from given list of document id&#34;)

                request_json[&#39;remActionRequest&#39;][&#39;searchRequest&#39;] = json.dumps(
                    self._ediscovery_client_ops.form_search_params(
                        criteria=EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS,
                        attr_list=EdiscoveryConstants.REVIEW_ACTION_SEARCH_FL_SET,
                        params={
                            &#34;start&#34;: &#34;0&#34;},
                        query=query, is_separate_attr=True))

            else:
                request_json[&#39;files&#39;] = json.dumps(
                    self._form_files_list(
                        document_ids=document_ids,
                        attr_list=attr_list))
        else:
            # bulk operation request. Delete unnecessary fields
            if &#39;files&#39; in request_json:
                del request_json[&#39;files&#39;]
            if not create_review:
                request_json[&#39;remActionRequest&#39;][&#39;searchRequest&#39;] = EdiscoveryConstants.REVIEW_ACTION_BULK_SEARCH_REQ
                request_json[&#39;remActionRequest&#39;][&#39;isBulkOperation&#39;] = True
                request_json[&#39;remActionRequest&#39;][&#39;handlerId&#39;] = self._ediscovery_client_ops.get_handler_id()
            else:
                request_json[&#39;searchRequest&#39;] = EdiscoveryConstants.REVIEW_ACTION_BULK_SEARCH_REQ
                request_json[&#39;handlerId&#39;] = self._ediscovery_client_ops.get_handler_id()
                request_json[&#39;isBulkOperation&#39;] = True

        if create_review:
            api = self._API_ACTIONS_WITH_REQUEST
            request_json[&#39;options&#39;] = json.dumps(
                self._form_request_options(
                    reviewers=reviewers,
                    approvers=approvers,
                    document_ids=document_ids,
                    req_name=req_name if req_name else f&#34;{self.data_source_name}_{action_type.name}&#34;
                                                       f&#34;_{int(time.time())}&#34;))

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, api, request_json
        )
        if flag:
            if response.json():
                response = response.json()
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;EdiscoveryClients&#39;,
                        &#39;102&#39;,
                        f&#34;Review action failed with error - {response.get(&#39;errorMsg&#39;)}&#34;)
                if &#39;jobId&#39; in response and not create_review:
                    return response[&#39;jobId&#39;]
                return
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;116&#39;)
        self._response_not_success(response)

    def start_collection(self, is_incr=True):
        &#34;&#34;&#34;Starts collection job on this data source
                Args:

                    is_incr         (bool)      --  Specifies whether to invoke incremental or full crawl job
                                                        Default:True (Incremental job)

                Return:

                    None

                Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.start_job(is_incr=is_incr)

    @property
    def data_source_name(self):
        &#34;&#34;&#34;returns the data source name

            Returns:

                str --  Name of data source

        &#34;&#34;&#34;
        return self._data_source_name

    @property
    def data_source_type(self):
        &#34;&#34;&#34;returns the data source type

            Returns:

                str --  Type of data source

        &#34;&#34;&#34;
        return self._data_source_type

    @property
    def data_source_type_id(self):
        &#34;&#34;&#34;returns the data source type id

            Returns:

                int --  data source type

        &#34;&#34;&#34;
        position = list(EdiscoveryConstants.DATA_SOURCE_TYPES.values()).index(self.data_source_type)
        return list(EdiscoveryConstants.DATA_SOURCE_TYPES.keys())[position]

    @property
    def data_source_id(self):
        &#34;&#34;&#34;returns the data source id

            Returns:

                int --  data source id

        &#34;&#34;&#34;
        return self._data_source_id

    @property
    def data_source_props(self):
        &#34;&#34;&#34;returns the data source properties

            Returns:

                dict --  data source properties

        &#34;&#34;&#34;
        return self._data_source_props

    @property
    def cloud_id(self):
        &#34;&#34;&#34;returns the index server cloudid associated with this data source

            Returns:

                int --  index server cloud id

        &#34;&#34;&#34;
        return self._cloud_id

    @property
    def core_name(self):
        &#34;&#34;&#34;returns the core name for this data source

            Returns:

                str --  core name for this data source

        &#34;&#34;&#34;
        return self._core_name

    @property
    def computed_core_name(self):
        &#34;&#34;&#34;returns the computed core name for this data source

            Returns:

                str --  Index server core name for this data source

        &#34;&#34;&#34;
        return self._computed_core_name

    @property
    def core_id(self):
        &#34;&#34;&#34;returns the core id for this data source

            Returns:

                int --  core id

        &#34;&#34;&#34;
        return self._core_id

    @property
    def crawl_type_name(self):
        &#34;&#34;&#34;returns the crawl type enum name for this data source

            Returns:

                str --  crawl type

        &#34;&#34;&#34;
        return EdiscoveryConstants.CrawlType(int(self._crawl_type)).name

    @property
    def crawl_type(self):
        &#34;&#34;&#34;returns the crawl type for this data source

            Returns:

                int --  crawl type

        &#34;&#34;&#34;
        return self._crawl_type

    @property
    def plan_id(self):
        &#34;&#34;&#34;returns the DC plan id associated

            Returns:

                int -- Data classification plan id

        &#34;&#34;&#34;
        return self._dc_plan_id

    @property
    def client_id(self):
        &#34;&#34;&#34;returns the client id associated

            Returns:

                int -- client id

        &#34;&#34;&#34;
        return self._client_id

    @property
    def total_documents(self):
        &#34;&#34;&#34;returns the total document from this data source

            Returns:

                int --  Total document count

        &#34;&#34;&#34;
        count, _, _ = self.search(criteria=EdiscoveryConstants.FIELD_IS_FILE, params={&#34;rows&#34;: &#34;0&#34;})
        return count

    @property
    def sensitive_files_count(self):
        &#34;&#34;&#34;returns the total sensitive files count on this data source

            Returns:

                int --  Sensitive files count

        &#34;&#34;&#34;
        count, _, _ = self.search(criteria=EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS,
                                  params={&#34;rows&#34;: &#34;0&#34;})
        return count

    @property
    def name(self):
        &#34;&#34;&#34;returns the actual name for this data source

            Returns:

                str --  Actual name of the datasource

        &#34;&#34;&#34;
        return self._data_source_actual_name

    @property
    def index_server_node_client_id(self):
        &#34;&#34;&#34;returns the associated Index server node client id on which the collection exists

            Returns:

                str --  Index server node client id on which the collection exists

        &#34;&#34;&#34;
        return self._collection_client_id</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.client_id"><code class="name">var <span class="ident">client_id</span></code></dt>
<dd>
<div class="desc"><p>returns the client id associated</p>
<h2 id="returns">Returns</h2>
<p>int &ndash; client id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3351-L3360" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_id(self):
    &#34;&#34;&#34;returns the client id associated

        Returns:

            int -- client id

    &#34;&#34;&#34;
    return self._client_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.cloud_id"><code class="name">var <span class="ident">cloud_id</span></code></dt>
<dd>
<div class="desc"><p>returns the index server cloudid associated with this data source</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
index server cloud id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3274-L3283" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cloud_id(self):
    &#34;&#34;&#34;returns the index server cloudid associated with this data source

        Returns:

            int --  index server cloud id

    &#34;&#34;&#34;
    return self._cloud_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.computed_core_name"><code class="name">var <span class="ident">computed_core_name</span></code></dt>
<dd>
<div class="desc"><p>returns the computed core name for this data source</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
Index server core name for this data source</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3296-L3305" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def computed_core_name(self):
    &#34;&#34;&#34;returns the computed core name for this data source

        Returns:

            str --  Index server core name for this data source

    &#34;&#34;&#34;
    return self._computed_core_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.core_id"><code class="name">var <span class="ident">core_id</span></code></dt>
<dd>
<div class="desc"><p>returns the core id for this data source</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
core id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3307-L3316" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def core_id(self):
    &#34;&#34;&#34;returns the core id for this data source

        Returns:

            int --  core id

    &#34;&#34;&#34;
    return self._core_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.core_name"><code class="name">var <span class="ident">core_name</span></code></dt>
<dd>
<div class="desc"><p>returns the core name for this data source</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
core name for this data source</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3285-L3294" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def core_name(self):
    &#34;&#34;&#34;returns the core name for this data source

        Returns:

            str --  core name for this data source

    &#34;&#34;&#34;
    return self._core_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.crawl_type"><code class="name">var <span class="ident">crawl_type</span></code></dt>
<dd>
<div class="desc"><p>returns the crawl type for this data source</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
crawl type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3329-L3338" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def crawl_type(self):
    &#34;&#34;&#34;returns the crawl type for this data source

        Returns:

            int --  crawl type

    &#34;&#34;&#34;
    return self._crawl_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.crawl_type_name"><code class="name">var <span class="ident">crawl_type_name</span></code></dt>
<dd>
<div class="desc"><p>returns the crawl type enum name for this data source</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
crawl type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3318-L3327" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def crawl_type_name(self):
    &#34;&#34;&#34;returns the crawl type enum name for this data source

        Returns:

            str --  crawl type

    &#34;&#34;&#34;
    return EdiscoveryConstants.CrawlType(int(self._crawl_type)).name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_id"><code class="name">var <span class="ident">data_source_id</span></code></dt>
<dd>
<div class="desc"><p>returns the data source id</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
data source id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3252-L3261" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_source_id(self):
    &#34;&#34;&#34;returns the data source id

        Returns:

            int --  data source id

    &#34;&#34;&#34;
    return self._data_source_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_name"><code class="name">var <span class="ident">data_source_name</span></code></dt>
<dd>
<div class="desc"><p>returns the data source name</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
Name of data source</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3218-L3227" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_source_name(self):
    &#34;&#34;&#34;returns the data source name

        Returns:

            str --  Name of data source

    &#34;&#34;&#34;
    return self._data_source_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_props"><code class="name">var <span class="ident">data_source_props</span></code></dt>
<dd>
<div class="desc"><p>returns the data source properties</p>
<h2 id="returns">Returns</h2>
<p>dict &ndash;
data source properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3263-L3272" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_source_props(self):
    &#34;&#34;&#34;returns the data source properties

        Returns:

            dict --  data source properties

    &#34;&#34;&#34;
    return self._data_source_props</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_type"><code class="name">var <span class="ident">data_source_type</span></code></dt>
<dd>
<div class="desc"><p>returns the data source type</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
Type of data source</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3229-L3238" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_source_type(self):
    &#34;&#34;&#34;returns the data source type

        Returns:

            str --  Type of data source

    &#34;&#34;&#34;
    return self._data_source_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_type_id"><code class="name">var <span class="ident">data_source_type_id</span></code></dt>
<dd>
<div class="desc"><p>returns the data source type id</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
data source type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3240-L3250" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_source_type_id(self):
    &#34;&#34;&#34;returns the data source type id

        Returns:

            int --  data source type

    &#34;&#34;&#34;
    position = list(EdiscoveryConstants.DATA_SOURCE_TYPES.values()).index(self.data_source_type)
    return list(EdiscoveryConstants.DATA_SOURCE_TYPES.keys())[position]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.index_server_node_client_id"><code class="name">var <span class="ident">index_server_node_client_id</span></code></dt>
<dd>
<div class="desc"><p>returns the associated Index server node client id on which the collection exists</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
Index server node client id on which the collection exists</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3398-L3407" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_server_node_client_id(self):
    &#34;&#34;&#34;returns the associated Index server node client id on which the collection exists

        Returns:

            str --  Index server node client id on which the collection exists

    &#34;&#34;&#34;
    return self._collection_client_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>returns the actual name for this data source</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
Actual name of the datasource</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3387-L3396" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;returns the actual name for this data source

        Returns:

            str --  Actual name of the datasource

    &#34;&#34;&#34;
    return self._data_source_actual_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.plan_id"><code class="name">var <span class="ident">plan_id</span></code></dt>
<dd>
<div class="desc"><p>returns the DC plan id associated</p>
<h2 id="returns">Returns</h2>
<p>int &ndash; Data classification plan id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3340-L3349" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def plan_id(self):
    &#34;&#34;&#34;returns the DC plan id associated

        Returns:

            int -- Data classification plan id

    &#34;&#34;&#34;
    return self._dc_plan_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.sensitive_files_count"><code class="name">var <span class="ident">sensitive_files_count</span></code></dt>
<dd>
<div class="desc"><p>returns the total sensitive files count on this data source</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Sensitive files count</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3374-L3385" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sensitive_files_count(self):
    &#34;&#34;&#34;returns the total sensitive files count on this data source

        Returns:

            int --  Sensitive files count

    &#34;&#34;&#34;
    count, _, _ = self.search(criteria=EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS,
                              params={&#34;rows&#34;: &#34;0&#34;})
    return count</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.total_documents"><code class="name">var <span class="ident">total_documents</span></code></dt>
<dd>
<div class="desc"><p>returns the total document from this data source</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Total document count</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3362-L3372" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def total_documents(self):
    &#34;&#34;&#34;returns the total document from this data source

        Returns:

            int --  Total document count

    &#34;&#34;&#34;
    count, _, _ = self.search(criteria=EdiscoveryConstants.FIELD_IS_FILE, params={&#34;rows&#34;: &#34;0&#34;})
    return count</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.export"><code class="name flex">
<span>def <span class="ident">export</span></span>(<span>self, criteria=None, attr_list=None, params=None)</span>
</code></dt>
<dd>
<div class="desc"><p>do export to CSV on data</p>
<h2 id="args">Args</h2>
<p>criteria
(str)
&ndash;
containing criteria for query
(Default : None - Exports all docs)</p>
<pre><code>                                Example :

                                    1) Size filter --&gt; Size:[10 TO 1024]
                                    2) File name filter --&gt; FileName_idx:09_23*
</code></pre>
<p>attr_list
(set)
&ndash;
Column names to be returned in results.
Acts as 'fl' in query</p>
<p>params
(dict)
&ndash;
Any other params which needs to be passed
Example : { "start" : "0" }</p>
<h2 id="returns">Returns</h2>
<p>str
&ndash;
export operation token</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to perform export
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2808-L2843" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def export(self, criteria=None, attr_list=None, params=None):
    &#34;&#34;&#34;do export to CSV on data

        Args:

            criteria        (str)      --  containing criteria for query
                                                (Default : None - Exports all docs)

                                                Example :

                                                    1) Size filter --&gt; Size:[10 TO 1024]
                                                    2) File name filter --&gt; FileName_idx:09_23*

            attr_list       (set)      --  Column names to be returned in results.
                                                 Acts as &#39;fl&#39; in query

            params          (dict)     --  Any other params which needs to be passed
                                               Example : { &#34;start&#34; : &#34;0&#34; }

        Returns:

            str     --  export operation token


        Raises:

            SDKException:

                    if failed to perform export

    &#34;&#34;&#34;
    if not attr_list:
        if self.data_source_type == EdiscoveryConstants.DATA_SOURCE_TYPES[5]:
            attr_list = EdiscoveryConstants.FS_DEFAULT_EXPORT_FIELDS
    return self._ediscovery_client_ops.export(criteria=criteria, attr_list=attr_list,
                                              params=params)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.get_active_jobs"><code class="name flex">
<span>def <span class="ident">get_active_jobs</span></span>(<span>self, limit=50, lookup_time=2160)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the active jobs details for this data source</p>
<h2 id="args">Args</h2>
<p>limit
(int)
&ndash;
No of jobs to return (default: 50 rows)</p>
<p>lookup_time (int)
&ndash;
list of jobs to be retrieved which are started within specified
hours older</p>
<pre><code>        default: 2160 hours (last 90 days)
</code></pre>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary consisting of the job IDs matching the given criteria
as the key, and their details as its value</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2752-L2772" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_active_jobs(self, limit=50, lookup_time=2160):
    &#34;&#34;&#34;returns the active jobs details for this data source

        Args:

            limit       (int)           --  No of jobs to return (default: 50 rows)

            lookup_time (int)           --  list of jobs to be retrieved which are started within specified
                hours older

                        default: 2160 hours (last 90 days)

        Returns:

                dict    -   dictionary consisting of the job IDs matching the given criteria
                            as the key, and their details as its value

    &#34;&#34;&#34;
    return self._jobs.active_jobs(lookup_time=lookup_time,
                                  limit=limit,
                                  entity={&#34;dataSourceId&#34;: self.data_source_id})</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.get_job_history"><code class="name flex">
<span>def <span class="ident">get_job_history</span></span>(<span>self, limit=50, lookup_time=2160)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the job history details for this data source</p>
<h2 id="args">Args</h2>
<p>limit
(int)
&ndash;
No of jobs to return (default: 50 rows)</p>
<p>lookup_time (int)
&ndash;
list of jobs to be retrieved which are specified
hours older</p>
<pre><code>        default: 2160 hours (last 90 days)
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2735-L2750" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_job_history(self, limit=50, lookup_time=2160):
    &#34;&#34;&#34;returns the job history details for this data source

        Args:

            limit       (int)           --  No of jobs to return (default: 50 rows)

            lookup_time (int)           --  list of jobs to be retrieved which are specified
                hours older

                        default: 2160 hours (last 90 days)

    &#34;&#34;&#34;
    return self._jobs.finished_jobs(lookup_time=lookup_time,
                                    limit=limit,
                                    entity={&#34;dataSourceId&#34;: self.data_source_id})</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>refresh the data source properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2598-L2600" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;refresh the data source properties&#34;&#34;&#34;
    self._data_source = self._get_data_source_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.review_action"><code class="name flex">
<span>def <span class="ident">review_action</span></span>(<span>self, action_type, reviewers=None, approvers=None, document_ids=None, req_name=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>do review action on documents</p>
<h2 id="args">Args</h2>
<p>action_type
(enum)
&ndash;
Type of action to be taken
Refer to EdiscoveryConstants.ReviewActions</p>
<p>document_ids
(list)
&ndash;
list of document id's
Default:None (means all docs)</p>
<p>reviewers
(list)
&ndash;
List of review users</p>
<p>approvers
(list)
&ndash;
List of approver users</p>
<p>req_name
(str)
&ndash;
Request name
kwargs arguments:</p>
<pre><code>backup_delete       (bool)      --  Specifies whether to delete document from backup or not

destination         (str)       --  Destination UNC path for move operation

user_name           (str)       --  Username to access share path

password            (str)       --  Password for user in base64 encoded

create_review       (bool)      --  speicifies whether to create review or not for this action
                                        (For Delete &amp; Move, it is TRUE always)

retain_month        (int)       --  no of months to set as retention

ignore_all_risks    (bool)      --  specifies whether it has to be ignore risk fully or not

ignore_risk_type    (list)      --  list of risks which needs to be ignored
                                        Refer to EDiscoveryConstants.RiskTypes
</code></pre>
<h2 id="returns">Returns</h2>
<p>None &ndash; if create_review is true</p>
<p>job id &ndash; if create_review is false</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if action type is not valid

if failed to do review action on documents

if document id's not found
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3000-L3196" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def review_action(self, action_type, reviewers=None, approvers=None, document_ids=None, req_name=None, **kwargs):
    &#34;&#34;&#34;do review action on documents

            Args:

                action_type         (enum)      --  Type of action to be taken
                                                    Refer to EdiscoveryConstants.ReviewActions

                document_ids        (list)      --  list of document id&#39;s
                                                        Default:None (means all docs)

                reviewers           (list)      --  List of review users

                approvers           (list)      --  List of approver users

                req_name            (str)       --  Request name

            kwargs arguments:

                backup_delete       (bool)      --  Specifies whether to delete document from backup or not

                destination         (str)       --  Destination UNC path for move operation

                user_name           (str)       --  Username to access share path

                password            (str)       --  Password for user in base64 encoded

                create_review       (bool)      --  speicifies whether to create review or not for this action
                                                        (For Delete &amp; Move, it is TRUE always)

                retain_month        (int)       --  no of months to set as retention

                ignore_all_risks    (bool)      --  specifies whether it has to be ignore risk fully or not

                ignore_risk_type    (list)      --  list of risks which needs to be ignored
                                                        Refer to EDiscoveryConstants.RiskTypes

            Returns:

                None -- if create_review is true

                job id -- if create_review is false

            Raises:

                SDKException:

                    if action type is not valid

                    if failed to do review action on documents

                    if document id&#39;s not found
    &#34;&#34;&#34;
    if not isinstance(action_type, EdiscoveryConstants.ReviewActions):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    if self._app_type.value == TargetApps.FSO.value and \
            action_type.value not in EdiscoveryConstants.REVIEW_ACTION_FSO_SUPPORTED:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;{action_type.value} is not supported for FSO app&#34;)
    if self._app_type.value == TargetApps.SDG.value and \
            action_type.value not in EdiscoveryConstants.REVIEW_ACTION_SDG_SUPPORTED:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, f&#34;{action_type.value} is not supported for SDG app&#34;)
    attr_list = None
    api = self._API_ACTIONS
    create_review = kwargs.get(&#39;create_review&#39;, False)
    if action_type == EdiscoveryConstants.ReviewActions.DELETE or \
            action_type == EdiscoveryConstants.ReviewActions.MOVE:
        # For Delete &amp; Move, review request is compulsory
        create_review = True
    if create_review:
        if not reviewers or not approvers:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Reviewers/Approvers missing in input&#39;)
    if self.data_source_type_id not in EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Not supported data source for review action&#34;)
    attr_list = EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET[self.data_source_type_id]
    request_json = None
    # For Delete &amp; Move, review request is compulsory so non-review case is not handled for this block
    if action_type.value == EdiscoveryConstants.ReviewActions.DELETE.value:
        request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_DELETE_REQ_JSON)
        request_json[&#39;deleteFromBackup&#39;] = kwargs.get(&#34;backup_delete&#34;, False)
    elif action_type.value == EdiscoveryConstants.ReviewActions.MOVE.value:
        if &#39;destination&#39; not in kwargs or &#39;user_name&#39; not in kwargs or &#39;password&#39; not in kwargs:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Required params missing for move operation&#34;)
        request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_MOVE_REQ_JSON)
        request_json[&#39;newDestination&#39;] = kwargs.get(&#34;destination&#34;, &#39;&#39;)
        request_json[&#39;username&#39;] = kwargs.get(&#34;user_name&#34;, &#39;&#39;)
        request_json[&#39;password&#39;] = kwargs.get(&#34;password&#34;, &#39;&#39;)
    elif action_type.value == EdiscoveryConstants.ReviewActions.RETENTION.value:
        request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_SET_RETENTION_REQ_JSON)
        if &#39;retain_month&#39; not in kwargs:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Retention month input missing for this operation&#39;)
        if not create_review:
            request_json[&#39;setRetentionReq&#39;][&#39;numOfMonthsRemain&#39;] = kwargs.get(&#39;retain_month&#39;)
            request_json[&#39;remActionRequest&#39;][&#39;dataSourceId&#39;] = self._data_source_id
        else:
            request_json[&#39;numOfMonthsRemain&#39;] = kwargs.get(&#39;retain_month&#39;)
            request_json[&#39;dataSourceId&#39;] = self._data_source_id
            # delete unwanted keys as it is review request
            if &#39;remActionRequest&#39; in request_json:
                del request_json[&#39;remActionRequest&#39;]
            if &#39;setRetentionReq&#39; in request_json:
                del request_json[&#39;setRetentionReq&#39;]
    elif action_type.value == EdiscoveryConstants.ReviewActions.IGNORE.value:
        request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_IGNORE_FILES_REQ_JSON)
        ignore_all = kwargs.get(&#39;ignore_all_risks&#39;, False)
        if not ignore_all and &#39;ignore_risk_type&#39; not in kwargs:
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#39;Ignore risk type details missing for this operation&#39;)
        if not create_review:
            if ignore_all:
                request_json[&#39;ignoreRisksReq&#39;][&#39;ignoreAllRisks&#39;] = True
            else:
                request_json[&#39;ignoreRisksReq&#39;][&#39;ignoreAllRisks&#39;] = False
                request_json[&#39;ignoreRisksReq&#39;][&#39;ignoreRiskTypeList&#39;] = kwargs.get(&#39;ignore_risk_type&#39;)
            request_json[&#39;remActionRequest&#39;][&#39;dataSourceId&#39;] = self._data_source_id
        else:
            if ignore_all:
                request_json[&#39;ignoreAllRisks&#39;] = True
            else:
                request_json[&#39;ignoreAllRisks&#39;] = False
                request_json[&#39;ignoreRiskTypeList&#39;] = kwargs.get(&#39;ignore_risk_type&#39;)

            request_json[&#39;dataSourceId&#39;] = self._data_source_id
            # delete unwanted keys as it is review request
            if &#39;remActionRequest&#39; in request_json:
                del request_json[&#39;remActionRequest&#39;]
            if &#39;ignoreRisksReq&#39; in request_json:
                del request_json[&#39;ignoreRisksReq&#39;]

    if document_ids:
        query = &#34;&#34;
        for doc in document_ids:
            query = query + f&#34;(contentid:{doc}) OR &#34;
        last_char_index = query.rfind(&#34; OR &#34;)
        query = query[:last_char_index]
        # for non-review request, doc id need to set at search request inside remaction
        if not create_review:
            # make sure whether passed document ids are correct
            count, docs, _ = self.search(
                criteria=f&#34;{EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS} AND {query}&#34;,
                attr_list=EdiscoveryConstants.REVIEW_ACTION_SEARCH_FL_SET)
            if len(docs) != len(document_ids):
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    &#34;Unable to find document details from given list of document id&#34;)

            request_json[&#39;remActionRequest&#39;][&#39;searchRequest&#39;] = json.dumps(
                self._ediscovery_client_ops.form_search_params(
                    criteria=EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS,
                    attr_list=EdiscoveryConstants.REVIEW_ACTION_SEARCH_FL_SET,
                    params={
                        &#34;start&#34;: &#34;0&#34;},
                    query=query, is_separate_attr=True))

        else:
            request_json[&#39;files&#39;] = json.dumps(
                self._form_files_list(
                    document_ids=document_ids,
                    attr_list=attr_list))
    else:
        # bulk operation request. Delete unnecessary fields
        if &#39;files&#39; in request_json:
            del request_json[&#39;files&#39;]
        if not create_review:
            request_json[&#39;remActionRequest&#39;][&#39;searchRequest&#39;] = EdiscoveryConstants.REVIEW_ACTION_BULK_SEARCH_REQ
            request_json[&#39;remActionRequest&#39;][&#39;isBulkOperation&#39;] = True
            request_json[&#39;remActionRequest&#39;][&#39;handlerId&#39;] = self._ediscovery_client_ops.get_handler_id()
        else:
            request_json[&#39;searchRequest&#39;] = EdiscoveryConstants.REVIEW_ACTION_BULK_SEARCH_REQ
            request_json[&#39;handlerId&#39;] = self._ediscovery_client_ops.get_handler_id()
            request_json[&#39;isBulkOperation&#39;] = True

    if create_review:
        api = self._API_ACTIONS_WITH_REQUEST
        request_json[&#39;options&#39;] = json.dumps(
            self._form_request_options(
                reviewers=reviewers,
                approvers=approvers,
                document_ids=document_ids,
                req_name=req_name if req_name else f&#34;{self.data_source_name}_{action_type.name}&#34;
                                                   f&#34;_{int(time.time())}&#34;))

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, api, request_json
    )
    if flag:
        if response.json():
            response = response.json()
            if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Review action failed with error - {response.get(&#39;errorMsg&#39;)}&#34;)
            if &#39;jobId&#39; in response and not create_review:
                return response[&#39;jobId&#39;]
            return
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;116&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.search"><code class="name flex">
<span>def <span class="ident">search</span></span>(<span>self, criteria=None, attr_list=None, params=None)</span>
</code></dt>
<dd>
<div class="desc"><p>do searches on data source and returns document details</p>
<h2 id="args">Args</h2>
<p>criteria
(str)
&ndash;
containing criteria for query
(Default : None - returns all docs)</p>
<pre><code>                                Example :

                                    1) Size filter --&gt; Size:[10 TO 1024]
                                    2) File name filter --&gt; FileName_idx:09_23*
</code></pre>
<p>attr_list
(set)
&ndash;
Column names to be returned in results.
Acts as 'fl' in query</p>
<p>params
(dict)
&ndash;
Any other params which needs to be passed
Example : { "start" : "0" }</p>
<h2 id="returns">Returns</h2>
<p>list(dict),dict
&ndash;
Containing document details &amp; facet details(if any)</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to perform search
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2845-L2876" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def search(self, criteria=None, attr_list=None, params=None):
    &#34;&#34;&#34;do searches on data source and returns document details

        Args:

            criteria        (str)      --  containing criteria for query
                                                (Default : None - returns all docs)

                                                Example :

                                                    1) Size filter --&gt; Size:[10 TO 1024]
                                                    2) File name filter --&gt; FileName_idx:09_23*

            attr_list       (set)      --  Column names to be returned in results.
                                                 Acts as &#39;fl&#39; in query

            params          (dict)     --  Any other params which needs to be passed
                                               Example : { &#34;start&#34; : &#34;0&#34; }

        Returns:

            list(dict),dict    --  Containing document details &amp; facet details(if any)

        Raises:

            SDKException:

                    if failed to perform search

    &#34;&#34;&#34;
    return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list,
                                              params=params)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.start_collection"><code class="name flex">
<span>def <span class="ident">start_collection</span></span>(<span>self, is_incr=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts collection job on this data source</p>
<h2 id="args">Args</h2>
<p>is_incr
(bool)
&ndash;
Specifies whether to invoke incremental or full crawl job
Default:True (Incremental job)</p>
<h2 id="return">Return</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to start collection job
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L3198-L3216" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def start_collection(self, is_incr=True):
    &#34;&#34;&#34;Starts collection job on this data source
            Args:

                is_incr         (bool)      --  Specifies whether to invoke incremental or full crawl job
                                                    Default:True (Incremental job)

            Return:

                None

            Raises:

                SDKException:

                        if failed to start collection job

    &#34;&#34;&#34;
    return self._ediscovery_client_ops.start_job(is_incr=is_incr)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.tag_items"><code class="name flex">
<span>def <span class="ident">tag_items</span></span>(<span>self, tags, document_ids=None, ops_type=1, create_review=False, reviewers=None, approvers=None, req_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Applies given tag to documents</p>
<h2 id="args">Args</h2>
<p>tags
(list)
&ndash;
list of tags names which needs to be applied
Format : Tagset\TagName
Example : DiscoveryEntity\American</p>
<p>document_ids
(list)
&ndash;
list of document content id's which needs to be tagged</p>
<p>ops_type
(int)
&ndash;
Denotes operation type for tagging
(1-Add or 2-Delete)
Default : 1(Add)</p>
<p>create_review
(bool)
&ndash;
Specifies whether to create review request for this tagging or not
Default:False</p>
<p>reviewers
(list)
&ndash;
List of review users</p>
<p>approvers
(list)
&ndash;
List of approver users</p>
<p>req_name
(str)
&ndash;
Request name</p>
<h2 id="returns">Returns</h2>
<p>None if it is tagging with review request</p>
<p>jobid (str) &ndash; if it is bulk operation of tagging all items without review request</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if tag name doesn't exists in commcell

    if failed to apply tag

    if response is empty

    if data source doesn't belongs to FSO app
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2602-L2733" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def tag_items(
        self,
        tags,
        document_ids=None,
        ops_type=1,
        create_review=False,
        reviewers=None,
        approvers=None,
        req_name=None):
    &#34;&#34;&#34;Applies given tag to documents

        Args:

            tags            (list)      --  list of tags names which needs to be applied
                                                    Format : Tagset\\TagName
                                                    Example : DiscoveryEntity\\American

            document_ids    (list)      --  list of document content id&#39;s which needs to be tagged

            ops_type        (int)       --  Denotes operation type for tagging  (1-Add or 2-Delete)
                                                    Default : 1(Add)

            create_review   (bool)      --  Specifies whether to create review request for this tagging or not
                                                    Default:False

            reviewers       (list)      --  List of review users

            approvers       (list)      --  List of approver users

            req_name        (str)       --  Request name

        Returns:

            None if it is tagging with review request

            jobid (str) -- if it is bulk operation of tagging all items without review request

        Raises:

            SDKException:

                    if tag name doesn&#39;t exists in commcell

                    if failed to apply tag

                    if response is empty

                    if data source doesn&#39;t belongs to FSO app
    &#34;&#34;&#34;
    if self._app_type.value != TargetApps.FSO.value:
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Tagging is supported only for FSO app&#34;)
    if not isinstance(tags, list):
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;101&#39;)
    query = &#34;&#34;
    request_json = None
    tag_guids = []
    api = self._API_ACTIONS
    if create_review:
        api = self._API_ACTIONS_WITH_REQUEST

    tag_mgr = self._commcell_object.activate.entity_manager(EntityManagerTypes.TAGS)
    for tag in tags:
        tag_split = tag.split(&#34;\\\\&#34;)
        if not tag_mgr.has_tag_set(tag_set_name=tag_split[0]):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unable to find tagset in the commcell&#34;)
        tag_set_obj = tag_mgr.get(tag_split[0])
        if not tag_set_obj.has_tag(tag_split[1]):
            raise SDKException(&#39;EdiscoveryClients&#39;, &#39;102&#39;, &#34;Unable to find tag in the tagset&#34;)
        tag_obj = tag_set_obj.get(tag_split[1])
        if not create_review:
            tag_guids.append({&#34;id&#34;: tag_obj.guid})
        else:
            tag_guids.append(tag_obj.guid)

    if not create_review:
        if document_ids:
            for doc in document_ids:
                query = query + f&#34;(contentid:{doc}) OR &#34;
            last_char_index = query.rfind(&#34; OR &#34;)
            query = query[:last_char_index]
        else:
            query = &#34;*:*&#34;
        search_params = self._ediscovery_client_ops.form_search_params(
            query=query, key=&#34;name&#34;, params={&#34;rows&#34;: &#34;0&#34;})
        tag_request = copy.deepcopy(EdiscoveryConstants.TAGGING_ITEMS_REQUEST)
        request_json = copy.deepcopy(EdiscoveryConstants.REVIEW_ACTION_TAG_REQ_JSON)
        if ops_type != 1:
            tag_request[&#39;opType&#39;] = &#34;DELETE&#34;
        if not document_ids:  # bulk request
            tag_request[&#39;isAsync&#39;] = True
        tag_request[&#39;entityIds&#39;].append(self.data_source_id)
        tag_request[&#39;searchRequest&#39;] = search_params
        tag_request[&#39;tags&#39;] = tag_guids
        tag_request[&#39;dsType&#39;] = self.data_source_type_id
        request_json[&#39;taggingRequest&#39;] = tag_request
        request_json[&#39;remActionRequest&#39;][&#39;dataSourceId&#39;] = self.data_source_id
        if not document_ids:
            request_json[&#39;remActionRequest&#39;][&#39;isBulkOperation&#39;] = True

    else:
        request_json = copy.deepcopy(EdiscoveryConstants.TAGGING_ITEMS_REVIEW_REQUEST)
        if ops_type != 1:
            request_json[&#39;taggingInformation&#39;][&#39;opType&#39;] = &#34;DELETE&#34;
        request_json[&#39;files&#39;] = json.dumps(self._form_files_list(
            document_ids=document_ids,
            attr_list=EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET[self.data_source_type_id]))
        request_json[&#39;taggingInformation&#39;][&#39;tagIds&#39;] = tag_guids
        request_json[&#39;options&#39;] = str(
            self._form_request_options(
                reviewers=reviewers,
                approvers=approvers,
                document_ids=document_ids,
                req_name=req_name if req_name else f&#34;{self.data_source_name}_tag_{int(time.time())}&#34;))

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, api, request_json
    )
    if flag:
        if response.json():
            response = response.json()
            if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;EdiscoveryClients&#39;,
                    &#39;102&#39;,
                    f&#34;Tagging failed with error : {response.get(&#39;errorMessage&#39;, &#39;&#39;)}&#34;)
            else:
                if not create_review:
                    # tagging without review. return the job id
                    return response[&#39;jobId&#39;]
                return
        raise SDKException(&#39;EdiscoveryClients&#39;, &#39;113&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.wait_for_export"><code class="name flex">
<span>def <span class="ident">wait_for_export</span></span>(<span>self, token, wait_time=60, download=True, download_location='C:\\Users\\<USER>\\Desktop\\Automated\\cvpysdk')</span>
</code></dt>
<dd>
<div class="desc"><p>Waits for Export to CSV to finish</p>
<h2 id="args">Args</h2>
<p>wait_time
(int)
&ndash;
time interval to wait for job completion in Mins
Default : 60Mins</p>
<p>token
(str)
&ndash;
Export to CSV token GUID</p>
<p>download
(bool)
&ndash;
specify whether to download exported file or not</p>
<p>download_location
(str)
&ndash;
Path where to download exported csv file
Default: Current working dir</p>
<h2 id="return">Return</h2>
<p>str
&ndash; Download GUID for exported CSV file if download=false
File path containing exported csv file if download=true</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if Export job fails

    if timeout happens
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/ediscovery_utils.py#L2774-L2806" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def wait_for_export(self, token, wait_time=60, download=True, download_location=os.getcwd()):
    &#34;&#34;&#34;Waits for Export to CSV to finish

            Args:

                wait_time           (int)       --  time interval to wait for job completion in Mins
                                                        Default : 60Mins

                token               (str)       --  Export to CSV token GUID

                download            (bool)      --  specify whether to download exported file or not

                download_location   (str)       --  Path where to download exported csv file
                                                            Default: Current working dir

            Return:

                str     -- Download GUID for exported CSV file if download=false
                           File path containing exported csv file if download=true

            Raises:

                SDKException:

                        if Export job fails

                        if timeout happens

    &#34;&#34;&#34;
    return self._ediscovery_client_ops.wait_for_export(token=token,
                                                       wait_time=wait_time,
                                                       download=download,
                                                       download_location=download_location)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#ediscoveryclientoperations-attributes">EdiscoveryClientOperations Attributes:</a></li>
<li><a href="#ediscoverydatasources-attributes">EdiscoveryDataSources Attributes:</a></li>
<li><a href="#ediscoverydatasource-attributes">EdiscoveryDataSource Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.activateapps" href="index.html">cvpysdk.activateapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations">EdiscoveryClientOperations</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.associations" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.associations">associations</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.configure_task" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.configure_task">configure_task</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.do_document_task" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.do_document_task">do_document_task</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.export" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.export">export</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.form_search_params" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.form_search_params">form_search_params</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_ediscovery_client_details" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_ediscovery_client_details">get_ediscovery_client_details</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_ediscovery_project_details" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_ediscovery_project_details">get_ediscovery_project_details</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_handler_id" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_handler_id">get_handler_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_job_history" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_job_history">get_job_history</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_job_status" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.get_job_status">get_job_status</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.refresh" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.schedule" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.schedule">schedule</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.search" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.search">search</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.share" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.share">share</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.start_job" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.start_job">start_job</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.task_workflow_operation" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.task_workflow_operation">task_workflow_operation</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.wait_for_collection_job" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.wait_for_collection_job">wait_for_collection_job</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.wait_for_export" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClientOperations.wait_for_export">wait_for_export</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients">EdiscoveryClients</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.add" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.add">add</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.delete" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.get_ediscovery_client_group_details" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.get_ediscovery_client_group_details">get_ediscovery_client_group_details</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.get_ediscovery_clients" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.get_ediscovery_clients">get_ediscovery_clients</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.get_ediscovery_projects" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryClients.get_ediscovery_projects">get_ediscovery_projects</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources">EdiscoveryDataSources</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.add_fs_data_source" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.add_fs_data_source">add_fs_data_source</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.add_o365_sdg_data_source" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.add_o365_sdg_data_source">add_o365_sdg_data_source</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.client_id" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.client_id">client_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.client_targetapp" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.client_targetapp">client_targetapp</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.data_sources" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.data_sources">data_sources</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.delete" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.ediscovery_client_props" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.ediscovery_client_props">ediscovery_client_props</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.get" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.get_datasource_document_count" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.get_datasource_document_count">get_datasource_document_count</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.has_data_source" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.has_data_source">has_data_source</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.refresh" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.total_documents" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDataSources.total_documents">total_documents</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource">EdiscoveryDatasource</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.client_id" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.client_id">client_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.cloud_id" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.cloud_id">cloud_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.computed_core_name" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.computed_core_name">computed_core_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.core_id" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.core_id">core_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.core_name" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.core_name">core_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.crawl_type" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.crawl_type">crawl_type</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.crawl_type_name" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.crawl_type_name">crawl_type_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_id" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_id">data_source_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_name" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_name">data_source_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_props" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_props">data_source_props</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_type" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_type">data_source_type</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_type_id" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.data_source_type_id">data_source_type_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.export" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.export">export</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.get_active_jobs" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.get_active_jobs">get_active_jobs</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.get_job_history" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.get_job_history">get_job_history</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.index_server_node_client_id" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.index_server_node_client_id">index_server_node_client_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.name" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.name">name</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.plan_id" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.plan_id">plan_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.refresh" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.review_action" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.review_action">review_action</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.search" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.search">search</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.sensitive_files_count" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.sensitive_files_count">sensitive_files_count</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.start_collection" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.start_collection">start_collection</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.tag_items" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.tag_items">tag_items</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.total_documents" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.total_documents">total_documents</a></code></li>
<li><code><a title="cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.wait_for_export" href="#cvpysdk.activateapps.ediscovery_utils.EdiscoveryDatasource.wait_for_export">wait_for_export</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>