<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.reports.report API documentation</title>
<meta name="description" content="Module for performing operations on classic Reports …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.reports.report</code></h1>
</header>
<section id="section-intro">
<p>Module for performing operations on classic Reports.</p>
<p>Report
:
Class for selecting different options on report and generate the report.
BackupJobSummary
:
Generate backup job summary report.
FormatType
:
Use this Enum class to provide different file extension.</p>
<h2 id="report">Report</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
Initialize the Report instance for the
commcell</p>
<p>backup_job_summary()
&ndash;
Returns backup job summary instance</p>
<p>set_format(format_type)
&ndash;
sets specified file extension for the report
to be generated</p>
<p>select_local_drive(report_copy_location, client_name) &ndash;
Selects local drive as report generation
location for specified client</p>
<p>select_network_share()
&ndash;
select network share as location</p>
<p>set_report_custom_name(name)
&ndash;
sets custom report name</p>
<p>run_report()
&ndash;
Generates the report</p>
<h2 id="backupjobsummary">Backupjobsummary</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
Initialize the backup job summary report object</p>
<p>select_protected_objects()
&ndash;
Select protected object option</p>
<p>set_last_hours(hours)
&ndash;
Jobs to be included since n hours</p>
<p>set_last_days(hours)
&ndash;
Jobs to be included since n days</p>
<p>select_computers(clients, client_groups)
&ndash;
select specific clients and clientgroups</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L1-L408" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Module for performing operations on classic Reports.

Report              :  Class for selecting different options on report and generate the report.
BackupJobSummary    :  Generate backup job summary report.
FormatType          :  Use this Enum class to provide different file extension.


Report:

     __init__(commcell_object)                   --  Initialize the Report instance for the
                                                     commcell

     backup_job_summary()                        --  Returns backup job summary instance

     set_format(format_type)                     --  sets specified file extension for the report
                                                     to be generated

     select_local_drive(report_copy_location, client_name) --  Selects local drive as report generation
                                                     location for specified client

     select_network_share()                      --  select network share as location

     set_report_custom_name(name)                --  sets custom report name

     run_report()                                --  Generates the report


BackupJobSummary:

    __init__(commcell_object)                   --  Initialize the backup job summary report object

     select_protected_objects()                  --  Select protected object option

     set_last_hours(hours)                       --  Jobs to be included since n hours

     set_last_days(hours)                        --  Jobs to be included since n days

     select_computers(clients, client_groups)    --  select specific clients and clientgroups


&#34;&#34;&#34;

from enum import Enum
from cvpysdk.exception import SDKException


class FormatType(Enum):
    &#34;&#34;&#34;Types of output format&#34;&#34;&#34;
    HTML = 1
    PDF = 6
    TEXT = 2
    XML = 12


class Report:
    &#34;&#34;&#34;Operations on classic report&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34; Initialize the report object &#34;&#34;&#34;
        self._commcell = commcell
        self._request_json = {}
        self._cvpysdk_commcell_object = commcell._cvpysdk_object
        self._services = commcell._services
        self._report_extension = FormatType.HTML.name
        self._backup_job_summary_report = None

    @property
    def backup_job_summary(self):
        &#34;&#34;&#34;Returns object of backup job summary report&#34;&#34;&#34;
        if self._backup_job_summary_report is None:
            self._backup_job_summary_report = BackupJobSummary(self._commcell)
        return self._backup_job_summary_report

    def set_format(self, format_type):
        &#34;&#34;&#34;Sets the output format of a report
        Args:
            format_type (FormatType): set file extension using Enum class FormatType
        &#34;&#34;&#34;
        for each_format_type in FormatType:
            if each_format_type.name == format_type.name:
                self._report_extension = each_format_type.name
                self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;]\
                [&#39;reportOption&#39;][&#39;commonOpt&#39;][&#39;outputFormat&#39;][&#39;outputType&#39;] = \
                    str(each_format_type.value)
                return
        raise Exception(&#34;Invalid format type,format should be one among the type in FormatType&#34;)

    def select_local_drive(self, report_copy_location, client_name=None):
        &#34;&#34;&#34;Select local drive
        Args:
            client_name          (String)        --       Name of the client
            report_copy_location (String)        --       location where report need to be saved
        &#34;&#34;&#34;
        if not client_name:
            client_name = self._commcell.commserv_name
        else:
            if not self._commcell.clients.has_client(client_name):
                raise Exception(f&#34;Client [{client_name}] does not exist&#34;)
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;]\
        [&#39;commonOpt&#39;][&#39;savedTo&#39;][&#39;reportSavedToClient&#39;][&#39;clientName&#39;] = client_name

        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;commonOpt&#39;][&#39;savedTo&#39;][&#39;locationURL&#39;] = report_copy_location

    def select_network_share(self):
        &#34;&#34;&#34;Select network share&#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;]\
            [&#39;commonOpt&#39;][&#39;savedTo&#39;][&#39;isNetworkDrive&#39;] = 1

    def set_report_custom_name(self, name):
        &#34;&#34;&#34; Sets report custom name
        Args:
            name(String)               --       Custom name of the report
        &#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;]\
        [&#39;commonOpt&#39;][&#39;reportCustomName&#39;] = (name + &#34;.&#34; + self._report_extension)

    def run_report(self):
        &#34;&#34;&#34; Executes the report
        Returns:
            str: Job ID
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_commcell_object.make_request(&#39;POST&#39;,
                                                                    self._services[&#39;CREATE_TASK&#39;],
                                                                    self._request_json)
        try:
            return response.json()[&#39;jobIds&#39;][0]
        except Exception:
            raise SDKException(&#34;RunReportError&#34;, &#39;101&#39;, response.json()[&#34;errorMessage&#34;])


class BackupJobSummary(Report):
    &#34;&#34;&#34;Operations on backup job summary report&#34;&#34;&#34;
    def __init__(self, commcell_object):
        &#34;&#34;&#34; Initialize the backup job summary report object &#34;&#34;&#34;
        super().__init__(commcell_object)
        self._request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: commcell_object.commcell_username,
                    &#34;sequenceNumber&#34;: 0,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;appGroup&#34;: {},
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4004
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;reportOption&#34;: {
                                    &#34;includeClientGroup&#34;: False,
                                    &#34;showHiddenStoragePolicies&#34;: False,
                                    &#34;showJobsWithFailedFailesOnly&#34;: False,
                                    &#34;showGlobalStoragePolicies&#34;: False,
                                    &#34;allowDynamicContent&#34;: False,
                                    &#34;jobOption&#34;: False,
                                    &#34;excludeHiddenSubclients&#34;: False,
                                    &#34;failedFilesThreshold&#34;: 0,
                                    &#34;mediaAgentList&#34;: [
                                        {
                                            &#34;_type_&#34;: 11,
                                            &#34;flags&#34;: {
                                                &#34;include&#34;: True
                                            }
                                        }
                                    ],
                                    &#34;storagePolicyCopyList&#34;: [
                                        {
                                            &#34;_type_&#34;: 17,
                                            &#34;allCopies&#34;: True
                                        }
                                    ],
                                    &#34;commonOpt&#34;: {
                                        &#34;dateFormat&#34;: &#34;mm/dd/yyyy&#34;,
                                        &#34;overrideDateTimeFormat&#34;: 0,
                                        &#34;reportType&#34;: 7715,
                                        &#34;summaryOnly&#34;: False,
                                        &#34;reportCustomName&#34;: &#34;&#34;,
                                        &#34;emailType&#34;: 2,
                                        &#34;timeFormat&#34;: &#34;hh:mm:ss am/pm&#34;,
                                        &#34;onCS&#34;: True,
                                        &#34;savedTo&#34;: {
                                            &#34;locationURL&#34;: &#34;&#34;,
                                            &#34;ftpUploadLocation&#34;: &#34;Commvault Reports&#34;,
                                            &#34;uploadAsCabinetFile&#34;: False,
                                            &#34;isNetworkDrive&#34;: False,
                                            &#34;reportSavedToClient&#34;: {
                                                &#34;hostName&#34;: &#34;&#34;,
                                                &#34;clientName&#34;: &#34;&#34;,
                                                &#34;_type_&#34;: 3
                                            },
                                            &#34;ftpDetails&#34;: {}
                                        },
                                        &#34;locale&#34;: {
                                            &#34;_type_&#34;: 66,
                                            &#34;LCID&#34;: 3081,
                                            &#34;displayString&#34;: &#34;English-Australia&#34;,
                                            &#34;locale&#34;: &#34;en-au&#34;,
                                            &#34;localeName&#34;: &#34;en&#34;,
                                            &#34;localeId&#34;: 0
                                        },
                                        &#34;outputFormat&#34;: {
                                            &#34;textDelimiter&#34;: &#34;\t&#34;,
                                            &#34;outputType&#34;: 1,
                                            &#34;isNetworkDrive&#34;: False
                                        }
                                    },
                                    &#34;computerSelectionList&#34;: {
                                        &#34;includeAll&#34;: True,
                                        &#34;clientGroupList&#34;: [
                                        ],
                                        &#34;clientList&#34;: [
                                        ]

                                    },
                                    &#34;jobSummaryReport&#34;: {
                                        &#34;subClientDescription&#34;: &#34;&#34;,
                                        &#34;subclientFilter&#34;: False,
                                        &#34;filterOnSubClientDesc&#34;: False,
                                        &#34;groupBy&#34;: 2,
                                        &#34;rptSelections&#34;: {
                                            &#34;includeSnapProtectionJobsOnly&#34;: False,
                                            &#34;includeArchivedPSTs&#34;: False,
                                            &#34;includeProtectedDatabases&#34;: False,
                                            &#34;includeClientDescription&#34;: False,
                                            &#34;includeBackupFilesOnly&#34;: False,
                                            &#34;description&#34;: True,
                                            &#34;stubbedFiles&#34;: False,
                                            &#34;includeFailedSkippedMailboxes&#34;: False,
                                            &#34;includeDisabledActivityClients&#34;: True,
                                            &#34;sizeChangePercentage&#34;: False,
                                            &#34;jobAttempts&#34;: False,
                                            &#34;includePerformanceJobsOnly&#34;: False,
                                            &#34;includeReferenceCopyClientMap&#34;: False,
                                            &#34;subclientContent&#34;: False,
                                            &#34;failedObjects&#34;: False,
                                            &#34;includeProtectedVMs&#34;: True,
                                            &#34;associatedEvent&#34;: False,
                                            &#34;mediaAgents&#34;: False,
                                            &#34;agedData&#34;: False,
                                            &#34;IncBackupCopyJobsOnly&#34;: False,
                                            &#34;subclientFilters&#34;: False,
                                            &#34;protectedObjects&#34;: False,
                                            &#34;IncBackupCopyJobs&#34;: False,
                                            &#34;contentIndexingFailures&#34;: False,
                                            &#34;subclientJobOpt&#34;: 0,
                                            &#34;associatedMedia&#34;: False,
                                            &#34;storagePolicy&#34;: False,
                                            &#34;numberOfHours&#34;: 0,
                                            &#34;initializingUser&#34;: False,
                                            &#34;includeDeconfiguredClients&#34;: True,
                                            &#34;numberOfObjects&#34;: 100,
                                            &#34;failureReason&#34;: True,
                                            &#34;IncludeMediaDeletedJobs&#34;: False,
                                            &#34;drive&#34;: False
                                },
                                &#34;jobOptions&#34;: {
                                    &#34;numberOfMostFreqErrors&#34;: 0,
                                    &#34;sizeUnit&#34;: 0,
                                    &#34;isThroughputInMB&#34;: False,
                                    &#34;isCommserveTimeZone&#34;: True,
                                    &#34;retentionType&#34;: {
                                        &#34;basicRetention&#34;: False,
                                        &#34;manualRetention&#34;: False,
                                        &#34;extendedRetention&#34;: False,
                                        &#34;retentionAll&#34;: False
                                    },
                                    &#34;backupTypes&#34;: {
                                        &#34;all&#34;: True,
                                        &#34;syntheticFull&#34;: True,
                                        &#34;automatedSystemRecovery&#34;: False,
                                        &#34;incremental&#34;: True,
                                        &#34;full&#34;: True,
                                        &#34;differential&#34;: True
                                    },
                                    &#34;jobStatus&#34;: {
                                        &#34;all&#34;: True
                                    },
                                    &#34;increaseInDataSize&#34;: {
                                        &#34;value&#34;: 10,
                                        &#34;selected&#34;: False
                                    },
                                    &#34;decreaseInDataSize&#34;: {
                                        &#34;value&#34;: 10,
                                        &#34;selected&#34;: False
                                        }
                                }
                                    },
                            &#34;agentList&#34;: [
                                {
                                    &#34;_type_&#34;: 4,
                                    &#34;flags&#34;: {
                                        &#34;include&#34;: True
                                    }
                                }
                            ],
                            &#34;timeRangeOption&#34;: {
                                &#34;type&#34;: 13,
                                &#34;_type_&#34;: 54,
                                &#34;TimeZoneID&#34;: 42,
                                &#34;toTime&#34;: 86400
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

    def select_protected_objects(self):
        &#34;&#34;&#34;select protected objects&#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;jobSummaryReport&#39;][&#39;rptSelections&#39;][&#39;protectedObjects&#39;] = True

    def __set_include_all(self, status=True):
        &#34;&#34;&#34;
        Set include all computers true/false if any client/client group are getting selected
        Args:
                status     (Boolean)    --  Set True to include all the clients otherwise set false

        &#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;computerSelectionList&#39;][&#39;includeAll&#39;] = status

    def __select_client_groups(self, client_groups):
        &#34;&#34;&#34;
        Select client groups
        Args:
                client_groups     (List)    --  list of clientgroups
        &#34;&#34;&#34;
        client_group_list_dict = []
        for each_client_group in client_groups:
            client_group_list_dict.append({&#34;clientGroupName&#34;: each_client_group})
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;computerSelectionList&#39;][&#39;clientGroupList&#39;] = client_group_list_dict

    def __select_clients(self, client_list):
        &#34;&#34;&#34;
        Select client clients
         Args:
                client_list     (List)    --  list of clients
        &#34;&#34;&#34;
        client_list_dict = []
        for each_client in client_list:
            client_list_dict.append({&#34;clientName&#34;: each_client})
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;computerSelectionList&#39;][&#39;clientList&#39;] = client_list_dict

    def set_last_hours(self, number_of_hours=24):
        &#34;&#34;&#34;
        Set time range to generate report since n number of hours
        Args:
                number_of_hours     (Int)    --  number of hours
        &#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;timeRangeOption&#39;][&#39;type&#39;] = 13
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;timeRangeOption&#39;][&#39;toTimeValue&#39;] = str(number_of_hours)

    def set_last_days(self, number_of_days=24):
        &#34;&#34;&#34;
        Set time range to generate report since n number of days
        Args:
                number_of_hours     (Int)    --  number of hours
        &#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;timeRangeOption&#39;][&#39;type&#39;] = 11
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;timeRangeOption&#39;][&#39;toTimeValue&#39;] = str(number_of_days)

    def select_computers(self, clients=None, client_groups=None):
        &#34;&#34;&#34;
        Select clients and client groups for generating the report
        Args:
                clients           (List)    --  List of clients
                client_groups     (List)    --  List of client groups
        &#34;&#34;&#34;
        self.__set_include_all(status=False)
        if clients:
            self.__select_clients(clients)
        if client_groups:
            self.__select_client_groups(client_groups)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.reports.report.BackupJobSummary"><code class="flex name class">
<span>class <span class="ident">BackupJobSummary</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Operations on backup job summary report</p>
<p>Initialize the backup job summary report object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L148-L408" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class BackupJobSummary(Report):
    &#34;&#34;&#34;Operations on backup job summary report&#34;&#34;&#34;
    def __init__(self, commcell_object):
        &#34;&#34;&#34; Initialize the backup job summary report object &#34;&#34;&#34;
        super().__init__(commcell_object)
        self._request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: commcell_object.commcell_username,
                    &#34;sequenceNumber&#34;: 0,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;appGroup&#34;: {},
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4004
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;reportOption&#34;: {
                                    &#34;includeClientGroup&#34;: False,
                                    &#34;showHiddenStoragePolicies&#34;: False,
                                    &#34;showJobsWithFailedFailesOnly&#34;: False,
                                    &#34;showGlobalStoragePolicies&#34;: False,
                                    &#34;allowDynamicContent&#34;: False,
                                    &#34;jobOption&#34;: False,
                                    &#34;excludeHiddenSubclients&#34;: False,
                                    &#34;failedFilesThreshold&#34;: 0,
                                    &#34;mediaAgentList&#34;: [
                                        {
                                            &#34;_type_&#34;: 11,
                                            &#34;flags&#34;: {
                                                &#34;include&#34;: True
                                            }
                                        }
                                    ],
                                    &#34;storagePolicyCopyList&#34;: [
                                        {
                                            &#34;_type_&#34;: 17,
                                            &#34;allCopies&#34;: True
                                        }
                                    ],
                                    &#34;commonOpt&#34;: {
                                        &#34;dateFormat&#34;: &#34;mm/dd/yyyy&#34;,
                                        &#34;overrideDateTimeFormat&#34;: 0,
                                        &#34;reportType&#34;: 7715,
                                        &#34;summaryOnly&#34;: False,
                                        &#34;reportCustomName&#34;: &#34;&#34;,
                                        &#34;emailType&#34;: 2,
                                        &#34;timeFormat&#34;: &#34;hh:mm:ss am/pm&#34;,
                                        &#34;onCS&#34;: True,
                                        &#34;savedTo&#34;: {
                                            &#34;locationURL&#34;: &#34;&#34;,
                                            &#34;ftpUploadLocation&#34;: &#34;Commvault Reports&#34;,
                                            &#34;uploadAsCabinetFile&#34;: False,
                                            &#34;isNetworkDrive&#34;: False,
                                            &#34;reportSavedToClient&#34;: {
                                                &#34;hostName&#34;: &#34;&#34;,
                                                &#34;clientName&#34;: &#34;&#34;,
                                                &#34;_type_&#34;: 3
                                            },
                                            &#34;ftpDetails&#34;: {}
                                        },
                                        &#34;locale&#34;: {
                                            &#34;_type_&#34;: 66,
                                            &#34;LCID&#34;: 3081,
                                            &#34;displayString&#34;: &#34;English-Australia&#34;,
                                            &#34;locale&#34;: &#34;en-au&#34;,
                                            &#34;localeName&#34;: &#34;en&#34;,
                                            &#34;localeId&#34;: 0
                                        },
                                        &#34;outputFormat&#34;: {
                                            &#34;textDelimiter&#34;: &#34;\t&#34;,
                                            &#34;outputType&#34;: 1,
                                            &#34;isNetworkDrive&#34;: False
                                        }
                                    },
                                    &#34;computerSelectionList&#34;: {
                                        &#34;includeAll&#34;: True,
                                        &#34;clientGroupList&#34;: [
                                        ],
                                        &#34;clientList&#34;: [
                                        ]

                                    },
                                    &#34;jobSummaryReport&#34;: {
                                        &#34;subClientDescription&#34;: &#34;&#34;,
                                        &#34;subclientFilter&#34;: False,
                                        &#34;filterOnSubClientDesc&#34;: False,
                                        &#34;groupBy&#34;: 2,
                                        &#34;rptSelections&#34;: {
                                            &#34;includeSnapProtectionJobsOnly&#34;: False,
                                            &#34;includeArchivedPSTs&#34;: False,
                                            &#34;includeProtectedDatabases&#34;: False,
                                            &#34;includeClientDescription&#34;: False,
                                            &#34;includeBackupFilesOnly&#34;: False,
                                            &#34;description&#34;: True,
                                            &#34;stubbedFiles&#34;: False,
                                            &#34;includeFailedSkippedMailboxes&#34;: False,
                                            &#34;includeDisabledActivityClients&#34;: True,
                                            &#34;sizeChangePercentage&#34;: False,
                                            &#34;jobAttempts&#34;: False,
                                            &#34;includePerformanceJobsOnly&#34;: False,
                                            &#34;includeReferenceCopyClientMap&#34;: False,
                                            &#34;subclientContent&#34;: False,
                                            &#34;failedObjects&#34;: False,
                                            &#34;includeProtectedVMs&#34;: True,
                                            &#34;associatedEvent&#34;: False,
                                            &#34;mediaAgents&#34;: False,
                                            &#34;agedData&#34;: False,
                                            &#34;IncBackupCopyJobsOnly&#34;: False,
                                            &#34;subclientFilters&#34;: False,
                                            &#34;protectedObjects&#34;: False,
                                            &#34;IncBackupCopyJobs&#34;: False,
                                            &#34;contentIndexingFailures&#34;: False,
                                            &#34;subclientJobOpt&#34;: 0,
                                            &#34;associatedMedia&#34;: False,
                                            &#34;storagePolicy&#34;: False,
                                            &#34;numberOfHours&#34;: 0,
                                            &#34;initializingUser&#34;: False,
                                            &#34;includeDeconfiguredClients&#34;: True,
                                            &#34;numberOfObjects&#34;: 100,
                                            &#34;failureReason&#34;: True,
                                            &#34;IncludeMediaDeletedJobs&#34;: False,
                                            &#34;drive&#34;: False
                                },
                                &#34;jobOptions&#34;: {
                                    &#34;numberOfMostFreqErrors&#34;: 0,
                                    &#34;sizeUnit&#34;: 0,
                                    &#34;isThroughputInMB&#34;: False,
                                    &#34;isCommserveTimeZone&#34;: True,
                                    &#34;retentionType&#34;: {
                                        &#34;basicRetention&#34;: False,
                                        &#34;manualRetention&#34;: False,
                                        &#34;extendedRetention&#34;: False,
                                        &#34;retentionAll&#34;: False
                                    },
                                    &#34;backupTypes&#34;: {
                                        &#34;all&#34;: True,
                                        &#34;syntheticFull&#34;: True,
                                        &#34;automatedSystemRecovery&#34;: False,
                                        &#34;incremental&#34;: True,
                                        &#34;full&#34;: True,
                                        &#34;differential&#34;: True
                                    },
                                    &#34;jobStatus&#34;: {
                                        &#34;all&#34;: True
                                    },
                                    &#34;increaseInDataSize&#34;: {
                                        &#34;value&#34;: 10,
                                        &#34;selected&#34;: False
                                    },
                                    &#34;decreaseInDataSize&#34;: {
                                        &#34;value&#34;: 10,
                                        &#34;selected&#34;: False
                                        }
                                }
                                    },
                            &#34;agentList&#34;: [
                                {
                                    &#34;_type_&#34;: 4,
                                    &#34;flags&#34;: {
                                        &#34;include&#34;: True
                                    }
                                }
                            ],
                            &#34;timeRangeOption&#34;: {
                                &#34;type&#34;: 13,
                                &#34;_type_&#34;: 54,
                                &#34;TimeZoneID&#34;: 42,
                                &#34;toTime&#34;: 86400
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

    def select_protected_objects(self):
        &#34;&#34;&#34;select protected objects&#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;jobSummaryReport&#39;][&#39;rptSelections&#39;][&#39;protectedObjects&#39;] = True

    def __set_include_all(self, status=True):
        &#34;&#34;&#34;
        Set include all computers true/false if any client/client group are getting selected
        Args:
                status     (Boolean)    --  Set True to include all the clients otherwise set false

        &#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;computerSelectionList&#39;][&#39;includeAll&#39;] = status

    def __select_client_groups(self, client_groups):
        &#34;&#34;&#34;
        Select client groups
        Args:
                client_groups     (List)    --  list of clientgroups
        &#34;&#34;&#34;
        client_group_list_dict = []
        for each_client_group in client_groups:
            client_group_list_dict.append({&#34;clientGroupName&#34;: each_client_group})
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;computerSelectionList&#39;][&#39;clientGroupList&#39;] = client_group_list_dict

    def __select_clients(self, client_list):
        &#34;&#34;&#34;
        Select client clients
         Args:
                client_list     (List)    --  list of clients
        &#34;&#34;&#34;
        client_list_dict = []
        for each_client in client_list:
            client_list_dict.append({&#34;clientName&#34;: each_client})
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;computerSelectionList&#39;][&#39;clientList&#39;] = client_list_dict

    def set_last_hours(self, number_of_hours=24):
        &#34;&#34;&#34;
        Set time range to generate report since n number of hours
        Args:
                number_of_hours     (Int)    --  number of hours
        &#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;timeRangeOption&#39;][&#39;type&#39;] = 13
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;timeRangeOption&#39;][&#39;toTimeValue&#39;] = str(number_of_hours)

    def set_last_days(self, number_of_days=24):
        &#34;&#34;&#34;
        Set time range to generate report since n number of days
        Args:
                number_of_hours     (Int)    --  number of hours
        &#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;timeRangeOption&#39;][&#39;type&#39;] = 11
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;timeRangeOption&#39;][&#39;toTimeValue&#39;] = str(number_of_days)

    def select_computers(self, clients=None, client_groups=None):
        &#34;&#34;&#34;
        Select clients and client groups for generating the report
        Args:
                clients           (List)    --  List of clients
                client_groups     (List)    --  List of client groups
        &#34;&#34;&#34;
        self.__set_include_all(status=False)
        if clients:
            self.__select_clients(clients)
        if client_groups:
            self.__select_client_groups(client_groups)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.reports.report.Report" href="#cvpysdk.reports.report.Report">Report</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.reports.report.BackupJobSummary.select_computers"><code class="name flex">
<span>def <span class="ident">select_computers</span></span>(<span>self, clients=None, client_groups=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Select clients and client groups for generating the report</p>
<h2 id="args">Args</h2>
<p>clients
(List)
&ndash;
List of clients
client_groups
(List)
&ndash;
List of client groups</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L397-L408" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def select_computers(self, clients=None, client_groups=None):
    &#34;&#34;&#34;
    Select clients and client groups for generating the report
    Args:
            clients           (List)    --  List of clients
            client_groups     (List)    --  List of client groups
    &#34;&#34;&#34;
    self.__set_include_all(status=False)
    if clients:
        self.__select_clients(clients)
    if client_groups:
        self.__select_client_groups(client_groups)</code></pre>
</details>
</dd>
<dt id="cvpysdk.reports.report.BackupJobSummary.select_protected_objects"><code class="name flex">
<span>def <span class="ident">select_protected_objects</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>select protected objects</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L336-L339" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def select_protected_objects(self):
    &#34;&#34;&#34;select protected objects&#34;&#34;&#34;
    self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
        [&#39;jobSummaryReport&#39;][&#39;rptSelections&#39;][&#39;protectedObjects&#39;] = True</code></pre>
</details>
</dd>
<dt id="cvpysdk.reports.report.BackupJobSummary.set_last_days"><code class="name flex">
<span>def <span class="ident">set_last_days</span></span>(<span>self, number_of_days=24)</span>
</code></dt>
<dd>
<div class="desc"><p>Set time range to generate report since n number of days</p>
<h2 id="args">Args</h2>
<p>number_of_hours
(Int)
&ndash;
number of hours</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L386-L395" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_last_days(self, number_of_days=24):
    &#34;&#34;&#34;
    Set time range to generate report since n number of days
    Args:
            number_of_hours     (Int)    --  number of hours
    &#34;&#34;&#34;
    self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
        [&#39;timeRangeOption&#39;][&#39;type&#39;] = 11
    self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
        [&#39;timeRangeOption&#39;][&#39;toTimeValue&#39;] = str(number_of_days)</code></pre>
</details>
</dd>
<dt id="cvpysdk.reports.report.BackupJobSummary.set_last_hours"><code class="name flex">
<span>def <span class="ident">set_last_hours</span></span>(<span>self, number_of_hours=24)</span>
</code></dt>
<dd>
<div class="desc"><p>Set time range to generate report since n number of hours</p>
<h2 id="args">Args</h2>
<p>number_of_hours
(Int)
&ndash;
number of hours</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L375-L384" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_last_hours(self, number_of_hours=24):
    &#34;&#34;&#34;
    Set time range to generate report since n number of hours
    Args:
            number_of_hours     (Int)    --  number of hours
    &#34;&#34;&#34;
    self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
        [&#39;timeRangeOption&#39;][&#39;type&#39;] = 13
    self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
        [&#39;timeRangeOption&#39;][&#39;toTimeValue&#39;] = str(number_of_hours)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.reports.report.Report" href="#cvpysdk.reports.report.Report">Report</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.reports.report.Report.backup_job_summary" href="#cvpysdk.reports.report.Report.backup_job_summary">backup_job_summary</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.run_report" href="#cvpysdk.reports.report.Report.run_report">run_report</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.select_local_drive" href="#cvpysdk.reports.report.Report.select_local_drive">select_local_drive</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.select_network_share" href="#cvpysdk.reports.report.Report.select_network_share">select_network_share</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.set_format" href="#cvpysdk.reports.report.Report.set_format">set_format</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.set_report_custom_name" href="#cvpysdk.reports.report.Report.set_report_custom_name">set_report_custom_name</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.reports.report.FormatType"><code class="flex name class">
<span>class <span class="ident">FormatType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Types of output format</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L63-L68" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class FormatType(Enum):
    &#34;&#34;&#34;Types of output format&#34;&#34;&#34;
    HTML = 1
    PDF = 6
    TEXT = 2
    XML = 12</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.reports.report.FormatType.HTML"><code class="name">var <span class="ident">HTML</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.reports.report.FormatType.PDF"><code class="name">var <span class="ident">PDF</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.reports.report.FormatType.TEXT"><code class="name">var <span class="ident">TEXT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.reports.report.FormatType.XML"><code class="name">var <span class="ident">XML</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.reports.report.Report"><code class="flex name class">
<span>class <span class="ident">Report</span></span>
<span>(</span><span>commcell)</span>
</code></dt>
<dd>
<div class="desc"><p>Operations on classic report</p>
<p>Initialize the report object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L71-L145" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Report:
    &#34;&#34;&#34;Operations on classic report&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34; Initialize the report object &#34;&#34;&#34;
        self._commcell = commcell
        self._request_json = {}
        self._cvpysdk_commcell_object = commcell._cvpysdk_object
        self._services = commcell._services
        self._report_extension = FormatType.HTML.name
        self._backup_job_summary_report = None

    @property
    def backup_job_summary(self):
        &#34;&#34;&#34;Returns object of backup job summary report&#34;&#34;&#34;
        if self._backup_job_summary_report is None:
            self._backup_job_summary_report = BackupJobSummary(self._commcell)
        return self._backup_job_summary_report

    def set_format(self, format_type):
        &#34;&#34;&#34;Sets the output format of a report
        Args:
            format_type (FormatType): set file extension using Enum class FormatType
        &#34;&#34;&#34;
        for each_format_type in FormatType:
            if each_format_type.name == format_type.name:
                self._report_extension = each_format_type.name
                self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;]\
                [&#39;reportOption&#39;][&#39;commonOpt&#39;][&#39;outputFormat&#39;][&#39;outputType&#39;] = \
                    str(each_format_type.value)
                return
        raise Exception(&#34;Invalid format type,format should be one among the type in FormatType&#34;)

    def select_local_drive(self, report_copy_location, client_name=None):
        &#34;&#34;&#34;Select local drive
        Args:
            client_name          (String)        --       Name of the client
            report_copy_location (String)        --       location where report need to be saved
        &#34;&#34;&#34;
        if not client_name:
            client_name = self._commcell.commserv_name
        else:
            if not self._commcell.clients.has_client(client_name):
                raise Exception(f&#34;Client [{client_name}] does not exist&#34;)
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;]\
        [&#39;commonOpt&#39;][&#39;savedTo&#39;][&#39;reportSavedToClient&#39;][&#39;clientName&#39;] = client_name

        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
            [&#39;commonOpt&#39;][&#39;savedTo&#39;][&#39;locationURL&#39;] = report_copy_location

    def select_network_share(self):
        &#34;&#34;&#34;Select network share&#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;]\
            [&#39;commonOpt&#39;][&#39;savedTo&#39;][&#39;isNetworkDrive&#39;] = 1

    def set_report_custom_name(self, name):
        &#34;&#34;&#34; Sets report custom name
        Args:
            name(String)               --       Custom name of the report
        &#34;&#34;&#34;
        self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;]\
        [&#39;commonOpt&#39;][&#39;reportCustomName&#39;] = (name + &#34;.&#34; + self._report_extension)

    def run_report(self):
        &#34;&#34;&#34; Executes the report
        Returns:
            str: Job ID
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_commcell_object.make_request(&#39;POST&#39;,
                                                                    self._services[&#39;CREATE_TASK&#39;],
                                                                    self._request_json)
        try:
            return response.json()[&#39;jobIds&#39;][0]
        except Exception:
            raise SDKException(&#34;RunReportError&#34;, &#39;101&#39;, response.json()[&#34;errorMessage&#34;])</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.reports.report.BackupJobSummary" href="#cvpysdk.reports.report.BackupJobSummary">BackupJobSummary</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.reports.report.Report.backup_job_summary"><code class="name">var <span class="ident">backup_job_summary</span></code></dt>
<dd>
<div class="desc"><p>Returns object of backup job summary report</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L83-L88" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_job_summary(self):
    &#34;&#34;&#34;Returns object of backup job summary report&#34;&#34;&#34;
    if self._backup_job_summary_report is None:
        self._backup_job_summary_report = BackupJobSummary(self._commcell)
    return self._backup_job_summary_report</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.reports.report.Report.run_report"><code class="name flex">
<span>def <span class="ident">run_report</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Executes the report</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>str</code></dt>
<dd>Job ID</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L134-L145" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_report(self):
    &#34;&#34;&#34; Executes the report
    Returns:
        str: Job ID
    &#34;&#34;&#34;
    flag, response = self._cvpysdk_commcell_object.make_request(&#39;POST&#39;,
                                                                self._services[&#39;CREATE_TASK&#39;],
                                                                self._request_json)
    try:
        return response.json()[&#39;jobIds&#39;][0]
    except Exception:
        raise SDKException(&#34;RunReportError&#34;, &#39;101&#39;, response.json()[&#34;errorMessage&#34;])</code></pre>
</details>
</dd>
<dt id="cvpysdk.reports.report.Report.select_local_drive"><code class="name flex">
<span>def <span class="ident">select_local_drive</span></span>(<span>self, report_copy_location, client_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Select local drive</p>
<h2 id="args">Args</h2>
<p>client_name
(String)
&ndash;
Name of the client
report_copy_location (String)
&ndash;
location where report need to be saved</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L104-L119" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def select_local_drive(self, report_copy_location, client_name=None):
    &#34;&#34;&#34;Select local drive
    Args:
        client_name          (String)        --       Name of the client
        report_copy_location (String)        --       location where report need to be saved
    &#34;&#34;&#34;
    if not client_name:
        client_name = self._commcell.commserv_name
    else:
        if not self._commcell.clients.has_client(client_name):
            raise Exception(f&#34;Client [{client_name}] does not exist&#34;)
    self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;]\
    [&#39;commonOpt&#39;][&#39;savedTo&#39;][&#39;reportSavedToClient&#39;][&#39;clientName&#39;] = client_name

    self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;] \
        [&#39;commonOpt&#39;][&#39;savedTo&#39;][&#39;locationURL&#39;] = report_copy_location</code></pre>
</details>
</dd>
<dt id="cvpysdk.reports.report.Report.select_network_share"><code class="name flex">
<span>def <span class="ident">select_network_share</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Select network share</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L121-L124" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def select_network_share(self):
    &#34;&#34;&#34;Select network share&#34;&#34;&#34;
    self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;]\
        [&#39;commonOpt&#39;][&#39;savedTo&#39;][&#39;isNetworkDrive&#39;] = 1</code></pre>
</details>
</dd>
<dt id="cvpysdk.reports.report.Report.set_format"><code class="name flex">
<span>def <span class="ident">set_format</span></span>(<span>self, format_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the output format of a report</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>format_type</code></strong> :&ensp;<code><a title="cvpysdk.reports.report.FormatType" href="#cvpysdk.reports.report.FormatType">FormatType</a></code></dt>
<dd>set file extension using Enum class FormatType</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L90-L102" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_format(self, format_type):
    &#34;&#34;&#34;Sets the output format of a report
    Args:
        format_type (FormatType): set file extension using Enum class FormatType
    &#34;&#34;&#34;
    for each_format_type in FormatType:
        if each_format_type.name == format_type.name:
            self._report_extension = each_format_type.name
            self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;]\
            [&#39;reportOption&#39;][&#39;commonOpt&#39;][&#39;outputFormat&#39;][&#39;outputType&#39;] = \
                str(each_format_type.value)
            return
    raise Exception(&#34;Invalid format type,format should be one among the type in FormatType&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.reports.report.Report.set_report_custom_name"><code class="name flex">
<span>def <span class="ident">set_report_custom_name</span></span>(<span>self, name)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets report custom name</p>
<h2 id="args">Args</h2>
<p>name(String)
&ndash;
Custom name of the report</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/reports/report.py#L126-L132" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_report_custom_name(self, name):
    &#34;&#34;&#34; Sets report custom name
    Args:
        name(String)               --       Custom name of the report
    &#34;&#34;&#34;
    self._request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;reportOption&#39;]\
    [&#39;commonOpt&#39;][&#39;reportCustomName&#39;] = (name + &#34;.&#34; + self._report_extension)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.reports" href="index.html">cvpysdk.reports</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.reports.report.BackupJobSummary" href="#cvpysdk.reports.report.BackupJobSummary">BackupJobSummary</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.reports.report.BackupJobSummary.select_computers" href="#cvpysdk.reports.report.BackupJobSummary.select_computers">select_computers</a></code></li>
<li><code><a title="cvpysdk.reports.report.BackupJobSummary.select_protected_objects" href="#cvpysdk.reports.report.BackupJobSummary.select_protected_objects">select_protected_objects</a></code></li>
<li><code><a title="cvpysdk.reports.report.BackupJobSummary.set_last_days" href="#cvpysdk.reports.report.BackupJobSummary.set_last_days">set_last_days</a></code></li>
<li><code><a title="cvpysdk.reports.report.BackupJobSummary.set_last_hours" href="#cvpysdk.reports.report.BackupJobSummary.set_last_hours">set_last_hours</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.reports.report.FormatType" href="#cvpysdk.reports.report.FormatType">FormatType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.reports.report.FormatType.HTML" href="#cvpysdk.reports.report.FormatType.HTML">HTML</a></code></li>
<li><code><a title="cvpysdk.reports.report.FormatType.PDF" href="#cvpysdk.reports.report.FormatType.PDF">PDF</a></code></li>
<li><code><a title="cvpysdk.reports.report.FormatType.TEXT" href="#cvpysdk.reports.report.FormatType.TEXT">TEXT</a></code></li>
<li><code><a title="cvpysdk.reports.report.FormatType.XML" href="#cvpysdk.reports.report.FormatType.XML">XML</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.reports.report.Report" href="#cvpysdk.reports.report.Report">Report</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.reports.report.Report.backup_job_summary" href="#cvpysdk.reports.report.Report.backup_job_summary">backup_job_summary</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.run_report" href="#cvpysdk.reports.report.Report.run_report">run_report</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.select_local_drive" href="#cvpysdk.reports.report.Report.select_local_drive">select_local_drive</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.select_network_share" href="#cvpysdk.reports.report.Report.select_network_share">select_network_share</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.set_format" href="#cvpysdk.reports.report.Report.set_format">set_format</a></code></li>
<li><code><a title="cvpysdk.reports.report.Report.set_report_custom_name" href="#cvpysdk.reports.report.Report.set_report_custom_name">set_report_custom_name</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>