# -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

"""
OneDriveClient class is defined in this file.

OneDriveClient:     Class for a single OneDrive for Business client (v2) of the commcell

OneDriveClient
=======

_get_subclient() --  Returns the sub-client object for OneDrive for Business client (v2)

backup_all_users_in_client() -- Run backup for all users present in OneDrive for Business client (v2)

in_place_restore()  --  Run an inplace restore of specified users for OneDrive for business client (v2)

out_of_place_restore()  --  Run an out-of-place restore of specified users for OneDrive for business client (v2)

disk_restore()  --  Runs disk restore of specified users for OneDrive for business client (v2)

modify_server_plan()  --  Method to Modify Server Plan Associated to Client

modify_job_results_directory()  --  Method to modify job results directory

run_trueup_for_whole_client()  --  Method to run TrueUp for whole client

read_trueup_items_for_whole_client()  --  Method to read data from TrueUp API for whole client

extract_last_sync_missing_items() --  Extract LastSyncMissingItems values from JSON data for TrueUp API

"""


from ..client import Client, SDKException


class OneDriveClient(Client):
    def __init__(self, commcell_object, client_name, client_id=None):
        """Initialise the OneDrive Client class instance.

            Args:
                commcell_object (object)     --  instance of the Commcell class

                client_name     (str)        --  name of the client

                client_id       (str)        --  id of the client
                                                default: None

            Returns:
                object - instance of the OneDrive Client class
        """
        super(OneDriveClient, self).__init__(
            commcell_object, client_name, client_id)

    def _get_subclient(self):
        """ Returns the sub-client object for OneDrive for Business client

            Returns:
                _subclient (object) -   Subclient object

        """
        _client = self._commcell_object.clients.get(self.client_name)
        _agent = _client.agents.get('Cloud Apps')
        _instance = _agent.instances.get('OneDrive')
        _backupset = _instance.backupsets.get('defaultbackupset')
        _subclient = _backupset.subclients.get('default')
        return _subclient

    def run_trueup_for_whole_client(self):
        """Function to run TrueUp for whole client
        Raises:
            SDKException:

            If TrueUp fails to work for client
        """
        base_url = self._commcell_object.webconsole_hostname
        url = self._services['RUN_TRUEUP'].format(base_url)
        trueup_json = {
            "processinginstructioninfo": {
                "formatFlags": {
                    "skipIdToNameConversion": True
                }
            },
            "isEnterprise": True,
            "discoverySentTypes": [
                20
            ],
            "subclientDetails": {
                "instanceId": 7,  # Used for unique verification of Agent Type, not the same as the instanceId generated by the CommServe
                "subclientName": "default",
                "clientId": int(self.client_id),
                "applicationId": 134,
                "instanceName": "OneDrive",
                "backupsetName": "defaultBackupSet"
            }
        }
        flag, response = self._cvpysdk_object.make_request(method='POST',
                                                           url=url, payload=trueup_json)
        if flag:
            if response.json() and 'errorCode' in response.json():
                error_code = response.json().get('errorCode')
                if error_code != 0:
                    error_message = response.json().get('errorMessage')
                    output_string = f'Failed to run trueup with \nError: {error_message}'
                    raise SDKException('Response', '101', output_string)
        else:
            raise SDKException('Response', '101',
                               self._update_response_(response.text))

    def read_trueup_items_for_whole_client(self):
        """
        Method to read data from TrueUp API for whole client
        Args:
            None
        Returns:
            num_of_items (int) : Number of items in TrueUp response
        Raises:
            SDKException:
            If error occurs while reading TrueUp response
        """

        def extract_last_sync_missing_items(json_data):
            """Extract LastSyncMissingItems values from JSON data

            Args:
                json_data (dict) : JSON data from TrueUp API

            Returns:
                last_sync_missing_items (list) : List of LastSyncMissingItems values from TrueUp API
            """
            last_sync_missing_items = []
            rows = json_data.get('rows', [])

            for row in rows:
                row_data = row.get('row', [])
                if len(row_data) >= 6:  # Ensure there are enough elements
                    last_sync_missing_items.append(int(row_data[5]))
            return last_sync_missing_items

        subclient_id = self._get_subclient().subclient_id
        client_id = self._client_id
        base_url = self._commcell_object.webconsole_hostname
        url = self._services['READ_TRUEUP_RESULTS_CLIENT'].format(base_url)
        url = url % (subclient_id, client_id)
        flag, response = self._cvpysdk_object.make_request(
            method='GET', url=url)
        if flag:
            last_sync_missing_items = extract_last_sync_missing_items(
                response.json())
            num_of_items = sum(last_sync_missing_items)
            return num_of_items
        else:
            raise (
                SDKException('Response', '101', f'Error occurred while reading TrueUp response \nError: {response.text}'))

    def backup_all_users_in_client(self):
        """ Run backup for all users present in OneDrive client

            Returns:
                object - instance of the Job class for this backup job
        """
        _subclient_object = self._get_subclient()
        return _subclient_object.backup(backup_level='INCREMENTAL')

    def in_place_restore(self, users, **kwargs):
        """ Run an inplace restore of specified users for OneDrive for business client

            Args:
                users (list) :  List of SMTP addresses of users
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        """
        _subclient_object = self._get_subclient()
        restore_job = _subclient_object.in_place_restore_onedrive_for_business_client(
            users, **kwargs)
        return restore_job

    def out_of_place_restore(self, users, destination_path, **kwargs):
        """ Run an out-of-place restore of specified users for OneDrive for business client

            Args:
                users (list) : list of SMTP addresses of users
                destination_path (str) : SMTP address of destination user
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        """
        _subclient_object = self._get_subclient()
        restore_job = _subclient_object.out_of_place_restore_onedrive_for_business_client(
            users, destination_path, **kwargs)
        return restore_job

    def disk_restore(self,
                     users,
                     destination_client,
                     destination_path,
                     skip_file_permissions=False):
        """ Runs disk restore of specified users for OneDrive for business client

               Args:
                users (list) : list of SMTP addresses of users
                destination_client (str) : client where the users need to be restored
                destination_path (str) : Destination folder location
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        """
        _subclient_object = self._get_subclient()
        restore_job = _subclient_object.disk_restore_onedrive_for_business_client(users,
                                                                                  destination_client,
                                                                                  destination_path,
                                                                                  skip_file_permissions=skip_file_permissions)
        return restore_job

    def modify_server_plan(self, old_plan, new_plan):
        """
           Method to Modify Server Plan Associated to Client

           Arguments:
                old_plan        (str)   --     existing server plan name
                new_plan        (str)   --     new server plan name
        """

        from ..plan import Plan
        entities = [{
            "clientName": self.client_name
        }]
        self.plan_obj = Plan(self._commcell_object, old_plan)
        self.plan_obj.edit_association(entities, new_plan)

    def modify_job_results_directory(self, modified_shared_jr_directory):
        """
        Method to modify job results directory

        modified_shared_jr_directory  (str)   --     new job results directory
        """
        jr_update_dict = {
            "client": {
                "jobResulsDir": {
                    "path": modified_shared_jr_directory
                }
            }
        }

        self.update_properties(properties_dict=jr_update_dict)
