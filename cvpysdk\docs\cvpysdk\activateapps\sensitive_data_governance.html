<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activateapps.sensitive_data_governance API documentation</title>
<meta name="description" content="Main file for performing operations on Sensitive data governance(SDG) app under Activate …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activateapps.sensitive_data_governance</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing operations on Sensitive data governance(SDG) app under Activate.</p>
<p>'Projects' &amp; 'Project' are 2 classes defined in this file</p>
<p>Projects:
Class to represent all SDG projects in the commcell</p>
<p>Project:
Class to represent single SDG project in the commcell</p>
<h2 id="projects">Projects</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the Projects class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_all_projects()
&ndash;
gets all the Projects from the SDG</p>
<p>refresh()
&ndash;
refresh the projects associated with the SDG</p>
<p>has_project()
&ndash;
checks whether given project name exists in SDG or not</p>
<p>get()
&ndash;
returns the Project class object for given project name</p>
<p>add()
&ndash;
adds project to the SDG</p>
<p>delete()
&ndash;
deletes project from the SDG</p>
<h2 id="project">Project</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the Project class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_project_details()
&ndash;
returns the project properties</p>
<p>_get_schedule_object()
&ndash;
returns the schedule object for associated project schedule</p>
<p>refresh()
&ndash;
refresh the project details</p>
<p>add_fs_data_source()
&ndash;
adds file system data source to project</p>
<p>add_o365_sdg_data_source()
&ndash;
Adds Office365 SDG data source to a project</p>
<p>delete_schedule()
&ndash;
deletes schedule for this project</p>
<p>add_schedule()
&ndash;
creates schedule for this project</p>
<p>share()
&ndash;
shares project with given user name or group name</p>
<p>search()
&ndash;
returns the search response containing document details from project</p>
<h2 id="project-attributes">Project Attributes</h2>
<pre><code>**project_id**              --  returns the id of the project

**project_details**         --  returns the project properties

**data_sources_name**       --  returns the list of data sources associated with this project

**data_sources**            --  returns the EdiscoveryDataSources object for this project

**total_data_sources**      --  returns total no of data sources associated with this project

**project_name**            --  returns the name of the project

**schedule**                --  returns the schedule object for associated project schedule

**sensitive_files_count**   --  returns the total sensitive files count
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L1-L649" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing operations on Sensitive data governance(SDG) app under Activate.

&#39;Projects&#39; &amp; &#39;Project&#39; are 2 classes defined in this file

Projects:   Class to represent all SDG projects in the commcell

Project:    Class to represent single SDG project in the commcell

Projects:

    __init__()                          --  initialise object of the Projects class

     _response_not_success()            --  parses through the exception response, and raises SDKException

     _get_all_projects()                --  gets all the Projects from the SDG

     refresh()                          --  refresh the projects associated with the SDG

     has_project()                      --  checks whether given project name exists in SDG or not

     get()                              --  returns the Project class object for given project name

     add()                              --  adds project to the SDG

     delete()                           --  deletes project from the SDG

Project:

     __init__()                         --  initialise object of the Project class

     _response_not_success()            --  parses through the exception response, and raises SDKException

     _get_project_details()             --  returns the project properties

     _get_schedule_object()             --  returns the schedule object for associated project schedule

     refresh()                          --  refresh the project details

     add_fs_data_source()               --  adds file system data source to project

     add_o365_sdg_data_source()         --  Adds Office365 SDG data source to a project

     delete_schedule()                  --  deletes schedule for this project

     add_schedule()                     --  creates schedule for this project

     share()                            --  shares project with given user name or group name

     search()                           --  returns the search response containing document details from project


Project Attributes
--------------------

    **project_id**              --  returns the id of the project

    **project_details**         --  returns the project properties

    **data_sources_name**       --  returns the list of data sources associated with this project

    **data_sources**            --  returns the EdiscoveryDataSources object for this project

    **total_data_sources**      --  returns total no of data sources associated with this project

    **project_name**            --  returns the name of the project

    **schedule**                --  returns the schedule object for associated project schedule

    **sensitive_files_count**   --  returns the total sensitive files count

&#34;&#34;&#34;

from ..schedules import Schedules

from ..activateapps.constants import EdiscoveryConstants

from ..activateapps.ediscovery_utils import EdiscoveryClients, EdiscoveryClientOperations, EdiscoveryDataSources

from ..exception import SDKException


class Projects():
    &#34;&#34;&#34;Class for representing all SDG Projects in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Projects class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Projects class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._sdg_projects = None
        self._ediscovery_clients_obj = EdiscoveryClients(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_all_sdg_projects(self):
        &#34;&#34;&#34;Returns all the SDG Projects found in the commcell

                Args:

                    None

                Returns:

                    dict        --  Containing SDG Project details

                Raises;

                    SDKException:

                            if failed to get SDG Project details

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        return self._ediscovery_clients_obj.get_ediscovery_projects()

    def refresh(self):
        &#34;&#34;&#34;Refresh the SDG Projects associated with the commcell.&#34;&#34;&#34;
        self._sdg_projects = self._get_all_sdg_projects()

    def delete(self, project_name):
        &#34;&#34;&#34;Deletes project from SDG

                Args:

                    project_name        (str)       --  Name of the project

                Returns:

                    None

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to delete project

                            if response is empty or not success
        &#34;&#34;&#34;
        if not self.has_project(project_name):
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;102&#39;, &#34;Project doesn&#39;t exists in SDG&#34;)
        project_id = self._sdg_projects[project_name.lower()][&#39;clientId&#39;]
        self._ediscovery_clients_obj.delete(client_id=project_id)
        self.refresh()

    def add(self, project_name, inventory_name, plan_name):
        &#34;&#34;&#34;Adds project to the SDG

                Args:

                    project_name        (str)       --  Name of the project

                    inventory_name      (str)       --  Name of inventory

                    plan_name           (str)       --  Plan name to associate with this project

                Returns:

                    obj --  Instance of Project class

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to create project

                            if response is empty or not success
        &#34;&#34;&#34;
        client_id = self._ediscovery_clients_obj.add(
            client_name=project_name,
            inventory_name=inventory_name,
            plan_name=plan_name)
        if client_id == 0:
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;102&#39;, &#39;Failed to add project to SDG&#39;)
        self.refresh()
        return Project(commcell_object=self._commcell_object, project_name=project_name, project_id=client_id)

    def has_project(self, project_name):
        &#34;&#34;&#34;Checks if a project exists in the commcell with the input name for SDG or not

            Args:
                project_name (str)  --  name of the project

            Returns:
                bool - boolean output whether the SDG Project exists in the commcell or not

            Raises:
                SDKException:
                    if type of the project name argument is not string

        &#34;&#34;&#34;
        if not isinstance(project_name, str):
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;101&#39;)
        return self._sdg_projects and project_name.lower() in self._sdg_projects

    def get(self, project_name):
        &#34;&#34;&#34;returns the Project object for given project name

                Args:

                    project_name         (str)       --  Name of the project

                Returns:

                    obj --  Instance of Project Class

                Raises:

                    SDKException:

                            if failed to find Project in SDG

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(project_name, str):
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;101&#39;)
        if not self.has_project(project_name):
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;103&#39;)
        project_id = self._sdg_projects[project_name.lower()][&#39;eDiscoveryClient&#39;][&#39;clientId&#39;]
        return Project(commcell_object=self._commcell_object, project_name=project_name, project_id=project_id)


class Project():
    &#34;&#34;&#34;Class to represent single SDG Project in the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, project_name, project_id=None):
        &#34;&#34;&#34;Initializes an instance of the Project class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                project_name        (str)       --  name of the project

                project_id          (int)       --  project&#39;s pseudoclient id

            Returns:
                object  -   instance of the Project class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._project_name = project_name
        self._project_id = None
        self._project_props = None
        self._schedule_obj = None
        if project_id:
            self._project_id = project_id
        else:
            self._project_id = self._commcell_object.activate.sensitive_data_governance().get(project_name).project_id
        self._ediscovery_data_srcs_obj = EdiscoveryDataSources(self._commcell_object, self)
        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_project_details(self):
        &#34;&#34;&#34;gets SDG Project details from the commcell

                Args:

                    None

                Returns:

                    dict    --  Containing project details

                Raises:

                     Raises;

                        SDKException:

                            if failed to get project details

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj.ediscovery_client_props

    def _get_schedule_object(self):
        &#34;&#34;&#34;returns the schedule object for associated project schedule

            Args:
                None

            Returns:

                obj --  Instance of Schedule class

                None -- if no schedule exists

            Raises:

                SDKException:

                        if failed to find schedule details associated with this project
        &#34;&#34;&#34;
        scd_obj = Schedules(self)
        if scd_obj.has_schedule():
            return scd_obj.get()
        return None

    def refresh(self):
        &#34;&#34;&#34;Refresh the SDG project details&#34;&#34;&#34;
        self._project_props = self._get_project_details()
        self._schedule_obj = self._get_schedule_object()

    def add_schedule(self, schedule_name, pattern_json):
        &#34;&#34;&#34;Creates the schedule and associate it with project

                        Args:

                            schedule_name       (str)       --  Schedule name

                            pattern_json        (dict)      --  Schedule pattern dict
                                                                    (Refer to Create_schedule_pattern in schedule.py)

                        Raises:

                              SDKException:

                                    if input is not valid

                                    if failed to create schedule

        &#34;&#34;&#34;
        self._ediscovery_client_ops.schedule(schedule_name=schedule_name, pattern_json=pattern_json)
        self.refresh()

    def delete_schedule(self):
        &#34;&#34;&#34;Deletes the schedule associated with project

                        Args:

                            None

                        Raises:

                              SDKException:

                                    if failed to Delete schedule

        &#34;&#34;&#34;
        if not self._schedule_obj:
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;102&#39;, &#34;No schedule is associated to this SDG Project&#34;)
        Schedules(self).delete()
        self.refresh()

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on entire project and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        Size:[10 TO 1024]
                                                        FileName:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                int,list(dict),dict    --  Containing document count, document details &amp; facet details(if any)

            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list, params=params)

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares project with given user or user group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        return self._ediscovery_client_ops.share(
            user_or_group_name=user_or_group_name,
            allow_edit_permission=allow_edit_permission,
            is_user=is_user,
            ops_type=ops_type)

    def add_fs_data_source(self, server_name, data_source_name,
                           source_type=EdiscoveryConstants.SourceType.BACKUP, **kwargs):
        &#34;&#34;&#34;Adds file system data source to project

                Args:

                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    source_type         (enum)      --  Source type for crawl (Live source or Backedup)
                                                                Refer EdiscoveryConstants.SourceType

                Kwargs Arguments:

                    crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

                    access_node         (str)       --  server name which needs to be used as access node in case
                                                                if server to be added is not a commvault client

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    user_name           (str)       --  User name who has access to UNC path

                    password            (str)       --  base64 encoded password to access unc path

                    enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this

                Returns:

                    obj     --  Instance of EdiscoveryDataSource class

                Raises:

                      SDKException:

                            if plan/inventory/index server doesn&#39;t exists

                            if failed to add FS data source
        &#34;&#34;&#34;
        inventory_name = self.project_details[&#39;inventoryDataSource&#39;][&#39;seaDataSourceName&#39;]
        plan_name = self.project_details[&#39;plan&#39;][&#39;planName&#39;]
        return self._ediscovery_data_srcs_obj.add_fs_data_source(
            server_name=server_name,
            data_source_name=data_source_name,
            inventory_name=inventory_name,
            plan_name=plan_name,
            source_type=source_type,
            **kwargs)

    def add_o365_sdg_data_source(self, server_name, data_source_name,
                                 datasource_type=EdiscoveryConstants.ClientType.ONEDRIVE, **kwargs):
        &#34;&#34;&#34;Adds Office365 SDG data source to a project

                Args:
                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    datasource_type     (str)       --  Type of O365 SDG datasource (Exchange/OneDrive)

                Kwargs Arguments:

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    users               (list)      --  List of users/mailboxes to be added. If empty, all users would be added

                Returns:

                    obj     --  Instance of EdiscoveryDataSource class

                Raises:

                      SDKException:

                            if plan doesn&#39;t exists
        &#34;&#34;&#34;
        plan_name = self.project_details[&#39;plan&#39;][&#39;planName&#39;]
        return self._ediscovery_data_srcs_obj.add_o365_sdg_data_source(
            server_name=server_name,
            data_source_name=data_source_name,
            plan_name=plan_name,
            datasource_type=datasource_type,
            **kwargs)

    @property
    def project_id(self):
        &#34;&#34;&#34;returns the project psuedoclient id

                Returns:

                    int --  Pseudoclient id associated with this project

        &#34;&#34;&#34;
        return self._project_id

    @property
    def project_name(self):
        &#34;&#34;&#34;returns the project name

                Returns:

                    str --  project name

        &#34;&#34;&#34;
        return self._project_name

    @property
    def project_details(self):
        &#34;&#34;&#34;returns the project properties

            Returns:

                dict    --  Containing project properties

        &#34;&#34;&#34;
        return self._project_props

    @property
    def data_sources_name(self):
        &#34;&#34;&#34;returns the associated data sources to this project

            Returns:

                list --  names of data sources

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj.data_sources

    @property
    def total_data_sources(self):
        &#34;&#34;&#34;returns the total number of data sources associated with this project

            Returns:

                int --  total number of data sources

        &#34;&#34;&#34;
        return len(self._ediscovery_data_srcs_obj.data_sources)

    @property
    def data_sources(self):
        &#34;&#34;&#34;returns the EdiscoveryDataSources object associated to this project

            Returns:

                obj --  Instance of EdiscoveryDataSources Object

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj

    @property
    def schedule(self):
        &#34;&#34;&#34;returns the schedule object for associated schedule

                Returns:

                    obj     --  Instance of Schedule Class if schedule exists

                    None    --  If no schedule exists

        &#34;&#34;&#34;
        return self._schedule_obj

    @property
    def sensitive_files_count(self):
        &#34;&#34;&#34;returns the total sensitive files count on this project

            Returns:

                int --  Sensitive files count

        &#34;&#34;&#34;
        count, _, _ = self.search(criteria=EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS,
                                  params={&#34;rows&#34;:&#34;0&#34;})
        return count</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project"><code class="flex name class">
<span>class <span class="ident">Project</span></span>
<span>(</span><span>commcell_object, project_name, project_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent single SDG Project in the commcell</p>
<p>Initializes an instance of the Project class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>project_name
(str)
&ndash;
name of the project</p>
<p>project_id
(int)
&ndash;
project's pseudoclient id</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Project class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L266-L649" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Project():
    &#34;&#34;&#34;Class to represent single SDG Project in the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, project_name, project_id=None):
        &#34;&#34;&#34;Initializes an instance of the Project class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                project_name        (str)       --  name of the project

                project_id          (int)       --  project&#39;s pseudoclient id

            Returns:
                object  -   instance of the Project class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._project_name = project_name
        self._project_id = None
        self._project_props = None
        self._schedule_obj = None
        if project_id:
            self._project_id = project_id
        else:
            self._project_id = self._commcell_object.activate.sensitive_data_governance().get(project_name).project_id
        self._ediscovery_data_srcs_obj = EdiscoveryDataSources(self._commcell_object, self)
        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_project_details(self):
        &#34;&#34;&#34;gets SDG Project details from the commcell

                Args:

                    None

                Returns:

                    dict    --  Containing project details

                Raises:

                     Raises;

                        SDKException:

                            if failed to get project details

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj.ediscovery_client_props

    def _get_schedule_object(self):
        &#34;&#34;&#34;returns the schedule object for associated project schedule

            Args:
                None

            Returns:

                obj --  Instance of Schedule class

                None -- if no schedule exists

            Raises:

                SDKException:

                        if failed to find schedule details associated with this project
        &#34;&#34;&#34;
        scd_obj = Schedules(self)
        if scd_obj.has_schedule():
            return scd_obj.get()
        return None

    def refresh(self):
        &#34;&#34;&#34;Refresh the SDG project details&#34;&#34;&#34;
        self._project_props = self._get_project_details()
        self._schedule_obj = self._get_schedule_object()

    def add_schedule(self, schedule_name, pattern_json):
        &#34;&#34;&#34;Creates the schedule and associate it with project

                        Args:

                            schedule_name       (str)       --  Schedule name

                            pattern_json        (dict)      --  Schedule pattern dict
                                                                    (Refer to Create_schedule_pattern in schedule.py)

                        Raises:

                              SDKException:

                                    if input is not valid

                                    if failed to create schedule

        &#34;&#34;&#34;
        self._ediscovery_client_ops.schedule(schedule_name=schedule_name, pattern_json=pattern_json)
        self.refresh()

    def delete_schedule(self):
        &#34;&#34;&#34;Deletes the schedule associated with project

                        Args:

                            None

                        Raises:

                              SDKException:

                                    if failed to Delete schedule

        &#34;&#34;&#34;
        if not self._schedule_obj:
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;102&#39;, &#34;No schedule is associated to this SDG Project&#34;)
        Schedules(self).delete()
        self.refresh()

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on entire project and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        Size:[10 TO 1024]
                                                        FileName:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                int,list(dict),dict    --  Containing document count, document details &amp; facet details(if any)

            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list, params=params)

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares project with given user or user group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        return self._ediscovery_client_ops.share(
            user_or_group_name=user_or_group_name,
            allow_edit_permission=allow_edit_permission,
            is_user=is_user,
            ops_type=ops_type)

    def add_fs_data_source(self, server_name, data_source_name,
                           source_type=EdiscoveryConstants.SourceType.BACKUP, **kwargs):
        &#34;&#34;&#34;Adds file system data source to project

                Args:

                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    source_type         (enum)      --  Source type for crawl (Live source or Backedup)
                                                                Refer EdiscoveryConstants.SourceType

                Kwargs Arguments:

                    crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

                    access_node         (str)       --  server name which needs to be used as access node in case
                                                                if server to be added is not a commvault client

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    user_name           (str)       --  User name who has access to UNC path

                    password            (str)       --  base64 encoded password to access unc path

                    enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this

                Returns:

                    obj     --  Instance of EdiscoveryDataSource class

                Raises:

                      SDKException:

                            if plan/inventory/index server doesn&#39;t exists

                            if failed to add FS data source
        &#34;&#34;&#34;
        inventory_name = self.project_details[&#39;inventoryDataSource&#39;][&#39;seaDataSourceName&#39;]
        plan_name = self.project_details[&#39;plan&#39;][&#39;planName&#39;]
        return self._ediscovery_data_srcs_obj.add_fs_data_source(
            server_name=server_name,
            data_source_name=data_source_name,
            inventory_name=inventory_name,
            plan_name=plan_name,
            source_type=source_type,
            **kwargs)

    def add_o365_sdg_data_source(self, server_name, data_source_name,
                                 datasource_type=EdiscoveryConstants.ClientType.ONEDRIVE, **kwargs):
        &#34;&#34;&#34;Adds Office365 SDG data source to a project

                Args:
                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    datasource_type     (str)       --  Type of O365 SDG datasource (Exchange/OneDrive)

                Kwargs Arguments:

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    users               (list)      --  List of users/mailboxes to be added. If empty, all users would be added

                Returns:

                    obj     --  Instance of EdiscoveryDataSource class

                Raises:

                      SDKException:

                            if plan doesn&#39;t exists
        &#34;&#34;&#34;
        plan_name = self.project_details[&#39;plan&#39;][&#39;planName&#39;]
        return self._ediscovery_data_srcs_obj.add_o365_sdg_data_source(
            server_name=server_name,
            data_source_name=data_source_name,
            plan_name=plan_name,
            datasource_type=datasource_type,
            **kwargs)

    @property
    def project_id(self):
        &#34;&#34;&#34;returns the project psuedoclient id

                Returns:

                    int --  Pseudoclient id associated with this project

        &#34;&#34;&#34;
        return self._project_id

    @property
    def project_name(self):
        &#34;&#34;&#34;returns the project name

                Returns:

                    str --  project name

        &#34;&#34;&#34;
        return self._project_name

    @property
    def project_details(self):
        &#34;&#34;&#34;returns the project properties

            Returns:

                dict    --  Containing project properties

        &#34;&#34;&#34;
        return self._project_props

    @property
    def data_sources_name(self):
        &#34;&#34;&#34;returns the associated data sources to this project

            Returns:

                list --  names of data sources

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj.data_sources

    @property
    def total_data_sources(self):
        &#34;&#34;&#34;returns the total number of data sources associated with this project

            Returns:

                int --  total number of data sources

        &#34;&#34;&#34;
        return len(self._ediscovery_data_srcs_obj.data_sources)

    @property
    def data_sources(self):
        &#34;&#34;&#34;returns the EdiscoveryDataSources object associated to this project

            Returns:

                obj --  Instance of EdiscoveryDataSources Object

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj

    @property
    def schedule(self):
        &#34;&#34;&#34;returns the schedule object for associated schedule

                Returns:

                    obj     --  Instance of Schedule Class if schedule exists

                    None    --  If no schedule exists

        &#34;&#34;&#34;
        return self._schedule_obj

    @property
    def sensitive_files_count(self):
        &#34;&#34;&#34;returns the total sensitive files count on this project

            Returns:

                int --  Sensitive files count

        &#34;&#34;&#34;
        count, _, _ = self.search(criteria=EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS,
                                  params={&#34;rows&#34;:&#34;0&#34;})
        return count</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.data_sources"><code class="name">var <span class="ident">data_sources</span></code></dt>
<dd>
<div class="desc"><p>returns the EdiscoveryDataSources object associated to this project</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of EdiscoveryDataSources Object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L614-L623" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_sources(self):
    &#34;&#34;&#34;returns the EdiscoveryDataSources object associated to this project

        Returns:

            obj --  Instance of EdiscoveryDataSources Object

    &#34;&#34;&#34;
    return self._ediscovery_data_srcs_obj</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.data_sources_name"><code class="name">var <span class="ident">data_sources_name</span></code></dt>
<dd>
<div class="desc"><p>returns the associated data sources to this project</p>
<h2 id="returns">Returns</h2>
<p>list &ndash;
names of data sources</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L592-L601" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_sources_name(self):
    &#34;&#34;&#34;returns the associated data sources to this project

        Returns:

            list --  names of data sources

    &#34;&#34;&#34;
    return self._ediscovery_data_srcs_obj.data_sources</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.project_details"><code class="name">var <span class="ident">project_details</span></code></dt>
<dd>
<div class="desc"><p>returns the project properties</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash;
Containing project properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L581-L590" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def project_details(self):
    &#34;&#34;&#34;returns the project properties

        Returns:

            dict    --  Containing project properties

    &#34;&#34;&#34;
    return self._project_props</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.project_id"><code class="name">var <span class="ident">project_id</span></code></dt>
<dd>
<div class="desc"><p>returns the project psuedoclient id</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Pseudoclient id associated with this project</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L559-L568" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def project_id(self):
    &#34;&#34;&#34;returns the project psuedoclient id

            Returns:

                int --  Pseudoclient id associated with this project

    &#34;&#34;&#34;
    return self._project_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.project_name"><code class="name">var <span class="ident">project_name</span></code></dt>
<dd>
<div class="desc"><p>returns the project name</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
project name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L570-L579" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def project_name(self):
    &#34;&#34;&#34;returns the project name

            Returns:

                str --  project name

    &#34;&#34;&#34;
    return self._project_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.schedule"><code class="name">var <span class="ident">schedule</span></code></dt>
<dd>
<div class="desc"><p>returns the schedule object for associated schedule</p>
<h2 id="returns">Returns</h2>
<p>obj
&ndash;
Instance of Schedule Class if schedule exists</p>
<p>None
&ndash;
If no schedule exists</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L625-L636" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def schedule(self):
    &#34;&#34;&#34;returns the schedule object for associated schedule

            Returns:

                obj     --  Instance of Schedule Class if schedule exists

                None    --  If no schedule exists

    &#34;&#34;&#34;
    return self._schedule_obj</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.sensitive_files_count"><code class="name">var <span class="ident">sensitive_files_count</span></code></dt>
<dd>
<div class="desc"><p>returns the total sensitive files count on this project</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Sensitive files count</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L638-L649" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sensitive_files_count(self):
    &#34;&#34;&#34;returns the total sensitive files count on this project

        Returns:

            int --  Sensitive files count

    &#34;&#34;&#34;
    count, _, _ = self.search(criteria=EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS,
                              params={&#34;rows&#34;:&#34;0&#34;})
    return count</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.total_data_sources"><code class="name">var <span class="ident">total_data_sources</span></code></dt>
<dd>
<div class="desc"><p>returns the total number of data sources associated with this project</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
total number of data sources</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L603-L612" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def total_data_sources(self):
    &#34;&#34;&#34;returns the total number of data sources associated with this project

        Returns:

            int --  total number of data sources

    &#34;&#34;&#34;
    return len(self._ediscovery_data_srcs_obj.data_sources)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.add_fs_data_source"><code class="name flex">
<span>def <span class="ident">add_fs_data_source</span></span>(<span>self, server_name, data_source_name, source_type=SourceType.BACKUP, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds file system data source to project</p>
<h2 id="args">Args</h2>
<p>server_name
(str)
&ndash;
Server name which needs to be added</p>
<p>data_source_name
(str)
&ndash;
Name for data source</p>
<p>source_type
(enum)
&ndash;
Source type for crawl (Live source or Backedup)
Refer EdiscoveryConstants.SourceType
Kwargs Arguments:</p>
<pre><code>crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

access_node         (str)       --  server name which needs to be used as access node in case
                                            if server to be added is not a commvault client

country_name        (str)       --  country name where server is located (default: USA)

country_code        (str)       --  Country code (ISO 3166 2-letter code)

user_name           (str)       --  User name who has access to UNC path

password            (str)       --  base64 encoded password to access unc path

enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this
</code></pre>
<h2 id="returns">Returns</h2>
<p>obj
&ndash;
Instance of EdiscoveryDataSource class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if plan/inventory/index server doesn't exists

  if failed to add FS data source
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L470-L520" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_fs_data_source(self, server_name, data_source_name,
                       source_type=EdiscoveryConstants.SourceType.BACKUP, **kwargs):
    &#34;&#34;&#34;Adds file system data source to project

            Args:

                server_name         (str)       --  Server name which needs to be added

                data_source_name    (str)       --  Name for data source

                source_type         (enum)      --  Source type for crawl (Live source or Backedup)
                                                            Refer EdiscoveryConstants.SourceType

            Kwargs Arguments:

                crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

                access_node         (str)       --  server name which needs to be used as access node in case
                                                            if server to be added is not a commvault client

                country_name        (str)       --  country name where server is located (default: USA)

                country_code        (str)       --  Country code (ISO 3166 2-letter code)

                user_name           (str)       --  User name who has access to UNC path

                password            (str)       --  base64 encoded password to access unc path

                enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this

            Returns:

                obj     --  Instance of EdiscoveryDataSource class

            Raises:

                  SDKException:

                        if plan/inventory/index server doesn&#39;t exists

                        if failed to add FS data source
    &#34;&#34;&#34;
    inventory_name = self.project_details[&#39;inventoryDataSource&#39;][&#39;seaDataSourceName&#39;]
    plan_name = self.project_details[&#39;plan&#39;][&#39;planName&#39;]
    return self._ediscovery_data_srcs_obj.add_fs_data_source(
        server_name=server_name,
        data_source_name=data_source_name,
        inventory_name=inventory_name,
        plan_name=plan_name,
        source_type=source_type,
        **kwargs)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.add_o365_sdg_data_source"><code class="name flex">
<span>def <span class="ident">add_o365_sdg_data_source</span></span>(<span>self, server_name, data_source_name, datasource_type=ClientType.ONEDRIVE, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds Office365 SDG data source to a project</p>
<h2 id="args">Args</h2>
<p>server_name
(str)
&ndash;
Server name which needs to be added</p>
<p>data_source_name
(str)
&ndash;
Name for data source</p>
<p>datasource_type
(str)
&ndash;
Type of O365 SDG datasource (Exchange/OneDrive)
Kwargs Arguments:</p>
<pre><code>country_name        (str)       --  country name where server is located (default: USA)

country_code        (str)       --  Country code (ISO 3166 2-letter code)

users               (list)      --  List of users/mailboxes to be added. If empty, all users would be added
</code></pre>
<h2 id="returns">Returns</h2>
<p>obj
&ndash;
Instance of EdiscoveryDataSource class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if plan doesn't exists
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L522-L557" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_o365_sdg_data_source(self, server_name, data_source_name,
                             datasource_type=EdiscoveryConstants.ClientType.ONEDRIVE, **kwargs):
    &#34;&#34;&#34;Adds Office365 SDG data source to a project

            Args:
                server_name         (str)       --  Server name which needs to be added

                data_source_name    (str)       --  Name for data source

                datasource_type     (str)       --  Type of O365 SDG datasource (Exchange/OneDrive)

            Kwargs Arguments:

                country_name        (str)       --  country name where server is located (default: USA)

                country_code        (str)       --  Country code (ISO 3166 2-letter code)

                users               (list)      --  List of users/mailboxes to be added. If empty, all users would be added

            Returns:

                obj     --  Instance of EdiscoveryDataSource class

            Raises:

                  SDKException:

                        if plan doesn&#39;t exists
    &#34;&#34;&#34;
    plan_name = self.project_details[&#39;plan&#39;][&#39;planName&#39;]
    return self._ediscovery_data_srcs_obj.add_o365_sdg_data_source(
        server_name=server_name,
        data_source_name=data_source_name,
        plan_name=plan_name,
        datasource_type=datasource_type,
        **kwargs)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.add_schedule"><code class="name flex">
<span>def <span class="ident">add_schedule</span></span>(<span>self, schedule_name, pattern_json)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates the schedule and associate it with project</p>
<h2 id="args">Args</h2>
<p>schedule_name
(str)
&ndash;
Schedule name</p>
<p>pattern_json
(dict)
&ndash;
Schedule pattern dict
(Refer to Create_schedule_pattern in schedule.py)</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if input is not valid

  if failed to create schedule
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L360-L380" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_schedule(self, schedule_name, pattern_json):
    &#34;&#34;&#34;Creates the schedule and associate it with project

                    Args:

                        schedule_name       (str)       --  Schedule name

                        pattern_json        (dict)      --  Schedule pattern dict
                                                                (Refer to Create_schedule_pattern in schedule.py)

                    Raises:

                          SDKException:

                                if input is not valid

                                if failed to create schedule

    &#34;&#34;&#34;
    self._ediscovery_client_ops.schedule(schedule_name=schedule_name, pattern_json=pattern_json)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.delete_schedule"><code class="name flex">
<span>def <span class="ident">delete_schedule</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the schedule associated with project</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if failed to Delete schedule
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L382-L399" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_schedule(self):
    &#34;&#34;&#34;Deletes the schedule associated with project

                    Args:

                        None

                    Raises:

                          SDKException:

                                if failed to Delete schedule

    &#34;&#34;&#34;
    if not self._schedule_obj:
        raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;102&#39;, &#34;No schedule is associated to this SDG Project&#34;)
    Schedules(self).delete()
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the SDG project details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L355-L358" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the SDG project details&#34;&#34;&#34;
    self._project_props = self._get_project_details()
    self._schedule_obj = self._get_schedule_object()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.search"><code class="name flex">
<span>def <span class="ident">search</span></span>(<span>self, criteria=None, attr_list=None, params=None)</span>
</code></dt>
<dd>
<div class="desc"><p>do searches on entire project and returns document details</p>
<h2 id="args">Args</h2>
<p>criteria
(str)
&ndash;
containing criteria for query
(Default : None - returns all docs)</p>
<pre><code>                                Example :

                                    Size:[10 TO 1024]
                                    FileName:09_23*
</code></pre>
<p>attr_list
(set)
&ndash;
Column names to be returned in results.
Acts as 'fl' in query</p>
<p>params
(dict)
&ndash;
Any other params which needs to be passed
Example : { "start" : "0" }</p>
<h2 id="returns">Returns</h2>
<p>int,list(dict),dict
&ndash;
Containing document count, document details &amp; facet details(if any)</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to perform search
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L401-L431" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def search(self, criteria=None, attr_list=None, params=None):
    &#34;&#34;&#34;do searches on entire project and returns document details

        Args:

            criteria        (str)      --  containing criteria for query
                                                (Default : None - returns all docs)

                                                Example :

                                                    Size:[10 TO 1024]
                                                    FileName:09_23*

            attr_list       (set)      --  Column names to be returned in results.
                                                 Acts as &#39;fl&#39; in query

            params          (dict)     --  Any other params which needs to be passed
                                               Example : { &#34;start&#34; : &#34;0&#34; }

        Returns:

            int,list(dict),dict    --  Containing document count, document details &amp; facet details(if any)

        Raises:

            SDKException:

                    if failed to perform search

    &#34;&#34;&#34;
    return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list, params=params)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Project.share"><code class="name flex">
<span>def <span class="ident">share</span></span>(<span>self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Shares project with given user or user group in commcell</p>
<h2 id="args">Args</h2>
<p>user_or_group_name
(str)
&ndash;
Name of user or group</p>
<p>is_user
(bool)
&ndash;
Denotes whether this is user or group name
default : True(User)</p>
<p>allow_edit_permission
(bool)
&ndash;
whether to give edit permission or not to user or group</p>
<p>ops_type
(int)
&ndash;
Operation type</p>
<pre><code>                                    Default : 1 (Add)

                                    Supported : 1 (Add)
                                                3 (Delete)
</code></pre>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if unable to update security associations

    if response is empty or not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L433-L468" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
    &#34;&#34;&#34;Shares project with given user or user group in commcell

            Args:

                user_or_group_name      (str)       --  Name of user or group

                is_user                 (bool)      --  Denotes whether this is user or group name
                                                            default : True(User)

                allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                ops_type                (int)       --  Operation type

                                                        Default : 1 (Add)

                                                        Supported : 1 (Add)
                                                                    3 (Delete)

            Returns:

                None

            Raises:

                SDKException:

                        if unable to update security associations

                        if response is empty or not success
    &#34;&#34;&#34;
    return self._ediscovery_client_ops.share(
        user_or_group_name=user_or_group_name,
        allow_edit_permission=allow_edit_permission,
        is_user=is_user,
        ops_type=ops_type)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Projects"><code class="flex name class">
<span>class <span class="ident">Projects</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all SDG Projects in the commcell.</p>
<p>Initializes an instance of the Projects class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Projects class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L100-L263" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Projects():
    &#34;&#34;&#34;Class for representing all SDG Projects in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Projects class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Projects class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._sdg_projects = None
        self._ediscovery_clients_obj = EdiscoveryClients(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_all_sdg_projects(self):
        &#34;&#34;&#34;Returns all the SDG Projects found in the commcell

                Args:

                    None

                Returns:

                    dict        --  Containing SDG Project details

                Raises;

                    SDKException:

                            if failed to get SDG Project details

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        return self._ediscovery_clients_obj.get_ediscovery_projects()

    def refresh(self):
        &#34;&#34;&#34;Refresh the SDG Projects associated with the commcell.&#34;&#34;&#34;
        self._sdg_projects = self._get_all_sdg_projects()

    def delete(self, project_name):
        &#34;&#34;&#34;Deletes project from SDG

                Args:

                    project_name        (str)       --  Name of the project

                Returns:

                    None

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to delete project

                            if response is empty or not success
        &#34;&#34;&#34;
        if not self.has_project(project_name):
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;102&#39;, &#34;Project doesn&#39;t exists in SDG&#34;)
        project_id = self._sdg_projects[project_name.lower()][&#39;clientId&#39;]
        self._ediscovery_clients_obj.delete(client_id=project_id)
        self.refresh()

    def add(self, project_name, inventory_name, plan_name):
        &#34;&#34;&#34;Adds project to the SDG

                Args:

                    project_name        (str)       --  Name of the project

                    inventory_name      (str)       --  Name of inventory

                    plan_name           (str)       --  Plan name to associate with this project

                Returns:

                    obj --  Instance of Project class

                Raises:

                    SDKException:

                            if input is not valid

                            if failed to create project

                            if response is empty or not success
        &#34;&#34;&#34;
        client_id = self._ediscovery_clients_obj.add(
            client_name=project_name,
            inventory_name=inventory_name,
            plan_name=plan_name)
        if client_id == 0:
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;102&#39;, &#39;Failed to add project to SDG&#39;)
        self.refresh()
        return Project(commcell_object=self._commcell_object, project_name=project_name, project_id=client_id)

    def has_project(self, project_name):
        &#34;&#34;&#34;Checks if a project exists in the commcell with the input name for SDG or not

            Args:
                project_name (str)  --  name of the project

            Returns:
                bool - boolean output whether the SDG Project exists in the commcell or not

            Raises:
                SDKException:
                    if type of the project name argument is not string

        &#34;&#34;&#34;
        if not isinstance(project_name, str):
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;101&#39;)
        return self._sdg_projects and project_name.lower() in self._sdg_projects

    def get(self, project_name):
        &#34;&#34;&#34;returns the Project object for given project name

                Args:

                    project_name         (str)       --  Name of the project

                Returns:

                    obj --  Instance of Project Class

                Raises:

                    SDKException:

                            if failed to find Project in SDG

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(project_name, str):
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;101&#39;)
        if not self.has_project(project_name):
            raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;103&#39;)
        project_id = self._sdg_projects[project_name.lower()][&#39;eDiscoveryClient&#39;][&#39;clientId&#39;]
        return Project(commcell_object=self._commcell_object, project_name=project_name, project_id=project_id)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Projects.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, project_name, inventory_name, plan_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds project to the SDG</p>
<h2 id="args">Args</h2>
<p>project_name
(str)
&ndash;
Name of the project</p>
<p>inventory_name
(str)
&ndash;
Name of inventory</p>
<p>plan_name
(str)
&ndash;
Plan name to associate with this project</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of Project class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if input is not valid

    if failed to create project

    if response is empty or not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L186-L218" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, project_name, inventory_name, plan_name):
    &#34;&#34;&#34;Adds project to the SDG

            Args:

                project_name        (str)       --  Name of the project

                inventory_name      (str)       --  Name of inventory

                plan_name           (str)       --  Plan name to associate with this project

            Returns:

                obj --  Instance of Project class

            Raises:

                SDKException:

                        if input is not valid

                        if failed to create project

                        if response is empty or not success
    &#34;&#34;&#34;
    client_id = self._ediscovery_clients_obj.add(
        client_name=project_name,
        inventory_name=inventory_name,
        plan_name=plan_name)
    if client_id == 0:
        raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;102&#39;, &#39;Failed to add project to SDG&#39;)
    self.refresh()
    return Project(commcell_object=self._commcell_object, project_name=project_name, project_id=client_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Projects.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, project_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes project from SDG</p>
<h2 id="args">Args</h2>
<p>project_name
(str)
&ndash;
Name of the project</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if input is not valid

    if failed to delete project

    if response is empty or not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L159-L184" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, project_name):
    &#34;&#34;&#34;Deletes project from SDG

            Args:

                project_name        (str)       --  Name of the project

            Returns:

                None

            Raises:

                SDKException:

                        if input is not valid

                        if failed to delete project

                        if response is empty or not success
    &#34;&#34;&#34;
    if not self.has_project(project_name):
        raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;102&#39;, &#34;Project doesn&#39;t exists in SDG&#34;)
    project_id = self._sdg_projects[project_name.lower()][&#39;clientId&#39;]
    self._ediscovery_clients_obj.delete(client_id=project_id)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Projects.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, project_name)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the Project object for given project name</p>
<h2 id="args">Args</h2>
<p>project_name
(str)
&ndash;
Name of the project</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of Project Class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to find Project in SDG

    if input is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L238-L263" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, project_name):
    &#34;&#34;&#34;returns the Project object for given project name

            Args:

                project_name         (str)       --  Name of the project

            Returns:

                obj --  Instance of Project Class

            Raises:

                SDKException:

                        if failed to find Project in SDG

                        if input is not valid

    &#34;&#34;&#34;
    if not isinstance(project_name, str):
        raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;101&#39;)
    if not self.has_project(project_name):
        raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;103&#39;)
    project_id = self._sdg_projects[project_name.lower()][&#39;eDiscoveryClient&#39;][&#39;clientId&#39;]
    return Project(commcell_object=self._commcell_object, project_name=project_name, project_id=project_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Projects.has_project"><code class="name flex">
<span>def <span class="ident">has_project</span></span>(<span>self, project_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a project exists in the commcell with the input name for SDG or not</p>
<h2 id="args">Args</h2>
<p>project_name (str)
&ndash;
name of the project</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the SDG Project exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the project name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L220-L236" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_project(self, project_name):
    &#34;&#34;&#34;Checks if a project exists in the commcell with the input name for SDG or not

        Args:
            project_name (str)  --  name of the project

        Returns:
            bool - boolean output whether the SDG Project exists in the commcell or not

        Raises:
            SDKException:
                if type of the project name argument is not string

    &#34;&#34;&#34;
    if not isinstance(project_name, str):
        raise SDKException(&#39;SensitiveDataGovernance&#39;, &#39;101&#39;)
    return self._sdg_projects and project_name.lower() in self._sdg_projects</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.sensitive_data_governance.Projects.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the SDG Projects associated with the commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/sensitive_data_governance.py#L155-L157" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the SDG Projects associated with the commcell.&#34;&#34;&#34;
    self._sdg_projects = self._get_all_sdg_projects()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#project-attributes">Project Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.activateapps" href="index.html">cvpysdk.activateapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project" href="#cvpysdk.activateapps.sensitive_data_governance.Project">Project</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.add_fs_data_source" href="#cvpysdk.activateapps.sensitive_data_governance.Project.add_fs_data_source">add_fs_data_source</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.add_o365_sdg_data_source" href="#cvpysdk.activateapps.sensitive_data_governance.Project.add_o365_sdg_data_source">add_o365_sdg_data_source</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.add_schedule" href="#cvpysdk.activateapps.sensitive_data_governance.Project.add_schedule">add_schedule</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.data_sources" href="#cvpysdk.activateapps.sensitive_data_governance.Project.data_sources">data_sources</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.data_sources_name" href="#cvpysdk.activateapps.sensitive_data_governance.Project.data_sources_name">data_sources_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.delete_schedule" href="#cvpysdk.activateapps.sensitive_data_governance.Project.delete_schedule">delete_schedule</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.project_details" href="#cvpysdk.activateapps.sensitive_data_governance.Project.project_details">project_details</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.project_id" href="#cvpysdk.activateapps.sensitive_data_governance.Project.project_id">project_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.project_name" href="#cvpysdk.activateapps.sensitive_data_governance.Project.project_name">project_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.refresh" href="#cvpysdk.activateapps.sensitive_data_governance.Project.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.schedule" href="#cvpysdk.activateapps.sensitive_data_governance.Project.schedule">schedule</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.search" href="#cvpysdk.activateapps.sensitive_data_governance.Project.search">search</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.sensitive_files_count" href="#cvpysdk.activateapps.sensitive_data_governance.Project.sensitive_files_count">sensitive_files_count</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.share" href="#cvpysdk.activateapps.sensitive_data_governance.Project.share">share</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Project.total_data_sources" href="#cvpysdk.activateapps.sensitive_data_governance.Project.total_data_sources">total_data_sources</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.sensitive_data_governance.Projects" href="#cvpysdk.activateapps.sensitive_data_governance.Projects">Projects</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Projects.add" href="#cvpysdk.activateapps.sensitive_data_governance.Projects.add">add</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Projects.delete" href="#cvpysdk.activateapps.sensitive_data_governance.Projects.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Projects.get" href="#cvpysdk.activateapps.sensitive_data_governance.Projects.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Projects.has_project" href="#cvpysdk.activateapps.sensitive_data_governance.Projects.has_project">has_project</a></code></li>
<li><code><a title="cvpysdk.activateapps.sensitive_data_governance.Projects.refresh" href="#cvpysdk.activateapps.sensitive_data_governance.Projects.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>