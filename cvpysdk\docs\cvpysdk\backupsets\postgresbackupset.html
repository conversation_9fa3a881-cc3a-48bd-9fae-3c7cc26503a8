<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.backupsets.postgresbackupset API documentation</title>
<meta name="description" content="File for operating on a Postgres Server Backupset …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.backupsets.postgresbackupset</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Postgres Server Backupset</p>
<p>PostgresBackupset is the only class defined in this file.</p>
<p>PostgresBackupset: Derived class from Backupset Base class, representing a Postgres
server backupset, and to perform operations on that backupset</p>
<h1 id="postgresbackupset">PostgresBackupset:</h1>
<pre><code>run_live_sync()                      --  runs live sync replication operation

configure_live_sync()                --  runs the Task API with the request JSON provided
to create live sync configuration, and returns the contents after parsing the response

restore_postgres_server()            --  method to restore the Postgres server
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/postgresbackupset.py#L1-L306" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Postgres Server Backupset

PostgresBackupset is the only class defined in this file.

PostgresBackupset: Derived class from Backupset Base class, representing a Postgres
server backupset, and to perform operations on that backupset

PostgresBackupset:
==================

    run_live_sync()                      --  runs live sync replication operation

    configure_live_sync()                --  runs the Task API with the request JSON provided
    to create live sync configuration, and returns the contents after parsing the response

    restore_postgres_server()            --  method to restore the Postgres server

&#34;&#34;&#34;
from __future__ import unicode_literals
from ..backupset import Backupset
from ..exception import SDKException
from ..schedules import Schedule, Schedules


class PostgresBackupset(Backupset):
    &#34;&#34;&#34;Derived class from Backupset Base class, representing a postgres backupset,
        and to perform operations on that backupset.&#34;&#34;&#34;

    def __init__(self, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            instance_object   (obj)     -- instance object

            backupset_name    (str)     -- name of the backupset

            backupset_id      (str)     -- id of the backupset

        &#34;&#34;&#34;
        super(PostgresBackupset, self).__init__(
            instance_object, backupset_name, backupset_id)
        self._LIVE_SYNC = self._commcell_object._services[&#39;LIVE_SYNC&#39;]

    def configure_live_sync(self, request_json):
        &#34;&#34;&#34;Runs the Task API with the request JSON provided to create live sync configuration,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                object - instance of the Schedule class

            Raises:
                SDKException:
                    if live sync configuration fails

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._LIVE_SYNC, request_json)

        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    error_message = &#39;Live Sync configuration failed\nError: &#34;{0}&#34;&#39;.format(
                        error_message)
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Failed to create schedule&#39;)
            else:
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;, self._update_response_(response.text))

    def run_live_sync(
            self,
            dest_client_name,
            dest_instance_name,
            baseline_job):
        &#34;&#34;&#34;runs live sync replication operation

            Args:

                dest_client_name        (str)   --  destination client name where files are to be
                restored

                dest_instance_name      (str)   --  destination postgres instance name of
                destination client

                baseline_job            (obj)   --  baseline backup job object

            Returns:
                object - instance of the Schedule class

        &#34;&#34;&#34;
        instance_object = self._instance_object
        instance_object._restore_association = self._properties[&#34;backupSetEntity&#34;]
        request_json = instance_object._restore_json(
            paths=[&#34;/data&#34;],
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            backupset_name=&#34;fsbasedbackupset&#34;,
            backupset_flag=True,
            no_image=True,
            overwrite=True,
            baseline_jobid=int(baseline_job.job_id),
            baseline_ref_time=int(baseline_job.summary[&#39;jobStartTime&#39;]))

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;] = {
            &#39;backupLevel&#39;: 2,
            &#39;vsaBackupOptions&#39;: {}
            }
        request_json[&#39;taskInfo&#39;][&#39;task&#39;][&#39;taskType&#39;] = 2
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;operationType&#39;] = 1007
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;subTaskName&#39;] = &#34;automation&#34;
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;] = {
            &#34;freq_type&#34;: 4096,
            &#34;timeZone&#34;: {
                &#34;TimeZoneName&#34;: &#34;(UTC) Coordinated Universal Time&#34;
                }
            }

        return self.configure_live_sync(request_json)

    def restore_postgres_server(
            self,
            database_list=None,
            dest_client_name=None,
            dest_instance_name=None,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            clone_env=False,
            clone_options=None,
            media_agent=None,
            table_level_restore=False,
            staging_path=None,
            no_of_streams=None,
            volume_level_restore=False,
            redirect_enabled=False,
            redirect_path=None,
            restore_to_disk=False,
            restore_to_disk_job=None,
            destination_path=None,
            revert=False):
        &#34;&#34;&#34;
        Method to restore the Postgres server

            Args:

                database_list               (List) -- List of databases

                dest_client_name            (str)  -- Destination Client name

                dest_instance_name          (str)  -- Destination Instance name

                copy_precedence             (int)  -- Copy precedence associted with storage

                from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                clone_env                   (bool)  --  boolean to specify whether the database
                should be cloned or not

                    default: False

                clone_options               (dict)  --  clone restore options passed in a dict

                    default: None

                    Accepted format: {
                                        &#34;stagingLocaion&#34;: &#34;/gk_snap&#34;,
                                        &#34;forceCleanup&#34;: True,
                                        &#34;port&#34;: &#34;5595&#34;,
                                        &#34;libDirectory&#34;: &#34;/opt/PostgreSQL/9.6/lib&#34;,
                                        &#34;isInstanceSelected&#34;: True,
                                        &#34;reservationPeriodS&#34;: 3600,
                                        &#34;user&#34;: &#34;postgres&#34;,
                                        &#34;binaryDirectory&#34;: &#34;/opt/PostgreSQL/9.6/bin&#34;

                                     }

                media_agent             (str)   --  media agent name

                    default: None

                table_level_restore     (bool)  --  boolean to specify if the restore operation
                is table level

                    default: False

                staging_path            (str)   --  staging path location for table level restore

                    default: None

                no_of_streams           (int)   --  number of streams to be used by
                volume level restore

                    default: None

                volume_level_restore    (bool)  --  volume level restore flag

                    default: False

                redirect_enabled         (bool)  --  boolean to specify if redirect restore is
                enabled

                    default: False

                redirect_path           (str)   --  Path specified in advanced restore options
                in order to perform redirect restore

                    default: None

                restore_to_disk         (bool)  --  restore to disk flag

                    default: False

                restore_to_disk_job     (list)   --  list of backup job ids to restore to disk

                    default: None

                destination_path        (str)   --  destination path for restore

                    default: None

                revert                 (bool)  --  boolean to specify whether to do a
                                                   hardware revert in restore
                    default: False

            Returns:
                object -- Job containing restore details

        &#34;&#34;&#34;
        instance_object = self._instance_object
        if dest_client_name is None:
            dest_client_name = instance_object._agent_object._client_object.client_name

        if dest_instance_name is None:
            dest_instance_name = instance_object.instance_name

        backupset_name = self.backupset_name

        if backupset_name.lower() == &#34;fsbasedbackupset&#34;:
            backupset_flag = True
            if database_list is None:
                database_list = [&#34;/data&#34;]
        else:
            backupset_flag = False

        instance_object._restore_association = self._properties[&#39;backupSetEntity&#39;]
        return instance_object.restore_in_place(
            database_list,
            dest_client_name,
            dest_instance_name,
            backupset_name,
            backupset_flag,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            clone_env=clone_env,
            clone_options=clone_options,
            media_agent=media_agent,
            table_level_restore=table_level_restore,
            staging_path=staging_path,
            no_of_streams=no_of_streams,
            volume_level_restore=volume_level_restore,
            redirect_enabled=redirect_enabled,
            redirect_path=redirect_path,
            restore_to_disk=restore_to_disk,
            restore_to_disk_job=restore_to_disk_job,
            destination_path=destination_path,
            revert=revert)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.backupsets.postgresbackupset.PostgresBackupset"><code class="flex name class">
<span>class <span class="ident">PostgresBackupset</span></span>
<span>(</span><span>instance_object, backupset_name, backupset_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Backupset Base class, representing a postgres backupset,
and to perform operations on that backupset.</p>
<p>Constructor for the class</p>
<h2 id="args">Args</h2>
<p>instance_object
(obj)
&ndash; instance object</p>
<p>backupset_name
(str)
&ndash; name of the backupset</p>
<p>backupset_id
(str)
&ndash; id of the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/postgresbackupset.py#L43-L306" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class PostgresBackupset(Backupset):
    &#34;&#34;&#34;Derived class from Backupset Base class, representing a postgres backupset,
        and to perform operations on that backupset.&#34;&#34;&#34;

    def __init__(self, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            instance_object   (obj)     -- instance object

            backupset_name    (str)     -- name of the backupset

            backupset_id      (str)     -- id of the backupset

        &#34;&#34;&#34;
        super(PostgresBackupset, self).__init__(
            instance_object, backupset_name, backupset_id)
        self._LIVE_SYNC = self._commcell_object._services[&#39;LIVE_SYNC&#39;]

    def configure_live_sync(self, request_json):
        &#34;&#34;&#34;Runs the Task API with the request JSON provided to create live sync configuration,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                object - instance of the Schedule class

            Raises:
                SDKException:
                    if live sync configuration fails

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._LIVE_SYNC, request_json)

        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    error_message = &#39;Live Sync configuration failed\nError: &#34;{0}&#34;&#39;.format(
                        error_message)
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Failed to create schedule&#39;)
            else:
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;, self._update_response_(response.text))

    def run_live_sync(
            self,
            dest_client_name,
            dest_instance_name,
            baseline_job):
        &#34;&#34;&#34;runs live sync replication operation

            Args:

                dest_client_name        (str)   --  destination client name where files are to be
                restored

                dest_instance_name      (str)   --  destination postgres instance name of
                destination client

                baseline_job            (obj)   --  baseline backup job object

            Returns:
                object - instance of the Schedule class

        &#34;&#34;&#34;
        instance_object = self._instance_object
        instance_object._restore_association = self._properties[&#34;backupSetEntity&#34;]
        request_json = instance_object._restore_json(
            paths=[&#34;/data&#34;],
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            backupset_name=&#34;fsbasedbackupset&#34;,
            backupset_flag=True,
            no_image=True,
            overwrite=True,
            baseline_jobid=int(baseline_job.job_id),
            baseline_ref_time=int(baseline_job.summary[&#39;jobStartTime&#39;]))

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;] = {
            &#39;backupLevel&#39;: 2,
            &#39;vsaBackupOptions&#39;: {}
            }
        request_json[&#39;taskInfo&#39;][&#39;task&#39;][&#39;taskType&#39;] = 2
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;operationType&#39;] = 1007
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;subTaskName&#39;] = &#34;automation&#34;
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;] = {
            &#34;freq_type&#34;: 4096,
            &#34;timeZone&#34;: {
                &#34;TimeZoneName&#34;: &#34;(UTC) Coordinated Universal Time&#34;
                }
            }

        return self.configure_live_sync(request_json)

    def restore_postgres_server(
            self,
            database_list=None,
            dest_client_name=None,
            dest_instance_name=None,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            clone_env=False,
            clone_options=None,
            media_agent=None,
            table_level_restore=False,
            staging_path=None,
            no_of_streams=None,
            volume_level_restore=False,
            redirect_enabled=False,
            redirect_path=None,
            restore_to_disk=False,
            restore_to_disk_job=None,
            destination_path=None,
            revert=False):
        &#34;&#34;&#34;
        Method to restore the Postgres server

            Args:

                database_list               (List) -- List of databases

                dest_client_name            (str)  -- Destination Client name

                dest_instance_name          (str)  -- Destination Instance name

                copy_precedence             (int)  -- Copy precedence associted with storage

                from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                clone_env                   (bool)  --  boolean to specify whether the database
                should be cloned or not

                    default: False

                clone_options               (dict)  --  clone restore options passed in a dict

                    default: None

                    Accepted format: {
                                        &#34;stagingLocaion&#34;: &#34;/gk_snap&#34;,
                                        &#34;forceCleanup&#34;: True,
                                        &#34;port&#34;: &#34;5595&#34;,
                                        &#34;libDirectory&#34;: &#34;/opt/PostgreSQL/9.6/lib&#34;,
                                        &#34;isInstanceSelected&#34;: True,
                                        &#34;reservationPeriodS&#34;: 3600,
                                        &#34;user&#34;: &#34;postgres&#34;,
                                        &#34;binaryDirectory&#34;: &#34;/opt/PostgreSQL/9.6/bin&#34;

                                     }

                media_agent             (str)   --  media agent name

                    default: None

                table_level_restore     (bool)  --  boolean to specify if the restore operation
                is table level

                    default: False

                staging_path            (str)   --  staging path location for table level restore

                    default: None

                no_of_streams           (int)   --  number of streams to be used by
                volume level restore

                    default: None

                volume_level_restore    (bool)  --  volume level restore flag

                    default: False

                redirect_enabled         (bool)  --  boolean to specify if redirect restore is
                enabled

                    default: False

                redirect_path           (str)   --  Path specified in advanced restore options
                in order to perform redirect restore

                    default: None

                restore_to_disk         (bool)  --  restore to disk flag

                    default: False

                restore_to_disk_job     (list)   --  list of backup job ids to restore to disk

                    default: None

                destination_path        (str)   --  destination path for restore

                    default: None

                revert                 (bool)  --  boolean to specify whether to do a
                                                   hardware revert in restore
                    default: False

            Returns:
                object -- Job containing restore details

        &#34;&#34;&#34;
        instance_object = self._instance_object
        if dest_client_name is None:
            dest_client_name = instance_object._agent_object._client_object.client_name

        if dest_instance_name is None:
            dest_instance_name = instance_object.instance_name

        backupset_name = self.backupset_name

        if backupset_name.lower() == &#34;fsbasedbackupset&#34;:
            backupset_flag = True
            if database_list is None:
                database_list = [&#34;/data&#34;]
        else:
            backupset_flag = False

        instance_object._restore_association = self._properties[&#39;backupSetEntity&#39;]
        return instance_object.restore_in_place(
            database_list,
            dest_client_name,
            dest_instance_name,
            backupset_name,
            backupset_flag,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            clone_env=clone_env,
            clone_options=clone_options,
            media_agent=media_agent,
            table_level_restore=table_level_restore,
            staging_path=staging_path,
            no_of_streams=no_of_streams,
            volume_level_restore=volume_level_restore,
            redirect_enabled=redirect_enabled,
            redirect_path=redirect_path,
            restore_to_disk=restore_to_disk,
            restore_to_disk_job=restore_to_disk_job,
            destination_path=destination_path,
            revert=revert)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.backupset.Backupset" href="../backupset.html#cvpysdk.backupset.Backupset">Backupset</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.backupsets.postgresbackupset.PostgresBackupset.configure_live_sync"><code class="name flex">
<span>def <span class="ident">configure_live_sync</span></span>(<span>self, request_json)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs the Task API with the request JSON provided to create live sync configuration,
and returns the contents after parsing the response.</p>
<h2 id="args">Args</h2>
<p>request_json
(dict)
&ndash;
JSON request to run for the API</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Schedule class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if live sync configuration fails</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/postgresbackupset.py#L63-L99" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_live_sync(self, request_json):
    &#34;&#34;&#34;Runs the Task API with the request JSON provided to create live sync configuration,
        and returns the contents after parsing the response.

        Args:
            request_json    (dict)  --  JSON request to run for the API

        Returns:
            object - instance of the Schedule class

        Raises:
            SDKException:
                if live sync configuration fails

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._LIVE_SYNC, request_json)

    if flag:
        if response.json():
            if &#34;taskId&#34; in response.json():
                return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]

                error_message = &#39;Live Sync configuration failed\nError: &#34;{0}&#34;&#39;.format(
                    error_message)
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Failed to create schedule&#39;)
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Backupset&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.postgresbackupset.PostgresBackupset.restore_postgres_server"><code class="name flex">
<span>def <span class="ident">restore_postgres_server</span></span>(<span>self, database_list=None, dest_client_name=None, dest_instance_name=None, copy_precedence=None, from_time=None, to_time=None, clone_env=False, clone_options=None, media_agent=None, table_level_restore=False, staging_path=None, no_of_streams=None, volume_level_restore=False, redirect_enabled=False, redirect_path=None, restore_to_disk=False, restore_to_disk_job=None, destination_path=None, revert=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to restore the Postgres server</p>
<pre><code>Args:

    database_list               (List) -- List of databases

    dest_client_name            (str)  -- Destination Client name

    dest_instance_name          (str)  -- Destination Instance name

    copy_precedence             (int)  -- Copy precedence associted with storage

    from_time               (str)   --  time to retore the contents after
        format: YYYY-MM-DD HH:MM:SS

        default: None

    to_time                 (str)   --  time to retore the contents before
        format: YYYY-MM-DD HH:MM:SS

        default: None

    clone_env                   (bool)  --  boolean to specify whether the database
    should be cloned or not

        default: False

    clone_options               (dict)  --  clone restore options passed in a dict

        default: None

        Accepted format: {
                            "stagingLocaion": "/gk_snap",
                            "forceCleanup": True,
                            "port": "5595",
                            "libDirectory": "/opt/PostgreSQL/9.6/lib",
                            "isInstanceSelected": True,
                            "reservationPeriodS": 3600,
                            "user": "postgres",
                            "binaryDirectory": "/opt/PostgreSQL/9.6/bin"

                         }

    media_agent             (str)   --  media agent name

        default: None

    table_level_restore     (bool)  --  boolean to specify if the restore operation
    is table level

        default: False

    staging_path            (str)   --  staging path location for table level restore

        default: None

    no_of_streams           (int)   --  number of streams to be used by
    volume level restore

        default: None

    volume_level_restore    (bool)  --  volume level restore flag

        default: False

    redirect_enabled         (bool)  --  boolean to specify if redirect restore is
    enabled

        default: False

    redirect_path           (str)   --  Path specified in advanced restore options
    in order to perform redirect restore

        default: None

    restore_to_disk         (bool)  --  restore to disk flag

        default: False

    restore_to_disk_job     (list)   --  list of backup job ids to restore to disk

        default: None

    destination_path        (str)   --  destination path for restore

        default: None

    revert                 (bool)  --  boolean to specify whether to do a
                                       hardware revert in restore
        default: False

Returns:
    object -- Job containing restore details
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/postgresbackupset.py#L151-L306" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_postgres_server(
        self,
        database_list=None,
        dest_client_name=None,
        dest_instance_name=None,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        clone_env=False,
        clone_options=None,
        media_agent=None,
        table_level_restore=False,
        staging_path=None,
        no_of_streams=None,
        volume_level_restore=False,
        redirect_enabled=False,
        redirect_path=None,
        restore_to_disk=False,
        restore_to_disk_job=None,
        destination_path=None,
        revert=False):
    &#34;&#34;&#34;
    Method to restore the Postgres server

        Args:

            database_list               (List) -- List of databases

            dest_client_name            (str)  -- Destination Client name

            dest_instance_name          (str)  -- Destination Instance name

            copy_precedence             (int)  -- Copy precedence associted with storage

            from_time               (str)   --  time to retore the contents after
                format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time                 (str)   --  time to retore the contents before
                format: YYYY-MM-DD HH:MM:SS

                default: None

            clone_env                   (bool)  --  boolean to specify whether the database
            should be cloned or not

                default: False

            clone_options               (dict)  --  clone restore options passed in a dict

                default: None

                Accepted format: {
                                    &#34;stagingLocaion&#34;: &#34;/gk_snap&#34;,
                                    &#34;forceCleanup&#34;: True,
                                    &#34;port&#34;: &#34;5595&#34;,
                                    &#34;libDirectory&#34;: &#34;/opt/PostgreSQL/9.6/lib&#34;,
                                    &#34;isInstanceSelected&#34;: True,
                                    &#34;reservationPeriodS&#34;: 3600,
                                    &#34;user&#34;: &#34;postgres&#34;,
                                    &#34;binaryDirectory&#34;: &#34;/opt/PostgreSQL/9.6/bin&#34;

                                 }

            media_agent             (str)   --  media agent name

                default: None

            table_level_restore     (bool)  --  boolean to specify if the restore operation
            is table level

                default: False

            staging_path            (str)   --  staging path location for table level restore

                default: None

            no_of_streams           (int)   --  number of streams to be used by
            volume level restore

                default: None

            volume_level_restore    (bool)  --  volume level restore flag

                default: False

            redirect_enabled         (bool)  --  boolean to specify if redirect restore is
            enabled

                default: False

            redirect_path           (str)   --  Path specified in advanced restore options
            in order to perform redirect restore

                default: None

            restore_to_disk         (bool)  --  restore to disk flag

                default: False

            restore_to_disk_job     (list)   --  list of backup job ids to restore to disk

                default: None

            destination_path        (str)   --  destination path for restore

                default: None

            revert                 (bool)  --  boolean to specify whether to do a
                                               hardware revert in restore
                default: False

        Returns:
            object -- Job containing restore details

    &#34;&#34;&#34;
    instance_object = self._instance_object
    if dest_client_name is None:
        dest_client_name = instance_object._agent_object._client_object.client_name

    if dest_instance_name is None:
        dest_instance_name = instance_object.instance_name

    backupset_name = self.backupset_name

    if backupset_name.lower() == &#34;fsbasedbackupset&#34;:
        backupset_flag = True
        if database_list is None:
            database_list = [&#34;/data&#34;]
    else:
        backupset_flag = False

    instance_object._restore_association = self._properties[&#39;backupSetEntity&#39;]
    return instance_object.restore_in_place(
        database_list,
        dest_client_name,
        dest_instance_name,
        backupset_name,
        backupset_flag,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        clone_env=clone_env,
        clone_options=clone_options,
        media_agent=media_agent,
        table_level_restore=table_level_restore,
        staging_path=staging_path,
        no_of_streams=no_of_streams,
        volume_level_restore=volume_level_restore,
        redirect_enabled=redirect_enabled,
        redirect_path=redirect_path,
        restore_to_disk=restore_to_disk,
        restore_to_disk_job=restore_to_disk_job,
        destination_path=destination_path,
        revert=revert)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.postgresbackupset.PostgresBackupset.run_live_sync"><code class="name flex">
<span>def <span class="ident">run_live_sync</span></span>(<span>self, dest_client_name, dest_instance_name, baseline_job)</span>
</code></dt>
<dd>
<div class="desc"><p>runs live sync replication operation</p>
<h2 id="args">Args</h2>
<p>dest_client_name
(str)
&ndash;
destination client name where files are to be
restored</p>
<p>dest_instance_name
(str)
&ndash;
destination postgres instance name of
destination client</p>
<p>baseline_job
(obj)
&ndash;
baseline backup job object</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Schedule class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/postgresbackupset.py#L101-L149" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_live_sync(
        self,
        dest_client_name,
        dest_instance_name,
        baseline_job):
    &#34;&#34;&#34;runs live sync replication operation

        Args:

            dest_client_name        (str)   --  destination client name where files are to be
            restored

            dest_instance_name      (str)   --  destination postgres instance name of
            destination client

            baseline_job            (obj)   --  baseline backup job object

        Returns:
            object - instance of the Schedule class

    &#34;&#34;&#34;
    instance_object = self._instance_object
    instance_object._restore_association = self._properties[&#34;backupSetEntity&#34;]
    request_json = instance_object._restore_json(
        paths=[&#34;/data&#34;],
        dest_client_name=dest_client_name,
        dest_instance_name=dest_instance_name,
        backupset_name=&#34;fsbasedbackupset&#34;,
        backupset_flag=True,
        no_image=True,
        overwrite=True,
        baseline_jobid=int(baseline_job.job_id),
        baseline_ref_time=int(baseline_job.summary[&#39;jobStartTime&#39;]))

    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;] = {
        &#39;backupLevel&#39;: 2,
        &#39;vsaBackupOptions&#39;: {}
        }
    request_json[&#39;taskInfo&#39;][&#39;task&#39;][&#39;taskType&#39;] = 2
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;operationType&#39;] = 1007
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;subTaskName&#39;] = &#34;automation&#34;
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;] = {
        &#34;freq_type&#34;: 4096,
        &#34;timeZone&#34;: {
            &#34;TimeZoneName&#34;: &#34;(UTC) Coordinated Universal Time&#34;
            }
        }

    return self.configure_live_sync(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.backupset.Backupset" href="../backupset.html#cvpysdk.backupset.Backupset">Backupset</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.backupset.Backupset.backed_up_files_count" href="../backupset.html#cvpysdk.backupset.Backupset.backed_up_files_count">backed_up_files_count</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backup" href="../backupset.html#cvpysdk.backupset.Backupset.backup">backup</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_id" href="../backupset.html#cvpysdk.backupset.Backupset.backupset_id">backupset_id</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_name" href="../backupset.html#cvpysdk.backupset.Backupset.backupset_name">backupset_name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.browse" href="../backupset.html#cvpysdk.backupset.Backupset.browse">browse</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.delete_data" href="../backupset.html#cvpysdk.backupset.Backupset.delete_data">delete_data</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.description" href="../backupset.html#cvpysdk.backupset.Backupset.description">description</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.find" href="../backupset.html#cvpysdk.backupset.Backupset.find">find</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.guid" href="../backupset.html#cvpysdk.backupset.Backupset.guid">guid</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_default_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.is_default_backupset">is_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_on_demand_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.is_on_demand_backupset">is_on_demand_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.list_media" href="../backupset.html#cvpysdk.backupset.Backupset.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.name" href="../backupset.html#cvpysdk.backupset.Backupset.name">name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.plan" href="../backupset.html#cvpysdk.backupset.Backupset.plan">plan</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.properties" href="../backupset.html#cvpysdk.backupset.Backupset.properties">properties</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.refresh" href="../backupset.html#cvpysdk.backupset.Backupset.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.set_default_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.set_default_backupset">set_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.update_properties" href="../backupset.html#cvpysdk.backupset.Backupset.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#postgresbackupset">PostgresBackupset:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.backupsets" href="index.html">cvpysdk.backupsets</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.backupsets.postgresbackupset.PostgresBackupset" href="#cvpysdk.backupsets.postgresbackupset.PostgresBackupset">PostgresBackupset</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.backupsets.postgresbackupset.PostgresBackupset.configure_live_sync" href="#cvpysdk.backupsets.postgresbackupset.PostgresBackupset.configure_live_sync">configure_live_sync</a></code></li>
<li><code><a title="cvpysdk.backupsets.postgresbackupset.PostgresBackupset.restore_postgres_server" href="#cvpysdk.backupsets.postgresbackupset.PostgresBackupset.restore_postgres_server">restore_postgres_server</a></code></li>
<li><code><a title="cvpysdk.backupsets.postgresbackupset.PostgresBackupset.run_live_sync" href="#cvpysdk.backupsets.postgresbackupset.PostgresBackupset.run_live_sync">run_live_sync</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>