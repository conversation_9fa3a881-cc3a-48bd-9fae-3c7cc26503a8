<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.services API documentation</title>
<meta name="description" content="Service URLs for REST API operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.services</code></h1>
</header>
<section id="section-intro">
<p>Service URLs for REST API operations.</p>
<h2 id="services_dict">Services_Dict</h2>
<p>A python dictionary for holding all the API services endpoints.</p>
<p>|</p>
<p>get_services(web_service):
updates the SERVICES_DICT with the WebConsole API URL</p>
<p>To add a new REST API End-point to the SDK, user needs to add a key to the SERVICES_DICT_TEMPLATE
dictionary, for their usage, and the value will be in the format:</p>
<pre><code>"{0}{{ENDPOINT}}"
</code></pre>
<p>{0} will be replaced by the webconsole URL</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/services.py#L1-L732" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Service URLs for REST API operations.

SERVICES_DICT:
    A python dictionary for holding all the API services endpoints.

|

get_services(web_service):
    updates the SERVICES_DICT with the WebConsole API URL


To add a new REST API End-point to the SDK, user needs to add a key to the SERVICES_DICT_TEMPLATE
dictionary, for their usage, and the value will be in the format:

    &#34;{0}{{ENDPOINT}}&#34;

{0} will be replaced by the webconsole URL

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals


SERVICES_DICT_TEMPLATE = {
    &#39;LOGIN&#39;: &#39;{0}Login&#39;,
    &#39;LOGOUT&#39;: &#39;{0}Logout&#39;,
    &#39;RENEW_LOGIN_TOKEN&#39;: &#39;{0}RenewLoginToken&#39;,
    &#39;COMMSERV&#39;: &#39;{0}CommServ&#39;,
    &#39;GET_SAML_TOKEN&#39;: &#39;{0}Commcell/SamlToken?validityInMins=%s&#39;,
    &#39;WHO_AM_I&#39;: &#39;{0}WhoAmI&#39;,
    &#39;CREATE_RC&#39;: &#39;{0}V4/SoftwareCache&#39;,
    &#39;DOWNLOAD_SOFTWARE&#39;: &#39;{0}V4/DownloadSoftware&#39;,
    &#39;UPGRADE_SOFTWARE&#39;: &#39;{0}V4/UpgradeSoftware&#39;,

    &#39;TFA&#39;: &#39;{0}Commcell/Properties/TwoFactorAuth&#39;,
    &#39;TFA_ENABLE&#39;: &#39;{0}Commcell/Properties/TwoFactorAuth/Action/Enable&#39;,
    &#39;TFA_DISABLE&#39;: &#39;{0}Commcell/Properties/TwoFactorAuth/Action/Disable&#39;,
    &#39;PRIVACY_ENABLE&#39;: &#39;{0}Commcell/Properties/Privacy/Action/Enable&#39;,
    &#39;PRIVACY_DISABLE&#39;: &#39;{0}Commcell/Properties/Privacy/Action/Disable&#39;,
    &#39;ACCOUNT_lOCK_SETTINGS&#39;: &#39;{0}Commcell/Properties/AccountLockSettings&#39;,
    &#39;ORG_TFA&#39;: &#39;{0}Organization/%s/TwoFactorAuth&#39;,
    &#39;ORG_TFA_ENABLE&#39;: &#39;{0}Organization/%s/TwoFactorAuth/Action/Enable&#39;,
    &#39;ORG_TFA_DISABLE&#39;: &#39;{0}Organization/%s/TwoFactorAuth/Action/Disable&#39;,
    &#39;TFA_STATUS_OF_USER&#39;: &#39;{0}Security/TwoFactorAuth/Status?username=%s&#39;,

    &#39;GET_ALL_CLIENTS&#39;: &#39;{0}Client&#39;,
    &#39;GET_VIRTUAL_CLIENTS&#39;: &#39;{0}Client?PseudoClientType=VSPseudo&#39;,
    &#39;GET_VIRTUALIZATION_ACCESS_NODES&#39;: &#39;{0}VSAClientAndClientGroupList&#39;,
    &#39;GET_FILE_SERVER_CLIENTS&#39;: &#39;{0}/v4/FileServers&#39;,
    &#39;CLIENTFORCEDELETE&#39;: &#39;{0}Client/%s?forceDelete=1&#39;,
    &#39;CLIENT&#39;: &#39;{0}Client/%s&#39;,
    &#39;CLIENT_LOGS&#39;: &#39;{0}Client/%s/Logs/Read&#39;,
    &#39;GET_ADDITIIONAL_SETTINGS&#39;: &#39;{0}Client/%s/AdditionalSettings&#39;,
    &#39;FILTER_CLIENTS&#39;: &#39;{0}Client?%s&#39;,
    &#39;GET_ALL_CLIENTS_PLUS_HIDDEN&#39;: &#39;{0}Client?hiddenclients=true&#39;,
    &#39;GET_ALL_PSEUDO_CLIENTS&#39;: &#39;{0}Client?PseudoClientType&#39;,
    &#39;CHECK_READINESS&#39;: &#39;{0}Client/%s/CheckReadiness?network=%s&amp;resourceCapacity=%s&#39;
                       &#39;&amp;NeedXmlResp=true&amp;includeDisabledClients=%s&amp;CSCCNetworkCheck=%s&#39;
                       &#39;&amp;applicationCheck=%s&amp;additionalResources=%s&#39;,
    &#39;RUN_TRUEUP&#39;: &#39;{0}Office365/TrueUp&#39;,
    &#39;READ_TRUEUP_RESULTS_CLIENT&#39;: &#39;{0}Job/Office365/Results?subclientId=%s&amp;clientId=%s&amp;appTypeId=134&amp;options=4&#39;,
    &#39;READ_TRUEUP_RESULTS_USER&#39;: &#39;{0}Job/Office365/Results?subclientId=%s&amp;clientId=%s&amp;options=3&amp;appTypeId=134&amp;userGUID=%s&#39;,
    &#39;MONGODB_CHECK_READINESS&#39;: &#39;{0}/clients/mongodb/status&#39;,
    &#39;CLIENT_BROWSE_FS&#39;: &#39;{0}/client/%s/browsefs&#39;,
    &#39;GET_ALL_AGENTS&#39;: &#39;{0}Agent?clientId=%s&#39;,
    &#39;AGENT&#39;: &#39;{0}Agent&#39;,
    &#39;GET_AGENT&#39;: &#39;{0}Agent?clientId=%s&amp;applicationId=%s&amp;propertyLevel=30&#39;,

    &#39;GET_ALL_BACKUPSETS&#39;: &#39;{0}Backupset?clientId=%s&amp;propertyLevel=10&#39;,
    &#39;BACKUPSET&#39;: &#39;{0}Backupset/%s&#39;,
    &#39;ADD_BACKUPSET&#39;: &#39;{0}Backupset&#39;,

    &#39;GET_ALL_INSTANCES&#39;: &#39;{0}Instance?clientId=%s&#39;,
    &#39;INSTANCE&#39;: &#39;{0}Instance/%s&#39;,
    &#39;APPLICATION_INSTANCE&#39;: &#39;{0}Application/%s&#39;,
    &#39;APPLICATION&#39;: &#39;{0}Application&#39;,
    &#39;INSTANCE_CREDENTIALS&#39;: &#39;{0}v4/Hypervisor/%s/Credentials&#39;,

    &#39;GET_ALL_SUBCLIENTS&#39;: &#39;{0}Subclient?clientId=%s&amp;applicationId=%s&amp;propertyLevel=20&#39;,
    &#39;ADD_SUBCLIENT&#39;: &#39;{0}Subclient&#39;,
    &#39;SUBCLIENT&#39;: &#39;{0}Subclient/%s&#39;,
    &#39;SUBCLIENT_BACKUP&#39;: &#39;{0}Subclient/%s/action/backup?backupLevel=%s&#39;,
    &#39;VM_BACKUP&#39;: &#39;{0}v2/vsa/vm/%s/backup?backupLevel=%s&#39;,
    &#39;PREVIEW&#39;: &#39;{0}Subclient/Content/Preview&#39;,

    &#39;GET_JOBS&#39;: &#39;{0}Job?clientId=%s&amp;jobFilter=%s&#39;,
    &#39;JOB&#39;: &#39;{0}Job/%s&#39;,
    &#39;JOB_DETAILS&#39;: &#39;{0}JobDetails&#39;,
    &#39;JOB_TASK_DETAILS&#39;: &#39;{0}Job/%s/TaskDetails&#39;,
    &#39;SUSPEND_JOB&#39;: &#39;{0}Job/%s/action/pause&#39;,
    &#39;RESUME_JOB&#39;: &#39;{0}Job/%s/action/resume&#39;,
    &#39;KILL_JOB&#39;: &#39;{0}Job/%s/action/kill&#39;,
    &#39;RESUBMIT_JOB&#39;: &#39;{0}Job/%s/action/Resubmit&#39;,
    &#39;ALL_JOBS&#39;: &#39;{0}Jobs&#39;,
    &#39;JOB_EVENTS&#39;: &#39;{0}Events?jobId=%s&#39;,
    &#39;JOB_MANAGEMENT_SETTINGS&#39;: &#39;{0}CommServ/JobManagementSetting&#39;,

    &#39;ENABLE_SHARED_LAPTOP&#39;: &#39;{0}Commcell/Properties/SharedLaptopUsage/Action/Enable&#39;,
    &#39;DISABLE_SHARED_LAPTOP&#39;: &#39;{0}Commcell/Properties/SharedLaptopUsage/Action/Disable&#39;,

    &#39;GET_MEDIA_AGENTS&#39;: &#39;{0}V2/MediaAgents&#39;,
    &#39;LIBRARY&#39;: &#39;{0}Library&#39;,
    &#39;GET_LIBRARY_PROPERTIES&#39;: &#39;{0}Library/%s&#39;,
    &#39;DETECT_TAPE_LIBRARY&#39;: &#39;{0}Library?Action=detect&#39;,
    &#39;CONFIGURE_TAPE_LIBRARY&#39;: &#39;{0}Library?Action=configureTape&#39;,
    &#39;EDIT_CLOUD_CONTROLLER&#39;: &#39;{0}V4/Storage/Cloud/0/Bucket/%s/AccessPath/%s&#39;,
    &#39;GET_AGP_STORAGE&#39;: &#39;{0}V4/Storage/Cloud?additionalProperties=true&amp;storageSubType=4&#39;,

    &#39;GET_MOVE_MOUNTPATH_DETAILS&#39;: &#39;{0}MountPath/%s/Move&#39;,
    &#39;MOVE_MOUNTPATH&#39;: &#39;{0}MountPath/Move&#39;,

    &#39;LOCK_MM_CONFIGURATION&#39;: &#39;{0}LockMMConfiguration&#39;,

    &#39;STORAGE_POLICY&#39;: &#39;{0}StoragePolicy&#39;,
    &#39;GET_STORAGE_POLICY&#39;: &#39;{0}StoragePolicy/%s&#39;,
    &#39;DELETE_STORAGE_POLICY&#39;: &#39;{0}V2/StoragePolicy&#39;,
    &#39;UPDATE_STORAGE_POLCY&#39;: &#39;{0}V2/StoragePolicy/%s&#39;,
    &#39;GET_STORAGE_POLICY_ADVANCED&#39;: &#39;{0}v2/StoragePolicy/%s?propertyLevel=10&#39;,
    &#39;CREATE_STORAGE_POLICY_COPY&#39;: &#39;{0}StoragePolicy?Action=createCopy&#39;,
    &#39;DELETE_STORAGE_POLICY_COPY&#39;: &#39;{0}StoragePolicy?Action=deleteCopy&#39;,
    &#39;SCHEDULE_POLICY&#39;: &#39;{0}SchedulePolicy&#39;,
    &#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;: &#39;{0}Task&#39;,
    &#39;GET_SCHEDULE_POLICY&#39;: &#39;{0}SchedulePolicy/%s&#39;,
    &#39;MEDIA_AGENT&#39;: &#39;{0}MediaAgent/%s&#39;,
    &#39;CLOUD_MEDIA_AGENT&#39;: &#39;{0}MediaAgent/%s/CloudVMPowerManagement&#39;,
    &#39;STORAGE_POLICY_COPY&#39;: &#39;{0}V2/StoragePolicy/%s/Copy/%s&#39;,
    &#39;DISABLE_STORAGE_POLICY_COMPLIANCE_LOCK&#39;: &#39;{0}V4/StoragePolicy/%s/Copy/%s/ComplianceLock/Disable&#39;,
    &#39;STORAGE_POLICY_INFRASTRUCTUREPOOL&#39;: &#39;{0}/StoragePolicy/Infrastructurepool?planId=%s&#39;,
    &#39;RECOVERY_ENABLERS&#39;: &#39;{0}MediaAgent/RecoveryEnabler?osType=CLIENT_PLATFORM_OSTYPE_UNIX &#39;,
    &#39;GET_ALL_ALERTS&#39;: &#39;{0}AlertRule&#39;,
    &#39;ALERT&#39;: &#39;{0}AlertRule/%s&#39;,
    &#39;ALERT_TEST&#39;: &#39;{0}AlertRule/%s/Test&#39;,
    &#39;CREATE_BLR_PAIR&#39;: &#39;{0}Replications/Groups&#39;,
    &#39;DELETE_BLR_PAIR&#39;: &#39;{0}Replications/Monitors/continuous/%s&#39;,
    &#39;GET_BLR_PAIRS&#39;: &#39;{0}Replications/Monitors/continuous&#39;,
    &#39;GET_BLR_PAIR&#39;: &#39;{0}Replications/Monitors/continuous?replicationPairId=%s&#39;,
    &#39;GET_BLR_PAIR_STATS&#39;: &#39;{0}Replications/Statistics/%s&#39;,
    &#39;GRANULAR_BLR_POINTS&#39;: &#39;{0}Replications/Monitors/continuous/VmScale?destProxyClientId=%s&amp;subclientId=%s&amp;vmUuid=%s&#39;,
    &#39;BLR_BOOT_DETAILS&#39;: &#39;{0}/Replications/Monitors/continuous/Boot?replicationPairId=%s&amp;bootType=%s&amp;latest=true&#39;,
    &#39;BROWSE_MOUNT_POINTS&#39;: &#39;{0}/Client/%s/Action/BrowseMountPoints&#39;,

    &#39;GET_VM_BROWSE&#39;: &#39;{0}/VMBrowse?inventoryPath=%%5CFOLDER%%3AApplications%%3AApplications&amp;PseudoClientId=%s&#39;,

    &#39;GET_K8S_NS_BROWSE&#39;: &#39;{0}/VMBrowse?inventoryPath=%%5CFOLDER%%3AApplications%%3AApplications&amp;PseudoClientId=%s&amp;vendor=KUBERNETES&#39;,
    &#39;GET_K8S_VOLUME_BROWSE&#39;: &#39;{0}/VMBrowse?inventoryPath=%%5CFOLDER%%3AVolumes%%3AVolumes%%5CFOLDER%%3A%s%%3A%s&amp;PseudoClientId=%s&amp;vendor=KUBERNETES&#39;,
    &#39;GET_K8S_APP_BROWSE&#39;: &#39;{0}/VMBrowse?inventoryPath=%%5CFOLDER%%3AApplications%%3AApplications%%5CFOLDER%%3A%s%%3A%s&amp;PseudoClientId=%s&amp;vendor=KUBERNETES&#39;,
    &#39;GET_K8S_LABEL_BROWSE&#39;: &#39;{0}/VMBrowse?inventoryPath=%%5CFOLDER%%3ALabels%%3ALabels%%5CFOLDER%%3A%s%%3A%s&amp;PseudoClientId=%s&amp;vendor=KUBERNETES&#39;,

    &#39;MODIFY_ALERT&#39;: &#39;{0}AlertRule/%s/Action/Modify&#39;,
    &#39;GET_ALL_CONSOLE_ALERTS&#39;: &#39;{0}Alert?pageNo=%s&amp;pageCount=%s&#39;,
    &#39;GET_CONSOLE_ALERT&#39;: &#39;{0}Alert/%s&#39;,
    &#39;ENABLE_ALERT_NOTIFICATION&#39;: &#39;{0}AlertRule/%s/notificationType/%s/Action/Enable&#39;,
    &#39;DISABLE_ALERT_NOTIFICATION&#39;: &#39;{0}AlertRule/%s/notificationType/%s/Action/Disable&#39;,
    &#39;ENABLE_ALERT&#39;: &#39;{0}AlertRule/%s/Action/Enable&#39;,
    &#39;DISABLE_ALERT&#39;: &#39;{0}AlertRule/%s/Action/Disable&#39;,
    &#39;EMAIL_SERVER&#39;: &#39;{0}EmailServer&#39;,

    &#39;INVENTORY_SCHEDULES&#39;: &#39;{0}Schedules?seaDataSourceId=%s&amp;appType=132&#39;,
    &#39;CLIENT_SCHEDULES&#39;: &#39;{0}Schedules?clientId=%s&#39;,
    &#39;AGENT_SCHEDULES&#39;: &#39;{0}Schedules?clientId=%s&amp;apptypeId=%s&#39;,
    &#39;BACKUPSET_SCHEDULES&#39;: &#39;{0}Schedules?clientId=%s&amp;apptypeId=%s&amp;backupsetId=%s&#39;,
    &#39;INSTANCE_SCHEDULES&#39;:  &#39;{0}Schedules?clientId=%s&amp;apptypeId=%s&amp;instanceId=%s&#39;,
    &#39;SUBCLIENT_SCHEDULES&#39;: (&#39;{0}Schedules?clientId=%s&amp;apptypeId=%s&amp;&#39;
                            &#39;backupsetId=%s&amp;subclientId=%s&#39;),
    &#39;WORKFLOW_SCHEDULES&#39;: &#39;{0}Schedules?workflowId=%s&#39;,
    &#39;REPORT_SCHEDULES&#39;: &#39;{0}/ScheduleReports&#39;,
    &#39;OPTYPE_SCHEDULES&#39;: &#39;{0}/Schedules?operationType=%s&#39;,
    &#39;COMMCELL_SCHEDULES&#39;: &#39;{0}/Schedules&#39;,
    &#39;SCHEDULE&#39;: &#39;{0}Schedules/%s&#39;,
    &#39;ENABLE_SCHEDULE&#39;: &#39;{0}Schedules/task/Action/Enable&#39;,
    &#39;DISABLE_SCHEDULE&#39;: &#39;{0}Schedules/task/Action/Disable&#39;,

    &#39;LIVE_SYNC&#39;: &#39;{0}Task&#39;,
    &#39;LIVE_SYNC_DETAILS&#39;: &#39;{0}/Task/%s/Details&#39;,

    &#39;CLIENTGROUPS&#39;: &#39;{0}ClientGroup&#39;,
    &#39;CLIENTGROUP&#39;: &#39;{0}ClientGroup/%s&#39;,
    &#39;SERVERGROUPS_V4&#39;: &#39;{0}V4/ServerGroup&#39;,

    &#39;USERGROUPS&#39;: &#39;{0}UserGroup?includeSystemCreated=%s&#39;,
    &#39;USERGROUP&#39;: &#39;{0}UserGroup/%s&#39;,
    &#39;USERGROUP_V4&#39;: &#39;{0}V4/UserGroup/%s&#39;,
    &#39;DELETE_USERGROUP&#39;: &#39;{0}UserGroup/%s?newUserId=%s&amp;newUserGroupId=%s&#39;,
    &#39;COMPANY_USERGROUP&#39;: &#39;{0}UserGroup?parentProvider/providerId=%s&#39;,

    &#39;BROWSE&#39;: &#39;{0}DoBrowse&#39;,
    &#39;RESTORE&#39;: &#39;{0}CreateTask&#39;,
    &#39;DELETE&#39;: &#39;{0}DeleteDocuments&#39;,
    &#39;DATABASES&#39;: &#39;{0}databases&#39;,
    &#39;DB_INSTANCES&#39;: &#39;{0}databases/instances&#39;,
    &#39;DB_CLONES&#39;: &#39;{0}databases/clones&#39;,
    &#39;SQL_CLONES&#39;: &#39;{0}sql/clones&#39;,
    &#39;SQL_DATABASE&#39;: &#39;{0}sql/databases?instance=%s&amp;databaseName=%s&#39;,
    &#39;SQL_DATABASES&#39;: &#39;{0}sql/databases?databaseName=%s&#39;,
    &#39;SQL_DATABASE_LIST&#39;: &#39;{0}sql/databases?instance=%s&#39;,
    &#39;SQL_DATABASE_DETAILS&#39;: &#39;{0}sql/instance/%s/database/%s&#39;,
    &#39;SQL_AG_GROUPS&#39;: &#39;{0}v2/sql/availabilityGroups/client/%s/instance/%s&#39;,
    &#39;SQL_AG_GROUP_REPLICAS&#39;: &#39;{0}v2/sql/availabilityGroupReplicas/client/%s/instance/%s/availabilityGroup/%s&#39;,

    &#39;GET_WORKFLOWS&#39;: &#39;{0}Workflow&#39;,
    &#39;DEPLOY_WORKFLOW&#39;: &#39;{0}Workflow/%s/action/deploy&#39;,
    &#39;EXECUTE_WORKFLOW_API&#39;: &#39;{0}Workflow/%s/action/execute&#39;,
    &#39;EXECUTE_WORKFLOW&#39;: &#39;{0}wapi/%s&#39;,
    &#39;EXECUTE_INTERACTIVE_WORKFLOW&#39;: &#39;{0}workflowrequest&#39;,
    &#39;GET_WORKFLOW&#39;: &#39;{0}Workflow/%s&#39;,
    &#39;GET_WORKFLOW_DEFINITION&#39;: &#39;{0}Workflow/%s/definition&#39;,
    &#39;GET_INTERACTIONS&#39;: &#39;{0}WorkflowInteractions&#39;,
    &#39;GET_INTERACTION&#39;: &#39;{0}Workflow/Interaction/%s&#39;,
    &#39;EDIT_WORKFLOW_CONFIG&#39;: &#39;{0}/cr/apps/configform/%s&#39;,
    &#39;APPROVE_WORKFLOW&#39;: &#39;{0}workflow_editor/configuration/%s/approve&#39;,

    &#39;INSTANCE_BROWSE&#39;: &#39;{0}Client/%s/%s/Instance/%s/Browse&#39;,
    &#39;CLOUD_DATABASE_BROWSE&#39;: &#39;{0}BrowseRDSBackups&#39;,

    &#39;SQL_RESTORE_OPTIONS&#39;: &#39;{0}SQL/RestoreOptions&#39;,

    &#39;EXECUTE_QCOMMAND&#39;: &#39;{0}Qcommand/qoperation execute&#39;,
    &#39;EXECUTE_QSCRIPT&#39;: &#39;{0}Qcommand/qoperation execscript %s&#39;,
    &#39;QCOMMAND&#39;: &#39;{0}QCommand&#39;,
    &#39;EXEC_QCOMMAND&#39;: &#39;{0}ExecuteQCommand&#39;,

    &#39;SOFTWARESTORE_DOWNLOADITEM&#39;: &#39;{0}DownloadFile&#39;,
    &#39;SOFTWARESTORE_PKGINFO&#39;: &#39;{0}SoftwareStore/getPackagePublishInfo?packageName=%s&#39;,
    &#39;SOFTWARESTORE_GETPKGID&#39;: &#39;{0}SoftwareStoreItem&#39;,

    &#39;CREATE_TASK&#39;: &#39;{0}CreateTask&#39;,
    &#39;ADD_INSTANCE&#39;: &#39;{0}Instance&#39;,
    &#39;MASKING_POLICY&#39;: &#39;{0}MaskingPolicy&#39;,

    &#39;DO_COMPLIANCE_SEARCH&#39;: &#39;{0}doWebSearch&#39;,
    &#39;GET_EXPORT_SETS&#39;: &#39;{0}GetContainers&#39;,
    &#39;GET_EXPORTS&#39;: &#39;{0}getContainerItems&#39;,
    &#39;ADD_EXPORT_SET&#39;: &#39;{0}PerformContainerOperation&#39;,
    &#39;DELETE_EXPORT_SET&#39;: &#39;{0}Containers/Action/Delete&#39;,
    &#39;EXPORT_ITEM_TO_SET&#39;: &#39;{0}Download&#39;,
    &#39;DOWNLOAD_EXPORT_ITEMS&#39;: &#39;{0}DownloadFile&#39;,
    &#39;GET_ANALYTICS_ENGINES&#39;: &#39;{0}dcube/getAnalyticsEngine&#39;,
    &#39;GET_ALL_DATASOURCES&#39;: &#39;{0}dcube/GetDataSources?summary=1&#39;,
    &#39;GET_DATASOURCE&#39;: &#39;{0}dcube/getDataSource/%s&#39;,
    &#39;GET_ALL_HANDLERS&#39;: &#39;{0}dcube/getAllHandlers?dsId=%s&#39;,
    &#39;GET_HANDLER&#39;: &#39;{0}dcube/getHandler/?dsId=%s&amp;handlerId=%s&#39;,
    &#39;GET_CRAWL_HISTORY&#39;: &#39;{0}dcube/GetHistory/%s&#39;,
    &#39;GET_HANDLERS&#39;: &#39;{0}dcube/gethandler?datasourceId=%s&#39;,
    &#39;CREATE_HANDLER&#39;: &#39;{0}dcube/savehandler&#39;,
    &#39;GET_DATASOURCE_SCHEMA&#39;: &#39;{0}dcube/getDSSchema/%s&#39;,
    &#39;UPDATE_DATASOURCE_SCHEMA&#39;: &#39;{0}dcube/updateschema&#39;,
    &#39;GET_JDBC_DRIVERS&#39;: &#39;{0}dcube/GetJDBCDrivers/%s&#39;,
    &#39;DELETE_DATASOURCE_CONTENTS&#39;: &#39;{0}dcube/deletedata/%s?softdelete=true&#39;,
    &#39;DELETE_DATASOURCE&#39;: &#39;{0}dcube/deleteDataSource/%s&#39;,
    &#39;PRUNE_DATASOURCE&#39;: &#39;{0}indexing/uns/deletecollection&#39;,
    &#39;CREATE_DATASOURCE&#39;: &#39;{0}dcube/createDataSource&#39;,
    &#39;DATACUBE_IMPORT_DATA&#39;: &#39;{0}dcube/post/%s/%s&#39;,
    &#39;START_JOB_DATASOURCE&#39;: &#39;{0}dcube/startjob/%s&#39;,
    &#39;GET_STATUS_DATASOURCE&#39;: &#39;{0}dcube/GetStatus/%s&#39;,
    &#39;EXECUTE_HANDLER&#39;: &#39;{0}dcube/handler/%s/%s?%s&#39;,
    &#39;DELETE_HANDLER&#39;: &#39;{0}dcube/deletehandler/%s&#39;,
    &#39;SHARE_HANDLER&#39;: &#39;{0}dcube/share/handler&#39;,
    &#39;SHARE_DATASOURCE&#39;: &#39;{0}dcube/share/datasource&#39;,
    &#39;GET_CONTENT_ANALYZER_CLOUD&#39;: &#39;{0}getContentAnalyzerClient&#39;,
    &#39;ACTIVATE_ENTITIES&#39;: &#39;{0}dcube/entity&#39;,
    &#39;ACTIVATE_ENTITY&#39;: &#39;{0}dcube/entity/%s&#39;,
    &#39;ACTIVATE_ENTITY_CONTAINER&#39;: &#39;{0}EntityExtractionRules?getDisabled=true&#39;,
    &#39;GET_TAGS&#39;: &#39;{0}EDiscovery/Tags&#39;,
    &#39;GET_ENTITY_TAGS&#39;: &#39;{0}V4/Tags/AssociatedEntities&#39;,
    &#39;CREATE_ENTITY_TAGS&#39;: &#39;{0}EDiscovery/Tags&#39;,
    &#39;DELETE_ENTITY_TAGS&#39;: &#39;{0}V4/EntityTags/%s&#39;,
    &#39;ADD_CONTAINER&#39;: &#39;{0}PerformContainerOperation&#39;,
    &#39;DELETE_CONTAINER&#39;: &#39;{0}Containers/Action/Delete&#39;,
    &#39;CA_UPLOAD_FILE&#39;: &#39;{0}ContentAnalyzer/%s/action/uploadFile&#39;,
    &#39;GET_CLASSIFIERS&#39;: &#39;{0}dcube/classifiers?getDisabled=True&#39;,
    &#39;START_TRAINING&#39;: &#39;{0}ContentAnalyzer/%s/%s/ml/action/train&#39;,
    &#39;CANCEL_TRAINING&#39;: &#39;{0}ContentAnalyzer/%s/%s/training/cancel&#39;,

    &#39;V4_ACTIVATE_DS_PERMISSION&#39;: &#39;{0}V4/Activate/SEA_DATASOURCE_ENTITY/%s/Permissions&#39;,
    &#39;V4_INVENTORY_CRAWL&#39;: &#39;{0}V4/InventoryManager/Inventory/%s/Crawl&#39;,
    &#39;EDISCOVERY_INVENTORIES&#39;: &#39;{0}V4/InventoryManager/Inventory&#39;,
    &#39;EDISCOVERY_INVENTORY&#39;: &#39;{0}V4/InventoryManager/Inventory/%s&#39;,
    &#39;EDISCOVERY_ASSETS&#39;: &#39;{0}V4/InventoryManager/Inventory/%s/Assets&#39;,
    &#39;EDISCOVERY_ASSET&#39;: &#39;{0}V4/InventoryManager/Inventory/%s/Assets/%s&#39;,
    &#39;EDISCOVERY_ASSET_JOBS&#39;: &#39;{0}V4/InventoryManager/Inventory/%s/Assets/%s/jobs&#39;,
    &#39;EDISCOVERY_CRAWL&#39;: &#39;{0}EDiscoveryClients/Clients/%s/Jobs?datasourceId=%s&amp;type=%s&amp;operation=%s&#39;,
    &#39;EDISCOVERY_JOBS_HISTORY&#39;: &#39;{0}EDiscoveryClients/Clients/%s/Jobs/History?type=%s&amp;datasourceId=%s&#39;,
    &#39;EDISCOVERY_JOB_STATUS&#39;: &#39;{0}EDiscoveryClients/Clients/%s/Jobs/Status?type=%s&amp;datasourceId=%s&#39;,
    &#39;EDISCOVERY_GET_DEFAULT_HANDLER&#39;: &#39;{0}dcube/getdefaulthandler/%s&#39;,
    &#39;EDISCOVERY_V2_GET_CLIENTS&#39;: &#39;{0}V2/EDiscoveryClients/Clients?datasourceType=%s&amp;clientGroup=%s&amp;limit=%s&amp;offset=%s&amp;sortBy=%s&amp;sortDir=%s&#39;,
    &#39;EDISCOVERY_V2_GET_CLIENT_DETAILS&#39;: &#39;{0}V2/EDiscoveryClients/Clients/%s?includeDocCount=%s&amp;limit=%s&amp;offset=%s&amp;sortBy=%s&amp;sortDir=%s&amp;datasourceType=%s&#39;,
    &#39;EDISCOVERY_V2_GET_CLIENT_GROUP_DETAILS&#39;: &#39;{0}V2/EDiscoveryClients/ClientGroups/%s?includeDocCount=%s&#39;,
    &#39;EDISCOVERY_SECURITY_ASSOCIATION&#39;: &#39;{0}EDiscoveryClients/Security?appType=%s&#39;,
    &#39;EDISCOVERY_DATA_SOURCES&#39;: &#39;{0}V2/EDiscoveryClients/Datasources?datasourceId=%s&amp;type=%s&#39;,
    &#39;EDISCOVERY_DATA_SOURCE_DELETE&#39;: &#39;{0}EDiscoveryClients/Datasources?datasourceId=%s&amp;clientId=%s&#39;,
    &#39;EDISCOVERY_DYNAMIC_FEDERATED&#39;: &#39;{0}dcube/dynamicfederated/%s/%s/default&#39;,
    &#39;EDISCOVERY_EXPORT&#39;: &#39;{0}dcube/export/%s&#39;,
    &#39;EDISCOVERY_EXPORT_STATUS&#39;: &#39;{0}dcube/export/%s/status?token=%s&#39;,
    &#39;EDISCOVERY_CREATE_DATA_SOURCE&#39;: &#39;{0}EDiscoveryClients/Datasources&#39;,
    &#39;EDISCOVERY_REVIEW_ACTIONS_WITH_REQUEST&#39;: &#39;{0}EDiscoveryClients/Datasources/Actions/Requests&#39;,
    &#39;EDISCOVERY_REVIEW_ACTIONS&#39;: &#39;{0}V2/EDiscoveryClients/Datasources/Actions&#39;,
    &#39;EDISCOVERY_CLIENTS&#39;: &#39;{0}EDiscoveryClients?eDiscoverySubtype=%s&#39;,
    &#39;EDISCOVERY_CLIENT_DETAILS&#39;: &#39;{0}EDiscoveryClients/%s&#39;,
    &#39;EDISCOVERY_DATA_SOURCE_STATS&#39;: &#39;{0}EDiscoveryClients/Datasources?id=%s&amp;type=%s&amp;start=0&amp;count=1000&#39;,
    &#39;EDISCOVERY_CREATE_CLIENT&#39;: &#39;{0}EDiscoveryClients/Clients&#39;,
    &#39;EDISCOVERY_DELETE_CLIENT&#39;: &#39;{0}EDiscoveryClients/Clients/%s&#39;,
    &#39;EDISCOVERY_REQUESTS&#39;: &#39;{0}V4/RequestManager/Request&#39;,
    &#39;EDISCOVERY_REQUEST_DETAILS&#39;: &#39;{0}V4/RequestManager/Request/%s&#39;,
    &#39;EDISCOVERY_REQUEST_CONFIGURE&#39;: &#39;{0}V4/RequestManager/Request/%s/Configure&#39;,
    &#39;EDISCOVERY_REQUEST_PROJECTS&#39;: &#39;{0}V4/RequestManager/Request/%s/Projects&#39;,
    &#39;EDISCOVERY_REQUEST_FEDERATED&#39;: &#39;{0}dcube/federated/%s/%s&#39;,
    &#39;EDISCOVERY_REQUEST_DOCUMENT_MARKER&#39;: &#39;{0}EDiscoveryClients/Tasks/%s/Documents&#39;,
    &#39;EDISCOVERY_CONFIGURE_TASK&#39;: &#39;{0}EDiscoveryClient/ConfigureTask&#39;,
    &#39;EDICOVERY_TASK_WORKFLOW&#39;: &#39;{0}EDiscoveryClients/Tasks/%s/Workflows&#39;,
    &#39;GET_RESOURCE_POOLS&#39;: &#39;{0}V4/ResourcePool&#39;,
    &#39;GET_RESOURCE_POOL_DETAILS&#39;: &#39;{0}ResourcePool/%s&#39;,
    &#39;DELETE_RESOURCE_POOL&#39;: &#39;{0}ResourcePool/%s&#39;,
    &#39;CREATE_RESOURCE_POOL&#39;: &#39;{0}ResourcePool&#39;,

    &#39;GLOBAL_FILTER&#39;: &#39;{0}GlobalFilter&#39;,
    &#39;RESTORE_OPTIONS&#39;: &#39;{0}Restore/GetDestinationsToRestore?clientId=0&amp;appId=%s&amp;flag=8&#39;,

    &#39;UPLOAD_FULL_FILE&#39;: &#39;{0}Client/%s/file/action/upload?uploadType=fullFile&#39;,
    &#39;UPLOAD_CHUNKED_FILE&#39;: &#39;{0}Client/%s/file/action/upload?uploadType=chunkedFile&#39;,
    &#39;PLANS&#39;: &#39;{0}V2/Plan&#39;,
    &#39;PLAN&#39;: &#39;{0}V2/Plan/%s&#39;,
    &#39;PLAN_SUMMARY&#39;: &#39;{0}v4/Plan/Summary?%s&#39;,
    &#39;DELETE_PLAN&#39;: &#39;{0}V2/Plan/%s?confirmDelete=True&#39;,
    &#39;ADD_USERS_TO_PLAN&#39;: &#39;{0}V2/Plan/%s/Users&#39;,
    &#39;GET_PLAN_TEMPLATE&#39;: &#39;{0}V2/Plan/template?type=%s&amp;subType=%s&#39;,
    &#39;ELIGIBLE_PLANS&#39;: &#39;{0}V2/Plan/Eligible?%s&#39;,
    &#39;ASSOCIATED_ENTITIES&#39;: &#39;{0}V2/Plan/%s/AssociatedEntities&#39;,
    &#39;GET_PLANS&#39;: &#39;{0}V2/Plan?type=%s&amp;subType=%s&#39;,
    &#39;APPLICABLE_SOLNS_ENABLE&#39;: &#39;{0}V4/ServerPlan/%s/ApplicableSolutions/Restrict/Enable&#39;,
    &#39;APPLICABLE_SOLNS_DISABLE&#39;: &#39;{0}V4/ServerPlan/%s/ApplicableSolutions/Restrict/Disable&#39;,
    &#39;PLAN_SUPPORTED_SOLUTIONS&#39;: &#39;{0}V4/Solutions?filter=PLAN_SUPPORTED_SOLUTIONS&#39;,
    &#39;V4_SERVER_PLAN&#39;: &#39;{0}V4/ServerPlan/%s&#39;,
    &#39;V4_SERVER_PLANS&#39;: &#39;{0}V4/ServerPlan&#39;,
    &#39;V4_GLOBAL_SERVER_PLANS&#39;: &#39;{0}/V4/Global/ServerPlan&#39;,
    &#39;V4_SERVER_PLAN_BACKUP_DESTINATION&#39;: &#39;{0}V4/ServerPlan/%s/BackupDestination&#39;,
    &#39;V4_SERVER_PLAN_COPY&#39;: &#39;{0}V4/ServerPlan/%s/BackupDestination/%s&#39;,
    &#39;V4_DC_PLANS&#39;: &#39;{0}V4/DCPlan&#39;,
    &#39;V4_DC_PLAN&#39;: &#39;{0}V4/DCPlan/%s&#39;,
    &#39;V5_SERVER_PLAN_COPY&#39;: &#39;{0}V5/ServerPlan/%s/BackupDestination/%s&#39;,
    &#39;SERVER_PLAN_REGIONS&#39;: &#39;{0}V4/ServerPlan/%s/storageRegion/%s?isRegionIdList=true&#39;,
    &#39;SERVER_PLAN_RPO&#39;: &#39;{0}V4/ServerPlan/%s/RPO&#39;,

    &#39;DOMAIN_CONTROLER&#39;: &#39;{0}CommCell/DomainController&#39;,
    &#39;DELETE_DOMAIN_CONTROLER&#39;: &#39;{0}CommCell/DomainController/%s&#39;,
    &#39;DOMAIN_PROPERTIES&#39;: &#39;{0}CommCell/DomainController?domainId=%s&#39;,

    &#39;DRBACKUP&#39;: &#39;{0}/CommServ/DRBackup&#39;,
    &#39;DRBACKUP_REGIONS&#39;: &#39;{0}/V4/DRBackup/Regions&#39;,
    &#39;DISASTER_RECOVERY_PROPERTIES&#39;: &#39;{0}/Commcell/DRBackup/Properties&#39;,
    &#39;DISASTER_RECOVERY_OPTIONS&#39;: &#39;{0}/Commcell/DRBackup/Options&#39;,
    &#39;CVDRBACKUP_STATUS&#39;: &#39;{0}/cvdrbackup/status?commcellid=%s&#39;,
    &#39;CVDRBACKUP_INFO&#39;: &#39;{0}/cvdrbackup/info&#39;,
    &#39;CVDRBACKUP_DOWNLOAD&#39;: &#39;{0}/cvdrbackup/download&#39;,
    &#39;CVDRBACKUP_REQUEST&#39;: &#39;{0}/cvdrbackup/requests&#39;,
    &#39;CVDRBACKUP_REQUEST_HISTORY&#39;: &#39;{0}/cr/reportsplusengine/datasets/%s/data/?parameter.duration=%s&#39;,

    &#39;ORACLE_INSTANCE_BROWSE&#39;: &#39;{0}Instance/DBBrowse/%s&#39;,

    &#39;METRICS&#39;: &#39;{0}CommServ/MetricsReporting&#39;,
    &#39;GET_METRICS&#39;: &#39;{0}CommServ/MetricsReporting?isPrivateCloud=%s&#39;,
    &#39;LOCAL_METRICS&#39;: &#39;{0}CommServ/MetricsReporting?isLocalMetrics=%s&#39;,

    &#39;INTERNET_PROXY&#39;: &#39;{0}/Commcell/InternetOptions/Proxy&#39;,

    &#39;PASSWORD_ENCRYPTION_CONFIG&#39;: &#39;{0}/Commcell/PasswordEncryptionConfig&#39;,

    &#39;VM_ALLOCATION_POLICY&#39;: &#39;{0}VMAllocationPolicy&#39;,
    &#39;ALL_VM_ALLOCATION_POLICY&#39;: &#39;{0}VMAllocationPolicy?showResourceGroupPolicy=true&amp;deep=false&amp;hiddenpolicies=true&#39;,
    &#39;GET_VM_ALLOCATION_POLICY&#39;: &#39;{0}VMAllocationPolicy/%s&#39;,
    &#39;PROTECTED_VMS&#39;: &#34;{0}VM?propertyLevel=AllProperties&amp;status=1&amp;fromTime=%s&amp;toTime=%s&amp;Limit=%s&#34;,
    &#39;CONTINUOUS_REPLICATION_MONITOR&#39;: &#34;{0}Replications/Monitors/continuous&#34;,
    &#39;USERS&#39;: &#39;{0}User&#39;,
    &#39;V4_USERS&#39;: &#39;{0}v4/user?additionalProperties=true&#39;,
    &#39;USER&#39;: &#39;{0}User/%s?Level=50&#39;,
    &#39;DELETE_USER&#39;: &#39;{0}User/%s?newUserId=%s&amp;newUserGroupId=%s&#39;,
    &#39;OTP&#39;: &#39;{0}User/%s/preferences/OTP&#39;,

    &#39;UNLOCK&#39;: &#39;{0}User/Unlock&#39;,

    &#39;ROLES&#39;: &#39;{0}Role&#39;,
    &#39;ROLE&#39;: &#39;{0}Role/%s&#39;,

    &#39;ALL_CREDENTIALS&#39;: &#39;{0}/CommCell/Credentials?propertyLevel=30&#39;,
    &#39;ONE_CREDENTIAL&#39;: &#39;{0}/CommCell/Credentials/%s?propertyLevel=30&#39;,
    &#39;CREDENTIAL&#39;:   &#39;{0}/Commcell/Credentials&#39;,
    &#39;DELETE_RECORD&#39;: &#39;{0}/Commcell/Credentials/action/delete&#39;,

    &#39;ADD_CREDENTIALS&#39;: &#39;{0}V4/Credential&#39;,

    &#39;GET_SECURITY_ROLES&#39;: &#39;{0}Security/Roles&#39;,
    &#39;SECURITY_ASSOCIATION&#39;: &#39;{0}Security&#39;,
    &#39;ENTITY_SECURITY_ASSOCIATION&#39;: &#39;{0}Security/%s/%s&#39;,
    &#39;GET_DATASTORE_BROWSE&#39;: &#39;{0}VSBrowse/%s/%s?requestType=%s&#39;,

    &#39;GET_DC_DATA&#39;: &#39;{0}getDownloadCenterLookupData&#39;,
    &#39;DC_ENTITY&#39;: &#39;{0}saveDownloadCenterLookupEntities&#39;,
    &#39;DC_SUB_CATEGORY&#39;: &#39;{0}saveDownloadCenterSubCategory&#39;,
    &#39;SEARCH_PACKAGES&#39;: &#39;{0}searchPackages?release=11&#39;,
    &#39;DOWNLOAD_PACKAGE&#39;: &#39;{0}DownloadFile&#39;,
    &#39;DOWNLOAD_VIA_STREAM&#39;: &#39;{0}Stream/getDownloadCenterFileStream&#39;,
    &#39;UPLOAD_PACKAGE&#39;: &#39;{0}saveDownloadCenterPackage&#39;,
    &#39;DELETE_PACKAGE&#39;: &#39;{0}deleteDownloadCenterPackage?packageId=%s&#39;,

    &#39;ORGANIZATIONS&#39;: &#39;{0}Organization&#39;,
    &#39;ORGANIZATION&#39;: &#39;{0}Organization/%s&#39;,
    &#39;UPDATE_ORGANIZATION&#39;: &#39;{0}Organization?organizationId=%s&#39;,
    &#39;GENERATE_AUTH_CODE&#39;: &#39;{0}Organization/%s/Authtoken&#39;,
    &#39;ACTIVATE_ORGANIZATION&#39;: &#39;{0}Organization/%s/action/activate&#39;,
    &#39;DEACTIVATE_ORGANIZATION&#39;: &#39;{0}Organization/%s/action/deactivate&#39;,
    &#39;ORGANIZATION_ASSOCIATION&#39;: &#39;{0}company/%s/company-association&#39;,
    &#39;ENABLE_PRIVACY_COMPANY_DATA&#39;: &#39;{0}V2/Organization/%s/Privacy/Action/Lock&#39;,
    &#39;DISABLE_PRIVACY_COMPANY_DATA&#39;: &#39;{0}V2/Organization/%s/Privacy/Action/Unlock&#39;,
    &#39;ORGANIZATION_THEME&#39;: &#39;{0}V2/Organization/%s/Customization&#39;,
    &#39;GET_ORGANIZATION_THEME&#39;: &#39;{0}Organization/%s/Customization&#39;,
    &#39;EXTEND_ORGANIZATION&#39;: &#39;{0}ThirdParty/App/Company/Extend&#39;,
    &#39;ORGANIZATION_TAGS&#39;: &#39;{0}Tags&#39;,
    &#39;GET_ORGANIZATION_TAGS&#39;: &#39;{0}Tags/PROVIDER_ENTITY/%s&#39;,
    &#39;COMPANY_PASSKEY&#39;: &#39;{0}Company/%s/Passkey&#39;,
    &#39;COMPANY_AUTH_RESTORE&#39;: &#39;{0}Company/%s/AuthRestore&#39;,
    &#39;EDIT_COMPANY_DETAILS&#39;: &#39;{0}v4/company/%s&#39;,
    &#39;CHECK_ELIGIBILITY_MIGRATION&#39;: &#39;{0}Company/%s/migration-entities&#39;,
    &#39;COMPANY_ENTITIES&#39;: &#39;{0}Company/%s/AssociatedEntities&#39;,
    &#39;MIGRATE_CLIENTS&#39;: &#39;{0}Company/%s/company-association&#39;,
    &#39;COMPANY_OPERATORS&#39;: &#39;{0}V4/Company/Operator&#39;,
    &#34;ORGANIZATION_ADDITIONAL_SETTINGS&#34;: &#39;{0}v4/workload/AdditionalSettings&#39;,

    &#39;STORAGE_POOL&#39;: &#39;{0}StoragePool&#39;,
    &#39;GET_STORAGE_POOL&#39;: &#39;{0}StoragePool/%s&#39;,
    &#39;ADD_STORAGE_POOL&#39;: &#39;{0}StoragePool?Action=create&#39;,
    &#39;DELETE_STORAGE_POOL&#39;: &#39;{0}StoragePool/%s&#39;,
    &#39;EDIT_STORAGE_POOL&#39;: &#39;{0}StoragePool?Action=edit&#39;,
    &#39;REPLACE_DISK_STORAGE_POOL&#39;: &#39;{0}StoragePool?action=diskOperation&#39;,
    &#39;GET_METALLIC_STORAGE_DETAILS&#39;: &#39;{0}metallic/storage&#39;,

    &#39;KEY_MANAGEMENT_SERVER_ADD_GET&#39;: &#39;{0}CommCell/KeyManagementServers&#39;,
    &#39;KEY_MANAGEMENT_SERVER_DELETE&#39;: &#39;{0}CommCell/KeyManagementServers/%s&#39;,

    &#39;GET_ALL_MONITORING_POLICIES&#39;: &#39;{0}logmonitoring/monitoringpolicy&#39;,
    &#39;GET_ALL_ANALYTICS_SERVERS&#39;: &#39;{0}AnalyticsServers&#39;,
    &#39;GET_ALL_TEMPLATES&#39;: &#39;{0}logmonitoring/search/getListOfTemplate&#39;,
    &#39;CREATE_DELETE_EDIT_OPERATIONS&#39;: &#39;{0}logmonitoring/policy/operations&#39;,
    &#39;GET_MONITORING_POLICY_PROP&#39;: &#39;{0}logmonitoring/Policy/%s&#39;,
    &#39;RUN_MONITORING_POLICY&#39;: &#39;{0}logmonitoring/Policy/%s/Action/Run&#39;,

    &#39;LICENSE&#39;: &#39;{0}CommcellRegistrationInformation&#39;,

    &#39;REPLICATION_GROUPS&#39;: &#39;{0}ReplicationGroups&#39;,
    &#39;DELETE_REPLICATION_GROUPS&#39;: &#39;{0}ReplicationGroups/Action/Delete&#39;,
    &#39;REPLICATION_GROUP_DETAILS&#39;: &#39;{0}Vsa/ReplicationGroup/%s&#39;,

    &#39;DR_GROUPS&#39;: &#39;{0}DRGroups&#39;,
    &#39;GET_DR_GROUP&#39;: &#39;{0}DRGroups/%s&#39;,
    &#39;DR_GROUP_MACHINES&#39;: &#39;{0}DRGroups/ClientList?source=1&amp;entityType=3&amp;entityId=%s&#39;,
    &#39;DR_GROUP_JOB_STATS&#39;: &#39;{0}DRGroups/JobStats?jobId=%s&amp;drGroupId=%s&amp;replicationId=%s&amp;clientId=0&#39;,
    &#39;DR_JOB_STATS&#39;: &#39;{0}DRGroups/JobStats?jobId=%s&#39;,

    &#39;FAILOVER_GROUPS&#39;: &#39;{0}FailoverGroups&#39;,
    &#39;GET_FAILOVER_GROUP&#39;: &#39;{0}FailoverGroups/%s&#39;,
    &#39;FAILOVER_GROUP_MACHINES&#39;: &#39;{0}FailoverGroups/ClientList?source=1&amp;entityType=3&amp;entityId=%s&#39;,
    &#39;FAILOVER_GROUP_JOB_STATS&#39;: &#39;{0}DR/JobStats?jobId=%s&amp;failoverGroupId=%s&amp;replicationId=%s&amp;clientId=0&#39;,
    &#39;DRORCHESTRATION_JOB_STATS&#39;: &#39;{0}DR/JobStats?jobId=%s&#39;,

    &#39;REVERSE_REPLICATION_TASK&#39;: &#39;{0}Replications/Monitors/streaming/Operation&#39;,
    &#39;REPLICATION_MONITOR&#39;: &#39;{0}Replications/Monitors/streaming?subclientId=0&#39;,
    &#39;RPSTORE&#39;: &#39;{0}Replications/RPStore&#39;,

    &#39;CREATE_PSEUDO_CLIENT&#39;: &#39;{0}pseudoClient&#39;,
    &#39;CREATE_YUGABYTE_CLIENT&#39;: &#39;{0}Client/YugabyteDB&#39;,
    &#39;CREATE_COUCHBASE_CLIENT&#39;: &#39;{0}Client/Couchbase&#39;,
    &#39;CREATE_NAS_CLIENT&#39;: &#39;{0}NASClient&#39;,
    &#39;GET_OFFICE_365_ENTITIES&#39;: &#39;{0}Office365/entities&#39;,
    &#39;GET_DYNAMICS_365_CLIENTS&#39;: &#39;{0}Office365/entities?agentType=5&#39;,
    &#39;GET_SALESFORCE_CLIENTS&#39;: &#39;{0}Salesforce/Organization&#39;,
    &#39;OFFICE365_OVERVIEW_STATS&#39;: &#39;{0}Office365/overview/%s?mode=0&#39;,
    &#39;OFFICE365_POPULATE_INDEX_STATS&#39;: &#39;{0}Office365/PopulateIdxStats&#39;,
    &#39;CLOUD_DISCOVERY&#39;: &#39;{0}Instance/%s/CloudDiscovery?clientId=%s&amp;appType=%s&#39;,
    &#39;GOOGLE_DISCOVERY_OVERVIEW&#39;: &#39;{0}Office365/overview/%s?mode=2&#39;,
    &#39;SET_USER_POLICY_ASSOCIATION&#39;: &#39;{0}Office365/CloudApps/SetUserPolicyAssociation&#39;,
    &#39;CUSTOM_CATEGORY&#39;: &#39;{0}Office365/SubClient/%s/CustomCategory&#39;,
    &#39;CUSTOM_CATEGORIES&#39;: &#39;{0}Office365/SubClient/%s/CustomCategories&#39;,
    &#39;INSTANCE_PROPERTIES&#39;: &#39;{0}instance/%s&#39;,
    &#39;USER_POLICY_ASSOCIATION&#39;: &#39;{0}Office365/CloudApps/UserPolicyAssociation&#39;,
    &#39;UPDATE_USER_POLICY_ASSOCIATION&#39;: &#39;{0}Office365/CloudApps/UpdateUserPolicyAssociation&#39;,
    &#39;GDRIVE_UPDATE_USERS&#39;: &#39;{0}GoogleWorkspace/GDrive/UpdateUsers&#39;,
    &#39;GMAIL_UPDATE_USERS&#39;: &#39;{0}GoogleWorkspace/GMail/UpdateUsers&#39;,
    &#39;GDRIVE_GET_USERS&#39;: &#39;{0}GoogleWorkspace/GDrive/GetUsers&#39;,
    &#39;GMAIL_GET_USERS&#39;: &#39;{0}GoogleWorkspace/GMail/GetUsers&#39;,
    &#39;OFFICE365_MOVE_JOB_RESULT_DIRECTORY&#39;: &#39;{0}Office365/MoveJobResultsDirectory&#39;,
    &#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;: &#39;{0}Office365/ProcessIdxRetentionRules&#39;,
    &#39;OFFICE365_POPULATE_INDEX_STATS&#39;: &#39;{0}Office365/PopulateIdxStats&#39;,
    &#39;OFFICE365_OVERVIEW_STATS&#39;: &#39;{0}Office365/overview/%s?mode=0&#39;,
    &#39;ADD_EXCHANGE&#39;: &#39;{0}pseudoClient&#39;,
    &#39;CREATE_CONFIGURATION_POLICIES&#39;: &#39;{0}ConfigurationPolicies&#39;,
    &#39;GET_CONFIGURATION_POLICIES&#39;: &#39;{0}ConfigurationPolicies?policyType=email&#39;,
    &#39;GET_CONFIGURATION_POLICIES_FS&#39;: &#39;{0}ConfigurationPolicies?policyType=filesytem&#39;,
    &#39;GET_CONFIGURATION_POLICY&#39;: &#39;{0}ConfigurationPolicies/%s&#39;,
    &#39;DELETE_CONFIGURATION_POLICY&#39;: &#39;{0}ConfigurationPolicies/%s&#39;,
    &#39;EMAIL_DISCOVERY&#39;: &#39;{0}Backupset/%s/mailboxDiscover?discoveryType=%s&amp;pageSize=-1&#39;,
    &#39;EMAIL_DISCOVERY_WITHOUT_REFRESH&#39;: &#39;{0}Backupset/%s/mailboxDiscover?discoveryType=%s&amp;refreshMailboxDb=false&amp;pageSize=-1&#39;,
    &#39;GET_EMAIL_POLICY_ASSOCIATIONS&#39;: &#39;{0}Subclient/%s/EmailPolicyAssociation?discoveryType=%s&#39;,
    &#39;SET_EMAIL_POLICY_ASSOCIATIONS&#39;: &#39;{0}/Subclient/EmailPolicyAssociation&#39;,
    &#39;DELETE_DOCUMENTS&#39;: &#39;{0}/DeleteDocuments&#39;,

    &#39;CREATE_NUTANIX_CLIENT&#39;: &#39;{0}Client/Nutanix&#39;,

    &#39;GET_EVENTS&#39;: &#39;{0}Events&#39;,
    &#39;GET_EVENT&#39;: &#39;{0}Events/%s&#39;,

    &#39;GET_ACTIVITY_CONTROL&#39;: &#39;{0}V4/CommCell/ActivityControl&#39;,
    &#39;SET_ACTIVITY_CONTROL&#39;: &#39;{0}CommCell/ActivityControl/%s/Action/%s&#39;,
    &#39;SET_COMMCELL_PROPERTIES&#39;: &#39;{0}Commcell/properties&#39;,

    &#39;OPERATION_WINDOW&#39;: &#39;{0}OperationWindow&#39;,
    &#39;LIST_OPERATION_WINDOW&#39;: &#39;{0}/OperationWindow/OpWindowList?clientId=%s&#39;,

    &#39;RELEASE_LICENSE&#39;: &#39;{0}Client/License/Release&#39;,
    &#39;RECONFIGURE_LICENSE&#39;: &#39;{0}Client/License/Reconfigure&#39;,
    &#39;LIST_LICENSES&#39;: &#39;{0}Client/%s/License&#39;,
    &#39;APPLY_LICENSE&#39;: &#39;{0}License&#39;,
    &#39;CAPACITY_LICENSE&#39;: &#39;{0}cr/reportsplusengine/datasets/d7faef75-cf66-40a2-98ce-a2d0cc2a144b:feabb5ca-b6b7-4572-b0cb-39352c7e1b67/data&#39;,
    &#39;OI_LICENSE&#39;: &#39;{0}cr/reportsplusengine/datasets/d7faef75-cf66-40a2-98ce-a2d0cc2a144b:cd38c52a-e099-4252-d36f-3e2c54540f6f/data&#39;,
    &#39;VIRTUALIZATION_LICENSE&#39;: &#39;{0}cr/reportsplusengine/datasets/d7faef75-cf66-40a2-98ce-a2d0cc2a144b:0aac5b36-10a4-4970-838a-c41fa2365583/data&#39;,
    &#39;USER_LICENSE&#39;: &#39;{0}cr/reportsplusengine/datasets/d7faef75-cf66-40a2-98ce-a2d0cc2a144b:44cd7de8-ecb2-4ec8-8b2b-162491172eef/data&#39;,
    &#39;ACTIVATE_LICENSE&#39;: &#39;{0}cr/reportsplusengine/datasets/d7faef75-cf66-40a2-98ce-a2d0cc2a144b:f7c6b473-f99d-44b4-ff5e-466b55656500/data&#39;,
    &#39;METALLIC_LICENSE&#39;: &#39;{0}cr/reportsplusengine/datasets/d7faef75-cf66-40a2-98ce-a2d0cc2a144b:cc2e77ec-9315-4446-cd7e-44ef80a8860e/data&#39;,
    &#39;OTHER_LICENSE&#39;: &#39;{0}cr/reportsplusengine/datasets/d7faef75-cf66-40a2-98ce-a2d0cc2a144b:2654b01f-9bb0-481e-b273-4b4fddc585b1/data&#39;,
    &#39;GET_CLOUDAPPS_USERS&#39;: &#39;{0}Instance/%s/CloudDiscovery?clientId=%s&amp;eDiscoverType=%s&#39;,
    &#39;GET_CLOUDAPPS_ONEDRIVE_USERS&#39;: &#39;{0}Instance/%s/CloudDiscovery?clientId=%s&amp;eDiscoverType=%s&amp;subclientId=%s&#39;,
    &#39;ENABLE_CLIENT_PRIVACY&#39;: &#39;{0}/V3/Client/%s/Lock&#39;,
    &#39;DISABLE_CLIENT_PRIVACY&#39;: &#39;{0}/V3/Client/%s/Unlock&#39;,

    &#39;IDENTITY_APPS&#39;: &#39;{0}ThirdParty/App&#39;,

    &#39;SET_GLOBAL_PARAM&#39;: &#39;{0}/setGlobalParam&#39;,
    &#39;GET_GLOBAL_PARAM&#39;: &#39;{0}/CommServ/GlobalParams&#39;,

    &#39;SNAP_OPERATIONS&#39;: &#39;{0}/Snaps/Operations&#39;,
    &#39;STORAGE_ARRAYS&#39;: &#39;{0}/StorageArrays&#39;,

    &#39;GET_NETWORK_SUMMARY&#39;: &#39;{0}/FirewallSummary/%s&#39;,
    &#39;NETWORK_TOPOLOGIES&#39;: &#39;{0}FirewallTopology&#39;,
    &#39;NETWORK_TOPOLOGY&#39;: &#39;{0}FirewallTopology/%s&#39;,
    &#39;PUSH_TOPOLOGY&#39;: &#39;{0}FirewallTopology/%s/Push&#39;,

    &#39;ADVANCED_JOB_DETAIL_TYPE&#39;: &#39;{0}Job/%s/AdvancedDetails?infoType=%s&#39;,

    &#39;CERTIFICATES&#39;: &#39;{0}CommServ/Certificates&#39;,

    # only for Exchange DAG
    &#39;GET_DAG_MEMBER_SERVERS&#39;: &#39;{0}Exchange/DAG/%s/PseudoClientInfo&#39;,
    &#39;GET_RECOVERY_POINTS&#39;: &#39;{0}Exchange/DAG/%s/RecoveryPoints?instanceId=%s&amp;backupSetId=%s&amp;subClientId=%s&amp;appId=%s&#39;,

    &#39;CASEDEFINITION&#39;: &#39;{0}EDiscoveryClients/CaseDefinitions&#39;,

    &#39;REGISTRATION&#39;: &#39;{0}/RegFrgnCell&#39;,
    &#39;UNREGISTRATION&#39;: &#39;{0}/UnRegisterCommCell&#39;,
    &#39;SERVICE_REGISTER&#39;: &#39;{0}/ServiceCommcells/Register&#39;,
    &#39;SERVICE_PROPS&#39;: &#39;{0}/ServiceCommcell/Properties&#39;,
    &#39;GET_REGISTERED_COMMCELLS&#39;: &#39;{0}/CommCell/registered&#39;,
    &#39;GET_REGISTERED_ROUTER_COMMCELLS&#39;: &#39;{0}/ServiceCommcells&#39;,
    &#39;GET_USERSPACE_SERVICE&#39;: &#39;{0}/ServiceCommcell/UserSpace&#39;,
    &#39;POLL_USER_SERVICE&#39;: &#39;{0}/ServiceCommcell/IsUserPresent?userName=%s&#39;,
    &#39;POLL_MAIL_SERVICE&#39;: &#39;{0}/ServiceCommcell/IsUserPresent?email=%s&#39;,
    &#39;POLL_REQUEST_ROUTER&#39;: &#39;{0}/CommcellRedirect/RedirectListforUser?user=%s&amp;getDistinctSAMLAppType=true&#39;,
    &#39;MULTI_COMMCELL_SWITCHER&#39;: &#39;{0}/CommcellRedirect/MultiCommcell&#39;,
    &#39;MULTI_COMMCELL_DROP_DOWN&#39;: &#39;{0}/MultiCommcellsForUser&#39;,
    &#39;SERVICE_COMMCELL_ASSOC&#39;: &#39;{0}/Security/MultiCommcell&#39;,
    &#39;SYNC_SERVICE_COMMCELL&#39;: &#39;{0}/RouterCommcell/SyncUserSpace?commcellGUID=%s&#39;,

    &#39;DASHBOARD_ENVIRONMENT_TILE&#39;: &#39;{0}clients/count?type=fileserver,vm,laptop&#39;,
    &#39;DASHBOARD_NEEDS_ATTENTION_TILE&#39;: &#39;{0}CommServ/Anomaly/Entity/Count?anomalousEntityType=14&#39;,

    &#39;GET_ALL_LIVE_SYNC_PAIRS&#39;: &#39;{0}Replications/Monitors/streaming?subclientId=%s&#39;,
    &#39;GET_ALL_LIVE_SYNC_VM_PAIRS&#39;: &#39;{0}Replications/Monitors/streaming?subclientId=%s&amp;taskId=%s&#39;,
    &#39;GET_LIVE_SYNC_VM_PAIR&#39;: &#39;{0}Replications/Monitors/streaming?subclientId=%s&amp;replicationPairId=%s&#39;,
    &#39;GET_REPLICATION_PAIR&#39;: &#39;{0}Replications/Monitors/streaming?replicationPairId=%s&#39;,
    &#39;GET_REPLICATION_PAIRS&#39;: &#39;{0}Replications/Monitors/streaming?&#39;,

    &#39;BACKUP_NETWORK_PAIRS&#39;: &#39;{0}CommServ/DataInterfacePairs?ClientId=%s&#39;,
    &#39;BACKUP_NETWORK_PAIR&#39;: &#39;{0}CommServ/DataInterfacePairs&#39;,

    &#39;GET_ALL_RECOVERY_TARGETS&#39;: &#39;{0}V4/RecoveryTargets&#39;,
    &#39;GET_RECOVERY_TARGET&#39;: &#39;{0}V4/RecoveryTarget/%s&#39;,

    &#39;RETIRE&#39;: &#39;{0}Client/%s/Retire&#39;,
    &#39;GET_REMOTE_CACHE_CLIENTS&#39;: &#39;{0}RemoteCacheClients&#39;,

    &#39;DATASOURCE_ACTIONS&#39;: &#39;{0}EDiscoveryClients/Datasources/Actions&#39;,
    &#39;CLOUD_CREATE&#39;: &#39;{0}cloud/create&#39;,
    &#39;CLOUD_MODIFY&#39;: &#39;{0}cloud/modify&#39;,
    &#39;CLOUD_DELETE&#39;: &#39;{0}cloud/delete&#39;,
    &#39;CLOUD_ROLE_UPDATE&#39;: &#39;{0}cloud/role/update&#39;,
    &#39;CLOUD_NODE_UPDATE&#39;: &#39;{0}cloud/node/update&#39;,
    &#39;GET_ALL_INDEX_SERVERS&#39;: &#39;{0}dcube/getAnalyticsEngine?retrieveall=true&#39;,
    &#39;GET_ALL_ROLES&#39;: &#39;{0}IndexingGateway/GetAnalyticsRolesInfo&#39;,
    &#39;GET_THREAT_INDICATORS&#39;: &#39;{0}/Client/Anomaly&#39;,
    &#39;GET_ALL_CLIENT_ANOMALIES&#39;: &#39;{0}/Client/AnomalyRecord?filter=%s&amp;clients=%s&#39;,
    &#39;CLEAR_ANOMALIES&#39;: &#39;{0}/Client/PruneAnomalyRecord&#39;,
    &#39;RUN_ANOMALY_SCAN&#39;: &#39;{0}/EDiscoveryClients/OnDemandAnalytics&#39;,
    &#39;ANOMALY_CLIENTS_COUNT&#39;:&#39;{0}/clients/count?type=fileserver,vm,laptop&#39;,
    &#39;MONITORED_VM_COUNT&#39;:&#39;{0}/Client/Anomaly/MonitoredVMCount&#39;,
    &#39;GET_SWAGGER&#39;: &#39;{0}swagger/V3/swagger.json&#39;,

    &#39;COMMCELL_METADATA&#39;: &#39;{0}Commcell/MetaData&#39;,
    &#39;METALLIC_LINKING&#39;: &#39;{0}CloudService/Subscription&#39;,
    &#39;CV_METALLIC_LINKING&#39;: &#39;{0}/CloudService/Subscription/Details&#39;,
    &#39;METALLIC_COMPLETED_SETUPS&#39;: &#39;{0}CloudService/CompletedSetups&#39;,
    &#39;USER_MAPPINGS&#39;: &#39;{0}GetUserMappings&#39;,
    &#39;METALLIC_REGISTERED&#39;: &#39;{0}CloudServices/Registered&#39;,
    &#39;METALLIC_UNLINK&#39;: &#39;{0}CloudService/Unsubscribe&#39;,

    &#39;ADD_OR_GET_SAML&#39;: &#39;{0}/v4/SAML&#39;,
    &#39;EDIT_SAML&#39;: &#39;{0}/v4/SAML/%s&#39;,
    &#39;GET_SAML_PROP&#39;: &#39;{0}/ThirdParty/SAML/App?isOpenAPISpec=true&#39;,

    &#39;REGIONS&#39;: &#39;{0}/v4/Regions&#39;,
    &#39;REGION&#39;: &#39;{0}/v4/Regions/%s&#39;,
    &#39;EDIT_REGION&#39;: &#39;{0}/entity/%s/%s/region&#39;,
    &#39;GET_REGION&#39;: &#39;{0}/entity/%s/%s/region?entityRegionType=%s&#39;,
    &#39;CALCULATE_REGION&#39;: &#39;{0}/entity/%s/%s/region?calculate=True&amp;entityRegionType=%s&#39;,

    &#39;GET_OEM_ID&#39;: &#39;{0}/GetOemId&#39;,

    &#39;DO_WEB_SEARCH&#39;: &#39;{0}/Search&#39;,


    &#39;GET_SLA&#39;: &#39;{0}GetSLAConfiguration&#39;,
    &#39;WORKLOAD_REGION&#39;: &#39;{0}entity/COMMCELL/%s/region?entityRegionType=WORKLOAD&#39;,

    &#39;GET_USER_SUGGESTIONS&#39;: &#39;{0}getADUserSuggestions&#39;,
    &#39;DOMAIN_SSO&#39;: &#39;{0}V4/LDAP/%s&#39;,

    &#39;LAUNCH_O365_LICENSING&#39;: &#39;{0}Office365/License&#39;,

    &#39;VM_GROUP&#39;: &#39;{0}V4/VmGroup/%s&#39;,

    &#39;VSA_HIDDEN_SUBCLIENT&#39;: &#39;{0}GetId?clientname=%s&amp;agent=Virtual Server&amp;backupset=%s&amp;subclient=Do Not Backup&#39;,

    &#39;ALL_RECOVERY_GROUPS&#39;: &#39;{0}RecoveryGroups&#39;,
    &#39;RECOVERY_GROUP&#39;: &#39;{0}RecoveryGroup/%s?getEntityDetails=true&#39;,
    &#39;RECOVERY_GROUP_RECOVER&#39;: &#39;{0}RecoveryGroup/%s/Recover&#39;,
    &#39;CLEANUP_RECOVERY_GROUP&#39;: &#39;{0}RecoveryGroup/CleanupRecovery&#39;,


    &#39;ADCOMPAREID&#39;: &#39;{0}/ActiveDirectory/Subclient/%s/Comparison&#39;,

    &#39;ADCOMPARESTATUSCHECK&#39;: &#39;{0}/ActiveDirectory/Comparison/%s&#39;,
    &#39;ADCOMPAREVIEWRESULTS&#39;: &#39;{0}/ActiveDirectory/Compare&#39;,

    &#39;LIST_BACKUP_JOBS&#39;: &#39;{0}Jobs/Calendar?dateListResponse=false&amp;agedJobs=false&amp;subclientId=&amp;instanceId=&amp;backupsetId=%s&#39;
                        &#39;&amp;clientId=%s&amp;jobTypeList=Backup,SYNTHFULL&amp;fromStartTime=%s&amp;toStartTime=%s&amp;applicationIdList&#39;
                        &#39;=&amp;statusList=%s&amp;clientIdList=%s&amp;isArchiverClient=false&amp;lastBackup=false&#39;,

    &#39;VIEW_PROPERTIES&#39;: &#39;{0}/Recall?at=%s&amp;si=%s&amp;op=%s&amp;appId=%s&amp;ec=%s&#39;,




    &#39;COMMSERVE_RECOVERY&#39;: &#39;{0}cvdrbackup/csrecovery&#39;,
    &#39;GET_BACKUPSET_INFO&#39;: &#39;{0}cvdrbackup/info?csGuid=%s&#39;,
    &#39;GET_COMMSERVE_RECOVERY_LICENSE_DETAILS&#39;: &#39;{0}cvdrbackup/csrecovery/quota?csGuid=%s&#39;,
    &#39;GET_COMMSERVE_RECOVERY_RETENTION_DETAILS&#39;: &#39;{0}cvdrbackup/cleanup/lock?csGuid=%s&#39;,
    &#39;ADDASHBOARD&#39;: &#39;{0}/ActiveDirectory/Overview&#39;,
    &#39;ADDAZURECLIENT&#39;: &#39;{0}/v4/ActiveDirectory/AzureAD&#39;,
    &#39;ADCLIENTS&#39;: &#39;{0}/ActiveDirectory/Clients?apptypeId=0&#39;,
    &#39;ADAPPS&#39;: &#39;{0}/v4/ActiveDirectory/Apps&#39;,

    &#39;ENABLE_DATA_AGING&#39;: &#39;{0}/V5/ServerPlan/%s/BackupDestination/%s&#39;,


    &#39;LICENSE_COLLECTION&#39;: &#39;{0}Office365/License&#39;,

    &#39;NAVIGATION_SETTINGS&#39;: &#39;{0}/NavigationSettings&#39;,

    &#39;COCKROACHDB&#39;: &#39;{0}/Client/CockroachDB&#39;,

    &#39;ALL_RPStores&#39;: &#39;{0}Library?libraryType=RPSTORE&#39;,

    &#39;RESET_TENANT_PASSWORD&#39;: &#39;{0}/user/Password/Forgot&#39;,

    &#39;GET_DOC_PREVIEW&#39;: &#39;{0}/GetDocPreviewWithFields&#39;,

    &#39;GET_VARIOUS_PREVIEW&#39;:  &#39;{0}/GetVariousPreview&#39;,

    &#39;GET_PREVIEW&#39;: &#39;{0}/GetPreview&#39;,

    &#39;CREATE_ACCESS_TOKEN&#39;: &#39;{0}/V4/AccessToken&#39;,

    &#39;UPDATE_ACCESS_TOKEN&#39;: &#39;{0}/V4/AccessToken/%s&#39;,

    &#39;REVOKE_ACCESS_TOKEN&#39;: &#39;{0}/V4/AccessToken/%s&#39;,

    &#39;GET_ACCESS_TOKENS&#39;: &#39;{0}/V4/AccessToken/?userId=%s&#39;,

    &#39;RENEW_TOKEN&#39;: &#39;{0}/V4/AccessToken/Renew&#39;
}


def get_services(web_service):
    &#34;&#34;&#34;Initializes the SERVICES DICT with the web service for APIs.

        Args:
            web_service     (str)   --  web service string for APIs

        Returns:
            dict    -   services dict consisting of all APIs

    &#34;&#34;&#34;
    services_dict = SERVICES_DICT_TEMPLATE.copy()
    for service in services_dict:
        services_dict[service] = services_dict[service].format(web_service)

    return services_dict</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-functions">Functions</h2>
<dl>
<dt id="cvpysdk.services.get_services"><code class="name flex">
<span>def <span class="ident">get_services</span></span>(<span>web_service)</span>
</code></dt>
<dd>
<div class="desc"><p>Initializes the SERVICES DICT with the web service for APIs.</p>
<h2 id="args">Args</h2>
<p>web_service
(str)
&ndash;
web service string for APIs</p>
<h2 id="returns">Returns</h2>
<p>dict
-
services dict consisting of all APIs</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/services.py#L718-L732" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_services(web_service):
    &#34;&#34;&#34;Initializes the SERVICES DICT with the web service for APIs.

        Args:
            web_service     (str)   --  web service string for APIs

        Returns:
            dict    -   services dict consisting of all APIs

    &#34;&#34;&#34;
    services_dict = SERVICES_DICT_TEMPLATE.copy()
    for service in services_dict:
        services_dict[service] = services_dict[service].format(web_service)

    return services_dict</code></pre>
</details>
</dd>
</dl>
</section>
<section>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-functions">Functions</a></h3>
<ul class="">
<li><code><a title="cvpysdk.services.get_services" href="#cvpysdk.services.get_services">get_services</a></code></li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>