<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.drorchestration.replicationmonitor API documentation</title>
<meta name="description" content="Main file for performing failover specific operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.drorchestration.replicationmonitor</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing failover specific operations.</p>
<p>ReplicationMonitor: Class for representing all the dr orchestration operations
from Replication monitor</p>
<h2 id="replicationmonitor">Replicationmonitor</h2>
<p><strong>init</strong>(commcell_object,
replication_monitor_options)
&ndash; Initialise object of ReplicationMonitor</p>
<p><strong>repr</strong>()
&ndash; Return the ReplicationMonitor name</p>
<p>testboot()
&ndash; Call testboot operation</p>
<p>planned_failover()
&ndash; Call Planned failvoer operation</p>
<p>unplanned_failover()
&ndash; Call Unplanned Failover operation</p>
<p>failback()
&ndash; Call failback operation</p>
<p>undo_failover()
&ndash; Call UndoFailover operation</p>
<p>revert_failover()
&ndash; Call RevertFailover operation</p>
<p>point_in_time_failover()
&ndash; Call PointInTimeFailover operation</p>
<p>reverse_replication()
&ndash; Schedule and call ReverseReplication operation</p>
<p>schedule_reverse_replication()
&ndash; Schedule ReverseReplication</p>
<p>force_reverse_replication()
&ndash; Call ReverseReplication operation</p>
<p>validate_dr_orchestration_job(jobId)
&ndash; Validate DR orchestration job Id</p>
<p>refresh()
&ndash; Refresh the object properties</p>
<h5 id="internal-methods">internal methods</h5>
<p>_get_replication_monitor()
&ndash; Gets replication monitor</p>
<p>_get_snapshot_list()
&ndash; Gets snapshot list for the destination client</p>
<h5 id="properties">properties</h5>
<p>_replication_Ids()
&ndash; Returns replication Ids list</p>
<p>replication_monitor_options()
&ndash; Returns replication monitor options</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L1-L392" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing failover specific operations.

ReplicationMonitor: Class for representing all the dr orchestration operations
from Replication monitor


ReplicationMonitor:
    __init__(commcell_object,
            replication_monitor_options)            -- Initialise object of ReplicationMonitor

    __repr__()                                      -- Return the ReplicationMonitor name

    testboot()                                      -- Call testboot operation

    planned_failover()                              -- Call Planned failvoer operation

    unplanned_failover()                            -- Call Unplanned Failover operation

    failback()                                      -- Call failback operation

    undo_failover()                                 -- Call UndoFailover operation

    revert_failover()                               -- Call RevertFailover operation

    point_in_time_failover()                        -- Call PointInTimeFailover operation

    reverse_replication()                           -- Schedule and call ReverseReplication operation

    schedule_reverse_replication()                  -- Schedule ReverseReplication

    force_reverse_replication()                     -- Call ReverseReplication operation

    validate_dr_orchestration_job(jobId)            -- Validate DR orchestration job Id

    refresh()                                       -- Refresh the object properties

    ##### internal methods #####
    _get_replication_monitor()                      -- Gets replication monitor

    _get_snapshot_list()                            -- Gets snapshot list for the destination client

    ##### properties #####
    _replication_Ids()                              -- Returns replication Ids list

    replication_monitor_options()                   -- Returns replication monitor options


&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from ..exception import SDKException
from .drorchestrationoperations import DROrchestrationOperations


class ReplicationMonitor(object):
    &#34;&#34;&#34;Class for performing DR orchestration operations on ReplicationMonitor.&#34;&#34;&#34;

    def __init__(self, commcell_object, replication_monitor_options):
        &#34;&#34;&#34;Initialise the ReplicationMonitor object.

            Args:
                commcell_object (object)  --  instance of the Commcell class

                input dict of replication monitor options
                replication_monitor_options (json) -- replication monitor options
                {
                    &#34;VirtualizationClient&#34;: &#34;&#34;,
                    &#34;approvalRequired&#34;: False,
                    &#34;skipDisableNetworkAdapter&#34;: False
                    &#34;initiatedFromMonitor&#34;: True,
                    &#34;vmName&#34;: &#39;DRVM1&#39;
                }

            Returns:
                object - instance of the ReplicationMonitor class
        &#34;&#34;&#34;
        ##### local variables of these class ########
        self._commcell_object = commcell_object
        self._replication_monitor_options = replication_monitor_options
        self._services = commcell_object._services

        # create DROrchestrationOperations object
        self._dr_operation = DROrchestrationOperations(commcell_object)

        ##### REST API URLs #####
        self._REPLICATION_MONITOR = self._commcell_object._services[&#39;REPLICATION_MONITOR&#39;]

        # init local variables
        self._replicationId = None

        self.refresh()

        # set dr orchestration options property
        self._replication_monitor_options[&#39;replicationIds&#39;] = self._replication_Ids
        self._dr_operation.dr_orchestration_options = self.replication_monitor_options

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return &#39;ReplicationMonitor instance for Commcell&#39;

    @property
    def _replication_Ids(self):
        &#34;&#34;&#34; Returns replicationIds of the failover &#34;&#34;&#34;
        if not self._replicationId:

            vm_name = self.replication_monitor_options.get(&#34;vmName&#34;, &#34;&#34;)
            _rep_Ids = []

            if not vm_name:

                # get the first VM if input vm doesnt exist
                vm = self.replication_monitor[0]
                _rep_Ids.append(vm.get(&#39;replicationId&#39;, 0))

            else:
                # adds support to a list of VM names
                # for backward compatibility, converts a single string to a list
                if not isinstance(vm_name, list):
                    assert isinstance(vm_name, str)
                    vm_name = [vm_name]

                # makes the entire list lower case
                vm_name = list(map(lambda x : str(x).lower(), vm_name))

                # iterate through all the vms
                for _vm in self.replication_monitor:
                    if str(_vm.get(&#34;sourceName&#34;)).lower() in vm_name:
                        _rep_Ids.append(_vm.get(&#34;replicationId&#34;, 0))

            self._replicationId = _rep_Ids

        return self._replicationId

    @property
    def replication_monitor_options(self):
        &#34;&#34;&#34;Getter replication monitor&#34;&#34;&#34;
        return self._replication_monitor_options

    @property
    def replication_monitor(self):
        &#34;&#34;&#34;Getter replication monitor&#34;&#34;&#34;
        return self._replication_monitor

    def refresh(self):
        &#34;&#34;&#34;Refresh the replication monitor.
        Args:

        Returns:

        Raises:
        &#34;&#34;&#34;
        self._get_replication_monitor()

    def testboot(self):
        &#34;&#34;&#34;Performs testboot failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Testboot job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.testboot()

    def planned_failover(self):
        &#34;&#34;&#34;Performs Planned failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Planned Failover job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.planned_failover()

    def unplanned_failover(self):
        &#34;&#34;&#34;Performs UnPlanned failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Unplanned Failover job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.unplanned_failover()

    def failback(self):
        &#34;&#34;&#34;Performs Failback operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;

        return self._dr_operation.failback()

    def undo_failover(self):
        &#34;&#34;&#34;Performs Undo Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.undo_failover()

    def reverse_replication(self):
        &#34;&#34;&#34;Schedules and calls Reverse Replication

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.reverse_replication()

    def schedule_reverse_replication(self):
        &#34;&#34;&#34;Schedules Reverse Replication.

            Args:

            Returns:
                (TaskId) - TaskId of the scheduling reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.schedule_reverse_replication()

    def force_reverse_replication(self):
        &#34;&#34;&#34;Performs one reverse replication operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.force_reverse_replication()

    def revert_failover(self):
        &#34;&#34;&#34;Performs Revert Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.revert_failover()

    def point_in_time_failover(self):
        &#34;&#34;&#34;Performs Revert Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        snapshot_list = self._get_snapshot_list()

        if len(snapshot_list) == 0:
            raise SDKException(&#34;ReplicationMonitor&#34;, &#34;101&#34;,
                               &#34;No snapshot is found.&#34;)

        # fetch the first snapshot to run
        return self._dr_operation.point_in_time_failover(
            snapshot_list[0][&#34;timestamp&#34;],
            self._replication_monitor_options[&#39;replicationIds&#39;][0])

    def validate_dr_orchestration_job(self, jobId):
        &#34;&#34;&#34; Validates DR orchestration job of jobId
            Args:
                JobId: Job Id of the DR orchestration job

            Returns:
                bool - boolean that represents whether the DR orchestration job finished successfully or not

            Raises:
                SDKException:
                    if proper inputs are not provided
                    If failover phase failed at any stage
        &#34;&#34;&#34;
        return self._dr_operation.validate_dr_orchestration_job(jobId)


#################### private functions #####################

    def _get_replication_monitor(self):
        &#34;&#34;&#34; Gets replication monitor options
            Args:

            Returns: Gets the Replication monitor options dict

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._REPLICATION_MONITOR)

        if flag:
            if response.json():
                self._replication_monitor = response.json()[&#39;siteInfo&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_snapshot_list(self):
        &#34;&#34;&#34; Gets snapshot list for the destination client

            Args:

            Returns:
                list of dict: list of snapshot information
        &#34;&#34;&#34;
        vm_name = self.replication_monitor_options.get(&#34;vmName&#34;, &#34;&#34;)
        vm = None

        # parses vm information from the replication monitor
        if not vm_name:
            vm = self.replication_monitor[0]
        else:
            for _vm in self.replication_monitor:
                if str(_vm.get(&#34;sourceName&#34;)).lower() == str(vm_name).lower():
                    vm = _vm
                    break

        # we only need the information about destination guid
        destination_guid = vm[&#34;destinationGuid&#34;]
        instance_id = vm[&#34;parentSubclient&#34;][&#34;instanceId&#34;]

        return self._dr_operation.get_snapshot_list(destination_guid, instance_id)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor"><code class="flex name class">
<span>class <span class="ident">ReplicationMonitor</span></span>
<span>(</span><span>commcell_object, replication_monitor_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing DR orchestration operations on ReplicationMonitor.</p>
<p>Initialise the ReplicationMonitor object.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<p>input dict of replication monitor options
replication_monitor_options (json) &ndash; replication monitor options
{
"VirtualizationClient": "",
"approvalRequired": False,
"skipDisableNetworkAdapter": False
"initiatedFromMonitor": True,
"vmName": 'DRVM1'
}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the ReplicationMonitor class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L75-L392" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ReplicationMonitor(object):
    &#34;&#34;&#34;Class for performing DR orchestration operations on ReplicationMonitor.&#34;&#34;&#34;

    def __init__(self, commcell_object, replication_monitor_options):
        &#34;&#34;&#34;Initialise the ReplicationMonitor object.

            Args:
                commcell_object (object)  --  instance of the Commcell class

                input dict of replication monitor options
                replication_monitor_options (json) -- replication monitor options
                {
                    &#34;VirtualizationClient&#34;: &#34;&#34;,
                    &#34;approvalRequired&#34;: False,
                    &#34;skipDisableNetworkAdapter&#34;: False
                    &#34;initiatedFromMonitor&#34;: True,
                    &#34;vmName&#34;: &#39;DRVM1&#39;
                }

            Returns:
                object - instance of the ReplicationMonitor class
        &#34;&#34;&#34;
        ##### local variables of these class ########
        self._commcell_object = commcell_object
        self._replication_monitor_options = replication_monitor_options
        self._services = commcell_object._services

        # create DROrchestrationOperations object
        self._dr_operation = DROrchestrationOperations(commcell_object)

        ##### REST API URLs #####
        self._REPLICATION_MONITOR = self._commcell_object._services[&#39;REPLICATION_MONITOR&#39;]

        # init local variables
        self._replicationId = None

        self.refresh()

        # set dr orchestration options property
        self._replication_monitor_options[&#39;replicationIds&#39;] = self._replication_Ids
        self._dr_operation.dr_orchestration_options = self.replication_monitor_options

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return &#39;ReplicationMonitor instance for Commcell&#39;

    @property
    def _replication_Ids(self):
        &#34;&#34;&#34; Returns replicationIds of the failover &#34;&#34;&#34;
        if not self._replicationId:

            vm_name = self.replication_monitor_options.get(&#34;vmName&#34;, &#34;&#34;)
            _rep_Ids = []

            if not vm_name:

                # get the first VM if input vm doesnt exist
                vm = self.replication_monitor[0]
                _rep_Ids.append(vm.get(&#39;replicationId&#39;, 0))

            else:
                # adds support to a list of VM names
                # for backward compatibility, converts a single string to a list
                if not isinstance(vm_name, list):
                    assert isinstance(vm_name, str)
                    vm_name = [vm_name]

                # makes the entire list lower case
                vm_name = list(map(lambda x : str(x).lower(), vm_name))

                # iterate through all the vms
                for _vm in self.replication_monitor:
                    if str(_vm.get(&#34;sourceName&#34;)).lower() in vm_name:
                        _rep_Ids.append(_vm.get(&#34;replicationId&#34;, 0))

            self._replicationId = _rep_Ids

        return self._replicationId

    @property
    def replication_monitor_options(self):
        &#34;&#34;&#34;Getter replication monitor&#34;&#34;&#34;
        return self._replication_monitor_options

    @property
    def replication_monitor(self):
        &#34;&#34;&#34;Getter replication monitor&#34;&#34;&#34;
        return self._replication_monitor

    def refresh(self):
        &#34;&#34;&#34;Refresh the replication monitor.
        Args:

        Returns:

        Raises:
        &#34;&#34;&#34;
        self._get_replication_monitor()

    def testboot(self):
        &#34;&#34;&#34;Performs testboot failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Testboot job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.testboot()

    def planned_failover(self):
        &#34;&#34;&#34;Performs Planned failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Planned Failover job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.planned_failover()

    def unplanned_failover(self):
        &#34;&#34;&#34;Performs UnPlanned failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Unplanned Failover job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.unplanned_failover()

    def failback(self):
        &#34;&#34;&#34;Performs Failback operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;

        return self._dr_operation.failback()

    def undo_failover(self):
        &#34;&#34;&#34;Performs Undo Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.undo_failover()

    def reverse_replication(self):
        &#34;&#34;&#34;Schedules and calls Reverse Replication

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.reverse_replication()

    def schedule_reverse_replication(self):
        &#34;&#34;&#34;Schedules Reverse Replication.

            Args:

            Returns:
                (TaskId) - TaskId of the scheduling reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.schedule_reverse_replication()

    def force_reverse_replication(self):
        &#34;&#34;&#34;Performs one reverse replication operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.force_reverse_replication()

    def revert_failover(self):
        &#34;&#34;&#34;Performs Revert Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        return self._dr_operation.revert_failover()

    def point_in_time_failover(self):
        &#34;&#34;&#34;Performs Revert Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        snapshot_list = self._get_snapshot_list()

        if len(snapshot_list) == 0:
            raise SDKException(&#34;ReplicationMonitor&#34;, &#34;101&#34;,
                               &#34;No snapshot is found.&#34;)

        # fetch the first snapshot to run
        return self._dr_operation.point_in_time_failover(
            snapshot_list[0][&#34;timestamp&#34;],
            self._replication_monitor_options[&#39;replicationIds&#39;][0])

    def validate_dr_orchestration_job(self, jobId):
        &#34;&#34;&#34; Validates DR orchestration job of jobId
            Args:
                JobId: Job Id of the DR orchestration job

            Returns:
                bool - boolean that represents whether the DR orchestration job finished successfully or not

            Raises:
                SDKException:
                    if proper inputs are not provided
                    If failover phase failed at any stage
        &#34;&#34;&#34;
        return self._dr_operation.validate_dr_orchestration_job(jobId)


#################### private functions #####################

    def _get_replication_monitor(self):
        &#34;&#34;&#34; Gets replication monitor options
            Args:

            Returns: Gets the Replication monitor options dict

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._REPLICATION_MONITOR)

        if flag:
            if response.json():
                self._replication_monitor = response.json()[&#39;siteInfo&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_snapshot_list(self):
        &#34;&#34;&#34; Gets snapshot list for the destination client

            Args:

            Returns:
                list of dict: list of snapshot information
        &#34;&#34;&#34;
        vm_name = self.replication_monitor_options.get(&#34;vmName&#34;, &#34;&#34;)
        vm = None

        # parses vm information from the replication monitor
        if not vm_name:
            vm = self.replication_monitor[0]
        else:
            for _vm in self.replication_monitor:
                if str(_vm.get(&#34;sourceName&#34;)).lower() == str(vm_name).lower():
                    vm = _vm
                    break

        # we only need the information about destination guid
        destination_guid = vm[&#34;destinationGuid&#34;]
        instance_id = vm[&#34;parentSubclient&#34;][&#34;instanceId&#34;]

        return self._dr_operation.get_snapshot_list(destination_guid, instance_id)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.replication_monitor"><code class="name">var <span class="ident">replication_monitor</span></code></dt>
<dd>
<div class="desc"><p>Getter replication monitor</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L159-L162" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def replication_monitor(self):
    &#34;&#34;&#34;Getter replication monitor&#34;&#34;&#34;
    return self._replication_monitor</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.replication_monitor_options"><code class="name">var <span class="ident">replication_monitor_options</span></code></dt>
<dd>
<div class="desc"><p>Getter replication monitor</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L154-L157" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def replication_monitor_options(self):
    &#34;&#34;&#34;Getter replication monitor&#34;&#34;&#34;
    return self._replication_monitor_options</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.failback"><code class="name flex">
<span>def <span class="ident">failback</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Failback operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the failback job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L216-L229" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def failback(self):
    &#34;&#34;&#34;Performs Failback operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the failback job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;

    return self._dr_operation.failback()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.force_reverse_replication"><code class="name flex">
<span>def <span class="ident">force_reverse_replication</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs one reverse replication operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the reverse replication job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L273-L285" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def force_reverse_replication(self):
    &#34;&#34;&#34;Performs one reverse replication operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    return self._dr_operation.force_reverse_replication()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.planned_failover"><code class="name flex">
<span>def <span class="ident">planned_failover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Planned failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the Planned Failover job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L188-L200" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def planned_failover(self):
    &#34;&#34;&#34;Performs Planned failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the Planned Failover job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    return self._dr_operation.planned_failover()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.point_in_time_failover"><code class="name flex">
<span>def <span class="ident">point_in_time_failover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Revert Failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the failback job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L301-L322" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def point_in_time_failover(self):
    &#34;&#34;&#34;Performs Revert Failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the failback job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    snapshot_list = self._get_snapshot_list()

    if len(snapshot_list) == 0:
        raise SDKException(&#34;ReplicationMonitor&#34;, &#34;101&#34;,
                           &#34;No snapshot is found.&#34;)

    # fetch the first snapshot to run
    return self._dr_operation.point_in_time_failover(
        snapshot_list[0][&#34;timestamp&#34;],
        self._replication_monitor_options[&#39;replicationIds&#39;][0])</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the replication monitor.
Args:</p>
<p>Returns:</p>
<p>Raises:</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L164-L172" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the replication monitor.
    Args:

    Returns:

    Raises:
    &#34;&#34;&#34;
    self._get_replication_monitor()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.reverse_replication"><code class="name flex">
<span>def <span class="ident">reverse_replication</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Schedules and calls Reverse Replication</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the reverse replication job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L245-L257" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def reverse_replication(self):
    &#34;&#34;&#34;Schedules and calls Reverse Replication

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    return self._dr_operation.reverse_replication()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.revert_failover"><code class="name flex">
<span>def <span class="ident">revert_failover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Revert Failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the failback job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L287-L299" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def revert_failover(self):
    &#34;&#34;&#34;Performs Revert Failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the failback job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    return self._dr_operation.revert_failover()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.schedule_reverse_replication"><code class="name flex">
<span>def <span class="ident">schedule_reverse_replication</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Schedules Reverse Replication.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(TaskId) - TaskId of the scheduling reverse replication job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L259-L271" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def schedule_reverse_replication(self):
    &#34;&#34;&#34;Schedules Reverse Replication.

        Args:

        Returns:
            (TaskId) - TaskId of the scheduling reverse replication job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    return self._dr_operation.schedule_reverse_replication()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.testboot"><code class="name flex">
<span>def <span class="ident">testboot</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs testboot failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the Testboot job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L174-L186" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def testboot(self):
    &#34;&#34;&#34;Performs testboot failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the Testboot job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    return self._dr_operation.testboot()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.undo_failover"><code class="name flex">
<span>def <span class="ident">undo_failover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Undo Failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the failback job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L231-L243" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def undo_failover(self):
    &#34;&#34;&#34;Performs Undo Failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the failback job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    return self._dr_operation.undo_failover()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.unplanned_failover"><code class="name flex">
<span>def <span class="ident">unplanned_failover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs UnPlanned failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the Unplanned Failover job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L202-L214" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def unplanned_failover(self):
    &#34;&#34;&#34;Performs UnPlanned failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the Unplanned Failover job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    return self._dr_operation.unplanned_failover()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.validate_dr_orchestration_job"><code class="name flex">
<span>def <span class="ident">validate_dr_orchestration_job</span></span>(<span>self, jobId)</span>
</code></dt>
<dd>
<div class="desc"><p>Validates DR orchestration job of jobId</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>JobId</code></strong></dt>
<dd>Job Id of the DR orchestration job</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>bool - boolean that represents whether the DR orchestration job finished successfully or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided
If failover phase failed at any stage</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replicationmonitor.py#L324-L337" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def validate_dr_orchestration_job(self, jobId):
    &#34;&#34;&#34; Validates DR orchestration job of jobId
        Args:
            JobId: Job Id of the DR orchestration job

        Returns:
            bool - boolean that represents whether the DR orchestration job finished successfully or not

        Raises:
            SDKException:
                if proper inputs are not provided
                If failover phase failed at any stage
    &#34;&#34;&#34;
    return self._dr_operation.validate_dr_orchestration_job(jobId)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.drorchestration" href="index.html">cvpysdk.drorchestration</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor">ReplicationMonitor</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.failback" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.failback">failback</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.force_reverse_replication" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.force_reverse_replication">force_reverse_replication</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.planned_failover" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.planned_failover">planned_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.point_in_time_failover" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.point_in_time_failover">point_in_time_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.refresh" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.replication_monitor" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.replication_monitor">replication_monitor</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.replication_monitor_options" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.replication_monitor_options">replication_monitor_options</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.reverse_replication" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.reverse_replication">reverse_replication</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.revert_failover" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.revert_failover">revert_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.schedule_reverse_replication" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.schedule_reverse_replication">schedule_reverse_replication</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.testboot" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.testboot">testboot</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.undo_failover" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.undo_failover">undo_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.unplanned_failover" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.unplanned_failover">unplanned_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.validate_dr_orchestration_job" href="#cvpysdk.drorchestration.replicationmonitor.ReplicationMonitor.validate_dr_orchestration_job">validate_dr_orchestration_job</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>