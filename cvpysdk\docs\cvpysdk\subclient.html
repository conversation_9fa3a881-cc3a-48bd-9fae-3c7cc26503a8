<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclient API documentation</title>
<meta name="description" content="Main file for performing subclient operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclient</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing subclient operations.</p>
<p>Subclients and Subclient are 2 classes defined in this file.</p>
<p>Subclients: Class for representing all the subclients associated with a backupset / instance</p>
<p>Subclient: Base class consisting of all the common properties and operations for a Subclient</p>
<h1 id="subclients">Subclients:</h1>
<pre><code>__init__(class_object)      --  initialise object of subclients object associated with
the specified backup set/instance.

__str__()                   --  returns all the subclients associated with the backupset

__repr__()                  --  returns the string for the instance of the Subclients class

__len__()                   --  returns the number of subclients associated with the Agent
for the selected Client

__getitem__()               --  returns the name of the subclient for the given subclient Id
or the details for the given subclient name

_get_subclients()           --  gets all the subclients associated with the backupset specified

_process_add_request()      --  to post the add client request

default_subclient()         --  returns the name of the default subclient

all_subclients()            --  returns dict of all the subclients on commcell

has_subclient()             --  checks if a subclient exists with the given name or not

add()                       --  adds a new subclient to the backupset

add_oracle_logical_dump_subclient()  --  add subclient for oracle logical dump

add_postgresql_subclient()  --  Adds a new postgresql subclient to the backupset.

 add_mysql_subclient()  --  Adds a new mysql subclient to the instance.

add_virtual_server_subclient()  -- adds a new virtual server subclient to the backupset

add_onedrive_subclient()   --  adds a new onedrive subclient to the instance

get(subclient_name)         --  returns the subclient object of the input subclient name

delete(subclient_name)      --  deletes the subclient (subclient name) from the backupset

refresh()                   --  refresh the subclients associated with the Backupset / Instance
</code></pre>
<h1 id="subclient">Subclient:</h1>
<pre><code>__init__()                  --  initialise instance of the Subclient class,
associated to the specified backupset

__getattr__()               --  provides access to restore helper methods

__repr__()                  --  return the subclient name, the instance is associated with

_get_subclient_id()         --  method to get subclient id, if not specified in __init__ method

_get_subclient_properties() --  get the properties of this subclient

_set_subclient_properties() --  sets the properties of this sub client .

_process_backup_request()   --  runs the backup request provided, and processes the response

_browse_and_find_json()     --  returns the appropriate JSON request to pass for either
Browse operation or Find operation

_process_browse_response()  --  processes response received for both Browse and Find request

_common_backup_options()    --  Generates the advanced job options dict

_json_task()                --  setter for task property

_json_restore_subtask()     --  setter for sub task property

_association_json()         --  setter for association property

_get_preview_metadata()     --  gets the preview metadata for the file

_get_preview()              --  gets the preview for the file

update_properties()         --  To update the subclient properties

description()               --  update the description of the subclient

content()                   --  update the content of the subclient

enable_backup()             --  enables the backup for the subclient

enable_trueup()             --  enables true up option for the subclient

enable_trueup_days()        --  enables true up option and sets days for backup

enable_backup_at_time()     --  enables backup for the subclient at the input time specified

disable_backup()             --  disables the backup for the subclient

set_proxy_for_snap()        --  method to set Use proxy option for intellisnap subclient

unset_proxy_for_snap()      --  method to unset Use proxy option for intellisnap subclient

backup()                    --  run a backup job for the subclient

browse()                    --  gets the content of the backup for this subclient
at the path specified

browse_in_time()            --  gets the content of the backup for this subclient
at the input path in the time range specified

find()                      --  searches a given file/folder name in the subclient content

list_media()                --  List media required to browse and restore backed up data from the backupset

restore_in_place()          --  Restores the files/folders specified in the
input paths list to the same location

restore_out_of_place()      --  Restores the files/folders specified in the input paths list
to the input client, at the specified destionation location

set_backup_nodes()          -- Set Backup Nodes for NFS Share Pseudo client's subclient.

find_latest_job()           --  Finds the latest job for the subclient
which includes current running job also.

run_content_indexing()      -- Runs CI for subclient

refresh()                   --  refresh the properties of the subclient
</code></pre>
<h1 id="subclient-instance-attributes">Subclient Instance Attributes:</h1>
<pre><code>**properties**                      --  returns the properties of the subclient

**name**                            --  returns the name of the subclient

**display_name**                    --  returns the display name of the subclient

**description**                     --  returns the description of the subclient

**snapshot_engine_name**            --  returns snapshot engine name associated
with the subclient

**is_default_subclient**            --  returns True if the subclient is default
subclient else returns False

**is_blocklevel_backup_enabled**    --  returns True if block level backup is enabled
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L1-L3585" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing subclient operations.

Subclients and Subclient are 2 classes defined in this file.

Subclients: Class for representing all the subclients associated with a backupset / instance

Subclient: Base class consisting of all the common properties and operations for a Subclient


Subclients:
===========
    __init__(class_object)      --  initialise object of subclients object associated with
    the specified backup set/instance.

    __str__()                   --  returns all the subclients associated with the backupset

    __repr__()                  --  returns the string for the instance of the Subclients class

    __len__()                   --  returns the number of subclients associated with the Agent
    for the selected Client

    __getitem__()               --  returns the name of the subclient for the given subclient Id
    or the details for the given subclient name

    _get_subclients()           --  gets all the subclients associated with the backupset specified

    _process_add_request()      --  to post the add client request

    default_subclient()         --  returns the name of the default subclient

    all_subclients()            --  returns dict of all the subclients on commcell

    has_subclient()             --  checks if a subclient exists with the given name or not

    add()                       --  adds a new subclient to the backupset

    add_oracle_logical_dump_subclient()  --  add subclient for oracle logical dump

    add_postgresql_subclient()  --  Adds a new postgresql subclient to the backupset.

     add_mysql_subclient()  --  Adds a new mysql subclient to the instance.

    add_virtual_server_subclient()  -- adds a new virtual server subclient to the backupset

    add_onedrive_subclient()   --  adds a new onedrive subclient to the instance

    get(subclient_name)         --  returns the subclient object of the input subclient name

    delete(subclient_name)      --  deletes the subclient (subclient name) from the backupset

    refresh()                   --  refresh the subclients associated with the Backupset / Instance


Subclient:
==========
    __init__()                  --  initialise instance of the Subclient class,
    associated to the specified backupset

    __getattr__()               --  provides access to restore helper methods

    __repr__()                  --  return the subclient name, the instance is associated with

    _get_subclient_id()         --  method to get subclient id, if not specified in __init__ method

    _get_subclient_properties() --  get the properties of this subclient

    _set_subclient_properties() --  sets the properties of this sub client .

    _process_backup_request()   --  runs the backup request provided, and processes the response

    _browse_and_find_json()     --  returns the appropriate JSON request to pass for either
    Browse operation or Find operation

    _process_browse_response()  --  processes response received for both Browse and Find request

    _common_backup_options()    --  Generates the advanced job options dict

    _json_task()                --  setter for task property

    _json_restore_subtask()     --  setter for sub task property

    _association_json()         --  setter for association property

    _get_preview_metadata()     --  gets the preview metadata for the file

    _get_preview()              --  gets the preview for the file

    update_properties()         --  To update the subclient properties

    description()               --  update the description of the subclient

    content()                   --  update the content of the subclient

    enable_backup()             --  enables the backup for the subclient

    enable_trueup()             --  enables true up option for the subclient

    enable_trueup_days()        --  enables true up option and sets days for backup

    enable_backup_at_time()     --  enables backup for the subclient at the input time specified

    disable_backup()             --  disables the backup for the subclient

    set_proxy_for_snap()        --  method to set Use proxy option for intellisnap subclient

    unset_proxy_for_snap()      --  method to unset Use proxy option for intellisnap subclient

    backup()                    --  run a backup job for the subclient

    browse()                    --  gets the content of the backup for this subclient
    at the path specified

    browse_in_time()            --  gets the content of the backup for this subclient
    at the input path in the time range specified

    find()                      --  searches a given file/folder name in the subclient content

    list_media()                --  List media required to browse and restore backed up data from the backupset

    restore_in_place()          --  Restores the files/folders specified in the
    input paths list to the same location

    restore_out_of_place()      --  Restores the files/folders specified in the input paths list
    to the input client, at the specified destionation location

    set_backup_nodes()          -- Set Backup Nodes for NFS Share Pseudo client&#39;s subclient.

    find_latest_job()           --  Finds the latest job for the subclient
    which includes current running job also.

    run_content_indexing()      -- Runs CI for subclient

    refresh()                   --  refresh the properties of the subclient


Subclient Instance Attributes:
==============================

    **properties**                      --  returns the properties of the subclient

    **name**                            --  returns the name of the subclient

    **display_name**                    --  returns the display name of the subclient

    **description**                     --  returns the description of the subclient

    **snapshot_engine_name**            --  returns snapshot engine name associated
    with the subclient

    **is_default_subclient**            --  returns True if the subclient is default
    subclient else returns False

    **is_blocklevel_backup_enabled**    --  returns True if block level backup is enabled

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

import math
import time
import copy
from base64 import b64encode
from .job import Job
from .job import JobController
from .schedules import Schedules
from .exception import SDKException
from .schedules import SchedulePattern


class Subclients(object):
    &#34;&#34;&#34;Class for getting all the subclients associated with a client.&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initialize the Subclients object for the given backupset.

            Args:
                class_object    (object)    --  instance of the Agent / Instance / Backupset class

            Returns:
                object  -   instance of the Subclients class

            Raises:
                SDKException:
                    if class object is not an instance of Agent / Instance / Backupset

        &#34;&#34;&#34;
        from .agent import Agent
        from .instance import Instance
        from .backupset import Backupset

        self._agent_object = None
        self._instance_object = None
        self._backupset_object = None
        self._url_param = &#39;&#39;

        if isinstance(class_object, Agent):
            self._agent_object = class_object
            self._url_param += self._agent_object.agent_id

        elif isinstance(class_object, Instance):
            self._instance_object = class_object
            self._agent_object = self._instance_object._agent_object
            self._url_param += &#39;{0}&amp;instanceId={1}&#39;.format(
                self._agent_object.agent_id, self._instance_object.instance_id
            )

        elif isinstance(class_object, Backupset):
            self._backupset_object = class_object
            self._instance_object = class_object._instance_object
            self._agent_object = self._instance_object._agent_object
            self._url_param += self._agent_object.agent_id
            self._url_param += &#39;&amp;backupsetId={0}&#39;.format(
                self._backupset_object.backupset_id
            )
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;115&#39;)

        self._client_object = self._agent_object._client_object
        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._SUBCLIENTS = self._services[&#39;GET_ALL_SUBCLIENTS&#39;] % (
            self._client_object.client_id, self._url_param
        )

        self._ADD_SUBCLIENT = self._services[&#39;ADD_SUBCLIENT&#39;]

        self._default_subclient = None

        # sql server subclient type dict
        self._sqlsubclient_type_dict = {
            &#39;DATABASE&#39;: 1,
            &#39;FILE_FILEGROUP&#39;: 2,
        }

        # this will work only for `Exchange Database` Agent, as only an object of
        # ExchangeDatabaseAgent class has these attributes
        if self._instance_object is None and hasattr(
                self._agent_object, &#39;_instance_object&#39;):
            self._instance_object = self._agent_object._instance_object

        if self._backupset_object is None and hasattr(
                self._agent_object, &#39;_backupset_object&#39;):
            self._backupset_object = self._agent_object._backupset_object

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all subclients of the backupset.

            Returns:
                str - string of all the subclients of th backupset of an agent of a client
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;S. No.&#39;, &#39;Subclient&#39;, &#39;Backupset&#39;, &#39;Instance&#39;, &#39;Agent&#39;, &#39;Client&#39;
        )

        for index, subclient in enumerate(self._subclients):
            sub_str = &#39;{:^5}\t{:20}\t{:20}\t{:20}\t{:20}\t{:20}\n&#39;.format(
                index + 1,
                subclient.split(&#39;\\&#39;)[-1],
                self._subclients[subclient][&#39;backupset&#39;],
                self._instance_object.instance_name,
                self._agent_object.agent_name,
                self._client_object.client_name
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Subclients class.&#34;&#34;&#34;
        if self._backupset_object is not None:
            o_str = (
                &#39;Subclients class instance for Backupset: &#34;{0}&#34;, &#39;
                &#39;of Instance: &#34;{1}&#34;, for Agent: &#34;{2}&#34;&#39;
            ).format(
                self._backupset_object.backupset_name,
                self._instance_object.instance_name,
                self._agent_object.agent_name
            )
        elif self._instance_object is not None:
            o_str = &#39;Subclients class instance for Instance: &#34;{0}&#34;, of Agent: &#34;{1}&#34;&#39;.format(
                self._instance_object.instance_name,
                self._agent_object.agent_name
            )
        else:
            o_str = &#39;Subclients class instance for Agent: &#34;{0}&#34;&#39;.format(
                self._agent_object.agent_name
            )

        return o_str

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the subclients associated to the Agent for the selected Client.&#34;&#34;&#34;
        return len(self.all_subclients)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the subclient for the given subclient ID or
            the details of the subclient for given subclient Name.

            Args:
                value   (str / int)     --  Name or ID of the subclient

            Returns:
                str     -   name of the subclient, if the subclient id was given

                dict    -   dict of details of the subclient, if subclient name was given

            Raises:
                IndexError:
                    no subclient exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_subclients:
            return self.all_subclients[value]
        else:
            try:
                return list(
                    filter(lambda x: x[1][&#39;id&#39;] == value, self.all_subclients.items())
                )[0][0]
            except IndexError:
                raise IndexError(&#39;No subclient exists with the given Name / Id&#39;)

    def _get_subclients(self):
        &#34;&#34;&#34;Gets all the subclients associated to the client specified by the backupset object.

            Returns:
                dict - consists of all subclients in the backupset
                    {
                         &#34;subclient1_name&#34;: {
                             &#34;id&#34;: subclient1_id,
                             &#34;backupset&#34;: backupset
                         },
                         &#34;subclient2_name&#34;: {
                             &#34;id&#34;: subclient2_id,
                             &#34;backupset&#34;: backupset
                         }
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SUBCLIENTS)

        if flag:
            if response.json() and &#39;subClientProperties&#39; in response.json():
                return_dict = {}

                for dictionary in response.json()[&#39;subClientProperties&#39;]:
                    # store the agent, instance, and backupset name for the current subclient
                    # the API call returns the subclients for all Agents, so we need to filter
                    # them out based on the Agent / Instance / Backupset that had been selected
                    # by the user earlier
                    agent = dictionary[&#39;subClientEntity&#39;][&#39;appName&#39;].lower()
                    instance = dictionary[&#39;subClientEntity&#39;][&#39;instanceName&#39;].lower(
                    )
                    backupset = dictionary[&#39;subClientEntity&#39;][&#39;backupsetName&#39;].lower(
                    )

                    # filter subclients for all entities: Agent, Instance, and Backupset
                    # as the instance of the Backupset class was passed for Subclients instance
                    # creation
                    if self._backupset_object is not None:
                        if (self._backupset_object.backupset_name in backupset and
                                self._instance_object.instance_name in instance and
                                self._agent_object.agent_name in agent):
                            temp_name = dictionary[&#39;subClientEntity&#39;][&#39;subclientName&#39;].lower(
                            )
                            temp_id = str(
                                dictionary[&#39;subClientEntity&#39;][&#39;subclientId&#39;]).lower()

                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;backupset&#34;: backupset
                            }

                            if dictionary[&#39;commonProperties&#39;].get(
                                    &#39;isDefaultSubclient&#39;):
                                self._default_subclient = temp_name

                    elif self._instance_object is not None:
                        if (self._instance_object.instance_name in instance and
                                self._agent_object.agent_name in agent):
                            temp_name = dictionary[&#39;subClientEntity&#39;][&#39;subclientName&#39;].lower(
                            )
                            temp_id = str(
                                dictionary[&#39;subClientEntity&#39;][&#39;subclientId&#39;]).lower()

                            if len(
                                    self._instance_object.backupsets.all_backupsets) &gt; 1:
                                temp_name = &#34;{0}\\{1}&#34;.format(
                                    backupset, temp_name)

                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;backupset&#34;: backupset
                            }

                            if dictionary[&#39;commonProperties&#39;].get(
                                    &#39;isDefaultSubclient&#39;):
                                self._default_subclient = temp_name

                    elif self._agent_object is not None:
                        if self._agent_object.agent_name in agent:
                            temp_name = dictionary[&#39;subClientEntity&#39;][&#39;subclientName&#39;].lower(
                            )
                            temp_id = str(
                                dictionary[&#39;subClientEntity&#39;][&#39;subclientId&#39;]).lower()

                            if len(self._agent_object.instances.all_instances) &gt; 1:
                                if len(
                                        self._instance_object.backupsets.all_backupsets) &gt; 1:
                                    temp_name = &#34;{0}\\{1}\\{2}&#34;.format(
                                        instance, backupset, temp_name
                                    )
                                else:
                                    temp_name = &#34;{0}\\{1}&#34;.format(
                                        instance, temp_name)
                            else:
                                if len(
                                        self._instance_object.backupsets.all_backupsets) &gt; 1:
                                    temp_name = &#34;{0}\\{1}&#34;.format(
                                        backupset, temp_name)

                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;backupset&#34;: backupset
                            }

                            if dictionary[&#39;commonProperties&#39;].get(
                                    &#39;isDefaultSubclient&#39;):
                                self._default_subclient = temp_name

                return return_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    @property
    def all_subclients(self):
        &#34;&#34;&#34;Returns dict of all the subclients configured on this backupset

            Retruns:
                dict    -   consists of all subclients in the backupset

                    {
                        &#34;subclient1_name&#34;: {
                            &#34;id&#34;: subclient1_id,

                            &#34;backupset&#34;: backupset
                        },
                        &#34;subclient2_name&#34;: {
                            &#34;id&#34;: subclient2_id,

                            &#34;backupset&#34;: backupset
                        }
                    }

        &#34;&#34;&#34;
        return self._subclients

    def has_subclient(self, subclient_name):
        &#34;&#34;&#34;Checks if a subclient exists in the commcell with the input subclient name.

            Args:
                subclient_name (str)  --  name of the subclient

            Returns:
                bool - boolean output whether the subclient exists in the backupset or not

            Raises:
                SDKException:
                    if type of the subclient name argument is not string
        &#34;&#34;&#34;
        if not isinstance(subclient_name, str):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return self._subclients and subclient_name.lower() in self._subclients

    def _process_add_request(self, request_json):
        &#34;&#34;&#34;To post the add subclient request

        Args:
            request_json    (dict)  -- Request json to be passed as the payload

        Returns:
            object  -   instance of the Subclient class

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._ADD_SUBCLIENT, request_json
        )

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Subclient&#39;,
                        &#39;102&#39;,
                        &#39;Failed to create subclient\nError: &#34;{0}&#34;&#39;.format(error_string)
                    )
                else:
                    # initialize the subclients again so the subclient object has all the subclients
                    self.refresh()

                    subclient_name = request_json[&#39;subClientProperties&#39;][&#39;subClientEntity&#39;][&#39;subclientName&#39;]

                    return self.get(subclient_name)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add(self, subclient_name, storage_policy=None,
            subclient_type=None, description=&#39;&#39;, advanced_options=None,
            pre_scan_cmd=None):
        &#34;&#34;&#34;Adds a new subclient to the backupset.

            Args:
                subclient_name      (str)   --  name of the new subclient to add

                storage_policy      (str)   --  name of the storage policy to be associated
                with the subclient

                    default: None

                subclient_type      (str)   --  type of subclient for sql server

                    default: None

                    Valid Values are:

                        - DATABASE

                        - FILE_FILEGROUP


                description         (str)   --  description for the subclient (optional)

                    default: &#39;&#39;

                advanced_options    (dict)  --  dict of additional options needed to create
                                                subclient with additional properties
                                                default : None
                    Example:
                        {
                            ondemand_subclient : True
                        }

                pre_scan_cmd        (str)   --  path to the batch file/shell script file to run
                                                before each backup of the subclient

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if description argument is not of type string

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(description, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if self._backupset_object is None:
            if self._instance_object.backupsets.has_backupset(
                    &#39;defaultBackupSet&#39;):
                self._backupset_object = self._instance_object.backupsets.get(
                    &#39;defaultBackupSet&#39;)
            else:
                self._backupset_object = self._instance_object.backupsets.get(
                    sorted(self._instance_object.backupsets.all_backupsets)[0]
                )

        if storage_policy and not self._commcell_object.storage_policies.has_policy(
                storage_policy):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    storage_policy)
            )

        if advanced_options:
            if advanced_options.get(&#34;ondemand_subclient&#34;, False):
                ondemand_value = advanced_options.get(&#34;ondemand_subclient&#34;)
            else:
                ondemand_value = False
        else:
            ondemand_value = False

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;contentOperationType&#34;: 2,
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;commonProperties&#34;: {
                    &#34;description&#34;: description,
                    &#34;enableBackup&#34;: True,
                    &#34;onDemandSubClient&#34;: ondemand_value,
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: storage_policy
                        }
                    },
                }
            }
        }

        if storage_policy is None:
            del request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;storageDevice&#34;]

        if pre_scan_cmd is not None:
            request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;prepostProcess&#34;] = {
                &#34;runAs&#34;: 1,
                &#34;preScanCommand&#34;: pre_scan_cmd
            }

        if self._agent_object.agent_name == &#39;sql server&#39;:
            request_json[&#39;subClientProperties&#39;][&#39;mssqlSubClientProp&#39;] = {
                &#39;sqlSubclientType&#39;: self._sqlsubclient_type_dict[subclient_type]
            }

        return self._process_add_request(request_json)

    def add_oracle_logical_dump_subclient(
            self,
            subclient_name,
            storage_policy,
            dump_dir,
            user_name,
            domain_name,
            password,
            full_mode,
            schema_value=None):
        &#34;&#34;&#34;
        Method to add subclient for oracle logical dump.
        This method add two type of subclient full mode
        and schema mode. For full mode full_mode should be
        true and schema_value should be none and for schema
        mode full_mode should be false and schema_value should
        be list of values.Rest of thing should be same for both.
        Args:
              subclient_name     (Str)  --  subclient name for logical dump

              storage_policy     (Str)  --  Storage policy for subclient

              dump_dir            (Str)  --  dump directory for subclient

              user_name           (Str)  --  username for oracle database

              domain_name         (Str)  --  domainname for oracle database

              password           (Str)  --  password for oracle database
                                            (should be in encrypted and decrypted form)

              full_mode           (bool) --  if ture then subclient for full mode otherwise schema mode

              schema_value        (list) --  schema value for schema mode subclient

                   default: None
        Return:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if subclient name already present

                    if storage policy does not exist

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(storage_policy, str) and
                isinstance(dump_dir, str) and
                isinstance(user_name, str) and
                isinstance(domain_name, str) and
                isinstance(password, str) and
                isinstance(full_mode, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        if (full_mode == False and not
        isinstance(schema_value, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if self._backupset_object is None:
            if self._instance_object.backupsets.has_backupset(
                    &#39;defaultBackupSet&#39;):
                self._backupset_object = self._instance_object.backupsets.get(
                    &#39;defaultBackupSet&#39;)
            else:
                self._backupset_object = self._instance_object.backupsets.get(
                    sorted(self._instance_object.backupsets.all_backupsets)[0]
                )

        if not self._commcell_object.storage_policies.has_policy(
                storage_policy):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    storage_policy)
            )

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;oracleSubclientProp&#34;: {
                    &#34;data&#34;: False,
                    &#34;archiveDelete&#34;: False,
                    &#34;useSQLConntect&#34;: False,
                    &#34;dbSubclientType&#34;: 2,
                    &#34;mergeIncImageCopies&#34;: False,
                    &#34;selectiveOnlineFull&#34;: False,
                    &#34;protectBackupRecoveryArea&#34;: False,
                    &#34;selectArchiveLogDestForBackup&#34;: False,
                    &#34;backupSPFile&#34;: False,
                    &#34;backupControlFile&#34;: False,
                    &#34;backupArchiveLog&#34;: False,
                    &#34;validate&#34;: False,
                },
                &#34;commonProperties&#34;: {
                    &#34;snapCopyInfo&#34;: {
                        &#34;useSeparateProxyForSnapToTape&#34;: False,
                        &#34;checkProxyForSQLIntegrity&#34;: False,
                        &#34;snapToTapeProxyToUseSource&#34;: False,
                        &#34;isSnapBackupEnabled&#34;: False,
                        &#34;IsOracleSposDriverEnabled&#34;: False,
                        &#34;isRMANEnableForTapeMovement&#34;: False
                    },
                    &#34;dbDumpConfig&#34;: {
                        &#34;fullMode&#34;: True,
                        &#34;database&#34;: &#34;&#34;,
                        &#34;dumpDir&#34;: dump_dir,
                        &#34;parallelism&#34;: 2,
                        &#34;overrideInstanceUser&#34;: True,
                        &#34;sqlConnect&#34;: {
                            &#34;password&#34;: b64encode(password.encode()).decode(),
                            &#34;domainName&#34;: domain_name,
                            &#34;userName&#34;: user_name
                        }
                    },
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: storage_policy
                        },
                        &#34;deDuplicationOptions&#34;: {
                            &#34;enableDeduplication&#34;: True
                        }
                    }
                }
            }
        }

        if (full_mode == False):
            request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;dbDumpConfig&#34;][&#34;fullMode&#34;] = False
            request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;dbDumpConfig&#34;][&#34;schema&#34;] = schema_value

        return self._process_add_request(request_json)

    def add_postgresql_subclient(
            self, subclient_name, storage_policy,
            contents, no_of_streams=1, collect_object_list=False):
        &#34;&#34;&#34;Adds a new postgresql subclient to the backupset.

            Args:
                subclient_name          (str)   --  name of the new subclient to add

                storage_policy          (str)   --  name of the storage policy to be associated
                with the subclient

                contents                (list)  --  database list to be added as subclient content


                no_of_streams           (int)   --  No of backup streams to be used

                    default: 1

                collect_object_list     (bool)  --  Boolean flag to determine if collect object
                list needs to be enabled for subclient or not

                    default: False

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if conetnts argument is not of type list

                    if contents is empty list

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(storage_policy, str) and
                isinstance(contents, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if not self._commcell_object.storage_policies.has_policy(
                storage_policy):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    storage_policy)
            )

        if not contents:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Content list cannot be empty&#39;
            )

        content_list = []
        for content in contents:
            content_list.append({&#34;postgreSQLContent&#34;: {&#34;databaseName&#34;: content}})

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;contentOperationType&#34;: 2,
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;commonProperties&#34;: {
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: storage_policy
                        }
                    },
                },
                &#34;postgreSQLSubclientProp&#34;: {
                    &#34;numberOfBackupStreams&#34;: no_of_streams,
                    &#34;collectObjectListDuringBackup&#34;: collect_object_list
                },
                &#34;content&#34;: content_list
            }
        }

        return self._process_add_request(request_json)

    def add_mysql_subclient(
            self,
            subclient_name,
            storage_policy,
            contents,
            **kwargs
    ):
        &#34;&#34;&#34;Adds a new mysql subclient to the instance.

            Args:
                subclient_name          (str)   --  name of the new subclient to add

                storage_policy          (str)   --  name of the storage policy to be associated
                with the subclient

                contents                (list)  --  database list to be added as subclient content

                kwargs      (dict)  -- dict of keyword arguments as follows

                    no_of_backup_streams    (int)   --  No of backup streams to be used
                    default: 1

                    no_of_log_backup_streams    (int)   -- No of Transaction log backup streams
                    default: 1

                    full_instance_xtrabackup    (bool)  -- True if XtraBackup is selected for subclient
                    default: False

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if conetnts argument is not of type list

                    if contents is empty list

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(storage_policy, str) and
                isinstance(contents, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if not self._commcell_object.storage_policies.has_policy(
                storage_policy):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    storage_policy)
            )

        if not contents:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Content list cannot be empty&#39;
            )

        content_list = []
        for content in contents:
            content_list.append({&#34;mySQLContent&#34;: {&#34;databaseName&#34;: content}})

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;contentOperationType&#34;: 2,
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: &#34;defaultDummyBackupSet&#34;,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;commonProperties&#34;: {
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: storage_policy
                        }
                    },
                },
                &#34;mySqlSubclientProp&#34;: {
                    &#34;numberOfBackupStreams&#34;: kwargs.get(&#39;no_of_backup_streams&#39;, 1),
                    &#34;numberOfTransactionLogStreams&#34;: kwargs.get(&#39;no_of_log_backup_streams&#39;, 1),
                    &#34;fullInstanceXtraBackup&#34;: kwargs.get(&#39;full_instance_xtrabackup&#39;, False)
                },
                &#34;content&#34;: content_list
            }
        }

        return self._process_add_request(request_json)

    def add_virtual_server_subclient(
            self,
            subclient_name,
            subclient_content,
            **kwargs
    ):
        &#34;&#34;&#34;Adds a new virtual server subclient to the backupset.

            Args:
                subclient_name      (str)   --  Name of the subclient to be created

                subclient_content   (list)  --  Content to be added to the subclient

                    Example 1:
                        [{
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;id&#39;: &#39;&#39;,
                            &#39;path&#39;: &#39;&#39;,
                            &#39;display_name&#39;: &#39;sample1&#39;,
                            &#39;type&#39;: VSAObjects.VMName
                        }]
                    Example 2:
                         [{
                        &#39;allOrAnyChildren&#39;: False,
                        &#39;content&#39;: [{
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;display_name&#39;: &#39;sample1&#39;,
                            &#39;type&#39;: VSAObjects.VMName
                        }, {
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;display_name&#39;: &#39;sample2&#39;,
                            &#39;type&#39;: VSAObjects.VMName
                        }
                        ]
                        }, {
                        &#39;allOrAnyChildren&#39;: True,
                        &#39;content&#39;: [{
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;display_name&#39;: &#39;sample3&#39;,
                            &#39;type&#39;: VSAObjects.RESOURCE_POOL
                        }, {
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;id&#39;: &#39;sample4&#39;,
                            &#39;display_name&#39;: &#39;sample4&#39;,
                            &#39;type&#39;: VSAObjects.SERVER
                            }
                            ]
                        }
                        ]
                        **Note** Use VSAObjects Enum present in constants.py to pass value to type

                kwargs      (dict)  -- dict of keyword arguments as follows

                    plan_name           (str)   --  Plan to be associated with the subclient

                    storage_policy      (str)   --  Storage policy to be associated with the subclient

                    description         (str)   --  Description for the subclient

                        default: &#39;&#39;

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if description argument is not of type string

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(subclient_content, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if self._backupset_object is None:
            if self._instance_object.backupsets.has_backupset(
                    &#39;defaultBackupSet&#39;):
                self._backupset_object = self._instance_object.backupsets.get(
                    &#39;defaultBackupSet&#39;)
            else:
                self._backupset_object = self._instance_object.backupsets.get(
                    sorted(self._instance_object.backupsets.all_backupsets)[0]
                )

        content = []

        def set_content(item_content):
            &#34;&#34;&#34;
            create content dictionary
            Args:
                item_content            (dict):     Dict of content details

                Example:
                    {
                        &#39;equal_value&#39;: True,
                        &#39;allOrAnyChildren&#39;: True,
                        &#39;display_name&#39;: &#39;sample1&#39;,
                        &#39;type&#39;:  &lt; VSAObjects.VMName: 10 &gt;
                    }

            Returns:

            &#34;&#34;&#34;
            return {
                &#34;equalsOrNotEquals&#34;: item_content.get(&#39;equal_value&#39;, True),
                &#34;name&#34;: item_content.get(&#39;id&#39;, &#39;&#39;),
                &#34;displayName&#34;: item_content.get(&#39;display_name&#39;, &#39;&#39;),
                &#34;path&#34;: item_content.get(&#39;path&#39;, &#39;&#39;),
                &#34;allOrAnyChildren&#34;: item.get(&#39;allOrAnyChildren&#39;, True),
                &#34;type&#34;: item_content[&#39;type&#39;] if isinstance(item_content[&#39;type&#39;], int) else item_content[&#39;type&#39;].value
            }

        for item in subclient_content:
            _temp_list = []
            _temp_dict = {}
            allOrAnyChildren = item.get(&#39;allOrAnyChildren&#39;, None)
            if &#39;content&#39; in item:
                nested_content = item[&#39;content&#39;]
                for each_condition in nested_content:
                    temp_dict = set_content(each_condition)
                    _temp_list.append(temp_dict)
                _temp_dict[&#39;allOrAnyChildren&#39;] = allOrAnyChildren
                _temp_dict[&#39;children&#39;] = _temp_list
                content.append(_temp_dict)
            else:
                temp_dict = set_content(item)
                content.append(temp_dict)

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;vmContentOperationType&#34;: 2,
                &#34;vmContent&#34;: {
                    &#34;children&#34;: content
                },
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;commonProperties&#34;: {
                    &#34;description&#34;: kwargs.get(&#39;description&#39;),
                    &#34;enableBackup&#34;: True
                }
            }
        }

        if kwargs.get(&#34;customSnapshotResourceGroup&#34;):
            request_json[&#34;subClientProperties&#34;][&#34;vsaSubclientProp&#34;] = \
                {&#34;customSnapshotResourceGroup&#34;: kwargs.get(&#34;customSnapshotResourceGroup&#34;)}

        if kwargs.get(&#39;plan_name&#39;):
            if not self._commcell_object.plans.has_plan(kwargs[&#39;plan_name&#39;]):
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Plan: &#34;{0}&#34; does not exist in the Commcell&#39;.format(kwargs[&#39;plan_name&#39;])
                )
            request_json[&#39;subClientProperties&#39;][&#39;planEntity&#39;] = {
                &#34;planName&#34;: kwargs[&#39;plan_name&#39;]
            }

        elif kwargs.get(&#39;storage_policy&#39;):
            if not self._commcell_object.storage_policies.has_policy(kwargs.get(&#39;storage_policy&#39;)):
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(kwargs.get(&#39;storage_policy&#39;))
                )
            request_json[&#39;subClientProperties&#39;][&#39;commonProperties&#39;][&#39;storageDevice&#39;] = {
                &#34;dataBackupStoragePolicy&#34;: {
                    &#34;storagePolicyName&#34;: kwargs.get(&#39;storage_policy&#39;)
                }
            }
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Either Plan or Storage policy should be given as input&#39;)

        return self._process_add_request(request_json)

    def add_onedrive_subclient(self,
                               subclient_name,
                               server_plan):

        &#34;&#34;&#34;Adds a new subclient to the backupset.

            Args:
                subclient_name     (str)   --  name of the new subclient to add

                server_plan     (str)   --  name of the server plan to be associated
                                                with the subclient

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if server plan argument is not of type string

                    if description argument is not of type string

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

                    if server plan  donot exists with the given name

                &#34;&#34;&#34;

        if not (isinstance(subclient_name, str) and
                isinstance(server_plan, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if self._backupset_object is None:
            if self._instance_object.backupsets.has_backupset(
                    self._instance_object.backupsets.default_backup_set):
                self._backupset_object = self._instance_object.backupsets.get(
                    self._instance_object.backupsets.default_backup_set)
            else:
                self._backupset_object = self._instance_object.backupsets.get(
                    sorted(self._instance_object.backupsets.all_backupsets)[0]
                )

        if self._commcell_object.plans.has_plan(server_plan):
            server_plan_object = self._commcell_object.plans.get(server_plan)
            server_plan_id = int(server_plan_object.plan_id)
        else:
            raise SDKException(&#39;Plan&#39;, &#39;102&#39;, &#39;Provide Valid Plan Name&#39;)

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
                    &#34;instanceId&#34;: int(self._instance_object.instance_id),
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;applicationId&#34;: 134,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;planEntity&#34;: {
                    &#34;planId&#34;: server_plan_id
                },
                &#34;cloudAppsSubClientProp&#34;: {
                    &#34;instanceType&#34;: 7,
                    &#34;oneDriveSubclient&#34;: {
                        &#34;enableOneNote&#34;: False,
                        &#34;isEnterprise&#34;: True
                    }
                },
                &#34;cloudconnectorSubclientProp&#34;: {
                    &#34;isAutoDiscoveryEnabled&#34;: False
                },
                &#34;commonProperties&#34;: {
                    &#34;enableBackup&#34;: True
                }
            }
        }

        return self._process_add_request(request_json)

    def get(self, subclient_name):
        &#34;&#34;&#34;Returns a subclient object of the specified backupset name.

            Args:
                subclient_name (str)  --  name of the subclient

            Returns:
                object - instance of the Subclient class for the given subclient name

            Raises:
                SDKException:
                    if type of the subclient name argument is not string

                    if no subclient exists with the given name
        &#34;&#34;&#34;
        if not isinstance(subclient_name, str):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        else:
            subclient_name = subclient_name.lower()

            if self.has_subclient(subclient_name):

                if self._backupset_object is None:
                    self._backupset_object = self._instance_object.backupsets.get(
                        self._subclients[subclient_name][&#39;backupset&#39;]
                    )
                return Subclient(
                    self._backupset_object, subclient_name, self._subclients[subclient_name][&#39;id&#39;]
                )

            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;No subclient exists with name: {0}&#39;.format(
                    subclient_name)
            )

    def delete(self, subclient_name):
        &#34;&#34;&#34;Deletes the subclient specified by the subclient_name from the backupset.

            Args:
                subclient_name (str)  --  name of the subclient to remove from the backupset

            Raises:
                SDKException:
                    if type of the subclient name argument is not string

                    if failed to delete subclient

                    if response is empty

                    if response is not success

                    if no subclient exists with the given name
        &#34;&#34;&#34;
        if not isinstance(subclient_name, str):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        else:
            subclient_name = subclient_name.lower()

        if self.has_subclient(subclient_name):
            delete_subclient_service = self._services[&#39;SUBCLIENT&#39;] % (
                self._subclients[subclient_name][&#39;id&#39;]
            )

            flag, response = self._cvpysdk_object.make_request(
                &#39;DELETE&#39;, delete_subclient_service)

            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response_value = response.json()[&#39;response&#39;][0]
                        error_code = str(response_value[&#39;errorCode&#39;])
                        error_message = None

                        if &#39;errorString&#39; in response_value:
                            error_message = response_value[&#39;errorString&#39;]

                        if error_message:
                            o_str = &#39;Failed to delete subclient\nError: &#34;{0}&#34;&#39;
                            raise SDKException(
                                &#39;Subclient&#39;, &#39;102&#39;, o_str.format(error_message))
                        else:
                            if error_code == &#39;0&#39;:
                                # initialize the subclients again
                                # so the subclient object has all the
                                # subclients
                                self.refresh()
                            else:
                                o_str = (&#39;Failed to delete subclient with Error Code: &#34;{0}&#34;\n&#39;
                                         &#39;Please check the documentation for &#39;
                                         &#39;more details on the error&#39;)
                                raise SDKException(
                                    &#39;Subclient&#39;, &#39;102&#39;, o_str.format(error_code))
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;No subclient exists with name: {0}&#39;.format(
                    subclient_name)
            )

    def refresh(self):
        &#34;&#34;&#34;Refresh the subclients associated with the Backupset / Instance.&#34;&#34;&#34;
        self._subclients = self._get_subclients()

    @property
    def default_subclient(self):
        &#34;&#34;&#34;Returns the name of the default subclient for the selected Agent and Backupset.&#34;&#34;&#34;
        return self._default_subclient


class Subclient(object):
    &#34;&#34;&#34;Base class consisting of all the common properties and operations for a Subclient&#34;&#34;&#34;

    def __new__(cls, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Class composition for CV subclients&#34;&#34;&#34;
        from .subclients.fssubclient import FileSystemSubclient
        from .subclients.bigdataappssubclient import BigDataAppsSubclient
        from .subclients.vssubclient import VirtualServerSubclient
        from .subclients.casubclient import CloudAppsSubclient
        from .subclients.sqlsubclient import SQLServerSubclient
        from .subclients.nassubclient import NASSubclient
        from .subclients.hanasubclient import SAPHANASubclient
        from .subclients.oraclesubclient import OracleSubclient
        from .subclients.lotusnotes.lndbsubclient import LNDbSubclient
        from .subclients.lotusnotes.lndocsubclient import LNDocSubclient
        from .subclients.lotusnotes.lndmsubclient import LNDmSubclient
        from .subclients.sybasesubclient import SybaseSubclient
        from .subclients.saporaclesubclient import SAPOracleSubclient
        from .subclients.exchsubclient import ExchangeSubclient
        from .subclients.mysqlsubclient import MYSQLSubclient
        from .subclients.exchange.exchange_database_subclient import ExchangeDatabaseSubclient
        from .subclients.postgressubclient import PostgresSubclient
        from .subclients.informixsubclient import InformixSubclient
        from .subclients.adsubclient import ADSubclient
        from .subclients.sharepointsubclient import SharepointSubclient
        from .subclients.sharepointsubclient import SharepointV1Subclient
        from .subclients.vminstancesubclient import VMInstanceSubclient
        from .subclients.db2subclient import DB2Subclient
        from .subclients.casesubclient import CaseSubclient
        from .subclients.aadsubclient import AzureAdSubclient

        # add the agent name to this dict, and its class as the value
        # the appropriate class object will be initialized based on the agent
        _subclients_dict = {
            &#39;big data apps&#39;: BigDataAppsSubclient,
            &#39;file system&#39;: FileSystemSubclient,
            &#39;virtual server&#39;: [VirtualServerSubclient, VMInstanceSubclient],
            &#39;cloud apps&#39;: CloudAppsSubclient,
            &#39;sql server&#39;: SQLServerSubclient,
            &#39;nas&#39;: NASSubclient,  # SP11 or lower CS honors NAS as the Agent Name
            &#39;ndmp&#39;: NASSubclient,  # SP12 and above honors NDMP as the Agent Name
            &#39;sap hana&#39;: SAPHANASubclient,
            &#39;oracle&#39;: OracleSubclient,
            &#39;oracle rac&#39;: OracleSubclient,
            &#39;notes database&#39;: LNDbSubclient,
            &#39;notes document&#39;: LNDocSubclient,
            &#39;domino mailbox archiver&#39;: LNDmSubclient,
            &#39;sybase&#39;: SybaseSubclient,
            &#39;sap for oracle&#39;: SAPOracleSubclient,
            &#34;exchange mailbox&#34;: [ExchangeSubclient, CaseSubclient],
            &#39;mysql&#39;: MYSQLSubclient,
            &#39;exchange database&#39;: ExchangeDatabaseSubclient,
            &#39;postgresql&#39;: PostgresSubclient,
            &#39;db2&#39;: DB2Subclient,
            &#39;informix&#39;: InformixSubclient,
            &#39;active directory&#39;: ADSubclient,
            &#39;sharepoint server&#39;: [SharepointV1Subclient, SharepointSubclient],
            &#34;azure ad&#34;: AzureAdSubclient
        }

        agent_object = backupset_object._agent_object
        instance_object = backupset_object._instance_object
        client_object = agent_object._client_object

        agent_name = agent_object.agent_name.lower()

        if isinstance(_subclients_dict.get(agent_name), list):
            if instance_object.instance_name == &#34;vminstance&#34;:
                _class = _subclients_dict[agent_name][-1]
            elif client_object.client_type and int(client_object.client_type) == 36:
                # client type 36 is case manager client
                _class = _subclients_dict[agent_name][-1]
            elif int(agent_object.agent_id) == 78 and client_object.client_type:
                # agent id 78 is sharepoint client
                _class = _subclients_dict[agent_name][-1]
            else:
                _class = _subclients_dict[agent_name][0]
        else:
            _class = _subclients_dict.get(agent_name, cls)

        if _class.__new__ == cls.__new__:
            return object.__new__(_class)
        return _class.__new__(_class, backupset_object, subclient_name, subclient_id)

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialise the Subclient object.

            Args:
                backupset_object (object)  --  instance of the Backupset class

                subclient_name   (str)     --  name of the subclient

                subclient_id     (str)     --  id of the subclient
                    default: None

            Returns:
                object - instance of the Subclient class
        &#34;&#34;&#34;
        self._backupset_object = backupset_object
        self._subclient_name = subclient_name.split(&#39;\\&#39;)[-1].lower()

        self._commcell_object = self._backupset_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._instance_object = self._backupset_object._instance_object
        self._agent_object = self._backupset_object._agent_object
        self._client_object = self._agent_object._client_object

        self._restore_methods = [
            &#39;_process_restore_response&#39;,
            &#39;_filter_paths&#39;,
            &#39;_process_search_response&#39;,
            &#39;_restore_json&#39;,
            &#39;_impersonation_json&#39;,
            &#39;_restore_browse_option_json&#39;,
            &#39;_restore_common_options_json&#39;,
            &#39;_restore_destination_json&#39;,
            &#39;_restore_fileoption_json&#39;,
            &#39;_json_restore_subtask&#39;
        ]

        self._restore_options_json = [
            &#39;_impersonation_json_&#39;,
            &#39;_browse_restore_json&#39;,
            &#39;_destination_restore_json&#39;,
            &#39;_commonoption_restore_json&#39;,
            &#39;_fileoption_restore_json&#39;,

        ]

        self._backupcopy_interfaces = {
            &#39;FILESYSTEM&#39;: 1,
            &#39;RMAN&#39;: 2,
            &#39;VOLUME&#39;: 3
        }

        if subclient_id:
            self._subclient_id = str(subclient_id)
        else:
            self._subclient_id = self._get_subclient_id()

        self._SUBCLIENT = self._services[&#39;SUBCLIENT&#39;] % (self.subclient_id)

        self._BROWSE = self._services[&#39;BROWSE&#39;]

        self._RESTORE = self._services[&#39;RESTORE&#39;]

        self._PREVIEW_CONTENT = self._services[&#39;GET_DOC_PREVIEW&#39;]

        self._subclient_properties = {}
        self._content = []

        self.schedules = None
        self.refresh()

    def __getattr__(self, attribute):
        &#34;&#34;&#34;Returns the persistent attributes&#34;&#34;&#34;
        if attribute in self._restore_methods:
            return getattr(self._backupset_object, attribute)
        if attribute in self._restore_options_json:
            return getattr(self._backupset_object, attribute)

        return super(Subclient, self).__getattribute__(attribute)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Subclient class instance for Subclient: &#34;{0}&#34; of Backupset: &#34;{1}&#34;&#39;
        return representation_string.format(
            self.subclient_name, self._backupset_object.backupset_name
        )

    def _get_subclient_id(self):
        &#34;&#34;&#34;Gets the subclient id associated to the specified backupset name and client name.

            Returns:
                str - id associated with this subclient
        &#34;&#34;&#34;
        subclients = Subclients(self._backupset_object)
        return subclients.get(self.subclient_name).subclient_id

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient properties of this subclient.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SUBCLIENT)

        if flag:
            if response.json() and &#39;subClientProperties&#39; in response.json():
                self._subclient_properties = response.json()[
                    &#39;subClientProperties&#39;][0]

                if &#39;commonProperties&#39; in self._subclient_properties:
                    self._commonProperties = self._subclient_properties[&#39;commonProperties&#39;]

                if &#39;subClientEntity&#39; in self._subclient_properties:
                    self._subClientEntity = self._subclient_properties[&#39;subClientEntity&#39;]

                if &#39;proxyClient&#39; in self._subclient_properties:
                    self._proxyClient = self._subclient_properties[&#39;proxyClient&#39;]

                if &#39;planEntity&#39; in self._subclient_properties:
                    self._planEntity = self._subclient_properties[&#39;planEntity&#39;]

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def _set_subclient_properties(self, attr_name, value):
        &#34;&#34;&#34;sets the properties of this sub client.value is updated to instance once when post call
            succeeds

            Args:
                attr_name (str) --  Name of the attribute. This should be an instance variable.
                value (str)     --  Value of the attribute. This should be an instance variable.

            Raises:
                SDKException:
                    if failed to update number properties for subclient


        &#34;&#34;&#34;
        try:
            backup = eval(&#39;self.%s&#39; % attr_name)  # Take backup of old value
        except (AttributeError, KeyError):
            backup = None

        exec(&#34;self.%s = %s&#34; % (attr_name, &#39;value&#39;))  # set new value

        request_json = self._get_subclient_properties_json()

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUBCLIENT, request_json)

        output = self._process_update_response(flag, response)

        if output[0]:
            return
        else:
            o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;

            # Restore original value from backup on failure
            exec(&#34;self.%s = %s&#34; % (attr_name, backup))

            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))

    @staticmethod
    def _convert_size(input_size):
        &#34;&#34;&#34;Converts the given float size to appropriate size in B / KB / MB / GB, etc.

            Args:
                size (float)  --  float value to convert

            Returns:
                str - size converted to the specific type (B, KB, MB, GB, etc.)
        &#34;&#34;&#34;
        if input_size == 0:
            return &#39;0B&#39;

        size_name = (&#34;B&#34;, &#34;KB&#34;, &#34;MB&#34;, &#34;GB&#34;, &#34;TB&#34;, &#34;PB&#34;, &#34;EB&#34;, &#34;ZB&#34;, &#34;YB&#34;)
        i = int(math.floor(math.log(input_size, 1024)))
        power = math.pow(1024, i)
        size = round(input_size / power, 2)
        return &#39;%s %s&#39; % (size, size_name[i])

    def _process_update_response(self, flag, response):
        &#34;&#34;&#34;Updates the subclient properties with the request provided.

            Args:
                update_request  (str)  --  update request specifying the details to update

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if failed to update properties

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;response&#34; in response.json():
                    error_code = str(
                        response.json()[&#34;response&#34;][0][&#34;errorCode&#34;])

                    if error_code == &#34;0&#34;:
                        return (True, &#34;0&#34;, &#34;&#34;)
                    else:
                        error_message = &#34;&#34;

                        if &#34;errorString&#34; in response.json()[&#34;response&#34;][0]:
                            error_message = response.json(
                            )[&#34;response&#34;][0][&#34;errorString&#34;]

                        if error_message:
                            return (False, error_code, error_message)
                        else:
                            return (False, error_code, &#34;&#34;)
                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return (True, &#34;0&#34;, &#34;&#34;)

                    if error_message:
                        return (False, error_code, error_message)
                    else:
                        return (False, error_code, &#34;&#34;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def _process_backup_response(self, flag, response):
        &#34;&#34;&#34;Runs the Backup for a subclient with the request provided and returns the Job object.

            Args:
                update_request  (str)  --  update request specifying the details to update

            Returns:
                object - instance of the Job class for this backup job if its an immediate Job
                        instance of the Schedule class for the backup job if its a scheduled Job


            Raises:
                SDKException:
                    if job initialization failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    if len(response.json()[&#39;jobIds&#39;]) == 1:
                        return Job(self._commcell_object,
                                   response.json()[&#39;jobIds&#39;][0])
                    else:
                        joblist = []
                        for jobids in response.json()[&#39;jobIds&#39;]:
                            joblist.append(Job(self._commcell_object, jobids))
                        return joblist
                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])
                elif &#34;errorCode&#34; in response.json():
                    o_str = &#39;Initializing backup failed\nError: &#34;{0}&#34;&#39;.format(
                        response.json()[&#39;errorMessage&#39;]
                    )
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def _backup_json(self,
                     backup_level,
                     incremental_backup,
                     incremental_level,
                     advanced_options=None,
                     schedule_pattern=None,
                     common_backup_options=None):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                backup_level        (str)   --  level of backup the user wish to run

                    Full / Incremental / Differential / Synthetic_full

                incremental_backup  (bool)  --  run incremental backup

                    only applicable in case of Synthetic_full backup

                incremental_level   (str)   --  run incremental backup before/after synthetic full

                    BEFORE_SYNTH / AFTER_SYNTH

                    only applicable in case of Synthetic_full backup

                advanced_options   (dict)  --  advanced backup options to be included while
                making the request

                    default: None

                common_backup_options   (dict)  --  advanced job options to be included while
                                                    making the request.

                    default: None

            Returns:
                dict    -   JSON request to pass to the API

        &#34;&#34;&#34;

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._json_backup_subtasks,
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;backupLevel&#34;: backup_level,
                                &#34;incLevel&#34;: incremental_level,
                                &#34;runIncrementalBackup&#34;: incremental_backup
                            }
                        }
                    }
                ]
            }
        }

        advanced_options_dict = {}

        if advanced_options:
            if advanced_options.get(&#39;impersonate_gui&#39;):
                request_json[&#39;taskInfo&#39;][&#39;task&#39;][&#39;initiatedFrom&#39;] = 1
            advanced_options_dict = self._advanced_backup_options(
                advanced_options)

        if advanced_options_dict:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;].update(
                advanced_options_dict
            )

        advance_job_option_dict = {}

        if common_backup_options:
            advance_job_option_dict = self._common_backup_options(
                common_backup_options)

        if advance_job_option_dict:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;commonOpts&#34;] = advance_job_option_dict

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        return request_json

    def _common_backup_options(self, options):
        &#34;&#34;&#34;
         Generates the advanced job options dict

            Args:
                options     (dict)      --    advanced job options that are to be included
                                                    in the request

            Returns:
                (dict)  -           generated advanced job options dict
        &#34;&#34;&#34;
        return options

    def _advanced_backup_options(self, options):
        &#34;&#34;&#34;Generates the advanced backup options dict

            Args:
                options     (dict)  --  advanced backup options that are to be included
                                            in the request
c
            Returns:
                (dict)  -   generated advanced options dict
        &#34;&#34;&#34;
        return options

    def _process_index_delete_response(self, flag, response):
        &#34;&#34;&#34;Runs the Index Delete job for a subclient with the request provided and returns the Job object.

            Args:
                flag (bool)        --  flag parameter of the request to initiate an index delete job
                response (object)  --  response of the request to initiate an index delete job

            Returns:
                object - instance of the Job class for this index delete job

            Raises:
                SDKException:
                    if job initialization failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json().get(&#39;response&#39;, {}).get(&#39;errorString&#39;, str())
                        o_str = &#39;Failed to Delete Documents\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to Delete Documents\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#34;jobIds&#34;][0])
            return None
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;Updates the subclient properties

            Args:
                properties_dict (dict)  --  subclient property dict which is to be updated

            Returns:
                None

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected

        **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
        need to change and use the update_properties method to set the properties

        &#34;&#34;&#34;
        request_json = {
            &#34;subClientProperties&#34;: {}
        }

        request_json[&#39;subClientProperties&#39;].update(properties_dict)

        # check if subclient name is updated in the request
        # if subclient name is updated set the newName field in the request
        if properties_dict.get(&#39;subClientEntity&#39;, {}).get(&#39;subclientName&#39;) and properties_dict.get(
                &#39;subClientEntity&#39;, {}).get(&#39;subclientName&#39;) != self._subClientEntity.get(&#39;subclientName&#39;):
            request_json[&#39;newName&#39;] = properties_dict.get(&#39;subClientEntity&#39;, {}).get(&#39;subclientName&#39;)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUBCLIENT, request_json)
        status, _, error_string = self._process_update_response(flag, response)
        self.refresh()

        if not status:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to update subclient properties\nError: &#34;{}&#34;&#39;.format(
                error_string))

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the subclient properties&#34;&#34;&#34;
        return copy.deepcopy(self._subclient_properties)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the Subclient display name&#34;&#34;&#34;
        return self._subclient_properties[&#39;subClientEntity&#39;][&#39;subclientName&#39;]

    @property
    def display_name(self):
        &#34;&#34;&#34;Returns the Subclient display name&#34;&#34;&#34;
        return self.name

    @property
    def subclient_guid(self):
        &#34;&#34;&#34;Returns the SubclientGUID&#34;&#34;&#34;
        return self._subclient_properties.get(&#39;subClientEntity&#39;, {}).get(&#39;subclientGUID&#39;)

    @display_name.setter
    def display_name(self, display_name):
        &#34;&#34;&#34;Sets the display name for the subclient
        Args:
            display_name    (str)   -- Display name for the subclient

        &#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#39;subClientEntity&#39;][&#39;subclientName&#39;] = display_name
        self.update_properties(update_properties)

    @name.setter
    def name(self, name):
        &#34;&#34;&#34;Sets the name for the subclient
        Args:
            name    (str)   -- name for the subclient
        &#34;&#34;&#34;
        self.display_name = name

    @property
    def _json_task(self):
        &#34;&#34;&#34;getter for the task information in JSON&#34;&#34;&#34;

        _taks_option_json = {
            &#34;initiatedFrom&#34;: 2,
            &#34;taskType&#34;: 1,
            &#34;policyType&#34;: 0,
            &#34;taskFlags&#34;: {
                &#34;disabled&#34;: False
            }
        }

        return _taks_option_json

    @property
    def _json_backup_subtasks(self):
        &#34;&#34;&#34;getter for the subtask in restore JSON . It is read only attribute&#34;&#34;&#34;

        _backup_subtask = {
            &#34;subTaskType&#34;: 2,
            &#34;operationType&#34;: 2
        }

        return _backup_subtask

    @property
    def subclient_id(self):
        &#34;&#34;&#34;Treats the subclient id as a read-only attribute.&#34;&#34;&#34;
        return self._subclient_id

    @property
    def subclient_name(self):
        &#34;&#34;&#34;Treats the subclient name as a read-only attribute.&#34;&#34;&#34;
        return self._subclient_name

    @property
    def last_backup_time(self):
        &#34;&#34;&#34;Treats the last backup time as a read-only attribute.&#34;&#34;&#34;
        if &#39;lastBackupTime&#39; in self._commonProperties:
            if self._commonProperties[&#39;lastBackupTime&#39;] != 0:
                _last_backup_time = time.ctime(
                    self._commonProperties[&#39;lastBackupTime&#39;]
                )
                return _last_backup_time
        return 0

    @property
    def next_backup_time(self):
        &#34;&#34;&#34;Treats the next backup time as a read-only attribute.&#34;&#34;&#34;
        if &#39;nextBackupTime&#39; in self._commonProperties:
            if self._commonProperties[&#39;nextBackupTime&#39;] != 0:
                _next_backup = time.ctime(
                    self._commonProperties[&#39;nextBackupTime&#39;]
                )
                return _next_backup

    @property
    def is_backup_enabled(self):
        &#34;&#34;&#34;Treats the is backup enabled as a read-only attribute.&#34;&#34;&#34;
        if &#39;enableBackup&#39; in self._commonProperties:
            return self._commonProperties[&#39;enableBackup&#39;]

    @property
    def is_intelli_snap_enabled(self):
        &#34;&#34;&#34;Treats the is intelli snap enabled as a read-only attribute.&#34;&#34;&#34;
        if &#39;snapCopyInfo&#39; in self._commonProperties:
            snap_copy_info = self._commonProperties.get(&#39;snapCopyInfo&#39;)
            return snap_copy_info.get(&#39;isSnapBackupEnabled&#39;)

    @property
    def is_blocklevel_backup_enabled(self):
        &#34;&#34;&#34;returns True if block level backup is enabled else returns false&#34;&#34;&#34;
        return bool(self._subclient_properties.get(
            &#39;postgreSQLSubclientProp&#39;, {}).get(&#39;isUseBlockLevelBackup&#39;, False))

    @property
    def snapshot_engine_name(self):
        &#34;&#34;&#34;returns snapshot engine name associated with the subclient&#34;&#34;&#34;
        if self.is_intelli_snap_enabled:
            if &#39;snapCopyInfo&#39; in self._commonProperties:
                snap_copy_info = self._commonProperties.get(&#39;snapCopyInfo&#39;, &#34;&#34;)
                if &#39;snapToTapeSelectedEngine&#39; in snap_copy_info:
                    if &#39;snapShotEngineName&#39; in snap_copy_info.get(&#39;snapToTapeSelectedEngine&#39;, &#34;&#34;):
                        return snap_copy_info[&#39;snapToTapeSelectedEngine&#39;].get(
                            &#39;snapShotEngineName&#39;, &#34;&#34;)
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            &#39;Cannot fetch snap engine name.&#39;)

    @property
    def is_trueup_enabled(self):
        &#34;&#34;&#34;Treats the True up enabled as a property of the Subclient class.&#34;&#34;&#34;
        if &#39;isTrueUpOptionEnabled&#39; in self._commonProperties:
            return self._commonProperties[&#39;isTrueUpOptionEnabled&#39;]

    @property
    def is_on_demand_subclient(self):
        &#34;&#34;&#34;Treats the on demand subclient as a read-only attribute.&#34;&#34;&#34;
        return self._backupset_object.is_on_demand_backupset

    @property
    def description(self):
        &#34;&#34;&#34;Treats the subclient description as a property of the Subclient class.&#34;&#34;&#34;
        if &#39;description&#39; in self._commonProperties:
            return self._commonProperties[&#39;description&#39;]

    @property
    def storage_policy(self):
        &#34;&#34;&#34;Treats the subclient storage policy as a read-only attribute.&#34;&#34;&#34;
        storage_device = self._commonProperties[&#39;storageDevice&#39;]
        if &#39;dataBackupStoragePolicy&#39; in storage_device:
            data_backup_storage_policy = storage_device[&#39;dataBackupStoragePolicy&#39;]
            if &#39;storagePolicyName&#39; in data_backup_storage_policy:
                return data_backup_storage_policy[&#39;storagePolicyName&#39;]

    @property
    def storage_ma(self):
        &#34;&#34;&#34;Treats the subclient storage ma as a read-only attribute.&#34;&#34;&#34;
        storage_device = self._commonProperties[&#39;storageDevice&#39;]
        if &#39;performanceMode&#39; in storage_device:
            data_backup_storage_device = storage_device[&#39;performanceMode&#39;]
            data_storage_details = data_backup_storage_device[&#34;perfCRCDetails&#34;][0]
            if &#39;perfMa&#39; in data_storage_details:
                return data_storage_details[&#39;perfMa&#39;]

    @property
    def storage_ma_id(self):
        &#34;&#34;&#34;Treats the subclient storage ma id as a read-only attribute.&#34;&#34;&#34;
        storage_device = self._commonProperties[&#39;storageDevice&#39;]
        if &#39;performanceMode&#39; in storage_device:
            data_backup_storage_device = storage_device[&#39;performanceMode&#39;]
            data_storage_details = data_backup_storage_device[&#34;perfCRCDetails&#34;][0]
            if &#39;perfMaId&#39; in data_storage_details:
                return data_storage_details[&#39;perfMaId&#39;]

    @property
    def data_readers(self):
        &#34;&#34;&#34;Treats the data readers as a read-only attribute.&#34;&#34;&#34;
        if &#39;numberOfBackupStreams&#39; in self._commonProperties:
            return int(
                self._commonProperties[&#39;numberOfBackupStreams&#39;]
            )

    @data_readers.setter
    def data_readers(self, value):
        &#34;&#34;&#34;Sets the count of data readers for the subclient as the value provided as input.

            Raises:
                SDKException:
                    if failed to update number of data readers for subclient

                    if the type of value input is not int
        &#34;&#34;&#34;
        if isinstance(value, int):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;numberOfBackupStreams&#39;]&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient data readers should be an int value&#39;
            )

    @property
    def allow_multiple_readers(self):
        &#34;&#34;&#34;Treats the allow multiple readers as a read-only attribute.&#34;&#34;&#34;
        if &#39;allowMultipleDataReaders&#39; in self._commonProperties:
            return bool(
                self._commonProperties[&#39;allowMultipleDataReaders&#39;]
            )

    @allow_multiple_readers.setter
    def allow_multiple_readers(self, value):
        &#34;&#34;&#34;To enable or disable allow multiple readers property
        for the subclient based on the value provided as input.

            Raises:
                SDKException:
                    if failed to update allow multiple readers for subclient

                    if the type of value input is not bool
        &#34;&#34;&#34;
        # Has to be initialized for new subclient as attribute is not present
        # default value is False
        if &#39;allowMultipleDataReaders&#39; not in self._commonProperties:
            self._commonProperties[&#39;allowMultipleDataReaders&#39;] = False

        if isinstance(value, bool):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;allowMultipleDataReaders&#39;]&#34;,
                value)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;,
                &#39;Subclient allow multple readers should be a bool value&#39;
            )

    @property
    def read_buffer_size(self):
        &#34;&#34;&#34;Treats the read buffer size as a read-only attribute.&#34;&#34;&#34;
        if &#39;readBuffersize&#39; in self._commonProperties:
            return int(
                self._commonProperties[&#39;readBuffersize&#39;]
            )

    @property
    def is_default_subclient(self):
        &#34;&#34;&#34;Returns True if the subclient is default
        subclient else returns False&#34;&#34;&#34;
        return self._commonProperties.get(&#39;isDefaultSubclient&#39;)

    @read_buffer_size.setter
    def read_buffer_size(self, value):
        &#34;&#34;&#34;Sets the read buffer size for the subclient
        as the value provided as input.
            (value in KB)

            Raises:
                SDKException:
                    if failed to update read buffer size for subclient

                    if the type of value input is not int
        &#34;&#34;&#34;
        # Has to be initialized for new subclient as attribute is not present
        # default value is 0
        if &#39;readBuffersize&#39; not in self._commonProperties:
            self._commonProperties[&#39;readBuffersize&#39;] = 0

        if isinstance(value, int):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;readBuffersize&#39;]&#34;,
                value)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;,
                &#39;Subclient read buffer size should be an int value&#39;
            )

    @description.setter
    def description(self, value):
        &#34;&#34;&#34;Sets the description of the subclient as the value provided as input.

            Raises:
                SDKException:
                    if failed to update description of subclient

                    if the type of value input is not string
        &#34;&#34;&#34;
        if isinstance(value, str):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;description&#39;]&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient description should be a string value&#39;
            )

    @storage_policy.setter
    def storage_policy(self, value):
        &#34;&#34;&#34;Sets the storage policy of subclient as the value provided as input.

            Args:
                value   (str)   -- Storage policy name to be assigned to subclient

            Raises:
                SDKException:
                    if storage policy name is not in string format

                    if failed to update storage policy name

        &#34;&#34;&#34;
        if isinstance(value, str):
            value = value.lower()

            if not self._commcell_object.storage_policies.has_policy(value):
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(value)
                )

            self._set_subclient_properties(
                &#34;_commonProperties[&#39;storageDevice&#39;][&#39;dataBackupStoragePolicy&#39;]&#34;,
                {
                    &#34;storagePolicyName&#34;: value,
                    &#34;storagePolicyId&#34;: int(
                        self._commcell_object.storage_policies.all_storage_policies[value]
                    )
                }
            )
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    def enable_backup(self):
        &#34;&#34;&#34;Enables Backup for the subclient.

            Raises:
                SDKException:
                    if failed to enable backup of subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(&#34;_commonProperties[&#39;enableBackup&#39;]&#34;, True)

    def enable_trueup(self):
        &#34;&#34;&#34;Setter for the TrueUp Option for a Subclient&#34;&#34;&#34;
        if &#39;isTrueUpOptionEnabled&#39; in self._commonProperties:
            self._set_subclient_properties(&#34;_commonProperties[&#39;isTrueUpOptionEnabled&#39;]&#34;, True)

    def enable_trueup_days(self, days=30):
        &#34;&#34;&#34;Setter for the TrueUp Option with reconcile after x days&#34;&#34;&#34;
        self.enable_trueup()
        self._set_subclient_properties(&#34;_commonProperties[&#39;runTrueUpJobAfterDays&#39;]&#34;, days)

    def enable_backup_at_time(self, enable_time):
        &#34;&#34;&#34;Disables Backup if not already disabled, and enables at the time specified.

            Args:
                enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                    format: YYYY-MM-DD HH:mm:ss

                **Note** In case of linux CommServer provide time in GMT timezone

            Raises:
                SDKException:
                    if time value entered is less than the current time

                    if time value entered is not of correct format

                    if failed to enable backup

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        try:
            time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
            if time.mktime(time_tuple) &lt; time.time():
                raise SDKException(&#39;Subclient&#39;, &#39;108&#39;)
        except ValueError:
            raise SDKException(&#39;Subclient&#39;, &#39;109&#39;)

        enable_backup_at_time = {
            &#34;TimeZoneName&#34;: self._commcell_object.default_timezone,
            &#34;timeValue&#34;: enable_time
        }

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;enableBackupAtDateTime&#39;]&#34;, enable_backup_at_time
        )

    def disable_backup(self):
        &#34;&#34;&#34;Disables Backup for the subclient.

            Raises:
                SDKException:
                    if failed to disable backup of subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_commonProperties[&#39;enableBackup&#39;]&#34;, False)

    def exclude_from_sla(self):
        &#34;&#34;&#34;Exclude subclient from SLA.

            Raises:
                SDKException:
                    if failed to exclude the subclient from SLA
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_commonProperties[&#39;excludeFromSLA&#39;]&#34;, True)

    def enable_intelli_snap(self, snap_engine_name, proxy_options=None):
        &#34;&#34;&#34;Enables Intelli Snap for the subclient.

            Args:
                snap_engine_name    (str)   --  Snap Engine Name

            Raises:
                SDKException:
                    if failed to enable intelli snap for subclient
        &#34;&#34;&#34;
        if not isinstance(snap_engine_name, str):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

        properties_dict = {
            &#34;isSnapBackupEnabled&#34;: True,
            &#34;snapToTapeSelectedEngine&#34;: {
                &#34;snapShotEngineName&#34;: snap_engine_name
            }
        }

        if proxy_options is not None:
            if &#34;snap_proxy&#34; in proxy_options:
                properties_dict[&#34;snapToTapeProxyToUse&#34;] = {
                    &#34;clientName&#34;: proxy_options[&#34;snap_proxy&#34;]
                }

            if &#34;backupcopy_proxy&#34; in proxy_options:
                properties_dict[&#34;useSeparateProxyForSnapToTape&#34;] = True
                properties_dict[&#34;separateProxyForSnapToTape&#34;] = {
                    &#34;clientName&#34;: proxy_options[&#34;backupcopy_proxy&#34;]
                }

            if &#34;use_source_if_proxy_unreachable&#34; in proxy_options:
                properties_dict[&#34;snapToTapeProxyToUseSource&#34;] = True

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;snapCopyInfo&#39;]&#34;, properties_dict)

    def disable_intelli_snap(self):
        &#34;&#34;&#34;Disables Intelli Snap for the subclient.

            Raises:
                SDKException:
                    if failed to disable intelli snap for subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_commonProperties[&#39;snapCopyInfo&#39;][&#39;isSnapBackupEnabled&#39;]&#34;, False
        )

    def set_proxy_for_snap(self, proxy_name):
        &#34;&#34;&#34; method to set Use proxy option for intellisnap subclient 

        Args:
            proxy_name(str) -- Name of the proxy to be used

        &#34;&#34;&#34;
        if not isinstance(proxy_name, str):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

        properties_dict = {
            &#34;clientName&#34;: proxy_name
        }

        update_properties = self.properties
        update_properties[&#39;commonProperties&#39;][&#39;snapCopyInfo&#39;][&#39;snapToTapeProxyToUse&#39;] = properties_dict
        self.update_properties(update_properties)

    def unset_proxy_for_snap(self):
        &#34;&#34;&#34; method to unset Use proxy option for intellisnap subclient &#34;&#34;&#34;

        properties_dict = {
            &#34;clientId&#34;: 0
        }
        update_properties = self.properties
        update_properties[&#39;commonProperties&#39;][&#39;snapCopyInfo&#39;][&#39;snapToTapeProxyToUse&#39;] = properties_dict
        self.update_properties(update_properties)

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               collect_metadata=False):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level        (str)   --  level of backup the user wish to run
                        Full / Incremental / Differential / Synthetic_full
                    default: Incremental

                incremental_backup  (bool)  --  run incremental backup
                        only applicable in case of Synthetic_full backup
                    default: False

                incremental_level   (str)   --  run incremental backup before/after synthetic full
                        BEFORE_SYNTH / AFTER_SYNTH

                        only applicable in case of Synthetic_full backup
                    default: BEFORE_SYNTH

            Returns:
                object - instance of the Job class for this backup job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        backup_level = backup_level.lower()

        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;, &#39;transaction_log&#39;,
                                &#39;differential&#39;, &#39;synthetic_full&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

        backup_request = backup_level

        if backup_level == &#39;synthetic_full&#39;:
            if incremental_backup:
                backup_request += &#39;&amp;runIncrementalBackup=True&#39;
                backup_request += &#39;&amp;incrementalLevel=%s&#39; % (
                    incremental_level.lower())
            else:
                backup_request += &#39;&amp;runIncrementalBackup=False&#39;

        backup_request += &#39;&amp;collectMetaInfo=%s&#39; % collect_metadata

        backup_service = self._services[&#39;SUBCLIENT_BACKUP&#39;] % (
            self.subclient_id, backup_request)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service)

        return self._process_backup_response(flag, response)

    def get_ma_associated_storagepolicy(self):
        &#34;&#34;&#34;
        Get Media agents associated with storage policy
        
        Raise Exception:
                if unable to get MA names
        &#34;&#34;&#34;
        storage = self._subclient_properties[&#39;commonProperties&#39;][&#39;storageDevice&#39;]
        if &#39;performanceMode&#39; in storage:
            data_backup_storage_device = storage[&#39;performanceMode&#39;][&#34;perfCRCDetails&#34;]
            malist = []
            for each_ma in data_backup_storage_device:
                malist.append(each_ma[&#39;perfMa&#39;])
            return malist

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Subclient.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;_subclient_id&#39;] = self._subclient_id

        return self._backupset_object.browse(options)

    def find(self, *args, **kwargs):
        &#34;&#34;&#34;Searches a file/folder in the backed up content of the subclient,
            and returns all the files matching the filters given.

            Args:
                Dictionary of browse options:
                    Example:

                        find({
                            &#39;file_name&#39;: &#39;*.txt&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        find(
                            file_name=&#39;*.txt&#39;,

                            show_deleted=True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            Additional options supported:
                file_name       (str)   --  Find files with name

                file_size_gt    (int)   --  Find files with size greater than size

                file_size_lt    (int)   --  Find files with size lesser than size

                file_size_et    (int)   --  Find files with size equal to size

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;_subclient_id&#39;] = self._subclient_id

        return self._backupset_object.find(options)

    def list_media(self, *args, **kwargs):
        &#34;&#34;&#34;List media required to browse and restore backed up data from the subclient

            Args:
                Dictionary of options:
                    Example:

                        list_media({
                            &#39;path&#39;: &#39;c:\\hello&#39;,
                            &#39;show_deleted&#39;: True,
                            &#39;from_time&#39;: &#39;2020-04-20 12:00:00&#39;,
                            &#39;to_time&#39;: &#39;2021-04-19 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of options:
                    Example:

                        list_media(
                            path=&#39;c:\\hello&#39;,
                            show_deleted=True,
                            from_time=&#39;2020-04-20 12:00:00&#39;,
                            to_time=&#39;2021-04-19 12:00:00&#39;
                        )

            Note:
                Refer `_default_browse_options` in backupset.py for all the supported options.

            Returns:
                (List, Dict) -
                    List - List of all the media required for the given options

                    Dict - Total size of the media

            Raises:
                SDKException:
                    if failed to list media for content

                    if response is not success

        &#34;&#34;&#34;

        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;_subclient_id&#39;] = self._subclient_id

        return self._backupset_object.list_media(options)

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            advanced_options=None):

        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to restore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to restore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options
                    options:
                        all_versions        : if set to True restores all the versions of the
                                                specified file
                        versions            : list of version numbers to be backed up
                        validate_only       : To validate data backed up for restore


                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity

        return self._instance_object._restore_in_place(
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            advanced_options=advanced_options
        )

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            advanced_options=None):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to restore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to restore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options
                    options:
                        preserve_level      : preserve level option to set in restore
                        proxy_client        : proxy that needed to be used for restore
                        impersonate_user    : Impersonate user options for restore
                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form
                        all_versions        : if set to True restores all the versions of the
                                                specified file
                        versions            : list of version numbers to be backed up
                        media_agent         : Media Agent need to be used for Browse and restore
                        validate_only       : To validate data backed up for restore


                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity

        if fs_options and &#39;proxy_client&#39; in fs_options:
            proxy_client = fs_options[&#39;proxy_client&#39;]

        return self._instance_object._restore_out_of_place(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            advanced_options=advanced_options
        )

    def set_backup_nodes(self, data_access_nodes):
        &#34;&#34;&#34;Sets the the backup nodes for NFS share subclient.

            Args:
                data_access_nodes   (list)  --  the list of data access nodes to be set
                as backup nodes for NFS share subclient

            Returns:
                None    -   if the operation is successful

            Raises:
                SDKException:
                    if unable to update the backup nodes for the subclient

        &#34;&#34;&#34;
        data_access_nodes_json = []
        for access_node in data_access_nodes:
            data_access_nodes_json.append({&#34;clientName&#34;: access_node})

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;fsSubClientProp&#34;: {
                    &#34;backupConfiguration&#34;: {
                        &#34;backupDataAccessNodes&#34;: data_access_nodes_json
                    }
                }
            }
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUBCLIENT, request_json)

        output = self._process_update_response(flag, response)

        if output[0]:
            return
        else:
            o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))

    def find_latest_job(
            self,
            include_active=True,
            include_finished=True,
            lookup_time=1,
            job_filter=&#39;Backup,SYNTHFULL&#39;):
        &#34;&#34;&#34;Finds the latest job for the subclient
            which includes current running job also.

            Args:
                include_active    (bool)    -- to indicate if
                                                active jobs should be included
                    default: True

                include_finished  (bool)    -- to indicate if finished jobs
                                                should be included
                    default: True

                lookup_time       (int)     -- get jobs executed
                                                within the number of hours
                    default: 1 Hour

                job_filter        (str)     -- to specify type of job
                    default: &#39;Backup,SYNTHFULL&#39;

                    for multiple filters,
                    give the values **comma(,)** separated

                    List of Possible Values:

                            Backup

                            Restore

                            AUXCOPY

                            WORKFLOW

                            etc..

                    http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                        to get the complete list of filters available

            Returns:
                object  -   instance of the Job class for the latest job

            Raises:
                SDKException:
                    if any error occurred while finding the latest job.

        &#34;&#34;&#34;
        job_controller = JobController(self._commcell_object)
        entity_dict = {
            &#34;subclientId&#34;: int(self.subclient_id)
        }
        if include_active and include_finished:
            client_jobs = job_controller.all_jobs(
                client_name=self._client_object.client_name,
                lookup_time=lookup_time,
                job_filter=job_filter,
                entity=entity_dict
            )
        elif include_active:
            client_jobs = job_controller.active_jobs(
                client_name=self._client_object.client_name,
                lookup_time=lookup_time,
                job_filter=job_filter,
                entity=entity_dict
            )
        elif include_finished:
            client_jobs = job_controller.finished_jobs(
                client_name=self._client_object.client_name,
                lookup_time=lookup_time,
                job_filter=job_filter,
                entity=entity_dict
            )
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#34;Either active or finished job must be included&#34;
            )

        latest_jobid = 0
        for job in client_jobs:
            if client_jobs[job][&#39;subclient_id&#39;] == int(self._subclient_id):
                if int(job) &gt; latest_jobid:
                    latest_jobid = int(job)

        if latest_jobid == 0:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;No jobs found&#34;)

        return Job(self._commcell_object, latest_jobid)

    def run_content_indexing(self,
                                   pick_failed_items=False,
                                   pick_only_failed_items=False,
                                   streams=4,
                                   proxies=None):
        &#34;&#34;&#34;Run content Indexing on job.

            Args:
               pick_failed_items
                        default:False   (bool)  --  Pick fail items during Content Indexing

                pick_only_failed_items  (bool)  --  Pick only fail items during Content
                                                    Indeixng
                    default: False

                streams                 (int)   --  Streams for Content Indexing job

                    default: 4

                proxies                 (list) --  provide the proxies to run CI
                    default: None

            Returns:
                object - instance of the Job class for the ContentIndexing job

        &#34;&#34;&#34;
        if not (isinstance(pick_failed_items, bool) and
                isinstance(pick_only_failed_items, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if proxies is None:
            proxies = {}

        self._media_option_json = {
            &#34;pickFailedItems&#34;: pick_failed_items,
            &#34;pickFailedItemsOnly&#34;: pick_only_failed_items,
            &#34;auxcopyJobOption&#34;: {
                &#34;maxNumberOfStreams&#34;: streams,
                &#34;allCopies&#34;: True,
                &#34;useMaximumStreams&#34;: False,
                &#34;proxies&#34;: proxies
            }
        }

        self._content_indexing_option_json= {
            &#34;reanalyze&#34;: False,
            &#34;fileAnalytics&#34;: False,
            &#34;subClientBasedAnalytics&#34;: False
        }
        self._subtask_restore_json = {
            &#34;subTaskType&#34;: 1,
            &#34;operationType&#34;: 5020
        }

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._subtask_restore_json,
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: self._media_option_json
                            },
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: self._content_indexing_option_json
                            },
                            &#34;restoreOptions&#34;: {
                                &#34;virtualServerRstOption&#34;: {
                                    &#34;isBlockLevelReplication&#34;: False
                                },
                                &#34;browseOption&#34;: {
                                    &#34;backupset&#34;: {}
                                }
                            }
                        }
                    }
                ]
            }
        }

        return self._process_restore_response(request_json)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Subclient.&#34;&#34;&#34;
        self._get_subclient_properties()
        self.schedules = Schedules(self)

    @property
    def software_compression(self):
        &#34;&#34;&#34;Returns the value of Software Compression settings on the Subclient.&#34;&#34;&#34;
        mapping = {
            0: &#39;ON_CLIENT&#39;,
            1: &#39;ON_MEDIAAGENT&#39;,
            2: &#39;USE_STORAGE_POLICY_SETTINGS&#39;,
            4: &#39;OFF&#39;
        }

        try:
            return mapping[self._commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]]
        except KeyError:
            return self._commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]

    @software_compression.setter
    def software_compression(self, value):
        &#34;&#34;&#34;Sets the software compression of the subclient as the value provided as input.

            Args:
                value   (str)   --  software compression setting

                Valid values are:

                -   ON_CLIENT
                -   ON_MEDIAAGENT
                -   USE_STORAGE_POLICY_SETTINGS
                -   OFF

            Raises:
                SDKException:
                    if failed to update software compression of subclient

                    if the type of value input is not string

        &#34;&#34;&#34;
        if isinstance(value, str):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]&#34;, value
            )
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    @property
    def network_agent(self):
        &#34;&#34;&#34;Returns the value of network agents setting on the Subclient.&#34;&#34;&#34;
        return self._commonProperties[&#39;storageDevice&#39;][&#39;networkAgents&#39;]

    @network_agent.setter
    def network_agent(self, value):
        &#34;&#34;&#34;Sets the network agents of the subclient as the value provided as input.

            Args:
                value   (int)   --  network agents value

            Raises:
                SDKException:
                    if failed to update network agents of subclient

                    if the type of value input is not integer

        &#34;&#34;&#34;

        if isinstance(value, int):
            self._set_subclient_properties(&#34;_commonProperties[&#39;storageDevice&#39;][&#39;networkAgents&#39;]&#34;, value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    @property
    def encryption_flag(self):
        &#34;&#34;&#34;Returns the value of encryption flag settings on the Subclient.&#34;&#34;&#34;
        mapping = {
            0: &#39;ENC_NONE&#39;,
            1: &#39;ENC_MEDIA_ONLY&#39;,
            2: &#39;ENC_NETWORK_AND_MEDIA&#39;,
            3: &#39;ENC_NETWORK_ONLY&#39;
        }

        try:
            return mapping[self._commonProperties[&#39;encryptionFlag&#39;]]
        except KeyError:
            return self._commonProperties[&#39;encryptionFlag&#39;]

    @encryption_flag.setter
    def encryption_flag(self, value):
        &#34;&#34;&#34;Sets the encryption Flag of the subclient as the value provided as input.

            Args:
                value   (str)   --  encryption flag value

                Valid values are:

                -   ENC_NONE
                -   ENC_MEDIA_ONLY
                -   ENC_NETWORK_AND_MEDIA
                -   ENC_NETWORK_ONLY

            Raises:
                SDKException:
                    if failed to update encryption Flag of subclient

                    if the type of value input is not string

        &#34;&#34;&#34;

        if isinstance(value, str):
            self._set_subclient_properties(&#34;_commonProperties[&#39;encryptionFlag&#39;]&#34;, value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    @property
    def deduplication_options(self):
        &#34;&#34;&#34;Returns the value of deduplication options settings on the Subclient.&#34;&#34;&#34;
        mapping_dedupe = {
            0: False,
            1: True,
        }
        mapping_signature = {
            1: &#34;ON_CLIENT&#34;,
            2: &#34;ON_MEDIA_AGENT&#34;
        }

        dedupe_options = self._commonProperties[&#39;storageDevice&#39;][&#39;deDuplicationOptions&#39;]

        if &#34;enableDeduplication&#34; in dedupe_options:
            if dedupe_options[&#39;enableDeduplication&#39;] == 0:
                return mapping_dedupe[dedupe_options[&#39;enableDeduplication&#39;]]
            else:
                if &#39;generateSignature&#39; in dedupe_options:
                    try:
                        return mapping_signature[dedupe_options[&#39;generateSignature&#39;]]
                    except KeyError:
                        return dedupe_options[&#39;generateSignature&#39;]

    @deduplication_options.setter
    def deduplication_options(self, enable_dedupe):
        &#34;&#34;&#34;Enables / Disables the deduplication options of the Subclient.

            Args:
                enable_dedupe   (tuple)     --  to enable or disable deduplication

                    tuple:
                        **bool**    -   boolean flag to specify whether to
                        enable / disable deduplication

                        **str**     -   where to generate the signature at

                            Valid Values are:

                            -   ON_CLIENT
                            -   ON_MEDIA_AGENT


                    e.g.:

                        &gt;&gt;&gt; subclient.deduplication_options = (False, None)

                        &gt;&gt;&gt; subclient.deduplication_options = (True, &#34;ON_CLIENT&#34;)

                        &gt;&gt;&gt; subclient.deduplication_options = (True, &#34;ON_MEDIA_AGENT&#34;)

            Raises:
                SDKException:
                    if failed to update deDuplication Options of subclient

                    if the type of value input is not correct

        &#34;&#34;&#34;
        if enable_dedupe[0] is True:
            if enable_dedupe[1] is not None:
                self._set_subclient_properties(
                    &#34;_commonProperties[&#39;storageDevice&#39;][&#39;deDuplicationOptions&#39;]&#34;,
                    {
                        &#34;enableDeduplication&#34;: enable_dedupe[0],
                        &#34;generateSignature&#34;: enable_dedupe[1]
                    }
                )
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;Input data is not correct&#34;)
        else:
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;storageDevice&#39;][&#39;deDuplicationOptions&#39;]&#34;,
                {
                    &#34;enableDeduplication&#34;: enable_dedupe[0],
                }
            )

    @property
    def plan(self):
        &#34;&#34;&#34;Returns the name of the plan associated with the subclient.
           Returns None if no plan is associated
        &#34;&#34;&#34;

        if &#39;planEntity&#39; in self._subclient_properties:
            planEntity = self._subclient_properties[&#39;planEntity&#39;]

            if bool(planEntity) and &#39;planName&#39; in planEntity:
                return planEntity[&#39;planName&#39;]
            else:
                return None
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;112&#39;)

    @plan.setter
    def plan(self, value):
        &#34;&#34;&#34;Associates a plan to the subclient.

            Args:
                value   (object)    --  the Plan object which is to be associated
                                        with the subclient

                value   (str)       --  name of the plan to be associated

                value   (None)      --  set value to None to remove plan associations
                

            Raises:
                SDKException:
                    if the type of input is incorrect

                    if the plan association is unsuccessful
        &#34;&#34;&#34;
        from .plan import Plan
        if isinstance(value, Plan):
            if self._commcell_object.plans.has_plan(value.plan_name):
                self.update_properties({
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value.plan_name
                    },
                    &#34;useContentFromPlan&#34;: True
                })
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Plan does not exist&#39;)
        elif isinstance(value, str):
            if self._commcell_object.plans.has_plan(value):
                self.update_properties({
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value
                    },
                    &#34;useContentFromPlan&#34;: True
                })
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Plan does not exist&#39;)
                
        elif value is None:
            self.update_properties({
                &#39;removePlanAssociation&#39;: True
            })
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    def _get_preview_metadata(self, file_path):
        &#34;&#34;&#34;Gets the preview metadata for the subclient.
            params:
                file_path (str) : file path for which preview metadata is required

            Returns:
                metadata   (dict)   --  metadata content of the preview

                None - if file not found

        &#34;&#34;&#34;

        paths, data = self.find()

        for path in paths:
            if path.lower() == file_path.lower():
                return data[path]
        else:
            return None

    def _get_preview(self, file_path):
        &#34;&#34;&#34;Gets the preview content for the subclient.
            Params:
                file_path (str) --  file path to get the preview content

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        metadata = self._get_preview_metadata(file_path)
        if metadata is None:
            raise SDKException(&#39;Subclient&#39;, &#39;123&#39;)

        if metadata[&#34;type&#34;] != &#34;File&#34;:
            raise SDKException(&#39;Subclient&#39;, &#39;124&#39;)

        if metadata[&#34;size&#34;] == 0:
            raise SDKException(&#39;Subclient&#39;, &#39;125&#39;)

        if metadata[&#34;size&#34;] &gt; 20 * 1024 * 1024:
            raise SDKException(&#39;Subclient&#39;, &#39;126&#39;)

        request_json = {
            &#34;filters&#34;: [
                {
                    &#34;field&#34;: &#34;CONTENTID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [

                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;COMMCELL_NUMBER&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;advConfig&#34;][&#34;browseAdvancedConfigResp&#34;][
                                    &#34;commcellNumber&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;CLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;sourceCommServer&#34;][&#34;commCellId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;SUBCLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;subclient&#34;][&#34;applicationId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;MODIFIED_TIME&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            metadata[&#34;modified_time&#34;]
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_PATH&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            file_path
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_SIZE&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;size&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;archiveFileId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_OFFSET&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;offset&#34;])
                        ]
                    }
                }
            ]
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._PREVIEW_CONTENT, request_json)

        if flag:
            if &#34;Preview not available&#34; not in response.text:
                return response.text
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;127&#39;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, self._update_response_(response.text))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclient.Subclient"><code class="flex name class">
<span>class <span class="ident">Subclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Base class consisting of all the common properties and operations for a Subclient</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L1458-L3582" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Subclient(object):
    &#34;&#34;&#34;Base class consisting of all the common properties and operations for a Subclient&#34;&#34;&#34;

    def __new__(cls, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Class composition for CV subclients&#34;&#34;&#34;
        from .subclients.fssubclient import FileSystemSubclient
        from .subclients.bigdataappssubclient import BigDataAppsSubclient
        from .subclients.vssubclient import VirtualServerSubclient
        from .subclients.casubclient import CloudAppsSubclient
        from .subclients.sqlsubclient import SQLServerSubclient
        from .subclients.nassubclient import NASSubclient
        from .subclients.hanasubclient import SAPHANASubclient
        from .subclients.oraclesubclient import OracleSubclient
        from .subclients.lotusnotes.lndbsubclient import LNDbSubclient
        from .subclients.lotusnotes.lndocsubclient import LNDocSubclient
        from .subclients.lotusnotes.lndmsubclient import LNDmSubclient
        from .subclients.sybasesubclient import SybaseSubclient
        from .subclients.saporaclesubclient import SAPOracleSubclient
        from .subclients.exchsubclient import ExchangeSubclient
        from .subclients.mysqlsubclient import MYSQLSubclient
        from .subclients.exchange.exchange_database_subclient import ExchangeDatabaseSubclient
        from .subclients.postgressubclient import PostgresSubclient
        from .subclients.informixsubclient import InformixSubclient
        from .subclients.adsubclient import ADSubclient
        from .subclients.sharepointsubclient import SharepointSubclient
        from .subclients.sharepointsubclient import SharepointV1Subclient
        from .subclients.vminstancesubclient import VMInstanceSubclient
        from .subclients.db2subclient import DB2Subclient
        from .subclients.casesubclient import CaseSubclient
        from .subclients.aadsubclient import AzureAdSubclient

        # add the agent name to this dict, and its class as the value
        # the appropriate class object will be initialized based on the agent
        _subclients_dict = {
            &#39;big data apps&#39;: BigDataAppsSubclient,
            &#39;file system&#39;: FileSystemSubclient,
            &#39;virtual server&#39;: [VirtualServerSubclient, VMInstanceSubclient],
            &#39;cloud apps&#39;: CloudAppsSubclient,
            &#39;sql server&#39;: SQLServerSubclient,
            &#39;nas&#39;: NASSubclient,  # SP11 or lower CS honors NAS as the Agent Name
            &#39;ndmp&#39;: NASSubclient,  # SP12 and above honors NDMP as the Agent Name
            &#39;sap hana&#39;: SAPHANASubclient,
            &#39;oracle&#39;: OracleSubclient,
            &#39;oracle rac&#39;: OracleSubclient,
            &#39;notes database&#39;: LNDbSubclient,
            &#39;notes document&#39;: LNDocSubclient,
            &#39;domino mailbox archiver&#39;: LNDmSubclient,
            &#39;sybase&#39;: SybaseSubclient,
            &#39;sap for oracle&#39;: SAPOracleSubclient,
            &#34;exchange mailbox&#34;: [ExchangeSubclient, CaseSubclient],
            &#39;mysql&#39;: MYSQLSubclient,
            &#39;exchange database&#39;: ExchangeDatabaseSubclient,
            &#39;postgresql&#39;: PostgresSubclient,
            &#39;db2&#39;: DB2Subclient,
            &#39;informix&#39;: InformixSubclient,
            &#39;active directory&#39;: ADSubclient,
            &#39;sharepoint server&#39;: [SharepointV1Subclient, SharepointSubclient],
            &#34;azure ad&#34;: AzureAdSubclient
        }

        agent_object = backupset_object._agent_object
        instance_object = backupset_object._instance_object
        client_object = agent_object._client_object

        agent_name = agent_object.agent_name.lower()

        if isinstance(_subclients_dict.get(agent_name), list):
            if instance_object.instance_name == &#34;vminstance&#34;:
                _class = _subclients_dict[agent_name][-1]
            elif client_object.client_type and int(client_object.client_type) == 36:
                # client type 36 is case manager client
                _class = _subclients_dict[agent_name][-1]
            elif int(agent_object.agent_id) == 78 and client_object.client_type:
                # agent id 78 is sharepoint client
                _class = _subclients_dict[agent_name][-1]
            else:
                _class = _subclients_dict[agent_name][0]
        else:
            _class = _subclients_dict.get(agent_name, cls)

        if _class.__new__ == cls.__new__:
            return object.__new__(_class)
        return _class.__new__(_class, backupset_object, subclient_name, subclient_id)

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialise the Subclient object.

            Args:
                backupset_object (object)  --  instance of the Backupset class

                subclient_name   (str)     --  name of the subclient

                subclient_id     (str)     --  id of the subclient
                    default: None

            Returns:
                object - instance of the Subclient class
        &#34;&#34;&#34;
        self._backupset_object = backupset_object
        self._subclient_name = subclient_name.split(&#39;\\&#39;)[-1].lower()

        self._commcell_object = self._backupset_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._instance_object = self._backupset_object._instance_object
        self._agent_object = self._backupset_object._agent_object
        self._client_object = self._agent_object._client_object

        self._restore_methods = [
            &#39;_process_restore_response&#39;,
            &#39;_filter_paths&#39;,
            &#39;_process_search_response&#39;,
            &#39;_restore_json&#39;,
            &#39;_impersonation_json&#39;,
            &#39;_restore_browse_option_json&#39;,
            &#39;_restore_common_options_json&#39;,
            &#39;_restore_destination_json&#39;,
            &#39;_restore_fileoption_json&#39;,
            &#39;_json_restore_subtask&#39;
        ]

        self._restore_options_json = [
            &#39;_impersonation_json_&#39;,
            &#39;_browse_restore_json&#39;,
            &#39;_destination_restore_json&#39;,
            &#39;_commonoption_restore_json&#39;,
            &#39;_fileoption_restore_json&#39;,

        ]

        self._backupcopy_interfaces = {
            &#39;FILESYSTEM&#39;: 1,
            &#39;RMAN&#39;: 2,
            &#39;VOLUME&#39;: 3
        }

        if subclient_id:
            self._subclient_id = str(subclient_id)
        else:
            self._subclient_id = self._get_subclient_id()

        self._SUBCLIENT = self._services[&#39;SUBCLIENT&#39;] % (self.subclient_id)

        self._BROWSE = self._services[&#39;BROWSE&#39;]

        self._RESTORE = self._services[&#39;RESTORE&#39;]

        self._PREVIEW_CONTENT = self._services[&#39;GET_DOC_PREVIEW&#39;]

        self._subclient_properties = {}
        self._content = []

        self.schedules = None
        self.refresh()

    def __getattr__(self, attribute):
        &#34;&#34;&#34;Returns the persistent attributes&#34;&#34;&#34;
        if attribute in self._restore_methods:
            return getattr(self._backupset_object, attribute)
        if attribute in self._restore_options_json:
            return getattr(self._backupset_object, attribute)

        return super(Subclient, self).__getattribute__(attribute)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Subclient class instance for Subclient: &#34;{0}&#34; of Backupset: &#34;{1}&#34;&#39;
        return representation_string.format(
            self.subclient_name, self._backupset_object.backupset_name
        )

    def _get_subclient_id(self):
        &#34;&#34;&#34;Gets the subclient id associated to the specified backupset name and client name.

            Returns:
                str - id associated with this subclient
        &#34;&#34;&#34;
        subclients = Subclients(self._backupset_object)
        return subclients.get(self.subclient_name).subclient_id

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient properties of this subclient.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SUBCLIENT)

        if flag:
            if response.json() and &#39;subClientProperties&#39; in response.json():
                self._subclient_properties = response.json()[
                    &#39;subClientProperties&#39;][0]

                if &#39;commonProperties&#39; in self._subclient_properties:
                    self._commonProperties = self._subclient_properties[&#39;commonProperties&#39;]

                if &#39;subClientEntity&#39; in self._subclient_properties:
                    self._subClientEntity = self._subclient_properties[&#39;subClientEntity&#39;]

                if &#39;proxyClient&#39; in self._subclient_properties:
                    self._proxyClient = self._subclient_properties[&#39;proxyClient&#39;]

                if &#39;planEntity&#39; in self._subclient_properties:
                    self._planEntity = self._subclient_properties[&#39;planEntity&#39;]

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def _set_subclient_properties(self, attr_name, value):
        &#34;&#34;&#34;sets the properties of this sub client.value is updated to instance once when post call
            succeeds

            Args:
                attr_name (str) --  Name of the attribute. This should be an instance variable.
                value (str)     --  Value of the attribute. This should be an instance variable.

            Raises:
                SDKException:
                    if failed to update number properties for subclient


        &#34;&#34;&#34;
        try:
            backup = eval(&#39;self.%s&#39; % attr_name)  # Take backup of old value
        except (AttributeError, KeyError):
            backup = None

        exec(&#34;self.%s = %s&#34; % (attr_name, &#39;value&#39;))  # set new value

        request_json = self._get_subclient_properties_json()

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUBCLIENT, request_json)

        output = self._process_update_response(flag, response)

        if output[0]:
            return
        else:
            o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;

            # Restore original value from backup on failure
            exec(&#34;self.%s = %s&#34; % (attr_name, backup))

            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))

    @staticmethod
    def _convert_size(input_size):
        &#34;&#34;&#34;Converts the given float size to appropriate size in B / KB / MB / GB, etc.

            Args:
                size (float)  --  float value to convert

            Returns:
                str - size converted to the specific type (B, KB, MB, GB, etc.)
        &#34;&#34;&#34;
        if input_size == 0:
            return &#39;0B&#39;

        size_name = (&#34;B&#34;, &#34;KB&#34;, &#34;MB&#34;, &#34;GB&#34;, &#34;TB&#34;, &#34;PB&#34;, &#34;EB&#34;, &#34;ZB&#34;, &#34;YB&#34;)
        i = int(math.floor(math.log(input_size, 1024)))
        power = math.pow(1024, i)
        size = round(input_size / power, 2)
        return &#39;%s %s&#39; % (size, size_name[i])

    def _process_update_response(self, flag, response):
        &#34;&#34;&#34;Updates the subclient properties with the request provided.

            Args:
                update_request  (str)  --  update request specifying the details to update

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if failed to update properties

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;response&#34; in response.json():
                    error_code = str(
                        response.json()[&#34;response&#34;][0][&#34;errorCode&#34;])

                    if error_code == &#34;0&#34;:
                        return (True, &#34;0&#34;, &#34;&#34;)
                    else:
                        error_message = &#34;&#34;

                        if &#34;errorString&#34; in response.json()[&#34;response&#34;][0]:
                            error_message = response.json(
                            )[&#34;response&#34;][0][&#34;errorString&#34;]

                        if error_message:
                            return (False, error_code, error_message)
                        else:
                            return (False, error_code, &#34;&#34;)
                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return (True, &#34;0&#34;, &#34;&#34;)

                    if error_message:
                        return (False, error_code, error_message)
                    else:
                        return (False, error_code, &#34;&#34;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def _process_backup_response(self, flag, response):
        &#34;&#34;&#34;Runs the Backup for a subclient with the request provided and returns the Job object.

            Args:
                update_request  (str)  --  update request specifying the details to update

            Returns:
                object - instance of the Job class for this backup job if its an immediate Job
                        instance of the Schedule class for the backup job if its a scheduled Job


            Raises:
                SDKException:
                    if job initialization failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    if len(response.json()[&#39;jobIds&#39;]) == 1:
                        return Job(self._commcell_object,
                                   response.json()[&#39;jobIds&#39;][0])
                    else:
                        joblist = []
                        for jobids in response.json()[&#39;jobIds&#39;]:
                            joblist.append(Job(self._commcell_object, jobids))
                        return joblist
                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])
                elif &#34;errorCode&#34; in response.json():
                    o_str = &#39;Initializing backup failed\nError: &#34;{0}&#34;&#39;.format(
                        response.json()[&#39;errorMessage&#39;]
                    )
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def _backup_json(self,
                     backup_level,
                     incremental_backup,
                     incremental_level,
                     advanced_options=None,
                     schedule_pattern=None,
                     common_backup_options=None):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                backup_level        (str)   --  level of backup the user wish to run

                    Full / Incremental / Differential / Synthetic_full

                incremental_backup  (bool)  --  run incremental backup

                    only applicable in case of Synthetic_full backup

                incremental_level   (str)   --  run incremental backup before/after synthetic full

                    BEFORE_SYNTH / AFTER_SYNTH

                    only applicable in case of Synthetic_full backup

                advanced_options   (dict)  --  advanced backup options to be included while
                making the request

                    default: None

                common_backup_options   (dict)  --  advanced job options to be included while
                                                    making the request.

                    default: None

            Returns:
                dict    -   JSON request to pass to the API

        &#34;&#34;&#34;

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._json_backup_subtasks,
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;backupLevel&#34;: backup_level,
                                &#34;incLevel&#34;: incremental_level,
                                &#34;runIncrementalBackup&#34;: incremental_backup
                            }
                        }
                    }
                ]
            }
        }

        advanced_options_dict = {}

        if advanced_options:
            if advanced_options.get(&#39;impersonate_gui&#39;):
                request_json[&#39;taskInfo&#39;][&#39;task&#39;][&#39;initiatedFrom&#39;] = 1
            advanced_options_dict = self._advanced_backup_options(
                advanced_options)

        if advanced_options_dict:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;].update(
                advanced_options_dict
            )

        advance_job_option_dict = {}

        if common_backup_options:
            advance_job_option_dict = self._common_backup_options(
                common_backup_options)

        if advance_job_option_dict:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;commonOpts&#34;] = advance_job_option_dict

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        return request_json

    def _common_backup_options(self, options):
        &#34;&#34;&#34;
         Generates the advanced job options dict

            Args:
                options     (dict)      --    advanced job options that are to be included
                                                    in the request

            Returns:
                (dict)  -           generated advanced job options dict
        &#34;&#34;&#34;
        return options

    def _advanced_backup_options(self, options):
        &#34;&#34;&#34;Generates the advanced backup options dict

            Args:
                options     (dict)  --  advanced backup options that are to be included
                                            in the request
c
            Returns:
                (dict)  -   generated advanced options dict
        &#34;&#34;&#34;
        return options

    def _process_index_delete_response(self, flag, response):
        &#34;&#34;&#34;Runs the Index Delete job for a subclient with the request provided and returns the Job object.

            Args:
                flag (bool)        --  flag parameter of the request to initiate an index delete job
                response (object)  --  response of the request to initiate an index delete job

            Returns:
                object - instance of the Job class for this index delete job

            Raises:
                SDKException:
                    if job initialization failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json().get(&#39;response&#39;, {}).get(&#39;errorString&#39;, str())
                        o_str = &#39;Failed to Delete Documents\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to Delete Documents\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#34;jobIds&#34;][0])
            return None
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;Updates the subclient properties

            Args:
                properties_dict (dict)  --  subclient property dict which is to be updated

            Returns:
                None

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected

        **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
        need to change and use the update_properties method to set the properties

        &#34;&#34;&#34;
        request_json = {
            &#34;subClientProperties&#34;: {}
        }

        request_json[&#39;subClientProperties&#39;].update(properties_dict)

        # check if subclient name is updated in the request
        # if subclient name is updated set the newName field in the request
        if properties_dict.get(&#39;subClientEntity&#39;, {}).get(&#39;subclientName&#39;) and properties_dict.get(
                &#39;subClientEntity&#39;, {}).get(&#39;subclientName&#39;) != self._subClientEntity.get(&#39;subclientName&#39;):
            request_json[&#39;newName&#39;] = properties_dict.get(&#39;subClientEntity&#39;, {}).get(&#39;subclientName&#39;)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUBCLIENT, request_json)
        status, _, error_string = self._process_update_response(flag, response)
        self.refresh()

        if not status:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to update subclient properties\nError: &#34;{}&#34;&#39;.format(
                error_string))

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the subclient properties&#34;&#34;&#34;
        return copy.deepcopy(self._subclient_properties)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the Subclient display name&#34;&#34;&#34;
        return self._subclient_properties[&#39;subClientEntity&#39;][&#39;subclientName&#39;]

    @property
    def display_name(self):
        &#34;&#34;&#34;Returns the Subclient display name&#34;&#34;&#34;
        return self.name

    @property
    def subclient_guid(self):
        &#34;&#34;&#34;Returns the SubclientGUID&#34;&#34;&#34;
        return self._subclient_properties.get(&#39;subClientEntity&#39;, {}).get(&#39;subclientGUID&#39;)

    @display_name.setter
    def display_name(self, display_name):
        &#34;&#34;&#34;Sets the display name for the subclient
        Args:
            display_name    (str)   -- Display name for the subclient

        &#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#39;subClientEntity&#39;][&#39;subclientName&#39;] = display_name
        self.update_properties(update_properties)

    @name.setter
    def name(self, name):
        &#34;&#34;&#34;Sets the name for the subclient
        Args:
            name    (str)   -- name for the subclient
        &#34;&#34;&#34;
        self.display_name = name

    @property
    def _json_task(self):
        &#34;&#34;&#34;getter for the task information in JSON&#34;&#34;&#34;

        _taks_option_json = {
            &#34;initiatedFrom&#34;: 2,
            &#34;taskType&#34;: 1,
            &#34;policyType&#34;: 0,
            &#34;taskFlags&#34;: {
                &#34;disabled&#34;: False
            }
        }

        return _taks_option_json

    @property
    def _json_backup_subtasks(self):
        &#34;&#34;&#34;getter for the subtask in restore JSON . It is read only attribute&#34;&#34;&#34;

        _backup_subtask = {
            &#34;subTaskType&#34;: 2,
            &#34;operationType&#34;: 2
        }

        return _backup_subtask

    @property
    def subclient_id(self):
        &#34;&#34;&#34;Treats the subclient id as a read-only attribute.&#34;&#34;&#34;
        return self._subclient_id

    @property
    def subclient_name(self):
        &#34;&#34;&#34;Treats the subclient name as a read-only attribute.&#34;&#34;&#34;
        return self._subclient_name

    @property
    def last_backup_time(self):
        &#34;&#34;&#34;Treats the last backup time as a read-only attribute.&#34;&#34;&#34;
        if &#39;lastBackupTime&#39; in self._commonProperties:
            if self._commonProperties[&#39;lastBackupTime&#39;] != 0:
                _last_backup_time = time.ctime(
                    self._commonProperties[&#39;lastBackupTime&#39;]
                )
                return _last_backup_time
        return 0

    @property
    def next_backup_time(self):
        &#34;&#34;&#34;Treats the next backup time as a read-only attribute.&#34;&#34;&#34;
        if &#39;nextBackupTime&#39; in self._commonProperties:
            if self._commonProperties[&#39;nextBackupTime&#39;] != 0:
                _next_backup = time.ctime(
                    self._commonProperties[&#39;nextBackupTime&#39;]
                )
                return _next_backup

    @property
    def is_backup_enabled(self):
        &#34;&#34;&#34;Treats the is backup enabled as a read-only attribute.&#34;&#34;&#34;
        if &#39;enableBackup&#39; in self._commonProperties:
            return self._commonProperties[&#39;enableBackup&#39;]

    @property
    def is_intelli_snap_enabled(self):
        &#34;&#34;&#34;Treats the is intelli snap enabled as a read-only attribute.&#34;&#34;&#34;
        if &#39;snapCopyInfo&#39; in self._commonProperties:
            snap_copy_info = self._commonProperties.get(&#39;snapCopyInfo&#39;)
            return snap_copy_info.get(&#39;isSnapBackupEnabled&#39;)

    @property
    def is_blocklevel_backup_enabled(self):
        &#34;&#34;&#34;returns True if block level backup is enabled else returns false&#34;&#34;&#34;
        return bool(self._subclient_properties.get(
            &#39;postgreSQLSubclientProp&#39;, {}).get(&#39;isUseBlockLevelBackup&#39;, False))

    @property
    def snapshot_engine_name(self):
        &#34;&#34;&#34;returns snapshot engine name associated with the subclient&#34;&#34;&#34;
        if self.is_intelli_snap_enabled:
            if &#39;snapCopyInfo&#39; in self._commonProperties:
                snap_copy_info = self._commonProperties.get(&#39;snapCopyInfo&#39;, &#34;&#34;)
                if &#39;snapToTapeSelectedEngine&#39; in snap_copy_info:
                    if &#39;snapShotEngineName&#39; in snap_copy_info.get(&#39;snapToTapeSelectedEngine&#39;, &#34;&#34;):
                        return snap_copy_info[&#39;snapToTapeSelectedEngine&#39;].get(
                            &#39;snapShotEngineName&#39;, &#34;&#34;)
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            &#39;Cannot fetch snap engine name.&#39;)

    @property
    def is_trueup_enabled(self):
        &#34;&#34;&#34;Treats the True up enabled as a property of the Subclient class.&#34;&#34;&#34;
        if &#39;isTrueUpOptionEnabled&#39; in self._commonProperties:
            return self._commonProperties[&#39;isTrueUpOptionEnabled&#39;]

    @property
    def is_on_demand_subclient(self):
        &#34;&#34;&#34;Treats the on demand subclient as a read-only attribute.&#34;&#34;&#34;
        return self._backupset_object.is_on_demand_backupset

    @property
    def description(self):
        &#34;&#34;&#34;Treats the subclient description as a property of the Subclient class.&#34;&#34;&#34;
        if &#39;description&#39; in self._commonProperties:
            return self._commonProperties[&#39;description&#39;]

    @property
    def storage_policy(self):
        &#34;&#34;&#34;Treats the subclient storage policy as a read-only attribute.&#34;&#34;&#34;
        storage_device = self._commonProperties[&#39;storageDevice&#39;]
        if &#39;dataBackupStoragePolicy&#39; in storage_device:
            data_backup_storage_policy = storage_device[&#39;dataBackupStoragePolicy&#39;]
            if &#39;storagePolicyName&#39; in data_backup_storage_policy:
                return data_backup_storage_policy[&#39;storagePolicyName&#39;]

    @property
    def storage_ma(self):
        &#34;&#34;&#34;Treats the subclient storage ma as a read-only attribute.&#34;&#34;&#34;
        storage_device = self._commonProperties[&#39;storageDevice&#39;]
        if &#39;performanceMode&#39; in storage_device:
            data_backup_storage_device = storage_device[&#39;performanceMode&#39;]
            data_storage_details = data_backup_storage_device[&#34;perfCRCDetails&#34;][0]
            if &#39;perfMa&#39; in data_storage_details:
                return data_storage_details[&#39;perfMa&#39;]

    @property
    def storage_ma_id(self):
        &#34;&#34;&#34;Treats the subclient storage ma id as a read-only attribute.&#34;&#34;&#34;
        storage_device = self._commonProperties[&#39;storageDevice&#39;]
        if &#39;performanceMode&#39; in storage_device:
            data_backup_storage_device = storage_device[&#39;performanceMode&#39;]
            data_storage_details = data_backup_storage_device[&#34;perfCRCDetails&#34;][0]
            if &#39;perfMaId&#39; in data_storage_details:
                return data_storage_details[&#39;perfMaId&#39;]

    @property
    def data_readers(self):
        &#34;&#34;&#34;Treats the data readers as a read-only attribute.&#34;&#34;&#34;
        if &#39;numberOfBackupStreams&#39; in self._commonProperties:
            return int(
                self._commonProperties[&#39;numberOfBackupStreams&#39;]
            )

    @data_readers.setter
    def data_readers(self, value):
        &#34;&#34;&#34;Sets the count of data readers for the subclient as the value provided as input.

            Raises:
                SDKException:
                    if failed to update number of data readers for subclient

                    if the type of value input is not int
        &#34;&#34;&#34;
        if isinstance(value, int):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;numberOfBackupStreams&#39;]&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient data readers should be an int value&#39;
            )

    @property
    def allow_multiple_readers(self):
        &#34;&#34;&#34;Treats the allow multiple readers as a read-only attribute.&#34;&#34;&#34;
        if &#39;allowMultipleDataReaders&#39; in self._commonProperties:
            return bool(
                self._commonProperties[&#39;allowMultipleDataReaders&#39;]
            )

    @allow_multiple_readers.setter
    def allow_multiple_readers(self, value):
        &#34;&#34;&#34;To enable or disable allow multiple readers property
        for the subclient based on the value provided as input.

            Raises:
                SDKException:
                    if failed to update allow multiple readers for subclient

                    if the type of value input is not bool
        &#34;&#34;&#34;
        # Has to be initialized for new subclient as attribute is not present
        # default value is False
        if &#39;allowMultipleDataReaders&#39; not in self._commonProperties:
            self._commonProperties[&#39;allowMultipleDataReaders&#39;] = False

        if isinstance(value, bool):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;allowMultipleDataReaders&#39;]&#34;,
                value)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;,
                &#39;Subclient allow multple readers should be a bool value&#39;
            )

    @property
    def read_buffer_size(self):
        &#34;&#34;&#34;Treats the read buffer size as a read-only attribute.&#34;&#34;&#34;
        if &#39;readBuffersize&#39; in self._commonProperties:
            return int(
                self._commonProperties[&#39;readBuffersize&#39;]
            )

    @property
    def is_default_subclient(self):
        &#34;&#34;&#34;Returns True if the subclient is default
        subclient else returns False&#34;&#34;&#34;
        return self._commonProperties.get(&#39;isDefaultSubclient&#39;)

    @read_buffer_size.setter
    def read_buffer_size(self, value):
        &#34;&#34;&#34;Sets the read buffer size for the subclient
        as the value provided as input.
            (value in KB)

            Raises:
                SDKException:
                    if failed to update read buffer size for subclient

                    if the type of value input is not int
        &#34;&#34;&#34;
        # Has to be initialized for new subclient as attribute is not present
        # default value is 0
        if &#39;readBuffersize&#39; not in self._commonProperties:
            self._commonProperties[&#39;readBuffersize&#39;] = 0

        if isinstance(value, int):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;readBuffersize&#39;]&#34;,
                value)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;,
                &#39;Subclient read buffer size should be an int value&#39;
            )

    @description.setter
    def description(self, value):
        &#34;&#34;&#34;Sets the description of the subclient as the value provided as input.

            Raises:
                SDKException:
                    if failed to update description of subclient

                    if the type of value input is not string
        &#34;&#34;&#34;
        if isinstance(value, str):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;description&#39;]&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient description should be a string value&#39;
            )

    @storage_policy.setter
    def storage_policy(self, value):
        &#34;&#34;&#34;Sets the storage policy of subclient as the value provided as input.

            Args:
                value   (str)   -- Storage policy name to be assigned to subclient

            Raises:
                SDKException:
                    if storage policy name is not in string format

                    if failed to update storage policy name

        &#34;&#34;&#34;
        if isinstance(value, str):
            value = value.lower()

            if not self._commcell_object.storage_policies.has_policy(value):
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(value)
                )

            self._set_subclient_properties(
                &#34;_commonProperties[&#39;storageDevice&#39;][&#39;dataBackupStoragePolicy&#39;]&#34;,
                {
                    &#34;storagePolicyName&#34;: value,
                    &#34;storagePolicyId&#34;: int(
                        self._commcell_object.storage_policies.all_storage_policies[value]
                    )
                }
            )
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    def enable_backup(self):
        &#34;&#34;&#34;Enables Backup for the subclient.

            Raises:
                SDKException:
                    if failed to enable backup of subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(&#34;_commonProperties[&#39;enableBackup&#39;]&#34;, True)

    def enable_trueup(self):
        &#34;&#34;&#34;Setter for the TrueUp Option for a Subclient&#34;&#34;&#34;
        if &#39;isTrueUpOptionEnabled&#39; in self._commonProperties:
            self._set_subclient_properties(&#34;_commonProperties[&#39;isTrueUpOptionEnabled&#39;]&#34;, True)

    def enable_trueup_days(self, days=30):
        &#34;&#34;&#34;Setter for the TrueUp Option with reconcile after x days&#34;&#34;&#34;
        self.enable_trueup()
        self._set_subclient_properties(&#34;_commonProperties[&#39;runTrueUpJobAfterDays&#39;]&#34;, days)

    def enable_backup_at_time(self, enable_time):
        &#34;&#34;&#34;Disables Backup if not already disabled, and enables at the time specified.

            Args:
                enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                    format: YYYY-MM-DD HH:mm:ss

                **Note** In case of linux CommServer provide time in GMT timezone

            Raises:
                SDKException:
                    if time value entered is less than the current time

                    if time value entered is not of correct format

                    if failed to enable backup

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        try:
            time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
            if time.mktime(time_tuple) &lt; time.time():
                raise SDKException(&#39;Subclient&#39;, &#39;108&#39;)
        except ValueError:
            raise SDKException(&#39;Subclient&#39;, &#39;109&#39;)

        enable_backup_at_time = {
            &#34;TimeZoneName&#34;: self._commcell_object.default_timezone,
            &#34;timeValue&#34;: enable_time
        }

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;enableBackupAtDateTime&#39;]&#34;, enable_backup_at_time
        )

    def disable_backup(self):
        &#34;&#34;&#34;Disables Backup for the subclient.

            Raises:
                SDKException:
                    if failed to disable backup of subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_commonProperties[&#39;enableBackup&#39;]&#34;, False)

    def exclude_from_sla(self):
        &#34;&#34;&#34;Exclude subclient from SLA.

            Raises:
                SDKException:
                    if failed to exclude the subclient from SLA
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_commonProperties[&#39;excludeFromSLA&#39;]&#34;, True)

    def enable_intelli_snap(self, snap_engine_name, proxy_options=None):
        &#34;&#34;&#34;Enables Intelli Snap for the subclient.

            Args:
                snap_engine_name    (str)   --  Snap Engine Name

            Raises:
                SDKException:
                    if failed to enable intelli snap for subclient
        &#34;&#34;&#34;
        if not isinstance(snap_engine_name, str):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

        properties_dict = {
            &#34;isSnapBackupEnabled&#34;: True,
            &#34;snapToTapeSelectedEngine&#34;: {
                &#34;snapShotEngineName&#34;: snap_engine_name
            }
        }

        if proxy_options is not None:
            if &#34;snap_proxy&#34; in proxy_options:
                properties_dict[&#34;snapToTapeProxyToUse&#34;] = {
                    &#34;clientName&#34;: proxy_options[&#34;snap_proxy&#34;]
                }

            if &#34;backupcopy_proxy&#34; in proxy_options:
                properties_dict[&#34;useSeparateProxyForSnapToTape&#34;] = True
                properties_dict[&#34;separateProxyForSnapToTape&#34;] = {
                    &#34;clientName&#34;: proxy_options[&#34;backupcopy_proxy&#34;]
                }

            if &#34;use_source_if_proxy_unreachable&#34; in proxy_options:
                properties_dict[&#34;snapToTapeProxyToUseSource&#34;] = True

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;snapCopyInfo&#39;]&#34;, properties_dict)

    def disable_intelli_snap(self):
        &#34;&#34;&#34;Disables Intelli Snap for the subclient.

            Raises:
                SDKException:
                    if failed to disable intelli snap for subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_commonProperties[&#39;snapCopyInfo&#39;][&#39;isSnapBackupEnabled&#39;]&#34;, False
        )

    def set_proxy_for_snap(self, proxy_name):
        &#34;&#34;&#34; method to set Use proxy option for intellisnap subclient 

        Args:
            proxy_name(str) -- Name of the proxy to be used

        &#34;&#34;&#34;
        if not isinstance(proxy_name, str):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

        properties_dict = {
            &#34;clientName&#34;: proxy_name
        }

        update_properties = self.properties
        update_properties[&#39;commonProperties&#39;][&#39;snapCopyInfo&#39;][&#39;snapToTapeProxyToUse&#39;] = properties_dict
        self.update_properties(update_properties)

    def unset_proxy_for_snap(self):
        &#34;&#34;&#34; method to unset Use proxy option for intellisnap subclient &#34;&#34;&#34;

        properties_dict = {
            &#34;clientId&#34;: 0
        }
        update_properties = self.properties
        update_properties[&#39;commonProperties&#39;][&#39;snapCopyInfo&#39;][&#39;snapToTapeProxyToUse&#39;] = properties_dict
        self.update_properties(update_properties)

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               collect_metadata=False):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level        (str)   --  level of backup the user wish to run
                        Full / Incremental / Differential / Synthetic_full
                    default: Incremental

                incremental_backup  (bool)  --  run incremental backup
                        only applicable in case of Synthetic_full backup
                    default: False

                incremental_level   (str)   --  run incremental backup before/after synthetic full
                        BEFORE_SYNTH / AFTER_SYNTH

                        only applicable in case of Synthetic_full backup
                    default: BEFORE_SYNTH

            Returns:
                object - instance of the Job class for this backup job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        backup_level = backup_level.lower()

        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;, &#39;transaction_log&#39;,
                                &#39;differential&#39;, &#39;synthetic_full&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

        backup_request = backup_level

        if backup_level == &#39;synthetic_full&#39;:
            if incremental_backup:
                backup_request += &#39;&amp;runIncrementalBackup=True&#39;
                backup_request += &#39;&amp;incrementalLevel=%s&#39; % (
                    incremental_level.lower())
            else:
                backup_request += &#39;&amp;runIncrementalBackup=False&#39;

        backup_request += &#39;&amp;collectMetaInfo=%s&#39; % collect_metadata

        backup_service = self._services[&#39;SUBCLIENT_BACKUP&#39;] % (
            self.subclient_id, backup_request)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service)

        return self._process_backup_response(flag, response)

    def get_ma_associated_storagepolicy(self):
        &#34;&#34;&#34;
        Get Media agents associated with storage policy
        
        Raise Exception:
                if unable to get MA names
        &#34;&#34;&#34;
        storage = self._subclient_properties[&#39;commonProperties&#39;][&#39;storageDevice&#39;]
        if &#39;performanceMode&#39; in storage:
            data_backup_storage_device = storage[&#39;performanceMode&#39;][&#34;perfCRCDetails&#34;]
            malist = []
            for each_ma in data_backup_storage_device:
                malist.append(each_ma[&#39;perfMa&#39;])
            return malist

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Subclient.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;_subclient_id&#39;] = self._subclient_id

        return self._backupset_object.browse(options)

    def find(self, *args, **kwargs):
        &#34;&#34;&#34;Searches a file/folder in the backed up content of the subclient,
            and returns all the files matching the filters given.

            Args:
                Dictionary of browse options:
                    Example:

                        find({
                            &#39;file_name&#39;: &#39;*.txt&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        find(
                            file_name=&#39;*.txt&#39;,

                            show_deleted=True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            Additional options supported:
                file_name       (str)   --  Find files with name

                file_size_gt    (int)   --  Find files with size greater than size

                file_size_lt    (int)   --  Find files with size lesser than size

                file_size_et    (int)   --  Find files with size equal to size

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;_subclient_id&#39;] = self._subclient_id

        return self._backupset_object.find(options)

    def list_media(self, *args, **kwargs):
        &#34;&#34;&#34;List media required to browse and restore backed up data from the subclient

            Args:
                Dictionary of options:
                    Example:

                        list_media({
                            &#39;path&#39;: &#39;c:\\hello&#39;,
                            &#39;show_deleted&#39;: True,
                            &#39;from_time&#39;: &#39;2020-04-20 12:00:00&#39;,
                            &#39;to_time&#39;: &#39;2021-04-19 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of options:
                    Example:

                        list_media(
                            path=&#39;c:\\hello&#39;,
                            show_deleted=True,
                            from_time=&#39;2020-04-20 12:00:00&#39;,
                            to_time=&#39;2021-04-19 12:00:00&#39;
                        )

            Note:
                Refer `_default_browse_options` in backupset.py for all the supported options.

            Returns:
                (List, Dict) -
                    List - List of all the media required for the given options

                    Dict - Total size of the media

            Raises:
                SDKException:
                    if failed to list media for content

                    if response is not success

        &#34;&#34;&#34;

        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;_subclient_id&#39;] = self._subclient_id

        return self._backupset_object.list_media(options)

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            advanced_options=None):

        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to restore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to restore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options
                    options:
                        all_versions        : if set to True restores all the versions of the
                                                specified file
                        versions            : list of version numbers to be backed up
                        validate_only       : To validate data backed up for restore


                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity

        return self._instance_object._restore_in_place(
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            advanced_options=advanced_options
        )

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            advanced_options=None):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to restore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to restore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options
                    options:
                        preserve_level      : preserve level option to set in restore
                        proxy_client        : proxy that needed to be used for restore
                        impersonate_user    : Impersonate user options for restore
                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form
                        all_versions        : if set to True restores all the versions of the
                                                specified file
                        versions            : list of version numbers to be backed up
                        media_agent         : Media Agent need to be used for Browse and restore
                        validate_only       : To validate data backed up for restore


                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity

        if fs_options and &#39;proxy_client&#39; in fs_options:
            proxy_client = fs_options[&#39;proxy_client&#39;]

        return self._instance_object._restore_out_of_place(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            advanced_options=advanced_options
        )

    def set_backup_nodes(self, data_access_nodes):
        &#34;&#34;&#34;Sets the the backup nodes for NFS share subclient.

            Args:
                data_access_nodes   (list)  --  the list of data access nodes to be set
                as backup nodes for NFS share subclient

            Returns:
                None    -   if the operation is successful

            Raises:
                SDKException:
                    if unable to update the backup nodes for the subclient

        &#34;&#34;&#34;
        data_access_nodes_json = []
        for access_node in data_access_nodes:
            data_access_nodes_json.append({&#34;clientName&#34;: access_node})

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;fsSubClientProp&#34;: {
                    &#34;backupConfiguration&#34;: {
                        &#34;backupDataAccessNodes&#34;: data_access_nodes_json
                    }
                }
            }
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUBCLIENT, request_json)

        output = self._process_update_response(flag, response)

        if output[0]:
            return
        else:
            o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))

    def find_latest_job(
            self,
            include_active=True,
            include_finished=True,
            lookup_time=1,
            job_filter=&#39;Backup,SYNTHFULL&#39;):
        &#34;&#34;&#34;Finds the latest job for the subclient
            which includes current running job also.

            Args:
                include_active    (bool)    -- to indicate if
                                                active jobs should be included
                    default: True

                include_finished  (bool)    -- to indicate if finished jobs
                                                should be included
                    default: True

                lookup_time       (int)     -- get jobs executed
                                                within the number of hours
                    default: 1 Hour

                job_filter        (str)     -- to specify type of job
                    default: &#39;Backup,SYNTHFULL&#39;

                    for multiple filters,
                    give the values **comma(,)** separated

                    List of Possible Values:

                            Backup

                            Restore

                            AUXCOPY

                            WORKFLOW

                            etc..

                    http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                        to get the complete list of filters available

            Returns:
                object  -   instance of the Job class for the latest job

            Raises:
                SDKException:
                    if any error occurred while finding the latest job.

        &#34;&#34;&#34;
        job_controller = JobController(self._commcell_object)
        entity_dict = {
            &#34;subclientId&#34;: int(self.subclient_id)
        }
        if include_active and include_finished:
            client_jobs = job_controller.all_jobs(
                client_name=self._client_object.client_name,
                lookup_time=lookup_time,
                job_filter=job_filter,
                entity=entity_dict
            )
        elif include_active:
            client_jobs = job_controller.active_jobs(
                client_name=self._client_object.client_name,
                lookup_time=lookup_time,
                job_filter=job_filter,
                entity=entity_dict
            )
        elif include_finished:
            client_jobs = job_controller.finished_jobs(
                client_name=self._client_object.client_name,
                lookup_time=lookup_time,
                job_filter=job_filter,
                entity=entity_dict
            )
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#34;Either active or finished job must be included&#34;
            )

        latest_jobid = 0
        for job in client_jobs:
            if client_jobs[job][&#39;subclient_id&#39;] == int(self._subclient_id):
                if int(job) &gt; latest_jobid:
                    latest_jobid = int(job)

        if latest_jobid == 0:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;No jobs found&#34;)

        return Job(self._commcell_object, latest_jobid)

    def run_content_indexing(self,
                                   pick_failed_items=False,
                                   pick_only_failed_items=False,
                                   streams=4,
                                   proxies=None):
        &#34;&#34;&#34;Run content Indexing on job.

            Args:
               pick_failed_items
                        default:False   (bool)  --  Pick fail items during Content Indexing

                pick_only_failed_items  (bool)  --  Pick only fail items during Content
                                                    Indeixng
                    default: False

                streams                 (int)   --  Streams for Content Indexing job

                    default: 4

                proxies                 (list) --  provide the proxies to run CI
                    default: None

            Returns:
                object - instance of the Job class for the ContentIndexing job

        &#34;&#34;&#34;
        if not (isinstance(pick_failed_items, bool) and
                isinstance(pick_only_failed_items, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if proxies is None:
            proxies = {}

        self._media_option_json = {
            &#34;pickFailedItems&#34;: pick_failed_items,
            &#34;pickFailedItemsOnly&#34;: pick_only_failed_items,
            &#34;auxcopyJobOption&#34;: {
                &#34;maxNumberOfStreams&#34;: streams,
                &#34;allCopies&#34;: True,
                &#34;useMaximumStreams&#34;: False,
                &#34;proxies&#34;: proxies
            }
        }

        self._content_indexing_option_json= {
            &#34;reanalyze&#34;: False,
            &#34;fileAnalytics&#34;: False,
            &#34;subClientBasedAnalytics&#34;: False
        }
        self._subtask_restore_json = {
            &#34;subTaskType&#34;: 1,
            &#34;operationType&#34;: 5020
        }

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._subtask_restore_json,
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: self._media_option_json
                            },
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: self._content_indexing_option_json
                            },
                            &#34;restoreOptions&#34;: {
                                &#34;virtualServerRstOption&#34;: {
                                    &#34;isBlockLevelReplication&#34;: False
                                },
                                &#34;browseOption&#34;: {
                                    &#34;backupset&#34;: {}
                                }
                            }
                        }
                    }
                ]
            }
        }

        return self._process_restore_response(request_json)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Subclient.&#34;&#34;&#34;
        self._get_subclient_properties()
        self.schedules = Schedules(self)

    @property
    def software_compression(self):
        &#34;&#34;&#34;Returns the value of Software Compression settings on the Subclient.&#34;&#34;&#34;
        mapping = {
            0: &#39;ON_CLIENT&#39;,
            1: &#39;ON_MEDIAAGENT&#39;,
            2: &#39;USE_STORAGE_POLICY_SETTINGS&#39;,
            4: &#39;OFF&#39;
        }

        try:
            return mapping[self._commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]]
        except KeyError:
            return self._commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]

    @software_compression.setter
    def software_compression(self, value):
        &#34;&#34;&#34;Sets the software compression of the subclient as the value provided as input.

            Args:
                value   (str)   --  software compression setting

                Valid values are:

                -   ON_CLIENT
                -   ON_MEDIAAGENT
                -   USE_STORAGE_POLICY_SETTINGS
                -   OFF

            Raises:
                SDKException:
                    if failed to update software compression of subclient

                    if the type of value input is not string

        &#34;&#34;&#34;
        if isinstance(value, str):
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]&#34;, value
            )
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    @property
    def network_agent(self):
        &#34;&#34;&#34;Returns the value of network agents setting on the Subclient.&#34;&#34;&#34;
        return self._commonProperties[&#39;storageDevice&#39;][&#39;networkAgents&#39;]

    @network_agent.setter
    def network_agent(self, value):
        &#34;&#34;&#34;Sets the network agents of the subclient as the value provided as input.

            Args:
                value   (int)   --  network agents value

            Raises:
                SDKException:
                    if failed to update network agents of subclient

                    if the type of value input is not integer

        &#34;&#34;&#34;

        if isinstance(value, int):
            self._set_subclient_properties(&#34;_commonProperties[&#39;storageDevice&#39;][&#39;networkAgents&#39;]&#34;, value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    @property
    def encryption_flag(self):
        &#34;&#34;&#34;Returns the value of encryption flag settings on the Subclient.&#34;&#34;&#34;
        mapping = {
            0: &#39;ENC_NONE&#39;,
            1: &#39;ENC_MEDIA_ONLY&#39;,
            2: &#39;ENC_NETWORK_AND_MEDIA&#39;,
            3: &#39;ENC_NETWORK_ONLY&#39;
        }

        try:
            return mapping[self._commonProperties[&#39;encryptionFlag&#39;]]
        except KeyError:
            return self._commonProperties[&#39;encryptionFlag&#39;]

    @encryption_flag.setter
    def encryption_flag(self, value):
        &#34;&#34;&#34;Sets the encryption Flag of the subclient as the value provided as input.

            Args:
                value   (str)   --  encryption flag value

                Valid values are:

                -   ENC_NONE
                -   ENC_MEDIA_ONLY
                -   ENC_NETWORK_AND_MEDIA
                -   ENC_NETWORK_ONLY

            Raises:
                SDKException:
                    if failed to update encryption Flag of subclient

                    if the type of value input is not string

        &#34;&#34;&#34;

        if isinstance(value, str):
            self._set_subclient_properties(&#34;_commonProperties[&#39;encryptionFlag&#39;]&#34;, value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    @property
    def deduplication_options(self):
        &#34;&#34;&#34;Returns the value of deduplication options settings on the Subclient.&#34;&#34;&#34;
        mapping_dedupe = {
            0: False,
            1: True,
        }
        mapping_signature = {
            1: &#34;ON_CLIENT&#34;,
            2: &#34;ON_MEDIA_AGENT&#34;
        }

        dedupe_options = self._commonProperties[&#39;storageDevice&#39;][&#39;deDuplicationOptions&#39;]

        if &#34;enableDeduplication&#34; in dedupe_options:
            if dedupe_options[&#39;enableDeduplication&#39;] == 0:
                return mapping_dedupe[dedupe_options[&#39;enableDeduplication&#39;]]
            else:
                if &#39;generateSignature&#39; in dedupe_options:
                    try:
                        return mapping_signature[dedupe_options[&#39;generateSignature&#39;]]
                    except KeyError:
                        return dedupe_options[&#39;generateSignature&#39;]

    @deduplication_options.setter
    def deduplication_options(self, enable_dedupe):
        &#34;&#34;&#34;Enables / Disables the deduplication options of the Subclient.

            Args:
                enable_dedupe   (tuple)     --  to enable or disable deduplication

                    tuple:
                        **bool**    -   boolean flag to specify whether to
                        enable / disable deduplication

                        **str**     -   where to generate the signature at

                            Valid Values are:

                            -   ON_CLIENT
                            -   ON_MEDIA_AGENT


                    e.g.:

                        &gt;&gt;&gt; subclient.deduplication_options = (False, None)

                        &gt;&gt;&gt; subclient.deduplication_options = (True, &#34;ON_CLIENT&#34;)

                        &gt;&gt;&gt; subclient.deduplication_options = (True, &#34;ON_MEDIA_AGENT&#34;)

            Raises:
                SDKException:
                    if failed to update deDuplication Options of subclient

                    if the type of value input is not correct

        &#34;&#34;&#34;
        if enable_dedupe[0] is True:
            if enable_dedupe[1] is not None:
                self._set_subclient_properties(
                    &#34;_commonProperties[&#39;storageDevice&#39;][&#39;deDuplicationOptions&#39;]&#34;,
                    {
                        &#34;enableDeduplication&#34;: enable_dedupe[0],
                        &#34;generateSignature&#34;: enable_dedupe[1]
                    }
                )
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;Input data is not correct&#34;)
        else:
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;storageDevice&#39;][&#39;deDuplicationOptions&#39;]&#34;,
                {
                    &#34;enableDeduplication&#34;: enable_dedupe[0],
                }
            )

    @property
    def plan(self):
        &#34;&#34;&#34;Returns the name of the plan associated with the subclient.
           Returns None if no plan is associated
        &#34;&#34;&#34;

        if &#39;planEntity&#39; in self._subclient_properties:
            planEntity = self._subclient_properties[&#39;planEntity&#39;]

            if bool(planEntity) and &#39;planName&#39; in planEntity:
                return planEntity[&#39;planName&#39;]
            else:
                return None
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;112&#39;)

    @plan.setter
    def plan(self, value):
        &#34;&#34;&#34;Associates a plan to the subclient.

            Args:
                value   (object)    --  the Plan object which is to be associated
                                        with the subclient

                value   (str)       --  name of the plan to be associated

                value   (None)      --  set value to None to remove plan associations
                

            Raises:
                SDKException:
                    if the type of input is incorrect

                    if the plan association is unsuccessful
        &#34;&#34;&#34;
        from .plan import Plan
        if isinstance(value, Plan):
            if self._commcell_object.plans.has_plan(value.plan_name):
                self.update_properties({
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value.plan_name
                    },
                    &#34;useContentFromPlan&#34;: True
                })
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Plan does not exist&#39;)
        elif isinstance(value, str):
            if self._commcell_object.plans.has_plan(value):
                self.update_properties({
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value
                    },
                    &#34;useContentFromPlan&#34;: True
                })
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Plan does not exist&#39;)
                
        elif value is None:
            self.update_properties({
                &#39;removePlanAssociation&#39;: True
            })
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    def _get_preview_metadata(self, file_path):
        &#34;&#34;&#34;Gets the preview metadata for the subclient.
            params:
                file_path (str) : file path for which preview metadata is required

            Returns:
                metadata   (dict)   --  metadata content of the preview

                None - if file not found

        &#34;&#34;&#34;

        paths, data = self.find()

        for path in paths:
            if path.lower() == file_path.lower():
                return data[path]
        else:
            return None

    def _get_preview(self, file_path):
        &#34;&#34;&#34;Gets the preview content for the subclient.
            Params:
                file_path (str) --  file path to get the preview content

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        metadata = self._get_preview_metadata(file_path)
        if metadata is None:
            raise SDKException(&#39;Subclient&#39;, &#39;123&#39;)

        if metadata[&#34;type&#34;] != &#34;File&#34;:
            raise SDKException(&#39;Subclient&#39;, &#39;124&#39;)

        if metadata[&#34;size&#34;] == 0:
            raise SDKException(&#39;Subclient&#39;, &#39;125&#39;)

        if metadata[&#34;size&#34;] &gt; 20 * 1024 * 1024:
            raise SDKException(&#39;Subclient&#39;, &#39;126&#39;)

        request_json = {
            &#34;filters&#34;: [
                {
                    &#34;field&#34;: &#34;CONTENTID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [

                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;COMMCELL_NUMBER&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;advConfig&#34;][&#34;browseAdvancedConfigResp&#34;][
                                    &#34;commcellNumber&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;CLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;sourceCommServer&#34;][&#34;commCellId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;SUBCLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;subclient&#34;][&#34;applicationId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;MODIFIED_TIME&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            metadata[&#34;modified_time&#34;]
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_PATH&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            file_path
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_SIZE&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;size&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;archiveFileId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_OFFSET&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;offset&#34;])
                        ]
                    }
                }
            ]
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._PREVIEW_CONTENT, request_json)

        if flag:
            if &#34;Preview not available&#34; not in response.text:
                return response.text
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;127&#39;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, self._update_response_(response.text))</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.aadsubclient.AzureAdSubclient" href="subclients/aadsubclient.html#cvpysdk.subclients.aadsubclient.AzureAdSubclient">AzureAdSubclient</a></li>
<li><a title="cvpysdk.subclients.adsubclient.ADSubclient" href="subclients/adsubclient.html#cvpysdk.subclients.adsubclient.ADSubclient">ADSubclient</a></li>
<li><a title="cvpysdk.subclients.casesubclient.CaseSubclient" href="subclients/casesubclient.html#cvpysdk.subclients.casesubclient.CaseSubclient">CaseSubclient</a></li>
<li><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="subclients/casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></li>
<li><a title="cvpysdk.subclients.db2subclient.DB2Subclient" href="subclients/db2subclient.html#cvpysdk.subclients.db2subclient.DB2Subclient">DB2Subclient</a></li>
<li><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient" href="subclients/dbsubclient.html#cvpysdk.subclients.dbsubclient.DatabaseSubclient">DatabaseSubclient</a></li>
<li><a title="cvpysdk.subclients.exchange.exchange_database_subclient.ExchangeDatabaseSubclient" href="subclients/exchange/exchange_database_subclient.html#cvpysdk.subclients.exchange.exchange_database_subclient.ExchangeDatabaseSubclient">ExchangeDatabaseSubclient</a></li>
<li><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient" href="subclients/exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient">ExchangeSubclient</a></li>
<li><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient" href="subclients/fssubclient.html#cvpysdk.subclients.fssubclient.FileSystemSubclient">FileSystemSubclient</a></li>
<li><a title="cvpysdk.subclients.informixsubclient.InformixSubclient" href="subclients/informixsubclient.html#cvpysdk.subclients.informixsubclient.InformixSubclient">InformixSubclient</a></li>
<li><a title="cvpysdk.subclients.lndbsubclient.LNDbSubclient" href="subclients/lndbsubclient.html#cvpysdk.subclients.lndbsubclient.LNDbSubclient">LNDbSubclient</a></li>
<li><a title="cvpysdk.subclients.lotusnotes.lnsubclient.LNSubclient" href="subclients/lotusnotes/lnsubclient.html#cvpysdk.subclients.lotusnotes.lnsubclient.LNSubclient">LNSubclient</a></li>
<li><a title="cvpysdk.subclients.mysqlsubclient.MYSQLSubclient" href="subclients/mysqlsubclient.html#cvpysdk.subclients.mysqlsubclient.MYSQLSubclient">MYSQLSubclient</a></li>
<li><a title="cvpysdk.subclients.saporaclesubclient.SAPOracleSubclient" href="subclients/saporaclesubclient.html#cvpysdk.subclients.saporaclesubclient.SAPOracleSubclient">SAPOracleSubclient</a></li>
<li><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient" href="subclients/sharepointsubclient.html#cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient">SharepointSuperSubclient</a></li>
<li><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient" href="subclients/sybasesubclient.html#cvpysdk.subclients.sybasesubclient.SybaseSubclient">SybaseSubclient</a></li>
<li><a title="cvpysdk.subclients.vminstancesubclient.VMInstanceSubclient" href="subclients/vminstancesubclient.html#cvpysdk.subclients.vminstancesubclient.VMInstanceSubclient">VMInstanceSubclient</a></li>
<li><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient" href="subclients/vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient">VirtualServerSubclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclient.Subclient.allow_multiple_readers"><code class="name">var <span class="ident">allow_multiple_readers</span></code></dt>
<dd>
<div class="desc"><p>Treats the allow multiple readers as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2234-L2240" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def allow_multiple_readers(self):
    &#34;&#34;&#34;Treats the allow multiple readers as a read-only attribute.&#34;&#34;&#34;
    if &#39;allowMultipleDataReaders&#39; in self._commonProperties:
        return bool(
            self._commonProperties[&#39;allowMultipleDataReaders&#39;]
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.data_readers"><code class="name">var <span class="ident">data_readers</span></code></dt>
<dd>
<div class="desc"><p>Treats the data readers as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2208-L2214" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_readers(self):
    &#34;&#34;&#34;Treats the data readers as a read-only attribute.&#34;&#34;&#34;
    if &#39;numberOfBackupStreams&#39; in self._commonProperties:
        return int(
            self._commonProperties[&#39;numberOfBackupStreams&#39;]
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.deduplication_options"><code class="name">var <span class="ident">deduplication_options</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of deduplication options settings on the Subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L3305-L3327" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def deduplication_options(self):
    &#34;&#34;&#34;Returns the value of deduplication options settings on the Subclient.&#34;&#34;&#34;
    mapping_dedupe = {
        0: False,
        1: True,
    }
    mapping_signature = {
        1: &#34;ON_CLIENT&#34;,
        2: &#34;ON_MEDIA_AGENT&#34;
    }

    dedupe_options = self._commonProperties[&#39;storageDevice&#39;][&#39;deDuplicationOptions&#39;]

    if &#34;enableDeduplication&#34; in dedupe_options:
        if dedupe_options[&#39;enableDeduplication&#39;] == 0:
            return mapping_dedupe[dedupe_options[&#39;enableDeduplication&#39;]]
        else:
            if &#39;generateSignature&#39; in dedupe_options:
                try:
                    return mapping_signature[dedupe_options[&#39;generateSignature&#39;]]
                except KeyError:
                    return dedupe_options[&#39;generateSignature&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.description"><code class="name">var <span class="ident">description</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient description as a property of the Subclient class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2173-L2177" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def description(self):
    &#34;&#34;&#34;Treats the subclient description as a property of the Subclient class.&#34;&#34;&#34;
    if &#39;description&#39; in self._commonProperties:
        return self._commonProperties[&#39;description&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.display_name"><code class="name">var <span class="ident">display_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the Subclient display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2042-L2045" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def display_name(self):
    &#34;&#34;&#34;Returns the Subclient display name&#34;&#34;&#34;
    return self.name</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.encryption_flag"><code class="name">var <span class="ident">encryption_flag</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of encryption flag settings on the Subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L3263-L3276" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def encryption_flag(self):
    &#34;&#34;&#34;Returns the value of encryption flag settings on the Subclient.&#34;&#34;&#34;
    mapping = {
        0: &#39;ENC_NONE&#39;,
        1: &#39;ENC_MEDIA_ONLY&#39;,
        2: &#39;ENC_NETWORK_AND_MEDIA&#39;,
        3: &#39;ENC_NETWORK_ONLY&#39;
    }

    try:
        return mapping[self._commonProperties[&#39;encryptionFlag&#39;]]
    except KeyError:
        return self._commonProperties[&#39;encryptionFlag&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.is_backup_enabled"><code class="name">var <span class="ident">is_backup_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the is backup enabled as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2128-L2132" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_backup_enabled(self):
    &#34;&#34;&#34;Treats the is backup enabled as a read-only attribute.&#34;&#34;&#34;
    if &#39;enableBackup&#39; in self._commonProperties:
        return self._commonProperties[&#39;enableBackup&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled"><code class="name">var <span class="ident">is_blocklevel_backup_enabled</span></code></dt>
<dd>
<div class="desc"><p>returns True if block level backup is enabled else returns false</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2141-L2145" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_blocklevel_backup_enabled(self):
    &#34;&#34;&#34;returns True if block level backup is enabled else returns false&#34;&#34;&#34;
    return bool(self._subclient_properties.get(
        &#39;postgreSQLSubclientProp&#39;, {}).get(&#39;isUseBlockLevelBackup&#39;, False))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.is_default_subclient"><code class="name">var <span class="ident">is_default_subclient</span></code></dt>
<dd>
<div class="desc"><p>Returns True if the subclient is default
subclient else returns False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2276-L2280" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_default_subclient(self):
    &#34;&#34;&#34;Returns True if the subclient is default
    subclient else returns False&#34;&#34;&#34;
    return self._commonProperties.get(&#39;isDefaultSubclient&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.is_intelli_snap_enabled"><code class="name">var <span class="ident">is_intelli_snap_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the is intelli snap enabled as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2134-L2139" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_intelli_snap_enabled(self):
    &#34;&#34;&#34;Treats the is intelli snap enabled as a read-only attribute.&#34;&#34;&#34;
    if &#39;snapCopyInfo&#39; in self._commonProperties:
        snap_copy_info = self._commonProperties.get(&#39;snapCopyInfo&#39;)
        return snap_copy_info.get(&#39;isSnapBackupEnabled&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.is_on_demand_subclient"><code class="name">var <span class="ident">is_on_demand_subclient</span></code></dt>
<dd>
<div class="desc"><p>Treats the on demand subclient as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2168-L2171" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_on_demand_subclient(self):
    &#34;&#34;&#34;Treats the on demand subclient as a read-only attribute.&#34;&#34;&#34;
    return self._backupset_object.is_on_demand_backupset</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.is_trueup_enabled"><code class="name">var <span class="ident">is_trueup_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the True up enabled as a property of the Subclient class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2162-L2166" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_trueup_enabled(self):
    &#34;&#34;&#34;Treats the True up enabled as a property of the Subclient class.&#34;&#34;&#34;
    if &#39;isTrueUpOptionEnabled&#39; in self._commonProperties:
        return self._commonProperties[&#39;isTrueUpOptionEnabled&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.last_backup_time"><code class="name">var <span class="ident">last_backup_time</span></code></dt>
<dd>
<div class="desc"><p>Treats the last backup time as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2107-L2116" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def last_backup_time(self):
    &#34;&#34;&#34;Treats the last backup time as a read-only attribute.&#34;&#34;&#34;
    if &#39;lastBackupTime&#39; in self._commonProperties:
        if self._commonProperties[&#39;lastBackupTime&#39;] != 0:
            _last_backup_time = time.ctime(
                self._commonProperties[&#39;lastBackupTime&#39;]
            )
            return _last_backup_time
    return 0</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Returns the Subclient display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2037-L2040" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Returns the Subclient display name&#34;&#34;&#34;
    return self._subclient_properties[&#39;subClientEntity&#39;][&#39;subclientName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.network_agent"><code class="name">var <span class="ident">network_agent</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of network agents setting on the Subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L3238-L3241" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def network_agent(self):
    &#34;&#34;&#34;Returns the value of network agents setting on the Subclient.&#34;&#34;&#34;
    return self._commonProperties[&#39;storageDevice&#39;][&#39;networkAgents&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.next_backup_time"><code class="name">var <span class="ident">next_backup_time</span></code></dt>
<dd>
<div class="desc"><p>Treats the next backup time as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2118-L2126" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def next_backup_time(self):
    &#34;&#34;&#34;Treats the next backup time as a read-only attribute.&#34;&#34;&#34;
    if &#39;nextBackupTime&#39; in self._commonProperties:
        if self._commonProperties[&#39;nextBackupTime&#39;] != 0:
            _next_backup = time.ctime(
                self._commonProperties[&#39;nextBackupTime&#39;]
            )
            return _next_backup</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.plan"><code class="name">var <span class="ident">plan</span></code></dt>
<dd>
<div class="desc"><p>Returns the name of the plan associated with the subclient.
Returns None if no plan is associated</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L3382-L3396" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def plan(self):
    &#34;&#34;&#34;Returns the name of the plan associated with the subclient.
       Returns None if no plan is associated
    &#34;&#34;&#34;

    if &#39;planEntity&#39; in self._subclient_properties:
        planEntity = self._subclient_properties[&#39;planEntity&#39;]

        if bool(planEntity) and &#39;planName&#39; in planEntity:
            return planEntity[&#39;planName&#39;]
        else:
            return None
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;112&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.properties"><code class="name">var <span class="ident">properties</span></code></dt>
<dd>
<div class="desc"><p>Returns the subclient properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2032-L2035" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def properties(self):
    &#34;&#34;&#34;Returns the subclient properties&#34;&#34;&#34;
    return copy.deepcopy(self._subclient_properties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.read_buffer_size"><code class="name">var <span class="ident">read_buffer_size</span></code></dt>
<dd>
<div class="desc"><p>Treats the read buffer size as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2268-L2274" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def read_buffer_size(self):
    &#34;&#34;&#34;Treats the read buffer size as a read-only attribute.&#34;&#34;&#34;
    if &#39;readBuffersize&#39; in self._commonProperties:
        return int(
            self._commonProperties[&#39;readBuffersize&#39;]
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.snapshot_engine_name"><code class="name">var <span class="ident">snapshot_engine_name</span></code></dt>
<dd>
<div class="desc"><p>returns snapshot engine name associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2147-L2160" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def snapshot_engine_name(self):
    &#34;&#34;&#34;returns snapshot engine name associated with the subclient&#34;&#34;&#34;
    if self.is_intelli_snap_enabled:
        if &#39;snapCopyInfo&#39; in self._commonProperties:
            snap_copy_info = self._commonProperties.get(&#39;snapCopyInfo&#39;, &#34;&#34;)
            if &#39;snapToTapeSelectedEngine&#39; in snap_copy_info:
                if &#39;snapShotEngineName&#39; in snap_copy_info.get(&#39;snapToTapeSelectedEngine&#39;, &#34;&#34;):
                    return snap_copy_info[&#39;snapToTapeSelectedEngine&#39;].get(
                        &#39;snapShotEngineName&#39;, &#34;&#34;)
    raise SDKException(
        &#39;Subclient&#39;,
        &#39;102&#39;,
        &#39;Cannot fetch snap engine name.&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.software_compression"><code class="name">var <span class="ident">software_compression</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of Software Compression settings on the Subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L3195-L3208" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def software_compression(self):
    &#34;&#34;&#34;Returns the value of Software Compression settings on the Subclient.&#34;&#34;&#34;
    mapping = {
        0: &#39;ON_CLIENT&#39;,
        1: &#39;ON_MEDIAAGENT&#39;,
        2: &#39;USE_STORAGE_POLICY_SETTINGS&#39;,
        4: &#39;OFF&#39;
    }

    try:
        return mapping[self._commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]]
    except KeyError:
        return self._commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.storage_ma"><code class="name">var <span class="ident">storage_ma</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient storage ma as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2188-L2196" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_ma(self):
    &#34;&#34;&#34;Treats the subclient storage ma as a read-only attribute.&#34;&#34;&#34;
    storage_device = self._commonProperties[&#39;storageDevice&#39;]
    if &#39;performanceMode&#39; in storage_device:
        data_backup_storage_device = storage_device[&#39;performanceMode&#39;]
        data_storage_details = data_backup_storage_device[&#34;perfCRCDetails&#34;][0]
        if &#39;perfMa&#39; in data_storage_details:
            return data_storage_details[&#39;perfMa&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.storage_ma_id"><code class="name">var <span class="ident">storage_ma_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient storage ma id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2198-L2206" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_ma_id(self):
    &#34;&#34;&#34;Treats the subclient storage ma id as a read-only attribute.&#34;&#34;&#34;
    storage_device = self._commonProperties[&#39;storageDevice&#39;]
    if &#39;performanceMode&#39; in storage_device:
        data_backup_storage_device = storage_device[&#39;performanceMode&#39;]
        data_storage_details = data_backup_storage_device[&#34;perfCRCDetails&#34;][0]
        if &#39;perfMaId&#39; in data_storage_details:
            return data_storage_details[&#39;perfMaId&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.storage_policy"><code class="name">var <span class="ident">storage_policy</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient storage policy as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2179-L2186" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_policy(self):
    &#34;&#34;&#34;Treats the subclient storage policy as a read-only attribute.&#34;&#34;&#34;
    storage_device = self._commonProperties[&#39;storageDevice&#39;]
    if &#39;dataBackupStoragePolicy&#39; in storage_device:
        data_backup_storage_policy = storage_device[&#39;dataBackupStoragePolicy&#39;]
        if &#39;storagePolicyName&#39; in data_backup_storage_policy:
            return data_backup_storage_policy[&#39;storagePolicyName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.subclient_guid"><code class="name">var <span class="ident">subclient_guid</span></code></dt>
<dd>
<div class="desc"><p>Returns the SubclientGUID</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2047-L2050" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclient_guid(self):
    &#34;&#34;&#34;Returns the SubclientGUID&#34;&#34;&#34;
    return self._subclient_properties.get(&#39;subClientEntity&#39;, {}).get(&#39;subclientGUID&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.subclient_id"><code class="name">var <span class="ident">subclient_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2097-L2100" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclient_id(self):
    &#34;&#34;&#34;Treats the subclient id as a read-only attribute.&#34;&#34;&#34;
    return self._subclient_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.subclient_name"><code class="name">var <span class="ident">subclient_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2102-L2105" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclient_name(self):
    &#34;&#34;&#34;Treats the subclient name as a read-only attribute.&#34;&#34;&#34;
    return self._subclient_name</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclient.Subclient.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, backup_level='Incremental', incremental_backup=False, incremental_level='BEFORE_SYNTH', collect_metadata=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a backup job for the subclient of the level specified.</p>
<h2 id="args">Args</h2>
<p>backup_level
(str)
&ndash;
level of backup the user wish to run
Full / Incremental / Differential / Synthetic_full
default: Incremental</p>
<p>incremental_backup
(bool)
&ndash;
run incremental backup
only applicable in case of Synthetic_full backup
default: False</p>
<p>incremental_level
(str)
&ndash;
run incremental backup before/after synthetic full
BEFORE_SYNTH / AFTER_SYNTH</p>
<pre><code>    only applicable in case of Synthetic_full backup
default: BEFORE_SYNTH
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level specified is not correct</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2516-L2573" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self,
           backup_level=&#34;Incremental&#34;,
           incremental_backup=False,
           incremental_level=&#39;BEFORE_SYNTH&#39;,
           collect_metadata=False):
    &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

        Args:
            backup_level        (str)   --  level of backup the user wish to run
                    Full / Incremental / Differential / Synthetic_full
                default: Incremental

            incremental_backup  (bool)  --  run incremental backup
                    only applicable in case of Synthetic_full backup
                default: False

            incremental_level   (str)   --  run incremental backup before/after synthetic full
                    BEFORE_SYNTH / AFTER_SYNTH

                    only applicable in case of Synthetic_full backup
                default: BEFORE_SYNTH

        Returns:
            object - instance of the Job class for this backup job

        Raises:
            SDKException:
                if backup level specified is not correct

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    backup_level = backup_level.lower()

    if backup_level not in [&#39;full&#39;, &#39;incremental&#39;, &#39;transaction_log&#39;,
                            &#39;differential&#39;, &#39;synthetic_full&#39;]:
        raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

    backup_request = backup_level

    if backup_level == &#39;synthetic_full&#39;:
        if incremental_backup:
            backup_request += &#39;&amp;runIncrementalBackup=True&#39;
            backup_request += &#39;&amp;incrementalLevel=%s&#39; % (
                incremental_level.lower())
        else:
            backup_request += &#39;&amp;runIncrementalBackup=False&#39;

    backup_request += &#39;&amp;collectMetaInfo=%s&#39; % collect_metadata

    backup_service = self._services[&#39;SUBCLIENT_BACKUP&#39;] % (
        self.subclient_id, backup_request)

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, backup_service)

    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the content of the Subclient.</p>
<h2 id="args">Args</h2>
<p>Dictionary of browse options:
Example:</p>
<pre><code>    browse({
        'path': 'c:\\hello',

        'show_deleted': True,

        'from_time': '2014-04-20 12:00:00',

        'to_time': '2016-04-21 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of browse options:
Example:</p>
<pre><code>    browse(
        path='c:\hello',

        show_deleted=True,

        from_time='2014-04-20 12:00:00',

        to_time='2016-04-21 12:00:00'
    )
</code></pre>
<h2 id="returns">Returns</h2>
<p>(list, dict)
list
-
List of only the file, folder paths from the browse response</p>
<pre><code>dict    -   Dictionary of all the paths with additional metadata retrieved
from browse operation
</code></pre>
<p>Refer <code>default_browse_options</code>_ for all the supported options.</p>
<p>.. _default_browse_options: <a href="https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565">https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565</a></p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2590-L2641" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, *args, **kwargs):
    &#34;&#34;&#34;Browses the content of the Subclient.

        Args:
            Dictionary of browse options:
                Example:

                    browse({
                        &#39;path&#39;: &#39;c:\\\\hello&#39;,

                        &#39;show_deleted&#39;: True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of browse options:
                Example:

                    browse(
                        path=&#39;c:\\hello&#39;,

                        show_deleted=True,

                        from_time=&#39;2014-04-20 12:00:00&#39;,

                        to_time=&#39;2016-04-21 12:00:00&#39;
                    )

        Returns:
            (list, dict)
                list    -   List of only the file, folder paths from the browse response

                dict    -   Dictionary of all the paths with additional metadata retrieved
                from browse operation


        Refer `default_browse_options`_ for all the supported options.

        .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

    &#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs

    options[&#39;_subclient_id&#39;] = self._subclient_id

    return self._backupset_object.browse(options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.disable_backup"><code class="name flex">
<span>def <span class="ident">disable_backup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Backup for the subclient.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable backup of subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2419-L2427" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_backup(self):
    &#34;&#34;&#34;Disables Backup for the subclient.

        Raises:
            SDKException:
                if failed to disable backup of subclient
    &#34;&#34;&#34;
    self._set_subclient_properties(
        &#34;_commonProperties[&#39;enableBackup&#39;]&#34;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.disable_intelli_snap"><code class="name flex">
<span>def <span class="ident">disable_intelli_snap</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Intelli Snap for the subclient.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable intelli snap for subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2477-L2486" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_intelli_snap(self):
    &#34;&#34;&#34;Disables Intelli Snap for the subclient.

        Raises:
            SDKException:
                if failed to disable intelli snap for subclient
    &#34;&#34;&#34;
    self._set_subclient_properties(
        &#34;_commonProperties[&#39;snapCopyInfo&#39;][&#39;isSnapBackupEnabled&#39;]&#34;, False
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.enable_backup"><code class="name flex">
<span>def <span class="ident">enable_backup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables Backup for the subclient.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable backup of subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2363-L2370" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_backup(self):
    &#34;&#34;&#34;Enables Backup for the subclient.

        Raises:
            SDKException:
                if failed to enable backup of subclient
    &#34;&#34;&#34;
    self._set_subclient_properties(&#34;_commonProperties[&#39;enableBackup&#39;]&#34;, True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.enable_backup_at_time"><code class="name flex">
<span>def <span class="ident">enable_backup_at_time</span></span>(<span>self, enable_time)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Backup if not already disabled, and enables at the time specified.</p>
<h2 id="args">Args</h2>
<p>enable_time (str)
&ndash;
UTC time to enable the backup at, in 24 Hour format
format: YYYY-MM-DD HH:mm:ss</p>
<p><strong>Note</strong> In case of linux CommServer provide time in GMT timezone</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if time value entered is less than the current time</p>
<pre><code>if time value entered is not of correct format

if failed to enable backup

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2382-L2417" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_backup_at_time(self, enable_time):
    &#34;&#34;&#34;Disables Backup if not already disabled, and enables at the time specified.

        Args:
            enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                format: YYYY-MM-DD HH:mm:ss

            **Note** In case of linux CommServer provide time in GMT timezone

        Raises:
            SDKException:
                if time value entered is less than the current time

                if time value entered is not of correct format

                if failed to enable backup

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    try:
        time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
        if time.mktime(time_tuple) &lt; time.time():
            raise SDKException(&#39;Subclient&#39;, &#39;108&#39;)
    except ValueError:
        raise SDKException(&#39;Subclient&#39;, &#39;109&#39;)

    enable_backup_at_time = {
        &#34;TimeZoneName&#34;: self._commcell_object.default_timezone,
        &#34;timeValue&#34;: enable_time
    }

    self._set_subclient_properties(
        &#34;_commonProperties[&#39;enableBackupAtDateTime&#39;]&#34;, enable_backup_at_time
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.enable_intelli_snap"><code class="name flex">
<span>def <span class="ident">enable_intelli_snap</span></span>(<span>self, snap_engine_name, proxy_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables Intelli Snap for the subclient.</p>
<h2 id="args">Args</h2>
<p>snap_engine_name
(str)
&ndash;
Snap Engine Name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable intelli snap for subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2439-L2475" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_intelli_snap(self, snap_engine_name, proxy_options=None):
    &#34;&#34;&#34;Enables Intelli Snap for the subclient.

        Args:
            snap_engine_name    (str)   --  Snap Engine Name

        Raises:
            SDKException:
                if failed to enable intelli snap for subclient
    &#34;&#34;&#34;
    if not isinstance(snap_engine_name, str):
        raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

    properties_dict = {
        &#34;isSnapBackupEnabled&#34;: True,
        &#34;snapToTapeSelectedEngine&#34;: {
            &#34;snapShotEngineName&#34;: snap_engine_name
        }
    }

    if proxy_options is not None:
        if &#34;snap_proxy&#34; in proxy_options:
            properties_dict[&#34;snapToTapeProxyToUse&#34;] = {
                &#34;clientName&#34;: proxy_options[&#34;snap_proxy&#34;]
            }

        if &#34;backupcopy_proxy&#34; in proxy_options:
            properties_dict[&#34;useSeparateProxyForSnapToTape&#34;] = True
            properties_dict[&#34;separateProxyForSnapToTape&#34;] = {
                &#34;clientName&#34;: proxy_options[&#34;backupcopy_proxy&#34;]
            }

        if &#34;use_source_if_proxy_unreachable&#34; in proxy_options:
            properties_dict[&#34;snapToTapeProxyToUseSource&#34;] = True

    self._set_subclient_properties(
        &#34;_commonProperties[&#39;snapCopyInfo&#39;]&#34;, properties_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.enable_trueup"><code class="name flex">
<span>def <span class="ident">enable_trueup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Setter for the TrueUp Option for a Subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2372-L2375" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_trueup(self):
    &#34;&#34;&#34;Setter for the TrueUp Option for a Subclient&#34;&#34;&#34;
    if &#39;isTrueUpOptionEnabled&#39; in self._commonProperties:
        self._set_subclient_properties(&#34;_commonProperties[&#39;isTrueUpOptionEnabled&#39;]&#34;, True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.enable_trueup_days"><code class="name flex">
<span>def <span class="ident">enable_trueup_days</span></span>(<span>self, days=30)</span>
</code></dt>
<dd>
<div class="desc"><p>Setter for the TrueUp Option with reconcile after x days</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2377-L2380" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_trueup_days(self, days=30):
    &#34;&#34;&#34;Setter for the TrueUp Option with reconcile after x days&#34;&#34;&#34;
    self.enable_trueup()
    self._set_subclient_properties(&#34;_commonProperties[&#39;runTrueUpJobAfterDays&#39;]&#34;, days)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.exclude_from_sla"><code class="name flex">
<span>def <span class="ident">exclude_from_sla</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Exclude subclient from SLA.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to exclude the subclient from SLA</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2429-L2437" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def exclude_from_sla(self):
    &#34;&#34;&#34;Exclude subclient from SLA.

        Raises:
            SDKException:
                if failed to exclude the subclient from SLA
    &#34;&#34;&#34;
    self._set_subclient_properties(
        &#34;_commonProperties[&#39;excludeFromSLA&#39;]&#34;, True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.find"><code class="name flex">
<span>def <span class="ident">find</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches a file/folder in the backed up content of the subclient,
and returns all the files matching the filters given.</p>
<h2 id="args">Args</h2>
<p>Dictionary of browse options:
Example:</p>
<pre><code>    find({
        'file_name': '*.txt',

        'show_deleted': True,

        'from_time': '2014-04-20 12:00:00',

        'to_time': '2016-04-31 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of browse options:
Example:</p>
<pre><code>    find(
        file_name='*.txt',

        show_deleted=True,

        'from_time': '2014-04-20 12:00:00',

        to_time='2016-04-31 12:00:00'
    )
</code></pre>
<h2 id="returns">Returns</h2>
<p>(list, dict)
list
-
List of only the file, folder paths from the browse response</p>
<pre><code>dict    -   Dictionary of all the paths with additional metadata retrieved
from browse operation
</code></pre>
<p>Refer <code>default_browse_options</code>_ for all the supported options.</p>
<p>Additional options supported:
file_name
(str)
&ndash;
Find files with name</p>
<pre><code>file_size_gt    (int)   --  Find files with size greater than size

file_size_lt    (int)   --  Find files with size lesser than size

file_size_et    (int)   --  Find files with size equal to size
</code></pre>
<p>.. _default_browse_options: <a href="https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565">https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565</a></p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2643-L2704" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def find(self, *args, **kwargs):
    &#34;&#34;&#34;Searches a file/folder in the backed up content of the subclient,
        and returns all the files matching the filters given.

        Args:
            Dictionary of browse options:
                Example:

                    find({
                        &#39;file_name&#39;: &#39;*.txt&#39;,

                        &#39;show_deleted&#39;: True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of browse options:
                Example:

                    find(
                        file_name=&#39;*.txt&#39;,

                        show_deleted=True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        to_time=&#39;2016-04-31 12:00:00&#39;
                    )

        Returns:
            (list, dict)
                list    -   List of only the file, folder paths from the browse response

                dict    -   Dictionary of all the paths with additional metadata retrieved
                from browse operation


        Refer `default_browse_options`_ for all the supported options.

        Additional options supported:
            file_name       (str)   --  Find files with name

            file_size_gt    (int)   --  Find files with size greater than size

            file_size_lt    (int)   --  Find files with size lesser than size

            file_size_et    (int)   --  Find files with size equal to size

        .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

    &#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs

    options[&#39;_subclient_id&#39;] = self._subclient_id

    return self._backupset_object.find(options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.find_latest_job"><code class="name flex">
<span>def <span class="ident">find_latest_job</span></span>(<span>self, include_active=True, include_finished=True, lookup_time=1, job_filter='Backup,SYNTHFULL')</span>
</code></dt>
<dd>
<div class="desc"><p>Finds the latest job for the subclient
which includes current running job also.</p>
<h2 id="args">Args</h2>
<p>include_active
(bool)
&ndash; to indicate if
active jobs should be included
default: True</p>
<p>include_finished
(bool)
&ndash; to indicate if finished jobs
should be included
default: True</p>
<p>lookup_time
(int)
&ndash; get jobs executed
within the number of hours
default: 1 Hour</p>
<p>job_filter
(str)
&ndash; to specify type of job
default: 'Backup,SYNTHFULL'</p>
<pre><code>for multiple filters,
give the values **comma(,)** separated

List of Possible Values:

        Backup

        Restore

        AUXCOPY

        WORKFLOW

        etc..

&lt;http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm&gt;
    to get the complete list of filters available
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Job class for the latest job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if any error occurred while finding the latest job.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L3011-L3103" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def find_latest_job(
        self,
        include_active=True,
        include_finished=True,
        lookup_time=1,
        job_filter=&#39;Backup,SYNTHFULL&#39;):
    &#34;&#34;&#34;Finds the latest job for the subclient
        which includes current running job also.

        Args:
            include_active    (bool)    -- to indicate if
                                            active jobs should be included
                default: True

            include_finished  (bool)    -- to indicate if finished jobs
                                            should be included
                default: True

            lookup_time       (int)     -- get jobs executed
                                            within the number of hours
                default: 1 Hour

            job_filter        (str)     -- to specify type of job
                default: &#39;Backup,SYNTHFULL&#39;

                for multiple filters,
                give the values **comma(,)** separated

                List of Possible Values:

                        Backup

                        Restore

                        AUXCOPY

                        WORKFLOW

                        etc..

                http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                    to get the complete list of filters available

        Returns:
            object  -   instance of the Job class for the latest job

        Raises:
            SDKException:
                if any error occurred while finding the latest job.

    &#34;&#34;&#34;
    job_controller = JobController(self._commcell_object)
    entity_dict = {
        &#34;subclientId&#34;: int(self.subclient_id)
    }
    if include_active and include_finished:
        client_jobs = job_controller.all_jobs(
            client_name=self._client_object.client_name,
            lookup_time=lookup_time,
            job_filter=job_filter,
            entity=entity_dict
        )
    elif include_active:
        client_jobs = job_controller.active_jobs(
            client_name=self._client_object.client_name,
            lookup_time=lookup_time,
            job_filter=job_filter,
            entity=entity_dict
        )
    elif include_finished:
        client_jobs = job_controller.finished_jobs(
            client_name=self._client_object.client_name,
            lookup_time=lookup_time,
            job_filter=job_filter,
            entity=entity_dict
        )
    else:
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            &#34;Either active or finished job must be included&#34;
        )

    latest_jobid = 0
    for job in client_jobs:
        if client_jobs[job][&#39;subclient_id&#39;] == int(self._subclient_id):
            if int(job) &gt; latest_jobid:
                latest_jobid = int(job)

    if latest_jobid == 0:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;No jobs found&#34;)

    return Job(self._commcell_object, latest_jobid)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy"><code class="name flex">
<span>def <span class="ident">get_ma_associated_storagepolicy</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Get Media agents associated with storage policy</p>
<p>Raise Exception:
if unable to get MA names</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2575-L2588" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_ma_associated_storagepolicy(self):
    &#34;&#34;&#34;
    Get Media agents associated with storage policy
    
    Raise Exception:
            if unable to get MA names
    &#34;&#34;&#34;
    storage = self._subclient_properties[&#39;commonProperties&#39;][&#39;storageDevice&#39;]
    if &#39;performanceMode&#39; in storage:
        data_backup_storage_device = storage[&#39;performanceMode&#39;][&#34;perfCRCDetails&#34;]
        malist = []
        for each_ma in data_backup_storage_device:
            malist.append(each_ma[&#39;perfMa&#39;])
        return malist</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.list_media"><code class="name flex">
<span>def <span class="ident">list_media</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>List media required to browse and restore backed up data from the subclient</p>
<h2 id="args">Args</h2>
<p>Dictionary of options:
Example:</p>
<pre><code>    list_media({
        'path': 'c:\hello',
        'show_deleted': True,
        'from_time': '2020-04-20 12:00:00',
        'to_time': '2021-04-19 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of options:
Example:</p>
<pre><code>    list_media(
        path='c:\hello',
        show_deleted=True,
        from_time='2020-04-20 12:00:00',
        to_time='2021-04-19 12:00:00'
    )
</code></pre>
<h2 id="note">Note</h2>
<p>Refer <code>_default_browse_options</code> in backupset.py for all the supported options.</p>
<h2 id="returns">Returns</h2>
<p>(List, Dict) -
List - List of all the media required for the given options</p>
<pre><code>Dict - Total size of the media
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to list media for content</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2706-L2755" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def list_media(self, *args, **kwargs):
    &#34;&#34;&#34;List media required to browse and restore backed up data from the subclient

        Args:
            Dictionary of options:
                Example:

                    list_media({
                        &#39;path&#39;: &#39;c:\\hello&#39;,
                        &#39;show_deleted&#39;: True,
                        &#39;from_time&#39;: &#39;2020-04-20 12:00:00&#39;,
                        &#39;to_time&#39;: &#39;2021-04-19 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of options:
                Example:

                    list_media(
                        path=&#39;c:\\hello&#39;,
                        show_deleted=True,
                        from_time=&#39;2020-04-20 12:00:00&#39;,
                        to_time=&#39;2021-04-19 12:00:00&#39;
                    )

        Note:
            Refer `_default_browse_options` in backupset.py for all the supported options.

        Returns:
            (List, Dict) -
                List - List of all the media required for the given options

                Dict - Total size of the media

        Raises:
            SDKException:
                if failed to list media for content

                if response is not success

    &#34;&#34;&#34;

    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs

    options[&#39;_subclient_id&#39;] = self._subclient_id

    return self._backupset_object.list_media(options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the Subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L3190-L3193" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the Subclient.&#34;&#34;&#34;
    self._get_subclient_properties()
    self.schedules = Schedules(self)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, fs_options=None, schedule_pattern=None, proxy_client=None, advanced_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of full paths of files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to restore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to restore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>fs_options
(dict)
&ndash; dictionary that includes all advanced options
options:
all_versions
: if set to True restores all the versions of the
specified file
versions
: list of version numbers to be backed up
validate_only
: To validate data backed up for restore</p>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<p>proxy_client
(str)
&ndash; Proxy client used during FS under NAS operations</p>
<p>advanced_options
(dict)
&ndash; Advanced restore options</p>
<pre><code>Options:

    job_description (str)   --  Restore job description

    timezone        (str)   --  Timezone to be used for restore

        **Note** make use of TIMEZONES dict in constants.py to pass timezone
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job if its an immediate Job
instance of the Schedule class for this restore job if its a scheduled Job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2757-L2851" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        fs_options=None,
        schedule_pattern=None,
        proxy_client=None,
        advanced_options=None):

    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

        Args:
            paths                   (list)  --  list of full paths of files/folders to restore

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl    (bool)  --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time           (str)       --  time to restore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to restore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            fs_options      (dict)          -- dictionary that includes all advanced options
                options:
                    all_versions        : if set to True restores all the versions of the
                                            specified file
                    versions            : list of version numbers to be backed up
                    validate_only       : To validate data backed up for restore


            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

            proxy_client    (str)          -- Proxy client used during FS under NAS operations

            advanced_options    (dict)  -- Advanced restore options

                Options:

                    job_description (str)   --  Restore job description

                    timezone        (str)   --  Timezone to be used for restore

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

        Returns:
            object - instance of the Job class for this restore job if its an immediate Job
                     instance of the Schedule class for this restore job if its a scheduled Job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    self._instance_object._restore_association = self._subClientEntity

    return self._instance_object._restore_in_place(
        paths=paths,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        fs_options=fs_options,
        schedule_pattern=schedule_pattern,
        proxy_client=proxy_client,
        advanced_options=advanced_options
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, client, destination_path, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, fs_options=None, schedule_pattern=None, proxy_client=None, advanced_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destionation location.</p>
<h2 id="args">Args</h2>
<p>client
(str/object) &ndash;
either the name of the client or
the instance of the Client</p>
<p>destination_path
(str)
&ndash;
full path of the restore location on client</p>
<p>paths
(list)
&ndash;
list of full paths of
files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to restore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to restore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>fs_options
(dict)
&ndash; dictionary that includes all advanced options
options:
preserve_level
: preserve level option to set in restore
proxy_client
: proxy that needed to be used for restore
impersonate_user
: Impersonate user options for restore
impersonate_password: Impersonate password option for restore
in base64 encoded form
all_versions
: if set to True restores all the versions of the
specified file
versions
: list of version numbers to be backed up
media_agent
: Media Agent need to be used for Browse and restore
validate_only
: To validate data backed up for restore</p>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<p>proxy_client
(str)
&ndash; Proxy client used during FS under NAS operations</p>
<p>advanced_options
(dict)
&ndash; Advanced restore options</p>
<pre><code>Options:

    job_description (str)   --  Restore job description

    timezone        (str)   --  Timezone to be used for restore

        **Note** make use of TIMEZONES dict in constants.py to pass timezone
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job if its an immediate Job
instance of the Schedule class for this restore job if its a scheduled Job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client instance</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2853-L2970" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        client,
        destination_path,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        fs_options=None,
        schedule_pattern=None,
        proxy_client=None,
        advanced_options=None):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destionation location.

        Args:
            client                (str/object) --  either the name of the client or
                                                       the instance of the Client

            destination_path      (str)        --  full path of the restore location on client

            paths                 (list)       --  list of full paths of
                                                       files/folders to restore

            overwrite             (bool)       --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl  (bool)       --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time           (str)       --  time to restore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to restore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            fs_options      (dict)          -- dictionary that includes all advanced options
                options:
                    preserve_level      : preserve level option to set in restore
                    proxy_client        : proxy that needed to be used for restore
                    impersonate_user    : Impersonate user options for restore
                    impersonate_password: Impersonate password option for restore
                                            in base64 encoded form
                    all_versions        : if set to True restores all the versions of the
                                            specified file
                    versions            : list of version numbers to be backed up
                    media_agent         : Media Agent need to be used for Browse and restore
                    validate_only       : To validate data backed up for restore


            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

            proxy_client    (str)          -- Proxy client used during FS under NAS operations

            advanced_options    (dict)  -- Advanced restore options

                Options:

                    job_description (str)   --  Restore job description

                    timezone        (str)   --  Timezone to be used for restore

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

        Returns:
            object - instance of the Job class for this restore job if its an immediate Job
                     instance of the Schedule class for this restore job if its a scheduled Job

        Raises:
            SDKException:
                if client is not a string or Client instance

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    self._instance_object._restore_association = self._subClientEntity

    if fs_options and &#39;proxy_client&#39; in fs_options:
        proxy_client = fs_options[&#39;proxy_client&#39;]

    return self._instance_object._restore_out_of_place(
        client=client,
        destination_path=destination_path,
        paths=paths,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        fs_options=fs_options,
        schedule_pattern=schedule_pattern,
        proxy_client=proxy_client,
        advanced_options=advanced_options
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.run_content_indexing"><code class="name flex">
<span>def <span class="ident">run_content_indexing</span></span>(<span>self, pick_failed_items=False, pick_only_failed_items=False, streams=4, proxies=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Run content Indexing on job.</p>
<h2 id="args">Args</h2>
<p>pick_failed_items
default:False
(bool)
&ndash;
Pick fail items during Content Indexing</p>
<p>pick_only_failed_items
(bool)
&ndash;
Pick only fail items during Content
Indeixng
default: False</p>
<p>streams
(int)
&ndash;
Streams for Content Indexing job</p>
<pre><code> default: 4
</code></pre>
<p>proxies
(list) &ndash;
provide the proxies to run CI
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for the ContentIndexing job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L3105-L3188" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_content_indexing(self,
                               pick_failed_items=False,
                               pick_only_failed_items=False,
                               streams=4,
                               proxies=None):
    &#34;&#34;&#34;Run content Indexing on job.

        Args:
           pick_failed_items
                    default:False   (bool)  --  Pick fail items during Content Indexing

            pick_only_failed_items  (bool)  --  Pick only fail items during Content
                                                Indeixng
                default: False

            streams                 (int)   --  Streams for Content Indexing job

                default: 4

            proxies                 (list) --  provide the proxies to run CI
                default: None

        Returns:
            object - instance of the Job class for the ContentIndexing job

    &#34;&#34;&#34;
    if not (isinstance(pick_failed_items, bool) and
            isinstance(pick_only_failed_items, bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if proxies is None:
        proxies = {}

    self._media_option_json = {
        &#34;pickFailedItems&#34;: pick_failed_items,
        &#34;pickFailedItemsOnly&#34;: pick_only_failed_items,
        &#34;auxcopyJobOption&#34;: {
            &#34;maxNumberOfStreams&#34;: streams,
            &#34;allCopies&#34;: True,
            &#34;useMaximumStreams&#34;: False,
            &#34;proxies&#34;: proxies
        }
    }

    self._content_indexing_option_json= {
        &#34;reanalyze&#34;: False,
        &#34;fileAnalytics&#34;: False,
        &#34;subClientBasedAnalytics&#34;: False
    }
    self._subtask_restore_json = {
        &#34;subTaskType&#34;: 1,
        &#34;operationType&#34;: 5020
    }

    request_json = {
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [self._subClientEntity],
            &#34;task&#34;: self._json_task,
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: self._subtask_restore_json,
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {
                            &#34;mediaOpt&#34;: self._media_option_json
                        },
                        &#34;adminOpts&#34;: {
                            &#34;contentIndexingOption&#34;: self._content_indexing_option_json
                        },
                        &#34;restoreOptions&#34;: {
                            &#34;virtualServerRstOption&#34;: {
                                &#34;isBlockLevelReplication&#34;: False
                            },
                            &#34;browseOption&#34;: {
                                &#34;backupset&#34;: {}
                            }
                        }
                    }
                }
            ]
        }
    }

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.set_backup_nodes"><code class="name flex">
<span>def <span class="ident">set_backup_nodes</span></span>(<span>self, data_access_nodes)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the the backup nodes for NFS share subclient.</p>
<h2 id="args">Args</h2>
<p>data_access_nodes
(list)
&ndash;
the list of data access nodes to be set
as backup nodes for NFS share subclient</p>
<h2 id="returns">Returns</h2>
<p>None
-
if the operation is successful</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if unable to update the backup nodes for the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2972-L3009" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_backup_nodes(self, data_access_nodes):
    &#34;&#34;&#34;Sets the the backup nodes for NFS share subclient.

        Args:
            data_access_nodes   (list)  --  the list of data access nodes to be set
            as backup nodes for NFS share subclient

        Returns:
            None    -   if the operation is successful

        Raises:
            SDKException:
                if unable to update the backup nodes for the subclient

    &#34;&#34;&#34;
    data_access_nodes_json = []
    for access_node in data_access_nodes:
        data_access_nodes_json.append({&#34;clientName&#34;: access_node})

    request_json = {
        &#34;subClientProperties&#34;: {
            &#34;fsSubClientProp&#34;: {
                &#34;backupConfiguration&#34;: {
                    &#34;backupDataAccessNodes&#34;: data_access_nodes_json
                }
            }
        }
    }

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUBCLIENT, request_json)

    output = self._process_update_response(flag, response)

    if output[0]:
        return
    else:
        o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.set_proxy_for_snap"><code class="name flex">
<span>def <span class="ident">set_proxy_for_snap</span></span>(<span>self, proxy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>method to set Use proxy option for intellisnap subclient </p>
<h2 id="args">Args</h2>
<p>proxy_name(str) &ndash; Name of the proxy to be used</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2488-L2504" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_proxy_for_snap(self, proxy_name):
    &#34;&#34;&#34; method to set Use proxy option for intellisnap subclient 

    Args:
        proxy_name(str) -- Name of the proxy to be used

    &#34;&#34;&#34;
    if not isinstance(proxy_name, str):
        raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

    properties_dict = {
        &#34;clientName&#34;: proxy_name
    }

    update_properties = self.properties
    update_properties[&#39;commonProperties&#39;][&#39;snapCopyInfo&#39;][&#39;snapToTapeProxyToUse&#39;] = properties_dict
    self.update_properties(update_properties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.unset_proxy_for_snap"><code class="name flex">
<span>def <span class="ident">unset_proxy_for_snap</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>method to unset Use proxy option for intellisnap subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L2506-L2514" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def unset_proxy_for_snap(self):
    &#34;&#34;&#34; method to unset Use proxy option for intellisnap subclient &#34;&#34;&#34;

    properties_dict = {
        &#34;clientId&#34;: 0
    }
    update_properties = self.properties
    update_properties[&#39;commonProperties&#39;][&#39;snapCopyInfo&#39;][&#39;snapToTapeProxyToUse&#39;] = properties_dict
    self.update_properties(update_properties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclient.update_properties"><code class="name flex">
<span>def <span class="ident">update_properties</span></span>(<span>self, properties_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the subclient properties</p>
<pre><code>Args:
    properties_dict (dict)  --  subclient property dict which is to be updated

Returns:
    None

Raises:
    SDKException:
        if failed to add

        if response is empty

        if response code is not as expected
</code></pre>
<p><strong>Note</strong> self.properties can be used to get a deep copy of all the properties, modify the properties which you
need to change and use the update_properties method to set the properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L1992-L2030" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_properties(self, properties_dict):
    &#34;&#34;&#34;Updates the subclient properties

        Args:
            properties_dict (dict)  --  subclient property dict which is to be updated

        Returns:
            None

        Raises:
            SDKException:
                if failed to add

                if response is empty

                if response code is not as expected

    **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
    need to change and use the update_properties method to set the properties

    &#34;&#34;&#34;
    request_json = {
        &#34;subClientProperties&#34;: {}
    }

    request_json[&#39;subClientProperties&#39;].update(properties_dict)

    # check if subclient name is updated in the request
    # if subclient name is updated set the newName field in the request
    if properties_dict.get(&#39;subClientEntity&#39;, {}).get(&#39;subclientName&#39;) and properties_dict.get(
            &#39;subClientEntity&#39;, {}).get(&#39;subclientName&#39;) != self._subClientEntity.get(&#39;subclientName&#39;):
        request_json[&#39;newName&#39;] = properties_dict.get(&#39;subClientEntity&#39;, {}).get(&#39;subclientName&#39;)
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUBCLIENT, request_json)
    status, _, error_string = self._process_update_response(flag, response)
    self.refresh()

    if not status:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to update subclient properties\nError: &#34;{}&#34;&#39;.format(
            error_string))</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.subclient.Subclients"><code class="flex name class">
<span>class <span class="ident">Subclients</span></span>
<span>(</span><span>class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the subclients associated with a client.</p>
<p>Initialize the Subclients object for the given backupset.</p>
<h2 id="args">Args</h2>
<p>class_object
(object)
&ndash;
instance of the Agent / Instance / Backupset class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Subclients class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if class object is not an instance of Agent / Instance / Backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L189-L1455" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Subclients(object):
    &#34;&#34;&#34;Class for getting all the subclients associated with a client.&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initialize the Subclients object for the given backupset.

            Args:
                class_object    (object)    --  instance of the Agent / Instance / Backupset class

            Returns:
                object  -   instance of the Subclients class

            Raises:
                SDKException:
                    if class object is not an instance of Agent / Instance / Backupset

        &#34;&#34;&#34;
        from .agent import Agent
        from .instance import Instance
        from .backupset import Backupset

        self._agent_object = None
        self._instance_object = None
        self._backupset_object = None
        self._url_param = &#39;&#39;

        if isinstance(class_object, Agent):
            self._agent_object = class_object
            self._url_param += self._agent_object.agent_id

        elif isinstance(class_object, Instance):
            self._instance_object = class_object
            self._agent_object = self._instance_object._agent_object
            self._url_param += &#39;{0}&amp;instanceId={1}&#39;.format(
                self._agent_object.agent_id, self._instance_object.instance_id
            )

        elif isinstance(class_object, Backupset):
            self._backupset_object = class_object
            self._instance_object = class_object._instance_object
            self._agent_object = self._instance_object._agent_object
            self._url_param += self._agent_object.agent_id
            self._url_param += &#39;&amp;backupsetId={0}&#39;.format(
                self._backupset_object.backupset_id
            )
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;115&#39;)

        self._client_object = self._agent_object._client_object
        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._SUBCLIENTS = self._services[&#39;GET_ALL_SUBCLIENTS&#39;] % (
            self._client_object.client_id, self._url_param
        )

        self._ADD_SUBCLIENT = self._services[&#39;ADD_SUBCLIENT&#39;]

        self._default_subclient = None

        # sql server subclient type dict
        self._sqlsubclient_type_dict = {
            &#39;DATABASE&#39;: 1,
            &#39;FILE_FILEGROUP&#39;: 2,
        }

        # this will work only for `Exchange Database` Agent, as only an object of
        # ExchangeDatabaseAgent class has these attributes
        if self._instance_object is None and hasattr(
                self._agent_object, &#39;_instance_object&#39;):
            self._instance_object = self._agent_object._instance_object

        if self._backupset_object is None and hasattr(
                self._agent_object, &#39;_backupset_object&#39;):
            self._backupset_object = self._agent_object._backupset_object

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all subclients of the backupset.

            Returns:
                str - string of all the subclients of th backupset of an agent of a client
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;S. No.&#39;, &#39;Subclient&#39;, &#39;Backupset&#39;, &#39;Instance&#39;, &#39;Agent&#39;, &#39;Client&#39;
        )

        for index, subclient in enumerate(self._subclients):
            sub_str = &#39;{:^5}\t{:20}\t{:20}\t{:20}\t{:20}\t{:20}\n&#39;.format(
                index + 1,
                subclient.split(&#39;\\&#39;)[-1],
                self._subclients[subclient][&#39;backupset&#39;],
                self._instance_object.instance_name,
                self._agent_object.agent_name,
                self._client_object.client_name
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Subclients class.&#34;&#34;&#34;
        if self._backupset_object is not None:
            o_str = (
                &#39;Subclients class instance for Backupset: &#34;{0}&#34;, &#39;
                &#39;of Instance: &#34;{1}&#34;, for Agent: &#34;{2}&#34;&#39;
            ).format(
                self._backupset_object.backupset_name,
                self._instance_object.instance_name,
                self._agent_object.agent_name
            )
        elif self._instance_object is not None:
            o_str = &#39;Subclients class instance for Instance: &#34;{0}&#34;, of Agent: &#34;{1}&#34;&#39;.format(
                self._instance_object.instance_name,
                self._agent_object.agent_name
            )
        else:
            o_str = &#39;Subclients class instance for Agent: &#34;{0}&#34;&#39;.format(
                self._agent_object.agent_name
            )

        return o_str

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the subclients associated to the Agent for the selected Client.&#34;&#34;&#34;
        return len(self.all_subclients)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the subclient for the given subclient ID or
            the details of the subclient for given subclient Name.

            Args:
                value   (str / int)     --  Name or ID of the subclient

            Returns:
                str     -   name of the subclient, if the subclient id was given

                dict    -   dict of details of the subclient, if subclient name was given

            Raises:
                IndexError:
                    no subclient exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_subclients:
            return self.all_subclients[value]
        else:
            try:
                return list(
                    filter(lambda x: x[1][&#39;id&#39;] == value, self.all_subclients.items())
                )[0][0]
            except IndexError:
                raise IndexError(&#39;No subclient exists with the given Name / Id&#39;)

    def _get_subclients(self):
        &#34;&#34;&#34;Gets all the subclients associated to the client specified by the backupset object.

            Returns:
                dict - consists of all subclients in the backupset
                    {
                         &#34;subclient1_name&#34;: {
                             &#34;id&#34;: subclient1_id,
                             &#34;backupset&#34;: backupset
                         },
                         &#34;subclient2_name&#34;: {
                             &#34;id&#34;: subclient2_id,
                             &#34;backupset&#34;: backupset
                         }
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SUBCLIENTS)

        if flag:
            if response.json() and &#39;subClientProperties&#39; in response.json():
                return_dict = {}

                for dictionary in response.json()[&#39;subClientProperties&#39;]:
                    # store the agent, instance, and backupset name for the current subclient
                    # the API call returns the subclients for all Agents, so we need to filter
                    # them out based on the Agent / Instance / Backupset that had been selected
                    # by the user earlier
                    agent = dictionary[&#39;subClientEntity&#39;][&#39;appName&#39;].lower()
                    instance = dictionary[&#39;subClientEntity&#39;][&#39;instanceName&#39;].lower(
                    )
                    backupset = dictionary[&#39;subClientEntity&#39;][&#39;backupsetName&#39;].lower(
                    )

                    # filter subclients for all entities: Agent, Instance, and Backupset
                    # as the instance of the Backupset class was passed for Subclients instance
                    # creation
                    if self._backupset_object is not None:
                        if (self._backupset_object.backupset_name in backupset and
                                self._instance_object.instance_name in instance and
                                self._agent_object.agent_name in agent):
                            temp_name = dictionary[&#39;subClientEntity&#39;][&#39;subclientName&#39;].lower(
                            )
                            temp_id = str(
                                dictionary[&#39;subClientEntity&#39;][&#39;subclientId&#39;]).lower()

                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;backupset&#34;: backupset
                            }

                            if dictionary[&#39;commonProperties&#39;].get(
                                    &#39;isDefaultSubclient&#39;):
                                self._default_subclient = temp_name

                    elif self._instance_object is not None:
                        if (self._instance_object.instance_name in instance and
                                self._agent_object.agent_name in agent):
                            temp_name = dictionary[&#39;subClientEntity&#39;][&#39;subclientName&#39;].lower(
                            )
                            temp_id = str(
                                dictionary[&#39;subClientEntity&#39;][&#39;subclientId&#39;]).lower()

                            if len(
                                    self._instance_object.backupsets.all_backupsets) &gt; 1:
                                temp_name = &#34;{0}\\{1}&#34;.format(
                                    backupset, temp_name)

                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;backupset&#34;: backupset
                            }

                            if dictionary[&#39;commonProperties&#39;].get(
                                    &#39;isDefaultSubclient&#39;):
                                self._default_subclient = temp_name

                    elif self._agent_object is not None:
                        if self._agent_object.agent_name in agent:
                            temp_name = dictionary[&#39;subClientEntity&#39;][&#39;subclientName&#39;].lower(
                            )
                            temp_id = str(
                                dictionary[&#39;subClientEntity&#39;][&#39;subclientId&#39;]).lower()

                            if len(self._agent_object.instances.all_instances) &gt; 1:
                                if len(
                                        self._instance_object.backupsets.all_backupsets) &gt; 1:
                                    temp_name = &#34;{0}\\{1}\\{2}&#34;.format(
                                        instance, backupset, temp_name
                                    )
                                else:
                                    temp_name = &#34;{0}\\{1}&#34;.format(
                                        instance, temp_name)
                            else:
                                if len(
                                        self._instance_object.backupsets.all_backupsets) &gt; 1:
                                    temp_name = &#34;{0}\\{1}&#34;.format(
                                        backupset, temp_name)

                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;backupset&#34;: backupset
                            }

                            if dictionary[&#39;commonProperties&#39;].get(
                                    &#39;isDefaultSubclient&#39;):
                                self._default_subclient = temp_name

                return return_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    @property
    def all_subclients(self):
        &#34;&#34;&#34;Returns dict of all the subclients configured on this backupset

            Retruns:
                dict    -   consists of all subclients in the backupset

                    {
                        &#34;subclient1_name&#34;: {
                            &#34;id&#34;: subclient1_id,

                            &#34;backupset&#34;: backupset
                        },
                        &#34;subclient2_name&#34;: {
                            &#34;id&#34;: subclient2_id,

                            &#34;backupset&#34;: backupset
                        }
                    }

        &#34;&#34;&#34;
        return self._subclients

    def has_subclient(self, subclient_name):
        &#34;&#34;&#34;Checks if a subclient exists in the commcell with the input subclient name.

            Args:
                subclient_name (str)  --  name of the subclient

            Returns:
                bool - boolean output whether the subclient exists in the backupset or not

            Raises:
                SDKException:
                    if type of the subclient name argument is not string
        &#34;&#34;&#34;
        if not isinstance(subclient_name, str):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return self._subclients and subclient_name.lower() in self._subclients

    def _process_add_request(self, request_json):
        &#34;&#34;&#34;To post the add subclient request

        Args:
            request_json    (dict)  -- Request json to be passed as the payload

        Returns:
            object  -   instance of the Subclient class

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._ADD_SUBCLIENT, request_json
        )

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Subclient&#39;,
                        &#39;102&#39;,
                        &#39;Failed to create subclient\nError: &#34;{0}&#34;&#39;.format(error_string)
                    )
                else:
                    # initialize the subclients again so the subclient object has all the subclients
                    self.refresh()

                    subclient_name = request_json[&#39;subClientProperties&#39;][&#39;subClientEntity&#39;][&#39;subclientName&#39;]

                    return self.get(subclient_name)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add(self, subclient_name, storage_policy=None,
            subclient_type=None, description=&#39;&#39;, advanced_options=None,
            pre_scan_cmd=None):
        &#34;&#34;&#34;Adds a new subclient to the backupset.

            Args:
                subclient_name      (str)   --  name of the new subclient to add

                storage_policy      (str)   --  name of the storage policy to be associated
                with the subclient

                    default: None

                subclient_type      (str)   --  type of subclient for sql server

                    default: None

                    Valid Values are:

                        - DATABASE

                        - FILE_FILEGROUP


                description         (str)   --  description for the subclient (optional)

                    default: &#39;&#39;

                advanced_options    (dict)  --  dict of additional options needed to create
                                                subclient with additional properties
                                                default : None
                    Example:
                        {
                            ondemand_subclient : True
                        }

                pre_scan_cmd        (str)   --  path to the batch file/shell script file to run
                                                before each backup of the subclient

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if description argument is not of type string

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(description, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if self._backupset_object is None:
            if self._instance_object.backupsets.has_backupset(
                    &#39;defaultBackupSet&#39;):
                self._backupset_object = self._instance_object.backupsets.get(
                    &#39;defaultBackupSet&#39;)
            else:
                self._backupset_object = self._instance_object.backupsets.get(
                    sorted(self._instance_object.backupsets.all_backupsets)[0]
                )

        if storage_policy and not self._commcell_object.storage_policies.has_policy(
                storage_policy):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    storage_policy)
            )

        if advanced_options:
            if advanced_options.get(&#34;ondemand_subclient&#34;, False):
                ondemand_value = advanced_options.get(&#34;ondemand_subclient&#34;)
            else:
                ondemand_value = False
        else:
            ondemand_value = False

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;contentOperationType&#34;: 2,
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;commonProperties&#34;: {
                    &#34;description&#34;: description,
                    &#34;enableBackup&#34;: True,
                    &#34;onDemandSubClient&#34;: ondemand_value,
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: storage_policy
                        }
                    },
                }
            }
        }

        if storage_policy is None:
            del request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;storageDevice&#34;]

        if pre_scan_cmd is not None:
            request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;prepostProcess&#34;] = {
                &#34;runAs&#34;: 1,
                &#34;preScanCommand&#34;: pre_scan_cmd
            }

        if self._agent_object.agent_name == &#39;sql server&#39;:
            request_json[&#39;subClientProperties&#39;][&#39;mssqlSubClientProp&#39;] = {
                &#39;sqlSubclientType&#39;: self._sqlsubclient_type_dict[subclient_type]
            }

        return self._process_add_request(request_json)

    def add_oracle_logical_dump_subclient(
            self,
            subclient_name,
            storage_policy,
            dump_dir,
            user_name,
            domain_name,
            password,
            full_mode,
            schema_value=None):
        &#34;&#34;&#34;
        Method to add subclient for oracle logical dump.
        This method add two type of subclient full mode
        and schema mode. For full mode full_mode should be
        true and schema_value should be none and for schema
        mode full_mode should be false and schema_value should
        be list of values.Rest of thing should be same for both.
        Args:
              subclient_name     (Str)  --  subclient name for logical dump

              storage_policy     (Str)  --  Storage policy for subclient

              dump_dir            (Str)  --  dump directory for subclient

              user_name           (Str)  --  username for oracle database

              domain_name         (Str)  --  domainname for oracle database

              password           (Str)  --  password for oracle database
                                            (should be in encrypted and decrypted form)

              full_mode           (bool) --  if ture then subclient for full mode otherwise schema mode

              schema_value        (list) --  schema value for schema mode subclient

                   default: None
        Return:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if subclient name already present

                    if storage policy does not exist

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(storage_policy, str) and
                isinstance(dump_dir, str) and
                isinstance(user_name, str) and
                isinstance(domain_name, str) and
                isinstance(password, str) and
                isinstance(full_mode, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        if (full_mode == False and not
        isinstance(schema_value, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if self._backupset_object is None:
            if self._instance_object.backupsets.has_backupset(
                    &#39;defaultBackupSet&#39;):
                self._backupset_object = self._instance_object.backupsets.get(
                    &#39;defaultBackupSet&#39;)
            else:
                self._backupset_object = self._instance_object.backupsets.get(
                    sorted(self._instance_object.backupsets.all_backupsets)[0]
                )

        if not self._commcell_object.storage_policies.has_policy(
                storage_policy):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    storage_policy)
            )

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;oracleSubclientProp&#34;: {
                    &#34;data&#34;: False,
                    &#34;archiveDelete&#34;: False,
                    &#34;useSQLConntect&#34;: False,
                    &#34;dbSubclientType&#34;: 2,
                    &#34;mergeIncImageCopies&#34;: False,
                    &#34;selectiveOnlineFull&#34;: False,
                    &#34;protectBackupRecoveryArea&#34;: False,
                    &#34;selectArchiveLogDestForBackup&#34;: False,
                    &#34;backupSPFile&#34;: False,
                    &#34;backupControlFile&#34;: False,
                    &#34;backupArchiveLog&#34;: False,
                    &#34;validate&#34;: False,
                },
                &#34;commonProperties&#34;: {
                    &#34;snapCopyInfo&#34;: {
                        &#34;useSeparateProxyForSnapToTape&#34;: False,
                        &#34;checkProxyForSQLIntegrity&#34;: False,
                        &#34;snapToTapeProxyToUseSource&#34;: False,
                        &#34;isSnapBackupEnabled&#34;: False,
                        &#34;IsOracleSposDriverEnabled&#34;: False,
                        &#34;isRMANEnableForTapeMovement&#34;: False
                    },
                    &#34;dbDumpConfig&#34;: {
                        &#34;fullMode&#34;: True,
                        &#34;database&#34;: &#34;&#34;,
                        &#34;dumpDir&#34;: dump_dir,
                        &#34;parallelism&#34;: 2,
                        &#34;overrideInstanceUser&#34;: True,
                        &#34;sqlConnect&#34;: {
                            &#34;password&#34;: b64encode(password.encode()).decode(),
                            &#34;domainName&#34;: domain_name,
                            &#34;userName&#34;: user_name
                        }
                    },
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: storage_policy
                        },
                        &#34;deDuplicationOptions&#34;: {
                            &#34;enableDeduplication&#34;: True
                        }
                    }
                }
            }
        }

        if (full_mode == False):
            request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;dbDumpConfig&#34;][&#34;fullMode&#34;] = False
            request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;dbDumpConfig&#34;][&#34;schema&#34;] = schema_value

        return self._process_add_request(request_json)

    def add_postgresql_subclient(
            self, subclient_name, storage_policy,
            contents, no_of_streams=1, collect_object_list=False):
        &#34;&#34;&#34;Adds a new postgresql subclient to the backupset.

            Args:
                subclient_name          (str)   --  name of the new subclient to add

                storage_policy          (str)   --  name of the storage policy to be associated
                with the subclient

                contents                (list)  --  database list to be added as subclient content


                no_of_streams           (int)   --  No of backup streams to be used

                    default: 1

                collect_object_list     (bool)  --  Boolean flag to determine if collect object
                list needs to be enabled for subclient or not

                    default: False

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if conetnts argument is not of type list

                    if contents is empty list

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(storage_policy, str) and
                isinstance(contents, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if not self._commcell_object.storage_policies.has_policy(
                storage_policy):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    storage_policy)
            )

        if not contents:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Content list cannot be empty&#39;
            )

        content_list = []
        for content in contents:
            content_list.append({&#34;postgreSQLContent&#34;: {&#34;databaseName&#34;: content}})

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;contentOperationType&#34;: 2,
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;commonProperties&#34;: {
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: storage_policy
                        }
                    },
                },
                &#34;postgreSQLSubclientProp&#34;: {
                    &#34;numberOfBackupStreams&#34;: no_of_streams,
                    &#34;collectObjectListDuringBackup&#34;: collect_object_list
                },
                &#34;content&#34;: content_list
            }
        }

        return self._process_add_request(request_json)

    def add_mysql_subclient(
            self,
            subclient_name,
            storage_policy,
            contents,
            **kwargs
    ):
        &#34;&#34;&#34;Adds a new mysql subclient to the instance.

            Args:
                subclient_name          (str)   --  name of the new subclient to add

                storage_policy          (str)   --  name of the storage policy to be associated
                with the subclient

                contents                (list)  --  database list to be added as subclient content

                kwargs      (dict)  -- dict of keyword arguments as follows

                    no_of_backup_streams    (int)   --  No of backup streams to be used
                    default: 1

                    no_of_log_backup_streams    (int)   -- No of Transaction log backup streams
                    default: 1

                    full_instance_xtrabackup    (bool)  -- True if XtraBackup is selected for subclient
                    default: False

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if conetnts argument is not of type list

                    if contents is empty list

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(storage_policy, str) and
                isinstance(contents, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if not self._commcell_object.storage_policies.has_policy(
                storage_policy):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    storage_policy)
            )

        if not contents:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Content list cannot be empty&#39;
            )

        content_list = []
        for content in contents:
            content_list.append({&#34;mySQLContent&#34;: {&#34;databaseName&#34;: content}})

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;contentOperationType&#34;: 2,
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: &#34;defaultDummyBackupSet&#34;,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;commonProperties&#34;: {
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: storage_policy
                        }
                    },
                },
                &#34;mySqlSubclientProp&#34;: {
                    &#34;numberOfBackupStreams&#34;: kwargs.get(&#39;no_of_backup_streams&#39;, 1),
                    &#34;numberOfTransactionLogStreams&#34;: kwargs.get(&#39;no_of_log_backup_streams&#39;, 1),
                    &#34;fullInstanceXtraBackup&#34;: kwargs.get(&#39;full_instance_xtrabackup&#39;, False)
                },
                &#34;content&#34;: content_list
            }
        }

        return self._process_add_request(request_json)

    def add_virtual_server_subclient(
            self,
            subclient_name,
            subclient_content,
            **kwargs
    ):
        &#34;&#34;&#34;Adds a new virtual server subclient to the backupset.

            Args:
                subclient_name      (str)   --  Name of the subclient to be created

                subclient_content   (list)  --  Content to be added to the subclient

                    Example 1:
                        [{
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;id&#39;: &#39;&#39;,
                            &#39;path&#39;: &#39;&#39;,
                            &#39;display_name&#39;: &#39;sample1&#39;,
                            &#39;type&#39;: VSAObjects.VMName
                        }]
                    Example 2:
                         [{
                        &#39;allOrAnyChildren&#39;: False,
                        &#39;content&#39;: [{
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;display_name&#39;: &#39;sample1&#39;,
                            &#39;type&#39;: VSAObjects.VMName
                        }, {
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;display_name&#39;: &#39;sample2&#39;,
                            &#39;type&#39;: VSAObjects.VMName
                        }
                        ]
                        }, {
                        &#39;allOrAnyChildren&#39;: True,
                        &#39;content&#39;: [{
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;display_name&#39;: &#39;sample3&#39;,
                            &#39;type&#39;: VSAObjects.RESOURCE_POOL
                        }, {
                            &#39;equal_value&#39;: True,
                            &#39;allOrAnyChildren&#39;: True,
                            &#39;id&#39;: &#39;sample4&#39;,
                            &#39;display_name&#39;: &#39;sample4&#39;,
                            &#39;type&#39;: VSAObjects.SERVER
                            }
                            ]
                        }
                        ]
                        **Note** Use VSAObjects Enum present in constants.py to pass value to type

                kwargs      (dict)  -- dict of keyword arguments as follows

                    plan_name           (str)   --  Plan to be associated with the subclient

                    storage_policy      (str)   --  Storage policy to be associated with the subclient

                    description         (str)   --  Description for the subclient

                        default: &#39;&#39;

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if storage policy argument is not of type string

                    if description argument is not of type string

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

        &#34;&#34;&#34;
        if not (isinstance(subclient_name, str) and
                isinstance(subclient_content, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if self._backupset_object is None:
            if self._instance_object.backupsets.has_backupset(
                    &#39;defaultBackupSet&#39;):
                self._backupset_object = self._instance_object.backupsets.get(
                    &#39;defaultBackupSet&#39;)
            else:
                self._backupset_object = self._instance_object.backupsets.get(
                    sorted(self._instance_object.backupsets.all_backupsets)[0]
                )

        content = []

        def set_content(item_content):
            &#34;&#34;&#34;
            create content dictionary
            Args:
                item_content            (dict):     Dict of content details

                Example:
                    {
                        &#39;equal_value&#39;: True,
                        &#39;allOrAnyChildren&#39;: True,
                        &#39;display_name&#39;: &#39;sample1&#39;,
                        &#39;type&#39;:  &lt; VSAObjects.VMName: 10 &gt;
                    }

            Returns:

            &#34;&#34;&#34;
            return {
                &#34;equalsOrNotEquals&#34;: item_content.get(&#39;equal_value&#39;, True),
                &#34;name&#34;: item_content.get(&#39;id&#39;, &#39;&#39;),
                &#34;displayName&#34;: item_content.get(&#39;display_name&#39;, &#39;&#39;),
                &#34;path&#34;: item_content.get(&#39;path&#39;, &#39;&#39;),
                &#34;allOrAnyChildren&#34;: item.get(&#39;allOrAnyChildren&#39;, True),
                &#34;type&#34;: item_content[&#39;type&#39;] if isinstance(item_content[&#39;type&#39;], int) else item_content[&#39;type&#39;].value
            }

        for item in subclient_content:
            _temp_list = []
            _temp_dict = {}
            allOrAnyChildren = item.get(&#39;allOrAnyChildren&#39;, None)
            if &#39;content&#39; in item:
                nested_content = item[&#39;content&#39;]
                for each_condition in nested_content:
                    temp_dict = set_content(each_condition)
                    _temp_list.append(temp_dict)
                _temp_dict[&#39;allOrAnyChildren&#39;] = allOrAnyChildren
                _temp_dict[&#39;children&#39;] = _temp_list
                content.append(_temp_dict)
            else:
                temp_dict = set_content(item)
                content.append(temp_dict)

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;vmContentOperationType&#34;: 2,
                &#34;vmContent&#34;: {
                    &#34;children&#34;: content
                },
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;commonProperties&#34;: {
                    &#34;description&#34;: kwargs.get(&#39;description&#39;),
                    &#34;enableBackup&#34;: True
                }
            }
        }

        if kwargs.get(&#34;customSnapshotResourceGroup&#34;):
            request_json[&#34;subClientProperties&#34;][&#34;vsaSubclientProp&#34;] = \
                {&#34;customSnapshotResourceGroup&#34;: kwargs.get(&#34;customSnapshotResourceGroup&#34;)}

        if kwargs.get(&#39;plan_name&#39;):
            if not self._commcell_object.plans.has_plan(kwargs[&#39;plan_name&#39;]):
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Plan: &#34;{0}&#34; does not exist in the Commcell&#39;.format(kwargs[&#39;plan_name&#39;])
                )
            request_json[&#39;subClientProperties&#39;][&#39;planEntity&#39;] = {
                &#34;planName&#34;: kwargs[&#39;plan_name&#39;]
            }

        elif kwargs.get(&#39;storage_policy&#39;):
            if not self._commcell_object.storage_policies.has_policy(kwargs.get(&#39;storage_policy&#39;)):
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(kwargs.get(&#39;storage_policy&#39;))
                )
            request_json[&#39;subClientProperties&#39;][&#39;commonProperties&#39;][&#39;storageDevice&#39;] = {
                &#34;dataBackupStoragePolicy&#34;: {
                    &#34;storagePolicyName&#34;: kwargs.get(&#39;storage_policy&#39;)
                }
            }
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Either Plan or Storage policy should be given as input&#39;)

        return self._process_add_request(request_json)

    def add_onedrive_subclient(self,
                               subclient_name,
                               server_plan):

        &#34;&#34;&#34;Adds a new subclient to the backupset.

            Args:
                subclient_name     (str)   --  name of the new subclient to add

                server_plan     (str)   --  name of the server plan to be associated
                                                with the subclient

            Returns:
                object  -   instance of the Subclient class

            Raises:
                SDKException:
                    if subclient name argument is not of type string

                    if server plan argument is not of type string

                    if description argument is not of type string

                    if failed to create subclient

                    if response is empty

                    if response is not success

                    if subclient already exists with the given name

                    if server plan  donot exists with the given name

                &#34;&#34;&#34;

        if not (isinstance(subclient_name, str) and
                isinstance(server_plan, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if self.has_subclient(subclient_name):
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                    subclient_name)
            )

        if self._backupset_object is None:
            if self._instance_object.backupsets.has_backupset(
                    self._instance_object.backupsets.default_backup_set):
                self._backupset_object = self._instance_object.backupsets.get(
                    self._instance_object.backupsets.default_backup_set)
            else:
                self._backupset_object = self._instance_object.backupsets.get(
                    sorted(self._instance_object.backupsets.all_backupsets)[0]
                )

        if self._commcell_object.plans.has_plan(server_plan):
            server_plan_object = self._commcell_object.plans.get(server_plan)
            server_plan_id = int(server_plan_object.plan_id)
        else:
            raise SDKException(&#39;Plan&#39;, &#39;102&#39;, &#39;Provide Valid Plan Name&#39;)

        request_json = {
            &#34;subClientProperties&#34;: {
                &#34;subClientEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
                    &#34;instanceId&#34;: int(self._instance_object.instance_id),
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;applicationId&#34;: 134,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;planEntity&#34;: {
                    &#34;planId&#34;: server_plan_id
                },
                &#34;cloudAppsSubClientProp&#34;: {
                    &#34;instanceType&#34;: 7,
                    &#34;oneDriveSubclient&#34;: {
                        &#34;enableOneNote&#34;: False,
                        &#34;isEnterprise&#34;: True
                    }
                },
                &#34;cloudconnectorSubclientProp&#34;: {
                    &#34;isAutoDiscoveryEnabled&#34;: False
                },
                &#34;commonProperties&#34;: {
                    &#34;enableBackup&#34;: True
                }
            }
        }

        return self._process_add_request(request_json)

    def get(self, subclient_name):
        &#34;&#34;&#34;Returns a subclient object of the specified backupset name.

            Args:
                subclient_name (str)  --  name of the subclient

            Returns:
                object - instance of the Subclient class for the given subclient name

            Raises:
                SDKException:
                    if type of the subclient name argument is not string

                    if no subclient exists with the given name
        &#34;&#34;&#34;
        if not isinstance(subclient_name, str):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        else:
            subclient_name = subclient_name.lower()

            if self.has_subclient(subclient_name):

                if self._backupset_object is None:
                    self._backupset_object = self._instance_object.backupsets.get(
                        self._subclients[subclient_name][&#39;backupset&#39;]
                    )
                return Subclient(
                    self._backupset_object, subclient_name, self._subclients[subclient_name][&#39;id&#39;]
                )

            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;No subclient exists with name: {0}&#39;.format(
                    subclient_name)
            )

    def delete(self, subclient_name):
        &#34;&#34;&#34;Deletes the subclient specified by the subclient_name from the backupset.

            Args:
                subclient_name (str)  --  name of the subclient to remove from the backupset

            Raises:
                SDKException:
                    if type of the subclient name argument is not string

                    if failed to delete subclient

                    if response is empty

                    if response is not success

                    if no subclient exists with the given name
        &#34;&#34;&#34;
        if not isinstance(subclient_name, str):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        else:
            subclient_name = subclient_name.lower()

        if self.has_subclient(subclient_name):
            delete_subclient_service = self._services[&#39;SUBCLIENT&#39;] % (
                self._subclients[subclient_name][&#39;id&#39;]
            )

            flag, response = self._cvpysdk_object.make_request(
                &#39;DELETE&#39;, delete_subclient_service)

            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response_value = response.json()[&#39;response&#39;][0]
                        error_code = str(response_value[&#39;errorCode&#39;])
                        error_message = None

                        if &#39;errorString&#39; in response_value:
                            error_message = response_value[&#39;errorString&#39;]

                        if error_message:
                            o_str = &#39;Failed to delete subclient\nError: &#34;{0}&#34;&#39;
                            raise SDKException(
                                &#39;Subclient&#39;, &#39;102&#39;, o_str.format(error_message))
                        else:
                            if error_code == &#39;0&#39;:
                                # initialize the subclients again
                                # so the subclient object has all the
                                # subclients
                                self.refresh()
                            else:
                                o_str = (&#39;Failed to delete subclient with Error Code: &#34;{0}&#34;\n&#39;
                                         &#39;Please check the documentation for &#39;
                                         &#39;more details on the error&#39;)
                                raise SDKException(
                                    &#39;Subclient&#39;, &#39;102&#39;, o_str.format(error_code))
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;No subclient exists with name: {0}&#39;.format(
                    subclient_name)
            )

    def refresh(self):
        &#34;&#34;&#34;Refresh the subclients associated with the Backupset / Instance.&#34;&#34;&#34;
        self._subclients = self._get_subclients()

    @property
    def default_subclient(self):
        &#34;&#34;&#34;Returns the name of the default subclient for the selected Agent and Backupset.&#34;&#34;&#34;
        return self._default_subclient</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups" href="subclients/virtualserver/kubernetes.html#cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups">ApplicationGroups</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclient.Subclients.all_subclients"><code class="name">var <span class="ident">all_subclients</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all the subclients configured on this backupset</p>
<h2 id="retruns">Retruns</h2>
<p>dict
-
consists of all subclients in the backupset</p>
<pre><code>{
    "subclient1_name": {
        "id": subclient1_id,

        "backupset": backupset
    },
    "subclient2_name": {
        "id": subclient2_id,

        "backupset": backupset
    }
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L473-L494" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_subclients(self):
    &#34;&#34;&#34;Returns dict of all the subclients configured on this backupset

        Retruns:
            dict    -   consists of all subclients in the backupset

                {
                    &#34;subclient1_name&#34;: {
                        &#34;id&#34;: subclient1_id,

                        &#34;backupset&#34;: backupset
                    },
                    &#34;subclient2_name&#34;: {
                        &#34;id&#34;: subclient2_id,

                        &#34;backupset&#34;: backupset
                    }
                }

    &#34;&#34;&#34;
    return self._subclients</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.default_subclient"><code class="name">var <span class="ident">default_subclient</span></code></dt>
<dd>
<div class="desc"><p>Returns the name of the default subclient for the selected Agent and Backupset.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L1452-L1455" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def default_subclient(self):
    &#34;&#34;&#34;Returns the name of the default subclient for the selected Agent and Backupset.&#34;&#34;&#34;
    return self._default_subclient</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclient.Subclients.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, subclient_name, storage_policy=None, subclient_type=None, description='', advanced_options=None, pre_scan_cmd=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new subclient to the backupset.</p>
<h2 id="args">Args</h2>
<p>subclient_name
(str)
&ndash;
name of the new subclient to add</p>
<p>storage_policy
(str)
&ndash;
name of the storage policy to be associated
with the subclient</p>
<pre><code>default: None
</code></pre>
<p>subclient_type
(str)
&ndash;
type of subclient for sql server</p>
<pre><code>default: None

Valid Values are:

    - DATABASE

    - FILE_FILEGROUP
</code></pre>
<p>description
(str)
&ndash;
description for the subclient (optional)</p>
<pre><code>default: ''
</code></pre>
<p>advanced_options
(dict)
&ndash;
dict of additional options needed to create
subclient with additional properties
default : None
Example:
{
ondemand_subclient : True
}</p>
<p>pre_scan_cmd
(str)
&ndash;
path to the batch file/shell script file to run
before each backup of the subclient</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Subclient class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if subclient name argument is not of type string</p>
<pre><code>if storage policy argument is not of type string

if description argument is not of type string

if failed to create subclient

if response is empty

if response is not success

if subclient already exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L552-L685" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, subclient_name, storage_policy=None,
        subclient_type=None, description=&#39;&#39;, advanced_options=None,
        pre_scan_cmd=None):
    &#34;&#34;&#34;Adds a new subclient to the backupset.

        Args:
            subclient_name      (str)   --  name of the new subclient to add

            storage_policy      (str)   --  name of the storage policy to be associated
            with the subclient

                default: None

            subclient_type      (str)   --  type of subclient for sql server

                default: None

                Valid Values are:

                    - DATABASE

                    - FILE_FILEGROUP


            description         (str)   --  description for the subclient (optional)

                default: &#39;&#39;

            advanced_options    (dict)  --  dict of additional options needed to create
                                            subclient with additional properties
                                            default : None
                Example:
                    {
                        ondemand_subclient : True
                    }

            pre_scan_cmd        (str)   --  path to the batch file/shell script file to run
                                            before each backup of the subclient

        Returns:
            object  -   instance of the Subclient class

        Raises:
            SDKException:
                if subclient name argument is not of type string

                if storage policy argument is not of type string

                if description argument is not of type string

                if failed to create subclient

                if response is empty

                if response is not success

                if subclient already exists with the given name

    &#34;&#34;&#34;
    if not (isinstance(subclient_name, str) and
            isinstance(description, str)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if self.has_subclient(subclient_name):
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                subclient_name)
        )

    if self._backupset_object is None:
        if self._instance_object.backupsets.has_backupset(
                &#39;defaultBackupSet&#39;):
            self._backupset_object = self._instance_object.backupsets.get(
                &#39;defaultBackupSet&#39;)
        else:
            self._backupset_object = self._instance_object.backupsets.get(
                sorted(self._instance_object.backupsets.all_backupsets)[0]
            )

    if storage_policy and not self._commcell_object.storage_policies.has_policy(
            storage_policy):
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                storage_policy)
        )

    if advanced_options:
        if advanced_options.get(&#34;ondemand_subclient&#34;, False):
            ondemand_value = advanced_options.get(&#34;ondemand_subclient&#34;)
        else:
            ondemand_value = False
    else:
        ondemand_value = False

    request_json = {
        &#34;subClientProperties&#34;: {
            &#34;contentOperationType&#34;: 2,
            &#34;subClientEntity&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;appName&#34;: self._agent_object.agent_name,
                &#34;instanceName&#34;: self._instance_object.instance_name,
                &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                &#34;subclientName&#34;: subclient_name
            },
            &#34;commonProperties&#34;: {
                &#34;description&#34;: description,
                &#34;enableBackup&#34;: True,
                &#34;onDemandSubClient&#34;: ondemand_value,
                &#34;storageDevice&#34;: {
                    &#34;dataBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: storage_policy
                    }
                },
            }
        }
    }

    if storage_policy is None:
        del request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;storageDevice&#34;]

    if pre_scan_cmd is not None:
        request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;prepostProcess&#34;] = {
            &#34;runAs&#34;: 1,
            &#34;preScanCommand&#34;: pre_scan_cmd
        }

    if self._agent_object.agent_name == &#39;sql server&#39;:
        request_json[&#39;subClientProperties&#39;][&#39;mssqlSubClientProp&#39;] = {
            &#39;sqlSubclientType&#39;: self._sqlsubclient_type_dict[subclient_type]
        }

    return self._process_add_request(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.add_mysql_subclient"><code class="name flex">
<span>def <span class="ident">add_mysql_subclient</span></span>(<span>self, subclient_name, storage_policy, contents, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new mysql subclient to the instance.</p>
<h2 id="args">Args</h2>
<p>subclient_name
(str)
&ndash;
name of the new subclient to add</p>
<p>storage_policy
(str)
&ndash;
name of the storage policy to be associated
with the subclient</p>
<p>contents
(list)
&ndash;
database list to be added as subclient content</p>
<p>kwargs
(dict)
&ndash; dict of keyword arguments as follows</p>
<pre><code>no_of_backup_streams    (int)   --  No of backup streams to be used
default: 1

no_of_log_backup_streams    (int)   -- No of Transaction log backup streams
default: 1

full_instance_xtrabackup    (bool)  -- True if XtraBackup is selected for subclient
default: False
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Subclient class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if subclient name argument is not of type string</p>
<pre><code>if storage policy argument is not of type string

if conetnts argument is not of type list

if contents is empty list

if failed to create subclient

if response is empty

if response is not success

if subclient already exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L939-L1046" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_mysql_subclient(
        self,
        subclient_name,
        storage_policy,
        contents,
        **kwargs
):
    &#34;&#34;&#34;Adds a new mysql subclient to the instance.

        Args:
            subclient_name          (str)   --  name of the new subclient to add

            storage_policy          (str)   --  name of the storage policy to be associated
            with the subclient

            contents                (list)  --  database list to be added as subclient content

            kwargs      (dict)  -- dict of keyword arguments as follows

                no_of_backup_streams    (int)   --  No of backup streams to be used
                default: 1

                no_of_log_backup_streams    (int)   -- No of Transaction log backup streams
                default: 1

                full_instance_xtrabackup    (bool)  -- True if XtraBackup is selected for subclient
                default: False

        Returns:
            object  -   instance of the Subclient class

        Raises:
            SDKException:
                if subclient name argument is not of type string

                if storage policy argument is not of type string

                if conetnts argument is not of type list

                if contents is empty list

                if failed to create subclient

                if response is empty

                if response is not success

                if subclient already exists with the given name

    &#34;&#34;&#34;
    if not (isinstance(subclient_name, str) and
            isinstance(storage_policy, str) and
            isinstance(contents, list)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if self.has_subclient(subclient_name):
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                subclient_name)
        )

    if not self._commcell_object.storage_policies.has_policy(
            storage_policy):
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                storage_policy)
        )

    if not contents:
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            &#39;Content list cannot be empty&#39;
        )

    content_list = []
    for content in contents:
        content_list.append({&#34;mySQLContent&#34;: {&#34;databaseName&#34;: content}})

    request_json = {
        &#34;subClientProperties&#34;: {
            &#34;contentOperationType&#34;: 2,
            &#34;subClientEntity&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;appName&#34;: self._agent_object.agent_name,
                &#34;instanceName&#34;: self._instance_object.instance_name,
                &#34;backupsetName&#34;: &#34;defaultDummyBackupSet&#34;,
                &#34;subclientName&#34;: subclient_name
            },
            &#34;commonProperties&#34;: {
                &#34;storageDevice&#34;: {
                    &#34;dataBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: storage_policy
                    }
                },
            },
            &#34;mySqlSubclientProp&#34;: {
                &#34;numberOfBackupStreams&#34;: kwargs.get(&#39;no_of_backup_streams&#39;, 1),
                &#34;numberOfTransactionLogStreams&#34;: kwargs.get(&#39;no_of_log_backup_streams&#39;, 1),
                &#34;fullInstanceXtraBackup&#34;: kwargs.get(&#39;full_instance_xtrabackup&#39;, False)
            },
            &#34;content&#34;: content_list
        }
    }

    return self._process_add_request(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.add_onedrive_subclient"><code class="name flex">
<span>def <span class="ident">add_onedrive_subclient</span></span>(<span>self, subclient_name, server_plan)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new subclient to the backupset.</p>
<h2 id="args">Args</h2>
<p>subclient_name
(str)
&ndash;
name of the new subclient to add</p>
<p>server_plan
(str)
&ndash;
name of the server plan to be associated
with the subclient</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Subclient class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if subclient name argument is not of type string</p>
<pre><code>if server plan argument is not of type string

if description argument is not of type string

if failed to create subclient

if response is empty

if response is not success

if subclient already exists with the given name

if server plan  donot exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L1250-L1342" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_onedrive_subclient(self,
                           subclient_name,
                           server_plan):

    &#34;&#34;&#34;Adds a new subclient to the backupset.

        Args:
            subclient_name     (str)   --  name of the new subclient to add

            server_plan     (str)   --  name of the server plan to be associated
                                            with the subclient

        Returns:
            object  -   instance of the Subclient class

        Raises:
            SDKException:
                if subclient name argument is not of type string

                if server plan argument is not of type string

                if description argument is not of type string

                if failed to create subclient

                if response is empty

                if response is not success

                if subclient already exists with the given name

                if server plan  donot exists with the given name

            &#34;&#34;&#34;

    if not (isinstance(subclient_name, str) and
            isinstance(server_plan, str)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if self.has_subclient(subclient_name):
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                subclient_name)
        )

    if self._backupset_object is None:
        if self._instance_object.backupsets.has_backupset(
                self._instance_object.backupsets.default_backup_set):
            self._backupset_object = self._instance_object.backupsets.get(
                self._instance_object.backupsets.default_backup_set)
        else:
            self._backupset_object = self._instance_object.backupsets.get(
                sorted(self._instance_object.backupsets.all_backupsets)[0]
            )

    if self._commcell_object.plans.has_plan(server_plan):
        server_plan_object = self._commcell_object.plans.get(server_plan)
        server_plan_id = int(server_plan_object.plan_id)
    else:
        raise SDKException(&#39;Plan&#39;, &#39;102&#39;, &#39;Provide Valid Plan Name&#39;)

    request_json = {
        &#34;subClientProperties&#34;: {
            &#34;subClientEntity&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;instanceName&#34;: self._instance_object.instance_name,
                &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
                &#34;instanceId&#34;: int(self._instance_object.instance_id),
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;appName&#34;: self._agent_object.agent_name,
                &#34;applicationId&#34;: 134,
                &#34;subclientName&#34;: subclient_name
            },
            &#34;planEntity&#34;: {
                &#34;planId&#34;: server_plan_id
            },
            &#34;cloudAppsSubClientProp&#34;: {
                &#34;instanceType&#34;: 7,
                &#34;oneDriveSubclient&#34;: {
                    &#34;enableOneNote&#34;: False,
                    &#34;isEnterprise&#34;: True
                }
            },
            &#34;cloudconnectorSubclientProp&#34;: {
                &#34;isAutoDiscoveryEnabled&#34;: False
            },
            &#34;commonProperties&#34;: {
                &#34;enableBackup&#34;: True
            }
        }
    }

    return self._process_add_request(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.add_oracle_logical_dump_subclient"><code class="name flex">
<span>def <span class="ident">add_oracle_logical_dump_subclient</span></span>(<span>self, subclient_name, storage_policy, dump_dir, user_name, domain_name, password, full_mode, schema_value=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to add subclient for oracle logical dump.
This method add two type of subclient full mode
and schema mode. For full mode full_mode should be
true and schema_value should be none and for schema
mode full_mode should be false and schema_value should
be list of values.Rest of thing should be same for both.</p>
<h2 id="args">Args</h2>
<p>subclient_name
(Str)
&ndash;
subclient name for logical dump</p>
<p>storage_policy
(Str)
&ndash;
Storage policy for subclient</p>
<p>dump_dir
(Str)
&ndash;
dump directory for subclient</p>
<p>user_name
(Str)
&ndash;
username for oracle database</p>
<p>domain_name
(Str)
&ndash;
domainname for oracle database</p>
<p>password
(Str)
&ndash;
password for oracle database
(should be in encrypted and decrypted form)</p>
<p>full_mode
(bool) &ndash;
if ture then subclient for full mode otherwise schema mode</p>
<p>schema_value
(list) &ndash;
schema value for schema mode subclient</p>
<pre><code> default: None
</code></pre>
<h2 id="return">Return</h2>
<pre><code>object  -   instance of the Subclient class
</code></pre>
<p>Raises:
SDKException:
if subclient name argument is not of type string</p>
<pre><code>    if storage policy argument is not of type string

    if subclient name already present

    if storage policy does not exist
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L687-L834" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_oracle_logical_dump_subclient(
        self,
        subclient_name,
        storage_policy,
        dump_dir,
        user_name,
        domain_name,
        password,
        full_mode,
        schema_value=None):
    &#34;&#34;&#34;
    Method to add subclient for oracle logical dump.
    This method add two type of subclient full mode
    and schema mode. For full mode full_mode should be
    true and schema_value should be none and for schema
    mode full_mode should be false and schema_value should
    be list of values.Rest of thing should be same for both.
    Args:
          subclient_name     (Str)  --  subclient name for logical dump

          storage_policy     (Str)  --  Storage policy for subclient

          dump_dir            (Str)  --  dump directory for subclient

          user_name           (Str)  --  username for oracle database

          domain_name         (Str)  --  domainname for oracle database

          password           (Str)  --  password for oracle database
                                        (should be in encrypted and decrypted form)

          full_mode           (bool) --  if ture then subclient for full mode otherwise schema mode

          schema_value        (list) --  schema value for schema mode subclient

               default: None
    Return:
            object  -   instance of the Subclient class

        Raises:
            SDKException:
                if subclient name argument is not of type string

                if storage policy argument is not of type string

                if subclient name already present

                if storage policy does not exist

    &#34;&#34;&#34;
    if not (isinstance(subclient_name, str) and
            isinstance(storage_policy, str) and
            isinstance(dump_dir, str) and
            isinstance(user_name, str) and
            isinstance(domain_name, str) and
            isinstance(password, str) and
            isinstance(full_mode, bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
    if (full_mode == False and not
    isinstance(schema_value, list)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if self.has_subclient(subclient_name):
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                subclient_name)
        )

    if self._backupset_object is None:
        if self._instance_object.backupsets.has_backupset(
                &#39;defaultBackupSet&#39;):
            self._backupset_object = self._instance_object.backupsets.get(
                &#39;defaultBackupSet&#39;)
        else:
            self._backupset_object = self._instance_object.backupsets.get(
                sorted(self._instance_object.backupsets.all_backupsets)[0]
            )

    if not self._commcell_object.storage_policies.has_policy(
            storage_policy):
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                storage_policy)
        )

    request_json = {
        &#34;subClientProperties&#34;: {
            &#34;subClientEntity&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;instanceName&#34;: self._instance_object.instance_name,
                &#34;appName&#34;: self._agent_object.agent_name,
                &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                &#34;subclientName&#34;: subclient_name
            },
            &#34;oracleSubclientProp&#34;: {
                &#34;data&#34;: False,
                &#34;archiveDelete&#34;: False,
                &#34;useSQLConntect&#34;: False,
                &#34;dbSubclientType&#34;: 2,
                &#34;mergeIncImageCopies&#34;: False,
                &#34;selectiveOnlineFull&#34;: False,
                &#34;protectBackupRecoveryArea&#34;: False,
                &#34;selectArchiveLogDestForBackup&#34;: False,
                &#34;backupSPFile&#34;: False,
                &#34;backupControlFile&#34;: False,
                &#34;backupArchiveLog&#34;: False,
                &#34;validate&#34;: False,
            },
            &#34;commonProperties&#34;: {
                &#34;snapCopyInfo&#34;: {
                    &#34;useSeparateProxyForSnapToTape&#34;: False,
                    &#34;checkProxyForSQLIntegrity&#34;: False,
                    &#34;snapToTapeProxyToUseSource&#34;: False,
                    &#34;isSnapBackupEnabled&#34;: False,
                    &#34;IsOracleSposDriverEnabled&#34;: False,
                    &#34;isRMANEnableForTapeMovement&#34;: False
                },
                &#34;dbDumpConfig&#34;: {
                    &#34;fullMode&#34;: True,
                    &#34;database&#34;: &#34;&#34;,
                    &#34;dumpDir&#34;: dump_dir,
                    &#34;parallelism&#34;: 2,
                    &#34;overrideInstanceUser&#34;: True,
                    &#34;sqlConnect&#34;: {
                        &#34;password&#34;: b64encode(password.encode()).decode(),
                        &#34;domainName&#34;: domain_name,
                        &#34;userName&#34;: user_name
                    }
                },
                &#34;storageDevice&#34;: {
                    &#34;dataBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: storage_policy
                    },
                    &#34;deDuplicationOptions&#34;: {
                        &#34;enableDeduplication&#34;: True
                    }
                }
            }
        }
    }

    if (full_mode == False):
        request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;dbDumpConfig&#34;][&#34;fullMode&#34;] = False
        request_json[&#34;subClientProperties&#34;][&#34;commonProperties&#34;][&#34;dbDumpConfig&#34;][&#34;schema&#34;] = schema_value

    return self._process_add_request(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.add_postgresql_subclient"><code class="name flex">
<span>def <span class="ident">add_postgresql_subclient</span></span>(<span>self, subclient_name, storage_policy, contents, no_of_streams=1, collect_object_list=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new postgresql subclient to the backupset.</p>
<h2 id="args">Args</h2>
<p>subclient_name
(str)
&ndash;
name of the new subclient to add</p>
<p>storage_policy
(str)
&ndash;
name of the storage policy to be associated
with the subclient</p>
<p>contents
(list)
&ndash;
database list to be added as subclient content</p>
<p>no_of_streams
(int)
&ndash;
No of backup streams to be used</p>
<pre><code>default: 1
</code></pre>
<p>collect_object_list
(bool)
&ndash;
Boolean flag to determine if collect object
list needs to be enabled for subclient or not</p>
<pre><code>default: False
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Subclient class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if subclient name argument is not of type string</p>
<pre><code>if storage policy argument is not of type string

if conetnts argument is not of type list

if contents is empty list

if failed to create subclient

if response is empty

if response is not success

if subclient already exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L836-L937" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_postgresql_subclient(
        self, subclient_name, storage_policy,
        contents, no_of_streams=1, collect_object_list=False):
    &#34;&#34;&#34;Adds a new postgresql subclient to the backupset.

        Args:
            subclient_name          (str)   --  name of the new subclient to add

            storage_policy          (str)   --  name of the storage policy to be associated
            with the subclient

            contents                (list)  --  database list to be added as subclient content


            no_of_streams           (int)   --  No of backup streams to be used

                default: 1

            collect_object_list     (bool)  --  Boolean flag to determine if collect object
            list needs to be enabled for subclient or not

                default: False

        Returns:
            object  -   instance of the Subclient class

        Raises:
            SDKException:
                if subclient name argument is not of type string

                if storage policy argument is not of type string

                if conetnts argument is not of type list

                if contents is empty list

                if failed to create subclient

                if response is empty

                if response is not success

                if subclient already exists with the given name

    &#34;&#34;&#34;
    if not (isinstance(subclient_name, str) and
            isinstance(storage_policy, str) and
            isinstance(contents, list)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if self.has_subclient(subclient_name):
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                subclient_name)
        )

    if not self._commcell_object.storage_policies.has_policy(
            storage_policy):
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                storage_policy)
        )

    if not contents:
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            &#39;Content list cannot be empty&#39;
        )

    content_list = []
    for content in contents:
        content_list.append({&#34;postgreSQLContent&#34;: {&#34;databaseName&#34;: content}})

    request_json = {
        &#34;subClientProperties&#34;: {
            &#34;contentOperationType&#34;: 2,
            &#34;subClientEntity&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;appName&#34;: self._agent_object.agent_name,
                &#34;instanceName&#34;: self._instance_object.instance_name,
                &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                &#34;subclientName&#34;: subclient_name
            },
            &#34;commonProperties&#34;: {
                &#34;storageDevice&#34;: {
                    &#34;dataBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: storage_policy
                    }
                },
            },
            &#34;postgreSQLSubclientProp&#34;: {
                &#34;numberOfBackupStreams&#34;: no_of_streams,
                &#34;collectObjectListDuringBackup&#34;: collect_object_list
            },
            &#34;content&#34;: content_list
        }
    }

    return self._process_add_request(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.add_virtual_server_subclient"><code class="name flex">
<span>def <span class="ident">add_virtual_server_subclient</span></span>(<span>self, subclient_name, subclient_content, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new virtual server subclient to the backupset.</p>
<h2 id="args">Args</h2>
<p>subclient_name
(str)
&ndash;
Name of the subclient to be created</p>
<p>subclient_content
(list)
&ndash;
Content to be added to the subclient</p>
<pre><code>Example 1:
    [{
        'equal_value': True,
        'allOrAnyChildren': True,
        'id': '',
        'path': '',
        'display_name': 'sample1',
        'type': VSAObjects.VMName
    }]
Example 2:
     [{
    'allOrAnyChildren': False,
    'content': [{
        'equal_value': True,
        'allOrAnyChildren': True,
        'display_name': 'sample1',
        'type': VSAObjects.VMName
    }, {
        'equal_value': True,
        'allOrAnyChildren': True,
        'display_name': 'sample2',
        'type': VSAObjects.VMName
    }
    ]
    }, {
    'allOrAnyChildren': True,
    'content': [{
        'equal_value': True,
        'allOrAnyChildren': True,
        'display_name': 'sample3',
        'type': VSAObjects.RESOURCE_POOL
    }, {
        'equal_value': True,
        'allOrAnyChildren': True,
        'id': 'sample4',
        'display_name': 'sample4',
        'type': VSAObjects.SERVER
        }
        ]
    }
    ]
    **Note** Use VSAObjects Enum present in constants.py to pass value to type
</code></pre>
<p>kwargs
(dict)
&ndash; dict of keyword arguments as follows</p>
<pre><code>plan_name           (str)   --  Plan to be associated with the subclient

storage_policy      (str)   --  Storage policy to be associated with the subclient

description         (str)   --  Description for the subclient

    default: ''
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Subclient class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if subclient name argument is not of type string</p>
<pre><code>if storage policy argument is not of type string

if description argument is not of type string

if failed to create subclient

if response is empty

if response is not success

if subclient already exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L1048-L1248" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_virtual_server_subclient(
        self,
        subclient_name,
        subclient_content,
        **kwargs
):
    &#34;&#34;&#34;Adds a new virtual server subclient to the backupset.

        Args:
            subclient_name      (str)   --  Name of the subclient to be created

            subclient_content   (list)  --  Content to be added to the subclient

                Example 1:
                    [{
                        &#39;equal_value&#39;: True,
                        &#39;allOrAnyChildren&#39;: True,
                        &#39;id&#39;: &#39;&#39;,
                        &#39;path&#39;: &#39;&#39;,
                        &#39;display_name&#39;: &#39;sample1&#39;,
                        &#39;type&#39;: VSAObjects.VMName
                    }]
                Example 2:
                     [{
                    &#39;allOrAnyChildren&#39;: False,
                    &#39;content&#39;: [{
                        &#39;equal_value&#39;: True,
                        &#39;allOrAnyChildren&#39;: True,
                        &#39;display_name&#39;: &#39;sample1&#39;,
                        &#39;type&#39;: VSAObjects.VMName
                    }, {
                        &#39;equal_value&#39;: True,
                        &#39;allOrAnyChildren&#39;: True,
                        &#39;display_name&#39;: &#39;sample2&#39;,
                        &#39;type&#39;: VSAObjects.VMName
                    }
                    ]
                    }, {
                    &#39;allOrAnyChildren&#39;: True,
                    &#39;content&#39;: [{
                        &#39;equal_value&#39;: True,
                        &#39;allOrAnyChildren&#39;: True,
                        &#39;display_name&#39;: &#39;sample3&#39;,
                        &#39;type&#39;: VSAObjects.RESOURCE_POOL
                    }, {
                        &#39;equal_value&#39;: True,
                        &#39;allOrAnyChildren&#39;: True,
                        &#39;id&#39;: &#39;sample4&#39;,
                        &#39;display_name&#39;: &#39;sample4&#39;,
                        &#39;type&#39;: VSAObjects.SERVER
                        }
                        ]
                    }
                    ]
                    **Note** Use VSAObjects Enum present in constants.py to pass value to type

            kwargs      (dict)  -- dict of keyword arguments as follows

                plan_name           (str)   --  Plan to be associated with the subclient

                storage_policy      (str)   --  Storage policy to be associated with the subclient

                description         (str)   --  Description for the subclient

                    default: &#39;&#39;

        Returns:
            object  -   instance of the Subclient class

        Raises:
            SDKException:
                if subclient name argument is not of type string

                if storage policy argument is not of type string

                if description argument is not of type string

                if failed to create subclient

                if response is empty

                if response is not success

                if subclient already exists with the given name

    &#34;&#34;&#34;
    if not (isinstance(subclient_name, str) and
            isinstance(subclient_content, list)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if self.has_subclient(subclient_name):
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient &#34;{0}&#34; already exists.&#39;.format(
                subclient_name)
        )

    if self._backupset_object is None:
        if self._instance_object.backupsets.has_backupset(
                &#39;defaultBackupSet&#39;):
            self._backupset_object = self._instance_object.backupsets.get(
                &#39;defaultBackupSet&#39;)
        else:
            self._backupset_object = self._instance_object.backupsets.get(
                sorted(self._instance_object.backupsets.all_backupsets)[0]
            )

    content = []

    def set_content(item_content):
        &#34;&#34;&#34;
        create content dictionary
        Args:
            item_content            (dict):     Dict of content details

            Example:
                {
                    &#39;equal_value&#39;: True,
                    &#39;allOrAnyChildren&#39;: True,
                    &#39;display_name&#39;: &#39;sample1&#39;,
                    &#39;type&#39;:  &lt; VSAObjects.VMName: 10 &gt;
                }

        Returns:

        &#34;&#34;&#34;
        return {
            &#34;equalsOrNotEquals&#34;: item_content.get(&#39;equal_value&#39;, True),
            &#34;name&#34;: item_content.get(&#39;id&#39;, &#39;&#39;),
            &#34;displayName&#34;: item_content.get(&#39;display_name&#39;, &#39;&#39;),
            &#34;path&#34;: item_content.get(&#39;path&#39;, &#39;&#39;),
            &#34;allOrAnyChildren&#34;: item.get(&#39;allOrAnyChildren&#39;, True),
            &#34;type&#34;: item_content[&#39;type&#39;] if isinstance(item_content[&#39;type&#39;], int) else item_content[&#39;type&#39;].value
        }

    for item in subclient_content:
        _temp_list = []
        _temp_dict = {}
        allOrAnyChildren = item.get(&#39;allOrAnyChildren&#39;, None)
        if &#39;content&#39; in item:
            nested_content = item[&#39;content&#39;]
            for each_condition in nested_content:
                temp_dict = set_content(each_condition)
                _temp_list.append(temp_dict)
            _temp_dict[&#39;allOrAnyChildren&#39;] = allOrAnyChildren
            _temp_dict[&#39;children&#39;] = _temp_list
            content.append(_temp_dict)
        else:
            temp_dict = set_content(item)
            content.append(temp_dict)

    request_json = {
        &#34;subClientProperties&#34;: {
            &#34;vmContentOperationType&#34;: 2,
            &#34;vmContent&#34;: {
                &#34;children&#34;: content
            },
            &#34;subClientEntity&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;appName&#34;: self._agent_object.agent_name,
                &#34;instanceName&#34;: self._instance_object.instance_name,
                &#34;backupsetName&#34;: self._backupset_object.backupset_name,
                &#34;subclientName&#34;: subclient_name
            },
            &#34;commonProperties&#34;: {
                &#34;description&#34;: kwargs.get(&#39;description&#39;),
                &#34;enableBackup&#34;: True
            }
        }
    }

    if kwargs.get(&#34;customSnapshotResourceGroup&#34;):
        request_json[&#34;subClientProperties&#34;][&#34;vsaSubclientProp&#34;] = \
            {&#34;customSnapshotResourceGroup&#34;: kwargs.get(&#34;customSnapshotResourceGroup&#34;)}

    if kwargs.get(&#39;plan_name&#39;):
        if not self._commcell_object.plans.has_plan(kwargs[&#39;plan_name&#39;]):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Plan: &#34;{0}&#34; does not exist in the Commcell&#39;.format(kwargs[&#39;plan_name&#39;])
            )
        request_json[&#39;subClientProperties&#39;][&#39;planEntity&#39;] = {
            &#34;planName&#34;: kwargs[&#39;plan_name&#39;]
        }

    elif kwargs.get(&#39;storage_policy&#39;):
        if not self._commcell_object.storage_policies.has_policy(kwargs.get(&#39;storage_policy&#39;)):
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(kwargs.get(&#39;storage_policy&#39;))
            )
        request_json[&#39;subClientProperties&#39;][&#39;commonProperties&#39;][&#39;storageDevice&#39;] = {
            &#34;dataBackupStoragePolicy&#34;: {
                &#34;storagePolicyName&#34;: kwargs.get(&#39;storage_policy&#39;)
            }
        }
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Either Plan or Storage policy should be given as input&#39;)

    return self._process_add_request(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, subclient_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the subclient specified by the subclient_name from the backupset.</p>
<h2 id="args">Args</h2>
<p>subclient_name (str)
&ndash;
name of the subclient to remove from the backupset</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the subclient name argument is not string</p>
<pre><code>if failed to delete subclient

if response is empty

if response is not success

if no subclient exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L1379-L1446" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, subclient_name):
    &#34;&#34;&#34;Deletes the subclient specified by the subclient_name from the backupset.

        Args:
            subclient_name (str)  --  name of the subclient to remove from the backupset

        Raises:
            SDKException:
                if type of the subclient name argument is not string

                if failed to delete subclient

                if response is empty

                if response is not success

                if no subclient exists with the given name
    &#34;&#34;&#34;
    if not isinstance(subclient_name, str):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
    else:
        subclient_name = subclient_name.lower()

    if self.has_subclient(subclient_name):
        delete_subclient_service = self._services[&#39;SUBCLIENT&#39;] % (
            self._subclients[subclient_name][&#39;id&#39;]
        )

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, delete_subclient_service)

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_value = response.json()[&#39;response&#39;][0]
                    error_code = str(response_value[&#39;errorCode&#39;])
                    error_message = None

                    if &#39;errorString&#39; in response_value:
                        error_message = response_value[&#39;errorString&#39;]

                    if error_message:
                        o_str = &#39;Failed to delete subclient\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Subclient&#39;, &#39;102&#39;, o_str.format(error_message))
                    else:
                        if error_code == &#39;0&#39;:
                            # initialize the subclients again
                            # so the subclient object has all the
                            # subclients
                            self.refresh()
                        else:
                            o_str = (&#39;Failed to delete subclient with Error Code: &#34;{0}&#34;\n&#39;
                                     &#39;Please check the documentation for &#39;
                                     &#39;more details on the error&#39;)
                            raise SDKException(
                                &#39;Subclient&#39;, &#39;102&#39;, o_str.format(error_code))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                    response.text))
    else:
        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;No subclient exists with name: {0}&#39;.format(
                subclient_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, subclient_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a subclient object of the specified backupset name.</p>
<h2 id="args">Args</h2>
<p>subclient_name (str)
&ndash;
name of the subclient</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class for the given subclient name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the subclient name argument is not string</p>
<pre><code>if no subclient exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L1344-L1377" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, subclient_name):
    &#34;&#34;&#34;Returns a subclient object of the specified backupset name.

        Args:
            subclient_name (str)  --  name of the subclient

        Returns:
            object - instance of the Subclient class for the given subclient name

        Raises:
            SDKException:
                if type of the subclient name argument is not string

                if no subclient exists with the given name
    &#34;&#34;&#34;
    if not isinstance(subclient_name, str):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
    else:
        subclient_name = subclient_name.lower()

        if self.has_subclient(subclient_name):

            if self._backupset_object is None:
                self._backupset_object = self._instance_object.backupsets.get(
                    self._subclients[subclient_name][&#39;backupset&#39;]
                )
            return Subclient(
                self._backupset_object, subclient_name, self._subclients[subclient_name][&#39;id&#39;]
            )

        raise SDKException(
            &#39;Subclient&#39;, &#39;102&#39;, &#39;No subclient exists with name: {0}&#39;.format(
                subclient_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.has_subclient"><code class="name flex">
<span>def <span class="ident">has_subclient</span></span>(<span>self, subclient_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a subclient exists in the commcell with the input subclient name.</p>
<h2 id="args">Args</h2>
<p>subclient_name (str)
&ndash;
name of the subclient</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the subclient exists in the backupset or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the subclient name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L496-L512" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_subclient(self, subclient_name):
    &#34;&#34;&#34;Checks if a subclient exists in the commcell with the input subclient name.

        Args:
            subclient_name (str)  --  name of the subclient

        Returns:
            bool - boolean output whether the subclient exists in the backupset or not

        Raises:
            SDKException:
                if type of the subclient name argument is not string
    &#34;&#34;&#34;
    if not isinstance(subclient_name, str):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    return self._subclients and subclient_name.lower() in self._subclients</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclient.Subclients.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the subclients associated with the Backupset / Instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclient.py#L1448-L1450" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the subclients associated with the Backupset / Instance.&#34;&#34;&#34;
    self._subclients = self._get_subclients()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#subclients">Subclients:</a></li>
<li><a href="#subclient">Subclient:</a></li>
<li><a href="#subclient-instance-attributes">Subclient Instance Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclient.Subclient" href="#cvpysdk.subclient.Subclient">Subclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclient.Subclient.allow_multiple_readers" href="#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.backup" href="#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.browse" href="#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.data_readers" href="#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.deduplication_options" href="#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.description" href="#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_backup" href="#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_intelli_snap" href="#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.display_name" href="#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup" href="#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup_at_time" href="#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_intelli_snap" href="#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup" href="#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup_days" href="#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.encryption_flag" href="#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.exclude_from_sla" href="#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find" href="#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find_latest_job" href="#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy" href="#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_backup_enabled" href="#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled" href="#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_default_subclient" href="#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_intelli_snap_enabled" href="#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_on_demand_subclient" href="#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_trueup_enabled" href="#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.last_backup_time" href="#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.list_media" href="#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.name" href="#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.network_agent" href="#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.next_backup_time" href="#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.plan" href="#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.properties" href="#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.read_buffer_size" href="#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.refresh" href="#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_in_place" href="#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_out_of_place" href="#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.run_content_indexing" href="#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_backup_nodes" href="#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_proxy_for_snap" href="#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.snapshot_engine_name" href="#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.software_compression" href="#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma" href="#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma_id" href="#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_policy" href="#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_guid" href="#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_id" href="#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_name" href="#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.unset_proxy_for_snap" href="#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.update_properties" href="#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.subclient.Subclients" href="#cvpysdk.subclient.Subclients">Subclients</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclient.Subclients.add" href="#cvpysdk.subclient.Subclients.add">add</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_mysql_subclient" href="#cvpysdk.subclient.Subclients.add_mysql_subclient">add_mysql_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_onedrive_subclient" href="#cvpysdk.subclient.Subclients.add_onedrive_subclient">add_onedrive_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_oracle_logical_dump_subclient" href="#cvpysdk.subclient.Subclients.add_oracle_logical_dump_subclient">add_oracle_logical_dump_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_postgresql_subclient" href="#cvpysdk.subclient.Subclients.add_postgresql_subclient">add_postgresql_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_virtual_server_subclient" href="#cvpysdk.subclient.Subclients.add_virtual_server_subclient">add_virtual_server_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.all_subclients" href="#cvpysdk.subclient.Subclients.all_subclients">all_subclients</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.default_subclient" href="#cvpysdk.subclient.Subclients.default_subclient">default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.delete" href="#cvpysdk.subclient.Subclients.delete">delete</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.get" href="#cvpysdk.subclient.Subclients.get">get</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.has_subclient" href="#cvpysdk.subclient.Subclients.has_subclient">has_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.refresh" href="#cvpysdk.subclient.Subclients.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>