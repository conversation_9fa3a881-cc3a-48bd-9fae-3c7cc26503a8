<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.credential_manager API documentation</title>
<meta name="description" content="Main file for managing credentials records on this commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.credential_manager</code></h1>
</header>
<section id="section-intro">
<p>Main file for managing credentials records on this commcell</p>
<p>Credentials and Credential are only the two classes defined in this commcell</p>
<h2 id="credentials">Credentials</h2>
<p><strong>init</strong>()
&ndash;
initializes the Credentials class object</p>
<p><strong>str</strong>()
&ndash;
returns all the Credentials associated
with the commcell</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the Credentials class</p>
<p>_get_credentials()
&ndash;
Returns the list of Credentials configured on this commcell</p>
<p>all_credentials()
&ndash;
Returns all the Credentials present in the commcell</p>
<p>has_credential()
&ndash;
Checks if any Credentials with specified name exists on
this commcell</p>
<p>get()
&ndash;
Returns the Credential object for the specified Credential name</p>
<p>add()
&ndash;
creates the credential record on this commcell</p>
<p>refresh()
&ndash;
refreshes the list of credentials on this commcell</p>
<p>delete()
&ndash;
deletes the credential record on this commcell</p>
<p>get_security_associations() &ndash;
Returns the security association dictionary for a given user or user group</p>
<p>add_db2_database_creds
&ndash;
Creates DB2 credential on this commcell</p>
<p>add_postgres_database_creds &ndash;
Creates PostgreSQL credential on this commcell</p>
<p>add_mysql_database_creds
&ndash;
Creates MySQL credential on this commcell</p>
<p>add_azure_cloud_creds()
&ndash;
Creates azure access key based credential on this commcell</p>
<p>add_azure_cosmosdb_creds()
&ndash;
Creates credential for azure cosmos db using azure application
id and application secret key </p>
<p>add_aws_s3_creds()
&ndash;
Creates aws s3 credential</p>
<h2 id="credential">Credential</h2>
<p><strong>init</strong>()
&ndash;
initiaizes the credential class object</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the
credential class</p>
<p>_get_credential_id()
&ndash;
Gets the Credential id associated with this Credential</p>
<p>credential_name
&ndash;
Returns the name of the credential record</p>
<p>credential_id
&ndash;
Returns the id of the credential record</p>
<p>credential_description
&ndash;
Returns the description set of credential record</p>
<p>credential_user_name
&ndash;
Returns the user name set in the credential record</p>
<p>update_user_credential
&ndash;
Sets the value for credential user name and password with
the parameters provided</p>
<p>refresh()
&ndash;
refreshes the properties of credential account</p>
<p>_get_credential_properties()&ndash;
returns the properties of credential account</p>
<p>_update_credential_props()
&ndash; Updates credential account properties</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L1-L1019" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for managing credentials records on this commcell

Credentials and Credential are only the two classes defined in this commcell

Credentials:
    __init__()                  --  initializes the Credentials class object

    __str__()                   --  returns all the Credentials associated
                                    with the commcell

    __repr__()                  --  returns the string for the instance of the Credentials class

    _get_credentials()          --  Returns the list of Credentials configured on this commcell

    all_credentials()           --  Returns all the Credentials present in the commcell

    has_credential()            --  Checks if any Credentials with specified name exists on
                                    this commcell

    get()                       --  Returns the Credential object for the specified Credential name

    add()                       --  creates the credential record on this commcell

    refresh()                   --  refreshes the list of credentials on this commcell

    delete()                    --  deletes the credential record on this commcell

    get_security_associations() --  Returns the security association dictionary for a given user or user group

    add_db2_database_creds      --  Creates DB2 credential on this commcell

    add_postgres_database_creds --  Creates PostgreSQL credential on this commcell

    add_mysql_database_creds    --  Creates MySQL credential on this commcell

    add_azure_cloud_creds()     --  Creates azure access key based credential on this commcell

    add_azure_cosmosdb_creds()  --  Creates credential for azure cosmos db using azure application
                                        id and application secret key 

    add_aws_s3_creds()          --  Creates aws s3 credential

Credential:
    __init__()                  --  initiaizes the credential class object

    __repr__()                  --  returns the string for the instance of the
                                    credential class

    _get_credential_id()        --  Gets the Credential id associated with this Credential

    credential_name             --  Returns the name of the credential record

    credential_id               --  Returns the id of the credential record

    credential_description      --  Returns the description set of credential record

    credential_user_name        --  Returns the user name set in the credential record

    update_user_credential      --  Sets the value for credential user name and password with
                                    the parameters provided

    refresh()                   --  refreshes the properties of credential account

    _get_credential_properties()--  returns the properties of credential account

    _update_credential_props()  -- Updates credential account properties


&#34;&#34;&#34;

from base64 import b64encode
from .security.usergroup import UserGroups
from .exception import SDKException
from .constants import Credential_Type


class Credentials(object):
    &#34;&#34;&#34;Class for maintaining all the configured credential on this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes the credentials class object for this commcell

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Clients class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._credentials = self._get_credentials()
        self.record_type = {
            &#39;windows&#39;: 1,
            &#39;linux&#39;: 2
        }

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all Credentials of the commcell.

            Returns:
                str - string of all the Credentials configured on the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Credentials&#39;)

        for index, credentials in enumerate(self._credentials):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, credentials)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Credentials class.&#34;&#34;&#34;
        return &#34;Credentials class instance for Commcell&#34;

    def _get_credentials(self):
        &#34;&#34;&#34;Returns the Credentials configured on this commcell

        Raises:
            Exception if response is not success
        &#34;&#34;&#34;
        get_all_credential_service = self._services[&#39;ALL_CREDENTIALS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, get_all_credential_service
        )

        if flag:
            credentials_dict = {}
            if response.json() and &#39;credentialRecordInfo&#39; in response.json():

                for credential in response.json()[&#39;credentialRecordInfo&#39;]:
                    temp_id = credential[&#39;credentialRecord&#39;][&#39;credentialId&#39;]
                    temp_name = credential[&#39;credentialRecord&#39;][&#39;credentialName&#39;].lower()
                    credentials_dict[temp_name] = temp_id

            return credentials_dict
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_credentials(self):
        &#34;&#34;&#34;&#34;Returns all the Credentials present in the commcell&#34;&#34;&#34;
        return self._credentials

    def has_credential(self, credential_name):
        &#34;&#34;&#34;Checks if any Credentials with specified name exists on this commcell

            Args:
                credential_name         (str)     --    name of the Credential which has to be
                                                        checked if exists

            Retruns:
                Bool- True if specified Credential is present on the commcell else false

            Raises:
                SDKException:
                    if data type of input is invalid
        &#34;&#34;&#34;
        if not isinstance(credential_name, str):
            raise SDKException(&#39;Credentials&#39;, &#39;101&#39;)

        return self._credentials and credential_name.lower() in self._credentials

    def get(self, credential_name):
        &#34;&#34;&#34;Returns the Credential object for the specified Credential name

            Args:
                credential_name  (str)    --  name of the Credential for which the object has to
                                              be created
            Raises:
                SDKException:
                    if Credential doesn&#39;t exist with specified name
        &#34;&#34;&#34;
        if not self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} doesn&#39;t exists on this commcell.&#34;.format(
                    credential_name)
            )

        return Credential(self._commcell_object, credential_name, self._credentials[
            credential_name.lower()])

    def add(self, record_type, credential_name, user_name, user_password, description=None):
        &#34;&#34;&#34;Creates credential account on this commcell

            Args:
                record_type     (str)   -- type of credential record to be created (windows/linux)

                credential_name (str)   --  name to be given to credential account

                user_name       (str)   --  name of the user to be associated to this credential
                                            account

                user_password   (str)   --  password for user

                description     (str)   --  description for credential account

            Raises:
                SDKException:
                    if credential account is already present on the commcell

                    if string format are not proper

                    if response is not successful

        &#34;&#34;&#34;

        if not (isinstance(credential_name, str) and isinstance(user_name, str)):
            raise SDKException(&#39;User&#39;, &#39;101&#39;)

        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;User {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )
        password = b64encode(user_password.encode()).decode()

        record = {
            &#34;userName&#34;: user_name,
            &#34;password&#34;: password
        }
        create_credential_account = {
            &#34;credentialRecordInfo&#34;: [{
                &#34;recordType&#34;: self.record_type[record_type.lower()],
                &#34;description&#34;: description,
                &#34;credentialRecord&#34;: {
                    &#34;credentialName&#34;: credential_name
                },
                &#34;record&#34;: record,
            }]
        }

        request = self._services[&#39;CREDENTIAL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential_account
        )
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;Refresh the list of credential records on this commcell.&#34;&#34;&#34;
        self._credentials = self._get_credentials()

    def delete(self, credential_name):
        &#34;&#34;&#34;Deletes the credential object for specified credential name

            Args:
                credential_name (str) --  name of the credential for which the object has to be
                                          deleted

            Raises:
                SDKException:
                    if credential doesn&#39;t exist

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;credential {0} doesn&#39;t exists on this commcell.&#34;.format(
                    credential_name)
            )

        delete_credential = self._services[&#39;DELETE_RECORD&#39;]

        request_json = {
            &#34;credentialRecordInfo&#34;: [{
                &#34;credentialRecord&#34;: {
                    &#34;credentialName&#34;: credential_name
                }
            }]
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, delete_credential, request_json
        )
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def get_security_associations(self, owner, is_user=False):
        &#34;&#34;&#34;
        Returns the security association dictionary for a given user or user group
        Args:
            owner(str)          -   Owner of the user or user group
            is_user(bool)       -   True if the owner is a user
                                    False if the owner is a user group

        Returns:
            dict containing the security association
        &#34;&#34;&#34;
        if is_user is True:
            userOrGroupInfo = {
                &#34;entityTypeName&#34;: &#34;USER_ENTITY&#34;,
                &#34;userGroupName&#34;: owner,
                &#34;userGroupId&#34;: int(self._commcell_object.users.get(owner).user_id)
            }
        else:
            userOrGroupInfo = {
                &#34;entityTypeName&#34;: &#34;USERGROUP_ENTITY&#34;,
                &#34;userGroupName&#34;: owner,
                &#34;userGroupId&#34;: int(self._commcell_object.user_groups.get(owner).user_group_id)
            }
        security_association = {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        userOrGroupInfo
                    ],
                    &#34;properties&#34;: {
                        &#34;isCreatorAssociation&#34;: False,
                        &#34;permissions&#34;: [
                            {
                                &#34;permissionId&#34;: 218,
                                &#34;_type_&#34;: 122,
                                &#34;permissionName&#34;: &#34;User Credential&#34;
                            }
                        ]
                    }
                }
            ]
        }
        return security_association

    def add_db2_database_creds(self, credential_name, username, password, description=None):
        &#34;&#34;&#34;Creates db2 credential on this commcell
            Args:

                credential_name (str)   --  name to be given to credential account

                username  (str)         --  name of the db2 credential

                password   (str)        --  password for the credential

                description (str)       --  description of the credential

            Raises:
                SDKException:
                    if credential account is already present on the commcell

                    if response is not successful
        &#34;&#34;&#34;
        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )
        password = b64encode(password.encode()).decode()
        create_credential = {
            &#34;accountType&#34;: &#34;DATABASE_ACCOUNT&#34;,
            &#34;databaseCredentialType&#34;: &#34;DB2&#34;,
            &#34;name&#34;: credential_name,
            &#34;username&#34;: username,
            &#34;password&#34;: password,
            &#34;description&#34;: description
        }

        request = self._services[&#39;ADD_CREDENTIALS&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential
        )
        if flag:
            if response.json():
                id = response.json()[&#39;id&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()
        return Credential(self._commcell_object, credential_name, id)

    def add_postgres_database_creds(self, credential_name, username, password, description=None):
        &#34;&#34;&#34;Creates PostgreSQL credential on this commcell
            Args:

                credential_name (str)   --  name to be given to credential account

                username  (str)         --  PostgreSQL username

                password   (str)        --  PostgreSQL password

                description (str)       --  description of the credential

            Raises:
                SDKException:
                    if credential account is already present on the commcell

                    if response is not successful
        &#34;&#34;&#34;
        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )
        password = b64encode(password.encode()).decode()
        create_credential = {
            &#34;accountType&#34;: &#34;DATABASE_ACCOUNT&#34;,
            &#34;databaseCredentialType&#34;: &#34;POSTGRESQL&#34;,
            &#34;name&#34;: credential_name,
            &#34;username&#34;: username,
            &#34;password&#34;: password,
            &#34;description&#34;: description
        }

        request = self._services[&#39;ADD_CREDENTIALS&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential
        )
        if flag:
            if response.json():
                id = response.json()[&#39;id&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()
        return Credential(self._commcell_object, credential_name, id)

    def add_mysql_database_creds(self, credential_name, username, password, description=None):
        &#34;&#34;&#34;Creates MySQL credential on this commcell
            Args:

                credential_name (str)   --  name to be given to credential account

                username  (str)         --  MySQL username

                password   (str)        --  MySQL password

                description (str)       --  description of the credential

            Raises:
                SDKException:
                    if credential account is already present on the commcell

                    if response is not successful
        &#34;&#34;&#34;
        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )
        password = b64encode(password.encode()).decode()
        create_credential = {
            &#34;accountType&#34;: &#34;DATABASE_ACCOUNT&#34;,
            &#34;databaseCredentialType&#34;: &#34;MYSQL&#34;,
            &#34;name&#34;: credential_name,
            &#34;username&#34;: username,
            &#34;password&#34;: password,
            &#34;description&#34;: description
        }

        request = self._services[&#39;ADD_CREDENTIALS&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential
        )
        if flag:
            if response.json():
                id = response.json()[&#39;id&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()
        return Credential(self._commcell_object, credential_name, id)

    def add_azure_cloud_creds(self, credential_name, account_name, access_key_id, **kwargs):
        &#34;&#34;&#34;Creates azure access key based credential on this commcell

            Args:

                credential_name (str)   --  name to be given to credential account

                account_name  (str)     --  name of the azure storage account

                access_key_id   (str)   --  access key for azure storage

                ** kwargs(dict)         --  Key value pairs for supported arguments

                Supported argument values:
                    description(str)            -   description of the credentials

            Raises:
                SDKException:
                    if arguments type is incorrect

                    if credential account is already present on the commcell

                    if string format are not proper

                    if response is not successful

        &#34;&#34;&#34;
        description = kwargs.get(&#34;description&#34;, &#34;&#34;)

        if not (isinstance(access_key_id, str)  and isinstance(account_name, str)
                 and isinstance(credential_name, str)):
            raise SDKException(&#34;Credential&#34;, &#34;101&#34;)

        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )

        password = b64encode(access_key_id.encode()).decode()
        additional_information = {
            &#34;azureCredInfo&#34;: {
                &#34;authType&#34;: &#34;AZURE_OAUTH_SHARED_SECRET&#34;,
                &#34;endpoints&#34;: {
                    &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                    &#34;storageEndpoint&#34;: &#34;blob.core.windows.net&#34;,
                    &#34;resourceManagerEndpoint&#34;: &#34;https://management.azure.com/&#34;
                }
            }
        }
        create_credential_account = {
            &#34;credentialRecordInfo&#34;: [
                {
                    &#34;credentialRecord&#34;: {
                        &#34;credentialName&#34;: credential_name
                    },

                    &#34;record&#34;: {
                        &#34;userName&#34;: account_name,
                        &#34;password&#34;: password
                    },
                    &#34;recordType&#34;: &#34;MICROSOFT_AZURE&#34;,
                    &#34;additionalInformation&#34;: additional_information,
                    &#34;description&#34;: description
                }
            ]
        }

        request = self._services[&#39;CREDENTIAL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential_account
        )
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def add_azure_cosmosdb_creds(
            self,
            credential_name,
            tenant_id,
            application_id,
            application_secret,
            description=&#34;&#34;):
        &#34;&#34;&#34;Creates azure application id and application secret key based credential on this commcell

            Args:

                credential_name (str)   --  name to be given to credential account

                tenant_id  (str)     --    name of tenant id

                application_id   (str)   --  application id

                application_secret  (str)  - application secet

                description(str)            -   description of the credentials

            Raises:
                SDKException:
                    if arguments type is incorrect

                    if credential account is already present on the commcell

                    if response is not successful

        &#34;&#34;&#34;

        if not (
            isinstance(
                application_id,
                str) and isinstance(
                tenant_id,
                str) and isinstance(
                credential_name,
                str) and isinstance(
                    application_secret,
                str)):
            raise SDKException(&#34;Credential&#34;, &#34;101&#34;)

        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;,
                &#39;102&#39;,
                &#34;Credential {0} already exists on this commcell.&#34;.format(credential_name))

        password = b64encode(application_secret.encode()).decode()

        create_credential_account = {
            &#34;credentialRecordInfo&#34;: [
                {
                    &#34;additionalInformation&#34;: {
                        &#34;azureCredInfo&#34;: {
                            &#34;applicationId&#34;: application_id,
                            &#34;endpoints&#34;: {
                                &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                &#34;resourceManagerEndpoint&#34;: &#34;https://management.azure.com/&#34;,
                                &#34;storageEndpoint&#34;: &#34;blob.core.windows.net&#34;},
                            &#34;environment&#34;: &#34;AzureCloud&#34;,
                            &#34;tenantId&#34;: tenant_id}},
                    &#34;createAs&#34;: {},
                    &#34;credentialRecord&#34;: {
                        &#34;credentialName&#34;: credential_name},
                    &#34;description&#34;: description,
                    &#34;record&#34;: {
                        &#34;userName&#34;: application_id,
                        &#34;password&#34;: password},
                    &#34;recordType&#34;: &#34;AZUREACCOUNT&#34;,
                    &#34;securityAssociations&#34;: {
                        &#34;associations&#34;: [],
                        &#34;associationsOperationType&#34;: 1}}]}

        request = self._services[&#39;CREDENTIAL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential_account
        )
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def add_aws_s3_creds(
            self,
            credential_name,
            access_key_id,
            secret_access_key,
            description=None):
        &#34;&#34;&#34;Creates aws s3 access key based credential on this commcell

            Args:

                credential_name (str)   --  name to be given to credential account

                access_key_id   (str)   --  access key id for aws S3 bucket

                secrete_access_key (str) -- secrete access key for aws s3 bucket

                description(str)         -- description of the credentials

            Raises:
                SDKException:
                    if arguments type is incorrect

                    if credential account is already present on the commcell

                    if response is not successful

        &#34;&#34;&#34;

        if not (
            isinstance(
                access_key_id,
                str) and isinstance(
                secret_access_key,
                str) and isinstance(
                credential_name,
                str)):
            raise SDKException(&#34;Credential&#34;, &#34;101&#34;)

        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;,
                &#39;102&#39;,
                &#34;Credential {0} already exists on this commcell.&#34;.format(credential_name))

        password = b64encode(secret_access_key.encode()).decode()
        create_credential_account = {
            &#34;credentialRecordInfo&#34;: [
                {
                    &#34;createAs&#34;: {
                    },
                    &#34;credentialRecord&#34;: {
                        &#34;credentialName&#34;: credential_name
                    },
                    &#34;description&#34;: description,
                    &#34;record&#34;: {
                        &#34;userName&#34;: access_key_id,
                        &#34;password&#34;: password
                    },
                    &#34;recordType&#34;: &#34;AMAZON_S3&#34;,
                    &#34;securityAssociations&#34;: {
                        &#34;associations&#34;: [
                        ],
                        &#34;associationsOperationType&#34;: 1
                    }
                }
            ]
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CREDENTIAL&#39;], payload=create_credential_account)
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()



class Credential(object):
    &#34;&#34;&#34;&#34;Class for representing a particular Credential record on this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, credential_name, credential_id=None):
        &#34;&#34;&#34;Initialize the Credential class object for specified Credential

            Args:
                commcell_object         (object)    --  instance of the Commcell class

                credential_name         (str)       --  name of the Credential

                credential_id           (str)       --  id of the credential
                    default: None

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._credential_name = credential_name.lower()

        if credential_id is None:
            self._credential_id = self._get_credential_id(self._credential_name)
        else:
            self._credential_id = credential_id

        self._credential_description = None
        self._credential_user_name = None
        self._credential_properties = None
        self._credential_security_assoc = []
        self._record_type = None
        self._credential_password = &#34;&#34;
        self._record_types = {
            1: &#39;Windows&#39;,
            2: &#39;Linux&#39;
        }
        self._get_credential_properties()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Credential class instance for Credential: &#34;{0}&#34;&#39;
        return representation_string.format(self.credential_name)

    def _get_credential_id(self, name):
        &#34;&#34;&#34;Gets the Credential id associated with this Credential.

            Args:
                name    (str)   --  credential account name

            Returns:
                str - id associated with this Credential
        &#34;&#34;&#34;
        creds = Credentials(self._commcell_object)
        return creds.get(credential_name=name)._credential_id

    @property
    def credential_name(self):
        &#34;&#34;&#34;Returns the name of the credential record&#34;&#34;&#34;
        return self._credential_name

    @credential_name.setter
    def credential_name(self, val):
        &#34;&#34;&#34;Sets the value for credential record with the parameter provided

        &#34;&#34;&#34;
        props_dict = {
            &#34;credentialRecord&#34;: {
                &#34;credentialId&#34;: self._credential_id,
                &#34;credentialName&#34;: val
            }
        }
        self._update_credential_props(properties_dict=props_dict)

    @property
    def credential_id(self):
        &#34;&#34;&#34;Returns the Credential id of this commcell Credential record&#34;&#34;&#34;
        return self._credential_id

    @property
    def credential_description(self):
        &#34;&#34;&#34;Returns the Credential_desccription of this commcell Credential reord&#34;&#34;&#34;
        return self._credential_properties.get(&#39;description&#39;)

    @credential_description.setter
    def credential_description(self, value):
        &#34;&#34;&#34;Sets the description for this commcell Credential record&#34;&#34;&#34;
        props_dict = {
            &#34;description&#34;: value
        }
        self._update_credential_props(props_dict)

    @property
    def credential_security_properties(self):
        &#34;&#34;&#34;Returns the Credential&#39;s security association&#34;&#34;&#34;
        return self._credential_security_assoc

    def update_securtiy(self, name, is_user=True):
        &#34;&#34;&#34;Updates the security association for this commcell Credential record

        Args:
            name    (str)   -- User or UserGroupName
            is_user (bool)  -- Set False for UserGroup

        &#34;&#34;&#34;

        props_dict = {
            &#34;securityAssociations&#34;: {
                &#34;associationsOperationType&#34;: 1,
                &#34;associations&#34;: [{
                    &#34;userOrGroup&#34;: [{
                        &#34;_type_&#34;: 13 if is_user else 15,
                        &#34;userName&#34; if is_user else &#34;userGroupName&#34;: name
                    }],
                    &#34;properties&#34;: {
                        &#34;isCreatorAssociation&#34;: False,
                        &#34;permissions&#34;: [{
                            &#34;permissionId&#34;: 218,
                            &#34;_type_&#34;: 122,
                            &#34;permissionName&#34;: &#34;User Credential&#34;
                        }]
                    }
                }],
                &#34;ownerAssociations&#34;: {}
            }
        }

        return self._update_credential_props(props_dict)

    @property
    def credential_user_name(self):
        &#34;&#34;&#34;Returns the Credential name of this commcell Credential record&#34;&#34;&#34;
        return self._credential_user_name

    def update_user_credential(self, uname, upassword):
        &#34;&#34;&#34;Sets the value for credential user name and password with the parameters provided
            Args:
                uname   (str)   --  new user name

                upassword(str)  --  new password for user

        &#34;&#34;&#34;
        creds_dict = {
            &#34;record&#34;: {
                &#34;userName&#34;: uname,
                &#34;password&#34;: b64encode(upassword.encode()).decode()
            }
        }
        self._update_credential_props(properties_dict=creds_dict)

    @property
    def credential_record_type(self):
        &#34;&#34;&#34;Returns the Credential name of this commcell Credential record&#34;&#34;&#34;
        return self._record_types[self._record_type]

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Credentials.&#34;&#34;&#34;
        self._get_credential_properties()

    def _get_credential_properties(self):
        &#34;&#34;&#34;Gets the properties of this Credential record&#34;&#34;&#34;
        property_request = self._services[&#39;ONE_CREDENTIAL&#39;] % (
            self._credential_name)
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, property_request
        )

        if flag:
            if response.json() and &#39;credentialRecordInfo&#39; in response.json():
                json_resp = response.json()
                self._credential_properties = response.json()[&#39;credentialRecordInfo&#39;][0]

                self._credential_id = self._credential_properties[&#39;credentialRecord&#39;].get(
                    &#39;credentialId&#39;)
                self._credential_name = self._credential_properties[&#39;credentialRecord&#39;].get(
                    &#39;credentialName&#39;)
                self._credential_user_name = self._credential_properties[&#39;record&#39;][&#39;userName&#39;]
                self._record_type = self._credential_properties[&#39;recordType&#39;]
                security = self._credential_properties.get(&#39;securityAssociations&#39;, {})
                if &#34;associations&#34; in security:
                    for each in security[&#39;associations&#39;]:
                        for userorgroup in each[&#34;userOrGroup&#34;]:
                            if &#34;userName&#34; in userorgroup:
                                self._credential_security_assoc.append(userorgroup[&#34;userName&#34;])
                            else:
                                self._credential_security_assoc.append(userorgroup[&#34;userGroupName&#34;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update_credential_props(self, properties_dict):
        &#34;&#34;&#34;Updates the properties of this credential

            Args:
                properties_dict (dict)  --  credential property dict which is to be updated
                    e.g.: {
                            &#34;description&#34;: &#34;My description&#34;
                        }

            Returns:
                credential Properties update dict

            Raises:
                SDKException:
                    if credential doesn&#39;t exist

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if &#34;record&#34; in properties_dict:
            self._credential_user_name = properties_dict[&#39;record&#39;][&#39;userName&#39;]
            self._credential_password = properties_dict.get(&#39;record&#39;, {}).get(&#39;password&#39;, {})

        if &#34;credentialRecord&#34; in properties_dict:
            self._credential_name = properties_dict[&#39;credentialRecord&#39;][&#39;credentialName&#39;]

        request_json = {
            &#34;credentialRecordInfo&#34;: [{
                &#34;recordType&#34;: self._record_type,
                &#34;credentialRecord&#34;: {
                    &#34;credentialId&#34;: self._credential_id,
                    &#34;credentialName&#34;: self._credential_name
                },
                &#34;record&#34;: {
                    &#34;userName&#34;: self._credential_user_name
                }
            }]
        }
        if &#34;securityAssociations&#34; in properties_dict:
            request_json[&#39;credentialRecordInfo&#39;][0].update(securityAssociations=properties_dict[&#39;securityAssociations&#39;])

        request = self._services[&#39;CREDENTIAL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, request, request_json
        )

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.credential_manager.Credential"><code class="flex name class">
<span>class <span class="ident">Credential</span></span>
<span>(</span><span>commcell_object, credential_name, credential_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>"Class for representing a particular Credential record on this commcell</p>
<p>Initialize the Credential class object for specified Credential</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>credential_name
(str)
&ndash;
name of the Credential</p>
<p>credential_id
(str)
&ndash;
id of the credential
default: None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L779-L1019" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Credential(object):
    &#34;&#34;&#34;&#34;Class for representing a particular Credential record on this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, credential_name, credential_id=None):
        &#34;&#34;&#34;Initialize the Credential class object for specified Credential

            Args:
                commcell_object         (object)    --  instance of the Commcell class

                credential_name         (str)       --  name of the Credential

                credential_id           (str)       --  id of the credential
                    default: None

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._credential_name = credential_name.lower()

        if credential_id is None:
            self._credential_id = self._get_credential_id(self._credential_name)
        else:
            self._credential_id = credential_id

        self._credential_description = None
        self._credential_user_name = None
        self._credential_properties = None
        self._credential_security_assoc = []
        self._record_type = None
        self._credential_password = &#34;&#34;
        self._record_types = {
            1: &#39;Windows&#39;,
            2: &#39;Linux&#39;
        }
        self._get_credential_properties()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Credential class instance for Credential: &#34;{0}&#34;&#39;
        return representation_string.format(self.credential_name)

    def _get_credential_id(self, name):
        &#34;&#34;&#34;Gets the Credential id associated with this Credential.

            Args:
                name    (str)   --  credential account name

            Returns:
                str - id associated with this Credential
        &#34;&#34;&#34;
        creds = Credentials(self._commcell_object)
        return creds.get(credential_name=name)._credential_id

    @property
    def credential_name(self):
        &#34;&#34;&#34;Returns the name of the credential record&#34;&#34;&#34;
        return self._credential_name

    @credential_name.setter
    def credential_name(self, val):
        &#34;&#34;&#34;Sets the value for credential record with the parameter provided

        &#34;&#34;&#34;
        props_dict = {
            &#34;credentialRecord&#34;: {
                &#34;credentialId&#34;: self._credential_id,
                &#34;credentialName&#34;: val
            }
        }
        self._update_credential_props(properties_dict=props_dict)

    @property
    def credential_id(self):
        &#34;&#34;&#34;Returns the Credential id of this commcell Credential record&#34;&#34;&#34;
        return self._credential_id

    @property
    def credential_description(self):
        &#34;&#34;&#34;Returns the Credential_desccription of this commcell Credential reord&#34;&#34;&#34;
        return self._credential_properties.get(&#39;description&#39;)

    @credential_description.setter
    def credential_description(self, value):
        &#34;&#34;&#34;Sets the description for this commcell Credential record&#34;&#34;&#34;
        props_dict = {
            &#34;description&#34;: value
        }
        self._update_credential_props(props_dict)

    @property
    def credential_security_properties(self):
        &#34;&#34;&#34;Returns the Credential&#39;s security association&#34;&#34;&#34;
        return self._credential_security_assoc

    def update_securtiy(self, name, is_user=True):
        &#34;&#34;&#34;Updates the security association for this commcell Credential record

        Args:
            name    (str)   -- User or UserGroupName
            is_user (bool)  -- Set False for UserGroup

        &#34;&#34;&#34;

        props_dict = {
            &#34;securityAssociations&#34;: {
                &#34;associationsOperationType&#34;: 1,
                &#34;associations&#34;: [{
                    &#34;userOrGroup&#34;: [{
                        &#34;_type_&#34;: 13 if is_user else 15,
                        &#34;userName&#34; if is_user else &#34;userGroupName&#34;: name
                    }],
                    &#34;properties&#34;: {
                        &#34;isCreatorAssociation&#34;: False,
                        &#34;permissions&#34;: [{
                            &#34;permissionId&#34;: 218,
                            &#34;_type_&#34;: 122,
                            &#34;permissionName&#34;: &#34;User Credential&#34;
                        }]
                    }
                }],
                &#34;ownerAssociations&#34;: {}
            }
        }

        return self._update_credential_props(props_dict)

    @property
    def credential_user_name(self):
        &#34;&#34;&#34;Returns the Credential name of this commcell Credential record&#34;&#34;&#34;
        return self._credential_user_name

    def update_user_credential(self, uname, upassword):
        &#34;&#34;&#34;Sets the value for credential user name and password with the parameters provided
            Args:
                uname   (str)   --  new user name

                upassword(str)  --  new password for user

        &#34;&#34;&#34;
        creds_dict = {
            &#34;record&#34;: {
                &#34;userName&#34;: uname,
                &#34;password&#34;: b64encode(upassword.encode()).decode()
            }
        }
        self._update_credential_props(properties_dict=creds_dict)

    @property
    def credential_record_type(self):
        &#34;&#34;&#34;Returns the Credential name of this commcell Credential record&#34;&#34;&#34;
        return self._record_types[self._record_type]

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Credentials.&#34;&#34;&#34;
        self._get_credential_properties()

    def _get_credential_properties(self):
        &#34;&#34;&#34;Gets the properties of this Credential record&#34;&#34;&#34;
        property_request = self._services[&#39;ONE_CREDENTIAL&#39;] % (
            self._credential_name)
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, property_request
        )

        if flag:
            if response.json() and &#39;credentialRecordInfo&#39; in response.json():
                json_resp = response.json()
                self._credential_properties = response.json()[&#39;credentialRecordInfo&#39;][0]

                self._credential_id = self._credential_properties[&#39;credentialRecord&#39;].get(
                    &#39;credentialId&#39;)
                self._credential_name = self._credential_properties[&#39;credentialRecord&#39;].get(
                    &#39;credentialName&#39;)
                self._credential_user_name = self._credential_properties[&#39;record&#39;][&#39;userName&#39;]
                self._record_type = self._credential_properties[&#39;recordType&#39;]
                security = self._credential_properties.get(&#39;securityAssociations&#39;, {})
                if &#34;associations&#34; in security:
                    for each in security[&#39;associations&#39;]:
                        for userorgroup in each[&#34;userOrGroup&#34;]:
                            if &#34;userName&#34; in userorgroup:
                                self._credential_security_assoc.append(userorgroup[&#34;userName&#34;])
                            else:
                                self._credential_security_assoc.append(userorgroup[&#34;userGroupName&#34;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update_credential_props(self, properties_dict):
        &#34;&#34;&#34;Updates the properties of this credential

            Args:
                properties_dict (dict)  --  credential property dict which is to be updated
                    e.g.: {
                            &#34;description&#34;: &#34;My description&#34;
                        }

            Returns:
                credential Properties update dict

            Raises:
                SDKException:
                    if credential doesn&#39;t exist

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if &#34;record&#34; in properties_dict:
            self._credential_user_name = properties_dict[&#39;record&#39;][&#39;userName&#39;]
            self._credential_password = properties_dict.get(&#39;record&#39;, {}).get(&#39;password&#39;, {})

        if &#34;credentialRecord&#34; in properties_dict:
            self._credential_name = properties_dict[&#39;credentialRecord&#39;][&#39;credentialName&#39;]

        request_json = {
            &#34;credentialRecordInfo&#34;: [{
                &#34;recordType&#34;: self._record_type,
                &#34;credentialRecord&#34;: {
                    &#34;credentialId&#34;: self._credential_id,
                    &#34;credentialName&#34;: self._credential_name
                },
                &#34;record&#34;: {
                    &#34;userName&#34;: self._credential_user_name
                }
            }]
        }
        if &#34;securityAssociations&#34; in properties_dict:
            request_json[&#39;credentialRecordInfo&#39;][0].update(securityAssociations=properties_dict[&#39;securityAssociations&#39;])

        request = self._services[&#39;CREDENTIAL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, request, request_json
        )

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.credential_manager.Credential.credential_description"><code class="name">var <span class="ident">credential_description</span></code></dt>
<dd>
<div class="desc"><p>Returns the Credential_desccription of this commcell Credential reord</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L855-L858" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def credential_description(self):
    &#34;&#34;&#34;Returns the Credential_desccription of this commcell Credential reord&#34;&#34;&#34;
    return self._credential_properties.get(&#39;description&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credential.credential_id"><code class="name">var <span class="ident">credential_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the Credential id of this commcell Credential record</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L850-L853" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def credential_id(self):
    &#34;&#34;&#34;Returns the Credential id of this commcell Credential record&#34;&#34;&#34;
    return self._credential_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credential.credential_name"><code class="name">var <span class="ident">credential_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the name of the credential record</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L832-L835" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def credential_name(self):
    &#34;&#34;&#34;Returns the name of the credential record&#34;&#34;&#34;
    return self._credential_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credential.credential_record_type"><code class="name">var <span class="ident">credential_record_type</span></code></dt>
<dd>
<div class="desc"><p>Returns the Credential name of this commcell Credential record</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L926-L929" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def credential_record_type(self):
    &#34;&#34;&#34;Returns the Credential name of this commcell Credential record&#34;&#34;&#34;
    return self._record_types[self._record_type]</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credential.credential_security_properties"><code class="name">var <span class="ident">credential_security_properties</span></code></dt>
<dd>
<div class="desc"><p>Returns the Credential's security association</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L868-L871" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def credential_security_properties(self):
    &#34;&#34;&#34;Returns the Credential&#39;s security association&#34;&#34;&#34;
    return self._credential_security_assoc</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credential.credential_user_name"><code class="name">var <span class="ident">credential_user_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the Credential name of this commcell Credential record</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L905-L908" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def credential_user_name(self):
    &#34;&#34;&#34;Returns the Credential name of this commcell Credential record&#34;&#34;&#34;
    return self._credential_user_name</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.credential_manager.Credential.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the Credentials.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L931-L933" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the Credentials.&#34;&#34;&#34;
    self._get_credential_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credential.update_securtiy"><code class="name flex">
<span>def <span class="ident">update_securtiy</span></span>(<span>self, name, is_user=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the security association for this commcell Credential record</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash; User or UserGroupName
is_user (bool)
&ndash; Set False for UserGroup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L873-L903" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_securtiy(self, name, is_user=True):
    &#34;&#34;&#34;Updates the security association for this commcell Credential record

    Args:
        name    (str)   -- User or UserGroupName
        is_user (bool)  -- Set False for UserGroup

    &#34;&#34;&#34;

    props_dict = {
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [{
                &#34;userOrGroup&#34;: [{
                    &#34;_type_&#34;: 13 if is_user else 15,
                    &#34;userName&#34; if is_user else &#34;userGroupName&#34;: name
                }],
                &#34;properties&#34;: {
                    &#34;isCreatorAssociation&#34;: False,
                    &#34;permissions&#34;: [{
                        &#34;permissionId&#34;: 218,
                        &#34;_type_&#34;: 122,
                        &#34;permissionName&#34;: &#34;User Credential&#34;
                    }]
                }
            }],
            &#34;ownerAssociations&#34;: {}
        }
    }

    return self._update_credential_props(props_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credential.update_user_credential"><code class="name flex">
<span>def <span class="ident">update_user_credential</span></span>(<span>self, uname, upassword)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the value for credential user name and password with the parameters provided</p>
<h2 id="args">Args</h2>
<p>uname
(str)
&ndash;
new user name</p>
<p>upassword(str)
&ndash;
new password for user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L910-L924" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_user_credential(self, uname, upassword):
    &#34;&#34;&#34;Sets the value for credential user name and password with the parameters provided
        Args:
            uname   (str)   --  new user name

            upassword(str)  --  new password for user

    &#34;&#34;&#34;
    creds_dict = {
        &#34;record&#34;: {
            &#34;userName&#34;: uname,
            &#34;password&#34;: b64encode(upassword.encode()).decode()
        }
    }
    self._update_credential_props(properties_dict=creds_dict)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.credential_manager.Credentials"><code class="flex name class">
<span>class <span class="ident">Credentials</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for maintaining all the configured credential on this commcell</p>
<p>Initializes the credentials class object for this commcell</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Clients class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L95-L775" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Credentials(object):
    &#34;&#34;&#34;Class for maintaining all the configured credential on this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes the credentials class object for this commcell

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Clients class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._credentials = self._get_credentials()
        self.record_type = {
            &#39;windows&#39;: 1,
            &#39;linux&#39;: 2
        }

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all Credentials of the commcell.

            Returns:
                str - string of all the Credentials configured on the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Credentials&#39;)

        for index, credentials in enumerate(self._credentials):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, credentials)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Credentials class.&#34;&#34;&#34;
        return &#34;Credentials class instance for Commcell&#34;

    def _get_credentials(self):
        &#34;&#34;&#34;Returns the Credentials configured on this commcell

        Raises:
            Exception if response is not success
        &#34;&#34;&#34;
        get_all_credential_service = self._services[&#39;ALL_CREDENTIALS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, get_all_credential_service
        )

        if flag:
            credentials_dict = {}
            if response.json() and &#39;credentialRecordInfo&#39; in response.json():

                for credential in response.json()[&#39;credentialRecordInfo&#39;]:
                    temp_id = credential[&#39;credentialRecord&#39;][&#39;credentialId&#39;]
                    temp_name = credential[&#39;credentialRecord&#39;][&#39;credentialName&#39;].lower()
                    credentials_dict[temp_name] = temp_id

            return credentials_dict
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_credentials(self):
        &#34;&#34;&#34;&#34;Returns all the Credentials present in the commcell&#34;&#34;&#34;
        return self._credentials

    def has_credential(self, credential_name):
        &#34;&#34;&#34;Checks if any Credentials with specified name exists on this commcell

            Args:
                credential_name         (str)     --    name of the Credential which has to be
                                                        checked if exists

            Retruns:
                Bool- True if specified Credential is present on the commcell else false

            Raises:
                SDKException:
                    if data type of input is invalid
        &#34;&#34;&#34;
        if not isinstance(credential_name, str):
            raise SDKException(&#39;Credentials&#39;, &#39;101&#39;)

        return self._credentials and credential_name.lower() in self._credentials

    def get(self, credential_name):
        &#34;&#34;&#34;Returns the Credential object for the specified Credential name

            Args:
                credential_name  (str)    --  name of the Credential for which the object has to
                                              be created
            Raises:
                SDKException:
                    if Credential doesn&#39;t exist with specified name
        &#34;&#34;&#34;
        if not self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} doesn&#39;t exists on this commcell.&#34;.format(
                    credential_name)
            )

        return Credential(self._commcell_object, credential_name, self._credentials[
            credential_name.lower()])

    def add(self, record_type, credential_name, user_name, user_password, description=None):
        &#34;&#34;&#34;Creates credential account on this commcell

            Args:
                record_type     (str)   -- type of credential record to be created (windows/linux)

                credential_name (str)   --  name to be given to credential account

                user_name       (str)   --  name of the user to be associated to this credential
                                            account

                user_password   (str)   --  password for user

                description     (str)   --  description for credential account

            Raises:
                SDKException:
                    if credential account is already present on the commcell

                    if string format are not proper

                    if response is not successful

        &#34;&#34;&#34;

        if not (isinstance(credential_name, str) and isinstance(user_name, str)):
            raise SDKException(&#39;User&#39;, &#39;101&#39;)

        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;User {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )
        password = b64encode(user_password.encode()).decode()

        record = {
            &#34;userName&#34;: user_name,
            &#34;password&#34;: password
        }
        create_credential_account = {
            &#34;credentialRecordInfo&#34;: [{
                &#34;recordType&#34;: self.record_type[record_type.lower()],
                &#34;description&#34;: description,
                &#34;credentialRecord&#34;: {
                    &#34;credentialName&#34;: credential_name
                },
                &#34;record&#34;: record,
            }]
        }

        request = self._services[&#39;CREDENTIAL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential_account
        )
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;Refresh the list of credential records on this commcell.&#34;&#34;&#34;
        self._credentials = self._get_credentials()

    def delete(self, credential_name):
        &#34;&#34;&#34;Deletes the credential object for specified credential name

            Args:
                credential_name (str) --  name of the credential for which the object has to be
                                          deleted

            Raises:
                SDKException:
                    if credential doesn&#39;t exist

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;credential {0} doesn&#39;t exists on this commcell.&#34;.format(
                    credential_name)
            )

        delete_credential = self._services[&#39;DELETE_RECORD&#39;]

        request_json = {
            &#34;credentialRecordInfo&#34;: [{
                &#34;credentialRecord&#34;: {
                    &#34;credentialName&#34;: credential_name
                }
            }]
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, delete_credential, request_json
        )
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def get_security_associations(self, owner, is_user=False):
        &#34;&#34;&#34;
        Returns the security association dictionary for a given user or user group
        Args:
            owner(str)          -   Owner of the user or user group
            is_user(bool)       -   True if the owner is a user
                                    False if the owner is a user group

        Returns:
            dict containing the security association
        &#34;&#34;&#34;
        if is_user is True:
            userOrGroupInfo = {
                &#34;entityTypeName&#34;: &#34;USER_ENTITY&#34;,
                &#34;userGroupName&#34;: owner,
                &#34;userGroupId&#34;: int(self._commcell_object.users.get(owner).user_id)
            }
        else:
            userOrGroupInfo = {
                &#34;entityTypeName&#34;: &#34;USERGROUP_ENTITY&#34;,
                &#34;userGroupName&#34;: owner,
                &#34;userGroupId&#34;: int(self._commcell_object.user_groups.get(owner).user_group_id)
            }
        security_association = {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        userOrGroupInfo
                    ],
                    &#34;properties&#34;: {
                        &#34;isCreatorAssociation&#34;: False,
                        &#34;permissions&#34;: [
                            {
                                &#34;permissionId&#34;: 218,
                                &#34;_type_&#34;: 122,
                                &#34;permissionName&#34;: &#34;User Credential&#34;
                            }
                        ]
                    }
                }
            ]
        }
        return security_association

    def add_db2_database_creds(self, credential_name, username, password, description=None):
        &#34;&#34;&#34;Creates db2 credential on this commcell
            Args:

                credential_name (str)   --  name to be given to credential account

                username  (str)         --  name of the db2 credential

                password   (str)        --  password for the credential

                description (str)       --  description of the credential

            Raises:
                SDKException:
                    if credential account is already present on the commcell

                    if response is not successful
        &#34;&#34;&#34;
        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )
        password = b64encode(password.encode()).decode()
        create_credential = {
            &#34;accountType&#34;: &#34;DATABASE_ACCOUNT&#34;,
            &#34;databaseCredentialType&#34;: &#34;DB2&#34;,
            &#34;name&#34;: credential_name,
            &#34;username&#34;: username,
            &#34;password&#34;: password,
            &#34;description&#34;: description
        }

        request = self._services[&#39;ADD_CREDENTIALS&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential
        )
        if flag:
            if response.json():
                id = response.json()[&#39;id&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()
        return Credential(self._commcell_object, credential_name, id)

    def add_postgres_database_creds(self, credential_name, username, password, description=None):
        &#34;&#34;&#34;Creates PostgreSQL credential on this commcell
            Args:

                credential_name (str)   --  name to be given to credential account

                username  (str)         --  PostgreSQL username

                password   (str)        --  PostgreSQL password

                description (str)       --  description of the credential

            Raises:
                SDKException:
                    if credential account is already present on the commcell

                    if response is not successful
        &#34;&#34;&#34;
        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )
        password = b64encode(password.encode()).decode()
        create_credential = {
            &#34;accountType&#34;: &#34;DATABASE_ACCOUNT&#34;,
            &#34;databaseCredentialType&#34;: &#34;POSTGRESQL&#34;,
            &#34;name&#34;: credential_name,
            &#34;username&#34;: username,
            &#34;password&#34;: password,
            &#34;description&#34;: description
        }

        request = self._services[&#39;ADD_CREDENTIALS&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential
        )
        if flag:
            if response.json():
                id = response.json()[&#39;id&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()
        return Credential(self._commcell_object, credential_name, id)

    def add_mysql_database_creds(self, credential_name, username, password, description=None):
        &#34;&#34;&#34;Creates MySQL credential on this commcell
            Args:

                credential_name (str)   --  name to be given to credential account

                username  (str)         --  MySQL username

                password   (str)        --  MySQL password

                description (str)       --  description of the credential

            Raises:
                SDKException:
                    if credential account is already present on the commcell

                    if response is not successful
        &#34;&#34;&#34;
        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )
        password = b64encode(password.encode()).decode()
        create_credential = {
            &#34;accountType&#34;: &#34;DATABASE_ACCOUNT&#34;,
            &#34;databaseCredentialType&#34;: &#34;MYSQL&#34;,
            &#34;name&#34;: credential_name,
            &#34;username&#34;: username,
            &#34;password&#34;: password,
            &#34;description&#34;: description
        }

        request = self._services[&#39;ADD_CREDENTIALS&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential
        )
        if flag:
            if response.json():
                id = response.json()[&#39;id&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()
        return Credential(self._commcell_object, credential_name, id)

    def add_azure_cloud_creds(self, credential_name, account_name, access_key_id, **kwargs):
        &#34;&#34;&#34;Creates azure access key based credential on this commcell

            Args:

                credential_name (str)   --  name to be given to credential account

                account_name  (str)     --  name of the azure storage account

                access_key_id   (str)   --  access key for azure storage

                ** kwargs(dict)         --  Key value pairs for supported arguments

                Supported argument values:
                    description(str)            -   description of the credentials

            Raises:
                SDKException:
                    if arguments type is incorrect

                    if credential account is already present on the commcell

                    if string format are not proper

                    if response is not successful

        &#34;&#34;&#34;
        description = kwargs.get(&#34;description&#34;, &#34;&#34;)

        if not (isinstance(access_key_id, str)  and isinstance(account_name, str)
                 and isinstance(credential_name, str)):
            raise SDKException(&#34;Credential&#34;, &#34;101&#34;)

        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                    credential_name)
            )

        password = b64encode(access_key_id.encode()).decode()
        additional_information = {
            &#34;azureCredInfo&#34;: {
                &#34;authType&#34;: &#34;AZURE_OAUTH_SHARED_SECRET&#34;,
                &#34;endpoints&#34;: {
                    &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                    &#34;storageEndpoint&#34;: &#34;blob.core.windows.net&#34;,
                    &#34;resourceManagerEndpoint&#34;: &#34;https://management.azure.com/&#34;
                }
            }
        }
        create_credential_account = {
            &#34;credentialRecordInfo&#34;: [
                {
                    &#34;credentialRecord&#34;: {
                        &#34;credentialName&#34;: credential_name
                    },

                    &#34;record&#34;: {
                        &#34;userName&#34;: account_name,
                        &#34;password&#34;: password
                    },
                    &#34;recordType&#34;: &#34;MICROSOFT_AZURE&#34;,
                    &#34;additionalInformation&#34;: additional_information,
                    &#34;description&#34;: description
                }
            ]
        }

        request = self._services[&#39;CREDENTIAL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential_account
        )
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def add_azure_cosmosdb_creds(
            self,
            credential_name,
            tenant_id,
            application_id,
            application_secret,
            description=&#34;&#34;):
        &#34;&#34;&#34;Creates azure application id and application secret key based credential on this commcell

            Args:

                credential_name (str)   --  name to be given to credential account

                tenant_id  (str)     --    name of tenant id

                application_id   (str)   --  application id

                application_secret  (str)  - application secet

                description(str)            -   description of the credentials

            Raises:
                SDKException:
                    if arguments type is incorrect

                    if credential account is already present on the commcell

                    if response is not successful

        &#34;&#34;&#34;

        if not (
            isinstance(
                application_id,
                str) and isinstance(
                tenant_id,
                str) and isinstance(
                credential_name,
                str) and isinstance(
                    application_secret,
                str)):
            raise SDKException(&#34;Credential&#34;, &#34;101&#34;)

        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;,
                &#39;102&#39;,
                &#34;Credential {0} already exists on this commcell.&#34;.format(credential_name))

        password = b64encode(application_secret.encode()).decode()

        create_credential_account = {
            &#34;credentialRecordInfo&#34;: [
                {
                    &#34;additionalInformation&#34;: {
                        &#34;azureCredInfo&#34;: {
                            &#34;applicationId&#34;: application_id,
                            &#34;endpoints&#34;: {
                                &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                &#34;resourceManagerEndpoint&#34;: &#34;https://management.azure.com/&#34;,
                                &#34;storageEndpoint&#34;: &#34;blob.core.windows.net&#34;},
                            &#34;environment&#34;: &#34;AzureCloud&#34;,
                            &#34;tenantId&#34;: tenant_id}},
                    &#34;createAs&#34;: {},
                    &#34;credentialRecord&#34;: {
                        &#34;credentialName&#34;: credential_name},
                    &#34;description&#34;: description,
                    &#34;record&#34;: {
                        &#34;userName&#34;: application_id,
                        &#34;password&#34;: password},
                    &#34;recordType&#34;: &#34;AZUREACCOUNT&#34;,
                    &#34;securityAssociations&#34;: {
                        &#34;associations&#34;: [],
                        &#34;associationsOperationType&#34;: 1}}]}

        request = self._services[&#39;CREDENTIAL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request, create_credential_account
        )
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def add_aws_s3_creds(
            self,
            credential_name,
            access_key_id,
            secret_access_key,
            description=None):
        &#34;&#34;&#34;Creates aws s3 access key based credential on this commcell

            Args:

                credential_name (str)   --  name to be given to credential account

                access_key_id   (str)   --  access key id for aws S3 bucket

                secrete_access_key (str) -- secrete access key for aws s3 bucket

                description(str)         -- description of the credentials

            Raises:
                SDKException:
                    if arguments type is incorrect

                    if credential account is already present on the commcell

                    if response is not successful

        &#34;&#34;&#34;

        if not (
            isinstance(
                access_key_id,
                str) and isinstance(
                secret_access_key,
                str) and isinstance(
                credential_name,
                str)):
            raise SDKException(&#34;Credential&#34;, &#34;101&#34;)

        if self.has_credential(credential_name):
            raise SDKException(
                &#39;Credential&#39;,
                &#39;102&#39;,
                &#34;Credential {0} already exists on this commcell.&#34;.format(credential_name))

        password = b64encode(secret_access_key.encode()).decode()
        create_credential_account = {
            &#34;credentialRecordInfo&#34;: [
                {
                    &#34;createAs&#34;: {
                    },
                    &#34;credentialRecord&#34;: {
                        &#34;credentialName&#34;: credential_name
                    },
                    &#34;description&#34;: description,
                    &#34;record&#34;: {
                        &#34;userName&#34;: access_key_id,
                        &#34;password&#34;: password
                    },
                    &#34;recordType&#34;: &#34;AMAZON_S3&#34;,
                    &#34;securityAssociations&#34;: {
                        &#34;associations&#34;: [
                        ],
                        &#34;associationsOperationType&#34;: 1
                    }
                }
            ]
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CREDENTIAL&#39;], payload=create_credential_account)
        if flag:
            if response.json():
                response_json = response.json()[&#39;error&#39;]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorMessage&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.credential_manager.Credentials.all_credentials"><code class="name">var <span class="ident">all_credentials</span></code></dt>
<dd>
<div class="desc"><p>"Returns all the Credentials present in the commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L159-L162" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_credentials(self):
    &#34;&#34;&#34;&#34;Returns all the Credentials present in the commcell&#34;&#34;&#34;
    return self._credentials</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.credential_manager.Credentials.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, record_type, credential_name, user_name, user_password, description=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates credential account on this commcell</p>
<h2 id="args">Args</h2>
<p>record_type
(str)
&ndash; type of credential record to be created (windows/linux)</p>
<p>credential_name (str)
&ndash;
name to be given to credential account</p>
<p>user_name
(str)
&ndash;
name of the user to be associated to this credential
account</p>
<p>user_password
(str)
&ndash;
password for user</p>
<p>description
(str)
&ndash;
description for credential account</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if credential account is already present on the commcell</p>
<pre><code>if string format are not proper

if response is not successful
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L202-L268" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, record_type, credential_name, user_name, user_password, description=None):
    &#34;&#34;&#34;Creates credential account on this commcell

        Args:
            record_type     (str)   -- type of credential record to be created (windows/linux)

            credential_name (str)   --  name to be given to credential account

            user_name       (str)   --  name of the user to be associated to this credential
                                        account

            user_password   (str)   --  password for user

            description     (str)   --  description for credential account

        Raises:
            SDKException:
                if credential account is already present on the commcell

                if string format are not proper

                if response is not successful

    &#34;&#34;&#34;

    if not (isinstance(credential_name, str) and isinstance(user_name, str)):
        raise SDKException(&#39;User&#39;, &#39;101&#39;)

    if self.has_credential(credential_name):
        raise SDKException(
            &#39;Credential&#39;, &#39;102&#39;, &#34;User {0} already exists on this commcell.&#34;.format(
                credential_name)
        )
    password = b64encode(user_password.encode()).decode()

    record = {
        &#34;userName&#34;: user_name,
        &#34;password&#34;: password
    }
    create_credential_account = {
        &#34;credentialRecordInfo&#34;: [{
            &#34;recordType&#34;: self.record_type[record_type.lower()],
            &#34;description&#34;: description,
            &#34;credentialRecord&#34;: {
                &#34;credentialName&#34;: credential_name
            },
            &#34;record&#34;: record,
        }]
    }

    request = self._services[&#39;CREDENTIAL&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, request, create_credential_account
    )
    if flag:
        if response.json():
            response_json = response.json()[&#39;error&#39;]
            error_code = response_json[&#39;errorCode&#39;]
            error_message = response_json[&#39;errorMessage&#39;]
            if not error_code == 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.add_aws_s3_creds"><code class="name flex">
<span>def <span class="ident">add_aws_s3_creds</span></span>(<span>self, credential_name, access_key_id, secret_access_key, description=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates aws s3 access key based credential on this commcell</p>
<h2 id="args">Args</h2>
<p>credential_name (str)
&ndash;
name to be given to credential account</p>
<p>access_key_id
(str)
&ndash;
access key id for aws S3 bucket</p>
<p>secrete_access_key (str) &ndash; secrete access key for aws s3 bucket</p>
<p>description(str)
&ndash; description of the credentials</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if arguments type is incorrect</p>
<pre><code>if credential account is already present on the commcell

if response is not successful
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L692-L775" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_aws_s3_creds(
        self,
        credential_name,
        access_key_id,
        secret_access_key,
        description=None):
    &#34;&#34;&#34;Creates aws s3 access key based credential on this commcell

        Args:

            credential_name (str)   --  name to be given to credential account

            access_key_id   (str)   --  access key id for aws S3 bucket

            secrete_access_key (str) -- secrete access key for aws s3 bucket

            description(str)         -- description of the credentials

        Raises:
            SDKException:
                if arguments type is incorrect

                if credential account is already present on the commcell

                if response is not successful

    &#34;&#34;&#34;

    if not (
        isinstance(
            access_key_id,
            str) and isinstance(
            secret_access_key,
            str) and isinstance(
            credential_name,
            str)):
        raise SDKException(&#34;Credential&#34;, &#34;101&#34;)

    if self.has_credential(credential_name):
        raise SDKException(
            &#39;Credential&#39;,
            &#39;102&#39;,
            &#34;Credential {0} already exists on this commcell.&#34;.format(credential_name))

    password = b64encode(secret_access_key.encode()).decode()
    create_credential_account = {
        &#34;credentialRecordInfo&#34;: [
            {
                &#34;createAs&#34;: {
                },
                &#34;credentialRecord&#34;: {
                    &#34;credentialName&#34;: credential_name
                },
                &#34;description&#34;: description,
                &#34;record&#34;: {
                    &#34;userName&#34;: access_key_id,
                    &#34;password&#34;: password
                },
                &#34;recordType&#34;: &#34;AMAZON_S3&#34;,
                &#34;securityAssociations&#34;: {
                    &#34;associations&#34;: [
                    ],
                    &#34;associationsOperationType&#34;: 1
                }
            }
        ]
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;CREDENTIAL&#39;], payload=create_credential_account)
    if flag:
        if response.json():
            response_json = response.json()[&#39;error&#39;]
            error_code = response_json[&#39;errorCode&#39;]
            error_message = response_json[&#39;errorMessage&#39;]
            if not error_code == 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.add_azure_cloud_creds"><code class="name flex">
<span>def <span class="ident">add_azure_cloud_creds</span></span>(<span>self, credential_name, account_name, access_key_id, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates azure access key based credential on this commcell</p>
<h2 id="args">Args</h2>
<p>credential_name (str)
&ndash;
name to be given to credential account</p>
<p>account_name
(str)
&ndash;
name of the azure storage account</p>
<p>access_key_id
(str)
&ndash;
access key for azure storage</p>
<p>** kwargs(dict)
&ndash;
Key value pairs for supported arguments</p>
<p>Supported argument values:
description(str)
-
description of the credentials</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if arguments type is incorrect</p>
<pre><code>if credential account is already present on the commcell

if string format are not proper

if response is not successful
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L512-L596" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_azure_cloud_creds(self, credential_name, account_name, access_key_id, **kwargs):
    &#34;&#34;&#34;Creates azure access key based credential on this commcell

        Args:

            credential_name (str)   --  name to be given to credential account

            account_name  (str)     --  name of the azure storage account

            access_key_id   (str)   --  access key for azure storage

            ** kwargs(dict)         --  Key value pairs for supported arguments

            Supported argument values:
                description(str)            -   description of the credentials

        Raises:
            SDKException:
                if arguments type is incorrect

                if credential account is already present on the commcell

                if string format are not proper

                if response is not successful

    &#34;&#34;&#34;
    description = kwargs.get(&#34;description&#34;, &#34;&#34;)

    if not (isinstance(access_key_id, str)  and isinstance(account_name, str)
             and isinstance(credential_name, str)):
        raise SDKException(&#34;Credential&#34;, &#34;101&#34;)

    if self.has_credential(credential_name):
        raise SDKException(
            &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                credential_name)
        )

    password = b64encode(access_key_id.encode()).decode()
    additional_information = {
        &#34;azureCredInfo&#34;: {
            &#34;authType&#34;: &#34;AZURE_OAUTH_SHARED_SECRET&#34;,
            &#34;endpoints&#34;: {
                &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                &#34;storageEndpoint&#34;: &#34;blob.core.windows.net&#34;,
                &#34;resourceManagerEndpoint&#34;: &#34;https://management.azure.com/&#34;
            }
        }
    }
    create_credential_account = {
        &#34;credentialRecordInfo&#34;: [
            {
                &#34;credentialRecord&#34;: {
                    &#34;credentialName&#34;: credential_name
                },

                &#34;record&#34;: {
                    &#34;userName&#34;: account_name,
                    &#34;password&#34;: password
                },
                &#34;recordType&#34;: &#34;MICROSOFT_AZURE&#34;,
                &#34;additionalInformation&#34;: additional_information,
                &#34;description&#34;: description
            }
        ]
    }

    request = self._services[&#39;CREDENTIAL&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, request, create_credential_account
    )
    if flag:
        if response.json():
            response_json = response.json()[&#39;error&#39;]
            error_code = response_json[&#39;errorCode&#39;]
            error_message = response_json[&#39;errorMessage&#39;]
            if not error_code == 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.add_azure_cosmosdb_creds"><code class="name flex">
<span>def <span class="ident">add_azure_cosmosdb_creds</span></span>(<span>self, credential_name, tenant_id, application_id, application_secret, description='')</span>
</code></dt>
<dd>
<div class="desc"><p>Creates azure application id and application secret key based credential on this commcell</p>
<h2 id="args">Args</h2>
<p>credential_name (str)
&ndash;
name to be given to credential account</p>
<p>tenant_id
(str)
&ndash;
name of tenant id</p>
<p>application_id
(str)
&ndash;
application id</p>
<p>application_secret
(str)
- application secet</p>
<p>description(str)
-
description of the credentials</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if arguments type is incorrect</p>
<pre><code>if credential account is already present on the commcell

if response is not successful
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L598-L690" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_azure_cosmosdb_creds(
        self,
        credential_name,
        tenant_id,
        application_id,
        application_secret,
        description=&#34;&#34;):
    &#34;&#34;&#34;Creates azure application id and application secret key based credential on this commcell

        Args:

            credential_name (str)   --  name to be given to credential account

            tenant_id  (str)     --    name of tenant id

            application_id   (str)   --  application id

            application_secret  (str)  - application secet

            description(str)            -   description of the credentials

        Raises:
            SDKException:
                if arguments type is incorrect

                if credential account is already present on the commcell

                if response is not successful

    &#34;&#34;&#34;

    if not (
        isinstance(
            application_id,
            str) and isinstance(
            tenant_id,
            str) and isinstance(
            credential_name,
            str) and isinstance(
                application_secret,
            str)):
        raise SDKException(&#34;Credential&#34;, &#34;101&#34;)

    if self.has_credential(credential_name):
        raise SDKException(
            &#39;Credential&#39;,
            &#39;102&#39;,
            &#34;Credential {0} already exists on this commcell.&#34;.format(credential_name))

    password = b64encode(application_secret.encode()).decode()

    create_credential_account = {
        &#34;credentialRecordInfo&#34;: [
            {
                &#34;additionalInformation&#34;: {
                    &#34;azureCredInfo&#34;: {
                        &#34;applicationId&#34;: application_id,
                        &#34;endpoints&#34;: {
                            &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                            &#34;resourceManagerEndpoint&#34;: &#34;https://management.azure.com/&#34;,
                            &#34;storageEndpoint&#34;: &#34;blob.core.windows.net&#34;},
                        &#34;environment&#34;: &#34;AzureCloud&#34;,
                        &#34;tenantId&#34;: tenant_id}},
                &#34;createAs&#34;: {},
                &#34;credentialRecord&#34;: {
                    &#34;credentialName&#34;: credential_name},
                &#34;description&#34;: description,
                &#34;record&#34;: {
                    &#34;userName&#34;: application_id,
                    &#34;password&#34;: password},
                &#34;recordType&#34;: &#34;AZUREACCOUNT&#34;,
                &#34;securityAssociations&#34;: {
                    &#34;associations&#34;: [],
                    &#34;associationsOperationType&#34;: 1}}]}

    request = self._services[&#39;CREDENTIAL&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, request, create_credential_account
    )
    if flag:
        if response.json():
            response_json = response.json()[&#39;error&#39;]
            error_code = response_json[&#39;errorCode&#39;]
            error_message = response_json[&#39;errorMessage&#39;]
            if not error_code == 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.add_db2_database_creds"><code class="name flex">
<span>def <span class="ident">add_db2_database_creds</span></span>(<span>self, credential_name, username, password, description=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates db2 credential on this commcell</p>
<h2 id="args">Args</h2>
<p>credential_name (str)
&ndash;
name to be given to credential account</p>
<p>username
(str)
&ndash;
name of the db2 credential</p>
<p>password
(str)
&ndash;
password for the credential</p>
<p>description (str)
&ndash;
description of the credential</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if credential account is already present on the commcell</p>
<pre><code>if response is not successful
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L368-L414" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_db2_database_creds(self, credential_name, username, password, description=None):
    &#34;&#34;&#34;Creates db2 credential on this commcell
        Args:

            credential_name (str)   --  name to be given to credential account

            username  (str)         --  name of the db2 credential

            password   (str)        --  password for the credential

            description (str)       --  description of the credential

        Raises:
            SDKException:
                if credential account is already present on the commcell

                if response is not successful
    &#34;&#34;&#34;
    if self.has_credential(credential_name):
        raise SDKException(
            &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                credential_name)
        )
    password = b64encode(password.encode()).decode()
    create_credential = {
        &#34;accountType&#34;: &#34;DATABASE_ACCOUNT&#34;,
        &#34;databaseCredentialType&#34;: &#34;DB2&#34;,
        &#34;name&#34;: credential_name,
        &#34;username&#34;: username,
        &#34;password&#34;: password,
        &#34;description&#34;: description
    }

    request = self._services[&#39;ADD_CREDENTIALS&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, request, create_credential
    )
    if flag:
        if response.json():
            id = response.json()[&#39;id&#39;]
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    self.refresh()
    return Credential(self._commcell_object, credential_name, id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.add_mysql_database_creds"><code class="name flex">
<span>def <span class="ident">add_mysql_database_creds</span></span>(<span>self, credential_name, username, password, description=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates MySQL credential on this commcell</p>
<h2 id="args">Args</h2>
<p>credential_name (str)
&ndash;
name to be given to credential account</p>
<p>username
(str)
&ndash;
MySQL username</p>
<p>password
(str)
&ndash;
MySQL password</p>
<p>description (str)
&ndash;
description of the credential</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if credential account is already present on the commcell</p>
<pre><code>if response is not successful
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L464-L510" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_mysql_database_creds(self, credential_name, username, password, description=None):
    &#34;&#34;&#34;Creates MySQL credential on this commcell
        Args:

            credential_name (str)   --  name to be given to credential account

            username  (str)         --  MySQL username

            password   (str)        --  MySQL password

            description (str)       --  description of the credential

        Raises:
            SDKException:
                if credential account is already present on the commcell

                if response is not successful
    &#34;&#34;&#34;
    if self.has_credential(credential_name):
        raise SDKException(
            &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                credential_name)
        )
    password = b64encode(password.encode()).decode()
    create_credential = {
        &#34;accountType&#34;: &#34;DATABASE_ACCOUNT&#34;,
        &#34;databaseCredentialType&#34;: &#34;MYSQL&#34;,
        &#34;name&#34;: credential_name,
        &#34;username&#34;: username,
        &#34;password&#34;: password,
        &#34;description&#34;: description
    }

    request = self._services[&#39;ADD_CREDENTIALS&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, request, create_credential
    )
    if flag:
        if response.json():
            id = response.json()[&#39;id&#39;]
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    self.refresh()
    return Credential(self._commcell_object, credential_name, id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.add_postgres_database_creds"><code class="name flex">
<span>def <span class="ident">add_postgres_database_creds</span></span>(<span>self, credential_name, username, password, description=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates PostgreSQL credential on this commcell</p>
<h2 id="args">Args</h2>
<p>credential_name (str)
&ndash;
name to be given to credential account</p>
<p>username
(str)
&ndash;
PostgreSQL username</p>
<p>password
(str)
&ndash;
PostgreSQL password</p>
<p>description (str)
&ndash;
description of the credential</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if credential account is already present on the commcell</p>
<pre><code>if response is not successful
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L416-L462" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_postgres_database_creds(self, credential_name, username, password, description=None):
    &#34;&#34;&#34;Creates PostgreSQL credential on this commcell
        Args:

            credential_name (str)   --  name to be given to credential account

            username  (str)         --  PostgreSQL username

            password   (str)        --  PostgreSQL password

            description (str)       --  description of the credential

        Raises:
            SDKException:
                if credential account is already present on the commcell

                if response is not successful
    &#34;&#34;&#34;
    if self.has_credential(credential_name):
        raise SDKException(
            &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} already exists on this commcell.&#34;.format(
                credential_name)
        )
    password = b64encode(password.encode()).decode()
    create_credential = {
        &#34;accountType&#34;: &#34;DATABASE_ACCOUNT&#34;,
        &#34;databaseCredentialType&#34;: &#34;POSTGRESQL&#34;,
        &#34;name&#34;: credential_name,
        &#34;username&#34;: username,
        &#34;password&#34;: password,
        &#34;description&#34;: description
    }

    request = self._services[&#39;ADD_CREDENTIALS&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, request, create_credential
    )
    if flag:
        if response.json():
            id = response.json()[&#39;id&#39;]
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    self.refresh()
    return Credential(self._commcell_object, credential_name, id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, credential_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the credential object for specified credential name</p>
<h2 id="args">Args</h2>
<p>credential_name (str) &ndash;
name of the credential for which the object has to be
deleted</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if credential doesn't exist</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L274-L321" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, credential_name):
    &#34;&#34;&#34;Deletes the credential object for specified credential name

        Args:
            credential_name (str) --  name of the credential for which the object has to be
                                      deleted

        Raises:
            SDKException:
                if credential doesn&#39;t exist

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not self.has_credential(credential_name):
        raise SDKException(
            &#39;Credential&#39;, &#39;102&#39;, &#34;credential {0} doesn&#39;t exists on this commcell.&#34;.format(
                credential_name)
        )

    delete_credential = self._services[&#39;DELETE_RECORD&#39;]

    request_json = {
        &#34;credentialRecordInfo&#34;: [{
            &#34;credentialRecord&#34;: {
                &#34;credentialName&#34;: credential_name
            }
        }]
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, delete_credential, request_json
    )
    if flag:
        if response.json():
            response_json = response.json()[&#39;error&#39;]
            error_code = response_json[&#39;errorCode&#39;]
            error_message = response_json[&#39;errorMessage&#39;]
            if not error_code == 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, credential_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the Credential object for the specified Credential name</p>
<h2 id="args">Args</h2>
<p>credential_name
(str)
&ndash;
name of the Credential for which the object has to
be created</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if Credential doesn't exist with specified name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L183-L200" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, credential_name):
    &#34;&#34;&#34;Returns the Credential object for the specified Credential name

        Args:
            credential_name  (str)    --  name of the Credential for which the object has to
                                          be created
        Raises:
            SDKException:
                if Credential doesn&#39;t exist with specified name
    &#34;&#34;&#34;
    if not self.has_credential(credential_name):
        raise SDKException(
            &#39;Credential&#39;, &#39;102&#39;, &#34;Credential {0} doesn&#39;t exists on this commcell.&#34;.format(
                credential_name)
        )

    return Credential(self._commcell_object, credential_name, self._credentials[
        credential_name.lower()])</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.get_security_associations"><code class="name flex">
<span>def <span class="ident">get_security_associations</span></span>(<span>self, owner, is_user=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the security association dictionary for a given user or user group</p>
<h2 id="args">Args</h2>
<p>owner(str)
-
Owner of the user or user group
is_user(bool)
-
True if the owner is a user
False if the owner is a user group</p>
<h2 id="returns">Returns</h2>
<p>dict containing the security association</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L323-L366" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_security_associations(self, owner, is_user=False):
    &#34;&#34;&#34;
    Returns the security association dictionary for a given user or user group
    Args:
        owner(str)          -   Owner of the user or user group
        is_user(bool)       -   True if the owner is a user
                                False if the owner is a user group

    Returns:
        dict containing the security association
    &#34;&#34;&#34;
    if is_user is True:
        userOrGroupInfo = {
            &#34;entityTypeName&#34;: &#34;USER_ENTITY&#34;,
            &#34;userGroupName&#34;: owner,
            &#34;userGroupId&#34;: int(self._commcell_object.users.get(owner).user_id)
        }
    else:
        userOrGroupInfo = {
            &#34;entityTypeName&#34;: &#34;USERGROUP_ENTITY&#34;,
            &#34;userGroupName&#34;: owner,
            &#34;userGroupId&#34;: int(self._commcell_object.user_groups.get(owner).user_group_id)
        }
    security_association = {
        &#34;associationsOperationType&#34;: 1,
        &#34;associations&#34;: [
            {
                &#34;userOrGroup&#34;: [
                    userOrGroupInfo
                ],
                &#34;properties&#34;: {
                    &#34;isCreatorAssociation&#34;: False,
                    &#34;permissions&#34;: [
                        {
                            &#34;permissionId&#34;: 218,
                            &#34;_type_&#34;: 122,
                            &#34;permissionName&#34;: &#34;User Credential&#34;
                        }
                    ]
                }
            }
        ]
    }
    return security_association</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.has_credential"><code class="name flex">
<span>def <span class="ident">has_credential</span></span>(<span>self, credential_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if any Credentials with specified name exists on this commcell</p>
<h2 id="args">Args</h2>
<p>credential_name
(str)
&ndash;
name of the Credential which has to be
checked if exists</p>
<h2 id="retruns">Retruns</h2>
<p>Bool- True if specified Credential is present on the commcell else false</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if data type of input is invalid</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L164-L181" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_credential(self, credential_name):
    &#34;&#34;&#34;Checks if any Credentials with specified name exists on this commcell

        Args:
            credential_name         (str)     --    name of the Credential which has to be
                                                    checked if exists

        Retruns:
            Bool- True if specified Credential is present on the commcell else false

        Raises:
            SDKException:
                if data type of input is invalid
    &#34;&#34;&#34;
    if not isinstance(credential_name, str):
        raise SDKException(&#39;Credentials&#39;, &#39;101&#39;)

    return self._credentials and credential_name.lower() in self._credentials</code></pre>
</details>
</dd>
<dt id="cvpysdk.credential_manager.Credentials.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the list of credential records on this commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/credential_manager.py#L270-L272" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the list of credential records on this commcell.&#34;&#34;&#34;
    self._credentials = self._get_credentials()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.credential_manager.Credential" href="#cvpysdk.credential_manager.Credential">Credential</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.credential_manager.Credential.credential_description" href="#cvpysdk.credential_manager.Credential.credential_description">credential_description</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credential.credential_id" href="#cvpysdk.credential_manager.Credential.credential_id">credential_id</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credential.credential_name" href="#cvpysdk.credential_manager.Credential.credential_name">credential_name</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credential.credential_record_type" href="#cvpysdk.credential_manager.Credential.credential_record_type">credential_record_type</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credential.credential_security_properties" href="#cvpysdk.credential_manager.Credential.credential_security_properties">credential_security_properties</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credential.credential_user_name" href="#cvpysdk.credential_manager.Credential.credential_user_name">credential_user_name</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credential.refresh" href="#cvpysdk.credential_manager.Credential.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credential.update_securtiy" href="#cvpysdk.credential_manager.Credential.update_securtiy">update_securtiy</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credential.update_user_credential" href="#cvpysdk.credential_manager.Credential.update_user_credential">update_user_credential</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.credential_manager.Credentials" href="#cvpysdk.credential_manager.Credentials">Credentials</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.credential_manager.Credentials.add" href="#cvpysdk.credential_manager.Credentials.add">add</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.add_aws_s3_creds" href="#cvpysdk.credential_manager.Credentials.add_aws_s3_creds">add_aws_s3_creds</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.add_azure_cloud_creds" href="#cvpysdk.credential_manager.Credentials.add_azure_cloud_creds">add_azure_cloud_creds</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.add_azure_cosmosdb_creds" href="#cvpysdk.credential_manager.Credentials.add_azure_cosmosdb_creds">add_azure_cosmosdb_creds</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.add_db2_database_creds" href="#cvpysdk.credential_manager.Credentials.add_db2_database_creds">add_db2_database_creds</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.add_mysql_database_creds" href="#cvpysdk.credential_manager.Credentials.add_mysql_database_creds">add_mysql_database_creds</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.add_postgres_database_creds" href="#cvpysdk.credential_manager.Credentials.add_postgres_database_creds">add_postgres_database_creds</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.all_credentials" href="#cvpysdk.credential_manager.Credentials.all_credentials">all_credentials</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.delete" href="#cvpysdk.credential_manager.Credentials.delete">delete</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.get" href="#cvpysdk.credential_manager.Credentials.get">get</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.get_security_associations" href="#cvpysdk.credential_manager.Credentials.get_security_associations">get_security_associations</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.has_credential" href="#cvpysdk.credential_manager.Credentials.has_credential">has_credential</a></code></li>
<li><code><a title="cvpysdk.credential_manager.Credentials.refresh" href="#cvpysdk.credential_manager.Credentials.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>