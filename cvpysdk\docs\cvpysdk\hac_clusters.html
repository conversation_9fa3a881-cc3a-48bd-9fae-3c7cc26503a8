<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.hac_clusters API documentation</title>
<meta name="description" content="File for performing hac cluster related operations on the commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.hac_clusters</code></h1>
</header>
<section id="section-intro">
<p>File for performing hac cluster related operations on the commcell</p>
<p>HACClusters and HACCluster are 2 classes defined in this file</p>
<p>HACClusters:
Class for representing all the hac clusters associated with the commcell</p>
<p>HACCluster:
Class for a instance of a single hac cluster of the commcell</p>
<h1 id="hacclusters">HACClusters</h1>
<pre><code>__init__()                          --  initialize object of HAC clusters class associated with
the commcell

__str()                             --  returns all the HAC clusters of the commcell

__repr__()                          --  returns the string to represent the instance

__get_item()                        --  returns the details of HAC cluster for given cloud name

_get_all_clusters()                 --  gets detail of all hac cluster associated to commcell

_response_not_success()             --  raise exception when response is not 200

get()                               --  return an HACCluster object for given cluster name

has_cluster()                       --  returns whether the hac cluster is present or not in
the commcell

add()                            --  creates a new hac cluster to the commcell

delete()                            --  deletes the hac cluster associated to commcell

refresh()                           --  refresh the hac clusters details associated with commcell
</code></pre>
<h2 id="hacclusters-attributes">Hacclusters Attributes</h2>
<pre><code>**all_hac_clusters**                 --  returns the dictionary consisting of all the hac clusters
associated with the commcell and there details
</code></pre>
<h1 id="haccluster">HACCluster</h1>
<pre><code>__init__()                          --  initialize object of IndexPool class

__repr__()                          --  returns the string to represent the instance

_response_not_success()             --  raise exception when response is not 200

modify_node()                       --  methods to modify the HAC cluster node properties

node_info()                         --  returns a dict consisting details of node present in the cluster

refresh()                           --  refresh the index pool details associated with commcell
</code></pre>
<h2 id="haccluster-attributes">Haccluster Attributes</h2>
<pre><code>**cluster_id**                      --  returns the cluster id for HAC cluster

**cluster_name**                    --  returns the HAC cluster name

**cloud_id**                        --  returns HAC cluster cloud id

**node_names**                      --  returns a list of names of all HAC cluster nodes
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L1-L515" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for performing hac cluster related operations on the commcell

HACClusters and HACCluster are 2 classes defined in this file

HACClusters:   Class for representing all the hac clusters associated with the commcell

HACCluster:    Class for a instance of a single hac cluster of the commcell


HACClusters
============

    __init__()                          --  initialize object of HAC clusters class associated with
    the commcell

    __str()                             --  returns all the HAC clusters of the commcell

    __repr__()                          --  returns the string to represent the instance

    __get_item()                        --  returns the details of HAC cluster for given cloud name

    _get_all_clusters()                 --  gets detail of all hac cluster associated to commcell

    _response_not_success()             --  raise exception when response is not 200

    get()                               --  return an HACCluster object for given cluster name

    has_cluster()                       --  returns whether the hac cluster is present or not in
    the commcell

    add()                            --  creates a new hac cluster to the commcell

    delete()                            --  deletes the hac cluster associated to commcell

    refresh()                           --  refresh the hac clusters details associated with commcell

HACClusters Attributes
-----------------------

    **all_hac_clusters**                 --  returns the dictionary consisting of all the hac clusters
    associated with the commcell and there details

HACCluster
============

    __init__()                          --  initialize object of IndexPool class

    __repr__()                          --  returns the string to represent the instance

    _response_not_success()             --  raise exception when response is not 200

    modify_node()                       --  methods to modify the HAC cluster node properties

    node_info()                         --  returns a dict consisting details of node present in the cluster

    refresh()                           --  refresh the index pool details associated with commcell

HACCluster Attributes
-----------------------

    **cluster_id**                      --  returns the cluster id for HAC cluster

    **cluster_name**                    --  returns the HAC cluster name

    **cloud_id**                        --  returns HAC cluster cloud id

    **node_names**                      --  returns a list of names of all HAC cluster nodes

&#34;&#34;&#34;

from copy import deepcopy
from .exception import SDKException
from .datacube.constants import IndexServerConstants


class HACClusters(object):
    &#34;&#34;&#34;Class for representing all the HAC clusters associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the HACClusters class

            Args:
                commcell_object (object)    --  instance of class Commcell

            Returns:
                object  -   instance of class HACClusters
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._hac_group = None
        self._all_hac_clusters = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all HAC Clusters of the commcell.

                Returns:
                    str - string of all the HAC clusters associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;HAC Name&#39;)
        index = 1
        for hac_name in self.all_hac_clusters:
            representation_string += &#39;{:^5}\t{:^20}\n&#39;.format(
                index, hac_name)
            index += 1
        return representation_string

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the HACClusters class.&#34;&#34;&#34;
        return &#34;HACClusters class instance for Commcell&#34;

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the details of HAC cluster for given HAC name

            Args:
                value   (str)       --  name of HAC cluster

            Returns:
                dict    -   details of the HAC cluster

            Raises:
                HAC cluster not found
        &#34;&#34;&#34;
        value = value.lower()
        if value.lower() in self.all_hac_clusters:
            return {&#34;name&#34;: value.lower, &#34;id&#34;: self.all_hac_clusters[value]}
        raise SDKException(&#39;HACClusters&#39;, &#39;102&#39;)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

            Args:
                response    (object)    -   response object

            Raises:
                SDKException:
                    Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def _get_all_clusters(self):
        &#34;&#34;&#34;Gets details of all HAC clusters associated to the commcell&#34;&#34;&#34;
        if self._commcell_object.client_groups.has_clientgroup(&#34;HAC Cluster&#34;):
            if self._hac_group is None:
                self._hac_group = self._commcell_object.client_groups.get(&#34;HAC Cluster&#34;)
            self._hac_group.refresh()
            for client_name in self._hac_group.associated_clients:
                client_obj = HACCluster(
                    self._commcell_object, client_name
                )
                self._all_hac_clusters[client_name.lower()] = int(client_obj.cloud_id)

    def has_cluster(self, hac_name):
        &#34;&#34;&#34;Returns whether the HAC cluster with given name is present or not

            Args:
                hac_name    (str)       --  hac cluster name

            Returns:
                boolean     -   True if hac cluster is associated with the commcell
                else returns False

            Raises:
                SDKException:
                    Data type of the input(s) is not valid
        &#34;&#34;&#34;
        if not isinstance(hac_name, str):
            raise SDKException(&#39;HACClusters&#39;, &#39;101&#39;)
        return hac_name.lower() in self._all_hac_clusters

    def get(self, hac_name):
        &#34;&#34;&#34;Returns instance of HACCluster class is cluster is found

            Args:
                hac_name        (str/int)   --      hac cluster name or id

            Returns:
                object          (HACCluster)   --  Instance of a single hac cluster

            Raises:
                SDKException:
                    Data type of the input(s) is not valid

                    HAC Cluster not found
        &#34;&#34;&#34;
        if isinstance(hac_name, str):
            if hac_name.lower() in self.all_hac_clusters:
                return HACCluster(self._commcell_object, hac_name.lower())
        elif isinstance(hac_name, int):
            for cluster_name in self.all_hac_clusters:
                if int(self._all_hac_clusters[cluster_name]) == int(hac_name):
                    return HACCluster(self._commcell_object, cluster_name)
        else:
            raise SDKException(&#39;HACClusters&#39;, &#39;101&#39;)
        raise SDKException(&#34;HACClusters&#34;, &#34;102&#34;)

    def refresh(self):
        &#34;&#34;&#34;Refreshes properties for HACClusters class&#34;&#34;&#34;
        self._commcell_object.clients.refresh()
        self._commcell_object.client_groups.refresh()
        self._all_hac_clusters = {}
        self._hac_group = None
        self._get_all_clusters()

    def add(self, cloud_name, cloud_node_names):
        &#34;&#34;&#34;Creates a new HAC cluster

            Args:
                cloud_name      (str)       --  hac cluster cloud name
                cloud_node_names    (list)  --  string array of node names to be added to cluster

            Raises:
                SDKException:
                    Data type of the input(s) is not valid.

                    Response was not success.

                    Response was empty.

            Returns:
                Object  -   Instance of class HACCluster
        &#34;&#34;&#34;
        if not (isinstance(cloud_name, str) and isinstance(cloud_node_names, list)):
            raise SDKException(&#39;HACClusters&#39;, &#39;101&#39;)
        cloud_node_names = sorted(cloud_node_names)
        node_meta_infos = {
            &#39;zkDataPort&#39;: &#39;8091&#39;,
            &#39;zkElectionPort&#39;: &#39;8097&#39;,
            &#39;zkListenerPort&#39;: &#39;8090&#39;,
            &#39;zkServerId&#39;: None,
            &#39;zkDataDir&#39;: None
        }
        req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
        del req_json[&#39;solrCloudInfo&#39;]
        del req_json[&#39;cloudMetaInfos&#39;]
        req_json[&#39;type&#39;] = 6
        req_json[&#39;cloudInfoEntity&#39;] = {
            &#34;_type_&#34;: 169,
            &#34;cloudName&#34;: cloud_name,
            &#34;cloudDisplayName&#34;: cloud_name
        }
        server_id = 1
        for node_name in cloud_node_names:
            node_obj = self._commcell_object.clients.get(node_name)
            node_data = {
                &#34;opType&#34;: IndexServerConstants.OPERATION_ADD,
                &#34;status&#34;: 0,
                &#34;nodeClientEntity&#34;: {
                    &#34;_type_&#34;: 3,
                    &#34;hostName&#34;: node_obj.client_hostname,
                    &#34;clientName&#34;: node_name,
                    &#34;clientId&#34;: int(node_obj.client_id)
                },
                &#34;nodeMetaInfos&#34;: []
            }
            node_meta_infos[&#39;zkServerId&#39;] = str(server_id)
            node_meta_infos[&#39;zkDataDir&#39;] = node_obj.install_directory + &#34;\\iDataAgent\\JobResults\\ZKData&#34;
            for node_info in node_meta_infos:
                node_data[&#39;nodeMetaInfos&#39;].append({
                    &#39;name&#39;: node_info,
                    &#39;value&#39;: node_meta_infos[node_info]
                })
            req_json[&#39;cloudNodes&#39;].append(node_data)
            server_id += 1
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_CREATE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json():
                if response.json()[&#39;genericResp&#39;] == {} and \
                        &#39;cloudId&#39; in response.json():
                    self.refresh()
                    return HACCluster(self._commcell_object, cloud_name)
                else:
                    o_str = &#39;Failed to create HAC Cluster. Error: &#34;{0}&#34;&#39;.format(
                        response.json()[&#39;genericResp&#39;])
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)

    def delete(self, cloud_name):
        &#34;&#34;&#34;Deletes an existing HAC cluster

        Args:
            cloud_name  (str)   --  HAC cluster cloud name to be deleted

        Raises:
            SDKException:
                Data type of the input(s) is not valid.

                Response was not success.

                Response was empty.
        &#34;&#34;&#34;
        if not isinstance(cloud_name, str):
            raise SDKException(&#39;HACCluster&#39;, &#39;101&#39;)
        cloud_id = self.all_hac_clusters[cloud_name.lower()]
        req_json = IndexServerConstants.REQUEST_JSON.copy()
        req_json[&#34;opType&#34;] = IndexServerConstants.OPERATION_DELETE
        req_json[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = cloud_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_DELETE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json() \
                    and &#39;errorCode&#39; not in response.json()[&#39;genericResp&#39;]:
                self.refresh()
                return
            if response.json() and &#39;genericResp&#39; in response.json():
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, response.json()[&#39;genericResp&#39;].get(
                        &#39;errorMessage&#39;, &#39;&#39;))
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        self._response_not_success(response)

    @property
    def all_hac_clusters(self):
        &#34;&#34;&#34;Returns the details of all HAC clusters associated with commcell&#34;&#34;&#34;
        return self._all_hac_clusters


class HACCluster(object):
    &#34;&#34;&#34;Class to perform HAC cluster operations on a specific HAC cluster&#34;&#34;&#34;

    def __init__(self, commcell_object, cluster_name, cluster_id=None):
        &#34;&#34;&#34;Initializes the HACCluster class object

            Args:
                commcell_object     (object)        --  Instance of commcell class
                cluster_name        (str)           --  HAC cluster cloud name
                cluster_id          (int)           --  HAC cluster cloud id
                    default: None

            Returns:
                object  -   instance of the HACCluster class
        &#34;&#34;&#34;
        self.commcell = commcell_object
        self._cluster_name = cluster_name
        self._cluster_id = cluster_id
        self._cluster_properties = None
        self.cluster_client_obj = None
        self.cluster_nodes = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the HACCluster class.&#34;&#34;&#34;
        return &#34;HACCluster class instance for Commcell&#34;

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

                Raises:
                    SDKException:
                        Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self.commcell._update_response_(
                response.text))

    def refresh(self):
        &#34;&#34;&#34;Refreshes properties of the HAC cluster&#34;&#34;&#34;
        self.commcell.clients.refresh()
        if not self.commcell.clients.has_client(self._cluster_name):
            raise SDKException(&#39;HACClusters&#39;, &#39;102&#39;)
        self.cluster_client_obj = self.commcell.clients.get(self._cluster_name)
        self._cluster_id = self.cluster_client_obj.client_id
        self._cluster_properties = self.cluster_client_obj.\
            properties[&#39;pseudoClientInfo&#39;][&#39;distributedClusterInstanceProperties&#39;][&#39;clusterConfig&#39;][&#39;cloudInfo&#39;]
        self.cluster_nodes = self._cluster_properties[&#39;cloudNodes&#39;]

    def modify_node(self, node_name, listener_port=None, data_port=None,
                    election_port=None, data_dir=None):
        &#34;&#34;&#34;Methods to modify the hac cluster node properties

        Args:
            node_name       (str)       -   Client name for the node
            listener_port   (int/str)   -   zkListenerPort address to be updated
                default - None              Sample: &#39;8090&#39; or 8090
            data_port       (int/str)   -   zkDataPort address to be updated
                default - None              Sample: &#39;8091&#39; or 8091
            election_port   (int/str)   -   zkElectionPort address to be updated
                default - None              Sample: &#39;8097&#39; or 8097
            data_dir        (str)       -   zoo keeper data directory
                default - None

        Raises:
            SDKException:
                HAC zKeeper node not found

                Response was not success

        Returns:
            None

        &#34;&#34;&#34;
        node_info = self.node_info(node_name)
        port_infos = {}
        node_meta_info = node_info[&#39;nodeMetaInfos&#39;]
        for meta_info in node_meta_info:
            port_infos[meta_info[&#39;name&#39;]] = meta_info[&#39;value&#39;]
        if listener_port:
            port_infos[&#39;ZKLISTENERPORT&#39;] = str(listener_port)
        if election_port:
            port_infos[&#39;ZKELECTIONPORT&#39;] = str(election_port)
        if data_port:
            port_infos[&#39;ZKDATAPORT&#39;] = str(data_port)
        if data_dir:
            port_infos[&#39;ZKDATADIR&#39;] = str(data_dir)
        req_json = {
            &#39;cloudId&#39;: self.cloud_id,
            &#39;nodes&#39;: [{
                &#39;status&#39;: 1,
                &#39;opType&#39;: IndexServerConstants.OPERATION_EDIT,
                &#39;nodeClientEntity&#39;: {
                    &#39;clientId&#39;: int(self.commcell.clients[node_name][&#39;id&#39;]),
                    &#39;hostName&#39;: self.commcell.clients[node_name][&#39;hostname&#39;],
                    &#39;clientName&#39;: node_name
                },
                &#39;nodeMetaInfos&#39;: [
                    {
                        &#34;name&#34;: &#34;zkListenerPort&#34;,
                        &#34;value&#34;: port_infos[&#39;ZKLISTENERPORT&#39;]
                    },
                    {
                        &#34;name&#34;: &#34;zkDataPort&#34;,
                        &#34;value&#34;: port_infos[&#39;ZKDATAPORT&#39;]
                    },
                    {
                        &#34;name&#34;: &#34;zkElectionPort&#34;,
                        &#34;value&#34;: port_infos[&#39;ZKELECTIONPORT&#39;]
                    },
                    {
                        &#34;name&#34;: &#34;zkDataDir&#34;,
                        &#34;value&#34;: port_infos[&#39;ZKDATADIR&#39;]
                    }]
            }]
        }
        flag, response = self.commcell._cvpysdk_object.make_request(
            &#39;POST&#39;, self.commcell._services[&#39;CLOUD_NODE_UPDATE&#39;],
            req_json
        )
        if flag:
            if response.json() is not None:
                if &#39;errorCode&#39; not in response.json():
                    self.refresh()
                    return
        self._response_not_success(response)

    def node_info(self, node_name):
        &#34;&#34;&#34;Returns the hac cluster node information

        Args:
            node_name       (str)   -   HAC cluster node name

        Returns:
            dict        -   dictionary containing details of the hac node

        Raises:
            SDKException:
                HAC zKeeper node not found

        &#34;&#34;&#34;
        for node_info in self.cluster_nodes:
            if node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;].lower() == node_name.lower():
                return node_info
        raise SDKException(&#39;HACCluster&#39;, &#39;103&#39;)

    @property
    def cloud_id(self):
        &#34;&#34;&#34;Returns HAC cluster cloud id&#34;&#34;&#34;
        return self._cluster_properties[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;]

    @property
    def node_names(self):
        &#34;&#34;&#34;Returns a list of HAC cluster node names&#34;&#34;&#34;
        result = []
        for node_info in self.cluster_nodes:
            result.append(node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;])
        return result

    @property
    def cluster_id(self):
        &#34;&#34;&#34;Returns the HAC cluster pseudo client id&#34;&#34;&#34;
        return self._cluster_id

    @property
    def cluster_name(self):
        &#34;&#34;&#34;Returns the HAC cluster cloud name&#34;&#34;&#34;
        return self._cluster_name</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.hac_clusters.HACCluster"><code class="flex name class">
<span>class <span class="ident">HACCluster</span></span>
<span>(</span><span>commcell_object, cluster_name, cluster_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to perform HAC cluster operations on a specific HAC cluster</p>
<p>Initializes the HACCluster class object</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
Instance of commcell class
cluster_name
(str)
&ndash;
HAC cluster cloud name
cluster_id
(int)
&ndash;
HAC cluster cloud id
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the HACCluster class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L346-L515" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class HACCluster(object):
    &#34;&#34;&#34;Class to perform HAC cluster operations on a specific HAC cluster&#34;&#34;&#34;

    def __init__(self, commcell_object, cluster_name, cluster_id=None):
        &#34;&#34;&#34;Initializes the HACCluster class object

            Args:
                commcell_object     (object)        --  Instance of commcell class
                cluster_name        (str)           --  HAC cluster cloud name
                cluster_id          (int)           --  HAC cluster cloud id
                    default: None

            Returns:
                object  -   instance of the HACCluster class
        &#34;&#34;&#34;
        self.commcell = commcell_object
        self._cluster_name = cluster_name
        self._cluster_id = cluster_id
        self._cluster_properties = None
        self.cluster_client_obj = None
        self.cluster_nodes = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the HACCluster class.&#34;&#34;&#34;
        return &#34;HACCluster class instance for Commcell&#34;

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

                Raises:
                    SDKException:
                        Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self.commcell._update_response_(
                response.text))

    def refresh(self):
        &#34;&#34;&#34;Refreshes properties of the HAC cluster&#34;&#34;&#34;
        self.commcell.clients.refresh()
        if not self.commcell.clients.has_client(self._cluster_name):
            raise SDKException(&#39;HACClusters&#39;, &#39;102&#39;)
        self.cluster_client_obj = self.commcell.clients.get(self._cluster_name)
        self._cluster_id = self.cluster_client_obj.client_id
        self._cluster_properties = self.cluster_client_obj.\
            properties[&#39;pseudoClientInfo&#39;][&#39;distributedClusterInstanceProperties&#39;][&#39;clusterConfig&#39;][&#39;cloudInfo&#39;]
        self.cluster_nodes = self._cluster_properties[&#39;cloudNodes&#39;]

    def modify_node(self, node_name, listener_port=None, data_port=None,
                    election_port=None, data_dir=None):
        &#34;&#34;&#34;Methods to modify the hac cluster node properties

        Args:
            node_name       (str)       -   Client name for the node
            listener_port   (int/str)   -   zkListenerPort address to be updated
                default - None              Sample: &#39;8090&#39; or 8090
            data_port       (int/str)   -   zkDataPort address to be updated
                default - None              Sample: &#39;8091&#39; or 8091
            election_port   (int/str)   -   zkElectionPort address to be updated
                default - None              Sample: &#39;8097&#39; or 8097
            data_dir        (str)       -   zoo keeper data directory
                default - None

        Raises:
            SDKException:
                HAC zKeeper node not found

                Response was not success

        Returns:
            None

        &#34;&#34;&#34;
        node_info = self.node_info(node_name)
        port_infos = {}
        node_meta_info = node_info[&#39;nodeMetaInfos&#39;]
        for meta_info in node_meta_info:
            port_infos[meta_info[&#39;name&#39;]] = meta_info[&#39;value&#39;]
        if listener_port:
            port_infos[&#39;ZKLISTENERPORT&#39;] = str(listener_port)
        if election_port:
            port_infos[&#39;ZKELECTIONPORT&#39;] = str(election_port)
        if data_port:
            port_infos[&#39;ZKDATAPORT&#39;] = str(data_port)
        if data_dir:
            port_infos[&#39;ZKDATADIR&#39;] = str(data_dir)
        req_json = {
            &#39;cloudId&#39;: self.cloud_id,
            &#39;nodes&#39;: [{
                &#39;status&#39;: 1,
                &#39;opType&#39;: IndexServerConstants.OPERATION_EDIT,
                &#39;nodeClientEntity&#39;: {
                    &#39;clientId&#39;: int(self.commcell.clients[node_name][&#39;id&#39;]),
                    &#39;hostName&#39;: self.commcell.clients[node_name][&#39;hostname&#39;],
                    &#39;clientName&#39;: node_name
                },
                &#39;nodeMetaInfos&#39;: [
                    {
                        &#34;name&#34;: &#34;zkListenerPort&#34;,
                        &#34;value&#34;: port_infos[&#39;ZKLISTENERPORT&#39;]
                    },
                    {
                        &#34;name&#34;: &#34;zkDataPort&#34;,
                        &#34;value&#34;: port_infos[&#39;ZKDATAPORT&#39;]
                    },
                    {
                        &#34;name&#34;: &#34;zkElectionPort&#34;,
                        &#34;value&#34;: port_infos[&#39;ZKELECTIONPORT&#39;]
                    },
                    {
                        &#34;name&#34;: &#34;zkDataDir&#34;,
                        &#34;value&#34;: port_infos[&#39;ZKDATADIR&#39;]
                    }]
            }]
        }
        flag, response = self.commcell._cvpysdk_object.make_request(
            &#39;POST&#39;, self.commcell._services[&#39;CLOUD_NODE_UPDATE&#39;],
            req_json
        )
        if flag:
            if response.json() is not None:
                if &#39;errorCode&#39; not in response.json():
                    self.refresh()
                    return
        self._response_not_success(response)

    def node_info(self, node_name):
        &#34;&#34;&#34;Returns the hac cluster node information

        Args:
            node_name       (str)   -   HAC cluster node name

        Returns:
            dict        -   dictionary containing details of the hac node

        Raises:
            SDKException:
                HAC zKeeper node not found

        &#34;&#34;&#34;
        for node_info in self.cluster_nodes:
            if node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;].lower() == node_name.lower():
                return node_info
        raise SDKException(&#39;HACCluster&#39;, &#39;103&#39;)

    @property
    def cloud_id(self):
        &#34;&#34;&#34;Returns HAC cluster cloud id&#34;&#34;&#34;
        return self._cluster_properties[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;]

    @property
    def node_names(self):
        &#34;&#34;&#34;Returns a list of HAC cluster node names&#34;&#34;&#34;
        result = []
        for node_info in self.cluster_nodes:
            result.append(node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;])
        return result

    @property
    def cluster_id(self):
        &#34;&#34;&#34;Returns the HAC cluster pseudo client id&#34;&#34;&#34;
        return self._cluster_id

    @property
    def cluster_name(self):
        &#34;&#34;&#34;Returns the HAC cluster cloud name&#34;&#34;&#34;
        return self._cluster_name</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.hac_clusters.HACCluster.cloud_id"><code class="name">var <span class="ident">cloud_id</span></code></dt>
<dd>
<div class="desc"><p>Returns HAC cluster cloud id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L494-L497" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cloud_id(self):
    &#34;&#34;&#34;Returns HAC cluster cloud id&#34;&#34;&#34;
    return self._cluster_properties[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.hac_clusters.HACCluster.cluster_id"><code class="name">var <span class="ident">cluster_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the HAC cluster pseudo client id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L507-L510" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cluster_id(self):
    &#34;&#34;&#34;Returns the HAC cluster pseudo client id&#34;&#34;&#34;
    return self._cluster_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.hac_clusters.HACCluster.cluster_name"><code class="name">var <span class="ident">cluster_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the HAC cluster cloud name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L512-L515" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cluster_name(self):
    &#34;&#34;&#34;Returns the HAC cluster cloud name&#34;&#34;&#34;
    return self._cluster_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.hac_clusters.HACCluster.node_names"><code class="name">var <span class="ident">node_names</span></code></dt>
<dd>
<div class="desc"><p>Returns a list of HAC cluster node names</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L499-L505" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def node_names(self):
    &#34;&#34;&#34;Returns a list of HAC cluster node names&#34;&#34;&#34;
    result = []
    for node_info in self.cluster_nodes:
        result.append(node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;])
    return result</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.hac_clusters.HACCluster.modify_node"><code class="name flex">
<span>def <span class="ident">modify_node</span></span>(<span>self, node_name, listener_port=None, data_port=None, election_port=None, data_dir=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Methods to modify the hac cluster node properties</p>
<h2 id="args">Args</h2>
<p>node_name
(str)
-
Client name for the node
listener_port
(int/str)
-
zkListenerPort address to be updated
default - None
Sample: '8090' or 8090
data_port
(int/str)
-
zkDataPort address to be updated
default - None
Sample: '8091' or 8091
election_port
(int/str)
-
zkElectionPort address to be updated
default - None
Sample: '8097' or 8097
data_dir
(str)
-
zoo keeper data directory
default - None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
HAC zKeeper node not found</p>
<pre><code>Response was not success
</code></pre>
<h2 id="returns">Returns</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L397-L473" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_node(self, node_name, listener_port=None, data_port=None,
                election_port=None, data_dir=None):
    &#34;&#34;&#34;Methods to modify the hac cluster node properties

    Args:
        node_name       (str)       -   Client name for the node
        listener_port   (int/str)   -   zkListenerPort address to be updated
            default - None              Sample: &#39;8090&#39; or 8090
        data_port       (int/str)   -   zkDataPort address to be updated
            default - None              Sample: &#39;8091&#39; or 8091
        election_port   (int/str)   -   zkElectionPort address to be updated
            default - None              Sample: &#39;8097&#39; or 8097
        data_dir        (str)       -   zoo keeper data directory
            default - None

    Raises:
        SDKException:
            HAC zKeeper node not found

            Response was not success

    Returns:
        None

    &#34;&#34;&#34;
    node_info = self.node_info(node_name)
    port_infos = {}
    node_meta_info = node_info[&#39;nodeMetaInfos&#39;]
    for meta_info in node_meta_info:
        port_infos[meta_info[&#39;name&#39;]] = meta_info[&#39;value&#39;]
    if listener_port:
        port_infos[&#39;ZKLISTENERPORT&#39;] = str(listener_port)
    if election_port:
        port_infos[&#39;ZKELECTIONPORT&#39;] = str(election_port)
    if data_port:
        port_infos[&#39;ZKDATAPORT&#39;] = str(data_port)
    if data_dir:
        port_infos[&#39;ZKDATADIR&#39;] = str(data_dir)
    req_json = {
        &#39;cloudId&#39;: self.cloud_id,
        &#39;nodes&#39;: [{
            &#39;status&#39;: 1,
            &#39;opType&#39;: IndexServerConstants.OPERATION_EDIT,
            &#39;nodeClientEntity&#39;: {
                &#39;clientId&#39;: int(self.commcell.clients[node_name][&#39;id&#39;]),
                &#39;hostName&#39;: self.commcell.clients[node_name][&#39;hostname&#39;],
                &#39;clientName&#39;: node_name
            },
            &#39;nodeMetaInfos&#39;: [
                {
                    &#34;name&#34;: &#34;zkListenerPort&#34;,
                    &#34;value&#34;: port_infos[&#39;ZKLISTENERPORT&#39;]
                },
                {
                    &#34;name&#34;: &#34;zkDataPort&#34;,
                    &#34;value&#34;: port_infos[&#39;ZKDATAPORT&#39;]
                },
                {
                    &#34;name&#34;: &#34;zkElectionPort&#34;,
                    &#34;value&#34;: port_infos[&#39;ZKELECTIONPORT&#39;]
                },
                {
                    &#34;name&#34;: &#34;zkDataDir&#34;,
                    &#34;value&#34;: port_infos[&#39;ZKDATADIR&#39;]
                }]
        }]
    }
    flag, response = self.commcell._cvpysdk_object.make_request(
        &#39;POST&#39;, self.commcell._services[&#39;CLOUD_NODE_UPDATE&#39;],
        req_json
    )
    if flag:
        if response.json() is not None:
            if &#39;errorCode&#39; not in response.json():
                self.refresh()
                return
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.hac_clusters.HACCluster.node_info"><code class="name flex">
<span>def <span class="ident">node_info</span></span>(<span>self, node_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the hac cluster node information</p>
<h2 id="args">Args</h2>
<p>node_name
(str)
-
HAC cluster node name</p>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary containing details of the hac node</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
HAC zKeeper node not found</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L475-L492" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def node_info(self, node_name):
    &#34;&#34;&#34;Returns the hac cluster node information

    Args:
        node_name       (str)   -   HAC cluster node name

    Returns:
        dict        -   dictionary containing details of the hac node

    Raises:
        SDKException:
            HAC zKeeper node not found

    &#34;&#34;&#34;
    for node_info in self.cluster_nodes:
        if node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;].lower() == node_name.lower():
            return node_info
    raise SDKException(&#39;HACCluster&#39;, &#39;103&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.hac_clusters.HACCluster.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refreshes properties of the HAC cluster</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L386-L395" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refreshes properties of the HAC cluster&#34;&#34;&#34;
    self.commcell.clients.refresh()
    if not self.commcell.clients.has_client(self._cluster_name):
        raise SDKException(&#39;HACClusters&#39;, &#39;102&#39;)
    self.cluster_client_obj = self.commcell.clients.get(self._cluster_name)
    self._cluster_id = self.cluster_client_obj.client_id
    self._cluster_properties = self.cluster_client_obj.\
        properties[&#39;pseudoClientInfo&#39;][&#39;distributedClusterInstanceProperties&#39;][&#39;clusterConfig&#39;][&#39;cloudInfo&#39;]
    self.cluster_nodes = self._cluster_properties[&#39;cloudNodes&#39;]</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.hac_clusters.HACClusters"><code class="flex name class">
<span>class <span class="ident">HACClusters</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the HAC clusters associated with the commcell.</p>
<p>Initialize object of the HACClusters class</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of class Commcell</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of class HACClusters</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L92-L343" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class HACClusters(object):
    &#34;&#34;&#34;Class for representing all the HAC clusters associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the HACClusters class

            Args:
                commcell_object (object)    --  instance of class Commcell

            Returns:
                object  -   instance of class HACClusters
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._hac_group = None
        self._all_hac_clusters = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all HAC Clusters of the commcell.

                Returns:
                    str - string of all the HAC clusters associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;HAC Name&#39;)
        index = 1
        for hac_name in self.all_hac_clusters:
            representation_string += &#39;{:^5}\t{:^20}\n&#39;.format(
                index, hac_name)
            index += 1
        return representation_string

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the HACClusters class.&#34;&#34;&#34;
        return &#34;HACClusters class instance for Commcell&#34;

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the details of HAC cluster for given HAC name

            Args:
                value   (str)       --  name of HAC cluster

            Returns:
                dict    -   details of the HAC cluster

            Raises:
                HAC cluster not found
        &#34;&#34;&#34;
        value = value.lower()
        if value.lower() in self.all_hac_clusters:
            return {&#34;name&#34;: value.lower, &#34;id&#34;: self.all_hac_clusters[value]}
        raise SDKException(&#39;HACClusters&#39;, &#39;102&#39;)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

            Args:
                response    (object)    -   response object

            Raises:
                SDKException:
                    Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def _get_all_clusters(self):
        &#34;&#34;&#34;Gets details of all HAC clusters associated to the commcell&#34;&#34;&#34;
        if self._commcell_object.client_groups.has_clientgroup(&#34;HAC Cluster&#34;):
            if self._hac_group is None:
                self._hac_group = self._commcell_object.client_groups.get(&#34;HAC Cluster&#34;)
            self._hac_group.refresh()
            for client_name in self._hac_group.associated_clients:
                client_obj = HACCluster(
                    self._commcell_object, client_name
                )
                self._all_hac_clusters[client_name.lower()] = int(client_obj.cloud_id)

    def has_cluster(self, hac_name):
        &#34;&#34;&#34;Returns whether the HAC cluster with given name is present or not

            Args:
                hac_name    (str)       --  hac cluster name

            Returns:
                boolean     -   True if hac cluster is associated with the commcell
                else returns False

            Raises:
                SDKException:
                    Data type of the input(s) is not valid
        &#34;&#34;&#34;
        if not isinstance(hac_name, str):
            raise SDKException(&#39;HACClusters&#39;, &#39;101&#39;)
        return hac_name.lower() in self._all_hac_clusters

    def get(self, hac_name):
        &#34;&#34;&#34;Returns instance of HACCluster class is cluster is found

            Args:
                hac_name        (str/int)   --      hac cluster name or id

            Returns:
                object          (HACCluster)   --  Instance of a single hac cluster

            Raises:
                SDKException:
                    Data type of the input(s) is not valid

                    HAC Cluster not found
        &#34;&#34;&#34;
        if isinstance(hac_name, str):
            if hac_name.lower() in self.all_hac_clusters:
                return HACCluster(self._commcell_object, hac_name.lower())
        elif isinstance(hac_name, int):
            for cluster_name in self.all_hac_clusters:
                if int(self._all_hac_clusters[cluster_name]) == int(hac_name):
                    return HACCluster(self._commcell_object, cluster_name)
        else:
            raise SDKException(&#39;HACClusters&#39;, &#39;101&#39;)
        raise SDKException(&#34;HACClusters&#34;, &#34;102&#34;)

    def refresh(self):
        &#34;&#34;&#34;Refreshes properties for HACClusters class&#34;&#34;&#34;
        self._commcell_object.clients.refresh()
        self._commcell_object.client_groups.refresh()
        self._all_hac_clusters = {}
        self._hac_group = None
        self._get_all_clusters()

    def add(self, cloud_name, cloud_node_names):
        &#34;&#34;&#34;Creates a new HAC cluster

            Args:
                cloud_name      (str)       --  hac cluster cloud name
                cloud_node_names    (list)  --  string array of node names to be added to cluster

            Raises:
                SDKException:
                    Data type of the input(s) is not valid.

                    Response was not success.

                    Response was empty.

            Returns:
                Object  -   Instance of class HACCluster
        &#34;&#34;&#34;
        if not (isinstance(cloud_name, str) and isinstance(cloud_node_names, list)):
            raise SDKException(&#39;HACClusters&#39;, &#39;101&#39;)
        cloud_node_names = sorted(cloud_node_names)
        node_meta_infos = {
            &#39;zkDataPort&#39;: &#39;8091&#39;,
            &#39;zkElectionPort&#39;: &#39;8097&#39;,
            &#39;zkListenerPort&#39;: &#39;8090&#39;,
            &#39;zkServerId&#39;: None,
            &#39;zkDataDir&#39;: None
        }
        req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
        del req_json[&#39;solrCloudInfo&#39;]
        del req_json[&#39;cloudMetaInfos&#39;]
        req_json[&#39;type&#39;] = 6
        req_json[&#39;cloudInfoEntity&#39;] = {
            &#34;_type_&#34;: 169,
            &#34;cloudName&#34;: cloud_name,
            &#34;cloudDisplayName&#34;: cloud_name
        }
        server_id = 1
        for node_name in cloud_node_names:
            node_obj = self._commcell_object.clients.get(node_name)
            node_data = {
                &#34;opType&#34;: IndexServerConstants.OPERATION_ADD,
                &#34;status&#34;: 0,
                &#34;nodeClientEntity&#34;: {
                    &#34;_type_&#34;: 3,
                    &#34;hostName&#34;: node_obj.client_hostname,
                    &#34;clientName&#34;: node_name,
                    &#34;clientId&#34;: int(node_obj.client_id)
                },
                &#34;nodeMetaInfos&#34;: []
            }
            node_meta_infos[&#39;zkServerId&#39;] = str(server_id)
            node_meta_infos[&#39;zkDataDir&#39;] = node_obj.install_directory + &#34;\\iDataAgent\\JobResults\\ZKData&#34;
            for node_info in node_meta_infos:
                node_data[&#39;nodeMetaInfos&#39;].append({
                    &#39;name&#39;: node_info,
                    &#39;value&#39;: node_meta_infos[node_info]
                })
            req_json[&#39;cloudNodes&#39;].append(node_data)
            server_id += 1
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_CREATE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json():
                if response.json()[&#39;genericResp&#39;] == {} and \
                        &#39;cloudId&#39; in response.json():
                    self.refresh()
                    return HACCluster(self._commcell_object, cloud_name)
                else:
                    o_str = &#39;Failed to create HAC Cluster. Error: &#34;{0}&#34;&#39;.format(
                        response.json()[&#39;genericResp&#39;])
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)

    def delete(self, cloud_name):
        &#34;&#34;&#34;Deletes an existing HAC cluster

        Args:
            cloud_name  (str)   --  HAC cluster cloud name to be deleted

        Raises:
            SDKException:
                Data type of the input(s) is not valid.

                Response was not success.

                Response was empty.
        &#34;&#34;&#34;
        if not isinstance(cloud_name, str):
            raise SDKException(&#39;HACCluster&#39;, &#39;101&#39;)
        cloud_id = self.all_hac_clusters[cloud_name.lower()]
        req_json = IndexServerConstants.REQUEST_JSON.copy()
        req_json[&#34;opType&#34;] = IndexServerConstants.OPERATION_DELETE
        req_json[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = cloud_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_DELETE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json() \
                    and &#39;errorCode&#39; not in response.json()[&#39;genericResp&#39;]:
                self.refresh()
                return
            if response.json() and &#39;genericResp&#39; in response.json():
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, response.json()[&#39;genericResp&#39;].get(
                        &#39;errorMessage&#39;, &#39;&#39;))
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        self._response_not_success(response)

    @property
    def all_hac_clusters(self):
        &#34;&#34;&#34;Returns the details of all HAC clusters associated with commcell&#34;&#34;&#34;
        return self._all_hac_clusters</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.hac_clusters.HACClusters.all_hac_clusters"><code class="name">var <span class="ident">all_hac_clusters</span></code></dt>
<dd>
<div class="desc"><p>Returns the details of all HAC clusters associated with commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L340-L343" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_hac_clusters(self):
    &#34;&#34;&#34;Returns the details of all HAC clusters associated with commcell&#34;&#34;&#34;
    return self._all_hac_clusters</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.hac_clusters.HACClusters.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, cloud_name, cloud_node_names)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates a new HAC cluster</p>
<h2 id="args">Args</h2>
<p>cloud_name
(str)
&ndash;
hac cluster cloud name
cloud_node_names
(list)
&ndash;
string array of node names to be added to cluster</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Data type of the input(s) is not valid.</p>
<pre><code>Response was not success.

Response was empty.
</code></pre>
<h2 id="returns">Returns</h2>
<p>Object
-
Instance of class HACCluster</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L227-L303" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, cloud_name, cloud_node_names):
    &#34;&#34;&#34;Creates a new HAC cluster

        Args:
            cloud_name      (str)       --  hac cluster cloud name
            cloud_node_names    (list)  --  string array of node names to be added to cluster

        Raises:
            SDKException:
                Data type of the input(s) is not valid.

                Response was not success.

                Response was empty.

        Returns:
            Object  -   Instance of class HACCluster
    &#34;&#34;&#34;
    if not (isinstance(cloud_name, str) and isinstance(cloud_node_names, list)):
        raise SDKException(&#39;HACClusters&#39;, &#39;101&#39;)
    cloud_node_names = sorted(cloud_node_names)
    node_meta_infos = {
        &#39;zkDataPort&#39;: &#39;8091&#39;,
        &#39;zkElectionPort&#39;: &#39;8097&#39;,
        &#39;zkListenerPort&#39;: &#39;8090&#39;,
        &#39;zkServerId&#39;: None,
        &#39;zkDataDir&#39;: None
    }
    req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
    del req_json[&#39;solrCloudInfo&#39;]
    del req_json[&#39;cloudMetaInfos&#39;]
    req_json[&#39;type&#39;] = 6
    req_json[&#39;cloudInfoEntity&#39;] = {
        &#34;_type_&#34;: 169,
        &#34;cloudName&#34;: cloud_name,
        &#34;cloudDisplayName&#34;: cloud_name
    }
    server_id = 1
    for node_name in cloud_node_names:
        node_obj = self._commcell_object.clients.get(node_name)
        node_data = {
            &#34;opType&#34;: IndexServerConstants.OPERATION_ADD,
            &#34;status&#34;: 0,
            &#34;nodeClientEntity&#34;: {
                &#34;_type_&#34;: 3,
                &#34;hostName&#34;: node_obj.client_hostname,
                &#34;clientName&#34;: node_name,
                &#34;clientId&#34;: int(node_obj.client_id)
            },
            &#34;nodeMetaInfos&#34;: []
        }
        node_meta_infos[&#39;zkServerId&#39;] = str(server_id)
        node_meta_infos[&#39;zkDataDir&#39;] = node_obj.install_directory + &#34;\\iDataAgent\\JobResults\\ZKData&#34;
        for node_info in node_meta_infos:
            node_data[&#39;nodeMetaInfos&#39;].append({
                &#39;name&#39;: node_info,
                &#39;value&#39;: node_meta_infos[node_info]
            })
        req_json[&#39;cloudNodes&#39;].append(node_data)
        server_id += 1
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;CLOUD_CREATE&#39;], req_json
    )
    if flag:
        if response.json() and &#39;genericResp&#39; in response.json():
            if response.json()[&#39;genericResp&#39;] == {} and \
                    &#39;cloudId&#39; in response.json():
                self.refresh()
                return HACCluster(self._commcell_object, cloud_name)
            else:
                o_str = &#39;Failed to create HAC Cluster. Error: &#34;{0}&#34;&#39;.format(
                    response.json()[&#39;genericResp&#39;])
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.hac_clusters.HACClusters.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, cloud_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes an existing HAC cluster</p>
<h2 id="args">Args</h2>
<p>cloud_name
(str)
&ndash;
HAC cluster cloud name to be deleted</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Data type of the input(s) is not valid.</p>
<pre><code>Response was not success.

Response was empty.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L305-L338" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, cloud_name):
    &#34;&#34;&#34;Deletes an existing HAC cluster

    Args:
        cloud_name  (str)   --  HAC cluster cloud name to be deleted

    Raises:
        SDKException:
            Data type of the input(s) is not valid.

            Response was not success.

            Response was empty.
    &#34;&#34;&#34;
    if not isinstance(cloud_name, str):
        raise SDKException(&#39;HACCluster&#39;, &#39;101&#39;)
    cloud_id = self.all_hac_clusters[cloud_name.lower()]
    req_json = IndexServerConstants.REQUEST_JSON.copy()
    req_json[&#34;opType&#34;] = IndexServerConstants.OPERATION_DELETE
    req_json[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = cloud_id
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;CLOUD_DELETE&#39;], req_json
    )
    if flag:
        if response.json() and &#39;genericResp&#39; in response.json() \
                and &#39;errorCode&#39; not in response.json()[&#39;genericResp&#39;]:
            self.refresh()
            return
        if response.json() and &#39;genericResp&#39; in response.json():
            raise SDKException(
                &#39;Response&#39;, &#39;102&#39;, response.json()[&#39;genericResp&#39;].get(
                    &#39;errorMessage&#39;, &#39;&#39;))
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.hac_clusters.HACClusters.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, hac_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns instance of HACCluster class is cluster is found</p>
<h2 id="args">Args</h2>
<p>hac_name
(str/int)
&ndash;
hac cluster name or id</p>
<h2 id="returns">Returns</h2>
<p>object
(HACCluster)
&ndash;
Instance of a single hac cluster</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Data type of the input(s) is not valid</p>
<pre><code>HAC Cluster not found
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L193-L217" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, hac_name):
    &#34;&#34;&#34;Returns instance of HACCluster class is cluster is found

        Args:
            hac_name        (str/int)   --      hac cluster name or id

        Returns:
            object          (HACCluster)   --  Instance of a single hac cluster

        Raises:
            SDKException:
                Data type of the input(s) is not valid

                HAC Cluster not found
    &#34;&#34;&#34;
    if isinstance(hac_name, str):
        if hac_name.lower() in self.all_hac_clusters:
            return HACCluster(self._commcell_object, hac_name.lower())
    elif isinstance(hac_name, int):
        for cluster_name in self.all_hac_clusters:
            if int(self._all_hac_clusters[cluster_name]) == int(hac_name):
                return HACCluster(self._commcell_object, cluster_name)
    else:
        raise SDKException(&#39;HACClusters&#39;, &#39;101&#39;)
    raise SDKException(&#34;HACClusters&#34;, &#34;102&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.hac_clusters.HACClusters.has_cluster"><code class="name flex">
<span>def <span class="ident">has_cluster</span></span>(<span>self, hac_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns whether the HAC cluster with given name is present or not</p>
<h2 id="args">Args</h2>
<p>hac_name
(str)
&ndash;
hac cluster name</p>
<h2 id="returns">Returns</h2>
<p>boolean
-
True if hac cluster is associated with the commcell
else returns False</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Data type of the input(s) is not valid</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L175-L191" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_cluster(self, hac_name):
    &#34;&#34;&#34;Returns whether the HAC cluster with given name is present or not

        Args:
            hac_name    (str)       --  hac cluster name

        Returns:
            boolean     -   True if hac cluster is associated with the commcell
            else returns False

        Raises:
            SDKException:
                Data type of the input(s) is not valid
    &#34;&#34;&#34;
    if not isinstance(hac_name, str):
        raise SDKException(&#39;HACClusters&#39;, &#39;101&#39;)
    return hac_name.lower() in self._all_hac_clusters</code></pre>
</details>
</dd>
<dt id="cvpysdk.hac_clusters.HACClusters.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refreshes properties for HACClusters class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/hac_clusters.py#L219-L225" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refreshes properties for HACClusters class&#34;&#34;&#34;
    self._commcell_object.clients.refresh()
    self._commcell_object.client_groups.refresh()
    self._all_hac_clusters = {}
    self._hac_group = None
    self._get_all_clusters()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#hacclusters">HACClusters</a><ul>
<li><a href="#hacclusters-attributes">HACClusters Attributes</a></li>
</ul>
</li>
<li><a href="#haccluster">HACCluster</a><ul>
<li><a href="#haccluster-attributes">HACCluster Attributes</a></li>
</ul>
</li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.hac_clusters.HACCluster" href="#cvpysdk.hac_clusters.HACCluster">HACCluster</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.hac_clusters.HACCluster.cloud_id" href="#cvpysdk.hac_clusters.HACCluster.cloud_id">cloud_id</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACCluster.cluster_id" href="#cvpysdk.hac_clusters.HACCluster.cluster_id">cluster_id</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACCluster.cluster_name" href="#cvpysdk.hac_clusters.HACCluster.cluster_name">cluster_name</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACCluster.modify_node" href="#cvpysdk.hac_clusters.HACCluster.modify_node">modify_node</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACCluster.node_info" href="#cvpysdk.hac_clusters.HACCluster.node_info">node_info</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACCluster.node_names" href="#cvpysdk.hac_clusters.HACCluster.node_names">node_names</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACCluster.refresh" href="#cvpysdk.hac_clusters.HACCluster.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.hac_clusters.HACClusters" href="#cvpysdk.hac_clusters.HACClusters">HACClusters</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.hac_clusters.HACClusters.add" href="#cvpysdk.hac_clusters.HACClusters.add">add</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACClusters.all_hac_clusters" href="#cvpysdk.hac_clusters.HACClusters.all_hac_clusters">all_hac_clusters</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACClusters.delete" href="#cvpysdk.hac_clusters.HACClusters.delete">delete</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACClusters.get" href="#cvpysdk.hac_clusters.HACClusters.get">get</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACClusters.has_cluster" href="#cvpysdk.hac_clusters.HACClusters.has_cluster">has_cluster</a></code></li>
<li><code><a title="cvpysdk.hac_clusters.HACClusters.refresh" href="#cvpysdk.hac_clusters.HACClusters.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>