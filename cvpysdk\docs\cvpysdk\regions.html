<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.regions API documentation</title>
<meta name="description" content="File for associating Workload and Backup destination regions for various entites
class: Regions. Region …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.regions</code></h1>
</header>
<section id="section-intro">
<p>File for associating Workload and Backup destination regions for various entites
class: Regions. Region</p>
<h1 id="regions">Regions:</h1>
<pre><code>_get_regions()                  --  Gets all the regions created in commcell
refresh()                       --  Refresh the list of Regions associated to commcell
has_region()                    --  Checks if region with given name exists
get()                           --  returns Region class object for the specified input name
set_region()                    --  Associate a region to an entity
get_region()                    --  Gets the Region associated to an Entity
calculate_region()              --  Calculates the Region to be associated to an Entity
add()                           --  Method to add a region
delete()                        --  Method to delete regions
</code></pre>
<h2 id="attributes">Attributes</h2>
<p><strong><em>all_regions</em></strong>
&ndash;
returns dict of details about region such as id
Region:
=======
_get_region_id()
&ndash;
Returns the region id
_get_region_properties()
&ndash;
Gets the properties of this region
refresh()
&ndash;
Refresh the properties of the regions</p>
<h2 id="attributes_1">Attributes</h2>
<p><strong><em>region_id</em></strong>
&ndash;
Id of the given Region
<strong><em>region_name</em></strong>
&ndash;
Name of the given region
<strong><em>region_type</em></strong>
&ndash;
type of the given region
<strong><em>locations</em></strong>
&ndash;
locations of the given region
<strong><em>associated_servers_count</em></strong>
&ndash;
associated servers count of the given region
<strong><em>associated_servers</em></strong>
&ndash;
associated servers of the given region
<strong><em>associated_plans_count</em></strong>
&ndash;
associated plans count of the given region
<strong><em>associated_plans</em></strong>
&ndash;
associated plan of the given region</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L1-L560" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34; File for associating Workload and Backup destination regions for various entites
class: Regions. Region

Regions:
========
    _get_regions()                  --  Gets all the regions created in commcell
    refresh()                       --  Refresh the list of Regions associated to commcell
    has_region()                    --  Checks if region with given name exists
    get()                           --  returns Region class object for the specified input name
    set_region()                    --  Associate a region to an entity
    get_region()                    --  Gets the Region associated to an Entity
    calculate_region()              --  Calculates the Region to be associated to an Entity
    add()                           --  Method to add a region
    delete()                        --  Method to delete regions

Attributes:

    ***all_regions***               --  returns dict of details about region such as id

Region:
=======
    _get_region_id()                --  Returns the region id
    _get_region_properties()        --  Gets the properties of this region
    refresh()                       --  Refresh the properties of the regions

Attributes:
    ***region_id***                 --  Id of the given Region
    ***region_name***               --  Name of the given region
    ***region_type***               --  type of the given region
    ***locations***                 --  locations of the given region
    ***associated_servers_count***  --  associated servers count of the given region
    ***associated_servers***        --  associated servers of the given region
    ***associated_plans_count***    --  associated plans count of the given region
    ***associated_plans***          --  associated plan of the given region

&#34;&#34;&#34;
from .exception import SDKException


class Regions:
    &#34;&#34;&#34;
    Class for representing all the Regions created in the commcell
    &#34;&#34;&#34;
    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialises the object of Regions class&#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._update_response_ = commcell_object._update_response_
        self._regions_api = self._commcell_object._services[&#39;REGIONS&#39;]
        self._EDIT_REGION = self._commcell_object._services[&#39;EDIT_REGION&#39;]
        self._GET_REGION = self._commcell_object._services[&#39;GET_REGION&#39;]
        self._CALCULATE_REGION = self._commcell_object._services[&#39;CALCULATE_REGION&#39;]
        self._regions = None
        self.refresh()

    def _get_regions(self):
        &#34;&#34;&#34;Gets all the regions created in commcell&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._regions_api)
        if flag:
            regions = {}
            if response.json() and &#39;regions&#39; in response.json():

                name_count = {}

                for region in response.json()[&#39;regions&#39;]:
                    temp_name = region[&#39;name&#39;].lower()
                    temp_company = region.get(&#39;company&#39;, {}).get(&#39;name&#39;, &#39;&#39;).lower()

                    if temp_name in name_count:
                        name_count[temp_name].add(temp_company)
                    else:
                        name_count[temp_name] = {temp_company}

                for region in response.json()[&#39;regions&#39;]:
                    temp_name = region[&#39;name&#39;].lower()
                    temp_id = region[&#39;id&#39;]
                    temp_company = region.get(&#39;company&#39;, {}).get(&#39;name&#39;, &#39;&#39;).lower()

                    if len(name_count[temp_name]) &gt; 1:
                        unique_key = f&#34;{temp_name}_({temp_company})&#34;
                    else:
                        unique_key = temp_name
                    regions[unique_key] = temp_id

                return regions

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the list of Regions associated to commcell&#34;&#34;&#34;
        self._regions = self._get_regions()

    def has_region(self, name):
        &#34;&#34;&#34;Checks if the given Region exists in the Commcell.

            Args:
                name    (str)   --  name of the Region

            Returns:
                bool    -   boolean output whether the Region exists in the commcell or not

            Raises:
                SDKException:
                    if type of the Region name argument is not string

        &#34;&#34;&#34;
        if not isinstance(name, str):
            raise SDKException(&#39;Region&#39;, &#39;103&#39;)

        return self._regions and (name.lower() in self._regions)

    def get(self, name):
        &#34;&#34;&#34;
        Returns the instance of Region class for the given Region name
        Args:
            name    (str)   --  name of the Region

        Returns:
            object  -- Instance of Region class for the given Region name

        Raises:
            SDKException:
                - If the Region name argument is not a string

                - If No Region found in commcell with the given region name
        &#34;&#34;&#34;
        if not isinstance(name, str):
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#34;Invalid input received&#34;)

        if not self.has_region(name):
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, f&#34;Region {name.lower()} not present in commcell&#34;)

        return Region(self._commcell_object, name, self._regions[name.lower()])

    def set_region(self, entity_type, entity_id, entity_region_type, region_id):
        &#34;&#34;&#34;
        Associate a region to an entity
        Args:
            entity_type         (str)   :   Type of the entity
                                            (eg:    COMMCELL,
                                                    COMPANY,
                                                    CLIENT,
                                                    CLIENT_GROUP,
                                                    MEDIAAGENT,
                                                    STORAGE_POOL, etc
                                            )
            entity_id           (int/str):   unique id of the entity

            entity_region_type  (str)   :   Type of the region
                                            (WORKLOAD or BACKUP)

            region_id           (int)   :   ID of the region from app_regions
        &#34;&#34;&#34;
        if isinstance(region_id,str):
            region_id = int(region_id)
        request = {
            &#34;entityRegionType&#34;: entity_region_type,
            &#34;region&#34;:
                {
                    &#34;id&#34;: region_id
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._EDIT_REGION % (entity_type, entity_id), request
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if error_code != 0:
                    if error_code == 50000:
                        raise SDKException(&#39;Regions&#39;, &#39;101&#39;)
                    elif error_code == 547:
                        raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;Invalid regionID provided in request&#39;)
                    else:
                        error_string = response.json()[&#39;errorMessage&#39;]
                        raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;{0}&#39;.format(error_string))

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_region(self, entity_type, entity_id, entity_region_type):
        &#34;&#34;&#34;
        Gets the Region associated to an Entity
        Args:
            entity_type         (str)   :   Type of the entity
                                            (eg:    COMMCELL,
                                                    COMPANY,


                                                    CLIENT,
                                                    CLIENT_GROUP,
                                                    MEDIAAGENT,
                                                    STORAGE_POOL, etc
                                            )
            entity_id           (int)   :   unique id of the entity

            entity_region_type  (str)   :   Type of the region
                                            (WORKLOAD or BACKUP)
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._GET_REGION % (entity_type, entity_id, entity_region_type)
        )

        if flag:
            if response.json():
                try:
                    if response.json()[&#39;errorCode&#39;]:
                        error_string = response.json()[&#39;errorMessage&#39;]
                        raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;{0}&#39;.format(error_string))

                except:
                    if response.json().get(&#39;regionId&#39;):
                        return response.json().get(&#39;regionId&#39;)
                    return 0

            else:
                return None
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def calculate_region(self, entity_type, entity_id, entity_region_type):
        &#34;&#34;&#34;
                Calculates the Region to be associated to an Entity
                Args:
                    entity_type         (str)   :   Type of the entity
                                                    (eg:    COMMCELL,
                                                            COMPANY,
                                                            CLIENT,
                                                            CLIENT_GROUP,
                                                            MEDIAAGENT,
                                                            STORAGE_POOL, etc
                                                    )
                    entity_id           (int)   :   unique id of the entity

                    entity_region_type  (str)   :   Type of the region
                                                    (WORKLOAD or BACKUP)
                &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._CALCULATE_REGION % (entity_type, entity_id, entity_region_type)
        )

        if flag:
            if response.json():
                try:
                    if response.json()[&#39;errorCode&#39;]:
                        error_string = response.json()[&#39;errorMessage&#39;]
                        raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;{0}&#39;.format(error_string))

                except:
                    return response.json()[&#39;regionId&#39;]

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add(self, region_name: str, region_type: str, locations: list) -&gt; object:
        &#34;&#34;&#34;
        Method to add a region

            Args:
                region_name    (str)      --      name of the region
                region_type    (str)      --      type of region
                locations      (list)     --      list of dictionaries containing details of a location
                    e.g. locations = [{&#34;city&#34;:&#34;city1&#34;,
                                        &#34;state&#34;:&#34;stateOfCity1&#34;,
                                        &#34;country&#34;:&#34;countryOfCity1&#34;,
                                        &#34;latitude&#34;:&#34;latitudeOfCity1&#34;,
                                        &#34;longitude&#34;:&#34;longitudeOfCity1&#34;},
                                        {&#34;city&#34;:&#34;city2&#34;,
                                        &#34;state&#34;:&#34;stateOfCity2&#34;,
                                        &#34;country&#34;:&#34;countryOfCity2&#34;,
                                        &#34;latitude&#34;:&#34;latitudeOfCity2&#34;,
                                        &#34;longitude&#34;:&#34;longitudeOfCity2&#34;}]]

            Returns:
                object  --   instance of the region class created by this method

            Raises:
                SDKException:
                    if input parameters are incorrect

                    if Plan already exists

                    if invalid region type is passed
        &#34;&#34;&#34;
        valid_region_types = [&#39;USER_CREATED&#39;, &#39;OCI&#39;, &#39;AWS&#39;, &#39;AZURE&#39;, &#39;GCP&#39;]

        if not (isinstance(region_name, str) and isinstance(region_type, str) and isinstance(locations, list)):
            raise SDKException(&#39;Region&#39;, &#39;101&#39;)

        elif self.has_region(region_name):
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#39;Region &#34;{0}&#34; already exists&#39;.format(region_name))

        elif region_type.upper() not in valid_region_types:
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#39;Invalid region type&#39;)

        request_json = {
            &#34;name&#34;: region_name,
            &#34;type&#34;: region_type,
            &#34;locations&#34;: locations
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._regions_api, request_json)

        if flag:
            if response.json():
                response_value = response.json()
                error_message = response_value.get(&#39;errorMessage&#39;)
                error_code = response_value.get(&#39;errorCode&#39;, 0)

                if error_code != 0:
                    raise SDKException(&#39;Region&#39;, &#39;102&#39;, f&#39;Failed to create new Region\nError: &#34;{error_message}&#34;&#39;)

                region_name = response_value[&#39;name&#39;]

                self.refresh()

                return self.get(region_name)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, region_name: str) -&gt; None:
        &#34;&#34;&#34;
        Method to delete regions

            Args:
                region_name    (str)   --  name of the region

            Returns:
                None    --  if the region is removed successfully

            Raises:
                SDKException:
                    if type of the Region name argument is not string

                    if no region exists with the given name

                    if response is empty

                    if failed to delete the region

        &#34;&#34;&#34;
        if not isinstance(region_name, str):
            raise SDKException(&#39;Region&#39;, &#39;101&#39;)

        else:
            region_name = region_name.lower()

            if not self.has_region(region_name):
                raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#39;No Region exists with name: &#34;{0}&#34;&#39;.format(region_name))

            region_id = self._regions[region_name]
            delete_region_service = self._commcell_object._services[&#39;REGION&#39;]%(region_id)
            flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_region_service)

            if not flag:
                response_string = self._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            if not response.json():
                raise SDKException(&#39;Response&#39;, &#39;102&#39; &#39;Empty response received from server.&#39;)
            response_json = response.json()
            error_code = str(response_json.get(&#39;errorCode&#39;))
            error_message = response_json.get(&#39;errorMessage&#39;)

            if error_code == &#39;0&#39;:
                self.refresh()
            else:
                raise SDKException(&#39;Region&#39;, &#39;102&#39;, f&#39;Failed to delete region. Error: {error_message}&#39;)

    @property
    def all_regions(self):
        &#34;&#34;&#34;Returns dict consisting of all regions details such as id&#34;&#34;&#34;
        return self._regions


class Region:
    &#34;&#34;&#34; Class for performing operations on a given Region &#34;&#34;&#34;
    def __init__(self, commcell_object, region_name, region_id=None):
        &#34;&#34;&#34; Initialise the Region class instance.
            Args:
                commcell_object     (object)    --  instance of the Commcell class

                region_name         (str)       --  name of the region

                region_id           (int)       --  ID of the REgion
                                                    Default : None

            Returns:
                object  -   instance of the Region class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._region_name = region_name
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._update_response_ = commcell_object._update_response_

        self._region_name = region_name

        if region_id:
            self._region_id = str(region_id)
        else:
            self._region_id = self._get_region_id()

        self._region_api = self._commcell_object._services[&#39;REGION&#39;]%(self._region_id)
        self._region_type = None
        self._locations = []
        self._associated_servers_count = None
        self._associated_servers = []
        self._associated_plans_count = None
        self._associated_plans = []
        self.refresh()
        self._region_properties = None

    def _get_region_id(self):
        &#34;&#34;&#34; Returns the ID of the Region &#34;&#34;&#34;
        regions = Regions(self._commcell_object)
        return regions.get(self._region_name).region_id

    def _get_region_properties(self):
        &#34;&#34;&#34;Gets the properties of this region&#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._region_api)

        if flag:
            if response.json():
                properties = response.json()
                self._region_id = properties.get(&#39;id&#39;)
                self._region_name = properties.get(&#39;name&#39;)
                self._region_type = properties.get(&#39;regionType&#39;)
                if &#39;locations&#39; in properties:
                    self._locations = properties.get(&#39;locations&#39;)
                if &#39;associatedServers&#39; in properties:
                    self._associated_servers_count = properties.get(&#39;associatedServers&#39;, {}).get(&#39;serversCount&#39;)
                    if &#39;servers&#39; in properties.get(&#39;associatedServers&#39;):
                        self._associated_servers = properties.get(&#39;associatedServers&#39;, {}).get(&#39;servers&#39;)
                if &#39;associatedRegionBasedPlans&#39; in properties:
                    self._associated_plans_count = properties.get(&#39;associatedRegionBasedPlans&#39;, {}).get(&#39;plansCount&#39;)
                    if &#39;plans&#39; in properties.get(&#39;associatedRegionBasedPlans&#39;):
                        for plan in properties.get(&#39;associatedRegionBasedPlans&#39;, {}).get(&#39;plans&#39;):
                            self._associated_plans.append(plan.get(&#39;plan&#39;))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def region_id(self) -&gt; int:
        &#34;&#34;&#34;
        Get Region ID

        Returns:
            int -- region ID
        &#34;&#34;&#34;
        return self._region_id

    @property
    def region_name(self) -&gt; str:
        &#34;&#34;&#34;
        Get Region name

        Returns:
            str -- name of the region
        &#34;&#34;&#34;
        return self._region_name

    @property
    def region_type(self) -&gt; str:
        &#34;&#34;&#34;
        Get Region type

        Returns:
            str -- type of the region
        &#34;&#34;&#34;
        return self._region_type

    @property
    def locations(self) -&gt; list:
        &#34;&#34;&#34;
        Get locations

        Returns:
            list -- locations added in the region
        &#34;&#34;&#34;
        return self._locations

    @property
    def associated_servers_count(self) -&gt; int:
        &#34;&#34;&#34;
         Get associated servers count

        Returns:
            int -- count of servers associated with the region
         &#34;&#34;&#34;
        return self._associated_servers_count

    @property
    def associated_servers(self) -&gt; list:
        &#34;&#34;&#34;
         Get associated servers

        Returns:
            list -- list of servers associated with the region
         &#34;&#34;&#34;
        return self._associated_servers

    @property
    def associated_plans_count(self) -&gt; int:
        &#34;&#34;&#34;
        Get associated plans count

        Returns:
            int -- count of plans associated with the region
        &#34;&#34;&#34;
        return self._associated_plans_count

    @property
    def associated_plans(self) -&gt; list:
        &#34;&#34;&#34;
        Get associated plans

        Returns:
            list -- list of plans associated with the region
        &#34;&#34;&#34;
        return self._associated_plans

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the regions.&#34;&#34;&#34;
        self._region_properties = self._get_region_properties()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.regions.Region"><code class="flex name class">
<span>class <span class="ident">Region</span></span>
<span>(</span><span>commcell_object, region_name, region_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on a given Region </p>
<p>Initialise the Region class instance.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>region_name
(str)
&ndash;
name of the region</p>
<p>region_id
(int)
&ndash;
ID of the REgion
Default : None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Region class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L408-L560" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Region:
    &#34;&#34;&#34; Class for performing operations on a given Region &#34;&#34;&#34;
    def __init__(self, commcell_object, region_name, region_id=None):
        &#34;&#34;&#34; Initialise the Region class instance.
            Args:
                commcell_object     (object)    --  instance of the Commcell class

                region_name         (str)       --  name of the region

                region_id           (int)       --  ID of the REgion
                                                    Default : None

            Returns:
                object  -   instance of the Region class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._region_name = region_name
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._update_response_ = commcell_object._update_response_

        self._region_name = region_name

        if region_id:
            self._region_id = str(region_id)
        else:
            self._region_id = self._get_region_id()

        self._region_api = self._commcell_object._services[&#39;REGION&#39;]%(self._region_id)
        self._region_type = None
        self._locations = []
        self._associated_servers_count = None
        self._associated_servers = []
        self._associated_plans_count = None
        self._associated_plans = []
        self.refresh()
        self._region_properties = None

    def _get_region_id(self):
        &#34;&#34;&#34; Returns the ID of the Region &#34;&#34;&#34;
        regions = Regions(self._commcell_object)
        return regions.get(self._region_name).region_id

    def _get_region_properties(self):
        &#34;&#34;&#34;Gets the properties of this region&#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._region_api)

        if flag:
            if response.json():
                properties = response.json()
                self._region_id = properties.get(&#39;id&#39;)
                self._region_name = properties.get(&#39;name&#39;)
                self._region_type = properties.get(&#39;regionType&#39;)
                if &#39;locations&#39; in properties:
                    self._locations = properties.get(&#39;locations&#39;)
                if &#39;associatedServers&#39; in properties:
                    self._associated_servers_count = properties.get(&#39;associatedServers&#39;, {}).get(&#39;serversCount&#39;)
                    if &#39;servers&#39; in properties.get(&#39;associatedServers&#39;):
                        self._associated_servers = properties.get(&#39;associatedServers&#39;, {}).get(&#39;servers&#39;)
                if &#39;associatedRegionBasedPlans&#39; in properties:
                    self._associated_plans_count = properties.get(&#39;associatedRegionBasedPlans&#39;, {}).get(&#39;plansCount&#39;)
                    if &#39;plans&#39; in properties.get(&#39;associatedRegionBasedPlans&#39;):
                        for plan in properties.get(&#39;associatedRegionBasedPlans&#39;, {}).get(&#39;plans&#39;):
                            self._associated_plans.append(plan.get(&#39;plan&#39;))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def region_id(self) -&gt; int:
        &#34;&#34;&#34;
        Get Region ID

        Returns:
            int -- region ID
        &#34;&#34;&#34;
        return self._region_id

    @property
    def region_name(self) -&gt; str:
        &#34;&#34;&#34;
        Get Region name

        Returns:
            str -- name of the region
        &#34;&#34;&#34;
        return self._region_name

    @property
    def region_type(self) -&gt; str:
        &#34;&#34;&#34;
        Get Region type

        Returns:
            str -- type of the region
        &#34;&#34;&#34;
        return self._region_type

    @property
    def locations(self) -&gt; list:
        &#34;&#34;&#34;
        Get locations

        Returns:
            list -- locations added in the region
        &#34;&#34;&#34;
        return self._locations

    @property
    def associated_servers_count(self) -&gt; int:
        &#34;&#34;&#34;
         Get associated servers count

        Returns:
            int -- count of servers associated with the region
         &#34;&#34;&#34;
        return self._associated_servers_count

    @property
    def associated_servers(self) -&gt; list:
        &#34;&#34;&#34;
         Get associated servers

        Returns:
            list -- list of servers associated with the region
         &#34;&#34;&#34;
        return self._associated_servers

    @property
    def associated_plans_count(self) -&gt; int:
        &#34;&#34;&#34;
        Get associated plans count

        Returns:
            int -- count of plans associated with the region
        &#34;&#34;&#34;
        return self._associated_plans_count

    @property
    def associated_plans(self) -&gt; list:
        &#34;&#34;&#34;
        Get associated plans

        Returns:
            list -- list of plans associated with the region
        &#34;&#34;&#34;
        return self._associated_plans

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the regions.&#34;&#34;&#34;
        self._region_properties = self._get_region_properties()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.regions.Region.associated_plans"><code class="name">var <span class="ident">associated_plans</span> : list</code></dt>
<dd>
<div class="desc"><p>Get associated plans</p>
<h2 id="returns">Returns</h2>
<p>list &ndash; list of plans associated with the region</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L548-L556" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associated_plans(self) -&gt; list:
    &#34;&#34;&#34;
    Get associated plans

    Returns:
        list -- list of plans associated with the region
    &#34;&#34;&#34;
    return self._associated_plans</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Region.associated_plans_count"><code class="name">var <span class="ident">associated_plans_count</span> : int</code></dt>
<dd>
<div class="desc"><p>Get associated plans count</p>
<h2 id="returns">Returns</h2>
<p>int &ndash; count of plans associated with the region</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L538-L546" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associated_plans_count(self) -&gt; int:
    &#34;&#34;&#34;
    Get associated plans count

    Returns:
        int -- count of plans associated with the region
    &#34;&#34;&#34;
    return self._associated_plans_count</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Region.associated_servers"><code class="name">var <span class="ident">associated_servers</span> : list</code></dt>
<dd>
<div class="desc"><p>Get associated servers</p>
<h2 id="returns">Returns</h2>
<p>list &ndash; list of servers associated with the region</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L528-L536" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associated_servers(self) -&gt; list:
    &#34;&#34;&#34;
     Get associated servers

    Returns:
        list -- list of servers associated with the region
     &#34;&#34;&#34;
    return self._associated_servers</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Region.associated_servers_count"><code class="name">var <span class="ident">associated_servers_count</span> : int</code></dt>
<dd>
<div class="desc"><p>Get associated servers count</p>
<h2 id="returns">Returns</h2>
<p>int &ndash; count of servers associated with the region</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L518-L526" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associated_servers_count(self) -&gt; int:
    &#34;&#34;&#34;
     Get associated servers count

    Returns:
        int -- count of servers associated with the region
     &#34;&#34;&#34;
    return self._associated_servers_count</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Region.locations"><code class="name">var <span class="ident">locations</span> : list</code></dt>
<dd>
<div class="desc"><p>Get locations</p>
<h2 id="returns">Returns</h2>
<p>list &ndash; locations added in the region</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L508-L516" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def locations(self) -&gt; list:
    &#34;&#34;&#34;
    Get locations

    Returns:
        list -- locations added in the region
    &#34;&#34;&#34;
    return self._locations</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Region.region_id"><code class="name">var <span class="ident">region_id</span> : int</code></dt>
<dd>
<div class="desc"><p>Get Region ID</p>
<h2 id="returns">Returns</h2>
<p>int &ndash; region ID</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L478-L486" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def region_id(self) -&gt; int:
    &#34;&#34;&#34;
    Get Region ID

    Returns:
        int -- region ID
    &#34;&#34;&#34;
    return self._region_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Region.region_name"><code class="name">var <span class="ident">region_name</span> : str</code></dt>
<dd>
<div class="desc"><p>Get Region name</p>
<h2 id="returns">Returns</h2>
<p>str &ndash; name of the region</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L488-L496" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def region_name(self) -&gt; str:
    &#34;&#34;&#34;
    Get Region name

    Returns:
        str -- name of the region
    &#34;&#34;&#34;
    return self._region_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Region.region_type"><code class="name">var <span class="ident">region_type</span> : str</code></dt>
<dd>
<div class="desc"><p>Get Region type</p>
<h2 id="returns">Returns</h2>
<p>str &ndash; type of the region</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L498-L506" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def region_type(self) -&gt; str:
    &#34;&#34;&#34;
    Get Region type

    Returns:
        str -- type of the region
    &#34;&#34;&#34;
    return self._region_type</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.regions.Region.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the regions.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L558-L560" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the regions.&#34;&#34;&#34;
    self._region_properties = self._get_region_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.regions.Regions"><code class="flex name class">
<span>class <span class="ident">Regions</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the Regions created in the commcell</p>
<p>Initialises the object of Regions class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L58-L405" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Regions:
    &#34;&#34;&#34;
    Class for representing all the Regions created in the commcell
    &#34;&#34;&#34;
    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialises the object of Regions class&#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._update_response_ = commcell_object._update_response_
        self._regions_api = self._commcell_object._services[&#39;REGIONS&#39;]
        self._EDIT_REGION = self._commcell_object._services[&#39;EDIT_REGION&#39;]
        self._GET_REGION = self._commcell_object._services[&#39;GET_REGION&#39;]
        self._CALCULATE_REGION = self._commcell_object._services[&#39;CALCULATE_REGION&#39;]
        self._regions = None
        self.refresh()

    def _get_regions(self):
        &#34;&#34;&#34;Gets all the regions created in commcell&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._regions_api)
        if flag:
            regions = {}
            if response.json() and &#39;regions&#39; in response.json():

                name_count = {}

                for region in response.json()[&#39;regions&#39;]:
                    temp_name = region[&#39;name&#39;].lower()
                    temp_company = region.get(&#39;company&#39;, {}).get(&#39;name&#39;, &#39;&#39;).lower()

                    if temp_name in name_count:
                        name_count[temp_name].add(temp_company)
                    else:
                        name_count[temp_name] = {temp_company}

                for region in response.json()[&#39;regions&#39;]:
                    temp_name = region[&#39;name&#39;].lower()
                    temp_id = region[&#39;id&#39;]
                    temp_company = region.get(&#39;company&#39;, {}).get(&#39;name&#39;, &#39;&#39;).lower()

                    if len(name_count[temp_name]) &gt; 1:
                        unique_key = f&#34;{temp_name}_({temp_company})&#34;
                    else:
                        unique_key = temp_name
                    regions[unique_key] = temp_id

                return regions

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the list of Regions associated to commcell&#34;&#34;&#34;
        self._regions = self._get_regions()

    def has_region(self, name):
        &#34;&#34;&#34;Checks if the given Region exists in the Commcell.

            Args:
                name    (str)   --  name of the Region

            Returns:
                bool    -   boolean output whether the Region exists in the commcell or not

            Raises:
                SDKException:
                    if type of the Region name argument is not string

        &#34;&#34;&#34;
        if not isinstance(name, str):
            raise SDKException(&#39;Region&#39;, &#39;103&#39;)

        return self._regions and (name.lower() in self._regions)

    def get(self, name):
        &#34;&#34;&#34;
        Returns the instance of Region class for the given Region name
        Args:
            name    (str)   --  name of the Region

        Returns:
            object  -- Instance of Region class for the given Region name

        Raises:
            SDKException:
                - If the Region name argument is not a string

                - If No Region found in commcell with the given region name
        &#34;&#34;&#34;
        if not isinstance(name, str):
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#34;Invalid input received&#34;)

        if not self.has_region(name):
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, f&#34;Region {name.lower()} not present in commcell&#34;)

        return Region(self._commcell_object, name, self._regions[name.lower()])

    def set_region(self, entity_type, entity_id, entity_region_type, region_id):
        &#34;&#34;&#34;
        Associate a region to an entity
        Args:
            entity_type         (str)   :   Type of the entity
                                            (eg:    COMMCELL,
                                                    COMPANY,
                                                    CLIENT,
                                                    CLIENT_GROUP,
                                                    MEDIAAGENT,
                                                    STORAGE_POOL, etc
                                            )
            entity_id           (int/str):   unique id of the entity

            entity_region_type  (str)   :   Type of the region
                                            (WORKLOAD or BACKUP)

            region_id           (int)   :   ID of the region from app_regions
        &#34;&#34;&#34;
        if isinstance(region_id,str):
            region_id = int(region_id)
        request = {
            &#34;entityRegionType&#34;: entity_region_type,
            &#34;region&#34;:
                {
                    &#34;id&#34;: region_id
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._EDIT_REGION % (entity_type, entity_id), request
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if error_code != 0:
                    if error_code == 50000:
                        raise SDKException(&#39;Regions&#39;, &#39;101&#39;)
                    elif error_code == 547:
                        raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;Invalid regionID provided in request&#39;)
                    else:
                        error_string = response.json()[&#39;errorMessage&#39;]
                        raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;{0}&#39;.format(error_string))

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_region(self, entity_type, entity_id, entity_region_type):
        &#34;&#34;&#34;
        Gets the Region associated to an Entity
        Args:
            entity_type         (str)   :   Type of the entity
                                            (eg:    COMMCELL,
                                                    COMPANY,


                                                    CLIENT,
                                                    CLIENT_GROUP,
                                                    MEDIAAGENT,
                                                    STORAGE_POOL, etc
                                            )
            entity_id           (int)   :   unique id of the entity

            entity_region_type  (str)   :   Type of the region
                                            (WORKLOAD or BACKUP)
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._GET_REGION % (entity_type, entity_id, entity_region_type)
        )

        if flag:
            if response.json():
                try:
                    if response.json()[&#39;errorCode&#39;]:
                        error_string = response.json()[&#39;errorMessage&#39;]
                        raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;{0}&#39;.format(error_string))

                except:
                    if response.json().get(&#39;regionId&#39;):
                        return response.json().get(&#39;regionId&#39;)
                    return 0

            else:
                return None
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def calculate_region(self, entity_type, entity_id, entity_region_type):
        &#34;&#34;&#34;
                Calculates the Region to be associated to an Entity
                Args:
                    entity_type         (str)   :   Type of the entity
                                                    (eg:    COMMCELL,
                                                            COMPANY,
                                                            CLIENT,
                                                            CLIENT_GROUP,
                                                            MEDIAAGENT,
                                                            STORAGE_POOL, etc
                                                    )
                    entity_id           (int)   :   unique id of the entity

                    entity_region_type  (str)   :   Type of the region
                                                    (WORKLOAD or BACKUP)
                &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._CALCULATE_REGION % (entity_type, entity_id, entity_region_type)
        )

        if flag:
            if response.json():
                try:
                    if response.json()[&#39;errorCode&#39;]:
                        error_string = response.json()[&#39;errorMessage&#39;]
                        raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;{0}&#39;.format(error_string))

                except:
                    return response.json()[&#39;regionId&#39;]

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add(self, region_name: str, region_type: str, locations: list) -&gt; object:
        &#34;&#34;&#34;
        Method to add a region

            Args:
                region_name    (str)      --      name of the region
                region_type    (str)      --      type of region
                locations      (list)     --      list of dictionaries containing details of a location
                    e.g. locations = [{&#34;city&#34;:&#34;city1&#34;,
                                        &#34;state&#34;:&#34;stateOfCity1&#34;,
                                        &#34;country&#34;:&#34;countryOfCity1&#34;,
                                        &#34;latitude&#34;:&#34;latitudeOfCity1&#34;,
                                        &#34;longitude&#34;:&#34;longitudeOfCity1&#34;},
                                        {&#34;city&#34;:&#34;city2&#34;,
                                        &#34;state&#34;:&#34;stateOfCity2&#34;,
                                        &#34;country&#34;:&#34;countryOfCity2&#34;,
                                        &#34;latitude&#34;:&#34;latitudeOfCity2&#34;,
                                        &#34;longitude&#34;:&#34;longitudeOfCity2&#34;}]]

            Returns:
                object  --   instance of the region class created by this method

            Raises:
                SDKException:
                    if input parameters are incorrect

                    if Plan already exists

                    if invalid region type is passed
        &#34;&#34;&#34;
        valid_region_types = [&#39;USER_CREATED&#39;, &#39;OCI&#39;, &#39;AWS&#39;, &#39;AZURE&#39;, &#39;GCP&#39;]

        if not (isinstance(region_name, str) and isinstance(region_type, str) and isinstance(locations, list)):
            raise SDKException(&#39;Region&#39;, &#39;101&#39;)

        elif self.has_region(region_name):
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#39;Region &#34;{0}&#34; already exists&#39;.format(region_name))

        elif region_type.upper() not in valid_region_types:
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#39;Invalid region type&#39;)

        request_json = {
            &#34;name&#34;: region_name,
            &#34;type&#34;: region_type,
            &#34;locations&#34;: locations
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._regions_api, request_json)

        if flag:
            if response.json():
                response_value = response.json()
                error_message = response_value.get(&#39;errorMessage&#39;)
                error_code = response_value.get(&#39;errorCode&#39;, 0)

                if error_code != 0:
                    raise SDKException(&#39;Region&#39;, &#39;102&#39;, f&#39;Failed to create new Region\nError: &#34;{error_message}&#34;&#39;)

                region_name = response_value[&#39;name&#39;]

                self.refresh()

                return self.get(region_name)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, region_name: str) -&gt; None:
        &#34;&#34;&#34;
        Method to delete regions

            Args:
                region_name    (str)   --  name of the region

            Returns:
                None    --  if the region is removed successfully

            Raises:
                SDKException:
                    if type of the Region name argument is not string

                    if no region exists with the given name

                    if response is empty

                    if failed to delete the region

        &#34;&#34;&#34;
        if not isinstance(region_name, str):
            raise SDKException(&#39;Region&#39;, &#39;101&#39;)

        else:
            region_name = region_name.lower()

            if not self.has_region(region_name):
                raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#39;No Region exists with name: &#34;{0}&#34;&#39;.format(region_name))

            region_id = self._regions[region_name]
            delete_region_service = self._commcell_object._services[&#39;REGION&#39;]%(region_id)
            flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_region_service)

            if not flag:
                response_string = self._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            if not response.json():
                raise SDKException(&#39;Response&#39;, &#39;102&#39; &#39;Empty response received from server.&#39;)
            response_json = response.json()
            error_code = str(response_json.get(&#39;errorCode&#39;))
            error_message = response_json.get(&#39;errorMessage&#39;)

            if error_code == &#39;0&#39;:
                self.refresh()
            else:
                raise SDKException(&#39;Region&#39;, &#39;102&#39;, f&#39;Failed to delete region. Error: {error_message}&#39;)

    @property
    def all_regions(self):
        &#34;&#34;&#34;Returns dict consisting of all regions details such as id&#34;&#34;&#34;
        return self._regions</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.regions.Regions.all_regions"><code class="name">var <span class="ident">all_regions</span></code></dt>
<dd>
<div class="desc"><p>Returns dict consisting of all regions details such as id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L402-L405" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_regions(self):
    &#34;&#34;&#34;Returns dict consisting of all regions details such as id&#34;&#34;&#34;
    return self._regions</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.regions.Regions.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, region_name: str, region_type: str, locations: list) ‑> object</span>
</code></dt>
<dd>
<div class="desc"><p>Method to add a region</p>
<pre><code>Args:
    region_name    (str)      --      name of the region
    region_type    (str)      --      type of region
    locations      (list)     --      list of dictionaries containing details of a location
        e.g. locations = [{"city":"city1",
                            "state":"stateOfCity1",
                            "country":"countryOfCity1",
                            "latitude":"latitudeOfCity1",
                            "longitude":"longitudeOfCity1"},
                            {"city":"city2",
                            "state":"stateOfCity2",
                            "country":"countryOfCity2",
                            "latitude":"latitudeOfCity2",
                            "longitude":"longitudeOfCity2"}]]

Returns:
    object  --   instance of the region class created by this method

Raises:
    SDKException:
        if input parameters are incorrect

        if Plan already exists

        if invalid region type is passed
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L286-L352" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, region_name: str, region_type: str, locations: list) -&gt; object:
    &#34;&#34;&#34;
    Method to add a region

        Args:
            region_name    (str)      --      name of the region
            region_type    (str)      --      type of region
            locations      (list)     --      list of dictionaries containing details of a location
                e.g. locations = [{&#34;city&#34;:&#34;city1&#34;,
                                    &#34;state&#34;:&#34;stateOfCity1&#34;,
                                    &#34;country&#34;:&#34;countryOfCity1&#34;,
                                    &#34;latitude&#34;:&#34;latitudeOfCity1&#34;,
                                    &#34;longitude&#34;:&#34;longitudeOfCity1&#34;},
                                    {&#34;city&#34;:&#34;city2&#34;,
                                    &#34;state&#34;:&#34;stateOfCity2&#34;,
                                    &#34;country&#34;:&#34;countryOfCity2&#34;,
                                    &#34;latitude&#34;:&#34;latitudeOfCity2&#34;,
                                    &#34;longitude&#34;:&#34;longitudeOfCity2&#34;}]]

        Returns:
            object  --   instance of the region class created by this method

        Raises:
            SDKException:
                if input parameters are incorrect

                if Plan already exists

                if invalid region type is passed
    &#34;&#34;&#34;
    valid_region_types = [&#39;USER_CREATED&#39;, &#39;OCI&#39;, &#39;AWS&#39;, &#39;AZURE&#39;, &#39;GCP&#39;]

    if not (isinstance(region_name, str) and isinstance(region_type, str) and isinstance(locations, list)):
        raise SDKException(&#39;Region&#39;, &#39;101&#39;)

    elif self.has_region(region_name):
        raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#39;Region &#34;{0}&#34; already exists&#39;.format(region_name))

    elif region_type.upper() not in valid_region_types:
        raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#39;Invalid region type&#39;)

    request_json = {
        &#34;name&#34;: region_name,
        &#34;type&#34;: region_type,
        &#34;locations&#34;: locations
    }
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._regions_api, request_json)

    if flag:
        if response.json():
            response_value = response.json()
            error_message = response_value.get(&#39;errorMessage&#39;)
            error_code = response_value.get(&#39;errorCode&#39;, 0)

            if error_code != 0:
                raise SDKException(&#39;Region&#39;, &#39;102&#39;, f&#39;Failed to create new Region\nError: &#34;{error_message}&#34;&#39;)

            region_name = response_value[&#39;name&#39;]

            self.refresh()

            return self.get(region_name)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Regions.calculate_region"><code class="name flex">
<span>def <span class="ident">calculate_region</span></span>(<span>self, entity_type, entity_id, entity_region_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Calculates the Region to be associated to an Entity</p>
<h2 id="args">Args</h2>
<p>entity_type
(str)
:
Type of the entity
(eg:
COMMCELL,
COMPANY,
CLIENT,
CLIENT_GROUP,
MEDIAAGENT,
STORAGE_POOL, etc
)
entity_id
(int)
:
unique id of the entity</p>
<p>entity_region_type
(str)
:
Type of the region
(WORKLOAD or BACKUP)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L249-L284" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def calculate_region(self, entity_type, entity_id, entity_region_type):
    &#34;&#34;&#34;
            Calculates the Region to be associated to an Entity
            Args:
                entity_type         (str)   :   Type of the entity
                                                (eg:    COMMCELL,
                                                        COMPANY,
                                                        CLIENT,
                                                        CLIENT_GROUP,
                                                        MEDIAAGENT,
                                                        STORAGE_POOL, etc
                                                )
                entity_id           (int)   :   unique id of the entity

                entity_region_type  (str)   :   Type of the region
                                                (WORKLOAD or BACKUP)
            &#34;&#34;&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;GET&#39;, self._CALCULATE_REGION % (entity_type, entity_id, entity_region_type)
    )

    if flag:
        if response.json():
            try:
                if response.json()[&#39;errorCode&#39;]:
                    error_string = response.json()[&#39;errorMessage&#39;]
                    raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;{0}&#39;.format(error_string))

            except:
                return response.json()[&#39;regionId&#39;]

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Regions.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, region_name: str) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Method to delete regions</p>
<pre><code>Args:
    region_name    (str)   --  name of the region

Returns:
    None    --  if the region is removed successfully

Raises:
    SDKException:
        if type of the Region name argument is not string

        if no region exists with the given name

        if response is empty

        if failed to delete the region
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L354-L400" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, region_name: str) -&gt; None:
    &#34;&#34;&#34;
    Method to delete regions

        Args:
            region_name    (str)   --  name of the region

        Returns:
            None    --  if the region is removed successfully

        Raises:
            SDKException:
                if type of the Region name argument is not string

                if no region exists with the given name

                if response is empty

                if failed to delete the region

    &#34;&#34;&#34;
    if not isinstance(region_name, str):
        raise SDKException(&#39;Region&#39;, &#39;101&#39;)

    else:
        region_name = region_name.lower()

        if not self.has_region(region_name):
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#39;No Region exists with name: &#34;{0}&#34;&#39;.format(region_name))

        region_id = self._regions[region_name]
        delete_region_service = self._commcell_object._services[&#39;REGION&#39;]%(region_id)
        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_region_service)

        if not flag:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        if not response.json():
            raise SDKException(&#39;Response&#39;, &#39;102&#39; &#39;Empty response received from server.&#39;)
        response_json = response.json()
        error_code = str(response_json.get(&#39;errorCode&#39;))
        error_message = response_json.get(&#39;errorMessage&#39;)

        if error_code == &#39;0&#39;:
            self.refresh()
        else:
            raise SDKException(&#39;Region&#39;, &#39;102&#39;, f&#39;Failed to delete region. Error: {error_message}&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Regions.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the instance of Region class for the given Region name</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the Region</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash; Instance of Region class for the given Region name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
- If the Region name argument is not a string</p>
<pre><code>- If No Region found in commcell with the given region name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L133-L154" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, name):
    &#34;&#34;&#34;
    Returns the instance of Region class for the given Region name
    Args:
        name    (str)   --  name of the Region

    Returns:
        object  -- Instance of Region class for the given Region name

    Raises:
        SDKException:
            - If the Region name argument is not a string

            - If No Region found in commcell with the given region name
    &#34;&#34;&#34;
    if not isinstance(name, str):
        raise SDKException(&#39;Region&#39;, &#39;102&#39;, &#34;Invalid input received&#34;)

    if not self.has_region(name):
        raise SDKException(&#39;Region&#39;, &#39;102&#39;, f&#34;Region {name.lower()} not present in commcell&#34;)

    return Region(self._commcell_object, name, self._regions[name.lower()])</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Regions.get_region"><code class="name flex">
<span>def <span class="ident">get_region</span></span>(<span>self, entity_type, entity_id, entity_region_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the Region associated to an Entity</p>
<h2 id="args">Args</h2>
<p>entity_type
(str)
:
Type of the entity
(eg:
COMMCELL,
COMPANY,</p>
<pre><code>                                    CLIENT,
                                    CLIENT_GROUP,
                                    MEDIAAGENT,
                                    STORAGE_POOL, etc
                            )
</code></pre>
<p>entity_id
(int)
:
unique id of the entity</p>
<p>entity_region_type
(str)
:
Type of the region
(WORKLOAD or BACKUP)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L208-L247" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_region(self, entity_type, entity_id, entity_region_type):
    &#34;&#34;&#34;
    Gets the Region associated to an Entity
    Args:
        entity_type         (str)   :   Type of the entity
                                        (eg:    COMMCELL,
                                                COMPANY,


                                                CLIENT,
                                                CLIENT_GROUP,
                                                MEDIAAGENT,
                                                STORAGE_POOL, etc
                                        )
        entity_id           (int)   :   unique id of the entity

        entity_region_type  (str)   :   Type of the region
                                        (WORKLOAD or BACKUP)
    &#34;&#34;&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;GET&#39;, self._GET_REGION % (entity_type, entity_id, entity_region_type)
    )

    if flag:
        if response.json():
            try:
                if response.json()[&#39;errorCode&#39;]:
                    error_string = response.json()[&#39;errorMessage&#39;]
                    raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;{0}&#39;.format(error_string))

            except:
                if response.json().get(&#39;regionId&#39;):
                    return response.json().get(&#39;regionId&#39;)
                return 0

        else:
            return None
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Regions.has_region"><code class="name flex">
<span>def <span class="ident">has_region</span></span>(<span>self, name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if the given Region exists in the Commcell.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the Region</p>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean output whether the Region exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the Region name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L114-L131" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_region(self, name):
    &#34;&#34;&#34;Checks if the given Region exists in the Commcell.

        Args:
            name    (str)   --  name of the Region

        Returns:
            bool    -   boolean output whether the Region exists in the commcell or not

        Raises:
            SDKException:
                if type of the Region name argument is not string

    &#34;&#34;&#34;
    if not isinstance(name, str):
        raise SDKException(&#39;Region&#39;, &#39;103&#39;)

    return self._regions and (name.lower() in self._regions)</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Regions.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the list of Regions associated to commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L110-L112" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the list of Regions associated to commcell&#34;&#34;&#34;
    self._regions = self._get_regions()</code></pre>
</details>
</dd>
<dt id="cvpysdk.regions.Regions.set_region"><code class="name flex">
<span>def <span class="ident">set_region</span></span>(<span>self, entity_type, entity_id, entity_region_type, region_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Associate a region to an entity</p>
<h2 id="args">Args</h2>
<p>entity_type
(str)
:
Type of the entity
(eg:
COMMCELL,
COMPANY,
CLIENT,
CLIENT_GROUP,
MEDIAAGENT,
STORAGE_POOL, etc
)
entity_id
(int/str):
unique id of the entity</p>
<p>entity_region_type
(str)
:
Type of the region
(WORKLOAD or BACKUP)</p>
<p>region_id
(int)
:
ID of the region from app_regions</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/regions.py#L156-L206" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_region(self, entity_type, entity_id, entity_region_type, region_id):
    &#34;&#34;&#34;
    Associate a region to an entity
    Args:
        entity_type         (str)   :   Type of the entity
                                        (eg:    COMMCELL,
                                                COMPANY,
                                                CLIENT,
                                                CLIENT_GROUP,
                                                MEDIAAGENT,
                                                STORAGE_POOL, etc
                                        )
        entity_id           (int/str):   unique id of the entity

        entity_region_type  (str)   :   Type of the region
                                        (WORKLOAD or BACKUP)

        region_id           (int)   :   ID of the region from app_regions
    &#34;&#34;&#34;
    if isinstance(region_id,str):
        region_id = int(region_id)
    request = {
        &#34;entityRegionType&#34;: entity_region_type,
        &#34;region&#34;:
            {
                &#34;id&#34;: region_id
            }
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._EDIT_REGION % (entity_type, entity_id), request
    )

    if flag:
        if response.json():
            error_code = response.json()[&#39;errorCode&#39;]

            if error_code != 0:
                if error_code == 50000:
                    raise SDKException(&#39;Regions&#39;, &#39;101&#39;)
                elif error_code == 547:
                    raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;Invalid regionID provided in request&#39;)
                else:
                    error_string = response.json()[&#39;errorMessage&#39;]
                    raise SDKException(&#39;Regions&#39;, &#39;102&#39;, &#39;{0}&#39;.format(error_string))

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#regions">Regions:</a></li>
<li><a href="#region">Region:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.regions.Region" href="#cvpysdk.regions.Region">Region</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.regions.Region.associated_plans" href="#cvpysdk.regions.Region.associated_plans">associated_plans</a></code></li>
<li><code><a title="cvpysdk.regions.Region.associated_plans_count" href="#cvpysdk.regions.Region.associated_plans_count">associated_plans_count</a></code></li>
<li><code><a title="cvpysdk.regions.Region.associated_servers" href="#cvpysdk.regions.Region.associated_servers">associated_servers</a></code></li>
<li><code><a title="cvpysdk.regions.Region.associated_servers_count" href="#cvpysdk.regions.Region.associated_servers_count">associated_servers_count</a></code></li>
<li><code><a title="cvpysdk.regions.Region.locations" href="#cvpysdk.regions.Region.locations">locations</a></code></li>
<li><code><a title="cvpysdk.regions.Region.refresh" href="#cvpysdk.regions.Region.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.regions.Region.region_id" href="#cvpysdk.regions.Region.region_id">region_id</a></code></li>
<li><code><a title="cvpysdk.regions.Region.region_name" href="#cvpysdk.regions.Region.region_name">region_name</a></code></li>
<li><code><a title="cvpysdk.regions.Region.region_type" href="#cvpysdk.regions.Region.region_type">region_type</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.regions.Regions" href="#cvpysdk.regions.Regions">Regions</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.regions.Regions.add" href="#cvpysdk.regions.Regions.add">add</a></code></li>
<li><code><a title="cvpysdk.regions.Regions.all_regions" href="#cvpysdk.regions.Regions.all_regions">all_regions</a></code></li>
<li><code><a title="cvpysdk.regions.Regions.calculate_region" href="#cvpysdk.regions.Regions.calculate_region">calculate_region</a></code></li>
<li><code><a title="cvpysdk.regions.Regions.delete" href="#cvpysdk.regions.Regions.delete">delete</a></code></li>
<li><code><a title="cvpysdk.regions.Regions.get" href="#cvpysdk.regions.Regions.get">get</a></code></li>
<li><code><a title="cvpysdk.regions.Regions.get_region" href="#cvpysdk.regions.Regions.get_region">get_region</a></code></li>
<li><code><a title="cvpysdk.regions.Regions.has_region" href="#cvpysdk.regions.Regions.has_region">has_region</a></code></li>
<li><code><a title="cvpysdk.regions.Regions.refresh" href="#cvpysdk.regions.Regions.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.regions.Regions.set_region" href="#cvpysdk.regions.Regions.set_region">set_region</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>