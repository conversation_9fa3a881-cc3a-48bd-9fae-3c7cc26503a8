<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.drorchestration.replication_groups API documentation</title>
<meta name="description" content="Main file for performing replication group specific operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.drorchestration.replication_groups</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing replication group specific operations.</p>
<p>ReplicationGroups and ReplicationGroup are 2 classes defined in this file.</p>
<p>ReplicationGroups:
Class for representing all the replication groups associated
with a specific client</p>
<p>ReplicationGroup:
Class for a single replication group selected for a client,
and to perform operations on that replication group</p>
<h2 id="replicationgroups">Replicationgroups</h2>
<p>ReplicationGroupType
&ndash;
Enum to denote all possible types of replication groups
<strong>init</strong>(commcell_object)
&ndash;
Initialise object of ReplicationGroups class</p>
<p><strong>str</strong>()
&ndash;
Returns all the replication groups</p>
<p><strong>repr</strong>()
&ndash;
Returns the string for the instance of the
ReplicationGroups class</p>
<p>has_replication_group(
replication_group_name)
&ndash;
Checks if replication group exists with the given name</p>
<p>get(replication_group_name)
&ndash;
Returns the ReplicationGroup class object of the input
replication name</p>
<p>delete(replication_group_name)
&ndash;
Delete replication group with replication group name</p>
<p>refresh()
&ndash;
Refresh all replication groups created on the commcell</p>
<h4 id="internal-methods">internal methods</h4>
<p>_get_replication_groups()
&ndash;
REST API call to get all replication groups
in the commcell</p>
<h5 id="properties">properties</h5>
<p>replication_groups()
&ndash;
Returns all replication groups in the commcell</p>
<h2 id="replicationgroup">Replicationgroup</h2>
<p><strong>init</strong>(commcell_object,
replication_group_name)
&ndash; Initialise object of ReplicationGroup with the
specified replication group name</p>
<p><strong>repr</strong>()
&ndash; return the ReplicationGroup name</p>
<p>refresh()
&ndash; Refresh the object properties</p>
<h5 id="internal-methods_1">internal methods</h5>
<p>_get_replication_group_dict()
&ndash; Method to get replication group dictionary</p>
<p>_get_replication_group_properties()
&ndash; Get the replication group properties</p>
<h5 id="properties_1">properties</h5>
<p>group_name()
&ndash; Returns the replication group name
group_id()
&ndash; Returns the replication group ID
task_id()
&ndash; Returns the ID of the task associated to replication group
replication_type()
&ndash; Returns the enum constant of the ReplicationGroupType
zeal_group()
&ndash; Returns a boolean to denote whether group is Zeal
or backup-based</p>
<p>restore_options()
&ndash; Returns a hypervisor specific set of restore options</p>
<p>is_dvdf_enabled()
&ndash; Returns whether 'Deploy VM during failover' enabled
is_warm_sync_enabled()
&ndash; Returns whether 'Warm site recovery' is enabled</p>
<p>source_client()
&ndash; Returns a client object of the source hypervisor
destination_client()
&ndash; Returns a client object of the destination hypervisor</p>
<p>source_agent()
&ndash; Returns an agent object of the source client
destination_agent()
&ndash; Returns an agent object of the destination client</p>
<p>source_instance()
&ndash; Returns an instance object of the source agent
destination_instance()
&ndash; Returns an instance object of the destination agent</p>
<p>subclient()
&ndash; Returns the subclient object of the VM group associated
with the replication group</p>
<p>live_sync_pairs()
&ndash; Returns the list of source VM names that are already present in
replication monitor
vm_pairs()
&ndash; Returns a dictionary of source VM names
and LiveSyncVMPairs object mapping</p>
<p>is_enabled()
&ndash; Returns a boolean to tell whether replication group
is enabled or disabled
group_frequency()
&ndash; Returns the group frequency in minutes</p>
<p>copy_precedence_applicable
&ndash; Returns a boolean whether the copy precedence is applicable or
not</p>
<p>copy_for_replication()
&ndash; Returns the copy precedence ID used for replication</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L1-L645" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing replication group specific operations.

ReplicationGroups and ReplicationGroup are 2 classes defined in this file.

ReplicationGroups:     Class for representing all the replication groups associated
                        with a specific client

ReplicationGroup:      Class for a single replication group selected for a client,
                        and to perform operations on that replication group


ReplicationGroups:
    ReplicationGroupType                        --  Enum to denote all possible types of replication groups
    __init__(commcell_object)                   --  Initialise object of ReplicationGroups class

    __str__()                                   --  Returns all the replication groups

    __repr__()                                  --  Returns the string for the instance of the
                                                    ReplicationGroups class

    has_replication_group(
            replication_group_name)             --  Checks if replication group exists with the given name

    get(replication_group_name)                 --  Returns the ReplicationGroup class object of the input
                                                    replication name

    delete(replication_group_name)              --  Delete replication group with replication group name

    refresh()                                   --  Refresh all replication groups created on the commcell

    #### internal methods ###
    _get_replication_groups()                   --  REST API call to get all replication groups
                                                    in the commcell

    ##### properties ######
    replication_groups()                        --  Returns all replication groups in the commcell


ReplicationGroup:
    __init__(commcell_object,
            replication_group_name)                 -- Initialise object of ReplicationGroup with the
                                                        specified replication group name

    __repr__()                                      -- return the ReplicationGroup name

    refresh()                                       -- Refresh the object properties

    ##### internal methods #####
    _get_replication_group_dict()                   -- Method to get replication group dictionary

    _get_replication_group_properties()             -- Get the replication group properties

    ##### properties #####
    group_name()                                    -- Returns the replication group name
    group_id()                                      -- Returns the replication group ID
    task_id()                                       -- Returns the ID of the task associated to replication group
    replication_type()                              -- Returns the enum constant of the ReplicationGroupType
    zeal_group()                                    -- Returns a boolean to denote whether group is Zeal
                                                        or backup-based

    restore_options()                               -- Returns a hypervisor specific set of restore options

    is_dvdf_enabled()                               -- Returns whether &#39;Deploy VM during failover&#39; enabled
    is_warm_sync_enabled()                          -- Returns whether &#39;Warm site recovery&#39; is enabled

    source_client()                                 -- Returns a client object of the source hypervisor
    destination_client()                            -- Returns a client object of the destination hypervisor

    source_agent()                                  -- Returns an agent object of the source client
    destination_agent()                             -- Returns an agent object of the destination client

    source_instance()                               -- Returns an instance object of the source agent
    destination_instance()                          -- Returns an instance object of the destination agent

    subclient()                                     -- Returns the subclient object of the VM group associated
                                                        with the replication group

    live_sync_pairs()                               -- Returns the list of source VM names that are already present in
                                                        replication monitor
    vm_pairs()                                      -- Returns a dictionary of source VM names
                                                        and LiveSyncVMPairs object mapping

    is_enabled()                                    -- Returns a boolean to tell whether replication group
                                                        is enabled or disabled
    group_frequency()                               -- Returns the group frequency in minutes

    copy_precedence_applicable                      -- Returns a boolean whether the copy precedence is applicable or
                                                        not

    copy_for_replication()                          -- Returns the copy precedence ID used for replication

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from enum import Enum

from ..constants import AppIDAType, AppIDAName
from .blr_pairs import BLRPairs
from ..exception import SDKException


class ReplicationGroups:
    &#34;&#34;&#34;Class for getting all the replication groups in commcell.&#34;&#34;&#34;

    class ReplicationGroupType(Enum):
        &#34;&#34;&#34; Enum to map Replication Group Types to integers&#34;&#34;&#34;
        VSA_PERIODIC = 1
        VSA_CONTINUOUS = 2
        FILE_SYSTEM = 3
        ORACLE = 4
        SQL_SERVER = 5
        SAP_HANA = 6

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the Replication groups
            Args:
                commcell_object (Commcell)  --  instance of the Commcell class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services

        self._replication_groups = None

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all replication groups in a formatted output
            Returns:
                str - string of all the replication groups
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;S. No.&#39;, &#39;Replication Group Id&#39;, &#39;Replication Group&#39;)

        for index, replication_group in enumerate(self._replication_groups):
            sub_str = &#39;{:^5}\t{:20}\t{:20}\n&#39;.format(
                index + 1,
                self._replication_groups[replication_group],
                replication_group
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the ReplicationGroups class.&#34;&#34;&#34;
        return &#34;Replication Groups for Commserv: &#39;{0}&#39;&#34;.format(
            self._commcell_object.commserv_name)

    def has_replication_group(self, replication_group_name):
        &#34;&#34;&#34;Checks if replication group exists or not

            Args:
                replication_group_name (str)  --  name of the replication group

            Returns:
                bool - boolean output whether replication group exists or not

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(replication_group_name, str):
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;)

        return self.replication_groups and replication_group_name.lower() in self.replication_groups

    def get(self, replication_group_name):
        &#34;&#34;&#34;Returns a replication group object of the specified replication group name.

            Args:
                replication_group_name (str)  --  name of the replication group

            Returns:
                object - instance of the ReplicationGroup class for the given replication group name

            Raises:
                SDKException:
                    if proper inputs are not provided
                    If Replication group doesnt exists with given name
        &#34;&#34;&#34;
        if not isinstance(replication_group_name, str):
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;)
        replication_group_name = replication_group_name.lower()
        if self.has_replication_group(replication_group_name):
            return ReplicationGroup(
                self._commcell_object, replication_group_name)

        raise SDKException(
            &#39;ReplicationGroup&#39;,
            &#39;102&#39;,
            &#34;Replication group doesn&#39;t exist with name: {0}&#34;.format(replication_group_name))

    def delete(self, replication_group_name):
        &#34;&#34;&#34; Deletes the specified replication group name.

            Args:
                replication_group_name (str)  --  name of the replication group

            Returns:


            Raises:
                SDKException:
                    if proper inputs are not provided
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;

        if not isinstance(replication_group_name, str):
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;)

        replication_group_name = replication_group_name.lower()
        if self.has_replication_group(replication_group_name):

            replication_group_dict = self.replication_groups.get(
                replication_group_name.lower(), {})

            if replication_group_dict:
                if replication_group_dict.get(&#39;zealGroup&#39;):
                    payload = {
                        &#34;repGrpIds&#34;: [int(replication_group_dict.get(&#39;id&#39;))],
                        &#34;taskIds&#34;: [],
                    }
                else:
                    payload = {
                        &#34;repGrpIds&#34;: [],
                        &#34;taskIds&#34;: [replication_group_dict.get(&#39;id&#39;)],
                    }

                flag, response = self._commcell_object._cvpysdk_object.make_request(
                    method=&#39;POST&#39;,
                    url=self._services[&#39;DELETE_REPLICATION_GROUPS&#39;],
                    payload=payload
                )

                if flag:
                    if response.json() and &#39;deleteGroupsResponse&#39; in response.json():
                        if (response.json().get(&#39;deleteGroupsResponse&#39;, [{}])[0]
                                .get(&#39;response&#39;).get(&#39;errorMessage&#39;)):
                            error_message = (response.json().get(&#39;deleteGroupsResponse&#39;, {})
                                             .get(&#39;response&#39;, {}).get(&#39;errorMessage&#39;))
                            o_str = (&#39;Failed to delete replication group: {0} \nError: &#34;{1}&#34;&#39;
                                     .format(replication_group_name, error_message))

                            raise SDKException(&#39;ReplicationGroup&#39;, &#39;102&#39;, o_str)
                        self.refresh()

                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    response_string = self._commcell_object._update_response_(
                        response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;, &#39;Replication group information is empty&#39;)
        else:
            raise SDKException(
                &#39;ReplicationGroup&#39;, &#39;102&#39;, &#39;No replication group exists with name: &#34;{0}&#34;&#39;.format(
                    replication_group_name)
            )

    @property
    def replication_groups(self):
        &#34;&#34;&#34; return all replication groups
        Args:

        Returns: All the replication groups in the commcell

        Raises:
        &#34;&#34;&#34;
        return self._replication_groups

    def _get_replication_groups(self):
        &#34;&#34;&#34;REST API call for all the replication groups in the commcell.
            Args:

            Returns:
                dict - consists of all replication groups
                    {
                         &#34;replication_group_name1&#34;: {id: &#39;1&#39;, &#39;type&#39;: VSA_PERIODIC, &#39;zealGroup&#39;: true},
                         &#34;replication_group_name2&#34;: {id: &#39;2&#39;, &#39;type&#39;: VSA_CONTINUOUS, &#39;zealGroup&#39;: false}
                    }

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[&#39;REPLICATION_GROUPS&#39;])

        if flag:
            if response.json() and &#39;replicationGroups&#39; in response.json():

                replication_groups = {}

                for group_dict in response.json()[&#39;replicationGroups&#39;]:
                    group_type = (self.ReplicationGroupType(group_dict.get(&#39;type&#39;))
                                  if group_dict.get(&#39;type&#39;) else None)
                    if group_dict.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupId&#39;):
                        group_name = group_dict.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupName&#39;, &#39;&#39;)
                        group_id = str(group_dict.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupId&#39;, 0))
                        zeal_group = True
                    else:
                        subtask_dict = group_dict.get(&#39;taskDetail&#39;, {}).get(&#39;subTasks&#39;)[0]
                        group_name = subtask_dict.get(&#39;subTask&#39;, {}).get(&#39;subTaskName&#39;, &#39;&#39;)
                        group_id = str(group_dict.get(&#39;taskDetail&#39;, {}).get(&#39;task&#39;, {}).get(&#39;taskId&#39;, 0))
                        zeal_group = False
                    replication_groups[group_name.lower()] = {
                        &#39;id&#39;: group_id,
                        &#39;type&#39;: group_type,
                        &#39;zealGroup&#39;: zeal_group,
                    }
                return replication_groups
            raise SDKException(&#39;Response&#39;, 102)

        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34; Refresh the replication groups created in the commcell.
        Args:

        Returns:

        Raises:

        &#34;&#34;&#34;
        self._replication_groups = self._get_replication_groups()


class ReplicationGroup:
    &#34;&#34;&#34;Class for all Replication groups related SDK&#34;&#34;&#34;

    def __init__(self, commcell_object, replication_group_name):
        &#34;&#34;&#34;Initialise the ReplicationGroup object for the given group name
            Args:
                commcell_object (Commcell)      --  instance of the Commcell class
                replication_group_name (str)    --  name of the replication group
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._replication_group_properties = None

        self._group_name = replication_group_name.lower()
        self._group_dict = self._get_replication_group_dict()

        self._source_client = None
        self._destination_client = None
        self._source_agent = None
        self._destination_agent = None
        self._source_instance = None
        self._destination_instance = None

        self._subclient = None
        self._vm_pairs = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of the replication group&#34;&#34;&#34;
        representation_string = f&#39;ReplicationGroup class instance for &#39; \
                                f&#39;{&#34;Zeal&#34; if self.zeal_group else &#34;Backup based&#34;}&#39; \
                                f&#39; replication group: &#34;{self.group_name}&#34;&#39;
        return representation_string.format(self.group_name)

    def __str__(self):
        &#34;&#34;&#34;Strings showing all VM pairs of the replication group in a formatted output
            Returns:
                str - string of all VM pairs
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\n\n&#39;.format(&#39;Pair Id&#39;, &#39;Source VM&#39;, &#39;Destination VM&#39;)

        for source_vm in self.vm_pairs:
            sub_str = &#39;{:^5}\t{:20}\t{:20}\n&#39;.format(
                self.vm_pairs[source_vm].vm_pair_id,
                source_vm,
                self.vm_pairs[source_vm].destination_vm
            )
            representation_string += sub_str

        return representation_string.strip()

    def _get_replication_group_dict(self):
        &#34;&#34;&#34; Gets replication group dict from the ReplicationGroups class
            Returns: (list) The list of replication groups dictionary objects
        &#34;&#34;&#34;
        rgs_obj = ReplicationGroups(self._commcell_object)
        return rgs_obj.replication_groups.get(self._group_name)

    def _get_replication_group_properties(self):
        &#34;&#34;&#34; Gets replication group properties
            Args:

            Returns: Gets the replication group properties dict

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if self.zeal_group:
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;GET&#39;, self._services[&#39;REPLICATION_GROUP_DETAILS&#39;] % self.group_id)
        else:
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;GET&#39;, self._services[&#39;LIVE_SYNC_DETAILS&#39;] % self.group_id)

        if flag:
            if response.json().get(&#39;replicationInfo&#39;, {}).get(&#39;replicationTargets&#39;, {}).get(&#39;taskInfo&#39;):
                return response.json().get(&#39;replicationInfo&#39;, {}).get(&#39;replicationTargets&#39;, {}).get(&#39;taskInfo&#39;)[0]
            if response.json().get(&#39;taskInfo&#39;):
                return response.json().get(&#39;taskInfo&#39;)
            if self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_CONTINUOUS:
                return response.json().get(&#39;replicationGroupDetails&#39;, {}).get(&#39;taskDetail&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34; Refresh the replication group properties &#34;&#34;&#34;
        self._replication_group_properties = self._get_replication_group_properties()
        self._vm_pairs = None

    @property
    def group_name(self):
        &#34;&#34;&#34;Returns: (str) Returns the name of the replication group&#34;&#34;&#34;
        return self._group_name

    @property
    def group_id(self):
        &#34;&#34;&#34;Returns: (str) Returns the ID of the replication group (Zeal)/subtask(classic)&#34;&#34;&#34;
        return str(self._group_dict.get(&#39;id&#39;))

    @property
    def task_id(self):
        &#34;&#34;&#34;Returns: (str) Returns the ID of the task associated to the replication group&#34;&#34;&#34;
        return str(self._replication_group_properties.get(&#39;task&#39;, {}).get(&#39;taskId&#39;))

    @property
    def replication_type(self):
        &#34;&#34;&#34;
        Returns: (enum) Returns the type of the replication group.
        &#34;&#34;&#34;
        return self._group_dict.get(&#39;type&#39;)

    @property
    def zeal_group(self):
        &#34;&#34;&#34;Returns: (bool) True, if zeal replication group, false otherwise&#34;&#34;&#34;
        return self._group_dict.get(&#39;zealGroup&#39;, False)

    @property
    def restore_options(self):
        &#34;&#34;&#34;
        Returns: (dict) The dictionary of restore options of the replication group
            The dictionary structure depends on the vendor
        &#34;&#34;&#34;
        return (self._replication_group_properties.get(&#39;subTasks&#39;, [{}])[0]
                .get(&#39;options&#39;, {}).get(&#39;restoreOptions&#39;, {}))

    @property
    def is_dvdf_enabled(self):
        &#34;&#34;&#34;Returns: (bool) Whether deploy VM during failover is enabled or not&#34;&#34;&#34;
        return (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                .get(&#39;diskLevelVMRestoreOption&#39;, {}).get(&#39;deployVmWhenFailover&#39;, False))

    @property
    def is_warm_sync_enabled(self):
        &#34;&#34;&#34;Returns: (bool) Whether Warm sync is enabled or not&#34;&#34;&#34;
        return (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                .get(&#39;diskLevelVMRestoreOption&#39;, {}).get(&#39;createVmsDuringFailover&#39;, False))
    
    @property
    def is_intelli_snap_enabled(self):
        &#34;&#34;&#34;Returns: (bool) Whether Snapshot on source is utilised or not&#34;&#34;&#34;
        return self.subclient.is_intelli_snap_enabled

    @property
    def source_client(self):
        &#34;&#34;&#34;Returns:  the client object of the source hypervisor&#34;&#34;&#34;
        if not self._source_client:
            client_id = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;clientId&#39;)
            self._source_client = self._commcell_object.clients.get(int(client_id))
        return self._source_client

    @property
    def destination_client(self):
        &#34;&#34;&#34;Returns: (str) the client object for the destination hypervisor&#34;&#34;&#34;
        if not self._destination_client:
            client_id = (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                         .get(&#39;vCenterInstance&#39;, {}).get(&#39;clientId&#39;))
            self._destination_client = self._commcell_object.clients.get(int(client_id))
        return self._destination_client

    @property
    def source_agent(self):
        &#34;&#34;&#34;Returns: the agent object of the source hypervisor&#34;&#34;&#34;
        if not self._source_agent:
            agent_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;appName&#39;)
            if not agent_name:
                app_id = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;applicationId&#39;)
                agent_name = AppIDAName[AppIDAType(app_id).name].value
            self._source_agent = self.source_client.agents.get(agent_name)
        return self._source_agent

    @property
    def destination_agent(self):
        &#34;&#34;&#34;Returns: the agent object of the destination hypervisor&#34;&#34;&#34;
        if not self._destination_agent:
            agent_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;appName&#39;)
            if not agent_name:
                app_id = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;applicationId&#39;)
                agent_name = AppIDAName[AppIDAType(app_id).name].value
            self._destination_agent = self.destination_client.agents.get(agent_name)
        return self._destination_agent

    @property
    def source_instance(self):
        &#34;&#34;&#34;Returns: (str) The source hypervisor&#39;s instance name&#34;&#34;&#34;
        if not self._source_instance:
            instance_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;instanceName&#39;)
            self._source_instance = self.source_agent.instances.get(instance_name)
        return self._source_instance

    @property
    def destination_instance(self):
        &#34;&#34;&#34;Returns: (str) The destination hypervisor&#39;s instance name&#34;&#34;&#34;
        if not self._destination_instance:
            instance_name = (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                             .get(&#39;vCenterInstance&#39;, {}).get(&#39;instanceName&#39;))
            
            # TODO : Depends on DR Layer changes : Workaround used
            instance_name = &#39;Amazon Web Services&#39; if instance_name == &#39;Amazon&#39; else instance_name
            
            self._destination_instance = self.destination_agent.instances.get(instance_name)
        return self._destination_instance

    @property
    def subclient(self):
        &#34;&#34;&#34;Returns: the subclient object of the replication group&#34;&#34;&#34;
        if not self._subclient:
            backupset_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;backupsetName&#39;)
            backupset = self.source_instance.backupsets.get(backupset_name)
            subclient_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;subclientName&#39;)
            self._subclient = backupset.subclients.get(subclient_name)
        return self._subclient

    @property
    def live_sync_pairs(self):
        &#34;&#34;&#34;
        Returns: A list of all source VM names for which live sync pair exists for a periodic replication group
            eg: [&#34;vm1&#34;, &#34;vm2&#34;]
        &#34;&#34;&#34;
        _live_sync_pairs = []
        if self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_PERIODIC:
            live_sync_name = self.group_name.replace(&#39;_ReplicationPlan__ReplicationGroup&#39;, &#39;&#39;)
            live_sync = self.subclient.live_sync.get(live_sync_name)
            live_sync.refresh()
            _live_sync_pairs = list(live_sync.vm_pairs)
        elif self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_CONTINUOUS:
            blr_pairs = BLRPairs(self._commcell_object, self.group_name)
            blr_pairs.refresh()
            _live_sync_pairs = [blr_pair.get(&#39;sourceName&#39;) for blr_pair in blr_pairs.blr_pairs.values()]
        else:
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;, &#39;Implemented only for replication groups&#39;
                                                          &#39; of virtual server periodic&#39;)
        return _live_sync_pairs

    @property
    def vm_pairs(self):
        &#34;&#34;&#34;Returns: A dictionary of livesyncVM pairs/BLR pairs object
            eg: {&#34;src_vm1&#34;: LiveSyncVMPair, &#34;src_vm2&#34;: LiveSyncVMPair}
        &#34;&#34;&#34;
        if not self._vm_pairs:
            if self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_PERIODIC:
                live_sync_name = self.group_name.replace(&#39;_ReplicationPlan__ReplicationGroup&#39;, &#39;&#39;)
                live_sync = self.subclient.live_sync.get(live_sync_name)
                self._vm_pairs = {source_vm: live_sync.get(source_vm)
                                  for source_vm in live_sync.vm_pairs}
            elif self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_CONTINUOUS:
                blr_pairs = BLRPairs(self._commcell_object, self.group_name)
                self._vm_pairs = {pair_dict.get(&#39;sourceName&#39;):
                                  blr_pairs.get(pair_dict.get(&#39;sourceName&#39;), pair_dict.get(&#39;destinationName&#39;))
                                  for pair_dict in blr_pairs.blr_pairs.values()}
            else:
                raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;, &#39;Implemented only for replication groups&#39;
                                                              &#39; of virtual server periodic&#39;)
        return self._vm_pairs

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Returns: (bool) Returns True if state of the replication group &#39;Enabled&#39; else False&#34;&#34;&#34;
        return not self._replication_group_properties.get(&#39;task&#39;, {}).get(&#39;taskFlags&#39;, {}).get(&#39;disabled&#39;, False)

    @property
    def group_frequency(self):
        &#34;&#34;&#34;Returns: (int) The frequency in minutes at which the group is synced (only applicable for Zeal groups)&#34;&#34;&#34;
        return self._replication_group_properties.get(&#39;pattern&#39;, {}).get(&#39;freq_interval&#39;, 0)

    @property
    def copy_precedence_applicable(self):
        &#34;&#34;&#34;Returns: (bool) Whether the copy precedence is applicable or not&#34;&#34;&#34;
        return (self.restore_options.get(&#39;browseOption&#39;, {}).get(&#39;mediaOption&#39;, {})
                .get(&#39;copyPrecedence&#39;, {}).get(&#39;copyPrecedenceApplicable&#39;, False))

    @property
    def copy_for_replication(self):
        &#34;&#34;&#34;Returns: (int) The ID of the copy used for the replication&#34;&#34;&#34;
        # Copy for replication is only applicable is group has copy precedence enabled
        return (self.restore_options.get(&#39;browseOption&#39;, {}).get(&#39;mediaOption&#39;, {})
                .get(&#39;copyPrecedence&#39;, {}).get(&#39;copyPrecedence&#39;))

    @property
    def recovery_target(self):
        &#34;&#34;&#34;Returns: (str) The recovery target used for the replication&#34;&#34;&#34;
        return (self.restore_options.get(&#39;virtualServerRstOption&#39;, {}).get(&#39;allocationPolicy&#39;, {})
                .get(&#39;vmAllocPolicyName&#39;))

    @property
    def intelli_snap_engine(self):
        &#34;&#34;&#34;Returns: (str) Intelli Snap Engine Name&#34;&#34;&#34;
        snap_engine_name = self.subclient.snapshot_engine_name if self.is_intelli_snap_enabled else &#39;&#39;
        return snap_engine_name</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup"><code class="flex name class">
<span>class <span class="ident">ReplicationGroup</span></span>
<span>(</span><span>commcell_object, replication_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for all Replication groups related SDK</p>
<p>Initialise the ReplicationGroup object for the given group name</p>
<h2 id="args">Args</h2>
<p>commcell_object (Commcell)
&ndash;
instance of the Commcell class
replication_group_name (str)
&ndash;
name of the replication group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L352-L645" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ReplicationGroup:
    &#34;&#34;&#34;Class for all Replication groups related SDK&#34;&#34;&#34;

    def __init__(self, commcell_object, replication_group_name):
        &#34;&#34;&#34;Initialise the ReplicationGroup object for the given group name
            Args:
                commcell_object (Commcell)      --  instance of the Commcell class
                replication_group_name (str)    --  name of the replication group
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._replication_group_properties = None

        self._group_name = replication_group_name.lower()
        self._group_dict = self._get_replication_group_dict()

        self._source_client = None
        self._destination_client = None
        self._source_agent = None
        self._destination_agent = None
        self._source_instance = None
        self._destination_instance = None

        self._subclient = None
        self._vm_pairs = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of the replication group&#34;&#34;&#34;
        representation_string = f&#39;ReplicationGroup class instance for &#39; \
                                f&#39;{&#34;Zeal&#34; if self.zeal_group else &#34;Backup based&#34;}&#39; \
                                f&#39; replication group: &#34;{self.group_name}&#34;&#39;
        return representation_string.format(self.group_name)

    def __str__(self):
        &#34;&#34;&#34;Strings showing all VM pairs of the replication group in a formatted output
            Returns:
                str - string of all VM pairs
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\n\n&#39;.format(&#39;Pair Id&#39;, &#39;Source VM&#39;, &#39;Destination VM&#39;)

        for source_vm in self.vm_pairs:
            sub_str = &#39;{:^5}\t{:20}\t{:20}\n&#39;.format(
                self.vm_pairs[source_vm].vm_pair_id,
                source_vm,
                self.vm_pairs[source_vm].destination_vm
            )
            representation_string += sub_str

        return representation_string.strip()

    def _get_replication_group_dict(self):
        &#34;&#34;&#34; Gets replication group dict from the ReplicationGroups class
            Returns: (list) The list of replication groups dictionary objects
        &#34;&#34;&#34;
        rgs_obj = ReplicationGroups(self._commcell_object)
        return rgs_obj.replication_groups.get(self._group_name)

    def _get_replication_group_properties(self):
        &#34;&#34;&#34; Gets replication group properties
            Args:

            Returns: Gets the replication group properties dict

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if self.zeal_group:
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;GET&#39;, self._services[&#39;REPLICATION_GROUP_DETAILS&#39;] % self.group_id)
        else:
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;GET&#39;, self._services[&#39;LIVE_SYNC_DETAILS&#39;] % self.group_id)

        if flag:
            if response.json().get(&#39;replicationInfo&#39;, {}).get(&#39;replicationTargets&#39;, {}).get(&#39;taskInfo&#39;):
                return response.json().get(&#39;replicationInfo&#39;, {}).get(&#39;replicationTargets&#39;, {}).get(&#39;taskInfo&#39;)[0]
            if response.json().get(&#39;taskInfo&#39;):
                return response.json().get(&#39;taskInfo&#39;)
            if self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_CONTINUOUS:
                return response.json().get(&#39;replicationGroupDetails&#39;, {}).get(&#39;taskDetail&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34; Refresh the replication group properties &#34;&#34;&#34;
        self._replication_group_properties = self._get_replication_group_properties()
        self._vm_pairs = None

    @property
    def group_name(self):
        &#34;&#34;&#34;Returns: (str) Returns the name of the replication group&#34;&#34;&#34;
        return self._group_name

    @property
    def group_id(self):
        &#34;&#34;&#34;Returns: (str) Returns the ID of the replication group (Zeal)/subtask(classic)&#34;&#34;&#34;
        return str(self._group_dict.get(&#39;id&#39;))

    @property
    def task_id(self):
        &#34;&#34;&#34;Returns: (str) Returns the ID of the task associated to the replication group&#34;&#34;&#34;
        return str(self._replication_group_properties.get(&#39;task&#39;, {}).get(&#39;taskId&#39;))

    @property
    def replication_type(self):
        &#34;&#34;&#34;
        Returns: (enum) Returns the type of the replication group.
        &#34;&#34;&#34;
        return self._group_dict.get(&#39;type&#39;)

    @property
    def zeal_group(self):
        &#34;&#34;&#34;Returns: (bool) True, if zeal replication group, false otherwise&#34;&#34;&#34;
        return self._group_dict.get(&#39;zealGroup&#39;, False)

    @property
    def restore_options(self):
        &#34;&#34;&#34;
        Returns: (dict) The dictionary of restore options of the replication group
            The dictionary structure depends on the vendor
        &#34;&#34;&#34;
        return (self._replication_group_properties.get(&#39;subTasks&#39;, [{}])[0]
                .get(&#39;options&#39;, {}).get(&#39;restoreOptions&#39;, {}))

    @property
    def is_dvdf_enabled(self):
        &#34;&#34;&#34;Returns: (bool) Whether deploy VM during failover is enabled or not&#34;&#34;&#34;
        return (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                .get(&#39;diskLevelVMRestoreOption&#39;, {}).get(&#39;deployVmWhenFailover&#39;, False))

    @property
    def is_warm_sync_enabled(self):
        &#34;&#34;&#34;Returns: (bool) Whether Warm sync is enabled or not&#34;&#34;&#34;
        return (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                .get(&#39;diskLevelVMRestoreOption&#39;, {}).get(&#39;createVmsDuringFailover&#39;, False))
    
    @property
    def is_intelli_snap_enabled(self):
        &#34;&#34;&#34;Returns: (bool) Whether Snapshot on source is utilised or not&#34;&#34;&#34;
        return self.subclient.is_intelli_snap_enabled

    @property
    def source_client(self):
        &#34;&#34;&#34;Returns:  the client object of the source hypervisor&#34;&#34;&#34;
        if not self._source_client:
            client_id = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;clientId&#39;)
            self._source_client = self._commcell_object.clients.get(int(client_id))
        return self._source_client

    @property
    def destination_client(self):
        &#34;&#34;&#34;Returns: (str) the client object for the destination hypervisor&#34;&#34;&#34;
        if not self._destination_client:
            client_id = (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                         .get(&#39;vCenterInstance&#39;, {}).get(&#39;clientId&#39;))
            self._destination_client = self._commcell_object.clients.get(int(client_id))
        return self._destination_client

    @property
    def source_agent(self):
        &#34;&#34;&#34;Returns: the agent object of the source hypervisor&#34;&#34;&#34;
        if not self._source_agent:
            agent_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;appName&#39;)
            if not agent_name:
                app_id = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;applicationId&#39;)
                agent_name = AppIDAName[AppIDAType(app_id).name].value
            self._source_agent = self.source_client.agents.get(agent_name)
        return self._source_agent

    @property
    def destination_agent(self):
        &#34;&#34;&#34;Returns: the agent object of the destination hypervisor&#34;&#34;&#34;
        if not self._destination_agent:
            agent_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;appName&#39;)
            if not agent_name:
                app_id = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;applicationId&#39;)
                agent_name = AppIDAName[AppIDAType(app_id).name].value
            self._destination_agent = self.destination_client.agents.get(agent_name)
        return self._destination_agent

    @property
    def source_instance(self):
        &#34;&#34;&#34;Returns: (str) The source hypervisor&#39;s instance name&#34;&#34;&#34;
        if not self._source_instance:
            instance_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;instanceName&#39;)
            self._source_instance = self.source_agent.instances.get(instance_name)
        return self._source_instance

    @property
    def destination_instance(self):
        &#34;&#34;&#34;Returns: (str) The destination hypervisor&#39;s instance name&#34;&#34;&#34;
        if not self._destination_instance:
            instance_name = (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                             .get(&#39;vCenterInstance&#39;, {}).get(&#39;instanceName&#39;))
            
            # TODO : Depends on DR Layer changes : Workaround used
            instance_name = &#39;Amazon Web Services&#39; if instance_name == &#39;Amazon&#39; else instance_name
            
            self._destination_instance = self.destination_agent.instances.get(instance_name)
        return self._destination_instance

    @property
    def subclient(self):
        &#34;&#34;&#34;Returns: the subclient object of the replication group&#34;&#34;&#34;
        if not self._subclient:
            backupset_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;backupsetName&#39;)
            backupset = self.source_instance.backupsets.get(backupset_name)
            subclient_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;subclientName&#39;)
            self._subclient = backupset.subclients.get(subclient_name)
        return self._subclient

    @property
    def live_sync_pairs(self):
        &#34;&#34;&#34;
        Returns: A list of all source VM names for which live sync pair exists for a periodic replication group
            eg: [&#34;vm1&#34;, &#34;vm2&#34;]
        &#34;&#34;&#34;
        _live_sync_pairs = []
        if self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_PERIODIC:
            live_sync_name = self.group_name.replace(&#39;_ReplicationPlan__ReplicationGroup&#39;, &#39;&#39;)
            live_sync = self.subclient.live_sync.get(live_sync_name)
            live_sync.refresh()
            _live_sync_pairs = list(live_sync.vm_pairs)
        elif self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_CONTINUOUS:
            blr_pairs = BLRPairs(self._commcell_object, self.group_name)
            blr_pairs.refresh()
            _live_sync_pairs = [blr_pair.get(&#39;sourceName&#39;) for blr_pair in blr_pairs.blr_pairs.values()]
        else:
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;, &#39;Implemented only for replication groups&#39;
                                                          &#39; of virtual server periodic&#39;)
        return _live_sync_pairs

    @property
    def vm_pairs(self):
        &#34;&#34;&#34;Returns: A dictionary of livesyncVM pairs/BLR pairs object
            eg: {&#34;src_vm1&#34;: LiveSyncVMPair, &#34;src_vm2&#34;: LiveSyncVMPair}
        &#34;&#34;&#34;
        if not self._vm_pairs:
            if self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_PERIODIC:
                live_sync_name = self.group_name.replace(&#39;_ReplicationPlan__ReplicationGroup&#39;, &#39;&#39;)
                live_sync = self.subclient.live_sync.get(live_sync_name)
                self._vm_pairs = {source_vm: live_sync.get(source_vm)
                                  for source_vm in live_sync.vm_pairs}
            elif self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_CONTINUOUS:
                blr_pairs = BLRPairs(self._commcell_object, self.group_name)
                self._vm_pairs = {pair_dict.get(&#39;sourceName&#39;):
                                  blr_pairs.get(pair_dict.get(&#39;sourceName&#39;), pair_dict.get(&#39;destinationName&#39;))
                                  for pair_dict in blr_pairs.blr_pairs.values()}
            else:
                raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;, &#39;Implemented only for replication groups&#39;
                                                              &#39; of virtual server periodic&#39;)
        return self._vm_pairs

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Returns: (bool) Returns True if state of the replication group &#39;Enabled&#39; else False&#34;&#34;&#34;
        return not self._replication_group_properties.get(&#39;task&#39;, {}).get(&#39;taskFlags&#39;, {}).get(&#39;disabled&#39;, False)

    @property
    def group_frequency(self):
        &#34;&#34;&#34;Returns: (int) The frequency in minutes at which the group is synced (only applicable for Zeal groups)&#34;&#34;&#34;
        return self._replication_group_properties.get(&#39;pattern&#39;, {}).get(&#39;freq_interval&#39;, 0)

    @property
    def copy_precedence_applicable(self):
        &#34;&#34;&#34;Returns: (bool) Whether the copy precedence is applicable or not&#34;&#34;&#34;
        return (self.restore_options.get(&#39;browseOption&#39;, {}).get(&#39;mediaOption&#39;, {})
                .get(&#39;copyPrecedence&#39;, {}).get(&#39;copyPrecedenceApplicable&#39;, False))

    @property
    def copy_for_replication(self):
        &#34;&#34;&#34;Returns: (int) The ID of the copy used for the replication&#34;&#34;&#34;
        # Copy for replication is only applicable is group has copy precedence enabled
        return (self.restore_options.get(&#39;browseOption&#39;, {}).get(&#39;mediaOption&#39;, {})
                .get(&#39;copyPrecedence&#39;, {}).get(&#39;copyPrecedence&#39;))

    @property
    def recovery_target(self):
        &#34;&#34;&#34;Returns: (str) The recovery target used for the replication&#34;&#34;&#34;
        return (self.restore_options.get(&#39;virtualServerRstOption&#39;, {}).get(&#39;allocationPolicy&#39;, {})
                .get(&#39;vmAllocPolicyName&#39;))

    @property
    def intelli_snap_engine(self):
        &#34;&#34;&#34;Returns: (str) Intelli Snap Engine Name&#34;&#34;&#34;
        snap_engine_name = self.subclient.snapshot_engine_name if self.is_intelli_snap_enabled else &#39;&#39;
        return snap_engine_name</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.copy_for_replication"><code class="name">var <span class="ident">copy_for_replication</span></code></dt>
<dd>
<div class="desc"><p>Returns: (int) The ID of the copy used for the replication</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L628-L633" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def copy_for_replication(self):
    &#34;&#34;&#34;Returns: (int) The ID of the copy used for the replication&#34;&#34;&#34;
    # Copy for replication is only applicable is group has copy precedence enabled
    return (self.restore_options.get(&#39;browseOption&#39;, {}).get(&#39;mediaOption&#39;, {})
            .get(&#39;copyPrecedence&#39;, {}).get(&#39;copyPrecedence&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.copy_precedence_applicable"><code class="name">var <span class="ident">copy_precedence_applicable</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) Whether the copy precedence is applicable or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L622-L626" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def copy_precedence_applicable(self):
    &#34;&#34;&#34;Returns: (bool) Whether the copy precedence is applicable or not&#34;&#34;&#34;
    return (self.restore_options.get(&#39;browseOption&#39;, {}).get(&#39;mediaOption&#39;, {})
            .get(&#39;copyPrecedence&#39;, {}).get(&#39;copyPrecedenceApplicable&#39;, False))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.destination_agent"><code class="name">var <span class="ident">destination_agent</span></code></dt>
<dd>
<div class="desc"><p>Returns: the agent object of the destination hypervisor</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L528-L537" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_agent(self):
    &#34;&#34;&#34;Returns: the agent object of the destination hypervisor&#34;&#34;&#34;
    if not self._destination_agent:
        agent_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;appName&#39;)
        if not agent_name:
            app_id = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;applicationId&#39;)
            agent_name = AppIDAName[AppIDAType(app_id).name].value
        self._destination_agent = self.destination_client.agents.get(agent_name)
    return self._destination_agent</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.destination_client"><code class="name">var <span class="ident">destination_client</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the client object for the destination hypervisor</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L508-L515" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_client(self):
    &#34;&#34;&#34;Returns: (str) the client object for the destination hypervisor&#34;&#34;&#34;
    if not self._destination_client:
        client_id = (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                     .get(&#39;vCenterInstance&#39;, {}).get(&#39;clientId&#39;))
        self._destination_client = self._commcell_object.clients.get(int(client_id))
    return self._destination_client</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.destination_instance"><code class="name">var <span class="ident">destination_instance</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) The destination hypervisor's instance name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L547-L558" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_instance(self):
    &#34;&#34;&#34;Returns: (str) The destination hypervisor&#39;s instance name&#34;&#34;&#34;
    if not self._destination_instance:
        instance_name = (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
                         .get(&#39;vCenterInstance&#39;, {}).get(&#39;instanceName&#39;))
        
        # TODO : Depends on DR Layer changes : Workaround used
        instance_name = &#39;Amazon Web Services&#39; if instance_name == &#39;Amazon&#39; else instance_name
        
        self._destination_instance = self.destination_agent.instances.get(instance_name)
    return self._destination_instance</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.group_frequency"><code class="name">var <span class="ident">group_frequency</span></code></dt>
<dd>
<div class="desc"><p>Returns: (int) The frequency in minutes at which the group is synced (only applicable for Zeal groups)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L617-L620" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def group_frequency(self):
    &#34;&#34;&#34;Returns: (int) The frequency in minutes at which the group is synced (only applicable for Zeal groups)&#34;&#34;&#34;
    return self._replication_group_properties.get(&#39;pattern&#39;, {}).get(&#39;freq_interval&#39;, 0)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.group_id"><code class="name">var <span class="ident">group_id</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Returns the ID of the replication group (Zeal)/subtask(classic)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L452-L455" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def group_id(self):
    &#34;&#34;&#34;Returns: (str) Returns the ID of the replication group (Zeal)/subtask(classic)&#34;&#34;&#34;
    return str(self._group_dict.get(&#39;id&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.group_name"><code class="name">var <span class="ident">group_name</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Returns the name of the replication group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L447-L450" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def group_name(self):
    &#34;&#34;&#34;Returns: (str) Returns the name of the replication group&#34;&#34;&#34;
    return self._group_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.intelli_snap_engine"><code class="name">var <span class="ident">intelli_snap_engine</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Intelli Snap Engine Name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L641-L645" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def intelli_snap_engine(self):
    &#34;&#34;&#34;Returns: (str) Intelli Snap Engine Name&#34;&#34;&#34;
    snap_engine_name = self.subclient.snapshot_engine_name if self.is_intelli_snap_enabled else &#39;&#39;
    return snap_engine_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_dvdf_enabled"><code class="name">var <span class="ident">is_dvdf_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) Whether deploy VM during failover is enabled or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L483-L487" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_dvdf_enabled(self):
    &#34;&#34;&#34;Returns: (bool) Whether deploy VM during failover is enabled or not&#34;&#34;&#34;
    return (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
            .get(&#39;diskLevelVMRestoreOption&#39;, {}).get(&#39;deployVmWhenFailover&#39;, False))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_enabled"><code class="name">var <span class="ident">is_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) Returns True if state of the replication group 'Enabled' else False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L612-L615" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_enabled(self):
    &#34;&#34;&#34;Returns: (bool) Returns True if state of the replication group &#39;Enabled&#39; else False&#34;&#34;&#34;
    return not self._replication_group_properties.get(&#39;task&#39;, {}).get(&#39;taskFlags&#39;, {}).get(&#39;disabled&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_intelli_snap_enabled"><code class="name">var <span class="ident">is_intelli_snap_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) Whether Snapshot on source is utilised or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L495-L498" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_intelli_snap_enabled(self):
    &#34;&#34;&#34;Returns: (bool) Whether Snapshot on source is utilised or not&#34;&#34;&#34;
    return self.subclient.is_intelli_snap_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_warm_sync_enabled"><code class="name">var <span class="ident">is_warm_sync_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) Whether Warm sync is enabled or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L489-L493" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_warm_sync_enabled(self):
    &#34;&#34;&#34;Returns: (bool) Whether Warm sync is enabled or not&#34;&#34;&#34;
    return (self.restore_options.get(&#39;virtualServerRstOption&#39;, {})
            .get(&#39;diskLevelVMRestoreOption&#39;, {}).get(&#39;createVmsDuringFailover&#39;, False))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.live_sync_pairs"><code class="name">var <span class="ident">live_sync_pairs</span></code></dt>
<dd>
<div class="desc"><p>Returns: A list of all source VM names for which live sync pair exists for a periodic replication group
eg: ["vm1", "vm2"]</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L570-L589" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def live_sync_pairs(self):
    &#34;&#34;&#34;
    Returns: A list of all source VM names for which live sync pair exists for a periodic replication group
        eg: [&#34;vm1&#34;, &#34;vm2&#34;]
    &#34;&#34;&#34;
    _live_sync_pairs = []
    if self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_PERIODIC:
        live_sync_name = self.group_name.replace(&#39;_ReplicationPlan__ReplicationGroup&#39;, &#39;&#39;)
        live_sync = self.subclient.live_sync.get(live_sync_name)
        live_sync.refresh()
        _live_sync_pairs = list(live_sync.vm_pairs)
    elif self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_CONTINUOUS:
        blr_pairs = BLRPairs(self._commcell_object, self.group_name)
        blr_pairs.refresh()
        _live_sync_pairs = [blr_pair.get(&#39;sourceName&#39;) for blr_pair in blr_pairs.blr_pairs.values()]
    else:
        raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;, &#39;Implemented only for replication groups&#39;
                                                      &#39; of virtual server periodic&#39;)
    return _live_sync_pairs</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.recovery_target"><code class="name">var <span class="ident">recovery_target</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) The recovery target used for the replication</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L635-L639" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def recovery_target(self):
    &#34;&#34;&#34;Returns: (str) The recovery target used for the replication&#34;&#34;&#34;
    return (self.restore_options.get(&#39;virtualServerRstOption&#39;, {}).get(&#39;allocationPolicy&#39;, {})
            .get(&#39;vmAllocPolicyName&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.replication_type"><code class="name">var <span class="ident">replication_type</span></code></dt>
<dd>
<div class="desc"><p>Returns: (enum) Returns the type of the replication group.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L462-L467" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def replication_type(self):
    &#34;&#34;&#34;
    Returns: (enum) Returns the type of the replication group.
    &#34;&#34;&#34;
    return self._group_dict.get(&#39;type&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.restore_options"><code class="name">var <span class="ident">restore_options</span></code></dt>
<dd>
<div class="desc"><p>Returns: (dict) The dictionary of restore options of the replication group
The dictionary structure depends on the vendor</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L474-L481" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def restore_options(self):
    &#34;&#34;&#34;
    Returns: (dict) The dictionary of restore options of the replication group
        The dictionary structure depends on the vendor
    &#34;&#34;&#34;
    return (self._replication_group_properties.get(&#39;subTasks&#39;, [{}])[0]
            .get(&#39;options&#39;, {}).get(&#39;restoreOptions&#39;, {}))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.source_agent"><code class="name">var <span class="ident">source_agent</span></code></dt>
<dd>
<div class="desc"><p>Returns: the agent object of the source hypervisor</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L517-L526" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def source_agent(self):
    &#34;&#34;&#34;Returns: the agent object of the source hypervisor&#34;&#34;&#34;
    if not self._source_agent:
        agent_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;appName&#39;)
        if not agent_name:
            app_id = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;applicationId&#39;)
            agent_name = AppIDAName[AppIDAType(app_id).name].value
        self._source_agent = self.source_client.agents.get(agent_name)
    return self._source_agent</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.source_client"><code class="name">var <span class="ident">source_client</span></code></dt>
<dd>
<div class="desc"><p>Returns:
the client object of the source hypervisor</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L500-L506" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def source_client(self):
    &#34;&#34;&#34;Returns:  the client object of the source hypervisor&#34;&#34;&#34;
    if not self._source_client:
        client_id = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;clientId&#39;)
        self._source_client = self._commcell_object.clients.get(int(client_id))
    return self._source_client</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.source_instance"><code class="name">var <span class="ident">source_instance</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) The source hypervisor's instance name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L539-L545" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def source_instance(self):
    &#34;&#34;&#34;Returns: (str) The source hypervisor&#39;s instance name&#34;&#34;&#34;
    if not self._source_instance:
        instance_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;instanceName&#39;)
        self._source_instance = self.source_agent.instances.get(instance_name)
    return self._source_instance</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.subclient"><code class="name">var <span class="ident">subclient</span></code></dt>
<dd>
<div class="desc"><p>Returns: the subclient object of the replication group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L560-L568" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclient(self):
    &#34;&#34;&#34;Returns: the subclient object of the replication group&#34;&#34;&#34;
    if not self._subclient:
        backupset_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;backupsetName&#39;)
        backupset = self.source_instance.backupsets.get(backupset_name)
        subclient_name = self._replication_group_properties.get(&#39;associations&#39;, [{}])[0].get(&#39;subclientName&#39;)
        self._subclient = backupset.subclients.get(subclient_name)
    return self._subclient</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.task_id"><code class="name">var <span class="ident">task_id</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Returns the ID of the task associated to the replication group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L457-L460" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def task_id(self):
    &#34;&#34;&#34;Returns: (str) Returns the ID of the task associated to the replication group&#34;&#34;&#34;
    return str(self._replication_group_properties.get(&#39;task&#39;, {}).get(&#39;taskId&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.vm_pairs"><code class="name">var <span class="ident">vm_pairs</span></code></dt>
<dd>
<div class="desc"><p>Returns: A dictionary of livesyncVM pairs/BLR pairs object
eg: {"src_vm1": LiveSyncVMPair, "src_vm2": LiveSyncVMPair}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L591-L610" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_pairs(self):
    &#34;&#34;&#34;Returns: A dictionary of livesyncVM pairs/BLR pairs object
        eg: {&#34;src_vm1&#34;: LiveSyncVMPair, &#34;src_vm2&#34;: LiveSyncVMPair}
    &#34;&#34;&#34;
    if not self._vm_pairs:
        if self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_PERIODIC:
            live_sync_name = self.group_name.replace(&#39;_ReplicationPlan__ReplicationGroup&#39;, &#39;&#39;)
            live_sync = self.subclient.live_sync.get(live_sync_name)
            self._vm_pairs = {source_vm: live_sync.get(source_vm)
                              for source_vm in live_sync.vm_pairs}
        elif self.replication_type == ReplicationGroups.ReplicationGroupType.VSA_CONTINUOUS:
            blr_pairs = BLRPairs(self._commcell_object, self.group_name)
            self._vm_pairs = {pair_dict.get(&#39;sourceName&#39;):
                              blr_pairs.get(pair_dict.get(&#39;sourceName&#39;), pair_dict.get(&#39;destinationName&#39;))
                              for pair_dict in blr_pairs.blr_pairs.values()}
        else:
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;, &#39;Implemented only for replication groups&#39;
                                                          &#39; of virtual server periodic&#39;)
    return self._vm_pairs</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.zeal_group"><code class="name">var <span class="ident">zeal_group</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) True, if zeal replication group, false otherwise</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L469-L472" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def zeal_group(self):
    &#34;&#34;&#34;Returns: (bool) True, if zeal replication group, false otherwise&#34;&#34;&#34;
    return self._group_dict.get(&#39;zealGroup&#39;, False)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroup.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the replication group properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L442-L445" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34; Refresh the replication group properties &#34;&#34;&#34;
    self._replication_group_properties = self._get_replication_group_properties()
    self._vm_pairs = None</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroups"><code class="flex name class">
<span>class <span class="ident">ReplicationGroups</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the replication groups in commcell.</p>
<p>Initialize object of the Replication groups</p>
<h2 id="args">Args</h2>
<p>commcell_object (Commcell)
&ndash;
instance of the Commcell class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L122-L349" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ReplicationGroups:
    &#34;&#34;&#34;Class for getting all the replication groups in commcell.&#34;&#34;&#34;

    class ReplicationGroupType(Enum):
        &#34;&#34;&#34; Enum to map Replication Group Types to integers&#34;&#34;&#34;
        VSA_PERIODIC = 1
        VSA_CONTINUOUS = 2
        FILE_SYSTEM = 3
        ORACLE = 4
        SQL_SERVER = 5
        SAP_HANA = 6

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the Replication groups
            Args:
                commcell_object (Commcell)  --  instance of the Commcell class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services

        self._replication_groups = None

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all replication groups in a formatted output
            Returns:
                str - string of all the replication groups
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;S. No.&#39;, &#39;Replication Group Id&#39;, &#39;Replication Group&#39;)

        for index, replication_group in enumerate(self._replication_groups):
            sub_str = &#39;{:^5}\t{:20}\t{:20}\n&#39;.format(
                index + 1,
                self._replication_groups[replication_group],
                replication_group
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the ReplicationGroups class.&#34;&#34;&#34;
        return &#34;Replication Groups for Commserv: &#39;{0}&#39;&#34;.format(
            self._commcell_object.commserv_name)

    def has_replication_group(self, replication_group_name):
        &#34;&#34;&#34;Checks if replication group exists or not

            Args:
                replication_group_name (str)  --  name of the replication group

            Returns:
                bool - boolean output whether replication group exists or not

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(replication_group_name, str):
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;)

        return self.replication_groups and replication_group_name.lower() in self.replication_groups

    def get(self, replication_group_name):
        &#34;&#34;&#34;Returns a replication group object of the specified replication group name.

            Args:
                replication_group_name (str)  --  name of the replication group

            Returns:
                object - instance of the ReplicationGroup class for the given replication group name

            Raises:
                SDKException:
                    if proper inputs are not provided
                    If Replication group doesnt exists with given name
        &#34;&#34;&#34;
        if not isinstance(replication_group_name, str):
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;)
        replication_group_name = replication_group_name.lower()
        if self.has_replication_group(replication_group_name):
            return ReplicationGroup(
                self._commcell_object, replication_group_name)

        raise SDKException(
            &#39;ReplicationGroup&#39;,
            &#39;102&#39;,
            &#34;Replication group doesn&#39;t exist with name: {0}&#34;.format(replication_group_name))

    def delete(self, replication_group_name):
        &#34;&#34;&#34; Deletes the specified replication group name.

            Args:
                replication_group_name (str)  --  name of the replication group

            Returns:


            Raises:
                SDKException:
                    if proper inputs are not provided
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;

        if not isinstance(replication_group_name, str):
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;)

        replication_group_name = replication_group_name.lower()
        if self.has_replication_group(replication_group_name):

            replication_group_dict = self.replication_groups.get(
                replication_group_name.lower(), {})

            if replication_group_dict:
                if replication_group_dict.get(&#39;zealGroup&#39;):
                    payload = {
                        &#34;repGrpIds&#34;: [int(replication_group_dict.get(&#39;id&#39;))],
                        &#34;taskIds&#34;: [],
                    }
                else:
                    payload = {
                        &#34;repGrpIds&#34;: [],
                        &#34;taskIds&#34;: [replication_group_dict.get(&#39;id&#39;)],
                    }

                flag, response = self._commcell_object._cvpysdk_object.make_request(
                    method=&#39;POST&#39;,
                    url=self._services[&#39;DELETE_REPLICATION_GROUPS&#39;],
                    payload=payload
                )

                if flag:
                    if response.json() and &#39;deleteGroupsResponse&#39; in response.json():
                        if (response.json().get(&#39;deleteGroupsResponse&#39;, [{}])[0]
                                .get(&#39;response&#39;).get(&#39;errorMessage&#39;)):
                            error_message = (response.json().get(&#39;deleteGroupsResponse&#39;, {})
                                             .get(&#39;response&#39;, {}).get(&#39;errorMessage&#39;))
                            o_str = (&#39;Failed to delete replication group: {0} \nError: &#34;{1}&#34;&#39;
                                     .format(replication_group_name, error_message))

                            raise SDKException(&#39;ReplicationGroup&#39;, &#39;102&#39;, o_str)
                        self.refresh()

                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    response_string = self._commcell_object._update_response_(
                        response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;, &#39;Replication group information is empty&#39;)
        else:
            raise SDKException(
                &#39;ReplicationGroup&#39;, &#39;102&#39;, &#39;No replication group exists with name: &#34;{0}&#34;&#39;.format(
                    replication_group_name)
            )

    @property
    def replication_groups(self):
        &#34;&#34;&#34; return all replication groups
        Args:

        Returns: All the replication groups in the commcell

        Raises:
        &#34;&#34;&#34;
        return self._replication_groups

    def _get_replication_groups(self):
        &#34;&#34;&#34;REST API call for all the replication groups in the commcell.
            Args:

            Returns:
                dict - consists of all replication groups
                    {
                         &#34;replication_group_name1&#34;: {id: &#39;1&#39;, &#39;type&#39;: VSA_PERIODIC, &#39;zealGroup&#39;: true},
                         &#34;replication_group_name2&#34;: {id: &#39;2&#39;, &#39;type&#39;: VSA_CONTINUOUS, &#39;zealGroup&#39;: false}
                    }

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[&#39;REPLICATION_GROUPS&#39;])

        if flag:
            if response.json() and &#39;replicationGroups&#39; in response.json():

                replication_groups = {}

                for group_dict in response.json()[&#39;replicationGroups&#39;]:
                    group_type = (self.ReplicationGroupType(group_dict.get(&#39;type&#39;))
                                  if group_dict.get(&#39;type&#39;) else None)
                    if group_dict.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupId&#39;):
                        group_name = group_dict.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupName&#39;, &#39;&#39;)
                        group_id = str(group_dict.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupId&#39;, 0))
                        zeal_group = True
                    else:
                        subtask_dict = group_dict.get(&#39;taskDetail&#39;, {}).get(&#39;subTasks&#39;)[0]
                        group_name = subtask_dict.get(&#39;subTask&#39;, {}).get(&#39;subTaskName&#39;, &#39;&#39;)
                        group_id = str(group_dict.get(&#39;taskDetail&#39;, {}).get(&#39;task&#39;, {}).get(&#39;taskId&#39;, 0))
                        zeal_group = False
                    replication_groups[group_name.lower()] = {
                        &#39;id&#39;: group_id,
                        &#39;type&#39;: group_type,
                        &#39;zealGroup&#39;: zeal_group,
                    }
                return replication_groups
            raise SDKException(&#39;Response&#39;, 102)

        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34; Refresh the replication groups created in the commcell.
        Args:

        Returns:

        Raises:

        &#34;&#34;&#34;
        self._replication_groups = self._get_replication_groups()</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroups.ReplicationGroupType"><code class="name">var <span class="ident">ReplicationGroupType</span></code></dt>
<dd>
<div class="desc"><p>Enum to map Replication Group Types to integers</p></div>
</dd>
</dl>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroups.replication_groups"><code class="name">var <span class="ident">replication_groups</span></code></dt>
<dd>
<div class="desc"><p>return all replication groups
Args:</p>
<p>Returns: All the replication groups in the commcell</p>
<p>Raises:</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L282-L291" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def replication_groups(self):
    &#34;&#34;&#34; return all replication groups
    Args:

    Returns: All the replication groups in the commcell

    Raises:
    &#34;&#34;&#34;
    return self._replication_groups</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroups.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, replication_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the specified replication group name.</p>
<h2 id="args">Args</h2>
<p>replication_group_name (str)
&ndash;
name of the replication group</p>
<h2 id="returns">Returns</h2>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L213-L280" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, replication_group_name):
    &#34;&#34;&#34; Deletes the specified replication group name.

        Args:
            replication_group_name (str)  --  name of the replication group

        Returns:


        Raises:
            SDKException:
                if proper inputs are not provided
                if response is empty
                if response is not success
    &#34;&#34;&#34;

    if not isinstance(replication_group_name, str):
        raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;)

    replication_group_name = replication_group_name.lower()
    if self.has_replication_group(replication_group_name):

        replication_group_dict = self.replication_groups.get(
            replication_group_name.lower(), {})

        if replication_group_dict:
            if replication_group_dict.get(&#39;zealGroup&#39;):
                payload = {
                    &#34;repGrpIds&#34;: [int(replication_group_dict.get(&#39;id&#39;))],
                    &#34;taskIds&#34;: [],
                }
            else:
                payload = {
                    &#34;repGrpIds&#34;: [],
                    &#34;taskIds&#34;: [replication_group_dict.get(&#39;id&#39;)],
                }

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                method=&#39;POST&#39;,
                url=self._services[&#39;DELETE_REPLICATION_GROUPS&#39;],
                payload=payload
            )

            if flag:
                if response.json() and &#39;deleteGroupsResponse&#39; in response.json():
                    if (response.json().get(&#39;deleteGroupsResponse&#39;, [{}])[0]
                            .get(&#39;response&#39;).get(&#39;errorMessage&#39;)):
                        error_message = (response.json().get(&#39;deleteGroupsResponse&#39;, {})
                                         .get(&#39;response&#39;, {}).get(&#39;errorMessage&#39;))
                        o_str = (&#39;Failed to delete replication group: {0} \nError: &#34;{1}&#34;&#39;
                                 .format(replication_group_name, error_message))

                        raise SDKException(&#39;ReplicationGroup&#39;, &#39;102&#39;, o_str)
                    self.refresh()

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(
                    response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;, &#39;Replication group information is empty&#39;)
    else:
        raise SDKException(
            &#39;ReplicationGroup&#39;, &#39;102&#39;, &#39;No replication group exists with name: &#34;{0}&#34;&#39;.format(
                replication_group_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroups.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, replication_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a replication group object of the specified replication group name.</p>
<h2 id="args">Args</h2>
<p>replication_group_name (str)
&ndash;
name of the replication group</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the ReplicationGroup class for the given replication group name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided
If Replication group doesnt exists with given name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L187-L211" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, replication_group_name):
    &#34;&#34;&#34;Returns a replication group object of the specified replication group name.

        Args:
            replication_group_name (str)  --  name of the replication group

        Returns:
            object - instance of the ReplicationGroup class for the given replication group name

        Raises:
            SDKException:
                if proper inputs are not provided
                If Replication group doesnt exists with given name
    &#34;&#34;&#34;
    if not isinstance(replication_group_name, str):
        raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;)
    replication_group_name = replication_group_name.lower()
    if self.has_replication_group(replication_group_name):
        return ReplicationGroup(
            self._commcell_object, replication_group_name)

    raise SDKException(
        &#39;ReplicationGroup&#39;,
        &#39;102&#39;,
        &#34;Replication group doesn&#39;t exist with name: {0}&#34;.format(replication_group_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroups.has_replication_group"><code class="name flex">
<span>def <span class="ident">has_replication_group</span></span>(<span>self, replication_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if replication group exists or not</p>
<h2 id="args">Args</h2>
<p>replication_group_name (str)
&ndash;
name of the replication group</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether replication group exists or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L169-L185" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_replication_group(self, replication_group_name):
    &#34;&#34;&#34;Checks if replication group exists or not

        Args:
            replication_group_name (str)  --  name of the replication group

        Returns:
            bool - boolean output whether replication group exists or not

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(replication_group_name, str):
        raise SDKException(&#39;ReplicationGroup&#39;, &#39;101&#39;)

    return self.replication_groups and replication_group_name.lower() in self.replication_groups</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_groups.ReplicationGroups.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the replication groups created in the commcell.
Args:</p>
<p>Returns:</p>
<p>Raises:</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_groups.py#L340-L349" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34; Refresh the replication groups created in the commcell.
    Args:

    Returns:

    Raises:

    &#34;&#34;&#34;
    self._replication_groups = self._get_replication_groups()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.drorchestration" href="index.html">cvpysdk.drorchestration</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup">ReplicationGroup</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.copy_for_replication" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.copy_for_replication">copy_for_replication</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.copy_precedence_applicable" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.copy_precedence_applicable">copy_precedence_applicable</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.destination_agent" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.destination_agent">destination_agent</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.destination_client" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.destination_client">destination_client</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.destination_instance" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.destination_instance">destination_instance</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.group_frequency" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.group_frequency">group_frequency</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.group_id" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.group_id">group_id</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.group_name" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.group_name">group_name</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.intelli_snap_engine" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.intelli_snap_engine">intelli_snap_engine</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_dvdf_enabled" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_dvdf_enabled">is_dvdf_enabled</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_enabled" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_enabled">is_enabled</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_intelli_snap_enabled" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_warm_sync_enabled" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.is_warm_sync_enabled">is_warm_sync_enabled</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.live_sync_pairs" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.live_sync_pairs">live_sync_pairs</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.recovery_target" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.recovery_target">recovery_target</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.refresh" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.replication_type" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.replication_type">replication_type</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.restore_options" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.restore_options">restore_options</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.source_agent" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.source_agent">source_agent</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.source_client" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.source_client">source_client</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.source_instance" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.source_instance">source_instance</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.subclient" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.subclient">subclient</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.task_id" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.task_id">task_id</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.vm_pairs" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.vm_pairs">vm_pairs</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroup.zeal_group" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroup.zeal_group">zeal_group</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroups" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroups">ReplicationGroups</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroups.ReplicationGroupType" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroups.ReplicationGroupType">ReplicationGroupType</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroups.delete" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroups.delete">delete</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroups.get" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroups.get">get</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroups.has_replication_group" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroups.has_replication_group">has_replication_group</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroups.refresh" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroups.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_groups.ReplicationGroups.replication_groups" href="#cvpysdk.drorchestration.replication_groups.ReplicationGroups.replication_groups">replication_groups</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>