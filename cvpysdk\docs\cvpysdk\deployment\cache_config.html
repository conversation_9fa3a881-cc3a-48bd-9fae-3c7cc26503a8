<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.deployment.cache_config API documentation</title>
<meta name="description" content="&#34; Main file for performing the software cache configuration related operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.deployment.cache_config</code></h1>
</header>
<section id="section-intro">
<p>" Main file for performing the software cache configuration related operations</p>
<p>CommserveCache
&ndash;
Class for performing operations on the CS cache
RemoteCache
&ndash;
Class for performing operations on the remote cache</p>
<h1 id="commservecache">CommServeCache</h1>
<pre><code>__init__(commcell_object)             --  initialize commcell_object of CommServeCache class
associated with the commcell

get_request_xml()                     --  returns request xml for cache and remote cache related operations

get_cs_cache_path()                   --  returns CS cache path

delete_cache()                        --  deletes CS cache

commit_cache()                        --  commits CS cache

get_remote_cache_clients()            --  fetches the list of Remote Cache configured for a particular Admin/Tenant
</code></pre>
<h1 id="remotecache">RemoteCache</h1>
<pre><code>__init__(commcell, client_object)     --  initialize commcell and client_object of RemoteCache class
associated with the client

get_remote_cache_path()               --  returns remote cache path, if exists, else None

configure_remote_cache()              --  Configures client as remote cache

configure_packages_to_sync()          --  Configures packages to sync for the remote cache

assoc_entity_to_remote_cache()        --  Associates entity to the Remote Cache

delete_remote_cache_contents()        --  deletes remote cache contents
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L1-L495" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;&#34; Main file for performing the software cache configuration related operations

CommserveCache   --  Class for performing operations on the CS cache
RemoteCache      --  Class for performing operations on the remote cache

CommServeCache
==============

    __init__(commcell_object)             --  initialize commcell_object of CommServeCache class
    associated with the commcell

    get_request_xml()                     --  returns request xml for cache and remote cache related operations

    get_cs_cache_path()                   --  returns CS cache path

    delete_cache()                        --  deletes CS cache

    commit_cache()                        --  commits CS cache

    get_remote_cache_clients()            --  fetches the list of Remote Cache configured for a particular Admin/Tenant

RemoteCache
==============

    __init__(commcell, client_object)     --  initialize commcell and client_object of RemoteCache class
    associated with the client

    get_remote_cache_path()               --  returns remote cache path, if exists, else None

    configure_remote_cache()              --  Configures client as remote cache

    configure_packages_to_sync()          --  Configures packages to sync for the remote cache

    assoc_entity_to_remote_cache()        --  Associates entity to the Remote Cache

    delete_remote_cache_contents()        --  deletes remote cache contents
&#34;&#34;&#34;
from xml.etree import ElementTree as ET
from ..exception import SDKException
from .deploymentconstants import UnixDownloadFeatures
from .deploymentconstants import WindowsDownloadFeatures
from .deploymentconstants import OSNameIDMapping


class CommServeCache(object):
    &#34;&#34;&#34;&#34;class for downloading software packages&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize commcell_object of the Download class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the CommServeCache class

        &#34;&#34;&#34;

        self.commcell_object = commcell_object
        self.request_xml = CommServeCache.get_request_xml()
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services

    @staticmethod
    def get_request_xml():
        &#34;&#34;&#34;Returns request xml for cache and remote cache related operations&#34;&#34;&#34;
        return &#34;&#34;&#34;&lt;EVGui_SetUpdateAgentInfoReq&gt;
                &lt;uaInfo deletePackageCache=&#34;&#34; deleteUpdateCache=&#34;&#34; swAgentOpType=&#34;&#34;
                uaOpCode=&#34;0&#34; uaPackageCacheStatus=&#34;0&#34;
                 uaUpdateCacheStatus=&#34;0&#34; &gt;
                &lt;uaName id=&#34;2&#34; name=&#34;&#34;/&gt;
                &lt;client _type_=&#34;3&#34;/&gt;
                &lt;/uaInfo&gt;
                &lt;/EVGui_SetUpdateAgentInfoReq&gt;
                &#34;&#34;&#34;

    def get_cs_cache_path(self):
        &#34;&#34;&#34;
        Returns CS cache path

        Returns:
            cs_cache_path (str) -- returns cs cache path

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect/empty
        &#34;&#34;&#34;
        try:
            response = self.commcell_object.get_gxglobalparam_value()
        except Exception:
            try:
                response = self.commcell_object.get_gxglobalparam_value(&#39;Patch Directory&#39;)
            except Exception:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#39;Failed to execute api for get_cs_cache_path&#39;)
            if response == &#39;&#39;:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            return response
        if response[&#39;error&#39;][&#39;errorCode&#39;] != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response[&#39;error&#39;][&#39;errorMessage&#39;]
            )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response[&#39;error&#39;][&#39;errorCode&#39;], error_message)
            )
        try:
            return response[&#39;commserveSoftwareCache&#39;][&#39;storePatchlocation&#39;]
        except Exception:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def delete_cache(self):
        &#34;&#34;&#34;
        Delete CS cache

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect
        &#34;&#34;&#34;
        root = ET.fromstring(self.request_xml)
        uaInfo = root.find(&#34;.//uaInfo&#34;)
        uaInfo.set(&#39;deletePackageCache&#39;, &#34;1&#34;)
        uaInfo.set(&#34;deleteUpdateCache&#34;, &#34;1&#34;)
        uaInfo.set(&#34;swAgentOpType&#34;, &#34;1&#34;)

        response = self.commcell_object.qoperation_execute(ET.tostring(root))
        if response.get(&#39;errorCode&#39;) != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response.get(&#39;errorMessage&#39;)
            )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
            )

    def commit_cache(self):
        &#34;&#34;&#34;
        Commits CS cache

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect
        &#34;&#34;&#34;

        root = ET.fromstring(self.request_xml)
        uaInfo = root.find(&#34;.//uaInfo&#34;)
        uaInfo.set(&#39;deletePackageCache&#39;, &#34;0&#34;)
        uaInfo.set(&#34;deleteUpdateCache&#34;, &#34;0&#34;)
        uaInfo.set(&#34;swAgentOpType&#34;, &#34;4&#34;)

        response = self.commcell_object.qoperation_execute(ET.tostring(root))
        if response.get(&#39;errorCode&#39;) != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response.get(&#39;errorMessage&#39;)
            )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
            )

    def get_remote_cache_clients(self):
        &#34;&#34;&#34;
        Fetches the List of Remote Cache configured for a particular Admin/Tenant
        :return: List of Remote Cache configured
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;GET_REMOTE_CACHE_CLIENTS&#39;])

        if flag:
            rc_client_names = []
            if response.ok:
                xml_tree = ET.fromstring(response.text)
                if xml_tree.findall(&#34;.//client&#34;):
                    # Find all &#39;client&#39; elements
                    client_elements = xml_tree.findall(&#39;.//client&#39;)
                    # Extract the client names
                    rc_client_names = [client.get(&#39;clientName&#39;) for client in client_elements]
                    rc_client_names.remove(self.commcell_object.commserv_name)
                return rc_client_names
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)


class RemoteCache(object):
    &#34;&#34;&#34;&#34;class for downloading software packages&#34;&#34;&#34;

    def __init__(self, commcell, client_name):
        &#34;&#34;&#34;Initialize commcell_object of the Download class.

            Args:
                commcell (object)     --  commcell object
                client_name           --  client name

            Returns:
                object - instance of the RemoteCache class

        &#34;&#34;&#34;
        self.commcell = commcell
        self.client_object = self.commcell.clients.get(client_name)
        self.request_xml = CommServeCache.get_request_xml()
        self._cvpysdk_object = commcell._cvpysdk_object
        self._services = commcell._services

    def get_remote_cache_path(self):
        &#34;&#34;&#34;
        Returns remote cache path, if exists, else None

        Returns:
            remote_cache_path (str) - remote cache path of the client if exists
            None                    - otherwise

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect/empty

        &#34;&#34;&#34;
        request_xml = &#39;&lt;EVGui_GetUpdateAgentInfoReq /&gt;&#39;
        response = self.commcell.qoperation_execute(request_xml)
        if response:
            try:
                for clients in response[&#34;uaInfo&#34;]:
                    if clients[&#39;client&#39;][&#39;clientName&#39;] == self.client_object.client_name:
                        return clients[&#34;uaCachePath&#34;]
                return None
            except Exception:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def configure_remotecache(self, cache_path):
        &#34;&#34;&#34;
        Configures client as remote cache

        Args:
              cache_path (str)  - Remote cache path

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect
        &#34;&#34;&#34;

        # using API to configure RC from SP34
        if self.commcell.commserv_version &gt;= 34:
            request_json = {
                &#34;cacheDirectory&#34;: cache_path,
                &#34;associations&#34;: [],
                &#34;cache&#34;: {
                    &#34;name&#34;: self.client_object.client_name
                }
            }

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._services[&#39;CREATE_RC&#39;], request_json
            )

            if flag:
                if response.json():
                    errorCode = response.json()[&#39;errorCode&#39;]
                    if errorCode != 0:
                        raise SDKException(
                            &#39;Response&#39;,
                            &#39;101&#39;,
                            &#39;Error Code: &#34;{0}&#34;&#39;.format(errorCode)
                        )
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;)

        # using qscript to configure machine as RC before SP34
        else:
            root = ET.fromstring(self.request_xml)
            uaInfo = root.find(&#34;.//uaInfo&#34;)
            uaInfo.set(&#39;uaCachePath&#39;, cache_path)
            uaInfo.set(&#39;uaOpCode&#39;, &#34;5&#34;)
            uaInfo.attrib.pop(&#34;uaPackageCacheStatus&#34;)
            uaInfo.attrib.pop(&#39;uaUpdateCacheStatus&#39;)
            root.find(&#34;./uaInfo/uaName&#34;).set(&#34;id&#34;, self.client_object.client_id)
            root.find(&#34;./uaInfo/uaName&#34;).set(&#34;name&#34;, self.client_object.client_name)
            response = self.commcell.qoperation_execute(ET.tostring(root))
            if response.get(&#39;errorCode&#39;) != 0:
                error_message = &#34;Failed with error: [{0}]&#34;.format(
                    response.get(&#39;errorMessage&#39;)
                )
                raise SDKException(
                    &#39;Response&#39;,
                    &#39;101&#39;,
                    &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
                )

    def configure_packages_to_sync(self, win_os=None, win_package_list=None, unix_os=None,
                                   unix_package_list=None):
        &#34;&#34;&#34;
        Configures packages to sync for the remote cache

        Args:
            win_os              (list)          -- list of windows oses to sync
            win_package_list  (list)-- list of windows packages to sync
            unix_os (list)                      -- list of unix oses to sync
            unix_package_list (list)-- list of unix packages to sync

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect

            - Incorrect input

        Usage:
            commcell_obj.configure_packages_to_sync()

            win_os = [&#34;WINDOWS_32&#34;, &#34;WINDOWS_64&#34;]
            unix_os = [&#34;UNIX_LINUX64&#34;, &#34;UNIX_AIX&#34;]
            win_package_list = [&#34;FILE_SYSTEM&#34;, &#34;MEDIA_AGENT&#34;]
            unix_package_list = [&#34;FILE_SYSTEM&#34;, &#34;MEDIA_AGENT&#34;]

            OS_Name_ID_Mapping, WindowsDownloadFeatures and UnixDownloadFeatures enum is used for
            providing input to the configure_packages_to_sync method, it can be imported by

                &gt;&gt;&gt; from cvpysdk.deployment.deploymentconstants import UnixDownloadFeatures
                    from cvpysdk.deployment.deploymentconstants import OS_Name_ID_Mapping
                    from cvpysdk.deployment.deploymentconstants import WindowsDownloadFeatures

        &#34;&#34;&#34;
        if win_os:
            win_os_id = [eval(f&#34;OSNameIDMapping.{each}.value&#34;) for each in win_os]
            win_packages = [eval(f&#34;WindowsDownloadFeatures.{packages}.value&#34;) for packages in win_package_list]
        if unix_os:
            unix_os_id = [eval(f&#34;OSNameIDMapping.{each}.value&#34;) for each in unix_os]
            unix_packages = [eval(f&#34;UnixDownloadFeatures.{packages}.value&#34;) for packages in unix_package_list]

        if not win_os and not unix_os:
            qscript = f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncAll&#39;&#39;&#39;
        elif not unix_os:
            qscript = (f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncCustom &#39;&#39;&#39;
                       f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, win_os_id))} -si {&#34;,&#34;.join(map(str, win_packages))}&#39;&#39;&#39;)
        elif not win_os:
            qscript = (f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncCustom &#39;&#39;&#39;
                       f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, unix_os_id))} -si {&#34;,&#34;.join(map(str, unix_packages))}&#39;&#39;&#39;)
        else:
            qscript = (f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncCustom &#39;&#39;&#39;
                       f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, win_os_id))} -si {&#34;,&#34;.join(map(str, win_packages))} &#39;&#39;&#39;
                       f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, unix_os_id))} -si {&#34;,&#34;.join(map(str, unix_packages))}&#39;&#39;&#39;)

        response = self.commcell._qoperation_execscript(qscript)
        if response.get(&#39;CVGui_GenericResp&#39;):
            if response[&#39;CVGui_GenericResp&#39;][&#39;@errorCode&#39;] != 0:
                error_message = &#34;Failed with error: [{0}]&#34;.format(
                    response[&#39;CVGui_GenericResp&#39;][&#39;@errorMessage&#39;]
                )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(
                    response[&#39;CVGui_GenericResp&#39;][&#39;@errorCode&#39;],
                    error_message))

    def delete_remote_cache_contents(self):
        &#34;&#34;&#34;
        Delete remote cache contents

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect
        &#34;&#34;&#34;
        root = ET.fromstring(self.request_xml)
        uaInfo = root.find(&#34;.//uaInfo&#34;)
        uaInfo.set(&#39;deletePackageCache&#39;, &#34;1&#34;)
        uaInfo.set(&#34;deleteUpdateCache&#34;, &#34;1&#34;)
        uaInfo.set(&#34;swAgentOpType&#34;, &#34;1&#34;)
        root.find(&#34;./uaInfo/uaName&#34;).set(&#34;id&#34;, self.client_object.client_id)
        root.find(&#34;./uaInfo/uaName&#34;).set(&#34;name&#34;, self.client_object.client_name)

        response = self.commcell.qoperation_execute(ET.tostring(root))
        if response.get(&#39;errorCode&#39;) != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response.get(&#39;errorMessage&#39;)
            )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
            )

    def assoc_entity_to_remote_cache(self, client_name=None, client_group_name=None):
        &#34;&#34;&#34;
            Points/Associates entity to the Remote Cache Client

                Args:
                    client_name (str)  -- The client which has to be pointed to Remote Cache

                    client_group_name (str)  -- The client_group which has to be pointed to Remote Cache

                Raises:
                    SDKException:
                    - Failed to execute the api

                    - Response is incorrect
        &#34;&#34;&#34;

        if client_name is None and client_group_name is None:
            raise Exception(&#34;No clients or client groups to associate; Please provide a valid name&#34;)

        if client_name and client_name in self.commcell.clients.all_clients:
            entity_obj = self.commcell.clients.get(client_name)
            entity_id = entity_obj.client_id
            entity_name = entity_obj.client_name
            entity_type =&#34;0&#34;

        elif client_group_name in self.commcell.client_groups.all_clientgroups:
            entity_obj = self.commcell.client_groups.get(client_group_name)
            entity_id = entity_obj.clientgroup_id
            entity_name = entity_obj.clientgroup_name
            entity_type = &#34;1&#34;

        else:
            raise Exception(&#34;{0} does not exist&#34;.format(client_name if client_name else client_group_name))

        request_json = {
                &#34;EVGui_SetUpdateAgentInfoReq&#34; :{
                &#34;uaInfo&#34;: {
                    &#34;uaCachePath&#34;: self.get_remote_cache_path(),
                    &#34;uaOpCode&#34;: &#34;5&#34;,
                    &#34;uaName&#34;: {
                        &#34;id&#34;: self.client_object.client_id,
                        &#34;name&#34;: self.client_object.client_name
                    }
                },
                &#34;uaList&#34;: {
                    &#34;addedList&#34;: {
                        &#34;id&#34;: entity_id,
                        &#34;name&#34;: entity_name,
                        &#34;type&#34;: entity_type
                    }
                }
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], request_json
        )

        if flag:
            if response.ok:
                if response.json():
                    if response.json().get(&#39;errorCode&#39;) != 0:
                        error_code = response.json().get(&#39;errorCode&#39;)
                        error_message = &#34;Failed with error: [{0}]&#34;.format(
                            response.json().get(&#39;errorMessage&#39;)
                        )
                        raise SDKException(
                            &#39;Response&#39;,
                            &#39;101&#39;,
                            &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(error_code, error_message)
                        )
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.deployment.cache_config.CommServeCache"><code class="flex name class">
<span>class <span class="ident">CommServeCache</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>"class for downloading software packages</p>
<p>Initialize commcell_object of the Download class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the CommServeCache class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L63-L208" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CommServeCache(object):
    &#34;&#34;&#34;&#34;class for downloading software packages&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize commcell_object of the Download class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the CommServeCache class

        &#34;&#34;&#34;

        self.commcell_object = commcell_object
        self.request_xml = CommServeCache.get_request_xml()
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services

    @staticmethod
    def get_request_xml():
        &#34;&#34;&#34;Returns request xml for cache and remote cache related operations&#34;&#34;&#34;
        return &#34;&#34;&#34;&lt;EVGui_SetUpdateAgentInfoReq&gt;
                &lt;uaInfo deletePackageCache=&#34;&#34; deleteUpdateCache=&#34;&#34; swAgentOpType=&#34;&#34;
                uaOpCode=&#34;0&#34; uaPackageCacheStatus=&#34;0&#34;
                 uaUpdateCacheStatus=&#34;0&#34; &gt;
                &lt;uaName id=&#34;2&#34; name=&#34;&#34;/&gt;
                &lt;client _type_=&#34;3&#34;/&gt;
                &lt;/uaInfo&gt;
                &lt;/EVGui_SetUpdateAgentInfoReq&gt;
                &#34;&#34;&#34;

    def get_cs_cache_path(self):
        &#34;&#34;&#34;
        Returns CS cache path

        Returns:
            cs_cache_path (str) -- returns cs cache path

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect/empty
        &#34;&#34;&#34;
        try:
            response = self.commcell_object.get_gxglobalparam_value()
        except Exception:
            try:
                response = self.commcell_object.get_gxglobalparam_value(&#39;Patch Directory&#39;)
            except Exception:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#39;Failed to execute api for get_cs_cache_path&#39;)
            if response == &#39;&#39;:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            return response
        if response[&#39;error&#39;][&#39;errorCode&#39;] != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response[&#39;error&#39;][&#39;errorMessage&#39;]
            )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response[&#39;error&#39;][&#39;errorCode&#39;], error_message)
            )
        try:
            return response[&#39;commserveSoftwareCache&#39;][&#39;storePatchlocation&#39;]
        except Exception:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def delete_cache(self):
        &#34;&#34;&#34;
        Delete CS cache

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect
        &#34;&#34;&#34;
        root = ET.fromstring(self.request_xml)
        uaInfo = root.find(&#34;.//uaInfo&#34;)
        uaInfo.set(&#39;deletePackageCache&#39;, &#34;1&#34;)
        uaInfo.set(&#34;deleteUpdateCache&#34;, &#34;1&#34;)
        uaInfo.set(&#34;swAgentOpType&#34;, &#34;1&#34;)

        response = self.commcell_object.qoperation_execute(ET.tostring(root))
        if response.get(&#39;errorCode&#39;) != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response.get(&#39;errorMessage&#39;)
            )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
            )

    def commit_cache(self):
        &#34;&#34;&#34;
        Commits CS cache

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect
        &#34;&#34;&#34;

        root = ET.fromstring(self.request_xml)
        uaInfo = root.find(&#34;.//uaInfo&#34;)
        uaInfo.set(&#39;deletePackageCache&#39;, &#34;0&#34;)
        uaInfo.set(&#34;deleteUpdateCache&#34;, &#34;0&#34;)
        uaInfo.set(&#34;swAgentOpType&#34;, &#34;4&#34;)

        response = self.commcell_object.qoperation_execute(ET.tostring(root))
        if response.get(&#39;errorCode&#39;) != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response.get(&#39;errorMessage&#39;)
            )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
            )

    def get_remote_cache_clients(self):
        &#34;&#34;&#34;
        Fetches the List of Remote Cache configured for a particular Admin/Tenant
        :return: List of Remote Cache configured
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;GET_REMOTE_CACHE_CLIENTS&#39;])

        if flag:
            rc_client_names = []
            if response.ok:
                xml_tree = ET.fromstring(response.text)
                if xml_tree.findall(&#34;.//client&#34;):
                    # Find all &#39;client&#39; elements
                    client_elements = xml_tree.findall(&#39;.//client&#39;)
                    # Extract the client names
                    rc_client_names = [client.get(&#39;clientName&#39;) for client in client_elements]
                    rc_client_names.remove(self.commcell_object.commserv_name)
                return rc_client_names
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
<h3>Static methods</h3>
<dl>
<dt id="cvpysdk.deployment.cache_config.CommServeCache.get_request_xml"><code class="name flex">
<span>def <span class="ident">get_request_xml</span></span>(<span>)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns request xml for cache and remote cache related operations</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L82-L93" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@staticmethod
def get_request_xml():
    &#34;&#34;&#34;Returns request xml for cache and remote cache related operations&#34;&#34;&#34;
    return &#34;&#34;&#34;&lt;EVGui_SetUpdateAgentInfoReq&gt;
            &lt;uaInfo deletePackageCache=&#34;&#34; deleteUpdateCache=&#34;&#34; swAgentOpType=&#34;&#34;
            uaOpCode=&#34;0&#34; uaPackageCacheStatus=&#34;0&#34;
             uaUpdateCacheStatus=&#34;0&#34; &gt;
            &lt;uaName id=&#34;2&#34; name=&#34;&#34;/&gt;
            &lt;client _type_=&#34;3&#34;/&gt;
            &lt;/uaInfo&gt;
            &lt;/EVGui_SetUpdateAgentInfoReq&gt;
            &#34;&#34;&#34;</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.deployment.cache_config.CommServeCache.commit_cache"><code class="name flex">
<span>def <span class="ident">commit_cache</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Commits CS cache</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
- Failed to execute the api</p>
<ul>
<li>Response is incorrect</li>
</ul></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L159-L185" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def commit_cache(self):
    &#34;&#34;&#34;
    Commits CS cache

    Raises:
        SDKException:
        - Failed to execute the api

        - Response is incorrect
    &#34;&#34;&#34;

    root = ET.fromstring(self.request_xml)
    uaInfo = root.find(&#34;.//uaInfo&#34;)
    uaInfo.set(&#39;deletePackageCache&#39;, &#34;0&#34;)
    uaInfo.set(&#34;deleteUpdateCache&#34;, &#34;0&#34;)
    uaInfo.set(&#34;swAgentOpType&#34;, &#34;4&#34;)

    response = self.commcell_object.qoperation_execute(ET.tostring(root))
    if response.get(&#39;errorCode&#39;) != 0:
        error_message = &#34;Failed with error: [{0}]&#34;.format(
            response.get(&#39;errorMessage&#39;)
        )
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.deployment.cache_config.CommServeCache.delete_cache"><code class="name flex">
<span>def <span class="ident">delete_cache</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Delete CS cache</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
- Failed to execute the api</p>
<ul>
<li>Response is incorrect</li>
</ul></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L132-L157" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_cache(self):
    &#34;&#34;&#34;
    Delete CS cache

    Raises:
        SDKException:
        - Failed to execute the api

        - Response is incorrect
    &#34;&#34;&#34;
    root = ET.fromstring(self.request_xml)
    uaInfo = root.find(&#34;.//uaInfo&#34;)
    uaInfo.set(&#39;deletePackageCache&#39;, &#34;1&#34;)
    uaInfo.set(&#34;deleteUpdateCache&#34;, &#34;1&#34;)
    uaInfo.set(&#34;swAgentOpType&#34;, &#34;1&#34;)

    response = self.commcell_object.qoperation_execute(ET.tostring(root))
    if response.get(&#39;errorCode&#39;) != 0:
        error_message = &#34;Failed with error: [{0}]&#34;.format(
            response.get(&#39;errorMessage&#39;)
        )
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.deployment.cache_config.CommServeCache.get_cs_cache_path"><code class="name flex">
<span>def <span class="ident">get_cs_cache_path</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns CS cache path</p>
<h2 id="returns">Returns</h2>
<p>cs_cache_path (str) &ndash; returns cs cache path</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
- Failed to execute the api</p>
<ul>
<li>Response is incorrect/empty</li>
</ul></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L95-L130" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_cs_cache_path(self):
    &#34;&#34;&#34;
    Returns CS cache path

    Returns:
        cs_cache_path (str) -- returns cs cache path

    Raises:
        SDKException:
        - Failed to execute the api

        - Response is incorrect/empty
    &#34;&#34;&#34;
    try:
        response = self.commcell_object.get_gxglobalparam_value()
    except Exception:
        try:
            response = self.commcell_object.get_gxglobalparam_value(&#39;Patch Directory&#39;)
        except Exception:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#39;Failed to execute api for get_cs_cache_path&#39;)
        if response == &#39;&#39;:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        return response
    if response[&#39;error&#39;][&#39;errorCode&#39;] != 0:
        error_message = &#34;Failed with error: [{0}]&#34;.format(
            response[&#39;error&#39;][&#39;errorMessage&#39;]
        )
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response[&#39;error&#39;][&#39;errorCode&#39;], error_message)
        )
    try:
        return response[&#39;commserveSoftwareCache&#39;][&#39;storePatchlocation&#39;]
    except Exception:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.deployment.cache_config.CommServeCache.get_remote_cache_clients"><code class="name flex">
<span>def <span class="ident">get_remote_cache_clients</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Fetches the List of Remote Cache configured for a particular Admin/Tenant
:return: List of Remote Cache configured</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L187-L208" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_remote_cache_clients(self):
    &#34;&#34;&#34;
    Fetches the List of Remote Cache configured for a particular Admin/Tenant
    :return: List of Remote Cache configured
    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;GET_REMOTE_CACHE_CLIENTS&#39;])

    if flag:
        rc_client_names = []
        if response.ok:
            xml_tree = ET.fromstring(response.text)
            if xml_tree.findall(&#34;.//client&#34;):
                # Find all &#39;client&#39; elements
                client_elements = xml_tree.findall(&#39;.//client&#39;)
                # Extract the client names
                rc_client_names = [client.get(&#39;clientName&#39;) for client in client_elements]
                rc_client_names.remove(self.commcell_object.commserv_name)
            return rc_client_names
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.deployment.cache_config.RemoteCache"><code class="flex name class">
<span>class <span class="ident">RemoteCache</span></span>
<span>(</span><span>commcell, client_name)</span>
</code></dt>
<dd>
<div class="desc"><p>"class for downloading software packages</p>
<p>Initialize commcell_object of the Download class.</p>
<h2 id="args">Args</h2>
<p>commcell (object)
&ndash;
commcell object
client_name
&ndash;
client name</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the RemoteCache class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L211-L495" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RemoteCache(object):
    &#34;&#34;&#34;&#34;class for downloading software packages&#34;&#34;&#34;

    def __init__(self, commcell, client_name):
        &#34;&#34;&#34;Initialize commcell_object of the Download class.

            Args:
                commcell (object)     --  commcell object
                client_name           --  client name

            Returns:
                object - instance of the RemoteCache class

        &#34;&#34;&#34;
        self.commcell = commcell
        self.client_object = self.commcell.clients.get(client_name)
        self.request_xml = CommServeCache.get_request_xml()
        self._cvpysdk_object = commcell._cvpysdk_object
        self._services = commcell._services

    def get_remote_cache_path(self):
        &#34;&#34;&#34;
        Returns remote cache path, if exists, else None

        Returns:
            remote_cache_path (str) - remote cache path of the client if exists
            None                    - otherwise

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect/empty

        &#34;&#34;&#34;
        request_xml = &#39;&lt;EVGui_GetUpdateAgentInfoReq /&gt;&#39;
        response = self.commcell.qoperation_execute(request_xml)
        if response:
            try:
                for clients in response[&#34;uaInfo&#34;]:
                    if clients[&#39;client&#39;][&#39;clientName&#39;] == self.client_object.client_name:
                        return clients[&#34;uaCachePath&#34;]
                return None
            except Exception:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def configure_remotecache(self, cache_path):
        &#34;&#34;&#34;
        Configures client as remote cache

        Args:
              cache_path (str)  - Remote cache path

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect
        &#34;&#34;&#34;

        # using API to configure RC from SP34
        if self.commcell.commserv_version &gt;= 34:
            request_json = {
                &#34;cacheDirectory&#34;: cache_path,
                &#34;associations&#34;: [],
                &#34;cache&#34;: {
                    &#34;name&#34;: self.client_object.client_name
                }
            }

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._services[&#39;CREATE_RC&#39;], request_json
            )

            if flag:
                if response.json():
                    errorCode = response.json()[&#39;errorCode&#39;]
                    if errorCode != 0:
                        raise SDKException(
                            &#39;Response&#39;,
                            &#39;101&#39;,
                            &#39;Error Code: &#34;{0}&#34;&#39;.format(errorCode)
                        )
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;)

        # using qscript to configure machine as RC before SP34
        else:
            root = ET.fromstring(self.request_xml)
            uaInfo = root.find(&#34;.//uaInfo&#34;)
            uaInfo.set(&#39;uaCachePath&#39;, cache_path)
            uaInfo.set(&#39;uaOpCode&#39;, &#34;5&#34;)
            uaInfo.attrib.pop(&#34;uaPackageCacheStatus&#34;)
            uaInfo.attrib.pop(&#39;uaUpdateCacheStatus&#39;)
            root.find(&#34;./uaInfo/uaName&#34;).set(&#34;id&#34;, self.client_object.client_id)
            root.find(&#34;./uaInfo/uaName&#34;).set(&#34;name&#34;, self.client_object.client_name)
            response = self.commcell.qoperation_execute(ET.tostring(root))
            if response.get(&#39;errorCode&#39;) != 0:
                error_message = &#34;Failed with error: [{0}]&#34;.format(
                    response.get(&#39;errorMessage&#39;)
                )
                raise SDKException(
                    &#39;Response&#39;,
                    &#39;101&#39;,
                    &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
                )

    def configure_packages_to_sync(self, win_os=None, win_package_list=None, unix_os=None,
                                   unix_package_list=None):
        &#34;&#34;&#34;
        Configures packages to sync for the remote cache

        Args:
            win_os              (list)          -- list of windows oses to sync
            win_package_list  (list)-- list of windows packages to sync
            unix_os (list)                      -- list of unix oses to sync
            unix_package_list (list)-- list of unix packages to sync

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect

            - Incorrect input

        Usage:
            commcell_obj.configure_packages_to_sync()

            win_os = [&#34;WINDOWS_32&#34;, &#34;WINDOWS_64&#34;]
            unix_os = [&#34;UNIX_LINUX64&#34;, &#34;UNIX_AIX&#34;]
            win_package_list = [&#34;FILE_SYSTEM&#34;, &#34;MEDIA_AGENT&#34;]
            unix_package_list = [&#34;FILE_SYSTEM&#34;, &#34;MEDIA_AGENT&#34;]

            OS_Name_ID_Mapping, WindowsDownloadFeatures and UnixDownloadFeatures enum is used for
            providing input to the configure_packages_to_sync method, it can be imported by

                &gt;&gt;&gt; from cvpysdk.deployment.deploymentconstants import UnixDownloadFeatures
                    from cvpysdk.deployment.deploymentconstants import OS_Name_ID_Mapping
                    from cvpysdk.deployment.deploymentconstants import WindowsDownloadFeatures

        &#34;&#34;&#34;
        if win_os:
            win_os_id = [eval(f&#34;OSNameIDMapping.{each}.value&#34;) for each in win_os]
            win_packages = [eval(f&#34;WindowsDownloadFeatures.{packages}.value&#34;) for packages in win_package_list]
        if unix_os:
            unix_os_id = [eval(f&#34;OSNameIDMapping.{each}.value&#34;) for each in unix_os]
            unix_packages = [eval(f&#34;UnixDownloadFeatures.{packages}.value&#34;) for packages in unix_package_list]

        if not win_os and not unix_os:
            qscript = f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncAll&#39;&#39;&#39;
        elif not unix_os:
            qscript = (f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncCustom &#39;&#39;&#39;
                       f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, win_os_id))} -si {&#34;,&#34;.join(map(str, win_packages))}&#39;&#39;&#39;)
        elif not win_os:
            qscript = (f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncCustom &#39;&#39;&#39;
                       f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, unix_os_id))} -si {&#34;,&#34;.join(map(str, unix_packages))}&#39;&#39;&#39;)
        else:
            qscript = (f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncCustom &#39;&#39;&#39;
                       f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, win_os_id))} -si {&#34;,&#34;.join(map(str, win_packages))} &#39;&#39;&#39;
                       f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, unix_os_id))} -si {&#34;,&#34;.join(map(str, unix_packages))}&#39;&#39;&#39;)

        response = self.commcell._qoperation_execscript(qscript)
        if response.get(&#39;CVGui_GenericResp&#39;):
            if response[&#39;CVGui_GenericResp&#39;][&#39;@errorCode&#39;] != 0:
                error_message = &#34;Failed with error: [{0}]&#34;.format(
                    response[&#39;CVGui_GenericResp&#39;][&#39;@errorMessage&#39;]
                )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(
                    response[&#39;CVGui_GenericResp&#39;][&#39;@errorCode&#39;],
                    error_message))

    def delete_remote_cache_contents(self):
        &#34;&#34;&#34;
        Delete remote cache contents

        Raises:
            SDKException:
            - Failed to execute the api

            - Response is incorrect
        &#34;&#34;&#34;
        root = ET.fromstring(self.request_xml)
        uaInfo = root.find(&#34;.//uaInfo&#34;)
        uaInfo.set(&#39;deletePackageCache&#39;, &#34;1&#34;)
        uaInfo.set(&#34;deleteUpdateCache&#34;, &#34;1&#34;)
        uaInfo.set(&#34;swAgentOpType&#34;, &#34;1&#34;)
        root.find(&#34;./uaInfo/uaName&#34;).set(&#34;id&#34;, self.client_object.client_id)
        root.find(&#34;./uaInfo/uaName&#34;).set(&#34;name&#34;, self.client_object.client_name)

        response = self.commcell.qoperation_execute(ET.tostring(root))
        if response.get(&#39;errorCode&#39;) != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response.get(&#39;errorMessage&#39;)
            )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
            )

    def assoc_entity_to_remote_cache(self, client_name=None, client_group_name=None):
        &#34;&#34;&#34;
            Points/Associates entity to the Remote Cache Client

                Args:
                    client_name (str)  -- The client which has to be pointed to Remote Cache

                    client_group_name (str)  -- The client_group which has to be pointed to Remote Cache

                Raises:
                    SDKException:
                    - Failed to execute the api

                    - Response is incorrect
        &#34;&#34;&#34;

        if client_name is None and client_group_name is None:
            raise Exception(&#34;No clients or client groups to associate; Please provide a valid name&#34;)

        if client_name and client_name in self.commcell.clients.all_clients:
            entity_obj = self.commcell.clients.get(client_name)
            entity_id = entity_obj.client_id
            entity_name = entity_obj.client_name
            entity_type =&#34;0&#34;

        elif client_group_name in self.commcell.client_groups.all_clientgroups:
            entity_obj = self.commcell.client_groups.get(client_group_name)
            entity_id = entity_obj.clientgroup_id
            entity_name = entity_obj.clientgroup_name
            entity_type = &#34;1&#34;

        else:
            raise Exception(&#34;{0} does not exist&#34;.format(client_name if client_name else client_group_name))

        request_json = {
                &#34;EVGui_SetUpdateAgentInfoReq&#34; :{
                &#34;uaInfo&#34;: {
                    &#34;uaCachePath&#34;: self.get_remote_cache_path(),
                    &#34;uaOpCode&#34;: &#34;5&#34;,
                    &#34;uaName&#34;: {
                        &#34;id&#34;: self.client_object.client_id,
                        &#34;name&#34;: self.client_object.client_name
                    }
                },
                &#34;uaList&#34;: {
                    &#34;addedList&#34;: {
                        &#34;id&#34;: entity_id,
                        &#34;name&#34;: entity_name,
                        &#34;type&#34;: entity_type
                    }
                }
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], request_json
        )

        if flag:
            if response.ok:
                if response.json():
                    if response.json().get(&#39;errorCode&#39;) != 0:
                        error_code = response.json().get(&#39;errorCode&#39;)
                        error_message = &#34;Failed with error: [{0}]&#34;.format(
                            response.json().get(&#39;errorMessage&#39;)
                        )
                        raise SDKException(
                            &#39;Response&#39;,
                            &#39;101&#39;,
                            &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(error_code, error_message)
                        )
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.deployment.cache_config.RemoteCache.assoc_entity_to_remote_cache"><code class="name flex">
<span>def <span class="ident">assoc_entity_to_remote_cache</span></span>(<span>self, client_name=None, client_group_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Points/Associates entity to the Remote Cache Client</p>
<pre><code>Args:
    client_name (str)  -- The client which has to be pointed to Remote Cache

    client_group_name (str)  -- The client_group which has to be pointed to Remote Cache

Raises:
    SDKException:
    - Failed to execute the api

    - Response is incorrect
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L419-L495" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def assoc_entity_to_remote_cache(self, client_name=None, client_group_name=None):
    &#34;&#34;&#34;
        Points/Associates entity to the Remote Cache Client

            Args:
                client_name (str)  -- The client which has to be pointed to Remote Cache

                client_group_name (str)  -- The client_group which has to be pointed to Remote Cache

            Raises:
                SDKException:
                - Failed to execute the api

                - Response is incorrect
    &#34;&#34;&#34;

    if client_name is None and client_group_name is None:
        raise Exception(&#34;No clients or client groups to associate; Please provide a valid name&#34;)

    if client_name and client_name in self.commcell.clients.all_clients:
        entity_obj = self.commcell.clients.get(client_name)
        entity_id = entity_obj.client_id
        entity_name = entity_obj.client_name
        entity_type =&#34;0&#34;

    elif client_group_name in self.commcell.client_groups.all_clientgroups:
        entity_obj = self.commcell.client_groups.get(client_group_name)
        entity_id = entity_obj.clientgroup_id
        entity_name = entity_obj.clientgroup_name
        entity_type = &#34;1&#34;

    else:
        raise Exception(&#34;{0} does not exist&#34;.format(client_name if client_name else client_group_name))

    request_json = {
            &#34;EVGui_SetUpdateAgentInfoReq&#34; :{
            &#34;uaInfo&#34;: {
                &#34;uaCachePath&#34;: self.get_remote_cache_path(),
                &#34;uaOpCode&#34;: &#34;5&#34;,
                &#34;uaName&#34;: {
                    &#34;id&#34;: self.client_object.client_id,
                    &#34;name&#34;: self.client_object.client_name
                }
            },
            &#34;uaList&#34;: {
                &#34;addedList&#34;: {
                    &#34;id&#34;: entity_id,
                    &#34;name&#34;: entity_name,
                    &#34;type&#34;: entity_type
                }
            }
        }
    }

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], request_json
    )

    if flag:
        if response.ok:
            if response.json():
                if response.json().get(&#39;errorCode&#39;) != 0:
                    error_code = response.json().get(&#39;errorCode&#39;)
                    error_message = &#34;Failed with error: [{0}]&#34;.format(
                        response.json().get(&#39;errorMessage&#39;)
                    )
                    raise SDKException(
                        &#39;Response&#39;,
                        &#39;101&#39;,
                        &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(error_code, error_message)
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.deployment.cache_config.RemoteCache.configure_packages_to_sync"><code class="name flex">
<span>def <span class="ident">configure_packages_to_sync</span></span>(<span>self, win_os=None, win_package_list=None, unix_os=None, unix_package_list=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Configures packages to sync for the remote cache</p>
<h2 id="args">Args</h2>
<p>win_os
(list)
&ndash; list of windows oses to sync
win_package_list
(list)&ndash; list of windows packages to sync
unix_os (list)
&ndash; list of unix oses to sync
unix_package_list (list)&ndash; list of unix packages to sync</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
- Failed to execute the api</p>
<ul>
<li>
<p>Response is incorrect</p>
</li>
<li>
<p>Incorrect input</p>
</li>
</ul>
<h2 id="usage">Usage</h2>
<p>commcell_obj.configure_packages_to_sync()</p>
<p>win_os = ["WINDOWS_32", "WINDOWS_64"]
unix_os = ["UNIX_LINUX64", "UNIX_AIX"]
win_package_list = ["FILE_SYSTEM", "MEDIA_AGENT"]
unix_package_list = ["FILE_SYSTEM", "MEDIA_AGENT"]</p>
<p>OS_Name_ID_Mapping, WindowsDownloadFeatures and UnixDownloadFeatures enum is used for
providing input to the configure_packages_to_sync method, it can be imported by</p>
<pre><code>&gt;&gt;&gt; from cvpysdk.deployment.deploymentconstants import UnixDownloadFeatures
    from cvpysdk.deployment.deploymentconstants import OS_Name_ID_Mapping
    from cvpysdk.deployment.deploymentconstants import WindowsDownloadFeatures
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L322-L388" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_packages_to_sync(self, win_os=None, win_package_list=None, unix_os=None,
                               unix_package_list=None):
    &#34;&#34;&#34;
    Configures packages to sync for the remote cache

    Args:
        win_os              (list)          -- list of windows oses to sync
        win_package_list  (list)-- list of windows packages to sync
        unix_os (list)                      -- list of unix oses to sync
        unix_package_list (list)-- list of unix packages to sync

    Raises:
        SDKException:
        - Failed to execute the api

        - Response is incorrect

        - Incorrect input

    Usage:
        commcell_obj.configure_packages_to_sync()

        win_os = [&#34;WINDOWS_32&#34;, &#34;WINDOWS_64&#34;]
        unix_os = [&#34;UNIX_LINUX64&#34;, &#34;UNIX_AIX&#34;]
        win_package_list = [&#34;FILE_SYSTEM&#34;, &#34;MEDIA_AGENT&#34;]
        unix_package_list = [&#34;FILE_SYSTEM&#34;, &#34;MEDIA_AGENT&#34;]

        OS_Name_ID_Mapping, WindowsDownloadFeatures and UnixDownloadFeatures enum is used for
        providing input to the configure_packages_to_sync method, it can be imported by

            &gt;&gt;&gt; from cvpysdk.deployment.deploymentconstants import UnixDownloadFeatures
                from cvpysdk.deployment.deploymentconstants import OS_Name_ID_Mapping
                from cvpysdk.deployment.deploymentconstants import WindowsDownloadFeatures

    &#34;&#34;&#34;
    if win_os:
        win_os_id = [eval(f&#34;OSNameIDMapping.{each}.value&#34;) for each in win_os]
        win_packages = [eval(f&#34;WindowsDownloadFeatures.{packages}.value&#34;) for packages in win_package_list]
    if unix_os:
        unix_os_id = [eval(f&#34;OSNameIDMapping.{each}.value&#34;) for each in unix_os]
        unix_packages = [eval(f&#34;UnixDownloadFeatures.{packages}.value&#34;) for packages in unix_package_list]

    if not win_os and not unix_os:
        qscript = f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncAll&#39;&#39;&#39;
    elif not unix_os:
        qscript = (f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncCustom &#39;&#39;&#39;
                   f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, win_os_id))} -si {&#34;,&#34;.join(map(str, win_packages))}&#39;&#39;&#39;)
    elif not win_os:
        qscript = (f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncCustom &#39;&#39;&#39;
                   f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, unix_os_id))} -si {&#34;,&#34;.join(map(str, unix_packages))}&#39;&#39;&#39;)
    else:
        qscript = (f&#39;&#39;&#39;-sn QS_GranularConfigRemoteCache -si &#39;{self.client_object.client_name}&#39; -si SyncCustom &#39;&#39;&#39;
                   f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, win_os_id))} -si {&#34;,&#34;.join(map(str, win_packages))} &#39;&#39;&#39;
                   f&#39;&#39;&#39;-si {&#34;,&#34;.join(map(str, unix_os_id))} -si {&#34;,&#34;.join(map(str, unix_packages))}&#39;&#39;&#39;)

    response = self.commcell._qoperation_execscript(qscript)
    if response.get(&#39;CVGui_GenericResp&#39;):
        if response[&#39;CVGui_GenericResp&#39;][&#39;@errorCode&#39;] != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response[&#39;CVGui_GenericResp&#39;][&#39;@errorMessage&#39;]
            )
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(
                response[&#39;CVGui_GenericResp&#39;][&#39;@errorCode&#39;],
                error_message))</code></pre>
</details>
</dd>
<dt id="cvpysdk.deployment.cache_config.RemoteCache.configure_remotecache"><code class="name flex">
<span>def <span class="ident">configure_remotecache</span></span>(<span>self, cache_path)</span>
</code></dt>
<dd>
<div class="desc"><p>Configures client as remote cache</p>
<h2 id="args">Args</h2>
<p>cache_path (str)
- Remote cache path</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
- Failed to execute the api</p>
<ul>
<li>Response is incorrect</li>
</ul></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L259-L320" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_remotecache(self, cache_path):
    &#34;&#34;&#34;
    Configures client as remote cache

    Args:
          cache_path (str)  - Remote cache path

    Raises:
        SDKException:
        - Failed to execute the api

        - Response is incorrect
    &#34;&#34;&#34;

    # using API to configure RC from SP34
    if self.commcell.commserv_version &gt;= 34:
        request_json = {
            &#34;cacheDirectory&#34;: cache_path,
            &#34;associations&#34;: [],
            &#34;cache&#34;: {
                &#34;name&#34;: self.client_object.client_name
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CREATE_RC&#39;], request_json
        )

        if flag:
            if response.json():
                errorCode = response.json()[&#39;errorCode&#39;]
                if errorCode != 0:
                    raise SDKException(
                        &#39;Response&#39;,
                        &#39;101&#39;,
                        &#39;Error Code: &#34;{0}&#34;&#39;.format(errorCode)
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    # using qscript to configure machine as RC before SP34
    else:
        root = ET.fromstring(self.request_xml)
        uaInfo = root.find(&#34;.//uaInfo&#34;)
        uaInfo.set(&#39;uaCachePath&#39;, cache_path)
        uaInfo.set(&#39;uaOpCode&#39;, &#34;5&#34;)
        uaInfo.attrib.pop(&#34;uaPackageCacheStatus&#34;)
        uaInfo.attrib.pop(&#39;uaUpdateCacheStatus&#39;)
        root.find(&#34;./uaInfo/uaName&#34;).set(&#34;id&#34;, self.client_object.client_id)
        root.find(&#34;./uaInfo/uaName&#34;).set(&#34;name&#34;, self.client_object.client_name)
        response = self.commcell.qoperation_execute(ET.tostring(root))
        if response.get(&#39;errorCode&#39;) != 0:
            error_message = &#34;Failed with error: [{0}]&#34;.format(
                response.get(&#39;errorMessage&#39;)
            )
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
            )</code></pre>
</details>
</dd>
<dt id="cvpysdk.deployment.cache_config.RemoteCache.delete_remote_cache_contents"><code class="name flex">
<span>def <span class="ident">delete_remote_cache_contents</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Delete remote cache contents</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
- Failed to execute the api</p>
<ul>
<li>Response is incorrect</li>
</ul></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L390-L417" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_remote_cache_contents(self):
    &#34;&#34;&#34;
    Delete remote cache contents

    Raises:
        SDKException:
        - Failed to execute the api

        - Response is incorrect
    &#34;&#34;&#34;
    root = ET.fromstring(self.request_xml)
    uaInfo = root.find(&#34;.//uaInfo&#34;)
    uaInfo.set(&#39;deletePackageCache&#39;, &#34;1&#34;)
    uaInfo.set(&#34;deleteUpdateCache&#34;, &#34;1&#34;)
    uaInfo.set(&#34;swAgentOpType&#34;, &#34;1&#34;)
    root.find(&#34;./uaInfo/uaName&#34;).set(&#34;id&#34;, self.client_object.client_id)
    root.find(&#34;./uaInfo/uaName&#34;).set(&#34;name&#34;, self.client_object.client_name)

    response = self.commcell.qoperation_execute(ET.tostring(root))
    if response.get(&#39;errorCode&#39;) != 0:
        error_message = &#34;Failed with error: [{0}]&#34;.format(
            response.get(&#39;errorMessage&#39;)
        )
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(response.get(&#39;errorCode&#39;), error_message)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.deployment.cache_config.RemoteCache.get_remote_cache_path"><code class="name flex">
<span>def <span class="ident">get_remote_cache_path</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns remote cache path, if exists, else None</p>
<h2 id="returns">Returns</h2>
<p>remote_cache_path (str) - remote cache path of the client if exists
None
- otherwise</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
- Failed to execute the api</p>
<ul>
<li>Response is incorrect/empty</li>
</ul></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/cache_config.py#L231-L257" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_remote_cache_path(self):
    &#34;&#34;&#34;
    Returns remote cache path, if exists, else None

    Returns:
        remote_cache_path (str) - remote cache path of the client if exists
        None                    - otherwise

    Raises:
        SDKException:
        - Failed to execute the api

        - Response is incorrect/empty

    &#34;&#34;&#34;
    request_xml = &#39;&lt;EVGui_GetUpdateAgentInfoReq /&gt;&#39;
    response = self.commcell.qoperation_execute(request_xml)
    if response:
        try:
            for clients in response[&#34;uaInfo&#34;]:
                if clients[&#39;client&#39;][&#39;clientName&#39;] == self.client_object.client_name:
                    return clients[&#34;uaCachePath&#34;]
            return None
        except Exception:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#commservecache">CommServeCache</a></li>
<li><a href="#remotecache">RemoteCache</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.deployment" href="index.html">cvpysdk.deployment</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.deployment.cache_config.CommServeCache" href="#cvpysdk.deployment.cache_config.CommServeCache">CommServeCache</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.cache_config.CommServeCache.commit_cache" href="#cvpysdk.deployment.cache_config.CommServeCache.commit_cache">commit_cache</a></code></li>
<li><code><a title="cvpysdk.deployment.cache_config.CommServeCache.delete_cache" href="#cvpysdk.deployment.cache_config.CommServeCache.delete_cache">delete_cache</a></code></li>
<li><code><a title="cvpysdk.deployment.cache_config.CommServeCache.get_cs_cache_path" href="#cvpysdk.deployment.cache_config.CommServeCache.get_cs_cache_path">get_cs_cache_path</a></code></li>
<li><code><a title="cvpysdk.deployment.cache_config.CommServeCache.get_remote_cache_clients" href="#cvpysdk.deployment.cache_config.CommServeCache.get_remote_cache_clients">get_remote_cache_clients</a></code></li>
<li><code><a title="cvpysdk.deployment.cache_config.CommServeCache.get_request_xml" href="#cvpysdk.deployment.cache_config.CommServeCache.get_request_xml">get_request_xml</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.deployment.cache_config.RemoteCache" href="#cvpysdk.deployment.cache_config.RemoteCache">RemoteCache</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.cache_config.RemoteCache.assoc_entity_to_remote_cache" href="#cvpysdk.deployment.cache_config.RemoteCache.assoc_entity_to_remote_cache">assoc_entity_to_remote_cache</a></code></li>
<li><code><a title="cvpysdk.deployment.cache_config.RemoteCache.configure_packages_to_sync" href="#cvpysdk.deployment.cache_config.RemoteCache.configure_packages_to_sync">configure_packages_to_sync</a></code></li>
<li><code><a title="cvpysdk.deployment.cache_config.RemoteCache.configure_remotecache" href="#cvpysdk.deployment.cache_config.RemoteCache.configure_remotecache">configure_remotecache</a></code></li>
<li><code><a title="cvpysdk.deployment.cache_config.RemoteCache.delete_remote_cache_contents" href="#cvpysdk.deployment.cache_config.RemoteCache.delete_remote_cache_contents">delete_remote_cache_contents</a></code></li>
<li><code><a title="cvpysdk.deployment.cache_config.RemoteCache.get_remote_cache_path" href="#cvpysdk.deployment.cache_config.RemoteCache.get_remote_cache_path">get_remote_cache_path</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>