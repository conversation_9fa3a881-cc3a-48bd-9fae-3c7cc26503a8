<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.virtualserver.amazon API documentation</title>
<meta name="description" content="File for operating on a Virtual Server Amazon AWS Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.virtualserver.amazon</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Virtual Server Amazon AWS Subclient.</p>
<p>AmazonVirtualServerSubclient is the only class defined in this file.</p>
<p>AmazonVirtualServerSubclient:
Derived class from VirtualServerSubClient Base
class,representing a AWS Subclient,
and to perform operations on that Subclient</p>
<h2 id="amazonvirtualserversubclient">Amazonvirtualserversubclient</h2>
<p><strong>init</strong>(
backupset_object,
subclient_name,
subclient_id)
&ndash;
initialize object of vmware subclient class,
associated with the VirtualServer subclient</p>
<p>full_vm_restore_in_place()
&ndash;
restores the VM specified by the user to
the same location</p>
<p>full_vm_restore_out_of_place() &ndash; restores the VM specified to the provided
Amazon AWS psuedoclient vcenter via
vcenter_client</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/2aca5f791188934b883a3498b8b92fae4b92ee4a/cvpysdk/subclients/virtualserver/amazon.py#L1-L556" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Virtual Server Amazon AWS Subclient.

AmazonVirtualServerSubclient is the only class defined in this file.

AmazonVirtualServerSubclient:   Derived class from VirtualServerSubClient Base
                                class,representing a AWS Subclient,
                                and to perform operations on that Subclient

AmazonVirtualServerSubclient:

    __init__(
        backupset_object,
        subclient_name,
        subclient_id)           --  initialize object of vmware subclient class,
                                    associated with the VirtualServer subclient

    full_vm_restore_in_place()  --  restores the VM specified by the user to
                                    the same location

    full_vm_restore_out_of_place() -- restores the VM specified to the provided
                                      Amazon AWS psuedoclient vcenter via
                                      vcenter_client

&#34;&#34;&#34;

from enum import Enum
from past.builtins import basestring
from ..vssubclient import VirtualServerSubclient
from ...exception import SDKException


class AmazonVirtualServerSubclient(VirtualServerSubclient):
    &#34;&#34;&#34;Derived class from VirtualServerSubclient Base class.
       This represents an Amazon AWS virtual server subclient,
       and can perform restore operations on only that subclient.

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given Virtual Server instance.
        Args
        class_object (backupset_object, subclient_name, subclient_id)  --  instance of the
                                         backupset class, subclient name, subclient id

        &#34;&#34;&#34;
        super(AmazonVirtualServerSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)
        self.diskExtension = [&#34;none&#34;]

    class disk_pattern(Enum):
        &#34;&#34;&#34;
        stores the disk pattern of all hypervisors
        &#34;&#34;&#34;
        name = &#34;name&#34;
        datastore = &#34;availabilityZone&#34;
        new_name = &#39;newName&#39;
        aws_bucket = &#39;Datastore&#39;

    def full_vm_restore_in_place(
            self,
            vm_to_restore=None,
            proxy_client=None,
            is_aws_proxy=True,
            amazon_bucket=None,
            overwrite=True,
            power_on=True,
            copy_precedence=0
    ):
        &#34;&#34;&#34;Restores the FULL Virtual machine specified in the input list
            to the location same as the actual location of the VM in VCenter.

            Args:
                vm_to_restore         (list)        --  provide the VM name to restore
                                                        default: None

                proxy_client          (basestring)  --  proxy client to be used for restore
                                                        default: proxy added in subclient

                is_aws_proxy          (basestring)  --  boolean value whether proxy resides in AWS
                                                        or not
                                                        default: True

                amazon_bucket         (basestring)  --  Amazon bucket (required when non-AWS proxy
                                                        is used)

                overwrite             (bool)        --  overwrite the existing VM
                                                        default: True

                power_on              (bool)        --  power on the  restored VM
                                                        default: True

                copy_precedence       (int)         --  copy precedence value
                                                        default: 0

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}

        # check input parameters are correct
        if vm_to_restore and not isinstance(vm_to_restore, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if copy_precedence:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        if not is_aws_proxy:
            if not amazon_bucket:
                raise SDKException(&#39;Subclient&#39;, 104)
            restore_option[&#39;datastore&#39;] = amazon_bucket

        instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]

        # set attr for all the option in restore xml from user inputs
        self._set_restore_inputs(
            restore_option,
            vm_to_restore=self._set_vm_to_restore(vm_to_restore),
            in_place=True,
            esx_server_name=instance_dict[&#34;clientName&#34;],
            volume_level_restore=1,
            unconditional_overwrite=overwrite,
            power_on=power_on,
            copy_precedence=copy_precedence,
            is_aws_proxy=is_aws_proxy,
            datacenter=None,
            securityGroups=None,
            keyPairList=None,
            resourcePoolPath=None,
            terminationProtected=None,
            optimizationEnabled=False,
            vmSize=None
        )

        request_json = self._prepare_fullvm_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def full_vm_restore_out_of_place(
            self,
            vm_to_restore=None,
            vm_display_name=None,
            proxy_client=None,
            is_aws_proxy=True,
            amazon_bucket=None,
            availability_zone=None,
            amazon_options=None,
            overwrite=True,
            power_on=True,
            copy_precedence=0
    ):
        &#34;&#34;&#34;Restores the FULL Virtual machine specified in the input list
            to the provided virtualization client along with the zone and instance type.
            If the provided client name is none then it restores the Full Virtual
            Machine to the source client and corresponding zone and instance type.

            Args:
                vm_to_restore         (basestring)  --  provide the VM name to restore
                                                        default: None

                vm_display_name       (basestring)        --  provide the new display name for the
                                                        restored VM
                                                        default: None

                proxy_client          (basestring)  --  proxy client to be used for restore
                                                        default: proxy added in subclient

                is_aws_proxy          (basestring)  --  boolean value whether proxy resides in AWS
                                                        or not
                                                        default: True

                amazon_bucket         (basestring)  --  Amazon bucket (required when non-AWS proxy
                                                        is used)

                amazon_options        (dict)        --  dict containing configuration options for
                                                        restored VM. Permissible keys are below
                    availability_zone

                    ami

                    instance_type

                    iam_role

                    termination_protection

                overwrite             (bool)        --  overwrite the existing VM
                                                        default: True

                power_on              (bool)        --  power on the  restored VM
                                                        default: True

                copy_precedence       (int)         --  copy precedence value
                                                        default: 0

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        restore_option = {}
        if not amazon_options:
            amazon_options = {}

        # check input parameters are correct
        if vm_to_restore and not isinstance(vm_to_restore, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if copy_precedence:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        # populating proxy client. It assumes the proxy controller added in instance
        # properties if not specified
        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        if vm_display_name:
            if not (isinstance(vm_to_restore, basestring) or
                    isinstance(vm_display_name, basestring)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
            restore_option[&#39;restore_new_name&#39;] = vm_display_name

        if vm_to_restore:
            vm_to_restore = [vm_to_restore]

        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        if not is_aws_proxy:
            if not amazon_bucket:
                raise SDKException(&#39;Subclient&#39;, 104)
            restore_option[&#39;datastore&#39;] = amazon_bucket

        instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]

        # set attr for all the option in restore xml from user inputs
        self._set_restore_inputs(
            restore_option,
            vm_to_restore=self._set_vm_to_restore(vm_to_restore),
            in_place=False,
            esx_server_name=instance_dict[&#34;clientName&#34;],
            volume_level_restore=1,
            unconditional_overwrite=overwrite,
            power_on=power_on,
            copy_precedence=copy_precedence,
            is_aws_proxy=is_aws_proxy,
            datacenter=None,
            resourcePoolPath=None,
            optimizationEnabled=False,
            availability_zone=availability_zone,
            esx_host=availability_zone,
            ami=amazon_options.get(&#39;ami&#39;, None),
            vmSize=amazon_options.get(&#39;instance_type&#39;, None),
            iamRole=amazon_options.get(&#39;iam_role&#39;, None),
            securityGroups=amazon_options.get(&#39;security_groups&#39;, None),
            keyPairList=amazon_options.get(&#39;keypair_list&#39;, None),
            terminationProtected=amazon_options.get(&#39;termination_protected&#39;, False),
        )

        request_json = self._prepare_fullvm_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def attach_disk_restore(
            self,
            vm_to_restore,
            destination_vm,
            proxy_client=None,
            amazon_options=None,
            overwrite=True,
            copy_precedence=0,
            destination_vm_guid=None,
            disk_prefix=None,
            availability_zone=None,
            media_agent=None,
            disk_name=None
    ):
        &#34;&#34;&#34;Restores the Attach Disk restore with  specified in the input list
            to the provided instance.

            Args:
                vm_to_restore         (basestring)  --  provide the source vm name

                destination_vm        (basestring)  --  provide the destination VM name to restore

                disk_prefix       (basestring)        --  provide the new display name for the
                                                    restored disk
                                                    default: None

                disk_name       (basestring)        --  provide the new display name for the source disk
                                                    default: None

                proxy_client          (basestring)  --  proxy client to be used for restore
                                                    default: proxy added in subclient

                destination_vm_guid     (basestring)  --  instance id of the vm
                                                            default:None

                media_agent             (basestring)  --  media agent to be used browse and restore

                amazon_options        (dict)        --  dict containing configuration options for
                                                        restored VM. Permissible keys are below
                    availability_zone

                    ami

                    instance_type

                overwrite             (bool)        --  overwrite the existing VM
                                                        default: True

                copy_precedence       (int)         --  copy precedence value
                                                        default: 0

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _attach_disk_restore_option = {}
        if not amazon_options:
            amazon_options = {}

        # check input parameters are correct
        if vm_to_restore and not isinstance(vm_to_restore, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if copy_precedence:
            _attach_disk_restore_option[&#39;copy_precedence_applicable&#39;] = True

        # populating proxy client. It assumes the proxy controller added in instance
        # properties if not specified
        if proxy_client is not None:
            _attach_disk_restore_option[&#39;client&#39;] = proxy_client

        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\&#34; + vm_ids[vm_to_restore])
        if not disk_name:
            disk_name = []
            for each_disk_path in disk_list:
                disk_name.append(each_disk_path.split(&#39;\\&#39;)[-1])

        else:
            for each_disk in disk_name:
                each_disk_path = &#34;\\&#34; + str(vm_to_restore) + &#34;\\&#34; + each_disk
                if each_disk_path not in disk_list:
                    raise SDKException(&#39;Subclient&#39;, &#39;111&#39;)

        src_item_list = []
        for each_disk in disk_name:
            src_item_list.append(&#34;\\&#34; + vm_ids[vm_to_restore] + &#34;\\&#34; + each_disk.split(&#34;\\&#34;)[-1])
        _attach_disk_restore_option[&#39;paths&#39;] = src_item_list
        if proxy_client is not None:
            _attach_disk_restore_option[&#39;client&#39;] = proxy_client
        if not destination_vm:
            destination_vm = vm_to_restore
        instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]
        _attach_disk_restore_option = self.amazon_defaults(vm_to_restore, _attach_disk_restore_option)

        # set attr for all the option in restore xml from user inputs
        self._set_restore_inputs(
            _attach_disk_restore_option,
            vm_to_restore=vm_to_restore,
            esx_server_name=instance_dict[&#34;clientName&#34;],
            volume_level_restore=6,
            unconditional_overwrite=overwrite,
            copy_precedence=copy_precedence,
            paths=src_item_list,
            datacenter=None,
            resourcePoolPath=None,
            availability_zone=availability_zone,
            esx_host=availability_zone,
            newName=destination_vm,
            newGUID=destination_vm_guid,
            disk_name_prefix=disk_prefix,
            ami=_attach_disk_restore_option.get(&#39;ami&#39;, None),
            vmSize=_attach_disk_restore_option.get(&#39;instance_type&#39;, None)
        )

        request_json = self._prepare_attach_disk_restore_json(_attach_disk_restore_option)
        return self._process_restore_response(request_json)

    def full_vm_conversion_azurerm(
            self,
            azure_client,
            vm_to_restore=None,
            resource_group=None,
            storage_account=None,
            datacenter=None,
            proxy_client=None,
            overwrite=True,
            power_on=True,
            instance_size=None,
            public_ip=False,
            restore_as_managed=False,
            copy_precedence=0,
            disk_type=None,
            restore_option=None,
            networkDisplayName=None,
            networkrsg=None,
            destsubid=None,
            subnetId=None):
        &#34;&#34;&#34;
                This converts the Hyperv VM to AzureRM
                Args:
                        vm_to_restore          (dict):     dict containing the VM name(s) to restore as
                                                           keys and the new VM name(s) as their values.
                                                           Input empty string for default VM name for
                                                           restored VM.
                                                           default: {}

                        azure_client    (basestring):      name of the AzureRM client
                                                           where the VM should be
                                                           restored.

                        resource_group   (basestring):      destination Resource group
                                                            in the AzureRM

                        storage_account  (basestring):    storage account where the
                                                          restored VM should be located
                                                          in AzureRM

                        overwrite              (bool):    overwrite the existing VM
                                                          default: True

                        power_on               (bool):    power on the  restored VM
                                                          default: True

                        instance_size    (basestring):    Instance Size of restored VM

                        public_ip              (bool):    If True, creates the Public IP of
                                                          restored VM

                        restore_as_managed     (bool):    If True, restore as Managed VM in Azure

                        copy_precedence         (int):    copy precedence value
                                                          default: 0

                        proxy_client      (basestring):   destination proxy client

                        networkDisplayName(basestring):   destination network display name

                        networkrsg        (basestring):   destination network display name&#39;s security group

                        destsubid         (basestring):   destination subscription id

                        subnetId          (basestring):   destination subet id



                    Returns:
                        object - instance of the Job class for this restore job

                    Raises:
                        SDKException:
                            if inputs are not of correct type as per definition

                            if failed to initialize job

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        if restore_option is None:
            restore_option = {}

        if vm_to_restore and not isinstance(vm_to_restore, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not isinstance(vm_to_restore, list):
            vm_to_restore = [vm_to_restore]
        # check mandatory input parameters are correct
        if not isinstance(azure_client, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        subclient = self._set_vm_conversion_defaults(azure_client, restore_option)
        instance = subclient._backupset_object._instance_object
        if proxy_client is None:
            proxy_client = instance.server_host_name[0]

        self._set_restore_inputs(
            restore_option,
            in_place=False,
            vcenter_client=azure_client,
            datastore=storage_account,
            esx_host=resource_group,
            datacenter=datacenter,
            unconditional_overwrite=overwrite,
            client_name=proxy_client,
            power_on=power_on,
            vm_to_restore=self._set_vm_to_restore(vm_to_restore),
            copy_precedence=copy_precedence,
            createPublicIP=public_ip,
            restoreAsManagedVM=restore_as_managed,
            instanceSize=instance_size,
            volume_level_restore=1,
            destination_instance=instance.instance_name,
            backupset_client_name=instance._agent_object._client_object.client_name,
            networkDisplayName=networkDisplayName,
            networkrsg=networkrsg,
            destsubid=destsubid,
            subnetId=subnetId
        )

        request_json = self._prepare_fullvm_restore_json(restore_option)
        return self._process_restore_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient"><code class="flex name class">
<span>class <span class="ident">AmazonVirtualServerSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from VirtualServerSubclient Base class.
This represents an Amazon AWS virtual server subclient,
and can perform restore operations on only that subclient.</p>
<p>Initialize the Instance object for the given Virtual Server instance.
Args
class_object (backupset_object, subclient_name, subclient_id)
&ndash;
instance of the
backupset class, subclient name, subclient id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/2aca5f791188934b883a3498b8b92fae4b92ee4a/cvpysdk/subclients/virtualserver/amazon.py#L50-L556" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AmazonVirtualServerSubclient(VirtualServerSubclient):
    &#34;&#34;&#34;Derived class from VirtualServerSubclient Base class.
       This represents an Amazon AWS virtual server subclient,
       and can perform restore operations on only that subclient.

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given Virtual Server instance.
        Args
        class_object (backupset_object, subclient_name, subclient_id)  --  instance of the
                                         backupset class, subclient name, subclient id

        &#34;&#34;&#34;
        super(AmazonVirtualServerSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)
        self.diskExtension = [&#34;none&#34;]

    class disk_pattern(Enum):
        &#34;&#34;&#34;
        stores the disk pattern of all hypervisors
        &#34;&#34;&#34;
        name = &#34;name&#34;
        datastore = &#34;availabilityZone&#34;
        new_name = &#39;newName&#39;
        aws_bucket = &#39;Datastore&#39;

    def full_vm_restore_in_place(
            self,
            vm_to_restore=None,
            proxy_client=None,
            is_aws_proxy=True,
            amazon_bucket=None,
            overwrite=True,
            power_on=True,
            copy_precedence=0
    ):
        &#34;&#34;&#34;Restores the FULL Virtual machine specified in the input list
            to the location same as the actual location of the VM in VCenter.

            Args:
                vm_to_restore         (list)        --  provide the VM name to restore
                                                        default: None

                proxy_client          (basestring)  --  proxy client to be used for restore
                                                        default: proxy added in subclient

                is_aws_proxy          (basestring)  --  boolean value whether proxy resides in AWS
                                                        or not
                                                        default: True

                amazon_bucket         (basestring)  --  Amazon bucket (required when non-AWS proxy
                                                        is used)

                overwrite             (bool)        --  overwrite the existing VM
                                                        default: True

                power_on              (bool)        --  power on the  restored VM
                                                        default: True

                copy_precedence       (int)         --  copy precedence value
                                                        default: 0

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}

        # check input parameters are correct
        if vm_to_restore and not isinstance(vm_to_restore, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if copy_precedence:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        if not is_aws_proxy:
            if not amazon_bucket:
                raise SDKException(&#39;Subclient&#39;, 104)
            restore_option[&#39;datastore&#39;] = amazon_bucket

        instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]

        # set attr for all the option in restore xml from user inputs
        self._set_restore_inputs(
            restore_option,
            vm_to_restore=self._set_vm_to_restore(vm_to_restore),
            in_place=True,
            esx_server_name=instance_dict[&#34;clientName&#34;],
            volume_level_restore=1,
            unconditional_overwrite=overwrite,
            power_on=power_on,
            copy_precedence=copy_precedence,
            is_aws_proxy=is_aws_proxy,
            datacenter=None,
            securityGroups=None,
            keyPairList=None,
            resourcePoolPath=None,
            terminationProtected=None,
            optimizationEnabled=False,
            vmSize=None
        )

        request_json = self._prepare_fullvm_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def full_vm_restore_out_of_place(
            self,
            vm_to_restore=None,
            vm_display_name=None,
            proxy_client=None,
            is_aws_proxy=True,
            amazon_bucket=None,
            availability_zone=None,
            amazon_options=None,
            overwrite=True,
            power_on=True,
            copy_precedence=0
    ):
        &#34;&#34;&#34;Restores the FULL Virtual machine specified in the input list
            to the provided virtualization client along with the zone and instance type.
            If the provided client name is none then it restores the Full Virtual
            Machine to the source client and corresponding zone and instance type.

            Args:
                vm_to_restore         (basestring)  --  provide the VM name to restore
                                                        default: None

                vm_display_name       (basestring)        --  provide the new display name for the
                                                        restored VM
                                                        default: None

                proxy_client          (basestring)  --  proxy client to be used for restore
                                                        default: proxy added in subclient

                is_aws_proxy          (basestring)  --  boolean value whether proxy resides in AWS
                                                        or not
                                                        default: True

                amazon_bucket         (basestring)  --  Amazon bucket (required when non-AWS proxy
                                                        is used)

                amazon_options        (dict)        --  dict containing configuration options for
                                                        restored VM. Permissible keys are below
                    availability_zone

                    ami

                    instance_type

                    iam_role

                    termination_protection

                overwrite             (bool)        --  overwrite the existing VM
                                                        default: True

                power_on              (bool)        --  power on the  restored VM
                                                        default: True

                copy_precedence       (int)         --  copy precedence value
                                                        default: 0

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        restore_option = {}
        if not amazon_options:
            amazon_options = {}

        # check input parameters are correct
        if vm_to_restore and not isinstance(vm_to_restore, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if copy_precedence:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        # populating proxy client. It assumes the proxy controller added in instance
        # properties if not specified
        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        if vm_display_name:
            if not (isinstance(vm_to_restore, basestring) or
                    isinstance(vm_display_name, basestring)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
            restore_option[&#39;restore_new_name&#39;] = vm_display_name

        if vm_to_restore:
            vm_to_restore = [vm_to_restore]

        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        if not is_aws_proxy:
            if not amazon_bucket:
                raise SDKException(&#39;Subclient&#39;, 104)
            restore_option[&#39;datastore&#39;] = amazon_bucket

        instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]

        # set attr for all the option in restore xml from user inputs
        self._set_restore_inputs(
            restore_option,
            vm_to_restore=self._set_vm_to_restore(vm_to_restore),
            in_place=False,
            esx_server_name=instance_dict[&#34;clientName&#34;],
            volume_level_restore=1,
            unconditional_overwrite=overwrite,
            power_on=power_on,
            copy_precedence=copy_precedence,
            is_aws_proxy=is_aws_proxy,
            datacenter=None,
            resourcePoolPath=None,
            optimizationEnabled=False,
            availability_zone=availability_zone,
            esx_host=availability_zone,
            ami=amazon_options.get(&#39;ami&#39;, None),
            vmSize=amazon_options.get(&#39;instance_type&#39;, None),
            iamRole=amazon_options.get(&#39;iam_role&#39;, None),
            securityGroups=amazon_options.get(&#39;security_groups&#39;, None),
            keyPairList=amazon_options.get(&#39;keypair_list&#39;, None),
            terminationProtected=amazon_options.get(&#39;termination_protected&#39;, False),
        )

        request_json = self._prepare_fullvm_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def attach_disk_restore(
            self,
            vm_to_restore,
            destination_vm,
            proxy_client=None,
            amazon_options=None,
            overwrite=True,
            copy_precedence=0,
            destination_vm_guid=None,
            disk_prefix=None,
            availability_zone=None,
            media_agent=None,
            disk_name=None
    ):
        &#34;&#34;&#34;Restores the Attach Disk restore with  specified in the input list
            to the provided instance.

            Args:
                vm_to_restore         (basestring)  --  provide the source vm name

                destination_vm        (basestring)  --  provide the destination VM name to restore

                disk_prefix       (basestring)        --  provide the new display name for the
                                                    restored disk
                                                    default: None

                disk_name       (basestring)        --  provide the new display name for the source disk
                                                    default: None

                proxy_client          (basestring)  --  proxy client to be used for restore
                                                    default: proxy added in subclient

                destination_vm_guid     (basestring)  --  instance id of the vm
                                                            default:None

                media_agent             (basestring)  --  media agent to be used browse and restore

                amazon_options        (dict)        --  dict containing configuration options for
                                                        restored VM. Permissible keys are below
                    availability_zone

                    ami

                    instance_type

                overwrite             (bool)        --  overwrite the existing VM
                                                        default: True

                copy_precedence       (int)         --  copy precedence value
                                                        default: 0

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _attach_disk_restore_option = {}
        if not amazon_options:
            amazon_options = {}

        # check input parameters are correct
        if vm_to_restore and not isinstance(vm_to_restore, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if copy_precedence:
            _attach_disk_restore_option[&#39;copy_precedence_applicable&#39;] = True

        # populating proxy client. It assumes the proxy controller added in instance
        # properties if not specified
        if proxy_client is not None:
            _attach_disk_restore_option[&#39;client&#39;] = proxy_client

        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\&#34; + vm_ids[vm_to_restore])
        if not disk_name:
            disk_name = []
            for each_disk_path in disk_list:
                disk_name.append(each_disk_path.split(&#39;\\&#39;)[-1])

        else:
            for each_disk in disk_name:
                each_disk_path = &#34;\\&#34; + str(vm_to_restore) + &#34;\\&#34; + each_disk
                if each_disk_path not in disk_list:
                    raise SDKException(&#39;Subclient&#39;, &#39;111&#39;)

        src_item_list = []
        for each_disk in disk_name:
            src_item_list.append(&#34;\\&#34; + vm_ids[vm_to_restore] + &#34;\\&#34; + each_disk.split(&#34;\\&#34;)[-1])
        _attach_disk_restore_option[&#39;paths&#39;] = src_item_list
        if proxy_client is not None:
            _attach_disk_restore_option[&#39;client&#39;] = proxy_client
        if not destination_vm:
            destination_vm = vm_to_restore
        instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]
        _attach_disk_restore_option = self.amazon_defaults(vm_to_restore, _attach_disk_restore_option)

        # set attr for all the option in restore xml from user inputs
        self._set_restore_inputs(
            _attach_disk_restore_option,
            vm_to_restore=vm_to_restore,
            esx_server_name=instance_dict[&#34;clientName&#34;],
            volume_level_restore=6,
            unconditional_overwrite=overwrite,
            copy_precedence=copy_precedence,
            paths=src_item_list,
            datacenter=None,
            resourcePoolPath=None,
            availability_zone=availability_zone,
            esx_host=availability_zone,
            newName=destination_vm,
            newGUID=destination_vm_guid,
            disk_name_prefix=disk_prefix,
            ami=_attach_disk_restore_option.get(&#39;ami&#39;, None),
            vmSize=_attach_disk_restore_option.get(&#39;instance_type&#39;, None)
        )

        request_json = self._prepare_attach_disk_restore_json(_attach_disk_restore_option)
        return self._process_restore_response(request_json)

    def full_vm_conversion_azurerm(
            self,
            azure_client,
            vm_to_restore=None,
            resource_group=None,
            storage_account=None,
            datacenter=None,
            proxy_client=None,
            overwrite=True,
            power_on=True,
            instance_size=None,
            public_ip=False,
            restore_as_managed=False,
            copy_precedence=0,
            disk_type=None,
            restore_option=None,
            networkDisplayName=None,
            networkrsg=None,
            destsubid=None,
            subnetId=None):
        &#34;&#34;&#34;
                This converts the Hyperv VM to AzureRM
                Args:
                        vm_to_restore          (dict):     dict containing the VM name(s) to restore as
                                                           keys and the new VM name(s) as their values.
                                                           Input empty string for default VM name for
                                                           restored VM.
                                                           default: {}

                        azure_client    (basestring):      name of the AzureRM client
                                                           where the VM should be
                                                           restored.

                        resource_group   (basestring):      destination Resource group
                                                            in the AzureRM

                        storage_account  (basestring):    storage account where the
                                                          restored VM should be located
                                                          in AzureRM

                        overwrite              (bool):    overwrite the existing VM
                                                          default: True

                        power_on               (bool):    power on the  restored VM
                                                          default: True

                        instance_size    (basestring):    Instance Size of restored VM

                        public_ip              (bool):    If True, creates the Public IP of
                                                          restored VM

                        restore_as_managed     (bool):    If True, restore as Managed VM in Azure

                        copy_precedence         (int):    copy precedence value
                                                          default: 0

                        proxy_client      (basestring):   destination proxy client

                        networkDisplayName(basestring):   destination network display name

                        networkrsg        (basestring):   destination network display name&#39;s security group

                        destsubid         (basestring):   destination subscription id

                        subnetId          (basestring):   destination subet id



                    Returns:
                        object - instance of the Job class for this restore job

                    Raises:
                        SDKException:
                            if inputs are not of correct type as per definition

                            if failed to initialize job

                            if response is empty

                            if response is not success

        &#34;&#34;&#34;
        if restore_option is None:
            restore_option = {}

        if vm_to_restore and not isinstance(vm_to_restore, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not isinstance(vm_to_restore, list):
            vm_to_restore = [vm_to_restore]
        # check mandatory input parameters are correct
        if not isinstance(azure_client, basestring):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        subclient = self._set_vm_conversion_defaults(azure_client, restore_option)
        instance = subclient._backupset_object._instance_object
        if proxy_client is None:
            proxy_client = instance.server_host_name[0]

        self._set_restore_inputs(
            restore_option,
            in_place=False,
            vcenter_client=azure_client,
            datastore=storage_account,
            esx_host=resource_group,
            datacenter=datacenter,
            unconditional_overwrite=overwrite,
            client_name=proxy_client,
            power_on=power_on,
            vm_to_restore=self._set_vm_to_restore(vm_to_restore),
            copy_precedence=copy_precedence,
            createPublicIP=public_ip,
            restoreAsManagedVM=restore_as_managed,
            instanceSize=instance_size,
            volume_level_restore=1,
            destination_instance=instance.instance_name,
            backupset_client_name=instance._agent_object._client_object.client_name,
            networkDisplayName=networkDisplayName,
            networkrsg=networkrsg,
            destsubid=destsubid,
            subnetId=subnetId
        )

        request_json = self._prepare_fullvm_restore_json(restore_option)
        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient">VirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.attach_disk_restore"><code class="name flex">
<span>def <span class="ident">attach_disk_restore</span></span>(<span>self, vm_to_restore, destination_vm, proxy_client=None, amazon_options=None, overwrite=True, copy_precedence=0, destination_vm_guid=None, disk_prefix=None, availability_zone=None, media_agent=None, disk_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the Attach Disk restore with
specified in the input list
to the provided instance.</p>
<h2 id="args">Args</h2>
<p>vm_to_restore
(basestring)
&ndash;
provide the source vm name</p>
<p>destination_vm
(basestring)
&ndash;
provide the destination VM name to restore</p>
<p>disk_prefix
(basestring)
&ndash;
provide the new display name for the
restored disk
default: None</p>
<p>disk_name
(basestring)
&ndash;
provide the new display name for the source disk
default: None</p>
<p>proxy_client
(basestring)
&ndash;
proxy client to be used for restore
default: proxy added in subclient</p>
<p>destination_vm_guid
(basestring)
&ndash;
instance id of the vm
default:None</p>
<p>media_agent
(basestring)
&ndash;
media agent to be used browse and restore</p>
<p>amazon_options
(dict)
&ndash;
dict containing configuration options for
restored VM. Permissible keys are below
availability_zone</p>
<pre><code>ami

instance_type
</code></pre>
<p>overwrite
(bool)
&ndash;
overwrite the existing VM
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value
default: 0</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if inputs are not of correct type as per definition</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/2aca5f791188934b883a3498b8b92fae4b92ee4a/cvpysdk/subclients/virtualserver/amazon.py#L303-L430" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def attach_disk_restore(
        self,
        vm_to_restore,
        destination_vm,
        proxy_client=None,
        amazon_options=None,
        overwrite=True,
        copy_precedence=0,
        destination_vm_guid=None,
        disk_prefix=None,
        availability_zone=None,
        media_agent=None,
        disk_name=None
):
    &#34;&#34;&#34;Restores the Attach Disk restore with  specified in the input list
        to the provided instance.

        Args:
            vm_to_restore         (basestring)  --  provide the source vm name

            destination_vm        (basestring)  --  provide the destination VM name to restore

            disk_prefix       (basestring)        --  provide the new display name for the
                                                restored disk
                                                default: None

            disk_name       (basestring)        --  provide the new display name for the source disk
                                                default: None

            proxy_client          (basestring)  --  proxy client to be used for restore
                                                default: proxy added in subclient

            destination_vm_guid     (basestring)  --  instance id of the vm
                                                        default:None

            media_agent             (basestring)  --  media agent to be used browse and restore

            amazon_options        (dict)        --  dict containing configuration options for
                                                    restored VM. Permissible keys are below
                availability_zone

                ami

                instance_type

            overwrite             (bool)        --  overwrite the existing VM
                                                    default: True

            copy_precedence       (int)         --  copy precedence value
                                                    default: 0

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if inputs are not of correct type as per definition

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
    _attach_disk_restore_option = {}
    if not amazon_options:
        amazon_options = {}

    # check input parameters are correct
    if vm_to_restore and not isinstance(vm_to_restore, basestring):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if copy_precedence:
        _attach_disk_restore_option[&#39;copy_precedence_applicable&#39;] = True

    # populating proxy client. It assumes the proxy controller added in instance
    # properties if not specified
    if proxy_client is not None:
        _attach_disk_restore_option[&#39;client&#39;] = proxy_client

    disk_list, disk_info_dict = self.disk_level_browse(
        &#34;\\&#34; + vm_ids[vm_to_restore])
    if not disk_name:
        disk_name = []
        for each_disk_path in disk_list:
            disk_name.append(each_disk_path.split(&#39;\\&#39;)[-1])

    else:
        for each_disk in disk_name:
            each_disk_path = &#34;\\&#34; + str(vm_to_restore) + &#34;\\&#34; + each_disk
            if each_disk_path not in disk_list:
                raise SDKException(&#39;Subclient&#39;, &#39;111&#39;)

    src_item_list = []
    for each_disk in disk_name:
        src_item_list.append(&#34;\\&#34; + vm_ids[vm_to_restore] + &#34;\\&#34; + each_disk.split(&#34;\\&#34;)[-1])
    _attach_disk_restore_option[&#39;paths&#39;] = src_item_list
    if proxy_client is not None:
        _attach_disk_restore_option[&#39;client&#39;] = proxy_client
    if not destination_vm:
        destination_vm = vm_to_restore
    instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]
    _attach_disk_restore_option = self.amazon_defaults(vm_to_restore, _attach_disk_restore_option)

    # set attr for all the option in restore xml from user inputs
    self._set_restore_inputs(
        _attach_disk_restore_option,
        vm_to_restore=vm_to_restore,
        esx_server_name=instance_dict[&#34;clientName&#34;],
        volume_level_restore=6,
        unconditional_overwrite=overwrite,
        copy_precedence=copy_precedence,
        paths=src_item_list,
        datacenter=None,
        resourcePoolPath=None,
        availability_zone=availability_zone,
        esx_host=availability_zone,
        newName=destination_vm,
        newGUID=destination_vm_guid,
        disk_name_prefix=disk_prefix,
        ami=_attach_disk_restore_option.get(&#39;ami&#39;, None),
        vmSize=_attach_disk_restore_option.get(&#39;instance_type&#39;, None)
    )

    request_json = self._prepare_attach_disk_restore_json(_attach_disk_restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.full_vm_conversion_azurerm"><code class="name flex">
<span>def <span class="ident">full_vm_conversion_azurerm</span></span>(<span>self, azure_client, vm_to_restore=None, resource_group=None, storage_account=None, datacenter=None, proxy_client=None, overwrite=True, power_on=True, instance_size=None, public_ip=False, restore_as_managed=False, copy_precedence=0, disk_type=None, restore_option=None, networkDisplayName=None, networkrsg=None, destsubid=None, subnetId=None)</span>
</code></dt>
<dd>
<div class="desc"><p>This converts the Hyperv VM to AzureRM</p>
<h2 id="args">Args</h2>
<pre><code>vm_to_restore          (dict):     dict containing the VM name(s) to restore as
                                   keys and the new VM name(s) as their values.
                                   Input empty string for default VM name for
                                   restored VM.
                                   default: {}

azure_client    (basestring):      name of the AzureRM client
                                   where the VM should be
                                   restored.

resource_group   (basestring):      destination Resource group
                                    in the AzureRM

storage_account  (basestring):    storage account where the
                                  restored VM should be located
                                  in AzureRM

overwrite              (bool):    overwrite the existing VM
                                  default: True

power_on               (bool):    power on the  restored VM
                                  default: True

instance_size    (basestring):    Instance Size of restored VM

public_ip              (bool):    If True, creates the Public IP of
                                  restored VM

restore_as_managed     (bool):    If True, restore as Managed VM in Azure

copy_precedence         (int):    copy precedence value
                                  default: 0

proxy_client      (basestring):   destination proxy client

networkDisplayName(basestring):   destination network display name

networkrsg        (basestring):   destination network display name's security group

destsubid         (basestring):   destination subscription id

subnetId          (basestring):   destination subet id
</code></pre>
<p>Returns:
object - instance of the Job class for this restore job</p>
<p>Raises:
SDKException:
if inputs are not of correct type as per definition</p>
<pre><code>    if failed to initialize job

    if response is empty

    if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/2aca5f791188934b883a3498b8b92fae4b92ee4a/cvpysdk/subclients/virtualserver/amazon.py#L432-L556" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def full_vm_conversion_azurerm(
        self,
        azure_client,
        vm_to_restore=None,
        resource_group=None,
        storage_account=None,
        datacenter=None,
        proxy_client=None,
        overwrite=True,
        power_on=True,
        instance_size=None,
        public_ip=False,
        restore_as_managed=False,
        copy_precedence=0,
        disk_type=None,
        restore_option=None,
        networkDisplayName=None,
        networkrsg=None,
        destsubid=None,
        subnetId=None):
    &#34;&#34;&#34;
            This converts the Hyperv VM to AzureRM
            Args:
                    vm_to_restore          (dict):     dict containing the VM name(s) to restore as
                                                       keys and the new VM name(s) as their values.
                                                       Input empty string for default VM name for
                                                       restored VM.
                                                       default: {}

                    azure_client    (basestring):      name of the AzureRM client
                                                       where the VM should be
                                                       restored.

                    resource_group   (basestring):      destination Resource group
                                                        in the AzureRM

                    storage_account  (basestring):    storage account where the
                                                      restored VM should be located
                                                      in AzureRM

                    overwrite              (bool):    overwrite the existing VM
                                                      default: True

                    power_on               (bool):    power on the  restored VM
                                                      default: True

                    instance_size    (basestring):    Instance Size of restored VM

                    public_ip              (bool):    If True, creates the Public IP of
                                                      restored VM

                    restore_as_managed     (bool):    If True, restore as Managed VM in Azure

                    copy_precedence         (int):    copy precedence value
                                                      default: 0

                    proxy_client      (basestring):   destination proxy client

                    networkDisplayName(basestring):   destination network display name

                    networkrsg        (basestring):   destination network display name&#39;s security group

                    destsubid         (basestring):   destination subscription id

                    subnetId          (basestring):   destination subet id



                Returns:
                    object - instance of the Job class for this restore job

                Raises:
                    SDKException:
                        if inputs are not of correct type as per definition

                        if failed to initialize job

                        if response is empty

                        if response is not success

    &#34;&#34;&#34;
    if restore_option is None:
        restore_option = {}

    if vm_to_restore and not isinstance(vm_to_restore, basestring):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if not isinstance(vm_to_restore, list):
        vm_to_restore = [vm_to_restore]
    # check mandatory input parameters are correct
    if not isinstance(azure_client, basestring):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    subclient = self._set_vm_conversion_defaults(azure_client, restore_option)
    instance = subclient._backupset_object._instance_object
    if proxy_client is None:
        proxy_client = instance.server_host_name[0]

    self._set_restore_inputs(
        restore_option,
        in_place=False,
        vcenter_client=azure_client,
        datastore=storage_account,
        esx_host=resource_group,
        datacenter=datacenter,
        unconditional_overwrite=overwrite,
        client_name=proxy_client,
        power_on=power_on,
        vm_to_restore=self._set_vm_to_restore(vm_to_restore),
        copy_precedence=copy_precedence,
        createPublicIP=public_ip,
        restoreAsManagedVM=restore_as_managed,
        instanceSize=instance_size,
        volume_level_restore=1,
        destination_instance=instance.instance_name,
        backupset_client_name=instance._agent_object._client_object.client_name,
        networkDisplayName=networkDisplayName,
        networkrsg=networkrsg,
        destsubid=destsubid,
        subnetId=subnetId
    )

    request_json = self._prepare_fullvm_restore_json(restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.full_vm_restore_in_place"><code class="name flex">
<span>def <span class="ident">full_vm_restore_in_place</span></span>(<span>self, vm_to_restore=None, proxy_client=None, is_aws_proxy=True, amazon_bucket=None, overwrite=True, power_on=True, copy_precedence=0)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the FULL Virtual machine specified in the input list
to the location same as the actual location of the VM in VCenter.</p>
<h2 id="args">Args</h2>
<p>vm_to_restore
(list)
&ndash;
provide the VM name to restore
default: None</p>
<p>proxy_client
(basestring)
&ndash;
proxy client to be used for restore
default: proxy added in subclient</p>
<p>is_aws_proxy
(basestring)
&ndash;
boolean value whether proxy resides in AWS
or not
default: True</p>
<p>amazon_bucket
(basestring)
&ndash;
Amazon bucket (required when non-AWS proxy
is used)</p>
<p>overwrite
(bool)
&ndash;
overwrite the existing VM
default: True</p>
<p>power_on
(bool)
&ndash;
power on the
restored VM
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value
default: 0</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if inputs are not of correct type as per definition</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/2aca5f791188934b883a3498b8b92fae4b92ee4a/cvpysdk/subclients/virtualserver/amazon.py#L77-L168" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def full_vm_restore_in_place(
        self,
        vm_to_restore=None,
        proxy_client=None,
        is_aws_proxy=True,
        amazon_bucket=None,
        overwrite=True,
        power_on=True,
        copy_precedence=0
):
    &#34;&#34;&#34;Restores the FULL Virtual machine specified in the input list
        to the location same as the actual location of the VM in VCenter.

        Args:
            vm_to_restore         (list)        --  provide the VM name to restore
                                                    default: None

            proxy_client          (basestring)  --  proxy client to be used for restore
                                                    default: proxy added in subclient

            is_aws_proxy          (basestring)  --  boolean value whether proxy resides in AWS
                                                    or not
                                                    default: True

            amazon_bucket         (basestring)  --  Amazon bucket (required when non-AWS proxy
                                                    is used)

            overwrite             (bool)        --  overwrite the existing VM
                                                    default: True

            power_on              (bool)        --  power on the  restored VM
                                                    default: True

            copy_precedence       (int)         --  copy precedence value
                                                    default: 0

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if inputs are not of correct type as per definition

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    restore_option = {}

    # check input parameters are correct
    if vm_to_restore and not isinstance(vm_to_restore, basestring):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if copy_precedence:
        restore_option[&#39;copy_precedence_applicable&#39;] = True

    if proxy_client is not None:
        restore_option[&#39;client&#39;] = proxy_client

    if not is_aws_proxy:
        if not amazon_bucket:
            raise SDKException(&#39;Subclient&#39;, 104)
        restore_option[&#39;datastore&#39;] = amazon_bucket

    instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]

    # set attr for all the option in restore xml from user inputs
    self._set_restore_inputs(
        restore_option,
        vm_to_restore=self._set_vm_to_restore(vm_to_restore),
        in_place=True,
        esx_server_name=instance_dict[&#34;clientName&#34;],
        volume_level_restore=1,
        unconditional_overwrite=overwrite,
        power_on=power_on,
        copy_precedence=copy_precedence,
        is_aws_proxy=is_aws_proxy,
        datacenter=None,
        securityGroups=None,
        keyPairList=None,
        resourcePoolPath=None,
        terminationProtected=None,
        optimizationEnabled=False,
        vmSize=None
    )

    request_json = self._prepare_fullvm_restore_json(restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.full_vm_restore_out_of_place"><code class="name flex">
<span>def <span class="ident">full_vm_restore_out_of_place</span></span>(<span>self, vm_to_restore=None, vm_display_name=None, proxy_client=None, is_aws_proxy=True, amazon_bucket=None, availability_zone=None, amazon_options=None, overwrite=True, power_on=True, copy_precedence=0)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the FULL Virtual machine specified in the input list
to the provided virtualization client along with the zone and instance type.
If the provided client name is none then it restores the Full Virtual
Machine to the source client and corresponding zone and instance type.</p>
<h2 id="args">Args</h2>
<p>vm_to_restore
(basestring)
&ndash;
provide the VM name to restore
default: None</p>
<p>vm_display_name
(basestring)
&ndash;
provide the new display name for the
restored VM
default: None</p>
<p>proxy_client
(basestring)
&ndash;
proxy client to be used for restore
default: proxy added in subclient</p>
<p>is_aws_proxy
(basestring)
&ndash;
boolean value whether proxy resides in AWS
or not
default: True</p>
<p>amazon_bucket
(basestring)
&ndash;
Amazon bucket (required when non-AWS proxy
is used)</p>
<p>amazon_options
(dict)
&ndash;
dict containing configuration options for
restored VM. Permissible keys are below
availability_zone</p>
<pre><code>ami

instance_type

iam_role

termination_protection
</code></pre>
<p>overwrite
(bool)
&ndash;
overwrite the existing VM
default: True</p>
<p>power_on
(bool)
&ndash;
power on the
restored VM
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value
default: 0</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if inputs are not of correct type as per definition</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/2aca5f791188934b883a3498b8b92fae4b92ee4a/cvpysdk/subclients/virtualserver/amazon.py#L170-L301" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def full_vm_restore_out_of_place(
        self,
        vm_to_restore=None,
        vm_display_name=None,
        proxy_client=None,
        is_aws_proxy=True,
        amazon_bucket=None,
        availability_zone=None,
        amazon_options=None,
        overwrite=True,
        power_on=True,
        copy_precedence=0
):
    &#34;&#34;&#34;Restores the FULL Virtual machine specified in the input list
        to the provided virtualization client along with the zone and instance type.
        If the provided client name is none then it restores the Full Virtual
        Machine to the source client and corresponding zone and instance type.

        Args:
            vm_to_restore         (basestring)  --  provide the VM name to restore
                                                    default: None

            vm_display_name       (basestring)        --  provide the new display name for the
                                                    restored VM
                                                    default: None

            proxy_client          (basestring)  --  proxy client to be used for restore
                                                    default: proxy added in subclient

            is_aws_proxy          (basestring)  --  boolean value whether proxy resides in AWS
                                                    or not
                                                    default: True

            amazon_bucket         (basestring)  --  Amazon bucket (required when non-AWS proxy
                                                    is used)

            amazon_options        (dict)        --  dict containing configuration options for
                                                    restored VM. Permissible keys are below
                availability_zone

                ami

                instance_type

                iam_role

                termination_protection

            overwrite             (bool)        --  overwrite the existing VM
                                                    default: True

            power_on              (bool)        --  power on the  restored VM
                                                    default: True

            copy_precedence       (int)         --  copy precedence value
                                                    default: 0

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if inputs are not of correct type as per definition

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    restore_option = {}
    if not amazon_options:
        amazon_options = {}

    # check input parameters are correct
    if vm_to_restore and not isinstance(vm_to_restore, basestring):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if copy_precedence:
        restore_option[&#39;copy_precedence_applicable&#39;] = True

    # populating proxy client. It assumes the proxy controller added in instance
    # properties if not specified
    if proxy_client is not None:
        restore_option[&#39;client&#39;] = proxy_client

    if vm_display_name:
        if not (isinstance(vm_to_restore, basestring) or
                isinstance(vm_display_name, basestring)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        restore_option[&#39;restore_new_name&#39;] = vm_display_name

    if vm_to_restore:
        vm_to_restore = [vm_to_restore]

    if proxy_client is not None:
        restore_option[&#39;client&#39;] = proxy_client

    if not is_aws_proxy:
        if not amazon_bucket:
            raise SDKException(&#39;Subclient&#39;, 104)
        restore_option[&#39;datastore&#39;] = amazon_bucket

    instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]

    # set attr for all the option in restore xml from user inputs
    self._set_restore_inputs(
        restore_option,
        vm_to_restore=self._set_vm_to_restore(vm_to_restore),
        in_place=False,
        esx_server_name=instance_dict[&#34;clientName&#34;],
        volume_level_restore=1,
        unconditional_overwrite=overwrite,
        power_on=power_on,
        copy_precedence=copy_precedence,
        is_aws_proxy=is_aws_proxy,
        datacenter=None,
        resourcePoolPath=None,
        optimizationEnabled=False,
        availability_zone=availability_zone,
        esx_host=availability_zone,
        ami=amazon_options.get(&#39;ami&#39;, None),
        vmSize=amazon_options.get(&#39;instance_type&#39;, None),
        iamRole=amazon_options.get(&#39;iam_role&#39;, None),
        securityGroups=amazon_options.get(&#39;security_groups&#39;, None),
        keyPairList=amazon_options.get(&#39;keypair_list&#39;, None),
        terminationProtected=amazon_options.get(&#39;termination_protected&#39;, False),
    )

    request_json = self._prepare_fullvm_restore_json(restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient">VirtualServerSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.allow_multiple_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.amazon_defaults" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.amazon_defaults">amazon_defaults</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.backup" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse_in_time" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse_in_time">browse_in_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.cbtvalue" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.cbtvalue">cbtvalue</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.content" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.data_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.deduplication_options" href="../../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.description" href="../../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_level_browse" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_level_browse">disk_level_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_pattern" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_pattern">disk_pattern</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.display_name" href="../../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.enable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.enable_backup_at_time" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.enable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.enable_trueup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.enable_trueup_days" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.encryption_flag" href="../../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.exclude_from_sla" href="../../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.find" href="../../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.find_latest_job" href="../../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.get_ma_associated_storagepolicy" href="../../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.get_nics_from_browse" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.get_nics_from_browse">get_nics_from_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_file_restore" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_file_restore">guest_file_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_files_browse" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_files_browse">guest_files_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.instance_proxy" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.instance_proxy">instance_proxy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_blocklevel_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_intelli_snap_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_on_demand_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_trueup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.last_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.list_media" href="../../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.live_sync" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.live_sync">live_sync</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.metadata" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.metadata">metadata</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.name" href="../../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.network_agent" href="../../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.next_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.parse_nics_xml" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.parse_nics_xml">parse_nics_xml</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.plan" href="../../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.preview_content" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.preview_content">preview_content</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.properties" href="../../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.read_buffer_size" href="../../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.refresh" href="../../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.restore_in_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.restore_out_of_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_attach_disk_restore_options" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_attach_disk_restore_options">set_advanced_attach_disk_restore_options</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_vm_restore_options" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_vm_restore_options">set_advanced_vm_restore_options</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_backup_nodes" href="../../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.snapshot_engine_name" href="../../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.software_compression" href="../../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.storage_ma" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.storage_ma_id" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.storage_policy" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_guid" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_id" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_name" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_proxy" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_proxy">subclient_proxy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.unset_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.update_properties" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.update_properties">update_properties</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_diskfilter" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_diskfilter">vm_diskfilter</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse">vm_files_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse_in_time" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse_in_time">vm_files_browse_in_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_filter" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_filter">vm_filter</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.virtualserver" href="index.html">cvpysdk.subclients.virtualserver</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient" href="#cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient">AmazonVirtualServerSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.attach_disk_restore" href="#cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.attach_disk_restore">attach_disk_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.full_vm_conversion_azurerm" href="#cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.full_vm_conversion_azurerm">full_vm_conversion_azurerm</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.full_vm_restore_in_place" href="#cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.full_vm_restore_in_place">full_vm_restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.full_vm_restore_out_of_place" href="#cvpysdk.subclients.virtualserver.amazon.AmazonVirtualServerSubclient.full_vm_restore_out_of_place">full_vm_restore_out_of_place</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>