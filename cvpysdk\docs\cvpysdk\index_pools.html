<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.index_pools API documentation</title>
<meta name="description" content="File for performing index pool related operations on the commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.index_pools</code></h1>
</header>
<section id="section-intro">
<p>File for performing index pool related operations on the commcell</p>
<p>IndexPools and IndexPool are 2 classes defined in this file</p>
<p>IndexPools:
Class for representing all the index pools associated with the commcell</p>
<p>IndexPool:
Class for a instance of a single index pool of the commcell</p>
<h1 id="indexpools">IndexPools</h1>
<pre><code>__init__()                          --  initialize object of IndexPools class associated with
the commcell

__str()                             --  returns all the Index pools of the commcell

__repr__()                          --  returns the string to represent the instance

__getitem__()                       --  returns the details of index pool for given pool name

_get_all_index_pools()              --  gets detail of all index pools using REST API call

_response_not_success()             --  raise exception when response is not 200

get()                               --  return an IndexPool object for given pool name

has_pool()                          --  returns whether the index pool is present or not in
the commcell

add()                            --  creates a new Index pool to the commcell

delete()                            --  deletes the index pool associated to commcell

refresh()                           --  refresh the index pools details associated with commcell
</code></pre>
<h2 id="indexpools-attributes">Indexpools Attributes</h2>
<pre><code>**all_index_pools**                 --  returns the dictionary consisting of all the index
pools associated with the commcell and there details
</code></pre>
<h1 id="indexpool">IndexPool</h1>
<pre><code>__init__()                          --  initialize object of IndexPool class

__repr__()                          --  returns the string to represent the instance

_response_not_success()             --  raise exception when response is not 200

modify_node()                       --  modify/add a node to the index pool

node_info()                         --  returns a dict consisting details of node present in the pool

refresh()                           --  refresh the index pool details associated with commcell
</code></pre>
<h2 id="indexpool-attributes">Indexpool Attributes</h2>
<pre><code>**pool_id**                         --  returns the pseudo client id for index pool cloud

**cloud_id**                        --  returns the cloud id for index pool

**node_names**                      --  returns a list of names of all the nodes present in pool

**hac_cluster**                     --  returns the hac cluster name

**pool_name**                       --  returns the client name for index pool
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L1-L521" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for performing index pool related operations on the commcell

IndexPools and IndexPool are 2 classes defined in this file

IndexPools:   Class for representing all the index pools associated with the commcell

IndexPool:    Class for a instance of a single index pool of the commcell


IndexPools
============

    __init__()                          --  initialize object of IndexPools class associated with
    the commcell

    __str()                             --  returns all the Index pools of the commcell

    __repr__()                          --  returns the string to represent the instance

    __getitem__()                       --  returns the details of index pool for given pool name

    _get_all_index_pools()              --  gets detail of all index pools using REST API call

    _response_not_success()             --  raise exception when response is not 200

    get()                               --  return an IndexPool object for given pool name

    has_pool()                          --  returns whether the index pool is present or not in
    the commcell

    add()                            --  creates a new Index pool to the commcell

    delete()                            --  deletes the index pool associated to commcell

    refresh()                           --  refresh the index pools details associated with commcell

IndexPools Attributes
-----------------------

    **all_index_pools**                 --  returns the dictionary consisting of all the index
    pools associated with the commcell and there details


IndexPool
=========

    __init__()                          --  initialize object of IndexPool class

    __repr__()                          --  returns the string to represent the instance

    _response_not_success()             --  raise exception when response is not 200

    modify_node()                       --  modify/add a node to the index pool

    node_info()                         --  returns a dict consisting details of node present in the pool

    refresh()                           --  refresh the index pool details associated with commcell

IndexPool Attributes
----------------------

    **pool_id**                         --  returns the pseudo client id for index pool cloud

    **cloud_id**                        --  returns the cloud id for index pool

    **node_names**                      --  returns a list of names of all the nodes present in pool

    **hac_cluster**                     --  returns the hac cluster name

    **pool_name**                       --  returns the client name for index pool

&#34;&#34;&#34;

from copy import deepcopy
from .exception import SDKException
from .datacube.constants import IndexServerConstants


class IndexPools(object):
    &#34;&#34;&#34;Class for representing all the Index pools associated with the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the IndexPools class

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of IndexPools class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._all_index_pools = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all Index pools of the commcell.

                Returns:
                    str - string of all the index pools associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Index pool Name&#39;)
        index = 1
        for pool_name in self.all_index_pools:
            representation_string += &#39;{:^5}\t{:^20}\n&#39;.format(
                index, pool_name)
            index += 1
        return representation_string

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the IndexPools class.&#34;&#34;&#34;
        return &#34;IndexPools class instance for Commcell&#34;

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the details of index pool for given index pool name

            Args:
                value   (str)       --  name of index pool

            Returns:
                dict    -   details of the index pool

            Raises:
                Index pool not found
        &#34;&#34;&#34;
        value = value.lower()
        if value in self.all_index_pools:
            return {&#34;pool_name&#34;: value, &#34;pool_id&#34;: self.all_index_pools[value]}
        raise SDKException(&#39;IndexPools&#39;, &#39;102&#39;)

    def _get_all_index_pools(self):
        &#34;&#34;&#34;Method to get details of all the index pools present in commcell

            Raises:
                SDKException:
                    Response was not success

                    Response was empty
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;GET_ALL_CLIENTS&#39;])
        if flag:
            if response.json() and &#39;clientProperties&#39; in response.json():
                for dictionary in response.json()[&#39;clientProperties&#39;]:
                    if dictionary[&#39;clientProps&#39;][&#39;clusterType&#39;] == 14:
                        temp_name = dictionary[&#39;client&#39;][&#39;clientEntity&#39;][&#39;clientName&#39;].lower()
                        temp_id = int(dictionary[&#39;client&#39;][&#39;clientEntity&#39;][&#39;clientId&#39;])
                        self._all_index_pools[temp_name] = temp_id
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)
            Args:
                response    (object)    -   response object

            Raises:
                SDKException:
                    Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def refresh(self):
        &#34;&#34;&#34;Refreshes the properties of IndexPools class&#34;&#34;&#34;
        self._commcell_object.clients.refresh()
        self._all_index_pools = {}
        self._get_all_index_pools()

    @property
    def all_index_pools(self):
        &#34;&#34;&#34;Returns a dict consisting details of all index pools

            Returns:
                dict    -   dictionary consisting details for all index pools
                Sample:
                    {
                        &lt;pool_name_1&gt; : &lt;pool_id_1&gt;,
                        &lt;pool_name_2&gt; : &lt;pool_id_2&gt;
                    }
        &#34;&#34;&#34;
        return self._all_index_pools

    def get(self, pool_name):
        &#34;&#34;&#34;Returns the IndexPool class object with given pool_name

        Args:
            pool_name       (int/str)       --  Index pool name present in commcell

        Returns:
            object  -   instance of IndexPool class

        Raises:
            SDKExecption:
                Data type of the input(s) is not valid

                Index pool not found
        &#34;&#34;&#34;
        if isinstance(pool_name, int):
            for index_pool_name in self.all_index_pools:
                if self.all_index_pools[index_pool_name] == pool_name:
                    return IndexPool(self._commcell_object, index_pool_name)
        elif isinstance(pool_name, str):
            if pool_name.lower() in self.all_index_pools:
                return IndexPool(self._commcell_object, pool_name.lower())
        else:
            raise SDKException(&#39;IndexPools&#39;, &#39;101&#39;)
        raise SDKException(&#39;IndexPools&#39;, &#39;102&#39;)

    def has_pool(self, pool_name):
        &#34;&#34;&#34;Returns whether a index pool is present in the commcell or not

        Args:
            pool_name       (str)       --  Index pool name

        Returns:
            bool    -   True if index pool with given pool_name is present else False
        &#34;&#34;&#34;
        return pool_name.lower() in self.all_index_pools

    def add(self, pool_name, node_names, hac_name):
        &#34;&#34;&#34;Creates a new Index pool within the commcell

        Args:
            pool_name       (str)   --  Name for the index pool
            node_names      (list)  --  List of strings of all the index pool node names
            hac_name        (str)   --  Name of the HAC cluster to be used while creating pool

        Raises:
            SDKExecption:
                Data type of the input(s) is not valid.

                Response was not success.

                Response was empty.

        Returns:
            object  -   Returns a object of class IndexPool

        &#34;&#34;&#34;
        if not (isinstance(pool_name, str) and isinstance(node_names, list)
                and isinstance(hac_name, str)):
            raise SDKException(&#39;IndexPools&#39;, &#39;101&#39;)
        req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
        del req_json[&#39;solrCloudInfo&#39;]
        del req_json[&#39;cloudMetaInfos&#39;]
        req_json[&#39;type&#39;] = 9
        req_json[&#39;cloudInfoEntity&#39;] = {
            &#34;cloudName&#34;: pool_name,
            &#34;cloudDisplayName&#34;: pool_name
        }
        req_json[&#39;solrCloudPoolInfo&#39;] = {
            &#39;zookerEntity&#39;: {
                &#34;_type_&#34;: 28,
                &#34;clientGroupId&#34;: int(self._commcell_object.hac_clusters.get(hac_name).cluster_id),
                &#34;clientGroupName&#34;: hac_name,
                &#34;flags&#34;: {
                    &#34;include&#34;: False
                }
            }
        }
        for node_name in node_names:
            node_obj = self._commcell_object.clients.get(node_name)
            node_data = {
                &#34;opType&#34;: IndexServerConstants.OPERATION_ADD,
                &#34;nodeClientEntity&#34;: {
                    &#34;hostName&#34;: node_obj.client_hostname,
                    &#34;clientId&#34;: int(node_obj.client_id),
                    &#34;clientName&#34;: node_obj.client_name,
                    &#34;_type_&#34;: 3
                },
                &#34;nodeMetaInfos&#34;: [
                    {
                        &#34;name&#34;: &#34;ISENABLED&#34;,
                        &#34;value&#34;: &#34;false&#34;
                    },
                    {
                        &#34;name&#34;: &#34;JVMMAXMEMORY&#34;,
                        &#34;value&#34;: &#34;8191&#34;
                    },
                    {
                        &#34;name&#34;: &#34;PORTNO&#34;,
                        &#34;value&#34;: &#34;20000&#34;
                    },
                    {
                        &#34;name&#34;: &#34;URL&#34;,
                        &#34;value&#34;: &#34;&#34;
                    },
                    {
                        &#34;name&#34;: &#34;INDEXLOCATION&#34;,
                        &#34;value&#34;: &#34;NA&#34;
                    }
                ]
            }
            req_json[&#39;cloudNodes&#39;].append(node_data)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_CREATE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json():
                if response.json()[&#39;genericResp&#39;] == {} and \
                        &#39;cloudId&#39; in response.json():
                    self.refresh()
                    return IndexPool(self._commcell_object, pool_name)
                o_str = &#39;Failed to create index pool. Error: &#34;{0}&#34;&#39;.format(
                    response.json()[&#39;genericResp&#39;])
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        self._response_not_success(response)

    def delete(self, pool_name):
        &#34;&#34;&#34;Deletes an existing index pool cloud from commcell

        Args:
            pool_name       (str)   --  Index pool cloud name

        Returns:
            None

        Raises:
              SDKExecption:
                  Data type of the input(s) is not valid.

                  Response was not success.

                  Response was empty.
        &#34;&#34;&#34;
        if not isinstance(pool_name, str):
            raise SDKException(&#39;IndexPools&#39;, &#39;101&#39;)
        client = self.get(pool_name)
        cloud_id = client.cloud_id
        req_json = IndexServerConstants.REQUEST_JSON.copy()
        req_json[&#34;opType&#34;] = IndexServerConstants.OPERATION_DELETE
        req_json[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = cloud_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_DELETE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json() \
                    and &#39;errorCode&#39; not in response.json()[&#39;genericResp&#39;]:
                self.refresh()
                return
            if response.json() and &#39;genericResp&#39; in response.json():
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, response.json()[&#39;genericResp&#39;].get(
                        &#39;errorMessage&#39;, &#39;&#39;))
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        self._response_not_success(response)


class IndexPool(object):
    &#34;&#34;&#34;Class for performing index pool operations on a specific index pool&#34;&#34;&#34;

    def __init__(self, commcell_obj, pool_name, pool_id=None):
        &#34;&#34;&#34;Initializes the IndexPool class instance

        Args:
            commcell_obj        (object)    --  Instance of class Commcell
            pool_name           (str)       --  Index pool name
            pool_id             (int)       --  Index pool client id
                default:
                    None

        Returns:
            object  -   instance of the IndexPool class

        &#34;&#34;&#34;
        self.commcell = commcell_obj
        self._pool_name = pool_name
        self._pool_id = pool_id
        self.pool_client = None
        self.pool_properties = None
        self.pool_nodes = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the IndexPool class.&#34;&#34;&#34;
        return &#34;IndexPool class instance for Commcell&#34;

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

                Raises:
                    SDKException:
                        Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self.commcell._update_response_(
                response.text))

    def refresh(self):
        &#34;&#34;&#34;Refreshes properties of IndexPool class&#34;&#34;&#34;
        self.commcell.clients.refresh()
        if not self.commcell.clients.has_client(self.pool_name):
            raise SDKException(&#39;IndexPools&#39;, &#39;102&#39;)
        self.pool_client = self.commcell.clients.get(self.pool_name)
        self._pool_id = self.pool_client.client_id
        self.pool_properties = self.pool_client.\
            properties[&#39;pseudoClientInfo&#39;][&#39;distributedClusterInstanceProperties&#39;][&#39;clusterConfig&#39;][&#39;cloudInfo&#39;]
        self.pool_nodes = self.pool_properties[&#39;cloudNodes&#39;]

    def node_info(self, node_name):
        &#34;&#34;&#34;Returns the index pool node information

            Args:
                node_name       (str)       --  index pool node name

            Returns:
                dict    -   dictionary consisting details of index pool node

            Raises:
                SDKException:
                    Index pool node not found

        &#34;&#34;&#34;
        for node_info in self.pool_nodes:
            if node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;].lower() == node_name.lower():
                return node_info
        raise SDKException(&#39;IndexPools&#39;, &#39;103&#39;)

    def modify_node(self, node_name,
                    operation_type=IndexServerConstants.OPERATION_EDIT,
                    node_params=None):
        &#34;&#34;&#34;Method to modify the pool node details

        Args:
            node_name           (str)   --  index pool node name
            operation_type      (int)   --  operation type (1, 2, 3)
                                            1 - Adds a new node
                                            2 - Removes existing node
                                            3 - Edits the existing node (default)
            node_params         (list)  --  list of all the properties for the index pool node
                                            for example:
                                                [{
                                                    &#34;name&#34;: &lt;property_name&gt;,
                                                    &#34;value&#34;: &lt;property_value&gt;
                                                },
                                                ]
        Raises:
            SDKException:
                Response was not success.
                Response was empty.

        &#34;&#34;&#34;
        req_json = {
            &#39;cloudId&#39;: self.cloud_id,
            &#39;type&#39;: 9,
            &#39;nodes&#39;: [{
                &#39;status&#39;: 1,
                &#39;opType&#39;: operation_type,
                &#39;nodeClientEntity&#39;: {
                    &#39;clientId&#39;: int(self.commcell.clients[node_name][&#39;id&#39;]),
                    &#39;clientName&#39;: node_name
                },
                &#39;nodeMetaInfos&#39;: []
            }]
        }
        if node_params:
            req_json[&#39;nodes&#39;][0][&#39;nodeMetaInfos&#39;] = node_params
        flag, response = self.commcell._cvpysdk_object.make_request(
            &#39;POST&#39;, self.commcell._services[&#39;CLOUD_NODE_UPDATE&#39;],
            req_json
        )
        if flag:
            if response.json() is not None:
                if &#39;errorCode&#39; not in response.json():
                    self.refresh()
                    return
        self._response_not_success(response)

    @property
    def pool_id(self):
        &#34;&#34;&#34;Returns the index pool client id&#34;&#34;&#34;
        return self._pool_id

    @property
    def cloud_id(self):
        &#34;&#34;&#34;Returns the index pool cloud id&#34;&#34;&#34;
        return self.pool_properties[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;]

    @property
    def pool_name(self):
        &#34;&#34;&#34;Returns index pool name&#34;&#34;&#34;
        return self._pool_name

    @property
    def node_names(self):
        &#34;&#34;&#34;Returns a list of index pool node names&#34;&#34;&#34;
        result = []
        for node_info in self.pool_nodes:
            result.append(node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;])
        return result

    @property
    def hac_cluster(self):
        &#34;&#34;&#34;Returns the hac cluster name assigned to the index pool&#34;&#34;&#34;
        return self.pool_properties[&#39;solrCloudPoolInfo&#39;][&#39;zookerEntity&#39;][&#39;clientGroupName&#39;]</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.index_pools.IndexPool"><code class="flex name class">
<span>class <span class="ident">IndexPool</span></span>
<span>(</span><span>commcell_obj, pool_name, pool_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing index pool operations on a specific index pool</p>
<p>Initializes the IndexPool class instance</p>
<h2 id="args">Args</h2>
<p>commcell_obj
(object)
&ndash;
Instance of class Commcell
pool_name
(str)
&ndash;
Index pool name
pool_id
(int)
&ndash;
Index pool client id
default:
None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the IndexPool class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L373-L521" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class IndexPool(object):
    &#34;&#34;&#34;Class for performing index pool operations on a specific index pool&#34;&#34;&#34;

    def __init__(self, commcell_obj, pool_name, pool_id=None):
        &#34;&#34;&#34;Initializes the IndexPool class instance

        Args:
            commcell_obj        (object)    --  Instance of class Commcell
            pool_name           (str)       --  Index pool name
            pool_id             (int)       --  Index pool client id
                default:
                    None

        Returns:
            object  -   instance of the IndexPool class

        &#34;&#34;&#34;
        self.commcell = commcell_obj
        self._pool_name = pool_name
        self._pool_id = pool_id
        self.pool_client = None
        self.pool_properties = None
        self.pool_nodes = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the IndexPool class.&#34;&#34;&#34;
        return &#34;IndexPool class instance for Commcell&#34;

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

                Raises:
                    SDKException:
                        Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self.commcell._update_response_(
                response.text))

    def refresh(self):
        &#34;&#34;&#34;Refreshes properties of IndexPool class&#34;&#34;&#34;
        self.commcell.clients.refresh()
        if not self.commcell.clients.has_client(self.pool_name):
            raise SDKException(&#39;IndexPools&#39;, &#39;102&#39;)
        self.pool_client = self.commcell.clients.get(self.pool_name)
        self._pool_id = self.pool_client.client_id
        self.pool_properties = self.pool_client.\
            properties[&#39;pseudoClientInfo&#39;][&#39;distributedClusterInstanceProperties&#39;][&#39;clusterConfig&#39;][&#39;cloudInfo&#39;]
        self.pool_nodes = self.pool_properties[&#39;cloudNodes&#39;]

    def node_info(self, node_name):
        &#34;&#34;&#34;Returns the index pool node information

            Args:
                node_name       (str)       --  index pool node name

            Returns:
                dict    -   dictionary consisting details of index pool node

            Raises:
                SDKException:
                    Index pool node not found

        &#34;&#34;&#34;
        for node_info in self.pool_nodes:
            if node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;].lower() == node_name.lower():
                return node_info
        raise SDKException(&#39;IndexPools&#39;, &#39;103&#39;)

    def modify_node(self, node_name,
                    operation_type=IndexServerConstants.OPERATION_EDIT,
                    node_params=None):
        &#34;&#34;&#34;Method to modify the pool node details

        Args:
            node_name           (str)   --  index pool node name
            operation_type      (int)   --  operation type (1, 2, 3)
                                            1 - Adds a new node
                                            2 - Removes existing node
                                            3 - Edits the existing node (default)
            node_params         (list)  --  list of all the properties for the index pool node
                                            for example:
                                                [{
                                                    &#34;name&#34;: &lt;property_name&gt;,
                                                    &#34;value&#34;: &lt;property_value&gt;
                                                },
                                                ]
        Raises:
            SDKException:
                Response was not success.
                Response was empty.

        &#34;&#34;&#34;
        req_json = {
            &#39;cloudId&#39;: self.cloud_id,
            &#39;type&#39;: 9,
            &#39;nodes&#39;: [{
                &#39;status&#39;: 1,
                &#39;opType&#39;: operation_type,
                &#39;nodeClientEntity&#39;: {
                    &#39;clientId&#39;: int(self.commcell.clients[node_name][&#39;id&#39;]),
                    &#39;clientName&#39;: node_name
                },
                &#39;nodeMetaInfos&#39;: []
            }]
        }
        if node_params:
            req_json[&#39;nodes&#39;][0][&#39;nodeMetaInfos&#39;] = node_params
        flag, response = self.commcell._cvpysdk_object.make_request(
            &#39;POST&#39;, self.commcell._services[&#39;CLOUD_NODE_UPDATE&#39;],
            req_json
        )
        if flag:
            if response.json() is not None:
                if &#39;errorCode&#39; not in response.json():
                    self.refresh()
                    return
        self._response_not_success(response)

    @property
    def pool_id(self):
        &#34;&#34;&#34;Returns the index pool client id&#34;&#34;&#34;
        return self._pool_id

    @property
    def cloud_id(self):
        &#34;&#34;&#34;Returns the index pool cloud id&#34;&#34;&#34;
        return self.pool_properties[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;]

    @property
    def pool_name(self):
        &#34;&#34;&#34;Returns index pool name&#34;&#34;&#34;
        return self._pool_name

    @property
    def node_names(self):
        &#34;&#34;&#34;Returns a list of index pool node names&#34;&#34;&#34;
        result = []
        for node_info in self.pool_nodes:
            result.append(node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;])
        return result

    @property
    def hac_cluster(self):
        &#34;&#34;&#34;Returns the hac cluster name assigned to the index pool&#34;&#34;&#34;
        return self.pool_properties[&#39;solrCloudPoolInfo&#39;][&#39;zookerEntity&#39;][&#39;clientGroupName&#39;]</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.index_pools.IndexPool.cloud_id"><code class="name">var <span class="ident">cloud_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the index pool cloud id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L500-L503" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cloud_id(self):
    &#34;&#34;&#34;Returns the index pool cloud id&#34;&#34;&#34;
    return self.pool_properties[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPool.hac_cluster"><code class="name">var <span class="ident">hac_cluster</span></code></dt>
<dd>
<div class="desc"><p>Returns the hac cluster name assigned to the index pool</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L518-L521" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def hac_cluster(self):
    &#34;&#34;&#34;Returns the hac cluster name assigned to the index pool&#34;&#34;&#34;
    return self.pool_properties[&#39;solrCloudPoolInfo&#39;][&#39;zookerEntity&#39;][&#39;clientGroupName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPool.node_names"><code class="name">var <span class="ident">node_names</span></code></dt>
<dd>
<div class="desc"><p>Returns a list of index pool node names</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L510-L516" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def node_names(self):
    &#34;&#34;&#34;Returns a list of index pool node names&#34;&#34;&#34;
    result = []
    for node_info in self.pool_nodes:
        result.append(node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;])
    return result</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPool.pool_id"><code class="name">var <span class="ident">pool_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the index pool client id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L495-L498" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pool_id(self):
    &#34;&#34;&#34;Returns the index pool client id&#34;&#34;&#34;
    return self._pool_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPool.pool_name"><code class="name">var <span class="ident">pool_name</span></code></dt>
<dd>
<div class="desc"><p>Returns index pool name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L505-L508" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pool_name(self):
    &#34;&#34;&#34;Returns index pool name&#34;&#34;&#34;
    return self._pool_name</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.index_pools.IndexPool.modify_node"><code class="name flex">
<span>def <span class="ident">modify_node</span></span>(<span>self, node_name, operation_type=3, node_params=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to modify the pool node details</p>
<h2 id="args">Args</h2>
<p>node_name
(str)
&ndash;
index pool node name
operation_type
(int)
&ndash;
operation type (1, 2, 3)
1 - Adds a new node
2 - Removes existing node
3 - Edits the existing node (default)
node_params
(list)
&ndash;
list of all the properties for the index pool node
for example:
[{
"name": <property_name>,
"value": <property_value>
},
]</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Response was not success.
Response was empty.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L445-L493" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_node(self, node_name,
                operation_type=IndexServerConstants.OPERATION_EDIT,
                node_params=None):
    &#34;&#34;&#34;Method to modify the pool node details

    Args:
        node_name           (str)   --  index pool node name
        operation_type      (int)   --  operation type (1, 2, 3)
                                        1 - Adds a new node
                                        2 - Removes existing node
                                        3 - Edits the existing node (default)
        node_params         (list)  --  list of all the properties for the index pool node
                                        for example:
                                            [{
                                                &#34;name&#34;: &lt;property_name&gt;,
                                                &#34;value&#34;: &lt;property_value&gt;
                                            },
                                            ]
    Raises:
        SDKException:
            Response was not success.
            Response was empty.

    &#34;&#34;&#34;
    req_json = {
        &#39;cloudId&#39;: self.cloud_id,
        &#39;type&#39;: 9,
        &#39;nodes&#39;: [{
            &#39;status&#39;: 1,
            &#39;opType&#39;: operation_type,
            &#39;nodeClientEntity&#39;: {
                &#39;clientId&#39;: int(self.commcell.clients[node_name][&#39;id&#39;]),
                &#39;clientName&#39;: node_name
            },
            &#39;nodeMetaInfos&#39;: []
        }]
    }
    if node_params:
        req_json[&#39;nodes&#39;][0][&#39;nodeMetaInfos&#39;] = node_params
    flag, response = self.commcell._cvpysdk_object.make_request(
        &#39;POST&#39;, self.commcell._services[&#39;CLOUD_NODE_UPDATE&#39;],
        req_json
    )
    if flag:
        if response.json() is not None:
            if &#39;errorCode&#39; not in response.json():
                self.refresh()
                return
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPool.node_info"><code class="name flex">
<span>def <span class="ident">node_info</span></span>(<span>self, node_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the index pool node information</p>
<h2 id="args">Args</h2>
<p>node_name
(str)
&ndash;
index pool node name</p>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary consisting details of index pool node</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Index pool node not found</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L426-L443" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def node_info(self, node_name):
    &#34;&#34;&#34;Returns the index pool node information

        Args:
            node_name       (str)       --  index pool node name

        Returns:
            dict    -   dictionary consisting details of index pool node

        Raises:
            SDKException:
                Index pool node not found

    &#34;&#34;&#34;
    for node_info in self.pool_nodes:
        if node_info[&#39;nodeClientEntity&#39;][&#39;clientName&#39;].lower() == node_name.lower():
            return node_info
    raise SDKException(&#39;IndexPools&#39;, &#39;103&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPool.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refreshes properties of IndexPool class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L415-L424" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refreshes properties of IndexPool class&#34;&#34;&#34;
    self.commcell.clients.refresh()
    if not self.commcell.clients.has_client(self.pool_name):
        raise SDKException(&#39;IndexPools&#39;, &#39;102&#39;)
    self.pool_client = self.commcell.clients.get(self.pool_name)
    self._pool_id = self.pool_client.client_id
    self.pool_properties = self.pool_client.\
        properties[&#39;pseudoClientInfo&#39;][&#39;distributedClusterInstanceProperties&#39;][&#39;clusterConfig&#39;][&#39;cloudInfo&#39;]
    self.pool_nodes = self.pool_properties[&#39;cloudNodes&#39;]</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.index_pools.IndexPools"><code class="flex name class">
<span>class <span class="ident">IndexPools</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the Index pools associated with the commcell</p>
<p>Initialize object of the IndexPools class</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of IndexPools class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L95-L370" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class IndexPools(object):
    &#34;&#34;&#34;Class for representing all the Index pools associated with the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the IndexPools class

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of IndexPools class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._all_index_pools = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all Index pools of the commcell.

                Returns:
                    str - string of all the index pools associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Index pool Name&#39;)
        index = 1
        for pool_name in self.all_index_pools:
            representation_string += &#39;{:^5}\t{:^20}\n&#39;.format(
                index, pool_name)
            index += 1
        return representation_string

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the IndexPools class.&#34;&#34;&#34;
        return &#34;IndexPools class instance for Commcell&#34;

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the details of index pool for given index pool name

            Args:
                value   (str)       --  name of index pool

            Returns:
                dict    -   details of the index pool

            Raises:
                Index pool not found
        &#34;&#34;&#34;
        value = value.lower()
        if value in self.all_index_pools:
            return {&#34;pool_name&#34;: value, &#34;pool_id&#34;: self.all_index_pools[value]}
        raise SDKException(&#39;IndexPools&#39;, &#39;102&#39;)

    def _get_all_index_pools(self):
        &#34;&#34;&#34;Method to get details of all the index pools present in commcell

            Raises:
                SDKException:
                    Response was not success

                    Response was empty
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;GET_ALL_CLIENTS&#39;])
        if flag:
            if response.json() and &#39;clientProperties&#39; in response.json():
                for dictionary in response.json()[&#39;clientProperties&#39;]:
                    if dictionary[&#39;clientProps&#39;][&#39;clusterType&#39;] == 14:
                        temp_name = dictionary[&#39;client&#39;][&#39;clientEntity&#39;][&#39;clientName&#39;].lower()
                        temp_id = int(dictionary[&#39;client&#39;][&#39;clientEntity&#39;][&#39;clientId&#39;])
                        self._all_index_pools[temp_name] = temp_id
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)
            Args:
                response    (object)    -   response object

            Raises:
                SDKException:
                    Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def refresh(self):
        &#34;&#34;&#34;Refreshes the properties of IndexPools class&#34;&#34;&#34;
        self._commcell_object.clients.refresh()
        self._all_index_pools = {}
        self._get_all_index_pools()

    @property
    def all_index_pools(self):
        &#34;&#34;&#34;Returns a dict consisting details of all index pools

            Returns:
                dict    -   dictionary consisting details for all index pools
                Sample:
                    {
                        &lt;pool_name_1&gt; : &lt;pool_id_1&gt;,
                        &lt;pool_name_2&gt; : &lt;pool_id_2&gt;
                    }
        &#34;&#34;&#34;
        return self._all_index_pools

    def get(self, pool_name):
        &#34;&#34;&#34;Returns the IndexPool class object with given pool_name

        Args:
            pool_name       (int/str)       --  Index pool name present in commcell

        Returns:
            object  -   instance of IndexPool class

        Raises:
            SDKExecption:
                Data type of the input(s) is not valid

                Index pool not found
        &#34;&#34;&#34;
        if isinstance(pool_name, int):
            for index_pool_name in self.all_index_pools:
                if self.all_index_pools[index_pool_name] == pool_name:
                    return IndexPool(self._commcell_object, index_pool_name)
        elif isinstance(pool_name, str):
            if pool_name.lower() in self.all_index_pools:
                return IndexPool(self._commcell_object, pool_name.lower())
        else:
            raise SDKException(&#39;IndexPools&#39;, &#39;101&#39;)
        raise SDKException(&#39;IndexPools&#39;, &#39;102&#39;)

    def has_pool(self, pool_name):
        &#34;&#34;&#34;Returns whether a index pool is present in the commcell or not

        Args:
            pool_name       (str)       --  Index pool name

        Returns:
            bool    -   True if index pool with given pool_name is present else False
        &#34;&#34;&#34;
        return pool_name.lower() in self.all_index_pools

    def add(self, pool_name, node_names, hac_name):
        &#34;&#34;&#34;Creates a new Index pool within the commcell

        Args:
            pool_name       (str)   --  Name for the index pool
            node_names      (list)  --  List of strings of all the index pool node names
            hac_name        (str)   --  Name of the HAC cluster to be used while creating pool

        Raises:
            SDKExecption:
                Data type of the input(s) is not valid.

                Response was not success.

                Response was empty.

        Returns:
            object  -   Returns a object of class IndexPool

        &#34;&#34;&#34;
        if not (isinstance(pool_name, str) and isinstance(node_names, list)
                and isinstance(hac_name, str)):
            raise SDKException(&#39;IndexPools&#39;, &#39;101&#39;)
        req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
        del req_json[&#39;solrCloudInfo&#39;]
        del req_json[&#39;cloudMetaInfos&#39;]
        req_json[&#39;type&#39;] = 9
        req_json[&#39;cloudInfoEntity&#39;] = {
            &#34;cloudName&#34;: pool_name,
            &#34;cloudDisplayName&#34;: pool_name
        }
        req_json[&#39;solrCloudPoolInfo&#39;] = {
            &#39;zookerEntity&#39;: {
                &#34;_type_&#34;: 28,
                &#34;clientGroupId&#34;: int(self._commcell_object.hac_clusters.get(hac_name).cluster_id),
                &#34;clientGroupName&#34;: hac_name,
                &#34;flags&#34;: {
                    &#34;include&#34;: False
                }
            }
        }
        for node_name in node_names:
            node_obj = self._commcell_object.clients.get(node_name)
            node_data = {
                &#34;opType&#34;: IndexServerConstants.OPERATION_ADD,
                &#34;nodeClientEntity&#34;: {
                    &#34;hostName&#34;: node_obj.client_hostname,
                    &#34;clientId&#34;: int(node_obj.client_id),
                    &#34;clientName&#34;: node_obj.client_name,
                    &#34;_type_&#34;: 3
                },
                &#34;nodeMetaInfos&#34;: [
                    {
                        &#34;name&#34;: &#34;ISENABLED&#34;,
                        &#34;value&#34;: &#34;false&#34;
                    },
                    {
                        &#34;name&#34;: &#34;JVMMAXMEMORY&#34;,
                        &#34;value&#34;: &#34;8191&#34;
                    },
                    {
                        &#34;name&#34;: &#34;PORTNO&#34;,
                        &#34;value&#34;: &#34;20000&#34;
                    },
                    {
                        &#34;name&#34;: &#34;URL&#34;,
                        &#34;value&#34;: &#34;&#34;
                    },
                    {
                        &#34;name&#34;: &#34;INDEXLOCATION&#34;,
                        &#34;value&#34;: &#34;NA&#34;
                    }
                ]
            }
            req_json[&#39;cloudNodes&#39;].append(node_data)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_CREATE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json():
                if response.json()[&#39;genericResp&#39;] == {} and \
                        &#39;cloudId&#39; in response.json():
                    self.refresh()
                    return IndexPool(self._commcell_object, pool_name)
                o_str = &#39;Failed to create index pool. Error: &#34;{0}&#34;&#39;.format(
                    response.json()[&#39;genericResp&#39;])
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        self._response_not_success(response)

    def delete(self, pool_name):
        &#34;&#34;&#34;Deletes an existing index pool cloud from commcell

        Args:
            pool_name       (str)   --  Index pool cloud name

        Returns:
            None

        Raises:
              SDKExecption:
                  Data type of the input(s) is not valid.

                  Response was not success.

                  Response was empty.
        &#34;&#34;&#34;
        if not isinstance(pool_name, str):
            raise SDKException(&#39;IndexPools&#39;, &#39;101&#39;)
        client = self.get(pool_name)
        cloud_id = client.cloud_id
        req_json = IndexServerConstants.REQUEST_JSON.copy()
        req_json[&#34;opType&#34;] = IndexServerConstants.OPERATION_DELETE
        req_json[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = cloud_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_DELETE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json() \
                    and &#39;errorCode&#39; not in response.json()[&#39;genericResp&#39;]:
                self.refresh()
                return
            if response.json() and &#39;genericResp&#39; in response.json():
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, response.json()[&#39;genericResp&#39;].get(
                        &#39;errorMessage&#39;, &#39;&#39;))
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        self._response_not_success(response)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.index_pools.IndexPools.all_index_pools"><code class="name">var <span class="ident">all_index_pools</span></code></dt>
<dd>
<div class="desc"><p>Returns a dict consisting details of all index pools</p>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary consisting details for all index pools
Sample:
{
<pool_name_1> : <pool_id_1>,
<pool_name_2> : <pool_id_2>
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L192-L204" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_index_pools(self):
    &#34;&#34;&#34;Returns a dict consisting details of all index pools

        Returns:
            dict    -   dictionary consisting details for all index pools
            Sample:
                {
                    &lt;pool_name_1&gt; : &lt;pool_id_1&gt;,
                    &lt;pool_name_2&gt; : &lt;pool_id_2&gt;
                }
    &#34;&#34;&#34;
    return self._all_index_pools</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.index_pools.IndexPools.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, pool_name, node_names, hac_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates a new Index pool within the commcell</p>
<h2 id="args">Args</h2>
<p>pool_name
(str)
&ndash;
Name for the index pool
node_names
(list)
&ndash;
List of strings of all the index pool node names
hac_name
(str)
&ndash;
Name of the HAC cluster to be used while creating pool</p>
<h2 id="raises">Raises</h2>
<p>SDKExecption:
Data type of the input(s) is not valid.</p>
<pre><code>Response was not success.

Response was empty.
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
Returns a object of class IndexPool</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L243-L331" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, pool_name, node_names, hac_name):
    &#34;&#34;&#34;Creates a new Index pool within the commcell

    Args:
        pool_name       (str)   --  Name for the index pool
        node_names      (list)  --  List of strings of all the index pool node names
        hac_name        (str)   --  Name of the HAC cluster to be used while creating pool

    Raises:
        SDKExecption:
            Data type of the input(s) is not valid.

            Response was not success.

            Response was empty.

    Returns:
        object  -   Returns a object of class IndexPool

    &#34;&#34;&#34;
    if not (isinstance(pool_name, str) and isinstance(node_names, list)
            and isinstance(hac_name, str)):
        raise SDKException(&#39;IndexPools&#39;, &#39;101&#39;)
    req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
    del req_json[&#39;solrCloudInfo&#39;]
    del req_json[&#39;cloudMetaInfos&#39;]
    req_json[&#39;type&#39;] = 9
    req_json[&#39;cloudInfoEntity&#39;] = {
        &#34;cloudName&#34;: pool_name,
        &#34;cloudDisplayName&#34;: pool_name
    }
    req_json[&#39;solrCloudPoolInfo&#39;] = {
        &#39;zookerEntity&#39;: {
            &#34;_type_&#34;: 28,
            &#34;clientGroupId&#34;: int(self._commcell_object.hac_clusters.get(hac_name).cluster_id),
            &#34;clientGroupName&#34;: hac_name,
            &#34;flags&#34;: {
                &#34;include&#34;: False
            }
        }
    }
    for node_name in node_names:
        node_obj = self._commcell_object.clients.get(node_name)
        node_data = {
            &#34;opType&#34;: IndexServerConstants.OPERATION_ADD,
            &#34;nodeClientEntity&#34;: {
                &#34;hostName&#34;: node_obj.client_hostname,
                &#34;clientId&#34;: int(node_obj.client_id),
                &#34;clientName&#34;: node_obj.client_name,
                &#34;_type_&#34;: 3
            },
            &#34;nodeMetaInfos&#34;: [
                {
                    &#34;name&#34;: &#34;ISENABLED&#34;,
                    &#34;value&#34;: &#34;false&#34;
                },
                {
                    &#34;name&#34;: &#34;JVMMAXMEMORY&#34;,
                    &#34;value&#34;: &#34;8191&#34;
                },
                {
                    &#34;name&#34;: &#34;PORTNO&#34;,
                    &#34;value&#34;: &#34;20000&#34;
                },
                {
                    &#34;name&#34;: &#34;URL&#34;,
                    &#34;value&#34;: &#34;&#34;
                },
                {
                    &#34;name&#34;: &#34;INDEXLOCATION&#34;,
                    &#34;value&#34;: &#34;NA&#34;
                }
            ]
        }
        req_json[&#39;cloudNodes&#39;].append(node_data)
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;CLOUD_CREATE&#39;], req_json
    )
    if flag:
        if response.json() and &#39;genericResp&#39; in response.json():
            if response.json()[&#39;genericResp&#39;] == {} and \
                    &#39;cloudId&#39; in response.json():
                self.refresh()
                return IndexPool(self._commcell_object, pool_name)
            o_str = &#39;Failed to create index pool. Error: &#34;{0}&#34;&#39;.format(
                response.json()[&#39;genericResp&#39;])
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPools.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, pool_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes an existing index pool cloud from commcell</p>
<h2 id="args">Args</h2>
<p>pool_name
(str)
&ndash;
Index pool cloud name</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKExecption:
Data type of the input(s) is not valid.</p>
<pre><code>Response was not success.

Response was empty.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L333-L370" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, pool_name):
    &#34;&#34;&#34;Deletes an existing index pool cloud from commcell

    Args:
        pool_name       (str)   --  Index pool cloud name

    Returns:
        None

    Raises:
          SDKExecption:
              Data type of the input(s) is not valid.

              Response was not success.

              Response was empty.
    &#34;&#34;&#34;
    if not isinstance(pool_name, str):
        raise SDKException(&#39;IndexPools&#39;, &#39;101&#39;)
    client = self.get(pool_name)
    cloud_id = client.cloud_id
    req_json = IndexServerConstants.REQUEST_JSON.copy()
    req_json[&#34;opType&#34;] = IndexServerConstants.OPERATION_DELETE
    req_json[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = cloud_id
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;CLOUD_DELETE&#39;], req_json
    )
    if flag:
        if response.json() and &#39;genericResp&#39; in response.json() \
                and &#39;errorCode&#39; not in response.json()[&#39;genericResp&#39;]:
            self.refresh()
            return
        if response.json() and &#39;genericResp&#39; in response.json():
            raise SDKException(
                &#39;Response&#39;, &#39;102&#39;, response.json()[&#39;genericResp&#39;].get(
                    &#39;errorMessage&#39;, &#39;&#39;))
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPools.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, pool_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the IndexPool class object with given pool_name</p>
<h2 id="args">Args</h2>
<p>pool_name
(int/str)
&ndash;
Index pool name present in commcell</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of IndexPool class</p>
<h2 id="raises">Raises</h2>
<p>SDKExecption:
Data type of the input(s) is not valid</p>
<pre><code>Index pool not found
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L206-L230" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, pool_name):
    &#34;&#34;&#34;Returns the IndexPool class object with given pool_name

    Args:
        pool_name       (int/str)       --  Index pool name present in commcell

    Returns:
        object  -   instance of IndexPool class

    Raises:
        SDKExecption:
            Data type of the input(s) is not valid

            Index pool not found
    &#34;&#34;&#34;
    if isinstance(pool_name, int):
        for index_pool_name in self.all_index_pools:
            if self.all_index_pools[index_pool_name] == pool_name:
                return IndexPool(self._commcell_object, index_pool_name)
    elif isinstance(pool_name, str):
        if pool_name.lower() in self.all_index_pools:
            return IndexPool(self._commcell_object, pool_name.lower())
    else:
        raise SDKException(&#39;IndexPools&#39;, &#39;101&#39;)
    raise SDKException(&#39;IndexPools&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPools.has_pool"><code class="name flex">
<span>def <span class="ident">has_pool</span></span>(<span>self, pool_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns whether a index pool is present in the commcell or not</p>
<h2 id="args">Args</h2>
<p>pool_name
(str)
&ndash;
Index pool name</p>
<h2 id="returns">Returns</h2>
<p>bool
-
True if index pool with given pool_name is present else False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L232-L241" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_pool(self, pool_name):
    &#34;&#34;&#34;Returns whether a index pool is present in the commcell or not

    Args:
        pool_name       (str)       --  Index pool name

    Returns:
        bool    -   True if index pool with given pool_name is present else False
    &#34;&#34;&#34;
    return pool_name.lower() in self.all_index_pools</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_pools.IndexPools.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refreshes the properties of IndexPools class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_pools.py#L186-L190" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refreshes the properties of IndexPools class&#34;&#34;&#34;
    self._commcell_object.clients.refresh()
    self._all_index_pools = {}
    self._get_all_index_pools()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#indexpools">IndexPools</a><ul>
<li><a href="#indexpools-attributes">IndexPools Attributes</a></li>
</ul>
</li>
<li><a href="#indexpool">IndexPool</a><ul>
<li><a href="#indexpool-attributes">IndexPool Attributes</a></li>
</ul>
</li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.index_pools.IndexPool" href="#cvpysdk.index_pools.IndexPool">IndexPool</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.index_pools.IndexPool.cloud_id" href="#cvpysdk.index_pools.IndexPool.cloud_id">cloud_id</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPool.hac_cluster" href="#cvpysdk.index_pools.IndexPool.hac_cluster">hac_cluster</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPool.modify_node" href="#cvpysdk.index_pools.IndexPool.modify_node">modify_node</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPool.node_info" href="#cvpysdk.index_pools.IndexPool.node_info">node_info</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPool.node_names" href="#cvpysdk.index_pools.IndexPool.node_names">node_names</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPool.pool_id" href="#cvpysdk.index_pools.IndexPool.pool_id">pool_id</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPool.pool_name" href="#cvpysdk.index_pools.IndexPool.pool_name">pool_name</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPool.refresh" href="#cvpysdk.index_pools.IndexPool.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.index_pools.IndexPools" href="#cvpysdk.index_pools.IndexPools">IndexPools</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.index_pools.IndexPools.add" href="#cvpysdk.index_pools.IndexPools.add">add</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPools.all_index_pools" href="#cvpysdk.index_pools.IndexPools.all_index_pools">all_index_pools</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPools.delete" href="#cvpysdk.index_pools.IndexPools.delete">delete</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPools.get" href="#cvpysdk.index_pools.IndexPools.get">get</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPools.has_pool" href="#cvpysdk.index_pools.IndexPools.has_pool">has_pool</a></code></li>
<li><code><a title="cvpysdk.index_pools.IndexPools.refresh" href="#cvpysdk.index_pools.IndexPools.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>