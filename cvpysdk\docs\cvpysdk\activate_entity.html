<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activate_entity API documentation</title>
<meta name="description" content="Main file for performing operations on Activate regex Entities, and a single Activate regex Entity in the commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activate_entity</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing operations on Activate regex Entities, and a single Activate regex Entity in the commcell.</p>
<p><code><a title="cvpysdk.activate_entity.ActivateEntities" href="#cvpysdk.activate_entity.ActivateEntities">ActivateEntities</a></code>, and <code><a title="cvpysdk.activate_entity.ActivateEntity" href="#cvpysdk.activate_entity.ActivateEntity">ActivateEntity</a></code> are 2 classes defined in this file.</p>
<p>ActivateEntities:
Class for representing all the regex entities in the commcell.</p>
<p>ActivateEntity:
Class for representing a single regex entity in the commcell.</p>
<h2 id="activateentities">Activateentities</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialise object of the ActivateEntities class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the regex entities associated with the commcell</p>
<p>get()
&ndash;
Returns an instance of ActivateEntity class for the given regex entity name</p>
<p>get_entity_ids()
&ndash;
Returns an list of entity ids for the given regex entity name list</p>
<p>get_entity_keys()
&ndash;
Returns an list of entity keys for the given regex entity name list</p>
<p>get_properties()
&ndash;
Returns the properties for the given regex entity name</p>
<p>_get_all_activate_entities()
&ndash;
Returns dict consisting all regex entities associated with commcell</p>
<p>_get_regex_entity_from_collections()&ndash;
gets all the regex entity details from collection response</p>
<p>has_entity()
&ndash;
Checks whether given regex entity exists in commcell or not</p>
<p>add()
&ndash;
adds the regex entity in the commcell</p>
<p>delete()
&ndash;
deletes the regex entity in the commcell for given entity name</p>
<h2 id="activateentity">Activateentity</h2>
<p><strong>init</strong>(
commcell_object,
entity_name,
entity_id=None)
&ndash;
initialize an object of ActivateEntity Class with the given regex entity
name and id</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the properties of the regex entity</p>
<p>_get_entity_id()
&ndash;
Gets entity id for the given regex entity name</p>
<p>_get_entity_properties()
&ndash;
Gets all the details of associated regex entity</p>
<p>modify()
&ndash;
Modifies the entity properties for the associated regex entity</p>
<h2 id="activateentity-attributes">Activateentity Attributes</h2>
<pre><code>**entity_id**         --  returns the id of the regex entity

**entity_key**        --  returns the key of the regex entity

**category_name**     --  returns the category name of the regex entity

**is_enabled**        --  returns the enabled flag of the regex entity

**display_name**      --  returns the display name of the regex entity

**entity_type**       --  returns the type of entity (1- NER 2-RER 3-Derived 4-Classifier)

**entity_xml**        --  returns the entity xml associated with this entity
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L1-L628" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing operations on Activate regex Entities, and a single Activate regex Entity in the commcell.

`ActivateEntities`, and `ActivateEntity` are 2 classes defined in this file.

ActivateEntities:   Class for representing all the regex entities in the commcell.

ActivateEntity:     Class for representing a single regex entity in the commcell.


ActivateEntities:

    __init__(commcell_object)           --  initialise object of the ActivateEntities class

     _response_not_success()            --  parses through the exception response, and raises SDKException

    refresh()                           --  refresh the regex entities associated with the commcell

    get()                               --  Returns an instance of ActivateEntity class for the given regex entity name

    get_entity_ids()                    --  Returns an list of entity ids for the given regex entity name list

    get_entity_keys()                   --  Returns an list of entity keys for the given regex entity name list

    get_properties()                    --  Returns the properties for the given regex entity name

    _get_all_activate_entities()        --  Returns dict consisting all regex entities associated with commcell

    _get_regex_entity_from_collections()--  gets all the regex entity details from collection response

    has_entity()                        --  Checks whether given regex entity exists in commcell or not

    add()                               --  adds the regex entity in the commcell

    delete()                            --  deletes the regex entity in the commcell for given entity name

ActivateEntity:

    __init__(
        commcell_object,
        entity_name,
        entity_id=None)             --  initialize an object of ActivateEntity Class with the given regex entity
                                                name and id

     _response_not_success()            --  parses through the exception response, and raises SDKException

    refresh()                           --  refresh the properties of the regex entity

    _get_entity_id()                    --  Gets entity id for the given regex entity name

    _get_entity_properties()            --  Gets all the details of associated regex entity

    modify()                            --  Modifies the entity properties for the associated regex entity


ActivateEntity Attributes
-----------------

    **entity_id**         --  returns the id of the regex entity

    **entity_key**        --  returns the key of the regex entity

    **category_name**     --  returns the category name of the regex entity

    **is_enabled**        --  returns the enabled flag of the regex entity

    **display_name**      --  returns the display name of the regex entity

    **entity_type**       --  returns the type of entity (1- NER 2-RER 3-Derived 4-Classifier)

    **entity_xml**        --  returns the entity xml associated with this entity

&#34;&#34;&#34;

from past.builtins import basestring
from .exception import SDKException
from .datacube.constants import ActivateEntityConstants


class ActivateEntities(object):
    &#34;&#34;&#34;Class for representing all the regex entities in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the ActivateEntities class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the ActivateEntities class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._regex_entities = None
        self._api_get_all_regex_entities = self._services[&#39;ACTIVATE_ENTITIES&#39;]
        self._api_create_regex_entity = self._api_get_all_regex_entities
        self._api_delete_regex_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]
        self.refresh()

    def add(self, entity_name, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
        &#34;&#34;&#34;Adds the specified regex entity name in the commcell

                    Args:
                        entity_name (str)      --  name of the regex entity

                        entity_regex (str)     --  Regex for the entity

                        entity_keywords (str)  --  Keywords for the entity

                        entity_flag (int)      --  Sensitivity flag value for entity
                                                        5-Highly sensitive
                                                        3-Moderate sensitive
                                                        1-Low sensitive

                        is_derived (bool)      --  represents whether it is derived entity or not

                        parent_entity(int)     -- entity id of the parent entity in case of derived entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to add regex entity in commcell

                                if entity_flag is not in proper allowed values [1,3,5]

                                if input data type is not valid
                &#34;&#34;&#34;
        if not isinstance(entity_name, basestring) or not isinstance(entity_regex, basestring) \
                or not isinstance(entity_keywords, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_flag not in [1, 3, 5]:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
        request_json = ActivateEntityConstants.REQUEST_JSON
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
            entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
        request_json[&#39;flags&#39;] = entity_flag
        request_json[&#39;entityName&#39;] = entity_name
        request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
        if is_derived:
            request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + entity_name + &#34;\&#34;}&#34;
            request_json[&#39;entityType&#39;] = 3
            if isinstance(parent_entity, int):
                request_json[&#39;parentEntityId&#39;] = parent_entity
            elif isinstance(parent_entity, basestring):
                request_json[&#39;parentEntityId&#39;] = self._regex_entities[parent_entity][&#39;entityId&#39;]
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_create_regex_entity, request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                self.refresh()
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def delete(self, entity_name):
        &#34;&#34;&#34;deletes the specified regex entity name in the commcell

                    Args:
                        entity_name (str)      --  name of the regex entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to delete regex entity in commcell

                                if unable to find entity name in the commcell

                                if data type of entity_name is invalid


                &#34;&#34;&#34;
        if not isinstance(entity_name, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_name not in self._regex_entities:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unable to find given regex entity name in the commcell&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._api_delete_regex_entity % self._regex_entities[entity_name][&#39;entityId&#39;]
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_properties(self, entity_name):
        &#34;&#34;&#34;Returns a properties of the specified regex entity name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:
                dict -  properties for the given regex entity name


        &#34;&#34;&#34;
        return self._regex_entities[entity_name]

    def _get_all_activate_entities(self):
        &#34;&#34;&#34;Gets the list of all regex entities associated with this commcell.

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single regex entity

                    {
                        &#34;entityDetails&#34;: [
                            {
                                &#34;displayName&#34;: &#34;US Social Security number&#34;,
                                &#34;flags&#34;: 5,
                                &#34;description&#34;: &#34;&#34;,
                                &#34;categoryName&#34;: &#34;US&#34;,
                                &#34;enabled&#34;: true,
                                &#34;entityName&#34;: &#34;SSN&#34;,
                                &#34;attribute&#34;: 3,
                                &#34;entityType&#34;: 2,
                                &#34;entityKey&#34;: &#34;ssn&#34;,
                                &#34;entityId&#34;: 1111,
                                &#34;entityXML&#34;: {
                                    &#34;keywords&#34;: &#34;Social Security,Social Security#,Soc Sec,SSN,SSNS,SSN#,SS#,SSID&#34;,
                                    &#34;entityKey&#34;: &#34;ssn&#34;,
                                    &#34;isSystemDefinedEntity&#34;: true,
                                    &#34;inheritBaseWords&#34;: false
                                }
                            },
                            {
                                &#34;displayName&#34;: &#34;Person Name&#34;,
                                &#34;flags&#34;: 1,
                                &#34;description&#34;: &#34;Name of a person.&#34;,
                                &#34;categoryName&#34;: &#34;Generic&#34;,
                                &#34;enabled&#34;: true,
                                &#34;entityName&#34;: &#34;Person&#34;,
                                &#34;attribute&#34;: 3,
                                &#34;entityType&#34;: 1,
                                &#34;entityKey&#34;: &#34;person&#34;,
                                &#34;entityId&#34;: 1112,
                                &#34;entityXML&#34;: {
                                    &#34;keywords&#34;: &#34;&#34;,
                                    &#34;entityKey&#34;: &#34;person&#34;,
                                    &#34;inheritBaseWords&#34;: false
                                }
                            }
                            ]
                            }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_all_regex_entities
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json():
                return self._get_regex_entity_from_collections(response.json())
            raise SDKException(&#39;ActivateEntity&#39;, &#39;103&#39;)
        self._response_not_success(response)

    @staticmethod
    def _get_regex_entity_from_collections(collections):
        &#34;&#34;&#34;Extracts all the regex entities, and their details from the list of collections given,
            and returns the dictionary of all regex entities

            Args:
                collections     (list)  --  list of all collections

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single regex entity

        &#34;&#34;&#34;
        _regex_entity = {}
        for regex_entity in collections[&#39;entityDetails&#39;]:
            regex_entity_dict = {}
            regex_entity_dict[&#39;displayName&#39;] = regex_entity.get(&#39;displayName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityKey&#39;] = regex_entity.get(&#39;entityKey&#39;, &#34;&#34;)
            regex_entity_dict[&#39;categoryName&#39;] = regex_entity.get(&#39;categoryName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityXML&#39;] = regex_entity.get(&#39;entityXML&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityId&#39;] = regex_entity.get(&#39;entityId&#39;, 0)
            regex_entity_dict[&#39;flags&#39;] = regex_entity.get(&#39;flags&#39;, 0)
            regex_entity_dict[&#39;entityType&#39;] = regex_entity.get(&#39;entityType&#39;, 0)
            regex_entity_dict[&#39;enabled&#39;] = regex_entity.get(&#39;enabled&#39;, False)
            _regex_entity[regex_entity[&#39;entityName&#39;]] = regex_entity_dict
        return _regex_entity

    def refresh(self):
        &#34;&#34;&#34;Refresh the activate regex entities associated with the commcell.&#34;&#34;&#34;
        self._regex_entities = self._get_all_activate_entities()

    def get(self, entity_name):
        &#34;&#34;&#34;Returns a ActivateEntity object for the given regex entity name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:

                obj                 -- Object of ActivateEntity class

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

                    if entity_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(entity_name, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

        if self.has_entity(entity_name):
            entity_id = self._regex_entities[entity_name][&#39;entityId&#39;]
            return ActivateEntity(self._commcell_object, entity_name, entity_id)
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#34;Unable to get ActivateEntity class object&#34;)

    def get_entity_ids(self, entity_name):
        &#34;&#34;&#34;Returns a list of entity id for the given regex entity name list.

            Args:
                entity_name (list)  --  names of the regex entity

            Returns:

                list                -- entity id&#39;s for the given entity names


        &#34;&#34;&#34;
        if not isinstance(entity_name, list):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        entity_ids = []
        for regex_entity in entity_name:
            if regex_entity in self._regex_entities:
                entity_ids.append(self._regex_entities[regex_entity][&#39;entityId&#39;])
            else:
                raise SDKException(
                    &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity id for given entity name :{regex_entity}&#34;)
        return entity_ids

    def get_entity_keys(self, entity_name):
        &#34;&#34;&#34;Returns a list of entity keys for the given regex entity name list.

            Args:
                entity_name (list)  --  names of the regex entity

            Returns:

                list                -- entity keys for the given entity names


        &#34;&#34;&#34;
        if not isinstance(entity_name, list):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        entity_keys = []
        for regex_entity in entity_name:
            if regex_entity in self._regex_entities:
                entity_keys.append(self._regex_entities[regex_entity][&#39;entityKey&#39;])
            else:
                raise SDKException(
                    &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity keys for given entity name :{regex_entity}&#34;)
        return entity_keys

    def has_entity(self, entity_name):
        &#34;&#34;&#34;Checks if a regex entity exists in the commcell with the input name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:
                bool - boolean output whether the regex entity exists in the commcell or not

            Raises:
                SDKException:
                    if type of the regex entity name argument is not string

        &#34;&#34;&#34;
        if not isinstance(entity_name, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

        return self._regex_entities and entity_name.lower() in map(str.lower, self._regex_entities)


class ActivateEntity(object):
    &#34;&#34;&#34;Class for performing operations on a single regex entity&#34;&#34;&#34;

    def __init__(self, commcell_object, entity_name, entity_id=None):
        &#34;&#34;&#34;Initialize an object of the ActivateEntity class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                entity_name     (str)           --  name of the regex entity

                entity_id       (str)           --  id of the regex entity
                    default: None

            Returns:
                object  -   instance of the ActivateEntity class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._entity_name = entity_name
        self._entity_id = None
        self._display_name = None
        self._entity_type = None
        self._is_enabled = None
        self._entity_key = None
        self._entity_xml = None
        self._category_name = None
        if entity_id is None:
            self._entity_id = self._get_entity_id(entity_name)
        else:
            self._entity_id = entity_id
        self.refresh()
        self._api_modify_regex_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def modify(self, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
        &#34;&#34;&#34;Modifies the specified regex entity details

                    Args:

                        entity_regex (str)     --  Regex for the entity

                        entity_keywords (str)  --  Keywords for the entity

                        entity_flag (int)      --  Sensitivity flag value for entity
                                                        5-Highly sensitive
                                                        3-Moderate sensitive
                                                        1-Low sensitive

                        is_derived (bool)      --  represents whether it is derived entity or not

                        parent_entity(int)     -- entity id of the parent entity in case of derived entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to modify regex entity in commcell

                                if input entity_keywords &amp; entity_regex is not string

                                if entity_flag value is not in allowed values[1,3,5]


                &#34;&#34;&#34;
        if not isinstance(entity_regex, basestring) \
                or not isinstance(entity_keywords, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_flag not in [1, 3, 5]:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
        request_json = ActivateEntityConstants.REQUEST_JSON
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
            self._entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
        request_json[&#39;flags&#39;] = entity_flag
        request_json[&#39;entityName&#39;] = self._entity_name
        request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
        if is_derived:
            request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + self._entity_name + &#34;\&#34;}&#34;
            request_json[&#39;entityType&#39;] = 3
            if isinstance(parent_entity, int):
                request_json[&#39;parentEntityId&#39;] = parent_entity
            elif isinstance(parent_entity, basestring):
                request_json[&#39;parentEntityId&#39;] = ActivateEntities.get(
                    self._commcell_object, entity_name=parent_entity).entity_id
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, (self._api_modify_regex_entity % self.entity_id), request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def _get_entity_id(self, entity_name):
        &#34;&#34;&#34; Get regex entity id for given entity name
                Args:

                    entity_name (str)  -- Name of the regex entity

                Returns:

                    int                -- id of the regex entity

        &#34;&#34;&#34;

        return self._commcell_object.activate_entity.get(entity_name).entity_id

    def _get_entity_properties(self):
        &#34;&#34;&#34; Get regex entity properties for given entity name
                Args:

                    None

                Returns:

                    None

        &#34;&#34;&#34;

        regex_entity_dict = self._commcell_object.activate_entity.get_properties(self._entity_name)
        self._display_name = regex_entity_dict[&#39;displayName&#39;]
        self._category_name = regex_entity_dict[&#39;categoryName&#39;]
        self._entity_id = regex_entity_dict[&#39;entityId&#39;]
        self._is_enabled = regex_entity_dict[&#39;enabled&#39;]
        self._entity_key = regex_entity_dict[&#39;entityKey&#39;]
        self._entity_type = regex_entity_dict[&#39;entityType&#39;]
        self._entity_xml = regex_entity_dict[&#39;entityXML&#39;]
        return regex_entity_dict

    @property
    def entity_id(self):
        &#34;&#34;&#34;Returns the value of the regex entity id attribute.&#34;&#34;&#34;
        return self._entity_id

    @property
    def entity_type(self):
        &#34;&#34;&#34;Returns the entity type attribute.&#34;&#34;&#34;
        return self._entity_type

    @property
    def category_name(self):
        &#34;&#34;&#34;Returns the entity category name attribute.&#34;&#34;&#34;
        return self._category_name

    @property
    def display_name(self):
        &#34;&#34;&#34;Returns the entity display name attribute.&#34;&#34;&#34;
        return self._display_name

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Returns the entity isenabled attribute.&#34;&#34;&#34;
        return self._is_enabled

    @property
    def entity_key(self):
        &#34;&#34;&#34;Returns the entity key attribute.&#34;&#34;&#34;
        return self._entity_key

    @property
    def entity_xml(self):
        &#34;&#34;&#34;Returns the entity xml attribute.&#34;&#34;&#34;
        return self._entity_xml

    def refresh(self):
        &#34;&#34;&#34;Refresh the regex entity details for associated object&#34;&#34;&#34;
        self._get_entity_properties()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activate_entity.ActivateEntities"><code class="flex name class">
<span>class <span class="ident">ActivateEntities</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the regex entities in the commcell.</p>
<p>Initializes an instance of the ActivateEntities class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the ActivateEntities class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L97-L437" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ActivateEntities(object):
    &#34;&#34;&#34;Class for representing all the regex entities in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the ActivateEntities class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the ActivateEntities class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._regex_entities = None
        self._api_get_all_regex_entities = self._services[&#39;ACTIVATE_ENTITIES&#39;]
        self._api_create_regex_entity = self._api_get_all_regex_entities
        self._api_delete_regex_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]
        self.refresh()

    def add(self, entity_name, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
        &#34;&#34;&#34;Adds the specified regex entity name in the commcell

                    Args:
                        entity_name (str)      --  name of the regex entity

                        entity_regex (str)     --  Regex for the entity

                        entity_keywords (str)  --  Keywords for the entity

                        entity_flag (int)      --  Sensitivity flag value for entity
                                                        5-Highly sensitive
                                                        3-Moderate sensitive
                                                        1-Low sensitive

                        is_derived (bool)      --  represents whether it is derived entity or not

                        parent_entity(int)     -- entity id of the parent entity in case of derived entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to add regex entity in commcell

                                if entity_flag is not in proper allowed values [1,3,5]

                                if input data type is not valid
                &#34;&#34;&#34;
        if not isinstance(entity_name, basestring) or not isinstance(entity_regex, basestring) \
                or not isinstance(entity_keywords, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_flag not in [1, 3, 5]:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
        request_json = ActivateEntityConstants.REQUEST_JSON
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
            entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
        request_json[&#39;flags&#39;] = entity_flag
        request_json[&#39;entityName&#39;] = entity_name
        request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
        if is_derived:
            request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + entity_name + &#34;\&#34;}&#34;
            request_json[&#39;entityType&#39;] = 3
            if isinstance(parent_entity, int):
                request_json[&#39;parentEntityId&#39;] = parent_entity
            elif isinstance(parent_entity, basestring):
                request_json[&#39;parentEntityId&#39;] = self._regex_entities[parent_entity][&#39;entityId&#39;]
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_create_regex_entity, request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                self.refresh()
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def delete(self, entity_name):
        &#34;&#34;&#34;deletes the specified regex entity name in the commcell

                    Args:
                        entity_name (str)      --  name of the regex entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to delete regex entity in commcell

                                if unable to find entity name in the commcell

                                if data type of entity_name is invalid


                &#34;&#34;&#34;
        if not isinstance(entity_name, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_name not in self._regex_entities:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unable to find given regex entity name in the commcell&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._api_delete_regex_entity % self._regex_entities[entity_name][&#39;entityId&#39;]
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_properties(self, entity_name):
        &#34;&#34;&#34;Returns a properties of the specified regex entity name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:
                dict -  properties for the given regex entity name


        &#34;&#34;&#34;
        return self._regex_entities[entity_name]

    def _get_all_activate_entities(self):
        &#34;&#34;&#34;Gets the list of all regex entities associated with this commcell.

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single regex entity

                    {
                        &#34;entityDetails&#34;: [
                            {
                                &#34;displayName&#34;: &#34;US Social Security number&#34;,
                                &#34;flags&#34;: 5,
                                &#34;description&#34;: &#34;&#34;,
                                &#34;categoryName&#34;: &#34;US&#34;,
                                &#34;enabled&#34;: true,
                                &#34;entityName&#34;: &#34;SSN&#34;,
                                &#34;attribute&#34;: 3,
                                &#34;entityType&#34;: 2,
                                &#34;entityKey&#34;: &#34;ssn&#34;,
                                &#34;entityId&#34;: 1111,
                                &#34;entityXML&#34;: {
                                    &#34;keywords&#34;: &#34;Social Security,Social Security#,Soc Sec,SSN,SSNS,SSN#,SS#,SSID&#34;,
                                    &#34;entityKey&#34;: &#34;ssn&#34;,
                                    &#34;isSystemDefinedEntity&#34;: true,
                                    &#34;inheritBaseWords&#34;: false
                                }
                            },
                            {
                                &#34;displayName&#34;: &#34;Person Name&#34;,
                                &#34;flags&#34;: 1,
                                &#34;description&#34;: &#34;Name of a person.&#34;,
                                &#34;categoryName&#34;: &#34;Generic&#34;,
                                &#34;enabled&#34;: true,
                                &#34;entityName&#34;: &#34;Person&#34;,
                                &#34;attribute&#34;: 3,
                                &#34;entityType&#34;: 1,
                                &#34;entityKey&#34;: &#34;person&#34;,
                                &#34;entityId&#34;: 1112,
                                &#34;entityXML&#34;: {
                                    &#34;keywords&#34;: &#34;&#34;,
                                    &#34;entityKey&#34;: &#34;person&#34;,
                                    &#34;inheritBaseWords&#34;: false
                                }
                            }
                            ]
                            }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_all_regex_entities
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json():
                return self._get_regex_entity_from_collections(response.json())
            raise SDKException(&#39;ActivateEntity&#39;, &#39;103&#39;)
        self._response_not_success(response)

    @staticmethod
    def _get_regex_entity_from_collections(collections):
        &#34;&#34;&#34;Extracts all the regex entities, and their details from the list of collections given,
            and returns the dictionary of all regex entities

            Args:
                collections     (list)  --  list of all collections

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single regex entity

        &#34;&#34;&#34;
        _regex_entity = {}
        for regex_entity in collections[&#39;entityDetails&#39;]:
            regex_entity_dict = {}
            regex_entity_dict[&#39;displayName&#39;] = regex_entity.get(&#39;displayName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityKey&#39;] = regex_entity.get(&#39;entityKey&#39;, &#34;&#34;)
            regex_entity_dict[&#39;categoryName&#39;] = regex_entity.get(&#39;categoryName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityXML&#39;] = regex_entity.get(&#39;entityXML&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityId&#39;] = regex_entity.get(&#39;entityId&#39;, 0)
            regex_entity_dict[&#39;flags&#39;] = regex_entity.get(&#39;flags&#39;, 0)
            regex_entity_dict[&#39;entityType&#39;] = regex_entity.get(&#39;entityType&#39;, 0)
            regex_entity_dict[&#39;enabled&#39;] = regex_entity.get(&#39;enabled&#39;, False)
            _regex_entity[regex_entity[&#39;entityName&#39;]] = regex_entity_dict
        return _regex_entity

    def refresh(self):
        &#34;&#34;&#34;Refresh the activate regex entities associated with the commcell.&#34;&#34;&#34;
        self._regex_entities = self._get_all_activate_entities()

    def get(self, entity_name):
        &#34;&#34;&#34;Returns a ActivateEntity object for the given regex entity name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:

                obj                 -- Object of ActivateEntity class

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

                    if entity_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(entity_name, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

        if self.has_entity(entity_name):
            entity_id = self._regex_entities[entity_name][&#39;entityId&#39;]
            return ActivateEntity(self._commcell_object, entity_name, entity_id)
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#34;Unable to get ActivateEntity class object&#34;)

    def get_entity_ids(self, entity_name):
        &#34;&#34;&#34;Returns a list of entity id for the given regex entity name list.

            Args:
                entity_name (list)  --  names of the regex entity

            Returns:

                list                -- entity id&#39;s for the given entity names


        &#34;&#34;&#34;
        if not isinstance(entity_name, list):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        entity_ids = []
        for regex_entity in entity_name:
            if regex_entity in self._regex_entities:
                entity_ids.append(self._regex_entities[regex_entity][&#39;entityId&#39;])
            else:
                raise SDKException(
                    &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity id for given entity name :{regex_entity}&#34;)
        return entity_ids

    def get_entity_keys(self, entity_name):
        &#34;&#34;&#34;Returns a list of entity keys for the given regex entity name list.

            Args:
                entity_name (list)  --  names of the regex entity

            Returns:

                list                -- entity keys for the given entity names


        &#34;&#34;&#34;
        if not isinstance(entity_name, list):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        entity_keys = []
        for regex_entity in entity_name:
            if regex_entity in self._regex_entities:
                entity_keys.append(self._regex_entities[regex_entity][&#39;entityKey&#39;])
            else:
                raise SDKException(
                    &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity keys for given entity name :{regex_entity}&#34;)
        return entity_keys

    def has_entity(self, entity_name):
        &#34;&#34;&#34;Checks if a regex entity exists in the commcell with the input name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:
                bool - boolean output whether the regex entity exists in the commcell or not

            Raises:
                SDKException:
                    if type of the regex entity name argument is not string

        &#34;&#34;&#34;
        if not isinstance(entity_name, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

        return self._regex_entities and entity_name.lower() in map(str.lower, self._regex_entities)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activate_entity.ActivateEntities.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, entity_name, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the specified regex entity name in the commcell</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<p>entity_regex (str)
&ndash;
Regex for the entity</p>
<p>entity_keywords (str)
&ndash;
Keywords for the entity</p>
<p>entity_flag (int)
&ndash;
Sensitivity flag value for entity
5-Highly sensitive
3-Moderate sensitive
1-Low sensitive</p>
<p>is_derived (bool)
&ndash;
represents whether it is derived entity or not</p>
<p>parent_entity(int)
&ndash; entity id of the parent entity in case of derived entity</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if response is empty

    if response is not success

    if unable to add regex entity in commcell

    if entity_flag is not in proper allowed values [1,3,5]

    if input data type is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L120-L185" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, entity_name, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
    &#34;&#34;&#34;Adds the specified regex entity name in the commcell

                Args:
                    entity_name (str)      --  name of the regex entity

                    entity_regex (str)     --  Regex for the entity

                    entity_keywords (str)  --  Keywords for the entity

                    entity_flag (int)      --  Sensitivity flag value for entity
                                                    5-Highly sensitive
                                                    3-Moderate sensitive
                                                    1-Low sensitive

                    is_derived (bool)      --  represents whether it is derived entity or not

                    parent_entity(int)     -- entity id of the parent entity in case of derived entity

                Returns:
                    None

                Raises:
                    SDKException:

                            if response is empty

                            if response is not success

                            if unable to add regex entity in commcell

                            if entity_flag is not in proper allowed values [1,3,5]

                            if input data type is not valid
            &#34;&#34;&#34;
    if not isinstance(entity_name, basestring) or not isinstance(entity_regex, basestring) \
            or not isinstance(entity_keywords, basestring):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    if entity_flag not in [1, 3, 5]:
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
    request_json = ActivateEntityConstants.REQUEST_JSON
    request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
        entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
    request_json[&#39;flags&#39;] = entity_flag
    request_json[&#39;entityName&#39;] = entity_name
    request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
    if is_derived:
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + entity_name + &#34;\&#34;}&#34;
        request_json[&#39;entityType&#39;] = 3
        if isinstance(parent_entity, int):
            request_json[&#39;parentEntityId&#39;] = parent_entity
        elif isinstance(parent_entity, basestring):
            request_json[&#39;parentEntityId&#39;] = self._regex_entities[parent_entity][&#39;entityId&#39;]
        else:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._api_create_regex_entity, request_json
    )

    if flag:
        if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
            self.refresh()
            return
        raise SDKException(&#39;ActivateEntity&#39;, &#39;104&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntities.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>deletes the specified regex entity name in the commcell</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if response is empty

    if response is not success

    if unable to delete regex entity in commcell

    if unable to find entity name in the commcell

    if data type of entity_name is invalid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L187-L225" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, entity_name):
    &#34;&#34;&#34;deletes the specified regex entity name in the commcell

                Args:
                    entity_name (str)      --  name of the regex entity

                Returns:
                    None

                Raises:
                    SDKException:

                            if response is empty

                            if response is not success

                            if unable to delete regex entity in commcell

                            if unable to find entity name in the commcell

                            if data type of entity_name is invalid


            &#34;&#34;&#34;
    if not isinstance(entity_name, basestring):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    if entity_name not in self._regex_entities:
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unable to find given regex entity name in the commcell&#39;)

    flag, response = self._cvpysdk_object.make_request(
        &#39;DELETE&#39;, self._api_delete_regex_entity % self._regex_entities[entity_name][&#39;entityId&#39;]
    )

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] == 0:
            self.refresh()
            return
        raise SDKException(&#39;ActivateEntity&#39;, &#39;105&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntities.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a ActivateEntity object for the given regex entity name.</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>obj
&ndash; Object of ActivateEntity class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success

if entity_name is not of type string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L346-L372" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, entity_name):
    &#34;&#34;&#34;Returns a ActivateEntity object for the given regex entity name.

        Args:
            entity_name (str)  --  name of the regex entity

        Returns:

            obj                 -- Object of ActivateEntity class

        Raises:
            SDKException:
                if response is empty

                if response is not success

                if entity_name is not of type string


    &#34;&#34;&#34;
    if not isinstance(entity_name, basestring):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

    if self.has_entity(entity_name):
        entity_id = self._regex_entities[entity_name][&#39;entityId&#39;]
        return ActivateEntity(self._commcell_object, entity_name, entity_id)
    raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#34;Unable to get ActivateEntity class object&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntities.get_entity_ids"><code class="name flex">
<span>def <span class="ident">get_entity_ids</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a list of entity id for the given regex entity name list.</p>
<h2 id="args">Args</h2>
<p>entity_name (list)
&ndash;
names of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>list
&ndash; entity id's for the given entity names</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L374-L395" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_entity_ids(self, entity_name):
    &#34;&#34;&#34;Returns a list of entity id for the given regex entity name list.

        Args:
            entity_name (list)  --  names of the regex entity

        Returns:

            list                -- entity id&#39;s for the given entity names


    &#34;&#34;&#34;
    if not isinstance(entity_name, list):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    entity_ids = []
    for regex_entity in entity_name:
        if regex_entity in self._regex_entities:
            entity_ids.append(self._regex_entities[regex_entity][&#39;entityId&#39;])
        else:
            raise SDKException(
                &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity id for given entity name :{regex_entity}&#34;)
    return entity_ids</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntities.get_entity_keys"><code class="name flex">
<span>def <span class="ident">get_entity_keys</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a list of entity keys for the given regex entity name list.</p>
<h2 id="args">Args</h2>
<p>entity_name (list)
&ndash;
names of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>list
&ndash; entity keys for the given entity names</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L397-L418" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_entity_keys(self, entity_name):
    &#34;&#34;&#34;Returns a list of entity keys for the given regex entity name list.

        Args:
            entity_name (list)  --  names of the regex entity

        Returns:

            list                -- entity keys for the given entity names


    &#34;&#34;&#34;
    if not isinstance(entity_name, list):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    entity_keys = []
    for regex_entity in entity_name:
        if regex_entity in self._regex_entities:
            entity_keys.append(self._regex_entities[regex_entity][&#39;entityKey&#39;])
        else:
            raise SDKException(
                &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity keys for given entity name :{regex_entity}&#34;)
    return entity_keys</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntities.get_properties"><code class="name flex">
<span>def <span class="ident">get_properties</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a properties of the specified regex entity name.</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>dict -
properties for the given regex entity name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L238-L249" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_properties(self, entity_name):
    &#34;&#34;&#34;Returns a properties of the specified regex entity name.

        Args:
            entity_name (str)  --  name of the regex entity

        Returns:
            dict -  properties for the given regex entity name


    &#34;&#34;&#34;
    return self._regex_entities[entity_name]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntities.has_entity"><code class="name flex">
<span>def <span class="ident">has_entity</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a regex entity exists in the commcell with the input name.</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the regex entity exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the regex entity name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L420-L437" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_entity(self, entity_name):
    &#34;&#34;&#34;Checks if a regex entity exists in the commcell with the input name.

        Args:
            entity_name (str)  --  name of the regex entity

        Returns:
            bool - boolean output whether the regex entity exists in the commcell or not

        Raises:
            SDKException:
                if type of the regex entity name argument is not string

    &#34;&#34;&#34;
    if not isinstance(entity_name, basestring):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

    return self._regex_entities and entity_name.lower() in map(str.lower, self._regex_entities)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntities.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the activate regex entities associated with the commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L342-L344" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the activate regex entities associated with the commcell.&#34;&#34;&#34;
    self._regex_entities = self._get_all_activate_entities()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntity"><code class="flex name class">
<span>class <span class="ident">ActivateEntity</span></span>
<span>(</span><span>commcell_object, entity_name, entity_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on a single regex entity</p>
<p>Initialize an object of the ActivateEntity class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>entity_name
(str)
&ndash;
name of the regex entity</p>
<p>entity_id
(str)
&ndash;
id of the regex entity
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the ActivateEntity class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L440-L628" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ActivateEntity(object):
    &#34;&#34;&#34;Class for performing operations on a single regex entity&#34;&#34;&#34;

    def __init__(self, commcell_object, entity_name, entity_id=None):
        &#34;&#34;&#34;Initialize an object of the ActivateEntity class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                entity_name     (str)           --  name of the regex entity

                entity_id       (str)           --  id of the regex entity
                    default: None

            Returns:
                object  -   instance of the ActivateEntity class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._entity_name = entity_name
        self._entity_id = None
        self._display_name = None
        self._entity_type = None
        self._is_enabled = None
        self._entity_key = None
        self._entity_xml = None
        self._category_name = None
        if entity_id is None:
            self._entity_id = self._get_entity_id(entity_name)
        else:
            self._entity_id = entity_id
        self.refresh()
        self._api_modify_regex_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def modify(self, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
        &#34;&#34;&#34;Modifies the specified regex entity details

                    Args:

                        entity_regex (str)     --  Regex for the entity

                        entity_keywords (str)  --  Keywords for the entity

                        entity_flag (int)      --  Sensitivity flag value for entity
                                                        5-Highly sensitive
                                                        3-Moderate sensitive
                                                        1-Low sensitive

                        is_derived (bool)      --  represents whether it is derived entity or not

                        parent_entity(int)     -- entity id of the parent entity in case of derived entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to modify regex entity in commcell

                                if input entity_keywords &amp; entity_regex is not string

                                if entity_flag value is not in allowed values[1,3,5]


                &#34;&#34;&#34;
        if not isinstance(entity_regex, basestring) \
                or not isinstance(entity_keywords, basestring):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_flag not in [1, 3, 5]:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
        request_json = ActivateEntityConstants.REQUEST_JSON
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
            self._entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
        request_json[&#39;flags&#39;] = entity_flag
        request_json[&#39;entityName&#39;] = self._entity_name
        request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
        if is_derived:
            request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + self._entity_name + &#34;\&#34;}&#34;
            request_json[&#39;entityType&#39;] = 3
            if isinstance(parent_entity, int):
                request_json[&#39;parentEntityId&#39;] = parent_entity
            elif isinstance(parent_entity, basestring):
                request_json[&#39;parentEntityId&#39;] = ActivateEntities.get(
                    self._commcell_object, entity_name=parent_entity).entity_id
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, (self._api_modify_regex_entity % self.entity_id), request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def _get_entity_id(self, entity_name):
        &#34;&#34;&#34; Get regex entity id for given entity name
                Args:

                    entity_name (str)  -- Name of the regex entity

                Returns:

                    int                -- id of the regex entity

        &#34;&#34;&#34;

        return self._commcell_object.activate_entity.get(entity_name).entity_id

    def _get_entity_properties(self):
        &#34;&#34;&#34; Get regex entity properties for given entity name
                Args:

                    None

                Returns:

                    None

        &#34;&#34;&#34;

        regex_entity_dict = self._commcell_object.activate_entity.get_properties(self._entity_name)
        self._display_name = regex_entity_dict[&#39;displayName&#39;]
        self._category_name = regex_entity_dict[&#39;categoryName&#39;]
        self._entity_id = regex_entity_dict[&#39;entityId&#39;]
        self._is_enabled = regex_entity_dict[&#39;enabled&#39;]
        self._entity_key = regex_entity_dict[&#39;entityKey&#39;]
        self._entity_type = regex_entity_dict[&#39;entityType&#39;]
        self._entity_xml = regex_entity_dict[&#39;entityXML&#39;]
        return regex_entity_dict

    @property
    def entity_id(self):
        &#34;&#34;&#34;Returns the value of the regex entity id attribute.&#34;&#34;&#34;
        return self._entity_id

    @property
    def entity_type(self):
        &#34;&#34;&#34;Returns the entity type attribute.&#34;&#34;&#34;
        return self._entity_type

    @property
    def category_name(self):
        &#34;&#34;&#34;Returns the entity category name attribute.&#34;&#34;&#34;
        return self._category_name

    @property
    def display_name(self):
        &#34;&#34;&#34;Returns the entity display name attribute.&#34;&#34;&#34;
        return self._display_name

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Returns the entity isenabled attribute.&#34;&#34;&#34;
        return self._is_enabled

    @property
    def entity_key(self):
        &#34;&#34;&#34;Returns the entity key attribute.&#34;&#34;&#34;
        return self._entity_key

    @property
    def entity_xml(self):
        &#34;&#34;&#34;Returns the entity xml attribute.&#34;&#34;&#34;
        return self._entity_xml

    def refresh(self):
        &#34;&#34;&#34;Refresh the regex entity details for associated object&#34;&#34;&#34;
        self._get_entity_properties()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activate_entity.ActivateEntity.category_name"><code class="name">var <span class="ident">category_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity category name attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L601-L604" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def category_name(self):
    &#34;&#34;&#34;Returns the entity category name attribute.&#34;&#34;&#34;
    return self._category_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntity.display_name"><code class="name">var <span class="ident">display_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity display name attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L606-L609" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def display_name(self):
    &#34;&#34;&#34;Returns the entity display name attribute.&#34;&#34;&#34;
    return self._display_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntity.entity_id"><code class="name">var <span class="ident">entity_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of the regex entity id attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L591-L594" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_id(self):
    &#34;&#34;&#34;Returns the value of the regex entity id attribute.&#34;&#34;&#34;
    return self._entity_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntity.entity_key"><code class="name">var <span class="ident">entity_key</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity key attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L616-L619" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_key(self):
    &#34;&#34;&#34;Returns the entity key attribute.&#34;&#34;&#34;
    return self._entity_key</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntity.entity_type"><code class="name">var <span class="ident">entity_type</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity type attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L596-L599" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_type(self):
    &#34;&#34;&#34;Returns the entity type attribute.&#34;&#34;&#34;
    return self._entity_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntity.entity_xml"><code class="name">var <span class="ident">entity_xml</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity xml attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L621-L624" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_xml(self):
    &#34;&#34;&#34;Returns the entity xml attribute.&#34;&#34;&#34;
    return self._entity_xml</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntity.is_enabled"><code class="name">var <span class="ident">is_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity isenabled attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L611-L614" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_enabled(self):
    &#34;&#34;&#34;Returns the entity isenabled attribute.&#34;&#34;&#34;
    return self._is_enabled</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activate_entity.ActivateEntity.modify"><code class="name flex">
<span>def <span class="ident">modify</span></span>(<span>self, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies the specified regex entity details</p>
<h2 id="args">Args</h2>
<p>entity_regex (str)
&ndash;
Regex for the entity</p>
<p>entity_keywords (str)
&ndash;
Keywords for the entity</p>
<p>entity_flag (int)
&ndash;
Sensitivity flag value for entity
5-Highly sensitive
3-Moderate sensitive
1-Low sensitive</p>
<p>is_derived (bool)
&ndash;
represents whether it is derived entity or not</p>
<p>parent_entity(int)
&ndash; entity id of the parent entity in case of derived entity</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if response is empty

    if response is not success

    if unable to modify regex entity in commcell

    if input entity_keywords &amp; entity_regex is not string

    if entity_flag value is not in allowed values[1,3,5]
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L487-L553" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify(self, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
    &#34;&#34;&#34;Modifies the specified regex entity details

                Args:

                    entity_regex (str)     --  Regex for the entity

                    entity_keywords (str)  --  Keywords for the entity

                    entity_flag (int)      --  Sensitivity flag value for entity
                                                    5-Highly sensitive
                                                    3-Moderate sensitive
                                                    1-Low sensitive

                    is_derived (bool)      --  represents whether it is derived entity or not

                    parent_entity(int)     -- entity id of the parent entity in case of derived entity

                Returns:
                    None

                Raises:
                    SDKException:

                            if response is empty

                            if response is not success

                            if unable to modify regex entity in commcell

                            if input entity_keywords &amp; entity_regex is not string

                            if entity_flag value is not in allowed values[1,3,5]


            &#34;&#34;&#34;
    if not isinstance(entity_regex, basestring) \
            or not isinstance(entity_keywords, basestring):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    if entity_flag not in [1, 3, 5]:
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
    request_json = ActivateEntityConstants.REQUEST_JSON
    request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
        self._entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
    request_json[&#39;flags&#39;] = entity_flag
    request_json[&#39;entityName&#39;] = self._entity_name
    request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
    if is_derived:
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + self._entity_name + &#34;\&#34;}&#34;
        request_json[&#39;entityType&#39;] = 3
        if isinstance(parent_entity, int):
            request_json[&#39;parentEntityId&#39;] = parent_entity
        elif isinstance(parent_entity, basestring):
            request_json[&#39;parentEntityId&#39;] = ActivateEntities.get(
                self._commcell_object, entity_name=parent_entity).entity_id
        else:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

    flag, response = self._cvpysdk_obj.make_request(
        &#39;PUT&#39;, (self._api_modify_regex_entity % self.entity_id), request_json
    )

    if flag:
        if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
            return
        raise SDKException(&#39;ActivateEntity&#39;, &#39;106&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate_entity.ActivateEntity.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the regex entity details for associated object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/ec40c964eb387f435de53daedf35366608b0001f/cvpysdk/activate_entity.py#L626-L628" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the regex entity details for associated object&#34;&#34;&#34;
    self._get_entity_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#activateentity-attributes">ActivateEntity Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activate_entity.ActivateEntities" href="#cvpysdk.activate_entity.ActivateEntities">ActivateEntities</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activate_entity.ActivateEntities.add" href="#cvpysdk.activate_entity.ActivateEntities.add">add</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntities.delete" href="#cvpysdk.activate_entity.ActivateEntities.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntities.get" href="#cvpysdk.activate_entity.ActivateEntities.get">get</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntities.get_entity_ids" href="#cvpysdk.activate_entity.ActivateEntities.get_entity_ids">get_entity_ids</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntities.get_entity_keys" href="#cvpysdk.activate_entity.ActivateEntities.get_entity_keys">get_entity_keys</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntities.get_properties" href="#cvpysdk.activate_entity.ActivateEntities.get_properties">get_properties</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntities.has_entity" href="#cvpysdk.activate_entity.ActivateEntities.has_entity">has_entity</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntities.refresh" href="#cvpysdk.activate_entity.ActivateEntities.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activate_entity.ActivateEntity" href="#cvpysdk.activate_entity.ActivateEntity">ActivateEntity</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activate_entity.ActivateEntity.category_name" href="#cvpysdk.activate_entity.ActivateEntity.category_name">category_name</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntity.display_name" href="#cvpysdk.activate_entity.ActivateEntity.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntity.entity_id" href="#cvpysdk.activate_entity.ActivateEntity.entity_id">entity_id</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntity.entity_key" href="#cvpysdk.activate_entity.ActivateEntity.entity_key">entity_key</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntity.entity_type" href="#cvpysdk.activate_entity.ActivateEntity.entity_type">entity_type</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntity.entity_xml" href="#cvpysdk.activate_entity.ActivateEntity.entity_xml">entity_xml</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntity.is_enabled" href="#cvpysdk.activate_entity.ActivateEntity.is_enabled">is_enabled</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntity.modify" href="#cvpysdk.activate_entity.ActivateEntity.modify">modify</a></code></li>
<li><code><a title="cvpysdk.activate_entity.ActivateEntity.refresh" href="#cvpysdk.activate_entity.ActivateEntity.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>