<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.drorchestration.drjob API documentation</title>
<meta name="description" content="Main file for getting DR orchestration/sync job details and phases …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.drorchestration.drjob</code></h1>
</header>
<section id="section-intro">
<p>Main file for getting DR orchestration/sync job details and phases</p>
<p>DRJob: Class for representing all the DR jobs</p>
<p>DRJob(Job):
<strong>init</strong>(commcell_object,
job_id)
&ndash; Initialise object of DRJob
_get_replication_job_stats()
&ndash; Gets the DR job statistics
get_phases()
&ndash; Gets the phases of the DR job
get_vm_list()
&ndash; Gets the list of all VMs and their properties</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drjob.py#L1-L187" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for getting DR orchestration/sync job details and phases

DRJob: Class for representing all the DR jobs

DRJob(Job):
    __init__(commcell_object,
            job_id)                 -- Initialise object of DRJob
    _get_replication_job_stats()    -- Gets the DR job statistics
    get_phases()                    -- Gets the phases of the DR job
    get_vm_list()                   -- Gets the list of all VMs and their properties
&#34;&#34;&#34;

from cvpysdk.exception import SDKException
from cvpysdk.job import Job
from cvpysdk.drorchestration.dr_orchestration_job_phase import DRJobPhases, DRJobPhaseToText


class DRJob(Job):
    &#34;&#34;&#34;Class for performing DR orchestration operations on ReplicationMonitor.&#34;&#34;&#34;

    def __init__(self, commcell_object, job_id):
        &#34;&#34;&#34;Initialise the DR job&#34;&#34;&#34;
        self._replication_job_stats = None

        service_url = (commcell_object._services[&#39;DRORCHESTRATION_JOB_STATS&#39;]
                       if commcell_object.commserv_version &gt; 30
                       else commcell_object._services[&#39;DR_JOB_STATS&#39;])
        self._REPLICATION_STATS = service_url % job_id

        Job.__init__(self, commcell_object, job_id)

    def __repr__(self):
        representation_string = &#39;DRJob class instance for job id: &#34;{0}&#34;&#39;
        return representation_string.format(self.job_id)

    def _get_replication_job_stats(self):
        &#34;&#34;&#34;Gets the statistics for the DR Job
        Returns:
                [{
                    &#39;jobId&#39;: 123,
                    &#39;replicationId&#39;: 1,
                    &#39;phase&#39;: [{
                            &#39;phase&#39;: 1,
                            &#39;status&#39;: 0,
                            &#39;startTime&#39;: {
                                &#39;_type_&#39;: 0,
                                &#39;time&#39;: 0
                            },
                            &#39;endTime&#39;: {
                                &#39;_type_&#39;: 0,
                                &#39;time&#39;: 0
                            },
                            &#39;entity&#39;: {
                                &#39;clientName&#39;: &#39;vm1&#39;
                            },
                            &#39;phaseInfo&#39;: {
                                &#39;job&#39;: [
                                    {
                                        &#39;jobid&#39;: 123,
                                        &#39;opType&#39;: 1,
                                        &#39;failure&#39;: {
                                            &#39;errorMessage&#39;: &#39;Error message&#39;
                                        },
                                        &#39;entity&#39;: {
                                            &#39;clientName&#39;: &#39;vm1&#39;,
                                            &#39;_type_&#39;: 0
                                        }
                                    }
                                ]
                            }
                        }]
                    &#39;client&#39;: {
                        &#39;clientId&#39;: 0,
                        &#39;clientName&#39;: &#39;vm1&#39;
                    },
                    &#39;vapp&#39;: {
                        &#39;vAppId&#39;: 1
                    }
                }]
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._REPLICATION_STATS)

        if flag:
            if response.json() and &#39;job&#39; in response.json():
                return response.json()[&#39;job&#39;] or []
            elif response.json() and &#39;errors&#39; in response.json():
                errors = response.json().get(&#39;errors&#39;, [{}])
                error_list = errors[0].get(&#39;errList&#39;, [{}])
                error_code = error_list[0].get(&#39;errorCode&#39;, 0)
                error_message = error_list.get(&#39;errLogMessage&#39;, &#39;&#39;).strip()
                if error_code != 0:
                    response_string = self._commcell_object._update_response_(
                        error_message)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                if response.json():
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _initialize_job_properties(self):
        &#34;&#34;&#34;Initialises the job properties and then initialises the DR job details&#34;&#34;&#34;
        Job._initialize_job_properties(self)
        self._replication_job_stats = self._get_replication_job_stats()

    def blobs_retained(self):
        &#34;&#34;&#34;Returns True if blobs to be retained chosen in failover job for Azure destination&#34;&#34;&#34;
        task_details_json = self.task_details
        dr_opts = task_details_json[&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;].get(&#39;drOrchestrationOption&#39;)
        blobs_retained = dr_opts.get(&#39;retainIntegritySnapshot&#39;) if dr_opts else None
        return blobs_retained

    def get_phases(self):
        &#34;&#34;&#34;
        Gets the DR phases of the job
        Returns: dictionaries of phases for each source and destination VM pair
            {&#34;source_vm_1&#34;: [{
                &#39;phase_name&#39;: enum - Enum of phase short name and full name mapping,
                &#39;phase_status&#39;: int - 0 for success, 1 for failed,
                &#39;start_time&#39;: int - timestamp of start of job,
                &#39;end_time&#39;: int - timestamp of end of job,
                &#39;machine_name&#39;: str - The name of the machine Job is executing on,
                &#39;error_message&#39;: str - Error message, if any,
            }],
            }
        &#34;&#34;&#34;
        job_stats = {}
        if not self._replication_job_stats:
            return job_stats
        for pair_stats in self._replication_job_stats:
            phases = []
            for phase in pair_stats.get(&#39;phase&#39;, []):
                phases.append({
                    &#39;phase_name&#39;: DRJobPhaseToText[DRJobPhases(phase.get(&#39;phase&#39;, &#39;&#39;)).name]
                    if phase.get(&#39;phase&#39;, &#39;&#39;) else &#39;&#39;,
                    &#39;phase_status&#39;: phase.get(&#39;status&#39;, 1),
                    &#39;start_time&#39;: phase.get(&#39;startTime&#39;, {}).get(&#39;time&#39;, &#39;&#39;),
                    &#39;end_time&#39;: phase.get(&#39;endTime&#39;, {}).get(&#39;time&#39;, &#39;&#39;),
                    &#39;machine_name&#39;: phase.get(&#39;entity&#39;, {}).get(&#39;clientName&#39;, &#39;&#39;),
                    &#39;error_message&#39;: phase.get(&#39;phaseInfo&#39;, {}).get(&#39;job&#39;, [{}])[0].get(&#39;failure&#39;, {})
                                     .get(&#39;errorMessage&#39;, &#39;&#39;),
                    &#39;job_id&#39;: str(phase.get(&#39;phaseInfo&#39;, {}).get(&#39;job&#39;, [{}])[0].get(&#39;jobid&#39;, &#39;&#39;)),
                })
            job_stats[str(pair_stats.get(&#39;client&#39;, {}).get(&#39;clientName&#39;, &#39;&#39;))] = phases
        return job_stats

    def get_storagepolicy_restore_vm(self, source):
        &#34;&#34;&#34;
        Fetches storage policy details for RESTORE_VM phases.
        &#34;&#34;&#34;

        fetched_value = {}
        phases = self.get_phases().get(source, [])
        for phase in phases:
            if phase.get(&#39;phase_name&#39;).name == &#34;RESTORE_VM&#34;:
                values = DRJob(self._commcell_object, phase.get(&#39;job_id&#39;)).task_details
                fetched_value = {
                    &#39;copyId&#39;: str(
                        values.get(&#39;subTasks&#39;, [])[0].get(&#39;options&#39;, {}).get(&#39;restoreOptions&#39;, {}).get(
                            &#39;storagePolicy&#39;, {}).get(&#39;copyId&#39;)),
                    &#39;copyName&#39;: str(
                        values.get(&#39;subTasks&#39;, [])[0].get(&#39;options&#39;, {}).get(&#39;restoreOptions&#39;, {}).get(
                            &#39;storagePolicy&#39;, {}).get(&#39;copyName&#39;))
                }

        return fetched_value</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.drorchestration.drjob.DRJob"><code class="flex name class">
<span>class <span class="ident">DRJob</span></span>
<span>(</span><span>commcell_object, job_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing DR orchestration operations on ReplicationMonitor.</p>
<p>Initialise the DR job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drjob.py#L36-L187" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DRJob(Job):
    &#34;&#34;&#34;Class for performing DR orchestration operations on ReplicationMonitor.&#34;&#34;&#34;

    def __init__(self, commcell_object, job_id):
        &#34;&#34;&#34;Initialise the DR job&#34;&#34;&#34;
        self._replication_job_stats = None

        service_url = (commcell_object._services[&#39;DRORCHESTRATION_JOB_STATS&#39;]
                       if commcell_object.commserv_version &gt; 30
                       else commcell_object._services[&#39;DR_JOB_STATS&#39;])
        self._REPLICATION_STATS = service_url % job_id

        Job.__init__(self, commcell_object, job_id)

    def __repr__(self):
        representation_string = &#39;DRJob class instance for job id: &#34;{0}&#34;&#39;
        return representation_string.format(self.job_id)

    def _get_replication_job_stats(self):
        &#34;&#34;&#34;Gets the statistics for the DR Job
        Returns:
                [{
                    &#39;jobId&#39;: 123,
                    &#39;replicationId&#39;: 1,
                    &#39;phase&#39;: [{
                            &#39;phase&#39;: 1,
                            &#39;status&#39;: 0,
                            &#39;startTime&#39;: {
                                &#39;_type_&#39;: 0,
                                &#39;time&#39;: 0
                            },
                            &#39;endTime&#39;: {
                                &#39;_type_&#39;: 0,
                                &#39;time&#39;: 0
                            },
                            &#39;entity&#39;: {
                                &#39;clientName&#39;: &#39;vm1&#39;
                            },
                            &#39;phaseInfo&#39;: {
                                &#39;job&#39;: [
                                    {
                                        &#39;jobid&#39;: 123,
                                        &#39;opType&#39;: 1,
                                        &#39;failure&#39;: {
                                            &#39;errorMessage&#39;: &#39;Error message&#39;
                                        },
                                        &#39;entity&#39;: {
                                            &#39;clientName&#39;: &#39;vm1&#39;,
                                            &#39;_type_&#39;: 0
                                        }
                                    }
                                ]
                            }
                        }]
                    &#39;client&#39;: {
                        &#39;clientId&#39;: 0,
                        &#39;clientName&#39;: &#39;vm1&#39;
                    },
                    &#39;vapp&#39;: {
                        &#39;vAppId&#39;: 1
                    }
                }]
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._REPLICATION_STATS)

        if flag:
            if response.json() and &#39;job&#39; in response.json():
                return response.json()[&#39;job&#39;] or []
            elif response.json() and &#39;errors&#39; in response.json():
                errors = response.json().get(&#39;errors&#39;, [{}])
                error_list = errors[0].get(&#39;errList&#39;, [{}])
                error_code = error_list[0].get(&#39;errorCode&#39;, 0)
                error_message = error_list.get(&#39;errLogMessage&#39;, &#39;&#39;).strip()
                if error_code != 0:
                    response_string = self._commcell_object._update_response_(
                        error_message)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                if response.json():
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _initialize_job_properties(self):
        &#34;&#34;&#34;Initialises the job properties and then initialises the DR job details&#34;&#34;&#34;
        Job._initialize_job_properties(self)
        self._replication_job_stats = self._get_replication_job_stats()

    def blobs_retained(self):
        &#34;&#34;&#34;Returns True if blobs to be retained chosen in failover job for Azure destination&#34;&#34;&#34;
        task_details_json = self.task_details
        dr_opts = task_details_json[&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;].get(&#39;drOrchestrationOption&#39;)
        blobs_retained = dr_opts.get(&#39;retainIntegritySnapshot&#39;) if dr_opts else None
        return blobs_retained

    def get_phases(self):
        &#34;&#34;&#34;
        Gets the DR phases of the job
        Returns: dictionaries of phases for each source and destination VM pair
            {&#34;source_vm_1&#34;: [{
                &#39;phase_name&#39;: enum - Enum of phase short name and full name mapping,
                &#39;phase_status&#39;: int - 0 for success, 1 for failed,
                &#39;start_time&#39;: int - timestamp of start of job,
                &#39;end_time&#39;: int - timestamp of end of job,
                &#39;machine_name&#39;: str - The name of the machine Job is executing on,
                &#39;error_message&#39;: str - Error message, if any,
            }],
            }
        &#34;&#34;&#34;
        job_stats = {}
        if not self._replication_job_stats:
            return job_stats
        for pair_stats in self._replication_job_stats:
            phases = []
            for phase in pair_stats.get(&#39;phase&#39;, []):
                phases.append({
                    &#39;phase_name&#39;: DRJobPhaseToText[DRJobPhases(phase.get(&#39;phase&#39;, &#39;&#39;)).name]
                    if phase.get(&#39;phase&#39;, &#39;&#39;) else &#39;&#39;,
                    &#39;phase_status&#39;: phase.get(&#39;status&#39;, 1),
                    &#39;start_time&#39;: phase.get(&#39;startTime&#39;, {}).get(&#39;time&#39;, &#39;&#39;),
                    &#39;end_time&#39;: phase.get(&#39;endTime&#39;, {}).get(&#39;time&#39;, &#39;&#39;),
                    &#39;machine_name&#39;: phase.get(&#39;entity&#39;, {}).get(&#39;clientName&#39;, &#39;&#39;),
                    &#39;error_message&#39;: phase.get(&#39;phaseInfo&#39;, {}).get(&#39;job&#39;, [{}])[0].get(&#39;failure&#39;, {})
                                     .get(&#39;errorMessage&#39;, &#39;&#39;),
                    &#39;job_id&#39;: str(phase.get(&#39;phaseInfo&#39;, {}).get(&#39;job&#39;, [{}])[0].get(&#39;jobid&#39;, &#39;&#39;)),
                })
            job_stats[str(pair_stats.get(&#39;client&#39;, {}).get(&#39;clientName&#39;, &#39;&#39;))] = phases
        return job_stats

    def get_storagepolicy_restore_vm(self, source):
        &#34;&#34;&#34;
        Fetches storage policy details for RESTORE_VM phases.
        &#34;&#34;&#34;

        fetched_value = {}
        phases = self.get_phases().get(source, [])
        for phase in phases:
            if phase.get(&#39;phase_name&#39;).name == &#34;RESTORE_VM&#34;:
                values = DRJob(self._commcell_object, phase.get(&#39;job_id&#39;)).task_details
                fetched_value = {
                    &#39;copyId&#39;: str(
                        values.get(&#39;subTasks&#39;, [])[0].get(&#39;options&#39;, {}).get(&#39;restoreOptions&#39;, {}).get(
                            &#39;storagePolicy&#39;, {}).get(&#39;copyId&#39;)),
                    &#39;copyName&#39;: str(
                        values.get(&#39;subTasks&#39;, [])[0].get(&#39;options&#39;, {}).get(&#39;restoreOptions&#39;, {}).get(
                            &#39;storagePolicy&#39;, {}).get(&#39;copyName&#39;))
                }

        return fetched_value</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.job.Job" href="../job.html#cvpysdk.job.Job">Job</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.drorchestration.drjob.DRJob.blobs_retained"><code class="name flex">
<span>def <span class="ident">blobs_retained</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns True if blobs to be retained chosen in failover job for Azure destination</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drjob.py#L127-L132" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def blobs_retained(self):
    &#34;&#34;&#34;Returns True if blobs to be retained chosen in failover job for Azure destination&#34;&#34;&#34;
    task_details_json = self.task_details
    dr_opts = task_details_json[&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;].get(&#39;drOrchestrationOption&#39;)
    blobs_retained = dr_opts.get(&#39;retainIntegritySnapshot&#39;) if dr_opts else None
    return blobs_retained</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drjob.DRJob.get_phases"><code class="name flex">
<span>def <span class="ident">get_phases</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the DR phases of the job
Returns: dictionaries of phases for each source and destination VM pair
{"source_vm_1": [{
'phase_name': enum - Enum of phase short name and full name mapping,
'phase_status': int - 0 for success, 1 for failed,
'start_time': int - timestamp of start of job,
'end_time': int - timestamp of end of job,
'machine_name': str - The name of the machine Job is executing on,
'error_message': str - Error message, if any,
}],
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drjob.py#L134-L166" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_phases(self):
    &#34;&#34;&#34;
    Gets the DR phases of the job
    Returns: dictionaries of phases for each source and destination VM pair
        {&#34;source_vm_1&#34;: [{
            &#39;phase_name&#39;: enum - Enum of phase short name and full name mapping,
            &#39;phase_status&#39;: int - 0 for success, 1 for failed,
            &#39;start_time&#39;: int - timestamp of start of job,
            &#39;end_time&#39;: int - timestamp of end of job,
            &#39;machine_name&#39;: str - The name of the machine Job is executing on,
            &#39;error_message&#39;: str - Error message, if any,
        }],
        }
    &#34;&#34;&#34;
    job_stats = {}
    if not self._replication_job_stats:
        return job_stats
    for pair_stats in self._replication_job_stats:
        phases = []
        for phase in pair_stats.get(&#39;phase&#39;, []):
            phases.append({
                &#39;phase_name&#39;: DRJobPhaseToText[DRJobPhases(phase.get(&#39;phase&#39;, &#39;&#39;)).name]
                if phase.get(&#39;phase&#39;, &#39;&#39;) else &#39;&#39;,
                &#39;phase_status&#39;: phase.get(&#39;status&#39;, 1),
                &#39;start_time&#39;: phase.get(&#39;startTime&#39;, {}).get(&#39;time&#39;, &#39;&#39;),
                &#39;end_time&#39;: phase.get(&#39;endTime&#39;, {}).get(&#39;time&#39;, &#39;&#39;),
                &#39;machine_name&#39;: phase.get(&#39;entity&#39;, {}).get(&#39;clientName&#39;, &#39;&#39;),
                &#39;error_message&#39;: phase.get(&#39;phaseInfo&#39;, {}).get(&#39;job&#39;, [{}])[0].get(&#39;failure&#39;, {})
                                 .get(&#39;errorMessage&#39;, &#39;&#39;),
                &#39;job_id&#39;: str(phase.get(&#39;phaseInfo&#39;, {}).get(&#39;job&#39;, [{}])[0].get(&#39;jobid&#39;, &#39;&#39;)),
            })
        job_stats[str(pair_stats.get(&#39;client&#39;, {}).get(&#39;clientName&#39;, &#39;&#39;))] = phases
    return job_stats</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drjob.DRJob.get_storagepolicy_restore_vm"><code class="name flex">
<span>def <span class="ident">get_storagepolicy_restore_vm</span></span>(<span>self, source)</span>
</code></dt>
<dd>
<div class="desc"><p>Fetches storage policy details for RESTORE_VM phases.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drjob.py#L168-L187" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_storagepolicy_restore_vm(self, source):
    &#34;&#34;&#34;
    Fetches storage policy details for RESTORE_VM phases.
    &#34;&#34;&#34;

    fetched_value = {}
    phases = self.get_phases().get(source, [])
    for phase in phases:
        if phase.get(&#39;phase_name&#39;).name == &#34;RESTORE_VM&#34;:
            values = DRJob(self._commcell_object, phase.get(&#39;job_id&#39;)).task_details
            fetched_value = {
                &#39;copyId&#39;: str(
                    values.get(&#39;subTasks&#39;, [])[0].get(&#39;options&#39;, {}).get(&#39;restoreOptions&#39;, {}).get(
                        &#39;storagePolicy&#39;, {}).get(&#39;copyId&#39;)),
                &#39;copyName&#39;: str(
                    values.get(&#39;subTasks&#39;, [])[0].get(&#39;options&#39;, {}).get(&#39;restoreOptions&#39;, {}).get(
                        &#39;storagePolicy&#39;, {}).get(&#39;copyName&#39;))
            }

    return fetched_value</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.job.Job" href="../job.html#cvpysdk.job.Job">Job</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.job.Job.advanced_job_details" href="../job.html#cvpysdk.job.Job.advanced_job_details">advanced_job_details</a></code></li>
<li><code><a title="cvpysdk.job.Job.agent_name" href="../job.html#cvpysdk.job.Job.agent_name">agent_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.attempts" href="../job.html#cvpysdk.job.Job.attempts">attempts</a></code></li>
<li><code><a title="cvpysdk.job.Job.backup_level" href="../job.html#cvpysdk.job.Job.backup_level">backup_level</a></code></li>
<li><code><a title="cvpysdk.job.Job.backupset_name" href="../job.html#cvpysdk.job.Job.backupset_name">backupset_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.client_name" href="../job.html#cvpysdk.job.Job.client_name">client_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.delay_reason" href="../job.html#cvpysdk.job.Job.delay_reason">delay_reason</a></code></li>
<li><code><a title="cvpysdk.job.Job.details" href="../job.html#cvpysdk.job.Job.details">details</a></code></li>
<li><code><a title="cvpysdk.job.Job.end_time" href="../job.html#cvpysdk.job.Job.end_time">end_time</a></code></li>
<li><code><a title="cvpysdk.job.Job.end_timestamp" href="../job.html#cvpysdk.job.Job.end_timestamp">end_timestamp</a></code></li>
<li><code><a title="cvpysdk.job.Job.get_child_jobs" href="../job.html#cvpysdk.job.Job.get_child_jobs">get_child_jobs</a></code></li>
<li><code><a title="cvpysdk.job.Job.get_events" href="../job.html#cvpysdk.job.Job.get_events">get_events</a></code></li>
<li><code><a title="cvpysdk.job.Job.get_vm_list" href="../job.html#cvpysdk.job.Job.get_vm_list">get_vm_list</a></code></li>
<li><code><a title="cvpysdk.job.Job.instance_name" href="../job.html#cvpysdk.job.Job.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.is_finished" href="../job.html#cvpysdk.job.Job.is_finished">is_finished</a></code></li>
<li><code><a title="cvpysdk.job.Job.job_id" href="../job.html#cvpysdk.job.Job.job_id">job_id</a></code></li>
<li><code><a title="cvpysdk.job.Job.job_type" href="../job.html#cvpysdk.job.Job.job_type">job_type</a></code></li>
<li><code><a title="cvpysdk.job.Job.kill" href="../job.html#cvpysdk.job.Job.kill">kill</a></code></li>
<li><code><a title="cvpysdk.job.Job.media_size" href="../job.html#cvpysdk.job.Job.media_size">media_size</a></code></li>
<li><code><a title="cvpysdk.job.Job.num_of_files_transferred" href="../job.html#cvpysdk.job.Job.num_of_files_transferred">num_of_files_transferred</a></code></li>
<li><code><a title="cvpysdk.job.Job.pause" href="../job.html#cvpysdk.job.Job.pause">pause</a></code></li>
<li><code><a title="cvpysdk.job.Job.pending_reason" href="../job.html#cvpysdk.job.Job.pending_reason">pending_reason</a></code></li>
<li><code><a title="cvpysdk.job.Job.phase" href="../job.html#cvpysdk.job.Job.phase">phase</a></code></li>
<li><code><a title="cvpysdk.job.Job.refresh" href="../job.html#cvpysdk.job.Job.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.job.Job.resubmit" href="../job.html#cvpysdk.job.Job.resubmit">resubmit</a></code></li>
<li><code><a title="cvpysdk.job.Job.resume" href="../job.html#cvpysdk.job.Job.resume">resume</a></code></li>
<li><code><a title="cvpysdk.job.Job.size_of_application" href="../job.html#cvpysdk.job.Job.size_of_application">size_of_application</a></code></li>
<li><code><a title="cvpysdk.job.Job.start_time" href="../job.html#cvpysdk.job.Job.start_time">start_time</a></code></li>
<li><code><a title="cvpysdk.job.Job.start_timestamp" href="../job.html#cvpysdk.job.Job.start_timestamp">start_timestamp</a></code></li>
<li><code><a title="cvpysdk.job.Job.state" href="../job.html#cvpysdk.job.Job.state">state</a></code></li>
<li><code><a title="cvpysdk.job.Job.status" href="../job.html#cvpysdk.job.Job.status">status</a></code></li>
<li><code><a title="cvpysdk.job.Job.subclient_name" href="../job.html#cvpysdk.job.Job.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.summary" href="../job.html#cvpysdk.job.Job.summary">summary</a></code></li>
<li><code><a title="cvpysdk.job.Job.task_details" href="../job.html#cvpysdk.job.Job.task_details">task_details</a></code></li>
<li><code><a title="cvpysdk.job.Job.userid" href="../job.html#cvpysdk.job.Job.userid">userid</a></code></li>
<li><code><a title="cvpysdk.job.Job.username" href="../job.html#cvpysdk.job.Job.username">username</a></code></li>
<li><code><a title="cvpysdk.job.Job.wait_for_completion" href="../job.html#cvpysdk.job.Job.wait_for_completion">wait_for_completion</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.drorchestration" href="index.html">cvpysdk.drorchestration</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.drorchestration.drjob.DRJob" href="#cvpysdk.drorchestration.drjob.DRJob">DRJob</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.drjob.DRJob.blobs_retained" href="#cvpysdk.drorchestration.drjob.DRJob.blobs_retained">blobs_retained</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drjob.DRJob.get_phases" href="#cvpysdk.drorchestration.drjob.DRJob.get_phases">get_phases</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drjob.DRJob.get_storagepolicy_restore_vm" href="#cvpysdk.drorchestration.drjob.DRJob.get_storagepolicy_restore_vm">get_storagepolicy_restore_vm</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>