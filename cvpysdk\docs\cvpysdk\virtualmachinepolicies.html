<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.virtualmachinepolicies API documentation</title>
<meta name="description" content="Main file for performing virtual machine policy related operations on the Commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.virtualmachinepolicies</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing virtual machine policy related operations on the Commcell.</p>
<p>VirtualMachinePolicies:
Class for representing all the Virtual Machine Policies associated
with the Commcell</p>
<p>VirtualMachinePolicy:
Class for representing a single Virtual Machine Policy. Contains
method definitions for common methods among all VM Policies</p>
<p>LiveMountPolicy:
Class for representing a single Live Mount Policy associated with
the Commcell; inherits VirtualMachinePolicy</p>
<h2 id="virtualmachinepolicies">Virtualmachinepolicies</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialize the VirtualMachinePolicies instance for
the Commcell</p>
<p><strong>str</strong>()
&ndash;
returns all the Virtual Machine policies associated
with the Commcell</p>
<p><strong>repr</strong>()
&ndash;
returns a string for the instance of the
VirtualMachinePolicies class</p>
<p>_get_vm_policies()
&ndash;
gets all the Virtual Machine policies of the
Commcell</p>
<p>_set_vclient_and_vcenter_names()
&ndash;
sets the virtualization client name; if a
vclient name is passed, checks against the
available virtualization clients, otherwise
sets the first one in the lists the vclient</p>
<p>_prepare_add_vmpolicy_json_default
(vm_policy_options)
&ndash;
sets values for creating the add policy json
that are common across all vm policies</p>
<p>_get_data_center_json
(vm_policy_options)
&ndash;
sets values for creating the datacenter json value
in the add policy json</p>
<p>_set_data_center(vm_policy_options)
&ndash;
sets the datacenter name if provided by user, or
sets the alphabetically lowest one in the
vcenter as default</p>
<p>_get_esx_servers_json
(vm_policy_options)
&ndash;
sets values for creating the esxServers value in
the add policy json</p>
<p>_get_esx_server_list(_datacenter)
&ndash;
returns list of esx servers in the datacenter</p>
<p>_get_data_stores_json
(vm_policy_options)
&ndash;
sets values for creating the datastore value in the
add policy json</p>
<p>_get_datastores_list(_esxservers)
&ndash;
returns list of datastores for all the esx servers
that are specified</p>
<p>_clone_vm_policy(vm_policy_json)
&ndash;
private method to clone a vm policy from
VirtualMachinePolicy object</p>
<p>_prepare_add_vmpolicy_json_livemount
(vm_policy_options)
&ndash;
sets values for creating the add policy json that
are specific for creating Live Mount policy</p>
<p>_security_associations_json
(vm_policy_options)
&ndash;
sets values for creating the security associations
value in the add policy json</p>
<p>_network_names_json
(vm_policy_options)
&ndash;
sets values for creating the network names value in
the add policy json</p>
<p>_media_agent_json(vm_policy_options)
&ndash;
sets values for creating the media agent json
value in the add policy json (only for
Live Mount policy)</p>
<p>_entity_json(vm_policy_options)
&ndash;
sets values for creating the entity json value in
the add policy json</p>
<p>has_policy(vm_policy_name)
&ndash;
checks if a Virtual Machine policy exists with the
given name in a particular instance</p>
<p>get(vm_policy_name)
&ndash;
returns a VirtualMachinePolicy object of the
specified virtual machine policy name</p>
<p>add(vm_policy_name, vm_policy_type,
vclient_name, vm_policy_options)
&ndash;
adds a new Virtual Machine policy to the
VirtualMachinePolicies instance,and returns an
object of corresponding vm_policy_type</p>
<p>delete(vm_policy_name)
&ndash;
removes the specified Virtual Machine policy from
the Commcell</p>
<p>refresh()
&ndash;
refresh the virtual machine policies</p>
<h2 id="virtualmachinepolicy">Virtualmachinepolicy</h2>
<p><strong>new</strong>(
cls,
commcell_object,
vm_policy_name,
vm_policy_type_id,
vm_policy_id=None)
&ndash;
decides which instance object needs to be
created</p>
<p><strong>init</strong>(commcell_object,
vm_policy_name,
vm_policy_type,
VMPolicy_id,
vm_policy_details)
&ndash;
initialize the instance of
VirtualMachinePolicy class for a specific
virtual machine policy of the Commcell</p>
<p><strong>repr</strong>()
&ndash;
returns a string representation of the
VirtualMachinePolicy instance</p>
<p>_get_vm_policy_id()
&ndash;
gets the id of the vm policy</p>
<p>_get_vm_policy_properties()
&ndash;
returns the
policy properties</p>
<p>_update_vm_policy()
&ndash;
updates the vm policy using a PUT request
with the updated properties json.</p>
<p>disable(vm_policy_name)
&ndash;
disables the specified policy, if enabled</p>
<p>enable(vm_policy_name)
&ndash;
enables the specified policy, if disabled</p>
<p>clone(desired_vm_policy_name)
&ndash;
copies properties of the vm policy instance
and creates a new VM Policy with the
specified name</p>
<p>properties()
&ndash;
returns the properties of the vm policy as a
dictionary</p>
<p>refresh()
&ndash;
refresh the virtual machine policy properties</p>
<h2 id="livemountpolicy">Livemountpolicy</h2>
<p><strong>init</strong>(commcell_object,
vm_policy_name,
vm_policy_type,
VMPolicy_id,
vm_policy_details)
&ndash;
initialize the instance of LiveMountPolicy
class for a specific virtual machine
policy of the Commcell</p>
<p><strong>repr</strong>()
&ndash;
returns a string representation of the
LiveMountPolicy instance</p>
<p>_set_mounted_vm_name(live_mount_options)
&ndash;
sets the vm name for the live mounted vm</p>
<p>_prepare_live_mount_json(live_mount_options)
&ndash;
sets values for creating the add policy
json</p>
<p>__associations_json(live_mount_options)
&ndash;
sets the associations value for the live
mount job json</p>
<p>_task_json(live_mount_options)
&ndash;
sets the task value for the live mount job
json</p>
<p>_subtask_json(live_mount_options)
&ndash;
sets the subTask value for the live mount
job json</p>
<p>_one_touch_response_json(live_mount_options)
&ndash;
sets the oneTouchResponse value for the
live mount job json</p>
<p>_hwconfig_json(live_mount_options)
&ndash;
sets the hwConfig value for the live mount
job json</p>
<p>_netconfig_json(live_mount_options)
&ndash;
sets the netConfig value for the live mount
job json</p>
<p>_vm_entity_json(live_mount_options)
&ndash;
sets the vmEntity value for the live mount
job json</p>
<p>_vm_info_json(live_mount_options)
&ndash;
Sets the vmInfo value for the live mount
job json</p>
<p>_is_hidden_client(self, client_name)
&ndash;
checks if specified client is a hidden
client for the Commcell instance</p>
<p>_validate_live_mount(self, client_name)
&ndash;
check if the specified vm has a backup
for live mount</p>
<p>view_active_mounts()
&ndash;
shows all active mounts for the specified
Live Mount Policy instance</p>
<p>live_mount(vm_name,
live_mount_options=None)
&ndash;
run Live Mount for this Live Mount policy
instance</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1-L1638" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing virtual machine policy related operations on the Commcell.

VirtualMachinePolicies:         Class for representing all the Virtual Machine Policies associated
                                    with the Commcell

VirtualMachinePolicy:           Class for representing a single Virtual Machine Policy. Contains
                                    method definitions for common methods among all VM Policies

LiveMountPolicy:                Class for representing a single Live Mount Policy associated with
                                    the Commcell; inherits VirtualMachinePolicy


VirtualMachinePolicies:
    __init__(commcell_object)               --  initialize the VirtualMachinePolicies instance for
                                                    the Commcell

    __str__()                               --  returns all the Virtual Machine policies associated
                                                    with the Commcell

    __repr__()                              --  returns a string for the instance of the
                                                    VirtualMachinePolicies class

    _get_vm_policies()                      --  gets all the Virtual Machine policies of the
                                                    Commcell

    _set_vclient_and_vcenter_names()        --  sets the virtualization client name; if a
                                                    vclient name is passed, checks against the
                                                    available virtualization clients, otherwise
                                                    sets the first one in the lists the vclient

    _prepare_add_vmpolicy_json_default
                    (vm_policy_options)     --  sets values for creating the add policy json
                                                    that are common across all vm policies

    _get_data_center_json
                    (vm_policy_options)     --  sets values for creating the datacenter json value
                                                    in the add policy json

    _set_data_center(vm_policy_options)     --  sets the datacenter name if provided by user, or
                                                    sets the alphabetically lowest one in the
                                                    vcenter as default

    _get_esx_servers_json
                    (vm_policy_options)     --  sets values for creating the esxServers value in
                                                    the add policy json

    _get_esx_server_list(_datacenter)       --  returns list of esx servers in the datacenter

    _get_data_stores_json
                (vm_policy_options)         --  sets values for creating the datastore value in the
                                                    add policy json

    _get_datastores_list(_esxservers)       --  returns list of datastores for all the esx servers
                                                    that are specified

    _clone_vm_policy(vm_policy_json)        --  private method to clone a vm policy from
                                                    VirtualMachinePolicy object

    _prepare_add_vmpolicy_json_livemount
                     (vm_policy_options)    --  sets values for creating the add policy json that
                                                    are specific for creating Live Mount policy

    _security_associations_json
                    (vm_policy_options)     --  sets values for creating the security associations
                                                    value in the add policy json

    _network_names_json
                (vm_policy_options)         --  sets values for creating the network names value in
                                                    the add policy json

    _media_agent_json(vm_policy_options)    --  sets values for creating the media agent json
                                                    value in the add policy json (only for
                                                    Live Mount policy)

    _entity_json(vm_policy_options)         --  sets values for creating the entity json value in
                                                    the add policy json

    has_policy(vm_policy_name)              --  checks if a Virtual Machine policy exists with the
                                                    given name in a particular instance

    get(vm_policy_name)                     --  returns a VirtualMachinePolicy object of the
                                                    specified virtual machine policy name

    add(vm_policy_name, vm_policy_type,
        vclient_name, vm_policy_options)    --  adds a new Virtual Machine policy to the
                                                    VirtualMachinePolicies instance,and returns an
                                                    object of corresponding vm_policy_type

    delete(vm_policy_name)                  --  removes the specified Virtual Machine policy from
                                                    the Commcell

    refresh()                               --  refresh the virtual machine policies


VirtualMachinePolicy:
    __new__(
            cls,
            commcell_object,
            vm_policy_name,
            vm_policy_type_id,
            vm_policy_id=None)                    --  decides which instance object needs to be
                                                          created

    __init__(commcell_object,
             vm_policy_name,
             vm_policy_type,
             VMPolicy_id,
             vm_policy_details)                   --  initialize the instance of
                                                          VirtualMachinePolicy class for a specific
                                                          virtual machine policy of the Commcell

    __repr__()                                    --  returns a string representation of the
                                                          VirtualMachinePolicy instance

    _get_vm_policy_id()                           --  gets the id of the vm policy

    _get_vm_policy_properties()                  --  returns the  policy properties

    _update_vm_policy()                           --  updates the vm policy using a PUT request
                                                          with the updated properties json.

    disable(vm_policy_name)                       --  disables the specified policy, if enabled

    enable(vm_policy_name)                        --  enables the specified policy, if disabled

    clone(desired_vm_policy_name)                 --  copies properties of the vm policy instance
                                                          and creates a new VM Policy with the
                                                          specified name

    properties()                                  --  returns the properties of the vm policy as a
                                                          dictionary

    refresh()                                     --  refresh the virtual machine policy properties

LiveMountPolicy:
    __init__(commcell_object,
             vm_policy_name,
             vm_policy_type,
             VMPolicy_id,
             vm_policy_details)                     --  initialize the instance of LiveMountPolicy
                                                            class for a specific virtual machine
                                                            policy of the Commcell

    __repr__()                                      --  returns a string representation of the
                                                            LiveMountPolicy instance

    _set_mounted_vm_name(live_mount_options)        --  sets the vm name for the live mounted vm

    _prepare_live_mount_json(live_mount_options)    --  sets values for creating the add policy
                                                            json

    __associations_json(live_mount_options)         --  sets the associations value for the live
                                                            mount job json

    _task_json(live_mount_options)                  --  sets the task value for the live mount job
                                                            json

    _subtask_json(live_mount_options)               --  sets the subTask value for the live mount
                                                            job json

    _one_touch_response_json(live_mount_options)    --  sets the oneTouchResponse value for the
                                                            live mount job json

    _hwconfig_json(live_mount_options)              --  sets the hwConfig value for the live mount
                                                            job json

    _netconfig_json(live_mount_options)             --  sets the netConfig value for the live mount
                                                            job json

    _vm_entity_json(live_mount_options)             --  sets the vmEntity value for the live mount
                                                            job json

    _vm_info_json(live_mount_options)               --  Sets the vmInfo value for the live mount
                                                            job json

    _is_hidden_client(self, client_name)            --  checks if specified client is a hidden
                                                            client for the Commcell instance

    _validate_live_mount(self, client_name)         --  check if the specified vm has a backup
                                                            for live mount

    view_active_mounts()                            --  shows all active mounts for the specified
                                                            Live Mount Policy instance

    live_mount(vm_name,
                live_mount_options=None)            --  run Live Mount for this Live Mount policy
                                                            instance
&#34;&#34;&#34;

from __future__ import unicode_literals

from .exception import SDKException
from .job import Job


class VirtualMachinePolicies(object):
    &#34;&#34;&#34;Class for representing all the Virtual Machine Policies associated with the Commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the VirtualMachinePolicies class.

            Args:
                commcell_object (object)  --  instance of the Commcell class
            Returns:
                object - instance of the VirtualMachinePolicies class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._VMPOLICIES_URL = self._commcell_object._services[&#39;VM_ALLOCATION_POLICY&#39;]
        self._ALL_VMPOLICIES_URL = self._commcell_object._services[&#39;ALL_VM_ALLOCATION_POLICY&#39;]
        self._VCLIENTS_URL = self._commcell_object._services[&#39;GET_VIRTUAL_CLIENTS&#39;]
        self._QOPERATION_URL = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        self._vm_policies = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all virtual machine policies of the commcell.

            Returns:
                str - string of all the virtual machine policies associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^28}&#39;.format(&#39;S. No.&#39;, &#39;Virtual Machine Policy&#39;)

        for (index, vm_policy) in enumerate(self._vm_policies):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, vm_policy)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Clients class.&#34;&#34;&#34;
        return &#34;VirtualMachinePolicies class instance for Commcell&#34;

    def _get_vm_policies(self):
        &#34;&#34;&#34;Gets all the virtual machine policies associated to the commcell specified by the
            Commcell object.

            Returns:
                dict - consists of all virtual machine policies for the commcell
                    {
                        &#34;vm_policy1_name&#34;: {
                                                &#34;id&#34;: vm_policy1Id,
                                                &#34;policyType&#34;: policyTypeId
                                            }
                        &#34;vm_policy2_name&#34;: {
                                                &#34;id&#34;: vm_policy2Id,
                                                &#34;policyType&#34;: policyTypeId
                                            }
                    }
            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;GET&#39;, url=self._ALL_VMPOLICIES_URL)

        if flag:
            if response.json() and &#39;policy&#39; in response.json():
                vm_policies = response.json()[&#39;policy&#39;]

                if vm_policies == []:
                    return {}

                vm_policies_dict = {}

                for vm_policy in vm_policies:
                    temp_name = vm_policy[&#39;entity&#39;][&#39;vmAllocPolicyName&#39;].lower()
                    temp_id = str(vm_policy[&#39;entity&#39;][&#39;vmAllocPolicyId&#39;]).lower()
                    temp_policy_type = str(vm_policy[&#39;entity&#39;][&#39;policyType&#39;]).lower()
                    vm_policies_dict[temp_name] = {
                        &#39;id&#39;: temp_id,
                        &#39;policyType&#39;: temp_policy_type
                    }

                return vm_policies_dict
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _set_vclient_and_vcenter_names(self, vm_policy_options, vclient_name):
        &#34;&#34;&#34;Sets the virtualization client name and the vcenter name for the corresponding vclient

            Args:
                vm_policy_options    --  optional policy paramters passed by user (None if user
                                             passes nothing

                vclient_name         --  virtualization client name

            Raises:
                SDKException:
                    if response is not success

                    if no virtualization client exists on the Commcell

                    if virtualization client with given name does not exist on this Commcell
        &#34;&#34;&#34;
        clients = self._commcell_object.clients
        vclient_name_dict = clients._get_virtualization_clients()

        if not vclient_name_dict:
            err_msg = &#39;No virtualization clients exist on this Commcell.&#39;
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

        if vclient_name in vclient_name_dict:
            vm_policy_options[&#39;clientName&#39;] = vclient_name
            # fetching the vcenter from the corresponding instance object
            client = self._commcell_object.clients.get(vm_policy_options[&#39;clientName&#39;])
            agent = client.agents.get(&#39;Virtual Server&#39;)
            instance_keys = next(iter(agent.instances._instances))
            instance = agent.instances.get(instance_keys)
            vm_policy_options[&#39;vCenterName&#39;] = instance.server_host_name[0]
        else:
            err_msg = &#39;Virtualization client &#34;{0}&#34; does not exist&#39;.format(vclient_name)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

    def _get_proxy_client_json(self, options):
        try:
            id_ = self._commcell_object.clients[options.get(&#34;proxy_client&#34;)][&#34;id&#34;]
        except KeyError:
            return dict()
        return{
            &#34;clientId&#34;: int(id_),
            &#34;clientName&#34;: options[&#34;proxy_client&#34;]
        }

    def _prepare_add_vmpolicy_json_default(self, vm_policy_options):
        &#34;&#34;&#34;Sets values for creating the add policy json

            Args:
                vm_policy_options    (dict)  --  main dict containing vm policy options

            Returns:
                vm_policy_json    (dict)  --  json to be passed for add policy POST request
        &#34;&#34;&#34;
        #  setting the json values using functions for elements having nested values
        _datacenter = self._get_data_center_json(vm_policy_options)
        _entity = VirtualMachinePolicies._entity_json(vm_policy_options)
        _esxservers = [{&#34;esxServerName&#34;: esx_server} for esx_server in vm_policy_options.get(&#34;esxServers&#34;, &#34;&#34;)]
        _datastores = [{&#34;dataStoreName&#34;: datastore} for datastore in vm_policy_options.get(&#34;dataStores&#34;, &#34;&#34;)]
        _security_associations = VirtualMachinePolicies._security_associations_json(
            vm_policy_options)
        _network_names = VirtualMachinePolicies._network_names_json(vm_policy_options)

        _vm_policy_json = {
            &#39;action&#39;: 0,        # 0 for add
            &#39;policy&#39;: {
                &#34;vmNameEditType&#34;: vm_policy_options.get(&#34;vm_name_edit&#34;, 1),
                &#34;vmNameEditString&#34;: vm_policy_options.get(&#34;vm_name_edit_string&#34;, &#34;Replicated_&#34;),
                &#34;createIsolatedNetwork&#34;: False,
                &#34;isResourceGroupPolicy&#34;: True,
                &#34;resourcePoolPath&#34;: &#34;//&#34;,
                &#34;destinationHyperV&#34;: {
                    &#34;clientId&#34;: int(self._commcell_object.clients[vm_policy_options[&#39;clientName&#39;]][&#34;id&#34;]),
                    &#34;clientName&#34;: vm_policy_options[&#39;clientName&#39;]
                },
                &#39;allDataStoresSelected&#39;: vm_policy_options.get(&#39;allDataStoresSelected&#39;, False),
                &#39;daysRetainUntil&#39;: vm_policy_options.get(&#39;daysRetainUntil&#39;, -1),
                &#39;migrateVMs&#39;: vm_policy_options.get(&#39;migrateVMs&#39;, False),
                &#39;senderEmailId&#39;: vm_policy_options.get(&#39;senderEmailId&#39;, &#39;&#39;),
                &#39;notifyToEmailIds&#39;: vm_policy_options.get(&#39;notifyToEmailIds&#39;, &#39;&#39;),
                &#39;quotaType&#39;: vm_policy_options.get(&#39;quotaType&#39;, 0),
                &#39;maxVMQuota&#39;: vm_policy_options.get(&#39;maxVMQuota&#39;, 10),
                &#39;namingPattern&#39;: vm_policy_options.get(&#39;namingPattern&#39;, &#39;&#39;),
                &#39;description&#39;: vm_policy_options.get(&#39;description&#39;, &#39;&#39;),
                &#39;enabled&#39;: vm_policy_options.get(&#39;enabled&#39;, True),
                &#39;allowRenewals&#39;: vm_policy_options.get(&#39;allowRenewals&#39;, True),
                &#39;disableSuccessEmail&#39;: vm_policy_options.get(&#39;disableSuccessEmail&#39;, False),
                &#39;performAutoMigration&#39;: vm_policy_options.get(&#39;performAutoMigration&#39;, False),
                &#39;allESXServersSelected&#39;: vm_policy_options.get(&#39;allESXServersSelected&#39;, False),
                &#39;dataCenter&#39;: _datacenter,
                &#39;entity&#39;: _entity,
                &#34;proxyClientEntity&#34;: self._get_proxy_client_json(vm_policy_options),
                &#34;networkList&#34;: [
                    {
                        &#34;destinationNetwork&#34;: vm_policy_options.get(&#34;destination_network&#34;),
                        &#34;sourceNetwork&#34;: &#34;Any Network&#34;
                    }
                ]
            }
        }

        # adding the optional values for the json if they exist
        if _esxservers and not _vm_policy_json[&#39;policy&#39;][&#39;allESXServersSelected&#39;]:
            _vm_policy_json[&#39;policy&#39;][&#39;esxServers&#39;] = _esxservers

        if _datastores and not _vm_policy_json[&#39;policy&#39;][&#39;allDataStoresSelected&#39;]:
            _vm_policy_json[&#39;policy&#39;][&#39;dataStores&#39;] = _datastores

        if _network_names:
            _vm_policy_json[&#39;policy&#39;][&#39;networkNames&#39;] = _network_names

        if _security_associations:
            _vm_policy_json[&#39;policy&#39;][&#39;securityAssociations&#39;] = _security_associations

        # setting json values that are specific to a particular policy type

        if vm_policy_options[&#34;policyType&#34;] == 4:  # for Live Mount policy
            self._prepare_add_vmpolicy_json_livemount(vm_policy_options, _vm_policy_json)
        # TODO: future support for Clone from Template policy
        elif vm_policy_options[&#34;policyType&#34;] == 0:
            pass
        # TODO: future support for Restore from Backup policy
        else:
            pass

        return _vm_policy_json

    def _get_data_center_json(self, vm_policy_options):
        &#34;&#34;&#34;Returns value for the datacenter json value in the add policy json

            Args:
                vm_policy_options    (dict)  --  main dict containing vm policy options

            Returns:
                _datacenter (dict)        --  datacenter json to add to vm policy json
        &#34;&#34;&#34;
        client = self._commcell_object.clients.get(vm_policy_options[&#39;clientName&#39;])
        vm_policy_options[&#39;clientId&#39;] = client.client_id
        agent = client.agents.get(&#39;Virtual Server&#39;)
        instance_keys = next(iter(agent.instances._instances))
        instance = agent.instances.get(instance_keys)
        vm_policy_options[&#39;instanceId&#39;] = instance.instance_id

        # self._set_data_center(vm_policy_options)
        _datacenter = {
            &#39;vCenterName&#39;: vm_policy_options[&#39;vCenterName&#39;],
            &#39;instanceEntity&#39;: {
                &#39;clientId&#39;: int(vm_policy_options[&#39;clientId&#39;]),
                &#39;instanceName&#39;: vm_policy_options[&#39;clientName&#39;],
                &#39;instanceId&#39;: int(vm_policy_options[&#39;instanceId&#39;])
            },
        }

        return _datacenter

    def _set_data_center(self, vm_policy_options):
        &#34;&#34;&#34;Sets the datacenter name if provided by user, or sets the alphabetically lowest one in
            the vcenter as default

            Args:
                vm_policy_options    (dict)  --  main dict containing vm policy options

            Raises:
                SDKException:
                    if specified datacenter is not found for the corresponding virtualization
                     client

                    if no datacenter is found for the virtaulization client

                    if no response is found

                    if response is not a success
        &#34;&#34;&#34;
        get_datacenter_xml = (
            &#39;&lt;?xml version=&#34;1.0&#34; encoding=&#34;UTF-8&#34; standalone=&#34;no&#34; ?&gt;&#39;
            &#39;&lt;Ida_GetDataCenterListReq&gt;&lt;policyType policyType=&#34;4&#34; &#39;
            &#39;vmAllocPolicyName=&#34;&#34; vCenterName=&#34;&#39; + vm_policy_options[&#39;vCenterName&#39;] +
            &#39;&#34;/&gt;&lt;/Ida_GetDataCenterListReq&gt;&#39;
        )
        response_json = self._commcell_object._qoperation_execute(request_xml=get_datacenter_xml)

        if &#39;dataCenterList&#39; in response_json:
            all_nodes = response_json[&#39;dataCenterList&#39;]
            datacenter_dict = {}
            for node in all_nodes:
                if node[&#39;vCenterName&#39;] == vm_policy_options[&#39;vCenterName&#39;]:
                    datacenter_dict[node[&#39;dataCenterName&#39;]] = node[&#39;dataCenterId&#39;]
            if &#39;dataCenterName&#39; in vm_policy_options:
                if vm_policy_options[&#39;dataCenterName&#39;] in datacenter_dict:
                    vm_policy_options[&#39;dataCenterId&#39;] = datacenter_dict[
                        vm_policy_options[&#39;dataCenterName&#39;]]
                else:
                    # if no datacenter is found for the vclient, throw error
                    err_msg = (
                        &#39;No datacenter found with name: {0} in virtual client: {1}&#39;.format(
                            vm_policy_options[&#39;dataCenterName&#39;],
                            vm_policy_options[&#39;clientName&#39;])
                    )
                    raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
            else:
                vm_policy_options[&#39;dataCenterName&#39;] = next(iter(datacenter_dict))
                vm_policy_options[&#39;dataCenterId&#39;] = datacenter_dict[vm_policy_options[
                    &#39;dataCenterName&#39;]]
        else:
            # if no datacenter is found for the vclient, throw error
            err_msg = (&#39;No datacenter found for virtual client: {0}&#39;.format(
                vm_policy_options[&#39;clientName&#39;]))
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

    def _clone_vm_policy(self, vm_policy_json):
        &#34;&#34;&#34;Private method to clone a vm policy from VirtualMachinePolicy object

            Args:
                vm_policy_json    --  dict containing information to clone a particular policy
                                          along with optional information passed by user
            Returns:
                object            --  VirtualMachinePolicy object of the newly cloned policy

            Raises:
                SDKException:
                    if failed to create vm policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;POST&#39;, url=self._ALL_VMPOLICIES_URL, payload=vm_policy_json)

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        o_str = &#39;Failed to create virtual machine policy\nError: &#34;{0}&#34;&#39;.format(
                            error_message)

                        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
                # return object of VirtualMachinePolicy if there is no error in response
                self.refresh()
                return VirtualMachinePolicy(
                    self._commcell_object,
                    vm_policy_json[&#39;policy&#39;][&#39;entity&#39;][&#39;vmAllocPolicyName&#39;],
                    int(vm_policy_json[&#39;policy&#39;][&#39;entity&#39;][&#39;policyType&#39;]),
                    int(self._vm_policies[vm_policy_json[&#39;policy&#39;][&#39;entity&#39;]
                                          [&#39;vmAllocPolicyName&#39;]][&#39;id&#39;])
                )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _prepare_add_vmpolicy_json_livemount(self, vm_policy_options, _vm_policy_json):
        &#34;&#34;&#34;Sets values for creating the add policy json that are specific for creating Live Mount
            policy.

            Args:
                vm_policy_options (dict)  --  vm policy options provided by user

                _vm_policy_json (dict)    --  vm policy json to which Live Mount policy specific
                                            information is added
        &#34;&#34;&#34;
        _media_agent_json = self._media_agent_json(vm_policy_options)

        _vm_policy_json[&#39;policy&#39;][&#39;minutesRetainUntil&#39;] = vm_policy_options.get(
            &#39;minutesRetainUntil&#39;, 1)

        _vm_policy_json[&#39;policy&#39;][&#39;mediaAgent&#39;] = _media_agent_json

    @staticmethod
    def _security_associations_json(vm_policy_options):
        &#34;&#34;&#34;Returns json for the security associations in the add policy json

            Args:
                vm_policy_options (dict)  --  vm policy options provided by user
        &#34;&#34;&#34;
        _users = []
        if &#39;users&#39; in vm_policy_options:
            # TODO: get user info using REST API. For every user, add user dict to _users
            pass
        else:
            # default - admin
            default_user = {
                &#34;_type_&#34;: 13,
                &#34;userGUID&#34;: &#34;admin&#34;,
                &#34;userName&#34;: &#34;admin&#34;,
                &#34;userId&#34;: 1
            }
            _users.append(default_user)

        _usergroups = []
        if &#39;userGroups&#39; in vm_policy_options:
            # TODO: get usergroups info using REST API. For every userGroup, add corresponding dict
            pass

        _security_associations = {}
        if _users:
            _security_associations[&#39;users&#39;] = _users
        if _usergroups:
            _security_associations[&#39;userGroups&#39;] = _usergroups

        return _security_associations

    @staticmethod
    def _network_names_json(vm_policy_options):
        &#34;&#34;&#34;Returns list of network names for the add policy json

            Args:
                vm_policy_options (dict)    --  vm policy options provided by user

            Returns:
                _network_names   (list)     --  list of network names (str)
        &#34;&#34;&#34;
        _network_names = []
        if &#39;networkNames&#39; in vm_policy_options:
            for network in vm_policy_options[&#39;networkNames&#39;]:
                _network_names.append(network)

        return _network_names

    def _media_agent_json(self, vm_policy_options):
        &#34;&#34;&#34;Returns json for the media agent json value in the add policy json (only for LM)

            Args:
                vm_policy_options (dict)  --  vm policy options provided by user (optional)

            Returns:
                _media_agent_json (dict)  --  json containing media agent information if media
                                                agent info is passed by user
        &#34;&#34;&#34;
        _media_agent_json = {}
        if &#39;mediaAgent&#39; in vm_policy_options:
            # TODO: there can be only one MA -- validate this (whole vm_policy_options)
            media_agent = vm_policy_options[&#39;mediaAgent&#39;]
            if not self._commcell_object.media_agents.has_media_agent(media_agent):
                raise SDKException(
                    &#39;Virtual Machine&#39;, &#39;102&#39;,
                    &#39;No media agent exists &#34;{0}&#34; exists in commserv &#34;{1}&#34;&#39;.format(
                        media_agent, self._commcell_object.commserv_name))
            else:
                _media_agent_json[&#39;clientName&#39;] = media_agent
        else:   # adding a default media agent for automation
            media_agent_dict = self._commcell_object.media_agents._media_agents
            media_agent = [ma for ma in media_agent_dict][0]
            _media_agent_json[&#39;clientName&#39;] = media_agent

        return _media_agent_json

    @staticmethod
    def _entity_json(vm_policy_options):
        &#34;&#34;&#34;Returns json for the entity  attribute in the add policy json

            Args:
                vm_policy_options  (dict)    --  vm policy options provided by user

            Returns:
                _entity            (dict)    --  json for the entity attribute in add policy json
        &#34;&#34;&#34;
        _entity = {
            &#39;vmAllocPolicyName&#39;: vm_policy_options[&#39;vmAllocPolicyName&#39;],
            &#39;_type_&#39;: 93,           # hardcoded
            &#39;policyType&#39;: vm_policy_options[&#34;policyType&#34;],
            &#39;region&#39;: {},
        }

        return _entity

    def has_policy(self, vm_policy_name):
        &#34;&#34;&#34;Checks if a Virtual Machine policy exists with the given name

            Args:
                policy_name (str)  --  name of the vm policy

            Returns:
                bool - boolean output whether the vm policy exists in the commcell or not

            Raises:
                SDKException:
                    if type of the vm policy name argument is not string
        &#34;&#34;&#34;
        if not isinstance(vm_policy_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)

        return (self._vm_policies and
                vm_policy_name.lower() in self._vm_policies)

    def get(self, vm_policy_name):
        &#34;&#34;&#34;Returns a VirtualMachinePolicy object of the specified virtual machine policy name.

            Args:
                vm_policy_name     (str)   --  name of the virtual machine policy

            Returns:
                object - instance of the VirtualMachinePolicy class for the given policy name

            Raises:
                SDKException:
                    if type of the virtual machine policy name argument is not string
                    if no virtual machine policy exists with the given name
        &#34;&#34;&#34;
        if not isinstance(vm_policy_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)

        vm_policy_name = vm_policy_name.lower()
        if self.has_policy(vm_policy_name):
            vm_policy_type_id = int(self._vm_policies[vm_policy_name][&#39;policyType&#39;])
            return VirtualMachinePolicy(
                self._commcell_object,
                vm_policy_name=vm_policy_name,
                vm_policy_type_id=vm_policy_type_id,
                vm_policy_id=int(self._vm_policies[vm_policy_name][&#39;id&#39;])
            )
        else:
            raise SDKException(
                &#39;Virtual Machine&#39;,
                &#39;102&#39;,
                &#39;No policy exists with name: {0}&#39;.format(vm_policy_name))

    def add(
            self,
            vm_policy_name,
            vm_policy_type,
            vclient_name,
            vm_policy_options=None
    ):
        &#34;&#34;&#34;Adds a new Virtual Machine Policy to the Commcell.

            Args:
                vm_policy_name       (str)   --  name of the new virtual machine policy to add to
                                                    the Commcell instance

                vm_policy_type       (str)   --  type of virtual machine policy to be added
                                                    [&#34;Live Mount&#34;, &#34;Clone From Template&#34;,
                                                    &#34;Restore From Backup&#34;]

                vclient_name         (str)   --  the name of the virtualization client under which
                                                    vm policy is to be added

                vm_policy_options    (dict)  --  optional dictionary passed by user to create a vm
                                                   policy. Allowed key-value pairs and input types
                                                   are given below
                    default: None

                    &#34;allDataStoresSelected&#34;    (Boolean)  : if all data stores are to be selected;
                                                                matters only if migrateVMs is set
                                                                to True,
                    &#34;daysRetainUntil&#34;          (int)      : how many days to retain backup until,
                    &#34;migrateVMs&#34;               (Boolean)  : migrate to datastore after expiry
                                                                (only for LiveMount),
                    &#34;senderEmailId&#34;            (str)      : email id of sender,
                    &#34;minutesRetainUntil&#34;       (int)      : how many days to retain backup until
                    &#34;notifyToEmailIds&#34;         (str)      : email id&#39;s to notify to; multiple
                                                                emails separated by a comma
                    &#34;quotaType&#34;                (int)      : number of vm&#39;s/live mounts/labs per
                                                                user,
                    &#34;maxVMQuota&#34;               (int)      : maximum number of VM quota,
                    &#34;namingPattern&#34;            (str)      : naming patter,
                    &#34;description&#34;              (str)      : description of vm policy,
                    &#34;enabled&#34;                  (Boolean)  : whether vm policy is enabled or not,
                    &#34;allowRenewals&#34;            (Boolean)  : whether to allow renewals or not,
                    &#34;disableSuccessEmail&#34;      (Boolean)  : send email on succesful creation of vm
                                                                policy,
                    &#34;allESXServersSelected&#34;    (Boolean)  : select all esx servers in the vcenter,
                    &#34;dataCenterName&#34;           (str)      : data center name for vm policy,
                    &#34;dataStores&#34;               list(str)  : list of data store names,
                    &#34;esxServers&#34;               list(str)  : list of esx server names,
                    &#34;users&#34;                    list(str)  : list of users (user-names) to add to vm
                                                                policy,
                    &#34;userGroups&#34;               list(str)  : list of usergroups (usergroup-names) to
                                                                add to vm policy,
                    &#34;networkNames&#34;             list(str)  : list of network names,
                    ------------------------ only for Live Mount ------------------------
                    &#34;mediaAgent&#34;               (str)      : media agent name for Live Mount,
                    &#34;performAutoMigration&#34;     (Boolean)  : automatic migration of vm

            Returns:
                object    --  object of the corresponding virtual machine policy type

            Raises:
                SDKException:
                    if type of the vm policy name argument is not string

                    if type of the vcenter name argument is not string

                    if type of virtualization client name argument is not string or None

                    if policy type is not one of the virtual machine policy types as defined

                    if the type of vm_policy_options is not dict or None

                    if vm policy already exists with the given name (case insensitive)

                    if failed to create vm policy

                    if response is empty

                    if response is not success
                &#34;&#34;&#34;
        vm_policy_name = vm_policy_name.lower()
        vm_policy_type = vm_policy_type.lower()
        vclient_name = vclient_name.lower()
        _vm_policy_types = {&#39;live mount&#39;: 4,
                            &#39;clone from template&#39;: 0,
                            &#39;restore from backup&#39;: 13}
        self.refresh()
        if (
                not isinstance(vm_policy_name, str)
                or not isinstance(vclient_name, str)
                or not isinstance(vm_policy_options, (dict, type(None)))
        ):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)
        elif vm_policy_type not in _vm_policy_types:
            err_msg = &#39;{0} is not a valid virtual machine policy type.&#39;.format(
                vm_policy_type)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        elif self.has_policy(vm_policy_name):
            err_msg = &#39;Virtual Machine Policy &#34;{0}&#34; already exists (not case sensitive)&#39;.format(
                vm_policy_name)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        else:
            if not vm_policy_options:
                vm_policy_options = {}
            vm_policy_options[&#39;vmAllocPolicyName&#39;] = vm_policy_name.lower()

            # setting the vclient name, vcenter name and policy type
            self._set_vclient_and_vcenter_names(vm_policy_options, vclient_name)
            vm_policy_options[&#39;policyType&#39;] = _vm_policy_types[vm_policy_type]

            # preparing the json values for adding the new policy
            _vm_policy_json = self._prepare_add_vmpolicy_json_default(vm_policy_options)

            # passing the built json to create the vm policy
            (flag, response) = self._commcell_object._cvpysdk_object.make_request(
                method=&#39;POST&#39;, url=self._VMPOLICIES_URL, payload=_vm_policy_json)

            if flag:
                if response.json():
                    if &#39;error&#39; in response.json():
                        if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                            error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                            o_str = &#39;Failed to create virtual machine policy\nError: &#34;{0}&#34;&#39;.format(
                                error_message)
                            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
                    # returning object of VirtualMachinePolicy if there is no error in response
                    self.refresh()
                    return VirtualMachinePolicy(
                        self._commcell_object,
                        vm_policy_name=vm_policy_options[&#39;vmAllocPolicyName&#39;],
                        vm_policy_type_id=int(vm_policy_options[&#39;policyType&#39;]),
                        vm_policy_id=int(self._vm_policies[vm_policy_name][&#39;id&#39;]))
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, vm_policy_name):
        &#34;&#34;&#34;Deletes the specified virtual machine policy from the commcell.

            Args:
                vm_policy_name (str)  --  name of the virtual machine policy to delete

            Raises:
                SDKException:
                    if type of the virtual machine policy name argument is not string

                    if failed to delete virtual machine policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(vm_policy_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)

        if self.has_policy(vm_policy_name):
            # retrieving the corresponding policy id for API call
            vm_policy_id = self._get_vm_policies()[vm_policy_name][&#39;id&#39;]
            policy_delete_url = self._VMPOLICIES_URL + &#39;/{0}&#39;.format(vm_policy_id)

            (flag, response) = self._commcell_object._cvpysdk_object.make_request(
                &#39;DELETE&#39;, policy_delete_url)

            if flag:
                try:
                    if response.json():
                        if &#39;errorCode&#39; in response.json() and &#39;errorMessage&#39; in response.json():
                            error_message = response.json()[&#39;errorMessage&#39;]
                            output_string = &#39;Failed to delete virtual machine policy\nError: &#34;{0}&#34;&#39;
                            raise SDKException(
                                &#39;Virtual Machine&#39;, &#39;102&#39;, output_string.format(error_message))
                except ValueError:
                    if response.text:
                        self.refresh()
                        return response.text.strip()
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        else:
            raise SDKException(
                &#39;Virtual Machine&#39;,
                &#39;102&#39;,
                &#39;No policy exists with name: {0}&#39;.format(vm_policy_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the Virtual Machine policies.&#34;&#34;&#34;
        self._vm_policies = self._get_vm_policies()


class VirtualMachinePolicy(object):
    &#34;&#34;&#34;Class for representing a single Virtual Machine Policy. Contains method definitions for
        common operations among all VM Policies&#34;&#34;&#34;

    def __new__(
            cls,
            commcell_object,
            vm_policy_name,
            vm_policy_type_id,
            vm_policy_id=None
    ):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;
        if vm_policy_type_id == 4 or vm_policy_type_id == 2:  # for &#39;Live Mount&#39;
            return object.__new__(LiveMountPolicy)
        # TODO: future support for &#39;Clone From Template&#39;
        elif vm_policy_type_id == 6:
            return object.__new__(VirtualMachinePolicy)
        # TODO: future support for &#39;Restore From Backup&#39;
        else:
            return object.__new__(VirtualMachinePolicy)

    def __init__(
            self,
            commcell_object,
            vm_policy_name,
            vm_policy_type_id,
            vm_policy_id=None
    ):
        &#34;&#34;&#34;Initialize object of the VirtualMachinePolicy class.

            Args:
                commcell_object      (object)  --  instance of the Commcell class
                vm_policy_name       (str)     --  name of the vm policy to be created
                vm_policy_type_id    (int)     --  type of policy (integer code for vm policy)
                vm_policy_id         (int)     --  vm policy id if available (optional)

            Returns:
                object                       -- instance of the VirtualMachinePolicy class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._vm_policy_name = vm_policy_name
        self._vm_policy_type_id = vm_policy_type_id

        if vm_policy_id:
            self._vm_policy_id = str(vm_policy_id)
        else:
            self._vm_policy_id = self._get_vm_policy_id()

        self._VM_POLICY_URL = (self._commcell_object._services[&#39;GET_VM_ALLOCATION_POLICY&#39;]
                               % self._vm_policy_id)

        self._vm_policy_properties = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of this class.&#34;&#34;&#34;
        return (&#34;VirtualMachinePolicy class instance for Virtual Machine Policy: &#39;{0}&#39; for &#34;
                &#34;Commcell: &#39;{1}&#39;&#34;.format(self.vm_policy_name, self._commcell_object.commserv_name))

    def _get_vm_policy_id(self):
        &#34;&#34;&#34;Gets the virtual machine policy id associated with the svirtual machine policy&#34;&#34;&#34;
        vm_policies = VirtualMachinePolicies(self._commcell_object)
        return vm_policies.get(self.vm_policy_name).vm_policy_id

    def _get_vm_policy_properties(self):
        &#34;&#34;&#34;Gets the properties of the virtual machine policy.

            Returns:
                dict    --  dictionary consisting of the properties of this vm policy

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._VM_POLICY_URL
        )

        if flag:
            if response.json()[&#39;policy&#39;][0]:        # API returns an array with one element
                return response.json()[&#39;policy&#39;][0]
            else:
                raise SDKException(&#39;Response&#39;, 102)
        else:
            response_str = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, 101, response_str)

    def _update_vm_policy(self):
        &#34;&#34;&#34;Updates the vm policy using a PUT request with the updated properties json.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        update_policy_json = {
            &#39;action&#39;: 1,        # action 1 for PUT
            &#39;policy&#39;: self._vm_policy_properties
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._VM_POLICY_URL, update_policy_json
        )

        self.refresh()

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        o_str = &#39;Failed to update virtual machine policy\nError: &#34;{0}&#34;&#39;.format(
                            error_message)
                        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_str = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_str)

    @property
    def vm_policy_name(self):
        &#34;&#34;&#34;Treats the virtual machine policy name as a read-only attribute.&#34;&#34;&#34;
        return self._vm_policy_name

    @property
    def vm_policy_id(self):
        &#34;&#34;&#34;Treats the virtual machine policy id as a read-only attribute.&#34;&#34;&#34;
        return self._vm_policy_id

    @property
    def vm_policy_type_id(self):
        &#34;&#34;&#34;Treats the virtual machine policy type id as a read-only attribute.&#34;&#34;&#34;
        return self._vm_policy_type_id

    def disable(self):
        &#34;&#34;&#34;Disables a virtual machine policy if it is enabled.

            Raises:
                SDKException:
                    if vm policy is already disabled
        &#34;&#34;&#34;
        if not self._vm_policy_properties[&#39;enabled&#39;]:
            err_msg = &#39;Policy is already disabled&#39;
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

        self._vm_policy_properties[&#39;enabled&#39;] = False
        self._update_vm_policy()

    def enable(self):
        &#34;&#34;&#34;Enables a virtual machine policy if it is disabled.

                    Raises:
                        SDKException:
                            if vm policy is already enabled
                &#34;&#34;&#34;
        if self._vm_policy_properties[&#39;enabled&#39;]:
            err_msg = &#39;Policy is already enabled&#39;
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

        self._vm_policy_properties[&#39;enabled&#39;] = True
        self._update_vm_policy()

    def clone(self, desired_vm_policy_name):
        &#34;&#34;&#34;
        copies properties of the particular VM Policy and creates a new VM Policy with the
         specified name

        Args:
            desired_vm_policy_name   (str)  --  name of the policy that is going to be created

        Returns:
            object                          --  object of the Virtual Machine Policy

        Raises:
                SDKException:
                    if type of the desired vm policy name argument is not string

                    if a vm policy already exists by the desired vm policy name
        &#34;&#34;&#34;
        vm_policies_object = VirtualMachinePolicies(self._commcell_object)
        if not isinstance(desired_vm_policy_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)
        elif vm_policies_object.has_policy(desired_vm_policy_name):
            err_msg = &#39;Policy &#34;{0}&#34; already exists&#39;.format(desired_vm_policy_name)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        else:
            import copy
            desired_vm_policy_properties = copy.deepcopy(self._vm_policy_properties)
            desired_vm_policy_name = desired_vm_policy_name.lower()
            desired_vm_policy_properties[&#39;entity&#39;][&#39;vmAllocPolicyName&#39;] = desired_vm_policy_name
            del desired_vm_policy_properties[&#39;entity&#39;][&#39;vmAllocPolicyId&#39;]
            desired_vm_policy_json = {
                &#39;action&#39;: 0,
                &#39;policy&#39;: desired_vm_policy_properties
            }

            return vm_policies_object._clone_vm_policy(desired_vm_policy_json)

    # TODO: modify(self, vm_policy_details) - Modifies the policy as per the details passed

    def properties(self):
        &#34;&#34;&#34;Returns the virtual machine properties&#34;&#34;&#34;
        return self._vm_policy_properties

    def refresh(self):
        &#34;&#34;&#34;Refresh the Virtual Machine policy properties.&#34;&#34;&#34;
        self._vm_policy_properties = self._get_vm_policy_properties()


class LiveMountPolicy(VirtualMachinePolicy):
    &#34;&#34;&#34;Derived class from VirtualMachinePolicy base class for representing a single Live Mount
       Policy. Contains method definitions for operations specific for Live Mount and also
       runnning Live Mount job&#34;&#34;&#34;

    def __init__(
            self,
            commcell_object,
            vm_policy_name,
            vm_policy_type_id,
            vm_policy_id=None
    ):
        &#34;&#34;&#34;Initialize object of the LiveMountPolicy class.
            Args:
                commcell_object      (object)  --  instance of the Commcell class
                vm_policy_name       (str)     --  name of the Live Mount policy
                vm_policy_type_id    (int)     -- policy type id
                vm_policy_id         (int)     --  id of the Live Mount policy, if available

            Returns:
                object                       -- instance of the LiveMountPolicy class
        &#34;&#34;&#34;
        super(LiveMountPolicy, self).__init__(commcell_object,
                                              vm_policy_name,
                                              vm_policy_type_id,
                                              vm_policy_id)
        self._LIVE_MOUNT_JOB_URL = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        self._QOPERATION_URL = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]
        self._live_mounted_vm_name = None

    def _prepare_live_mount_json(self, live_mount_options):
        &#34;&#34;&#34;Sets values for creating the add policy json
            Args:
                live_mount_options (dict)  --  live mount job  options provided by user
        &#34;&#34;&#34;
        self._set_mounted_vm_name(live_mount_options)
        self._live_mounted_vm_name = live_mount_options[&#39;vmName&#39;]

        _associations = LiveMountPolicy.__associations_json(live_mount_options)
        _task = LiveMountPolicy._task_json()
        _subtask = LiveMountPolicy._subtask_json()
        _one_touch_response = LiveMountPolicy._one_touch_response_json(live_mount_options)
        _vm_entity = LiveMountPolicy._vm_entity_json(live_mount_options)
        _vm_info = LiveMountPolicy._vm_info_json(live_mount_options)

        # TODO: only if live mount is scheduled (non default)

        # TODO: _pattern = live_mount_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;]

        # TODO:  backupOpts = live_mount_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;]
        live_mount_json = {
            &#39;taskInfo&#39;: {
                &#39;associations&#39;: _associations,
                &#39;task&#39;: _task,
                &#39;subTasks&#39;: [
                    {
                        &#39;subTaskOperation&#39;: 1,
                        &#39;subTask&#39;: _subtask,
                        &#39;options&#39;: {
                            &#39;adminOpts&#39;: {
                                &#39;vmProvisioningOption&#39;: {
                                    &#39;operationType&#39;: 23,
                                    &#39;virtualMachineOption&#39;: [
                                        {
                                            &#39;powerOnVM&#39;: True,
                                            &#39;flags&#39;: 0,
                                            &#39;useLinkedClone&#39;: False,
                                            &#39;vendor&#39;: 1,
                                            &#39;doLinkedCloneFromLocalTemplateCopy&#39;: False,
                                            &#39;vmAllocPolicy&#39;: {
                                                &#39;vmAllocPolicyName&#39;: self._vm_policy_name
                                            },
                                            &#39;oneTouchResponse&#39;: _one_touch_response,
                                            &#39;vmEntity&#39;: _vm_entity,
                                            &#39;vmInfo&#39;: _vm_info
                                        }
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        }
        return live_mount_json

    def _set_mounted_vm_name(self, live_mount_options):
        &#34;&#34;&#34;
        Sets the vm name for the live mounted vm

        Args:
                live_mount_options    (dict)  --  live mount job options

        Raises:
            SDK Exception:
                if user passes a vm name that already exists as a hidden client on the Commcell
        &#34;&#34;&#34;
        clients = self._commcell_object.clients
        if &#39;vmName&#39; in live_mount_options:
            if live_mount_options[&#39;vmName&#39;].lower() in clients._hidden_clients:
                err_msg = &#39;A client already exists by the name &#34;{0}&#34;&#39;.format(
                    live_mount_options[&#39;vmName&#39;])
                raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        else:
            vm_name = live_mount_options[&#39;clientName&#39;] + &#39;VM&#39;
            digit = 1
            while vm_name.lower() in clients._hidden_clients:
                vm_name += str(digit)
            live_mount_options[&#39;vmName&#39;] = vm_name

    @staticmethod
    def __associations_json(live_mount_options):
        &#34;&#34;&#34;
        Sets the associations value for the live mount job json

            Args:
                live_mount_options    (dict)  --  live mount job options

            Returns:
                _associations          (list)  --  list containing the associations value
        &#34;&#34;&#34;
        _associations = []
        _associations_element = {
            # &#39;type&#39;: 0,
            &#39;clientName&#39;: live_mount_options[&#39;clientName&#39;],
            # &#39;clientSidePackage&#39;: True,
            &#39;subclientName&#39;: &#39;&#39;,
            &#39;backupsetName&#39;: &#39;&#39;,
            &#39;instanceName&#39;: &#39;&#39;,
            &#39;appName&#39;: &#39;&#39;,
            # &#39;consumeLicense&#39;: True
        }
        _associations.append(_associations_element)
        return _associations

    @staticmethod
    def _task_json():
        &#34;&#34;&#34;Sets the task value for the live mount job json

            Returns:
                _task                 (dict)  --  dict containing the task value
        &#34;&#34;&#34;
        _task = {
            &#39;taskType&#39;: 1,
            &#39;initiatedFrom&#39;: 2,
            &#39;alert&#39;: {
                &#39;alertName&#39;: &#39;&#39;
            },
            &#39;taskFlags&#39;: {
                &#39;disabled&#39;: False
            }
        }

        # TODO: if &#39;schedule&#39; is there in options, change 06 07 json

        return _task

    @staticmethod
    def _subtask_json():
        &#34;&#34;&#34;Sets the subTask value for the live mount job json

            Returns:
                _subtask              (dict)  --  dict containing the subTask value
        &#34;&#34;&#34;
        _subtask = {
            &#39;subTaskType&#39;: 1,
            &#39;operationType&#39;: 4038
        }

        # TODO: if &#39;schedule&#39; in live_mount_options: add subTaskName to json

        return _subtask

    @staticmethod
    def _one_touch_response_json(live_mount_options):
        &#34;&#34;&#34;Sets the oneTouchResponse value for the live mount job json

            Args:
                live_mount_options     (dict)  --  live mount job options

            Returns:
                _one_touch_response    (dict)  --  dict containing the oneTouchResponse value
        &#34;&#34;&#34;
        _csinfo = LiveMountPolicy._csinfo_json(live_mount_options)
        _hwconfig = LiveMountPolicy._hwconfig_json(live_mount_options)
        _netconfig = LiveMountPolicy._netconfig_json()
        _one_touch_response = {
            &#39;copyPrecedence&#39;: live_mount_options.get(&#39;copyPrecedence&#39;, 0),
            &#39;version&#39;: &#39;&#39;,
            &#39;platform&#39;: 0,
            &#39;dateCreated&#39;: &#39;&#39;,
            &#39;automationTest&#39;: False,
            &#39;autoReboot&#39;: True,
            &#39;csinfo&#39;: _csinfo,
            &#39;hwconfig&#39;: _hwconfig,
            &#39;netconfig&#39;: _netconfig,
            &#39;dataBrowseTime&#39;: live_mount_options.get(&#39;pointInTime&#39;, {}),
            &#39;maInfo&#39;: {
                &#39;clientName&#39;: &#39;&#39;
            },
            &#39;datastoreList&#39;: {}
        }

        return _one_touch_response

    @staticmethod
    def _csinfo_json(live_mount_options):
        &#34;&#34;&#34;Sets the csinfo value for the live mount job json

            Args:
                live_mount_options     (dict)  --  live mount job options

            Returns:
                _csinfo                (dict)  --  dict containing the hwconfig value
        &#34;&#34;&#34;
        _csinfo = {
            &#34;firewallPort&#34;: 0,
            &#34;cvdPort&#34;: 0,
            &#34;evmgrPort&#34;: 0,
            &#34;fwClientGroupName&#34;: &#34;&#34;,
            &#34;mediaAgentInfo&#34;: {},
            &#34;mediaAgentIP&#34;: {},
            &#34;ip&#34;: {},
            &#34;commservInfo&#34;: {},
            &#34;creds&#34;: {
                &#34;password&#34;: &#34;&#34;,
                &#34;domainName&#34;: &#34;&#34;,
                &#34;confirmPassword&#34;: &#34;&#34;,
                &#34;userName&#34;: &#34;&#34;
            }
        }

        return _csinfo

    @staticmethod
    def _hwconfig_json(live_mount_options):
        &#34;&#34;&#34;Sets the hwconfig value for the live mount job json

            Args:
                live_mount_options     (dict)  --  live mount job options

            Returns:
                _hwconfig              (dict)  --  dict containing the hwconfig value
        &#34;&#34;&#34;
        _hwconfig = {
            &#39;vmName&#39;: live_mount_options[&#39;vmName&#39;],
            &#39;magicno&#39;: &#39;&#39;,
            &#39;bootFirmware&#39;: 0,
            &#39;version&#39;: &#39;&#39;,
            &#39;mem_size&#39;: 0,
            &#39;cpu_count&#39;: 0,
            &#39;nic_count&#39;: 0,
            &#39;overwriteVm&#39;: False,
            &#39;useMtptSelection&#39;: False,
            &#39;ide_count&#39;: 0,
            &#39;mtpt_count&#39;: 0,
            &#39;scsi_count&#39;: 0,
            &#39;diskType&#39;: 1,
            &#39;optimizeStorage&#39;: False,
            &#39;systemDisk&#39;: {
                &#39;forceProvision&#39;: False,
                &#39;bus&#39;: 0,
                &#39;refcnt&#39;: 0,
                &#39;size&#39;: 0,
                &#39;name&#39;: &#39;&#39;,
                &#39;dataStoreName&#39;: &#39;&#39;,
                &#39;vm_disk_type&#39;: 0,
                &#39;slot&#39;: 0,
                &#39;diskType&#39;: 1,
                &#39;tx_type&#39;: 0
            }
        }

        return _hwconfig

    @staticmethod
    def _netconfig_json():
        &#34;&#34;&#34;Sets the netconfig value for the live mount job json

            Returns:
                _netconfig             (dict)  --  dict containing the netconfig value
        &#34;&#34;&#34;
        _netconfig = {
            &#39;wins&#39;: {
                &#39;useDhcp&#39;: False
            },
            &#39;firewall&#39;: {
                &#39;certificatePath&#39;: &#39;&#39;,
                &#39;certificateBlob&#39;: &#39;&#39;,
                &#39;configBlob&#39;: &#39;&#39;
            },
            &#39;dns&#39;: {
                &#39;suffix&#39;: &#39;&#39;,
                &#39;useDhcp&#39;: False
            },
            &#39;ipinfo&#39;: {
                &#39;defaultgw&#39;: &#39;&#39;
            }
        }

        return _netconfig

    @staticmethod
    def _vm_entity_json(live_mount_options):
        &#34;&#34;&#34;Sets the vmEntity value for the live mount job json
            Args:
                live_mount_options    (dict)  --  live mount job options

            Returns:
                _vm_entity            (dict)  --  dict containing the vmEntity value
        &#34;&#34;&#34;
        _vm_entity = {
            &#39;vmName&#39;: live_mount_options[&#39;vmName&#39;],
            &#39;clientName&#39;: live_mount_options[&#39;clientName&#39;],
            &#39;_type_&#39;: 88
        }

        return _vm_entity

    @staticmethod
    def _vm_info_json(live_mount_options):
        &#34;&#34;&#34;Sets the vmInfo value for the live mount job json
            Args:
                live_mount_options    (dict)  --  live mount job options

            Returns:
                _vm_info              (dict)  --  dict containing the vmInfo value
        &#34;&#34;&#34;
        _vm_info = {
            &#39;advancedProperties&#39;: {
                &#39;networkCards&#39;: [
                    {
                        &#39;label&#39;: live_mount_options.get(&#39;network_name&#39;, &#39;&#39;)
                    }
                ]
            },
            &#39;vm&#39;: {
                &#39;vmName&#39;: live_mount_options[&#39;vmName&#39;],
                &#39;_type_&#39;: 88
            }
        }

        # TODO: if &#39;original network&#39; is chosen as option in livemount option, verify network json

        return _vm_info

    def _is_hidden_client(self, client_name):
        &#34;&#34;&#34;Checks if specified client is a hidden client for the Commcell instance

            Args:
                client_name    (str)  -- name of the client

            Returns:
                bool                  -- boolean output whether the client is indeed a hidden
                                            client in the Commcell
        &#34;&#34;&#34;
        clients = self._commcell_object.clients
        return clients.has_hidden_client(client_name)

    def _validate_live_mount(self, client_name):
        &#34;&#34;&#34;Check if the specified vm has a backup for live mount

            Args:
                client_name    (str)  --  name of the vm
                client_id      (int)  --  client_id of the vm

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

                    if there is an error in the response json
        &#34;&#34;&#34;
        clients = self._commcell_object.clients
        client_id = clients.get(client_name.lower()).client_id

        validate_live_mount_xml = (
            &#39;&lt;?xml version=&#34;1.0&#34; encoding=&#34;UTF-8&#34;?&gt;&#39;
            &#39;&lt;Ida_ValidateLiveMountReq&gt;&#39;
            &#39;&lt;vmClient clientId=&#34;&#39; + client_id + &#39;&#34; clientName=&#34;&#39; + client_name + &#39;&#34; /&gt;&#39;
            &#39;&lt;/Ida_ValidateLiveMountReq&gt;&#39;
        )
        response_json = self._commcell_object._qoperation_execute(
            request_xml=validate_live_mount_xml)

        if response_json[&#39;error&#39;]:
            if response_json[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                err_msg = &#39;Unable to validate client &#34;{0}&#34; for live mount. Error: {1}&#39;.format(
                    client_name, response_json[&#39;error&#39;][&#39;errorMessage&#39;])
                raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

    def view_active_mounts(self):
        &#34;&#34;&#34;View active mounts for this Live Mount policy instance

            Returns:
                response.json()[&#39;virtualMachines&#39;]   (list) --  list of dictionary containing
                                                                    information about the vm&#39;s
                                                                    that are currently mounted
                                                                    using this ive mount policy

            Raises:
                SDKException:
                    if no response is found

                    if response is not a success
        &#34;&#34;&#34;
        active_mount_xml = (&#39;&lt;?xml version=&#34;1.0&#34; encoding=&#34;UTF-8&#34; standalone=&#34;no&#34; ?&gt;&#39;
                            &#39;&lt;Ida_GetVirtualMachinesReq&gt;&lt;filter&gt;&#39;
                            &#39;&lt;allocationPolicy vmAllocPolicyId=&#34;&#39; + str(self.vm_policy_id) + &#39;&#34;/&gt;&#39;
                            &#39;&lt;/filter&gt;&lt;/Ida_GetVirtualMachinesReq&gt;&#39;)

        response_json = self._commcell_object._qoperation_execute(request_xml=active_mount_xml)

        if &#39;virtualMachines&#39; in response_json:
            return response_json[&#39;virtualMachines&#39;]

    def live_mount(
            self,
            client_vm_name,
            live_mount_options=None
    ):
        &#34;&#34;&#34;Run Live Mount for this Live Mount policy instance

            Args:
                client_vm_name          (str)  --   client vm name for which live mount is to
                                                         be run
                live_mount_options:    (dict)  --  list of optional parameters  for each live
                                                        mount job.
                                                        Allowed key-value pairs and input types
                                                        are given below
                    default                       :  None
                    &#39;vmName&#39;              (str)   :  name of the new vm that will be mounted
                    &#39;copyPrecedence&#39;      (int)   :  number for the storage policy copy to use
                                                     Default value is zero (copy with highest
                                                     precedence is used)
                    &#39;pointInTime&#39;         (dict)  :  to select live mount from point in time,
                                                     provide a dict with following key-value pairs
                        &#34;timeValue&#34;             (str)  : date and time in below format
                                                         &#34;yyyy-mm-dd hh:mm:ss&#34;.
                                                         &#34;2018-06-18 16:09:00&#34;, for example.
                        &#34;TimeZoneName&#34;          (str)  : time zone value in given format
                                                        (MS Windows time zone options).
                                                         &#34;(UTC-05:00) Eastern Time (US &amp; Canada)&#34;
                }


            Raises:
                SDKException:
                    if the vm name passed is not string

                    if the vm name passed does not exist

                    if a vm is not backed up

                    if the destination vm name (if provided) is not a string

                    if a vm with the destination vm name already exists (if provided)

            Returns:
                live_mount_job              (object)  -- Job object for the corresponding live
                                                            mount job
        &#34;&#34;&#34;
        # check if client name is string
        if not isinstance(client_vm_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)
        # check if client is a valid hidden client
        elif not self._is_hidden_client(client_vm_name):
            err_msg = &#39;Client &#34;{0}&#34; not found in Commcell&#39;.format(client_vm_name)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        else:
            # check if vm to be live mounted is backed up
            #self._validate_live_mount(client_vm_name)
            
            # default options if nothing is passed
            if not live_mount_options:
                live_mount_options = {}

            live_mount_options[&#39;clientName&#39;] = client_vm_name

            live_mount_json = self._prepare_live_mount_json(live_mount_options)

            # making a POST call for running the Live Mount job
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, self._LIVE_MOUNT_JOB_URL, live_mount_json
            )

            if flag:
                if response.json():
                    if &#39;error&#39; in response.json():
                        if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                            error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                            o_str = &#39;Failed to run Live Mount\nError: &#34;{0}&#34;&#39;.format(error_message)
                            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
                    # if no valid error in response
                    if &#39;jobIds&#39; in response.json():
                        return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                    else:
                        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;,
                                           &#39;Failed to run live mount&#39;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def live_mounted_vm_name(self):
        &#34;&#34;&#34;Treats the live mounted vm name as a read-only attribute.&#34;&#34;&#34;
        return self._live_mounted_vm_name</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.virtualmachinepolicies.LiveMountPolicy"><code class="flex name class">
<span>class <span class="ident">LiveMountPolicy</span></span>
<span>(</span><span>commcell_object, vm_policy_name, vm_policy_type_id, vm_policy_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from VirtualMachinePolicy base class for representing a single Live Mount
Policy. Contains method definitions for operations specific for Live Mount and also
runnning Live Mount job</p>
<p>Initialize object of the LiveMountPolicy class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class
vm_policy_name
(str)
&ndash;
name of the Live Mount policy
vm_policy_type_id
(int)
&ndash; policy type id
vm_policy_id
(int)
&ndash;
id of the Live Mount policy, if available</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash; instance of the LiveMountPolicy class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1129-L1638" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class LiveMountPolicy(VirtualMachinePolicy):
    &#34;&#34;&#34;Derived class from VirtualMachinePolicy base class for representing a single Live Mount
       Policy. Contains method definitions for operations specific for Live Mount and also
       runnning Live Mount job&#34;&#34;&#34;

    def __init__(
            self,
            commcell_object,
            vm_policy_name,
            vm_policy_type_id,
            vm_policy_id=None
    ):
        &#34;&#34;&#34;Initialize object of the LiveMountPolicy class.
            Args:
                commcell_object      (object)  --  instance of the Commcell class
                vm_policy_name       (str)     --  name of the Live Mount policy
                vm_policy_type_id    (int)     -- policy type id
                vm_policy_id         (int)     --  id of the Live Mount policy, if available

            Returns:
                object                       -- instance of the LiveMountPolicy class
        &#34;&#34;&#34;
        super(LiveMountPolicy, self).__init__(commcell_object,
                                              vm_policy_name,
                                              vm_policy_type_id,
                                              vm_policy_id)
        self._LIVE_MOUNT_JOB_URL = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        self._QOPERATION_URL = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]
        self._live_mounted_vm_name = None

    def _prepare_live_mount_json(self, live_mount_options):
        &#34;&#34;&#34;Sets values for creating the add policy json
            Args:
                live_mount_options (dict)  --  live mount job  options provided by user
        &#34;&#34;&#34;
        self._set_mounted_vm_name(live_mount_options)
        self._live_mounted_vm_name = live_mount_options[&#39;vmName&#39;]

        _associations = LiveMountPolicy.__associations_json(live_mount_options)
        _task = LiveMountPolicy._task_json()
        _subtask = LiveMountPolicy._subtask_json()
        _one_touch_response = LiveMountPolicy._one_touch_response_json(live_mount_options)
        _vm_entity = LiveMountPolicy._vm_entity_json(live_mount_options)
        _vm_info = LiveMountPolicy._vm_info_json(live_mount_options)

        # TODO: only if live mount is scheduled (non default)

        # TODO: _pattern = live_mount_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;]

        # TODO:  backupOpts = live_mount_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;]
        live_mount_json = {
            &#39;taskInfo&#39;: {
                &#39;associations&#39;: _associations,
                &#39;task&#39;: _task,
                &#39;subTasks&#39;: [
                    {
                        &#39;subTaskOperation&#39;: 1,
                        &#39;subTask&#39;: _subtask,
                        &#39;options&#39;: {
                            &#39;adminOpts&#39;: {
                                &#39;vmProvisioningOption&#39;: {
                                    &#39;operationType&#39;: 23,
                                    &#39;virtualMachineOption&#39;: [
                                        {
                                            &#39;powerOnVM&#39;: True,
                                            &#39;flags&#39;: 0,
                                            &#39;useLinkedClone&#39;: False,
                                            &#39;vendor&#39;: 1,
                                            &#39;doLinkedCloneFromLocalTemplateCopy&#39;: False,
                                            &#39;vmAllocPolicy&#39;: {
                                                &#39;vmAllocPolicyName&#39;: self._vm_policy_name
                                            },
                                            &#39;oneTouchResponse&#39;: _one_touch_response,
                                            &#39;vmEntity&#39;: _vm_entity,
                                            &#39;vmInfo&#39;: _vm_info
                                        }
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        }
        return live_mount_json

    def _set_mounted_vm_name(self, live_mount_options):
        &#34;&#34;&#34;
        Sets the vm name for the live mounted vm

        Args:
                live_mount_options    (dict)  --  live mount job options

        Raises:
            SDK Exception:
                if user passes a vm name that already exists as a hidden client on the Commcell
        &#34;&#34;&#34;
        clients = self._commcell_object.clients
        if &#39;vmName&#39; in live_mount_options:
            if live_mount_options[&#39;vmName&#39;].lower() in clients._hidden_clients:
                err_msg = &#39;A client already exists by the name &#34;{0}&#34;&#39;.format(
                    live_mount_options[&#39;vmName&#39;])
                raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        else:
            vm_name = live_mount_options[&#39;clientName&#39;] + &#39;VM&#39;
            digit = 1
            while vm_name.lower() in clients._hidden_clients:
                vm_name += str(digit)
            live_mount_options[&#39;vmName&#39;] = vm_name

    @staticmethod
    def __associations_json(live_mount_options):
        &#34;&#34;&#34;
        Sets the associations value for the live mount job json

            Args:
                live_mount_options    (dict)  --  live mount job options

            Returns:
                _associations          (list)  --  list containing the associations value
        &#34;&#34;&#34;
        _associations = []
        _associations_element = {
            # &#39;type&#39;: 0,
            &#39;clientName&#39;: live_mount_options[&#39;clientName&#39;],
            # &#39;clientSidePackage&#39;: True,
            &#39;subclientName&#39;: &#39;&#39;,
            &#39;backupsetName&#39;: &#39;&#39;,
            &#39;instanceName&#39;: &#39;&#39;,
            &#39;appName&#39;: &#39;&#39;,
            # &#39;consumeLicense&#39;: True
        }
        _associations.append(_associations_element)
        return _associations

    @staticmethod
    def _task_json():
        &#34;&#34;&#34;Sets the task value for the live mount job json

            Returns:
                _task                 (dict)  --  dict containing the task value
        &#34;&#34;&#34;
        _task = {
            &#39;taskType&#39;: 1,
            &#39;initiatedFrom&#39;: 2,
            &#39;alert&#39;: {
                &#39;alertName&#39;: &#39;&#39;
            },
            &#39;taskFlags&#39;: {
                &#39;disabled&#39;: False
            }
        }

        # TODO: if &#39;schedule&#39; is there in options, change 06 07 json

        return _task

    @staticmethod
    def _subtask_json():
        &#34;&#34;&#34;Sets the subTask value for the live mount job json

            Returns:
                _subtask              (dict)  --  dict containing the subTask value
        &#34;&#34;&#34;
        _subtask = {
            &#39;subTaskType&#39;: 1,
            &#39;operationType&#39;: 4038
        }

        # TODO: if &#39;schedule&#39; in live_mount_options: add subTaskName to json

        return _subtask

    @staticmethod
    def _one_touch_response_json(live_mount_options):
        &#34;&#34;&#34;Sets the oneTouchResponse value for the live mount job json

            Args:
                live_mount_options     (dict)  --  live mount job options

            Returns:
                _one_touch_response    (dict)  --  dict containing the oneTouchResponse value
        &#34;&#34;&#34;
        _csinfo = LiveMountPolicy._csinfo_json(live_mount_options)
        _hwconfig = LiveMountPolicy._hwconfig_json(live_mount_options)
        _netconfig = LiveMountPolicy._netconfig_json()
        _one_touch_response = {
            &#39;copyPrecedence&#39;: live_mount_options.get(&#39;copyPrecedence&#39;, 0),
            &#39;version&#39;: &#39;&#39;,
            &#39;platform&#39;: 0,
            &#39;dateCreated&#39;: &#39;&#39;,
            &#39;automationTest&#39;: False,
            &#39;autoReboot&#39;: True,
            &#39;csinfo&#39;: _csinfo,
            &#39;hwconfig&#39;: _hwconfig,
            &#39;netconfig&#39;: _netconfig,
            &#39;dataBrowseTime&#39;: live_mount_options.get(&#39;pointInTime&#39;, {}),
            &#39;maInfo&#39;: {
                &#39;clientName&#39;: &#39;&#39;
            },
            &#39;datastoreList&#39;: {}
        }

        return _one_touch_response

    @staticmethod
    def _csinfo_json(live_mount_options):
        &#34;&#34;&#34;Sets the csinfo value for the live mount job json

            Args:
                live_mount_options     (dict)  --  live mount job options

            Returns:
                _csinfo                (dict)  --  dict containing the hwconfig value
        &#34;&#34;&#34;
        _csinfo = {
            &#34;firewallPort&#34;: 0,
            &#34;cvdPort&#34;: 0,
            &#34;evmgrPort&#34;: 0,
            &#34;fwClientGroupName&#34;: &#34;&#34;,
            &#34;mediaAgentInfo&#34;: {},
            &#34;mediaAgentIP&#34;: {},
            &#34;ip&#34;: {},
            &#34;commservInfo&#34;: {},
            &#34;creds&#34;: {
                &#34;password&#34;: &#34;&#34;,
                &#34;domainName&#34;: &#34;&#34;,
                &#34;confirmPassword&#34;: &#34;&#34;,
                &#34;userName&#34;: &#34;&#34;
            }
        }

        return _csinfo

    @staticmethod
    def _hwconfig_json(live_mount_options):
        &#34;&#34;&#34;Sets the hwconfig value for the live mount job json

            Args:
                live_mount_options     (dict)  --  live mount job options

            Returns:
                _hwconfig              (dict)  --  dict containing the hwconfig value
        &#34;&#34;&#34;
        _hwconfig = {
            &#39;vmName&#39;: live_mount_options[&#39;vmName&#39;],
            &#39;magicno&#39;: &#39;&#39;,
            &#39;bootFirmware&#39;: 0,
            &#39;version&#39;: &#39;&#39;,
            &#39;mem_size&#39;: 0,
            &#39;cpu_count&#39;: 0,
            &#39;nic_count&#39;: 0,
            &#39;overwriteVm&#39;: False,
            &#39;useMtptSelection&#39;: False,
            &#39;ide_count&#39;: 0,
            &#39;mtpt_count&#39;: 0,
            &#39;scsi_count&#39;: 0,
            &#39;diskType&#39;: 1,
            &#39;optimizeStorage&#39;: False,
            &#39;systemDisk&#39;: {
                &#39;forceProvision&#39;: False,
                &#39;bus&#39;: 0,
                &#39;refcnt&#39;: 0,
                &#39;size&#39;: 0,
                &#39;name&#39;: &#39;&#39;,
                &#39;dataStoreName&#39;: &#39;&#39;,
                &#39;vm_disk_type&#39;: 0,
                &#39;slot&#39;: 0,
                &#39;diskType&#39;: 1,
                &#39;tx_type&#39;: 0
            }
        }

        return _hwconfig

    @staticmethod
    def _netconfig_json():
        &#34;&#34;&#34;Sets the netconfig value for the live mount job json

            Returns:
                _netconfig             (dict)  --  dict containing the netconfig value
        &#34;&#34;&#34;
        _netconfig = {
            &#39;wins&#39;: {
                &#39;useDhcp&#39;: False
            },
            &#39;firewall&#39;: {
                &#39;certificatePath&#39;: &#39;&#39;,
                &#39;certificateBlob&#39;: &#39;&#39;,
                &#39;configBlob&#39;: &#39;&#39;
            },
            &#39;dns&#39;: {
                &#39;suffix&#39;: &#39;&#39;,
                &#39;useDhcp&#39;: False
            },
            &#39;ipinfo&#39;: {
                &#39;defaultgw&#39;: &#39;&#39;
            }
        }

        return _netconfig

    @staticmethod
    def _vm_entity_json(live_mount_options):
        &#34;&#34;&#34;Sets the vmEntity value for the live mount job json
            Args:
                live_mount_options    (dict)  --  live mount job options

            Returns:
                _vm_entity            (dict)  --  dict containing the vmEntity value
        &#34;&#34;&#34;
        _vm_entity = {
            &#39;vmName&#39;: live_mount_options[&#39;vmName&#39;],
            &#39;clientName&#39;: live_mount_options[&#39;clientName&#39;],
            &#39;_type_&#39;: 88
        }

        return _vm_entity

    @staticmethod
    def _vm_info_json(live_mount_options):
        &#34;&#34;&#34;Sets the vmInfo value for the live mount job json
            Args:
                live_mount_options    (dict)  --  live mount job options

            Returns:
                _vm_info              (dict)  --  dict containing the vmInfo value
        &#34;&#34;&#34;
        _vm_info = {
            &#39;advancedProperties&#39;: {
                &#39;networkCards&#39;: [
                    {
                        &#39;label&#39;: live_mount_options.get(&#39;network_name&#39;, &#39;&#39;)
                    }
                ]
            },
            &#39;vm&#39;: {
                &#39;vmName&#39;: live_mount_options[&#39;vmName&#39;],
                &#39;_type_&#39;: 88
            }
        }

        # TODO: if &#39;original network&#39; is chosen as option in livemount option, verify network json

        return _vm_info

    def _is_hidden_client(self, client_name):
        &#34;&#34;&#34;Checks if specified client is a hidden client for the Commcell instance

            Args:
                client_name    (str)  -- name of the client

            Returns:
                bool                  -- boolean output whether the client is indeed a hidden
                                            client in the Commcell
        &#34;&#34;&#34;
        clients = self._commcell_object.clients
        return clients.has_hidden_client(client_name)

    def _validate_live_mount(self, client_name):
        &#34;&#34;&#34;Check if the specified vm has a backup for live mount

            Args:
                client_name    (str)  --  name of the vm
                client_id      (int)  --  client_id of the vm

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

                    if there is an error in the response json
        &#34;&#34;&#34;
        clients = self._commcell_object.clients
        client_id = clients.get(client_name.lower()).client_id

        validate_live_mount_xml = (
            &#39;&lt;?xml version=&#34;1.0&#34; encoding=&#34;UTF-8&#34;?&gt;&#39;
            &#39;&lt;Ida_ValidateLiveMountReq&gt;&#39;
            &#39;&lt;vmClient clientId=&#34;&#39; + client_id + &#39;&#34; clientName=&#34;&#39; + client_name + &#39;&#34; /&gt;&#39;
            &#39;&lt;/Ida_ValidateLiveMountReq&gt;&#39;
        )
        response_json = self._commcell_object._qoperation_execute(
            request_xml=validate_live_mount_xml)

        if response_json[&#39;error&#39;]:
            if response_json[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                err_msg = &#39;Unable to validate client &#34;{0}&#34; for live mount. Error: {1}&#39;.format(
                    client_name, response_json[&#39;error&#39;][&#39;errorMessage&#39;])
                raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

    def view_active_mounts(self):
        &#34;&#34;&#34;View active mounts for this Live Mount policy instance

            Returns:
                response.json()[&#39;virtualMachines&#39;]   (list) --  list of dictionary containing
                                                                    information about the vm&#39;s
                                                                    that are currently mounted
                                                                    using this ive mount policy

            Raises:
                SDKException:
                    if no response is found

                    if response is not a success
        &#34;&#34;&#34;
        active_mount_xml = (&#39;&lt;?xml version=&#34;1.0&#34; encoding=&#34;UTF-8&#34; standalone=&#34;no&#34; ?&gt;&#39;
                            &#39;&lt;Ida_GetVirtualMachinesReq&gt;&lt;filter&gt;&#39;
                            &#39;&lt;allocationPolicy vmAllocPolicyId=&#34;&#39; + str(self.vm_policy_id) + &#39;&#34;/&gt;&#39;
                            &#39;&lt;/filter&gt;&lt;/Ida_GetVirtualMachinesReq&gt;&#39;)

        response_json = self._commcell_object._qoperation_execute(request_xml=active_mount_xml)

        if &#39;virtualMachines&#39; in response_json:
            return response_json[&#39;virtualMachines&#39;]

    def live_mount(
            self,
            client_vm_name,
            live_mount_options=None
    ):
        &#34;&#34;&#34;Run Live Mount for this Live Mount policy instance

            Args:
                client_vm_name          (str)  --   client vm name for which live mount is to
                                                         be run
                live_mount_options:    (dict)  --  list of optional parameters  for each live
                                                        mount job.
                                                        Allowed key-value pairs and input types
                                                        are given below
                    default                       :  None
                    &#39;vmName&#39;              (str)   :  name of the new vm that will be mounted
                    &#39;copyPrecedence&#39;      (int)   :  number for the storage policy copy to use
                                                     Default value is zero (copy with highest
                                                     precedence is used)
                    &#39;pointInTime&#39;         (dict)  :  to select live mount from point in time,
                                                     provide a dict with following key-value pairs
                        &#34;timeValue&#34;             (str)  : date and time in below format
                                                         &#34;yyyy-mm-dd hh:mm:ss&#34;.
                                                         &#34;2018-06-18 16:09:00&#34;, for example.
                        &#34;TimeZoneName&#34;          (str)  : time zone value in given format
                                                        (MS Windows time zone options).
                                                         &#34;(UTC-05:00) Eastern Time (US &amp; Canada)&#34;
                }


            Raises:
                SDKException:
                    if the vm name passed is not string

                    if the vm name passed does not exist

                    if a vm is not backed up

                    if the destination vm name (if provided) is not a string

                    if a vm with the destination vm name already exists (if provided)

            Returns:
                live_mount_job              (object)  -- Job object for the corresponding live
                                                            mount job
        &#34;&#34;&#34;
        # check if client name is string
        if not isinstance(client_vm_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)
        # check if client is a valid hidden client
        elif not self._is_hidden_client(client_vm_name):
            err_msg = &#39;Client &#34;{0}&#34; not found in Commcell&#39;.format(client_vm_name)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        else:
            # check if vm to be live mounted is backed up
            #self._validate_live_mount(client_vm_name)
            
            # default options if nothing is passed
            if not live_mount_options:
                live_mount_options = {}

            live_mount_options[&#39;clientName&#39;] = client_vm_name

            live_mount_json = self._prepare_live_mount_json(live_mount_options)

            # making a POST call for running the Live Mount job
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, self._LIVE_MOUNT_JOB_URL, live_mount_json
            )

            if flag:
                if response.json():
                    if &#39;error&#39; in response.json():
                        if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                            error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                            o_str = &#39;Failed to run Live Mount\nError: &#34;{0}&#34;&#39;.format(error_message)
                            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
                    # if no valid error in response
                    if &#39;jobIds&#39; in response.json():
                        return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                    else:
                        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;,
                                           &#39;Failed to run live mount&#39;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def live_mounted_vm_name(self):
        &#34;&#34;&#34;Treats the live mounted vm name as a read-only attribute.&#34;&#34;&#34;
        return self._live_mounted_vm_name</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy">VirtualMachinePolicy</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.virtualmachinepolicies.LiveMountPolicy.live_mounted_vm_name"><code class="name">var <span class="ident">live_mounted_vm_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the live mounted vm name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1635-L1638" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def live_mounted_vm_name(self):
    &#34;&#34;&#34;Treats the live mounted vm name as a read-only attribute.&#34;&#34;&#34;
    return self._live_mounted_vm_name</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.virtualmachinepolicies.LiveMountPolicy.live_mount"><code class="name flex">
<span>def <span class="ident">live_mount</span></span>(<span>self, client_vm_name, live_mount_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Run Live Mount for this Live Mount policy instance</p>
<h2 id="args">Args</h2>
<dl>
<dt>client_vm_name
(str)
&ndash;
client vm name for which live mount is to</dt>
<dt>be run</dt>
<dt><strong><code>live_mount_options</code></strong></dt>
<dd>(dict)
&ndash;
list of optional parameters
for each live
mount job.
Allowed key-value pairs and input types
are given below
default
:
None
'vmName'
(str)
:
name of the new vm that will be mounted
'copyPrecedence'
(int)
:
number for the storage policy copy to use
Default value is zero (copy with highest
precedence is used)
'pointInTime'
(dict)
:
to select live mount from point in time,
provide a dict with following key-value pairs
"timeValue"
(str)
: date and time in below format
"yyyy-mm-dd hh:mm:ss".
"2018-06-18 16:09:00", for example.
"TimeZoneName"
(str)
: time zone value in given format
(MS Windows time zone options).
"(UTC-05:00) Eastern Time (US &amp; Canada)"</dd>
</dl>
<p>}</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if the vm name passed is not string</p>
<pre><code>if the vm name passed does not exist

if a vm is not backed up

if the destination vm name (if provided) is not a string

if a vm with the destination vm name already exists (if provided)
</code></pre>
<h2 id="returns">Returns</h2>
<p>live_mount_job
(object)
&ndash; Job object for the corresponding live
mount job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1546-L1633" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def live_mount(
        self,
        client_vm_name,
        live_mount_options=None
):
    &#34;&#34;&#34;Run Live Mount for this Live Mount policy instance

        Args:
            client_vm_name          (str)  --   client vm name for which live mount is to
                                                     be run
            live_mount_options:    (dict)  --  list of optional parameters  for each live
                                                    mount job.
                                                    Allowed key-value pairs and input types
                                                    are given below
                default                       :  None
                &#39;vmName&#39;              (str)   :  name of the new vm that will be mounted
                &#39;copyPrecedence&#39;      (int)   :  number for the storage policy copy to use
                                                 Default value is zero (copy with highest
                                                 precedence is used)
                &#39;pointInTime&#39;         (dict)  :  to select live mount from point in time,
                                                 provide a dict with following key-value pairs
                    &#34;timeValue&#34;             (str)  : date and time in below format
                                                     &#34;yyyy-mm-dd hh:mm:ss&#34;.
                                                     &#34;2018-06-18 16:09:00&#34;, for example.
                    &#34;TimeZoneName&#34;          (str)  : time zone value in given format
                                                    (MS Windows time zone options).
                                                     &#34;(UTC-05:00) Eastern Time (US &amp; Canada)&#34;
            }


        Raises:
            SDKException:
                if the vm name passed is not string

                if the vm name passed does not exist

                if a vm is not backed up

                if the destination vm name (if provided) is not a string

                if a vm with the destination vm name already exists (if provided)

        Returns:
            live_mount_job              (object)  -- Job object for the corresponding live
                                                        mount job
    &#34;&#34;&#34;
    # check if client name is string
    if not isinstance(client_vm_name, str):
        raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)
    # check if client is a valid hidden client
    elif not self._is_hidden_client(client_vm_name):
        err_msg = &#39;Client &#34;{0}&#34; not found in Commcell&#39;.format(client_vm_name)
        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
    else:
        # check if vm to be live mounted is backed up
        #self._validate_live_mount(client_vm_name)
        
        # default options if nothing is passed
        if not live_mount_options:
            live_mount_options = {}

        live_mount_options[&#39;clientName&#39;] = client_vm_name

        live_mount_json = self._prepare_live_mount_json(live_mount_options)

        # making a POST call for running the Live Mount job
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._LIVE_MOUNT_JOB_URL, live_mount_json
        )

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        o_str = &#39;Failed to run Live Mount\nError: &#34;{0}&#34;&#39;.format(error_message)
                        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
                # if no valid error in response
                if &#39;jobIds&#39; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                else:
                    raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;,
                                       &#39;Failed to run live mount&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.LiveMountPolicy.view_active_mounts"><code class="name flex">
<span>def <span class="ident">view_active_mounts</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>View active mounts for this Live Mount policy instance</p>
<h2 id="returns">Returns</h2>
<p>response.json()['virtualMachines']
(list) &ndash;
list of dictionary containing
information about the vm's
that are currently mounted
using this ive mount policy</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if no response is found</p>
<pre><code>if response is not a success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1521-L1544" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def view_active_mounts(self):
    &#34;&#34;&#34;View active mounts for this Live Mount policy instance

        Returns:
            response.json()[&#39;virtualMachines&#39;]   (list) --  list of dictionary containing
                                                                information about the vm&#39;s
                                                                that are currently mounted
                                                                using this ive mount policy

        Raises:
            SDKException:
                if no response is found

                if response is not a success
    &#34;&#34;&#34;
    active_mount_xml = (&#39;&lt;?xml version=&#34;1.0&#34; encoding=&#34;UTF-8&#34; standalone=&#34;no&#34; ?&gt;&#39;
                        &#39;&lt;Ida_GetVirtualMachinesReq&gt;&lt;filter&gt;&#39;
                        &#39;&lt;allocationPolicy vmAllocPolicyId=&#34;&#39; + str(self.vm_policy_id) + &#39;&#34;/&gt;&#39;
                        &#39;&lt;/filter&gt;&lt;/Ida_GetVirtualMachinesReq&gt;&#39;)

    response_json = self._commcell_object._qoperation_execute(request_xml=active_mount_xml)

    if &#39;virtualMachines&#39; in response_json:
        return response_json[&#39;virtualMachines&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy">VirtualMachinePolicy</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.clone" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.clone">clone</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.disable" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.disable">disable</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.enable" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.enable">enable</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.properties" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.properties">properties</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.refresh" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_id" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_id">vm_policy_id</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_name" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_name">vm_policy_name</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_type_id" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_type_id">vm_policy_type_id</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies"><code class="flex name class">
<span>class <span class="ident">VirtualMachinePolicies</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the Virtual Machine Policies associated with the Commcell.</p>
<p>Initialize object of the VirtualMachinePolicies class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the VirtualMachinePolicies class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L214-L913" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VirtualMachinePolicies(object):
    &#34;&#34;&#34;Class for representing all the Virtual Machine Policies associated with the Commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the VirtualMachinePolicies class.

            Args:
                commcell_object (object)  --  instance of the Commcell class
            Returns:
                object - instance of the VirtualMachinePolicies class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._VMPOLICIES_URL = self._commcell_object._services[&#39;VM_ALLOCATION_POLICY&#39;]
        self._ALL_VMPOLICIES_URL = self._commcell_object._services[&#39;ALL_VM_ALLOCATION_POLICY&#39;]
        self._VCLIENTS_URL = self._commcell_object._services[&#39;GET_VIRTUAL_CLIENTS&#39;]
        self._QOPERATION_URL = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        self._vm_policies = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all virtual machine policies of the commcell.

            Returns:
                str - string of all the virtual machine policies associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^28}&#39;.format(&#39;S. No.&#39;, &#39;Virtual Machine Policy&#39;)

        for (index, vm_policy) in enumerate(self._vm_policies):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, vm_policy)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Clients class.&#34;&#34;&#34;
        return &#34;VirtualMachinePolicies class instance for Commcell&#34;

    def _get_vm_policies(self):
        &#34;&#34;&#34;Gets all the virtual machine policies associated to the commcell specified by the
            Commcell object.

            Returns:
                dict - consists of all virtual machine policies for the commcell
                    {
                        &#34;vm_policy1_name&#34;: {
                                                &#34;id&#34;: vm_policy1Id,
                                                &#34;policyType&#34;: policyTypeId
                                            }
                        &#34;vm_policy2_name&#34;: {
                                                &#34;id&#34;: vm_policy2Id,
                                                &#34;policyType&#34;: policyTypeId
                                            }
                    }
            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;GET&#39;, url=self._ALL_VMPOLICIES_URL)

        if flag:
            if response.json() and &#39;policy&#39; in response.json():
                vm_policies = response.json()[&#39;policy&#39;]

                if vm_policies == []:
                    return {}

                vm_policies_dict = {}

                for vm_policy in vm_policies:
                    temp_name = vm_policy[&#39;entity&#39;][&#39;vmAllocPolicyName&#39;].lower()
                    temp_id = str(vm_policy[&#39;entity&#39;][&#39;vmAllocPolicyId&#39;]).lower()
                    temp_policy_type = str(vm_policy[&#39;entity&#39;][&#39;policyType&#39;]).lower()
                    vm_policies_dict[temp_name] = {
                        &#39;id&#39;: temp_id,
                        &#39;policyType&#39;: temp_policy_type
                    }

                return vm_policies_dict
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _set_vclient_and_vcenter_names(self, vm_policy_options, vclient_name):
        &#34;&#34;&#34;Sets the virtualization client name and the vcenter name for the corresponding vclient

            Args:
                vm_policy_options    --  optional policy paramters passed by user (None if user
                                             passes nothing

                vclient_name         --  virtualization client name

            Raises:
                SDKException:
                    if response is not success

                    if no virtualization client exists on the Commcell

                    if virtualization client with given name does not exist on this Commcell
        &#34;&#34;&#34;
        clients = self._commcell_object.clients
        vclient_name_dict = clients._get_virtualization_clients()

        if not vclient_name_dict:
            err_msg = &#39;No virtualization clients exist on this Commcell.&#39;
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

        if vclient_name in vclient_name_dict:
            vm_policy_options[&#39;clientName&#39;] = vclient_name
            # fetching the vcenter from the corresponding instance object
            client = self._commcell_object.clients.get(vm_policy_options[&#39;clientName&#39;])
            agent = client.agents.get(&#39;Virtual Server&#39;)
            instance_keys = next(iter(agent.instances._instances))
            instance = agent.instances.get(instance_keys)
            vm_policy_options[&#39;vCenterName&#39;] = instance.server_host_name[0]
        else:
            err_msg = &#39;Virtualization client &#34;{0}&#34; does not exist&#39;.format(vclient_name)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

    def _get_proxy_client_json(self, options):
        try:
            id_ = self._commcell_object.clients[options.get(&#34;proxy_client&#34;)][&#34;id&#34;]
        except KeyError:
            return dict()
        return{
            &#34;clientId&#34;: int(id_),
            &#34;clientName&#34;: options[&#34;proxy_client&#34;]
        }

    def _prepare_add_vmpolicy_json_default(self, vm_policy_options):
        &#34;&#34;&#34;Sets values for creating the add policy json

            Args:
                vm_policy_options    (dict)  --  main dict containing vm policy options

            Returns:
                vm_policy_json    (dict)  --  json to be passed for add policy POST request
        &#34;&#34;&#34;
        #  setting the json values using functions for elements having nested values
        _datacenter = self._get_data_center_json(vm_policy_options)
        _entity = VirtualMachinePolicies._entity_json(vm_policy_options)
        _esxservers = [{&#34;esxServerName&#34;: esx_server} for esx_server in vm_policy_options.get(&#34;esxServers&#34;, &#34;&#34;)]
        _datastores = [{&#34;dataStoreName&#34;: datastore} for datastore in vm_policy_options.get(&#34;dataStores&#34;, &#34;&#34;)]
        _security_associations = VirtualMachinePolicies._security_associations_json(
            vm_policy_options)
        _network_names = VirtualMachinePolicies._network_names_json(vm_policy_options)

        _vm_policy_json = {
            &#39;action&#39;: 0,        # 0 for add
            &#39;policy&#39;: {
                &#34;vmNameEditType&#34;: vm_policy_options.get(&#34;vm_name_edit&#34;, 1),
                &#34;vmNameEditString&#34;: vm_policy_options.get(&#34;vm_name_edit_string&#34;, &#34;Replicated_&#34;),
                &#34;createIsolatedNetwork&#34;: False,
                &#34;isResourceGroupPolicy&#34;: True,
                &#34;resourcePoolPath&#34;: &#34;//&#34;,
                &#34;destinationHyperV&#34;: {
                    &#34;clientId&#34;: int(self._commcell_object.clients[vm_policy_options[&#39;clientName&#39;]][&#34;id&#34;]),
                    &#34;clientName&#34;: vm_policy_options[&#39;clientName&#39;]
                },
                &#39;allDataStoresSelected&#39;: vm_policy_options.get(&#39;allDataStoresSelected&#39;, False),
                &#39;daysRetainUntil&#39;: vm_policy_options.get(&#39;daysRetainUntil&#39;, -1),
                &#39;migrateVMs&#39;: vm_policy_options.get(&#39;migrateVMs&#39;, False),
                &#39;senderEmailId&#39;: vm_policy_options.get(&#39;senderEmailId&#39;, &#39;&#39;),
                &#39;notifyToEmailIds&#39;: vm_policy_options.get(&#39;notifyToEmailIds&#39;, &#39;&#39;),
                &#39;quotaType&#39;: vm_policy_options.get(&#39;quotaType&#39;, 0),
                &#39;maxVMQuota&#39;: vm_policy_options.get(&#39;maxVMQuota&#39;, 10),
                &#39;namingPattern&#39;: vm_policy_options.get(&#39;namingPattern&#39;, &#39;&#39;),
                &#39;description&#39;: vm_policy_options.get(&#39;description&#39;, &#39;&#39;),
                &#39;enabled&#39;: vm_policy_options.get(&#39;enabled&#39;, True),
                &#39;allowRenewals&#39;: vm_policy_options.get(&#39;allowRenewals&#39;, True),
                &#39;disableSuccessEmail&#39;: vm_policy_options.get(&#39;disableSuccessEmail&#39;, False),
                &#39;performAutoMigration&#39;: vm_policy_options.get(&#39;performAutoMigration&#39;, False),
                &#39;allESXServersSelected&#39;: vm_policy_options.get(&#39;allESXServersSelected&#39;, False),
                &#39;dataCenter&#39;: _datacenter,
                &#39;entity&#39;: _entity,
                &#34;proxyClientEntity&#34;: self._get_proxy_client_json(vm_policy_options),
                &#34;networkList&#34;: [
                    {
                        &#34;destinationNetwork&#34;: vm_policy_options.get(&#34;destination_network&#34;),
                        &#34;sourceNetwork&#34;: &#34;Any Network&#34;
                    }
                ]
            }
        }

        # adding the optional values for the json if they exist
        if _esxservers and not _vm_policy_json[&#39;policy&#39;][&#39;allESXServersSelected&#39;]:
            _vm_policy_json[&#39;policy&#39;][&#39;esxServers&#39;] = _esxservers

        if _datastores and not _vm_policy_json[&#39;policy&#39;][&#39;allDataStoresSelected&#39;]:
            _vm_policy_json[&#39;policy&#39;][&#39;dataStores&#39;] = _datastores

        if _network_names:
            _vm_policy_json[&#39;policy&#39;][&#39;networkNames&#39;] = _network_names

        if _security_associations:
            _vm_policy_json[&#39;policy&#39;][&#39;securityAssociations&#39;] = _security_associations

        # setting json values that are specific to a particular policy type

        if vm_policy_options[&#34;policyType&#34;] == 4:  # for Live Mount policy
            self._prepare_add_vmpolicy_json_livemount(vm_policy_options, _vm_policy_json)
        # TODO: future support for Clone from Template policy
        elif vm_policy_options[&#34;policyType&#34;] == 0:
            pass
        # TODO: future support for Restore from Backup policy
        else:
            pass

        return _vm_policy_json

    def _get_data_center_json(self, vm_policy_options):
        &#34;&#34;&#34;Returns value for the datacenter json value in the add policy json

            Args:
                vm_policy_options    (dict)  --  main dict containing vm policy options

            Returns:
                _datacenter (dict)        --  datacenter json to add to vm policy json
        &#34;&#34;&#34;
        client = self._commcell_object.clients.get(vm_policy_options[&#39;clientName&#39;])
        vm_policy_options[&#39;clientId&#39;] = client.client_id
        agent = client.agents.get(&#39;Virtual Server&#39;)
        instance_keys = next(iter(agent.instances._instances))
        instance = agent.instances.get(instance_keys)
        vm_policy_options[&#39;instanceId&#39;] = instance.instance_id

        # self._set_data_center(vm_policy_options)
        _datacenter = {
            &#39;vCenterName&#39;: vm_policy_options[&#39;vCenterName&#39;],
            &#39;instanceEntity&#39;: {
                &#39;clientId&#39;: int(vm_policy_options[&#39;clientId&#39;]),
                &#39;instanceName&#39;: vm_policy_options[&#39;clientName&#39;],
                &#39;instanceId&#39;: int(vm_policy_options[&#39;instanceId&#39;])
            },
        }

        return _datacenter

    def _set_data_center(self, vm_policy_options):
        &#34;&#34;&#34;Sets the datacenter name if provided by user, or sets the alphabetically lowest one in
            the vcenter as default

            Args:
                vm_policy_options    (dict)  --  main dict containing vm policy options

            Raises:
                SDKException:
                    if specified datacenter is not found for the corresponding virtualization
                     client

                    if no datacenter is found for the virtaulization client

                    if no response is found

                    if response is not a success
        &#34;&#34;&#34;
        get_datacenter_xml = (
            &#39;&lt;?xml version=&#34;1.0&#34; encoding=&#34;UTF-8&#34; standalone=&#34;no&#34; ?&gt;&#39;
            &#39;&lt;Ida_GetDataCenterListReq&gt;&lt;policyType policyType=&#34;4&#34; &#39;
            &#39;vmAllocPolicyName=&#34;&#34; vCenterName=&#34;&#39; + vm_policy_options[&#39;vCenterName&#39;] +
            &#39;&#34;/&gt;&lt;/Ida_GetDataCenterListReq&gt;&#39;
        )
        response_json = self._commcell_object._qoperation_execute(request_xml=get_datacenter_xml)

        if &#39;dataCenterList&#39; in response_json:
            all_nodes = response_json[&#39;dataCenterList&#39;]
            datacenter_dict = {}
            for node in all_nodes:
                if node[&#39;vCenterName&#39;] == vm_policy_options[&#39;vCenterName&#39;]:
                    datacenter_dict[node[&#39;dataCenterName&#39;]] = node[&#39;dataCenterId&#39;]
            if &#39;dataCenterName&#39; in vm_policy_options:
                if vm_policy_options[&#39;dataCenterName&#39;] in datacenter_dict:
                    vm_policy_options[&#39;dataCenterId&#39;] = datacenter_dict[
                        vm_policy_options[&#39;dataCenterName&#39;]]
                else:
                    # if no datacenter is found for the vclient, throw error
                    err_msg = (
                        &#39;No datacenter found with name: {0} in virtual client: {1}&#39;.format(
                            vm_policy_options[&#39;dataCenterName&#39;],
                            vm_policy_options[&#39;clientName&#39;])
                    )
                    raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
            else:
                vm_policy_options[&#39;dataCenterName&#39;] = next(iter(datacenter_dict))
                vm_policy_options[&#39;dataCenterId&#39;] = datacenter_dict[vm_policy_options[
                    &#39;dataCenterName&#39;]]
        else:
            # if no datacenter is found for the vclient, throw error
            err_msg = (&#39;No datacenter found for virtual client: {0}&#39;.format(
                vm_policy_options[&#39;clientName&#39;]))
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

    def _clone_vm_policy(self, vm_policy_json):
        &#34;&#34;&#34;Private method to clone a vm policy from VirtualMachinePolicy object

            Args:
                vm_policy_json    --  dict containing information to clone a particular policy
                                          along with optional information passed by user
            Returns:
                object            --  VirtualMachinePolicy object of the newly cloned policy

            Raises:
                SDKException:
                    if failed to create vm policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;POST&#39;, url=self._ALL_VMPOLICIES_URL, payload=vm_policy_json)

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        o_str = &#39;Failed to create virtual machine policy\nError: &#34;{0}&#34;&#39;.format(
                            error_message)

                        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
                # return object of VirtualMachinePolicy if there is no error in response
                self.refresh()
                return VirtualMachinePolicy(
                    self._commcell_object,
                    vm_policy_json[&#39;policy&#39;][&#39;entity&#39;][&#39;vmAllocPolicyName&#39;],
                    int(vm_policy_json[&#39;policy&#39;][&#39;entity&#39;][&#39;policyType&#39;]),
                    int(self._vm_policies[vm_policy_json[&#39;policy&#39;][&#39;entity&#39;]
                                          [&#39;vmAllocPolicyName&#39;]][&#39;id&#39;])
                )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _prepare_add_vmpolicy_json_livemount(self, vm_policy_options, _vm_policy_json):
        &#34;&#34;&#34;Sets values for creating the add policy json that are specific for creating Live Mount
            policy.

            Args:
                vm_policy_options (dict)  --  vm policy options provided by user

                _vm_policy_json (dict)    --  vm policy json to which Live Mount policy specific
                                            information is added
        &#34;&#34;&#34;
        _media_agent_json = self._media_agent_json(vm_policy_options)

        _vm_policy_json[&#39;policy&#39;][&#39;minutesRetainUntil&#39;] = vm_policy_options.get(
            &#39;minutesRetainUntil&#39;, 1)

        _vm_policy_json[&#39;policy&#39;][&#39;mediaAgent&#39;] = _media_agent_json

    @staticmethod
    def _security_associations_json(vm_policy_options):
        &#34;&#34;&#34;Returns json for the security associations in the add policy json

            Args:
                vm_policy_options (dict)  --  vm policy options provided by user
        &#34;&#34;&#34;
        _users = []
        if &#39;users&#39; in vm_policy_options:
            # TODO: get user info using REST API. For every user, add user dict to _users
            pass
        else:
            # default - admin
            default_user = {
                &#34;_type_&#34;: 13,
                &#34;userGUID&#34;: &#34;admin&#34;,
                &#34;userName&#34;: &#34;admin&#34;,
                &#34;userId&#34;: 1
            }
            _users.append(default_user)

        _usergroups = []
        if &#39;userGroups&#39; in vm_policy_options:
            # TODO: get usergroups info using REST API. For every userGroup, add corresponding dict
            pass

        _security_associations = {}
        if _users:
            _security_associations[&#39;users&#39;] = _users
        if _usergroups:
            _security_associations[&#39;userGroups&#39;] = _usergroups

        return _security_associations

    @staticmethod
    def _network_names_json(vm_policy_options):
        &#34;&#34;&#34;Returns list of network names for the add policy json

            Args:
                vm_policy_options (dict)    --  vm policy options provided by user

            Returns:
                _network_names   (list)     --  list of network names (str)
        &#34;&#34;&#34;
        _network_names = []
        if &#39;networkNames&#39; in vm_policy_options:
            for network in vm_policy_options[&#39;networkNames&#39;]:
                _network_names.append(network)

        return _network_names

    def _media_agent_json(self, vm_policy_options):
        &#34;&#34;&#34;Returns json for the media agent json value in the add policy json (only for LM)

            Args:
                vm_policy_options (dict)  --  vm policy options provided by user (optional)

            Returns:
                _media_agent_json (dict)  --  json containing media agent information if media
                                                agent info is passed by user
        &#34;&#34;&#34;
        _media_agent_json = {}
        if &#39;mediaAgent&#39; in vm_policy_options:
            # TODO: there can be only one MA -- validate this (whole vm_policy_options)
            media_agent = vm_policy_options[&#39;mediaAgent&#39;]
            if not self._commcell_object.media_agents.has_media_agent(media_agent):
                raise SDKException(
                    &#39;Virtual Machine&#39;, &#39;102&#39;,
                    &#39;No media agent exists &#34;{0}&#34; exists in commserv &#34;{1}&#34;&#39;.format(
                        media_agent, self._commcell_object.commserv_name))
            else:
                _media_agent_json[&#39;clientName&#39;] = media_agent
        else:   # adding a default media agent for automation
            media_agent_dict = self._commcell_object.media_agents._media_agents
            media_agent = [ma for ma in media_agent_dict][0]
            _media_agent_json[&#39;clientName&#39;] = media_agent

        return _media_agent_json

    @staticmethod
    def _entity_json(vm_policy_options):
        &#34;&#34;&#34;Returns json for the entity  attribute in the add policy json

            Args:
                vm_policy_options  (dict)    --  vm policy options provided by user

            Returns:
                _entity            (dict)    --  json for the entity attribute in add policy json
        &#34;&#34;&#34;
        _entity = {
            &#39;vmAllocPolicyName&#39;: vm_policy_options[&#39;vmAllocPolicyName&#39;],
            &#39;_type_&#39;: 93,           # hardcoded
            &#39;policyType&#39;: vm_policy_options[&#34;policyType&#34;],
            &#39;region&#39;: {},
        }

        return _entity

    def has_policy(self, vm_policy_name):
        &#34;&#34;&#34;Checks if a Virtual Machine policy exists with the given name

            Args:
                policy_name (str)  --  name of the vm policy

            Returns:
                bool - boolean output whether the vm policy exists in the commcell or not

            Raises:
                SDKException:
                    if type of the vm policy name argument is not string
        &#34;&#34;&#34;
        if not isinstance(vm_policy_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)

        return (self._vm_policies and
                vm_policy_name.lower() in self._vm_policies)

    def get(self, vm_policy_name):
        &#34;&#34;&#34;Returns a VirtualMachinePolicy object of the specified virtual machine policy name.

            Args:
                vm_policy_name     (str)   --  name of the virtual machine policy

            Returns:
                object - instance of the VirtualMachinePolicy class for the given policy name

            Raises:
                SDKException:
                    if type of the virtual machine policy name argument is not string
                    if no virtual machine policy exists with the given name
        &#34;&#34;&#34;
        if not isinstance(vm_policy_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)

        vm_policy_name = vm_policy_name.lower()
        if self.has_policy(vm_policy_name):
            vm_policy_type_id = int(self._vm_policies[vm_policy_name][&#39;policyType&#39;])
            return VirtualMachinePolicy(
                self._commcell_object,
                vm_policy_name=vm_policy_name,
                vm_policy_type_id=vm_policy_type_id,
                vm_policy_id=int(self._vm_policies[vm_policy_name][&#39;id&#39;])
            )
        else:
            raise SDKException(
                &#39;Virtual Machine&#39;,
                &#39;102&#39;,
                &#39;No policy exists with name: {0}&#39;.format(vm_policy_name))

    def add(
            self,
            vm_policy_name,
            vm_policy_type,
            vclient_name,
            vm_policy_options=None
    ):
        &#34;&#34;&#34;Adds a new Virtual Machine Policy to the Commcell.

            Args:
                vm_policy_name       (str)   --  name of the new virtual machine policy to add to
                                                    the Commcell instance

                vm_policy_type       (str)   --  type of virtual machine policy to be added
                                                    [&#34;Live Mount&#34;, &#34;Clone From Template&#34;,
                                                    &#34;Restore From Backup&#34;]

                vclient_name         (str)   --  the name of the virtualization client under which
                                                    vm policy is to be added

                vm_policy_options    (dict)  --  optional dictionary passed by user to create a vm
                                                   policy. Allowed key-value pairs and input types
                                                   are given below
                    default: None

                    &#34;allDataStoresSelected&#34;    (Boolean)  : if all data stores are to be selected;
                                                                matters only if migrateVMs is set
                                                                to True,
                    &#34;daysRetainUntil&#34;          (int)      : how many days to retain backup until,
                    &#34;migrateVMs&#34;               (Boolean)  : migrate to datastore after expiry
                                                                (only for LiveMount),
                    &#34;senderEmailId&#34;            (str)      : email id of sender,
                    &#34;minutesRetainUntil&#34;       (int)      : how many days to retain backup until
                    &#34;notifyToEmailIds&#34;         (str)      : email id&#39;s to notify to; multiple
                                                                emails separated by a comma
                    &#34;quotaType&#34;                (int)      : number of vm&#39;s/live mounts/labs per
                                                                user,
                    &#34;maxVMQuota&#34;               (int)      : maximum number of VM quota,
                    &#34;namingPattern&#34;            (str)      : naming patter,
                    &#34;description&#34;              (str)      : description of vm policy,
                    &#34;enabled&#34;                  (Boolean)  : whether vm policy is enabled or not,
                    &#34;allowRenewals&#34;            (Boolean)  : whether to allow renewals or not,
                    &#34;disableSuccessEmail&#34;      (Boolean)  : send email on succesful creation of vm
                                                                policy,
                    &#34;allESXServersSelected&#34;    (Boolean)  : select all esx servers in the vcenter,
                    &#34;dataCenterName&#34;           (str)      : data center name for vm policy,
                    &#34;dataStores&#34;               list(str)  : list of data store names,
                    &#34;esxServers&#34;               list(str)  : list of esx server names,
                    &#34;users&#34;                    list(str)  : list of users (user-names) to add to vm
                                                                policy,
                    &#34;userGroups&#34;               list(str)  : list of usergroups (usergroup-names) to
                                                                add to vm policy,
                    &#34;networkNames&#34;             list(str)  : list of network names,
                    ------------------------ only for Live Mount ------------------------
                    &#34;mediaAgent&#34;               (str)      : media agent name for Live Mount,
                    &#34;performAutoMigration&#34;     (Boolean)  : automatic migration of vm

            Returns:
                object    --  object of the corresponding virtual machine policy type

            Raises:
                SDKException:
                    if type of the vm policy name argument is not string

                    if type of the vcenter name argument is not string

                    if type of virtualization client name argument is not string or None

                    if policy type is not one of the virtual machine policy types as defined

                    if the type of vm_policy_options is not dict or None

                    if vm policy already exists with the given name (case insensitive)

                    if failed to create vm policy

                    if response is empty

                    if response is not success
                &#34;&#34;&#34;
        vm_policy_name = vm_policy_name.lower()
        vm_policy_type = vm_policy_type.lower()
        vclient_name = vclient_name.lower()
        _vm_policy_types = {&#39;live mount&#39;: 4,
                            &#39;clone from template&#39;: 0,
                            &#39;restore from backup&#39;: 13}
        self.refresh()
        if (
                not isinstance(vm_policy_name, str)
                or not isinstance(vclient_name, str)
                or not isinstance(vm_policy_options, (dict, type(None)))
        ):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)
        elif vm_policy_type not in _vm_policy_types:
            err_msg = &#39;{0} is not a valid virtual machine policy type.&#39;.format(
                vm_policy_type)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        elif self.has_policy(vm_policy_name):
            err_msg = &#39;Virtual Machine Policy &#34;{0}&#34; already exists (not case sensitive)&#39;.format(
                vm_policy_name)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        else:
            if not vm_policy_options:
                vm_policy_options = {}
            vm_policy_options[&#39;vmAllocPolicyName&#39;] = vm_policy_name.lower()

            # setting the vclient name, vcenter name and policy type
            self._set_vclient_and_vcenter_names(vm_policy_options, vclient_name)
            vm_policy_options[&#39;policyType&#39;] = _vm_policy_types[vm_policy_type]

            # preparing the json values for adding the new policy
            _vm_policy_json = self._prepare_add_vmpolicy_json_default(vm_policy_options)

            # passing the built json to create the vm policy
            (flag, response) = self._commcell_object._cvpysdk_object.make_request(
                method=&#39;POST&#39;, url=self._VMPOLICIES_URL, payload=_vm_policy_json)

            if flag:
                if response.json():
                    if &#39;error&#39; in response.json():
                        if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                            error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                            o_str = &#39;Failed to create virtual machine policy\nError: &#34;{0}&#34;&#39;.format(
                                error_message)
                            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
                    # returning object of VirtualMachinePolicy if there is no error in response
                    self.refresh()
                    return VirtualMachinePolicy(
                        self._commcell_object,
                        vm_policy_name=vm_policy_options[&#39;vmAllocPolicyName&#39;],
                        vm_policy_type_id=int(vm_policy_options[&#39;policyType&#39;]),
                        vm_policy_id=int(self._vm_policies[vm_policy_name][&#39;id&#39;]))
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, vm_policy_name):
        &#34;&#34;&#34;Deletes the specified virtual machine policy from the commcell.

            Args:
                vm_policy_name (str)  --  name of the virtual machine policy to delete

            Raises:
                SDKException:
                    if type of the virtual machine policy name argument is not string

                    if failed to delete virtual machine policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(vm_policy_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)

        if self.has_policy(vm_policy_name):
            # retrieving the corresponding policy id for API call
            vm_policy_id = self._get_vm_policies()[vm_policy_name][&#39;id&#39;]
            policy_delete_url = self._VMPOLICIES_URL + &#39;/{0}&#39;.format(vm_policy_id)

            (flag, response) = self._commcell_object._cvpysdk_object.make_request(
                &#39;DELETE&#39;, policy_delete_url)

            if flag:
                try:
                    if response.json():
                        if &#39;errorCode&#39; in response.json() and &#39;errorMessage&#39; in response.json():
                            error_message = response.json()[&#39;errorMessage&#39;]
                            output_string = &#39;Failed to delete virtual machine policy\nError: &#34;{0}&#34;&#39;
                            raise SDKException(
                                &#39;Virtual Machine&#39;, &#39;102&#39;, output_string.format(error_message))
                except ValueError:
                    if response.text:
                        self.refresh()
                        return response.text.strip()
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        else:
            raise SDKException(
                &#39;Virtual Machine&#39;,
                &#39;102&#39;,
                &#39;No policy exists with name: {0}&#39;.format(vm_policy_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the Virtual Machine policies.&#34;&#34;&#34;
        self._vm_policies = self._get_vm_policies()</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, vm_policy_name, vm_policy_type, vclient_name, vm_policy_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new Virtual Machine Policy to the Commcell.</p>
<h2 id="args">Args</h2>
<p>vm_policy_name
(str)
&ndash;
name of the new virtual machine policy to add to
the Commcell instance</p>
<p>vm_policy_type
(str)
&ndash;
type of virtual machine policy to be added
["Live Mount", "Clone From Template",
"Restore From Backup"]</p>
<p>vclient_name
(str)
&ndash;
the name of the virtualization client under which
vm policy is to be added</p>
<p>vm_policy_options
(dict)
&ndash;
optional dictionary passed by user to create a vm
policy. Allowed key-value pairs and input types
are given below
default: None</p>
<pre><code>"allDataStoresSelected"    (Boolean)  : if all data stores are to be selected;
                                            matters only if migrateVMs is set
                                            to True,
"daysRetainUntil"          (int)      : how many days to retain backup until,
"migrateVMs"               (Boolean)  : migrate to datastore after expiry
                                            (only for LiveMount),
"senderEmailId"            (str)      : email id of sender,
"minutesRetainUntil"       (int)      : how many days to retain backup until
"notifyToEmailIds"         (str)      : email id's to notify to; multiple
                                            emails separated by a comma
"quotaType"                (int)      : number of vm's/live mounts/labs per
                                            user,
"maxVMQuota"               (int)      : maximum number of VM quota,
"namingPattern"            (str)      : naming patter,
"description"              (str)      : description of vm policy,
"enabled"                  (Boolean)  : whether vm policy is enabled or not,
"allowRenewals"            (Boolean)  : whether to allow renewals or not,
"disableSuccessEmail"      (Boolean)  : send email on succesful creation of vm
                                            policy,
"allESXServersSelected"    (Boolean)  : select all esx servers in the vcenter,
"dataCenterName"           (str)      : data center name for vm policy,
"dataStores"               list(str)  : list of data store names,
"esxServers"               list(str)  : list of esx server names,
"users"                    list(str)  : list of users (user-names) to add to vm
                                            policy,
"userGroups"               list(str)  : list of usergroups (usergroup-names) to
                                            add to vm policy,
"networkNames"             list(str)  : list of network names,
------------------------ only for Live Mount ------------------------
"mediaAgent"               (str)      : media agent name for Live Mount,
"performAutoMigration"     (Boolean)  : automatic migration of vm
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
object of the corresponding virtual machine policy type</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the vm policy name argument is not string</p>
<pre><code>if type of the vcenter name argument is not string

if type of virtualization client name argument is not string or None

if policy type is not one of the virtual machine policy types as defined

if the type of vm_policy_options is not dict or None

if vm policy already exists with the given name (case insensitive)

if failed to create vm policy

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L722-L858" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(
        self,
        vm_policy_name,
        vm_policy_type,
        vclient_name,
        vm_policy_options=None
):
    &#34;&#34;&#34;Adds a new Virtual Machine Policy to the Commcell.

        Args:
            vm_policy_name       (str)   --  name of the new virtual machine policy to add to
                                                the Commcell instance

            vm_policy_type       (str)   --  type of virtual machine policy to be added
                                                [&#34;Live Mount&#34;, &#34;Clone From Template&#34;,
                                                &#34;Restore From Backup&#34;]

            vclient_name         (str)   --  the name of the virtualization client under which
                                                vm policy is to be added

            vm_policy_options    (dict)  --  optional dictionary passed by user to create a vm
                                               policy. Allowed key-value pairs and input types
                                               are given below
                default: None

                &#34;allDataStoresSelected&#34;    (Boolean)  : if all data stores are to be selected;
                                                            matters only if migrateVMs is set
                                                            to True,
                &#34;daysRetainUntil&#34;          (int)      : how many days to retain backup until,
                &#34;migrateVMs&#34;               (Boolean)  : migrate to datastore after expiry
                                                            (only for LiveMount),
                &#34;senderEmailId&#34;            (str)      : email id of sender,
                &#34;minutesRetainUntil&#34;       (int)      : how many days to retain backup until
                &#34;notifyToEmailIds&#34;         (str)      : email id&#39;s to notify to; multiple
                                                            emails separated by a comma
                &#34;quotaType&#34;                (int)      : number of vm&#39;s/live mounts/labs per
                                                            user,
                &#34;maxVMQuota&#34;               (int)      : maximum number of VM quota,
                &#34;namingPattern&#34;            (str)      : naming patter,
                &#34;description&#34;              (str)      : description of vm policy,
                &#34;enabled&#34;                  (Boolean)  : whether vm policy is enabled or not,
                &#34;allowRenewals&#34;            (Boolean)  : whether to allow renewals or not,
                &#34;disableSuccessEmail&#34;      (Boolean)  : send email on succesful creation of vm
                                                            policy,
                &#34;allESXServersSelected&#34;    (Boolean)  : select all esx servers in the vcenter,
                &#34;dataCenterName&#34;           (str)      : data center name for vm policy,
                &#34;dataStores&#34;               list(str)  : list of data store names,
                &#34;esxServers&#34;               list(str)  : list of esx server names,
                &#34;users&#34;                    list(str)  : list of users (user-names) to add to vm
                                                            policy,
                &#34;userGroups&#34;               list(str)  : list of usergroups (usergroup-names) to
                                                            add to vm policy,
                &#34;networkNames&#34;             list(str)  : list of network names,
                ------------------------ only for Live Mount ------------------------
                &#34;mediaAgent&#34;               (str)      : media agent name for Live Mount,
                &#34;performAutoMigration&#34;     (Boolean)  : automatic migration of vm

        Returns:
            object    --  object of the corresponding virtual machine policy type

        Raises:
            SDKException:
                if type of the vm policy name argument is not string

                if type of the vcenter name argument is not string

                if type of virtualization client name argument is not string or None

                if policy type is not one of the virtual machine policy types as defined

                if the type of vm_policy_options is not dict or None

                if vm policy already exists with the given name (case insensitive)

                if failed to create vm policy

                if response is empty

                if response is not success
            &#34;&#34;&#34;
    vm_policy_name = vm_policy_name.lower()
    vm_policy_type = vm_policy_type.lower()
    vclient_name = vclient_name.lower()
    _vm_policy_types = {&#39;live mount&#39;: 4,
                        &#39;clone from template&#39;: 0,
                        &#39;restore from backup&#39;: 13}
    self.refresh()
    if (
            not isinstance(vm_policy_name, str)
            or not isinstance(vclient_name, str)
            or not isinstance(vm_policy_options, (dict, type(None)))
    ):
        raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)
    elif vm_policy_type not in _vm_policy_types:
        err_msg = &#39;{0} is not a valid virtual machine policy type.&#39;.format(
            vm_policy_type)
        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
    elif self.has_policy(vm_policy_name):
        err_msg = &#39;Virtual Machine Policy &#34;{0}&#34; already exists (not case sensitive)&#39;.format(
            vm_policy_name)
        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
    else:
        if not vm_policy_options:
            vm_policy_options = {}
        vm_policy_options[&#39;vmAllocPolicyName&#39;] = vm_policy_name.lower()

        # setting the vclient name, vcenter name and policy type
        self._set_vclient_and_vcenter_names(vm_policy_options, vclient_name)
        vm_policy_options[&#39;policyType&#39;] = _vm_policy_types[vm_policy_type]

        # preparing the json values for adding the new policy
        _vm_policy_json = self._prepare_add_vmpolicy_json_default(vm_policy_options)

        # passing the built json to create the vm policy
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;POST&#39;, url=self._VMPOLICIES_URL, payload=_vm_policy_json)

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        o_str = &#39;Failed to create virtual machine policy\nError: &#34;{0}&#34;&#39;.format(
                            error_message)
                        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
                # returning object of VirtualMachinePolicy if there is no error in response
                self.refresh()
                return VirtualMachinePolicy(
                    self._commcell_object,
                    vm_policy_name=vm_policy_options[&#39;vmAllocPolicyName&#39;],
                    vm_policy_type_id=int(vm_policy_options[&#39;policyType&#39;]),
                    vm_policy_id=int(self._vm_policies[vm_policy_name][&#39;id&#39;]))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, vm_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the specified virtual machine policy from the commcell.</p>
<h2 id="args">Args</h2>
<p>vm_policy_name (str)
&ndash;
name of the virtual machine policy to delete</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the virtual machine policy name argument is not string</p>
<pre><code>if failed to delete virtual machine policy

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L860-L909" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, vm_policy_name):
    &#34;&#34;&#34;Deletes the specified virtual machine policy from the commcell.

        Args:
            vm_policy_name (str)  --  name of the virtual machine policy to delete

        Raises:
            SDKException:
                if type of the virtual machine policy name argument is not string

                if failed to delete virtual machine policy

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if not isinstance(vm_policy_name, str):
        raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)

    if self.has_policy(vm_policy_name):
        # retrieving the corresponding policy id for API call
        vm_policy_id = self._get_vm_policies()[vm_policy_name][&#39;id&#39;]
        policy_delete_url = self._VMPOLICIES_URL + &#39;/{0}&#39;.format(vm_policy_id)

        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            &#39;DELETE&#39;, policy_delete_url)

        if flag:
            try:
                if response.json():
                    if &#39;errorCode&#39; in response.json() and &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to delete virtual machine policy\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Virtual Machine&#39;, &#39;102&#39;, output_string.format(error_message))
            except ValueError:
                if response.text:
                    self.refresh()
                    return response.text.strip()
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    else:
        raise SDKException(
            &#39;Virtual Machine&#39;,
            &#39;102&#39;,
            &#39;No policy exists with name: {0}&#39;.format(vm_policy_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, vm_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a VirtualMachinePolicy object of the specified virtual machine policy name.</p>
<h2 id="args">Args</h2>
<p>vm_policy_name
(str)
&ndash;
name of the virtual machine policy</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the VirtualMachinePolicy class for the given policy name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the virtual machine policy name argument is not string
if no virtual machine policy exists with the given name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L690-L720" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, vm_policy_name):
    &#34;&#34;&#34;Returns a VirtualMachinePolicy object of the specified virtual machine policy name.

        Args:
            vm_policy_name     (str)   --  name of the virtual machine policy

        Returns:
            object - instance of the VirtualMachinePolicy class for the given policy name

        Raises:
            SDKException:
                if type of the virtual machine policy name argument is not string
                if no virtual machine policy exists with the given name
    &#34;&#34;&#34;
    if not isinstance(vm_policy_name, str):
        raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)

    vm_policy_name = vm_policy_name.lower()
    if self.has_policy(vm_policy_name):
        vm_policy_type_id = int(self._vm_policies[vm_policy_name][&#39;policyType&#39;])
        return VirtualMachinePolicy(
            self._commcell_object,
            vm_policy_name=vm_policy_name,
            vm_policy_type_id=vm_policy_type_id,
            vm_policy_id=int(self._vm_policies[vm_policy_name][&#39;id&#39;])
        )
    else:
        raise SDKException(
            &#39;Virtual Machine&#39;,
            &#39;102&#39;,
            &#39;No policy exists with name: {0}&#39;.format(vm_policy_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.has_policy"><code class="name flex">
<span>def <span class="ident">has_policy</span></span>(<span>self, vm_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a Virtual Machine policy exists with the given name</p>
<h2 id="args">Args</h2>
<p>policy_name (str)
&ndash;
name of the vm policy</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the vm policy exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the vm policy name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L671-L688" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_policy(self, vm_policy_name):
    &#34;&#34;&#34;Checks if a Virtual Machine policy exists with the given name

        Args:
            policy_name (str)  --  name of the vm policy

        Returns:
            bool - boolean output whether the vm policy exists in the commcell or not

        Raises:
            SDKException:
                if type of the vm policy name argument is not string
    &#34;&#34;&#34;
    if not isinstance(vm_policy_name, str):
        raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)

    return (self._vm_policies and
            vm_policy_name.lower() in self._vm_policies)</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the Virtual Machine policies.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L911-L913" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the Virtual Machine policies.&#34;&#34;&#34;
    self._vm_policies = self._get_vm_policies()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy"><code class="flex name class">
<span>class <span class="ident">VirtualMachinePolicy</span></span>
<span>(</span><span>commcell_object, vm_policy_name, vm_policy_type_id, vm_policy_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing a single Virtual Machine Policy. Contains method definitions for
common operations among all VM Policies</p>
<p>Initialize object of the VirtualMachinePolicy class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class
vm_policy_name
(str)
&ndash;
name of the vm policy to be created
vm_policy_type_id
(int)
&ndash;
type of policy (integer code for vm policy)
vm_policy_id
(int)
&ndash;
vm policy id if available (optional)</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash; instance of the VirtualMachinePolicy class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L916-L1126" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VirtualMachinePolicy(object):
    &#34;&#34;&#34;Class for representing a single Virtual Machine Policy. Contains method definitions for
        common operations among all VM Policies&#34;&#34;&#34;

    def __new__(
            cls,
            commcell_object,
            vm_policy_name,
            vm_policy_type_id,
            vm_policy_id=None
    ):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;
        if vm_policy_type_id == 4 or vm_policy_type_id == 2:  # for &#39;Live Mount&#39;
            return object.__new__(LiveMountPolicy)
        # TODO: future support for &#39;Clone From Template&#39;
        elif vm_policy_type_id == 6:
            return object.__new__(VirtualMachinePolicy)
        # TODO: future support for &#39;Restore From Backup&#39;
        else:
            return object.__new__(VirtualMachinePolicy)

    def __init__(
            self,
            commcell_object,
            vm_policy_name,
            vm_policy_type_id,
            vm_policy_id=None
    ):
        &#34;&#34;&#34;Initialize object of the VirtualMachinePolicy class.

            Args:
                commcell_object      (object)  --  instance of the Commcell class
                vm_policy_name       (str)     --  name of the vm policy to be created
                vm_policy_type_id    (int)     --  type of policy (integer code for vm policy)
                vm_policy_id         (int)     --  vm policy id if available (optional)

            Returns:
                object                       -- instance of the VirtualMachinePolicy class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._vm_policy_name = vm_policy_name
        self._vm_policy_type_id = vm_policy_type_id

        if vm_policy_id:
            self._vm_policy_id = str(vm_policy_id)
        else:
            self._vm_policy_id = self._get_vm_policy_id()

        self._VM_POLICY_URL = (self._commcell_object._services[&#39;GET_VM_ALLOCATION_POLICY&#39;]
                               % self._vm_policy_id)

        self._vm_policy_properties = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of this class.&#34;&#34;&#34;
        return (&#34;VirtualMachinePolicy class instance for Virtual Machine Policy: &#39;{0}&#39; for &#34;
                &#34;Commcell: &#39;{1}&#39;&#34;.format(self.vm_policy_name, self._commcell_object.commserv_name))

    def _get_vm_policy_id(self):
        &#34;&#34;&#34;Gets the virtual machine policy id associated with the svirtual machine policy&#34;&#34;&#34;
        vm_policies = VirtualMachinePolicies(self._commcell_object)
        return vm_policies.get(self.vm_policy_name).vm_policy_id

    def _get_vm_policy_properties(self):
        &#34;&#34;&#34;Gets the properties of the virtual machine policy.

            Returns:
                dict    --  dictionary consisting of the properties of this vm policy

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._VM_POLICY_URL
        )

        if flag:
            if response.json()[&#39;policy&#39;][0]:        # API returns an array with one element
                return response.json()[&#39;policy&#39;][0]
            else:
                raise SDKException(&#39;Response&#39;, 102)
        else:
            response_str = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, 101, response_str)

    def _update_vm_policy(self):
        &#34;&#34;&#34;Updates the vm policy using a PUT request with the updated properties json.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        update_policy_json = {
            &#39;action&#39;: 1,        # action 1 for PUT
            &#39;policy&#39;: self._vm_policy_properties
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._VM_POLICY_URL, update_policy_json
        )

        self.refresh()

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    if response.json()[&#39;error&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        o_str = &#39;Failed to update virtual machine policy\nError: &#34;{0}&#34;&#39;.format(
                            error_message)
                        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_str = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_str)

    @property
    def vm_policy_name(self):
        &#34;&#34;&#34;Treats the virtual machine policy name as a read-only attribute.&#34;&#34;&#34;
        return self._vm_policy_name

    @property
    def vm_policy_id(self):
        &#34;&#34;&#34;Treats the virtual machine policy id as a read-only attribute.&#34;&#34;&#34;
        return self._vm_policy_id

    @property
    def vm_policy_type_id(self):
        &#34;&#34;&#34;Treats the virtual machine policy type id as a read-only attribute.&#34;&#34;&#34;
        return self._vm_policy_type_id

    def disable(self):
        &#34;&#34;&#34;Disables a virtual machine policy if it is enabled.

            Raises:
                SDKException:
                    if vm policy is already disabled
        &#34;&#34;&#34;
        if not self._vm_policy_properties[&#39;enabled&#39;]:
            err_msg = &#39;Policy is already disabled&#39;
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

        self._vm_policy_properties[&#39;enabled&#39;] = False
        self._update_vm_policy()

    def enable(self):
        &#34;&#34;&#34;Enables a virtual machine policy if it is disabled.

                    Raises:
                        SDKException:
                            if vm policy is already enabled
                &#34;&#34;&#34;
        if self._vm_policy_properties[&#39;enabled&#39;]:
            err_msg = &#39;Policy is already enabled&#39;
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

        self._vm_policy_properties[&#39;enabled&#39;] = True
        self._update_vm_policy()

    def clone(self, desired_vm_policy_name):
        &#34;&#34;&#34;
        copies properties of the particular VM Policy and creates a new VM Policy with the
         specified name

        Args:
            desired_vm_policy_name   (str)  --  name of the policy that is going to be created

        Returns:
            object                          --  object of the Virtual Machine Policy

        Raises:
                SDKException:
                    if type of the desired vm policy name argument is not string

                    if a vm policy already exists by the desired vm policy name
        &#34;&#34;&#34;
        vm_policies_object = VirtualMachinePolicies(self._commcell_object)
        if not isinstance(desired_vm_policy_name, str):
            raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)
        elif vm_policies_object.has_policy(desired_vm_policy_name):
            err_msg = &#39;Policy &#34;{0}&#34; already exists&#39;.format(desired_vm_policy_name)
            raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
        else:
            import copy
            desired_vm_policy_properties = copy.deepcopy(self._vm_policy_properties)
            desired_vm_policy_name = desired_vm_policy_name.lower()
            desired_vm_policy_properties[&#39;entity&#39;][&#39;vmAllocPolicyName&#39;] = desired_vm_policy_name
            del desired_vm_policy_properties[&#39;entity&#39;][&#39;vmAllocPolicyId&#39;]
            desired_vm_policy_json = {
                &#39;action&#39;: 0,
                &#39;policy&#39;: desired_vm_policy_properties
            }

            return vm_policies_object._clone_vm_policy(desired_vm_policy_json)

    # TODO: modify(self, vm_policy_details) - Modifies the policy as per the details passed

    def properties(self):
        &#34;&#34;&#34;Returns the virtual machine properties&#34;&#34;&#34;
        return self._vm_policy_properties

    def refresh(self):
        &#34;&#34;&#34;Refresh the Virtual Machine policy properties.&#34;&#34;&#34;
        self._vm_policy_properties = self._get_vm_policy_properties()</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.virtualmachinepolicies.LiveMountPolicy" href="#cvpysdk.virtualmachinepolicies.LiveMountPolicy">LiveMountPolicy</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_id"><code class="name">var <span class="ident">vm_policy_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the virtual machine policy id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1044-L1047" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_policy_id(self):
    &#34;&#34;&#34;Treats the virtual machine policy id as a read-only attribute.&#34;&#34;&#34;
    return self._vm_policy_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_name"><code class="name">var <span class="ident">vm_policy_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the virtual machine policy name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1039-L1042" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_policy_name(self):
    &#34;&#34;&#34;Treats the virtual machine policy name as a read-only attribute.&#34;&#34;&#34;
    return self._vm_policy_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_type_id"><code class="name">var <span class="ident">vm_policy_type_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the virtual machine policy type id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1049-L1052" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_policy_type_id(self):
    &#34;&#34;&#34;Treats the virtual machine policy type id as a read-only attribute.&#34;&#34;&#34;
    return self._vm_policy_type_id</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.clone"><code class="name flex">
<span>def <span class="ident">clone</span></span>(<span>self, desired_vm_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>copies properties of the particular VM Policy and creates a new VM Policy with the
specified name</p>
<h2 id="args">Args</h2>
<p>desired_vm_policy_name
(str)
&ndash;
name of the policy that is going to be created</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
object of the Virtual Machine Policy</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the desired vm policy name argument is not string</p>
<pre><code>if a vm policy already exists by the desired vm policy name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1082-L1116" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def clone(self, desired_vm_policy_name):
    &#34;&#34;&#34;
    copies properties of the particular VM Policy and creates a new VM Policy with the
     specified name

    Args:
        desired_vm_policy_name   (str)  --  name of the policy that is going to be created

    Returns:
        object                          --  object of the Virtual Machine Policy

    Raises:
            SDKException:
                if type of the desired vm policy name argument is not string

                if a vm policy already exists by the desired vm policy name
    &#34;&#34;&#34;
    vm_policies_object = VirtualMachinePolicies(self._commcell_object)
    if not isinstance(desired_vm_policy_name, str):
        raise SDKException(&#39;Virtual Machine&#39;, &#39;101&#39;)
    elif vm_policies_object.has_policy(desired_vm_policy_name):
        err_msg = &#39;Policy &#34;{0}&#34; already exists&#39;.format(desired_vm_policy_name)
        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)
    else:
        import copy
        desired_vm_policy_properties = copy.deepcopy(self._vm_policy_properties)
        desired_vm_policy_name = desired_vm_policy_name.lower()
        desired_vm_policy_properties[&#39;entity&#39;][&#39;vmAllocPolicyName&#39;] = desired_vm_policy_name
        del desired_vm_policy_properties[&#39;entity&#39;][&#39;vmAllocPolicyId&#39;]
        desired_vm_policy_json = {
            &#39;action&#39;: 0,
            &#39;policy&#39;: desired_vm_policy_properties
        }

        return vm_policies_object._clone_vm_policy(desired_vm_policy_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.disable"><code class="name flex">
<span>def <span class="ident">disable</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables a virtual machine policy if it is enabled.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if vm policy is already disabled</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1054-L1066" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable(self):
    &#34;&#34;&#34;Disables a virtual machine policy if it is enabled.

        Raises:
            SDKException:
                if vm policy is already disabled
    &#34;&#34;&#34;
    if not self._vm_policy_properties[&#39;enabled&#39;]:
        err_msg = &#39;Policy is already disabled&#39;
        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

    self._vm_policy_properties[&#39;enabled&#39;] = False
    self._update_vm_policy()</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.enable"><code class="name flex">
<span>def <span class="ident">enable</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables a virtual machine policy if it is disabled.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if vm policy is already enabled</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1068-L1080" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable(self):
    &#34;&#34;&#34;Enables a virtual machine policy if it is disabled.

                Raises:
                    SDKException:
                        if vm policy is already enabled
            &#34;&#34;&#34;
    if self._vm_policy_properties[&#39;enabled&#39;]:
        err_msg = &#39;Policy is already enabled&#39;
        raise SDKException(&#39;Virtual Machine&#39;, &#39;102&#39;, err_msg)

    self._vm_policy_properties[&#39;enabled&#39;] = True
    self._update_vm_policy()</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.properties"><code class="name flex">
<span>def <span class="ident">properties</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the virtual machine properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1120-L1122" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def properties(self):
    &#34;&#34;&#34;Returns the virtual machine properties&#34;&#34;&#34;
    return self._vm_policy_properties</code></pre>
</details>
</dd>
<dt id="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the Virtual Machine policy properties.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/virtualmachinepolicies.py#L1124-L1126" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the Virtual Machine policy properties.&#34;&#34;&#34;
    self._vm_policy_properties = self._get_vm_policy_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.virtualmachinepolicies.LiveMountPolicy" href="#cvpysdk.virtualmachinepolicies.LiveMountPolicy">LiveMountPolicy</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.virtualmachinepolicies.LiveMountPolicy.live_mount" href="#cvpysdk.virtualmachinepolicies.LiveMountPolicy.live_mount">live_mount</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.LiveMountPolicy.live_mounted_vm_name" href="#cvpysdk.virtualmachinepolicies.LiveMountPolicy.live_mounted_vm_name">live_mounted_vm_name</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.LiveMountPolicy.view_active_mounts" href="#cvpysdk.virtualmachinepolicies.LiveMountPolicy.view_active_mounts">view_active_mounts</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicies">VirtualMachinePolicies</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.add" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.add">add</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.delete" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.delete">delete</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.get" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.get">get</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.has_policy" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.has_policy">has_policy</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.refresh" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicies.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy">VirtualMachinePolicy</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.clone" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.clone">clone</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.disable" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.disable">disable</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.enable" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.enable">enable</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.properties" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.properties">properties</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.refresh" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_id" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_id">vm_policy_id</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_name" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_name">vm_policy_name</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_type_id" href="#cvpysdk.virtualmachinepolicies.VirtualMachinePolicy.vm_policy_type_id">vm_policy_type_id</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>