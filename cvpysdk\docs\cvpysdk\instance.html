<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instance API documentation</title>
<meta name="description" content="Main file for performing instance operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instance</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing instance operations.</p>
<p>Instances and Instance are 2 classes defined in this file.</p>
<p>Instances:
Class for representing all the instances associated with a specific agent</p>
<p>Instance:
Class for a single instance selected for an agent,
and to perform operations on that instance</p>
<h2 id="instances">Instances</h2>
<p><strong>init</strong>(agent_object)
&ndash;
initialise object of Instances class associated with
the specified agent</p>
<p><strong>str</strong>()
&ndash;
returns all the instances associated with the agent</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the object of the Instances class</p>
<p><strong>len</strong>()
&ndash;
returns the number of instances associated to the Agent</p>
<p><strong>getitem</strong>()
&ndash;
returns the name of the instance for the given instance ID
or the details for the given instance name</p>
<p>_get_instances()
&ndash;
gets all the instances associated with the agent specified</p>
<p>all_instances()
&ndash;
returns the dict of all the instances</p>
<p>has_instance(instance_name)
&ndash;
checks if a instance exists with the given name or not</p>
<p>get(instance_name)
&ndash;
returns the Instance class object
of the input backup set name</p>
<p>_process_add_response()
&ndash;
to process the add instance request using API call</p>
<p>add_sap_hana_instance()
&ndash;
method to add new sap hana instance</p>
<p>add_informix_instance()
&ndash;
adds new Informix Instance to given Client</p>
<p>delete()
&ndash;
deletes the instance specified by the instance_name
from the agent.</p>
<p>add_sybase_instance()
&ndash;
To add sybase server instance</p>
<p>add_big_data_apps_instance()
&ndash;
To add an instance with the big data apps agent specified</p>
<p>add_cloud_storage_instance()
&ndash;
Method to add a new cloud storage instance</p>
<p>add_salesforce_instance()
&ndash;
Method to add a new salesforce instance</p>
<p>add_postgresql_instance()
&ndash;
Method to add a new postgresql instance</p>
<p>add_cosmosdb_instance()
&ndash;
Method to add new cosmosdb instance</p>
<p>_set_general_properties_json()
&ndash;
setter for general cloud properties while adding a new
cloud storage instance</p>
<p>_set_instance_properties_json() &ndash;
setter for cloud storage instance properties while adding a
new cloud storage instance</p>
<p>refresh()
&ndash;
refresh the instances associated with the agent</p>
<p>add_mysql_instance()
&ndash;
Method to add new mysql Instance</p>
<h2 id="instance">Instance</h2>
<p><strong>init</strong>()
&ndash;
initialise object of Instance with the specified instance
name and id, and associated to the specified agent</p>
<p><strong>repr</strong>()
&ndash;
return the instance name, the object is associated with</p>
<p>_get_instance_id()
&ndash;
method to get the instance id, if not specified in <strong>init</strong></p>
<p>_get_instance_properties()
&ndash;
method to get the properties of the instance</p>
<p>_process_update_response()
&ndash;
updates the instance properties</p>
<p>_process_restore_response()
&ndash;
processes the restore request sent to server
and returns the restore job object</p>
<p>_process_delete_response()
&ndash;
Runs the DeleteDocuments API with the request JSON provided for Delete,
and returns the contents after parsing the response</p>
<p>_filter_paths()
&ndash;
filters the path as per the OS, and the Agent</p>
<p>_impersonation_json()
&ndash;
setter for impersonation Property</p>
<p>_restore_browse_option_json()
&ndash;
setter for
browse option
property in restore</p>
<p>_restore_commonOptions_json()
&ndash;
setter for common options property in restore</p>
<p>_restore_destination_json()
&ndash;
setter for destination options property in restore</p>
<p>_restore_fileoption_json()
&ndash;
setter for file option property in restore</p>
<p>_restore_virtual_rst_option_json &ndash;
setter for the virtualServer restore option in restore JSON</p>
<p>_restore_destination_json()
&ndash;
setter for destination property in restore</p>
<p>_restore_volume_rst_option_json()
&ndash;
setter for the volumeRst restore option in restore JSON</p>
<p>_restore_json()
&ndash;
returns the apppropriate JSON request to pass for either
Restore In-Place or Out-of-Place operation</p>
<p>_restore_in_place()
&ndash;
Restores the files/folders specified in the
input paths list to the same location</p>
<p>_restore_out_of_place()
&ndash;
Restores the files/folders specified in the input paths
list to the input client, at the specified destination location</p>
<p>_restore_bigdataapps_option_json() &ndash;
setter for bigdata apps option property in restore</p>
<p>_task()
&ndash;
the task dict used while restore/backup job</p>
<p>_restore_sub_task()
&ndash;
the restore job specific sub task dict used to form
restore json</p>
<p>_process_update_request()
&ndash;
to process the request using API call</p>
<p>_get_instance_properties_json() &ndash;
returns the instance properties</p>
<p>update_properties()
&ndash;
to update the instance properties</p>
<p>instance_id()
&ndash;
id of this instance</p>
<p>instance_name()
&ndash;
name of this instance</p>
<p>credentials()
&ndash;
Sets the credentials for the instance</p>
<p>browse()
&ndash;
browse the content of the instance</p>
<p>find()
&ndash;
find content in the instance</p>
<p>refresh()
&ndash;
refresh the properties of the instance</p>
<h2 id="instance-attributes">Instance Attributes</h2>
<pre><code>**instance_id**          --  returns the instance id

**instance_name**        --  returns the instance name

**name**                 --  returns the instance display name

**properties**           --  returns the properties of the instance

**subclients**           --  returns the configured subclients for the instance

**backupsets**           --  returns the backupsets associated with the instance

**credentials**          --  returns the credentials associated with the instance
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L1-L3401" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing instance operations.

Instances and Instance are 2 classes defined in this file.

Instances:  Class for representing all the instances associated with a specific agent

Instance:   Class for a single instance selected for an agent,
and to perform operations on that instance


Instances:
    __init__(agent_object)          --  initialise object of Instances class associated with
    the specified agent

    __str__()                       --  returns all the instances associated with the agent

    __repr__()                      --  returns the string for the object of the Instances class

    __len__()                       --  returns the number of instances associated to the Agent

    __getitem__()                   --  returns the name of the instance for the given instance ID
    or the details for the given instance name

    _get_instances()                --  gets all the instances associated with the agent specified

    all_instances()                 --  returns the dict of all the instances

    has_instance(instance_name)     --  checks if a instance exists with the given name or not

    get(instance_name)              --  returns the Instance class object
    of the input backup set name

    _process_add_response()         --  to process the add instance request using API call

    add_sap_hana_instance()         --  method to add new sap hana instance

    add_informix_instance()         --  adds new Informix Instance to given Client

    delete()                        --  deletes the instance specified by the instance_name
    from the agent.

    add_sybase_instance()           --  To add sybase server instance

    add_big_data_apps_instance()    --  To add an instance with the big data apps agent specified

    add_cloud_storage_instance()    --  Method to add a new cloud storage instance

    add_salesforce_instance()       --  Method to add a new salesforce instance

    add_postgresql_instance()       --  Method to add a new postgresql instance

    add_cosmosdb_instance()         --  Method to add new cosmosdb instance

    _set_general_properties_json()  --  setter for general cloud properties while adding a new
    cloud storage instance

    _set_instance_properties_json() --  setter for cloud storage instance properties while adding a
    new cloud storage instance

    refresh()                       --  refresh the instances associated with the agent
    
    add_mysql_instance()            --  Method to add new mysql Instance


Instance:
    __init__()                      --  initialise object of Instance with the specified instance
    name and id, and associated to the specified agent

    __repr__()                      --  return the instance name, the object is associated with

    _get_instance_id()              --  method to get the instance id, if not specified in __init__

    _get_instance_properties()      --  method to get the properties of the instance

    _process_update_response()      --  updates the instance properties

    _process_restore_response()     --  processes the restore request sent to server
    and returns the restore job object

    _process_delete_response()      --  Runs the DeleteDocuments API with the request JSON provided for Delete,
                                        and returns the contents after parsing the response

    _filter_paths()                 --  filters the path as per the OS, and the Agent

    _impersonation_json()           --  setter for impersonation Property

    _restore_browse_option_json()   --  setter for  browse option  property in restore

    _restore_commonOptions_json()   --  setter for common options property in restore

    _restore_destination_json()     --  setter for destination options property in restore

    _restore_fileoption_json()      --  setter for file option property in restore

    _restore_virtual_rst_option_json --  setter for the virtualServer restore option in restore JSON

    _restore_destination_json()     --  setter for destination property in restore

    _restore_volume_rst_option_json()  --  setter for the volumeRst restore option in restore JSON

    _restore_json()                 --  returns the apppropriate JSON request to pass for either
    Restore In-Place or Out-of-Place operation

    _restore_in_place()             --  Restores the files/folders specified in the
    input paths list to the same location

    _restore_out_of_place()         --  Restores the files/folders specified in the input paths
    list to the input client, at the specified destination location

   _restore_bigdataapps_option_json() --  setter for bigdata apps option property in restore

    _task()                         --  the task dict used while restore/backup job

    _restore_sub_task()             --  the restore job specific sub task dict used to form
    restore json

    _process_update_request()       --  to process the request using API call

    _get_instance_properties_json() --  returns the instance properties

    update_properties()             --  to update the instance properties

    instance_id()                   --  id of this instance

    instance_name()                 --  name of this instance

    credentials()                   --  Sets the credentials for the instance

    browse()                        --  browse the content of the instance

    find()                          --  find content in the instance

    refresh()                       --  refresh the properties of the instance

Instance Attributes
-----------------

    **instance_id**          --  returns the instance id

    **instance_name**        --  returns the instance name

    **name**                 --  returns the instance display name

    **properties**           --  returns the properties of the instance

    **subclients**           --  returns the configured subclients for the instance

    **backupsets**           --  returns the backupsets associated with the instance

    **credentials**          --  returns the credentials associated with the instance

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

import copy

from base64 import b64encode

from .job import Job
from .subclient import Subclients
from .constants import AppIDAType
from .exception import SDKException
from .schedules import SchedulePattern, Schedules


class Instances(object):
    &#34;&#34;&#34;Class for getting all the instances associated with a client.&#34;&#34;&#34;

    def __init__(self, agent_object):
        &#34;&#34;&#34;Initialize object of the Instances class.

            Args:
                agent_object (object)  --  instance of the Agent class

            Returns:
                object - instance of the Instances class
        &#34;&#34;&#34;
        self._agent_object = agent_object
        self._client_object = self._agent_object._client_object

        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._INSTANCES = self._services[&#39;GET_ALL_INSTANCES&#39;] % (
            self._client_object.client_id
        )

        self._general_properties = None
        self._instance_properties = None
        self._instances = None
        self._vs_instance_type_dict = {}
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all instances of the agent of a client.

            Returns:
                str - string of all the instances of an agent of a client
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;S. No.&#39;, &#39;Instance&#39;, &#39;Agent&#39;, &#39;Client&#39;
        )

        for index, instance in enumerate(self._instances):
            sub_str = &#39;{:^5}\t{:20}\t{:20}\t{:20}\n&#39;.format(
                index + 1,
                instance,
                self._agent_object.agent_name,
                self._client_object.client_name
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Instances class.&#34;&#34;&#34;
        return &#34;Instances class instance for Agent: &#39;{0}&#39;&#34;.format(self._agent_object.agent_name)

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the instances associated to the Agent.&#34;&#34;&#34;
        return len(self.all_instances)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the instance for the given instance ID or
            the details of the instance for given instance Name.

            Args:
                value   (str / int)     --  Name or ID of the instance

            Returns:
                str     -   name of the instance, if the instance id was given

                dict    -   dict of details of the instance, if instance name was given

            Raises:
                IndexError:
                    no instance exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_instances:
            return self.all_instances[value]
        else:
            try:
                return list(
                    filter(lambda x: x[1] == value, self.all_instances.items())
                )[0][0]
            except IndexError:
                raise IndexError(&#39;No instance exists with the given Name / Id&#39;)

    def _get_instances(self):
        &#34;&#34;&#34;Gets all the instances associated to the agent specified by agent_object.

            Returns:
                dict - consists of all instances of the agent
                    {
                         &#34;instance1_name&#34;: instance1_id,
                         &#34;instance2_name&#34;: instance2_id
                    }

            Raises:
                SDKException:
                    if failed to get instances

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if &#39;file system&#39; in self._agent_object.agent_name:
            return_dict = {
                &#39;defaultinstancename&#39;: 1
            }
            return return_dict

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._INSTANCES)

        if flag:
            if response.json():
                if &#39;instanceProperties&#39; in response.json():
                    return_dict = {}

                    instance_properties = response.json()[&#39;instanceProperties&#39;]
                    for dictionary in instance_properties:

                        agent = dictionary[&#39;instance&#39;][&#39;appName&#39;].lower()

                        if (self._agent_object.agent_name in agent) or (&#39;mariadb&#39; in agent and &#39;mysql&#39; in self._agent_object.agent_name):
                            temp_name = dictionary[&#39;instance&#39;][&#39;instanceName&#39;].lower()
                            temp_id = str(dictionary[&#39;instance&#39;][&#39;instanceId&#39;]).lower()
                            return_dict[temp_name] = temp_id

                        if &#39;vsInstanceType&#39; in dictionary.get(&#39;virtualServerInstance&#39;, &#39;&#39;):
                            self._vs_instance_type_dict[str(dictionary[&#39;instance&#39;][&#39;instanceId&#39;])] = dictionary[
                                &#34;virtualServerInstance&#34;][&#34;vsInstanceType&#34;]

                    return return_dict
                elif &#39;errors&#39; in response.json():
                    error = response.json()[&#39;errors&#39;][0]
                    error_string = error[&#39;errorString&#39;]
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, error_string)
                else:
                    return {}
            else:
                return {}
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def all_instances(self):
        &#34;&#34;&#34;Returns dict of all the instances associated with the agent

            dict - consists of all instances of the agent
                    {
                         &#34;instance1_name&#34;: instance1_id,
                         &#34;instance2_name&#34;: instance2_id
                    }

        &#34;&#34;&#34;
        return self._instances

    def has_instance(self, instance_name):
        &#34;&#34;&#34;Checks if a instance exists for the agent with the input instance name.

            Args:
                instance_name (str)  --  name of the instance

            Returns:
                bool - boolean output whether the instance exists for the agent or not

            Raises:
                SDKException:
                    if type of the instance name argument is not string
        &#34;&#34;&#34;
        if not isinstance(instance_name, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        return self._instances and instance_name.lower() in self._instances

    def get(self, instance_name):
        &#34;&#34;&#34;Returns a instance object of the specified instance name.

            Args:
                instance_name (str/int)  --  name or ID of the instance

            Returns:
                object - instance of the Instance class for the given instance name

            Raises:
                SDKException:
                    if type of the instance name argument is not string or Int

                    if no instance exists with the given name
        &#34;&#34;&#34;
        if isinstance(instance_name, str):
            instance_name = instance_name.lower()

            if self.has_instance(instance_name):
                return Instance(self._agent_object, instance_name, self._instances[instance_name])

            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;No instance exists with name: &#34;{0}&#34;&#39;.format(instance_name)
            )
        elif isinstance(instance_name, int):
            instance_name = str(instance_name)
            instance_name = [name for name, instance_id in self.all_instances.items() if instance_name == instance_id]

            if instance_name:
                return self.get(instance_name[0])
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists with the given ID: {0}&#39;.format(instance_name))

        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    def _process_add_response(self, request_json):
        &#34;&#34;&#34;Runs the Intance Add API with the request JSON provided,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;ADD_INSTANCE&#39;], request_json)
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to create instance\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                    else:
                        # initialize the instances again
                        # so the instance object has all the instances
                        instance_name = response.json(
                        )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                        self.refresh()
                        return self.get(instance_name)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create instance\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_informix_instance(self, informix_options):
        &#34;&#34;&#34;Adds new Informix Instance to given Client
            Args:
                Dictionary of informix instance creation options:
                    Example:
                       informix_options = {
                            &#39;instance_name&#39;: &#34;&#34;,
                            &#39;onconfig_file&#39;: &#34;&#34;,
                            &#39;sql_host_file&#39;: &#34;&#34;,
                            &#39;informix_dir&#39;: &#34;&#34;,
                            &#39;user_name&#39;: &#34;&#34;,
                            &#39;domain_name&#39;: &#34;&#34;,
                            &#39;password&#39;: &#34;&#34;,
                            &#39;storage_policy&#39;: &#34;&#34;,
                            &#39;description&#39;:&#39;created from automation&#39;
                        }

            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if None value in informix options

                    if Informix instance with same name already exists

                    if given storage policy does not exists in commcell
        &#34;&#34;&#34;
        if None in informix_options.values():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;One of the informix parameter is None so cannot proceed with instance creation&#34;)

        if self.has_instance(informix_options[&#34;instance_name&#34;]):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    informix_options[&#34;instance_name&#34;])
            )

        if not self._commcell_object.storage_policies.has_policy(
                informix_options[&#34;storage_policy&#34;]):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    informix_options[&#34;storage_policy&#34;])
            )
        password = b64encode(informix_options[&#34;password&#34;].encode()).decode()

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;description&#34;: informix_options[&#39;description&#39;],
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;instanceName&#34;: informix_options[&#34;instance_name&#34;],
                    &#34;appName&#34;: &#34;Informix Database&#34;
                },
                &#34;informixInstance&#34;: {
                    &#34;onConfigFile&#34;: informix_options[&#34;onconfig_file&#34;],
                    &#34;sqlHostfile&#34;: informix_options[&#34;sql_host_file&#34;],
                    &#34;informixDir&#34;: informix_options[&#34;informix_dir&#34;],
                    &#34;informixUser&#34;: {
                        &#34;password&#34;: password,
                        &#34;domainName&#34;: informix_options[&#34;domain_name&#34;],
                        &#34;userName&#34;: informix_options[&#34;user_name&#34;]
                    },
                    &#34;informixStorageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: informix_options[&#34;storage_policy&#34;]
                        },
                        &#34;deDuplicationOptions&#34;: {},
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: informix_options[&#34;storage_policy&#34;]
                        },
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: informix_options[&#34;storage_policy&#34;]
                        }
                    }
                }
            }
        }

        add_instance = self._commcell_object._services[&#39;ADD_INSTANCE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, add_instance, request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;].get(&#39;errorCode&#39;)

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;].get(&#39;errorString&#39;)
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    if &#39;entity&#39; in response.json()[&#39;response&#39;]:
                        self.refresh()
                        return self.get(response.json()[&#39;response&#39;][&#39;entity&#39;].get(&#39;instanceName&#39;))
                    else:
                        raise SDKException(
                            &#39;Instance&#39;,
                            &#39;102&#39;,
                            &#39;Unable to get instance name and id&#39;
                        )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add_sap_hana_instance(self, **kwargs):
        &#34;&#34;&#34;Adds new sap hana instance to given client
            Args:
                sid             (str)   -- Database SID
                hana_client_name(str)   --  Client where the hana server exists
                db_user_name   (str)    -- postgres user name
                db_password    (str)    -- DB password
                storage_policy  (str)   --  Storage Policy name
            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if None value in sap hana options

                    if sap hana instance with same name already exists

                    if given storage policy does not exists in commcell
        &#34;&#34;&#34;

        if self.has_instance(kwargs.get(&#39;sid&#39;, &#39;&#39;)):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    kwargs.get(&#39;sid&#39;, &#39;&#39;))
            )
        password = b64encode(kwargs.get(&#34;db_password&#34;, &#34;&#34;).encode()).decode()
        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: kwargs.get(&#39;sid&#39;, &#39;&#39;),
                    &#34;applicationId&#34;: 135,
                },
                &#34;saphanaInstance&#34;: {
                    &#34;dbInstanceNumber&#34;: &#34;00&#34;,
                    &#34;hdbsqlLocationDirectory&#34;: f&#34;/usr/sap/{kwargs.get(&#39;sid&#39;, &#39;&#39;)}/HDB00/exe&#34;,
                    &#34;SAPHANAUser&#34;: {
                        &#34;userName&#34;: f&#34;{kwargs.get(&#39;sid&#39;, &#39;&#39;).lower()}adm&#34;
                    },
                    &#34;dbUser&#34;: {
                        &#34;userName&#34;: kwargs.get(&#34;db_user_name&#34;, &#34;&#34;),
                        &#34;password&#34;: password
                    },
                    &#34;saphanaStorageDevice&#34;: {
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: kwargs.get(&#34;storage_policy&#34;, &#34;&#34;)
                        },
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: kwargs.get(&#34;storage_policy&#34;, &#34;&#34;)
                        }
                    },
                    &#34;DBInstances&#34;: [
                        {
                            &#34;clientName&#34;: kwargs.get(&#34;hana_client_name&#34;, &#34;&#34;)
                        }
                    ]

                }
            }
        }
        self._process_add_response(request_json)

    def delete(self, instance_name):
        &#34;&#34;&#34;Deletes the instance specified by the instance_name from the agent.

            Args:
                instance_name (str)  --  name of the instance to remove from the agent

            Raises:
                SDKException:
                    if type of the instance name argument is not string

                    if failed to delete instance

                    if response is empty

                    if response is not success

                    if no instance exists with the given name
        &#34;&#34;&#34;
        if not isinstance(instance_name, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        else:
            instance_name = instance_name.lower()

        if self.has_instance(instance_name):
            delete_instance_service = self._commcell_object._services[&#39;INSTANCE&#39;] % (
                self._instances.get(instance_name)
            )

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;DELETE&#39;, delete_instance_service
            )

            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response_value = response.json()[&#39;response&#39;][0]
                        error_code = str(response_value.get(&#39;errorCode&#39;))
                        error_message = None

                        if &#39;errorString&#39; in response_value:
                            error_message = response_value[&#39;errorString&#39;]

                        if error_message:
                            o_str = &#39;Failed to delete instance\nError: &#34;{0}&#34;&#39;
                            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(error_message))
                        else:
                            if error_code == &#39;0&#39;:
                                # initialize the instances again
                                # so the instance object has all the instances
                                self.refresh()
                            else:
                                o_str = (&#39;Failed to delete instance with Error Code: &#34;{0}&#34;\n&#39;
                                         &#39;Please check the documentation for &#39;
                                         &#39;more details on the error&#39;)
                                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(error_code))
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists with name: {0}&#39;.format(instance_name)
            )

    def add_sybase_instance(self, sybase_options):
        &#34;&#34;&#34;
            Method to Add new Sybase Instance to given Client
            Args:
                Dictionary of sybase instance creation options:
                    Example:
                       sybase_options = {
                            &#39;instance_name&#39;: &#39;&#39;,
                            &#39;sybase_ocs&#39;: &#39;&#39;,
                            &#39;sybase_ase&#39;: &#39;&#39;,
                            &#39;backup_server&#39;: &#39;&#39;,
                            &#39;sybase_home&#39;: &#39;&#39;,
                            &#39;config_file&#39;: &#39;&#39;,
                            &#39;enable_auto_discovery&#39;: True,
                            &#39;shared_memory_directory&#39;: &#39;&#39;,
                            &#39;storage_policy&#39;: &#39;&#39;,
                            &#39;sa_username&#39;: &#39;&#39;,
                            &#39;sa_password&#39;: &#39;&#39;,
                            &#39;localadmin_username&#39;: &#39;&#39;,
                            &#39;localadmin_password&#39;: &#39;&#39;,
                            &#39;masterkey_password&#39;:&#39;&#39;
                        }
            Raises:
                SDKException:
                    if None value in sybase options

                    if Sybase instance with same name already exists

                    if given storage policy does not exists in commcell

        &#34;&#34;&#34;

        if None in sybase_options.values():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;One of the sybase parameter is None so cannot proceed with instance creation&#34;)

        if self.has_instance(sybase_options[&#34;instance_name&#34;]):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    sybase_options[&#34;instance_name&#34;])
            )

        if not self._commcell_object.storage_policies.has_policy(sybase_options[&#34;storage_policy&#34;]):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    sybase_options[&#34;storage_policy&#34;])
            )

        # encodes the plain text password using base64 encoding
        sa_password = b64encode(sybase_options[&#34;sa_password&#34;].encode()).decode()

        enable_auto_discovery = sybase_options[&#34;enable_auto_discovery&#34;]

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: &#34;Sybase&#34;,
                    &#34;instanceName&#34;: sybase_options[&#34;instance_name&#34;],
                    &#34;_type_&#34;: 5,
                    &#34;applicationId&#34;: 5
                },
                &#34;planEntity&#34;: {
                    &#34;planName&#34;: sybase_options[&#34;storage_policy&#34;]
                },
                &#34;sybaseInstance&#34;: {
                    &#34;sybaseOCS&#34;: sybase_options[&#34;sybase_ocs&#34;],
                    &#34;sybaseBlockSize&#34;: 65536,
                    &#34;backupServer&#34;: sybase_options[&#34;backup_server&#34;],
                    &#34;sybaseHome&#34;: sybase_options[&#34;sybase_home&#34;],
                    &#34;sybaseASE&#34;: sybase_options[&#34;sybase_ase&#34;],
                    &#34;configFile&#34;: sybase_options[&#34;config_file&#34;],
                    &#34;enableAutoDiscovery&#34;: enable_auto_discovery,
                    &#34;sharedMemoryDirectory&#34;: sybase_options[&#34;shared_memory_directory&#34;],
                    &#34;saUser&#34;: {&#34;password&#34;: sa_password, &#34;userName&#34;: sybase_options[&#34;sa_username&#34;]},
                    &#34;localAdministrator&#34;: {
                        &#34;password&#34;: sybase_options[&#34;localadmin_password&#34;],
                        &#34;userName&#34;: sybase_options[&#34;localadmin_username&#34;]
                    }
                }
            }
        }
        if &#34;masterkey_password&#34; in sybase_options.keys():
            masterkey_password = b64encode(sybase_options[&#34;masterkey_password&#34;].encode()).decode()
            request_json[&#34;instanceProperties&#34;][&#34;sybaseInstance&#34;][&#34;masterKeyPwd&#34;]=masterkey_password

        if &#34;localadmin_password&#34; in sybase_options.keys():
            localadmin_password = b64encode(sybase_options[&#34;localadmin_password&#34;].encode()).decode()
            request_json[&#39;instanceProperties&#39;][&#39;sybaseInstance&#39;][&#39;localAdministrator&#39;][&#39;password&#39;] = localadmin_password

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ADD_INSTANCE&#39;], request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    instance_name = response.json(
                    )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                    instance_id = response.json(
                    )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceId&#39;]
                    agent_name = self._agent_object.agent_name
                    self.refresh()
                    return self.get(instance_name)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_db2_instance(self, db2_options):
        &#34;&#34;&#34;
            Method to Add new Db2 Instance to given Client
                Args:
                        Dictionary of db2 instance creation options:
                            Example:
                               db2_options = {
                                    &#39;instance_name&#39;: &#39;db2inst1&#39;,
                                    &#39;data_storage_policy&#39;: &#39;data_sp&#39;,
                                    &#39;log_storage_policy&#39;: &#39;log_sp&#39;,
                                    &#39;command_storage_policy&#39;: &#39;cmd_sp&#39;,
                                    &#39;home_directory&#39;:&#39;/home/<USER>
                                    &#39;password&#39;:&#39;#####&#39;,
                                    &#39;user_name&#39;:&#39;db2inst1&#39;,
                                    &#39;credential_name&#39;: &#39;cred_name&#39;
                                }
                    Raises:
                        SDKException:
                            if None value in db2 options

                            if db2 instance with same name already exists

                            if given storage policy does not exists in commcell

        &#34;&#34;&#34;
        if not all(
                key in db2_options for key in(
                    &#34;instance_name&#34;,
                    &#34;data_storage_policy&#34;,
                    &#34;log_storage_policy&#34;,
                    &#34;command_storage_policy&#34;,
                    &#34;home_directory&#34;,
                    &#34;password&#34;,
                    &#34;user_name&#34;)):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;Not all db2_options are provided&#34;)

        if not db2_options.get(&#34;instance_name&#34;):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;)

        storage_policy = db2_options.get(&#39;storage_policy&#39;,db2_options.get(&#39;data_storage_policy&#39;))

        if not self._commcell_object.storage_policies.has_policy(storage_policy):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    db2_options[&#34;data_storage_policy&#34;])
            )

        if self._commcell_object.credentials.has_credential(db2_options[&#34;credential_name&#34;]):
            credential = self._commcell_object.credentials.get(db2_options[&#34;credential_name&#34;])
        else:
            credential = self._commcell_object.credentials.add_db2_database_creds(db2_options[&#34;credential_name&#34;],
                                                                              db2_options[&#34;user_name&#34;],
                                                                              db2_options[&#34;password&#34;])

        # encodes the plain text password using base64 encoding

        #enable_auto_discovery = db2_options[&#34;enable_auto_discovery&#34;]

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;instanceName&#34;: db2_options[&#34;instance_name&#34;],
                    &#34;commcellId&#34;: self._commcell_object.commcell_id,
                    &#34;instanceId&#34;: -1,
                    &#34;applicationId&#34;: int(self._agent_object.agent_id)
                },
                &#34;credentialEntity&#34;: {
                    &#34;credentialId&#34;: credential.credential_id,
                    &#34;credentialName&#34;: credential.credential_name,
                    &#34;description&#34;: credential.credential_description,
                    &#34;recordType&#34;: credential._record_type,
                    &#34;selected&#34;: True
                },
                &#34;planEntity&#34;: {
                    &#34;planId&#34;: int(self._commcell_object.plans.get(storage_policy).plan_id)
                },
                &#34;db2Instance&#34;: {
                    &#34;homeDirectory&#34;: db2_options[&#34;home_directory&#34;],
                    &#34;DB2StorageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyId&#34;: 1
                        },
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyId&#34;: 1
                        },
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyId&#34;: 1
                        }
                    }
                }
            }
        }
        add_instance = self._commcell_object._services[&#39;ADD_INSTANCE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, add_instance, request_json)
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;].get(&#39;errorCode&#39;)

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;].get(&#39;errorString&#39;)
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(error_string))
                else:
                    if &#39;entity&#39; in response.json()[&#39;response&#39;]:
                        self.refresh()
                        return self.get(response.json()[&#39;response&#39;][&#39;entity&#39;].get(&#39;instanceName&#39;))
                    else:
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Unable to get instance name and id&#39;
                                           )
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add_big_data_apps_instance(self, distributed_options):
        &#34;&#34;&#34;
            Method to add big data apps instance to the given client.

            distributed_options {
                &#34;instanceName&#34;: &#34;ClusterInstance&#34;
                &#34;MasterNode&#34; : $MASTER_NODE$ (Optional based on cluster Type. If not present set it to &#34;&#34;)
                &#34;dataAccessNodes&#34;: [
                    {
                        &#34;clientName&#34;: &#34;DataClient1&#34;
                    }
                ]
            }

            Raises:
                SDKException:
                    if None value in Distributed options
                    if Big Data Apps instance with same name already exists
                    if cannot retrieve cluster type from default Instance
        &#34;&#34;&#34;
        if None in distributed_options.values():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;One of the distributed parameter is None so cannot proceed with instance creation&#34;)

        if self.has_instance(distributed_options[&#34;instanceName&#34;]):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    distributed_options[&#34;instanceName&#34;])
            )

        &#34;&#34;&#34;
            Get Cluster Type from Default Instance to assign it to the New Instance.
            Atleast one instance should be present in the client.
        &#34;&#34;&#34;
        cluster_properties = {}
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._INSTANCES)
        if flag:
            if response.json() and &#34;instanceProperties&#34; in response.json():
                cluster_properties = response.json()[&#34;instanceProperties&#34;][0]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

        cluster_type = cluster_properties.get(&#39;distributedClusterInstance&#39;, {}).get(&#39;clusterType&#39;)
        cluster_config = {}
        uxfs_config = cluster_properties.get(
            &#39;distributedClusterInstance&#39;, {}).get(&#39;clusterConfig&#39;, {}).get(&#39;uxfsConfig&#39;)
        hadoop_config = cluster_properties.get(
            &#39;distributedClusterInstance&#39;, {}).get(&#39;clusterConfig&#39;, {}).get(&#39;hadoopConfig&#39;)
        if uxfs_config is not None:
            uxfs_config[&#39;coordinatorNode&#39;] = {&#34;clientName&#34;: distributed_options.get(&#39;MasterNode&#39;, &#39;&#39;)}
            cluster_config[&#39;uxfsConfig&#39;] = uxfs_config
        if hadoop_config is not None:
            hadoop_config[&#39;coordinatorNode&#39;] = {&#34;clientName&#34;: distributed_options.get(&#39;MasterNode&#39;, &#39;&#39;)}
            hbase_config = hadoop_config.get(&#39;hadoopApps&#39;, {}).get(&#39;appConfigs&#39;, [{}])[0].get(&#39;hBaseConfig&#39;)
            if hbase_config is not None:
                hadoop_config[&#34;hadoopApps&#34;][&#34;appConfigs&#34;][0][&#39;hBaseConfig&#39;][&#34;hbaseClientNode&#34;] = {
                    &#34;clientName&#34;: distributed_options.get(&#39;MasterNode&#39;, &#39;&#39;)}
            cluster_config[&#39;hadoopConfig&#39;] = hadoop_config

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: distributed_options[&#34;instanceName&#34;],
                },
                &#34;distributedClusterInstance&#34;: {
                    &#34;clusterType&#34;: cluster_type,
                    &#34;instance&#34;: {
                        &#34;instanceName&#34;: distributed_options[&#34;instanceName&#34;]
                    },
                    &#34;clusterConfig&#34;: cluster_config,
                    &#34;dataAccessNodes&#34;: {
                        &#34;dataAccessNodes&#34;: distributed_options[&#34;dataAccessNodes&#34;]
                    }
                }
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ADD_INSTANCE&#39;], request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    instance_name = response.json(
                    )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                    self.refresh()
                    return self.get(instance_name)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_cloud_storage_instance(self, cloud_options):
        &#34;&#34;&#34;Returns the JSON request to pass to the API for adding a cloud storage instance

        Args:
            cloud_options    (dict)    --    Options needed for adding a new cloud storage instance.

        Example:
        Cloud : S3
        cloud_options = {
                            &#39;instance_name&#39;: &#39;S3&#39;,
                            &#39;description&#39;: &#39;instance for s3&#39;,
                            &#39;storage_policy&#39;:&#39;cs_sp&#39;,
                            &#39;number_of_streams&#39;: 2,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;accesskey&#39;:&#39;xxxxxxxx&#39;,
                            &#39;secretkey&#39;:&#39;yyyyyyyy&#39;,
                            &#39;cloudapps_type&#39;: &#39;s3&#39;

            }
        Cloud : Google Cloud
        cloud_options = {
                            &#39;instance_name&#39;: &#39;google_test&#39;,
                            &#39;description&#39;: &#39;instance for google&#39;,
                            &#39;storage_plan&#39;:&#39;cs_sp&#39;,
                            &#39;number_of_streams&#39;: 2,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;cloudapps_type&#39;: &#39;google_cloud&#39;
                            &#39;host_url&#39;:&#39;storage.googleapis.com&#39;,
                            &#39;access_key&#39;:&#39;xxxxxx&#39;,
                            &#39;secret_key&#39;:&#39;yyyyyy&#39;
                        }
        Cloud : Azure Datalake Gen2
        cloud_options = {

                            &#39;instance_name&#39;: &#39;TestAzureDL&#39;,
                            &#39;storage_plan&#39;:&#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;description&#39;: None,
                            &#39;accountname&#39;: &#39;xxxxxx&#39;,
                            &#39;accesskey&#39;: &#39;xxxxxx&#39;,
                            &#39;number_of_streams&#39;: 1,
                            &#39;cloudapps_type&#39;: &#39;azureDL&#39;
                        }
        Cloud : Amazon RDS
        cloud_options = {
                            &#39;instance_name&#39;: &#39;RDS&#39;,
                            &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;access_key&#39;: &#39;xxxxx&#39;,
                            &#39;secret_key&#39;: &#39;xxxxx&#39;,
                            &#39;cloudapps_type&#39;: &#39;amazon_rds&#39;
                        }
        Cloud : Amazon Redshift
        cloud_options = {

                            &#39;instance_name&#39;: &#39;Redshift&#39;,
                            &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;access_key&#39;: &#39;xxxxx&#39;,
                            &#39;secret_key&#39;: &#39;xxxxx&#39;,
                            &#39;cloudapps_type&#39;: &#39;amazon_redshift&#39;
                        }
        Cloud : Amazon Document DB
        cloud_options = {
                            &#39;instance_name&#39;: &#39;DocumentDB&#39;,
                            &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;access_key&#39;: &#39;xxxxxx&#39;,
                            &#39;secret_key&#39;: &#39;xxxxxx&#39;,
                            &#39;cloudapps_type&#39;: &#39;amazon_docdb&#39;
                        }
        Returns:
            dict     --   JSON request to pass to the API
        Raises :
            SDKException :

                if cloud storage instance with same name already exists

                if given storage policy does not exist in commcell

        Cloud : Amazon DynamoDB
        cloud_options = {
                            &#39;instance_name&#39;: &#39;DynamoDB&#39;,
                            &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;access_key&#39;: &#39;xxxxxx&#39;,
                            &#39;secret_key&#39;: &#39;xxxxxx&#39;,
                            &#39;cloudapps_type&#39;: &#39;amazon_dynamodb&#39;
                        }
        Returns:
            dict     --   JSON request to pass to the API
        Raises :
            SDKException :

                if cloud storage instance with same name already exists

                if given storage policy does not exist in commcell

        &#34;&#34;&#34;
        if cloud_options.get(&#34;instance_name&#34;):
            if self.has_instance(cloud_options.get(&#34;instance_name&#34;)):
                raise SDKException(
                    &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                        cloud_options.get(&#34;instance_name&#34;))
                )
        else:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Empty instance name provided&#39;)

        # setting the storage_policy for the general_properties setter method
        if cloud_options.get(&#39;storage_plan&#39;) and not cloud_options.get(&#39;storage_policy&#39;):
            cloud_options[&#39;storage_policy&#39;] = cloud_options.get(&#39;storage_plan&#39;)

        # setting storage_plan if not passed and storage_policy is passed instead
        if cloud_options.get(&#39;storage_policy&#39;) and not cloud_options.get(&#39;storage_plan&#39;):
            cloud_options[&#39;storage_plan&#39;] = cloud_options.get(&#39;storage_policy&#39;)

        if cloud_options.get(&#39;description&#39;):
            description = cloud_options.get(&#39;description&#39;)
        else:
            description = &#39;&#39;

        self._instance_properties_json = cloud_options
        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;description&#34;: description,
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;instanceName&#34;: cloud_options.get(&#34;instance_name&#34;),
                    &#34;appName&#34;: self._agent_object.agent_name,
                },
                &#34;cloudAppsInstance&#34;: self._instance_properties_json
            }
        }

        if cloud_options.get(&#34;storage_plan&#34;):
            if not self._commcell_object.storage_policies.has_policy(
                    cloud_options.get(&#34;storage_plan&#34;)):
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;Storage plan: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                        cloud_options.get(&#34;storage_plan&#34;))
                )
            request_json[&#34;instanceProperties&#34;][&#34;planEntity&#34;] = {&#34;planName&#34;: cloud_options.get(&#34;storage_plan&#34;)}
        else:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Empty storage plan provided&#39;)

        add_instance = self._commcell_object._services[&#39;ADD_INSTANCE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, add_instance, request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    instance_name = response.json()[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                    instance_id = response.json()[&#39;response&#39;][&#39;entity&#39;][&#39;instanceId&#39;]
                    agent_name = self._agent_object.agent_name
                    self.refresh()
                    return self.get(instance_name)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add_salesforce_instance(
            self, instance_name, access_node,
            salesforce_options,
            db_options=None, **kwargs):
        &#34;&#34;&#34;Adds a new salesforce instance.

            Args:
                instance_name               (str)   -- instance_name
                access_node                 (str)   -- access node name
                salesforce_options          (dict)  -- salesforce options
                                                        {
                                                                &#34;login_url&#34;: &#39;salesforce login url&#39;,
                                                                &#34;consume_id&#34;: &#39;salesforce consumer key&#39;,
                                                                &#34;consumer_secret&#34;: &#39;salesforce consumer secret&#39;,
                                                                &#34;salesforce_user_name&#34;: &#39;salesforce login user&#39;,
                                                                &#34;salesforce_user_password&#34;: &#39;salesforce user password&#39;,
                                                                &#34;salesforce_user_token&#34;: &#39;salesforce user token&#39;
                                                        }

                db_options                  (dict)  -- database options to configure sync db
                                                        {
                                                            &#34;db_enabled&#34;: &#39;True or False&#39;,
                                                            &#34;db_type&#34;: &#39;SQLSERVER or POSTGRESQL&#39;,
                                                            &#34;db_host_name&#34;: &#39;database hostname&#39;,
                                                            &#34;db_instance&#34;: &#39;database instance name&#39;,
                                                            &#34;db_name&#34;: &#39;database name&#39;,
                                                            &#34;db_port&#34;: &#39;port of the database&#39;,
                                                            &#34;db_user_name&#34;: &#39;database user name&#39;,
                                                            &#34;db_user_password&#34;: &#39;database user password&#39;
                                                        }

                **kwargs                    (dict)   -- dict of keyword arguments as follows

                                                         download_cache_path     (str)   -- download cache path
                                                         mutual_auth_path        (str)   -- mutual auth cert path
                                                         storage_policy          (str)   -- storage policy
                                                         streams                 (int)   -- number of streams
            Returns:
                object  -   instance of the instance class for this new instance

            Raises:
                SDKException:
                    if instance with given name already exists

                    if failed to add the instance

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if db_options is None:
            db_options = {&#39;db_enabled&#39;: False}
        if self.has_instance(instance_name):
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;,
                               &#39;Instance &#34;{0}&#34; already exists.&#39;.format(instance_name))

        salesforce_password = b64encode(salesforce_options.get(&#39;salesforce_user_password&#39;).encode()).decode()
        salesforce_consumer_secret = b64encode(
            salesforce_options.get(&#39;consumer_secret&#39;, &#39;3951207263309722430&#39;).encode()).decode()
        salesforce_token = b64encode(salesforce_options.get(&#39;salesforce_user_token&#39;, &#39;&#39;).encode()).decode()
        db_user_password = &#34;&#34;
        if db_options.get(&#39;db_enabled&#39;, False):
            db_user_password = b64encode(db_options.get(&#39;db_user_password&#39;).encode()).decode()

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: instance_name,
                    &#34;appName&#34;: self._agent_object.agent_name
                },
                &#34;cloudAppsInstance&#34;: {
                    &#34;instanceType&#34;: 3,
                    &#34;salesforceInstance&#34;: {
                        &#34;enableREST&#34;: True,
                        &#34;endpoint&#34;: salesforce_options.get(&#39;login_url&#39;, &#34;https://login.salesforce.com&#34;),
                        &#34;consumerId&#34;: salesforce_options.get(&#39;consumer_id&#39;,
                                                             &#39;3MVG9Nc1qcZ7BbZ0Ep18pfQsltTkZtbcMG9GMQzsVHGS8268yaOqmZ1lEEakAs8Xley85RBH1xKR1.eoUu1Z4&#39;),
                        &#34;consumerSecret&#34;: salesforce_consumer_secret,
                        &#34;defaultBackupsetProp&#34;: {
                            &#34;downloadCachePath&#34;: kwargs.get(&#39;download_cache_path&#39;, &#39;/tmp&#39;),
                            &#34;mutualAuthPath&#34;: kwargs.get(&#39;mutual_auth_path&#39;, &#39;&#39;),
                            &#34;token&#34;: salesforce_token,
                            &#34;userPassword&#34;: {
                                &#34;userName&#34;: salesforce_options.get(&#39;salesforce_user_name&#39;),
                                &#34;password&#34;: salesforce_password,
                            },
                            &#34;syncDatabase&#34;: {
                                &#34;dbEnabled&#34;: db_options.get(&#39;db_enabled&#39;, False),
                                &#34;dbPort&#34;: db_options.get(&#39;db_port&#39;, &#34;1433&#34;),
                                &#34;dbInstance&#34;: db_options.get(&#39;db_instance&#39;, &#39;&#39;),
                                &#34;dbName&#34;: db_options.get(&#39;db_name&#39;, instance_name),
                                &#34;dbType&#34;: db_options.get(&#39;db_type&#39;, &#39;SQLSERVER&#39;),
                                &#34;dbHost&#34;: db_options.get(&#39;db_host_name&#39;, &#39;&#39;),
                                &#34;dbUserPassword&#34;: {
                                    &#34;userName&#34;: db_options.get(&#39;db_user_name&#39;, &#39;&#39;),
                                    &#34;password&#34;: db_user_password,

                                },
                            },
                        },
                    },
                    &#34;generalCloudProperties&#34;: {
                        &#34;numberOfBackupStreams&#34;: kwargs.get(&#39;streams&#39;, 2),
                        &#34;proxyServers&#34;: [
                            {
                                &#34;clientName&#34;: access_node
                            }
                        ],
                        &#34;storageDevice&#34;: {
                            &#34;dataBackupStoragePolicy&#34;: {
                                &#34;storagePolicyName&#34;: kwargs.get(&#39;storage_policy&#39;, &#39;&#39;)
                            },
                        },
                    },
                },
            },
        }
        self._process_add_response(request_json)

    def add_postgresql_instance(self, instance_name, **kwargs):
        &#34;&#34;&#34;Adds new postgresql instance to given client
            Args:
                instance_name       (str)   --  instance_name
                kwargs              (dict)  --  dict of keyword arguments as follows:
                                                   storage_policy       (str)          -- storage policy
                                                   port                 (int or str)   -- port or end point
                                                   postgres_user_name   (str)          -- postgres user name
                                                   postgres_password    (str)          -- postgres password
                                                   version              (str)          -- postgres version
                                                   maintenance_db       (str)          -- maintenance db
                                                   binary_directory     (str)          -- postgres binary location
                                                   lib_directory        (str)          -- postgres lib location
                                                   archive_log_directory (str)         -- postgres archive log location
                                                   credential_name      (str)          -- PostgreSQL crdential name
            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if None value in postgres options

                    if postgres instance with same name already exists

                    if given storage policy does not exist in commcell
        &#34;&#34;&#34;

        if self.has_instance(instance_name):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    instance_name)
            )
        if not self._commcell_object.credentials.has_credential(kwargs.get(&#34;credential_name&#34;)):
            self._commcell_object.credentials.add_postgres_database_creds(
                kwargs.get(&#34;credential_name&#34;), kwargs.get(&#34;user_name&#34;), kwargs.get(&#34;password&#34;))
        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: instance_name,
                    &#34;appName&#34;: &#34;PostgreSQL&#34;,
                },
                &#34;version&#34;: kwargs.get(&#34;version&#34;, &#34;10.0&#34;),
                &#34;postGreSQLInstance&#34;: {
                    &#34;LibDirectory&#34;: kwargs.get(&#34;lib_directory&#34;, &#34;&#34;),
                    &#34;MaintainenceDB&#34;: kwargs.get(&#34;maintenance_db&#34;, &#34;postgres&#34;),
                    &#34;port&#34;: kwargs.get(&#34;port&#34;, &#34;5432&#34;),
                    &#34;ArchiveLogDirectory&#34;: kwargs.get(&#34;archive_log_directory&#34;, &#34;&#34;),
                    &#34;BinaryDirectory&#34;: kwargs.get(&#34;binary_directory&#34;, &#34;&#34;),
                    &#34;SAUser&#34;: {},
                    &#34;logStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: kwargs.get(&#34;storage_policy&#34;, &#34;&#34;)
                    },
                    &#34;credentialEntity&#34;: {
                        &#34;credentialName&#34;: kwargs.get(&#34;credential_name&#34;, &#34;&#34;)
                    }

                }
            }
        }
        self._process_add_response(request_json)


    @property
    def _general_properties_json(self):
        &#34;&#34;&#34;Returns the general properties json.&#34;&#34;&#34;
        return self._general_properties

    @_general_properties_json.setter
    def _general_properties_json(self, value):
        &#34;&#34;&#34;setter for general cloud properties in instance JSON.

        Args:

            value    (dict)    --    options needed to set general cloud properties

        Example:

            value = {
                &#34;number_of_streams&#34;:1,
                &#34;access_node&#34;:&#34;test&#34;,
                &#34;storage_policy&#34;:&#34;policy1&#34;,
                &#34;access_key&#34;: &#34;xxxxxx&#34;,
                &#34;secret_key&#34;: &#34;xxxxxx&#34;
            }

        &#34;&#34;&#34;

        supported_cloudapps_type = [&#34;amazon_rds&#34;, &#34;amazon_redshift&#34;,
                                    &#34;amazon_docdb&#34;, &#34;amazon_dynamodb&#34;]
        if value.get(&#34;cloudapps_type&#34;) in supported_cloudapps_type:
            self._general_properties = {
                &#34;accessNodes&#34;: {
                    &#34;memberServers&#34;: [
                        {
                            &#34;client&#34;: {
                                &#34;clientName&#34;: value.get(&#34;access_node&#34;)
                            }
                        }
                    ]
                },
                &#34;amazonInstanceInfo&#34;: {
                    &#34;secretKey&#34;: value.get(&#34;secret_key&#34;),
                    &#34;accessKey&#34;: value.get(&#34;access_key&#34;)
                }
            }

        else:
            self._general_properties = {
                &#34;numberOfBackupStreams&#34;: value.get(&#34;number_of_streams&#34;),
                &#34;proxyServers&#34;: [
                    {
                        &#34;clientName&#34;: value.get(&#34;access_node&#34;)
                    }
                ],
                &#34;storageDevice&#34;: {
                    &#34;dataBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: value.get(&#34;storage_policy&#34;)
                    }
                }
            }

    @property
    def _instance_properties_json(self):
        &#34;&#34;&#34;Returns the instance properties json.&#34;&#34;&#34;
        return self._instance_properties

    @_instance_properties_json.setter
    def _instance_properties_json(self, value):
        &#34;&#34;&#34;setter for cloud storage instance properties in instance JSON.

        Args:

            value    (dict)    --    options needed to set cloud storage instance properties

        Example:
            value = {
                &#34;accesskey&#34; : &#34;xxxxxxxxx&#34;
                &#34;secretkey&#34; : &#34;yyyyyyyy&#34;
            }

        &#34;&#34;&#34;

        supported_cloudapps_type = {&#34;amazon_rds&#34;: 4, &#34;amazon_redshift&#34;: 26,
                                    &#34;amazon_docdb&#34;: 27, &#34;amazon_dynamodb&#34;: 22}
        self._general_properties_json = value
        if value.get(&#34;cloudapps_type&#34;) == &#39;s3&#39;:
            self._instance_properties = {
                &#34;instanceType&#34;: 5,
                &#34;s3Instance&#34;: {
                    &#34;accessKeyId&#34;: value.get(&#34;accesskey&#34;),
                    &#34;secretAccessKey&#34;: value.get(&#34;secretkey&#34;),
                    &#34;hostURL&#34;: &#34;s3.amazonaws.com&#34;
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
        elif value.get(&#34;cloudapps_type&#34;) == &#39;azure&#39;:
            self._instance_properties = {
                &#34;instanceType&#34;: 6,
                &#34;azureInstance&#34;: {
                    &#34;accountName&#34;: value.get(&#34;accountname&#34;),
                    &#34;accessKey&#34;: value.get(&#34;accesskey&#34;),
                    &#34;hostURL&#34;: &#34;blob.core.windows.net&#34;
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
        elif value.get(&#34;cloudapps_type&#34;) == &#39;oraclecloud&#39;:
            password = b64encode(value.get(&#34;password&#34;).encode()).decode()
            self._instance_properties = {
                &#34;instanceType&#34;: 14,
                &#34;oraCloudInstance&#34;: {
                    &#34;endpointURL&#34;: value.get(&#34;endpointurl&#34;),
                    &#34;user&#34;: {
                        &#34;password&#34;: password,
                        &#34;userName&#34;: value.get(&#34;username&#34;)
                    }
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
        elif value.get(&#34;cloudapps_type&#34;) == &#39;openstack&#39;:
            apikey = b64encode(value.get(&#34;apikey&#34;).encode()).decode()
            self._instance_properties = {
                &#34;instanceType&#34;: 15,
                &#34;openStackInstance&#34;: {
                    &#34;serverName&#34;: value.get(&#34;servername&#34;),
                    &#34;credentials&#34;: {
                        &#34;password&#34;: apikey,
                        &#34;userName&#34;: value.get(&#34;username&#34;)
                    }
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }

        elif value.get(&#34;cloudapps_type&#34;) == &#39;google_cloud&#39;:
            secret_key = b64encode(value.get(&#34;secret_key&#34;).encode()).decode()
            self._instance_properties = {
                &#34;instanceType&#34;: 20,
                &#34;googleCloudInstance&#34;: {
                    &#34;serverName&#34;: value.get(&#34;host_url&#34;),
                    &#34;credentials&#34;: {
                        &#34;password&#34;: secret_key,
                        &#34;userName&#34;: value.get(&#34;access_key&#34;)
                    }
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }

        elif value.get(&#34;cloudapps_type&#34;) == &#39;azureDL&#39;:
            accesskey = b64encode(value.get(&#34;accesskey&#34;).encode()).decode()
            self._instance_properties = {
                &#34;instanceType&#34;: 21,
                &#34;azureDataLakeInstance&#34;: {
                    &#34;serverName&#34;: &#34;dfs.core.windows.net&#34;,
                    &#34;credentials&#34;: {
                        &#34;userName&#34;: value.get(&#34;accountname&#34;),
                        &#34;password&#34;: accesskey
                    }
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
        elif value.get(&#34;cloudapps_type&#34;) in supported_cloudapps_type:
            self._instance_properties = {
                &#34;instanceType&#34;: supported_cloudapps_type[value.get(&#34;cloudapps_type&#34;)],
                &#34;rdsInstance&#34;: {
                    &#34;secretKey&#34;: value.get(&#34;secret_key&#34;),
                    &#34;accessKey&#34;: value.get(&#34;access_key&#34;),
                    &#34;regionEndPoints&#34;: &#34;default&#34;
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
    
    def add_mysql_instance(self, instance_name, database_options):
        &#34;&#34;&#34;Adds new mysql Instance to given Client
            Args:
                                instance_name       (str)   --  instance_name
              mysql_options       (dict)  --  dict of keyword arguments as follows:
                    Example:
                       database_options = {
                            &#39;enable_auto_discovery&#39;: True,
                            &#39;storage_policy&#39;: &#39;sai-sp&#39;,
                            &#39;port&#39;: &#39;hotsname:port&#39;,
                            &#39;mysql_user_name&#39;: &#39;mysqlusername&#39;
                            &#39;mysql_password&#39;: &#39;######&#39;,
                            &#39;version&#39;: &#39;5.7&#39;,
                            &#39;binary_directory&#39;: &#34;&#34;,
                            &#39;config_file&#39;: &#34;&#34;,
                            &#39;log_data_directory&#39;: &#34;&#34;,
                            &#39;data_directory&#39;: &#34;&#34;,
                            &#39;description&#39;: &#34;Automation created instance&#34;
                        }

            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if None value in mysql options

                    if mysql instance with same name already exists

                    if given storage policy does not exists in commcell
        &#34;&#34;&#34;
        if None in database_options.values():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;One of the mysql parameter is None so cannot proceed with instance creation&#34;)

        if self.has_instance(instance_name):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    instance_name)
            )

        if not self._commcell_object.storage_policies.has_policy(
                database_options[&#34;storage_policy&#34;]):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    database_options[&#34;storage_policy&#34;])
            )
        if not self._commcell_object.credentials.has_credential(database_options.get(&#34;credential_name&#34;)):
            self._commcell_object.credentials.add_mysql_database_creds(
                database_options.get(&#34;credential_name&#34;),
                database_options.get(&#34;mysql_user_name&#34;),
                database_options.get(&#34;mysql_password&#34;))

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;description&#34;: &#34;Automation created instance&#34;,
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;instanceName&#34;: instance_name,
                    &#34;appName&#34;: &#34;MySQL&#34;,
                    &#34;applicationId&#34;: 104,
                    &#34;_type_&#34;: 0
                },
                &#34;credentialEntity&#34;: {
                    &#34;credentialName&#34;: database_options.get(&#34;credential_name&#34;, &#34;&#34;)
                },
                &#34;mySqlInstance&#34;: {
                    &#34;BinaryDirectory&#34;: database_options.get(&#34;binary_directory&#34;, &#34;&#34;),
                    &#34;ConfigFile&#34;: database_options.get(&#34;config_file&#34;, &#34;&#34;),
                    &#34;EnableAutoDiscovery&#34;: database_options.get(&#34;enable_auto_discovery&#34;, True),
                    &#34;LogDataDirectory&#34;: database_options.get(&#34;log_data_directory&#34;, &#34;&#34;),
                    &#34;dataDirectory&#34;: database_options.get(&#34;data_directory&#34;, &#34;&#34;),
                    &#34;port&#34;: database_options.get(&#34;port&#34;, &#34;3306&#34;),
                    &#34;version&#34;: database_options.get(&#34;version&#34;, &#34;5.7&#34;),
                    &#34;sslCAFile&#34;: database_options.get(&#34;sslca_file_path&#34;, &#34;&#34;),
                    &#34;SAUser&#34;: {
                    },
                    &#34;proxySettings&#34;: {
                        &#34;isProxyEnabled&#34;: False,
                        &#34;isUseSSL&#34;: False,
                        &#34;runBackupOnProxy&#34;: False
                    },
                    &#34;mysqlStorageDevice&#34;: {
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: database_options.get(&#34;storage_policy&#34;, &#34;&#34;)
                        },
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: database_options.get(&#34;storage_policy&#34;, &#34;&#34;)
                        }
                    }
                }
            }
        }
        self._process_add_response(request_json)

    def add_oracle_instance(self, instance_name, **oracle_options):
        &#34;&#34;&#34;Adds new oracle instance for the given client
            Args:
                instance_name       (str)   --  instance_name (Oracle SID)
                oracle_options      (dict)  --  dict of keyword arguments as follows:
                                        log_storage_policy    (str)  -- log storage policy
                                        cmdline_storage_policy (str) -- Commandline data storage policy
                                        oracle_domain_name (str)   -- Domain name- only for windows
                                        oracle_user_name   (str)   -- oracle OS user name
                                        oracle_password    (str)   -- oracle OS user password
                                        oracle_home        (str)   -- oracle home path
                                        tns_admin          (str)   -- tns admin path
                                        connect_string     (dict)  -- Credentials to connect to Oracle DB
                                        {
                                            &#34;username&#34;: &#34;&#34;, (str)         -- User to connect to Oracle DB
                                            &#34;password&#34;: &#34;&#34;, (str)         -- Password
                                            &#34;service_name&#34;: &#34;&#34;  (str)     -- Oracle SID or service name
                                        }
                                        catalog_connect     (dict)--  Credentials to connect to catalog
                                        {
                                            &#34;userName&#34;: &#34;&#34;,  (str)        -- Catalog DB user name
                                            &#34;password&#34;; &#34;&#34;,  (str)        -- Password of catalog user
                                            &#34;domainName&#34;: &#34;&#34;    (str)     -- SID of catalog database
                                        }
            Returns:
                object - instance of the Instance class
            Raises:
                SDKException:
                            if instance with same name exists already
                            if required options are not provided
                            Given storage policies do not exist in Commcell
        &#34;&#34;&#34;

        if self.has_instance(instance_name):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    instance_name)
            )
        required_options = [&#39;oracle_user_name&#39;, &#39;oracle_home&#39;, &#39;cmdline_storage_policy&#39;,
                            &#39;log_storage_policy&#39;, &#39;connect_string&#39;]
        for option in required_options:
            if option not in oracle_options.keys():
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#34;Required option: {0} is missing, Please provide all parameters:&#34;.format(option))
        password = b64encode(oracle_options.get(&#34;oracle_password&#34;, &#34;&#34;).encode()).decode()
        connect_string_password = b64encode(
            oracle_options.get(&#34;connect_string&#34;, {}).get(&#34;password&#34;, &#34;&#34;).encode()
        ).decode()

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: instance_name,
                    &#34;appName&#34;: &#34;Oracle&#34;,
                },
                &#34;oracleInstance&#34;: {
                    &#34;TNSAdminPath&#34;: oracle_options.get(&#34;tns_admin&#34;, &#34;&#34;),
                    &#34;oracleHome&#34;: oracle_options.get(&#34;oracle_home&#34;, &#34;&#34;),
                    &#34;oracleUser&#34;: {
                        &#34;userName&#34;: oracle_options.get(&#34;oracle_user_name&#34;, &#34;&#34;),
                        &#34;domainName&#34;: oracle_options.get(&#34;oracle_domain_name&#34;, &#34;&#34;),
                        &#34;password&#34;: password,
                    },
                    &#34;sqlConnect&#34;: {
                        &#34;domainName&#34;: oracle_options.get(&#34;connect_string&#34;, {}).get(&#34;service_name&#34;, &#34;&#34;),
                        &#34;userName&#34;: oracle_options.get(&#34;connect_string&#34;, {}).get(&#34;username&#34;, &#34;/&#34;),
                        &#34;password&#34;: connect_string_password,
                    },
                    &#34;oracleStorageDevice&#34;: {
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: oracle_options.get(&#34;cmdline_storage_policy&#34;, &#34;&#34;)
                        },
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: oracle_options.get(&#34;log_storage_policy&#34;, &#34;&#34;)
                        }
                    },
                }
            }
        }
        if oracle_options.get(&#34;catalog_connect&#34;):
            catalog = {
                &#39;useCatalogConnect&#39;: True,
                &#39;catalogConnect&#39;: oracle_options.get(&#34;catalog_connect&#34;, &#34;&#34;)
            }
            request_json[&#39;instanceProperties&#39;][&#39;oracleInstance&#39;].update(catalog)
        self._process_add_response(request_json)

    def add_cosmosdb_instance(self, instance_name, **instance_options):
        &#34;&#34;&#34;Adds new cosmosdb cassandra api Instance to given Client
            Args:
                                instance_name       (str)   --  instance_name
                instance_options       (dict)  --  dict of keyword arguments as follows:
                    Example:
                       instance_options = {
                            &#39;plan_name&#39;: &#39;server plan&#39;,
                            &#39;cloudaccount_name&#39;: &#39;hotsname:port&#39;,
                            &#39;cloudinstancetype&#39;: &#39;instancetype&#39;,
                        }

            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if instance with same name already exists
                    if given plan name does not exists in commcell
        &#34;&#34;&#34;

        if self.has_instance(instance_name):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    instance_name)
            )

        if not self._commcell_object.plans.has_plan(
                instance_options.get(&#34;plan_name&#34;)):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    instance_options.get(&#34;plan_name&#34;))
            )

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;cloudAppsInstance&#34;: {
                    &#34;instanceType&#34;: instance_options.get(&#34;cloudinstancetype&#34;,&#34;&#34;),
                    &#34;rdsInstance&#34;: {
                    }
                },
                &#34;instance&#34;: {
                    &#34;applicationId&#34;: 134,
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;clientName&#34;: instance_options.get(&#34;cloudaccount_name&#34;,&#34;&#34;),
                    &#34;instanceName&#34;: instance_name
                },
                &#34;planEntity&#34;: {
                    &#34;planName&#34;: instance_options.get(&#34;plan_name&#34;)
                }
            }
        }

        self._process_add_response(request_json)

    def refresh(self):
        &#34;&#34;&#34;Refresh the instances associated with the Agent of the selected Client.&#34;&#34;&#34;
        self._instances = self._get_instances()


class Instance(object):
    &#34;&#34;&#34;Class for performing instance operations for a specific instance.&#34;&#34;&#34;

    def __new__(cls, agent_object, instance_name, instance_id=None):
        from .instances.vsinstance import VirtualServerInstance
        from .instances.cainstance import CloudAppsInstance
        from .instances.bigdataappsinstance import BigDataAppsInstance
        from .instances.sqlinstance import SQLServerInstance
        from .instances.hanainstance import SAPHANAInstance
        from .instances.oracleinstance import OracleInstance
        from .instances.sybaseinstance import SybaseInstance
        from .instances.saporacleinstance import SAPOracleInstance
        from .instances.mysqlinstance import MYSQLInstance
        from .instances.lotusnotes.lndbinstance import LNDBInstance
        from .instances.lotusnotes.lndocinstance import LNDOCInstance
        from .instances.lotusnotes.lndminstance import LNDMInstance
        from .instances.postgresinstance import PostgreSQLInstance
        from .instances.informixinstance import InformixInstance
        from .instances.vminstance import VMInstance
        from .instances.db2instance import DB2Instance
        from .instances.aadinstance import AzureAdInstance
        from .instances.sharepointinstance import SharepointInstance

        # add the agent name to this dict, and its class as the value
        # the appropriate class object will be initialized based on the agent
        _instances_dict = {
            &#39;virtual server&#39;: [VirtualServerInstance, VMInstance],
            &#39;big data apps&#39;: BigDataAppsInstance,
            &#39;cloud apps&#39;: CloudAppsInstance,
            &#39;sql server&#39;: SQLServerInstance,
            &#39;sap hana&#39;: SAPHANAInstance,
            &#39;oracle&#39;: OracleInstance,
            &#39;oracle rac&#39;: OracleInstance,
            &#39;sybase&#39;: SybaseInstance,
            &#39;sap for oracle&#39;: SAPOracleInstance,
            &#39;mysql&#39;: MYSQLInstance,
            &#39;notes database&#39;: LNDBInstance,
            &#39;notes document&#39;: LNDOCInstance,
            &#39;domino mailbox archiver&#39;: LNDMInstance,
            &#39;postgresql&#39;: PostgreSQLInstance,
            &#39;informix&#39;: InformixInstance,
            &#39;db2&#39;: DB2Instance,
            &#39;azure ad&#39;: AzureAdInstance,
            &#39;sharepoint server&#39;: SharepointInstance
        }
        agent_name = agent_object.agent_name

        if agent_name in _instances_dict:
            if isinstance(_instances_dict[agent_name], list):
                if instance_name == &#34;vminstance&#34;:
                    _class = _instances_dict[agent_name][-1]
                else:
                    _class = _instances_dict[agent_name][0]
            else:
                _class = _instances_dict[agent_name]
            if _class.__new__ == cls.__new__:
                return object.__new__(_class)
            return _class.__new__(_class, agent_object, instance_name, instance_id)
        else:
            return object.__new__(cls)

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initialise the instance object.

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class
        &#34;&#34;&#34;
        self._agent_object = agent_object
        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_
        self._instance_name = instance_name.lower()

        if instance_id:
            # Use the instance id provided in the arguments
            self._instance_id = str(instance_id)
        else:
            # Get the id associated with this instance
            self._instance_id = self._get_instance_id()

        self._INSTANCE = self._services[&#39;INSTANCE&#39;] % (self._instance_id)
        self._ALLINSTANCES = self._services[&#39;GET_ALL_INSTANCES&#39;] % (
            self._agent_object._client_object.client_id
        )
        self._RESTORE = self._services[&#39;RESTORE&#39;]
        self._SEARCH_DURING_RESTORE = self._services[&#39;DO_WEB_SEARCH&#39;]
        self._properties = None
        self._restore_association = None

        # Restore json instance var
        self._commonopts_restore_json = {}

        self._backupsets = None
        self._subclients = None
        self.refresh()

    def _get_instance_id(self):
        &#34;&#34;&#34;Gets the instance id associated with this backupset.

             Returns:
                str - id associated with this instance
        &#34;&#34;&#34;
        instances = Instances(self._agent_object)
        return instances.get(self.instance_name).instance_id

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Instance class instance for Instance: &#34;{0}&#34; of Agent: &#34;{1}&#34;&#39;
        return representation_string.format(
            self._instance[&#34;instanceName&#34;], self._agent_object.agent_name
        )

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        # skip GET instance properties api call if instance id is 1
        if int(self.instance_id) == 1:
            self._properties = {
                &#39;instance&#39;: {
                    &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;instanceName&#34;: self.instance_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceId&#34;: int(self.instance_id),
                    &#34;applicationId&#34;: int(self._agent_object.agent_id)
                }
            }

            self._instance = self._properties[&#34;instance&#34;]
            # stop the execution here for instance id 1 (DefaultInstanceName)
            return

        instance_service = (
            &#34;{0}?association/entity/clientId={1}&amp;association/entity/applicationId={2}&#34;.format(
                self._INSTANCE, self._agent_object._client_object.client_id,
                self._agent_object.agent_id
            )
        )
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, instance_service)

        if flag:
            if response.json() and &#34;instanceProperties&#34; in response.json():
                self._properties = response.json()[&#34;instanceProperties&#34;][0]
                try:
                    self._instance = self._properties[&#34;instance&#34;]
                    self._instance_name = self._properties[&#34;instance&#34;][&#34;instanceName&#34;].lower()
                    self._instanceActivityControl = self._properties[&#34;instanceActivityControl&#34;]
                except KeyError:
                    instance_service = (
                        &#34;{0}&amp;applicationId={1}&#34;.format(self._ALLINSTANCES, self._agent_object.agent_id))
                    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, instance_service)
                    if flag:
                        if response.json() and &#34;instanceProperties&#34; in response.json():
                            self._properties = response.json()[&#34;instanceProperties&#34;][0]
                            self._instance = self._properties[&#34;instance&#34;]
                            self._instance_name = self._properties[&#34;instance&#34;][&#34;instanceName&#34;].lower()
                            self._instanceActivityControl = self._properties[&#34;instanceActivityControl&#34;]
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;get the all instance related properties.

           Returns:
                dict - all instance properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;: {
                &#34;isDeleted&#34;: False,
                &#34;instance&#34;: self._instance,
                &#34;instanceActivityControl&#34;: self._instanceActivityControl
            }
        }

        return instance_json

    def _set_instance_properties(self, attr_name, value):
        &#34;&#34;&#34;sets the properties of this sub client.value is updated to instance once when post call
            succeeds.

            Args:
                attr_name   (str)   --  old value of the property. this should be instance variable

                value       (str)   --  new value of the property. this should be instance variable

            Raises:
                SDKException:
                    if failed to update number properties for subclient

        &#34;&#34;&#34;
        try:
            backup = eval(&#39;self.%s&#39; % attr_name)        # Take backup of old value
        except (AttributeError, KeyError):
            backup = None

        exec(&#34;self.%s = %s&#34; % (attr_name, &#39;value&#39;))     # set new value

        # _get_instance_properties_json method must be added in all child classes
        # not to be added for classes, which does not support updating properties
        request_json = self._get_instance_properties_json()

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._INSTANCE, request_json)

        output = self._process_update_response(flag, response)
        if output[0]:
            return
        else:
            o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;

            # Restore original value from backup on failure
            exec(&#34;self.%s = %s&#34; % (attr_name, backup))
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))

    def _process_update_response(self, flag, response):
        &#34;&#34;&#34;Updates the subclient properties with the request provided.

            Args:
                update_request  (str)  --  update request specifying the details to update

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if failed to update properties

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;response&#34; in response.json():
                    error_code = str(response.json()[&#34;response&#34;][0][&#34;errorCode&#34;])

                    if error_code == &#34;0&#34;:
                        return (True, &#34;0&#34;, &#34;&#34;)
                    else:
                        error_message = &#34;&#34;

                        if &#34;errorString&#34; in response.json()[&#34;response&#34;][0]:
                            error_message = response.json()[&#34;response&#34;][0][&#34;errorString&#34;]

                        if error_message:
                            return (False, error_code, error_message)
                        else:
                            return (False, error_code, &#34;&#34;)
                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return (True, &#34;0&#34;, &#34;&#34;)

                    if error_message:
                        return (False, error_code, error_message)
                    else:
                        return (False, error_code, &#34;&#34;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _process_search_response(self, request_json):
        &#34;&#34;&#34;Runs the Search API with the request JSON provided for Find and Restore,
                    and returns the contents after parsing the response.
            Args:
                request_json    (dict)  --  JSON request to run for the API
            Returns:
                result          (list)  -- list of messages eligible for restore for as per request json
                    Raises:
                        SDKException:
                            if response is empty
                            if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SEARCH_DURING_RESTORE, request_json)
        self._restore_association = None
        result = []
        if flag:
            if response.json():
                res = response.json()
                result_item_result = res.get(&#34;searchResult&#34;, {}).get(&#34;resultItem&#34;, [])
                for item in result_item_result:
                    app_specific = item.get(&#34;appSpecific&#34;, {})
                    e_mail = app_specific.get(&#34;eMail&#34;, {})
                    link = e_mail.get(&#34;links&#34;)
                    result.append(link)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return result

    def _process_restore_response(self, request_json):
        &#34;&#34;&#34;Runs the CreateTask API with the request JSON provided for Restore,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if restore job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._RESTORE, request_json)

        self._restore_association = None

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _process_delete_response(self, request_json):
        &#34;&#34;&#34;Runs the DeleteDocuments API with the request JSON provided for Delete,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                object - instance of the Job class for this Delete job if it is a folder

            Raises:
                SDKException:
                    if Delete job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._DELETE, request_json)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;processingInfo&#34; in response.json():
                    pass
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Delete job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to run the Delete job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _filter_paths(self, paths, is_single_path=False):
        &#34;&#34;&#34;Filters the paths based on the Operating System, and Agent.

            Args:
                paths           (list)  --  list containing paths to be filtered

                is_single_path  (bool)  --  boolean specifying whether to return a single path
                                                or the entire list

            Returns:
                list    -   if the boolean is_single_path is set to False

                str     -   if the boolean is_single_path is set to True
        &#34;&#34;&#34;
        for index, path in enumerate(paths):
            if int(self._agent_object.agent_id) == AppIDAType.WINDOWS_FILE_SYSTEM:
                path = path.strip(&#39;\\&#39;).strip(&#39;/&#39;)
                if path:
                    path = path.replace(&#39;/&#39;, &#39;\\&#39;)
                else:
                    path = &#39;\\&#39;
            elif int(self._agent_object.agent_id) == AppIDAType.LINUX_FILE_SYSTEM:
                path = path.strip(&#39;\\&#39;).strip(&#39;/&#39;)
                if path:
                    path = path.replace(&#39;\\&#39;, &#39;/&#39;)
                else:
                    path = &#39;\\&#39;
                path = &#39;/&#39; + path

            paths[index] = path

        if is_single_path:
            return paths[0]
        else:
            return paths

    def _restore_json(
            self,
            **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (list)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;

        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        if self._restore_association is None:
            self._restore_association = self._instance
            
        if restore_option.get(&#39;deduce_bkset_subcl&#39;) == True:
            del self._restore_association[&#39;backupsetName&#39;]
            del self._restore_association[&#39;subclientName&#39;]
            del self._restore_association[&#39;backupsetId&#39;]
            del self._restore_association[&#39;subclientId&#39;]
            del self._restore_association[&#39;subclientGUID&#39;]
            

        if restore_option.get(&#39;copy_precedence&#39;) is None:
            restore_option[&#39;copy_precedence&#39;] = 0

        if restore_option.get(&#39;overwrite&#39;) is not None:
            restore_option[&#39;unconditional_overwrite&#39;] = restore_option[&#39;overwrite&#39;]

        if restore_option.get(&#39;live_browse&#39;):
            restore_option[&#39;liveBrowse&#39;] = True
        else:
            restore_option[&#39;liveBrowse&#39;] = False
        
        if restore_option.get(&#39;file_browse&#39;):
            restore_option[&#39;fileBrowse&#39;] = True
        else:
            restore_option[&#39;fileBrowse&#39;] = False

        # restore_option should use client key for destination client info
        client = restore_option.get(&#34;client&#34;, self._agent_object._client_object)

        if isinstance(client, str):
            client = self._commcell_object.clients.get(client)

        restore_option[&#34;client_name&#34;] = client.client_name
        restore_option[&#34;client_id&#34;] = int(client.client_id)

        # set time zone
        from_time = restore_option.get(&#34;from_time&#34;, None)
        to_time = restore_option.get(&#34;to_time&#34;, None)
        time_list = [&#39;01/01/1970 00:00:00&#39;, &#39;1/1/1970 00:00:00&#39;]

        if from_time and from_time not in time_list:
            restore_option[&#34;from_time&#34;] = from_time

        if to_time and to_time not in time_list:
            restore_option[&#34;to_time&#34;] = to_time

        # set versions
        if &#34;versions&#34; in restore_option:
            versions = restore_option[&#39;versions&#39;]
            if not isinstance(versions, list):
                raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
            if &#39;win&#39; in self._agent_object._client_object.os_info.lower() or self._agent_object._agent_name == &#34;virtual server&#34;:
                version_string = &#34;|\\|#15!vErSiOnS|#15!\\{0}&#34;
            else:
                version_string = &#34;|/|#15!vErSiOnS|#15!/{0}&#34;

            for version in versions:
                version = version_string.format(version)
                restore_option[&#34;paths&#34;].append(version)

        self._restore_browse_option_json(restore_option)
        self._restore_common_options_json(restore_option)
        self._impersonation_json(restore_option)
        self._restore_destination_json(restore_option)
        self._restore_fileoption_json(restore_option)
        self._restore_virtual_rst_option_json(restore_option)
        self._restore_volume_rst_option_json(restore_option)
        self._sync_restore_option_json(restore_option)
        self._restore_common_opts_json(restore_option)

        if not restore_option.get(&#39;index_free_restore&#39;, False):
            if restore_option.get(&#34;paths&#34;) == []:
                raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._restore_association],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [{
                    &#34;subTaskOperation&#34;: 1,
                    &#34;options&#34;: {
                        &#34;commonOpts&#34;: self._commonopts_restore_json,
                        &#34;restoreOptions&#34;: {
                            &#34;impersonation&#34;: self._impersonation_json_,
                            &#34;browseOption&#34;: self._browse_restore_json,
                            &#34;commonOptions&#34;: self._commonoption_restore_json,
                            &#34;destination&#34;: self._destination_restore_json,
                            &#34;fileOption&#34;: self._fileoption_restore_json,
                            &#34;virtualServerRstOption&#34;: self._virtualserver_restore_json,
                            &#34;sharePointRstOption&#34;: self._restore_sharepoint_json,
                            &#34;volumeRstOption&#34;: self._volume_restore_json
                        }
                    }
                }]
            }
        }

        if restore_option.get(&#39;index_free_restore&#39;, False):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;subTask&#34;] = self._json_restore_by_job_subtask
            jobs_list = restore_option.get(&#39;backup_job_ids&#39;)
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;jobIds&#34;] = jobs_list
            source_item = []
            for i in jobs_list:
                source_item.append(&#34;2:{0}&#34;.format(i))
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;fileOption&#34;][&#34;sourceItem&#34;] = source_item

        else:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;subTask&#34;] = self._json_restore_subtask

        if restore_option.get(&#39;schedule_pattern&#39;) is not None:
            request_json = SchedulePattern().create_schedule(request_json,
                                                             restore_option[&#39;schedule_pattern&#39;])

        if restore_option.get(&#34;multinode_restore&#34;, False):

            self._distributed_restore_json = {
                &#34;clientType&#34;: restore_option.get(&#39;client_type&#39;, 0),
                &#34;distributedRestore&#34;: restore_option.get(&#34;multinode_restore&#34;, False),
                &#34;dataAccessNodes&#34;: {
                    &#34;dataAccessNodes&#34;: restore_option.get(&#39;data_access_nodes&#39;, [])
                },
                &#34;isMultiNodeRestore&#34;: restore_option.get(&#34;multinode_restore&#34;, False),
                &#34;backupConfiguration&#34;: {
                    &#34;backupDataAccessNodes&#34;: restore_option.get(&#39;data_access_nodes&#39;, [])
                }
            }

            self._qr_restore_option = {
                &#34;destAppTypeId&#34;: restore_option.get(&#39;destination_appTypeId&#39;, 64)
            }

            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;adminOpts&#34;] = {
                &#34;contentIndexingOption&#34;: {
                    &#34;subClientBasedAnalytics&#34;: False
                }
            }

            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;distributedAppsRestoreOptions&#34;] = self._distributed_restore_json
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;qrOption&#34;] = self._qr_restore_option

        if restore_option.get(&#34;destination_appTypeId&#34;, False):
            self._qr_restore_option = {
                &#34;destAppTypeId&#34;: restore_option.get(&#39;destination_appTypeId&#39;)
            }
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;qrOption&#34;] = self._qr_restore_option

        if &#34;sync_restore&#34; in restore_option:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;syncOption&#34;] = self._sync_restore_json
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][&#34;includeMetaData&#34;] = True
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;destination&#34;][&#34;inPlace&#34;] = True

        if &#39;backup_level&#39; in restore_option:
            backup_opt_json = {
                &#34;backupLevel&#34;: restore_option.get(&#39;backup_level&#39;, &#39;Incremental&#39;)
            }
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;] = backup_opt_json

        if restore_option.get(&#39;restore_acls_only&#39;, False):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;restoreACLsType&#34;] = 1

        if restore_option.get(&#39;restore_data_only&#39;, False):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;restoreACLsType&#34;] = 2

        if &#39;latest_version&#39; in restore_option:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;commonOptions&#34;][&#34;restoreLatestVersionOnly&#34;] = restore_option.get(&#39;latest_version&#39;, True)

        return request_json

    def _restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            restore_jobs=[],
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options

                    options:

                        preserve_level      : preserve level option to set in restore

                        proxy_client        : proxy that needed to be used for restore

                        impersonate_user    : Impersonate user options for restore

                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form

                        all_versions        : if set to True restores all the versions of the
                                                specified file

                        versions            : list of version numbers to be backed up

                        validate_only       : To validate data backed up for restore

                        no_of_streams   (int)       -- Number of streams to be used for restore

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        paths = self._filter_paths(paths)

        request_json = self._restore_json(
            paths=paths,
            in_place=True,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            restore_option=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            restore_jobs=restore_jobs,
            advanced_options=advanced_options
        )

        return self._process_restore_response(request_json)

    def _restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            restore_jobs=[],
            advanced_options=None,
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options

                    options:

                        preserve_level      : preserve level option to set in restore

                        proxy_client        : proxy that needed to be used for restore

                        impersonate_user    : Impersonate user options for restore

                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form

                        all_versions        : if set to True restores all the versions of the
                                                specified file

                        versions            : list of version numbers to be backed up

                        media_agent         : Media Agent need to be used for Browse and restore

                        validate_only       : To validate data backed up for restore

                        no_of_streams   (int)       -- Number of streams to be used for restore

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        from .client import Client

        if not ((isinstance(client, str) or isinstance(client, Client)) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if fs_options is None:
            fs_options = {}

        if isinstance(client, Client):
            client = client
        elif isinstance(client, str):
            client = Client(self._commcell_object, client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        paths = self._filter_paths(paths)

        destination_path = self._filter_paths([destination_path], True)

        request_json = self._restore_json(
            paths=paths,
            in_place=False,
            client=client,
            destination_path=destination_path,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            restore_option=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            restore_jobs=restore_jobs,
            advanced_options=advanced_options
        )

        return self._process_restore_response(request_json)

    def _process_update_request(self, request_json):
        &#34;&#34;&#34;Runs the Instance update API

            Args:
                request_json    (dict)  -- request json sent as payload

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._INSTANCE, request_json
        )

        status, _, error_string = self._process_update_response(flag, response)

        if not status:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to update the instance properties\nError: &#34;{0}&#34;&#39;.format(
                error_string))
        self.refresh()

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;Updates the instance properties

            Args:
                properties_dict (dict)  --  instance properties dict which is to be updated

            Returns:
                None

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected

        **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
        need to change and use the update_properties method to set the properties

        &#34;&#34;&#34;
        request_json = {
            &#34;instanceProperties&#34;: {

            }
        }

        request_json[&#39;instanceProperties&#39;].update(properties_dict)
        self._process_update_request(request_json)

    @property
    def backupsets(self):
        &#34;&#34;&#34;Returns the instance of the Backupsets class representing the list of Backupsets
        installed / configured on the Client for the selected Instance.
        &#34;&#34;&#34;
        if self._backupsets is None:
            from .backupset import Backupsets
            self._backupsets = Backupsets(self)

        return self._backupsets

    @property
    def subclients(self):
        &#34;&#34;&#34;Returns the instance of the Subclients class representing the list of Subclients
        installed / configured on the Client for the selected Instance.
        &#34;&#34;&#34;
        if self._subclients is None:
            self._subclients = Subclients(self)

        return self._subclients

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the instance properties&#34;&#34;&#34;
        return copy.deepcopy(self._properties)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the Instance Display name&#34;&#34;&#34;
        return self._properties[&#34;instance&#34;][&#34;instanceName&#34;]

    @property
    def _task(self):
        &#34;&#34;&#34;Treats the task dict as read only property&#34;&#34;&#34;
        task = {
            &#34;initiatedFrom&#34;: 2,
            &#34;taskType&#34;: 1,
            &#34;policyType&#34;: 0,
            &#34;taskFlags&#34;: {
                &#34;disabled&#34;: False
            }
        }

        return task

    @property
    def _restore_sub_task(self):
        &#34;&#34;&#34;Treats the sub task dict as read only property&#34;&#34;&#34;
        sub_task = {
            &#34;subTaskType&#34;: 3,
            &#34;operationType&#34;: 1001
        }

        return sub_task

    @property
    def instance_id(self):
        &#34;&#34;&#34;Treats the instance id as a read-only attribute.&#34;&#34;&#34;
        return self._instance_id

    @property
    def instance_name(self):
        &#34;&#34;&#34;Treats the instance name as a read-only attribute.&#34;&#34;&#34;
        return self._instance_name

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Instance.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation

            Raises:
                SDKException:
                    if there are more than one backupsets in the instance


            Refer `default_browse_options`_ for all the supported options.

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        # do browse operation if there is only one backupset in the instance
        # raise `SDKException` if there is more than one backupset in the instance

        if len(self.backupsets.all_backupsets) == 1:
            backupset_name = list(self.backupsets.all_backupsets.keys())[0]
            temp_backupset_obj = self.backupsets.get(backupset_name)
            return temp_backupset_obj.browse(*args, **kwargs)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

    def find(self, *args, **kwargs):
        &#34;&#34;&#34;Searches a file/folder in the backed up content of the instance,
            and returns all the files matching the filters given.

            Args:
                Dictionary of browse options:
                    Example:

                        find({
                            &#39;file_name&#39;: &#39;*.txt&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        find(
                            file_name=&#39;*.txt&#39;,

                            show_deleted=True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation

            Raises:
                SDKException:
                    if there are more than one backupsets in the instance


            Refer `default_browse_options`_ for all the supported options.

            Additional options supported:
                file_name       (str)   --  Find files with name

                file_size_gt    (int)   --  Find files with size greater than size

                file_size_lt    (int)   --  Find files with size lesser than size

                file_size_et    (int)   --  Find files with size equal to size

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        # do find operation if there is only one backupset in the instance
        # raise `SDKException` if there is more than one backupset in the instance

        if len(self.backupsets.all_backupsets) == 1:
            backupset_name = list(self.backupsets.all_backupsets.keys())[0]
            temp_backupset_obj = self.backupsets.get(backupset_name)
            return temp_backupset_obj.find(*args, **kwargs)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

    def _impersonation_json(self, value):
        &#34;&#34;&#34;setter of Impersonation Json entity of Json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        use_impersonate = bool(value.get(&#34;impersonate_user&#34;))

        self._impersonation_json_ = {
            &#34;useImpersonation&#34;: use_impersonate,
            &#34;user&#34;: {
                &#34;userName&#34;: value.get(&#34;impersonate_user&#34;, &#34;&#34;),
                &#34;password&#34;: value.get(&#34;impersonate_password&#34;, &#34;&#34;)
            }
        }

    def _restore_browse_option_json(self, value):
        &#34;&#34;&#34;setter  the Browse options for restore in Json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if &#34;copy_precedence&#34; in value and value[&#34;copy_precedence&#34;] != 0:
            value[&#34;copy_precedence_applicable&#34;] = True

        time_range_dict = {}
        options = value.get(&#34;advanced_options&#34;) or {}

        if value.get(&#39;from_time&#39;):
            time_range_dict[&#39;fromTimeValue&#39;] = value.get(&#39;from_time&#39;)

        if value.get(&#39;to_time&#39;):
            time_range_dict[&#39;toTimeValue&#39;] = value.get(&#39;to_time&#39;)

        self._browse_restore_json = {
            &#34;listMedia&#34;: False,
            &#34;useExactIndex&#34;: False,
            &#34;noImage&#34;: value.get(&#34;no_image&#34;, False),
            &#34;commCellId&#34;: 2,
            &#34;liveBrowse&#34;: value.get(&#39;live_browse&#39;, False),
            &#34;mediaOption&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentName&#34;: value.get(&#34;media_agent&#34;, None) or &#34;&#34;
                },
                &#34;proxyForSnapClients&#34;: {
                    &#34;clientName&#34;: value.get(&#34;snap_proxy&#34;, None) or value.get(&#34;proxy_client&#34;, None) or &#34;&#34;
                },
                &#34;library&#34;: {},
                &#34;copyPrecedence&#34;: {
                    &#34;copyPrecedenceApplicable&#34;: value.get(&#34;copy_precedence_applicable&#34;, False),
                    &#34;copyPrecedence&#34;: value.get(&#34;copy_precedence&#34;, 0)
                },
                &#34;drivePool&#34;: {}
            },
            &#34;backupset&#34;: {
                &#34;clientName&#34;: self._agent_object._client_object.client_name,
                &#34;appName&#34;: self._agent_object.agent_name
            },
            &#34;timeZone&#34;: {
                &#34;TimeZoneName&#34;: options.get(&#34;timezone&#34;, self._commcell_object.default_timezone)
            },
            &#34;timeRange&#34;: time_range_dict
        }

        if &#34;browse_job_id&#34; in value:
            self._browse_restore_json[&#34;browseJobId&#34;] = value.get(&#34;browse_job_id&#34;, False)
            self._browse_restore_json[&#34;browseJobCommCellId&#34;] = value.get(
                &#34;commcell_id&#34;, self._commcell_object.commcell_id)

        if value.get(&#39;iscsi_server&#39;):

            self._browse_restore_json[&#39;mediaOption&#39;][&#39;iSCSIServer&#39;] = {
                &#39;clientName&#39;: value.get(&#34;iscsi_server&#34;)
            }

        # Add this option to enable restoring of troubleshooting folder
        if value.get(&#34;include_metadata&#34;, False):
            self._browse_restore_json[&#34;includeMetaData&#34;] = True

        if value.get(&#39;cvcBrowse&#39;):
            self._browse_restore_json[&#34;cvcBrowse&#34;] = True

    def _restore_common_opts_json(self, value):
        &#34;&#34;&#34; Method to set commonOpts for restore

        Args:
             value  (dict)  -- restore options dictionary

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        options = value.get(&#34;advanced_options&#34;)

        if not options:
            return

        # taskInfo -&gt; subTasks -&gt; options -&gt; commonOpts
        self._commonopts_restore_json = {
            &#34;jobDescription&#34;: options.get(&#34;job_description&#34;, &#34;&#34;)
        }

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;setter for  the Common options of in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._commonoption_restore_json = {
            &#34;systemStateBackup&#34;: False,
            &#34;clusterDBBackedup&#34;: False,
            &#34;powerRestore&#34;: False,
            &#34;restoreToDisk&#34;: value.get(&#34;restore_to_disk&#34;, False),
            &#34;indexFreeRestore&#34;: value.get(&#34;index_free_restore&#34;, False),
            &#34;offlineMiningRestore&#34;: False,
            &#34;onePassRestore&#34;: False,
            &#34;detectRegularExpression&#34;: True,
            &#34;wildCard&#34;: False,
            &#34;preserveLevel&#34;: value.get(&#34;preserve_level&#34;, 1),
            &#34;restoreToExchange&#34;: False,
            &#34;stripLevel&#34;: 0,
            &#34;skipErrorsAndContinue&#34;: value.get(&#34;skipErrorsAndContinue&#34;, False),
            &#34;restoreACLs&#34;: value.get(&#34;restore_ACL&#34;, value.get(&#34;restore_data_and_acl&#34;, True)),
            &#34;stripLevelType&#34;: value.get(&#34;striplevel_type&#34;, 0),
            &#34;allVersion&#34;: value.get(&#34;all_versions&#34;, False),
            &#34;unconditionalOverwrite&#34;: value.get(&#34;unconditional_overwrite&#34;, False),
            &#34;includeAgedData&#34;: value.get(&#34;include_aged_data&#34;, False),
            &#34;validateOnly&#34;: value.get(&#34;validate_only&#34;, False)
        }

        if value.get(&#39;advanced_options&#39;):
            if value[&#39;advanced_options&#39;].get(&#34;iSeriesObject&#34;):
                ibmi_value = value[&#39;advanced_options&#39;][&#34;iSeriesObject&#34;]
                ibmi_opts = {}
                if ibmi_value.get(&#34;restorePrivateAuthority&#34;):
                    ibmi_opts.update({&#39;restorePrivateAuthority&#39;: ibmi_value.get(&#39;restorePrivateAuthority&#39;)})
                if ibmi_value.get(&#34;restoreSpooledFileData&#34;):
                    ibmi_opts.update({&#39;restoreSpooledFileData&#39;: ibmi_value.get(&#39;restoreSpooledFileData&#39;)})
                if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;):
                    if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;) == &#34;None&#34;:
                        ibmi_opts.update({&#39;iseriesDifferentObjectType&#39;: &#34;0&#34;})
                    if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;) == &#34;*ALL&#34;:
                        ibmi_opts.update({&#39;iseriesDifferentObjectType&#39;: &#34;1&#34;})
                    if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;) == &#34;*COMPATIBLE&#34;:
                        ibmi_opts.update({&#39;iseriesDifferentObjectType&#39;: &#34;2&#34;})
                    if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;) == &#34;OTHER&#34;:
                        ibmi_opts.update({&#39;iseriesDifferentObjectType&#39;: &#34;3&#34;})
                        if ibmi_value.get(&#34;autl&#34;):
                            ibmi_opts.update({&#39;autl&#39;: ibmi_value.get(&#39;autl&#39;)})
                        if ibmi_value.get(&#34;fileLevel&#34;):
                            ibmi_opts.update({&#39;fileLevel&#39;: ibmi_value.get(&#39;fileLevel&#39;)})
                        if ibmi_value.get(&#34;owner&#34;):
                            ibmi_opts.update({&#39;owner&#39;: ibmi_value.get(&#39;owner&#39;)})
                        if ibmi_value.get(&#34;pgp&#34;):
                            ibmi_opts.update({&#39;pgp&#39;: ibmi_value.get(&#39;pgp&#39;)})
                if ibmi_value.get(&#34;forceObjectConversionSelction&#34;):
                    ibmi_opts.update({&#39;forceObjectConversionSelction&#39;: ibmi_value.get(
                        &#39;forceObjectConversionSelction&#39;)})
                if ibmi_value.get(&#34;securityDataParameter&#34;):
                    ibmi_opts.update({&#39;securityDataParameter&#39;: ibmi_value.get(&#39;securityDataParameter&#39;)})
                if ibmi_value.get(&#34;deferId&#34;):
                    ibmi_opts.update({&#39;deferId&#39;: ibmi_value.get(&#39;deferId&#39;)})

                self._commonoption_restore_json[&#39;iSeriesObject&#39;] = ibmi_opts

        if value.get(&#34;instant_clone_options&#34;, {}).get(&#34;post_clone_script&#34;, None):
            self._commonoption_restore_json[&#39;prePostCloneOption&#39;] = {
                &#39;postCloneCmd&#39;: value.get(&#34;instant_clone_options&#34;).get(&#34;post_clone_script&#34;)
            }

        _advance_fs_keys = [&#34;restoreDataInsteadOfStub&#34;,
                            &#34;restoreOnlyStubExists&#34;,
                            &#34;overwriteFiles&#34;,
                            &#34;doNotOverwriteFileOnDisk&#34;,
                            &#34;disableStubRestore&#34;]

        if &#34;fs_options&#34; in value:
            _fs_option_value = value[&#34;fs_options&#34;]
            if _fs_option_value is not None:
                for _key in _advance_fs_keys:
                    if _key in _fs_option_value:
                        self._commonoption_restore_json[_key] = _fs_option_value[_key]

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;setter for  the destination restore option in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if value.get(&#34;proxy_client&#34;) is not None and \
                (self._agent_object.agent_name).upper() == &#34;FILE SYSTEM&#34;:
            self._destination_restore_json = {
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;, True),
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;proxy_client&#34;, &#34;&#34;)
                }
            }
            if value.get(&#39;destination_path&#39;):
                destination_path = value.get(&#34;destination_path&#34;, &#34;&#34;)
                self._destination_restore_json[&#34;destPath&#34;] = [destination_path] if destination_path != &#34;&#34; else []

        # For Index server restore, we need to set proxy client &amp; in-place flag
        elif value.get(&#34;proxy_client&#34;) is not None and \
                (self._agent_object.agent_name).upper() == &#34;BIG DATA APPS&#34; and \
                self.name.upper() == &#34;DYNAMICINDEXINSTANCE&#34;:
            self._destination_restore_json = {
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;, True),
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;proxy_client&#34;, &#34;&#34;)
                }
            }
        else:
            # removed clientId from destClient as VSA Restores fail with it
            self._destination_restore_json = {
                &#34;isLegalHold&#34;: False,
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;, True),
                &#34;destPath&#34;: [value.get(&#34;destination_path&#34;, &#34;&#34;)],
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;client_name&#34;, &#34;&#34;),
                }
            }
            # removing &#39;destPath&#39; if restoring in place
            self._destination_restore_json.pop(&#39;destPath&#39;) if value.get(&#34;in_place&#34;, True) else None

        if value.get(&#34;multinode_restore&#34;, False) or value.get(&#34;no_of_streams&#34;, 1) &gt; 1:
            self._destination_restore_json[&#34;destinationInstance&#34;] = {
                &#34;instanceName&#34;: value.get(&#39;destination_instance&#39;, self.instance_name)
            }
            if value.get(&#39;destination_instance_id&#39;) is not None:
                self._destination_restore_json[&#34;destinationInstance&#34;][&#34;instanceId&#34;] = int(
                    value.get(&#39;destination_instance_id&#39;)
                    )

            self._destination_restore_json[&#34;noOfStreams&#34;] = value.get(&#39;no_of_streams&#39;, 2)

    def _restore_fileoption_json(self, value):
        &#34;&#34;&#34;setter for  the fileoption restore option in restore JSON&#34;&#34;&#34;
        self._fileoption_restore_json = {
            &#34;sourceItem&#34;: value[&#34;instant_clone_options&#34;][&#34;instant_clone_src_path&#34;] if value.get(&#34;instant_clone_options&#34;, None) else value.get(&#34;paths&#34;, []),
            &#34;browseFilters&#34;: value.get(&#34;browse_filters&#34;, [])
        }

        if value.get(&#34;instant_clone_options&#34;, None):
            self._fileoption_restore_json[&#34;fsCloneOptions&#34;] = {
                &#34;reservationTime&#34;: value[&#34;instant_clone_options&#34;][&#34;reservation_time&#34;],
                &#34;cloneMountPath&#34;: value[&#34;instant_clone_options&#34;][&#34;clone_mount_path&#34;]}

            if value[&#34;instant_clone_options&#34;].get(&#34;clone_cleanup_script&#34;, None):
                self._fileoption_restore_json[&#34;fsCloneOptions&#34;][&#34;cloneCleanupOptions&#34;] = {
                    &#34;cleanupScriptPath&#34;: value.get(&#34;instant_clone_options&#34;).get(&#34;clone_cleanup_script&#34;)
                }
        
        if value.get(&#39;advanced_options&#39;):
            if value[&#39;advanced_options&#39;].get(&#34;mapFiles&#34;):
                map_file = value[&#39;advanced_options&#39;][&#34;mapFiles&#34;]
                self._fileoption_restore_json[&#34;mapFiles&#34;] = {
                    &#34;useMapFile&#34;: True,
                    &#34;mapFilePath&#34;: map_file.get(&#39;mapFilePath&#39;, &#34;&#34;),
                    &#34;restoreUnmappedFiles&#34;: map_file.get(&#34;restoreUnmappedFiles&#34;, &#34;&#34;),
                    &#34;renameFilesSuffix&#34;: map_file.get(&#34;renameFilesSuffix&#34;, &#34;&#34;),
                    &#34;mapFileClient&#34;: {&#34;clientName&#34;: map_file.get(&#34;clientName&#34;, &#34;&#34;)}
                }

    def _restore_bigdataapps_option_json(self, value):
        &#34;&#34;&#34;setter for Big Data Apps restore option in restore JSON&#34;&#34;&#34;
        request_json = self._restore_json(restore_option=value)

        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;subclientId&#34;] = value.get(
            &#34;subclient_id&#34;, -1)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = value.get(
            &#34;backupset_name&#34;)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = value.get(
            &#34;_type_&#34;)

        distributed_restore_json = {
            &#34;distributedRestore&#34;: True,
        }

        if value.get(&#34;cassandra_restore&#34;, False):
            distributed_restore_json[&#34;cassandraRestoreOptions&#34;] = {
                &#34;outofPlaceRestore&#34;: value.get(&#34;outofPlaceRestore&#34;, False),
                &#34;runStageFreeRestore&#34;: value.get(&#34;runStageFreeRestore&#34;, False),
                &#34;recover&#34;: value.get(&#34;recover&#34;, False),
                &#34;replaceDeadNode&#34;: value.get(&#34;replaceDeadNode&#34;, False),
                &#34;stagingLocation&#34;: value.get(&#34;stagingLocation&#34;),
                &#34;useSSTableLoader&#34;: value.get(&#34;useSSTableLoader&#34;, False),
                &#34;runLogRestore&#34;: value.get(&#34;runLogRestore&#34;, False),
                &#34;nodeMap&#34;: value.get(&#34;nodeMap&#34;, []),
                &#34;dockerNodeMap&#34;: value.get(&#34;dockerNodeMap&#34;, []),
                &#34;DBRestore&#34;: value.get(&#34;DBRestore&#34;, False),
                &#34;truncateTables&#34;: value.get(&#34;truncateTables&#34;, False)
            }

        if value.get(&#34;cockroachdb_restore&#34;, False):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = {
                &#34;instanceType&#34;: &#34;AMAZON_S3&#34;}
            distributed_restore_json[&#34;clientType&#34;] = value.get(&#34;client_type&#34;)
            distributed_restore_json[&#34;isMultiNodeRestore&#34;] = True
            distributed_restore_json[&#34;noSQLGenericRestoreOptions&#34;] = {
                &#34;pointInTime&#34;: {
                    &#34;time&#34;: 0
                },
                &#34;restoreCluster&#34;: False,
                &#34;tableMap&#34;: [
                    {
                        &#34;fromTable&#34;: value.get(&#34;fromtable&#34;),
                        &#34;toTable&#34;: value.get(&#34;totable&#34;)
                    }
                ]
            }

            access_nodes = []
            for node in value.get(&#34;accessnodes&#34;):
                client_object = self._commcell_object.clients.get(node)
                client_object._get_client_properties()
                client_id = int(client_object.client_id)
                access_node = {
                    &#34;clientId&#34;: client_id,
                    &#34;clientName&#34;: client_object.client_name
                }
                access_nodes.append(access_node)

            distributed_restore_json[&#34;dataAccessNodes&#34;] = {
                &#34;dataAccessNodes&#34;: access_nodes
            }

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;distributedAppsRestoreOptions&#34;] = distributed_restore_json
        return request_json

    def _restore_virtual_rst_option_json(self, value):
        &#34;&#34;&#34;setter for the virtualServer restore option in restore JSON&#34;&#34;&#34;
        self._virtualserver_restore_json = {
            &#34;isFileBrowse&#34;: value.get(&#34;fileBrowse&#34;)
        }

    def _restore_volume_rst_option_json(self, value):
        &#34;&#34;&#34;setter for the volumeRst restore option in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._volume_restore_json = {
            &#34;volumeLeveRestore&#34;: value.get(&#34;volume_level_restore&#34;, False),
            &#34;volumeLevelRestoreType&#34;: value.get(&#34;volume_level_restore_type&#34;, &#34;PHYSICAL_VOLUME&#34;)
        }

    def _sync_restore_option_json(self, value):
        &#34;&#34;&#34;setter for the Sync. Restore option in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._sync_restore_json = {
            &#34;PreserveModifiedFiles&#34;: True,
            &#34;isMigration&#34;: True,
            &#34;isSyncRestore&#34;: True,
            &#34;paths&#34;: value.get(&#34;sync_option_paths&#34;)
        }

    @property
    def _restore_sharepoint_json(self):
        &#34;&#34;&#34;getter for Sharepoint restore option in JSON. it is read only attribute&#34;&#34;&#34;
        _sharepoint_restore_json = {
            &#34;is90OrUpgradedClient&#34;: False
        }

        return _sharepoint_restore_json

    @property
    def _json_task(self):
        &#34;&#34;&#34;getter for the task information in JSON&#34;&#34;&#34;

        _taks_option_json = {
            &#34;initiatedFrom&#34;: 2,
            &#34;taskType&#34;: 1,
            &#34;policyType&#34;: 0,
            &#34;taskFlags&#34;: {
                &#34;disabled&#34;: False
            }
        }

        return _taks_option_json

    @property
    def _json_restore_subtask(self):
        &#34;&#34;&#34;getter for the subtask in restore JSON . It is read only attribute&#34;&#34;&#34;

        _subtask_restore_json = {
            &#34;subTaskType&#34;: 3,
            &#34;operationType&#34;: 1001
        }

        return _subtask_restore_json

    @property
    def _json_restore_by_job_subtask(self):
        &#34;&#34;&#34;getter for the subtast in restore by job JSON&#34;&#34;&#34;

        _subtask_restore_by_job_json = {
            &#34;subTaskType&#34;: 3,
            &#34;operationType&#34;: 1005
        }

        return _subtask_restore_by_job_json

    @property
    def _json_backup_subtasks(self):
        &#34;&#34;&#34;getter for the subtask in restore JSON . It is read only attribute&#34;&#34;&#34;

        _backup_subtask = {
            &#34;subTaskType&#34;: 2,
            &#34;operationType&#34;: 2
        }

        return _backup_subtask

    @property
    def credentials(self):
        &#34;&#34;&#34;Getter for the instance credentials

            Returns:
                str - name of the credential associated with the instance

        &#34;&#34;&#34;
        return self._properties.get(&#39;credentialEntity&#39;, {}).get(&#39;credentialName&#39;, &#39;&#39;)

    @credentials.setter
    def credentials(self, credential_name):
        &#34;&#34;&#34;Setter for the instance credentials

            Args:
                credential_name (str)  --  name of the credential to be associated with the instance

        &#34;&#34;&#34;
        properties = self._properties
        credential_id = self._commcell_object.credentials.get(credential_name).credential_id
        credential_json = {
            &#34;credentialId&#34;: credential_id,
            &#34;credentialName&#34;: credential_name
        }
        properties[&#39;credentialEntity&#39;] = credential_json
        self.update_properties(properties)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Instance.&#34;&#34;&#34;
        self._get_instance_properties()
        self._backupsets = None
        self._subclients = None</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instance.Instance"><code class="flex name class">
<span>class <span class="ident">Instance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing instance operations for a specific instance.</p>
<p>Initialise the instance object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L1825-L3400" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Instance(object):
    &#34;&#34;&#34;Class for performing instance operations for a specific instance.&#34;&#34;&#34;

    def __new__(cls, agent_object, instance_name, instance_id=None):
        from .instances.vsinstance import VirtualServerInstance
        from .instances.cainstance import CloudAppsInstance
        from .instances.bigdataappsinstance import BigDataAppsInstance
        from .instances.sqlinstance import SQLServerInstance
        from .instances.hanainstance import SAPHANAInstance
        from .instances.oracleinstance import OracleInstance
        from .instances.sybaseinstance import SybaseInstance
        from .instances.saporacleinstance import SAPOracleInstance
        from .instances.mysqlinstance import MYSQLInstance
        from .instances.lotusnotes.lndbinstance import LNDBInstance
        from .instances.lotusnotes.lndocinstance import LNDOCInstance
        from .instances.lotusnotes.lndminstance import LNDMInstance
        from .instances.postgresinstance import PostgreSQLInstance
        from .instances.informixinstance import InformixInstance
        from .instances.vminstance import VMInstance
        from .instances.db2instance import DB2Instance
        from .instances.aadinstance import AzureAdInstance
        from .instances.sharepointinstance import SharepointInstance

        # add the agent name to this dict, and its class as the value
        # the appropriate class object will be initialized based on the agent
        _instances_dict = {
            &#39;virtual server&#39;: [VirtualServerInstance, VMInstance],
            &#39;big data apps&#39;: BigDataAppsInstance,
            &#39;cloud apps&#39;: CloudAppsInstance,
            &#39;sql server&#39;: SQLServerInstance,
            &#39;sap hana&#39;: SAPHANAInstance,
            &#39;oracle&#39;: OracleInstance,
            &#39;oracle rac&#39;: OracleInstance,
            &#39;sybase&#39;: SybaseInstance,
            &#39;sap for oracle&#39;: SAPOracleInstance,
            &#39;mysql&#39;: MYSQLInstance,
            &#39;notes database&#39;: LNDBInstance,
            &#39;notes document&#39;: LNDOCInstance,
            &#39;domino mailbox archiver&#39;: LNDMInstance,
            &#39;postgresql&#39;: PostgreSQLInstance,
            &#39;informix&#39;: InformixInstance,
            &#39;db2&#39;: DB2Instance,
            &#39;azure ad&#39;: AzureAdInstance,
            &#39;sharepoint server&#39;: SharepointInstance
        }
        agent_name = agent_object.agent_name

        if agent_name in _instances_dict:
            if isinstance(_instances_dict[agent_name], list):
                if instance_name == &#34;vminstance&#34;:
                    _class = _instances_dict[agent_name][-1]
                else:
                    _class = _instances_dict[agent_name][0]
            else:
                _class = _instances_dict[agent_name]
            if _class.__new__ == cls.__new__:
                return object.__new__(_class)
            return _class.__new__(_class, agent_object, instance_name, instance_id)
        else:
            return object.__new__(cls)

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initialise the instance object.

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class
        &#34;&#34;&#34;
        self._agent_object = agent_object
        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_
        self._instance_name = instance_name.lower()

        if instance_id:
            # Use the instance id provided in the arguments
            self._instance_id = str(instance_id)
        else:
            # Get the id associated with this instance
            self._instance_id = self._get_instance_id()

        self._INSTANCE = self._services[&#39;INSTANCE&#39;] % (self._instance_id)
        self._ALLINSTANCES = self._services[&#39;GET_ALL_INSTANCES&#39;] % (
            self._agent_object._client_object.client_id
        )
        self._RESTORE = self._services[&#39;RESTORE&#39;]
        self._SEARCH_DURING_RESTORE = self._services[&#39;DO_WEB_SEARCH&#39;]
        self._properties = None
        self._restore_association = None

        # Restore json instance var
        self._commonopts_restore_json = {}

        self._backupsets = None
        self._subclients = None
        self.refresh()

    def _get_instance_id(self):
        &#34;&#34;&#34;Gets the instance id associated with this backupset.

             Returns:
                str - id associated with this instance
        &#34;&#34;&#34;
        instances = Instances(self._agent_object)
        return instances.get(self.instance_name).instance_id

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Instance class instance for Instance: &#34;{0}&#34; of Agent: &#34;{1}&#34;&#39;
        return representation_string.format(
            self._instance[&#34;instanceName&#34;], self._agent_object.agent_name
        )

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        # skip GET instance properties api call if instance id is 1
        if int(self.instance_id) == 1:
            self._properties = {
                &#39;instance&#39;: {
                    &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;instanceName&#34;: self.instance_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceId&#34;: int(self.instance_id),
                    &#34;applicationId&#34;: int(self._agent_object.agent_id)
                }
            }

            self._instance = self._properties[&#34;instance&#34;]
            # stop the execution here for instance id 1 (DefaultInstanceName)
            return

        instance_service = (
            &#34;{0}?association/entity/clientId={1}&amp;association/entity/applicationId={2}&#34;.format(
                self._INSTANCE, self._agent_object._client_object.client_id,
                self._agent_object.agent_id
            )
        )
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, instance_service)

        if flag:
            if response.json() and &#34;instanceProperties&#34; in response.json():
                self._properties = response.json()[&#34;instanceProperties&#34;][0]
                try:
                    self._instance = self._properties[&#34;instance&#34;]
                    self._instance_name = self._properties[&#34;instance&#34;][&#34;instanceName&#34;].lower()
                    self._instanceActivityControl = self._properties[&#34;instanceActivityControl&#34;]
                except KeyError:
                    instance_service = (
                        &#34;{0}&amp;applicationId={1}&#34;.format(self._ALLINSTANCES, self._agent_object.agent_id))
                    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, instance_service)
                    if flag:
                        if response.json() and &#34;instanceProperties&#34; in response.json():
                            self._properties = response.json()[&#34;instanceProperties&#34;][0]
                            self._instance = self._properties[&#34;instance&#34;]
                            self._instance_name = self._properties[&#34;instance&#34;][&#34;instanceName&#34;].lower()
                            self._instanceActivityControl = self._properties[&#34;instanceActivityControl&#34;]
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;get the all instance related properties.

           Returns:
                dict - all instance properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;: {
                &#34;isDeleted&#34;: False,
                &#34;instance&#34;: self._instance,
                &#34;instanceActivityControl&#34;: self._instanceActivityControl
            }
        }

        return instance_json

    def _set_instance_properties(self, attr_name, value):
        &#34;&#34;&#34;sets the properties of this sub client.value is updated to instance once when post call
            succeeds.

            Args:
                attr_name   (str)   --  old value of the property. this should be instance variable

                value       (str)   --  new value of the property. this should be instance variable

            Raises:
                SDKException:
                    if failed to update number properties for subclient

        &#34;&#34;&#34;
        try:
            backup = eval(&#39;self.%s&#39; % attr_name)        # Take backup of old value
        except (AttributeError, KeyError):
            backup = None

        exec(&#34;self.%s = %s&#34; % (attr_name, &#39;value&#39;))     # set new value

        # _get_instance_properties_json method must be added in all child classes
        # not to be added for classes, which does not support updating properties
        request_json = self._get_instance_properties_json()

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._INSTANCE, request_json)

        output = self._process_update_response(flag, response)
        if output[0]:
            return
        else:
            o_str = &#39;Failed to update properties of subclient\nError: &#34;{0}&#34;&#39;

            # Restore original value from backup on failure
            exec(&#34;self.%s = %s&#34; % (attr_name, backup))
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))

    def _process_update_response(self, flag, response):
        &#34;&#34;&#34;Updates the subclient properties with the request provided.

            Args:
                update_request  (str)  --  update request specifying the details to update

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if failed to update properties

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;response&#34; in response.json():
                    error_code = str(response.json()[&#34;response&#34;][0][&#34;errorCode&#34;])

                    if error_code == &#34;0&#34;:
                        return (True, &#34;0&#34;, &#34;&#34;)
                    else:
                        error_message = &#34;&#34;

                        if &#34;errorString&#34; in response.json()[&#34;response&#34;][0]:
                            error_message = response.json()[&#34;response&#34;][0][&#34;errorString&#34;]

                        if error_message:
                            return (False, error_code, error_message)
                        else:
                            return (False, error_code, &#34;&#34;)
                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return (True, &#34;0&#34;, &#34;&#34;)

                    if error_message:
                        return (False, error_code, error_message)
                    else:
                        return (False, error_code, &#34;&#34;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _process_search_response(self, request_json):
        &#34;&#34;&#34;Runs the Search API with the request JSON provided for Find and Restore,
                    and returns the contents after parsing the response.
            Args:
                request_json    (dict)  --  JSON request to run for the API
            Returns:
                result          (list)  -- list of messages eligible for restore for as per request json
                    Raises:
                        SDKException:
                            if response is empty
                            if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SEARCH_DURING_RESTORE, request_json)
        self._restore_association = None
        result = []
        if flag:
            if response.json():
                res = response.json()
                result_item_result = res.get(&#34;searchResult&#34;, {}).get(&#34;resultItem&#34;, [])
                for item in result_item_result:
                    app_specific = item.get(&#34;appSpecific&#34;, {})
                    e_mail = app_specific.get(&#34;eMail&#34;, {})
                    link = e_mail.get(&#34;links&#34;)
                    result.append(link)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return result

    def _process_restore_response(self, request_json):
        &#34;&#34;&#34;Runs the CreateTask API with the request JSON provided for Restore,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if restore job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._RESTORE, request_json)

        self._restore_association = None

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _process_delete_response(self, request_json):
        &#34;&#34;&#34;Runs the DeleteDocuments API with the request JSON provided for Delete,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                object - instance of the Job class for this Delete job if it is a folder

            Raises:
                SDKException:
                    if Delete job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._DELETE, request_json)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;processingInfo&#34; in response.json():
                    pass
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Delete job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to run the Delete job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _filter_paths(self, paths, is_single_path=False):
        &#34;&#34;&#34;Filters the paths based on the Operating System, and Agent.

            Args:
                paths           (list)  --  list containing paths to be filtered

                is_single_path  (bool)  --  boolean specifying whether to return a single path
                                                or the entire list

            Returns:
                list    -   if the boolean is_single_path is set to False

                str     -   if the boolean is_single_path is set to True
        &#34;&#34;&#34;
        for index, path in enumerate(paths):
            if int(self._agent_object.agent_id) == AppIDAType.WINDOWS_FILE_SYSTEM:
                path = path.strip(&#39;\\&#39;).strip(&#39;/&#39;)
                if path:
                    path = path.replace(&#39;/&#39;, &#39;\\&#39;)
                else:
                    path = &#39;\\&#39;
            elif int(self._agent_object.agent_id) == AppIDAType.LINUX_FILE_SYSTEM:
                path = path.strip(&#39;\\&#39;).strip(&#39;/&#39;)
                if path:
                    path = path.replace(&#39;\\&#39;, &#39;/&#39;)
                else:
                    path = &#39;\\&#39;
                path = &#39;/&#39; + path

            paths[index] = path

        if is_single_path:
            return paths[0]
        else:
            return paths

    def _restore_json(
            self,
            **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (list)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;

        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        if self._restore_association is None:
            self._restore_association = self._instance
            
        if restore_option.get(&#39;deduce_bkset_subcl&#39;) == True:
            del self._restore_association[&#39;backupsetName&#39;]
            del self._restore_association[&#39;subclientName&#39;]
            del self._restore_association[&#39;backupsetId&#39;]
            del self._restore_association[&#39;subclientId&#39;]
            del self._restore_association[&#39;subclientGUID&#39;]
            

        if restore_option.get(&#39;copy_precedence&#39;) is None:
            restore_option[&#39;copy_precedence&#39;] = 0

        if restore_option.get(&#39;overwrite&#39;) is not None:
            restore_option[&#39;unconditional_overwrite&#39;] = restore_option[&#39;overwrite&#39;]

        if restore_option.get(&#39;live_browse&#39;):
            restore_option[&#39;liveBrowse&#39;] = True
        else:
            restore_option[&#39;liveBrowse&#39;] = False
        
        if restore_option.get(&#39;file_browse&#39;):
            restore_option[&#39;fileBrowse&#39;] = True
        else:
            restore_option[&#39;fileBrowse&#39;] = False

        # restore_option should use client key for destination client info
        client = restore_option.get(&#34;client&#34;, self._agent_object._client_object)

        if isinstance(client, str):
            client = self._commcell_object.clients.get(client)

        restore_option[&#34;client_name&#34;] = client.client_name
        restore_option[&#34;client_id&#34;] = int(client.client_id)

        # set time zone
        from_time = restore_option.get(&#34;from_time&#34;, None)
        to_time = restore_option.get(&#34;to_time&#34;, None)
        time_list = [&#39;01/01/1970 00:00:00&#39;, &#39;1/1/1970 00:00:00&#39;]

        if from_time and from_time not in time_list:
            restore_option[&#34;from_time&#34;] = from_time

        if to_time and to_time not in time_list:
            restore_option[&#34;to_time&#34;] = to_time

        # set versions
        if &#34;versions&#34; in restore_option:
            versions = restore_option[&#39;versions&#39;]
            if not isinstance(versions, list):
                raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
            if &#39;win&#39; in self._agent_object._client_object.os_info.lower() or self._agent_object._agent_name == &#34;virtual server&#34;:
                version_string = &#34;|\\|#15!vErSiOnS|#15!\\{0}&#34;
            else:
                version_string = &#34;|/|#15!vErSiOnS|#15!/{0}&#34;

            for version in versions:
                version = version_string.format(version)
                restore_option[&#34;paths&#34;].append(version)

        self._restore_browse_option_json(restore_option)
        self._restore_common_options_json(restore_option)
        self._impersonation_json(restore_option)
        self._restore_destination_json(restore_option)
        self._restore_fileoption_json(restore_option)
        self._restore_virtual_rst_option_json(restore_option)
        self._restore_volume_rst_option_json(restore_option)
        self._sync_restore_option_json(restore_option)
        self._restore_common_opts_json(restore_option)

        if not restore_option.get(&#39;index_free_restore&#39;, False):
            if restore_option.get(&#34;paths&#34;) == []:
                raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._restore_association],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [{
                    &#34;subTaskOperation&#34;: 1,
                    &#34;options&#34;: {
                        &#34;commonOpts&#34;: self._commonopts_restore_json,
                        &#34;restoreOptions&#34;: {
                            &#34;impersonation&#34;: self._impersonation_json_,
                            &#34;browseOption&#34;: self._browse_restore_json,
                            &#34;commonOptions&#34;: self._commonoption_restore_json,
                            &#34;destination&#34;: self._destination_restore_json,
                            &#34;fileOption&#34;: self._fileoption_restore_json,
                            &#34;virtualServerRstOption&#34;: self._virtualserver_restore_json,
                            &#34;sharePointRstOption&#34;: self._restore_sharepoint_json,
                            &#34;volumeRstOption&#34;: self._volume_restore_json
                        }
                    }
                }]
            }
        }

        if restore_option.get(&#39;index_free_restore&#39;, False):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;subTask&#34;] = self._json_restore_by_job_subtask
            jobs_list = restore_option.get(&#39;backup_job_ids&#39;)
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;jobIds&#34;] = jobs_list
            source_item = []
            for i in jobs_list:
                source_item.append(&#34;2:{0}&#34;.format(i))
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;fileOption&#34;][&#34;sourceItem&#34;] = source_item

        else:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;subTask&#34;] = self._json_restore_subtask

        if restore_option.get(&#39;schedule_pattern&#39;) is not None:
            request_json = SchedulePattern().create_schedule(request_json,
                                                             restore_option[&#39;schedule_pattern&#39;])

        if restore_option.get(&#34;multinode_restore&#34;, False):

            self._distributed_restore_json = {
                &#34;clientType&#34;: restore_option.get(&#39;client_type&#39;, 0),
                &#34;distributedRestore&#34;: restore_option.get(&#34;multinode_restore&#34;, False),
                &#34;dataAccessNodes&#34;: {
                    &#34;dataAccessNodes&#34;: restore_option.get(&#39;data_access_nodes&#39;, [])
                },
                &#34;isMultiNodeRestore&#34;: restore_option.get(&#34;multinode_restore&#34;, False),
                &#34;backupConfiguration&#34;: {
                    &#34;backupDataAccessNodes&#34;: restore_option.get(&#39;data_access_nodes&#39;, [])
                }
            }

            self._qr_restore_option = {
                &#34;destAppTypeId&#34;: restore_option.get(&#39;destination_appTypeId&#39;, 64)
            }

            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;adminOpts&#34;] = {
                &#34;contentIndexingOption&#34;: {
                    &#34;subClientBasedAnalytics&#34;: False
                }
            }

            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;distributedAppsRestoreOptions&#34;] = self._distributed_restore_json
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;qrOption&#34;] = self._qr_restore_option

        if restore_option.get(&#34;destination_appTypeId&#34;, False):
            self._qr_restore_option = {
                &#34;destAppTypeId&#34;: restore_option.get(&#39;destination_appTypeId&#39;)
            }
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;qrOption&#34;] = self._qr_restore_option

        if &#34;sync_restore&#34; in restore_option:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;syncOption&#34;] = self._sync_restore_json
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][&#34;includeMetaData&#34;] = True
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;destination&#34;][&#34;inPlace&#34;] = True

        if &#39;backup_level&#39; in restore_option:
            backup_opt_json = {
                &#34;backupLevel&#34;: restore_option.get(&#39;backup_level&#39;, &#39;Incremental&#39;)
            }
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;] = backup_opt_json

        if restore_option.get(&#39;restore_acls_only&#39;, False):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;restoreACLsType&#34;] = 1

        if restore_option.get(&#39;restore_data_only&#39;, False):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;restoreACLsType&#34;] = 2

        if &#39;latest_version&#39; in restore_option:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;commonOptions&#34;][&#34;restoreLatestVersionOnly&#34;] = restore_option.get(&#39;latest_version&#39;, True)

        return request_json

    def _restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            restore_jobs=[],
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options

                    options:

                        preserve_level      : preserve level option to set in restore

                        proxy_client        : proxy that needed to be used for restore

                        impersonate_user    : Impersonate user options for restore

                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form

                        all_versions        : if set to True restores all the versions of the
                                                specified file

                        versions            : list of version numbers to be backed up

                        validate_only       : To validate data backed up for restore

                        no_of_streams   (int)       -- Number of streams to be used for restore

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        paths = self._filter_paths(paths)

        request_json = self._restore_json(
            paths=paths,
            in_place=True,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            restore_option=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            restore_jobs=restore_jobs,
            advanced_options=advanced_options
        )

        return self._process_restore_response(request_json)

    def _restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            restore_jobs=[],
            advanced_options=None,
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options

                    options:

                        preserve_level      : preserve level option to set in restore

                        proxy_client        : proxy that needed to be used for restore

                        impersonate_user    : Impersonate user options for restore

                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form

                        all_versions        : if set to True restores all the versions of the
                                                specified file

                        versions            : list of version numbers to be backed up

                        media_agent         : Media Agent need to be used for Browse and restore

                        validate_only       : To validate data backed up for restore

                        no_of_streams   (int)       -- Number of streams to be used for restore

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        from .client import Client

        if not ((isinstance(client, str) or isinstance(client, Client)) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if fs_options is None:
            fs_options = {}

        if isinstance(client, Client):
            client = client
        elif isinstance(client, str):
            client = Client(self._commcell_object, client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        paths = self._filter_paths(paths)

        destination_path = self._filter_paths([destination_path], True)

        request_json = self._restore_json(
            paths=paths,
            in_place=False,
            client=client,
            destination_path=destination_path,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            restore_option=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            restore_jobs=restore_jobs,
            advanced_options=advanced_options
        )

        return self._process_restore_response(request_json)

    def _process_update_request(self, request_json):
        &#34;&#34;&#34;Runs the Instance update API

            Args:
                request_json    (dict)  -- request json sent as payload

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._INSTANCE, request_json
        )

        status, _, error_string = self._process_update_response(flag, response)

        if not status:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to update the instance properties\nError: &#34;{0}&#34;&#39;.format(
                error_string))
        self.refresh()

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;Updates the instance properties

            Args:
                properties_dict (dict)  --  instance properties dict which is to be updated

            Returns:
                None

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected

        **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
        need to change and use the update_properties method to set the properties

        &#34;&#34;&#34;
        request_json = {
            &#34;instanceProperties&#34;: {

            }
        }

        request_json[&#39;instanceProperties&#39;].update(properties_dict)
        self._process_update_request(request_json)

    @property
    def backupsets(self):
        &#34;&#34;&#34;Returns the instance of the Backupsets class representing the list of Backupsets
        installed / configured on the Client for the selected Instance.
        &#34;&#34;&#34;
        if self._backupsets is None:
            from .backupset import Backupsets
            self._backupsets = Backupsets(self)

        return self._backupsets

    @property
    def subclients(self):
        &#34;&#34;&#34;Returns the instance of the Subclients class representing the list of Subclients
        installed / configured on the Client for the selected Instance.
        &#34;&#34;&#34;
        if self._subclients is None:
            self._subclients = Subclients(self)

        return self._subclients

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the instance properties&#34;&#34;&#34;
        return copy.deepcopy(self._properties)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the Instance Display name&#34;&#34;&#34;
        return self._properties[&#34;instance&#34;][&#34;instanceName&#34;]

    @property
    def _task(self):
        &#34;&#34;&#34;Treats the task dict as read only property&#34;&#34;&#34;
        task = {
            &#34;initiatedFrom&#34;: 2,
            &#34;taskType&#34;: 1,
            &#34;policyType&#34;: 0,
            &#34;taskFlags&#34;: {
                &#34;disabled&#34;: False
            }
        }

        return task

    @property
    def _restore_sub_task(self):
        &#34;&#34;&#34;Treats the sub task dict as read only property&#34;&#34;&#34;
        sub_task = {
            &#34;subTaskType&#34;: 3,
            &#34;operationType&#34;: 1001
        }

        return sub_task

    @property
    def instance_id(self):
        &#34;&#34;&#34;Treats the instance id as a read-only attribute.&#34;&#34;&#34;
        return self._instance_id

    @property
    def instance_name(self):
        &#34;&#34;&#34;Treats the instance name as a read-only attribute.&#34;&#34;&#34;
        return self._instance_name

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Instance.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation

            Raises:
                SDKException:
                    if there are more than one backupsets in the instance


            Refer `default_browse_options`_ for all the supported options.

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        # do browse operation if there is only one backupset in the instance
        # raise `SDKException` if there is more than one backupset in the instance

        if len(self.backupsets.all_backupsets) == 1:
            backupset_name = list(self.backupsets.all_backupsets.keys())[0]
            temp_backupset_obj = self.backupsets.get(backupset_name)
            return temp_backupset_obj.browse(*args, **kwargs)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

    def find(self, *args, **kwargs):
        &#34;&#34;&#34;Searches a file/folder in the backed up content of the instance,
            and returns all the files matching the filters given.

            Args:
                Dictionary of browse options:
                    Example:

                        find({
                            &#39;file_name&#39;: &#39;*.txt&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        find(
                            file_name=&#39;*.txt&#39;,

                            show_deleted=True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation

            Raises:
                SDKException:
                    if there are more than one backupsets in the instance


            Refer `default_browse_options`_ for all the supported options.

            Additional options supported:
                file_name       (str)   --  Find files with name

                file_size_gt    (int)   --  Find files with size greater than size

                file_size_lt    (int)   --  Find files with size lesser than size

                file_size_et    (int)   --  Find files with size equal to size

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        # do find operation if there is only one backupset in the instance
        # raise `SDKException` if there is more than one backupset in the instance

        if len(self.backupsets.all_backupsets) == 1:
            backupset_name = list(self.backupsets.all_backupsets.keys())[0]
            temp_backupset_obj = self.backupsets.get(backupset_name)
            return temp_backupset_obj.find(*args, **kwargs)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

    def _impersonation_json(self, value):
        &#34;&#34;&#34;setter of Impersonation Json entity of Json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        use_impersonate = bool(value.get(&#34;impersonate_user&#34;))

        self._impersonation_json_ = {
            &#34;useImpersonation&#34;: use_impersonate,
            &#34;user&#34;: {
                &#34;userName&#34;: value.get(&#34;impersonate_user&#34;, &#34;&#34;),
                &#34;password&#34;: value.get(&#34;impersonate_password&#34;, &#34;&#34;)
            }
        }

    def _restore_browse_option_json(self, value):
        &#34;&#34;&#34;setter  the Browse options for restore in Json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if &#34;copy_precedence&#34; in value and value[&#34;copy_precedence&#34;] != 0:
            value[&#34;copy_precedence_applicable&#34;] = True

        time_range_dict = {}
        options = value.get(&#34;advanced_options&#34;) or {}

        if value.get(&#39;from_time&#39;):
            time_range_dict[&#39;fromTimeValue&#39;] = value.get(&#39;from_time&#39;)

        if value.get(&#39;to_time&#39;):
            time_range_dict[&#39;toTimeValue&#39;] = value.get(&#39;to_time&#39;)

        self._browse_restore_json = {
            &#34;listMedia&#34;: False,
            &#34;useExactIndex&#34;: False,
            &#34;noImage&#34;: value.get(&#34;no_image&#34;, False),
            &#34;commCellId&#34;: 2,
            &#34;liveBrowse&#34;: value.get(&#39;live_browse&#39;, False),
            &#34;mediaOption&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentName&#34;: value.get(&#34;media_agent&#34;, None) or &#34;&#34;
                },
                &#34;proxyForSnapClients&#34;: {
                    &#34;clientName&#34;: value.get(&#34;snap_proxy&#34;, None) or value.get(&#34;proxy_client&#34;, None) or &#34;&#34;
                },
                &#34;library&#34;: {},
                &#34;copyPrecedence&#34;: {
                    &#34;copyPrecedenceApplicable&#34;: value.get(&#34;copy_precedence_applicable&#34;, False),
                    &#34;copyPrecedence&#34;: value.get(&#34;copy_precedence&#34;, 0)
                },
                &#34;drivePool&#34;: {}
            },
            &#34;backupset&#34;: {
                &#34;clientName&#34;: self._agent_object._client_object.client_name,
                &#34;appName&#34;: self._agent_object.agent_name
            },
            &#34;timeZone&#34;: {
                &#34;TimeZoneName&#34;: options.get(&#34;timezone&#34;, self._commcell_object.default_timezone)
            },
            &#34;timeRange&#34;: time_range_dict
        }

        if &#34;browse_job_id&#34; in value:
            self._browse_restore_json[&#34;browseJobId&#34;] = value.get(&#34;browse_job_id&#34;, False)
            self._browse_restore_json[&#34;browseJobCommCellId&#34;] = value.get(
                &#34;commcell_id&#34;, self._commcell_object.commcell_id)

        if value.get(&#39;iscsi_server&#39;):

            self._browse_restore_json[&#39;mediaOption&#39;][&#39;iSCSIServer&#39;] = {
                &#39;clientName&#39;: value.get(&#34;iscsi_server&#34;)
            }

        # Add this option to enable restoring of troubleshooting folder
        if value.get(&#34;include_metadata&#34;, False):
            self._browse_restore_json[&#34;includeMetaData&#34;] = True

        if value.get(&#39;cvcBrowse&#39;):
            self._browse_restore_json[&#34;cvcBrowse&#34;] = True

    def _restore_common_opts_json(self, value):
        &#34;&#34;&#34; Method to set commonOpts for restore

        Args:
             value  (dict)  -- restore options dictionary

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        options = value.get(&#34;advanced_options&#34;)

        if not options:
            return

        # taskInfo -&gt; subTasks -&gt; options -&gt; commonOpts
        self._commonopts_restore_json = {
            &#34;jobDescription&#34;: options.get(&#34;job_description&#34;, &#34;&#34;)
        }

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;setter for  the Common options of in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._commonoption_restore_json = {
            &#34;systemStateBackup&#34;: False,
            &#34;clusterDBBackedup&#34;: False,
            &#34;powerRestore&#34;: False,
            &#34;restoreToDisk&#34;: value.get(&#34;restore_to_disk&#34;, False),
            &#34;indexFreeRestore&#34;: value.get(&#34;index_free_restore&#34;, False),
            &#34;offlineMiningRestore&#34;: False,
            &#34;onePassRestore&#34;: False,
            &#34;detectRegularExpression&#34;: True,
            &#34;wildCard&#34;: False,
            &#34;preserveLevel&#34;: value.get(&#34;preserve_level&#34;, 1),
            &#34;restoreToExchange&#34;: False,
            &#34;stripLevel&#34;: 0,
            &#34;skipErrorsAndContinue&#34;: value.get(&#34;skipErrorsAndContinue&#34;, False),
            &#34;restoreACLs&#34;: value.get(&#34;restore_ACL&#34;, value.get(&#34;restore_data_and_acl&#34;, True)),
            &#34;stripLevelType&#34;: value.get(&#34;striplevel_type&#34;, 0),
            &#34;allVersion&#34;: value.get(&#34;all_versions&#34;, False),
            &#34;unconditionalOverwrite&#34;: value.get(&#34;unconditional_overwrite&#34;, False),
            &#34;includeAgedData&#34;: value.get(&#34;include_aged_data&#34;, False),
            &#34;validateOnly&#34;: value.get(&#34;validate_only&#34;, False)
        }

        if value.get(&#39;advanced_options&#39;):
            if value[&#39;advanced_options&#39;].get(&#34;iSeriesObject&#34;):
                ibmi_value = value[&#39;advanced_options&#39;][&#34;iSeriesObject&#34;]
                ibmi_opts = {}
                if ibmi_value.get(&#34;restorePrivateAuthority&#34;):
                    ibmi_opts.update({&#39;restorePrivateAuthority&#39;: ibmi_value.get(&#39;restorePrivateAuthority&#39;)})
                if ibmi_value.get(&#34;restoreSpooledFileData&#34;):
                    ibmi_opts.update({&#39;restoreSpooledFileData&#39;: ibmi_value.get(&#39;restoreSpooledFileData&#39;)})
                if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;):
                    if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;) == &#34;None&#34;:
                        ibmi_opts.update({&#39;iseriesDifferentObjectType&#39;: &#34;0&#34;})
                    if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;) == &#34;*ALL&#34;:
                        ibmi_opts.update({&#39;iseriesDifferentObjectType&#39;: &#34;1&#34;})
                    if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;) == &#34;*COMPATIBLE&#34;:
                        ibmi_opts.update({&#39;iseriesDifferentObjectType&#39;: &#34;2&#34;})
                    if ibmi_value.get(&#34;iseriesDifferentObjectType&#34;) == &#34;OTHER&#34;:
                        ibmi_opts.update({&#39;iseriesDifferentObjectType&#39;: &#34;3&#34;})
                        if ibmi_value.get(&#34;autl&#34;):
                            ibmi_opts.update({&#39;autl&#39;: ibmi_value.get(&#39;autl&#39;)})
                        if ibmi_value.get(&#34;fileLevel&#34;):
                            ibmi_opts.update({&#39;fileLevel&#39;: ibmi_value.get(&#39;fileLevel&#39;)})
                        if ibmi_value.get(&#34;owner&#34;):
                            ibmi_opts.update({&#39;owner&#39;: ibmi_value.get(&#39;owner&#39;)})
                        if ibmi_value.get(&#34;pgp&#34;):
                            ibmi_opts.update({&#39;pgp&#39;: ibmi_value.get(&#39;pgp&#39;)})
                if ibmi_value.get(&#34;forceObjectConversionSelction&#34;):
                    ibmi_opts.update({&#39;forceObjectConversionSelction&#39;: ibmi_value.get(
                        &#39;forceObjectConversionSelction&#39;)})
                if ibmi_value.get(&#34;securityDataParameter&#34;):
                    ibmi_opts.update({&#39;securityDataParameter&#39;: ibmi_value.get(&#39;securityDataParameter&#39;)})
                if ibmi_value.get(&#34;deferId&#34;):
                    ibmi_opts.update({&#39;deferId&#39;: ibmi_value.get(&#39;deferId&#39;)})

                self._commonoption_restore_json[&#39;iSeriesObject&#39;] = ibmi_opts

        if value.get(&#34;instant_clone_options&#34;, {}).get(&#34;post_clone_script&#34;, None):
            self._commonoption_restore_json[&#39;prePostCloneOption&#39;] = {
                &#39;postCloneCmd&#39;: value.get(&#34;instant_clone_options&#34;).get(&#34;post_clone_script&#34;)
            }

        _advance_fs_keys = [&#34;restoreDataInsteadOfStub&#34;,
                            &#34;restoreOnlyStubExists&#34;,
                            &#34;overwriteFiles&#34;,
                            &#34;doNotOverwriteFileOnDisk&#34;,
                            &#34;disableStubRestore&#34;]

        if &#34;fs_options&#34; in value:
            _fs_option_value = value[&#34;fs_options&#34;]
            if _fs_option_value is not None:
                for _key in _advance_fs_keys:
                    if _key in _fs_option_value:
                        self._commonoption_restore_json[_key] = _fs_option_value[_key]

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;setter for  the destination restore option in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if value.get(&#34;proxy_client&#34;) is not None and \
                (self._agent_object.agent_name).upper() == &#34;FILE SYSTEM&#34;:
            self._destination_restore_json = {
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;, True),
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;proxy_client&#34;, &#34;&#34;)
                }
            }
            if value.get(&#39;destination_path&#39;):
                destination_path = value.get(&#34;destination_path&#34;, &#34;&#34;)
                self._destination_restore_json[&#34;destPath&#34;] = [destination_path] if destination_path != &#34;&#34; else []

        # For Index server restore, we need to set proxy client &amp; in-place flag
        elif value.get(&#34;proxy_client&#34;) is not None and \
                (self._agent_object.agent_name).upper() == &#34;BIG DATA APPS&#34; and \
                self.name.upper() == &#34;DYNAMICINDEXINSTANCE&#34;:
            self._destination_restore_json = {
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;, True),
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;proxy_client&#34;, &#34;&#34;)
                }
            }
        else:
            # removed clientId from destClient as VSA Restores fail with it
            self._destination_restore_json = {
                &#34;isLegalHold&#34;: False,
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;, True),
                &#34;destPath&#34;: [value.get(&#34;destination_path&#34;, &#34;&#34;)],
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;client_name&#34;, &#34;&#34;),
                }
            }
            # removing &#39;destPath&#39; if restoring in place
            self._destination_restore_json.pop(&#39;destPath&#39;) if value.get(&#34;in_place&#34;, True) else None

        if value.get(&#34;multinode_restore&#34;, False) or value.get(&#34;no_of_streams&#34;, 1) &gt; 1:
            self._destination_restore_json[&#34;destinationInstance&#34;] = {
                &#34;instanceName&#34;: value.get(&#39;destination_instance&#39;, self.instance_name)
            }
            if value.get(&#39;destination_instance_id&#39;) is not None:
                self._destination_restore_json[&#34;destinationInstance&#34;][&#34;instanceId&#34;] = int(
                    value.get(&#39;destination_instance_id&#39;)
                    )

            self._destination_restore_json[&#34;noOfStreams&#34;] = value.get(&#39;no_of_streams&#39;, 2)

    def _restore_fileoption_json(self, value):
        &#34;&#34;&#34;setter for  the fileoption restore option in restore JSON&#34;&#34;&#34;
        self._fileoption_restore_json = {
            &#34;sourceItem&#34;: value[&#34;instant_clone_options&#34;][&#34;instant_clone_src_path&#34;] if value.get(&#34;instant_clone_options&#34;, None) else value.get(&#34;paths&#34;, []),
            &#34;browseFilters&#34;: value.get(&#34;browse_filters&#34;, [])
        }

        if value.get(&#34;instant_clone_options&#34;, None):
            self._fileoption_restore_json[&#34;fsCloneOptions&#34;] = {
                &#34;reservationTime&#34;: value[&#34;instant_clone_options&#34;][&#34;reservation_time&#34;],
                &#34;cloneMountPath&#34;: value[&#34;instant_clone_options&#34;][&#34;clone_mount_path&#34;]}

            if value[&#34;instant_clone_options&#34;].get(&#34;clone_cleanup_script&#34;, None):
                self._fileoption_restore_json[&#34;fsCloneOptions&#34;][&#34;cloneCleanupOptions&#34;] = {
                    &#34;cleanupScriptPath&#34;: value.get(&#34;instant_clone_options&#34;).get(&#34;clone_cleanup_script&#34;)
                }
        
        if value.get(&#39;advanced_options&#39;):
            if value[&#39;advanced_options&#39;].get(&#34;mapFiles&#34;):
                map_file = value[&#39;advanced_options&#39;][&#34;mapFiles&#34;]
                self._fileoption_restore_json[&#34;mapFiles&#34;] = {
                    &#34;useMapFile&#34;: True,
                    &#34;mapFilePath&#34;: map_file.get(&#39;mapFilePath&#39;, &#34;&#34;),
                    &#34;restoreUnmappedFiles&#34;: map_file.get(&#34;restoreUnmappedFiles&#34;, &#34;&#34;),
                    &#34;renameFilesSuffix&#34;: map_file.get(&#34;renameFilesSuffix&#34;, &#34;&#34;),
                    &#34;mapFileClient&#34;: {&#34;clientName&#34;: map_file.get(&#34;clientName&#34;, &#34;&#34;)}
                }

    def _restore_bigdataapps_option_json(self, value):
        &#34;&#34;&#34;setter for Big Data Apps restore option in restore JSON&#34;&#34;&#34;
        request_json = self._restore_json(restore_option=value)

        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;subclientId&#34;] = value.get(
            &#34;subclient_id&#34;, -1)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = value.get(
            &#34;backupset_name&#34;)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = value.get(
            &#34;_type_&#34;)

        distributed_restore_json = {
            &#34;distributedRestore&#34;: True,
        }

        if value.get(&#34;cassandra_restore&#34;, False):
            distributed_restore_json[&#34;cassandraRestoreOptions&#34;] = {
                &#34;outofPlaceRestore&#34;: value.get(&#34;outofPlaceRestore&#34;, False),
                &#34;runStageFreeRestore&#34;: value.get(&#34;runStageFreeRestore&#34;, False),
                &#34;recover&#34;: value.get(&#34;recover&#34;, False),
                &#34;replaceDeadNode&#34;: value.get(&#34;replaceDeadNode&#34;, False),
                &#34;stagingLocation&#34;: value.get(&#34;stagingLocation&#34;),
                &#34;useSSTableLoader&#34;: value.get(&#34;useSSTableLoader&#34;, False),
                &#34;runLogRestore&#34;: value.get(&#34;runLogRestore&#34;, False),
                &#34;nodeMap&#34;: value.get(&#34;nodeMap&#34;, []),
                &#34;dockerNodeMap&#34;: value.get(&#34;dockerNodeMap&#34;, []),
                &#34;DBRestore&#34;: value.get(&#34;DBRestore&#34;, False),
                &#34;truncateTables&#34;: value.get(&#34;truncateTables&#34;, False)
            }

        if value.get(&#34;cockroachdb_restore&#34;, False):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = {
                &#34;instanceType&#34;: &#34;AMAZON_S3&#34;}
            distributed_restore_json[&#34;clientType&#34;] = value.get(&#34;client_type&#34;)
            distributed_restore_json[&#34;isMultiNodeRestore&#34;] = True
            distributed_restore_json[&#34;noSQLGenericRestoreOptions&#34;] = {
                &#34;pointInTime&#34;: {
                    &#34;time&#34;: 0
                },
                &#34;restoreCluster&#34;: False,
                &#34;tableMap&#34;: [
                    {
                        &#34;fromTable&#34;: value.get(&#34;fromtable&#34;),
                        &#34;toTable&#34;: value.get(&#34;totable&#34;)
                    }
                ]
            }

            access_nodes = []
            for node in value.get(&#34;accessnodes&#34;):
                client_object = self._commcell_object.clients.get(node)
                client_object._get_client_properties()
                client_id = int(client_object.client_id)
                access_node = {
                    &#34;clientId&#34;: client_id,
                    &#34;clientName&#34;: client_object.client_name
                }
                access_nodes.append(access_node)

            distributed_restore_json[&#34;dataAccessNodes&#34;] = {
                &#34;dataAccessNodes&#34;: access_nodes
            }

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;distributedAppsRestoreOptions&#34;] = distributed_restore_json
        return request_json

    def _restore_virtual_rst_option_json(self, value):
        &#34;&#34;&#34;setter for the virtualServer restore option in restore JSON&#34;&#34;&#34;
        self._virtualserver_restore_json = {
            &#34;isFileBrowse&#34;: value.get(&#34;fileBrowse&#34;)
        }

    def _restore_volume_rst_option_json(self, value):
        &#34;&#34;&#34;setter for the volumeRst restore option in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._volume_restore_json = {
            &#34;volumeLeveRestore&#34;: value.get(&#34;volume_level_restore&#34;, False),
            &#34;volumeLevelRestoreType&#34;: value.get(&#34;volume_level_restore_type&#34;, &#34;PHYSICAL_VOLUME&#34;)
        }

    def _sync_restore_option_json(self, value):
        &#34;&#34;&#34;setter for the Sync. Restore option in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._sync_restore_json = {
            &#34;PreserveModifiedFiles&#34;: True,
            &#34;isMigration&#34;: True,
            &#34;isSyncRestore&#34;: True,
            &#34;paths&#34;: value.get(&#34;sync_option_paths&#34;)
        }

    @property
    def _restore_sharepoint_json(self):
        &#34;&#34;&#34;getter for Sharepoint restore option in JSON. it is read only attribute&#34;&#34;&#34;
        _sharepoint_restore_json = {
            &#34;is90OrUpgradedClient&#34;: False
        }

        return _sharepoint_restore_json

    @property
    def _json_task(self):
        &#34;&#34;&#34;getter for the task information in JSON&#34;&#34;&#34;

        _taks_option_json = {
            &#34;initiatedFrom&#34;: 2,
            &#34;taskType&#34;: 1,
            &#34;policyType&#34;: 0,
            &#34;taskFlags&#34;: {
                &#34;disabled&#34;: False
            }
        }

        return _taks_option_json

    @property
    def _json_restore_subtask(self):
        &#34;&#34;&#34;getter for the subtask in restore JSON . It is read only attribute&#34;&#34;&#34;

        _subtask_restore_json = {
            &#34;subTaskType&#34;: 3,
            &#34;operationType&#34;: 1001
        }

        return _subtask_restore_json

    @property
    def _json_restore_by_job_subtask(self):
        &#34;&#34;&#34;getter for the subtast in restore by job JSON&#34;&#34;&#34;

        _subtask_restore_by_job_json = {
            &#34;subTaskType&#34;: 3,
            &#34;operationType&#34;: 1005
        }

        return _subtask_restore_by_job_json

    @property
    def _json_backup_subtasks(self):
        &#34;&#34;&#34;getter for the subtask in restore JSON . It is read only attribute&#34;&#34;&#34;

        _backup_subtask = {
            &#34;subTaskType&#34;: 2,
            &#34;operationType&#34;: 2
        }

        return _backup_subtask

    @property
    def credentials(self):
        &#34;&#34;&#34;Getter for the instance credentials

            Returns:
                str - name of the credential associated with the instance

        &#34;&#34;&#34;
        return self._properties.get(&#39;credentialEntity&#39;, {}).get(&#39;credentialName&#39;, &#39;&#39;)

    @credentials.setter
    def credentials(self, credential_name):
        &#34;&#34;&#34;Setter for the instance credentials

            Args:
                credential_name (str)  --  name of the credential to be associated with the instance

        &#34;&#34;&#34;
        properties = self._properties
        credential_id = self._commcell_object.credentials.get(credential_name).credential_id
        credential_json = {
            &#34;credentialId&#34;: credential_id,
            &#34;credentialName&#34;: credential_name
        }
        properties[&#39;credentialEntity&#39;] = credential_json
        self.update_properties(properties)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Instance.&#34;&#34;&#34;
        self._get_instance_properties()
        self._backupsets = None
        self._subclients = None</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.aadinstance.AzureAdInstance" href="instances/aadinstance.html#cvpysdk.instances.aadinstance.AzureAdInstance">AzureAdInstance</a></li>
<li><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance" href="instances/bigdataappsinstance.html#cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance">BigDataAppsInstance</a></li>
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="instances/cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instances.db2instance.DB2Instance" href="instances/db2instance.html#cvpysdk.instances.db2instance.DB2Instance">DB2Instance</a></li>
<li><a title="cvpysdk.instances.dbinstance.DatabaseInstance" href="instances/dbinstance.html#cvpysdk.instances.dbinstance.DatabaseInstance">DatabaseInstance</a></li>
<li><a title="cvpysdk.instances.hanainstance.SAPHANAInstance" href="instances/hanainstance.html#cvpysdk.instances.hanainstance.SAPHANAInstance">SAPHANAInstance</a></li>
<li><a title="cvpysdk.instances.informixinstance.InformixInstance" href="instances/informixinstance.html#cvpysdk.instances.informixinstance.InformixInstance">InformixInstance</a></li>
<li><a title="cvpysdk.instances.lndbinstance.LNDBInstance" href="instances/lndbinstance.html#cvpysdk.instances.lndbinstance.LNDBInstance">LNDBInstance</a></li>
<li><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance" href="instances/lotusnotes/lninstance.html#cvpysdk.instances.lotusnotes.lninstance.LNInstance">LNInstance</a></li>
<li><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance" href="instances/mysqlinstance.html#cvpysdk.instances.mysqlinstance.MYSQLInstance">MYSQLInstance</a></li>
<li><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance" href="instances/postgresinstance.html#cvpysdk.instances.postgresinstance.PostgreSQLInstance">PostgreSQLInstance</a></li>
<li><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance" href="instances/saporacleinstance.html#cvpysdk.instances.saporacleinstance.SAPOracleInstance">SAPOracleInstance</a></li>
<li><a title="cvpysdk.instances.sharepointinstance.SharepointInstance" href="instances/sharepointinstance.html#cvpysdk.instances.sharepointinstance.SharepointInstance">SharepointInstance</a></li>
<li><a title="cvpysdk.instances.sqlinstance.SQLServerInstance" href="instances/sqlinstance.html#cvpysdk.instances.sqlinstance.SQLServerInstance">SQLServerInstance</a></li>
<li><a title="cvpysdk.instances.vminstance.VMInstance" href="instances/vminstance.html#cvpysdk.instances.vminstance.VMInstance">VMInstance</a></li>
<li><a title="cvpysdk.instances.vsinstance.VirtualServerInstance" href="instances/vsinstance.html#cvpysdk.instances.vsinstance.VirtualServerInstance">VirtualServerInstance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instance.Instance.backupsets"><code class="name">var <span class="ident">backupsets</span></code></dt>
<dd>
<div class="desc"><p>Returns the instance of the Backupsets class representing the list of Backupsets
installed / configured on the Client for the selected Instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L2766-L2775" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backupsets(self):
    &#34;&#34;&#34;Returns the instance of the Backupsets class representing the list of Backupsets
    installed / configured on the Client for the selected Instance.
    &#34;&#34;&#34;
    if self._backupsets is None:
        from .backupset import Backupsets
        self._backupsets = Backupsets(self)

    return self._backupsets</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instance.credentials"><code class="name">var <span class="ident">credentials</span></code></dt>
<dd>
<div class="desc"><p>Getter for the instance credentials</p>
<h2 id="returns">Returns</h2>
<p>str - name of the credential associated with the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L3369-L3377" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def credentials(self):
    &#34;&#34;&#34;Getter for the instance credentials

        Returns:
            str - name of the credential associated with the instance

    &#34;&#34;&#34;
    return self._properties.get(&#39;credentialEntity&#39;, {}).get(&#39;credentialName&#39;, &#39;&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instance.instance_id"><code class="name">var <span class="ident">instance_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the instance id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L2821-L2824" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def instance_id(self):
    &#34;&#34;&#34;Treats the instance id as a read-only attribute.&#34;&#34;&#34;
    return self._instance_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instance.instance_name"><code class="name">var <span class="ident">instance_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the instance name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L2826-L2829" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def instance_name(self):
    &#34;&#34;&#34;Treats the instance name as a read-only attribute.&#34;&#34;&#34;
    return self._instance_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instance.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Returns the Instance Display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L2792-L2795" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Returns the Instance Display name&#34;&#34;&#34;
    return self._properties[&#34;instance&#34;][&#34;instanceName&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instance.properties"><code class="name">var <span class="ident">properties</span></code></dt>
<dd>
<div class="desc"><p>Returns the instance properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L2787-L2790" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def properties(self):
    &#34;&#34;&#34;Returns the instance properties&#34;&#34;&#34;
    return copy.deepcopy(self._properties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instance.subclients"><code class="name">var <span class="ident">subclients</span></code></dt>
<dd>
<div class="desc"><p>Returns the instance of the Subclients class representing the list of Subclients
installed / configured on the Client for the selected Instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L2777-L2785" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclients(self):
    &#34;&#34;&#34;Returns the instance of the Subclients class representing the list of Subclients
    installed / configured on the Client for the selected Instance.
    &#34;&#34;&#34;
    if self._subclients is None:
        self._subclients = Subclients(self)

    return self._subclients</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instance.Instance.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the content of the Instance.</p>
<h2 id="args">Args</h2>
<p>Dictionary of browse options:
Example:</p>
<pre><code>    browse({
        'path': 'c:\\hello',

        'show_deleted': True,

        'from_time': '2014-04-20 12:00:00',

        'to_time': '2016-04-21 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of browse options:
Example:</p>
<pre><code>    browse(
        path='c:\hello',

        show_deleted=True,

        from_time='2014-04-20 12:00:00',

        to_time='2016-04-21 12:00:00'
    )
</code></pre>
<h2 id="returns">Returns</h2>
<p>(list, dict)
list
-
List of only the file, folder paths from the browse response</p>
<pre><code>dict    -   Dictionary of all the paths with additional metadata retrieved
from browse operation
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if there are more than one backupsets in the instance
Refer <code>default_browse_options</code>_ for all the supported options.</p>
<p>.. _default_browse_options: <a href="https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565">https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565</a></p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L2831-L2887" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, *args, **kwargs):
    &#34;&#34;&#34;Browses the content of the Instance.

        Args:
            Dictionary of browse options:
                Example:

                    browse({
                        &#39;path&#39;: &#39;c:\\\\hello&#39;,

                        &#39;show_deleted&#39;: True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of browse options:
                Example:

                    browse(
                        path=&#39;c:\\hello&#39;,

                        show_deleted=True,

                        from_time=&#39;2014-04-20 12:00:00&#39;,

                        to_time=&#39;2016-04-21 12:00:00&#39;
                    )

        Returns:
            (list, dict)
                list    -   List of only the file, folder paths from the browse response

                dict    -   Dictionary of all the paths with additional metadata retrieved
                from browse operation

        Raises:
            SDKException:
                if there are more than one backupsets in the instance


        Refer `default_browse_options`_ for all the supported options.

        .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

    &#34;&#34;&#34;
    # do browse operation if there is only one backupset in the instance
    # raise `SDKException` if there is more than one backupset in the instance

    if len(self.backupsets.all_backupsets) == 1:
        backupset_name = list(self.backupsets.all_backupsets.keys())[0]
        temp_backupset_obj = self.backupsets.get(backupset_name)
        return temp_backupset_obj.browse(*args, **kwargs)
    else:
        raise SDKException(&#39;Instance&#39;, &#39;104&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instance.find"><code class="name flex">
<span>def <span class="ident">find</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches a file/folder in the backed up content of the instance,
and returns all the files matching the filters given.</p>
<h2 id="args">Args</h2>
<p>Dictionary of browse options:
Example:</p>
<pre><code>    find({
        'file_name': '*.txt',

        'show_deleted': True,

        'from_time': '2014-04-20 12:00:00',

        'to_time': '2016-04-31 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of browse options:
Example:</p>
<pre><code>    find(
        file_name='*.txt',

        show_deleted=True,

        'from_time': '2014-04-20 12:00:00',

        to_time='2016-04-31 12:00:00'
    )
</code></pre>
<h2 id="returns">Returns</h2>
<p>(list, dict)
list
-
List of only the file, folder paths from the browse response</p>
<pre><code>dict    -   Dictionary of all the paths with additional metadata retrieved
from browse operation
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if there are more than one backupsets in the instance
Refer <code>default_browse_options</code>_ for all the supported options.</p>
<p>Additional options supported:
file_name
(str)
&ndash;
Find files with name</p>
<pre><code>file_size_gt    (int)   --  Find files with size greater than size

file_size_lt    (int)   --  Find files with size lesser than size

file_size_et    (int)   --  Find files with size equal to size
</code></pre>
<p>.. _default_browse_options: <a href="https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565">https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565</a></p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L2889-L2955" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def find(self, *args, **kwargs):
    &#34;&#34;&#34;Searches a file/folder in the backed up content of the instance,
        and returns all the files matching the filters given.

        Args:
            Dictionary of browse options:
                Example:

                    find({
                        &#39;file_name&#39;: &#39;*.txt&#39;,

                        &#39;show_deleted&#39;: True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of browse options:
                Example:

                    find(
                        file_name=&#39;*.txt&#39;,

                        show_deleted=True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        to_time=&#39;2016-04-31 12:00:00&#39;
                    )

        Returns:
            (list, dict)
                list    -   List of only the file, folder paths from the browse response

                dict    -   Dictionary of all the paths with additional metadata retrieved
                from browse operation

        Raises:
            SDKException:
                if there are more than one backupsets in the instance


        Refer `default_browse_options`_ for all the supported options.

        Additional options supported:
            file_name       (str)   --  Find files with name

            file_size_gt    (int)   --  Find files with size greater than size

            file_size_lt    (int)   --  Find files with size lesser than size

            file_size_et    (int)   --  Find files with size equal to size

        .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

    &#34;&#34;&#34;
    # do find operation if there is only one backupset in the instance
    # raise `SDKException` if there is more than one backupset in the instance

    if len(self.backupsets.all_backupsets) == 1:
        backupset_name = list(self.backupsets.all_backupsets.keys())[0]
        temp_backupset_obj = self.backupsets.get(backupset_name)
        return temp_backupset_obj.find(*args, **kwargs)
    else:
        raise SDKException(&#39;Instance&#39;, &#39;104&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instance.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the Instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L3396-L3400" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the Instance.&#34;&#34;&#34;
    self._get_instance_properties()
    self._backupsets = None
    self._subclients = None</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instance.update_properties"><code class="name flex">
<span>def <span class="ident">update_properties</span></span>(<span>self, properties_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the instance properties</p>
<pre><code>Args:
    properties_dict (dict)  --  instance properties dict which is to be updated

Returns:
    None

Raises:
    SDKException:
        if failed to add

        if response is empty

        if response code is not as expected
</code></pre>
<p><strong>Note</strong> self.properties can be used to get a deep copy of all the properties, modify the properties which you
need to change and use the update_properties method to set the properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L2736-L2764" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_properties(self, properties_dict):
    &#34;&#34;&#34;Updates the instance properties

        Args:
            properties_dict (dict)  --  instance properties dict which is to be updated

        Returns:
            None

        Raises:
            SDKException:
                if failed to add

                if response is empty

                if response code is not as expected

    **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
    need to change and use the update_properties method to set the properties

    &#34;&#34;&#34;
    request_json = {
        &#34;instanceProperties&#34;: {

        }
    }

    request_json[&#39;instanceProperties&#39;].update(properties_dict)
    self._process_update_request(request_json)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.instance.Instances"><code class="flex name class">
<span>class <span class="ident">Instances</span></span>
<span>(</span><span>agent_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the instances associated with a client.</p>
<p>Initialize object of the Instances class.</p>
<h2 id="args">Args</h2>
<p>agent_object (object)
&ndash;
instance of the Agent class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instances class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L186-L1822" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Instances(object):
    &#34;&#34;&#34;Class for getting all the instances associated with a client.&#34;&#34;&#34;

    def __init__(self, agent_object):
        &#34;&#34;&#34;Initialize object of the Instances class.

            Args:
                agent_object (object)  --  instance of the Agent class

            Returns:
                object - instance of the Instances class
        &#34;&#34;&#34;
        self._agent_object = agent_object
        self._client_object = self._agent_object._client_object

        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._INSTANCES = self._services[&#39;GET_ALL_INSTANCES&#39;] % (
            self._client_object.client_id
        )

        self._general_properties = None
        self._instance_properties = None
        self._instances = None
        self._vs_instance_type_dict = {}
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all instances of the agent of a client.

            Returns:
                str - string of all the instances of an agent of a client
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;S. No.&#39;, &#39;Instance&#39;, &#39;Agent&#39;, &#39;Client&#39;
        )

        for index, instance in enumerate(self._instances):
            sub_str = &#39;{:^5}\t{:20}\t{:20}\t{:20}\n&#39;.format(
                index + 1,
                instance,
                self._agent_object.agent_name,
                self._client_object.client_name
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Instances class.&#34;&#34;&#34;
        return &#34;Instances class instance for Agent: &#39;{0}&#39;&#34;.format(self._agent_object.agent_name)

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the instances associated to the Agent.&#34;&#34;&#34;
        return len(self.all_instances)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the instance for the given instance ID or
            the details of the instance for given instance Name.

            Args:
                value   (str / int)     --  Name or ID of the instance

            Returns:
                str     -   name of the instance, if the instance id was given

                dict    -   dict of details of the instance, if instance name was given

            Raises:
                IndexError:
                    no instance exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_instances:
            return self.all_instances[value]
        else:
            try:
                return list(
                    filter(lambda x: x[1] == value, self.all_instances.items())
                )[0][0]
            except IndexError:
                raise IndexError(&#39;No instance exists with the given Name / Id&#39;)

    def _get_instances(self):
        &#34;&#34;&#34;Gets all the instances associated to the agent specified by agent_object.

            Returns:
                dict - consists of all instances of the agent
                    {
                         &#34;instance1_name&#34;: instance1_id,
                         &#34;instance2_name&#34;: instance2_id
                    }

            Raises:
                SDKException:
                    if failed to get instances

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if &#39;file system&#39; in self._agent_object.agent_name:
            return_dict = {
                &#39;defaultinstancename&#39;: 1
            }
            return return_dict

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._INSTANCES)

        if flag:
            if response.json():
                if &#39;instanceProperties&#39; in response.json():
                    return_dict = {}

                    instance_properties = response.json()[&#39;instanceProperties&#39;]
                    for dictionary in instance_properties:

                        agent = dictionary[&#39;instance&#39;][&#39;appName&#39;].lower()

                        if (self._agent_object.agent_name in agent) or (&#39;mariadb&#39; in agent and &#39;mysql&#39; in self._agent_object.agent_name):
                            temp_name = dictionary[&#39;instance&#39;][&#39;instanceName&#39;].lower()
                            temp_id = str(dictionary[&#39;instance&#39;][&#39;instanceId&#39;]).lower()
                            return_dict[temp_name] = temp_id

                        if &#39;vsInstanceType&#39; in dictionary.get(&#39;virtualServerInstance&#39;, &#39;&#39;):
                            self._vs_instance_type_dict[str(dictionary[&#39;instance&#39;][&#39;instanceId&#39;])] = dictionary[
                                &#34;virtualServerInstance&#34;][&#34;vsInstanceType&#34;]

                    return return_dict
                elif &#39;errors&#39; in response.json():
                    error = response.json()[&#39;errors&#39;][0]
                    error_string = error[&#39;errorString&#39;]
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, error_string)
                else:
                    return {}
            else:
                return {}
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def all_instances(self):
        &#34;&#34;&#34;Returns dict of all the instances associated with the agent

            dict - consists of all instances of the agent
                    {
                         &#34;instance1_name&#34;: instance1_id,
                         &#34;instance2_name&#34;: instance2_id
                    }

        &#34;&#34;&#34;
        return self._instances

    def has_instance(self, instance_name):
        &#34;&#34;&#34;Checks if a instance exists for the agent with the input instance name.

            Args:
                instance_name (str)  --  name of the instance

            Returns:
                bool - boolean output whether the instance exists for the agent or not

            Raises:
                SDKException:
                    if type of the instance name argument is not string
        &#34;&#34;&#34;
        if not isinstance(instance_name, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        return self._instances and instance_name.lower() in self._instances

    def get(self, instance_name):
        &#34;&#34;&#34;Returns a instance object of the specified instance name.

            Args:
                instance_name (str/int)  --  name or ID of the instance

            Returns:
                object - instance of the Instance class for the given instance name

            Raises:
                SDKException:
                    if type of the instance name argument is not string or Int

                    if no instance exists with the given name
        &#34;&#34;&#34;
        if isinstance(instance_name, str):
            instance_name = instance_name.lower()

            if self.has_instance(instance_name):
                return Instance(self._agent_object, instance_name, self._instances[instance_name])

            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;No instance exists with name: &#34;{0}&#34;&#39;.format(instance_name)
            )
        elif isinstance(instance_name, int):
            instance_name = str(instance_name)
            instance_name = [name for name, instance_id in self.all_instances.items() if instance_name == instance_id]

            if instance_name:
                return self.get(instance_name[0])
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists with the given ID: {0}&#39;.format(instance_name))

        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    def _process_add_response(self, request_json):
        &#34;&#34;&#34;Runs the Intance Add API with the request JSON provided,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;ADD_INSTANCE&#39;], request_json)
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to create instance\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                    else:
                        # initialize the instances again
                        # so the instance object has all the instances
                        instance_name = response.json(
                        )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                        self.refresh()
                        return self.get(instance_name)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create instance\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_informix_instance(self, informix_options):
        &#34;&#34;&#34;Adds new Informix Instance to given Client
            Args:
                Dictionary of informix instance creation options:
                    Example:
                       informix_options = {
                            &#39;instance_name&#39;: &#34;&#34;,
                            &#39;onconfig_file&#39;: &#34;&#34;,
                            &#39;sql_host_file&#39;: &#34;&#34;,
                            &#39;informix_dir&#39;: &#34;&#34;,
                            &#39;user_name&#39;: &#34;&#34;,
                            &#39;domain_name&#39;: &#34;&#34;,
                            &#39;password&#39;: &#34;&#34;,
                            &#39;storage_policy&#39;: &#34;&#34;,
                            &#39;description&#39;:&#39;created from automation&#39;
                        }

            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if None value in informix options

                    if Informix instance with same name already exists

                    if given storage policy does not exists in commcell
        &#34;&#34;&#34;
        if None in informix_options.values():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;One of the informix parameter is None so cannot proceed with instance creation&#34;)

        if self.has_instance(informix_options[&#34;instance_name&#34;]):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    informix_options[&#34;instance_name&#34;])
            )

        if not self._commcell_object.storage_policies.has_policy(
                informix_options[&#34;storage_policy&#34;]):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    informix_options[&#34;storage_policy&#34;])
            )
        password = b64encode(informix_options[&#34;password&#34;].encode()).decode()

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;description&#34;: informix_options[&#39;description&#39;],
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;instanceName&#34;: informix_options[&#34;instance_name&#34;],
                    &#34;appName&#34;: &#34;Informix Database&#34;
                },
                &#34;informixInstance&#34;: {
                    &#34;onConfigFile&#34;: informix_options[&#34;onconfig_file&#34;],
                    &#34;sqlHostfile&#34;: informix_options[&#34;sql_host_file&#34;],
                    &#34;informixDir&#34;: informix_options[&#34;informix_dir&#34;],
                    &#34;informixUser&#34;: {
                        &#34;password&#34;: password,
                        &#34;domainName&#34;: informix_options[&#34;domain_name&#34;],
                        &#34;userName&#34;: informix_options[&#34;user_name&#34;]
                    },
                    &#34;informixStorageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: informix_options[&#34;storage_policy&#34;]
                        },
                        &#34;deDuplicationOptions&#34;: {},
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: informix_options[&#34;storage_policy&#34;]
                        },
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: informix_options[&#34;storage_policy&#34;]
                        }
                    }
                }
            }
        }

        add_instance = self._commcell_object._services[&#39;ADD_INSTANCE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, add_instance, request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;].get(&#39;errorCode&#39;)

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;].get(&#39;errorString&#39;)
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    if &#39;entity&#39; in response.json()[&#39;response&#39;]:
                        self.refresh()
                        return self.get(response.json()[&#39;response&#39;][&#39;entity&#39;].get(&#39;instanceName&#39;))
                    else:
                        raise SDKException(
                            &#39;Instance&#39;,
                            &#39;102&#39;,
                            &#39;Unable to get instance name and id&#39;
                        )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add_sap_hana_instance(self, **kwargs):
        &#34;&#34;&#34;Adds new sap hana instance to given client
            Args:
                sid             (str)   -- Database SID
                hana_client_name(str)   --  Client where the hana server exists
                db_user_name   (str)    -- postgres user name
                db_password    (str)    -- DB password
                storage_policy  (str)   --  Storage Policy name
            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if None value in sap hana options

                    if sap hana instance with same name already exists

                    if given storage policy does not exists in commcell
        &#34;&#34;&#34;

        if self.has_instance(kwargs.get(&#39;sid&#39;, &#39;&#39;)):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    kwargs.get(&#39;sid&#39;, &#39;&#39;))
            )
        password = b64encode(kwargs.get(&#34;db_password&#34;, &#34;&#34;).encode()).decode()
        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: kwargs.get(&#39;sid&#39;, &#39;&#39;),
                    &#34;applicationId&#34;: 135,
                },
                &#34;saphanaInstance&#34;: {
                    &#34;dbInstanceNumber&#34;: &#34;00&#34;,
                    &#34;hdbsqlLocationDirectory&#34;: f&#34;/usr/sap/{kwargs.get(&#39;sid&#39;, &#39;&#39;)}/HDB00/exe&#34;,
                    &#34;SAPHANAUser&#34;: {
                        &#34;userName&#34;: f&#34;{kwargs.get(&#39;sid&#39;, &#39;&#39;).lower()}adm&#34;
                    },
                    &#34;dbUser&#34;: {
                        &#34;userName&#34;: kwargs.get(&#34;db_user_name&#34;, &#34;&#34;),
                        &#34;password&#34;: password
                    },
                    &#34;saphanaStorageDevice&#34;: {
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: kwargs.get(&#34;storage_policy&#34;, &#34;&#34;)
                        },
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: kwargs.get(&#34;storage_policy&#34;, &#34;&#34;)
                        }
                    },
                    &#34;DBInstances&#34;: [
                        {
                            &#34;clientName&#34;: kwargs.get(&#34;hana_client_name&#34;, &#34;&#34;)
                        }
                    ]

                }
            }
        }
        self._process_add_response(request_json)

    def delete(self, instance_name):
        &#34;&#34;&#34;Deletes the instance specified by the instance_name from the agent.

            Args:
                instance_name (str)  --  name of the instance to remove from the agent

            Raises:
                SDKException:
                    if type of the instance name argument is not string

                    if failed to delete instance

                    if response is empty

                    if response is not success

                    if no instance exists with the given name
        &#34;&#34;&#34;
        if not isinstance(instance_name, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        else:
            instance_name = instance_name.lower()

        if self.has_instance(instance_name):
            delete_instance_service = self._commcell_object._services[&#39;INSTANCE&#39;] % (
                self._instances.get(instance_name)
            )

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;DELETE&#39;, delete_instance_service
            )

            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response_value = response.json()[&#39;response&#39;][0]
                        error_code = str(response_value.get(&#39;errorCode&#39;))
                        error_message = None

                        if &#39;errorString&#39; in response_value:
                            error_message = response_value[&#39;errorString&#39;]

                        if error_message:
                            o_str = &#39;Failed to delete instance\nError: &#34;{0}&#34;&#39;
                            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(error_message))
                        else:
                            if error_code == &#39;0&#39;:
                                # initialize the instances again
                                # so the instance object has all the instances
                                self.refresh()
                            else:
                                o_str = (&#39;Failed to delete instance with Error Code: &#34;{0}&#34;\n&#39;
                                         &#39;Please check the documentation for &#39;
                                         &#39;more details on the error&#39;)
                                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(error_code))
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists with name: {0}&#39;.format(instance_name)
            )

    def add_sybase_instance(self, sybase_options):
        &#34;&#34;&#34;
            Method to Add new Sybase Instance to given Client
            Args:
                Dictionary of sybase instance creation options:
                    Example:
                       sybase_options = {
                            &#39;instance_name&#39;: &#39;&#39;,
                            &#39;sybase_ocs&#39;: &#39;&#39;,
                            &#39;sybase_ase&#39;: &#39;&#39;,
                            &#39;backup_server&#39;: &#39;&#39;,
                            &#39;sybase_home&#39;: &#39;&#39;,
                            &#39;config_file&#39;: &#39;&#39;,
                            &#39;enable_auto_discovery&#39;: True,
                            &#39;shared_memory_directory&#39;: &#39;&#39;,
                            &#39;storage_policy&#39;: &#39;&#39;,
                            &#39;sa_username&#39;: &#39;&#39;,
                            &#39;sa_password&#39;: &#39;&#39;,
                            &#39;localadmin_username&#39;: &#39;&#39;,
                            &#39;localadmin_password&#39;: &#39;&#39;,
                            &#39;masterkey_password&#39;:&#39;&#39;
                        }
            Raises:
                SDKException:
                    if None value in sybase options

                    if Sybase instance with same name already exists

                    if given storage policy does not exists in commcell

        &#34;&#34;&#34;

        if None in sybase_options.values():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;One of the sybase parameter is None so cannot proceed with instance creation&#34;)

        if self.has_instance(sybase_options[&#34;instance_name&#34;]):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    sybase_options[&#34;instance_name&#34;])
            )

        if not self._commcell_object.storage_policies.has_policy(sybase_options[&#34;storage_policy&#34;]):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    sybase_options[&#34;storage_policy&#34;])
            )

        # encodes the plain text password using base64 encoding
        sa_password = b64encode(sybase_options[&#34;sa_password&#34;].encode()).decode()

        enable_auto_discovery = sybase_options[&#34;enable_auto_discovery&#34;]

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: &#34;Sybase&#34;,
                    &#34;instanceName&#34;: sybase_options[&#34;instance_name&#34;],
                    &#34;_type_&#34;: 5,
                    &#34;applicationId&#34;: 5
                },
                &#34;planEntity&#34;: {
                    &#34;planName&#34;: sybase_options[&#34;storage_policy&#34;]
                },
                &#34;sybaseInstance&#34;: {
                    &#34;sybaseOCS&#34;: sybase_options[&#34;sybase_ocs&#34;],
                    &#34;sybaseBlockSize&#34;: 65536,
                    &#34;backupServer&#34;: sybase_options[&#34;backup_server&#34;],
                    &#34;sybaseHome&#34;: sybase_options[&#34;sybase_home&#34;],
                    &#34;sybaseASE&#34;: sybase_options[&#34;sybase_ase&#34;],
                    &#34;configFile&#34;: sybase_options[&#34;config_file&#34;],
                    &#34;enableAutoDiscovery&#34;: enable_auto_discovery,
                    &#34;sharedMemoryDirectory&#34;: sybase_options[&#34;shared_memory_directory&#34;],
                    &#34;saUser&#34;: {&#34;password&#34;: sa_password, &#34;userName&#34;: sybase_options[&#34;sa_username&#34;]},
                    &#34;localAdministrator&#34;: {
                        &#34;password&#34;: sybase_options[&#34;localadmin_password&#34;],
                        &#34;userName&#34;: sybase_options[&#34;localadmin_username&#34;]
                    }
                }
            }
        }
        if &#34;masterkey_password&#34; in sybase_options.keys():
            masterkey_password = b64encode(sybase_options[&#34;masterkey_password&#34;].encode()).decode()
            request_json[&#34;instanceProperties&#34;][&#34;sybaseInstance&#34;][&#34;masterKeyPwd&#34;]=masterkey_password

        if &#34;localadmin_password&#34; in sybase_options.keys():
            localadmin_password = b64encode(sybase_options[&#34;localadmin_password&#34;].encode()).decode()
            request_json[&#39;instanceProperties&#39;][&#39;sybaseInstance&#39;][&#39;localAdministrator&#39;][&#39;password&#39;] = localadmin_password

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ADD_INSTANCE&#39;], request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    instance_name = response.json(
                    )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                    instance_id = response.json(
                    )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceId&#39;]
                    agent_name = self._agent_object.agent_name
                    self.refresh()
                    return self.get(instance_name)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_db2_instance(self, db2_options):
        &#34;&#34;&#34;
            Method to Add new Db2 Instance to given Client
                Args:
                        Dictionary of db2 instance creation options:
                            Example:
                               db2_options = {
                                    &#39;instance_name&#39;: &#39;db2inst1&#39;,
                                    &#39;data_storage_policy&#39;: &#39;data_sp&#39;,
                                    &#39;log_storage_policy&#39;: &#39;log_sp&#39;,
                                    &#39;command_storage_policy&#39;: &#39;cmd_sp&#39;,
                                    &#39;home_directory&#39;:&#39;/home/<USER>
                                    &#39;password&#39;:&#39;#####&#39;,
                                    &#39;user_name&#39;:&#39;db2inst1&#39;,
                                    &#39;credential_name&#39;: &#39;cred_name&#39;
                                }
                    Raises:
                        SDKException:
                            if None value in db2 options

                            if db2 instance with same name already exists

                            if given storage policy does not exists in commcell

        &#34;&#34;&#34;
        if not all(
                key in db2_options for key in(
                    &#34;instance_name&#34;,
                    &#34;data_storage_policy&#34;,
                    &#34;log_storage_policy&#34;,
                    &#34;command_storage_policy&#34;,
                    &#34;home_directory&#34;,
                    &#34;password&#34;,
                    &#34;user_name&#34;)):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;Not all db2_options are provided&#34;)

        if not db2_options.get(&#34;instance_name&#34;):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;)

        storage_policy = db2_options.get(&#39;storage_policy&#39;,db2_options.get(&#39;data_storage_policy&#39;))

        if not self._commcell_object.storage_policies.has_policy(storage_policy):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    db2_options[&#34;data_storage_policy&#34;])
            )

        if self._commcell_object.credentials.has_credential(db2_options[&#34;credential_name&#34;]):
            credential = self._commcell_object.credentials.get(db2_options[&#34;credential_name&#34;])
        else:
            credential = self._commcell_object.credentials.add_db2_database_creds(db2_options[&#34;credential_name&#34;],
                                                                              db2_options[&#34;user_name&#34;],
                                                                              db2_options[&#34;password&#34;])

        # encodes the plain text password using base64 encoding

        #enable_auto_discovery = db2_options[&#34;enable_auto_discovery&#34;]

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;instanceName&#34;: db2_options[&#34;instance_name&#34;],
                    &#34;commcellId&#34;: self._commcell_object.commcell_id,
                    &#34;instanceId&#34;: -1,
                    &#34;applicationId&#34;: int(self._agent_object.agent_id)
                },
                &#34;credentialEntity&#34;: {
                    &#34;credentialId&#34;: credential.credential_id,
                    &#34;credentialName&#34;: credential.credential_name,
                    &#34;description&#34;: credential.credential_description,
                    &#34;recordType&#34;: credential._record_type,
                    &#34;selected&#34;: True
                },
                &#34;planEntity&#34;: {
                    &#34;planId&#34;: int(self._commcell_object.plans.get(storage_policy).plan_id)
                },
                &#34;db2Instance&#34;: {
                    &#34;homeDirectory&#34;: db2_options[&#34;home_directory&#34;],
                    &#34;DB2StorageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyId&#34;: 1
                        },
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyId&#34;: 1
                        },
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyId&#34;: 1
                        }
                    }
                }
            }
        }
        add_instance = self._commcell_object._services[&#39;ADD_INSTANCE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, add_instance, request_json)
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;].get(&#39;errorCode&#39;)

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;].get(&#39;errorString&#39;)
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(error_string))
                else:
                    if &#39;entity&#39; in response.json()[&#39;response&#39;]:
                        self.refresh()
                        return self.get(response.json()[&#39;response&#39;][&#39;entity&#39;].get(&#39;instanceName&#39;))
                    else:
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Unable to get instance name and id&#39;
                                           )
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add_big_data_apps_instance(self, distributed_options):
        &#34;&#34;&#34;
            Method to add big data apps instance to the given client.

            distributed_options {
                &#34;instanceName&#34;: &#34;ClusterInstance&#34;
                &#34;MasterNode&#34; : $MASTER_NODE$ (Optional based on cluster Type. If not present set it to &#34;&#34;)
                &#34;dataAccessNodes&#34;: [
                    {
                        &#34;clientName&#34;: &#34;DataClient1&#34;
                    }
                ]
            }

            Raises:
                SDKException:
                    if None value in Distributed options
                    if Big Data Apps instance with same name already exists
                    if cannot retrieve cluster type from default Instance
        &#34;&#34;&#34;
        if None in distributed_options.values():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;One of the distributed parameter is None so cannot proceed with instance creation&#34;)

        if self.has_instance(distributed_options[&#34;instanceName&#34;]):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    distributed_options[&#34;instanceName&#34;])
            )

        &#34;&#34;&#34;
            Get Cluster Type from Default Instance to assign it to the New Instance.
            Atleast one instance should be present in the client.
        &#34;&#34;&#34;
        cluster_properties = {}
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._INSTANCES)
        if flag:
            if response.json() and &#34;instanceProperties&#34; in response.json():
                cluster_properties = response.json()[&#34;instanceProperties&#34;][0]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

        cluster_type = cluster_properties.get(&#39;distributedClusterInstance&#39;, {}).get(&#39;clusterType&#39;)
        cluster_config = {}
        uxfs_config = cluster_properties.get(
            &#39;distributedClusterInstance&#39;, {}).get(&#39;clusterConfig&#39;, {}).get(&#39;uxfsConfig&#39;)
        hadoop_config = cluster_properties.get(
            &#39;distributedClusterInstance&#39;, {}).get(&#39;clusterConfig&#39;, {}).get(&#39;hadoopConfig&#39;)
        if uxfs_config is not None:
            uxfs_config[&#39;coordinatorNode&#39;] = {&#34;clientName&#34;: distributed_options.get(&#39;MasterNode&#39;, &#39;&#39;)}
            cluster_config[&#39;uxfsConfig&#39;] = uxfs_config
        if hadoop_config is not None:
            hadoop_config[&#39;coordinatorNode&#39;] = {&#34;clientName&#34;: distributed_options.get(&#39;MasterNode&#39;, &#39;&#39;)}
            hbase_config = hadoop_config.get(&#39;hadoopApps&#39;, {}).get(&#39;appConfigs&#39;, [{}])[0].get(&#39;hBaseConfig&#39;)
            if hbase_config is not None:
                hadoop_config[&#34;hadoopApps&#34;][&#34;appConfigs&#34;][0][&#39;hBaseConfig&#39;][&#34;hbaseClientNode&#34;] = {
                    &#34;clientName&#34;: distributed_options.get(&#39;MasterNode&#39;, &#39;&#39;)}
            cluster_config[&#39;hadoopConfig&#39;] = hadoop_config

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: distributed_options[&#34;instanceName&#34;],
                },
                &#34;distributedClusterInstance&#34;: {
                    &#34;clusterType&#34;: cluster_type,
                    &#34;instance&#34;: {
                        &#34;instanceName&#34;: distributed_options[&#34;instanceName&#34;]
                    },
                    &#34;clusterConfig&#34;: cluster_config,
                    &#34;dataAccessNodes&#34;: {
                        &#34;dataAccessNodes&#34;: distributed_options[&#34;dataAccessNodes&#34;]
                    }
                }
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ADD_INSTANCE&#39;], request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    instance_name = response.json(
                    )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                    self.refresh()
                    return self.get(instance_name)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_cloud_storage_instance(self, cloud_options):
        &#34;&#34;&#34;Returns the JSON request to pass to the API for adding a cloud storage instance

        Args:
            cloud_options    (dict)    --    Options needed for adding a new cloud storage instance.

        Example:
        Cloud : S3
        cloud_options = {
                            &#39;instance_name&#39;: &#39;S3&#39;,
                            &#39;description&#39;: &#39;instance for s3&#39;,
                            &#39;storage_policy&#39;:&#39;cs_sp&#39;,
                            &#39;number_of_streams&#39;: 2,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;accesskey&#39;:&#39;xxxxxxxx&#39;,
                            &#39;secretkey&#39;:&#39;yyyyyyyy&#39;,
                            &#39;cloudapps_type&#39;: &#39;s3&#39;

            }
        Cloud : Google Cloud
        cloud_options = {
                            &#39;instance_name&#39;: &#39;google_test&#39;,
                            &#39;description&#39;: &#39;instance for google&#39;,
                            &#39;storage_plan&#39;:&#39;cs_sp&#39;,
                            &#39;number_of_streams&#39;: 2,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;cloudapps_type&#39;: &#39;google_cloud&#39;
                            &#39;host_url&#39;:&#39;storage.googleapis.com&#39;,
                            &#39;access_key&#39;:&#39;xxxxxx&#39;,
                            &#39;secret_key&#39;:&#39;yyyyyy&#39;
                        }
        Cloud : Azure Datalake Gen2
        cloud_options = {

                            &#39;instance_name&#39;: &#39;TestAzureDL&#39;,
                            &#39;storage_plan&#39;:&#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;description&#39;: None,
                            &#39;accountname&#39;: &#39;xxxxxx&#39;,
                            &#39;accesskey&#39;: &#39;xxxxxx&#39;,
                            &#39;number_of_streams&#39;: 1,
                            &#39;cloudapps_type&#39;: &#39;azureDL&#39;
                        }
        Cloud : Amazon RDS
        cloud_options = {
                            &#39;instance_name&#39;: &#39;RDS&#39;,
                            &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;access_key&#39;: &#39;xxxxx&#39;,
                            &#39;secret_key&#39;: &#39;xxxxx&#39;,
                            &#39;cloudapps_type&#39;: &#39;amazon_rds&#39;
                        }
        Cloud : Amazon Redshift
        cloud_options = {

                            &#39;instance_name&#39;: &#39;Redshift&#39;,
                            &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;access_key&#39;: &#39;xxxxx&#39;,
                            &#39;secret_key&#39;: &#39;xxxxx&#39;,
                            &#39;cloudapps_type&#39;: &#39;amazon_redshift&#39;
                        }
        Cloud : Amazon Document DB
        cloud_options = {
                            &#39;instance_name&#39;: &#39;DocumentDB&#39;,
                            &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;access_key&#39;: &#39;xxxxxx&#39;,
                            &#39;secret_key&#39;: &#39;xxxxxx&#39;,
                            &#39;cloudapps_type&#39;: &#39;amazon_docdb&#39;
                        }
        Returns:
            dict     --   JSON request to pass to the API
        Raises :
            SDKException :

                if cloud storage instance with same name already exists

                if given storage policy does not exist in commcell

        Cloud : Amazon DynamoDB
        cloud_options = {
                            &#39;instance_name&#39;: &#39;DynamoDB&#39;,
                            &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                            &#39;access_node&#39;: &#39;CS&#39;,
                            &#39;access_key&#39;: &#39;xxxxxx&#39;,
                            &#39;secret_key&#39;: &#39;xxxxxx&#39;,
                            &#39;cloudapps_type&#39;: &#39;amazon_dynamodb&#39;
                        }
        Returns:
            dict     --   JSON request to pass to the API
        Raises :
            SDKException :

                if cloud storage instance with same name already exists

                if given storage policy does not exist in commcell

        &#34;&#34;&#34;
        if cloud_options.get(&#34;instance_name&#34;):
            if self.has_instance(cloud_options.get(&#34;instance_name&#34;)):
                raise SDKException(
                    &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                        cloud_options.get(&#34;instance_name&#34;))
                )
        else:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Empty instance name provided&#39;)

        # setting the storage_policy for the general_properties setter method
        if cloud_options.get(&#39;storage_plan&#39;) and not cloud_options.get(&#39;storage_policy&#39;):
            cloud_options[&#39;storage_policy&#39;] = cloud_options.get(&#39;storage_plan&#39;)

        # setting storage_plan if not passed and storage_policy is passed instead
        if cloud_options.get(&#39;storage_policy&#39;) and not cloud_options.get(&#39;storage_plan&#39;):
            cloud_options[&#39;storage_plan&#39;] = cloud_options.get(&#39;storage_policy&#39;)

        if cloud_options.get(&#39;description&#39;):
            description = cloud_options.get(&#39;description&#39;)
        else:
            description = &#39;&#39;

        self._instance_properties_json = cloud_options
        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;description&#34;: description,
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;instanceName&#34;: cloud_options.get(&#34;instance_name&#34;),
                    &#34;appName&#34;: self._agent_object.agent_name,
                },
                &#34;cloudAppsInstance&#34;: self._instance_properties_json
            }
        }

        if cloud_options.get(&#34;storage_plan&#34;):
            if not self._commcell_object.storage_policies.has_policy(
                    cloud_options.get(&#34;storage_plan&#34;)):
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;Storage plan: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                        cloud_options.get(&#34;storage_plan&#34;))
                )
            request_json[&#34;instanceProperties&#34;][&#34;planEntity&#34;] = {&#34;planName&#34;: cloud_options.get(&#34;storage_plan&#34;)}
        else:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Empty storage plan provided&#39;)

        add_instance = self._commcell_object._services[&#39;ADD_INSTANCE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, add_instance, request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    instance_name = response.json()[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                    instance_id = response.json()[&#39;response&#39;][&#39;entity&#39;][&#39;instanceId&#39;]
                    agent_name = self._agent_object.agent_name
                    self.refresh()
                    return self.get(instance_name)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add_salesforce_instance(
            self, instance_name, access_node,
            salesforce_options,
            db_options=None, **kwargs):
        &#34;&#34;&#34;Adds a new salesforce instance.

            Args:
                instance_name               (str)   -- instance_name
                access_node                 (str)   -- access node name
                salesforce_options          (dict)  -- salesforce options
                                                        {
                                                                &#34;login_url&#34;: &#39;salesforce login url&#39;,
                                                                &#34;consume_id&#34;: &#39;salesforce consumer key&#39;,
                                                                &#34;consumer_secret&#34;: &#39;salesforce consumer secret&#39;,
                                                                &#34;salesforce_user_name&#34;: &#39;salesforce login user&#39;,
                                                                &#34;salesforce_user_password&#34;: &#39;salesforce user password&#39;,
                                                                &#34;salesforce_user_token&#34;: &#39;salesforce user token&#39;
                                                        }

                db_options                  (dict)  -- database options to configure sync db
                                                        {
                                                            &#34;db_enabled&#34;: &#39;True or False&#39;,
                                                            &#34;db_type&#34;: &#39;SQLSERVER or POSTGRESQL&#39;,
                                                            &#34;db_host_name&#34;: &#39;database hostname&#39;,
                                                            &#34;db_instance&#34;: &#39;database instance name&#39;,
                                                            &#34;db_name&#34;: &#39;database name&#39;,
                                                            &#34;db_port&#34;: &#39;port of the database&#39;,
                                                            &#34;db_user_name&#34;: &#39;database user name&#39;,
                                                            &#34;db_user_password&#34;: &#39;database user password&#39;
                                                        }

                **kwargs                    (dict)   -- dict of keyword arguments as follows

                                                         download_cache_path     (str)   -- download cache path
                                                         mutual_auth_path        (str)   -- mutual auth cert path
                                                         storage_policy          (str)   -- storage policy
                                                         streams                 (int)   -- number of streams
            Returns:
                object  -   instance of the instance class for this new instance

            Raises:
                SDKException:
                    if instance with given name already exists

                    if failed to add the instance

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if db_options is None:
            db_options = {&#39;db_enabled&#39;: False}
        if self.has_instance(instance_name):
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;,
                               &#39;Instance &#34;{0}&#34; already exists.&#39;.format(instance_name))

        salesforce_password = b64encode(salesforce_options.get(&#39;salesforce_user_password&#39;).encode()).decode()
        salesforce_consumer_secret = b64encode(
            salesforce_options.get(&#39;consumer_secret&#39;, &#39;3951207263309722430&#39;).encode()).decode()
        salesforce_token = b64encode(salesforce_options.get(&#39;salesforce_user_token&#39;, &#39;&#39;).encode()).decode()
        db_user_password = &#34;&#34;
        if db_options.get(&#39;db_enabled&#39;, False):
            db_user_password = b64encode(db_options.get(&#39;db_user_password&#39;).encode()).decode()

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: instance_name,
                    &#34;appName&#34;: self._agent_object.agent_name
                },
                &#34;cloudAppsInstance&#34;: {
                    &#34;instanceType&#34;: 3,
                    &#34;salesforceInstance&#34;: {
                        &#34;enableREST&#34;: True,
                        &#34;endpoint&#34;: salesforce_options.get(&#39;login_url&#39;, &#34;https://login.salesforce.com&#34;),
                        &#34;consumerId&#34;: salesforce_options.get(&#39;consumer_id&#39;,
                                                             &#39;3MVG9Nc1qcZ7BbZ0Ep18pfQsltTkZtbcMG9GMQzsVHGS8268yaOqmZ1lEEakAs8Xley85RBH1xKR1.eoUu1Z4&#39;),
                        &#34;consumerSecret&#34;: salesforce_consumer_secret,
                        &#34;defaultBackupsetProp&#34;: {
                            &#34;downloadCachePath&#34;: kwargs.get(&#39;download_cache_path&#39;, &#39;/tmp&#39;),
                            &#34;mutualAuthPath&#34;: kwargs.get(&#39;mutual_auth_path&#39;, &#39;&#39;),
                            &#34;token&#34;: salesforce_token,
                            &#34;userPassword&#34;: {
                                &#34;userName&#34;: salesforce_options.get(&#39;salesforce_user_name&#39;),
                                &#34;password&#34;: salesforce_password,
                            },
                            &#34;syncDatabase&#34;: {
                                &#34;dbEnabled&#34;: db_options.get(&#39;db_enabled&#39;, False),
                                &#34;dbPort&#34;: db_options.get(&#39;db_port&#39;, &#34;1433&#34;),
                                &#34;dbInstance&#34;: db_options.get(&#39;db_instance&#39;, &#39;&#39;),
                                &#34;dbName&#34;: db_options.get(&#39;db_name&#39;, instance_name),
                                &#34;dbType&#34;: db_options.get(&#39;db_type&#39;, &#39;SQLSERVER&#39;),
                                &#34;dbHost&#34;: db_options.get(&#39;db_host_name&#39;, &#39;&#39;),
                                &#34;dbUserPassword&#34;: {
                                    &#34;userName&#34;: db_options.get(&#39;db_user_name&#39;, &#39;&#39;),
                                    &#34;password&#34;: db_user_password,

                                },
                            },
                        },
                    },
                    &#34;generalCloudProperties&#34;: {
                        &#34;numberOfBackupStreams&#34;: kwargs.get(&#39;streams&#39;, 2),
                        &#34;proxyServers&#34;: [
                            {
                                &#34;clientName&#34;: access_node
                            }
                        ],
                        &#34;storageDevice&#34;: {
                            &#34;dataBackupStoragePolicy&#34;: {
                                &#34;storagePolicyName&#34;: kwargs.get(&#39;storage_policy&#39;, &#39;&#39;)
                            },
                        },
                    },
                },
            },
        }
        self._process_add_response(request_json)

    def add_postgresql_instance(self, instance_name, **kwargs):
        &#34;&#34;&#34;Adds new postgresql instance to given client
            Args:
                instance_name       (str)   --  instance_name
                kwargs              (dict)  --  dict of keyword arguments as follows:
                                                   storage_policy       (str)          -- storage policy
                                                   port                 (int or str)   -- port or end point
                                                   postgres_user_name   (str)          -- postgres user name
                                                   postgres_password    (str)          -- postgres password
                                                   version              (str)          -- postgres version
                                                   maintenance_db       (str)          -- maintenance db
                                                   binary_directory     (str)          -- postgres binary location
                                                   lib_directory        (str)          -- postgres lib location
                                                   archive_log_directory (str)         -- postgres archive log location
                                                   credential_name      (str)          -- PostgreSQL crdential name
            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if None value in postgres options

                    if postgres instance with same name already exists

                    if given storage policy does not exist in commcell
        &#34;&#34;&#34;

        if self.has_instance(instance_name):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    instance_name)
            )
        if not self._commcell_object.credentials.has_credential(kwargs.get(&#34;credential_name&#34;)):
            self._commcell_object.credentials.add_postgres_database_creds(
                kwargs.get(&#34;credential_name&#34;), kwargs.get(&#34;user_name&#34;), kwargs.get(&#34;password&#34;))
        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: instance_name,
                    &#34;appName&#34;: &#34;PostgreSQL&#34;,
                },
                &#34;version&#34;: kwargs.get(&#34;version&#34;, &#34;10.0&#34;),
                &#34;postGreSQLInstance&#34;: {
                    &#34;LibDirectory&#34;: kwargs.get(&#34;lib_directory&#34;, &#34;&#34;),
                    &#34;MaintainenceDB&#34;: kwargs.get(&#34;maintenance_db&#34;, &#34;postgres&#34;),
                    &#34;port&#34;: kwargs.get(&#34;port&#34;, &#34;5432&#34;),
                    &#34;ArchiveLogDirectory&#34;: kwargs.get(&#34;archive_log_directory&#34;, &#34;&#34;),
                    &#34;BinaryDirectory&#34;: kwargs.get(&#34;binary_directory&#34;, &#34;&#34;),
                    &#34;SAUser&#34;: {},
                    &#34;logStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: kwargs.get(&#34;storage_policy&#34;, &#34;&#34;)
                    },
                    &#34;credentialEntity&#34;: {
                        &#34;credentialName&#34;: kwargs.get(&#34;credential_name&#34;, &#34;&#34;)
                    }

                }
            }
        }
        self._process_add_response(request_json)


    @property
    def _general_properties_json(self):
        &#34;&#34;&#34;Returns the general properties json.&#34;&#34;&#34;
        return self._general_properties

    @_general_properties_json.setter
    def _general_properties_json(self, value):
        &#34;&#34;&#34;setter for general cloud properties in instance JSON.

        Args:

            value    (dict)    --    options needed to set general cloud properties

        Example:

            value = {
                &#34;number_of_streams&#34;:1,
                &#34;access_node&#34;:&#34;test&#34;,
                &#34;storage_policy&#34;:&#34;policy1&#34;,
                &#34;access_key&#34;: &#34;xxxxxx&#34;,
                &#34;secret_key&#34;: &#34;xxxxxx&#34;
            }

        &#34;&#34;&#34;

        supported_cloudapps_type = [&#34;amazon_rds&#34;, &#34;amazon_redshift&#34;,
                                    &#34;amazon_docdb&#34;, &#34;amazon_dynamodb&#34;]
        if value.get(&#34;cloudapps_type&#34;) in supported_cloudapps_type:
            self._general_properties = {
                &#34;accessNodes&#34;: {
                    &#34;memberServers&#34;: [
                        {
                            &#34;client&#34;: {
                                &#34;clientName&#34;: value.get(&#34;access_node&#34;)
                            }
                        }
                    ]
                },
                &#34;amazonInstanceInfo&#34;: {
                    &#34;secretKey&#34;: value.get(&#34;secret_key&#34;),
                    &#34;accessKey&#34;: value.get(&#34;access_key&#34;)
                }
            }

        else:
            self._general_properties = {
                &#34;numberOfBackupStreams&#34;: value.get(&#34;number_of_streams&#34;),
                &#34;proxyServers&#34;: [
                    {
                        &#34;clientName&#34;: value.get(&#34;access_node&#34;)
                    }
                ],
                &#34;storageDevice&#34;: {
                    &#34;dataBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: value.get(&#34;storage_policy&#34;)
                    }
                }
            }

    @property
    def _instance_properties_json(self):
        &#34;&#34;&#34;Returns the instance properties json.&#34;&#34;&#34;
        return self._instance_properties

    @_instance_properties_json.setter
    def _instance_properties_json(self, value):
        &#34;&#34;&#34;setter for cloud storage instance properties in instance JSON.

        Args:

            value    (dict)    --    options needed to set cloud storage instance properties

        Example:
            value = {
                &#34;accesskey&#34; : &#34;xxxxxxxxx&#34;
                &#34;secretkey&#34; : &#34;yyyyyyyy&#34;
            }

        &#34;&#34;&#34;

        supported_cloudapps_type = {&#34;amazon_rds&#34;: 4, &#34;amazon_redshift&#34;: 26,
                                    &#34;amazon_docdb&#34;: 27, &#34;amazon_dynamodb&#34;: 22}
        self._general_properties_json = value
        if value.get(&#34;cloudapps_type&#34;) == &#39;s3&#39;:
            self._instance_properties = {
                &#34;instanceType&#34;: 5,
                &#34;s3Instance&#34;: {
                    &#34;accessKeyId&#34;: value.get(&#34;accesskey&#34;),
                    &#34;secretAccessKey&#34;: value.get(&#34;secretkey&#34;),
                    &#34;hostURL&#34;: &#34;s3.amazonaws.com&#34;
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
        elif value.get(&#34;cloudapps_type&#34;) == &#39;azure&#39;:
            self._instance_properties = {
                &#34;instanceType&#34;: 6,
                &#34;azureInstance&#34;: {
                    &#34;accountName&#34;: value.get(&#34;accountname&#34;),
                    &#34;accessKey&#34;: value.get(&#34;accesskey&#34;),
                    &#34;hostURL&#34;: &#34;blob.core.windows.net&#34;
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
        elif value.get(&#34;cloudapps_type&#34;) == &#39;oraclecloud&#39;:
            password = b64encode(value.get(&#34;password&#34;).encode()).decode()
            self._instance_properties = {
                &#34;instanceType&#34;: 14,
                &#34;oraCloudInstance&#34;: {
                    &#34;endpointURL&#34;: value.get(&#34;endpointurl&#34;),
                    &#34;user&#34;: {
                        &#34;password&#34;: password,
                        &#34;userName&#34;: value.get(&#34;username&#34;)
                    }
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
        elif value.get(&#34;cloudapps_type&#34;) == &#39;openstack&#39;:
            apikey = b64encode(value.get(&#34;apikey&#34;).encode()).decode()
            self._instance_properties = {
                &#34;instanceType&#34;: 15,
                &#34;openStackInstance&#34;: {
                    &#34;serverName&#34;: value.get(&#34;servername&#34;),
                    &#34;credentials&#34;: {
                        &#34;password&#34;: apikey,
                        &#34;userName&#34;: value.get(&#34;username&#34;)
                    }
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }

        elif value.get(&#34;cloudapps_type&#34;) == &#39;google_cloud&#39;:
            secret_key = b64encode(value.get(&#34;secret_key&#34;).encode()).decode()
            self._instance_properties = {
                &#34;instanceType&#34;: 20,
                &#34;googleCloudInstance&#34;: {
                    &#34;serverName&#34;: value.get(&#34;host_url&#34;),
                    &#34;credentials&#34;: {
                        &#34;password&#34;: secret_key,
                        &#34;userName&#34;: value.get(&#34;access_key&#34;)
                    }
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }

        elif value.get(&#34;cloudapps_type&#34;) == &#39;azureDL&#39;:
            accesskey = b64encode(value.get(&#34;accesskey&#34;).encode()).decode()
            self._instance_properties = {
                &#34;instanceType&#34;: 21,
                &#34;azureDataLakeInstance&#34;: {
                    &#34;serverName&#34;: &#34;dfs.core.windows.net&#34;,
                    &#34;credentials&#34;: {
                        &#34;userName&#34;: value.get(&#34;accountname&#34;),
                        &#34;password&#34;: accesskey
                    }
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
        elif value.get(&#34;cloudapps_type&#34;) in supported_cloudapps_type:
            self._instance_properties = {
                &#34;instanceType&#34;: supported_cloudapps_type[value.get(&#34;cloudapps_type&#34;)],
                &#34;rdsInstance&#34;: {
                    &#34;secretKey&#34;: value.get(&#34;secret_key&#34;),
                    &#34;accessKey&#34;: value.get(&#34;access_key&#34;),
                    &#34;regionEndPoints&#34;: &#34;default&#34;
                },
                &#34;generalCloudProperties&#34;: self._general_properties_json
            }
    
    def add_mysql_instance(self, instance_name, database_options):
        &#34;&#34;&#34;Adds new mysql Instance to given Client
            Args:
                                instance_name       (str)   --  instance_name
              mysql_options       (dict)  --  dict of keyword arguments as follows:
                    Example:
                       database_options = {
                            &#39;enable_auto_discovery&#39;: True,
                            &#39;storage_policy&#39;: &#39;sai-sp&#39;,
                            &#39;port&#39;: &#39;hotsname:port&#39;,
                            &#39;mysql_user_name&#39;: &#39;mysqlusername&#39;
                            &#39;mysql_password&#39;: &#39;######&#39;,
                            &#39;version&#39;: &#39;5.7&#39;,
                            &#39;binary_directory&#39;: &#34;&#34;,
                            &#39;config_file&#39;: &#34;&#34;,
                            &#39;log_data_directory&#39;: &#34;&#34;,
                            &#39;data_directory&#39;: &#34;&#34;,
                            &#39;description&#39;: &#34;Automation created instance&#34;
                        }

            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if None value in mysql options

                    if mysql instance with same name already exists

                    if given storage policy does not exists in commcell
        &#34;&#34;&#34;
        if None in database_options.values():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;One of the mysql parameter is None so cannot proceed with instance creation&#34;)

        if self.has_instance(instance_name):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    instance_name)
            )

        if not self._commcell_object.storage_policies.has_policy(
                database_options[&#34;storage_policy&#34;]):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    database_options[&#34;storage_policy&#34;])
            )
        if not self._commcell_object.credentials.has_credential(database_options.get(&#34;credential_name&#34;)):
            self._commcell_object.credentials.add_mysql_database_creds(
                database_options.get(&#34;credential_name&#34;),
                database_options.get(&#34;mysql_user_name&#34;),
                database_options.get(&#34;mysql_password&#34;))

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;description&#34;: &#34;Automation created instance&#34;,
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;instanceName&#34;: instance_name,
                    &#34;appName&#34;: &#34;MySQL&#34;,
                    &#34;applicationId&#34;: 104,
                    &#34;_type_&#34;: 0
                },
                &#34;credentialEntity&#34;: {
                    &#34;credentialName&#34;: database_options.get(&#34;credential_name&#34;, &#34;&#34;)
                },
                &#34;mySqlInstance&#34;: {
                    &#34;BinaryDirectory&#34;: database_options.get(&#34;binary_directory&#34;, &#34;&#34;),
                    &#34;ConfigFile&#34;: database_options.get(&#34;config_file&#34;, &#34;&#34;),
                    &#34;EnableAutoDiscovery&#34;: database_options.get(&#34;enable_auto_discovery&#34;, True),
                    &#34;LogDataDirectory&#34;: database_options.get(&#34;log_data_directory&#34;, &#34;&#34;),
                    &#34;dataDirectory&#34;: database_options.get(&#34;data_directory&#34;, &#34;&#34;),
                    &#34;port&#34;: database_options.get(&#34;port&#34;, &#34;3306&#34;),
                    &#34;version&#34;: database_options.get(&#34;version&#34;, &#34;5.7&#34;),
                    &#34;sslCAFile&#34;: database_options.get(&#34;sslca_file_path&#34;, &#34;&#34;),
                    &#34;SAUser&#34;: {
                    },
                    &#34;proxySettings&#34;: {
                        &#34;isProxyEnabled&#34;: False,
                        &#34;isUseSSL&#34;: False,
                        &#34;runBackupOnProxy&#34;: False
                    },
                    &#34;mysqlStorageDevice&#34;: {
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: database_options.get(&#34;storage_policy&#34;, &#34;&#34;)
                        },
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: database_options.get(&#34;storage_policy&#34;, &#34;&#34;)
                        }
                    }
                }
            }
        }
        self._process_add_response(request_json)

    def add_oracle_instance(self, instance_name, **oracle_options):
        &#34;&#34;&#34;Adds new oracle instance for the given client
            Args:
                instance_name       (str)   --  instance_name (Oracle SID)
                oracle_options      (dict)  --  dict of keyword arguments as follows:
                                        log_storage_policy    (str)  -- log storage policy
                                        cmdline_storage_policy (str) -- Commandline data storage policy
                                        oracle_domain_name (str)   -- Domain name- only for windows
                                        oracle_user_name   (str)   -- oracle OS user name
                                        oracle_password    (str)   -- oracle OS user password
                                        oracle_home        (str)   -- oracle home path
                                        tns_admin          (str)   -- tns admin path
                                        connect_string     (dict)  -- Credentials to connect to Oracle DB
                                        {
                                            &#34;username&#34;: &#34;&#34;, (str)         -- User to connect to Oracle DB
                                            &#34;password&#34;: &#34;&#34;, (str)         -- Password
                                            &#34;service_name&#34;: &#34;&#34;  (str)     -- Oracle SID or service name
                                        }
                                        catalog_connect     (dict)--  Credentials to connect to catalog
                                        {
                                            &#34;userName&#34;: &#34;&#34;,  (str)        -- Catalog DB user name
                                            &#34;password&#34;; &#34;&#34;,  (str)        -- Password of catalog user
                                            &#34;domainName&#34;: &#34;&#34;    (str)     -- SID of catalog database
                                        }
            Returns:
                object - instance of the Instance class
            Raises:
                SDKException:
                            if instance with same name exists already
                            if required options are not provided
                            Given storage policies do not exist in Commcell
        &#34;&#34;&#34;

        if self.has_instance(instance_name):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    instance_name)
            )
        required_options = [&#39;oracle_user_name&#39;, &#39;oracle_home&#39;, &#39;cmdline_storage_policy&#39;,
                            &#39;log_storage_policy&#39;, &#39;connect_string&#39;]
        for option in required_options:
            if option not in oracle_options.keys():
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#34;Required option: {0} is missing, Please provide all parameters:&#34;.format(option))
        password = b64encode(oracle_options.get(&#34;oracle_password&#34;, &#34;&#34;).encode()).decode()
        connect_string_password = b64encode(
            oracle_options.get(&#34;connect_string&#34;, {}).get(&#34;password&#34;, &#34;&#34;).encode()
        ).decode()

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;instance&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: instance_name,
                    &#34;appName&#34;: &#34;Oracle&#34;,
                },
                &#34;oracleInstance&#34;: {
                    &#34;TNSAdminPath&#34;: oracle_options.get(&#34;tns_admin&#34;, &#34;&#34;),
                    &#34;oracleHome&#34;: oracle_options.get(&#34;oracle_home&#34;, &#34;&#34;),
                    &#34;oracleUser&#34;: {
                        &#34;userName&#34;: oracle_options.get(&#34;oracle_user_name&#34;, &#34;&#34;),
                        &#34;domainName&#34;: oracle_options.get(&#34;oracle_domain_name&#34;, &#34;&#34;),
                        &#34;password&#34;: password,
                    },
                    &#34;sqlConnect&#34;: {
                        &#34;domainName&#34;: oracle_options.get(&#34;connect_string&#34;, {}).get(&#34;service_name&#34;, &#34;&#34;),
                        &#34;userName&#34;: oracle_options.get(&#34;connect_string&#34;, {}).get(&#34;username&#34;, &#34;/&#34;),
                        &#34;password&#34;: connect_string_password,
                    },
                    &#34;oracleStorageDevice&#34;: {
                        &#34;commandLineStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: oracle_options.get(&#34;cmdline_storage_policy&#34;, &#34;&#34;)
                        },
                        &#34;logBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: oracle_options.get(&#34;log_storage_policy&#34;, &#34;&#34;)
                        }
                    },
                }
            }
        }
        if oracle_options.get(&#34;catalog_connect&#34;):
            catalog = {
                &#39;useCatalogConnect&#39;: True,
                &#39;catalogConnect&#39;: oracle_options.get(&#34;catalog_connect&#34;, &#34;&#34;)
            }
            request_json[&#39;instanceProperties&#39;][&#39;oracleInstance&#39;].update(catalog)
        self._process_add_response(request_json)

    def add_cosmosdb_instance(self, instance_name, **instance_options):
        &#34;&#34;&#34;Adds new cosmosdb cassandra api Instance to given Client
            Args:
                                instance_name       (str)   --  instance_name
                instance_options       (dict)  --  dict of keyword arguments as follows:
                    Example:
                       instance_options = {
                            &#39;plan_name&#39;: &#39;server plan&#39;,
                            &#39;cloudaccount_name&#39;: &#39;hotsname:port&#39;,
                            &#39;cloudinstancetype&#39;: &#39;instancetype&#39;,
                        }

            Returns:
                object - instance of the Instance class

            Raises:
                SDKException:
                    if instance with same name already exists
                    if given plan name does not exists in commcell
        &#34;&#34;&#34;

        if self.has_instance(instance_name):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    instance_name)
            )

        if not self._commcell_object.plans.has_plan(
                instance_options.get(&#34;plan_name&#34;)):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    instance_options.get(&#34;plan_name&#34;))
            )

        request_json = {
            &#34;instanceProperties&#34;: {
                &#34;cloudAppsInstance&#34;: {
                    &#34;instanceType&#34;: instance_options.get(&#34;cloudinstancetype&#34;,&#34;&#34;),
                    &#34;rdsInstance&#34;: {
                    }
                },
                &#34;instance&#34;: {
                    &#34;applicationId&#34;: 134,
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;clientName&#34;: instance_options.get(&#34;cloudaccount_name&#34;,&#34;&#34;),
                    &#34;instanceName&#34;: instance_name
                },
                &#34;planEntity&#34;: {
                    &#34;planName&#34;: instance_options.get(&#34;plan_name&#34;)
                }
            }
        }

        self._process_add_response(request_json)

    def refresh(self):
        &#34;&#34;&#34;Refresh the instances associated with the Agent of the selected Client.&#34;&#34;&#34;
        self._instances = self._get_instances()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instance.Instances.all_instances"><code class="name">var <span class="ident">all_instances</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all the instances associated with the agent</p>
<p>dict - consists of all instances of the agent
{
"instance1_name": instance1_id,
"instance2_name": instance2_id
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L332-L343" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_instances(self):
    &#34;&#34;&#34;Returns dict of all the instances associated with the agent

        dict - consists of all instances of the agent
                {
                     &#34;instance1_name&#34;: instance1_id,
                     &#34;instance2_name&#34;: instance2_id
                }

    &#34;&#34;&#34;
    return self._instances</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instance.Instances.add_big_data_apps_instance"><code class="name flex">
<span>def <span class="ident">add_big_data_apps_instance</span></span>(<span>self, distributed_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to add big data apps instance to the given client.</p>
<p>distributed_options {
"instanceName": "ClusterInstance"
"MasterNode" : $MASTER_NODE$ (Optional based on cluster Type. If not present set it to "")
"dataAccessNodes": [
{
"clientName": "DataClient1"
}
]
}</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if None value in Distributed options
if Big Data Apps instance with same name already exists
if cannot retrieve cluster type from default Instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L935-L1042" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_big_data_apps_instance(self, distributed_options):
    &#34;&#34;&#34;
        Method to add big data apps instance to the given client.

        distributed_options {
            &#34;instanceName&#34;: &#34;ClusterInstance&#34;
            &#34;MasterNode&#34; : $MASTER_NODE$ (Optional based on cluster Type. If not present set it to &#34;&#34;)
            &#34;dataAccessNodes&#34;: [
                {
                    &#34;clientName&#34;: &#34;DataClient1&#34;
                }
            ]
        }

        Raises:
            SDKException:
                if None value in Distributed options
                if Big Data Apps instance with same name already exists
                if cannot retrieve cluster type from default Instance
    &#34;&#34;&#34;
    if None in distributed_options.values():
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#34;One of the distributed parameter is None so cannot proceed with instance creation&#34;)

    if self.has_instance(distributed_options[&#34;instanceName&#34;]):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                distributed_options[&#34;instanceName&#34;])
        )

    &#34;&#34;&#34;
        Get Cluster Type from Default Instance to assign it to the New Instance.
        Atleast one instance should be present in the client.
    &#34;&#34;&#34;
    cluster_properties = {}
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._INSTANCES)
    if flag:
        if response.json() and &#34;instanceProperties&#34; in response.json():
            cluster_properties = response.json()[&#34;instanceProperties&#34;][0]
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    cluster_type = cluster_properties.get(&#39;distributedClusterInstance&#39;, {}).get(&#39;clusterType&#39;)
    cluster_config = {}
    uxfs_config = cluster_properties.get(
        &#39;distributedClusterInstance&#39;, {}).get(&#39;clusterConfig&#39;, {}).get(&#39;uxfsConfig&#39;)
    hadoop_config = cluster_properties.get(
        &#39;distributedClusterInstance&#39;, {}).get(&#39;clusterConfig&#39;, {}).get(&#39;hadoopConfig&#39;)
    if uxfs_config is not None:
        uxfs_config[&#39;coordinatorNode&#39;] = {&#34;clientName&#34;: distributed_options.get(&#39;MasterNode&#39;, &#39;&#39;)}
        cluster_config[&#39;uxfsConfig&#39;] = uxfs_config
    if hadoop_config is not None:
        hadoop_config[&#39;coordinatorNode&#39;] = {&#34;clientName&#34;: distributed_options.get(&#39;MasterNode&#39;, &#39;&#39;)}
        hbase_config = hadoop_config.get(&#39;hadoopApps&#39;, {}).get(&#39;appConfigs&#39;, [{}])[0].get(&#39;hBaseConfig&#39;)
        if hbase_config is not None:
            hadoop_config[&#34;hadoopApps&#34;][&#34;appConfigs&#34;][0][&#39;hBaseConfig&#39;][&#34;hbaseClientNode&#34;] = {
                &#34;clientName&#34;: distributed_options.get(&#39;MasterNode&#39;, &#39;&#39;)}
        cluster_config[&#39;hadoopConfig&#39;] = hadoop_config

    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;instance&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;appName&#34;: self._agent_object.agent_name,
                &#34;instanceName&#34;: distributed_options[&#34;instanceName&#34;],
            },
            &#34;distributedClusterInstance&#34;: {
                &#34;clusterType&#34;: cluster_type,
                &#34;instance&#34;: {
                    &#34;instanceName&#34;: distributed_options[&#34;instanceName&#34;]
                },
                &#34;clusterConfig&#34;: cluster_config,
                &#34;dataAccessNodes&#34;: {
                    &#34;dataAccessNodes&#34;: distributed_options[&#34;dataAccessNodes&#34;]
                }
            }
        }
    }

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;ADD_INSTANCE&#39;], request_json
    )
    if flag:
        if response.json() and &#39;response&#39; in response.json():
            error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

            if error_code != 0:
                error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                )
            else:
                instance_name = response.json(
                )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                self.refresh()
                return self.get(instance_name)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_cloud_storage_instance"><code class="name flex">
<span>def <span class="ident">add_cloud_storage_instance</span></span>(<span>self, cloud_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the JSON request to pass to the API for adding a cloud storage instance</p>
<h2 id="args">Args</h2>
<p>cloud_options
(dict)
&ndash;
Options needed for adding a new cloud storage instance.
Example:
Cloud : S3
cloud_options = {
'instance_name': 'S3',
'description': 'instance for s3',
'storage_policy':'cs_sp',
'number_of_streams': 2,
'access_node': 'CS',
'accesskey':'xxxxxxxx',
'secretkey':'yyyyyyyy',
'cloudapps_type': 's3'</p>
<pre><code>}
</code></pre>
<p>Cloud : Google Cloud
cloud_options = {
'instance_name': 'google_test',
'description': 'instance for google',
'storage_plan':'cs_sp',
'number_of_streams': 2,
'access_node': 'CS',
'cloudapps_type': 'google_cloud'
'host_url':'storage.googleapis.com',
'access_key':'xxxxxx',
'secret_key':'yyyyyy'
}
Cloud : Azure Datalake Gen2
cloud_options = {</p>
<pre><code>                'instance_name': 'TestAzureDL',
                'storage_plan':'cs_sp',
                'access_node': 'CS',
                'description': None,
                'accountname': 'xxxxxx',
                'accesskey': 'xxxxxx',
                'number_of_streams': 1,
                'cloudapps_type': 'azureDL'
            }
</code></pre>
<p>Cloud : Amazon RDS
cloud_options = {
'instance_name': 'RDS',
'storage_plan': 'cs_sp',
'access_node': 'CS',
'access_key': 'xxxxx',
'secret_key': 'xxxxx',
'cloudapps_type': 'amazon_rds'
}
Cloud : Amazon Redshift
cloud_options = {</p>
<pre><code>                'instance_name': 'Redshift',
                'storage_plan': 'cs_sp',
                'access_node': 'CS',
                'access_key': 'xxxxx',
                'secret_key': 'xxxxx',
                'cloudapps_type': 'amazon_redshift'
            }
</code></pre>
<p>Cloud : Amazon Document DB
cloud_options = {
'instance_name': 'DocumentDB',
'storage_plan': 'cs_sp',
'access_node': 'CS',
'access_key': 'xxxxxx',
'secret_key': 'xxxxxx',
'cloudapps_type': 'amazon_docdb'
}</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash;
JSON request to pass to the API
Raises :
SDKException :</p>
<pre><code>    if cloud storage instance with same name already exists

    if given storage policy does not exist in commcell
</code></pre>
<p>Cloud : Amazon DynamoDB
cloud_options = {
'instance_name': 'DynamoDB',
'storage_plan': 'cs_sp',
'access_node': 'CS',
'access_key': 'xxxxxx',
'secret_key': 'xxxxxx',
'cloudapps_type': 'amazon_dynamodb'
}</p>
<h2 id="returns_1">Returns</h2>
<p>dict
&ndash;
JSON request to pass to the API
Raises :
SDKException :</p>
<pre><code>    if cloud storage instance with same name already exists

    if given storage policy does not exist in commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L1044-L1221" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_cloud_storage_instance(self, cloud_options):
    &#34;&#34;&#34;Returns the JSON request to pass to the API for adding a cloud storage instance

    Args:
        cloud_options    (dict)    --    Options needed for adding a new cloud storage instance.

    Example:
    Cloud : S3
    cloud_options = {
                        &#39;instance_name&#39;: &#39;S3&#39;,
                        &#39;description&#39;: &#39;instance for s3&#39;,
                        &#39;storage_policy&#39;:&#39;cs_sp&#39;,
                        &#39;number_of_streams&#39;: 2,
                        &#39;access_node&#39;: &#39;CS&#39;,
                        &#39;accesskey&#39;:&#39;xxxxxxxx&#39;,
                        &#39;secretkey&#39;:&#39;yyyyyyyy&#39;,
                        &#39;cloudapps_type&#39;: &#39;s3&#39;

        }
    Cloud : Google Cloud
    cloud_options = {
                        &#39;instance_name&#39;: &#39;google_test&#39;,
                        &#39;description&#39;: &#39;instance for google&#39;,
                        &#39;storage_plan&#39;:&#39;cs_sp&#39;,
                        &#39;number_of_streams&#39;: 2,
                        &#39;access_node&#39;: &#39;CS&#39;,
                        &#39;cloudapps_type&#39;: &#39;google_cloud&#39;
                        &#39;host_url&#39;:&#39;storage.googleapis.com&#39;,
                        &#39;access_key&#39;:&#39;xxxxxx&#39;,
                        &#39;secret_key&#39;:&#39;yyyyyy&#39;
                    }
    Cloud : Azure Datalake Gen2
    cloud_options = {

                        &#39;instance_name&#39;: &#39;TestAzureDL&#39;,
                        &#39;storage_plan&#39;:&#39;cs_sp&#39;,
                        &#39;access_node&#39;: &#39;CS&#39;,
                        &#39;description&#39;: None,
                        &#39;accountname&#39;: &#39;xxxxxx&#39;,
                        &#39;accesskey&#39;: &#39;xxxxxx&#39;,
                        &#39;number_of_streams&#39;: 1,
                        &#39;cloudapps_type&#39;: &#39;azureDL&#39;
                    }
    Cloud : Amazon RDS
    cloud_options = {
                        &#39;instance_name&#39;: &#39;RDS&#39;,
                        &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                        &#39;access_node&#39;: &#39;CS&#39;,
                        &#39;access_key&#39;: &#39;xxxxx&#39;,
                        &#39;secret_key&#39;: &#39;xxxxx&#39;,
                        &#39;cloudapps_type&#39;: &#39;amazon_rds&#39;
                    }
    Cloud : Amazon Redshift
    cloud_options = {

                        &#39;instance_name&#39;: &#39;Redshift&#39;,
                        &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                        &#39;access_node&#39;: &#39;CS&#39;,
                        &#39;access_key&#39;: &#39;xxxxx&#39;,
                        &#39;secret_key&#39;: &#39;xxxxx&#39;,
                        &#39;cloudapps_type&#39;: &#39;amazon_redshift&#39;
                    }
    Cloud : Amazon Document DB
    cloud_options = {
                        &#39;instance_name&#39;: &#39;DocumentDB&#39;,
                        &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                        &#39;access_node&#39;: &#39;CS&#39;,
                        &#39;access_key&#39;: &#39;xxxxxx&#39;,
                        &#39;secret_key&#39;: &#39;xxxxxx&#39;,
                        &#39;cloudapps_type&#39;: &#39;amazon_docdb&#39;
                    }
    Returns:
        dict     --   JSON request to pass to the API
    Raises :
        SDKException :

            if cloud storage instance with same name already exists

            if given storage policy does not exist in commcell

    Cloud : Amazon DynamoDB
    cloud_options = {
                        &#39;instance_name&#39;: &#39;DynamoDB&#39;,
                        &#39;storage_plan&#39;: &#39;cs_sp&#39;,
                        &#39;access_node&#39;: &#39;CS&#39;,
                        &#39;access_key&#39;: &#39;xxxxxx&#39;,
                        &#39;secret_key&#39;: &#39;xxxxxx&#39;,
                        &#39;cloudapps_type&#39;: &#39;amazon_dynamodb&#39;
                    }
    Returns:
        dict     --   JSON request to pass to the API
    Raises :
        SDKException :

            if cloud storage instance with same name already exists

            if given storage policy does not exist in commcell

    &#34;&#34;&#34;
    if cloud_options.get(&#34;instance_name&#34;):
        if self.has_instance(cloud_options.get(&#34;instance_name&#34;)):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                    cloud_options.get(&#34;instance_name&#34;))
            )
    else:
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Empty instance name provided&#39;)

    # setting the storage_policy for the general_properties setter method
    if cloud_options.get(&#39;storage_plan&#39;) and not cloud_options.get(&#39;storage_policy&#39;):
        cloud_options[&#39;storage_policy&#39;] = cloud_options.get(&#39;storage_plan&#39;)

    # setting storage_plan if not passed and storage_policy is passed instead
    if cloud_options.get(&#39;storage_policy&#39;) and not cloud_options.get(&#39;storage_plan&#39;):
        cloud_options[&#39;storage_plan&#39;] = cloud_options.get(&#39;storage_policy&#39;)

    if cloud_options.get(&#39;description&#39;):
        description = cloud_options.get(&#39;description&#39;)
    else:
        description = &#39;&#39;

    self._instance_properties_json = cloud_options
    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;description&#34;: description,
            &#34;instance&#34;: {
                &#34;clientName&#34;: self._agent_object._client_object.client_name,
                &#34;instanceName&#34;: cloud_options.get(&#34;instance_name&#34;),
                &#34;appName&#34;: self._agent_object.agent_name,
            },
            &#34;cloudAppsInstance&#34;: self._instance_properties_json
        }
    }

    if cloud_options.get(&#34;storage_plan&#34;):
        if not self._commcell_object.storage_policies.has_policy(
                cloud_options.get(&#34;storage_plan&#34;)):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Storage plan: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                    cloud_options.get(&#34;storage_plan&#34;))
            )
        request_json[&#34;instanceProperties&#34;][&#34;planEntity&#34;] = {&#34;planName&#34;: cloud_options.get(&#34;storage_plan&#34;)}
    else:
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Empty storage plan provided&#39;)

    add_instance = self._commcell_object._services[&#39;ADD_INSTANCE&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, add_instance, request_json
    )
    if flag:
        if response.json() and &#39;response&#39; in response.json():
            error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

            if error_code != 0:
                error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                )
            else:
                instance_name = response.json()[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                instance_id = response.json()[&#39;response&#39;][&#39;entity&#39;][&#39;instanceId&#39;]
                agent_name = self._agent_object.agent_name
                self.refresh()
                return self.get(instance_name)

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_cosmosdb_instance"><code class="name flex">
<span>def <span class="ident">add_cosmosdb_instance</span></span>(<span>self, instance_name, **instance_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds new cosmosdb cassandra api Instance to given Client</p>
<h2 id="args">Args</h2>
<pre><code>            instance_name       (str)   --  instance_name
</code></pre>
<p>instance_options
(dict)
&ndash;
dict of keyword arguments as follows:
Example:
instance_options = {
'plan_name': 'server plan',
'cloudaccount_name': 'hotsname:port',
'cloudinstancetype': 'instancetype',
}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if instance with same name already exists
if given plan name does not exists in commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L1763-L1818" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_cosmosdb_instance(self, instance_name, **instance_options):
    &#34;&#34;&#34;Adds new cosmosdb cassandra api Instance to given Client
        Args:
                            instance_name       (str)   --  instance_name
            instance_options       (dict)  --  dict of keyword arguments as follows:
                Example:
                   instance_options = {
                        &#39;plan_name&#39;: &#39;server plan&#39;,
                        &#39;cloudaccount_name&#39;: &#39;hotsname:port&#39;,
                        &#39;cloudinstancetype&#39;: &#39;instancetype&#39;,
                    }

        Returns:
            object - instance of the Instance class

        Raises:
            SDKException:
                if instance with same name already exists
                if given plan name does not exists in commcell
    &#34;&#34;&#34;

    if self.has_instance(instance_name):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                instance_name)
        )

    if not self._commcell_object.plans.has_plan(
            instance_options.get(&#34;plan_name&#34;)):
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                instance_options.get(&#34;plan_name&#34;))
        )

    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;cloudAppsInstance&#34;: {
                &#34;instanceType&#34;: instance_options.get(&#34;cloudinstancetype&#34;,&#34;&#34;),
                &#34;rdsInstance&#34;: {
                }
            },
            &#34;instance&#34;: {
                &#34;applicationId&#34;: 134,
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;clientName&#34;: instance_options.get(&#34;cloudaccount_name&#34;,&#34;&#34;),
                &#34;instanceName&#34;: instance_name
            },
            &#34;planEntity&#34;: {
                &#34;planName&#34;: instance_options.get(&#34;plan_name&#34;)
            }
        }
    }

    self._process_add_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_db2_instance"><code class="name flex">
<span>def <span class="ident">add_db2_instance</span></span>(<span>self, db2_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to Add new Db2 Instance to given Client
Args:
Dictionary of db2 instance creation options:
Example:
db2_options = {
'instance_name': 'db2inst1',
'data_storage_policy': 'data_sp',
'log_storage_policy': 'log_sp',
'command_storage_policy': 'cmd_sp',
'home_directory':'/home/<USER>',
'password':'#####',
'user_name':'db2inst1',
'credential_name': 'cred_name'
}
Raises:
SDKException:
if None value in db2 options</p>
<pre><code>            if db2 instance with same name already exists

            if given storage policy does not exists in commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L811-L933" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_db2_instance(self, db2_options):
    &#34;&#34;&#34;
        Method to Add new Db2 Instance to given Client
            Args:
                    Dictionary of db2 instance creation options:
                        Example:
                           db2_options = {
                                &#39;instance_name&#39;: &#39;db2inst1&#39;,
                                &#39;data_storage_policy&#39;: &#39;data_sp&#39;,
                                &#39;log_storage_policy&#39;: &#39;log_sp&#39;,
                                &#39;command_storage_policy&#39;: &#39;cmd_sp&#39;,
                                &#39;home_directory&#39;:&#39;/home/<USER>
                                &#39;password&#39;:&#39;#####&#39;,
                                &#39;user_name&#39;:&#39;db2inst1&#39;,
                                &#39;credential_name&#39;: &#39;cred_name&#39;
                            }
                Raises:
                    SDKException:
                        if None value in db2 options

                        if db2 instance with same name already exists

                        if given storage policy does not exists in commcell

    &#34;&#34;&#34;
    if not all(
            key in db2_options for key in(
                &#34;instance_name&#34;,
                &#34;data_storage_policy&#34;,
                &#34;log_storage_policy&#34;,
                &#34;command_storage_policy&#34;,
                &#34;home_directory&#34;,
                &#34;password&#34;,
                &#34;user_name&#34;)):
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#34;Not all db2_options are provided&#34;)

    if not db2_options.get(&#34;instance_name&#34;):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;)

    storage_policy = db2_options.get(&#39;storage_policy&#39;,db2_options.get(&#39;data_storage_policy&#39;))

    if not self._commcell_object.storage_policies.has_policy(storage_policy):
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                db2_options[&#34;data_storage_policy&#34;])
        )

    if self._commcell_object.credentials.has_credential(db2_options[&#34;credential_name&#34;]):
        credential = self._commcell_object.credentials.get(db2_options[&#34;credential_name&#34;])
    else:
        credential = self._commcell_object.credentials.add_db2_database_creds(db2_options[&#34;credential_name&#34;],
                                                                          db2_options[&#34;user_name&#34;],
                                                                          db2_options[&#34;password&#34;])

    # encodes the plain text password using base64 encoding

    #enable_auto_discovery = db2_options[&#34;enable_auto_discovery&#34;]

    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;instance&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;instanceName&#34;: db2_options[&#34;instance_name&#34;],
                &#34;commcellId&#34;: self._commcell_object.commcell_id,
                &#34;instanceId&#34;: -1,
                &#34;applicationId&#34;: int(self._agent_object.agent_id)
            },
            &#34;credentialEntity&#34;: {
                &#34;credentialId&#34;: credential.credential_id,
                &#34;credentialName&#34;: credential.credential_name,
                &#34;description&#34;: credential.credential_description,
                &#34;recordType&#34;: credential._record_type,
                &#34;selected&#34;: True
            },
            &#34;planEntity&#34;: {
                &#34;planId&#34;: int(self._commcell_object.plans.get(storage_policy).plan_id)
            },
            &#34;db2Instance&#34;: {
                &#34;homeDirectory&#34;: db2_options[&#34;home_directory&#34;],
                &#34;DB2StorageDevice&#34;: {
                    &#34;dataBackupStoragePolicy&#34;: {
                        &#34;storagePolicyId&#34;: 1
                    },
                    &#34;commandLineStoragePolicy&#34;: {
                        &#34;storagePolicyId&#34;: 1
                    },
                    &#34;logBackupStoragePolicy&#34;: {
                        &#34;storagePolicyId&#34;: 1
                    }
                }
            }
        }
    }
    add_instance = self._commcell_object._services[&#39;ADD_INSTANCE&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, add_instance, request_json)
    if flag:
        if response.json() and &#39;response&#39; in response.json():
            error_code = response.json()[&#39;response&#39;].get(&#39;errorCode&#39;)

            if error_code != 0:
                error_string = response.json()[&#39;response&#39;].get(&#39;errorString&#39;)
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(error_string))
            else:
                if &#39;entity&#39; in response.json()[&#39;response&#39;]:
                    self.refresh()
                    return self.get(response.json()[&#39;response&#39;][&#39;entity&#39;].get(&#39;instanceName&#39;))
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Unable to get instance name and id&#39;
                                       )
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_informix_instance"><code class="name flex">
<span>def <span class="ident">add_informix_instance</span></span>(<span>self, informix_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds new Informix Instance to given Client</p>
<h2 id="args">Args</h2>
<p>Dictionary of informix instance creation options:
Example:
informix_options = {
'instance_name': "",
'onconfig_file': "",
'sql_host_file': "",
'informix_dir': "",
'user_name': "",
'domain_name': "",
'password': "",
'storage_policy': "",
'description':'created from automation'
}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if None value in informix options</p>
<pre><code>if Informix instance with same name already exists

if given storage policy does not exists in commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L446-L560" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_informix_instance(self, informix_options):
    &#34;&#34;&#34;Adds new Informix Instance to given Client
        Args:
            Dictionary of informix instance creation options:
                Example:
                   informix_options = {
                        &#39;instance_name&#39;: &#34;&#34;,
                        &#39;onconfig_file&#39;: &#34;&#34;,
                        &#39;sql_host_file&#39;: &#34;&#34;,
                        &#39;informix_dir&#39;: &#34;&#34;,
                        &#39;user_name&#39;: &#34;&#34;,
                        &#39;domain_name&#39;: &#34;&#34;,
                        &#39;password&#39;: &#34;&#34;,
                        &#39;storage_policy&#39;: &#34;&#34;,
                        &#39;description&#39;:&#39;created from automation&#39;
                    }

        Returns:
            object - instance of the Instance class

        Raises:
            SDKException:
                if None value in informix options

                if Informix instance with same name already exists

                if given storage policy does not exists in commcell
    &#34;&#34;&#34;
    if None in informix_options.values():
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#34;One of the informix parameter is None so cannot proceed with instance creation&#34;)

    if self.has_instance(informix_options[&#34;instance_name&#34;]):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                informix_options[&#34;instance_name&#34;])
        )

    if not self._commcell_object.storage_policies.has_policy(
            informix_options[&#34;storage_policy&#34;]):
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                informix_options[&#34;storage_policy&#34;])
        )
    password = b64encode(informix_options[&#34;password&#34;].encode()).decode()

    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;description&#34;: informix_options[&#39;description&#39;],
            &#34;instance&#34;: {
                &#34;clientName&#34;: self._agent_object._client_object.client_name,
                &#34;instanceName&#34;: informix_options[&#34;instance_name&#34;],
                &#34;appName&#34;: &#34;Informix Database&#34;
            },
            &#34;informixInstance&#34;: {
                &#34;onConfigFile&#34;: informix_options[&#34;onconfig_file&#34;],
                &#34;sqlHostfile&#34;: informix_options[&#34;sql_host_file&#34;],
                &#34;informixDir&#34;: informix_options[&#34;informix_dir&#34;],
                &#34;informixUser&#34;: {
                    &#34;password&#34;: password,
                    &#34;domainName&#34;: informix_options[&#34;domain_name&#34;],
                    &#34;userName&#34;: informix_options[&#34;user_name&#34;]
                },
                &#34;informixStorageDevice&#34;: {
                    &#34;dataBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: informix_options[&#34;storage_policy&#34;]
                    },
                    &#34;deDuplicationOptions&#34;: {},
                    &#34;logBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: informix_options[&#34;storage_policy&#34;]
                    },
                    &#34;commandLineStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: informix_options[&#34;storage_policy&#34;]
                    }
                }
            }
        }
    }

    add_instance = self._commcell_object._services[&#39;ADD_INSTANCE&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, add_instance, request_json
    )
    if flag:
        if response.json() and &#39;response&#39; in response.json():
            error_code = response.json()[&#39;response&#39;].get(&#39;errorCode&#39;)

            if error_code != 0:
                error_string = response.json()[&#39;response&#39;].get(&#39;errorString&#39;)
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                )
            else:
                if &#39;entity&#39; in response.json()[&#39;response&#39;]:
                    self.refresh()
                    return self.get(response.json()[&#39;response&#39;][&#39;entity&#39;].get(&#39;instanceName&#39;))
                else:
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Unable to get instance name and id&#39;
                    )
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_mysql_instance"><code class="name flex">
<span>def <span class="ident">add_mysql_instance</span></span>(<span>self, instance_name, database_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds new mysql Instance to given Client</p>
<h2 id="args">Args</h2>
<pre><code>              instance_name       (str)   --  instance_name
</code></pre>
<p>mysql_options
(dict)
&ndash;
dict of keyword arguments as follows:
Example:
database_options = {
'enable_auto_discovery': True,
'storage_policy': 'sai-sp',
'port': 'hotsname:port',
'mysql_user_name': 'mysqlusername'
'mysql_password': '######',
'version': '5.7',
'binary_directory': "",
'config_file': "",
'log_data_directory': "",
'data_directory': "",
'description': "Automation created instance"
}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if None value in mysql options</p>
<pre><code>if mysql instance with same name already exists

if given storage policy does not exists in commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L1574-L1671" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_mysql_instance(self, instance_name, database_options):
    &#34;&#34;&#34;Adds new mysql Instance to given Client
        Args:
                            instance_name       (str)   --  instance_name
          mysql_options       (dict)  --  dict of keyword arguments as follows:
                Example:
                   database_options = {
                        &#39;enable_auto_discovery&#39;: True,
                        &#39;storage_policy&#39;: &#39;sai-sp&#39;,
                        &#39;port&#39;: &#39;hotsname:port&#39;,
                        &#39;mysql_user_name&#39;: &#39;mysqlusername&#39;
                        &#39;mysql_password&#39;: &#39;######&#39;,
                        &#39;version&#39;: &#39;5.7&#39;,
                        &#39;binary_directory&#39;: &#34;&#34;,
                        &#39;config_file&#39;: &#34;&#34;,
                        &#39;log_data_directory&#39;: &#34;&#34;,
                        &#39;data_directory&#39;: &#34;&#34;,
                        &#39;description&#39;: &#34;Automation created instance&#34;
                    }

        Returns:
            object - instance of the Instance class

        Raises:
            SDKException:
                if None value in mysql options

                if mysql instance with same name already exists

                if given storage policy does not exists in commcell
    &#34;&#34;&#34;
    if None in database_options.values():
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#34;One of the mysql parameter is None so cannot proceed with instance creation&#34;)

    if self.has_instance(instance_name):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                instance_name)
        )

    if not self._commcell_object.storage_policies.has_policy(
            database_options[&#34;storage_policy&#34;]):
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                database_options[&#34;storage_policy&#34;])
        )
    if not self._commcell_object.credentials.has_credential(database_options.get(&#34;credential_name&#34;)):
        self._commcell_object.credentials.add_mysql_database_creds(
            database_options.get(&#34;credential_name&#34;),
            database_options.get(&#34;mysql_user_name&#34;),
            database_options.get(&#34;mysql_password&#34;))

    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;description&#34;: &#34;Automation created instance&#34;,
            &#34;instance&#34;: {
                &#34;clientName&#34;: self._agent_object._client_object.client_name,
                &#34;instanceName&#34;: instance_name,
                &#34;appName&#34;: &#34;MySQL&#34;,
                &#34;applicationId&#34;: 104,
                &#34;_type_&#34;: 0
            },
            &#34;credentialEntity&#34;: {
                &#34;credentialName&#34;: database_options.get(&#34;credential_name&#34;, &#34;&#34;)
            },
            &#34;mySqlInstance&#34;: {
                &#34;BinaryDirectory&#34;: database_options.get(&#34;binary_directory&#34;, &#34;&#34;),
                &#34;ConfigFile&#34;: database_options.get(&#34;config_file&#34;, &#34;&#34;),
                &#34;EnableAutoDiscovery&#34;: database_options.get(&#34;enable_auto_discovery&#34;, True),
                &#34;LogDataDirectory&#34;: database_options.get(&#34;log_data_directory&#34;, &#34;&#34;),
                &#34;dataDirectory&#34;: database_options.get(&#34;data_directory&#34;, &#34;&#34;),
                &#34;port&#34;: database_options.get(&#34;port&#34;, &#34;3306&#34;),
                &#34;version&#34;: database_options.get(&#34;version&#34;, &#34;5.7&#34;),
                &#34;sslCAFile&#34;: database_options.get(&#34;sslca_file_path&#34;, &#34;&#34;),
                &#34;SAUser&#34;: {
                },
                &#34;proxySettings&#34;: {
                    &#34;isProxyEnabled&#34;: False,
                    &#34;isUseSSL&#34;: False,
                    &#34;runBackupOnProxy&#34;: False
                },
                &#34;mysqlStorageDevice&#34;: {
                    &#34;commandLineStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: database_options.get(&#34;storage_policy&#34;, &#34;&#34;)
                    },
                    &#34;logBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: database_options.get(&#34;storage_policy&#34;, &#34;&#34;)
                    }
                }
            }
        }
    }
    self._process_add_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_oracle_instance"><code class="name flex">
<span>def <span class="ident">add_oracle_instance</span></span>(<span>self, instance_name, **oracle_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds new oracle instance for the given client</p>
<h2 id="args">Args</h2>
<p>instance_name
(str)
&ndash;
instance_name (Oracle SID)
oracle_options
(dict)
&ndash;
dict of keyword arguments as follows:
log_storage_policy
(str)
&ndash; log storage policy
cmdline_storage_policy (str) &ndash; Commandline data storage policy
oracle_domain_name (str)
&ndash; Domain name- only for windows
oracle_user_name
(str)
&ndash; oracle OS user name
oracle_password
(str)
&ndash; oracle OS user password
oracle_home
(str)
&ndash; oracle home path
tns_admin
(str)
&ndash; tns admin path
connect_string
(dict)
&ndash; Credentials to connect to Oracle DB
{
"username": "", (str)
&ndash; User to connect to Oracle DB
"password": "", (str)
&ndash; Password
"service_name": ""
(str)
&ndash; Oracle SID or service name
}
catalog_connect
(dict)&ndash;
Credentials to connect to catalog
{
"userName": "",
(str)
&ndash; Catalog DB user name
"password"; "",
(str)
&ndash; Password of catalog user
"domainName": ""
(str)
&ndash; SID of catalog database
}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if instance with same name exists already
if required options are not provided
Given storage policies do not exist in Commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L1673-L1761" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_oracle_instance(self, instance_name, **oracle_options):
    &#34;&#34;&#34;Adds new oracle instance for the given client
        Args:
            instance_name       (str)   --  instance_name (Oracle SID)
            oracle_options      (dict)  --  dict of keyword arguments as follows:
                                    log_storage_policy    (str)  -- log storage policy
                                    cmdline_storage_policy (str) -- Commandline data storage policy
                                    oracle_domain_name (str)   -- Domain name- only for windows
                                    oracle_user_name   (str)   -- oracle OS user name
                                    oracle_password    (str)   -- oracle OS user password
                                    oracle_home        (str)   -- oracle home path
                                    tns_admin          (str)   -- tns admin path
                                    connect_string     (dict)  -- Credentials to connect to Oracle DB
                                    {
                                        &#34;username&#34;: &#34;&#34;, (str)         -- User to connect to Oracle DB
                                        &#34;password&#34;: &#34;&#34;, (str)         -- Password
                                        &#34;service_name&#34;: &#34;&#34;  (str)     -- Oracle SID or service name
                                    }
                                    catalog_connect     (dict)--  Credentials to connect to catalog
                                    {
                                        &#34;userName&#34;: &#34;&#34;,  (str)        -- Catalog DB user name
                                        &#34;password&#34;; &#34;&#34;,  (str)        -- Password of catalog user
                                        &#34;domainName&#34;: &#34;&#34;    (str)     -- SID of catalog database
                                    }
        Returns:
            object - instance of the Instance class
        Raises:
            SDKException:
                        if instance with same name exists already
                        if required options are not provided
                        Given storage policies do not exist in Commcell
    &#34;&#34;&#34;

    if self.has_instance(instance_name):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                instance_name)
        )
    required_options = [&#39;oracle_user_name&#39;, &#39;oracle_home&#39;, &#39;cmdline_storage_policy&#39;,
                        &#39;log_storage_policy&#39;, &#39;connect_string&#39;]
    for option in required_options:
        if option not in oracle_options.keys():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#34;Required option: {0} is missing, Please provide all parameters:&#34;.format(option))
    password = b64encode(oracle_options.get(&#34;oracle_password&#34;, &#34;&#34;).encode()).decode()
    connect_string_password = b64encode(
        oracle_options.get(&#34;connect_string&#34;, {}).get(&#34;password&#34;, &#34;&#34;).encode()
    ).decode()

    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;instance&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;instanceName&#34;: instance_name,
                &#34;appName&#34;: &#34;Oracle&#34;,
            },
            &#34;oracleInstance&#34;: {
                &#34;TNSAdminPath&#34;: oracle_options.get(&#34;tns_admin&#34;, &#34;&#34;),
                &#34;oracleHome&#34;: oracle_options.get(&#34;oracle_home&#34;, &#34;&#34;),
                &#34;oracleUser&#34;: {
                    &#34;userName&#34;: oracle_options.get(&#34;oracle_user_name&#34;, &#34;&#34;),
                    &#34;domainName&#34;: oracle_options.get(&#34;oracle_domain_name&#34;, &#34;&#34;),
                    &#34;password&#34;: password,
                },
                &#34;sqlConnect&#34;: {
                    &#34;domainName&#34;: oracle_options.get(&#34;connect_string&#34;, {}).get(&#34;service_name&#34;, &#34;&#34;),
                    &#34;userName&#34;: oracle_options.get(&#34;connect_string&#34;, {}).get(&#34;username&#34;, &#34;/&#34;),
                    &#34;password&#34;: connect_string_password,
                },
                &#34;oracleStorageDevice&#34;: {
                    &#34;commandLineStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: oracle_options.get(&#34;cmdline_storage_policy&#34;, &#34;&#34;)
                    },
                    &#34;logBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: oracle_options.get(&#34;log_storage_policy&#34;, &#34;&#34;)
                    }
                },
            }
        }
    }
    if oracle_options.get(&#34;catalog_connect&#34;):
        catalog = {
            &#39;useCatalogConnect&#39;: True,
            &#39;catalogConnect&#39;: oracle_options.get(&#34;catalog_connect&#34;, &#34;&#34;)
        }
        request_json[&#39;instanceProperties&#39;][&#39;oracleInstance&#39;].update(catalog)
    self._process_add_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_postgresql_instance"><code class="name flex">
<span>def <span class="ident">add_postgresql_instance</span></span>(<span>self, instance_name, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds new postgresql instance to given client</p>
<h2 id="args">Args</h2>
<p>instance_name
(str)
&ndash;
instance_name
kwargs
(dict)
&ndash;
dict of keyword arguments as follows:
storage_policy
(str)
&ndash; storage policy
port
(int or str)
&ndash; port or end point
postgres_user_name
(str)
&ndash; postgres user name
postgres_password
(str)
&ndash; postgres password
version
(str)
&ndash; postgres version
maintenance_db
(str)
&ndash; maintenance db
binary_directory
(str)
&ndash; postgres binary location
lib_directory
(str)
&ndash; postgres lib location
archive_log_directory (str)
&ndash; postgres archive log location
credential_name
(str)
&ndash; PostgreSQL crdential name</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if None value in postgres options</p>
<pre><code>if postgres instance with same name already exists

if given storage policy does not exist in commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L1343-L1403" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_postgresql_instance(self, instance_name, **kwargs):
    &#34;&#34;&#34;Adds new postgresql instance to given client
        Args:
            instance_name       (str)   --  instance_name
            kwargs              (dict)  --  dict of keyword arguments as follows:
                                               storage_policy       (str)          -- storage policy
                                               port                 (int or str)   -- port or end point
                                               postgres_user_name   (str)          -- postgres user name
                                               postgres_password    (str)          -- postgres password
                                               version              (str)          -- postgres version
                                               maintenance_db       (str)          -- maintenance db
                                               binary_directory     (str)          -- postgres binary location
                                               lib_directory        (str)          -- postgres lib location
                                               archive_log_directory (str)         -- postgres archive log location
                                               credential_name      (str)          -- PostgreSQL crdential name
        Returns:
            object - instance of the Instance class

        Raises:
            SDKException:
                if None value in postgres options

                if postgres instance with same name already exists

                if given storage policy does not exist in commcell
    &#34;&#34;&#34;

    if self.has_instance(instance_name):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                instance_name)
        )
    if not self._commcell_object.credentials.has_credential(kwargs.get(&#34;credential_name&#34;)):
        self._commcell_object.credentials.add_postgres_database_creds(
            kwargs.get(&#34;credential_name&#34;), kwargs.get(&#34;user_name&#34;), kwargs.get(&#34;password&#34;))
    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;instance&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;instanceName&#34;: instance_name,
                &#34;appName&#34;: &#34;PostgreSQL&#34;,
            },
            &#34;version&#34;: kwargs.get(&#34;version&#34;, &#34;10.0&#34;),
            &#34;postGreSQLInstance&#34;: {
                &#34;LibDirectory&#34;: kwargs.get(&#34;lib_directory&#34;, &#34;&#34;),
                &#34;MaintainenceDB&#34;: kwargs.get(&#34;maintenance_db&#34;, &#34;postgres&#34;),
                &#34;port&#34;: kwargs.get(&#34;port&#34;, &#34;5432&#34;),
                &#34;ArchiveLogDirectory&#34;: kwargs.get(&#34;archive_log_directory&#34;, &#34;&#34;),
                &#34;BinaryDirectory&#34;: kwargs.get(&#34;binary_directory&#34;, &#34;&#34;),
                &#34;SAUser&#34;: {},
                &#34;logStoragePolicy&#34;: {
                    &#34;storagePolicyName&#34;: kwargs.get(&#34;storage_policy&#34;, &#34;&#34;)
                },
                &#34;credentialEntity&#34;: {
                    &#34;credentialName&#34;: kwargs.get(&#34;credential_name&#34;, &#34;&#34;)
                }

            }
        }
    }
    self._process_add_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_salesforce_instance"><code class="name flex">
<span>def <span class="ident">add_salesforce_instance</span></span>(<span>self, instance_name, access_node, salesforce_options, db_options=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new salesforce instance.</p>
<h2 id="args">Args</h2>
<p>instance_name
(str)
&ndash; instance_name
access_node
(str)
&ndash; access node name
salesforce_options
(dict)
&ndash; salesforce options
{
"login_url": 'salesforce login url',
"consume_id": 'salesforce consumer key',
"consumer_secret": 'salesforce consumer secret',
"salesforce_user_name": 'salesforce login user',
"salesforce_user_password": 'salesforce user password',
"salesforce_user_token": 'salesforce user token'
}</p>
<p>db_options
(dict)
&ndash; database options to configure sync db
{
"db_enabled": 'True or False',
"db_type": 'SQLSERVER or POSTGRESQL',
"db_host_name": 'database hostname',
"db_instance": 'database instance name',
"db_name": 'database name',
"db_port": 'port of the database',
"db_user_name": 'database user name',
"db_user_password": 'database user password'
}</p>
<p>**kwargs
(dict)
&ndash; dict of keyword arguments as follows</p>
<pre><code>                                     download_cache_path     (str)   -- download cache path
                                     mutual_auth_path        (str)   -- mutual auth cert path
                                     storage_policy          (str)   -- storage policy
                                     streams                 (int)   -- number of streams
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the instance class for this new instance</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if instance with given name already exists</p>
<pre><code>if failed to add the instance

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L1223-L1341" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_salesforce_instance(
        self, instance_name, access_node,
        salesforce_options,
        db_options=None, **kwargs):
    &#34;&#34;&#34;Adds a new salesforce instance.

        Args:
            instance_name               (str)   -- instance_name
            access_node                 (str)   -- access node name
            salesforce_options          (dict)  -- salesforce options
                                                    {
                                                            &#34;login_url&#34;: &#39;salesforce login url&#39;,
                                                            &#34;consume_id&#34;: &#39;salesforce consumer key&#39;,
                                                            &#34;consumer_secret&#34;: &#39;salesforce consumer secret&#39;,
                                                            &#34;salesforce_user_name&#34;: &#39;salesforce login user&#39;,
                                                            &#34;salesforce_user_password&#34;: &#39;salesforce user password&#39;,
                                                            &#34;salesforce_user_token&#34;: &#39;salesforce user token&#39;
                                                    }

            db_options                  (dict)  -- database options to configure sync db
                                                    {
                                                        &#34;db_enabled&#34;: &#39;True or False&#39;,
                                                        &#34;db_type&#34;: &#39;SQLSERVER or POSTGRESQL&#39;,
                                                        &#34;db_host_name&#34;: &#39;database hostname&#39;,
                                                        &#34;db_instance&#34;: &#39;database instance name&#39;,
                                                        &#34;db_name&#34;: &#39;database name&#39;,
                                                        &#34;db_port&#34;: &#39;port of the database&#39;,
                                                        &#34;db_user_name&#34;: &#39;database user name&#39;,
                                                        &#34;db_user_password&#34;: &#39;database user password&#39;
                                                    }

            **kwargs                    (dict)   -- dict of keyword arguments as follows

                                                     download_cache_path     (str)   -- download cache path
                                                     mutual_auth_path        (str)   -- mutual auth cert path
                                                     storage_policy          (str)   -- storage policy
                                                     streams                 (int)   -- number of streams
        Returns:
            object  -   instance of the instance class for this new instance

        Raises:
            SDKException:
                if instance with given name already exists

                if failed to add the instance

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if db_options is None:
        db_options = {&#39;db_enabled&#39;: False}
    if self.has_instance(instance_name):
        raise SDKException(&#39;Instance&#39;, &#39;102&#39;,
                           &#39;Instance &#34;{0}&#34; already exists.&#39;.format(instance_name))

    salesforce_password = b64encode(salesforce_options.get(&#39;salesforce_user_password&#39;).encode()).decode()
    salesforce_consumer_secret = b64encode(
        salesforce_options.get(&#39;consumer_secret&#39;, &#39;3951207263309722430&#39;).encode()).decode()
    salesforce_token = b64encode(salesforce_options.get(&#39;salesforce_user_token&#39;, &#39;&#39;).encode()).decode()
    db_user_password = &#34;&#34;
    if db_options.get(&#39;db_enabled&#39;, False):
        db_user_password = b64encode(db_options.get(&#39;db_user_password&#39;).encode()).decode()

    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;instance&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;instanceName&#34;: instance_name,
                &#34;appName&#34;: self._agent_object.agent_name
            },
            &#34;cloudAppsInstance&#34;: {
                &#34;instanceType&#34;: 3,
                &#34;salesforceInstance&#34;: {
                    &#34;enableREST&#34;: True,
                    &#34;endpoint&#34;: salesforce_options.get(&#39;login_url&#39;, &#34;https://login.salesforce.com&#34;),
                    &#34;consumerId&#34;: salesforce_options.get(&#39;consumer_id&#39;,
                                                         &#39;3MVG9Nc1qcZ7BbZ0Ep18pfQsltTkZtbcMG9GMQzsVHGS8268yaOqmZ1lEEakAs8Xley85RBH1xKR1.eoUu1Z4&#39;),
                    &#34;consumerSecret&#34;: salesforce_consumer_secret,
                    &#34;defaultBackupsetProp&#34;: {
                        &#34;downloadCachePath&#34;: kwargs.get(&#39;download_cache_path&#39;, &#39;/tmp&#39;),
                        &#34;mutualAuthPath&#34;: kwargs.get(&#39;mutual_auth_path&#39;, &#39;&#39;),
                        &#34;token&#34;: salesforce_token,
                        &#34;userPassword&#34;: {
                            &#34;userName&#34;: salesforce_options.get(&#39;salesforce_user_name&#39;),
                            &#34;password&#34;: salesforce_password,
                        },
                        &#34;syncDatabase&#34;: {
                            &#34;dbEnabled&#34;: db_options.get(&#39;db_enabled&#39;, False),
                            &#34;dbPort&#34;: db_options.get(&#39;db_port&#39;, &#34;1433&#34;),
                            &#34;dbInstance&#34;: db_options.get(&#39;db_instance&#39;, &#39;&#39;),
                            &#34;dbName&#34;: db_options.get(&#39;db_name&#39;, instance_name),
                            &#34;dbType&#34;: db_options.get(&#39;db_type&#39;, &#39;SQLSERVER&#39;),
                            &#34;dbHost&#34;: db_options.get(&#39;db_host_name&#39;, &#39;&#39;),
                            &#34;dbUserPassword&#34;: {
                                &#34;userName&#34;: db_options.get(&#39;db_user_name&#39;, &#39;&#39;),
                                &#34;password&#34;: db_user_password,

                            },
                        },
                    },
                },
                &#34;generalCloudProperties&#34;: {
                    &#34;numberOfBackupStreams&#34;: kwargs.get(&#39;streams&#39;, 2),
                    &#34;proxyServers&#34;: [
                        {
                            &#34;clientName&#34;: access_node
                        }
                    ],
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: kwargs.get(&#39;storage_policy&#39;, &#39;&#39;)
                        },
                    },
                },
            },
        },
    }
    self._process_add_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_sap_hana_instance"><code class="name flex">
<span>def <span class="ident">add_sap_hana_instance</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds new sap hana instance to given client</p>
<h2 id="args">Args</h2>
<p>sid
(str)
&ndash; Database SID
hana_client_name(str)
&ndash;
Client where the hana server exists
db_user_name
(str)
&ndash; postgres user name
db_password
(str)
&ndash; DB password
storage_policy
(str)
&ndash;
Storage Policy name</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if None value in sap hana options</p>
<pre><code>if sap hana instance with same name already exists

if given storage policy does not exists in commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L562-L622" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_sap_hana_instance(self, **kwargs):
    &#34;&#34;&#34;Adds new sap hana instance to given client
        Args:
            sid             (str)   -- Database SID
            hana_client_name(str)   --  Client where the hana server exists
            db_user_name   (str)    -- postgres user name
            db_password    (str)    -- DB password
            storage_policy  (str)   --  Storage Policy name
        Returns:
            object - instance of the Instance class

        Raises:
            SDKException:
                if None value in sap hana options

                if sap hana instance with same name already exists

                if given storage policy does not exists in commcell
    &#34;&#34;&#34;

    if self.has_instance(kwargs.get(&#39;sid&#39;, &#39;&#39;)):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                kwargs.get(&#39;sid&#39;, &#39;&#39;))
        )
    password = b64encode(kwargs.get(&#34;db_password&#34;, &#34;&#34;).encode()).decode()
    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;instance&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;instanceName&#34;: kwargs.get(&#39;sid&#39;, &#39;&#39;),
                &#34;applicationId&#34;: 135,
            },
            &#34;saphanaInstance&#34;: {
                &#34;dbInstanceNumber&#34;: &#34;00&#34;,
                &#34;hdbsqlLocationDirectory&#34;: f&#34;/usr/sap/{kwargs.get(&#39;sid&#39;, &#39;&#39;)}/HDB00/exe&#34;,
                &#34;SAPHANAUser&#34;: {
                    &#34;userName&#34;: f&#34;{kwargs.get(&#39;sid&#39;, &#39;&#39;).lower()}adm&#34;
                },
                &#34;dbUser&#34;: {
                    &#34;userName&#34;: kwargs.get(&#34;db_user_name&#34;, &#34;&#34;),
                    &#34;password&#34;: password
                },
                &#34;saphanaStorageDevice&#34;: {
                    &#34;logBackupStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: kwargs.get(&#34;storage_policy&#34;, &#34;&#34;)
                    },
                    &#34;commandLineStoragePolicy&#34;: {
                        &#34;storagePolicyName&#34;: kwargs.get(&#34;storage_policy&#34;, &#34;&#34;)
                    }
                },
                &#34;DBInstances&#34;: [
                    {
                        &#34;clientName&#34;: kwargs.get(&#34;hana_client_name&#34;, &#34;&#34;)
                    }
                ]

            }
        }
    }
    self._process_add_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.add_sybase_instance"><code class="name flex">
<span>def <span class="ident">add_sybase_instance</span></span>(<span>self, sybase_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to Add new Sybase Instance to given Client</p>
<h2 id="args">Args</h2>
<p>Dictionary of sybase instance creation options:
Example:
sybase_options = {
'instance_name': '',
'sybase_ocs': '',
'sybase_ase': '',
'backup_server': '',
'sybase_home': '',
'config_file': '',
'enable_auto_discovery': True,
'shared_memory_directory': '',
'storage_policy': '',
'sa_username': '',
'sa_password': '',
'localadmin_username': '',
'localadmin_password': '',
'masterkey_password':''
}</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if None value in sybase options</p>
<pre><code>if Sybase instance with same name already exists

if given storage policy does not exists in commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L689-L809" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_sybase_instance(self, sybase_options):
    &#34;&#34;&#34;
        Method to Add new Sybase Instance to given Client
        Args:
            Dictionary of sybase instance creation options:
                Example:
                   sybase_options = {
                        &#39;instance_name&#39;: &#39;&#39;,
                        &#39;sybase_ocs&#39;: &#39;&#39;,
                        &#39;sybase_ase&#39;: &#39;&#39;,
                        &#39;backup_server&#39;: &#39;&#39;,
                        &#39;sybase_home&#39;: &#39;&#39;,
                        &#39;config_file&#39;: &#39;&#39;,
                        &#39;enable_auto_discovery&#39;: True,
                        &#39;shared_memory_directory&#39;: &#39;&#39;,
                        &#39;storage_policy&#39;: &#39;&#39;,
                        &#39;sa_username&#39;: &#39;&#39;,
                        &#39;sa_password&#39;: &#39;&#39;,
                        &#39;localadmin_username&#39;: &#39;&#39;,
                        &#39;localadmin_password&#39;: &#39;&#39;,
                        &#39;masterkey_password&#39;:&#39;&#39;
                    }
        Raises:
            SDKException:
                if None value in sybase options

                if Sybase instance with same name already exists

                if given storage policy does not exists in commcell

    &#34;&#34;&#34;

    if None in sybase_options.values():
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#34;One of the sybase parameter is None so cannot proceed with instance creation&#34;)

    if self.has_instance(sybase_options[&#34;instance_name&#34;]):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Instance &#34;{0}&#34; already exists.&#39;.format(
                sybase_options[&#34;instance_name&#34;])
        )

    if not self._commcell_object.storage_policies.has_policy(sybase_options[&#34;storage_policy&#34;]):
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#39;Storage Policy: &#34;{0}&#34; does not exist in the Commcell&#39;.format(
                sybase_options[&#34;storage_policy&#34;])
        )

    # encodes the plain text password using base64 encoding
    sa_password = b64encode(sybase_options[&#34;sa_password&#34;].encode()).decode()

    enable_auto_discovery = sybase_options[&#34;enable_auto_discovery&#34;]

    request_json = {
        &#34;instanceProperties&#34;: {
            &#34;instance&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;appName&#34;: &#34;Sybase&#34;,
                &#34;instanceName&#34;: sybase_options[&#34;instance_name&#34;],
                &#34;_type_&#34;: 5,
                &#34;applicationId&#34;: 5
            },
            &#34;planEntity&#34;: {
                &#34;planName&#34;: sybase_options[&#34;storage_policy&#34;]
            },
            &#34;sybaseInstance&#34;: {
                &#34;sybaseOCS&#34;: sybase_options[&#34;sybase_ocs&#34;],
                &#34;sybaseBlockSize&#34;: 65536,
                &#34;backupServer&#34;: sybase_options[&#34;backup_server&#34;],
                &#34;sybaseHome&#34;: sybase_options[&#34;sybase_home&#34;],
                &#34;sybaseASE&#34;: sybase_options[&#34;sybase_ase&#34;],
                &#34;configFile&#34;: sybase_options[&#34;config_file&#34;],
                &#34;enableAutoDiscovery&#34;: enable_auto_discovery,
                &#34;sharedMemoryDirectory&#34;: sybase_options[&#34;shared_memory_directory&#34;],
                &#34;saUser&#34;: {&#34;password&#34;: sa_password, &#34;userName&#34;: sybase_options[&#34;sa_username&#34;]},
                &#34;localAdministrator&#34;: {
                    &#34;password&#34;: sybase_options[&#34;localadmin_password&#34;],
                    &#34;userName&#34;: sybase_options[&#34;localadmin_username&#34;]
                }
            }
        }
    }
    if &#34;masterkey_password&#34; in sybase_options.keys():
        masterkey_password = b64encode(sybase_options[&#34;masterkey_password&#34;].encode()).decode()
        request_json[&#34;instanceProperties&#34;][&#34;sybaseInstance&#34;][&#34;masterKeyPwd&#34;]=masterkey_password

    if &#34;localadmin_password&#34; in sybase_options.keys():
        localadmin_password = b64encode(sybase_options[&#34;localadmin_password&#34;].encode()).decode()
        request_json[&#39;instanceProperties&#39;][&#39;sybaseInstance&#39;][&#39;localAdministrator&#39;][&#39;password&#39;] = localadmin_password

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;ADD_INSTANCE&#39;], request_json
    )
    if flag:
        if response.json() and &#39;response&#39; in response.json():
            error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

            if error_code != 0:
                error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;Error while creating instance\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                )
            else:
                instance_name = response.json(
                )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceName&#39;]
                instance_id = response.json(
                )[&#39;response&#39;][&#39;entity&#39;][&#39;instanceId&#39;]
                agent_name = self._agent_object.agent_name
                self.refresh()
                return self.get(instance_name)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, instance_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the instance specified by the instance_name from the agent.</p>
<h2 id="args">Args</h2>
<p>instance_name (str)
&ndash;
name of the instance to remove from the agent</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the instance name argument is not string</p>
<pre><code>if failed to delete instance

if response is empty

if response is not success

if no instance exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L624-L687" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, instance_name):
    &#34;&#34;&#34;Deletes the instance specified by the instance_name from the agent.

        Args:
            instance_name (str)  --  name of the instance to remove from the agent

        Raises:
            SDKException:
                if type of the instance name argument is not string

                if failed to delete instance

                if response is empty

                if response is not success

                if no instance exists with the given name
    &#34;&#34;&#34;
    if not isinstance(instance_name, str):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
    else:
        instance_name = instance_name.lower()

    if self.has_instance(instance_name):
        delete_instance_service = self._commcell_object._services[&#39;INSTANCE&#39;] % (
            self._instances.get(instance_name)
        )

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;DELETE&#39;, delete_instance_service
        )

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_value = response.json()[&#39;response&#39;][0]
                    error_code = str(response_value.get(&#39;errorCode&#39;))
                    error_message = None

                    if &#39;errorString&#39; in response_value:
                        error_message = response_value[&#39;errorString&#39;]

                    if error_message:
                        o_str = &#39;Failed to delete instance\nError: &#34;{0}&#34;&#39;
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(error_message))
                    else:
                        if error_code == &#39;0&#39;:
                            # initialize the instances again
                            # so the instance object has all the instances
                            self.refresh()
                        else:
                            o_str = (&#39;Failed to delete instance with Error Code: &#34;{0}&#34;\n&#39;
                                     &#39;Please check the documentation for &#39;
                                     &#39;more details on the error&#39;)
                            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(error_code))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    else:
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists with name: {0}&#39;.format(instance_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, instance_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a instance object of the specified instance name.</p>
<h2 id="args">Args</h2>
<p>instance_name (str/int)
&ndash;
name or ID of the instance</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class for the given instance name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the instance name argument is not string or Int</p>
<pre><code>if no instance exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L363-L395" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, instance_name):
    &#34;&#34;&#34;Returns a instance object of the specified instance name.

        Args:
            instance_name (str/int)  --  name or ID of the instance

        Returns:
            object - instance of the Instance class for the given instance name

        Raises:
            SDKException:
                if type of the instance name argument is not string or Int

                if no instance exists with the given name
    &#34;&#34;&#34;
    if isinstance(instance_name, str):
        instance_name = instance_name.lower()

        if self.has_instance(instance_name):
            return Instance(self._agent_object, instance_name, self._instances[instance_name])

        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;No instance exists with name: &#34;{0}&#34;&#39;.format(instance_name)
        )
    elif isinstance(instance_name, int):
        instance_name = str(instance_name)
        instance_name = [name for name, instance_id in self.all_instances.items() if instance_name == instance_id]

        if instance_name:
            return self.get(instance_name[0])
        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists with the given ID: {0}&#39;.format(instance_name))

    raise SDKException(&#39;Instance&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.has_instance"><code class="name flex">
<span>def <span class="ident">has_instance</span></span>(<span>self, instance_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a instance exists for the agent with the input instance name.</p>
<h2 id="args">Args</h2>
<p>instance_name (str)
&ndash;
name of the instance</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the instance exists for the agent or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the instance name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L345-L361" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_instance(self, instance_name):
    &#34;&#34;&#34;Checks if a instance exists for the agent with the input instance name.

        Args:
            instance_name (str)  --  name of the instance

        Returns:
            bool - boolean output whether the instance exists for the agent or not

        Raises:
            SDKException:
                if type of the instance name argument is not string
    &#34;&#34;&#34;
    if not isinstance(instance_name, str):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    return self._instances and instance_name.lower() in self._instances</code></pre>
</details>
</dd>
<dt id="cvpysdk.instance.Instances.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the instances associated with the Agent of the selected Client.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instance.py#L1820-L1822" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the instances associated with the Agent of the selected Client.&#34;&#34;&#34;
    self._instances = self._get_instances()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#instance-attributes">Instance Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instance.Instance" href="#cvpysdk.instance.Instance">Instance</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.instance.Instance.backupsets" href="#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.browse" href="#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.credentials" href="#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.find" href="#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_id" href="#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_name" href="#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.name" href="#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.properties" href="#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.refresh" href="#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.subclients" href="#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.update_properties" href="#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.instance.Instances" href="#cvpysdk.instance.Instances">Instances</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instance.Instances.add_big_data_apps_instance" href="#cvpysdk.instance.Instances.add_big_data_apps_instance">add_big_data_apps_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_cloud_storage_instance" href="#cvpysdk.instance.Instances.add_cloud_storage_instance">add_cloud_storage_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_cosmosdb_instance" href="#cvpysdk.instance.Instances.add_cosmosdb_instance">add_cosmosdb_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_db2_instance" href="#cvpysdk.instance.Instances.add_db2_instance">add_db2_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_informix_instance" href="#cvpysdk.instance.Instances.add_informix_instance">add_informix_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_mysql_instance" href="#cvpysdk.instance.Instances.add_mysql_instance">add_mysql_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_oracle_instance" href="#cvpysdk.instance.Instances.add_oracle_instance">add_oracle_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_postgresql_instance" href="#cvpysdk.instance.Instances.add_postgresql_instance">add_postgresql_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_salesforce_instance" href="#cvpysdk.instance.Instances.add_salesforce_instance">add_salesforce_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_sap_hana_instance" href="#cvpysdk.instance.Instances.add_sap_hana_instance">add_sap_hana_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.add_sybase_instance" href="#cvpysdk.instance.Instances.add_sybase_instance">add_sybase_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.all_instances" href="#cvpysdk.instance.Instances.all_instances">all_instances</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.delete" href="#cvpysdk.instance.Instances.delete">delete</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.get" href="#cvpysdk.instance.Instances.get">get</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.has_instance" href="#cvpysdk.instance.Instances.has_instance">has_instance</a></code></li>
<li><code><a title="cvpysdk.instance.Instances.refresh" href="#cvpysdk.instance.Instances.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>