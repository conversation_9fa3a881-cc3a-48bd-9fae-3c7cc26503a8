<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.postgresinstance API documentation</title>
<meta name="description" content="File for operating on a POSTGRESQL Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.postgresinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a POSTGRESQL Instance.</p>
<p>PostgreSQLInstance is the only class defined in this file.</p>
<p>PostgreSQLInstance: Derived class from Instance Base class, representing a postgres server instance,
and to perform operations on that instance</p>
<h1 id="postgresqlinstance">PostgreSQLInstance:</h1>
<pre><code>_get_instance_properties()           --     Gets the properties of this instance

_get_instance_properties_json()      --     Gets all the instance related properties of
PostgreSQL instance.

_restore_json()                      --     returns the JSON request to pass to the API as per
the options selected by the user

_restore_common_options_json()       --     setter for the common options in restore JSON

_restore_destination_json()          --     setter for the Destination options in restore JSON

_restore_postgres_option_json()      --     setter for the postgres restore option
in restore JSONRe

restore_in_place()                   --     Restores the postgres data/log files
specified in the input paths list to the same location

change_sa_password()                 --     Changes postgresql user password
</code></pre>
<h1 id="postgresqlinstance-instance-attributes">PostgreSQLInstance instance Attributes</h1>
<pre><code>**postgres_bin_directory**           --  returns the postgres bin directory of postgres server

**postgres_lib_directory**           --  returns the lib directory of postgres server

**postgres_archive_log_directory**   --  returns the postgres archive log directory
of postgres server

**log_storage_policy**               --  returns the log storage policy for the instance

**postgres_server_user_name**        --  returns the postgres server user name
of postgres server

**postgres_server_port_number**      --  returns the postgres server port number
of postgres server

**maintenance_database**             --  returns the maintenance database associated
with postgres server

**postgres_version**                 --  returns the postgres server version

**standby_instance_name**            --  Returns the standby instance name

**standby_instance_id**              --  Returns the standby instance id

**use_master_for_log_backup**        --  Returns True if master is used for log backup

**use_master_for_data_backup**       --  Returns True if master is used for data backup

**archive_delete**                   --  Returns True if archive delete is enabled for instance

**postgres_ssl_status**              --  Returns True/False based on if ssl is enabled or not

**postgres_ssl_ca_file**             --  Returns SSL CA file path

**postgres_ssl_key_file**            --  Returns SSL key file path

**postgres_ssl_cert_file**           --  Returns SSL cert file path
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L1-L726" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a POSTGRESQL Instance.

PostgreSQLInstance is the only class defined in this file.

PostgreSQLInstance: Derived class from Instance Base class, representing a postgres server instance,
                       and to perform operations on that instance

PostgreSQLInstance:
===================

    _get_instance_properties()           --     Gets the properties of this instance

    _get_instance_properties_json()      --     Gets all the instance related properties of
    PostgreSQL instance.

    _restore_json()                      --     returns the JSON request to pass to the API as per
    the options selected by the user

    _restore_common_options_json()       --     setter for the common options in restore JSON

    _restore_destination_json()          --     setter for the Destination options in restore JSON

    _restore_postgres_option_json()      --     setter for the postgres restore option
    in restore JSONRe

    restore_in_place()                   --     Restores the postgres data/log files
    specified in the input paths list to the same location

    change_sa_password()                 --     Changes postgresql user password

PostgreSQLInstance instance Attributes
======================================

    **postgres_bin_directory**           --  returns the postgres bin directory of postgres server

    **postgres_lib_directory**           --  returns the lib directory of postgres server

    **postgres_archive_log_directory**   --  returns the postgres archive log directory
    of postgres server

    **log_storage_policy**               --  returns the log storage policy for the instance

    **postgres_server_user_name**        --  returns the postgres server user name
    of postgres server

    **postgres_server_port_number**      --  returns the postgres server port number
    of postgres server

    **maintenance_database**             --  returns the maintenance database associated
    with postgres server

    **postgres_version**                 --  returns the postgres server version

    **standby_instance_name**            --  Returns the standby instance name

    **standby_instance_id**              --  Returns the standby instance id

    **use_master_for_log_backup**        --  Returns True if master is used for log backup

    **use_master_for_data_backup**       --  Returns True if master is used for data backup

    **archive_delete**                   --  Returns True if archive delete is enabled for instance

    **postgres_ssl_status**              --  Returns True/False based on if ssl is enabled or not

    **postgres_ssl_ca_file**             --  Returns SSL CA file path

    **postgres_ssl_key_file**            --  Returns SSL key file path

    **postgres_ssl_cert_file**           --  Returns SSL cert file path

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from base64 import b64encode
from ..instance import Instance
from ..exception import SDKException


class PostgreSQLInstance(Instance):
    &#34;&#34;&#34;Derived class from Instance Base class, representing a POSTGRESQL instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id):
        &#34;&#34;&#34;Initialize object of the Instances class.

            Args:
                agent_object (object)  --  instance of the Agent class

            Returns:
                object - instance of the Instances class

        &#34;&#34;&#34;
        super(
            PostgreSQLInstance,
            self).__init__(
            agent_object,
            instance_name,
            instance_id)
        self.backup_object = None
        self.backupset_object = None
        self.sub_client_object = None
        self.postgres_restore_json = None
        self._postgres_restore_options = None
        self._destination_restore_json = None

    @property
    def postgres_bin_directory(self):
        &#34;&#34;&#34;Returns the bin directory of postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;][&#39;BinaryDirectory&#39;]:
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;BinaryDirectory&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the Binary directory.&#34;)

    @property
    def postgres_lib_directory(self):
        &#34;&#34;&#34;Returns the lib directory of postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;][&#39;LibDirectory&#39;]:
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;LibDirectory&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the Lib directory.&#34;)

    @property
    def postgres_archive_log_directory(self):
        &#34;&#34;&#34;Returns the archive log directory of postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;][&#39;ArchiveLogDirectory&#39;]:
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;ArchiveLogDirectory&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the Archive log directory.&#34;)

    @property
    def log_storage_policy(self):
        &#34;&#34;&#34;Returns the log storage policy for the instance

            Return Type: str
                        Default: None
        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;logStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, None)

    @log_storage_policy.setter
    def log_storage_policy(self, value):
        &#34;&#34;&#34; Setter for log storage policy in instance property

            Args:

                value (str)  -- Storage policy name

        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;logStoragePolicy&#39;] = {}
        properties[&#39;postGreSQLInstance&#39;][&#39;logStoragePolicy&#39;][&#39;storagePolicyName&#39;] = value
        self.update_properties(properties)

    @property
    def postgres_server_user_name(self):
        &#34;&#34;&#34;Returns the username of postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self.credentials:
            return self._commcell_object.credentials.get(self.credentials).credential_user_name
        else:
            if self._properties[&#39;postGreSQLInstance&#39;][&#39;SAUser&#39;][&#39;userName&#39;]:
                return self._properties[&#39;postGreSQLInstance&#39;][&#39;SAUser&#39;][&#39;userName&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the Server name.&#34;)

    @property
    def postgres_server_port_number(self):
        &#34;&#34;&#34;Returns the port number associated with postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;][&#39;port&#39;]:
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;port&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the port Number.&#34;)

    @property
    def maintenance_database(self):
        &#34;&#34;&#34;Returns the maintenance database associated with postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;].get(&#39;MaintainenceDB&#39;):
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;MaintainenceDB&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch maintenance database.&#34;)

    @property
    def postgres_version(self):
        &#34;&#34;&#34;Returns the postgres server version

            Return Type: str

        &#34;&#34;&#34;
        if self._properties.get(&#39;version&#39;):
            return self._properties[&#39;version&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch postgres version.&#34;)

    @property
    def archive_delete(self):
        &#34;&#34;&#34;Returns True if archive delete enabled. False if not

            Return Type: bool

        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;ArchiveDelete&#39;, False)

    @archive_delete.setter
    def archive_delete(self, value):
        &#34;&#34;&#34; Setter for archive delete instance property

            Args:

                value (bool)  -- True to enable archive delete

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;ArchiveDelete&#39;] = value
        self.update_properties(properties)

    @property
    def standby_instance_name(self):
        &#34;&#34;&#34;Returns the standby instance name

            Return Type: str

        &#34;&#34;&#34;
        if self.is_standby_enabled:
            return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;standbyInstance&#39;,
                                                                                                {}).get(&#39;instanceName&#39;,
                                                                                                        &#34;&#34;)
        return None

    @property
    def standby_instance_id(self):
        &#34;&#34;&#34;Returns the standby instance id

            Return Type: str

        &#34;&#34;&#34;
        if self.is_standby_enabled:
            return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;standbyInstance&#39;,
                                                                                                {}).get(&#39;instanceId&#39;,
                                                                                                        &#34;&#34;)
        return None

    @property
    def is_standby_enabled(self):
        &#34;&#34;&#34;Returns True if standby enabled. False if not

            Return Type: bool

        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;isStandbyEnabled&#39;, False)

    @property
    def use_master_for_log_backup(self):
        &#34;&#34;&#34; Returns True if master is used for log backup

            Return Type: bool

        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;useMasterForLogBkp&#39;, False)

    @use_master_for_log_backup.setter
    def use_master_for_log_backup(self, value):
        &#34;&#34;&#34; Setter for user master for log backup standby property

            Args:

                value (bool)  -- True to use master for log backup

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;standbyOptions&#39;][&#39;useMasterForLogBkp&#39;] = value
        self.update_properties(properties)

    @property
    def use_master_for_data_backup(self):
        &#34;&#34;&#34; Returns True if master is used for data backup

            Return Type: bool

        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;useMasterForDataBkp&#39;,
                                                                                            False)

    @use_master_for_data_backup.setter
    def use_master_for_data_backup(self, value):
        &#34;&#34;&#34; Setter for user master for data backup standby property

            Args:

                value (bool)  -- True to use master for data backup

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;standbyOptions&#39;][&#39;useMasterForDataBkp&#39;] = value
        self.update_properties(properties)

    @property
    def postgres_ssl_status(self):
        &#34;&#34;&#34;Returns True/False based on if ssl is enabled or not&#34;&#34;&#34;
        return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslEnabled&#34;, False)

    @property
    def postgres_ssl_ca_file(self):
        &#34;&#34;&#34;Returns: str - ssl ca file path&#34;&#34;&#34;
        return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslCa&#34;, &#34;&#34;)

    @property
    def postgres_ssl_key_file(self):
        &#34;&#34;&#34;Returns: str - ssl key file path&#34;&#34;&#34;
        return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslKey&#34;, &#34;&#34;)

    @property
    def postgres_ssl_cert_file(self):
        &#34;&#34;&#34;Returns:str - ssl cert file path&#34;&#34;&#34;
        return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslCert&#34;, &#34;&#34;)

    def change_sa_password(self, value):
        &#34;&#34;&#34; Changes postgresql user password

            Args:

                value (bool)  -- PostgreSQL password

        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;SAUser&#39;][&#39;password&#39;] = b64encode(value.encode()).decode()
        self.update_properties(properties)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(PostgreSQLInstance, self)._get_instance_properties()
        self._postgresql_instance = self._properties[&#39;postGreSQLInstance&#39;]

    def _get_instance_properties_json(self):
        &#34;&#34;&#34; Gets all the instance related properties of PostgreSQL instance.

           Returns:
                dict - all instance properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;:
                {
                    &#34;instance&#34;: self._instance,
                    &#34;postGreSQLInstance&#34;: self._postgresql_instance
                }
        }
        return instance_json

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (dict)  --  Dictionary of options need to be set for restore

            Returns:
                dict             -- JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(PostgreSQLInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        self._restore_postgres_option_json(restore_option)
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;postgresRstOption&#34;] = self.postgres_restore_json
        return rest_json

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;setter for  the Common options of in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        super(PostgreSQLInstance, self)._restore_common_options_json(value)
        if value.get(&#34;baseline_jobid&#34;):
            self._commonoption_restore_json = {
                &#34;clusterDBBackedup&#34;: False,
                &#34;restoreToDisk&#34;: False,
                &#34;baselineBackup&#34;: 1,
                &#34;baselineRefTime&#34;: value.get(&#34;baseline_ref_time&#34;, &#34;&#34;),
                &#34;baselineJobId&#34;: value.get(&#34;baseline_jobid&#34;, &#34;&#34;),
                &#34;copyToObjectStore&#34;: False,
                &#34;onePassRestore&#34;: False,
                &#34;syncRestore&#34;: value.get(&#34;sync_restore&#34;, True)
            }

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;setter for the Destination options in restore JSON

            Args:
                value   (dict)  --  Dictionary of options need to be set for restore

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if value.get(&#34;restore_to_disk&#34;):
            return super(PostgreSQLInstance, self)._restore_destination_json(value)

        else:
            self._destination_restore_json = {
                &#34;destinationInstance&#34;: {
                    &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;),
                    &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                    &#34;appName&#34;: self._agent_object.agent_name
                },
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
                }
            }

    def _restore_postgres_option_json(self, value):
        &#34;&#34;&#34;setter for the restore option in restore JSON

            Args:
                value   (dict)  --  Dictionary of options need to be set for restore

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self.postgres_restore_json = self._postgres_restore_options = {
            &#34;restoreToSameServer&#34;: False,
            &#34;tableLevelRestore&#34;: value.get(&#34;table_level_restore&#34;, False),
            &#34;instanceRestore&#34;: False,
            &#34;fsBackupSetRestore&#34;: value.get(&#34;backupset_flag&#34;, &#34;&#34;),
            &#34;isCloneRestore&#34;: value.get(&#34;clone_env&#34;, False),
            &#34;refTime&#34;: {}
        }

        if value.get(&#34;clone_env&#34;, False):
            self.postgres_restore_json[&#34;cloneOptions&#34;] = value.get(&#34;clone_options&#34;, &#34;&#34;)

        if value.get(&#34;to_time&#34;):
            time_value = {&#34;timevalue&#34;: value.get(&#34;to_time&#34;, &#34;&#34;)}
            self.postgres_restore_json[&#34;refTime&#34;] = time_value
            self.postgres_restore_json[&#34;fromTime&#34;] = time_value
            self.postgres_restore_json[&#34;pointOfTime&#34;] = time_value

        if value.get(&#34;table_level_restore&#34;):
            self.postgres_restore_json[&#34;stagingPath&#34;] = value.get(&#34;staging_path&#34;, &#34;&#34;)
            self.postgres_restore_json[&#34;auxilaryMap&#34;] = []
            database_list = []
            for table_path in value.get(&#34;paths&#34;):
                database_list.append(table_path.split(&#39;/&#39;)[1])
            database_list = set(database_list)
            for database_name in database_list:
                self.postgres_restore_json[&#34;auxilaryMap&#34;].append({&#34;sourceDB&#34;: database_name})

        if value.get(&#34;redirect_path&#34;):
            self.postgres_restore_json[&#34;redirectEnabled&#34;] = True
            self.postgres_restore_json[&#34;redirectItems&#34;] = [value.get(&#34;redirect_path&#34;)]

        if value.get(&#34;restore_to_disk&#34;):
            self.postgres_restore_json[&#34;fsBackupSetRestore&#34;] = False

    def restore_in_place(
            self,
            path,
            dest_client_name,
            dest_instance_name,
            backupset_name,
            backupset_flag,
            overwrite=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            clone_env=False,
            clone_options=None,
            media_agent=None,
            table_level_restore=False,
            staging_path=None,
            no_of_streams=None,
            volume_level_restore=False,
            redirect_enabled=False,
            redirect_path=None,
            restore_to_disk=False,
            restore_to_disk_job=None,
            destination_path=None,
            revert=False):
        &#34;&#34;&#34;Restores the postgres data/log files specified in the input paths
        list to the same location.

            Args:
                path                    (list)  --  list of database/databases to be restored

                dest_client_name        (str)   --  destination client name where files are to be
                restored

                dest_instance_name      (str)   --  destination postgres instance name of
                destination client

                backupset_name          (str)   --  destination postgres backupset name of
                destination client

                backupset_flag          (bool)  --  flag to indicate fsbased backup

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                clone_env               (bool)  --  boolean to specify whether the database
                should be cloned or not

                    default: False

                clone_options           (dict)  --  clone restore options passed in a dict

                    default: None

                    Accepted format: {
                                        &#34;stagingLocaion&#34;: &#34;/gk_snap&#34;,
                                        &#34;forceCleanup&#34;: True,
                                        &#34;port&#34;: &#34;5595&#34;,
                                        &#34;libDirectory&#34;: &#34;/opt/PostgreSQL/9.6/lib&#34;,
                                        &#34;isInstanceSelected&#34;: True,
                                        &#34;reservationPeriodS&#34;: 3600,
                                        &#34;user&#34;: &#34;postgres&#34;,
                                        &#34;binaryDirectory&#34;: &#34;/opt/PostgreSQL/9.6/bin&#34;
                                     }

                media_agent             (str)   --  media agent name

                    default: None

                table_level_restore     (bool)  --  boolean to specify if the restore operation
                is table level

                    default: False

                staging_path            (str)   --  staging path location for table level restore

                    default: None

                no_of_streams           (int)   --  number of streams to be used by
                volume level restore

                    default: None

                volume_level_restore    (bool)  --  volume level restore flag

                    default: False

                redirect_enabled         (bool)  --  boolean to specify if redirect restore is
                enabled

                    default: False

                redirect_path           (str)   --  Path specified in advanced restore options
                in order to perform redirect restore

                    default: None

                restore_to_disk         (bool)  --  restore to disk flag

                    default: False

                restore_to_disk_job     (int)   --  backup job id to restore to disk

                    default: None

                destination_path        (str)   --  destinath path for restore

                    default: None

                revert                  (bool)  --  boolean to specify whether to do a
                                                    hardware revert in restore
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(path, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not path:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        if not no_of_streams:
            no_of_streams = 1

        index_free_restore = False
        if restore_to_disk:
            index_free_restore = True

        request_json = self._restore_json(
            paths=path,
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            backupset_name=backupset_name,
            backupset_flag=backupset_flag,
            copy_precedence=copy_precedence,
            overwrite=overwrite,
            from_time=from_time,
            to_time=to_time,
            clone_env=clone_env,
            clone_options=clone_options,
            media_agent=media_agent,
            table_level_restore=table_level_restore,
            staging_path=staging_path,
            no_of_streams=no_of_streams,
            volume_level_restore=volume_level_restore,
            redirect_enabled=redirect_enabled,
            redirect_path=redirect_path,
            restore_to_disk=restore_to_disk,
            index_free_restore=index_free_restore,
            destination_path=destination_path,
            restore_jobs=restore_to_disk_job)

        if volume_level_restore:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;destination&#39;][&#34;noOfStreams&#34;] = no_of_streams

        if restore_to_disk:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;destination&#39;][&#34;destPath&#34;] = [destination_path]

        if revert:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;commonOptions&#39;][&#34;revert&#34;] = revert
        return self._process_restore_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance"><code class="flex name class">
<span>class <span class="ident">PostgreSQLInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Instance Base class, representing a POSTGRESQL instance,
and to perform operations on that Instance.</p>
<p>Initialize object of the Instances class.</p>
<h2 id="args">Args</h2>
<p>agent_object (object)
&ndash;
instance of the Agent class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instances class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L100-L726" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class PostgreSQLInstance(Instance):
    &#34;&#34;&#34;Derived class from Instance Base class, representing a POSTGRESQL instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id):
        &#34;&#34;&#34;Initialize object of the Instances class.

            Args:
                agent_object (object)  --  instance of the Agent class

            Returns:
                object - instance of the Instances class

        &#34;&#34;&#34;
        super(
            PostgreSQLInstance,
            self).__init__(
            agent_object,
            instance_name,
            instance_id)
        self.backup_object = None
        self.backupset_object = None
        self.sub_client_object = None
        self.postgres_restore_json = None
        self._postgres_restore_options = None
        self._destination_restore_json = None

    @property
    def postgres_bin_directory(self):
        &#34;&#34;&#34;Returns the bin directory of postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;][&#39;BinaryDirectory&#39;]:
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;BinaryDirectory&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the Binary directory.&#34;)

    @property
    def postgres_lib_directory(self):
        &#34;&#34;&#34;Returns the lib directory of postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;][&#39;LibDirectory&#39;]:
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;LibDirectory&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the Lib directory.&#34;)

    @property
    def postgres_archive_log_directory(self):
        &#34;&#34;&#34;Returns the archive log directory of postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;][&#39;ArchiveLogDirectory&#39;]:
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;ArchiveLogDirectory&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the Archive log directory.&#34;)

    @property
    def log_storage_policy(self):
        &#34;&#34;&#34;Returns the log storage policy for the instance

            Return Type: str
                        Default: None
        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;logStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, None)

    @log_storage_policy.setter
    def log_storage_policy(self, value):
        &#34;&#34;&#34; Setter for log storage policy in instance property

            Args:

                value (str)  -- Storage policy name

        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;logStoragePolicy&#39;] = {}
        properties[&#39;postGreSQLInstance&#39;][&#39;logStoragePolicy&#39;][&#39;storagePolicyName&#39;] = value
        self.update_properties(properties)

    @property
    def postgres_server_user_name(self):
        &#34;&#34;&#34;Returns the username of postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self.credentials:
            return self._commcell_object.credentials.get(self.credentials).credential_user_name
        else:
            if self._properties[&#39;postGreSQLInstance&#39;][&#39;SAUser&#39;][&#39;userName&#39;]:
                return self._properties[&#39;postGreSQLInstance&#39;][&#39;SAUser&#39;][&#39;userName&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the Server name.&#34;)

    @property
    def postgres_server_port_number(self):
        &#34;&#34;&#34;Returns the port number associated with postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;][&#39;port&#39;]:
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;port&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch the port Number.&#34;)

    @property
    def maintenance_database(self):
        &#34;&#34;&#34;Returns the maintenance database associated with postgres server

            Return Type: str

        &#34;&#34;&#34;
        if self._properties[&#39;postGreSQLInstance&#39;].get(&#39;MaintainenceDB&#39;):
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;MaintainenceDB&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch maintenance database.&#34;)

    @property
    def postgres_version(self):
        &#34;&#34;&#34;Returns the postgres server version

            Return Type: str

        &#34;&#34;&#34;
        if self._properties.get(&#39;version&#39;):
            return self._properties[&#39;version&#39;]
        raise SDKException(
            &#39;Instance&#39;,
            &#39;105&#39;,
            &#34;Could not fetch postgres version.&#34;)

    @property
    def archive_delete(self):
        &#34;&#34;&#34;Returns True if archive delete enabled. False if not

            Return Type: bool

        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;ArchiveDelete&#39;, False)

    @archive_delete.setter
    def archive_delete(self, value):
        &#34;&#34;&#34; Setter for archive delete instance property

            Args:

                value (bool)  -- True to enable archive delete

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;ArchiveDelete&#39;] = value
        self.update_properties(properties)

    @property
    def standby_instance_name(self):
        &#34;&#34;&#34;Returns the standby instance name

            Return Type: str

        &#34;&#34;&#34;
        if self.is_standby_enabled:
            return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;standbyInstance&#39;,
                                                                                                {}).get(&#39;instanceName&#39;,
                                                                                                        &#34;&#34;)
        return None

    @property
    def standby_instance_id(self):
        &#34;&#34;&#34;Returns the standby instance id

            Return Type: str

        &#34;&#34;&#34;
        if self.is_standby_enabled:
            return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;standbyInstance&#39;,
                                                                                                {}).get(&#39;instanceId&#39;,
                                                                                                        &#34;&#34;)
        return None

    @property
    def is_standby_enabled(self):
        &#34;&#34;&#34;Returns True if standby enabled. False if not

            Return Type: bool

        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;isStandbyEnabled&#39;, False)

    @property
    def use_master_for_log_backup(self):
        &#34;&#34;&#34; Returns True if master is used for log backup

            Return Type: bool

        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;useMasterForLogBkp&#39;, False)

    @use_master_for_log_backup.setter
    def use_master_for_log_backup(self, value):
        &#34;&#34;&#34; Setter for user master for log backup standby property

            Args:

                value (bool)  -- True to use master for log backup

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;standbyOptions&#39;][&#39;useMasterForLogBkp&#39;] = value
        self.update_properties(properties)

    @property
    def use_master_for_data_backup(self):
        &#34;&#34;&#34; Returns True if master is used for data backup

            Return Type: bool

        &#34;&#34;&#34;
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;useMasterForDataBkp&#39;,
                                                                                            False)

    @use_master_for_data_backup.setter
    def use_master_for_data_backup(self, value):
        &#34;&#34;&#34; Setter for user master for data backup standby property

            Args:

                value (bool)  -- True to use master for data backup

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;standbyOptions&#39;][&#39;useMasterForDataBkp&#39;] = value
        self.update_properties(properties)

    @property
    def postgres_ssl_status(self):
        &#34;&#34;&#34;Returns True/False based on if ssl is enabled or not&#34;&#34;&#34;
        return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslEnabled&#34;, False)

    @property
    def postgres_ssl_ca_file(self):
        &#34;&#34;&#34;Returns: str - ssl ca file path&#34;&#34;&#34;
        return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslCa&#34;, &#34;&#34;)

    @property
    def postgres_ssl_key_file(self):
        &#34;&#34;&#34;Returns: str - ssl key file path&#34;&#34;&#34;
        return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslKey&#34;, &#34;&#34;)

    @property
    def postgres_ssl_cert_file(self):
        &#34;&#34;&#34;Returns:str - ssl cert file path&#34;&#34;&#34;
        return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslCert&#34;, &#34;&#34;)

    def change_sa_password(self, value):
        &#34;&#34;&#34; Changes postgresql user password

            Args:

                value (bool)  -- PostgreSQL password

        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;postGreSQLInstance&#39;][&#39;SAUser&#39;][&#39;password&#39;] = b64encode(value.encode()).decode()
        self.update_properties(properties)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(PostgreSQLInstance, self)._get_instance_properties()
        self._postgresql_instance = self._properties[&#39;postGreSQLInstance&#39;]

    def _get_instance_properties_json(self):
        &#34;&#34;&#34; Gets all the instance related properties of PostgreSQL instance.

           Returns:
                dict - all instance properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;:
                {
                    &#34;instance&#34;: self._instance,
                    &#34;postGreSQLInstance&#34;: self._postgresql_instance
                }
        }
        return instance_json

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (dict)  --  Dictionary of options need to be set for restore

            Returns:
                dict             -- JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(PostgreSQLInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        self._restore_postgres_option_json(restore_option)
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;postgresRstOption&#34;] = self.postgres_restore_json
        return rest_json

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;setter for  the Common options of in restore JSON&#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        super(PostgreSQLInstance, self)._restore_common_options_json(value)
        if value.get(&#34;baseline_jobid&#34;):
            self._commonoption_restore_json = {
                &#34;clusterDBBackedup&#34;: False,
                &#34;restoreToDisk&#34;: False,
                &#34;baselineBackup&#34;: 1,
                &#34;baselineRefTime&#34;: value.get(&#34;baseline_ref_time&#34;, &#34;&#34;),
                &#34;baselineJobId&#34;: value.get(&#34;baseline_jobid&#34;, &#34;&#34;),
                &#34;copyToObjectStore&#34;: False,
                &#34;onePassRestore&#34;: False,
                &#34;syncRestore&#34;: value.get(&#34;sync_restore&#34;, True)
            }

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;setter for the Destination options in restore JSON

            Args:
                value   (dict)  --  Dictionary of options need to be set for restore

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if value.get(&#34;restore_to_disk&#34;):
            return super(PostgreSQLInstance, self)._restore_destination_json(value)

        else:
            self._destination_restore_json = {
                &#34;destinationInstance&#34;: {
                    &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;),
                    &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                    &#34;appName&#34;: self._agent_object.agent_name
                },
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
                }
            }

    def _restore_postgres_option_json(self, value):
        &#34;&#34;&#34;setter for the restore option in restore JSON

            Args:
                value   (dict)  --  Dictionary of options need to be set for restore

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self.postgres_restore_json = self._postgres_restore_options = {
            &#34;restoreToSameServer&#34;: False,
            &#34;tableLevelRestore&#34;: value.get(&#34;table_level_restore&#34;, False),
            &#34;instanceRestore&#34;: False,
            &#34;fsBackupSetRestore&#34;: value.get(&#34;backupset_flag&#34;, &#34;&#34;),
            &#34;isCloneRestore&#34;: value.get(&#34;clone_env&#34;, False),
            &#34;refTime&#34;: {}
        }

        if value.get(&#34;clone_env&#34;, False):
            self.postgres_restore_json[&#34;cloneOptions&#34;] = value.get(&#34;clone_options&#34;, &#34;&#34;)

        if value.get(&#34;to_time&#34;):
            time_value = {&#34;timevalue&#34;: value.get(&#34;to_time&#34;, &#34;&#34;)}
            self.postgres_restore_json[&#34;refTime&#34;] = time_value
            self.postgres_restore_json[&#34;fromTime&#34;] = time_value
            self.postgres_restore_json[&#34;pointOfTime&#34;] = time_value

        if value.get(&#34;table_level_restore&#34;):
            self.postgres_restore_json[&#34;stagingPath&#34;] = value.get(&#34;staging_path&#34;, &#34;&#34;)
            self.postgres_restore_json[&#34;auxilaryMap&#34;] = []
            database_list = []
            for table_path in value.get(&#34;paths&#34;):
                database_list.append(table_path.split(&#39;/&#39;)[1])
            database_list = set(database_list)
            for database_name in database_list:
                self.postgres_restore_json[&#34;auxilaryMap&#34;].append({&#34;sourceDB&#34;: database_name})

        if value.get(&#34;redirect_path&#34;):
            self.postgres_restore_json[&#34;redirectEnabled&#34;] = True
            self.postgres_restore_json[&#34;redirectItems&#34;] = [value.get(&#34;redirect_path&#34;)]

        if value.get(&#34;restore_to_disk&#34;):
            self.postgres_restore_json[&#34;fsBackupSetRestore&#34;] = False

    def restore_in_place(
            self,
            path,
            dest_client_name,
            dest_instance_name,
            backupset_name,
            backupset_flag,
            overwrite=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            clone_env=False,
            clone_options=None,
            media_agent=None,
            table_level_restore=False,
            staging_path=None,
            no_of_streams=None,
            volume_level_restore=False,
            redirect_enabled=False,
            redirect_path=None,
            restore_to_disk=False,
            restore_to_disk_job=None,
            destination_path=None,
            revert=False):
        &#34;&#34;&#34;Restores the postgres data/log files specified in the input paths
        list to the same location.

            Args:
                path                    (list)  --  list of database/databases to be restored

                dest_client_name        (str)   --  destination client name where files are to be
                restored

                dest_instance_name      (str)   --  destination postgres instance name of
                destination client

                backupset_name          (str)   --  destination postgres backupset name of
                destination client

                backupset_flag          (bool)  --  flag to indicate fsbased backup

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                clone_env               (bool)  --  boolean to specify whether the database
                should be cloned or not

                    default: False

                clone_options           (dict)  --  clone restore options passed in a dict

                    default: None

                    Accepted format: {
                                        &#34;stagingLocaion&#34;: &#34;/gk_snap&#34;,
                                        &#34;forceCleanup&#34;: True,
                                        &#34;port&#34;: &#34;5595&#34;,
                                        &#34;libDirectory&#34;: &#34;/opt/PostgreSQL/9.6/lib&#34;,
                                        &#34;isInstanceSelected&#34;: True,
                                        &#34;reservationPeriodS&#34;: 3600,
                                        &#34;user&#34;: &#34;postgres&#34;,
                                        &#34;binaryDirectory&#34;: &#34;/opt/PostgreSQL/9.6/bin&#34;
                                     }

                media_agent             (str)   --  media agent name

                    default: None

                table_level_restore     (bool)  --  boolean to specify if the restore operation
                is table level

                    default: False

                staging_path            (str)   --  staging path location for table level restore

                    default: None

                no_of_streams           (int)   --  number of streams to be used by
                volume level restore

                    default: None

                volume_level_restore    (bool)  --  volume level restore flag

                    default: False

                redirect_enabled         (bool)  --  boolean to specify if redirect restore is
                enabled

                    default: False

                redirect_path           (str)   --  Path specified in advanced restore options
                in order to perform redirect restore

                    default: None

                restore_to_disk         (bool)  --  restore to disk flag

                    default: False

                restore_to_disk_job     (int)   --  backup job id to restore to disk

                    default: None

                destination_path        (str)   --  destinath path for restore

                    default: None

                revert                  (bool)  --  boolean to specify whether to do a
                                                    hardware revert in restore
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(path, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not path:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        if not no_of_streams:
            no_of_streams = 1

        index_free_restore = False
        if restore_to_disk:
            index_free_restore = True

        request_json = self._restore_json(
            paths=path,
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            backupset_name=backupset_name,
            backupset_flag=backupset_flag,
            copy_precedence=copy_precedence,
            overwrite=overwrite,
            from_time=from_time,
            to_time=to_time,
            clone_env=clone_env,
            clone_options=clone_options,
            media_agent=media_agent,
            table_level_restore=table_level_restore,
            staging_path=staging_path,
            no_of_streams=no_of_streams,
            volume_level_restore=volume_level_restore,
            redirect_enabled=redirect_enabled,
            redirect_path=redirect_path,
            restore_to_disk=restore_to_disk,
            index_free_restore=index_free_restore,
            destination_path=destination_path,
            restore_jobs=restore_to_disk_job)

        if volume_level_restore:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;destination&#39;][&#34;noOfStreams&#34;] = no_of_streams

        if restore_to_disk:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;destination&#39;][&#34;destPath&#34;] = [destination_path]

        if revert:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;commonOptions&#39;][&#34;revert&#34;] = revert
        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.archive_delete"><code class="name">var <span class="ident">archive_delete</span></code></dt>
<dd>
<div class="desc"><p>Returns True if archive delete enabled. False if not</p>
<p>Return Type: bool</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L253-L260" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archive_delete(self):
    &#34;&#34;&#34;Returns True if archive delete enabled. False if not

        Return Type: bool

    &#34;&#34;&#34;
    return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;ArchiveDelete&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.is_standby_enabled"><code class="name">var <span class="ident">is_standby_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns True if standby enabled. False if not</p>
<p>Return Type: bool</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L303-L310" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_standby_enabled(self):
    &#34;&#34;&#34;Returns True if standby enabled. False if not

        Return Type: bool

    &#34;&#34;&#34;
    return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;isStandbyEnabled&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.log_storage_policy"><code class="name">var <span class="ident">log_storage_policy</span></code></dt>
<dd>
<div class="desc"><p>Returns the log storage policy for the instance</p>
<p>Return Type: str
Default: None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L169-L176" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def log_storage_policy(self):
    &#34;&#34;&#34;Returns the log storage policy for the instance

        Return Type: str
                    Default: None
    &#34;&#34;&#34;
    return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;logStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.maintenance_database"><code class="name">var <span class="ident">maintenance_database</span></code></dt>
<dd>
<div class="desc"><p>Returns the maintenance database associated with postgres server</p>
<p>Return Type: str</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L225-L237" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def maintenance_database(self):
    &#34;&#34;&#34;Returns the maintenance database associated with postgres server

        Return Type: str

    &#34;&#34;&#34;
    if self._properties[&#39;postGreSQLInstance&#39;].get(&#39;MaintainenceDB&#39;):
        return self._properties[&#39;postGreSQLInstance&#39;][&#39;MaintainenceDB&#39;]
    raise SDKException(
        &#39;Instance&#39;,
        &#39;105&#39;,
        &#34;Could not fetch maintenance database.&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_archive_log_directory"><code class="name">var <span class="ident">postgres_archive_log_directory</span></code></dt>
<dd>
<div class="desc"><p>Returns the archive log directory of postgres server</p>
<p>Return Type: str</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L155-L167" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_archive_log_directory(self):
    &#34;&#34;&#34;Returns the archive log directory of postgres server

        Return Type: str

    &#34;&#34;&#34;
    if self._properties[&#39;postGreSQLInstance&#39;][&#39;ArchiveLogDirectory&#39;]:
        return self._properties[&#39;postGreSQLInstance&#39;][&#39;ArchiveLogDirectory&#39;]
    raise SDKException(
        &#39;Instance&#39;,
        &#39;105&#39;,
        &#34;Could not fetch the Archive log directory.&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_bin_directory"><code class="name">var <span class="ident">postgres_bin_directory</span></code></dt>
<dd>
<div class="desc"><p>Returns the bin directory of postgres server</p>
<p>Return Type: str</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L127-L139" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_bin_directory(self):
    &#34;&#34;&#34;Returns the bin directory of postgres server

        Return Type: str

    &#34;&#34;&#34;
    if self._properties[&#39;postGreSQLInstance&#39;][&#39;BinaryDirectory&#39;]:
        return self._properties[&#39;postGreSQLInstance&#39;][&#39;BinaryDirectory&#39;]
    raise SDKException(
        &#39;Instance&#39;,
        &#39;105&#39;,
        &#34;Could not fetch the Binary directory.&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_lib_directory"><code class="name">var <span class="ident">postgres_lib_directory</span></code></dt>
<dd>
<div class="desc"><p>Returns the lib directory of postgres server</p>
<p>Return Type: str</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L141-L153" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_lib_directory(self):
    &#34;&#34;&#34;Returns the lib directory of postgres server

        Return Type: str

    &#34;&#34;&#34;
    if self._properties[&#39;postGreSQLInstance&#39;][&#39;LibDirectory&#39;]:
        return self._properties[&#39;postGreSQLInstance&#39;][&#39;LibDirectory&#39;]
    raise SDKException(
        &#39;Instance&#39;,
        &#39;105&#39;,
        &#34;Could not fetch the Lib directory.&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_server_port_number"><code class="name">var <span class="ident">postgres_server_port_number</span></code></dt>
<dd>
<div class="desc"><p>Returns the port number associated with postgres server</p>
<p>Return Type: str</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L211-L223" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_server_port_number(self):
    &#34;&#34;&#34;Returns the port number associated with postgres server

        Return Type: str

    &#34;&#34;&#34;
    if self._properties[&#39;postGreSQLInstance&#39;][&#39;port&#39;]:
        return self._properties[&#39;postGreSQLInstance&#39;][&#39;port&#39;]
    raise SDKException(
        &#39;Instance&#39;,
        &#39;105&#39;,
        &#34;Could not fetch the port Number.&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_server_user_name"><code class="name">var <span class="ident">postgres_server_user_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the username of postgres server</p>
<p>Return Type: str</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L194-L209" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_server_user_name(self):
    &#34;&#34;&#34;Returns the username of postgres server

        Return Type: str

    &#34;&#34;&#34;
    if self.credentials:
        return self._commcell_object.credentials.get(self.credentials).credential_user_name
    else:
        if self._properties[&#39;postGreSQLInstance&#39;][&#39;SAUser&#39;][&#39;userName&#39;]:
            return self._properties[&#39;postGreSQLInstance&#39;][&#39;SAUser&#39;][&#39;userName&#39;]
    raise SDKException(
        &#39;Instance&#39;,
        &#39;105&#39;,
        &#34;Could not fetch the Server name.&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_ca_file"><code class="name">var <span class="ident">postgres_ssl_ca_file</span></code></dt>
<dd>
<div class="desc"><p>Returns: str - ssl ca file path</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L366-L369" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_ssl_ca_file(self):
    &#34;&#34;&#34;Returns: str - ssl ca file path&#34;&#34;&#34;
    return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslCa&#34;, &#34;&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_cert_file"><code class="name">var <span class="ident">postgres_ssl_cert_file</span></code></dt>
<dd>
<div class="desc"><p>Returns:str - ssl cert file path</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L376-L379" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_ssl_cert_file(self):
    &#34;&#34;&#34;Returns:str - ssl cert file path&#34;&#34;&#34;
    return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslCert&#34;, &#34;&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_key_file"><code class="name">var <span class="ident">postgres_ssl_key_file</span></code></dt>
<dd>
<div class="desc"><p>Returns: str - ssl key file path</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L371-L374" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_ssl_key_file(self):
    &#34;&#34;&#34;Returns: str - ssl key file path&#34;&#34;&#34;
    return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslKey&#34;, &#34;&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_status"><code class="name">var <span class="ident">postgres_ssl_status</span></code></dt>
<dd>
<div class="desc"><p>Returns True/False based on if ssl is enabled or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L361-L364" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_ssl_status(self):
    &#34;&#34;&#34;Returns True/False based on if ssl is enabled or not&#34;&#34;&#34;
    return self._properties.get(&#34;postGreSQLInstance&#34;, {}).get(&#34;sslOpt&#34;, {}).get(&#34;sslEnabled&#34;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_version"><code class="name">var <span class="ident">postgres_version</span></code></dt>
<dd>
<div class="desc"><p>Returns the postgres server version</p>
<p>Return Type: str</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L239-L251" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def postgres_version(self):
    &#34;&#34;&#34;Returns the postgres server version

        Return Type: str

    &#34;&#34;&#34;
    if self._properties.get(&#39;version&#39;):
        return self._properties[&#39;version&#39;]
    raise SDKException(
        &#39;Instance&#39;,
        &#39;105&#39;,
        &#34;Could not fetch postgres version.&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.standby_instance_id"><code class="name">var <span class="ident">standby_instance_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the standby instance id</p>
<p>Return Type: str</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L290-L301" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def standby_instance_id(self):
    &#34;&#34;&#34;Returns the standby instance id

        Return Type: str

    &#34;&#34;&#34;
    if self.is_standby_enabled:
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;standbyInstance&#39;,
                                                                                            {}).get(&#39;instanceId&#39;,
                                                                                                    &#34;&#34;)
    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.standby_instance_name"><code class="name">var <span class="ident">standby_instance_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the standby instance name</p>
<p>Return Type: str</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L277-L288" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def standby_instance_name(self):
    &#34;&#34;&#34;Returns the standby instance name

        Return Type: str

    &#34;&#34;&#34;
    if self.is_standby_enabled:
        return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;standbyInstance&#39;,
                                                                                            {}).get(&#39;instanceName&#39;,
                                                                                                    &#34;&#34;)
    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.use_master_for_data_backup"><code class="name">var <span class="ident">use_master_for_data_backup</span></code></dt>
<dd>
<div class="desc"><p>Returns True if master is used for data backup</p>
<p>Return Type: bool</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L336-L344" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def use_master_for_data_backup(self):
    &#34;&#34;&#34; Returns True if master is used for data backup

        Return Type: bool

    &#34;&#34;&#34;
    return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;useMasterForDataBkp&#39;,
                                                                                        False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.use_master_for_log_backup"><code class="name">var <span class="ident">use_master_for_log_backup</span></code></dt>
<dd>
<div class="desc"><p>Returns True if master is used for log backup</p>
<p>Return Type: bool</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L312-L319" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def use_master_for_log_backup(self):
    &#34;&#34;&#34; Returns True if master is used for log backup

        Return Type: bool

    &#34;&#34;&#34;
    return self._properties.get(&#39;postGreSQLInstance&#39;, {}).get(&#39;standbyOptions&#39;, {}).get(&#39;useMasterForLogBkp&#39;, False)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.change_sa_password"><code class="name flex">
<span>def <span class="ident">change_sa_password</span></span>(<span>self, value)</span>
</code></dt>
<dd>
<div class="desc"><p>Changes postgresql user password</p>
<h2 id="args">Args</h2>
<p>value (bool)
&ndash; PostgreSQL password</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L381-L393" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def change_sa_password(self, value):
    &#34;&#34;&#34; Changes postgresql user password

        Args:

            value (bool)  -- PostgreSQL password

    &#34;&#34;&#34;
    if not isinstance(value, str):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
    properties = self._properties
    properties[&#39;postGreSQLInstance&#39;][&#39;SAUser&#39;][&#39;password&#39;] = b64encode(value.encode()).decode()
    self.update_properties(properties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.postgresinstance.PostgreSQLInstance.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, path, dest_client_name, dest_instance_name, backupset_name, backupset_flag, overwrite=True, copy_precedence=None, from_time=None, to_time=None, clone_env=False, clone_options=None, media_agent=None, table_level_restore=False, staging_path=None, no_of_streams=None, volume_level_restore=False, redirect_enabled=False, redirect_path=None, restore_to_disk=False, restore_to_disk_job=None, destination_path=None, revert=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the postgres data/log files specified in the input paths
list to the same location.</p>
<pre><code>Args:
    path                    (list)  --  list of database/databases to be restored

    dest_client_name        (str)   --  destination client name where files are to be
    restored

    dest_instance_name      (str)   --  destination postgres instance name of
    destination client

    backupset_name          (str)   --  destination postgres backupset name of
    destination client

    backupset_flag          (bool)  --  flag to indicate fsbased backup

    overwrite               (bool)  --  unconditional overwrite files during restore
        default: True

    copy_precedence         (int)   --  copy precedence value of storage policy copy
        default: None

    from_time               (str)   --  time to retore the contents after
        format: YYYY-MM-DD HH:MM:SS

        default: None

    to_time                 (str)   --  time to retore the contents before
        format: YYYY-MM-DD HH:MM:SS

        default: None

    clone_env               (bool)  --  boolean to specify whether the database
    should be cloned or not

        default: False

    clone_options           (dict)  --  clone restore options passed in a dict

        default: None

        Accepted format: {
                            "stagingLocaion": "/gk_snap",
                            "forceCleanup": True,
                            "port": "5595",
                            "libDirectory": "/opt/PostgreSQL/9.6/lib",
                            "isInstanceSelected": True,
                            "reservationPeriodS": 3600,
                            "user": "postgres",
                            "binaryDirectory": "/opt/PostgreSQL/9.6/bin"
                         }

    media_agent             (str)   --  media agent name

        default: None

    table_level_restore     (bool)  --  boolean to specify if the restore operation
    is table level

        default: False

    staging_path            (str)   --  staging path location for table level restore

        default: None

    no_of_streams           (int)   --  number of streams to be used by
    volume level restore

        default: None

    volume_level_restore    (bool)  --  volume level restore flag

        default: False

    redirect_enabled         (bool)  --  boolean to specify if redirect restore is
    enabled

        default: False

    redirect_path           (str)   --  Path specified in advanced restore options
    in order to perform redirect restore

        default: None

    restore_to_disk         (bool)  --  restore to disk flag

        default: False

    restore_to_disk_job     (int)   --  backup job id to restore to disk

        default: None

    destination_path        (str)   --  destinath path for restore

        default: None

    revert                  (bool)  --  boolean to specify whether to do a
                                        hardware revert in restore
        default: False

Returns:
    object - instance of the Job class for this restore job

Raises:
    SDKException:
        if paths is not a list

        if failed to initialize job

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/postgresinstance.py#L538-L726" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        path,
        dest_client_name,
        dest_instance_name,
        backupset_name,
        backupset_flag,
        overwrite=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        clone_env=False,
        clone_options=None,
        media_agent=None,
        table_level_restore=False,
        staging_path=None,
        no_of_streams=None,
        volume_level_restore=False,
        redirect_enabled=False,
        redirect_path=None,
        restore_to_disk=False,
        restore_to_disk_job=None,
        destination_path=None,
        revert=False):
    &#34;&#34;&#34;Restores the postgres data/log files specified in the input paths
    list to the same location.

        Args:
            path                    (list)  --  list of database/databases to be restored

            dest_client_name        (str)   --  destination client name where files are to be
            restored

            dest_instance_name      (str)   --  destination postgres instance name of
            destination client

            backupset_name          (str)   --  destination postgres backupset name of
            destination client

            backupset_flag          (bool)  --  flag to indicate fsbased backup

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time               (str)   --  time to retore the contents after
                format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time                 (str)   --  time to retore the contents before
                format: YYYY-MM-DD HH:MM:SS

                default: None

            clone_env               (bool)  --  boolean to specify whether the database
            should be cloned or not

                default: False

            clone_options           (dict)  --  clone restore options passed in a dict

                default: None

                Accepted format: {
                                    &#34;stagingLocaion&#34;: &#34;/gk_snap&#34;,
                                    &#34;forceCleanup&#34;: True,
                                    &#34;port&#34;: &#34;5595&#34;,
                                    &#34;libDirectory&#34;: &#34;/opt/PostgreSQL/9.6/lib&#34;,
                                    &#34;isInstanceSelected&#34;: True,
                                    &#34;reservationPeriodS&#34;: 3600,
                                    &#34;user&#34;: &#34;postgres&#34;,
                                    &#34;binaryDirectory&#34;: &#34;/opt/PostgreSQL/9.6/bin&#34;
                                 }

            media_agent             (str)   --  media agent name

                default: None

            table_level_restore     (bool)  --  boolean to specify if the restore operation
            is table level

                default: False

            staging_path            (str)   --  staging path location for table level restore

                default: None

            no_of_streams           (int)   --  number of streams to be used by
            volume level restore

                default: None

            volume_level_restore    (bool)  --  volume level restore flag

                default: False

            redirect_enabled         (bool)  --  boolean to specify if redirect restore is
            enabled

                default: False

            redirect_path           (str)   --  Path specified in advanced restore options
            in order to perform redirect restore

                default: None

            restore_to_disk         (bool)  --  restore to disk flag

                default: False

            restore_to_disk_job     (int)   --  backup job id to restore to disk

                default: None

            destination_path        (str)   --  destinath path for restore

                default: None

            revert                  (bool)  --  boolean to specify whether to do a
                                                hardware revert in restore
                default: False

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not (isinstance(path, list) and
            isinstance(overwrite, bool)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if not path:
        raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

    if not no_of_streams:
        no_of_streams = 1

    index_free_restore = False
    if restore_to_disk:
        index_free_restore = True

    request_json = self._restore_json(
        paths=path,
        dest_client_name=dest_client_name,
        dest_instance_name=dest_instance_name,
        backupset_name=backupset_name,
        backupset_flag=backupset_flag,
        copy_precedence=copy_precedence,
        overwrite=overwrite,
        from_time=from_time,
        to_time=to_time,
        clone_env=clone_env,
        clone_options=clone_options,
        media_agent=media_agent,
        table_level_restore=table_level_restore,
        staging_path=staging_path,
        no_of_streams=no_of_streams,
        volume_level_restore=volume_level_restore,
        redirect_enabled=redirect_enabled,
        redirect_path=redirect_path,
        restore_to_disk=restore_to_disk,
        index_free_restore=index_free_restore,
        destination_path=destination_path,
        restore_jobs=restore_to_disk_job)

    if volume_level_restore:
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;destination&#39;][&#34;noOfStreams&#34;] = no_of_streams

    if restore_to_disk:
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;destination&#39;][&#34;destPath&#34;] = [destination_path]

    if revert:
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;commonOptions&#39;][&#34;revert&#34;] = revert
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instance.Instance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.browse" href="../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#postgresqlinstance">PostgreSQLInstance:</a></li>
<li><a href="#postgresqlinstance-instance-attributes">PostgreSQLInstance instance Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance">PostgreSQLInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.archive_delete" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.archive_delete">archive_delete</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.change_sa_password" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.change_sa_password">change_sa_password</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.is_standby_enabled" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.is_standby_enabled">is_standby_enabled</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.log_storage_policy" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.log_storage_policy">log_storage_policy</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.maintenance_database" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.maintenance_database">maintenance_database</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_archive_log_directory" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_archive_log_directory">postgres_archive_log_directory</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_bin_directory" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_bin_directory">postgres_bin_directory</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_lib_directory" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_lib_directory">postgres_lib_directory</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_server_port_number" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_server_port_number">postgres_server_port_number</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_server_user_name" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_server_user_name">postgres_server_user_name</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_ca_file" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_ca_file">postgres_ssl_ca_file</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_cert_file" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_cert_file">postgres_ssl_cert_file</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_key_file" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_key_file">postgres_ssl_key_file</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_status" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_ssl_status">postgres_ssl_status</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_version" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.postgres_version">postgres_version</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.restore_in_place" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.standby_instance_id" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.standby_instance_id">standby_instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.standby_instance_name" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.standby_instance_name">standby_instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.use_master_for_data_backup" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.use_master_for_data_backup">use_master_for_data_backup</a></code></li>
<li><code><a title="cvpysdk.instances.postgresinstance.PostgreSQLInstance.use_master_for_log_backup" href="#cvpysdk.instances.postgresinstance.PostgreSQLInstance.use_master_for_log_backup">use_master_for_log_backup</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>