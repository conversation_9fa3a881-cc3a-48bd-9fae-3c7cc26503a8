<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.deployment.deploymentconstants API documentation</title>
<meta name="description" content="File that contains list of constants used by Deployment package" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.deployment.deploymentconstants</code></h1>
</header>
<section id="section-intro">
<p>File that contains list of constants used by Deployment package</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/deploymentconstants.py#L1-L153" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
File that contains list of constants used by Deployment package
&#34;&#34;&#34;

from enum import Enum


class DownloadOptions(Enum):
    &#34;&#34;&#34;
    Enum with list of all options available under Download software
    &#34;&#34;&#34;
    LATEST_SERVICEPACK = &#34;latest service pack&#34;
    LATEST_HOTFIXES = &#34;latest hotfixes for the installed service pack&#34;
    SERVICEPACK_AND_HOTFIXES = &#34;service pack and hotfixes&#34;


class DownloadPackages(Enum):
    &#34;&#34;&#34;
    List of supported OS groups.
    &#34;&#34;&#34;
    WINDOWS_32 = &#34;WINDOWS_X32&#34;
    WINDOWS_64 = &#34;WINDOWS_X64&#34;
    WINDOWS_ARM64 = &#34;WINDOWS_ARM64&#34;
    UNIX_AIX = &#34;AIX_PPC&#34;
    UNIX_AIX32 = &#34;Aix-PPC-32&#34;
    UNIX_MAC = &#34;MAC_OS&#34;
    UNIX_ARM64 = &#34;LINUX_ARM64&#34;
    UNIX_FREEBSD86 = &#34;FREEBSD_X86&#34;
    UNIX_FREEBSD64 = &#34;FREEBSD_X86_64&#34;
    UNIX_HP = &#34;HP_IA64&#34;
    UNIX_LINUX86 = &#34;LINUX_X86&#34;
    UNIX_LINUX64 = &#34;LINUX_X86_64&#34;
    UNIX_S390 = &#34;Linux-S390&#34;
    UNIX_S390_31 = &#34;Linux-S390-31&#34;
    UNIX_PPC64 = &#34;LINUX_PPC64&#34;
    UNIX_SOLARIS86 = &#34;Solaris X86&#34;
    UNIX_SOLARIS64 = &#34;SOLARIS_X86_64&#34;
    UNIX_SOLARIS_SPARC = &#34;SOLARIS_SPARC&#34;
    UNIX_SOLARIS_SPARC86 = &#34;Solaris-SPARC-X86&#34;
    UNIX_LINUX64LE = &#34;LINUX_PPC64LE&#34;


class UnixDownloadFeatures(Enum):
    &#34;&#34;&#34;
    list of Unix features supported
    &#34;&#34;&#34;
    COMMSERVE = 1020
    CASSANDRA = 1211
    CLOUD_APPS = 1140
    DOMINO_DATABASE = 1051
    FILE_SYSTEM = 1101
    FILE_SYSTEM_CORE = 1002
    FILE_SYSTEM_FOR_IBMI = 1137
    FILE_SYSTEM_FOR_OPEN_VMS = 1138
    MEDIA_AGENT = 1301
    ORACLE = 1204
    POSTGRESQL = 1209
    SAPHANA = 1210
    SQLSERVER = 1212
    VIRTUAL_SERVER = 1136
    TEST_AUTOMATION = 1153
    PYTHON_SDK = 1154
    CONTENT_ANALYZER = 1108
    DB2_AGENT = 1207
    INFORMIX = 1201
    SYBASE = 1202
    WEB_SERVER = 1174


class WindowsDownloadFeatures(Enum):
    &#34;&#34;&#34;
    list of Windows features supported
    &#34;&#34;&#34;
    COMMSERVE = 20
    ACTIVE_DIRECTORY = 703
    CLOUD_APPS = 730
    DOMINO_DATABASE = 201
    EXCHANGE = 151
    FILE_SYSTEM = 702
    FILE_SYSTEM_CORE = 1
    MEDIA_AGENT = 51
    SHAREPOINT = 101
    ORACLE = 352
    POSTGRESQL = 362
    SQLSERVER = 353
    VIRTUAL_SERVER = 713
    VSS_PROVIDER = 453
    VSS_HARDWARE_PROVIDER = 455
    WEB_CONSOLE = 726
    TEST_AUTOMATION = 719
    PYTHON_SDK = 754
    COMMSERVE_LITE = 25
    CONTENT_ANALYZER = 729
    INDEX_STORE = 55
    INDEX_GATEWAY = 263
    CONTENT_EXTRACTOR = 259
    DB2_AGENT = 351
    INFORMIX = 360
    SYBASE = 1202
    WEB_SERVER = 252


class OSNameIDMapping(Enum):
    &#34;&#34;&#34;
    Class for os name to id mapping
    &#34;&#34;&#34;
    WINDOWS_32 = 1
    WINDOWS_64 = 3
    UNIX_AIX = 14
    UNIX_AIX32 = 28
    UNIX_FREEBSD86 = 25
    UNIX_FREEBSD64 = 26
    UNIX_HP = 20
    UNIX_LINUX86 = 15
    UNIX_LINUX64 = 16
    UNIX_S390 = 18
    UNIX_S390_31 = 29
    UNIX_PPC64 = 17
    UNIX_SOLARIS86 = 31
    UNIX_SOLARIS64 = 23
    UNIX_SOLARIS_SPARC = 22
    UNIX_SOLARIS_SPARC86 = 30
    UNIX_LINUX64LE = 32

class InstallUpdateOptions(Enum):
    &#34;&#34;&#34;
    Enum with list of all options available under Upgrade software
    &#34;&#34;&#34;
    UPDATE_INSTALL_CV = 1
    UPDATE_INSTALL_SQL = 2
    UPDATE_INSTALL_WINOS = 4
    UPDATE_INSTALL_FREL_OS_UPDATES = 8
    UPDATE_INSTALL_HYPERSCALE_OS_UPDATES = 16
    UPDATE_INSTALL_HSX_STORAGE_UPDATES = 32
    UPDATE_INSTALL_HSX_STORAGE_UPDATES_DISRUPTIVE_MODE = 64</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadOptions"><code class="flex name class">
<span>class <span class="ident">DownloadOptions</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Enum with list of all options available under Download software</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/deploymentconstants.py#L26-L32" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DownloadOptions(Enum):
    &#34;&#34;&#34;
    Enum with list of all options available under Download software
    &#34;&#34;&#34;
    LATEST_SERVICEPACK = &#34;latest service pack&#34;
    LATEST_HOTFIXES = &#34;latest hotfixes for the installed service pack&#34;
    SERVICEPACK_AND_HOTFIXES = &#34;service pack and hotfixes&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadOptions.LATEST_HOTFIXES"><code class="name">var <span class="ident">LATEST_HOTFIXES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadOptions.LATEST_SERVICEPACK"><code class="name">var <span class="ident">LATEST_SERVICEPACK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadOptions.SERVICEPACK_AND_HOTFIXES"><code class="name">var <span class="ident">SERVICEPACK_AND_HOTFIXES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages"><code class="flex name class">
<span>class <span class="ident">DownloadPackages</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>List of supported OS groups.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/deploymentconstants.py#L35-L58" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DownloadPackages(Enum):
    &#34;&#34;&#34;
    List of supported OS groups.
    &#34;&#34;&#34;
    WINDOWS_32 = &#34;WINDOWS_X32&#34;
    WINDOWS_64 = &#34;WINDOWS_X64&#34;
    WINDOWS_ARM64 = &#34;WINDOWS_ARM64&#34;
    UNIX_AIX = &#34;AIX_PPC&#34;
    UNIX_AIX32 = &#34;Aix-PPC-32&#34;
    UNIX_MAC = &#34;MAC_OS&#34;
    UNIX_ARM64 = &#34;LINUX_ARM64&#34;
    UNIX_FREEBSD86 = &#34;FREEBSD_X86&#34;
    UNIX_FREEBSD64 = &#34;FREEBSD_X86_64&#34;
    UNIX_HP = &#34;HP_IA64&#34;
    UNIX_LINUX86 = &#34;LINUX_X86&#34;
    UNIX_LINUX64 = &#34;LINUX_X86_64&#34;
    UNIX_S390 = &#34;Linux-S390&#34;
    UNIX_S390_31 = &#34;Linux-S390-31&#34;
    UNIX_PPC64 = &#34;LINUX_PPC64&#34;
    UNIX_SOLARIS86 = &#34;Solaris X86&#34;
    UNIX_SOLARIS64 = &#34;SOLARIS_X86_64&#34;
    UNIX_SOLARIS_SPARC = &#34;SOLARIS_SPARC&#34;
    UNIX_SOLARIS_SPARC86 = &#34;Solaris-SPARC-X86&#34;
    UNIX_LINUX64LE = &#34;LINUX_PPC64LE&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_AIX"><code class="name">var <span class="ident">UNIX_AIX</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_AIX32"><code class="name">var <span class="ident">UNIX_AIX32</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_ARM64"><code class="name">var <span class="ident">UNIX_ARM64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_FREEBSD64"><code class="name">var <span class="ident">UNIX_FREEBSD64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_FREEBSD86"><code class="name">var <span class="ident">UNIX_FREEBSD86</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_HP"><code class="name">var <span class="ident">UNIX_HP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_LINUX64"><code class="name">var <span class="ident">UNIX_LINUX64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_LINUX64LE"><code class="name">var <span class="ident">UNIX_LINUX64LE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_LINUX86"><code class="name">var <span class="ident">UNIX_LINUX86</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_MAC"><code class="name">var <span class="ident">UNIX_MAC</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_PPC64"><code class="name">var <span class="ident">UNIX_PPC64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_S390"><code class="name">var <span class="ident">UNIX_S390</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_S390_31"><code class="name">var <span class="ident">UNIX_S390_31</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS64"><code class="name">var <span class="ident">UNIX_SOLARIS64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS86"><code class="name">var <span class="ident">UNIX_SOLARIS86</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS_SPARC"><code class="name">var <span class="ident">UNIX_SOLARIS_SPARC</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS_SPARC86"><code class="name">var <span class="ident">UNIX_SOLARIS_SPARC86</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.WINDOWS_32"><code class="name">var <span class="ident">WINDOWS_32</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.WINDOWS_64"><code class="name">var <span class="ident">WINDOWS_64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.DownloadPackages.WINDOWS_ARM64"><code class="name">var <span class="ident">WINDOWS_ARM64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions"><code class="flex name class">
<span>class <span class="ident">InstallUpdateOptions</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Enum with list of all options available under Upgrade software</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/deploymentconstants.py#L143-L153" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class InstallUpdateOptions(Enum):
    &#34;&#34;&#34;
    Enum with list of all options available under Upgrade software
    &#34;&#34;&#34;
    UPDATE_INSTALL_CV = 1
    UPDATE_INSTALL_SQL = 2
    UPDATE_INSTALL_WINOS = 4
    UPDATE_INSTALL_FREL_OS_UPDATES = 8
    UPDATE_INSTALL_HYPERSCALE_OS_UPDATES = 16
    UPDATE_INSTALL_HSX_STORAGE_UPDATES = 32
    UPDATE_INSTALL_HSX_STORAGE_UPDATES_DISRUPTIVE_MODE = 64</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_CV"><code class="name">var <span class="ident">UPDATE_INSTALL_CV</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_FREL_OS_UPDATES"><code class="name">var <span class="ident">UPDATE_INSTALL_FREL_OS_UPDATES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_HSX_STORAGE_UPDATES"><code class="name">var <span class="ident">UPDATE_INSTALL_HSX_STORAGE_UPDATES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_HSX_STORAGE_UPDATES_DISRUPTIVE_MODE"><code class="name">var <span class="ident">UPDATE_INSTALL_HSX_STORAGE_UPDATES_DISRUPTIVE_MODE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_HYPERSCALE_OS_UPDATES"><code class="name">var <span class="ident">UPDATE_INSTALL_HYPERSCALE_OS_UPDATES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_SQL"><code class="name">var <span class="ident">UPDATE_INSTALL_SQL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_WINOS"><code class="name">var <span class="ident">UPDATE_INSTALL_WINOS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping"><code class="flex name class">
<span>class <span class="ident">OSNameIDMapping</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for os name to id mapping</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/deploymentconstants.py#L121-L141" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OSNameIDMapping(Enum):
    &#34;&#34;&#34;
    Class for os name to id mapping
    &#34;&#34;&#34;
    WINDOWS_32 = 1
    WINDOWS_64 = 3
    UNIX_AIX = 14
    UNIX_AIX32 = 28
    UNIX_FREEBSD86 = 25
    UNIX_FREEBSD64 = 26
    UNIX_HP = 20
    UNIX_LINUX86 = 15
    UNIX_LINUX64 = 16
    UNIX_S390 = 18
    UNIX_S390_31 = 29
    UNIX_PPC64 = 17
    UNIX_SOLARIS86 = 31
    UNIX_SOLARIS64 = 23
    UNIX_SOLARIS_SPARC = 22
    UNIX_SOLARIS_SPARC86 = 30
    UNIX_LINUX64LE = 32</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_AIX"><code class="name">var <span class="ident">UNIX_AIX</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_AIX32"><code class="name">var <span class="ident">UNIX_AIX32</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_FREEBSD64"><code class="name">var <span class="ident">UNIX_FREEBSD64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_FREEBSD86"><code class="name">var <span class="ident">UNIX_FREEBSD86</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_HP"><code class="name">var <span class="ident">UNIX_HP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_LINUX64"><code class="name">var <span class="ident">UNIX_LINUX64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_LINUX64LE"><code class="name">var <span class="ident">UNIX_LINUX64LE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_LINUX86"><code class="name">var <span class="ident">UNIX_LINUX86</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_PPC64"><code class="name">var <span class="ident">UNIX_PPC64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_S390"><code class="name">var <span class="ident">UNIX_S390</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_S390_31"><code class="name">var <span class="ident">UNIX_S390_31</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS64"><code class="name">var <span class="ident">UNIX_SOLARIS64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS86"><code class="name">var <span class="ident">UNIX_SOLARIS86</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS_SPARC"><code class="name">var <span class="ident">UNIX_SOLARIS_SPARC</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS_SPARC86"><code class="name">var <span class="ident">UNIX_SOLARIS_SPARC86</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.WINDOWS_32"><code class="name">var <span class="ident">WINDOWS_32</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.WINDOWS_64"><code class="name">var <span class="ident">WINDOWS_64</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures"><code class="flex name class">
<span>class <span class="ident">UnixDownloadFeatures</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>list of Unix features supported</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/deploymentconstants.py#L61-L85" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class UnixDownloadFeatures(Enum):
    &#34;&#34;&#34;
    list of Unix features supported
    &#34;&#34;&#34;
    COMMSERVE = 1020
    CASSANDRA = 1211
    CLOUD_APPS = 1140
    DOMINO_DATABASE = 1051
    FILE_SYSTEM = 1101
    FILE_SYSTEM_CORE = 1002
    FILE_SYSTEM_FOR_IBMI = 1137
    FILE_SYSTEM_FOR_OPEN_VMS = 1138
    MEDIA_AGENT = 1301
    ORACLE = 1204
    POSTGRESQL = 1209
    SAPHANA = 1210
    SQLSERVER = 1212
    VIRTUAL_SERVER = 1136
    TEST_AUTOMATION = 1153
    PYTHON_SDK = 1154
    CONTENT_ANALYZER = 1108
    DB2_AGENT = 1207
    INFORMIX = 1201
    SYBASE = 1202
    WEB_SERVER = 1174</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.CASSANDRA"><code class="name">var <span class="ident">CASSANDRA</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.CLOUD_APPS"><code class="name">var <span class="ident">CLOUD_APPS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.COMMSERVE"><code class="name">var <span class="ident">COMMSERVE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.CONTENT_ANALYZER"><code class="name">var <span class="ident">CONTENT_ANALYZER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.DB2_AGENT"><code class="name">var <span class="ident">DB2_AGENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.DOMINO_DATABASE"><code class="name">var <span class="ident">DOMINO_DATABASE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM"><code class="name">var <span class="ident">FILE_SYSTEM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM_CORE"><code class="name">var <span class="ident">FILE_SYSTEM_CORE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM_FOR_IBMI"><code class="name">var <span class="ident">FILE_SYSTEM_FOR_IBMI</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM_FOR_OPEN_VMS"><code class="name">var <span class="ident">FILE_SYSTEM_FOR_OPEN_VMS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.INFORMIX"><code class="name">var <span class="ident">INFORMIX</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.MEDIA_AGENT"><code class="name">var <span class="ident">MEDIA_AGENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.ORACLE"><code class="name">var <span class="ident">ORACLE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.POSTGRESQL"><code class="name">var <span class="ident">POSTGRESQL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.PYTHON_SDK"><code class="name">var <span class="ident">PYTHON_SDK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.SAPHANA"><code class="name">var <span class="ident">SAPHANA</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.SQLSERVER"><code class="name">var <span class="ident">SQLSERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.SYBASE"><code class="name">var <span class="ident">SYBASE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.TEST_AUTOMATION"><code class="name">var <span class="ident">TEST_AUTOMATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.VIRTUAL_SERVER"><code class="name">var <span class="ident">VIRTUAL_SERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.WEB_SERVER"><code class="name">var <span class="ident">WEB_SERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures"><code class="flex name class">
<span>class <span class="ident">WindowsDownloadFeatures</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>list of Windows features supported</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/deploymentconstants.py#L88-L118" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class WindowsDownloadFeatures(Enum):
    &#34;&#34;&#34;
    list of Windows features supported
    &#34;&#34;&#34;
    COMMSERVE = 20
    ACTIVE_DIRECTORY = 703
    CLOUD_APPS = 730
    DOMINO_DATABASE = 201
    EXCHANGE = 151
    FILE_SYSTEM = 702
    FILE_SYSTEM_CORE = 1
    MEDIA_AGENT = 51
    SHAREPOINT = 101
    ORACLE = 352
    POSTGRESQL = 362
    SQLSERVER = 353
    VIRTUAL_SERVER = 713
    VSS_PROVIDER = 453
    VSS_HARDWARE_PROVIDER = 455
    WEB_CONSOLE = 726
    TEST_AUTOMATION = 719
    PYTHON_SDK = 754
    COMMSERVE_LITE = 25
    CONTENT_ANALYZER = 729
    INDEX_STORE = 55
    INDEX_GATEWAY = 263
    CONTENT_EXTRACTOR = 259
    DB2_AGENT = 351
    INFORMIX = 360
    SYBASE = 1202
    WEB_SERVER = 252</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.ACTIVE_DIRECTORY"><code class="name">var <span class="ident">ACTIVE_DIRECTORY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.CLOUD_APPS"><code class="name">var <span class="ident">CLOUD_APPS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.COMMSERVE"><code class="name">var <span class="ident">COMMSERVE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.COMMSERVE_LITE"><code class="name">var <span class="ident">COMMSERVE_LITE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.CONTENT_ANALYZER"><code class="name">var <span class="ident">CONTENT_ANALYZER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.CONTENT_EXTRACTOR"><code class="name">var <span class="ident">CONTENT_EXTRACTOR</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.DB2_AGENT"><code class="name">var <span class="ident">DB2_AGENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.DOMINO_DATABASE"><code class="name">var <span class="ident">DOMINO_DATABASE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.EXCHANGE"><code class="name">var <span class="ident">EXCHANGE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.FILE_SYSTEM"><code class="name">var <span class="ident">FILE_SYSTEM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.FILE_SYSTEM_CORE"><code class="name">var <span class="ident">FILE_SYSTEM_CORE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.INDEX_GATEWAY"><code class="name">var <span class="ident">INDEX_GATEWAY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.INDEX_STORE"><code class="name">var <span class="ident">INDEX_STORE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.INFORMIX"><code class="name">var <span class="ident">INFORMIX</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.MEDIA_AGENT"><code class="name">var <span class="ident">MEDIA_AGENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.ORACLE"><code class="name">var <span class="ident">ORACLE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.POSTGRESQL"><code class="name">var <span class="ident">POSTGRESQL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.PYTHON_SDK"><code class="name">var <span class="ident">PYTHON_SDK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.SHAREPOINT"><code class="name">var <span class="ident">SHAREPOINT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.SQLSERVER"><code class="name">var <span class="ident">SQLSERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.SYBASE"><code class="name">var <span class="ident">SYBASE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.TEST_AUTOMATION"><code class="name">var <span class="ident">TEST_AUTOMATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.VIRTUAL_SERVER"><code class="name">var <span class="ident">VIRTUAL_SERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.VSS_HARDWARE_PROVIDER"><code class="name">var <span class="ident">VSS_HARDWARE_PROVIDER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.VSS_PROVIDER"><code class="name">var <span class="ident">VSS_PROVIDER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.WEB_CONSOLE"><code class="name">var <span class="ident">WEB_CONSOLE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.WEB_SERVER"><code class="name">var <span class="ident">WEB_SERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.deployment" href="index.html">cvpysdk.deployment</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.deployment.deploymentconstants.DownloadOptions" href="#cvpysdk.deployment.deploymentconstants.DownloadOptions">DownloadOptions</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadOptions.LATEST_HOTFIXES" href="#cvpysdk.deployment.deploymentconstants.DownloadOptions.LATEST_HOTFIXES">LATEST_HOTFIXES</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadOptions.LATEST_SERVICEPACK" href="#cvpysdk.deployment.deploymentconstants.DownloadOptions.LATEST_SERVICEPACK">LATEST_SERVICEPACK</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadOptions.SERVICEPACK_AND_HOTFIXES" href="#cvpysdk.deployment.deploymentconstants.DownloadOptions.SERVICEPACK_AND_HOTFIXES">SERVICEPACK_AND_HOTFIXES</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages">DownloadPackages</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_AIX" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_AIX">UNIX_AIX</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_AIX32" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_AIX32">UNIX_AIX32</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_ARM64" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_ARM64">UNIX_ARM64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_FREEBSD64" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_FREEBSD64">UNIX_FREEBSD64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_FREEBSD86" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_FREEBSD86">UNIX_FREEBSD86</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_HP" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_HP">UNIX_HP</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_LINUX64" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_LINUX64">UNIX_LINUX64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_LINUX64LE" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_LINUX64LE">UNIX_LINUX64LE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_LINUX86" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_LINUX86">UNIX_LINUX86</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_MAC" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_MAC">UNIX_MAC</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_PPC64" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_PPC64">UNIX_PPC64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_S390" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_S390">UNIX_S390</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_S390_31" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_S390_31">UNIX_S390_31</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS64" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS64">UNIX_SOLARIS64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS86" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS86">UNIX_SOLARIS86</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS_SPARC" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS_SPARC">UNIX_SOLARIS_SPARC</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS_SPARC86" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.UNIX_SOLARIS_SPARC86">UNIX_SOLARIS_SPARC86</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.WINDOWS_32" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.WINDOWS_32">WINDOWS_32</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.WINDOWS_64" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.WINDOWS_64">WINDOWS_64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.DownloadPackages.WINDOWS_ARM64" href="#cvpysdk.deployment.deploymentconstants.DownloadPackages.WINDOWS_ARM64">WINDOWS_ARM64</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions" href="#cvpysdk.deployment.deploymentconstants.InstallUpdateOptions">InstallUpdateOptions</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_CV" href="#cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_CV">UPDATE_INSTALL_CV</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_FREL_OS_UPDATES" href="#cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_FREL_OS_UPDATES">UPDATE_INSTALL_FREL_OS_UPDATES</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_HSX_STORAGE_UPDATES" href="#cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_HSX_STORAGE_UPDATES">UPDATE_INSTALL_HSX_STORAGE_UPDATES</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_HSX_STORAGE_UPDATES_DISRUPTIVE_MODE" href="#cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_HSX_STORAGE_UPDATES_DISRUPTIVE_MODE">UPDATE_INSTALL_HSX_STORAGE_UPDATES_DISRUPTIVE_MODE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_HYPERSCALE_OS_UPDATES" href="#cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_HYPERSCALE_OS_UPDATES">UPDATE_INSTALL_HYPERSCALE_OS_UPDATES</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_SQL" href="#cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_SQL">UPDATE_INSTALL_SQL</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_WINOS" href="#cvpysdk.deployment.deploymentconstants.InstallUpdateOptions.UPDATE_INSTALL_WINOS">UPDATE_INSTALL_WINOS</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping">OSNameIDMapping</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_AIX" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_AIX">UNIX_AIX</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_AIX32" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_AIX32">UNIX_AIX32</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_FREEBSD64" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_FREEBSD64">UNIX_FREEBSD64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_FREEBSD86" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_FREEBSD86">UNIX_FREEBSD86</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_HP" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_HP">UNIX_HP</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_LINUX64" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_LINUX64">UNIX_LINUX64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_LINUX64LE" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_LINUX64LE">UNIX_LINUX64LE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_LINUX86" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_LINUX86">UNIX_LINUX86</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_PPC64" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_PPC64">UNIX_PPC64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_S390" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_S390">UNIX_S390</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_S390_31" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_S390_31">UNIX_S390_31</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS64" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS64">UNIX_SOLARIS64</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS86" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS86">UNIX_SOLARIS86</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS_SPARC" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS_SPARC">UNIX_SOLARIS_SPARC</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS_SPARC86" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.UNIX_SOLARIS_SPARC86">UNIX_SOLARIS_SPARC86</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.WINDOWS_32" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.WINDOWS_32">WINDOWS_32</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.OSNameIDMapping.WINDOWS_64" href="#cvpysdk.deployment.deploymentconstants.OSNameIDMapping.WINDOWS_64">WINDOWS_64</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures">UnixDownloadFeatures</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.CASSANDRA" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.CASSANDRA">CASSANDRA</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.CLOUD_APPS" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.CLOUD_APPS">CLOUD_APPS</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.COMMSERVE" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.COMMSERVE">COMMSERVE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.CONTENT_ANALYZER" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.CONTENT_ANALYZER">CONTENT_ANALYZER</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.DB2_AGENT" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.DB2_AGENT">DB2_AGENT</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.DOMINO_DATABASE" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.DOMINO_DATABASE">DOMINO_DATABASE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM">FILE_SYSTEM</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM_CORE" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM_CORE">FILE_SYSTEM_CORE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM_FOR_IBMI" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM_FOR_IBMI">FILE_SYSTEM_FOR_IBMI</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM_FOR_OPEN_VMS" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.FILE_SYSTEM_FOR_OPEN_VMS">FILE_SYSTEM_FOR_OPEN_VMS</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.INFORMIX" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.INFORMIX">INFORMIX</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.MEDIA_AGENT" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.MEDIA_AGENT">MEDIA_AGENT</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.ORACLE" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.ORACLE">ORACLE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.POSTGRESQL" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.POSTGRESQL">POSTGRESQL</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.PYTHON_SDK" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.PYTHON_SDK">PYTHON_SDK</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.SAPHANA" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.SAPHANA">SAPHANA</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.SQLSERVER" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.SQLSERVER">SQLSERVER</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.SYBASE" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.SYBASE">SYBASE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.TEST_AUTOMATION" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.TEST_AUTOMATION">TEST_AUTOMATION</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.VIRTUAL_SERVER" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.VIRTUAL_SERVER">VIRTUAL_SERVER</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.WEB_SERVER" href="#cvpysdk.deployment.deploymentconstants.UnixDownloadFeatures.WEB_SERVER">WEB_SERVER</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures">WindowsDownloadFeatures</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.ACTIVE_DIRECTORY" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.ACTIVE_DIRECTORY">ACTIVE_DIRECTORY</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.CLOUD_APPS" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.CLOUD_APPS">CLOUD_APPS</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.COMMSERVE" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.COMMSERVE">COMMSERVE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.COMMSERVE_LITE" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.COMMSERVE_LITE">COMMSERVE_LITE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.CONTENT_ANALYZER" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.CONTENT_ANALYZER">CONTENT_ANALYZER</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.CONTENT_EXTRACTOR" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.CONTENT_EXTRACTOR">CONTENT_EXTRACTOR</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.DB2_AGENT" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.DB2_AGENT">DB2_AGENT</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.DOMINO_DATABASE" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.DOMINO_DATABASE">DOMINO_DATABASE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.EXCHANGE" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.EXCHANGE">EXCHANGE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.FILE_SYSTEM" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.FILE_SYSTEM">FILE_SYSTEM</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.FILE_SYSTEM_CORE" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.FILE_SYSTEM_CORE">FILE_SYSTEM_CORE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.INDEX_GATEWAY" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.INDEX_GATEWAY">INDEX_GATEWAY</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.INDEX_STORE" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.INDEX_STORE">INDEX_STORE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.INFORMIX" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.INFORMIX">INFORMIX</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.MEDIA_AGENT" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.MEDIA_AGENT">MEDIA_AGENT</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.ORACLE" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.ORACLE">ORACLE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.POSTGRESQL" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.POSTGRESQL">POSTGRESQL</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.PYTHON_SDK" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.PYTHON_SDK">PYTHON_SDK</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.SHAREPOINT" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.SHAREPOINT">SHAREPOINT</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.SQLSERVER" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.SQLSERVER">SQLSERVER</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.SYBASE" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.SYBASE">SYBASE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.TEST_AUTOMATION" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.TEST_AUTOMATION">TEST_AUTOMATION</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.VIRTUAL_SERVER" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.VIRTUAL_SERVER">VIRTUAL_SERVER</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.VSS_HARDWARE_PROVIDER" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.VSS_HARDWARE_PROVIDER">VSS_HARDWARE_PROVIDER</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.VSS_PROVIDER" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.VSS_PROVIDER">VSS_PROVIDER</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.WEB_CONSOLE" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.WEB_CONSOLE">WEB_CONSOLE</a></code></li>
<li><code><a title="cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.WEB_SERVER" href="#cvpysdk.deployment.deploymentconstants.WindowsDownloadFeatures.WEB_SERVER">WEB_SERVER</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>