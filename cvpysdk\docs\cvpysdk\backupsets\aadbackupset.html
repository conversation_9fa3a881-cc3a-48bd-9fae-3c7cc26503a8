<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.backupsets.aadbackupset API documentation</title>
<meta name="description" content="File for operating on an AD agent Backupset …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.backupsets.aadbackupset</code></h1>
</header>
<section id="section-intro">
<p>File for operating on an AD agent Backupset.</p>
<p>adbackupset is the only class defined in this file.</p>
<h2 id="function">Function</h2>
<p>azuread_browse_double_query
create browse options for objects in folder</p>
<p>azuread_browse_double_query_adv
create browse options for objects attrbute</p>
<p>azuread_browse_options_builder
build browse options for azure ad browse</p>
<h2 id="class">Class</h2>
<p>AzureADBackupset:
Derived class from Backuset Base class, representing a
Azure AD agent backupset, and to perform operations on that backupset</p>
<pre><code>_azuread_browse_basic : Do basic browse with option

_azuread_browse_meta  : Get Azure ad folder meta information

_azuread_browse_folder : Get Azure objects based on the folder type

_adv_attributes : Get Azure AD object attribute

browse() : Overwrite default browse operation

_process_browse_repsonse : process the browse result

azuread_get_metadata : create azure ad object meta data information

__prepare_search_json : Prepare search json for search api request

get_search_response : Get search response from search api

view_attributes_url_builder : Build view attribute url

get_view_attribute_response : Get view attribute response from view attribute url
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L1-L591" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# pylint: disable=R1705, R0205

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------
&#34;&#34;&#34;File for operating on an AD agent Backupset.

adbackupset is the only class defined in this file.

Function:
    azuread_browse_double_query     create browse options for objects in folder

    azuread_browse_double_query_adv    create browse options for objects attrbute

    azuread_browse_options_builder    build browse options for azure ad browse
Class:

    AzureADBackupset:  Derived class from Backuset Base class, representing a
                            Azure AD agent backupset, and to perform operations on that backupset

        _azuread_browse_basic : Do basic browse with option

        _azuread_browse_meta  : Get Azure ad folder meta information

        _azuread_browse_folder : Get Azure objects based on the folder type

        _adv_attributes : Get Azure AD object attribute

        browse() : Overwrite default browse operation

        _process_browse_repsonse : process the browse result

        azuread_get_metadata : create azure ad object meta data information
        
        __prepare_search_json : Prepare search json for search api request

        get_search_response : Get search response from search api

        view_attributes_url_builder : Build view attribute url

        get_view_attribute_response : Get view attribute response from view attribute url

&#34;&#34;&#34;

from __future__ import unicode_literals
import base64
from ..backupset import Backupset
from ..exception import SDKException


class AzureAdBackupset(Backupset):
    &#34;&#34;&#34; Azure AD agent backupset class &#34;&#34;&#34;

    def _azuread_browse_basic(self, options):
        &#34;&#34;&#34; do basic browse activity with options
            Args:
                options    (dict)    browse option from impoort
                                adv_attributes    get object advanced attribute
            Return:
                count     (int)    return count from browse
                result    (list)    objects list from browse
            Raise:
                101    If browse return Nothing
        &#34;&#34;&#34;
        options = self._prepare_browse_options(options)

        request_json = self._prepare_browse_json(options)
        request_json = self.azuread_browse_double_query(options, request_json)
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;,
                                                           self._BROWSE,
                                                           request_json)
        if flag:
            count, result = self._process_browse_response(flag, response, options)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return count, result

    def _azuread_browse_meta(self, options):
        &#34;&#34;&#34; get basic browse related meta data
            Args:
                options    (dict)    browse option from impoort
            Return:
                azure_meta    (dict)    azure ad meta data from browse result
            Raise:
                None
        &#34;&#34;&#34;
        if &#34;meta&#34; in options:
            azure_meta = options[&#39;meta&#39;]
        else:
            azure_meta = {}
        count, result = self._azuread_browse_basic(options)
        if len(result) == 1 and result[0][&#39;objType&#39;] == 1:
            newid = result[0][&#39;commonData&#39;][&#39;id&#39;]
            options[&#39;filters&#39;] = [(_[0], newid, _[2]) for _ in options[&#39;filters&#39;]]
            name, metainfo = self.azuread_browse_obj_meta(result[0])
            azure_meta[&#39;root&#39;] = metainfo
            options[&#39;meta&#39;] = azure_meta
            self._azuread_browse_meta(options)
        else:
            for r_ in result:
                if r_[&#39;objType&#39;] == 100:
                    options[&#39;filters&#39;] = [(&#34;76&#34;, r_[&#39;commonData&#39;][&#39;id&#39;], &#34;9&#34;),
                                        (&#34;125&#34;, &#34;FOLDER&#34;)]
                    count_, metainfo_ = self._azuread_browse_basic(options)
                    for i_ in metainfo_:
                        name, metainfo = self.azuread_browse_obj_meta(i_)
                        azure_meta[name] = metainfo
                else:
                    name, metainfo = self.azuread_browse_obj_meta(r_)
                    azure_meta[name] = metainfo
        return azure_meta

    def _azuread_browse_folder(self, options):
        &#34;&#34;&#34; browse folder content
            Args:
                options    (dict)    browse option from impoort
            Return:
                count     (int)    return count from browse
                result    (list)    objects list from browse
            Raise:
                None
        &#34;&#34;&#34;
        azure_meta_mapper = {
            &#34;user&#34; : { &#34;displayname&#34; : &#34;Users&#34;, &#34;browsetype&#34; : 2, &#34;browsestring&#34; : &#34;USER&#34;},
            &#34;group&#34; : { &#34;displayname&#34; : &#34;Groups&#34;, &#34;browsetype&#34; : 3, &#34;browsestring&#34;: &#34;GROUP&#34;},
            &#34;reg_app&#34; : {&#34;displayname&#34; : &#34;App registrations&#34;, &#34;browsetype&#34; : 5, &#34;browsestring&#34; : &#34;APPLICATION&#34;},
            &#34;ent_app&#34; : { &#34;displayname&#34; : &#34;Enterprise applications&#34;,&#34;browsetype&#34;: 6, &#34;browsestring&#34; : &#34;SERVICE_PRINCIPAL&#34;},
            &#34;ca_policy&#34; : { &#34;displayname&#34; : &#34;Policies&#34;,&#34;browsetype&#34;: 11, &#34;browsestring&#34; : &#34;CONDITIONAL_ACCESS_POLICY&#34;},
            &#34;ca_name_location&#34; : { &#34;displayname&#34; : &#34;Named locations&#34;,&#34;browsetype&#34;: 12, &#34;browsestring&#34; : &#34;NAMED_LOCATION&#34;} ,
            &#34;ca_auth_context&#34; : { &#34;displayname&#34; : &#34;Authentication context&#34;,&#34;browsetype&#34;: 13, &#34;browsestring&#34; : &#34;AUTHENTICATION_CONTEXT&#34;},
            &#34;ca_auth_strength&#34; : { &#34;displayname&#34; : &#34;Authentication strengths&#34;,&#34;browsetype&#34;: 14, &#34;browsestring&#34; : &#34;AUTHENTICATION_STRENGTH&#34;},
            &#34;role&#34; : { &#34;displayname&#34; : &#34;Roles&#34;,&#34;browsetype&#34;: 15, &#34;browsestring&#34; : &#34;DIRECTORY_ROLE_DEFINITIONS&#34;},
            &#34;admin_unit&#34; : { &#34;displayname&#34; : &#34;Admin units&#34;,&#34;browsetype&#34;: 16, &#34;browsestring&#34; : &#34;ADMINISTRATIVE_UNIT&#34;}}
        azure_meta = options[&#39;meta&#39;]
        newid = azure_meta[azure_meta_mapper[options[&#39;folder&#39;]][&#39;displayname&#39;]][&#39;id&#39;]
        options[&#39;filters&#39;] = [(&#34;76&#34;, newid, &#34;9&#34;),
                              (&#34;125&#34;, azure_meta_mapper[options[&#39;folder&#39;]][&#39;browsestring&#39;])]
        if &#34;search&#34; in options:
            if isinstance(options[&#39;search&#39;], dict):
                search_dict = options[&#39;search&#39;]
                if &#34;obj_id&#34; in search_dict:
                    options[&#39;filters&#39;].append((&#34;130&#34;, search_dict[&#39;obj_id&#39;]))
                if &#34;source&#34; in search_dict:
                    if search_dict[&#39;source&#39;] == &#34;AzureAD&#34;:
                        options[&#39;filters&#39;].append((&#34;128&#34;, &#34;0&#34;))
                    elif search_dict[&#39;source&#39;] == &#34;WinAD&#34;:
                        options[&#39;filters&#39;].append((&#34;128&#34;, &#34;1&#34;))
            else:
                options[&#39;filters&#39;].append((&#34;30&#34;, options[&#39;search&#39;]))

        del(options[&#39;folder&#39;])
        del(options[&#39;meta&#39;])
        del(options[&#39;path&#39;])
        count, results = self._azuread_browse_basic(options)
        results = self._process_result_format(results)
        return count, results

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Backupset.
            Args:
                args    list    args passed for browse
                kwargs    dict    dict passed for browse
            Return:
                count     (int)    return count from browse
                browse_result    (list)    objects list from browse
            Raise:
                None
        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs
        options = self.azuread_browse_options_builder(options)
        azure_meta = self._azuread_browse_meta(options)
        options[&#39;meta&#39;] = azure_meta
        count, browse_result = self._azuread_browse_folder(options)

        return count, browse_result

    def _process_browse_response(self, flag, response, options):
        &#34;&#34;&#34;Retrieves the items from browse response.
            Args:
                flag    (bool)  --  boolean, whether the response was success or not
                response (dict)  --  JSON response received for the request from the Server
                options  (dict)  --  The browse options dictionary
            Returns:
                list - List of only the file / folder paths from the browse response
                dict - Dictionary of all the paths with additional metadata retrieved from browse
            Raises:
                SDKException:
                    if failed to browse/search for content
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        metadatas = []
        if flag:
            if response.json():
                response_json = response.json()
                if response_json and &#39;browseResponses&#39; in response_json:
                    _browse_responses = response_json[&#39;browseResponses&#39;]
                    for browse_response in _browse_responses:
                        if &#34;browseResult&#34; in browse_response:
                            browse_result = browse_response[&#39;browseResult&#39;]
                            browseresulttcount = browse_result[&#39;totalItemsFound&#39;]
                            if &#39;dataResultSet&#39; in browse_result:
                                result_set = browse_result[&#39;dataResultSet&#39;]
                    if not result_set and result_set != []:
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;,
                                           &#34;Failed to browse for subclient backup content&#34;)
                    else:
                        for result in result_set:
                            metadata = self.azuread_get_metadata(result)
                            metadatas.append(metadata[&#39;azureADDataV2&#39;])
            else:
                raise SDKException(&#34;Backupset&#34;, &#34;102&#34;, &#34;response is not valid&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return browseresulttcount, metadatas

    def _process_result_format(self, results):
        &#34;&#34;&#34;
        process the browse result to original data format
        Args:
            results     (list)  search results list
        return
            results     (list)  search results list
        &#34;&#34;&#34;
        for _ in results:
            _[&#39;azureid&#39;] =  _[&#39;commonData&#39;][&#39;id&#39;].replace(&#34;x&#34;,&#34;-&#34;)
        return results
    def azuread_get_metadata(self, result):
        &#34;&#34;&#34; Get azure ad meta data for browse result
            Args:
                result    (list)    objects list from browse
            Return:
                metadata    (dict)    azure ad browse meta data
            Raise:
                110    can&#39;t find meta data
        &#34;&#34;&#34;
        metadata = {}
        if &#34;advancedData&#34; in result:
            if &#34;browseMetaData&#34; in result[&#39;advancedData&#39;]:
                metadata = result[&#39;advancedData&#39;][&#39;browseMetaData&#39;]
                if &#34;azureADDataV2&#34; in metadata:
                    metadata[&#39;azureADDataV2&#39;][&#39;guid&#39;] = result[&#39;advancedData&#39;][&#39;objectGuid&#39;]
                else:
                    raise SDKException(&#39;Backupset&#39;, &#39;110&#39;,
                                       &#34;Azure AD meta data is not found&#34;)
        return metadata

    def azuread_browse_obj_meta(self, obj_):
        &#34;&#34;&#34; get azure ad obj meta info
            Args:
                obj_    (obj)    azuare ad object
            Return:
                name    (str)    azure ad display name
                metainfo     (dict)    azure ad browse meta data
            Raise:
                None
        &#34;&#34;&#34;
        name = obj_[&#39;commonData&#39;][&#39;displayName&#39;]
        metainfo = {}
        metainfo[&#39;id&#39;] = obj_[&#39;commonData&#39;][&#39;id&#39;]
        metainfo[&#39;azureid&#39;] = metainfo[&#39;id&#39;].replace(&#34;x&#34;, &#34;-&#34;)
        metainfo[&#39;name&#39;] = name
        metainfo[&#39;guid&#39;] = obj_[&#39;guid&#39;]
        metainfo[&#39;type&#39;] = obj_[&#39;objType&#39;]
        return name, metainfo

    def azuread_browse_double_query(self, options, request_json):
        &#34;&#34;&#34; create request json for azure ad based on double query
            Args:
                options    (dict)    browse option from impoort
                request_json    (json)    request json file from basic request class
            Return:
                request_json    (json)    request json with addittional options
            Raise:
                None
        &#34;&#34;&#34;
        request_json[&#39;queries&#39;] = [{
                        &#34;type&#34;: &#34;0&#34;,
                        &#34;queryId&#34;: &#34;0&#34;,
                        &#34;whereClause&#34; :[],
                        &#34;dataParam&#34;: {
                            &#34;sortParam&#34;: {
                                &#34;ascending&#34;: True,
                                &#34;sortBy&#34;: [126]
                            },
                            &#34;paging&#34;: {
                                &#34;pageSize&#34;: int(options[&#39;page_size&#39;]),
                                &#34;skipNode&#34;: int(options[&#39;skip_node&#39;]),
                                &#34;firstNode&#34;: 0
                            }
                        }
                    },
                    {   &#34;type&#34;: &#34;1&#34;,
                        &#34;queryId&#34;: &#34;1&#34;,
                        &#34;whereClause&#34;: [],
                        &#34;aggrParam&#34;  : {&#39;aggrType&#39;: 4,
                                        &#39;field&#39;: 0}}]

        if options[&#39;filters&#39;]:
            for filter_ in options[&#39;filters&#39;]:
                filter_dict = {
                    &#39;connector&#39;: 0,
                    &#39;criteria&#39;: {
                        &#39;field&#39;: filter_[0],
                        &#39;values&#39;: [filter_[1]]}}
                if len(filter_) == 3:
                    filter_dict[&#39;criteria&#39;][&#39;dataOperator&#39;] = int(filter_[2])
                if filter_[0] == &#34;125&#34;:
                    del (filter_dict[&#39;connector&#39;])
                request_json[&#39;queries&#39;][0][&#39;whereClause&#39;].append(filter_dict)
                request_json[&#39;queries&#39;][1][&#39;whereClause&#39;].append(filter_dict)

        del(request_json[&#39;paths&#39;])
        return request_json

    def azuread_browse_options_builder(self, options):
        &#34;&#34;&#34; build browse options
            Args:
                options    (dict)    browse option from impoort
            Return:
                options    (list)    create formated options based on import
            Raise:
                None
        &#34;&#34;&#34;
        if &#34;filters&#34; not in options:
            options[&#39;filters&#39;] = [(&#34;76&#34;, &#34;00000000000000000000000000000001&#34;, &#34;9&#34;),
                                  (&#34;76&#34;, &#34;00000000000000000000000000000001&#34;, &#34;9&#34;)]
        if &#34;operation&#34; not in options:
            options[&#39;operation&#39;] = &#34;browse&#34;
            options[&#39;page_size&#39;] = 20
            options[&#39;skip_node&#39;] = 0
        return options

    def __prepare_search_json(self, options):
        &#34;&#34;&#34;
        performs view_properties (Cvpysdk Api call)
        Args:
            options     (dict)      example {&#34;to_time&#34;:(epoch) ,
                                            &#34;subclient_id&#34;:(string/optional),
                                            &#34;attribute&#34;: &#34;attribute to perform search &#34;}
            Return:     (dict)      view properties
        &#34;&#34;&#34;
        options[&#34;subclient_id&#34;] = self.subclients.all_subclients[&#39;default&#39;][&#39;id&#39;]

        request_json = {
            &#34;mode&#34;: 4,
            &#34;advSearchGrp&#34;: {
                &#34;commonFilter&#34;: [
                    {
                        &#34;filter&#34;: {
                            &#34;interFilterOP&#34;: 2,
                            &#34;filters&#34;: [
                                {
                                    &#34;field&#34;: &#34;CISTATE&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;1&#34;
                                        ]
                                    }
                                },
                                {
                                    &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;true&#34;
                                        ]
                                    }
                                }
                            ]
                        }
                    }
                ],
                &#34;fileFilter&#34;: [
                    {
                        &#34;interGroupOP&#34;: 2,
                        &#34;filter&#34;: {
                            &#34;interFilterOP&#34;: 2,
                            &#34;filters&#34;: [
                                {
                                    &#34;field&#34;: &#34;BACKUPTIME&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;0&#34;,
                                            str(options[&#34;to_time&#34;])
                                        ]
                                    }
                                },
                                {
                                    &#34;field&#34;: &#34;DATA_TYPE&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;1&#34;
                                        ]
                                    }
                                }
                            ]
                        }
                    }
                ],
                &#34;emailFilter&#34;: [],
                &#34;galaxyFilter&#34;: [
                    {
                        &#34;appIdList&#34;: [
                            int(options[&#34;subclient_id&#34;])
                        ]
                    }
                ],
                &#34;cvSearchKeyword&#34;: {
                    &#34;isExactWordsOptionSelected&#34;: False,
                    &#34;keyword&#34;: str(options[&#34;attribute&#34;]) + &#34;*&#34;
                }
            },
            &#34;searchProcessingInfo&#34;: {
                &#34;resultOffset&#34;: 0,
                &#34;pageSize&#34;: 15,
                &#34;queryParams&#34;: [
                    {
                        &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;,
                        &#34;value&#34;: &#34;true&#34;
                    },
                    {
                        &#34;param&#34;: &#34;ENABLE_NAVIGATION&#34;,
                        &#34;value&#34;: &#34;on&#34;
                    },
                    {
                        &#34;param&#34;: &#34;ENABLE_DEFAULTFACETS&#34;,
                        &#34;value&#34;: &#34;false&#34;
                    },
                    {
                        &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                        &#34;value&#34;: &#34;CONTENTID,CV_TURBO_GUID,PARENT_GUID,AFILEID,AFILEOFFSET,&#34;
                                 &#34;COMMCELLNO,MODIFIEDTIME,SIZEINKB,DATA_TYPE,AD_DISPLAYNAME,&#34;
                                 &#34;AD_ID,AD_OBJECT_TYPE,BACKUPTIME,AD_FLAGS,CISTATE,DATE_DELETED,&#34;
                                 &#34;AD_MAIL,AD_MAILNICKNAME,AD_PROXY_ADDRESSES,AD_BUSINESS_PHONES,&#34;
                                 &#34;AD_CITY,AD_COUNTRY,AD_DELETED_TIME,AD_POSTALCODE,AD_STATE,&#34;
                                 &#34;AD_STREET_ADDRESS,AD_LAST_DIR_SYNC_TIME,&#34;
                                 &#34;AD_COUNTRY_LETTER_CODE,AD_DIR_SYNC_ENABLED,AD_MKT_NOTIFY_MAILS,&#34;
                                 &#34;AD_TENANT_OBJECT_TYPE,AD_PREFER_LANG,AD_SEC_NOTIFY_MAILS,&#34;
                                 &#34;AD_SEC_NOTIFY_PHONES,AD_TECH_NOTIFY_MAILS,AD_TELEPHONE_NR,&#34;
                                 &#34;AD_CREATED_TIME,AD_DESCRIPTION,AD_GROUP_TYPES,AD_MAIL_ENABLED,&#34;
                                 &#34;AD_VISIBILITY,AD_SOURCE_TYPE,AD_AZURE_APP_ID,AD_HOME_PAGE_URL,&#34;
                                 &#34;AD_TAGS,AD_AZURE_APP_DISPLAY_NAME,AD_APP_OWNER_ORGID,&#34;
                                 &#34;AD_REPLY_URLS,AD_PUBLISHER_NAME,AD_SERVICE_PRINCIPAL_NAMES&#34;
                    },
                    {
                        &#34;param&#34;: &#34;DO_NOT_AUDIT&#34;,
                        &#34;value&#34;: &#34;true&#34;
                    },
                    {
                        &#34;param&#34;: &#34;COLLAPSE_FIELD&#34;,
                        &#34;value&#34;: &#34;AD_ID&#34;
                    },
                    {
                        &#34;param&#34;: &#34;COLLAPSE_SORT&#34;,
                        &#34;value&#34;: &#34;BACKUPTIME DESC&#34;
                    }
                ],
                &#34;sortParams&#34;: [
                    {
                        &#34;sortDirection&#34;: 0,
                        &#34;sortField&#34;: &#34;AD_DISPLAYNAME&#34;
                    }
                ]
            },
            &#34;facetRequests&#34;: {
                &#34;facetRequest&#34;: [
                    {
                        &#34;name&#34;: &#34;AD_OBJECT_TYPE&#34;
                    }
                ]
            }
        }

        return request_json

    def get_search_response(self, job_time, attribute):
        &#34;&#34;&#34;
            Searches for jobs based on the specified parameters.

            This method performs a search operation for jobs using the
            given job time, display name and application ID.
            Args:
                job_time    (str)   The job ends time.
                attribute   (str)  Attribute to search for.
            Return:
                The response contains the search results.
            Raises:
                SDKException: If there is a bad request
                or no object found with the specified display name.
        &#34;&#34;&#34;

        uri = self._services[&#34;DO_WEB_SEARCH&#34;]
        options = {&#34;to_time&#34;: job_time, &#34;attribute&#34;: attribute}
        request_json = self.__prepare_search_json(options)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, uri, request_json)
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        response = response.json()
        if response[&#34;proccessingInfo&#34;][&#34;totalHits&#34;] == 0:
            raise SDKException(&#39;Backupset&#39;, &#39;107&#39;, &#34;no result found with specified attribute&#34;)
        return response

    def view_attributes_url_builder(self,
                                    job_time,
                                    display_name):
        &#34;&#34;&#34;
        Builds a URL for viewing attributes based on the specified parameters.

        This method constructs a URL for viewing attributes of an
        object identified by the given job time and display name.
        Args:
            job_time    (str)   The job time.
            display_name    (str)   The display name of the object.
        Return:
            The URL for viewing attributes.
        &#34;&#34;&#34;

        subclient_id = self.subclients.all_subclients[&#39;default&#39;][&#39;id&#39;]

        try:
            search_response = self.get_search_response(job_time=job_time, attribute=display_name)
            afile_id = search_response[&#34;searchResult&#34;][&#34;resultItem&#34;][0][&#34;aFileId&#34;]
            afile_offset = search_response[&#34;searchResult&#34;][&#34;resultItem&#34;][0][&#34;aFileOffset&#34;]
            commcell_no = search_response[&#34;searchResult&#34;][&#34;resultItem&#34;][0][&#34;commcellNo&#34;]

            op = &#34;dGVtcC5qc29u&#34;  # any filename.json for view properties call
            # encoding string to base64
            stub_info = str(&#39;2:&#39; + str(commcell_no) + &#39;:0:&#39; +
                            str(afile_id) + &#39;:&#39; + str(afile_offset))
            stub_info = stub_info.encode(&#34;ascii&#34;)
            stub_info = base64.b64encode(stub_info)
            stub_info = stub_info.decode(&#34;ascii&#34;)

            url = self._services[&#39;VIEW_PROPERTIES&#39;] % (str(self._agent_object.agent_id),
                                                       stub_info,
                                                       op,
                                                       str(subclient_id),
                                                       &#39;1&#39;)
            return url
        except SDKException as e:
            raise SDKException(&#39;Backupset&#39;, &#39;107&#39;,
                               f&#34;No result found with specified attribute {e.exception_message}&#34;)

    def get_view_attribute_response(self,
                                    job_time,
                                    display_name):
        &#34;&#34;&#34;
        Retrieves view attributes based on the specified parameters.

        This method retrieves the view attributes of an
        object identified by the given job time and display name.
        Args:
            job_time    (int)   The job time.
            display_name    (string)    The display name of the object.
        Return:
            (dict)  The JSON response contains the view attributes.
        Raises:
            SDKException: If there is an error while retrieving the view attributes.
        &#34;&#34;&#34;
        try:
            url = self.view_attributes_url_builder(display_name=display_name,
                                                   job_time=job_time)

            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, url)
            if not flag:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#34;Response was not success&#34;)
            if not response:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;Response received is empty&#34;)
            return response.json()
        except Exception as e:
            raise SDKException(&#39;Backupset&#39;, &#39;107&#39;, f&#34;No result found {e.exception_message}&#34;)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.backupsets.aadbackupset.AzureAdBackupset"><code class="flex name class">
<span>class <span class="ident">AzureAdBackupset</span></span>
<span>(</span><span>instance_object, backupset_name, backupset_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Azure AD agent backupset class </p>
<p>Initialise the backupset object.</p>
<h2 id="args">Args</h2>
<p>instance_object
(object)
&ndash;
instance of the Instance class</p>
<p>backupset_name
(str)
&ndash;
name of the backupset</p>
<p>backupset_id
(str)
&ndash;
id of the backupset
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Backupset class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L64-L591" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AzureAdBackupset(Backupset):
    &#34;&#34;&#34; Azure AD agent backupset class &#34;&#34;&#34;

    def _azuread_browse_basic(self, options):
        &#34;&#34;&#34; do basic browse activity with options
            Args:
                options    (dict)    browse option from impoort
                                adv_attributes    get object advanced attribute
            Return:
                count     (int)    return count from browse
                result    (list)    objects list from browse
            Raise:
                101    If browse return Nothing
        &#34;&#34;&#34;
        options = self._prepare_browse_options(options)

        request_json = self._prepare_browse_json(options)
        request_json = self.azuread_browse_double_query(options, request_json)
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;,
                                                           self._BROWSE,
                                                           request_json)
        if flag:
            count, result = self._process_browse_response(flag, response, options)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return count, result

    def _azuread_browse_meta(self, options):
        &#34;&#34;&#34; get basic browse related meta data
            Args:
                options    (dict)    browse option from impoort
            Return:
                azure_meta    (dict)    azure ad meta data from browse result
            Raise:
                None
        &#34;&#34;&#34;
        if &#34;meta&#34; in options:
            azure_meta = options[&#39;meta&#39;]
        else:
            azure_meta = {}
        count, result = self._azuread_browse_basic(options)
        if len(result) == 1 and result[0][&#39;objType&#39;] == 1:
            newid = result[0][&#39;commonData&#39;][&#39;id&#39;]
            options[&#39;filters&#39;] = [(_[0], newid, _[2]) for _ in options[&#39;filters&#39;]]
            name, metainfo = self.azuread_browse_obj_meta(result[0])
            azure_meta[&#39;root&#39;] = metainfo
            options[&#39;meta&#39;] = azure_meta
            self._azuread_browse_meta(options)
        else:
            for r_ in result:
                if r_[&#39;objType&#39;] == 100:
                    options[&#39;filters&#39;] = [(&#34;76&#34;, r_[&#39;commonData&#39;][&#39;id&#39;], &#34;9&#34;),
                                        (&#34;125&#34;, &#34;FOLDER&#34;)]
                    count_, metainfo_ = self._azuread_browse_basic(options)
                    for i_ in metainfo_:
                        name, metainfo = self.azuread_browse_obj_meta(i_)
                        azure_meta[name] = metainfo
                else:
                    name, metainfo = self.azuread_browse_obj_meta(r_)
                    azure_meta[name] = metainfo
        return azure_meta

    def _azuread_browse_folder(self, options):
        &#34;&#34;&#34; browse folder content
            Args:
                options    (dict)    browse option from impoort
            Return:
                count     (int)    return count from browse
                result    (list)    objects list from browse
            Raise:
                None
        &#34;&#34;&#34;
        azure_meta_mapper = {
            &#34;user&#34; : { &#34;displayname&#34; : &#34;Users&#34;, &#34;browsetype&#34; : 2, &#34;browsestring&#34; : &#34;USER&#34;},
            &#34;group&#34; : { &#34;displayname&#34; : &#34;Groups&#34;, &#34;browsetype&#34; : 3, &#34;browsestring&#34;: &#34;GROUP&#34;},
            &#34;reg_app&#34; : {&#34;displayname&#34; : &#34;App registrations&#34;, &#34;browsetype&#34; : 5, &#34;browsestring&#34; : &#34;APPLICATION&#34;},
            &#34;ent_app&#34; : { &#34;displayname&#34; : &#34;Enterprise applications&#34;,&#34;browsetype&#34;: 6, &#34;browsestring&#34; : &#34;SERVICE_PRINCIPAL&#34;},
            &#34;ca_policy&#34; : { &#34;displayname&#34; : &#34;Policies&#34;,&#34;browsetype&#34;: 11, &#34;browsestring&#34; : &#34;CONDITIONAL_ACCESS_POLICY&#34;},
            &#34;ca_name_location&#34; : { &#34;displayname&#34; : &#34;Named locations&#34;,&#34;browsetype&#34;: 12, &#34;browsestring&#34; : &#34;NAMED_LOCATION&#34;} ,
            &#34;ca_auth_context&#34; : { &#34;displayname&#34; : &#34;Authentication context&#34;,&#34;browsetype&#34;: 13, &#34;browsestring&#34; : &#34;AUTHENTICATION_CONTEXT&#34;},
            &#34;ca_auth_strength&#34; : { &#34;displayname&#34; : &#34;Authentication strengths&#34;,&#34;browsetype&#34;: 14, &#34;browsestring&#34; : &#34;AUTHENTICATION_STRENGTH&#34;},
            &#34;role&#34; : { &#34;displayname&#34; : &#34;Roles&#34;,&#34;browsetype&#34;: 15, &#34;browsestring&#34; : &#34;DIRECTORY_ROLE_DEFINITIONS&#34;},
            &#34;admin_unit&#34; : { &#34;displayname&#34; : &#34;Admin units&#34;,&#34;browsetype&#34;: 16, &#34;browsestring&#34; : &#34;ADMINISTRATIVE_UNIT&#34;}}
        azure_meta = options[&#39;meta&#39;]
        newid = azure_meta[azure_meta_mapper[options[&#39;folder&#39;]][&#39;displayname&#39;]][&#39;id&#39;]
        options[&#39;filters&#39;] = [(&#34;76&#34;, newid, &#34;9&#34;),
                              (&#34;125&#34;, azure_meta_mapper[options[&#39;folder&#39;]][&#39;browsestring&#39;])]
        if &#34;search&#34; in options:
            if isinstance(options[&#39;search&#39;], dict):
                search_dict = options[&#39;search&#39;]
                if &#34;obj_id&#34; in search_dict:
                    options[&#39;filters&#39;].append((&#34;130&#34;, search_dict[&#39;obj_id&#39;]))
                if &#34;source&#34; in search_dict:
                    if search_dict[&#39;source&#39;] == &#34;AzureAD&#34;:
                        options[&#39;filters&#39;].append((&#34;128&#34;, &#34;0&#34;))
                    elif search_dict[&#39;source&#39;] == &#34;WinAD&#34;:
                        options[&#39;filters&#39;].append((&#34;128&#34;, &#34;1&#34;))
            else:
                options[&#39;filters&#39;].append((&#34;30&#34;, options[&#39;search&#39;]))

        del(options[&#39;folder&#39;])
        del(options[&#39;meta&#39;])
        del(options[&#39;path&#39;])
        count, results = self._azuread_browse_basic(options)
        results = self._process_result_format(results)
        return count, results

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Backupset.
            Args:
                args    list    args passed for browse
                kwargs    dict    dict passed for browse
            Return:
                count     (int)    return count from browse
                browse_result    (list)    objects list from browse
            Raise:
                None
        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs
        options = self.azuread_browse_options_builder(options)
        azure_meta = self._azuread_browse_meta(options)
        options[&#39;meta&#39;] = azure_meta
        count, browse_result = self._azuread_browse_folder(options)

        return count, browse_result

    def _process_browse_response(self, flag, response, options):
        &#34;&#34;&#34;Retrieves the items from browse response.
            Args:
                flag    (bool)  --  boolean, whether the response was success or not
                response (dict)  --  JSON response received for the request from the Server
                options  (dict)  --  The browse options dictionary
            Returns:
                list - List of only the file / folder paths from the browse response
                dict - Dictionary of all the paths with additional metadata retrieved from browse
            Raises:
                SDKException:
                    if failed to browse/search for content
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        metadatas = []
        if flag:
            if response.json():
                response_json = response.json()
                if response_json and &#39;browseResponses&#39; in response_json:
                    _browse_responses = response_json[&#39;browseResponses&#39;]
                    for browse_response in _browse_responses:
                        if &#34;browseResult&#34; in browse_response:
                            browse_result = browse_response[&#39;browseResult&#39;]
                            browseresulttcount = browse_result[&#39;totalItemsFound&#39;]
                            if &#39;dataResultSet&#39; in browse_result:
                                result_set = browse_result[&#39;dataResultSet&#39;]
                    if not result_set and result_set != []:
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;,
                                           &#34;Failed to browse for subclient backup content&#34;)
                    else:
                        for result in result_set:
                            metadata = self.azuread_get_metadata(result)
                            metadatas.append(metadata[&#39;azureADDataV2&#39;])
            else:
                raise SDKException(&#34;Backupset&#34;, &#34;102&#34;, &#34;response is not valid&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return browseresulttcount, metadatas

    def _process_result_format(self, results):
        &#34;&#34;&#34;
        process the browse result to original data format
        Args:
            results     (list)  search results list
        return
            results     (list)  search results list
        &#34;&#34;&#34;
        for _ in results:
            _[&#39;azureid&#39;] =  _[&#39;commonData&#39;][&#39;id&#39;].replace(&#34;x&#34;,&#34;-&#34;)
        return results
    def azuread_get_metadata(self, result):
        &#34;&#34;&#34; Get azure ad meta data for browse result
            Args:
                result    (list)    objects list from browse
            Return:
                metadata    (dict)    azure ad browse meta data
            Raise:
                110    can&#39;t find meta data
        &#34;&#34;&#34;
        metadata = {}
        if &#34;advancedData&#34; in result:
            if &#34;browseMetaData&#34; in result[&#39;advancedData&#39;]:
                metadata = result[&#39;advancedData&#39;][&#39;browseMetaData&#39;]
                if &#34;azureADDataV2&#34; in metadata:
                    metadata[&#39;azureADDataV2&#39;][&#39;guid&#39;] = result[&#39;advancedData&#39;][&#39;objectGuid&#39;]
                else:
                    raise SDKException(&#39;Backupset&#39;, &#39;110&#39;,
                                       &#34;Azure AD meta data is not found&#34;)
        return metadata

    def azuread_browse_obj_meta(self, obj_):
        &#34;&#34;&#34; get azure ad obj meta info
            Args:
                obj_    (obj)    azuare ad object
            Return:
                name    (str)    azure ad display name
                metainfo     (dict)    azure ad browse meta data
            Raise:
                None
        &#34;&#34;&#34;
        name = obj_[&#39;commonData&#39;][&#39;displayName&#39;]
        metainfo = {}
        metainfo[&#39;id&#39;] = obj_[&#39;commonData&#39;][&#39;id&#39;]
        metainfo[&#39;azureid&#39;] = metainfo[&#39;id&#39;].replace(&#34;x&#34;, &#34;-&#34;)
        metainfo[&#39;name&#39;] = name
        metainfo[&#39;guid&#39;] = obj_[&#39;guid&#39;]
        metainfo[&#39;type&#39;] = obj_[&#39;objType&#39;]
        return name, metainfo

    def azuread_browse_double_query(self, options, request_json):
        &#34;&#34;&#34; create request json for azure ad based on double query
            Args:
                options    (dict)    browse option from impoort
                request_json    (json)    request json file from basic request class
            Return:
                request_json    (json)    request json with addittional options
            Raise:
                None
        &#34;&#34;&#34;
        request_json[&#39;queries&#39;] = [{
                        &#34;type&#34;: &#34;0&#34;,
                        &#34;queryId&#34;: &#34;0&#34;,
                        &#34;whereClause&#34; :[],
                        &#34;dataParam&#34;: {
                            &#34;sortParam&#34;: {
                                &#34;ascending&#34;: True,
                                &#34;sortBy&#34;: [126]
                            },
                            &#34;paging&#34;: {
                                &#34;pageSize&#34;: int(options[&#39;page_size&#39;]),
                                &#34;skipNode&#34;: int(options[&#39;skip_node&#39;]),
                                &#34;firstNode&#34;: 0
                            }
                        }
                    },
                    {   &#34;type&#34;: &#34;1&#34;,
                        &#34;queryId&#34;: &#34;1&#34;,
                        &#34;whereClause&#34;: [],
                        &#34;aggrParam&#34;  : {&#39;aggrType&#39;: 4,
                                        &#39;field&#39;: 0}}]

        if options[&#39;filters&#39;]:
            for filter_ in options[&#39;filters&#39;]:
                filter_dict = {
                    &#39;connector&#39;: 0,
                    &#39;criteria&#39;: {
                        &#39;field&#39;: filter_[0],
                        &#39;values&#39;: [filter_[1]]}}
                if len(filter_) == 3:
                    filter_dict[&#39;criteria&#39;][&#39;dataOperator&#39;] = int(filter_[2])
                if filter_[0] == &#34;125&#34;:
                    del (filter_dict[&#39;connector&#39;])
                request_json[&#39;queries&#39;][0][&#39;whereClause&#39;].append(filter_dict)
                request_json[&#39;queries&#39;][1][&#39;whereClause&#39;].append(filter_dict)

        del(request_json[&#39;paths&#39;])
        return request_json

    def azuread_browse_options_builder(self, options):
        &#34;&#34;&#34; build browse options
            Args:
                options    (dict)    browse option from impoort
            Return:
                options    (list)    create formated options based on import
            Raise:
                None
        &#34;&#34;&#34;
        if &#34;filters&#34; not in options:
            options[&#39;filters&#39;] = [(&#34;76&#34;, &#34;00000000000000000000000000000001&#34;, &#34;9&#34;),
                                  (&#34;76&#34;, &#34;00000000000000000000000000000001&#34;, &#34;9&#34;)]
        if &#34;operation&#34; not in options:
            options[&#39;operation&#39;] = &#34;browse&#34;
            options[&#39;page_size&#39;] = 20
            options[&#39;skip_node&#39;] = 0
        return options

    def __prepare_search_json(self, options):
        &#34;&#34;&#34;
        performs view_properties (Cvpysdk Api call)
        Args:
            options     (dict)      example {&#34;to_time&#34;:(epoch) ,
                                            &#34;subclient_id&#34;:(string/optional),
                                            &#34;attribute&#34;: &#34;attribute to perform search &#34;}
            Return:     (dict)      view properties
        &#34;&#34;&#34;
        options[&#34;subclient_id&#34;] = self.subclients.all_subclients[&#39;default&#39;][&#39;id&#39;]

        request_json = {
            &#34;mode&#34;: 4,
            &#34;advSearchGrp&#34;: {
                &#34;commonFilter&#34;: [
                    {
                        &#34;filter&#34;: {
                            &#34;interFilterOP&#34;: 2,
                            &#34;filters&#34;: [
                                {
                                    &#34;field&#34;: &#34;CISTATE&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;1&#34;
                                        ]
                                    }
                                },
                                {
                                    &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;true&#34;
                                        ]
                                    }
                                }
                            ]
                        }
                    }
                ],
                &#34;fileFilter&#34;: [
                    {
                        &#34;interGroupOP&#34;: 2,
                        &#34;filter&#34;: {
                            &#34;interFilterOP&#34;: 2,
                            &#34;filters&#34;: [
                                {
                                    &#34;field&#34;: &#34;BACKUPTIME&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;0&#34;,
                                            str(options[&#34;to_time&#34;])
                                        ]
                                    }
                                },
                                {
                                    &#34;field&#34;: &#34;DATA_TYPE&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;1&#34;
                                        ]
                                    }
                                }
                            ]
                        }
                    }
                ],
                &#34;emailFilter&#34;: [],
                &#34;galaxyFilter&#34;: [
                    {
                        &#34;appIdList&#34;: [
                            int(options[&#34;subclient_id&#34;])
                        ]
                    }
                ],
                &#34;cvSearchKeyword&#34;: {
                    &#34;isExactWordsOptionSelected&#34;: False,
                    &#34;keyword&#34;: str(options[&#34;attribute&#34;]) + &#34;*&#34;
                }
            },
            &#34;searchProcessingInfo&#34;: {
                &#34;resultOffset&#34;: 0,
                &#34;pageSize&#34;: 15,
                &#34;queryParams&#34;: [
                    {
                        &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;,
                        &#34;value&#34;: &#34;true&#34;
                    },
                    {
                        &#34;param&#34;: &#34;ENABLE_NAVIGATION&#34;,
                        &#34;value&#34;: &#34;on&#34;
                    },
                    {
                        &#34;param&#34;: &#34;ENABLE_DEFAULTFACETS&#34;,
                        &#34;value&#34;: &#34;false&#34;
                    },
                    {
                        &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                        &#34;value&#34;: &#34;CONTENTID,CV_TURBO_GUID,PARENT_GUID,AFILEID,AFILEOFFSET,&#34;
                                 &#34;COMMCELLNO,MODIFIEDTIME,SIZEINKB,DATA_TYPE,AD_DISPLAYNAME,&#34;
                                 &#34;AD_ID,AD_OBJECT_TYPE,BACKUPTIME,AD_FLAGS,CISTATE,DATE_DELETED,&#34;
                                 &#34;AD_MAIL,AD_MAILNICKNAME,AD_PROXY_ADDRESSES,AD_BUSINESS_PHONES,&#34;
                                 &#34;AD_CITY,AD_COUNTRY,AD_DELETED_TIME,AD_POSTALCODE,AD_STATE,&#34;
                                 &#34;AD_STREET_ADDRESS,AD_LAST_DIR_SYNC_TIME,&#34;
                                 &#34;AD_COUNTRY_LETTER_CODE,AD_DIR_SYNC_ENABLED,AD_MKT_NOTIFY_MAILS,&#34;
                                 &#34;AD_TENANT_OBJECT_TYPE,AD_PREFER_LANG,AD_SEC_NOTIFY_MAILS,&#34;
                                 &#34;AD_SEC_NOTIFY_PHONES,AD_TECH_NOTIFY_MAILS,AD_TELEPHONE_NR,&#34;
                                 &#34;AD_CREATED_TIME,AD_DESCRIPTION,AD_GROUP_TYPES,AD_MAIL_ENABLED,&#34;
                                 &#34;AD_VISIBILITY,AD_SOURCE_TYPE,AD_AZURE_APP_ID,AD_HOME_PAGE_URL,&#34;
                                 &#34;AD_TAGS,AD_AZURE_APP_DISPLAY_NAME,AD_APP_OWNER_ORGID,&#34;
                                 &#34;AD_REPLY_URLS,AD_PUBLISHER_NAME,AD_SERVICE_PRINCIPAL_NAMES&#34;
                    },
                    {
                        &#34;param&#34;: &#34;DO_NOT_AUDIT&#34;,
                        &#34;value&#34;: &#34;true&#34;
                    },
                    {
                        &#34;param&#34;: &#34;COLLAPSE_FIELD&#34;,
                        &#34;value&#34;: &#34;AD_ID&#34;
                    },
                    {
                        &#34;param&#34;: &#34;COLLAPSE_SORT&#34;,
                        &#34;value&#34;: &#34;BACKUPTIME DESC&#34;
                    }
                ],
                &#34;sortParams&#34;: [
                    {
                        &#34;sortDirection&#34;: 0,
                        &#34;sortField&#34;: &#34;AD_DISPLAYNAME&#34;
                    }
                ]
            },
            &#34;facetRequests&#34;: {
                &#34;facetRequest&#34;: [
                    {
                        &#34;name&#34;: &#34;AD_OBJECT_TYPE&#34;
                    }
                ]
            }
        }

        return request_json

    def get_search_response(self, job_time, attribute):
        &#34;&#34;&#34;
            Searches for jobs based on the specified parameters.

            This method performs a search operation for jobs using the
            given job time, display name and application ID.
            Args:
                job_time    (str)   The job ends time.
                attribute   (str)  Attribute to search for.
            Return:
                The response contains the search results.
            Raises:
                SDKException: If there is a bad request
                or no object found with the specified display name.
        &#34;&#34;&#34;

        uri = self._services[&#34;DO_WEB_SEARCH&#34;]
        options = {&#34;to_time&#34;: job_time, &#34;attribute&#34;: attribute}
        request_json = self.__prepare_search_json(options)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, uri, request_json)
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        response = response.json()
        if response[&#34;proccessingInfo&#34;][&#34;totalHits&#34;] == 0:
            raise SDKException(&#39;Backupset&#39;, &#39;107&#39;, &#34;no result found with specified attribute&#34;)
        return response

    def view_attributes_url_builder(self,
                                    job_time,
                                    display_name):
        &#34;&#34;&#34;
        Builds a URL for viewing attributes based on the specified parameters.

        This method constructs a URL for viewing attributes of an
        object identified by the given job time and display name.
        Args:
            job_time    (str)   The job time.
            display_name    (str)   The display name of the object.
        Return:
            The URL for viewing attributes.
        &#34;&#34;&#34;

        subclient_id = self.subclients.all_subclients[&#39;default&#39;][&#39;id&#39;]

        try:
            search_response = self.get_search_response(job_time=job_time, attribute=display_name)
            afile_id = search_response[&#34;searchResult&#34;][&#34;resultItem&#34;][0][&#34;aFileId&#34;]
            afile_offset = search_response[&#34;searchResult&#34;][&#34;resultItem&#34;][0][&#34;aFileOffset&#34;]
            commcell_no = search_response[&#34;searchResult&#34;][&#34;resultItem&#34;][0][&#34;commcellNo&#34;]

            op = &#34;dGVtcC5qc29u&#34;  # any filename.json for view properties call
            # encoding string to base64
            stub_info = str(&#39;2:&#39; + str(commcell_no) + &#39;:0:&#39; +
                            str(afile_id) + &#39;:&#39; + str(afile_offset))
            stub_info = stub_info.encode(&#34;ascii&#34;)
            stub_info = base64.b64encode(stub_info)
            stub_info = stub_info.decode(&#34;ascii&#34;)

            url = self._services[&#39;VIEW_PROPERTIES&#39;] % (str(self._agent_object.agent_id),
                                                       stub_info,
                                                       op,
                                                       str(subclient_id),
                                                       &#39;1&#39;)
            return url
        except SDKException as e:
            raise SDKException(&#39;Backupset&#39;, &#39;107&#39;,
                               f&#34;No result found with specified attribute {e.exception_message}&#34;)

    def get_view_attribute_response(self,
                                    job_time,
                                    display_name):
        &#34;&#34;&#34;
        Retrieves view attributes based on the specified parameters.

        This method retrieves the view attributes of an
        object identified by the given job time and display name.
        Args:
            job_time    (int)   The job time.
            display_name    (string)    The display name of the object.
        Return:
            (dict)  The JSON response contains the view attributes.
        Raises:
            SDKException: If there is an error while retrieving the view attributes.
        &#34;&#34;&#34;
        try:
            url = self.view_attributes_url_builder(display_name=display_name,
                                                   job_time=job_time)

            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, url)
            if not flag:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#34;Response was not success&#34;)
            if not response:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;Response received is empty&#34;)
            return response.json()
        except Exception as e:
            raise SDKException(&#39;Backupset&#39;, &#39;107&#39;, f&#34;No result found {e.exception_message}&#34;)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.backupset.Backupset" href="../backupset.html#cvpysdk.backupset.Backupset">Backupset</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_browse_double_query"><code class="name flex">
<span>def <span class="ident">azuread_browse_double_query</span></span>(<span>self, options, request_json)</span>
</code></dt>
<dd>
<div class="desc"><p>create request json for azure ad based on double query</p>
<h2 id="args">Args</h2>
<p>options
(dict)
browse option from impoort
request_json
(json)
request json file from basic request class</p>
<h2 id="return">Return</h2>
<p>request_json
(json)
request json with addittional options</p>
<h2 id="raise">Raise</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L283-L330" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def azuread_browse_double_query(self, options, request_json):
    &#34;&#34;&#34; create request json for azure ad based on double query
        Args:
            options    (dict)    browse option from impoort
            request_json    (json)    request json file from basic request class
        Return:
            request_json    (json)    request json with addittional options
        Raise:
            None
    &#34;&#34;&#34;
    request_json[&#39;queries&#39;] = [{
                    &#34;type&#34;: &#34;0&#34;,
                    &#34;queryId&#34;: &#34;0&#34;,
                    &#34;whereClause&#34; :[],
                    &#34;dataParam&#34;: {
                        &#34;sortParam&#34;: {
                            &#34;ascending&#34;: True,
                            &#34;sortBy&#34;: [126]
                        },
                        &#34;paging&#34;: {
                            &#34;pageSize&#34;: int(options[&#39;page_size&#39;]),
                            &#34;skipNode&#34;: int(options[&#39;skip_node&#39;]),
                            &#34;firstNode&#34;: 0
                        }
                    }
                },
                {   &#34;type&#34;: &#34;1&#34;,
                    &#34;queryId&#34;: &#34;1&#34;,
                    &#34;whereClause&#34;: [],
                    &#34;aggrParam&#34;  : {&#39;aggrType&#39;: 4,
                                    &#39;field&#39;: 0}}]

    if options[&#39;filters&#39;]:
        for filter_ in options[&#39;filters&#39;]:
            filter_dict = {
                &#39;connector&#39;: 0,
                &#39;criteria&#39;: {
                    &#39;field&#39;: filter_[0],
                    &#39;values&#39;: [filter_[1]]}}
            if len(filter_) == 3:
                filter_dict[&#39;criteria&#39;][&#39;dataOperator&#39;] = int(filter_[2])
            if filter_[0] == &#34;125&#34;:
                del (filter_dict[&#39;connector&#39;])
            request_json[&#39;queries&#39;][0][&#39;whereClause&#39;].append(filter_dict)
            request_json[&#39;queries&#39;][1][&#39;whereClause&#39;].append(filter_dict)

    del(request_json[&#39;paths&#39;])
    return request_json</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_browse_obj_meta"><code class="name flex">
<span>def <span class="ident">azuread_browse_obj_meta</span></span>(<span>self, obj_)</span>
</code></dt>
<dd>
<div class="desc"><p>get azure ad obj meta info</p>
<h2 id="args">Args</h2>
<p>obj_
(obj)
azuare ad object</p>
<h2 id="return">Return</h2>
<p>name
(str)
azure ad display name
metainfo
(dict)
azure ad browse meta data</p>
<h2 id="raise">Raise</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L264-L281" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def azuread_browse_obj_meta(self, obj_):
    &#34;&#34;&#34; get azure ad obj meta info
        Args:
            obj_    (obj)    azuare ad object
        Return:
            name    (str)    azure ad display name
            metainfo     (dict)    azure ad browse meta data
        Raise:
            None
    &#34;&#34;&#34;
    name = obj_[&#39;commonData&#39;][&#39;displayName&#39;]
    metainfo = {}
    metainfo[&#39;id&#39;] = obj_[&#39;commonData&#39;][&#39;id&#39;]
    metainfo[&#39;azureid&#39;] = metainfo[&#39;id&#39;].replace(&#34;x&#34;, &#34;-&#34;)
    metainfo[&#39;name&#39;] = name
    metainfo[&#39;guid&#39;] = obj_[&#39;guid&#39;]
    metainfo[&#39;type&#39;] = obj_[&#39;objType&#39;]
    return name, metainfo</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_browse_options_builder"><code class="name flex">
<span>def <span class="ident">azuread_browse_options_builder</span></span>(<span>self, options)</span>
</code></dt>
<dd>
<div class="desc"><p>build browse options</p>
<h2 id="args">Args</h2>
<p>options
(dict)
browse option from impoort</p>
<h2 id="return">Return</h2>
<p>options
(list)
create formated options based on import</p>
<h2 id="raise">Raise</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L332-L348" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def azuread_browse_options_builder(self, options):
    &#34;&#34;&#34; build browse options
        Args:
            options    (dict)    browse option from impoort
        Return:
            options    (list)    create formated options based on import
        Raise:
            None
    &#34;&#34;&#34;
    if &#34;filters&#34; not in options:
        options[&#39;filters&#39;] = [(&#34;76&#34;, &#34;00000000000000000000000000000001&#34;, &#34;9&#34;),
                              (&#34;76&#34;, &#34;00000000000000000000000000000001&#34;, &#34;9&#34;)]
    if &#34;operation&#34; not in options:
        options[&#39;operation&#39;] = &#34;browse&#34;
        options[&#39;page_size&#39;] = 20
        options[&#39;skip_node&#39;] = 0
    return options</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_get_metadata"><code class="name flex">
<span>def <span class="ident">azuread_get_metadata</span></span>(<span>self, result)</span>
</code></dt>
<dd>
<div class="desc"><p>Get azure ad meta data for browse result</p>
<h2 id="args">Args</h2>
<p>result
(list)
objects list from browse</p>
<h2 id="return">Return</h2>
<p>metadata
(dict)
azure ad browse meta data</p>
<h2 id="raise">Raise</h2>
<p>110
can't find meta data</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L244-L262" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def azuread_get_metadata(self, result):
    &#34;&#34;&#34; Get azure ad meta data for browse result
        Args:
            result    (list)    objects list from browse
        Return:
            metadata    (dict)    azure ad browse meta data
        Raise:
            110    can&#39;t find meta data
    &#34;&#34;&#34;
    metadata = {}
    if &#34;advancedData&#34; in result:
        if &#34;browseMetaData&#34; in result[&#39;advancedData&#39;]:
            metadata = result[&#39;advancedData&#39;][&#39;browseMetaData&#39;]
            if &#34;azureADDataV2&#34; in metadata:
                metadata[&#39;azureADDataV2&#39;][&#39;guid&#39;] = result[&#39;advancedData&#39;][&#39;objectGuid&#39;]
            else:
                raise SDKException(&#39;Backupset&#39;, &#39;110&#39;,
                                   &#34;Azure AD meta data is not found&#34;)
    return metadata</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the content of the Backupset.</p>
<h2 id="args">Args</h2>
<p>args
list
args passed for browse
kwargs
dict
dict passed for browse</p>
<h2 id="return">Return</h2>
<p>count
(int)
return count from browse
browse_result
(list)
objects list from browse</p>
<h2 id="raise">Raise</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L171-L191" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, *args, **kwargs):
    &#34;&#34;&#34;Browses the content of the Backupset.
        Args:
            args    list    args passed for browse
            kwargs    dict    dict passed for browse
        Return:
            count     (int)    return count from browse
            browse_result    (list)    objects list from browse
        Raise:
            None
    &#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs
    options = self.azuread_browse_options_builder(options)
    azure_meta = self._azuread_browse_meta(options)
    options[&#39;meta&#39;] = azure_meta
    count, browse_result = self._azuread_browse_folder(options)

    return count, browse_result</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.get_search_response"><code class="name flex">
<span>def <span class="ident">get_search_response</span></span>(<span>self, job_time, attribute)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches for jobs based on the specified parameters.</p>
<p>This method performs a search operation for jobs using the
given job time, display name and application ID.</p>
<h2 id="args">Args</h2>
<p>job_time
(str)
The job ends time.
attribute
(str)
Attribute to search for.</p>
<h2 id="return">Return</h2>
<p>The response contains the search results.</p>
<h2 id="raises">Raises</h2>
<dl>
<dt><code>SDKException</code></dt>
<dd>If there is a bad request</dd>
</dl>
<p>or no object found with the specified display name.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L496-L521" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_search_response(self, job_time, attribute):
    &#34;&#34;&#34;
        Searches for jobs based on the specified parameters.

        This method performs a search operation for jobs using the
        given job time, display name and application ID.
        Args:
            job_time    (str)   The job ends time.
            attribute   (str)  Attribute to search for.
        Return:
            The response contains the search results.
        Raises:
            SDKException: If there is a bad request
            or no object found with the specified display name.
    &#34;&#34;&#34;

    uri = self._services[&#34;DO_WEB_SEARCH&#34;]
    options = {&#34;to_time&#34;: job_time, &#34;attribute&#34;: attribute}
    request_json = self.__prepare_search_json(options)
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, uri, request_json)
    if not flag:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    response = response.json()
    if response[&#34;proccessingInfo&#34;][&#34;totalHits&#34;] == 0:
        raise SDKException(&#39;Backupset&#39;, &#39;107&#39;, &#34;no result found with specified attribute&#34;)
    return response</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.get_view_attribute_response"><code class="name flex">
<span>def <span class="ident">get_view_attribute_response</span></span>(<span>self, job_time, display_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Retrieves view attributes based on the specified parameters.</p>
<p>This method retrieves the view attributes of an
object identified by the given job time and display name.</p>
<h2 id="args">Args</h2>
<p>job_time
(int)
The job time.
display_name
(string)
The display name of the object.</p>
<h2 id="return">Return</h2>
<p>(dict)
The JSON response contains the view attributes.</p>
<h2 id="raises">Raises</h2>
<dl>
<dt><code>SDKException</code></dt>
<dd>If there is an error while retrieving the view attributes.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L564-L591" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_view_attribute_response(self,
                                job_time,
                                display_name):
    &#34;&#34;&#34;
    Retrieves view attributes based on the specified parameters.

    This method retrieves the view attributes of an
    object identified by the given job time and display name.
    Args:
        job_time    (int)   The job time.
        display_name    (string)    The display name of the object.
    Return:
        (dict)  The JSON response contains the view attributes.
    Raises:
        SDKException: If there is an error while retrieving the view attributes.
    &#34;&#34;&#34;
    try:
        url = self.view_attributes_url_builder(display_name=display_name,
                                               job_time=job_time)

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, url)
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#34;Response was not success&#34;)
        if not response:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;Response received is empty&#34;)
        return response.json()
    except Exception as e:
        raise SDKException(&#39;Backupset&#39;, &#39;107&#39;, f&#34;No result found {e.exception_message}&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.view_attributes_url_builder"><code class="name flex">
<span>def <span class="ident">view_attributes_url_builder</span></span>(<span>self, job_time, display_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Builds a URL for viewing attributes based on the specified parameters.</p>
<p>This method constructs a URL for viewing attributes of an
object identified by the given job time and display name.</p>
<h2 id="args">Args</h2>
<p>job_time
(str)
The job time.
display_name
(str)
The display name of the object.</p>
<h2 id="return">Return</h2>
<p>The URL for viewing attributes.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/aadbackupset.py#L523-L562" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def view_attributes_url_builder(self,
                                job_time,
                                display_name):
    &#34;&#34;&#34;
    Builds a URL for viewing attributes based on the specified parameters.

    This method constructs a URL for viewing attributes of an
    object identified by the given job time and display name.
    Args:
        job_time    (str)   The job time.
        display_name    (str)   The display name of the object.
    Return:
        The URL for viewing attributes.
    &#34;&#34;&#34;

    subclient_id = self.subclients.all_subclients[&#39;default&#39;][&#39;id&#39;]

    try:
        search_response = self.get_search_response(job_time=job_time, attribute=display_name)
        afile_id = search_response[&#34;searchResult&#34;][&#34;resultItem&#34;][0][&#34;aFileId&#34;]
        afile_offset = search_response[&#34;searchResult&#34;][&#34;resultItem&#34;][0][&#34;aFileOffset&#34;]
        commcell_no = search_response[&#34;searchResult&#34;][&#34;resultItem&#34;][0][&#34;commcellNo&#34;]

        op = &#34;dGVtcC5qc29u&#34;  # any filename.json for view properties call
        # encoding string to base64
        stub_info = str(&#39;2:&#39; + str(commcell_no) + &#39;:0:&#39; +
                        str(afile_id) + &#39;:&#39; + str(afile_offset))
        stub_info = stub_info.encode(&#34;ascii&#34;)
        stub_info = base64.b64encode(stub_info)
        stub_info = stub_info.decode(&#34;ascii&#34;)

        url = self._services[&#39;VIEW_PROPERTIES&#39;] % (str(self._agent_object.agent_id),
                                                   stub_info,
                                                   op,
                                                   str(subclient_id),
                                                   &#39;1&#39;)
        return url
    except SDKException as e:
        raise SDKException(&#39;Backupset&#39;, &#39;107&#39;,
                           f&#34;No result found with specified attribute {e.exception_message}&#34;)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.backupset.Backupset" href="../backupset.html#cvpysdk.backupset.Backupset">Backupset</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.backupset.Backupset.backed_up_files_count" href="../backupset.html#cvpysdk.backupset.Backupset.backed_up_files_count">backed_up_files_count</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backup" href="../backupset.html#cvpysdk.backupset.Backupset.backup">backup</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_id" href="../backupset.html#cvpysdk.backupset.Backupset.backupset_id">backupset_id</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_name" href="../backupset.html#cvpysdk.backupset.Backupset.backupset_name">backupset_name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.delete_data" href="../backupset.html#cvpysdk.backupset.Backupset.delete_data">delete_data</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.description" href="../backupset.html#cvpysdk.backupset.Backupset.description">description</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.find" href="../backupset.html#cvpysdk.backupset.Backupset.find">find</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.guid" href="../backupset.html#cvpysdk.backupset.Backupset.guid">guid</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_default_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.is_default_backupset">is_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_on_demand_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.is_on_demand_backupset">is_on_demand_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.list_media" href="../backupset.html#cvpysdk.backupset.Backupset.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.name" href="../backupset.html#cvpysdk.backupset.Backupset.name">name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.plan" href="../backupset.html#cvpysdk.backupset.Backupset.plan">plan</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.properties" href="../backupset.html#cvpysdk.backupset.Backupset.properties">properties</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.refresh" href="../backupset.html#cvpysdk.backupset.Backupset.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.set_default_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.set_default_backupset">set_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.update_properties" href="../backupset.html#cvpysdk.backupset.Backupset.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.backupsets" href="index.html">cvpysdk.backupsets</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset" href="#cvpysdk.backupsets.aadbackupset.AzureAdBackupset">AzureAdBackupset</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_browse_double_query" href="#cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_browse_double_query">azuread_browse_double_query</a></code></li>
<li><code><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_browse_obj_meta" href="#cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_browse_obj_meta">azuread_browse_obj_meta</a></code></li>
<li><code><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_browse_options_builder" href="#cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_browse_options_builder">azuread_browse_options_builder</a></code></li>
<li><code><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_get_metadata" href="#cvpysdk.backupsets.aadbackupset.AzureAdBackupset.azuread_get_metadata">azuread_get_metadata</a></code></li>
<li><code><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.browse" href="#cvpysdk.backupsets.aadbackupset.AzureAdBackupset.browse">browse</a></code></li>
<li><code><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.get_search_response" href="#cvpysdk.backupsets.aadbackupset.AzureAdBackupset.get_search_response">get_search_response</a></code></li>
<li><code><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.get_view_attribute_response" href="#cvpysdk.backupsets.aadbackupset.AzureAdBackupset.get_view_attribute_response">get_view_attribute_response</a></code></li>
<li><code><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset.view_attributes_url_builder" href="#cvpysdk.backupsets.aadbackupset.AzureAdBackupset.view_attributes_url_builder">view_attributes_url_builder</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>