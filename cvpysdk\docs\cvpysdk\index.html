<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk API documentation</title>
<meta name="description" content="CVPySDK (Developer SDK - Python) is a Python Package for Commvault Software …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title"> <code>cvpysdk</code></h1>
</header>
<section id="section-intro">
<p>CVPySDK (Developer SDK - Python) is a Python Package for Commvault Software.</p>
<p>CVPySDK uses Commvault REST API to perform operations on a Commcell.</p>
<p>CVPySDK is available on GitHub (<a href="https://github.com/Commvault/cvpysdk">https://github.com/Commvault/cvpysdk</a>).</p>
<p>CVPySDK is compatible with Python 3</p>
<p>CVPySDK requires the following Python packages to run:</p>
<pre><code>-   **requests**

-   **xmltodict**
</code></pre>
<p>And Commvault Software v11 SP7 or later release with WebConsole installed</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/__init__.py#L1-L39" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
CVPySDK (Developer SDK - Python) is a Python Package for Commvault Software.

CVPySDK uses Commvault REST API to perform operations on a Commcell.

CVPySDK is available on GitHub (https://github.com/Commvault/cvpysdk).

CVPySDK is compatible with Python 3

CVPySDK requires the following Python packages to run:

    -   **requests**

    -   **xmltodict**

And Commvault Software v11 SP7 or later release with WebConsole installed

&#34;&#34;&#34;

__author__ = &#39;Commvault Systems Inc.&#39;
__version__ = &#39;11.40&#39;</code></pre>
</details>
</section>
<section>
<h2 class="section-title" id="header-submodules">Sub-modules</h2>
<dl>
<dt><code class="name"><a title="cvpysdk.activate" href="activate.html">cvpysdk.activate</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing operations on Activate apps …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.activateapps" href="activateapps/index.html">cvpysdk.activateapps</a></code></dt>
<dd>
<div class="desc"><p>Initialize Activate Apps for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.activitycontrol" href="activitycontrol.html">cvpysdk.activitycontrol</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing activity control operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.agent" href="agent.html">cvpysdk.agent</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing agent specific operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.agents" href="agents/index.html">cvpysdk.agents</a></code></dt>
<dd>
<div class="desc"><p>Initialize the Agents for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.alert" href="alert.html">cvpysdk.alert</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing alert operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.array_management" href="array_management.html">cvpysdk.array_management</a></code></dt>
<dd>
<div class="desc"><p>File for performing IntelliSnap and Array Management operations on Commcell via REST API …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.backup_network_pairs" href="backup_network_pairs.html">cvpysdk.backup_network_pairs</a></code></dt>
<dd>
<div class="desc"><p>Class to perform all the Backup Network Pairs operations on commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.backupset" href="backupset.html">cvpysdk.backupset</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing backup set operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.backupsets" href="backupsets/index.html">cvpysdk.backupsets</a></code></dt>
<dd>
<div class="desc"><p>Initialize Backupsets for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.certificates" href="certificates.html">cvpysdk.certificates</a></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt><code class="name"><a title="cvpysdk.cleanroom" href="cleanroom/index.html">cvpysdk.cleanroom</a></code></dt>
<dd>
<div class="desc"><p>Initialize Cleanroom for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.client" href="client.html">cvpysdk.client</a></code></dt>
<dd>
<div class="desc"><p>File for performing client related operations on the Commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.clientgroup" href="clientgroup.html">cvpysdk.clientgroup</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing client group operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.clients" href="clients/index.html">cvpysdk.clients</a></code></dt>
<dd>
<div class="desc"><p>Initialize Clients for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.commcell" href="commcell.html">cvpysdk.commcell</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing operations on Commcell via REST API …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.commcell_migration" href="commcell_migration.html">cvpysdk.commcell_migration</a></code></dt>
<dd>
<div class="desc"><p>Class to perform all the CommCell Migration operations on commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.constants" href="constants.html">cvpysdk.constants</a></code></dt>
<dd>
<div class="desc"><p>Helper file to maintain all the constants used in the SDK …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.content_analyzer" href="content_analyzer.html">cvpysdk.content_analyzer</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing operations on content analyzers, and a single content analyzer client in the commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.credential_manager" href="credential_manager.html">cvpysdk.credential_manager</a></code></dt>
<dd>
<div class="desc"><p>Main file for managing credentials records on this commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.cvpysdk" href="cvpysdk.html">cvpysdk.cvpysdk</a></code></dt>
<dd>
<div class="desc"><p>Helper file for session operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.dashboard" href="dashboard/index.html">cvpysdk.dashboard</a></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt><code class="name"><a title="cvpysdk.datacube" href="datacube/index.html">cvpysdk.datacube</a></code></dt>
<dd>
<div class="desc"><p>Initialize Datacube APIs, and Classes for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.deduplication_engines" href="deduplication_engines.html">cvpysdk.deduplication_engines</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing deduplication engine related operations on the commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.deployment" href="deployment/index.html">cvpysdk.deployment</a></code></dt>
<dd>
<div class="desc"><p>Initialize Deployment for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.dev_test_group" href="dev_test_group.html">cvpysdk.dev_test_group</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing virtual lab restore operations on the Commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.disasterrecovery" href="disasterrecovery.html">cvpysdk.disasterrecovery</a></code></dt>
<dd>
<div class="desc"><p>main file for performing disaster recovery operations on commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.domains" href="domains.html">cvpysdk.domains</a></code></dt>
<dd>
<div class="desc"><p>File for performing domain related operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.download_center" href="download_center.html">cvpysdk.download_center</a></code></dt>
<dd>
<div class="desc"><p>File for doing operations on Download Center …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.drorchestration" href="drorchestration/index.html">cvpysdk.drorchestration</a></code></dt>
<dd>
<div class="desc"><p>Initialize Replication APIs, and Classes for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.eventviewer" href="eventviewer.html">cvpysdk.eventviewer</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing Event Viewer Operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.exception" href="exception.html">cvpysdk.exception</a></code></dt>
<dd>
<div class="desc"><p>File for handling all the exceptions for the CVPySDK python package …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.globalfilter" href="globalfilter.html">cvpysdk.globalfilter</a></code></dt>
<dd>
<div class="desc"><p>Main file for managing global filters for this commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.hac_clusters" href="hac_clusters.html">cvpysdk.hac_clusters</a></code></dt>
<dd>
<div class="desc"><p>File for performing hac cluster related operations on the commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.identity_management" href="identity_management.html">cvpysdk.identity_management</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing identity management operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.index_pools" href="index_pools.html">cvpysdk.index_pools</a></code></dt>
<dd>
<div class="desc"><p>File for performing index pool related operations on the commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.index_server" href="index_server.html">cvpysdk.index_server</a></code></dt>
<dd>
<div class="desc"><p>File for performing index server related operations on the commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.instance" href="instance.html">cvpysdk.instance</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing instance operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.instances" href="instances/index.html">cvpysdk.instances</a></code></dt>
<dd>
<div class="desc"><p>Initialize Instances for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.internetoptions" href="internetoptions.html">cvpysdk.internetoptions</a></code></dt>
<dd>
<div class="desc"><p>File for setting internet options
InternetOptions: class for setting Internet options in CommServe …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.job" href="job.html">cvpysdk.job</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing operations on a job …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.key_management_server" href="key_management_server.html">cvpysdk.key_management_server</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing Key Management Server operations on commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.license" href="license.html">cvpysdk.license</a></code></dt>
<dd>
<div class="desc"><p>File for License operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.metallic" href="metallic.html">cvpysdk.metallic</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing Metallic Integration steps with existing commcell
…</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.metricsreport" href="metricsreport.html">cvpysdk.metricsreport</a></code></dt>
<dd>
<div class="desc"><p>File for performing Metrics operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.monitoring" href="monitoring.html">cvpysdk.monitoring</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing Monitoring related operations on the commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.monitoringapps" href="monitoringapps/index.html">cvpysdk.monitoringapps</a></code></dt>
<dd>
<div class="desc"><p>Initialize Monitoring apps APIs, and Classes for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.name_change" href="name_change.html">cvpysdk.name_change</a></code></dt>
<dd>
<div class="desc"><p>Main file for doing Name Change operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.network" href="network.html">cvpysdk.network</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing network related operations on a client/client group …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.network_throttle" href="network_throttle.html">cvpysdk.network_throttle</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing network throttle related operations on a client/client group …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.network_topology" href="network_topology.html">cvpysdk.network_topology</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing network topology operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.operation_window" href="operation_window.html">cvpysdk.operation_window</a></code></dt>
<dd>
<div class="desc"><p>File for performing Operation Window related operations on given Commcell entity …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.organization" href="organization.html">cvpysdk.organization</a></code></dt>
<dd>
<div class="desc"><p>File for doing operations on an organization …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.plan" href="plan.html">cvpysdk.plan</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing plan operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.policies" href="policies/index.html">cvpysdk.policies</a></code></dt>
<dd>
<div class="desc"><p>Initialize Policies APIs, and Classes for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.policy" href="policy.html">cvpysdk.policy</a></code></dt>
<dd>
<div class="desc"><p>File for operating on all types of Policies associated with the Commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.recovery_targets" href="recovery_targets.html">cvpysdk.recovery_targets</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing Replication Target specific operations (Auto Recovery) …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.regions" href="regions.html">cvpysdk.regions</a></code></dt>
<dd>
<div class="desc"><p>File for associating Workload and Backup destination regions for various entites
class: Regions. Region …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.reports" href="reports/index.html">cvpysdk.reports</a></code></dt>
<dd>
<div class="desc"><p>Initialize Reports for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.resource_pool" href="resource_pool.html">cvpysdk.resource_pool</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing resource pool related operations on CS …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.schedules" href="schedules.html">cvpysdk.schedules</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing schedule related operations for client/agent/backupset/subclient …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.security" href="security/index.html">cvpysdk.security</a></code></dt>
<dd>
<div class="desc"><p>Initialize Security Objects for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.services" href="services.html">cvpysdk.services</a></code></dt>
<dd>
<div class="desc"><p>Service URLs for REST API operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.storage" href="storage.html">cvpysdk.storage</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing storage related operations on the commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.storage_pool" href="storage_pool.html">cvpysdk.storage_pool</a></code></dt>
<dd>
<div class="desc"><p>File for doing operations on an Storage Pools …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.subclient" href="subclient.html">cvpysdk.subclient</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing subclient operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.subclients" href="subclients/index.html">cvpysdk.subclients</a></code></dt>
<dd>
<div class="desc"><p>Initialize Subclients for the SDK.</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.system" href="system.html">cvpysdk.system</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing system related operations on Commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.tags" href="tags.html">cvpysdk.tags</a></code></dt>
<dd>
<div class="desc"><p>File for performing tags related operations …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.virtualmachinepolicies" href="virtualmachinepolicies.html">cvpysdk.virtualmachinepolicies</a></code></dt>
<dd>
<div class="desc"><p>Main file for performing virtual machine policy related operations on the Commcell …</p></div>
</dd>
<dt><code class="name"><a title="cvpysdk.workflow" href="workflow.html">cvpysdk.workflow</a></code></dt>
<dd>
<div class="desc"><p>File for performing Workflow related operations on Commcell …</p></div>
</dd>
</dl>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3><a href="#header-submodules">Sub-modules</a></h3>
<ul>
<li><code><a title="cvpysdk.activate" href="activate.html">cvpysdk.activate</a></code></li>
<li><code><a title="cvpysdk.activateapps" href="activateapps/index.html">cvpysdk.activateapps</a></code></li>
<li><code><a title="cvpysdk.activitycontrol" href="activitycontrol.html">cvpysdk.activitycontrol</a></code></li>
<li><code><a title="cvpysdk.agent" href="agent.html">cvpysdk.agent</a></code></li>
<li><code><a title="cvpysdk.agents" href="agents/index.html">cvpysdk.agents</a></code></li>
<li><code><a title="cvpysdk.alert" href="alert.html">cvpysdk.alert</a></code></li>
<li><code><a title="cvpysdk.array_management" href="array_management.html">cvpysdk.array_management</a></code></li>
<li><code><a title="cvpysdk.backup_network_pairs" href="backup_network_pairs.html">cvpysdk.backup_network_pairs</a></code></li>
<li><code><a title="cvpysdk.backupset" href="backupset.html">cvpysdk.backupset</a></code></li>
<li><code><a title="cvpysdk.backupsets" href="backupsets/index.html">cvpysdk.backupsets</a></code></li>
<li><code><a title="cvpysdk.certificates" href="certificates.html">cvpysdk.certificates</a></code></li>
<li><code><a title="cvpysdk.cleanroom" href="cleanroom/index.html">cvpysdk.cleanroom</a></code></li>
<li><code><a title="cvpysdk.client" href="client.html">cvpysdk.client</a></code></li>
<li><code><a title="cvpysdk.clientgroup" href="clientgroup.html">cvpysdk.clientgroup</a></code></li>
<li><code><a title="cvpysdk.clients" href="clients/index.html">cvpysdk.clients</a></code></li>
<li><code><a title="cvpysdk.commcell" href="commcell.html">cvpysdk.commcell</a></code></li>
<li><code><a title="cvpysdk.commcell_migration" href="commcell_migration.html">cvpysdk.commcell_migration</a></code></li>
<li><code><a title="cvpysdk.constants" href="constants.html">cvpysdk.constants</a></code></li>
<li><code><a title="cvpysdk.content_analyzer" href="content_analyzer.html">cvpysdk.content_analyzer</a></code></li>
<li><code><a title="cvpysdk.credential_manager" href="credential_manager.html">cvpysdk.credential_manager</a></code></li>
<li><code><a title="cvpysdk.cvpysdk" href="cvpysdk.html">cvpysdk.cvpysdk</a></code></li>
<li><code><a title="cvpysdk.dashboard" href="dashboard/index.html">cvpysdk.dashboard</a></code></li>
<li><code><a title="cvpysdk.datacube" href="datacube/index.html">cvpysdk.datacube</a></code></li>
<li><code><a title="cvpysdk.deduplication_engines" href="deduplication_engines.html">cvpysdk.deduplication_engines</a></code></li>
<li><code><a title="cvpysdk.deployment" href="deployment/index.html">cvpysdk.deployment</a></code></li>
<li><code><a title="cvpysdk.dev_test_group" href="dev_test_group.html">cvpysdk.dev_test_group</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery" href="disasterrecovery.html">cvpysdk.disasterrecovery</a></code></li>
<li><code><a title="cvpysdk.domains" href="domains.html">cvpysdk.domains</a></code></li>
<li><code><a title="cvpysdk.download_center" href="download_center.html">cvpysdk.download_center</a></code></li>
<li><code><a title="cvpysdk.drorchestration" href="drorchestration/index.html">cvpysdk.drorchestration</a></code></li>
<li><code><a title="cvpysdk.eventviewer" href="eventviewer.html">cvpysdk.eventviewer</a></code></li>
<li><code><a title="cvpysdk.exception" href="exception.html">cvpysdk.exception</a></code></li>
<li><code><a title="cvpysdk.globalfilter" href="globalfilter.html">cvpysdk.globalfilter</a></code></li>
<li><code><a title="cvpysdk.hac_clusters" href="hac_clusters.html">cvpysdk.hac_clusters</a></code></li>
<li><code><a title="cvpysdk.identity_management" href="identity_management.html">cvpysdk.identity_management</a></code></li>
<li><code><a title="cvpysdk.index_pools" href="index_pools.html">cvpysdk.index_pools</a></code></li>
<li><code><a title="cvpysdk.index_server" href="index_server.html">cvpysdk.index_server</a></code></li>
<li><code><a title="cvpysdk.instance" href="instance.html">cvpysdk.instance</a></code></li>
<li><code><a title="cvpysdk.instances" href="instances/index.html">cvpysdk.instances</a></code></li>
<li><code><a title="cvpysdk.internetoptions" href="internetoptions.html">cvpysdk.internetoptions</a></code></li>
<li><code><a title="cvpysdk.job" href="job.html">cvpysdk.job</a></code></li>
<li><code><a title="cvpysdk.key_management_server" href="key_management_server.html">cvpysdk.key_management_server</a></code></li>
<li><code><a title="cvpysdk.license" href="license.html">cvpysdk.license</a></code></li>
<li><code><a title="cvpysdk.metallic" href="metallic.html">cvpysdk.metallic</a></code></li>
<li><code><a title="cvpysdk.metricsreport" href="metricsreport.html">cvpysdk.metricsreport</a></code></li>
<li><code><a title="cvpysdk.monitoring" href="monitoring.html">cvpysdk.monitoring</a></code></li>
<li><code><a title="cvpysdk.monitoringapps" href="monitoringapps/index.html">cvpysdk.monitoringapps</a></code></li>
<li><code><a title="cvpysdk.name_change" href="name_change.html">cvpysdk.name_change</a></code></li>
<li><code><a title="cvpysdk.network" href="network.html">cvpysdk.network</a></code></li>
<li><code><a title="cvpysdk.network_throttle" href="network_throttle.html">cvpysdk.network_throttle</a></code></li>
<li><code><a title="cvpysdk.network_topology" href="network_topology.html">cvpysdk.network_topology</a></code></li>
<li><code><a title="cvpysdk.operation_window" href="operation_window.html">cvpysdk.operation_window</a></code></li>
<li><code><a title="cvpysdk.organization" href="organization.html">cvpysdk.organization</a></code></li>
<li><code><a title="cvpysdk.plan" href="plan.html">cvpysdk.plan</a></code></li>
<li><code><a title="cvpysdk.policies" href="policies/index.html">cvpysdk.policies</a></code></li>
<li><code><a title="cvpysdk.policy" href="policy.html">cvpysdk.policy</a></code></li>
<li><code><a title="cvpysdk.recovery_targets" href="recovery_targets.html">cvpysdk.recovery_targets</a></code></li>
<li><code><a title="cvpysdk.regions" href="regions.html">cvpysdk.regions</a></code></li>
<li><code><a title="cvpysdk.reports" href="reports/index.html">cvpysdk.reports</a></code></li>
<li><code><a title="cvpysdk.resource_pool" href="resource_pool.html">cvpysdk.resource_pool</a></code></li>
<li><code><a title="cvpysdk.schedules" href="schedules.html">cvpysdk.schedules</a></code></li>
<li><code><a title="cvpysdk.security" href="security/index.html">cvpysdk.security</a></code></li>
<li><code><a title="cvpysdk.services" href="services.html">cvpysdk.services</a></code></li>
<li><code><a title="cvpysdk.storage" href="storage.html">cvpysdk.storage</a></code></li>
<li><code><a title="cvpysdk.storage_pool" href="storage_pool.html">cvpysdk.storage_pool</a></code></li>
<li><code><a title="cvpysdk.subclient" href="subclient.html">cvpysdk.subclient</a></code></li>
<li><code><a title="cvpysdk.subclients" href="subclients/index.html">cvpysdk.subclients</a></code></li>
<li><code><a title="cvpysdk.system" href="system.html">cvpysdk.system</a></code></li>
<li><code><a title="cvpysdk.tags" href="tags.html">cvpysdk.tags</a></code></li>
<li><code><a title="cvpysdk.virtualmachinepolicies" href="virtualmachinepolicies.html">cvpysdk.virtualmachinepolicies</a></code></li>
<li><code><a title="cvpysdk.workflow" href="workflow.html">cvpysdk.workflow</a></code></li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>