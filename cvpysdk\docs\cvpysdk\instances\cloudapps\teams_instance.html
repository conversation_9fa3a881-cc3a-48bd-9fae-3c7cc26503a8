<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.cloudapps.teams_instance API documentation</title>
<meta name="description" content="File for operating on a Teams Instance.
TeamsInstance is the only class defined in this file …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.cloudapps.teams_instance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Teams Instance.
TeamsInstance is the only class defined in this file.</p>
<p>TeamsInstance: Derived class from CloudAppsInstance Base class, representing Office 365 Teams.</p>
<h2 id="teamsinstance">Teamsinstance</h2>
<p>_get_instance_properties()
&ndash;
Gets the properties of this machine.
_get_instance_properties_json() &ndash;
Returns the instance properties json.
discover()
&ndash;
Launches Discovery and returns the discovered teams.
_restore_json()
&ndash;
Returns JSON request to pass to API as per the options selected by the user.
_cloud_apps_restore_json()
&ndash;
Returns JSON for Cloud Apps related properties.
restore_out_of_place()
&ndash;
Restore a team to another location.
update_instance()
&ndash;
Update Instance properties.</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/teams_instance.py#L1-L347" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Teams Instance.
TeamsInstance is the only class defined in this file.

TeamsInstance: Derived class from CloudAppsInstance Base class, representing Office 365 Teams.

TeamsInstance:
    _get_instance_properties()      --  Gets the properties of this machine.
    _get_instance_properties_json() --  Returns the instance properties json.
    discover()                      --  Launches Discovery and returns the discovered teams.
    _restore_json()                 --  Returns JSON request to pass to API as per the options selected by the user.
    _cloud_apps_restore_json()      --  Returns JSON for Cloud Apps related properties.
    restore_out_of_place()          --  Restore a team to another location.
    update_instance()               --  Update Instance properties.

&#34;&#34;&#34;

from __future__ import unicode_literals

from ...exception import SDKException
from ..cainstance import CloudAppsInstance
from cvpysdk.job import Job

import time


class TeamsInstance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of Office 365 Teams.&#34;&#34;&#34;

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.
            Args:
                None

            Returns:
                None

            Raises:
                SDKException:
                    if response is empty.
                    if response is not success.
                    if access node is not configured.

        &#34;&#34;&#34;
        super(TeamsInstance, self)._get_instance_properties()

        if &#39;cloudAppsInstance&#39; in self._properties:
            cloud_apps_instance = self._properties[&#39;cloudAppsInstance&#39;]
            self._ca_instance_type = cloud_apps_instance[&#39;instanceType&#39;]

            if &#39;generalCloudProperties&#39; in cloud_apps_instance:
                if &#39;proxyServers&#39; in cloud_apps_instance[&#39;generalCloudProperties&#39;]:
                    self._proxy_client = cloud_apps_instance.get(
                        &#39;generalCloudProperties&#39;, {}).get(&#39;proxyServers&#39;, [{}])[0].get(&#39;clientName&#39;)
                else:
                    if &#39;clientName&#39; in cloud_apps_instance.get(
                            &#39;generalCloudProperties&#39;, {}).get(&#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;):
                        self._proxy_client = cloud_apps_instance.get(&#39;generalCloudProperties&#39;, {}).get(
                            &#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;, {}).get(&#39;clientName&#39;)
                    else:
                        self._proxy_client = cloud_apps_instance.get(&#39;generalCloudProperties&#39;, {}).get(
                            &#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;, {}).get(&#39;clientGroupName&#39;)

                if self._proxy_client is None:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Access Node has not been configured&#39;)

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;Returns the instance properties json.
            Returns:
                dict    --  Dictionary of the instance properties.

        &#34;&#34;&#34;

        return {&#39;instanceProperties&#39;: self._properties}

    def discover(self, discovery_type, refresh_cache=True):
        &#34;&#34;&#34;Launches Discovery and returns the discovered teams.
            Args:
                discovery_type (int) -- TYpe of the discovery Example: Teams 12, users
                refresh_cache   --  Refreshes Discover cache information.
                    default:    True

            Returns:
                dict    --  Returns dictionary with team email ID as key and team properties as value.

            Raises:
                SDKException:
                    If discovery failed to launch.
                    If response is empty.
                    If response is not success.

        &#34;&#34;&#34;

        DISCOVERY_TYPE = discovery_type
        max_retries = 5
        url = f&#34;{self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self.instance_id, self._agent_object._client_object.client_id, DISCOVERY_TYPE)}&amp;pageSize=0&#34;

        for retry in range(max_retries):

            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, f&#34;{url}&amp;refreshCache=1&#34; if refresh_cache else url)

            # NEED TO REFRESH CACHE ONLY THE FIRST TIME
            refresh_cache = False

            if flag:

                if response.json():
                    resp = response.json()
                    if &#39;userAccounts&#39; in resp:
                        self.discovered_users = {team[&#39;smtpAddress&#39;]: team for team in resp[&#39;userAccounts&#39;]}
                        return self.discovered_users
                    elif &#39;groups&#39; in resp:
                        self.discovered_users = {team[&#39;name&#39;]: team for team in resp[&#39;groups&#39;]}
                        return self.discovered_users
                    elif not resp:
                        return {}

                    # IF OUR RESPONSE IS EMPTY OR WE HAVE REACHED MAXIMUM NUMBER OF ATTEMPTS WITHOUT DESIRED RESPONSE
                    elif not resp or retry == max_retries-1:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)

                    time.sleep(30)
                    continue  # TO AVOID RAISING EXCEPTION

                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            if response.json():
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def _restore_json(self):
        &#34;&#34;&#34;Returns JSON request to pass to API as per the options selected by the user.

            Returns:
                dict - JSON request to pass to the API.
        &#34;&#34;&#34;

        request_json = super(TeamsInstance, self)._restore_json(restore_option=self._restore_association)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#34;clientGUID&#34;] = self._agent_object._client_object.client_guid
        return request_json

    def _cloud_apps_restore_json(self, source_team, destination_team):
        &#34;&#34;&#34;Returns JSON for Cloud Apps related properties.

            Args:
                source_team         (dict)   --  Dictionary of properties from discover() for team that is to be restored.
                destination_team    (dict)   --  Dictionary of properties from discover() of team to be restored to.

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;

        ca_json = {
            &#34;instanceType&#34;:  int(self._properties[&#39;cloudAppsInstance&#39;][&#39;instanceType&#39;]),
            &#34;msTeamsRestoreOptions&#34;: {
                &#34;restoreAllMatching&#34;: False,
                &#34;sourceTeamItemType&#34;: 1,
                &#34;overWriteItems&#34;: False,
                &#34;restoreToTeams&#34;: True,
                &#34;destLocation&#34;: destination_team[&#39;displayName&#39;],
                &#34;restoreUsingFindQuery&#34;: False,
                &#34;findQuery&#34;: {
                    &#34;mode&#34;: 4,
                    &#34;facetRequests&#34;: {},
                    &#34;advSearchGrp&#34;: {
                        &#34;commonFilter&#34;: [
                            {
                                &#34;filter&#34;: {
                                    &#34;interFilterOP&#34;: 2,
                                    &#34;filters&#34;: [
                                        {
                                            &#34;groupType&#34;: 0,
                                            &#34;field&#34;: &#34;CISTATE&#34;,
                                            &#34;intraFieldOp&#34;: 0,
                                            &#34;fieldValues&#34;: {
                                                &#34;values&#34;: [&#34;1&#34;]
                                            }
                                        }
                                    ]
                                }
                            }
                        ],
                        &#34;fileFilter&#34;: [
                            {
                                &#34;filter&#34;: {
                                    &#34;interFilterOP&#34;: 2,
                                    &#34;filters&#34;: [
                                        {
                                            &#34;field&#34;: &#34;CV_OBJECT_GUID&#34;,
                                            &#34;intraFieldOp&#34;: 0,
                                            &#34;fieldValues&#34;: {
                                                &#34;values&#34;: [source_team[&#39;user&#39;][&#39;userGUID&#39;].lower()]
                                            }
                                        }
                                    ]
                                }
                            }
                        ],
                        &#34;galaxyFilter&#34;: [
                            {
                                &#34;appIdList&#34;: [int(self._restore_association[&#39;subclientId&#39;])]
                            }
                        ]
                    },
                    &#34;searchProcessingInfo&#34;: {
                        &#34;resultOffset&#34;: 0,
                        &#34;pageSize&#34;: 1,
                        &#34;queryParams&#34;: [
                            {
                                &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;, &#34;value&#34;: &#34;true&#34;
                            },
                            {
                                &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                                &#34;value&#34;: &#34;DATA_TYPE,CONTENTID,CV_OBJECT_GUID,PARENT_GUID,CV_TURBO_GUID,AFILEID,&#34;
                                         &#34;AFILEOFFSET,COMMCELLNO,MODIFIEDTIME,SIZEINKB,BACKUPTIME,CISTATE,DATE_DELETED,&#34;
                                         &#34;TEAMS_ITEM_ID,TEAMS_ITEM_NAME,TEAMS_NAME,TEAMS_SMTP,TEAMS_ITEM_TYPE,&#34;
                                         &#34;TEAMS_CHANNEL_TYPE,TEAMS_TAB_TYPE,TEAMS_GROUP_VISIBILITY,TEAMS_GUID,&#34;
                                         &#34;TEAMS_CONV_ITEM_TYPE,TEAMS_CONV_MESSAGE_TYPE,TEAMS_CONV_SUBJECT,&#34;
                                         &#34;TEAMS_CONV_IMPORTANCE,TEAMS_CONV_SENDER_TYPE,TEAMS_CONV_SENDER_NAME,&#34;
                                         &#34;TEAMS_CONV_HAS_REPLIES,CI_URL&#34;}
                        ],
                        &#34;sortParams&#34;: [
                            {
                                &#34;sortDirection&#34;: 0,
                                &#34;sortField&#34;: &#34;SIZEINKB&#34;
                            }
                        ]
                    }
                },
                &#34;selectedItemsToRestsore&#34;: [
                    {
                        &#34;itemId&#34;: source_team[&#39;user&#39;][&#39;userGUID&#39;].lower(),
                        &#34;path&#34;: &#34;&#34;, &#34;itemType&#34;: 1,
                        &#34;isDirectory&#34;: True
                    }
                ],
                &#34;destinationTeamInfo&#34;: {
                    &#34;tabId&#34;: &#34;&#34;,
                    &#34;teamName&#34;: destination_team[&#39;displayName&#39;],
                    &#34;tabName&#34;: &#34;&#34;,
                    &#34;folder&#34;: &#34;&#34;,
                    &#34;teamId&#34;: destination_team[&#39;user&#39;][&#39;userGUID&#39;].lower(),
                    &#34;destination&#34;: 1,
                    &#34;channelName&#34;: &#34;&#34;,
                    &#34;channelId&#34;: &#34;&#34;
                }
            }
        }

        return ca_json

    def restore_out_of_place(self, source_team, destination_team):
        &#34;&#34;&#34;Restore a team to another location.

            Args:
                source_team         (str)   --  The email ID of the team that needs to be restored.
                destination_team    (str)   --  The email ID of the team to be restored to.

            Returns:
                obj   --  Instance of Restore job.

            Raises:
                SDKException:
                    If restore failed to run.
                    If response is empty.
                    If response is not success.

        &#34;&#34;&#34;

        discovered_teams = self.discover()
        source_team = discovered_teams[source_team]
        destination_team = discovered_teams[destination_team]

        request_json = self._restore_json()
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;inPlace&#39;] = False
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;] = self._cloud_apps_restore_json(source_team=source_team, destination_team=destination_team)

        url = self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, url, request_json)

        if flag:

            if response.json():

                if &#39;jobIds&#39; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, f&#34;Restore failed, error message : {error_message}&#34;)

                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def update_instance(self, request_json):
        &#34;&#34;&#34;Update Instance properties.

                    Args:
                        request_json        (dict)   --  Dict of instance properties.
                    Returns:
                        response of the request

                    Raises:
                        SDKException:
                            If update failed.
                            If response is empty.
                            If response is not success.

                &#34;&#34;&#34;

        url = self._services[&#39;INSTANCE_PROPERTIES&#39;] % (self.instance_id)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, url, request_json)
        if response.json():

            if &#39;processinginstructioninfo&#39; in response.json():
                return response.json()

            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, f&#34;Update failed, error message : {error_message}&#34;)

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.cloudapps.teams_instance.TeamsInstance"><code class="flex name class">
<span>class <span class="ident">TeamsInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of Office 365 Teams.</p>
<p>Initialise the instance object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/teams_instance.py#L44-L347" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TeamsInstance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of Office 365 Teams.&#34;&#34;&#34;

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.
            Args:
                None

            Returns:
                None

            Raises:
                SDKException:
                    if response is empty.
                    if response is not success.
                    if access node is not configured.

        &#34;&#34;&#34;
        super(TeamsInstance, self)._get_instance_properties()

        if &#39;cloudAppsInstance&#39; in self._properties:
            cloud_apps_instance = self._properties[&#39;cloudAppsInstance&#39;]
            self._ca_instance_type = cloud_apps_instance[&#39;instanceType&#39;]

            if &#39;generalCloudProperties&#39; in cloud_apps_instance:
                if &#39;proxyServers&#39; in cloud_apps_instance[&#39;generalCloudProperties&#39;]:
                    self._proxy_client = cloud_apps_instance.get(
                        &#39;generalCloudProperties&#39;, {}).get(&#39;proxyServers&#39;, [{}])[0].get(&#39;clientName&#39;)
                else:
                    if &#39;clientName&#39; in cloud_apps_instance.get(
                            &#39;generalCloudProperties&#39;, {}).get(&#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;):
                        self._proxy_client = cloud_apps_instance.get(&#39;generalCloudProperties&#39;, {}).get(
                            &#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;, {}).get(&#39;clientName&#39;)
                    else:
                        self._proxy_client = cloud_apps_instance.get(&#39;generalCloudProperties&#39;, {}).get(
                            &#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;, {}).get(&#39;clientGroupName&#39;)

                if self._proxy_client is None:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Access Node has not been configured&#39;)

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;Returns the instance properties json.
            Returns:
                dict    --  Dictionary of the instance properties.

        &#34;&#34;&#34;

        return {&#39;instanceProperties&#39;: self._properties}

    def discover(self, discovery_type, refresh_cache=True):
        &#34;&#34;&#34;Launches Discovery and returns the discovered teams.
            Args:
                discovery_type (int) -- TYpe of the discovery Example: Teams 12, users
                refresh_cache   --  Refreshes Discover cache information.
                    default:    True

            Returns:
                dict    --  Returns dictionary with team email ID as key and team properties as value.

            Raises:
                SDKException:
                    If discovery failed to launch.
                    If response is empty.
                    If response is not success.

        &#34;&#34;&#34;

        DISCOVERY_TYPE = discovery_type
        max_retries = 5
        url = f&#34;{self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self.instance_id, self._agent_object._client_object.client_id, DISCOVERY_TYPE)}&amp;pageSize=0&#34;

        for retry in range(max_retries):

            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, f&#34;{url}&amp;refreshCache=1&#34; if refresh_cache else url)

            # NEED TO REFRESH CACHE ONLY THE FIRST TIME
            refresh_cache = False

            if flag:

                if response.json():
                    resp = response.json()
                    if &#39;userAccounts&#39; in resp:
                        self.discovered_users = {team[&#39;smtpAddress&#39;]: team for team in resp[&#39;userAccounts&#39;]}
                        return self.discovered_users
                    elif &#39;groups&#39; in resp:
                        self.discovered_users = {team[&#39;name&#39;]: team for team in resp[&#39;groups&#39;]}
                        return self.discovered_users
                    elif not resp:
                        return {}

                    # IF OUR RESPONSE IS EMPTY OR WE HAVE REACHED MAXIMUM NUMBER OF ATTEMPTS WITHOUT DESIRED RESPONSE
                    elif not resp or retry == max_retries-1:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)

                    time.sleep(30)
                    continue  # TO AVOID RAISING EXCEPTION

                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            if response.json():
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def _restore_json(self):
        &#34;&#34;&#34;Returns JSON request to pass to API as per the options selected by the user.

            Returns:
                dict - JSON request to pass to the API.
        &#34;&#34;&#34;

        request_json = super(TeamsInstance, self)._restore_json(restore_option=self._restore_association)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#34;clientGUID&#34;] = self._agent_object._client_object.client_guid
        return request_json

    def _cloud_apps_restore_json(self, source_team, destination_team):
        &#34;&#34;&#34;Returns JSON for Cloud Apps related properties.

            Args:
                source_team         (dict)   --  Dictionary of properties from discover() for team that is to be restored.
                destination_team    (dict)   --  Dictionary of properties from discover() of team to be restored to.

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;

        ca_json = {
            &#34;instanceType&#34;:  int(self._properties[&#39;cloudAppsInstance&#39;][&#39;instanceType&#39;]),
            &#34;msTeamsRestoreOptions&#34;: {
                &#34;restoreAllMatching&#34;: False,
                &#34;sourceTeamItemType&#34;: 1,
                &#34;overWriteItems&#34;: False,
                &#34;restoreToTeams&#34;: True,
                &#34;destLocation&#34;: destination_team[&#39;displayName&#39;],
                &#34;restoreUsingFindQuery&#34;: False,
                &#34;findQuery&#34;: {
                    &#34;mode&#34;: 4,
                    &#34;facetRequests&#34;: {},
                    &#34;advSearchGrp&#34;: {
                        &#34;commonFilter&#34;: [
                            {
                                &#34;filter&#34;: {
                                    &#34;interFilterOP&#34;: 2,
                                    &#34;filters&#34;: [
                                        {
                                            &#34;groupType&#34;: 0,
                                            &#34;field&#34;: &#34;CISTATE&#34;,
                                            &#34;intraFieldOp&#34;: 0,
                                            &#34;fieldValues&#34;: {
                                                &#34;values&#34;: [&#34;1&#34;]
                                            }
                                        }
                                    ]
                                }
                            }
                        ],
                        &#34;fileFilter&#34;: [
                            {
                                &#34;filter&#34;: {
                                    &#34;interFilterOP&#34;: 2,
                                    &#34;filters&#34;: [
                                        {
                                            &#34;field&#34;: &#34;CV_OBJECT_GUID&#34;,
                                            &#34;intraFieldOp&#34;: 0,
                                            &#34;fieldValues&#34;: {
                                                &#34;values&#34;: [source_team[&#39;user&#39;][&#39;userGUID&#39;].lower()]
                                            }
                                        }
                                    ]
                                }
                            }
                        ],
                        &#34;galaxyFilter&#34;: [
                            {
                                &#34;appIdList&#34;: [int(self._restore_association[&#39;subclientId&#39;])]
                            }
                        ]
                    },
                    &#34;searchProcessingInfo&#34;: {
                        &#34;resultOffset&#34;: 0,
                        &#34;pageSize&#34;: 1,
                        &#34;queryParams&#34;: [
                            {
                                &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;, &#34;value&#34;: &#34;true&#34;
                            },
                            {
                                &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                                &#34;value&#34;: &#34;DATA_TYPE,CONTENTID,CV_OBJECT_GUID,PARENT_GUID,CV_TURBO_GUID,AFILEID,&#34;
                                         &#34;AFILEOFFSET,COMMCELLNO,MODIFIEDTIME,SIZEINKB,BACKUPTIME,CISTATE,DATE_DELETED,&#34;
                                         &#34;TEAMS_ITEM_ID,TEAMS_ITEM_NAME,TEAMS_NAME,TEAMS_SMTP,TEAMS_ITEM_TYPE,&#34;
                                         &#34;TEAMS_CHANNEL_TYPE,TEAMS_TAB_TYPE,TEAMS_GROUP_VISIBILITY,TEAMS_GUID,&#34;
                                         &#34;TEAMS_CONV_ITEM_TYPE,TEAMS_CONV_MESSAGE_TYPE,TEAMS_CONV_SUBJECT,&#34;
                                         &#34;TEAMS_CONV_IMPORTANCE,TEAMS_CONV_SENDER_TYPE,TEAMS_CONV_SENDER_NAME,&#34;
                                         &#34;TEAMS_CONV_HAS_REPLIES,CI_URL&#34;}
                        ],
                        &#34;sortParams&#34;: [
                            {
                                &#34;sortDirection&#34;: 0,
                                &#34;sortField&#34;: &#34;SIZEINKB&#34;
                            }
                        ]
                    }
                },
                &#34;selectedItemsToRestsore&#34;: [
                    {
                        &#34;itemId&#34;: source_team[&#39;user&#39;][&#39;userGUID&#39;].lower(),
                        &#34;path&#34;: &#34;&#34;, &#34;itemType&#34;: 1,
                        &#34;isDirectory&#34;: True
                    }
                ],
                &#34;destinationTeamInfo&#34;: {
                    &#34;tabId&#34;: &#34;&#34;,
                    &#34;teamName&#34;: destination_team[&#39;displayName&#39;],
                    &#34;tabName&#34;: &#34;&#34;,
                    &#34;folder&#34;: &#34;&#34;,
                    &#34;teamId&#34;: destination_team[&#39;user&#39;][&#39;userGUID&#39;].lower(),
                    &#34;destination&#34;: 1,
                    &#34;channelName&#34;: &#34;&#34;,
                    &#34;channelId&#34;: &#34;&#34;
                }
            }
        }

        return ca_json

    def restore_out_of_place(self, source_team, destination_team):
        &#34;&#34;&#34;Restore a team to another location.

            Args:
                source_team         (str)   --  The email ID of the team that needs to be restored.
                destination_team    (str)   --  The email ID of the team to be restored to.

            Returns:
                obj   --  Instance of Restore job.

            Raises:
                SDKException:
                    If restore failed to run.
                    If response is empty.
                    If response is not success.

        &#34;&#34;&#34;

        discovered_teams = self.discover()
        source_team = discovered_teams[source_team]
        destination_team = discovered_teams[destination_team]

        request_json = self._restore_json()
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;inPlace&#39;] = False
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;] = self._cloud_apps_restore_json(source_team=source_team, destination_team=destination_team)

        url = self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, url, request_json)

        if flag:

            if response.json():

                if &#39;jobIds&#39; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, f&#34;Restore failed, error message : {error_message}&#34;)

                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def update_instance(self, request_json):
        &#34;&#34;&#34;Update Instance properties.

                    Args:
                        request_json        (dict)   --  Dict of instance properties.
                    Returns:
                        response of the request

                    Raises:
                        SDKException:
                            If update failed.
                            If response is empty.
                            If response is not success.

                &#34;&#34;&#34;

        url = self._services[&#39;INSTANCE_PROPERTIES&#39;] % (self.instance_id)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, url, request_json)
        if response.json():

            if &#39;processinginstructioninfo&#39; in response.json():
                return response.json()

            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, f&#34;Update failed, error message : {error_message}&#34;)

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.cloudapps.teams_instance.TeamsInstance.discover"><code class="name flex">
<span>def <span class="ident">discover</span></span>(<span>self, discovery_type, refresh_cache=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Launches Discovery and returns the discovered teams.</p>
<h2 id="args">Args</h2>
<p>discovery_type (int) &ndash; TYpe of the discovery Example: Teams 12, users
refresh_cache
&ndash;
Refreshes Discover cache information.
default:
True</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash;
Returns dictionary with team email ID as key and team properties as value.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
If discovery failed to launch.
If response is empty.
If response is not success.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/teams_instance.py#L93-L148" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def discover(self, discovery_type, refresh_cache=True):
    &#34;&#34;&#34;Launches Discovery and returns the discovered teams.
        Args:
            discovery_type (int) -- TYpe of the discovery Example: Teams 12, users
            refresh_cache   --  Refreshes Discover cache information.
                default:    True

        Returns:
            dict    --  Returns dictionary with team email ID as key and team properties as value.

        Raises:
            SDKException:
                If discovery failed to launch.
                If response is empty.
                If response is not success.

    &#34;&#34;&#34;

    DISCOVERY_TYPE = discovery_type
    max_retries = 5
    url = f&#34;{self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self.instance_id, self._agent_object._client_object.client_id, DISCOVERY_TYPE)}&amp;pageSize=0&#34;

    for retry in range(max_retries):

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, f&#34;{url}&amp;refreshCache=1&#34; if refresh_cache else url)

        # NEED TO REFRESH CACHE ONLY THE FIRST TIME
        refresh_cache = False

        if flag:

            if response.json():
                resp = response.json()
                if &#39;userAccounts&#39; in resp:
                    self.discovered_users = {team[&#39;smtpAddress&#39;]: team for team in resp[&#39;userAccounts&#39;]}
                    return self.discovered_users
                elif &#39;groups&#39; in resp:
                    self.discovered_users = {team[&#39;name&#39;]: team for team in resp[&#39;groups&#39;]}
                    return self.discovered_users
                elif not resp:
                    return {}

                # IF OUR RESPONSE IS EMPTY OR WE HAVE REACHED MAXIMUM NUMBER OF ATTEMPTS WITHOUT DESIRED RESPONSE
                elif not resp or retry == max_retries-1:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)

                time.sleep(30)
                continue  # TO AVOID RAISING EXCEPTION

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        if response.json():
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        raise SDKException(&#39;Response&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.teams_instance.TeamsInstance.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, source_team, destination_team)</span>
</code></dt>
<dd>
<div class="desc"><p>Restore a team to another location.</p>
<h2 id="args">Args</h2>
<p>source_team
(str)
&ndash;
The email ID of the team that needs to be restored.
destination_team
(str)
&ndash;
The email ID of the team to be restored to.</p>
<h2 id="returns">Returns</h2>
<p>obj
&ndash;
Instance of Restore job.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
If restore failed to run.
If response is empty.
If response is not success.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/teams_instance.py#L271-L315" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(self, source_team, destination_team):
    &#34;&#34;&#34;Restore a team to another location.

        Args:
            source_team         (str)   --  The email ID of the team that needs to be restored.
            destination_team    (str)   --  The email ID of the team to be restored to.

        Returns:
            obj   --  Instance of Restore job.

        Raises:
            SDKException:
                If restore failed to run.
                If response is empty.
                If response is not success.

    &#34;&#34;&#34;

    discovered_teams = self.discover()
    source_team = discovered_teams[source_team]
    destination_team = discovered_teams[destination_team]

    request_json = self._restore_json()
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;inPlace&#39;] = False
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;] = self._cloud_apps_restore_json(source_team=source_team, destination_team=destination_team)

    url = self._services[&#39;CREATE_TASK&#39;]

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, url, request_json)

    if flag:

        if response.json():

            if &#39;jobIds&#39; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, f&#34;Restore failed, error message : {error_message}&#34;)

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.teams_instance.TeamsInstance.update_instance"><code class="name flex">
<span>def <span class="ident">update_instance</span></span>(<span>self, request_json)</span>
</code></dt>
<dd>
<div class="desc"><p>Update Instance properties.</p>
<h2 id="args">Args</h2>
<p>request_json
(dict)
&ndash;
Dict of instance properties.</p>
<h2 id="returns">Returns</h2>
<p>response of the request</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
If update failed.
If response is empty.
If response is not success.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/teams_instance.py#L317-L347" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_instance(self, request_json):
    &#34;&#34;&#34;Update Instance properties.

                Args:
                    request_json        (dict)   --  Dict of instance properties.
                Returns:
                    response of the request

                Raises:
                    SDKException:
                        If update failed.
                        If response is empty.
                        If response is not success.

            &#34;&#34;&#34;

    url = self._services[&#39;INSTANCE_PROPERTIES&#39;] % (self.instance_id)
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, url, request_json)
    if response.json():

        if &#39;processinginstructioninfo&#39; in response.json():
            return response.json()

        elif &#34;errorCode&#34; in response.json():
            error_message = response.json()[&#39;errorMessage&#39;]
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, f&#34;Update failed, error message : {error_message}&#34;)

        raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    response_string = self._commcell_object._update_response_(response.text)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.browse" href="../../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances.cloudapps" href="index.html">cvpysdk.instances.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.cloudapps.teams_instance.TeamsInstance" href="#cvpysdk.instances.cloudapps.teams_instance.TeamsInstance">TeamsInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.cloudapps.teams_instance.TeamsInstance.discover" href="#cvpysdk.instances.cloudapps.teams_instance.TeamsInstance.discover">discover</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.teams_instance.TeamsInstance.restore_out_of_place" href="#cvpysdk.instances.cloudapps.teams_instance.TeamsInstance.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.teams_instance.TeamsInstance.update_instance" href="#cvpysdk.instances.cloudapps.teams_instance.TeamsInstance.update_instance">update_instance</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>