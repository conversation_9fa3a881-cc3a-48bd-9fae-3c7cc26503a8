<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.exchsubclient API documentation</title>
<meta name="description" content="File for operating on an Exchange Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.exchsubclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on an Exchange Subclient.</p>
<p>ExchangeSubclient is the only class defined in this file.</p>
<p>ExchangeSubclient:
Derived class from Subclient Base class,
representing an Exchange Mailbox
Agent subclient, and to perform operations on that subclient.</p>
<h2 id="exchangesubclient">Exchangesubclient</h2>
<p><strong>new</strong>()
&ndash;
Method to create object based on the backupset name</p>
<p>_prepare_attachment_json() &ndash; Method to prepare the requestJSON for attachment restore</p>
<p>_get_attachment() &ndash; Method to get the content of the attachment</p>
<p>_get_attachment_preview() &ndash; Method to get the preview content of all the attachments in the attachment list</p>
<p>preview_backedup_file &ndash; Method to get the preview content of the mail</p>
<p>_get_ad_group_backup_task_json &ndash; Get AD Group back task json</p>
<p>ad_group_backup &ndash; Run AD Group backup</p>
<p>_get_ad_group_backup_subtask_json &ndash; Gets the subtask json for ad group backup</p>
<p>_get_ad_group_backup_options_json &ndash; Gets the option json for ad group backup</p>
<p>_get_ad_group_backup_backupoptions_json &ndash; Gets the backup option json for ad group backup</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L1-L1436" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on an Exchange Subclient.

ExchangeSubclient is the only class defined in this file.

ExchangeSubclient:  Derived class from Subclient Base class,  representing an Exchange Mailbox
Agent subclient, and to perform operations on that subclient.

ExchangeSubclient:

    __new__()   --  Method to create object based on the backupset name

    _prepare_attachment_json() -- Method to prepare the requestJSON for attachment restore

    _get_attachment() -- Method to get the content of the attachment

    _get_attachment_preview() -- Method to get the preview content of all the attachments in the attachment list

    preview_backedup_file -- Method to get the preview content of the mail

    _get_ad_group_backup_task_json -- Get AD Group back task json

    ad_group_backup -- Run AD Group backup

    _get_ad_group_backup_subtask_json -- Gets the subtask json for ad group backup

    _get_ad_group_backup_options_json -- Gets the option json for ad group backup

    _get_ad_group_backup_backupoptions_json -- Gets the backup option json for ad group backup


&#34;&#34;&#34;

from __future__ import unicode_literals
import re
from ..client import Client
from ..subclient import Subclient
from ..exception import SDKException
from .exchange.constants import JobOptionKeys, JobOptionValues, JobOptionIntegers, ExchangeConstants



class ExchangeSubclient(Subclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing an Exchange subclient,
        and to perform operations on that subclient.
    &#34;&#34;&#34;

    def __new__(cls, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Decides which subclient object needs to be created&#34;&#34;&#34;
        from .exchange.usermailbox_subclient import UsermailboxSubclient
        from .exchange.journalmailbox_subclient import JournalMailboxSubclient
        from .exchange.contentstoremailbox_subclient import ContentStoreMailboxSubclient
        backupset_types = {
            &#34;user mailbox&#34;: UsermailboxSubclient,
            &#34;journal mailbox&#34;: JournalMailboxSubclient,
            &#34;contentstore mailbox&#34;: ContentStoreMailboxSubclient
        }

        backupset_name = backupset_object.backupset_name

        if backupset_name in backupset_types:
            subclient_type = backupset_types[backupset_name]
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient for this instance type is not yet implemented&#39;
            )

        return object.__new__(subclient_type)

    @staticmethod
    def _get_client_dict(client_object):
        &#34;&#34;&#34;Returns the client dict for the client object to be appended to member server.

            Args:
                client_object   (object)    --  instance of the Client class

            Returns:
                dict    -   dictionary for a single client to be associated
        &#34;&#34;&#34;
        client_dict = {
            &#34;client&#34;: {
                &#34;clientName&#34;: client_object.client_name,
                &#34;clientId&#34;: int(client_object.client_id),
                &#34;_type_&#34;: 3
            }
        }

        return client_dict

    def _member_servers(self, clients_list):
        &#34;&#34;&#34;Returns the proxy clients to be associated .

            Args:
                clients_list (list)    --  list of the clients to associated

            Returns:
                list - list consisting of all member servers to be associated

            Raises:
                SDKException:
                    if type of clients list argument is not list

        &#34;&#34;&#34;
        if not isinstance(clients_list, list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        member_servers = []

        for client in clients_list:
            if isinstance(client, str):
                client = client.strip().lower()

                if self._commcell_object.clients.has_client(client):
                    temp_client = self._commcell_object.clients.get(client)

                    if temp_client.agents.has_agent(&#39;exchange mailbox (classic)&#39;):
                        client_dict = self._get_client_dict(temp_client)
                        member_servers.append(client_dict)

                    del temp_client
            elif isinstance(client, Client):
                if client.agents.has_agent(&#39;exchange mailbox (classic)&#39;):
                    client_dict = self._get_client_dict(client)
                    member_servers.append(client_dict)

        return member_servers

    def _content_indexing_option_json(self):
        &#34;&#34;&#34;Getter for  the content indexing options of ContentIndexing JSON&#34;&#34;&#34;

        self._content_indexing_option_json_ = {
            &#34;reanalyze&#34;: False,
            &#34;selectInactiveMailboxes&#34;: False,
            &#34;fileAnalytics&#34;: False,
            &#34;subClientBasedAnalytics&#34;: False
        }

    def _media_option_json(self, value):
        &#34;&#34;&#34;Setter for  the media options of ContentIndexing JSON

            Args:
                value   (dict)  --  media option need to be included

            Returns:
                (dict)  -       generated media options JSON

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._media_option_json_ = {
            &#34;pickFailedItems&#34;: value.get(&#34;pick_failed_items&#34;),
            &#34;pickFailedItemsOnly&#34;: value.get(&#34;pick_only_failed_items&#34;),
            &#34;auxcopyJobOption&#34;: {
                &#34;maxNumberOfStreams&#34;: value.get(&#34;streams&#34;),
                &#34;allCopies&#34;: True,
                &#34;useMaximumStreams&#34;: False,
                &#34;proxies&#34;: value.get(&#34;proxies&#34;)
            }
        }

    def _json_backupset(self):
        &#34;&#34;&#34;Getter for the Exchange Mailbox backupset option in restore json&#34;&#34;&#34;

        self._exchange_backupset_json = {
            &#34;clientName&#34;: self._client_object.client_name,
            &#34;backupsetName&#34;: self._backupset_object.backupset_name
        }

    def _json_restore_exchange_restore_option(self, value):
        &#34;&#34;&#34;
            setter for  the Exchange Mailbox in place restore  option in restore json

            Args:
                value   (dict)  --  restore option need to be included

            Returns:
                (dict)  -       generated exchange restore options JSON
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._exchange_option_restore_json = {
            &#34;exchangeRestoreChoice&#34;: 1,
            &#34;exchangeRestoreDrive&#34;: 1,
            &#34;isJournalReport&#34;: value.get(&#34;journal_report&#34;, False),
            &#34;pstFilePath&#34;: &#34;&#34;,
            &#34;targetMailBox&#34;: value.get(&#34;target_mailbox&#34;,None)
        }

    def _json_restore_exchange_common_option(self, value):
        &#34;&#34;&#34;
           Prepare exchange mailbox in place restore common options in restore json

            Args:
                value   (dict)  --  restore common options need to be included

            Returns:
                (dict)  -       generated exchange restore common options JSON
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._exchange_common_option_restore_json = {
            &#34;clusterDBBackedup&#34;: False,
            &#34;restoreToDisk&#34;: False,
            &#34;restoreDataInsteadOfStub&#34;: True,
            &#34;offlineMiningRestore&#34;: False,
            &#34;browse&#34;: True,
            &#34;skip&#34;: False,
            &#34;restoreOnlyStubExists&#34;: False,
            &#34;truncateBody&#34;: value.get(&#34;truncate_body&#34;, False),
            &#34;restoreAsStubs&#34;: value.get(&#34;restore_as_stubs&#34;, False),
            &#34;copyToObjectStore&#34;: False,
            &#34;onePassRestore&#34;: False,
            &#34;collectMsgsLargerThan&#34;: value.get(&#34;collect_msgs_larger_than&#34;, 1024),
            &#34;collectMsgsDaysAfter&#34;: value.get(&#34;collect_msgs_days_after&#34;, 30),
            &#34;unconditionalOverwrite&#34;: True,
            &#34;syncRestore&#34;: False,
            &#34;leaveMessageBody&#34;: value.get(&#34;leave_message_body&#34;, False),
            &#34;collectMsgWithAttach&#34;: value.get(&#34;collect_msg_with_attach&#34;, False),
            &#34;truncateBodyToBytes&#34;: value.get(&#34;truncate_body_to_bytes&#34;, 0),
            &#34;recoverToRecoveredItemsFolder&#34;: False,
            &#34;append&#34;: False
        }

        return self._exchange_common_option_restore_json

    def _json_out_of_place_destination_option(self, value):
        &#34;&#34;&#34;setter for  the Exchange Mailbox out of place restore
        option in restore json

            Args:
                value   (dict)  --  restore option need to be included

            Returns:
                (dict)  -       generated exchange restore options JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._out_of_place_destination_json = {
            &#34;inPlace&#34;: False,
            &#34;destPath&#34;: [value.get(&#34;destination_path&#34;)],
            &#34;destClient&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;clientName&#34;: self._client_object.client_name
            },
        }

    def _json_job_option_items(self, value):
        &#34;&#34;&#34;
        Generates JSON for job options.
        Args:
            value (dict): Dictionary containing job options.
        Returns:
            dict: JSON representation of job options.
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        additional_options = []
        message_exist = value.get(&#34;if_message_exists&#34;, JobOptionValues.SKIP.value)
        stub_rehydration = value.get(&#39;stub_rehydration&#39;, JobOptionValues.DISABLED.value)
        include_deleted_items = value.get(&#39;include_deleted_items&#39;, JobOptionValues.DISABLED.value)
        match_destination_user = value.get(&#39;match_destination_user&#39;, JobOptionValues.DISABLED.value)
        stub_rehydration_option = value.get(&#39;stub_rehydration_option&#39;,
                                            JobOptionValues.RECOVER_STUBS.value)
        email_level_reporting = value.get(&#39;email_level_reporting&#39;, JobOptionValues.DISABLED.value)
        old_link = value.get(&#39;old_recall_link&#39;, None)
        new_link = value.get(&#39;new_recall_link&#39;, None)

        self._job_option_items_json = [
            {&#34;option&#34;: JobOptionKeys.RESTORE_DESTINATION.value, &#34;value&#34;: JobOptionValues.EXCHANGE.value},
            {&#34;option&#34;: JobOptionKeys.DESTINATION.value, &#34;value&#34;: JobOptionValues.ORIGINAL_LOCATION.value},
            {&#34;option&#34;: JobOptionKeys.IF_MESSAGE_EXISTS.value, &#34;value&#34;: message_exist},
            {&#34;option&#34;: JobOptionKeys.INCLUDE_DELETED_ITEMS.value, &#34;value&#34;: include_deleted_items},
            {&#34;option&#34;: JobOptionKeys.MATCH_DESTINATION_USER.value, &#34;value&#34;: match_destination_user},
            {&#34;option&#34;: JobOptionKeys.STUB_REHYDRATION.value, &#34;value&#34;: stub_rehydration},
        ]

        if stub_rehydration != JobOptionValues.DISABLED.value:
            if stub_rehydration_option == JobOptionValues.RECOVER_STUBS.value:
                additional_options = []
            elif stub_rehydration_option == JobOptionValues.STUB_REPORTING.value:
                additional_options = [
                    {&#34;option&#34;: JobOptionKeys.MAILBOX_LEVEL_REPORTING.value, &#34;value&#34;: JobOptionValues.ENABLED.value},
                    {&#34;option&#34;: JobOptionKeys.EMAIL_LEVEL_REPORTING.value, &#34;value&#34;: email_level_reporting},
                ]
            elif stub_rehydration_option == JobOptionValues.UPDATE_RECALL_LINK.value:
                additional_options = [
                    {&#34;option&#34;: JobOptionKeys.OLD_RECALL_LINK.value, &#34;value&#34;: old_link},
                    {&#34;option&#34;: JobOptionKeys.NEW_RECALL_LINK.value, &#34;value&#34;: new_link},
                ]

        if additional_options:
            self._job_option_items_json.extend(additional_options)

        self._job_option_items_json.append({
            &#34;option&#34;: JobOptionKeys.STUB_REHYDRATION_OPTION.value,
            &#34;value&#34;: stub_rehydration_option
        })

        return self._job_option_items_json

    def _exchange_option_restore_json_rehydration(self, value):
        &#34;&#34;&#34;
        Generates JSON for Exchange restore rehydration options.
        Args:
            value (dict): Dictionary containing rehydration options.
        Returns:
            dict: JSON representation of rehydration options.
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        mapping = {
            JobOptionValues.RECOVER_STUBS.value: JobOptionIntegers.RECOVER_STUBS.value,
            JobOptionValues.STUB_REPORTING.value: JobOptionIntegers.STUB_REPORTING.value,
            JobOptionValues.UPDATE_RECALL_LINK.value: JobOptionIntegers.UPDATE_RECALL_LINK.value
        }

        stub_rehydration_option = mapping.get(
            value.get(&#39;stub_rehydration_option&#39;, JobOptionValues.RECOVER_STUBS.value)
        )

        email_level_reporting = value.get(&#39;email_level_reporting&#39;, JobOptionValues.DISABLED.value)
        old_link = value.get(&#39;old_recall_link&#39;, None)
        new_link = value.get(&#39;new_recall_link&#39;, None)

        base = {
            JobOptionKeys.STUB_REHYDRATION.value: True,
            JobOptionKeys.STUB_REHYDRATION_OPTION.value: stub_rehydration_option
        }

        additional_options = {}
        if stub_rehydration_option == JobOptionIntegers.RECOVER_STUBS.value:
            additional_options = {}
        elif stub_rehydration_option == JobOptionIntegers.STUB_REPORTING.value:
            additional_options = {
                &#34;stubReportOption&#34;: {
                    &#34;messageLevelReport&#34;: email_level_reporting == JobOptionValues.ENABLED.value,
                    &#34;mailboxLevelReport&#34;: True
                }
            }
        elif stub_rehydration_option == JobOptionIntegers.UPDATE_RECALL_LINK.value:
            additional_options = {
                &#34;stubOldRecallLink&#34;: old_link,
                &#34;stubRecallLink&#34;: new_link
            }

        base.update(additional_options)

        self._json_exchange_options = {
            JobOptionKeys.EXCHANGE_RESTORE_CHOICE.value: JobOptionIntegers.EXCHANGE_RESTORE_CHOICE.value,
            JobOptionKeys.EXCHANGE_RESTORE_DRIVE.value: JobOptionIntegers.EXCHANGE_RESTORE_DRIVE.value,
            JobOptionKeys.IS_JOURNAL_REPORT.value: value.get(&#34;journal_report&#34;, False),
            JobOptionKeys.PST_FILE_PATH.value: &#34;&#34;,
            JobOptionKeys.TARGET_MAILBOX.value: value.get(&#34;target_mailbox&#34;, None),
            JobOptionKeys.STUB_REHYDRATION.value: base
        }

        return self._json_exchange_options

    def _browse_filter_xml(self, value):
        &#34;&#34;&#34;
        Generates an XML query for browsing with a filter.
        Args:
            value (dict): Dictionary containing the filter value with the key &#39;keyword&#39;.
        Returns:
            str: XML query string for the filter.
        &#34;&#34;&#34;
        xml_query = f&#34;&#34;&#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;
        &lt;databrowse_Query type=&#34;0&#34; queryId=&#34;0&#34; isFacet=&#34;1&#34;&gt;
            &lt;whereClause connector=&#34;0&#34;&gt;
                &lt;criteria field=&#34;29&#34;&gt;
                    &lt;values val=&#34;{value[&#34;keyword&#34;]}&#34;/&gt;
                &lt;/criteria&gt;
            &lt;/whereClause&gt;
        &lt;/databrowse_Query&gt;&#34;&#34;&#34;
        return xml_query

    def _request_json_search_in_restore(self, filters):
        &#34;&#34;&#34;
        Generates a JSON request for searching within a restore operation.
        Args:
            filters (dict): Dictionary containing the search filter parameters.
                               Expected keys are &#39;keyword&#39; and &#39;appID&#39;.
        Returns:
            dict: JSON request for the search operation.
        &#34;&#34;&#34;
        req_json = ExchangeConstants.SEARCH_IN_RESTORE_PAYLOAD
        req_json[&#34;advSearchGrp&#34;][&#34;cvSearchKeyword&#34;][&#34;keyword&#34;] = filters.get(&#34;keyword&#34;, &#34;&#34;)
        req_json[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;appIdList&#34;] = filters.get(&#34;appID&#34;, [])
        return req_json

    def _json_disk_restore_exchange_restore_option(self, value):
        &#34;&#34;&#34;Setter for  the Exchange Mailbox Disk restore option
        in restore json

            Args:
                value   (dict)  --  restore option need to be included

            Returns:
                (dict)  -       generated exchange restore options JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._exchange_disk_option_restore_json = {
            &#34;exchangeRestoreChoice&#34;: 3,
            &#34;exchangeRestoreDrive&#34;: 1,
            &#34;diskFilePath&#34;: value.get(&#34;destination_path&#34;),
            &#34;isJournalReport&#34;: value.get(&#34;journal_report&#34;, False),
            &#34;pstFilePath&#34;: &#34;&#34;
        }

    def _json_pst_restore_exchange_restore_option(self, value):
        &#34;&#34;&#34;Setter for  the Exchange Mailbox PST restore option in restore json
            Args:
                value   (dict)  --  restore option need to be included

            Returns:
                (dict)  -       generated exchange restore options JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._exchange_pst_option_restore_json = {
            &#34;exchangeRestoreChoice&#34;: 2,
            &#34;exchangeRestoreDrive&#34;: 1,
            &#34;isJournalReport&#34;: value.get(&#34;journal_report&#34;, False),
            &#34;pstFilePath&#34;: value.get(&#34;destination_path&#34;),
            &#34;pstRestoreOption&#34;: value.get(&#34;limit_pst_size&#34;, 0),
            &#34;pstSize&#34;: value.get(&#34;pst_size&#34;, 2048)
        }

    @property
    def _json_content_indexing_subtasks(self):
        &#34;&#34;&#34;Getter for the contentindexing subtask in restore JSON.
        It is read only attribute&#34;&#34;&#34;

        _subtask_restore_json = {
            &#34;subTaskType&#34;: 1,
            &#34;operationType&#34;: 5020
        }

        return _subtask_restore_json

    def _prepare_pst_restore_json(self, _pst_restore_option=None):
        &#34;&#34;&#34;
        Prepare PST retsore Json with all getters

        Args:
            _pst_restore_option - dictionary with all PST restore options

            value:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  PST path where the mailboxes needs to be
                                                    restored
                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False

        returns:
            request_json   -- complete json for performing PST Restore options
        &#34;&#34;&#34;

        if _pst_restore_option is None:
            _pst_restore_option = {}

        paths = self._filter_paths(_pst_restore_option[&#39;paths&#39;])
        self._json_pst_restore_exchange_restore_option(_pst_restore_option)
        self._json_backupset()

        _pst_restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_pst_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;exchangeOption&#39;] = self._exchange_pst_option_restore_json

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        return request_json

    def _prepare_disk_restore_json(self, _disk_restore_option):
        &#34;&#34;&#34;
        Prepare disk retsore Json with all getters

        Args:
            _disk_restore_option - dictionary with all disk restore options

            value:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  path where the mailboxes needs to be restored
                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False


        returns:
            request_json        -complete json for performing disk Restore options
        &#34;&#34;&#34;

        if _disk_restore_option is None:
            _disk_restore_option = {}

        paths = self._filter_paths(_disk_restore_option[&#39;paths&#39;])
        self._json_disk_restore_exchange_restore_option(_disk_restore_option)
        self._json_backupset()
        _disk_restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_disk_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;exchangeOption&#39;] = self._exchange_disk_option_restore_json

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        return request_json

    def _prepare_out_of_place_restore_json(self, _restore_option):
        &#34;&#34;&#34;
        Prepare out of place retsore Json with all getters

        Args:
            _restore_option - dictionary with all out of place restore options

            value:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  path where the mailboxes needs to be restored
                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False


        returns:
            request_json        -  complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _restore_option is None:
            _restore_option = {}

        paths = self._filter_paths(_restore_option[&#39;paths&#39;])
        self._json_restore_exchange_restore_option(_restore_option)
        self._json_out_of_place_destination_option(_restore_option)
        self._json_backupset()
        _restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;exchangeOption&#39;] = self._exchange_option_restore_json

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;destination&#39;] = self._out_of_place_destination_json

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        return request_json

    def _cleanup_json(self):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.
            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._json_backup_subtasks,
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;backupLevel&#34;: 15
                            }
                        }
                    }
                ]
            }
        }

        return request_json

    def cleanup(self):
        &#34;&#34;&#34;Runs a cleanup job for the subclient .

            Returns:
                object - instance of the Job class for this backup job

        &#34;&#34;&#34;

        request_json = self._cleanup_json()
        return self._process_restore_response(request_json)

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            journal_report=False,
            restore_as_stub=None,
            recovery_point_id=None,
            **kwargs):
        &#34;&#34;&#34;Restores the mailboxes/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                        contentStore Mailbox
                    default: False

                restore_as_stub         (dict)  --  setters for common options

                recovery_point_id       (int)   --  ID of the recovery point to which the mailbox is to be restored to
                    Default: None

                **kwargs:
                Expected:
                stub_rehydration        (dict)  --  stub rules to rehydrate items during restore
                    Default: None

                filters                  (dict)  --  filter values for find and restore
                    Default: None

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        restore_option = {}
        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;journal_report&#39;] = journal_report

        paths = self._filter_paths(paths)
        self._json_restore_exchange_restore_option(restore_option)
        self._json_backupset()
        restore_option[&#39;unconditional_overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths

        request_json = self._restore_json(restore_option=restore_option)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = self.subclient_name
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][
            &#39;backupsetName&#39;] = self._backupset_object.backupset_name
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][&#39;exchangeOption&#39;] = self._exchange_option_restore_json
        if restore_as_stub:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;commonOptions&#39;] = self._json_restore_exchange_common_option(restore_as_stub)

        stub_rehydration = kwargs.get(&#34;stub_rehydration&#34;)
        if stub_rehydration:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;commonOpts&#39;][
                &#39;jobOptionItems&#39;] = self._json_job_option_items(stub_rehydration)
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
                &#39;options&#39;][&#39;restoreOptions&#39;][&#39;exchangeOption&#39;] = self._exchange_option_restore_json_rehydration(stub_rehydration)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        if recovery_point_id is not None:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#39;exchangeOption&#39;][&#34;recoveryPointId&#34;] = recovery_point_id

        filters = kwargs.get(&#34;filters&#34;)
        if filters:
            if (filters.get(&#34;restore_all_matching&#34;)):
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                    &#34;restoreOptions&#34;][&#34;exchangeOption&#34;][&#34;restoreAllMatching&#34;] = filters.get(&#34;restore_all_matching&#34;,
                                                                                           False)
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                    &#34;restoreOptions&#34;][&#34;fileOption&#34;][&#34;browseFilters&#34;] = [self._browse_filter_xml(filters)]
            if (filters.get(&#34;restore_selected_items&#34;)):
                req = self._request_json_search_in_restore(filters)
                result = self._process_search_response(req)
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                    &#34;restoreOptions&#34;][&#34;fileOption&#34;][&#34;sourceItem&#34;] = result

        return self._process_restore_response(request_json)

    def out_of_place_restore(
            self,
            paths,
            destination_path,
            overwrite=True,
            journal_report=False):
        &#34;&#34;&#34;Restores the mailboxes/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of paths of mailboxes/folders to restore
                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  path where the mailboxes needs to be restored

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}
        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;journal_report&#39;] = journal_report
        restore_option[&#39;unconditional_overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        # restore_option[&#39;client&#39;] = destination_client
        restore_option[&#39;destination_path&#39;] = destination_path
        oop_target_mailbox = destination_path.split(chr(18))[0].split(&#34;\\MB\\&#34;)[1]
        restore_option[&#39;target_mailbox&#39;] = oop_target_mailbox
        request_json = self._prepare_out_of_place_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def disk_restore(
            self,
            paths,
            destination_client,
            destination_path,
            overwrite=True,
            journal_report=False):
        &#34;&#34;&#34;Restores the mailboxes/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of paths of mailboxes/folders to restore
                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  path where the mailboxes needs to be restored

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}
        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;journal_report&#39;] = journal_report
        restore_option[&#39;unconditional_overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        restore_option[&#39;client&#39;] = destination_client
        restore_option[&#39;destination_path&#39;] = destination_path

        request_json = self._prepare_disk_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def pst_restore(
            self,
            paths,
            destination_client,
            pst_path,
            overwrite=True,
            journal_report=False):
        &#34;&#34;&#34;Restores the Mailbox/Emails specified in the input paths list to the PST PATH location.

            Args:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}
        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;journal_report&#39;] = journal_report
        restore_option[&#39;unconditional_overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        restore_option[&#39;client&#39;] = destination_client
        restore_option[&#39;destination_path&#39;] = pst_path

        request_json = self._prepare_pst_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def pst_ingestion(self):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.
            Returns:
                object - instance of the Job class for this backup job

        &#34;&#34;&#34;

        payload_dict = {
            &#34;taskInfo&#34;:{
                &#34;associations&#34;:[self._subClientEntity],
                &#34;task&#34;: self.get_pst_task_json(),
                &#34;subTasks&#34;:[{
                    &#34;subTaskOperation&#34;:1,
                    &#34;subTask&#34;:{
                        &#34;subTaskType&#34;:2,
                        &#34;operationType&#34;:5024
                    },
                    &#34;options&#34;:{
                        &#34;backupOpts&#34;:self.get_pst_backup_opt_json(),
                        &#34;adminOpts&#34;:{
                            &#34;contentIndexingOption&#34;:{
                                &#34;subClientBasedAnalytics&#34;:False
                            }
                        },
                        &#34;restoreOptions&#34;:{
                            &#34;virtualServerRstOption&#34;:{
                                &#34;isBlockLevelReplication&#34;:False
                            }
                        }
                    }
                }]
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#34;RESTORE&#34;], payload_dict)

        return self._process_backup_response(flag, response)

    def subclient_content_indexing(self,
                                   pick_failed_items=False,
                                   pick_only_failed_items=False,
                                   streams=4,
                                   proxies=None):
        &#34;&#34;&#34;Run content Indexing on Subclient .

            Args:
               pick_failed_items
                        default:False   (bool)  --  Pick fail items during Content Indexing

                pick_only_failed_items  (bool)  --  Pick only fail items items during Content
                                                    Indeixng
                    default: False

                streams                 (int)   --  Streams for Content Indexing job

                    default: 4

                proxies                 (list) --  provide the proxies to run CI
                    default: None

            Returns:
                object - instance of the Job class for this ContentIndexing job

        &#34;&#34;&#34;
        # check if inputs are correct

        ci_option = {}
        if not (isinstance(pick_failed_items, bool) and
                isinstance(pick_only_failed_items, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        ci_option[&#39;pick_failed_items&#39;] = pick_failed_items
        ci_option[&#39;pick_only_failed_items&#39;] = pick_only_failed_items
        ci_option[&#39;streams&#39;] = streams
        if proxies is None:
            ci_option[&#39;proxies&#39;] = {}
        else:
            member_servers = self._member_servers(proxies)
            ci_option[&#39;proxies&#39;] = {
                &#34;memberServers&#34;: member_servers
            }

        self._media_option_json(ci_option)
        self._content_indexing_option_json()
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._json_content_indexing_subtasks,
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: self._media_option_json_
                            },
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: self._content_indexing_option_json_
                            },
                            &#34;restoreOptions&#34;: {
                                &#34;virtualServerRstOption&#34;: {
                                    &#34;isBlockLevelReplication&#34;: False
                                },
                                &#34;browseOption&#34;: {
                                    &#34;backupset&#34;: {}
                                }
                            }
                        }
                    }
                ]
            }
        }

        return self._process_restore_response(request_json)

    def get_pst_task_json(self):
        &#34;&#34;&#34;Get task json for pst ingestion job

            Returns:
                 Pst task json
        &#34;&#34;&#34;
        task_json = {
            &#34;ownerId&#34;: self._commcell_object.users.all_users[
                self._commcell_object.commcell_username],
            &#34;taskType&#34;: 1,
            &#34;ownerName&#34;: self._commcell_object.commcell_username,
            &#34;sequenceNumber&#34;: 0,
            &#34;initiatedFrom&#34;: 1,
            &#34;policyType&#34;: 0,
            &#34;taskId&#34;: 0,
            &#34;taskFlags&#34;: {
                &#34;isEZOperation&#34;: False, &#34;disabled&#34;: False
            }
        }
        return task_json

    def get_pst_backup_opt_json(self):
        &#34;&#34;&#34;Get backup options json for pst ingestion job

            Returns:
                 Pst backup options json
        &#34;&#34;&#34;
        backup_opt_json = {
            &#34;truncateLogsOnSource&#34;: False,
            &#34;sybaseSkipFullafterLogBkp&#34;: False,
            &#34;notSynthesizeFullFromPrevBackup&#34;: False,
            &#34;backupLevel&#34;: 2,
            &#34;incLevel&#34;: 1,
            &#34;adHocBackup&#34;: False,
            &#34;runIncrementalBackup&#34;: False,
            &#34;isSpHasInLineCopy&#34;: False,
            &#34;runSILOBackup&#34;: False,
            &#34;doNotTruncateLog&#34;: False,
            &#34;exchOnePassOptions&#34;: {
                &#34;proxies&#34;: {}
            },
            &#34;dataOpt&#34;: self.get_pst_data_opt_json(),
            &#34;mediaOpt&#34;: {}
        }
        return backup_opt_json

    def get_pst_data_opt_json(self):
        &#34;&#34;&#34;Get data options json for pst ingestion job

            Returns:
                 Pst data options json
        &#34;&#34;&#34;
        data_json = {
            &#34;skipCatalogPhaseForSnapBackup&#34;: True,
            &#34;useCatalogServer&#34;: False,
            &#34;followMountPoints&#34;: True,
            &#34;enforceTransactionLogUsage&#34;: False,
            &#34;skipConsistencyCheck&#34;: False,
            &#34;createNewIndex&#34;: False
        }
        return data_json

    def _prepare_attachment_json(self, metadata, attachment_id):
        &#34;&#34;&#34;Prepare the JSON for the attachment files with the options provided.
            Args:
                metadata    (dict)  --  metadata of the mail
                attachment_id(str)  --  attachment id of the file
            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;

        request_json = {
            &#34;filters&#34;: [
                {
                    &#34;field&#34;: &#34;CLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(self._get_client_dict(self._client_object)[&#34;client&#34;][&#34;clientId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;SUBCLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;subclient&#34;][&#34;applicationId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;APP_TYPE&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            &#34;137&#34;
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;CONTENTID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;indexing&#34;][&#34;objectGUID&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ATTACHMENTID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            attachment_id
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;CV_TURBO_GUID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;indexing&#34;][&#34;objectGUID&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;archiveFileId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_OFFSET&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;offset&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;COMMCELL_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;advConfig&#34;][&#34;browseAdvancedConfigResp&#34;][
                                    &#34;commcellNumber&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_SIZE&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;size&#34;])
                        ]
                    }
                }
            ]
        }
        return request_json

    def _get_attachment(self,metadata,attachmentId):
        &#34;&#34;&#34;Get the content of the attachment file with the options provided.
            Args:
                metadata    (dict)  --  metadata of the mail
                attachmentId(str)  --  attachment id of the file
            Returns:
                str - content of the attachment file (html content)
        &#34;&#34;&#34;

        request_json = self._prepare_attachment_json(metadata,attachmentId)
        self._GET_VARIOUS_PREVIEW = self._services[&#39;GET_VARIOUS_PREVIEW&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._GET_VARIOUS_PREVIEW, request_json)

        if flag:
            return response.text
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, self._update_response_(response.text))


    def _get_attachment_preview(self, attachments, metadata):
        &#34;&#34;&#34;
        Get the preview content of all the attachments in the attachment list
        Args:
            attachments (list)  --  list of attachments
            metadata    (dict)  --  metadata of the mail

        Returns:
            dict - dictionary with attachment name as key and content as value

        &#34;&#34;&#34;
        attachments_preview = {}
        pattern = re.compile(r&#39;attId=([^&amp;]+)&#39;)
        for attachment in attachments:
            match = pattern.search(attachment[&#39;ciPreviewLink&#39;]).group(1)
            attachments_preview[attachment[&#39;name&#39;]] = self._get_attachment(metadata, match)
        return attachments_preview

    def preview_backedup_file(self, file_path):
        &#34;&#34;&#34;Gets the preview content for the subclient.

            Params:
                file_path (str)  --  path of the file for which preview is needed

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._GET_PREVIEW_CONTENT = self._services[&#39;GET_PREVIEW&#39;]
        metadata = self._get_preview_metadata(file_path)
        if metadata is None:
            raise SDKException(&#39;Subclient&#39;, &#39;123&#39;)

        if metadata[&#34;type&#34;] != &#34;File&#34;:
            raise SDKException(&#39;Subclient&#39;, &#39;124&#39;)

        if metadata[&#34;size&#34;] == 0:
            raise SDKException(&#39;Subclient&#39;, &#39;125&#39;)

        if metadata[&#34;size&#34;] &gt; 20 * 1024 * 1024:
            raise SDKException(&#39;Subclient&#39;, &#39;126&#39;)
        request_json = {
            &#34;filters&#34;: [
                {
                    &#34;field&#34;: &#34;CLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(self._get_client_dict(self._client_object)[&#34;client&#34;][&#34;clientId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;SUBCLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;subclient&#34;][&#34;applicationId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;CONTENTID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;indexing&#34;][&#34;objectGUID&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;archiveFileId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_OFFSET&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;offset&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;COMMCELL_NUMBER&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;advConfig&#34;][&#34;browseAdvancedConfigResp&#34;][
                                            &#34;commcellNumber&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_SIZE&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;size&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_PATH&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            file_path
                        ]
                    }
                }
            ]
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._GET_PREVIEW_CONTENT, request_json)

        if flag:
            if &#34;Preview not available&#34; not in response.text:
                response =  response.json()
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;127&#39;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, self._update_response_(response.text))

        result = &#34;&#34;
        attachments = response[&#34;attachments&#34;]
        del response[&#34;attachments&#34;]
        for key in response:
            result += key + &#34; : &#34; + str(response[key]) + &#34;\n&#34;
        # If mail has attachments we add the content of the attachments
        if len (attachments) != 0:
            attachments_preview = self._get_attachment_preview(attachments, metadata)
            result += &#34;Attachments:\n&#34;
            for file in attachments_preview:
                result += file + &#34; : &#34; + attachments_preview[file] + &#34;\n&#34;
        return result

    @staticmethod
    def _get_ad_group_backup_common_opt_json(ad_group_name):
        &#34;&#34;&#34;
            Gets the common options json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Return:
                Common options for da group backup
        &#34;&#34;&#34;
        return {
            &#34;notifyUserOnJobCompletion&#34;: False,
            &#34;jobMetadata&#34;: [
                {
                    &#34;selectedItems&#34;: [
                        {
                            &#34;itemName&#34;: ad_group_name,
                            &#34;itemType&#34;: &#34;AD group&#34;
                        }
                    ],
                    &#34;jobOptionItems&#34;: [
                        {
                            &#34;option&#34;: &#34;Total running time&#34;,
                            &#34;value&#34;: &#34;Disabled&#34;
                        }
                    ]
                }
            ]
        }

    @staticmethod
    def _get_ad_group_backup_backupoptions_json(ad_group_name):
        &#34;&#34;&#34;
            Gets the backup options json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                Backup options json for ad group backup
        &#34;&#34;&#34;
        return {
            &#34;backupLevel&#34;: 2,
            &#34;incLevel&#34;: 1,
            &#34;exchOnePassOptions&#34;: {
                &#34;adGroups&#34;: [
                    {
                        &#34;adGroupName&#34;: ad_group_name
                    }
                ]
            },
            &#34;dataOpt&#34;: {
                &#34;useCatalogServer&#34;: False,
                &#34;followMountPoints&#34;: True,
                &#34;enforceTransactionLogUsage&#34;: False,
                &#34;skipConsistencyCheck&#34;: True,
                &#34;createNewIndex&#34;: False
            }
        }

    @staticmethod
    def _get_ad_group_backup_options_json(ad_group_name):
        &#34;&#34;&#34;
            Get options json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                options json for ad group backup
        &#34;&#34;&#34;
        return {
            &#34;backupOpts&#34;: ExchangeSubclient._get_ad_group_backup_backupoptions_json(ad_group_name),
            &#34;commonOpts&#34;: ExchangeSubclient._get_ad_group_backup_common_opt_json(ad_group_name)
        }

    @staticmethod
    def _get_ad_group_backup_subtask_json(ad_group_name):
        &#34;&#34;&#34;
            Gets the subtask json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                Sub task json for ad group backup
        &#34;&#34;&#34;
        sub_task_json = [{
            &#34;subTask&#34;: {
              &#34;subTaskType&#34;: 2,
              &#34;operationType&#34;: 2
            },
            &#34;options&#34;: ExchangeSubclient._get_ad_group_backup_options_json(ad_group_name)
        }]
        return sub_task_json

    def _get_ad_group_backup_task_json(self, ad_group_name):
        &#34;&#34;&#34;
            Gets the task json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                Task json for ad group backup
        &#34;&#34;&#34;
        task_json = {
            &#34;associations&#34;: [self._subClientEntity],
            &#34;task&#34;: {&#34;taskType&#34;: 1},
            &#34;subTasks&#34;: ExchangeSubclient._get_ad_group_backup_subtask_json(ad_group_name)
        }
        return task_json
    
    def ad_group_backup(self, ad_group_name):
        &#34;&#34;&#34;
            Run backup of AD Group
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                Processed job
        &#34;&#34;&#34;
        task_json = self._get_ad_group_backup_task_json(ad_group_name)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient"><code class="flex name class">
<span>class <span class="ident">ExchangeSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Subclient Base class, representing an Exchange subclient,
and to perform operations on that subclient.</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L60-L1436" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ExchangeSubclient(Subclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing an Exchange subclient,
        and to perform operations on that subclient.
    &#34;&#34;&#34;

    def __new__(cls, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Decides which subclient object needs to be created&#34;&#34;&#34;
        from .exchange.usermailbox_subclient import UsermailboxSubclient
        from .exchange.journalmailbox_subclient import JournalMailboxSubclient
        from .exchange.contentstoremailbox_subclient import ContentStoreMailboxSubclient
        backupset_types = {
            &#34;user mailbox&#34;: UsermailboxSubclient,
            &#34;journal mailbox&#34;: JournalMailboxSubclient,
            &#34;contentstore mailbox&#34;: ContentStoreMailboxSubclient
        }

        backupset_name = backupset_object.backupset_name

        if backupset_name in backupset_types:
            subclient_type = backupset_types[backupset_name]
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient for this instance type is not yet implemented&#39;
            )

        return object.__new__(subclient_type)

    @staticmethod
    def _get_client_dict(client_object):
        &#34;&#34;&#34;Returns the client dict for the client object to be appended to member server.

            Args:
                client_object   (object)    --  instance of the Client class

            Returns:
                dict    -   dictionary for a single client to be associated
        &#34;&#34;&#34;
        client_dict = {
            &#34;client&#34;: {
                &#34;clientName&#34;: client_object.client_name,
                &#34;clientId&#34;: int(client_object.client_id),
                &#34;_type_&#34;: 3
            }
        }

        return client_dict

    def _member_servers(self, clients_list):
        &#34;&#34;&#34;Returns the proxy clients to be associated .

            Args:
                clients_list (list)    --  list of the clients to associated

            Returns:
                list - list consisting of all member servers to be associated

            Raises:
                SDKException:
                    if type of clients list argument is not list

        &#34;&#34;&#34;
        if not isinstance(clients_list, list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        member_servers = []

        for client in clients_list:
            if isinstance(client, str):
                client = client.strip().lower()

                if self._commcell_object.clients.has_client(client):
                    temp_client = self._commcell_object.clients.get(client)

                    if temp_client.agents.has_agent(&#39;exchange mailbox (classic)&#39;):
                        client_dict = self._get_client_dict(temp_client)
                        member_servers.append(client_dict)

                    del temp_client
            elif isinstance(client, Client):
                if client.agents.has_agent(&#39;exchange mailbox (classic)&#39;):
                    client_dict = self._get_client_dict(client)
                    member_servers.append(client_dict)

        return member_servers

    def _content_indexing_option_json(self):
        &#34;&#34;&#34;Getter for  the content indexing options of ContentIndexing JSON&#34;&#34;&#34;

        self._content_indexing_option_json_ = {
            &#34;reanalyze&#34;: False,
            &#34;selectInactiveMailboxes&#34;: False,
            &#34;fileAnalytics&#34;: False,
            &#34;subClientBasedAnalytics&#34;: False
        }

    def _media_option_json(self, value):
        &#34;&#34;&#34;Setter for  the media options of ContentIndexing JSON

            Args:
                value   (dict)  --  media option need to be included

            Returns:
                (dict)  -       generated media options JSON

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._media_option_json_ = {
            &#34;pickFailedItems&#34;: value.get(&#34;pick_failed_items&#34;),
            &#34;pickFailedItemsOnly&#34;: value.get(&#34;pick_only_failed_items&#34;),
            &#34;auxcopyJobOption&#34;: {
                &#34;maxNumberOfStreams&#34;: value.get(&#34;streams&#34;),
                &#34;allCopies&#34;: True,
                &#34;useMaximumStreams&#34;: False,
                &#34;proxies&#34;: value.get(&#34;proxies&#34;)
            }
        }

    def _json_backupset(self):
        &#34;&#34;&#34;Getter for the Exchange Mailbox backupset option in restore json&#34;&#34;&#34;

        self._exchange_backupset_json = {
            &#34;clientName&#34;: self._client_object.client_name,
            &#34;backupsetName&#34;: self._backupset_object.backupset_name
        }

    def _json_restore_exchange_restore_option(self, value):
        &#34;&#34;&#34;
            setter for  the Exchange Mailbox in place restore  option in restore json

            Args:
                value   (dict)  --  restore option need to be included

            Returns:
                (dict)  -       generated exchange restore options JSON
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._exchange_option_restore_json = {
            &#34;exchangeRestoreChoice&#34;: 1,
            &#34;exchangeRestoreDrive&#34;: 1,
            &#34;isJournalReport&#34;: value.get(&#34;journal_report&#34;, False),
            &#34;pstFilePath&#34;: &#34;&#34;,
            &#34;targetMailBox&#34;: value.get(&#34;target_mailbox&#34;,None)
        }

    def _json_restore_exchange_common_option(self, value):
        &#34;&#34;&#34;
           Prepare exchange mailbox in place restore common options in restore json

            Args:
                value   (dict)  --  restore common options need to be included

            Returns:
                (dict)  -       generated exchange restore common options JSON
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._exchange_common_option_restore_json = {
            &#34;clusterDBBackedup&#34;: False,
            &#34;restoreToDisk&#34;: False,
            &#34;restoreDataInsteadOfStub&#34;: True,
            &#34;offlineMiningRestore&#34;: False,
            &#34;browse&#34;: True,
            &#34;skip&#34;: False,
            &#34;restoreOnlyStubExists&#34;: False,
            &#34;truncateBody&#34;: value.get(&#34;truncate_body&#34;, False),
            &#34;restoreAsStubs&#34;: value.get(&#34;restore_as_stubs&#34;, False),
            &#34;copyToObjectStore&#34;: False,
            &#34;onePassRestore&#34;: False,
            &#34;collectMsgsLargerThan&#34;: value.get(&#34;collect_msgs_larger_than&#34;, 1024),
            &#34;collectMsgsDaysAfter&#34;: value.get(&#34;collect_msgs_days_after&#34;, 30),
            &#34;unconditionalOverwrite&#34;: True,
            &#34;syncRestore&#34;: False,
            &#34;leaveMessageBody&#34;: value.get(&#34;leave_message_body&#34;, False),
            &#34;collectMsgWithAttach&#34;: value.get(&#34;collect_msg_with_attach&#34;, False),
            &#34;truncateBodyToBytes&#34;: value.get(&#34;truncate_body_to_bytes&#34;, 0),
            &#34;recoverToRecoveredItemsFolder&#34;: False,
            &#34;append&#34;: False
        }

        return self._exchange_common_option_restore_json

    def _json_out_of_place_destination_option(self, value):
        &#34;&#34;&#34;setter for  the Exchange Mailbox out of place restore
        option in restore json

            Args:
                value   (dict)  --  restore option need to be included

            Returns:
                (dict)  -       generated exchange restore options JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._out_of_place_destination_json = {
            &#34;inPlace&#34;: False,
            &#34;destPath&#34;: [value.get(&#34;destination_path&#34;)],
            &#34;destClient&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;clientName&#34;: self._client_object.client_name
            },
        }

    def _json_job_option_items(self, value):
        &#34;&#34;&#34;
        Generates JSON for job options.
        Args:
            value (dict): Dictionary containing job options.
        Returns:
            dict: JSON representation of job options.
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        additional_options = []
        message_exist = value.get(&#34;if_message_exists&#34;, JobOptionValues.SKIP.value)
        stub_rehydration = value.get(&#39;stub_rehydration&#39;, JobOptionValues.DISABLED.value)
        include_deleted_items = value.get(&#39;include_deleted_items&#39;, JobOptionValues.DISABLED.value)
        match_destination_user = value.get(&#39;match_destination_user&#39;, JobOptionValues.DISABLED.value)
        stub_rehydration_option = value.get(&#39;stub_rehydration_option&#39;,
                                            JobOptionValues.RECOVER_STUBS.value)
        email_level_reporting = value.get(&#39;email_level_reporting&#39;, JobOptionValues.DISABLED.value)
        old_link = value.get(&#39;old_recall_link&#39;, None)
        new_link = value.get(&#39;new_recall_link&#39;, None)

        self._job_option_items_json = [
            {&#34;option&#34;: JobOptionKeys.RESTORE_DESTINATION.value, &#34;value&#34;: JobOptionValues.EXCHANGE.value},
            {&#34;option&#34;: JobOptionKeys.DESTINATION.value, &#34;value&#34;: JobOptionValues.ORIGINAL_LOCATION.value},
            {&#34;option&#34;: JobOptionKeys.IF_MESSAGE_EXISTS.value, &#34;value&#34;: message_exist},
            {&#34;option&#34;: JobOptionKeys.INCLUDE_DELETED_ITEMS.value, &#34;value&#34;: include_deleted_items},
            {&#34;option&#34;: JobOptionKeys.MATCH_DESTINATION_USER.value, &#34;value&#34;: match_destination_user},
            {&#34;option&#34;: JobOptionKeys.STUB_REHYDRATION.value, &#34;value&#34;: stub_rehydration},
        ]

        if stub_rehydration != JobOptionValues.DISABLED.value:
            if stub_rehydration_option == JobOptionValues.RECOVER_STUBS.value:
                additional_options = []
            elif stub_rehydration_option == JobOptionValues.STUB_REPORTING.value:
                additional_options = [
                    {&#34;option&#34;: JobOptionKeys.MAILBOX_LEVEL_REPORTING.value, &#34;value&#34;: JobOptionValues.ENABLED.value},
                    {&#34;option&#34;: JobOptionKeys.EMAIL_LEVEL_REPORTING.value, &#34;value&#34;: email_level_reporting},
                ]
            elif stub_rehydration_option == JobOptionValues.UPDATE_RECALL_LINK.value:
                additional_options = [
                    {&#34;option&#34;: JobOptionKeys.OLD_RECALL_LINK.value, &#34;value&#34;: old_link},
                    {&#34;option&#34;: JobOptionKeys.NEW_RECALL_LINK.value, &#34;value&#34;: new_link},
                ]

        if additional_options:
            self._job_option_items_json.extend(additional_options)

        self._job_option_items_json.append({
            &#34;option&#34;: JobOptionKeys.STUB_REHYDRATION_OPTION.value,
            &#34;value&#34;: stub_rehydration_option
        })

        return self._job_option_items_json

    def _exchange_option_restore_json_rehydration(self, value):
        &#34;&#34;&#34;
        Generates JSON for Exchange restore rehydration options.
        Args:
            value (dict): Dictionary containing rehydration options.
        Returns:
            dict: JSON representation of rehydration options.
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        mapping = {
            JobOptionValues.RECOVER_STUBS.value: JobOptionIntegers.RECOVER_STUBS.value,
            JobOptionValues.STUB_REPORTING.value: JobOptionIntegers.STUB_REPORTING.value,
            JobOptionValues.UPDATE_RECALL_LINK.value: JobOptionIntegers.UPDATE_RECALL_LINK.value
        }

        stub_rehydration_option = mapping.get(
            value.get(&#39;stub_rehydration_option&#39;, JobOptionValues.RECOVER_STUBS.value)
        )

        email_level_reporting = value.get(&#39;email_level_reporting&#39;, JobOptionValues.DISABLED.value)
        old_link = value.get(&#39;old_recall_link&#39;, None)
        new_link = value.get(&#39;new_recall_link&#39;, None)

        base = {
            JobOptionKeys.STUB_REHYDRATION.value: True,
            JobOptionKeys.STUB_REHYDRATION_OPTION.value: stub_rehydration_option
        }

        additional_options = {}
        if stub_rehydration_option == JobOptionIntegers.RECOVER_STUBS.value:
            additional_options = {}
        elif stub_rehydration_option == JobOptionIntegers.STUB_REPORTING.value:
            additional_options = {
                &#34;stubReportOption&#34;: {
                    &#34;messageLevelReport&#34;: email_level_reporting == JobOptionValues.ENABLED.value,
                    &#34;mailboxLevelReport&#34;: True
                }
            }
        elif stub_rehydration_option == JobOptionIntegers.UPDATE_RECALL_LINK.value:
            additional_options = {
                &#34;stubOldRecallLink&#34;: old_link,
                &#34;stubRecallLink&#34;: new_link
            }

        base.update(additional_options)

        self._json_exchange_options = {
            JobOptionKeys.EXCHANGE_RESTORE_CHOICE.value: JobOptionIntegers.EXCHANGE_RESTORE_CHOICE.value,
            JobOptionKeys.EXCHANGE_RESTORE_DRIVE.value: JobOptionIntegers.EXCHANGE_RESTORE_DRIVE.value,
            JobOptionKeys.IS_JOURNAL_REPORT.value: value.get(&#34;journal_report&#34;, False),
            JobOptionKeys.PST_FILE_PATH.value: &#34;&#34;,
            JobOptionKeys.TARGET_MAILBOX.value: value.get(&#34;target_mailbox&#34;, None),
            JobOptionKeys.STUB_REHYDRATION.value: base
        }

        return self._json_exchange_options

    def _browse_filter_xml(self, value):
        &#34;&#34;&#34;
        Generates an XML query for browsing with a filter.
        Args:
            value (dict): Dictionary containing the filter value with the key &#39;keyword&#39;.
        Returns:
            str: XML query string for the filter.
        &#34;&#34;&#34;
        xml_query = f&#34;&#34;&#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;
        &lt;databrowse_Query type=&#34;0&#34; queryId=&#34;0&#34; isFacet=&#34;1&#34;&gt;
            &lt;whereClause connector=&#34;0&#34;&gt;
                &lt;criteria field=&#34;29&#34;&gt;
                    &lt;values val=&#34;{value[&#34;keyword&#34;]}&#34;/&gt;
                &lt;/criteria&gt;
            &lt;/whereClause&gt;
        &lt;/databrowse_Query&gt;&#34;&#34;&#34;
        return xml_query

    def _request_json_search_in_restore(self, filters):
        &#34;&#34;&#34;
        Generates a JSON request for searching within a restore operation.
        Args:
            filters (dict): Dictionary containing the search filter parameters.
                               Expected keys are &#39;keyword&#39; and &#39;appID&#39;.
        Returns:
            dict: JSON request for the search operation.
        &#34;&#34;&#34;
        req_json = ExchangeConstants.SEARCH_IN_RESTORE_PAYLOAD
        req_json[&#34;advSearchGrp&#34;][&#34;cvSearchKeyword&#34;][&#34;keyword&#34;] = filters.get(&#34;keyword&#34;, &#34;&#34;)
        req_json[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;appIdList&#34;] = filters.get(&#34;appID&#34;, [])
        return req_json

    def _json_disk_restore_exchange_restore_option(self, value):
        &#34;&#34;&#34;Setter for  the Exchange Mailbox Disk restore option
        in restore json

            Args:
                value   (dict)  --  restore option need to be included

            Returns:
                (dict)  -       generated exchange restore options JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._exchange_disk_option_restore_json = {
            &#34;exchangeRestoreChoice&#34;: 3,
            &#34;exchangeRestoreDrive&#34;: 1,
            &#34;diskFilePath&#34;: value.get(&#34;destination_path&#34;),
            &#34;isJournalReport&#34;: value.get(&#34;journal_report&#34;, False),
            &#34;pstFilePath&#34;: &#34;&#34;
        }

    def _json_pst_restore_exchange_restore_option(self, value):
        &#34;&#34;&#34;Setter for  the Exchange Mailbox PST restore option in restore json
            Args:
                value   (dict)  --  restore option need to be included

            Returns:
                (dict)  -       generated exchange restore options JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._exchange_pst_option_restore_json = {
            &#34;exchangeRestoreChoice&#34;: 2,
            &#34;exchangeRestoreDrive&#34;: 1,
            &#34;isJournalReport&#34;: value.get(&#34;journal_report&#34;, False),
            &#34;pstFilePath&#34;: value.get(&#34;destination_path&#34;),
            &#34;pstRestoreOption&#34;: value.get(&#34;limit_pst_size&#34;, 0),
            &#34;pstSize&#34;: value.get(&#34;pst_size&#34;, 2048)
        }

    @property
    def _json_content_indexing_subtasks(self):
        &#34;&#34;&#34;Getter for the contentindexing subtask in restore JSON.
        It is read only attribute&#34;&#34;&#34;

        _subtask_restore_json = {
            &#34;subTaskType&#34;: 1,
            &#34;operationType&#34;: 5020
        }

        return _subtask_restore_json

    def _prepare_pst_restore_json(self, _pst_restore_option=None):
        &#34;&#34;&#34;
        Prepare PST retsore Json with all getters

        Args:
            _pst_restore_option - dictionary with all PST restore options

            value:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  PST path where the mailboxes needs to be
                                                    restored
                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False

        returns:
            request_json   -- complete json for performing PST Restore options
        &#34;&#34;&#34;

        if _pst_restore_option is None:
            _pst_restore_option = {}

        paths = self._filter_paths(_pst_restore_option[&#39;paths&#39;])
        self._json_pst_restore_exchange_restore_option(_pst_restore_option)
        self._json_backupset()

        _pst_restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_pst_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;exchangeOption&#39;] = self._exchange_pst_option_restore_json

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        return request_json

    def _prepare_disk_restore_json(self, _disk_restore_option):
        &#34;&#34;&#34;
        Prepare disk retsore Json with all getters

        Args:
            _disk_restore_option - dictionary with all disk restore options

            value:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  path where the mailboxes needs to be restored
                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False


        returns:
            request_json        -complete json for performing disk Restore options
        &#34;&#34;&#34;

        if _disk_restore_option is None:
            _disk_restore_option = {}

        paths = self._filter_paths(_disk_restore_option[&#39;paths&#39;])
        self._json_disk_restore_exchange_restore_option(_disk_restore_option)
        self._json_backupset()
        _disk_restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_disk_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;exchangeOption&#39;] = self._exchange_disk_option_restore_json

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        return request_json

    def _prepare_out_of_place_restore_json(self, _restore_option):
        &#34;&#34;&#34;
        Prepare out of place retsore Json with all getters

        Args:
            _restore_option - dictionary with all out of place restore options

            value:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  path where the mailboxes needs to be restored
                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False


        returns:
            request_json        -  complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _restore_option is None:
            _restore_option = {}

        paths = self._filter_paths(_restore_option[&#39;paths&#39;])
        self._json_restore_exchange_restore_option(_restore_option)
        self._json_out_of_place_destination_option(_restore_option)
        self._json_backupset()
        _restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;exchangeOption&#39;] = self._exchange_option_restore_json

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;destination&#39;] = self._out_of_place_destination_json

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        return request_json

    def _cleanup_json(self):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.
            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._json_backup_subtasks,
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;backupLevel&#34;: 15
                            }
                        }
                    }
                ]
            }
        }

        return request_json

    def cleanup(self):
        &#34;&#34;&#34;Runs a cleanup job for the subclient .

            Returns:
                object - instance of the Job class for this backup job

        &#34;&#34;&#34;

        request_json = self._cleanup_json()
        return self._process_restore_response(request_json)

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            journal_report=False,
            restore_as_stub=None,
            recovery_point_id=None,
            **kwargs):
        &#34;&#34;&#34;Restores the mailboxes/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                        contentStore Mailbox
                    default: False

                restore_as_stub         (dict)  --  setters for common options

                recovery_point_id       (int)   --  ID of the recovery point to which the mailbox is to be restored to
                    Default: None

                **kwargs:
                Expected:
                stub_rehydration        (dict)  --  stub rules to rehydrate items during restore
                    Default: None

                filters                  (dict)  --  filter values for find and restore
                    Default: None

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        restore_option = {}
        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;journal_report&#39;] = journal_report

        paths = self._filter_paths(paths)
        self._json_restore_exchange_restore_option(restore_option)
        self._json_backupset()
        restore_option[&#39;unconditional_overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths

        request_json = self._restore_json(restore_option=restore_option)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = self.subclient_name
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][
            &#39;backupsetName&#39;] = self._backupset_object.backupset_name
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][&#39;exchangeOption&#39;] = self._exchange_option_restore_json
        if restore_as_stub:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;commonOptions&#39;] = self._json_restore_exchange_common_option(restore_as_stub)

        stub_rehydration = kwargs.get(&#34;stub_rehydration&#34;)
        if stub_rehydration:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;commonOpts&#39;][
                &#39;jobOptionItems&#39;] = self._json_job_option_items(stub_rehydration)
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
                &#39;options&#39;][&#39;restoreOptions&#39;][&#39;exchangeOption&#39;] = self._exchange_option_restore_json_rehydration(stub_rehydration)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        if recovery_point_id is not None:
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#39;exchangeOption&#39;][&#34;recoveryPointId&#34;] = recovery_point_id

        filters = kwargs.get(&#34;filters&#34;)
        if filters:
            if (filters.get(&#34;restore_all_matching&#34;)):
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                    &#34;restoreOptions&#34;][&#34;exchangeOption&#34;][&#34;restoreAllMatching&#34;] = filters.get(&#34;restore_all_matching&#34;,
                                                                                           False)
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                    &#34;restoreOptions&#34;][&#34;fileOption&#34;][&#34;browseFilters&#34;] = [self._browse_filter_xml(filters)]
            if (filters.get(&#34;restore_selected_items&#34;)):
                req = self._request_json_search_in_restore(filters)
                result = self._process_search_response(req)
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                    &#34;restoreOptions&#34;][&#34;fileOption&#34;][&#34;sourceItem&#34;] = result

        return self._process_restore_response(request_json)

    def out_of_place_restore(
            self,
            paths,
            destination_path,
            overwrite=True,
            journal_report=False):
        &#34;&#34;&#34;Restores the mailboxes/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of paths of mailboxes/folders to restore
                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  path where the mailboxes needs to be restored

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}
        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;journal_report&#39;] = journal_report
        restore_option[&#39;unconditional_overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        # restore_option[&#39;client&#39;] = destination_client
        restore_option[&#39;destination_path&#39;] = destination_path
        oop_target_mailbox = destination_path.split(chr(18))[0].split(&#34;\\MB\\&#34;)[1]
        restore_option[&#39;target_mailbox&#39;] = oop_target_mailbox
        request_json = self._prepare_out_of_place_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def disk_restore(
            self,
            paths,
            destination_client,
            destination_path,
            overwrite=True,
            journal_report=False):
        &#34;&#34;&#34;Restores the mailboxes/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of paths of mailboxes/folders to restore
                destination_client              --  client where the mailboxes needs to be restored
                destination_path                --  path where the mailboxes needs to be restored

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}
        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;journal_report&#39;] = journal_report
        restore_option[&#39;unconditional_overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        restore_option[&#39;client&#39;] = destination_client
        restore_option[&#39;destination_path&#39;] = destination_path

        request_json = self._prepare_disk_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def pst_restore(
            self,
            paths,
            destination_client,
            pst_path,
            overwrite=True,
            journal_report=False):
        &#34;&#34;&#34;Restores the Mailbox/Emails specified in the input paths list to the PST PATH location.

            Args:
                paths                   (list)  --  list of paths of mailboxes/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True
                journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}
        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;journal_report&#39;] = journal_report
        restore_option[&#39;unconditional_overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        restore_option[&#39;client&#39;] = destination_client
        restore_option[&#39;destination_path&#39;] = pst_path

        request_json = self._prepare_pst_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def pst_ingestion(self):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.
            Returns:
                object - instance of the Job class for this backup job

        &#34;&#34;&#34;

        payload_dict = {
            &#34;taskInfo&#34;:{
                &#34;associations&#34;:[self._subClientEntity],
                &#34;task&#34;: self.get_pst_task_json(),
                &#34;subTasks&#34;:[{
                    &#34;subTaskOperation&#34;:1,
                    &#34;subTask&#34;:{
                        &#34;subTaskType&#34;:2,
                        &#34;operationType&#34;:5024
                    },
                    &#34;options&#34;:{
                        &#34;backupOpts&#34;:self.get_pst_backup_opt_json(),
                        &#34;adminOpts&#34;:{
                            &#34;contentIndexingOption&#34;:{
                                &#34;subClientBasedAnalytics&#34;:False
                            }
                        },
                        &#34;restoreOptions&#34;:{
                            &#34;virtualServerRstOption&#34;:{
                                &#34;isBlockLevelReplication&#34;:False
                            }
                        }
                    }
                }]
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#34;RESTORE&#34;], payload_dict)

        return self._process_backup_response(flag, response)

    def subclient_content_indexing(self,
                                   pick_failed_items=False,
                                   pick_only_failed_items=False,
                                   streams=4,
                                   proxies=None):
        &#34;&#34;&#34;Run content Indexing on Subclient .

            Args:
               pick_failed_items
                        default:False   (bool)  --  Pick fail items during Content Indexing

                pick_only_failed_items  (bool)  --  Pick only fail items items during Content
                                                    Indeixng
                    default: False

                streams                 (int)   --  Streams for Content Indexing job

                    default: 4

                proxies                 (list) --  provide the proxies to run CI
                    default: None

            Returns:
                object - instance of the Job class for this ContentIndexing job

        &#34;&#34;&#34;
        # check if inputs are correct

        ci_option = {}
        if not (isinstance(pick_failed_items, bool) and
                isinstance(pick_only_failed_items, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        ci_option[&#39;pick_failed_items&#39;] = pick_failed_items
        ci_option[&#39;pick_only_failed_items&#39;] = pick_only_failed_items
        ci_option[&#39;streams&#39;] = streams
        if proxies is None:
            ci_option[&#39;proxies&#39;] = {}
        else:
            member_servers = self._member_servers(proxies)
            ci_option[&#39;proxies&#39;] = {
                &#34;memberServers&#34;: member_servers
            }

        self._media_option_json(ci_option)
        self._content_indexing_option_json()
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._json_content_indexing_subtasks,
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: self._media_option_json_
                            },
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: self._content_indexing_option_json_
                            },
                            &#34;restoreOptions&#34;: {
                                &#34;virtualServerRstOption&#34;: {
                                    &#34;isBlockLevelReplication&#34;: False
                                },
                                &#34;browseOption&#34;: {
                                    &#34;backupset&#34;: {}
                                }
                            }
                        }
                    }
                ]
            }
        }

        return self._process_restore_response(request_json)

    def get_pst_task_json(self):
        &#34;&#34;&#34;Get task json for pst ingestion job

            Returns:
                 Pst task json
        &#34;&#34;&#34;
        task_json = {
            &#34;ownerId&#34;: self._commcell_object.users.all_users[
                self._commcell_object.commcell_username],
            &#34;taskType&#34;: 1,
            &#34;ownerName&#34;: self._commcell_object.commcell_username,
            &#34;sequenceNumber&#34;: 0,
            &#34;initiatedFrom&#34;: 1,
            &#34;policyType&#34;: 0,
            &#34;taskId&#34;: 0,
            &#34;taskFlags&#34;: {
                &#34;isEZOperation&#34;: False, &#34;disabled&#34;: False
            }
        }
        return task_json

    def get_pst_backup_opt_json(self):
        &#34;&#34;&#34;Get backup options json for pst ingestion job

            Returns:
                 Pst backup options json
        &#34;&#34;&#34;
        backup_opt_json = {
            &#34;truncateLogsOnSource&#34;: False,
            &#34;sybaseSkipFullafterLogBkp&#34;: False,
            &#34;notSynthesizeFullFromPrevBackup&#34;: False,
            &#34;backupLevel&#34;: 2,
            &#34;incLevel&#34;: 1,
            &#34;adHocBackup&#34;: False,
            &#34;runIncrementalBackup&#34;: False,
            &#34;isSpHasInLineCopy&#34;: False,
            &#34;runSILOBackup&#34;: False,
            &#34;doNotTruncateLog&#34;: False,
            &#34;exchOnePassOptions&#34;: {
                &#34;proxies&#34;: {}
            },
            &#34;dataOpt&#34;: self.get_pst_data_opt_json(),
            &#34;mediaOpt&#34;: {}
        }
        return backup_opt_json

    def get_pst_data_opt_json(self):
        &#34;&#34;&#34;Get data options json for pst ingestion job

            Returns:
                 Pst data options json
        &#34;&#34;&#34;
        data_json = {
            &#34;skipCatalogPhaseForSnapBackup&#34;: True,
            &#34;useCatalogServer&#34;: False,
            &#34;followMountPoints&#34;: True,
            &#34;enforceTransactionLogUsage&#34;: False,
            &#34;skipConsistencyCheck&#34;: False,
            &#34;createNewIndex&#34;: False
        }
        return data_json

    def _prepare_attachment_json(self, metadata, attachment_id):
        &#34;&#34;&#34;Prepare the JSON for the attachment files with the options provided.
            Args:
                metadata    (dict)  --  metadata of the mail
                attachment_id(str)  --  attachment id of the file
            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;

        request_json = {
            &#34;filters&#34;: [
                {
                    &#34;field&#34;: &#34;CLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(self._get_client_dict(self._client_object)[&#34;client&#34;][&#34;clientId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;SUBCLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;subclient&#34;][&#34;applicationId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;APP_TYPE&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            &#34;137&#34;
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;CONTENTID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;indexing&#34;][&#34;objectGUID&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ATTACHMENTID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            attachment_id
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;CV_TURBO_GUID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;indexing&#34;][&#34;objectGUID&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;archiveFileId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_OFFSET&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;offset&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;COMMCELL_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;advConfig&#34;][&#34;browseAdvancedConfigResp&#34;][
                                    &#34;commcellNumber&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_SIZE&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;size&#34;])
                        ]
                    }
                }
            ]
        }
        return request_json

    def _get_attachment(self,metadata,attachmentId):
        &#34;&#34;&#34;Get the content of the attachment file with the options provided.
            Args:
                metadata    (dict)  --  metadata of the mail
                attachmentId(str)  --  attachment id of the file
            Returns:
                str - content of the attachment file (html content)
        &#34;&#34;&#34;

        request_json = self._prepare_attachment_json(metadata,attachmentId)
        self._GET_VARIOUS_PREVIEW = self._services[&#39;GET_VARIOUS_PREVIEW&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._GET_VARIOUS_PREVIEW, request_json)

        if flag:
            return response.text
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, self._update_response_(response.text))


    def _get_attachment_preview(self, attachments, metadata):
        &#34;&#34;&#34;
        Get the preview content of all the attachments in the attachment list
        Args:
            attachments (list)  --  list of attachments
            metadata    (dict)  --  metadata of the mail

        Returns:
            dict - dictionary with attachment name as key and content as value

        &#34;&#34;&#34;
        attachments_preview = {}
        pattern = re.compile(r&#39;attId=([^&amp;]+)&#39;)
        for attachment in attachments:
            match = pattern.search(attachment[&#39;ciPreviewLink&#39;]).group(1)
            attachments_preview[attachment[&#39;name&#39;]] = self._get_attachment(metadata, match)
        return attachments_preview

    def preview_backedup_file(self, file_path):
        &#34;&#34;&#34;Gets the preview content for the subclient.

            Params:
                file_path (str)  --  path of the file for which preview is needed

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._GET_PREVIEW_CONTENT = self._services[&#39;GET_PREVIEW&#39;]
        metadata = self._get_preview_metadata(file_path)
        if metadata is None:
            raise SDKException(&#39;Subclient&#39;, &#39;123&#39;)

        if metadata[&#34;type&#34;] != &#34;File&#34;:
            raise SDKException(&#39;Subclient&#39;, &#39;124&#39;)

        if metadata[&#34;size&#34;] == 0:
            raise SDKException(&#39;Subclient&#39;, &#39;125&#39;)

        if metadata[&#34;size&#34;] &gt; 20 * 1024 * 1024:
            raise SDKException(&#39;Subclient&#39;, &#39;126&#39;)
        request_json = {
            &#34;filters&#34;: [
                {
                    &#34;field&#34;: &#34;CLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(self._get_client_dict(self._client_object)[&#34;client&#34;][&#34;clientId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;SUBCLIENT_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;subclient&#34;][&#34;applicationId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;CONTENTID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;indexing&#34;][&#34;objectGUID&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_ID&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;archiveFileId&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ARCHIVE_FILE_OFFSET&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;offset&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;COMMCELL_NUMBER&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;advanced_data&#34;][&#34;advConfig&#34;][&#34;browseAdvancedConfigResp&#34;][
                                            &#34;commcellNumber&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_SIZE&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            str(metadata[&#34;size&#34;])
                        ]
                    }
                },
                {
                    &#34;field&#34;: &#34;ITEM_PATH&#34;,
                    &#34;fieldValues&#34;: {
                        &#34;values&#34;: [
                            file_path
                        ]
                    }
                }
            ]
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._GET_PREVIEW_CONTENT, request_json)

        if flag:
            if &#34;Preview not available&#34; not in response.text:
                response =  response.json()
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;127&#39;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, self._update_response_(response.text))

        result = &#34;&#34;
        attachments = response[&#34;attachments&#34;]
        del response[&#34;attachments&#34;]
        for key in response:
            result += key + &#34; : &#34; + str(response[key]) + &#34;\n&#34;
        # If mail has attachments we add the content of the attachments
        if len (attachments) != 0:
            attachments_preview = self._get_attachment_preview(attachments, metadata)
            result += &#34;Attachments:\n&#34;
            for file in attachments_preview:
                result += file + &#34; : &#34; + attachments_preview[file] + &#34;\n&#34;
        return result

    @staticmethod
    def _get_ad_group_backup_common_opt_json(ad_group_name):
        &#34;&#34;&#34;
            Gets the common options json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Return:
                Common options for da group backup
        &#34;&#34;&#34;
        return {
            &#34;notifyUserOnJobCompletion&#34;: False,
            &#34;jobMetadata&#34;: [
                {
                    &#34;selectedItems&#34;: [
                        {
                            &#34;itemName&#34;: ad_group_name,
                            &#34;itemType&#34;: &#34;AD group&#34;
                        }
                    ],
                    &#34;jobOptionItems&#34;: [
                        {
                            &#34;option&#34;: &#34;Total running time&#34;,
                            &#34;value&#34;: &#34;Disabled&#34;
                        }
                    ]
                }
            ]
        }

    @staticmethod
    def _get_ad_group_backup_backupoptions_json(ad_group_name):
        &#34;&#34;&#34;
            Gets the backup options json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                Backup options json for ad group backup
        &#34;&#34;&#34;
        return {
            &#34;backupLevel&#34;: 2,
            &#34;incLevel&#34;: 1,
            &#34;exchOnePassOptions&#34;: {
                &#34;adGroups&#34;: [
                    {
                        &#34;adGroupName&#34;: ad_group_name
                    }
                ]
            },
            &#34;dataOpt&#34;: {
                &#34;useCatalogServer&#34;: False,
                &#34;followMountPoints&#34;: True,
                &#34;enforceTransactionLogUsage&#34;: False,
                &#34;skipConsistencyCheck&#34;: True,
                &#34;createNewIndex&#34;: False
            }
        }

    @staticmethod
    def _get_ad_group_backup_options_json(ad_group_name):
        &#34;&#34;&#34;
            Get options json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                options json for ad group backup
        &#34;&#34;&#34;
        return {
            &#34;backupOpts&#34;: ExchangeSubclient._get_ad_group_backup_backupoptions_json(ad_group_name),
            &#34;commonOpts&#34;: ExchangeSubclient._get_ad_group_backup_common_opt_json(ad_group_name)
        }

    @staticmethod
    def _get_ad_group_backup_subtask_json(ad_group_name):
        &#34;&#34;&#34;
            Gets the subtask json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                Sub task json for ad group backup
        &#34;&#34;&#34;
        sub_task_json = [{
            &#34;subTask&#34;: {
              &#34;subTaskType&#34;: 2,
              &#34;operationType&#34;: 2
            },
            &#34;options&#34;: ExchangeSubclient._get_ad_group_backup_options_json(ad_group_name)
        }]
        return sub_task_json

    def _get_ad_group_backup_task_json(self, ad_group_name):
        &#34;&#34;&#34;
            Gets the task json for ad group backup
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                Task json for ad group backup
        &#34;&#34;&#34;
        task_json = {
            &#34;associations&#34;: [self._subClientEntity],
            &#34;task&#34;: {&#34;taskType&#34;: 1},
            &#34;subTasks&#34;: ExchangeSubclient._get_ad_group_backup_subtask_json(ad_group_name)
        }
        return task_json
    
    def ad_group_backup(self, ad_group_name):
        &#34;&#34;&#34;
            Run backup of AD Group
            Args:
                ad_group_name(str):     Name of ad group
            Returns:
                Processed job
        &#34;&#34;&#34;
        task_json = self._get_ad_group_backup_task_json(ad_group_name)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient" href="exchange/contentstoremailbox_subclient.html#cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient">ContentStoreMailboxSubclient</a></li>
<li><a title="cvpysdk.subclients.exchange.journalmailbox_subclient.JournalMailboxSubclient" href="exchange/journalmailbox_subclient.html#cvpysdk.subclients.exchange.journalmailbox_subclient.JournalMailboxSubclient">JournalMailboxSubclient</a></li>
<li><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient" href="exchange/usermailbox_subclient.html#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient">UsermailboxSubclient</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.ad_group_backup"><code class="name flex">
<span>def <span class="ident">ad_group_backup</span></span>(<span>self, ad_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Run backup of AD Group</p>
<h2 id="args">Args</h2>
<p>ad_group_name(str):
Name of ad group</p>
<h2 id="returns">Returns</h2>
<p>Processed job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L1423-L1436" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def ad_group_backup(self, ad_group_name):
    &#34;&#34;&#34;
        Run backup of AD Group
        Args:
            ad_group_name(str):     Name of ad group
        Returns:
            Processed job
    &#34;&#34;&#34;
    task_json = self._get_ad_group_backup_task_json(ad_group_name)
    create_task = self._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, create_task, task_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.cleanup"><code class="name flex">
<span>def <span class="ident">cleanup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a cleanup job for the subclient .</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L638-L647" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def cleanup(self):
    &#34;&#34;&#34;Runs a cleanup job for the subclient .

        Returns:
            object - instance of the Job class for this backup job

    &#34;&#34;&#34;

    request_json = self._cleanup_json()
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.disk_restore"><code class="name flex">
<span>def <span class="ident">disk_restore</span></span>(<span>self, paths, destination_client, destination_path, overwrite=True, journal_report=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the mailboxes/folders specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of paths of mailboxes/folders to restore
destination_client
&ndash;
client where the mailboxes needs to be restored
destination_path
&ndash;
path where the mailboxes needs to be restored</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True
journal_report
(bool)
&ndash;
Journal report is true for journal and
contentStore Mailbox
default: False</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L792-L837" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disk_restore(
        self,
        paths,
        destination_client,
        destination_path,
        overwrite=True,
        journal_report=False):
    &#34;&#34;&#34;Restores the mailboxes/folders specified in the input paths list to the same location.

        Args:
            paths                   (list)  --  list of paths of mailboxes/folders to restore
            destination_client              --  client where the mailboxes needs to be restored
            destination_path                --  path where the mailboxes needs to be restored

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True
            journal_report          (bool)  --  Journal report is true for journal and
                                                contentStore Mailbox
                default: False

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    restore_option = {}
    if paths == []:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
    restore_option[&#39;journal_report&#39;] = journal_report
    restore_option[&#39;unconditional_overwrite&#39;] = overwrite
    restore_option[&#39;paths&#39;] = paths
    restore_option[&#39;client&#39;] = destination_client
    restore_option[&#39;destination_path&#39;] = destination_path

    request_json = self._prepare_disk_restore_json(restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_backup_opt_json"><code class="name flex">
<span>def <span class="ident">get_pst_backup_opt_json</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Get backup options json for pst ingestion job</p>
<h2 id="returns">Returns</h2>
<p>Pst backup options json</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L1020-L1043" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_pst_backup_opt_json(self):
    &#34;&#34;&#34;Get backup options json for pst ingestion job

        Returns:
             Pst backup options json
    &#34;&#34;&#34;
    backup_opt_json = {
        &#34;truncateLogsOnSource&#34;: False,
        &#34;sybaseSkipFullafterLogBkp&#34;: False,
        &#34;notSynthesizeFullFromPrevBackup&#34;: False,
        &#34;backupLevel&#34;: 2,
        &#34;incLevel&#34;: 1,
        &#34;adHocBackup&#34;: False,
        &#34;runIncrementalBackup&#34;: False,
        &#34;isSpHasInLineCopy&#34;: False,
        &#34;runSILOBackup&#34;: False,
        &#34;doNotTruncateLog&#34;: False,
        &#34;exchOnePassOptions&#34;: {
            &#34;proxies&#34;: {}
        },
        &#34;dataOpt&#34;: self.get_pst_data_opt_json(),
        &#34;mediaOpt&#34;: {}
    }
    return backup_opt_json</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_data_opt_json"><code class="name flex">
<span>def <span class="ident">get_pst_data_opt_json</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Get data options json for pst ingestion job</p>
<h2 id="returns">Returns</h2>
<p>Pst data options json</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L1045-L1059" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_pst_data_opt_json(self):
    &#34;&#34;&#34;Get data options json for pst ingestion job

        Returns:
             Pst data options json
    &#34;&#34;&#34;
    data_json = {
        &#34;skipCatalogPhaseForSnapBackup&#34;: True,
        &#34;useCatalogServer&#34;: False,
        &#34;followMountPoints&#34;: True,
        &#34;enforceTransactionLogUsage&#34;: False,
        &#34;skipConsistencyCheck&#34;: False,
        &#34;createNewIndex&#34;: False
    }
    return data_json</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_task_json"><code class="name flex">
<span>def <span class="ident">get_pst_task_json</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Get task json for pst ingestion job</p>
<h2 id="returns">Returns</h2>
<p>Pst task json</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L999-L1018" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_pst_task_json(self):
    &#34;&#34;&#34;Get task json for pst ingestion job

        Returns:
             Pst task json
    &#34;&#34;&#34;
    task_json = {
        &#34;ownerId&#34;: self._commcell_object.users.all_users[
            self._commcell_object.commcell_username],
        &#34;taskType&#34;: 1,
        &#34;ownerName&#34;: self._commcell_object.commcell_username,
        &#34;sequenceNumber&#34;: 0,
        &#34;initiatedFrom&#34;: 1,
        &#34;policyType&#34;: 0,
        &#34;taskId&#34;: 0,
        &#34;taskFlags&#34;: {
            &#34;isEZOperation&#34;: False, &#34;disabled&#34;: False
        }
    }
    return task_json</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.out_of_place_restore"><code class="name flex">
<span>def <span class="ident">out_of_place_restore</span></span>(<span>self, paths, destination_path, overwrite=True, journal_report=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the mailboxes/folders specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of paths of mailboxes/folders to restore
destination_client
&ndash;
client where the mailboxes needs to be restored
destination_path
&ndash;
path where the mailboxes needs to be restored</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True
journal_report
(bool)
&ndash;
Journal report is true for journal and
contentStore Mailbox
default: False</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L745-L790" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def out_of_place_restore(
        self,
        paths,
        destination_path,
        overwrite=True,
        journal_report=False):
    &#34;&#34;&#34;Restores the mailboxes/folders specified in the input paths list to the same location.

        Args:
            paths                   (list)  --  list of paths of mailboxes/folders to restore
            destination_client              --  client where the mailboxes needs to be restored
            destination_path                --  path where the mailboxes needs to be restored

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True
            journal_report          (bool)  --  Journal report is true for journal and
                                                contentStore Mailbox
                default: False

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    restore_option = {}
    if paths == []:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
    restore_option[&#39;journal_report&#39;] = journal_report
    restore_option[&#39;unconditional_overwrite&#39;] = overwrite
    restore_option[&#39;paths&#39;] = paths
    # restore_option[&#39;client&#39;] = destination_client
    restore_option[&#39;destination_path&#39;] = destination_path
    oop_target_mailbox = destination_path.split(chr(18))[0].split(&#34;\\MB\\&#34;)[1]
    restore_option[&#39;target_mailbox&#39;] = oop_target_mailbox
    request_json = self._prepare_out_of_place_restore_json(restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.preview_backedup_file"><code class="name flex">
<span>def <span class="ident">preview_backedup_file</span></span>(<span>self, file_path)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the preview content for the subclient.</p>
<h2 id="params">Params</h2>
<p>file_path (str)
&ndash;
path of the file for which preview is needed</p>
<h2 id="returns">Returns</h2>
<p>html
(str)
&ndash;
html content of the preview</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if file is not found</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L1196-L1317" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def preview_backedup_file(self, file_path):
    &#34;&#34;&#34;Gets the preview content for the subclient.

        Params:
            file_path (str)  --  path of the file for which preview is needed

        Returns:
            html   (str)   --  html content of the preview

        Raises:
            SDKException:
                if file is not found

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    self._GET_PREVIEW_CONTENT = self._services[&#39;GET_PREVIEW&#39;]
    metadata = self._get_preview_metadata(file_path)
    if metadata is None:
        raise SDKException(&#39;Subclient&#39;, &#39;123&#39;)

    if metadata[&#34;type&#34;] != &#34;File&#34;:
        raise SDKException(&#39;Subclient&#39;, &#39;124&#39;)

    if metadata[&#34;size&#34;] == 0:
        raise SDKException(&#39;Subclient&#39;, &#39;125&#39;)

    if metadata[&#34;size&#34;] &gt; 20 * 1024 * 1024:
        raise SDKException(&#39;Subclient&#39;, &#39;126&#39;)
    request_json = {
        &#34;filters&#34;: [
            {
                &#34;field&#34;: &#34;CLIENT_ID&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        str(self._get_client_dict(self._client_object)[&#34;client&#34;][&#34;clientId&#34;])
                    ]
                }
            },
            {
                &#34;field&#34;: &#34;SUBCLIENT_ID&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        str(metadata[&#34;advanced_data&#34;][&#34;subclient&#34;][&#34;applicationId&#34;])
                    ]
                }
            },
            {
                &#34;field&#34;: &#34;CONTENTID&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        str(metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;indexing&#34;][&#34;objectGUID&#34;])
                    ]
                }
            },
            {
                &#34;field&#34;: &#34;ARCHIVE_FILE_ID&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        str(metadata[&#34;advanced_data&#34;][&#34;archiveFileId&#34;])
                    ]
                }
            },
            {
                &#34;field&#34;: &#34;ARCHIVE_FILE_OFFSET&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        str(metadata[&#34;advanced_data&#34;][&#34;offset&#34;])
                    ]
                }
            },
            {
                &#34;field&#34;: &#34;COMMCELL_NUMBER&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        str(metadata[&#34;advanced_data&#34;][&#34;advConfig&#34;][&#34;browseAdvancedConfigResp&#34;][
                                        &#34;commcellNumber&#34;])
                    ]
                }
            },
            {
                &#34;field&#34;: &#34;ITEM_SIZE&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        str(metadata[&#34;size&#34;])
                    ]
                }
            },
            {
                &#34;field&#34;: &#34;ITEM_PATH&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        file_path
                    ]
                }
            }
        ]
    }
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._GET_PREVIEW_CONTENT, request_json)

    if flag:
        if &#34;Preview not available&#34; not in response.text:
            response =  response.json()
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;127&#39;)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, self._update_response_(response.text))

    result = &#34;&#34;
    attachments = response[&#34;attachments&#34;]
    del response[&#34;attachments&#34;]
    for key in response:
        result += key + &#34; : &#34; + str(response[key]) + &#34;\n&#34;
    # If mail has attachments we add the content of the attachments
    if len (attachments) != 0:
        attachments_preview = self._get_attachment_preview(attachments, metadata)
        result += &#34;Attachments:\n&#34;
        for file in attachments_preview:
            result += file + &#34; : &#34; + attachments_preview[file] + &#34;\n&#34;
    return result</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_ingestion"><code class="name flex">
<span>def <span class="ident">pst_ingestion</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a backup job for the subclient of the level specified.</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L884-L920" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def pst_ingestion(self):
    &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.
        Returns:
            object - instance of the Job class for this backup job

    &#34;&#34;&#34;

    payload_dict = {
        &#34;taskInfo&#34;:{
            &#34;associations&#34;:[self._subClientEntity],
            &#34;task&#34;: self.get_pst_task_json(),
            &#34;subTasks&#34;:[{
                &#34;subTaskOperation&#34;:1,
                &#34;subTask&#34;:{
                    &#34;subTaskType&#34;:2,
                    &#34;operationType&#34;:5024
                },
                &#34;options&#34;:{
                    &#34;backupOpts&#34;:self.get_pst_backup_opt_json(),
                    &#34;adminOpts&#34;:{
                        &#34;contentIndexingOption&#34;:{
                            &#34;subClientBasedAnalytics&#34;:False
                        }
                    },
                    &#34;restoreOptions&#34;:{
                        &#34;virtualServerRstOption&#34;:{
                            &#34;isBlockLevelReplication&#34;:False
                        }
                    }
                }
            }]
        }
    }
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._commcell_object._services[&#34;RESTORE&#34;], payload_dict)

    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_restore"><code class="name flex">
<span>def <span class="ident">pst_restore</span></span>(<span>self, paths, destination_client, pst_path, overwrite=True, journal_report=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the Mailbox/Emails specified in the input paths list to the PST PATH location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of paths of mailboxes/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True
journal_report
(bool)
&ndash;
Journal report is true for journal and
contentStore Mailbox
default: False</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L839-L882" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def pst_restore(
        self,
        paths,
        destination_client,
        pst_path,
        overwrite=True,
        journal_report=False):
    &#34;&#34;&#34;Restores the Mailbox/Emails specified in the input paths list to the PST PATH location.

        Args:
            paths                   (list)  --  list of paths of mailboxes/folders to restore

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True
            journal_report          (bool)  --  Journal report is true for journal and
                                                contentStore Mailbox
                default: False

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    restore_option = {}
    if paths == []:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
    restore_option[&#39;journal_report&#39;] = journal_report
    restore_option[&#39;unconditional_overwrite&#39;] = overwrite
    restore_option[&#39;paths&#39;] = paths
    restore_option[&#39;client&#39;] = destination_client
    restore_option[&#39;destination_path&#39;] = pst_path

    request_json = self._prepare_pst_restore_json(restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, paths, overwrite=True, journal_report=False, restore_as_stub=None, recovery_point_id=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the mailboxes/folders specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of paths of mailboxes/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True
journal_report
(bool)
&ndash;
Journal report is true for journal and
contentStore Mailbox
default: False</p>
<p>restore_as_stub
(dict)
&ndash;
setters for common options</p>
<p>recovery_point_id
(int)
&ndash;
ID of the recovery point to which the mailbox is to be restored to
Default: None</p>
<p>**kwargs:
Expected:
stub_rehydration
(dict)
&ndash;
stub rules to rehydrate items during restore
Default: None</p>
<p>filters
(dict)
&ndash;
filter values for find and restore
Default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L649-L743" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        paths,
        overwrite=True,
        journal_report=False,
        restore_as_stub=None,
        recovery_point_id=None,
        **kwargs):
    &#34;&#34;&#34;Restores the mailboxes/folders specified in the input paths list to the same location.

        Args:
            paths                   (list)  --  list of paths of mailboxes/folders to restore

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True
            journal_report          (bool)  --  Journal report is true for journal and
                                                    contentStore Mailbox
                default: False

            restore_as_stub         (dict)  --  setters for common options

            recovery_point_id       (int)   --  ID of the recovery point to which the mailbox is to be restored to
                Default: None

            **kwargs:
            Expected:
            stub_rehydration        (dict)  --  stub rules to rehydrate items during restore
                Default: None

            filters                  (dict)  --  filter values for find and restore
                Default: None

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    restore_option = {}
    if paths == []:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
    restore_option[&#39;journal_report&#39;] = journal_report

    paths = self._filter_paths(paths)
    self._json_restore_exchange_restore_option(restore_option)
    self._json_backupset()
    restore_option[&#39;unconditional_overwrite&#39;] = overwrite
    restore_option[&#39;paths&#39;] = paths

    request_json = self._restore_json(restore_option=restore_option)
    request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = self.subclient_name
    request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][
        &#39;backupsetName&#39;] = self._backupset_object.backupset_name
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
        &#39;options&#39;][&#39;restoreOptions&#39;][&#39;exchangeOption&#39;] = self._exchange_option_restore_json
    if restore_as_stub:
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;commonOptions&#39;] = self._json_restore_exchange_common_option(restore_as_stub)

    stub_rehydration = kwargs.get(&#34;stub_rehydration&#34;)
    if stub_rehydration:
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;commonOpts&#39;][
            &#39;jobOptionItems&#39;] = self._json_job_option_items(stub_rehydration)
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][&#39;exchangeOption&#39;] = self._exchange_option_restore_json_rehydration(stub_rehydration)

    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
        &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

    if recovery_point_id is not None:
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#39;exchangeOption&#39;][&#34;recoveryPointId&#34;] = recovery_point_id

    filters = kwargs.get(&#34;filters&#34;)
    if filters:
        if (filters.get(&#34;restore_all_matching&#34;)):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#34;exchangeOption&#34;][&#34;restoreAllMatching&#34;] = filters.get(&#34;restore_all_matching&#34;,
                                                                                       False)
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#34;fileOption&#34;][&#34;browseFilters&#34;] = [self._browse_filter_xml(filters)]
        if (filters.get(&#34;restore_selected_items&#34;)):
            req = self._request_json_search_in_restore(filters)
            result = self._process_search_response(req)
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#34;fileOption&#34;][&#34;sourceItem&#34;] = result

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_content_indexing"><code class="name flex">
<span>def <span class="ident">subclient_content_indexing</span></span>(<span>self, pick_failed_items=False, pick_only_failed_items=False, streams=4, proxies=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Run content Indexing on Subclient .</p>
<h2 id="args">Args</h2>
<p>pick_failed_items
default:False
(bool)
&ndash;
Pick fail items during Content Indexing</p>
<p>pick_only_failed_items
(bool)
&ndash;
Pick only fail items items during Content
Indeixng
default: False</p>
<p>streams
(int)
&ndash;
Streams for Content Indexing job</p>
<pre><code> default: 4
</code></pre>
<p>proxies
(list) &ndash;
provide the proxies to run CI
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this ContentIndexing job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchsubclient.py#L922-L997" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def subclient_content_indexing(self,
                               pick_failed_items=False,
                               pick_only_failed_items=False,
                               streams=4,
                               proxies=None):
    &#34;&#34;&#34;Run content Indexing on Subclient .

        Args:
           pick_failed_items
                    default:False   (bool)  --  Pick fail items during Content Indexing

            pick_only_failed_items  (bool)  --  Pick only fail items items during Content
                                                Indeixng
                default: False

            streams                 (int)   --  Streams for Content Indexing job

                default: 4

            proxies                 (list) --  provide the proxies to run CI
                default: None

        Returns:
            object - instance of the Job class for this ContentIndexing job

    &#34;&#34;&#34;
    # check if inputs are correct

    ci_option = {}
    if not (isinstance(pick_failed_items, bool) and
            isinstance(pick_only_failed_items, bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    ci_option[&#39;pick_failed_items&#39;] = pick_failed_items
    ci_option[&#39;pick_only_failed_items&#39;] = pick_only_failed_items
    ci_option[&#39;streams&#39;] = streams
    if proxies is None:
        ci_option[&#39;proxies&#39;] = {}
    else:
        member_servers = self._member_servers(proxies)
        ci_option[&#39;proxies&#39;] = {
            &#34;memberServers&#34;: member_servers
        }

    self._media_option_json(ci_option)
    self._content_indexing_option_json()
    request_json = {
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [self._subClientEntity],
            &#34;task&#34;: self._json_task,
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: self._json_content_indexing_subtasks,
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {
                            &#34;mediaOpt&#34;: self._media_option_json_
                        },
                        &#34;adminOpts&#34;: {
                            &#34;contentIndexingOption&#34;: self._content_indexing_option_json_
                        },
                        &#34;restoreOptions&#34;: {
                            &#34;virtualServerRstOption&#34;: {
                                &#34;isBlockLevelReplication&#34;: False
                            },
                            &#34;browseOption&#34;: {
                                &#34;backupset&#34;: {}
                            }
                        }
                    }
                }
            ]
        }
    }

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclient.Subclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.backup" href="../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient">ExchangeSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.ad_group_backup" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.ad_group_backup">ad_group_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.cleanup" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.cleanup">cleanup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.disk_restore" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.disk_restore">disk_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_backup_opt_json" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_backup_opt_json">get_pst_backup_opt_json</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_data_opt_json" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_data_opt_json">get_pst_data_opt_json</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_task_json" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_task_json">get_pst_task_json</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.out_of_place_restore" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.out_of_place_restore">out_of_place_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.preview_backedup_file" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.preview_backedup_file">preview_backedup_file</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_ingestion" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_ingestion">pst_ingestion</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_restore" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_restore">pst_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.restore_in_place" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_content_indexing" href="#cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_content_indexing">subclient_content_indexing</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>