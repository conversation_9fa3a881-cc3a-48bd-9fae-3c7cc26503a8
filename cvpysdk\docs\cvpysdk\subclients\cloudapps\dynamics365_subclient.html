<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.cloudapps.dynamics365_subclient API documentation</title>
<meta name="description" content="File for operating on a Dynamics 365 CRM Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.cloudapps.dynamics365_subclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Dynamics 365 CRM Subclient.</p>
<p>MSDynamics365Subclient is the only class defined in this file.</p>
<p>MSDynamics365Subclient:
Derived class from O365AppsSubclient Base class, representing a
Dynamics 365 subclient, and to perform operations on that subclient</p>
<h2 id="msdynamics365subclient">Msdynamics365Subclient</h2>
<p><strong><em>*</em></strong><strong><em>*</em></strong><strong><em>
Methods
</em></strong><strong><em>*</em></strong><strong><em>*</em></strong></p>
<p>_get_subclient_properties()
&ndash;
Gets the subclient related properties of a MS Dynamics 365 subclient</p>
<p>_get_subclient_properties_json()
&ndash;
get the all subclient related properties of this subclient.</p>
<p>get_discovered_tables()
&ndash;
Method to get the tables discovered from the MS Dynamics 365 CRM subclient
get_discovered_environments()
&ndash;
Method to get the environments discovered from the Dynamics 365 CRM subclient
_get_associated_content()
&ndash;
Method to get the content associated with a Dynamics 365 CRM subclient
get_associated_tables()
&ndash;
Method to get the tables associated with a Dynamics 365 CRM client
get_associated_environments()
&ndash;
Method to get the environments associated with a Dynamics 365 CRM client
_set_association_json()
&ndash;
JSON to set the content association for a Dynamics 365 CRM client
_set_content_association()
&ndash;
Method to associate some content to a Dynamics 365 CRM client&hellip;
_table_association_info_json()
&ndash;
Private Method to create the association JSON for associating tables
to a Dynamics 365 CRM client.
set_table_associations()
&ndash;
Method to add table associations to a Dynamics 365 CRM client.
_environment_association_info_json()
&ndash;
Method to create the association JSON for associating environments
to a Dynamics 365 CRM client.
set_environment_associations()
&ndash;
Method to add environment associations to a Dynamics 365 CRM client.
_json_for_backup_task()
&ndash;
Method to create the association JSON for backing up content for a Dynamics 365 subclient
_backup_content_json()
&ndash;
Method to fetch the metadata properties for backing up content for a Dynamics 365 subclient
_run_backup()
&ndash;
Method to run backup for the content of a Dynamics 365 subclient
backup_tables()
&ndash;
Method to run backup for the specified tables of a Dynamics 365 subclient
backup_environments()
&ndash;
Method to run backup for the specified environments of a Dynamics 365 subclient
launch_client_level_full_backup()
&ndash;
Method to run client level full backup for the content of a Dynamics 365 subclient
_restore_content_json()
&ndash;
Restore JSON for restoring content for a Dynamics 365 subclient
_get_restore_item_path()
&ndash;
Get the complete path of the content for running a restore job
_prepare_restore_json()
&ndash;
Method to prepare JSON/ Python dict for
in- place restore for the content specified.
restore_in_place()
&ndash;
Method to run in- place restore for the content specified.
launch_d365_licensing()
&ndash;
Method to launch Licensing API call.
_get_environment_id_for_oop_restore()
&ndash;
Get the Environment ID for an environment for Out of Place Restore
restore_out_of_place()
&ndash;
Method to run out-of-place restore for the content specified.
browse()
&ndash;
Browse for the backed up content for a Dynamics 365 subclient
_get_guid_for_path()
&ndash;
Method to get the browse GUID corresponding to the path
_perform_browse()
&ndash;
Perform a browse of the backed up content
_get_dynamics365_browse_params()
&ndash;
Default dictionary for the browse parameters for a Dynamics 365 browse query.</p>
<p><strong><em>*</em></strong><strong><em>*</em></strong><strong><em>
Properties
</em></strong><strong><em>*</em></strong><strong><em>*</em></strong></p>
<p>discovered_environments
&ndash;
Property to get the tables discovered by the Dynamics 365 subclient.
discovered_tables
&ndash;
Dictionary of tables discovered by the subclient
browse_item_type()
&ndash;
Dynamics 365 item type</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L1-L1222" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
    File for operating on a Dynamics 365 CRM Subclient.

MSDynamics365Subclient is the only class defined in this file.

MSDynamics365Subclient:             Derived class from O365AppsSubclient Base class, representing a
                                    Dynamics 365 subclient, and to perform operations on that subclient

MSDynamics365Subclient:

    *****************                       Methods                      *****************

    _get_subclient_properties()             --  Gets the subclient related properties of a MS Dynamics 365 subclient

    _get_subclient_properties_json()        --  get the all subclient related properties of this subclient.

    get_discovered_tables()                 --  Method to get the tables discovered from the MS Dynamics 365 CRM subclient
    get_discovered_environments()           --  Method to get the environments discovered from the Dynamics 365 CRM subclient
    _get_associated_content()               --  Method to get the content associated with a Dynamics 365 CRM subclient
    get_associated_tables()                 --  Method to get the tables associated with a Dynamics 365 CRM client
    get_associated_environments()           --  Method to get the environments associated with a Dynamics 365 CRM client
    _set_association_json()                 --  JSON to set the content association for a Dynamics 365 CRM client
    _set_content_association()              --  Method to associate some content to a Dynamics 365 CRM client...
    _table_association_info_json()          --  Private Method to create the association JSON for associating tables
                                                to a Dynamics 365 CRM client.
    set_table_associations()                --  Method to add table associations to a Dynamics 365 CRM client.
    _environment_association_info_json()    --  Method to create the association JSON for associating environments
                                                to a Dynamics 365 CRM client.
    set_environment_associations()          --  Method to add environment associations to a Dynamics 365 CRM client.
    _json_for_backup_task()                 --  Method to create the association JSON for backing up content for a Dynamics 365 subclient
    _backup_content_json()                  --  Method to fetch the metadata properties for backing up content for a Dynamics 365 subclient
    _run_backup()                           --  Method to run backup for the content of a Dynamics 365 subclient
    backup_tables()                         --  Method to run backup for the specified tables of a Dynamics 365 subclient
    backup_environments()                   --  Method to run backup for the specified environments of a Dynamics 365 subclient
    launch_client_level_full_backup()     --  Method to run client level full backup for the content of a Dynamics 365 subclient
    _restore_content_json()                 --  Restore JSON for restoring content for a Dynamics 365 subclient
    _get_restore_item_path()                --  Get the complete path of the content for running a restore job
    _prepare_restore_json()                 --  Method to prepare JSON/ Python dict for  in- place restore for the content specified.
    restore_in_place()                      --  Method to run in- place restore for the content specified.
    launch_d365_licensing()                 --  Method to launch Licensing API call.
    _get_environment_id_for_oop_restore()   --  Get the Environment ID for an environment for Out of Place Restore
    restore_out_of_place()                  --  Method to run out-of-place restore for the content specified.
    browse()                                --  Browse for the backed up content for a Dynamics 365 subclient
    _get_guid_for_path()                    --  Method to get the browse GUID corresponding to the path
    _perform_browse()                       --  Perform a browse of the backed up content
    _get_dynamics365_browse_params()        --  Default dictionary for the browse parameters for a Dynamics 365 browse query.


    *****************                       Properties                      *****************

    discovered_environments                 --  Property to get the tables discovered by the Dynamics 365 subclient.
    discovered_tables                       --  Dictionary of tables discovered by the subclient
    browse_item_type()                      --  Dynamics 365 item type

&#34;&#34;&#34;

import copy
import json
from ...exception import SDKException

from ..o365apps_subclient import O365AppsSubclient
from ..casubclient import CloudAppsSubclient


class MSDynamics365Subclient(O365AppsSubclient):
    &#34;&#34;&#34;
        Class representing a MS Dynamics 365 subclient.
            Class has been derived from the O365AppsSubclient.
    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Subclient object for the given MSDynamics365 Subclient.

            Args:
                backupset_object    (object)    --  instance of the backup-set class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(MSDynamics365Subclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

        self._instance_object = backupset_object._instance_object
        self._client_object = self._instance_object._agent_object._client_object
        self._associated_tables: dict = dict()
        self._associated_environments: dict = dict()
        self._discovered_environments: dict = dict()
        self._discovered_tables: dict = dict()
        self._instance_type: int = 35
        self._app_id: int = 134
        # App ID for cloud apps
        self._Dynamics365_SET_USER_POLICY_ASSOCIATION = self._commcell_object._services[&#39;SET_USER_POLICY_ASSOCIATION&#39;]

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient related properties of a MS Dynamics 365 subclient&#34;&#34;&#34;
        super(MSDynamics365Subclient, self)._get_subclient_properties()

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;

        return {&#39;subClientProperties&#39;: self._subclient_properties}

    def discover_tables(self):
        &#34;&#34;&#34;
            Method to get the tables discovered from the MS Dynamics 365 CRM subclient

            Returns:
                discovered_tables       (dict)--    Dictionary of returned tables

        &#34;&#34;&#34;
        self._discovered_tables = self._instance_object.discover_content(environment_discovery=False)
        return self._discovered_tables

    def discover_environments(self):
        &#34;&#34;&#34;
            Method to get the environments discovered from the Dynamics 365 CRM subclient

            Returns:
                discovered_environments       (dict)--    Dictionary of discovered environments

        &#34;&#34;&#34;
        self._discovered_environments = self._instance_object.discover_content(environment_discovery=True)
        return self._discovered_environments

    @property
    def discovered_environments(self):
        &#34;&#34;&#34;
            Property to get the environments discovered by the Dynamics 365 subclient.

            If updated list is required, call refresh method prior to using this property.

            Returns:
                discovered_environments       (dict)--    Dictionary of discovered environments
        &#34;&#34;&#34;
        if not bool(self._discovered_environments):
            self.discover_environments()
        return self._discovered_environments

    @property
    def discovered_tables(self):
        &#34;&#34;&#34;
            Property to get the tables discovered by the Dynamics 365 subclient.

            If updated list is required, call refresh method prior to using this property.

            Returns:
                discovered_tables      (dict)--    Dictionary of discovered tables
        &#34;&#34;&#34;
        if not bool(self._discovered_tables):
            self.discover_tables()
        return self._discovered_tables

    def _get_associated_content(self, is_environment=False):
        &#34;&#34;&#34;
            Method to get the content associated with a Dynamics 365 CRM subclient

            Arguments:
                is_environment      (bool)--    Whether to get the associated environments or tables
                    Default Value:
                        False
                            Returns the associated tables

            Returns:
                associated_content_list     (list)--    List of content associated with the client
                    Format:
                        Each list element will be a dictionary denoting that particular environment/ table
        &#34;&#34;&#34;
        discover_by_type: int
        if is_environment is True:
            discover_by_type = 5
        else:
            discover_by_type = 14

        _GET_ASSOCIATED_CONTENT = self._services[&#39;USER_POLICY_ASSOCIATION&#39;]
        request_json = {
            &#34;discoverByType&#34;: discover_by_type,
            &#34;bIncludeDeleted&#34;: False,
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                }
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, _GET_ASSOCIATED_CONTENT, request_json
        )
        if flag:
            if response and response.json():
                no_of_records = int()
                if &#39;associations&#39; in response.json():
                    no_of_records = response.json().get(&#39;associations&#39;, [])[0].get(&#39;pagingInfo&#39;, {}). \
                        get(&#39;totalRecords&#39;, -1)

                    associations = response.json().get(&#39;associations&#39;, [])
                    content_list = list()
                    if discover_by_type == 5:
                        for environment in associations:
                            environment_name = environment.get(&#34;groups&#34;, {}).get(&#34;name&#34;)
                            env_dict = {
                                &#34;name&#34;: environment_name,
                                &#34;id&#34;: environment.get(&#34;groups&#34;, {}).get(&#34;id&#34;),
                                &#34;userAccountInfo&#34;: environment.get(&#34;userAccountInfo&#34;, {}),
                                &#34;plan&#34;: environment.get(&#34;plan&#34;, {}),
                                &#34;is_environment&#34;: True
                            }
                            content_list.append(env_dict)

                    elif discover_by_type == 14:
                        for table in associations:
                            table_name = table.get(&#34;userAccountInfo&#34;, {}).get(&#34;displayName&#34;)
                            table_dict = {
                                &#34;name&#34;: table_name,
                                &#34;environment_name&#34;: table.get(&#34;userAccountInfo&#34;, {}).get(&#34;ParentWebGuid&#34;, &#34;&#34;),
                                &#34;userAccountInfo&#34;: table.get(&#34;userAccountInfo&#34;, {}),
                                &#34;plan&#34;: table.get(&#34;plan&#34;, {}),
                                &#34;is_environment&#34;: False
                            }
                            content_list.append(table_dict)
                    return content_list
                    # return content_list, no_of_records
            return {}, 0
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_associated_tables(self, refresh: bool = False):
        &#34;&#34;&#34;
            Method to get the tables associated with a Dynamics 365 CRM client

            Arguments:
                refresh                     (bool)--    Whether to refresh the dictionary contents
                    If True
                        get associated environments, will fetch the latest associations and return them

            Returns:
                associated_tables     (list)--    List of tables associated with the client
                    Format:
                        Each list element will be a dictionary denoting that particular  table
                        Dictionary keys/ format will be:
                            name : name of the table
                            environment_name : name of the environment to which the table belongs to
                            plan: Dynamics 365 plan used for content association
                            is_environment: False for a Table
                            userAccountInfo:    Metadata info for that table

                    Sample Response:
                        {
                            &#39;name&#39;: &#39;account&#39;,
                            &#39;environment_name&#39;: &#39;sample-environment-name&#39;,
                            &#39;userAccountInfo&#39;:
                                {
                                &#39;aliasName&#39;: &#39;https://&lt;org-url-name&gt;.crm.dynamics.com/api/data/v9.1/account&#39;,
                                &#39;displayName&#39;: &#39;Account,
                                &#39;ParentWebGuid&#39;: &#39;org-environment-name&#39;,
                                &#39;lastBackupJobRanTime&#39;: {&#39;time&#39;: &lt;last-backup-time&gt;},
                                &#39;IdxCollectionTime&#39;: {&#39;time&#39;: &lt;last-index-time&gt;},
                                &#39;user&#39;: {
                                    &#39;_type_&#39;: 13,
                                     &#39;userGUID&#39;: &#39;&lt;table-GUID&gt;&gt;&#39;
                                     }
                                },
                            &#39;plan&#39;: {
                                &#39;planName&#39;: &#39;&lt;PLAN-NAME&gt;&#39;, &#39;planId&#39;: &lt;plan-id&gt;},
                            &#39;is_environment&#39;: False
                        }
                    Environment name/ URL in the sample response is for description purpose only

        &#34;&#34;&#34;
        if refresh is True:
            self._associated_tables = self._get_associated_content(is_environment=False)
        return self._associated_tables

    def get_associated_environments(self, refresh: bool = False):
        &#34;&#34;&#34;
            Method to get the environments associated with a Dynamics 365 CRM client

            Arguments:
                refresh                     (bool)--    Whether to refresh the dictionary contents
                    If True
                        get associated environments, will fetch the latest associations and return them

            Returns:
                associated_environments     (list)--    List of environments associated with the client
                    Format:
                        Each list element will be a dictionary denoting that particular environment
                        Dictionary keys/ format will be:
                            name :              name of the table
                            plan:               Dynamics 365 plan used for content association
                            is_environment:     True for an environment
                            userAccountInfo:    Metadata info for that environment

                    Sample Response:
                        {
                                &#39;name&#39;: &#39;sample-environment-name&#39;,
                                &#39;id&#39;: &#39;&lt;environment-ID&gt;&gt;&#39;,
                                &#39;userAccountInfo&#39;:
                                {
                                    &#39;aliasName&#39;: &#39;https://&lt;org-url-name&gt;.crm.dynamics.com&#39;,
                                    &#39;itemType&#39;: 0,
                                    &#39;ItemClassification&#39;: 0,
                                    &#39;displayName&#39;: &#39;org-environment-display-name&#39;,
                                    &#39;BackupSetId&#39;: 0,
                                    &#39;isAutoDiscoveredUser&#39;: False,
                                    &#39;lastBackupJobRanTime&#39;: &#39;time&#39;: &lt;last-backup-time&gt;,
                                    &#39;IdxCollectionTime&#39;: {&#39;time&#39;: &lt;last-index-playback-time&gt;},
                                    user&#39;: {
                                        &#39;_type_&#39;: 13,
                                        &#39;userGUID&#39;: &#39;&lt;env-GUID&gt;&#39;
                                        }
                                },
                                &#39;plan&#39;: {&#39;planName&#39;: &#39;&lt;name-of-plan&gt;&#39;, &#39;planId&#39;: &lt;id-of-plan&gt;},
                                 &#39;is_environment&#39;: True}

        &#34;&#34;&#34;
        if refresh is True:
            self._associated_environments = self._get_associated_content(is_environment=True)
        return self._associated_environments

    def _set_association_json(self, is_environment: bool = False):
        &#34;&#34;&#34;
            JSON to set the content association for a Dynamics 365 CRM client

            Arguments:
                is_environment      (bool):     Whether the content to be associated is an environment
                    Default Value:
                        False
            Returns:
                set_content_association_json    (dict)--    Content Association JSON
        &#34;&#34;&#34;
        set_content_association_json = {
                                            &#34;LaunchAutoDiscovery&#34;: is_environment,
                                            &#34;cloudAppAssociation&#34;: {
                                            &#34;accountStatus&#34;: 0,
                                            &#34;cloudAppDiscoverinfo&#34;: {
                                                &#34;userAccounts&#34;: [
                                                ],
                                                &#34;groups&#34;: [],
                                                &#34;discoverByType&#34;: 14 if is_environment is False else 15
                                            },
                                            &#34;subclientEntity&#34;: self._subClientEntity
                                            }
                                        }

        return set_content_association_json

    def _set_content_association(self, content_json: dict):
        &#34;&#34;&#34;
            Method to associate some content to a Dynamics 365 CRM client...

            Arguments:
                content_json        (dict)--        Association JSON to be used for the content
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._Dynamics365_SET_USER_POLICY_ASSOCIATION, content_json
        )

        if flag:
            try:
                if response.json():
                    if response.json().get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to Create Association for a Dynamics 365 CRM client\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message)
                        )
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _table_association_info_json(self, tables_list: list):
        &#34;&#34;&#34;
            Private Method to create the association JSON for associating tables
            to a Dynamics 365 CRM client.

            Arguments:
                tables_list     (list)--    List of tables to be associated to the content
                    List Format:
                        Each list element should be a tuple of the format:
                            (&#34;table_name&#34;, &#34;environment_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be associated

            Returns:
                tables_info     (list)--    List of metadata info for the tables to be used for associating content
        &#34;&#34;&#34;
        tables_info: list = list()
        _discovered_tables = self.discovered_tables
        tables_dict = {}

        if not bool(_discovered_tables):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                               &#34;Discovered Tables is Empty.&#34;)

        for table in _discovered_tables:
            if table[&#34;ParentWebGuid&#34;] in tables_dict:
                tables_dict[table[&#34;ParentWebGuid&#34;]].update({table[&#34;displayName&#34;]: table})
            else:
                tables_dict.update({table[&#34;ParentWebGuid&#34;]: {}})

        for table in tables_list:
            table_name, env_name = table
            if env_name in tables_dict:
                if table_name in tables_dict[env_name]:
                    tables_info.append(tables_dict[env_name][table_name])
                else:
                    raise SDKException(&#34;Subclient&#34;, &#34;101&#34;,
                                       &#34;Table {} not found in the environment {}&#34;.format(table_name, env_name))
            else:
                raise SDKException(&#34;Subclient&#34;, &#34;101&#34;,
                                   &#34;Environment {} not found in the list of discovered environments&#34;.format(env_name))

        if len(tables_info) != len(tables_list):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;, &#34;All of the input tables were in the list of discovered tables&#34;)

        return tables_info

    def set_table_associations(self, tables_list: list, plan_name: str = str()):
        &#34;&#34;&#34;
            Method to add table associations
            to a Dynamics 365 CRM client.

            Arguments:
                tables_list     (list)--    List of tables to be associated to the content
                    List Format:
                        Each list element should be a tuple of the format:
                            (&#34;table_name&#34;, &#34;environment_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be associated
                    Sample input:
                        [ (&#34;account&#34;, &#34;testenv1&#34;) , (&#34;note&#34;, &#34;testenv2&#34;) , (&#34;attachments&#34;, &#34;testenv1&#34;)]

                plan_name       (str)--     Name of the Dynamics 365 Plan to be used for content association
        &#34;&#34;&#34;

        plan_id = int(self._commcell_object.plans[plan_name.lower()])

        tables_info = self._table_association_info_json(tables_list=tables_list)

        _table_association_json = self._set_association_json(is_environment=False)
        _table_association_json[&#34;cloudAppAssociation&#34;][&#34;plan&#34;] = {
            &#34;planId&#34;: plan_id
        }
        _table_association_json[&#34;cloudAppAssociation&#34;][&#34;cloudAppDiscoverinfo&#34;][&#34;userAccounts&#34;] = tables_info
        self._set_content_association(content_json=_table_association_json)

    def _environment_association_info_json(self, environments_name: list):
        &#34;&#34;&#34;
            Method to create the association JSON for associating environments
            to a Dynamics 365 CRM client.

            Arguments:
                environments_name     (list)--    List of environments to be associated to the content
                    List Format:
                        Each list element should be a string of the name of the environment

            Returns:
                environments_info     (list)--    List of metadata info for the environments to
                                                    be used for associating content
        &#34;&#34;&#34;
        environments_info: list = list()
        _discovered_envs = self.discovered_environments

        if not bool(_discovered_envs):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                               &#34;Discovered Environments List is Empty&#34;)

        for environment in _discovered_envs:
            if environment[&#34;displayName&#34;] in environments_name:
                _env_assoc_info = environment
                _env_assoc_info[&#34;user.userGUID&#34;] = environment.get(&#34;user&#34;).get(&#34;userGUID&#34;)
                _env_assoc_info[&#34;rawCommonFlag&#34;] = environment.get(&#34;commonFlags&#34;, 0)

                environments_info.append(_env_assoc_info)

        if len(environments_info) == 0:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;,
                               &#34;None of the input environments were in the list of discovered environments&#34;)

        return environments_info

    def set_environment_associations(self, environments_name: list, plan_name: str = str()):
        &#34;&#34;&#34;
            Method to add environment associations
            to a Dynamics 365 CRM client.

            Arguments:
                environments_name     (list)--    List of environments to be associated to the content
                    List Format:
                        Each list element should be a string of the name of the environment
                    Sample Values:
                        [&#39;testenv1&#39; , &#39;testenv2&#39;, &#39;testenv3&#39;]

                plan_name       (str)--     Name of the Dynamics 365 Plan to be used for content association
        &#34;&#34;&#34;
        environments_info: list = self._environment_association_info_json(environments_name=environments_name)

        if self._commcell_object.plans.has_plan(plan_name.lower()):
            plan_id = int(self._commcell_object.plans[plan_name.lower()])

        else:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;,
                               &#34;Dynamics 365 Plan does not exist&#34;)

        _env_association_json = self._set_association_json(is_environment=True)
        _env_association_json[&#34;cloudAppAssociation&#34;][&#34;plan&#34;] = {
            &#34;planId&#34;: plan_id
        }
        _env_association_json[&#34;cloudAppAssociation&#34;][&#34;cloudAppDiscoverinfo&#34;][&#34;userAccounts&#34;] = environments_info
        self._set_content_association(content_json=_env_association_json)

    def _json_for_backup_task(self, content_list: list, is_environment: bool = False, force_full_backup: bool = False):
        &#34;&#34;&#34;
            Method to create the association JSON for backing up content for a Dynamics 365 subclient

            Arguments:
                content_list     (list)--    List of content to be backed up
                    List Format, if content to be backed up is tables:
                        Each list element should be a tuple of the format:
                            (&#34;environment_name&#34;,&#34;table_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be backed up
                    List Format, if content to be associated is environments:
                        Each list element should be a string of the name of the environment

                is_environment  (bool)--    Content passed to be backed up is environment type content or table type
                force_full_backup (bool) -- If True, will force a full backup of the content

            Returns:
                _backup_task_json     (list)--    JSON for backing up the content
        &#34;&#34;&#34;
        _backup_task_json = self._backup_json(&#39;Full&#39;, False, &#39;&#39;)
        backup_options = {
            &#39;backupLevel&#39;: 2,  # Incremental
            &#39;cloudAppOptions&#39;: {
            }
        }

        if len(content_list) &gt; 0:
            _sub_client_content_json = self._backup_content_json(content_list=content_list, is_environment=is_environment)
            backup_options[&#39;cloudAppOptions&#39;][&#39;userAccounts&#39;] = _sub_client_content_json

        if force_full_backup is True:
            backup_options[&#39;cloudAppOptions&#39;][&#39;forceFullBackup&#39;] = True

        _backup_task_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;] = backup_options
        return _backup_task_json

    def _backup_content_json(self, content_list: list, is_environment: bool = False):
        &#34;&#34;&#34;
            Method to fetch the metadata properties for backing up content for a Dynamics 365 subclient

            Arguments:
                content_list     (list)--    List of content to be backed up
                    List Format, if content to be backed up is tables:
                        Each list element should be a tuple of the format:
                            (&#34;environment_name&#34;,&#34;table_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be backed up
                    List Format, if content to be associated is environments:
                        Each list element should be a string of the name of the environment

                is_environment  (bool)--    Content passed to be backed up is environment type content or table type

            Returns:
                _bkp_content_json     (list)--    Metadata JSON for backing up that content
        &#34;&#34;&#34;
        _bkp_content_json = list()

        if is_environment is True:
            for environment in self.get_associated_environments(refresh=True):
                if environment[&#34;name&#34;] in content_list:
                    _env_bkp_info = environment[&#34;userAccountInfo&#34;]
                    _bkp_content_json.append(_env_bkp_info)

        elif is_environment is False:
            for _table in self.get_associated_tables(refresh=True):
                _table_name, _parent_env_name = _table[&#34;name&#34;].lower(), _table[&#34;environment_name&#34;].lower()
                try:
                    if (_parent_env_name, _table_name) in content_list:
                        _table_bkp_info = _table[&#34;userAccountInfo&#34;]
                        _bkp_content_json.append(_table_bkp_info)
                except TypeError:
                    raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                                       &#34;For backing up tables, content list should be a list of tuples&#34;)

        return _bkp_content_json

    def _run_backup(self, backup_content: list, is_environment: bool = False, force_full_backup: bool = False):
        &#34;&#34;&#34;
            Method to run backup for the content of a Dynamics 365 subclient

            Arguments:
                backup_content     (list)--    List of content to be backed up
                    List Format, if content to be backed up is tables:
                        Each list element should be a tuple of the format:
                            (&#34;environment_name&#34;,&#34;table_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be backed up
                    List Format, if content to be associated is environments:
                        Each list element should be a string of the name of the environment

                is_environment  (bool)--    Content passed to be backed up is environment type content or table type

            Returns:
                backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
        &#34;&#34;&#34;
        _backup_json = self._json_for_backup_task(content_list=backup_content, is_environment=is_environment, force_full_backup=force_full_backup)
        backup_endpoint = self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;POST&#34;, backup_endpoint, _backup_json)
        return self._process_backup_response(flag, response)

    def backup_tables(self, tables_list: list, force_full_backup: bool = False):
        &#34;&#34;&#34;
            Method to run backup for the specified tables of a Dynamics 365 subclient

            Arguments:
                tables_list     (list)--    List of tables to be backed up
                    List Format
                        Each list element should be a tuple of the format:
                            (&#34;environment_name&#34;,&#34;table_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be backed up

                    Sample input:
                        [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]
            Returns:
                backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
        &#34;&#34;&#34;
        return self._run_backup(backup_content=tables_list, is_environment=False, force_full_backup=force_full_backup)

    def backup_environments(self, environments_list: list):
        &#34;&#34;&#34;
            Method to run backup for the specified environments of a Dynamics 365 subclient

            Arguments:
                environments_list     (list)--    List of environments to be backed up
                    List Format, for backing up specified environments:
                        Each list element should be a string of the name of the environment
                    Sample List:
                        [&#39;testenv1&#39;,&#39;testenv2&#39;,&#39;testenv3&#39;]

            Returns:
                backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
        &#34;&#34;&#34;
        return self._run_backup(backup_content=environments_list, is_environment=True)

    def launch_client_level_full_backup(self):
        &#34;&#34;&#34;
            Method to run full backup for the Dynamics 365 subclient

            Returns:
                backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
        &#34;&#34;&#34;
        return self._run_backup(backup_content=[], is_environment=False, force_full_backup=True)

    def _restore_content_json(self):
        &#34;&#34;&#34;
            Restore JSON for restoring content for a Dynamics 365 subclient

            Returns:
                _restore_task_json          (dict)--    JSON to be used for running a restore task
        &#34;&#34;&#34;
        _restore_task_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subclient_properties[&#39;subClientEntity&#39;]],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;policyType&#34;: 0
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 3,
                            &#34;operationType&#34;: 1001
                        },
                        &#34;options&#34;: {
                            &#34;restoreOptions&#34;: {
                                &#34;browseOption&#34;: {
                                    &#34;timeRange&#34;: {}
                                },
                                &#34;commonOptions&#34;: {
                                    &#34;skip&#34;: True,
                                    &#34;overwriteFiles&#34;: False,
                                    &#34;unconditionalOverwrite&#34;: False
                                },
                                &#34;destination&#34;: {
                                    &#34;destAppId&#34;: self._app_id,
                                    &#34;inPlace&#34;: True,
                                    &#34;destClient&#34;: {
                                        &#34;clientId&#34;: int(self._client_object.client_id),
                                        &#34;clientName&#34;: self._client_object.client_name
                                    },
                                    &#34;destPath&#34;: []
                                },
                                &#34;fileOption&#34;: {
                                    &#34;sourceItem&#34;: list()
                                },
                                &#34;cloudAppsRestoreOptions&#34;: {
                                    &#34;instanceType&#34;: self._instance_type,
                                    &#34;d365RestoreOptions&#34;: {
                                        &#34;restoreAllMatching&#34;: False,
                                        &#34;restoreToDynamics365&#34;: True,
                                        &#34;overWriteItems&#34;: False,
                                        &#34;destLocation&#34;: &#34;&#34;,
                                        &#34;restoreUsingFindQuery&#34;: False
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }
        return _restore_task_json

    def _get_restore_item_path(self, content_list: list, is_environment: bool = False):
        &#34;&#34;&#34;
            Get the complete path of the content for running a restore job

            Arguments:
                content_list            (list)--        List of content ot be restored
                    If content is environment,
                        List format:
                            list of strings, with each string corresponding to the environments display name, in lower case

                    If content is tables:
                        List format:
                            list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                                where environment name if the name of the environment to which the table belongs to
                        Sample input:
                            [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

                is_environment          (bool)--        Whether the content is environment or tables
            Returns:
                __restore_content_list  (list)--        List of complete path for running restore job for the specifiec content
        &#34;&#34;&#34;
        __restore_content_list = list()

        if is_environment is True:
            for environment in self.get_associated_environments(refresh=True):
                if environment[&#34;name&#34;] in content_list:
                    _restore_id = environment[&#34;id&#34;]
                    __restore_content_list.append(_restore_id)

        elif is_environment is False:
            for _table in self.get_associated_tables(refresh=True):
                _table_name, _parent_env_name = _table[&#34;name&#34;].lower(), _table[&#34;environment_name&#34;].lower()

                try:
                    if (_parent_env_name, _table_name) in content_list:
                        _id = _table.get(&#34;userAccountInfo&#34;).get(&#34;smtpAddress&#34;).split(&#39;/&#39;)
                        _table_id = _id[-1]
                        _env_id = _id[-2]
                        _restore_id = f&#34;{_env_id}/{_table_id}&#34;
                        __restore_content_list.append(_restore_id)

                except TypeError:
                    raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                                       &#34;For restoring the tables, content list should be a list of tuples&#34;)
        __restore_content_list = list(
            map(lambda _restore_id: f&#34;/tenant/{_restore_id}&#34;, __restore_content_list)
        )

        return __restore_content_list

    def _prepare_restore_json(self,
                              restore_content: list,
                              restore_path: list = None,
                              overwrite: bool = True,
                              job_id: int = None,
                              is_environment: bool = False,
                              is_out_of_place_restore: bool = False,
                              destination_environment: str = str()
                              ) -&gt; dict:
        &#34;&#34;&#34;
            Method to prepare JSON/ Python dict for  in- place restore for the content specified.

            Arguments:
                restore_content         (str)--     List of the content to restore
                    If content is environment,
                        List format:
                            list of strings, with each string corresponding to the environments display name, in lower case

                    If content is tables:
                        List format:
                            list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                                where environment name if the name of the environment to which the table belongs to
                        Sample input:
                            [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

                restore_path            (list)--    List of the paths of the items to restore
                    Instead of passing, the restore content, restore path can be passed
                    Restore path, is the path for each item, that is to be restored.
                        Path is returned by the browse operation

                is_environment          (bool)--    Whether to content to be restored is a table or an environment
                overwrite               (bool)--    Skip or overwrite content
                job_id                  (int)--     Job ID for point in time restores
                destination_environment (Str)--     Destination environment for OOP restore.
                is_out_of_place_restore (bool)--    Is Out of Place Restore?
            Returns:
                _restore_content_json   (dict)--    Python dict to be used for restore content request
        &#34;&#34;&#34;
        _restore_content_json = self._restore_content_json()
        if restore_path is None:
            restore_path = self._get_restore_item_path(content_list=restore_content, is_environment=is_environment)

        _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;fileOption&#34;][
            &#34;sourceItem&#34;] = restore_path

        if job_id is not None:
            _job = self._commcell_object.job_controller.get(job_id)
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][&#34;timeRange&#34;][
                &#34;toTime&#34;] = _job.end_timestamp

        if overwrite is True:
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;commonOptions&#34;][&#34;overwriteFiles&#34;] = True
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;commonOptions&#34;][
                &#34;skip&#34;] = False
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;commonOptions&#34;][
                &#34;unconditionalOverwrite&#34;] = True
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] \
                [&#34;d365RestoreOptions&#34;][&#34;overWriteItems&#34;] = True

        if is_out_of_place_restore:
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;destination&#34;][&#34;destPath&#34;] = [destination_environment]
            _instance_id = self._get_environment_id_for_oop_restore(environment_name=destination_environment)
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] \
                [&#34;d365RestoreOptions&#34;][&#34;destLocation&#34;] = _instance_id
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;destination&#34;][&#34;inPlace&#34;] = False

        return _restore_content_json

    def restore_in_place(
            self,
            restore_content: list = None,
            restore_path: list = None,
            is_environment: bool = False,
            overwrite: bool = True,
            job_id: int = None):
        &#34;&#34;&#34;
            Method to run in- place restore for the content specified.

            Arguments:
                restore_content         (str)--     List of the content to restore
                    If content is environment,
                        List format:
                            list of strings, with each string corresponding to the environments display name, in lower case
                        Sample Input:
                            [ &#39;testenv1&#39; , &#39;testenv2&#39; , &#39;testenv3&#39; ]

                    If content is tables:
                        List format:
                            list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                                where environment name if the name of the environment to which the table belongs to
                        Sample input:
                            [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

                restore_path            (list)--    List of the paths of the items to restore
                    Instead of passing, the restore content, restore path can be passed
                    Restore path, is the path for each item, that is to be restored.
                        Path is returned by the browse operation

                is_environment          (bool)--    Whether to content to be restored is a table or an environment
                overwrite               (bool)--    Skip or overwrite content
                job_id                  (int)--     Job ID for point in time restores
            Returns:
                restore_job             (job)--     Instance of CVPySDK.Job for the restore job
        &#34;&#34;&#34;

        if restore_content is None and restore_path is None:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;, &#34;Need to have either of restore content or restore path&#34;)

        _restore_json = self._prepare_restore_json(
            restore_content=restore_content,
            restore_path=restore_path,
            is_environment=is_environment,
            job_id=job_id,
            overwrite=overwrite,
            is_out_of_place_restore=False)

        return self._process_restore_response(_restore_json)

    def launch_d365_licensing(self, run_for_all_clients=False):
        &#34;&#34;&#34;
            Method to launch Licensing API call.
            Arguments:
                run_for_all_clients(bool)      --  True if thread is to be run on all clients, False otherwise
                    default: False
        &#34;&#34;&#34;

        _LAUNCH_LICENSING = self._services[&#39;LAUNCH_O365_LICENSING&#39;]

        request_json = {
            &#34;subClient&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id)
            },
            &#34;runForAllClients&#34;: run_for_all_clients,
            &#34;appType&#34;: 6
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, _LAUNCH_LICENSING, request_json
        )

        if flag:
            try:
                if response.json():
                    if response.json().get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to Launch Licensing Thread\nError: &#34;{0}&#34;&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message))
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_environment_id_for_oop_restore(self, environment_name: str) -&gt; str:
        &#34;&#34;&#34;
            Get the Environment ID for an environment for Out of Place Restore

            Arguments:
                environment_name        (str)--     Name of the environment

            Returns:
                environment_id          (str)--     ID for the environment
        &#34;&#34;&#34;
        for environment in self.discovered_environments:
            if environment[&#39;displayName&#39;] == environment_name:
                _env_xml = environment.get(&#34;xmlGeneric&#34;)
                _env_json = json.loads(_env_xml)
                _env_id = _env_json.get(&#34;_instanceName&#34;)
        return _env_id

    def restore_out_of_place(
            self,
            restore_content: list = None,
            restore_path: list = None,
            is_environment: bool = False,
            overwrite: bool = True,
            job_id: int = None,
            destination_environment: str = str()):
        &#34;&#34;&#34;
            Method to run out-of-place restore for the content specified.

            Arguments:
                restore_content         (str)--     List of the content to restore
                    If content is environment,
                        List format:
                            list of strings, with each string corresponding to the environments display name, in lower case
                        Sample Input:
                            [ &#39;testenv1&#39; , &#39;testenv2&#39; , &#39;testenv3&#39; ]

                    If content is tables:
                        List format:
                            list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                                where environment name if the name of the environment to which the table belongs to
                        Sample input:
                            [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

                restore_path            (list)--    List of the paths of the items to restore
                    Instead of passing, the restore content, restore path can be passed
                    Restore path, is the path for each item, that is to be restored.
                        Path is returned by the browse operation

                is_environment          (bool)--    Whether to content to be restored is a table or an environment
                overwrite               (bool)--    Skip or overwrite content
                job_id                  (int)--     Job ID for point in time restores
                destination_environment (str)--     Destination environment name
            Returns:
                restore_job             (job)--     Instance of CVPySDK.Job for the restore job
        &#34;&#34;&#34;

        if restore_content is None and restore_path is None:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;, &#34;Need to have either of restore content or restore path&#34;)

        _restore_json = self._prepare_restore_json(
            restore_content=restore_content,
            restore_path=restore_path,
            is_environment=is_environment,
            job_id=job_id,
            overwrite=overwrite,
            is_out_of_place_restore=True,
            destination_environment=destination_environment)

        return self._process_restore_response(_restore_json)

    def browse(self,
               browse_path: list[str] = None,
               include_deleted_items: bool = False,
               till_time: int = -1):
        &#34;&#34;&#34;
            Browse for the backed up content for a Dynamics 365 subclient

            Arguments:
                browse_path         (list)  --      Path to be browsed
                    Sample Value:
                        [&#34;environment-name&#34; , &#34;table-name&#34;]
                include_deleted_items
                                    (bool)  --      Whether to include deleted items in the browse response
                till_time           (int)   --      Time-stamp for point in time browse
        &#34;&#34;&#34;
        _parent_path = str()

        _environments = self._perform_browse(parent_path=_parent_path, till_time=till_time, item_type=2,
                                             include_deleted_items=include_deleted_items)
        _browse_response = copy.deepcopy(_environments)

        if browse_path:
            _item_type: int = 3
            for path in browse_path:
                _parent_path = self._get_guid_for_path(_browse_response, path)
                if not _parent_path:
                    raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                                       f&#34;Path: {path} not found in browse content: {_browse_response}&#34;)
                _browse_response = self._perform_browse(parent_path=_parent_path, till_time=till_time,
                                                        item_type=_item_type,
                                                        include_deleted_items=include_deleted_items)
                _item_type += 1

        return _browse_response

    def _get_guid_for_path(self, browse_response: dict, path: str) -&gt; str:
        &#34;&#34;&#34;
            Method to get the browse GUID corresponding to the path

            Arguments:
                browse_reponse          (dict)--    Response from the browse query
                path                    (str)--     Path for which GUID is to be fetched

            Example:
                from the browse for &#34;d365-env&#34;, find the GUID for the &#34;Accounts&#34; table

            The GUID would be used im the subsequent browse requests
        &#34;&#34;&#34;
        guid: str = str()
        for item in browse_response:
            if item.get(&#34;appSpecific&#34;).get(&#34;d365Item&#34;).get(&#34;displayName&#34;) == path:
                guid = item.get(&#34;cvObjectGuid&#34;)
        return guid

    def _perform_browse(self, parent_path: str = str(), till_time: int = -1, item_type: int = 2,
                        include_deleted_items: bool = False):
        &#34;&#34;&#34;
            Perform a browse of the backed up content
            Arguments:
                parent_path         (str)   --      GUID for the parent path
                include_deleted_items
                                    (bool)  --      Whether to include deleted items in the browse response
                till_time           (int)   --      Time-stamp for point in time browse
                item_type           (int)   --      Item type to be browsed
        &#34;&#34;&#34;
        _browse_default_params = self._get_dynamics365_browse_params(item_type=item_type)

        _query_params = _browse_default_params.get(&#34;query_params&#34;)
        _file_filter = _browse_default_params.get(&#34;file_filter&#34;)
        _sort_params = _browse_default_params.get(&#34;sort_param&#34;)
        _common_filters = _browse_default_params.get(&#34;common_filters&#34;)

        if parent_path:
            _parent_path_filter = {
                &#34;field&#34;: &#34;PARENT_GUID&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        f&#34;{parent_path}&#34;
                    ]
                }
            }
            _file_filter.append(_parent_path_filter)

        if till_time != -1:
            _backup_time_filter = {
                &#34;field&#34;: &#34;BACKUPTIME&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;0&#34;,
                        f&#34;{till_time}&#34;
                    ]
                }
            }
            _file_filter.append(_backup_time_filter)

        if include_deleted_items:
            _common_filter = {
                &#34;groupType&#34;: 0,
                &#34;field&#34;: &#34;CISTATE&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;1&#34;,
                        &#34;3333&#34;,
                        &#34;3334&#34;,
                        &#34;3335&#34;
                    ]
                }
            }
            _common_filters[0] = _common_filter

        return self.do_web_search(query_params=_query_params, file_filter=_file_filter, sort_param=_sort_params,
                                  common_filters=_common_filters)

    def _get_dynamics365_browse_params(self, item_type: int = 2) -&gt; dict:
        &#34;&#34;&#34;
            Default dictionary for the browse parameters for a Dynamics 365 browse query.

            Arguments:
                item_type       (int)   --  Item type to br browsed for
        &#34;&#34;&#34;
        _query_params: list = [
            {
                &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;,
                &#34;value&#34;: &#34;true&#34;
            },
            {
                &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                &#34;value&#34;: &#34;D365_ENTITY_DISP_NAME,D365_ENTITY_DISP_NAME,CONTENTID,CV_OBJECT_GUID,CV_TURBO_GUID,&#34;
                         &#34;PARENT_GUID,AFILEID,AFILEOFFSET,COMMCELLNO,APPID,D365_ID,D365_DISPLAYNAME,&#34;
                         &#34;FILE_CREATEDTIME,MODIFIEDTIME,BACKUPTIME,D365_OBJECT_TYPE,D365_CONTENTHASH,D365_FLAGS,&#34;
                         &#34;D365_ENTITYSET_ID,D365_ENTITYSET_NAME,D365_INSTANCE_ID,D365_INSTANCE_NAME,&#34;
                         &#34;D365_CREATEDBY_GUID,D365_CREATEDBY_NAME,D365_MODIFIEDBY_GUID,D365_MODIFIEDBY_NAME,&#34;
                         &#34;D365_OWNER_GUID,D365_OWNER_NAME,DATE_DELETED,CISTATE &#34;
            },
            {
                &#34;param&#34;: &#34;COLLAPSE_FIELD&#34;,
                &#34;value&#34;: &#34;CV_OBJECT_GUID&#34;
            }]

        _sort_params: list = [
            {
                &#34;sortDirection&#34;: 0,
                &#34;sortField&#34;: &#34;D365_DISPLAYNAME&#34;
            }
        ]

        _common_filters: list = [
            {
                &#34;groupType&#34;: 0,
                &#34;field&#34;: &#34;CISTATE&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;1&#34;
                    ]
                }
            },
            {
                &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                &#34;intraFieldOpStr&#34;: &#34;None&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;isMoniker&#34;: False,
                    &#34;isRange&#34;: False,
                    &#34;values&#34;: [
                        &#34;true&#34;
                    ]
                }
            }
        ]

        _file_filter: list = [
            {
                &#34;field&#34;: &#34;D365_OBJECT_TYPE&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        f&#34;{item_type}&#34;
                    ]
                }
            }
        ]
        return {&#34;query_params&#34;: _query_params, &#34;file_filter&#34;: _file_filter, &#34;sort_param&#34;: _sort_params,
                &#34;common_filters&#34;: _common_filters}

    @property
    def browse_item_type(self):
        &#34;&#34;&#34;Dynamics 365 item types&#34;&#34;&#34;
        _browse_item_type = {&#34;environment&#34;: 2,
                             &#34;table&#34;: 3,
                             &#34;record&#34;: 4}
        return _browse_item_type</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient"><code class="flex name class">
<span>class <span class="ident">MSDynamics365Subclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class representing a MS Dynamics 365 subclient.
Class has been derived from the O365AppsSubclient.</p>
<p>Initialize the Subclient object for the given MSDynamics365 Subclient.</p>
<h2 id="args">Args</h2>
<p>backupset_object
(object)
&ndash;
instance of the backup-set class</p>
<p>subclient_name
(str)
&ndash;
subclient name</p>
<p>subclient_id
(int)
&ndash;
subclient id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L83-L1222" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class MSDynamics365Subclient(O365AppsSubclient):
    &#34;&#34;&#34;
        Class representing a MS Dynamics 365 subclient.
            Class has been derived from the O365AppsSubclient.
    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Subclient object for the given MSDynamics365 Subclient.

            Args:
                backupset_object    (object)    --  instance of the backup-set class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(MSDynamics365Subclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

        self._instance_object = backupset_object._instance_object
        self._client_object = self._instance_object._agent_object._client_object
        self._associated_tables: dict = dict()
        self._associated_environments: dict = dict()
        self._discovered_environments: dict = dict()
        self._discovered_tables: dict = dict()
        self._instance_type: int = 35
        self._app_id: int = 134
        # App ID for cloud apps
        self._Dynamics365_SET_USER_POLICY_ASSOCIATION = self._commcell_object._services[&#39;SET_USER_POLICY_ASSOCIATION&#39;]

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient related properties of a MS Dynamics 365 subclient&#34;&#34;&#34;
        super(MSDynamics365Subclient, self)._get_subclient_properties()

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;

        return {&#39;subClientProperties&#39;: self._subclient_properties}

    def discover_tables(self):
        &#34;&#34;&#34;
            Method to get the tables discovered from the MS Dynamics 365 CRM subclient

            Returns:
                discovered_tables       (dict)--    Dictionary of returned tables

        &#34;&#34;&#34;
        self._discovered_tables = self._instance_object.discover_content(environment_discovery=False)
        return self._discovered_tables

    def discover_environments(self):
        &#34;&#34;&#34;
            Method to get the environments discovered from the Dynamics 365 CRM subclient

            Returns:
                discovered_environments       (dict)--    Dictionary of discovered environments

        &#34;&#34;&#34;
        self._discovered_environments = self._instance_object.discover_content(environment_discovery=True)
        return self._discovered_environments

    @property
    def discovered_environments(self):
        &#34;&#34;&#34;
            Property to get the environments discovered by the Dynamics 365 subclient.

            If updated list is required, call refresh method prior to using this property.

            Returns:
                discovered_environments       (dict)--    Dictionary of discovered environments
        &#34;&#34;&#34;
        if not bool(self._discovered_environments):
            self.discover_environments()
        return self._discovered_environments

    @property
    def discovered_tables(self):
        &#34;&#34;&#34;
            Property to get the tables discovered by the Dynamics 365 subclient.

            If updated list is required, call refresh method prior to using this property.

            Returns:
                discovered_tables      (dict)--    Dictionary of discovered tables
        &#34;&#34;&#34;
        if not bool(self._discovered_tables):
            self.discover_tables()
        return self._discovered_tables

    def _get_associated_content(self, is_environment=False):
        &#34;&#34;&#34;
            Method to get the content associated with a Dynamics 365 CRM subclient

            Arguments:
                is_environment      (bool)--    Whether to get the associated environments or tables
                    Default Value:
                        False
                            Returns the associated tables

            Returns:
                associated_content_list     (list)--    List of content associated with the client
                    Format:
                        Each list element will be a dictionary denoting that particular environment/ table
        &#34;&#34;&#34;
        discover_by_type: int
        if is_environment is True:
            discover_by_type = 5
        else:
            discover_by_type = 14

        _GET_ASSOCIATED_CONTENT = self._services[&#39;USER_POLICY_ASSOCIATION&#39;]
        request_json = {
            &#34;discoverByType&#34;: discover_by_type,
            &#34;bIncludeDeleted&#34;: False,
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                }
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, _GET_ASSOCIATED_CONTENT, request_json
        )
        if flag:
            if response and response.json():
                no_of_records = int()
                if &#39;associations&#39; in response.json():
                    no_of_records = response.json().get(&#39;associations&#39;, [])[0].get(&#39;pagingInfo&#39;, {}). \
                        get(&#39;totalRecords&#39;, -1)

                    associations = response.json().get(&#39;associations&#39;, [])
                    content_list = list()
                    if discover_by_type == 5:
                        for environment in associations:
                            environment_name = environment.get(&#34;groups&#34;, {}).get(&#34;name&#34;)
                            env_dict = {
                                &#34;name&#34;: environment_name,
                                &#34;id&#34;: environment.get(&#34;groups&#34;, {}).get(&#34;id&#34;),
                                &#34;userAccountInfo&#34;: environment.get(&#34;userAccountInfo&#34;, {}),
                                &#34;plan&#34;: environment.get(&#34;plan&#34;, {}),
                                &#34;is_environment&#34;: True
                            }
                            content_list.append(env_dict)

                    elif discover_by_type == 14:
                        for table in associations:
                            table_name = table.get(&#34;userAccountInfo&#34;, {}).get(&#34;displayName&#34;)
                            table_dict = {
                                &#34;name&#34;: table_name,
                                &#34;environment_name&#34;: table.get(&#34;userAccountInfo&#34;, {}).get(&#34;ParentWebGuid&#34;, &#34;&#34;),
                                &#34;userAccountInfo&#34;: table.get(&#34;userAccountInfo&#34;, {}),
                                &#34;plan&#34;: table.get(&#34;plan&#34;, {}),
                                &#34;is_environment&#34;: False
                            }
                            content_list.append(table_dict)
                    return content_list
                    # return content_list, no_of_records
            return {}, 0
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_associated_tables(self, refresh: bool = False):
        &#34;&#34;&#34;
            Method to get the tables associated with a Dynamics 365 CRM client

            Arguments:
                refresh                     (bool)--    Whether to refresh the dictionary contents
                    If True
                        get associated environments, will fetch the latest associations and return them

            Returns:
                associated_tables     (list)--    List of tables associated with the client
                    Format:
                        Each list element will be a dictionary denoting that particular  table
                        Dictionary keys/ format will be:
                            name : name of the table
                            environment_name : name of the environment to which the table belongs to
                            plan: Dynamics 365 plan used for content association
                            is_environment: False for a Table
                            userAccountInfo:    Metadata info for that table

                    Sample Response:
                        {
                            &#39;name&#39;: &#39;account&#39;,
                            &#39;environment_name&#39;: &#39;sample-environment-name&#39;,
                            &#39;userAccountInfo&#39;:
                                {
                                &#39;aliasName&#39;: &#39;https://&lt;org-url-name&gt;.crm.dynamics.com/api/data/v9.1/account&#39;,
                                &#39;displayName&#39;: &#39;Account,
                                &#39;ParentWebGuid&#39;: &#39;org-environment-name&#39;,
                                &#39;lastBackupJobRanTime&#39;: {&#39;time&#39;: &lt;last-backup-time&gt;},
                                &#39;IdxCollectionTime&#39;: {&#39;time&#39;: &lt;last-index-time&gt;},
                                &#39;user&#39;: {
                                    &#39;_type_&#39;: 13,
                                     &#39;userGUID&#39;: &#39;&lt;table-GUID&gt;&gt;&#39;
                                     }
                                },
                            &#39;plan&#39;: {
                                &#39;planName&#39;: &#39;&lt;PLAN-NAME&gt;&#39;, &#39;planId&#39;: &lt;plan-id&gt;},
                            &#39;is_environment&#39;: False
                        }
                    Environment name/ URL in the sample response is for description purpose only

        &#34;&#34;&#34;
        if refresh is True:
            self._associated_tables = self._get_associated_content(is_environment=False)
        return self._associated_tables

    def get_associated_environments(self, refresh: bool = False):
        &#34;&#34;&#34;
            Method to get the environments associated with a Dynamics 365 CRM client

            Arguments:
                refresh                     (bool)--    Whether to refresh the dictionary contents
                    If True
                        get associated environments, will fetch the latest associations and return them

            Returns:
                associated_environments     (list)--    List of environments associated with the client
                    Format:
                        Each list element will be a dictionary denoting that particular environment
                        Dictionary keys/ format will be:
                            name :              name of the table
                            plan:               Dynamics 365 plan used for content association
                            is_environment:     True for an environment
                            userAccountInfo:    Metadata info for that environment

                    Sample Response:
                        {
                                &#39;name&#39;: &#39;sample-environment-name&#39;,
                                &#39;id&#39;: &#39;&lt;environment-ID&gt;&gt;&#39;,
                                &#39;userAccountInfo&#39;:
                                {
                                    &#39;aliasName&#39;: &#39;https://&lt;org-url-name&gt;.crm.dynamics.com&#39;,
                                    &#39;itemType&#39;: 0,
                                    &#39;ItemClassification&#39;: 0,
                                    &#39;displayName&#39;: &#39;org-environment-display-name&#39;,
                                    &#39;BackupSetId&#39;: 0,
                                    &#39;isAutoDiscoveredUser&#39;: False,
                                    &#39;lastBackupJobRanTime&#39;: &#39;time&#39;: &lt;last-backup-time&gt;,
                                    &#39;IdxCollectionTime&#39;: {&#39;time&#39;: &lt;last-index-playback-time&gt;},
                                    user&#39;: {
                                        &#39;_type_&#39;: 13,
                                        &#39;userGUID&#39;: &#39;&lt;env-GUID&gt;&#39;
                                        }
                                },
                                &#39;plan&#39;: {&#39;planName&#39;: &#39;&lt;name-of-plan&gt;&#39;, &#39;planId&#39;: &lt;id-of-plan&gt;},
                                 &#39;is_environment&#39;: True}

        &#34;&#34;&#34;
        if refresh is True:
            self._associated_environments = self._get_associated_content(is_environment=True)
        return self._associated_environments

    def _set_association_json(self, is_environment: bool = False):
        &#34;&#34;&#34;
            JSON to set the content association for a Dynamics 365 CRM client

            Arguments:
                is_environment      (bool):     Whether the content to be associated is an environment
                    Default Value:
                        False
            Returns:
                set_content_association_json    (dict)--    Content Association JSON
        &#34;&#34;&#34;
        set_content_association_json = {
                                            &#34;LaunchAutoDiscovery&#34;: is_environment,
                                            &#34;cloudAppAssociation&#34;: {
                                            &#34;accountStatus&#34;: 0,
                                            &#34;cloudAppDiscoverinfo&#34;: {
                                                &#34;userAccounts&#34;: [
                                                ],
                                                &#34;groups&#34;: [],
                                                &#34;discoverByType&#34;: 14 if is_environment is False else 15
                                            },
                                            &#34;subclientEntity&#34;: self._subClientEntity
                                            }
                                        }

        return set_content_association_json

    def _set_content_association(self, content_json: dict):
        &#34;&#34;&#34;
            Method to associate some content to a Dynamics 365 CRM client...

            Arguments:
                content_json        (dict)--        Association JSON to be used for the content
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._Dynamics365_SET_USER_POLICY_ASSOCIATION, content_json
        )

        if flag:
            try:
                if response.json():
                    if response.json().get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to Create Association for a Dynamics 365 CRM client\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message)
                        )
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _table_association_info_json(self, tables_list: list):
        &#34;&#34;&#34;
            Private Method to create the association JSON for associating tables
            to a Dynamics 365 CRM client.

            Arguments:
                tables_list     (list)--    List of tables to be associated to the content
                    List Format:
                        Each list element should be a tuple of the format:
                            (&#34;table_name&#34;, &#34;environment_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be associated

            Returns:
                tables_info     (list)--    List of metadata info for the tables to be used for associating content
        &#34;&#34;&#34;
        tables_info: list = list()
        _discovered_tables = self.discovered_tables
        tables_dict = {}

        if not bool(_discovered_tables):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                               &#34;Discovered Tables is Empty.&#34;)

        for table in _discovered_tables:
            if table[&#34;ParentWebGuid&#34;] in tables_dict:
                tables_dict[table[&#34;ParentWebGuid&#34;]].update({table[&#34;displayName&#34;]: table})
            else:
                tables_dict.update({table[&#34;ParentWebGuid&#34;]: {}})

        for table in tables_list:
            table_name, env_name = table
            if env_name in tables_dict:
                if table_name in tables_dict[env_name]:
                    tables_info.append(tables_dict[env_name][table_name])
                else:
                    raise SDKException(&#34;Subclient&#34;, &#34;101&#34;,
                                       &#34;Table {} not found in the environment {}&#34;.format(table_name, env_name))
            else:
                raise SDKException(&#34;Subclient&#34;, &#34;101&#34;,
                                   &#34;Environment {} not found in the list of discovered environments&#34;.format(env_name))

        if len(tables_info) != len(tables_list):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;, &#34;All of the input tables were in the list of discovered tables&#34;)

        return tables_info

    def set_table_associations(self, tables_list: list, plan_name: str = str()):
        &#34;&#34;&#34;
            Method to add table associations
            to a Dynamics 365 CRM client.

            Arguments:
                tables_list     (list)--    List of tables to be associated to the content
                    List Format:
                        Each list element should be a tuple of the format:
                            (&#34;table_name&#34;, &#34;environment_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be associated
                    Sample input:
                        [ (&#34;account&#34;, &#34;testenv1&#34;) , (&#34;note&#34;, &#34;testenv2&#34;) , (&#34;attachments&#34;, &#34;testenv1&#34;)]

                plan_name       (str)--     Name of the Dynamics 365 Plan to be used for content association
        &#34;&#34;&#34;

        plan_id = int(self._commcell_object.plans[plan_name.lower()])

        tables_info = self._table_association_info_json(tables_list=tables_list)

        _table_association_json = self._set_association_json(is_environment=False)
        _table_association_json[&#34;cloudAppAssociation&#34;][&#34;plan&#34;] = {
            &#34;planId&#34;: plan_id
        }
        _table_association_json[&#34;cloudAppAssociation&#34;][&#34;cloudAppDiscoverinfo&#34;][&#34;userAccounts&#34;] = tables_info
        self._set_content_association(content_json=_table_association_json)

    def _environment_association_info_json(self, environments_name: list):
        &#34;&#34;&#34;
            Method to create the association JSON for associating environments
            to a Dynamics 365 CRM client.

            Arguments:
                environments_name     (list)--    List of environments to be associated to the content
                    List Format:
                        Each list element should be a string of the name of the environment

            Returns:
                environments_info     (list)--    List of metadata info for the environments to
                                                    be used for associating content
        &#34;&#34;&#34;
        environments_info: list = list()
        _discovered_envs = self.discovered_environments

        if not bool(_discovered_envs):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                               &#34;Discovered Environments List is Empty&#34;)

        for environment in _discovered_envs:
            if environment[&#34;displayName&#34;] in environments_name:
                _env_assoc_info = environment
                _env_assoc_info[&#34;user.userGUID&#34;] = environment.get(&#34;user&#34;).get(&#34;userGUID&#34;)
                _env_assoc_info[&#34;rawCommonFlag&#34;] = environment.get(&#34;commonFlags&#34;, 0)

                environments_info.append(_env_assoc_info)

        if len(environments_info) == 0:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;,
                               &#34;None of the input environments were in the list of discovered environments&#34;)

        return environments_info

    def set_environment_associations(self, environments_name: list, plan_name: str = str()):
        &#34;&#34;&#34;
            Method to add environment associations
            to a Dynamics 365 CRM client.

            Arguments:
                environments_name     (list)--    List of environments to be associated to the content
                    List Format:
                        Each list element should be a string of the name of the environment
                    Sample Values:
                        [&#39;testenv1&#39; , &#39;testenv2&#39;, &#39;testenv3&#39;]

                plan_name       (str)--     Name of the Dynamics 365 Plan to be used for content association
        &#34;&#34;&#34;
        environments_info: list = self._environment_association_info_json(environments_name=environments_name)

        if self._commcell_object.plans.has_plan(plan_name.lower()):
            plan_id = int(self._commcell_object.plans[plan_name.lower()])

        else:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;,
                               &#34;Dynamics 365 Plan does not exist&#34;)

        _env_association_json = self._set_association_json(is_environment=True)
        _env_association_json[&#34;cloudAppAssociation&#34;][&#34;plan&#34;] = {
            &#34;planId&#34;: plan_id
        }
        _env_association_json[&#34;cloudAppAssociation&#34;][&#34;cloudAppDiscoverinfo&#34;][&#34;userAccounts&#34;] = environments_info
        self._set_content_association(content_json=_env_association_json)

    def _json_for_backup_task(self, content_list: list, is_environment: bool = False, force_full_backup: bool = False):
        &#34;&#34;&#34;
            Method to create the association JSON for backing up content for a Dynamics 365 subclient

            Arguments:
                content_list     (list)--    List of content to be backed up
                    List Format, if content to be backed up is tables:
                        Each list element should be a tuple of the format:
                            (&#34;environment_name&#34;,&#34;table_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be backed up
                    List Format, if content to be associated is environments:
                        Each list element should be a string of the name of the environment

                is_environment  (bool)--    Content passed to be backed up is environment type content or table type
                force_full_backup (bool) -- If True, will force a full backup of the content

            Returns:
                _backup_task_json     (list)--    JSON for backing up the content
        &#34;&#34;&#34;
        _backup_task_json = self._backup_json(&#39;Full&#39;, False, &#39;&#39;)
        backup_options = {
            &#39;backupLevel&#39;: 2,  # Incremental
            &#39;cloudAppOptions&#39;: {
            }
        }

        if len(content_list) &gt; 0:
            _sub_client_content_json = self._backup_content_json(content_list=content_list, is_environment=is_environment)
            backup_options[&#39;cloudAppOptions&#39;][&#39;userAccounts&#39;] = _sub_client_content_json

        if force_full_backup is True:
            backup_options[&#39;cloudAppOptions&#39;][&#39;forceFullBackup&#39;] = True

        _backup_task_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;] = backup_options
        return _backup_task_json

    def _backup_content_json(self, content_list: list, is_environment: bool = False):
        &#34;&#34;&#34;
            Method to fetch the metadata properties for backing up content for a Dynamics 365 subclient

            Arguments:
                content_list     (list)--    List of content to be backed up
                    List Format, if content to be backed up is tables:
                        Each list element should be a tuple of the format:
                            (&#34;environment_name&#34;,&#34;table_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be backed up
                    List Format, if content to be associated is environments:
                        Each list element should be a string of the name of the environment

                is_environment  (bool)--    Content passed to be backed up is environment type content or table type

            Returns:
                _bkp_content_json     (list)--    Metadata JSON for backing up that content
        &#34;&#34;&#34;
        _bkp_content_json = list()

        if is_environment is True:
            for environment in self.get_associated_environments(refresh=True):
                if environment[&#34;name&#34;] in content_list:
                    _env_bkp_info = environment[&#34;userAccountInfo&#34;]
                    _bkp_content_json.append(_env_bkp_info)

        elif is_environment is False:
            for _table in self.get_associated_tables(refresh=True):
                _table_name, _parent_env_name = _table[&#34;name&#34;].lower(), _table[&#34;environment_name&#34;].lower()
                try:
                    if (_parent_env_name, _table_name) in content_list:
                        _table_bkp_info = _table[&#34;userAccountInfo&#34;]
                        _bkp_content_json.append(_table_bkp_info)
                except TypeError:
                    raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                                       &#34;For backing up tables, content list should be a list of tuples&#34;)

        return _bkp_content_json

    def _run_backup(self, backup_content: list, is_environment: bool = False, force_full_backup: bool = False):
        &#34;&#34;&#34;
            Method to run backup for the content of a Dynamics 365 subclient

            Arguments:
                backup_content     (list)--    List of content to be backed up
                    List Format, if content to be backed up is tables:
                        Each list element should be a tuple of the format:
                            (&#34;environment_name&#34;,&#34;table_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be backed up
                    List Format, if content to be associated is environments:
                        Each list element should be a string of the name of the environment

                is_environment  (bool)--    Content passed to be backed up is environment type content or table type

            Returns:
                backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
        &#34;&#34;&#34;
        _backup_json = self._json_for_backup_task(content_list=backup_content, is_environment=is_environment, force_full_backup=force_full_backup)
        backup_endpoint = self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;POST&#34;, backup_endpoint, _backup_json)
        return self._process_backup_response(flag, response)

    def backup_tables(self, tables_list: list, force_full_backup: bool = False):
        &#34;&#34;&#34;
            Method to run backup for the specified tables of a Dynamics 365 subclient

            Arguments:
                tables_list     (list)--    List of tables to be backed up
                    List Format
                        Each list element should be a tuple of the format:
                            (&#34;environment_name&#34;,&#34;table_name&#34;)
                                environment_name is the name of the environment to which the table belongs to
                                table_name is the name of the table to be backed up

                    Sample input:
                        [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]
            Returns:
                backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
        &#34;&#34;&#34;
        return self._run_backup(backup_content=tables_list, is_environment=False, force_full_backup=force_full_backup)

    def backup_environments(self, environments_list: list):
        &#34;&#34;&#34;
            Method to run backup for the specified environments of a Dynamics 365 subclient

            Arguments:
                environments_list     (list)--    List of environments to be backed up
                    List Format, for backing up specified environments:
                        Each list element should be a string of the name of the environment
                    Sample List:
                        [&#39;testenv1&#39;,&#39;testenv2&#39;,&#39;testenv3&#39;]

            Returns:
                backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
        &#34;&#34;&#34;
        return self._run_backup(backup_content=environments_list, is_environment=True)

    def launch_client_level_full_backup(self):
        &#34;&#34;&#34;
            Method to run full backup for the Dynamics 365 subclient

            Returns:
                backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
        &#34;&#34;&#34;
        return self._run_backup(backup_content=[], is_environment=False, force_full_backup=True)

    def _restore_content_json(self):
        &#34;&#34;&#34;
            Restore JSON for restoring content for a Dynamics 365 subclient

            Returns:
                _restore_task_json          (dict)--    JSON to be used for running a restore task
        &#34;&#34;&#34;
        _restore_task_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subclient_properties[&#39;subClientEntity&#39;]],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;policyType&#34;: 0
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 3,
                            &#34;operationType&#34;: 1001
                        },
                        &#34;options&#34;: {
                            &#34;restoreOptions&#34;: {
                                &#34;browseOption&#34;: {
                                    &#34;timeRange&#34;: {}
                                },
                                &#34;commonOptions&#34;: {
                                    &#34;skip&#34;: True,
                                    &#34;overwriteFiles&#34;: False,
                                    &#34;unconditionalOverwrite&#34;: False
                                },
                                &#34;destination&#34;: {
                                    &#34;destAppId&#34;: self._app_id,
                                    &#34;inPlace&#34;: True,
                                    &#34;destClient&#34;: {
                                        &#34;clientId&#34;: int(self._client_object.client_id),
                                        &#34;clientName&#34;: self._client_object.client_name
                                    },
                                    &#34;destPath&#34;: []
                                },
                                &#34;fileOption&#34;: {
                                    &#34;sourceItem&#34;: list()
                                },
                                &#34;cloudAppsRestoreOptions&#34;: {
                                    &#34;instanceType&#34;: self._instance_type,
                                    &#34;d365RestoreOptions&#34;: {
                                        &#34;restoreAllMatching&#34;: False,
                                        &#34;restoreToDynamics365&#34;: True,
                                        &#34;overWriteItems&#34;: False,
                                        &#34;destLocation&#34;: &#34;&#34;,
                                        &#34;restoreUsingFindQuery&#34;: False
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }
        return _restore_task_json

    def _get_restore_item_path(self, content_list: list, is_environment: bool = False):
        &#34;&#34;&#34;
            Get the complete path of the content for running a restore job

            Arguments:
                content_list            (list)--        List of content ot be restored
                    If content is environment,
                        List format:
                            list of strings, with each string corresponding to the environments display name, in lower case

                    If content is tables:
                        List format:
                            list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                                where environment name if the name of the environment to which the table belongs to
                        Sample input:
                            [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

                is_environment          (bool)--        Whether the content is environment or tables
            Returns:
                __restore_content_list  (list)--        List of complete path for running restore job for the specifiec content
        &#34;&#34;&#34;
        __restore_content_list = list()

        if is_environment is True:
            for environment in self.get_associated_environments(refresh=True):
                if environment[&#34;name&#34;] in content_list:
                    _restore_id = environment[&#34;id&#34;]
                    __restore_content_list.append(_restore_id)

        elif is_environment is False:
            for _table in self.get_associated_tables(refresh=True):
                _table_name, _parent_env_name = _table[&#34;name&#34;].lower(), _table[&#34;environment_name&#34;].lower()

                try:
                    if (_parent_env_name, _table_name) in content_list:
                        _id = _table.get(&#34;userAccountInfo&#34;).get(&#34;smtpAddress&#34;).split(&#39;/&#39;)
                        _table_id = _id[-1]
                        _env_id = _id[-2]
                        _restore_id = f&#34;{_env_id}/{_table_id}&#34;
                        __restore_content_list.append(_restore_id)

                except TypeError:
                    raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                                       &#34;For restoring the tables, content list should be a list of tuples&#34;)
        __restore_content_list = list(
            map(lambda _restore_id: f&#34;/tenant/{_restore_id}&#34;, __restore_content_list)
        )

        return __restore_content_list

    def _prepare_restore_json(self,
                              restore_content: list,
                              restore_path: list = None,
                              overwrite: bool = True,
                              job_id: int = None,
                              is_environment: bool = False,
                              is_out_of_place_restore: bool = False,
                              destination_environment: str = str()
                              ) -&gt; dict:
        &#34;&#34;&#34;
            Method to prepare JSON/ Python dict for  in- place restore for the content specified.

            Arguments:
                restore_content         (str)--     List of the content to restore
                    If content is environment,
                        List format:
                            list of strings, with each string corresponding to the environments display name, in lower case

                    If content is tables:
                        List format:
                            list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                                where environment name if the name of the environment to which the table belongs to
                        Sample input:
                            [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

                restore_path            (list)--    List of the paths of the items to restore
                    Instead of passing, the restore content, restore path can be passed
                    Restore path, is the path for each item, that is to be restored.
                        Path is returned by the browse operation

                is_environment          (bool)--    Whether to content to be restored is a table or an environment
                overwrite               (bool)--    Skip or overwrite content
                job_id                  (int)--     Job ID for point in time restores
                destination_environment (Str)--     Destination environment for OOP restore.
                is_out_of_place_restore (bool)--    Is Out of Place Restore?
            Returns:
                _restore_content_json   (dict)--    Python dict to be used for restore content request
        &#34;&#34;&#34;
        _restore_content_json = self._restore_content_json()
        if restore_path is None:
            restore_path = self._get_restore_item_path(content_list=restore_content, is_environment=is_environment)

        _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;fileOption&#34;][
            &#34;sourceItem&#34;] = restore_path

        if job_id is not None:
            _job = self._commcell_object.job_controller.get(job_id)
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][&#34;timeRange&#34;][
                &#34;toTime&#34;] = _job.end_timestamp

        if overwrite is True:
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;commonOptions&#34;][&#34;overwriteFiles&#34;] = True
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;commonOptions&#34;][
                &#34;skip&#34;] = False
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;commonOptions&#34;][
                &#34;unconditionalOverwrite&#34;] = True
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] \
                [&#34;d365RestoreOptions&#34;][&#34;overWriteItems&#34;] = True

        if is_out_of_place_restore:
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;destination&#34;][&#34;destPath&#34;] = [destination_environment]
            _instance_id = self._get_environment_id_for_oop_restore(environment_name=destination_environment)
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] \
                [&#34;d365RestoreOptions&#34;][&#34;destLocation&#34;] = _instance_id
            _restore_content_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;] \
                [&#34;restoreOptions&#34;][&#34;destination&#34;][&#34;inPlace&#34;] = False

        return _restore_content_json

    def restore_in_place(
            self,
            restore_content: list = None,
            restore_path: list = None,
            is_environment: bool = False,
            overwrite: bool = True,
            job_id: int = None):
        &#34;&#34;&#34;
            Method to run in- place restore for the content specified.

            Arguments:
                restore_content         (str)--     List of the content to restore
                    If content is environment,
                        List format:
                            list of strings, with each string corresponding to the environments display name, in lower case
                        Sample Input:
                            [ &#39;testenv1&#39; , &#39;testenv2&#39; , &#39;testenv3&#39; ]

                    If content is tables:
                        List format:
                            list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                                where environment name if the name of the environment to which the table belongs to
                        Sample input:
                            [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

                restore_path            (list)--    List of the paths of the items to restore
                    Instead of passing, the restore content, restore path can be passed
                    Restore path, is the path for each item, that is to be restored.
                        Path is returned by the browse operation

                is_environment          (bool)--    Whether to content to be restored is a table or an environment
                overwrite               (bool)--    Skip or overwrite content
                job_id                  (int)--     Job ID for point in time restores
            Returns:
                restore_job             (job)--     Instance of CVPySDK.Job for the restore job
        &#34;&#34;&#34;

        if restore_content is None and restore_path is None:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;, &#34;Need to have either of restore content or restore path&#34;)

        _restore_json = self._prepare_restore_json(
            restore_content=restore_content,
            restore_path=restore_path,
            is_environment=is_environment,
            job_id=job_id,
            overwrite=overwrite,
            is_out_of_place_restore=False)

        return self._process_restore_response(_restore_json)

    def launch_d365_licensing(self, run_for_all_clients=False):
        &#34;&#34;&#34;
            Method to launch Licensing API call.
            Arguments:
                run_for_all_clients(bool)      --  True if thread is to be run on all clients, False otherwise
                    default: False
        &#34;&#34;&#34;

        _LAUNCH_LICENSING = self._services[&#39;LAUNCH_O365_LICENSING&#39;]

        request_json = {
            &#34;subClient&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id)
            },
            &#34;runForAllClients&#34;: run_for_all_clients,
            &#34;appType&#34;: 6
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, _LAUNCH_LICENSING, request_json
        )

        if flag:
            try:
                if response.json():
                    if response.json().get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to Launch Licensing Thread\nError: &#34;{0}&#34;&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message))
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_environment_id_for_oop_restore(self, environment_name: str) -&gt; str:
        &#34;&#34;&#34;
            Get the Environment ID for an environment for Out of Place Restore

            Arguments:
                environment_name        (str)--     Name of the environment

            Returns:
                environment_id          (str)--     ID for the environment
        &#34;&#34;&#34;
        for environment in self.discovered_environments:
            if environment[&#39;displayName&#39;] == environment_name:
                _env_xml = environment.get(&#34;xmlGeneric&#34;)
                _env_json = json.loads(_env_xml)
                _env_id = _env_json.get(&#34;_instanceName&#34;)
        return _env_id

    def restore_out_of_place(
            self,
            restore_content: list = None,
            restore_path: list = None,
            is_environment: bool = False,
            overwrite: bool = True,
            job_id: int = None,
            destination_environment: str = str()):
        &#34;&#34;&#34;
            Method to run out-of-place restore for the content specified.

            Arguments:
                restore_content         (str)--     List of the content to restore
                    If content is environment,
                        List format:
                            list of strings, with each string corresponding to the environments display name, in lower case
                        Sample Input:
                            [ &#39;testenv1&#39; , &#39;testenv2&#39; , &#39;testenv3&#39; ]

                    If content is tables:
                        List format:
                            list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                                where environment name if the name of the environment to which the table belongs to
                        Sample input:
                            [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

                restore_path            (list)--    List of the paths of the items to restore
                    Instead of passing, the restore content, restore path can be passed
                    Restore path, is the path for each item, that is to be restored.
                        Path is returned by the browse operation

                is_environment          (bool)--    Whether to content to be restored is a table or an environment
                overwrite               (bool)--    Skip or overwrite content
                job_id                  (int)--     Job ID for point in time restores
                destination_environment (str)--     Destination environment name
            Returns:
                restore_job             (job)--     Instance of CVPySDK.Job for the restore job
        &#34;&#34;&#34;

        if restore_content is None and restore_path is None:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;, &#34;Need to have either of restore content or restore path&#34;)

        _restore_json = self._prepare_restore_json(
            restore_content=restore_content,
            restore_path=restore_path,
            is_environment=is_environment,
            job_id=job_id,
            overwrite=overwrite,
            is_out_of_place_restore=True,
            destination_environment=destination_environment)

        return self._process_restore_response(_restore_json)

    def browse(self,
               browse_path: list[str] = None,
               include_deleted_items: bool = False,
               till_time: int = -1):
        &#34;&#34;&#34;
            Browse for the backed up content for a Dynamics 365 subclient

            Arguments:
                browse_path         (list)  --      Path to be browsed
                    Sample Value:
                        [&#34;environment-name&#34; , &#34;table-name&#34;]
                include_deleted_items
                                    (bool)  --      Whether to include deleted items in the browse response
                till_time           (int)   --      Time-stamp for point in time browse
        &#34;&#34;&#34;
        _parent_path = str()

        _environments = self._perform_browse(parent_path=_parent_path, till_time=till_time, item_type=2,
                                             include_deleted_items=include_deleted_items)
        _browse_response = copy.deepcopy(_environments)

        if browse_path:
            _item_type: int = 3
            for path in browse_path:
                _parent_path = self._get_guid_for_path(_browse_response, path)
                if not _parent_path:
                    raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                                       f&#34;Path: {path} not found in browse content: {_browse_response}&#34;)
                _browse_response = self._perform_browse(parent_path=_parent_path, till_time=till_time,
                                                        item_type=_item_type,
                                                        include_deleted_items=include_deleted_items)
                _item_type += 1

        return _browse_response

    def _get_guid_for_path(self, browse_response: dict, path: str) -&gt; str:
        &#34;&#34;&#34;
            Method to get the browse GUID corresponding to the path

            Arguments:
                browse_reponse          (dict)--    Response from the browse query
                path                    (str)--     Path for which GUID is to be fetched

            Example:
                from the browse for &#34;d365-env&#34;, find the GUID for the &#34;Accounts&#34; table

            The GUID would be used im the subsequent browse requests
        &#34;&#34;&#34;
        guid: str = str()
        for item in browse_response:
            if item.get(&#34;appSpecific&#34;).get(&#34;d365Item&#34;).get(&#34;displayName&#34;) == path:
                guid = item.get(&#34;cvObjectGuid&#34;)
        return guid

    def _perform_browse(self, parent_path: str = str(), till_time: int = -1, item_type: int = 2,
                        include_deleted_items: bool = False):
        &#34;&#34;&#34;
            Perform a browse of the backed up content
            Arguments:
                parent_path         (str)   --      GUID for the parent path
                include_deleted_items
                                    (bool)  --      Whether to include deleted items in the browse response
                till_time           (int)   --      Time-stamp for point in time browse
                item_type           (int)   --      Item type to be browsed
        &#34;&#34;&#34;
        _browse_default_params = self._get_dynamics365_browse_params(item_type=item_type)

        _query_params = _browse_default_params.get(&#34;query_params&#34;)
        _file_filter = _browse_default_params.get(&#34;file_filter&#34;)
        _sort_params = _browse_default_params.get(&#34;sort_param&#34;)
        _common_filters = _browse_default_params.get(&#34;common_filters&#34;)

        if parent_path:
            _parent_path_filter = {
                &#34;field&#34;: &#34;PARENT_GUID&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        f&#34;{parent_path}&#34;
                    ]
                }
            }
            _file_filter.append(_parent_path_filter)

        if till_time != -1:
            _backup_time_filter = {
                &#34;field&#34;: &#34;BACKUPTIME&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;0&#34;,
                        f&#34;{till_time}&#34;
                    ]
                }
            }
            _file_filter.append(_backup_time_filter)

        if include_deleted_items:
            _common_filter = {
                &#34;groupType&#34;: 0,
                &#34;field&#34;: &#34;CISTATE&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;1&#34;,
                        &#34;3333&#34;,
                        &#34;3334&#34;,
                        &#34;3335&#34;
                    ]
                }
            }
            _common_filters[0] = _common_filter

        return self.do_web_search(query_params=_query_params, file_filter=_file_filter, sort_param=_sort_params,
                                  common_filters=_common_filters)

    def _get_dynamics365_browse_params(self, item_type: int = 2) -&gt; dict:
        &#34;&#34;&#34;
            Default dictionary for the browse parameters for a Dynamics 365 browse query.

            Arguments:
                item_type       (int)   --  Item type to br browsed for
        &#34;&#34;&#34;
        _query_params: list = [
            {
                &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;,
                &#34;value&#34;: &#34;true&#34;
            },
            {
                &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                &#34;value&#34;: &#34;D365_ENTITY_DISP_NAME,D365_ENTITY_DISP_NAME,CONTENTID,CV_OBJECT_GUID,CV_TURBO_GUID,&#34;
                         &#34;PARENT_GUID,AFILEID,AFILEOFFSET,COMMCELLNO,APPID,D365_ID,D365_DISPLAYNAME,&#34;
                         &#34;FILE_CREATEDTIME,MODIFIEDTIME,BACKUPTIME,D365_OBJECT_TYPE,D365_CONTENTHASH,D365_FLAGS,&#34;
                         &#34;D365_ENTITYSET_ID,D365_ENTITYSET_NAME,D365_INSTANCE_ID,D365_INSTANCE_NAME,&#34;
                         &#34;D365_CREATEDBY_GUID,D365_CREATEDBY_NAME,D365_MODIFIEDBY_GUID,D365_MODIFIEDBY_NAME,&#34;
                         &#34;D365_OWNER_GUID,D365_OWNER_NAME,DATE_DELETED,CISTATE &#34;
            },
            {
                &#34;param&#34;: &#34;COLLAPSE_FIELD&#34;,
                &#34;value&#34;: &#34;CV_OBJECT_GUID&#34;
            }]

        _sort_params: list = [
            {
                &#34;sortDirection&#34;: 0,
                &#34;sortField&#34;: &#34;D365_DISPLAYNAME&#34;
            }
        ]

        _common_filters: list = [
            {
                &#34;groupType&#34;: 0,
                &#34;field&#34;: &#34;CISTATE&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;1&#34;
                    ]
                }
            },
            {
                &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                &#34;intraFieldOpStr&#34;: &#34;None&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;isMoniker&#34;: False,
                    &#34;isRange&#34;: False,
                    &#34;values&#34;: [
                        &#34;true&#34;
                    ]
                }
            }
        ]

        _file_filter: list = [
            {
                &#34;field&#34;: &#34;D365_OBJECT_TYPE&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        f&#34;{item_type}&#34;
                    ]
                }
            }
        ]
        return {&#34;query_params&#34;: _query_params, &#34;file_filter&#34;: _file_filter, &#34;sort_param&#34;: _sort_params,
                &#34;common_filters&#34;: _common_filters}

    @property
    def browse_item_type(self):
        &#34;&#34;&#34;Dynamics 365 item types&#34;&#34;&#34;
        _browse_item_type = {&#34;environment&#34;: 2,
                             &#34;table&#34;: 3,
                             &#34;record&#34;: 4}
        return _browse_item_type</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient" href="../o365apps_subclient.html#cvpysdk.subclients.o365apps_subclient.O365AppsSubclient">O365AppsSubclient</a></li>
<li><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="../casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.browse_item_type"><code class="name">var <span class="ident">browse_item_type</span></code></dt>
<dd>
<div class="desc"><p>Dynamics 365 item types</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L1216-L1222" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def browse_item_type(self):
    &#34;&#34;&#34;Dynamics 365 item types&#34;&#34;&#34;
    _browse_item_type = {&#34;environment&#34;: 2,
                         &#34;table&#34;: 3,
                         &#34;record&#34;: 4}
    return _browse_item_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discovered_environments"><code class="name">var <span class="ident">discovered_environments</span></code></dt>
<dd>
<div class="desc"><p>Property to get the environments discovered by the Dynamics 365 subclient.</p>
<p>If updated list is required, call refresh method prior to using this property.</p>
<h2 id="returns">Returns</h2>
<p>discovered_environments
(dict)&ndash;
Dictionary of discovered environments</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L150-L162" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def discovered_environments(self):
    &#34;&#34;&#34;
        Property to get the environments discovered by the Dynamics 365 subclient.

        If updated list is required, call refresh method prior to using this property.

        Returns:
            discovered_environments       (dict)--    Dictionary of discovered environments
    &#34;&#34;&#34;
    if not bool(self._discovered_environments):
        self.discover_environments()
    return self._discovered_environments</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discovered_tables"><code class="name">var <span class="ident">discovered_tables</span></code></dt>
<dd>
<div class="desc"><p>Property to get the tables discovered by the Dynamics 365 subclient.</p>
<p>If updated list is required, call refresh method prior to using this property.</p>
<h2 id="returns">Returns</h2>
<p>discovered_tables
(dict)&ndash;
Dictionary of discovered tables</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L164-L176" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def discovered_tables(self):
    &#34;&#34;&#34;
        Property to get the tables discovered by the Dynamics 365 subclient.

        If updated list is required, call refresh method prior to using this property.

        Returns:
            discovered_tables      (dict)--    Dictionary of discovered tables
    &#34;&#34;&#34;
    if not bool(self._discovered_tables):
        self.discover_tables()
    return self._discovered_tables</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.backup_environments"><code class="name flex">
<span>def <span class="ident">backup_environments</span></span>(<span>self, environments_list: list)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to run backup for the specified environments of a Dynamics 365 subclient</p>
<h2 id="arguments">Arguments</h2>
<p>environments_list
(list)&ndash;
List of environments to be backed up
List Format, for backing up specified environments:
Each list element should be a string of the name of the environment
Sample List:
['testenv1','testenv2','testenv3']</p>
<h2 id="returns">Returns</h2>
<p>backup_job
(Job)&ndash;
CVPySDK.Job class instance for that particular backup job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L659-L673" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup_environments(self, environments_list: list):
    &#34;&#34;&#34;
        Method to run backup for the specified environments of a Dynamics 365 subclient

        Arguments:
            environments_list     (list)--    List of environments to be backed up
                List Format, for backing up specified environments:
                    Each list element should be a string of the name of the environment
                Sample List:
                    [&#39;testenv1&#39;,&#39;testenv2&#39;,&#39;testenv3&#39;]

        Returns:
            backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
    &#34;&#34;&#34;
    return self._run_backup(backup_content=environments_list, is_environment=True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.backup_tables"><code class="name flex">
<span>def <span class="ident">backup_tables</span></span>(<span>self, tables_list: list, force_full_backup: bool = False)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to run backup for the specified tables of a Dynamics 365 subclient</p>
<h2 id="arguments">Arguments</h2>
<p>tables_list
(list)&ndash;
List of tables to be backed up
List Format
Each list element should be a tuple of the format:
("environment_name","table_name")
environment_name is the name of the environment to which the table belongs to
table_name is the name of the table to be backed up</p>
<pre><code>Sample input:
    [ ("testenv1" , "account") , ("testenv2","note") , ("testenv1","attachments")]
</code></pre>
<h2 id="returns">Returns</h2>
<p>backup_job
(Job)&ndash;
CVPySDK.Job class instance for that particular backup job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L640-L657" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup_tables(self, tables_list: list, force_full_backup: bool = False):
    &#34;&#34;&#34;
        Method to run backup for the specified tables of a Dynamics 365 subclient

        Arguments:
            tables_list     (list)--    List of tables to be backed up
                List Format
                    Each list element should be a tuple of the format:
                        (&#34;environment_name&#34;,&#34;table_name&#34;)
                            environment_name is the name of the environment to which the table belongs to
                            table_name is the name of the table to be backed up

                Sample input:
                    [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]
        Returns:
            backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
    &#34;&#34;&#34;
    return self._run_backup(backup_content=tables_list, is_environment=False, force_full_backup=force_full_backup)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, browse_path: list[str] = None, include_deleted_items: bool = False, till_time: int = -1)</span>
</code></dt>
<dd>
<div class="desc"><p>Browse for the backed up content for a Dynamics 365 subclient</p>
<h2 id="arguments">Arguments</h2>
<p>browse_path
(list)
&ndash;
Path to be browsed
Sample Value:
["environment-name" , "table-name"]
include_deleted_items
(bool)
&ndash;
Whether to include deleted items in the browse response
till_time
(int)
&ndash;
Time-stamp for point in time browse</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L1028-L1061" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self,
           browse_path: list[str] = None,
           include_deleted_items: bool = False,
           till_time: int = -1):
    &#34;&#34;&#34;
        Browse for the backed up content for a Dynamics 365 subclient

        Arguments:
            browse_path         (list)  --      Path to be browsed
                Sample Value:
                    [&#34;environment-name&#34; , &#34;table-name&#34;]
            include_deleted_items
                                (bool)  --      Whether to include deleted items in the browse response
            till_time           (int)   --      Time-stamp for point in time browse
    &#34;&#34;&#34;
    _parent_path = str()

    _environments = self._perform_browse(parent_path=_parent_path, till_time=till_time, item_type=2,
                                         include_deleted_items=include_deleted_items)
    _browse_response = copy.deepcopy(_environments)

    if browse_path:
        _item_type: int = 3
        for path in browse_path:
            _parent_path = self._get_guid_for_path(_browse_response, path)
            if not _parent_path:
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                                   f&#34;Path: {path} not found in browse content: {_browse_response}&#34;)
            _browse_response = self._perform_browse(parent_path=_parent_path, till_time=till_time,
                                                    item_type=_item_type,
                                                    include_deleted_items=include_deleted_items)
            _item_type += 1

    return _browse_response</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discover_environments"><code class="name flex">
<span>def <span class="ident">discover_environments</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to get the environments discovered from the Dynamics 365 CRM subclient</p>
<h2 id="returns">Returns</h2>
<p>discovered_environments
(dict)&ndash;
Dictionary of discovered environments</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L139-L148" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def discover_environments(self):
    &#34;&#34;&#34;
        Method to get the environments discovered from the Dynamics 365 CRM subclient

        Returns:
            discovered_environments       (dict)--    Dictionary of discovered environments

    &#34;&#34;&#34;
    self._discovered_environments = self._instance_object.discover_content(environment_discovery=True)
    return self._discovered_environments</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discover_tables"><code class="name flex">
<span>def <span class="ident">discover_tables</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to get the tables discovered from the MS Dynamics 365 CRM subclient</p>
<h2 id="returns">Returns</h2>
<p>discovered_tables
(dict)&ndash;
Dictionary of returned tables</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L128-L137" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def discover_tables(self):
    &#34;&#34;&#34;
        Method to get the tables discovered from the MS Dynamics 365 CRM subclient

        Returns:
            discovered_tables       (dict)--    Dictionary of returned tables

    &#34;&#34;&#34;
    self._discovered_tables = self._instance_object.discover_content(environment_discovery=False)
    return self._discovered_tables</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.get_associated_environments"><code class="name flex">
<span>def <span class="ident">get_associated_environments</span></span>(<span>self, refresh: bool = False)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to get the environments associated with a Dynamics 365 CRM client</p>
<h2 id="arguments">Arguments</h2>
<p>refresh
(bool)&ndash;
Whether to refresh the dictionary contents
If True
get associated environments, will fetch the latest associations and return them</p>
<h2 id="returns">Returns</h2>
<dl>
<dt>associated_environments
(list)&ndash;
List of environments associated with the client</dt>
<dt>Format:</dt>
<dt>Each list element will be a dictionary denoting that particular environment</dt>
<dt>Dictionary keys/ format will be:</dt>
<dt><code>
name </code></dt>
<dd>
<pre><code>     name of the table
plan:               Dynamics 365 plan used for content association
is_environment:     True for an environment
userAccountInfo:    Metadata info for that environment
</code></pre>
<p>Sample Response:
{
'name': 'sample-environment-name',
'id': '<environment-ID>&gt;',
'userAccountInfo':
{
'aliasName': 'https://<org-url-name>.crm.dynamics.com',
'itemType': 0,
'ItemClassification': 0,
'displayName': 'org-environment-display-name',
'BackupSetId': 0,
'isAutoDiscoveredUser': False,
'lastBackupJobRanTime': 'time': <last-backup-time>,
'IdxCollectionTime': {'time': <last-index-playback-time>},
user': {
'<em>type</em>': 13,
'userGUID': '<env-GUID>'
}
},
'plan': {'planName': '<name-of-plan>', 'planId': <id-of-plan>},
'is_environment': True}</p>
</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L296-L340" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_associated_environments(self, refresh: bool = False):
    &#34;&#34;&#34;
        Method to get the environments associated with a Dynamics 365 CRM client

        Arguments:
            refresh                     (bool)--    Whether to refresh the dictionary contents
                If True
                    get associated environments, will fetch the latest associations and return them

        Returns:
            associated_environments     (list)--    List of environments associated with the client
                Format:
                    Each list element will be a dictionary denoting that particular environment
                    Dictionary keys/ format will be:
                        name :              name of the table
                        plan:               Dynamics 365 plan used for content association
                        is_environment:     True for an environment
                        userAccountInfo:    Metadata info for that environment

                Sample Response:
                    {
                            &#39;name&#39;: &#39;sample-environment-name&#39;,
                            &#39;id&#39;: &#39;&lt;environment-ID&gt;&gt;&#39;,
                            &#39;userAccountInfo&#39;:
                            {
                                &#39;aliasName&#39;: &#39;https://&lt;org-url-name&gt;.crm.dynamics.com&#39;,
                                &#39;itemType&#39;: 0,
                                &#39;ItemClassification&#39;: 0,
                                &#39;displayName&#39;: &#39;org-environment-display-name&#39;,
                                &#39;BackupSetId&#39;: 0,
                                &#39;isAutoDiscoveredUser&#39;: False,
                                &#39;lastBackupJobRanTime&#39;: &#39;time&#39;: &lt;last-backup-time&gt;,
                                &#39;IdxCollectionTime&#39;: {&#39;time&#39;: &lt;last-index-playback-time&gt;},
                                user&#39;: {
                                    &#39;_type_&#39;: 13,
                                    &#39;userGUID&#39;: &#39;&lt;env-GUID&gt;&#39;
                                    }
                            },
                            &#39;plan&#39;: {&#39;planName&#39;: &#39;&lt;name-of-plan&gt;&#39;, &#39;planId&#39;: &lt;id-of-plan&gt;},
                             &#39;is_environment&#39;: True}

    &#34;&#34;&#34;
    if refresh is True:
        self._associated_environments = self._get_associated_content(is_environment=True)
    return self._associated_environments</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.get_associated_tables"><code class="name flex">
<span>def <span class="ident">get_associated_tables</span></span>(<span>self, refresh: bool = False)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to get the tables associated with a Dynamics 365 CRM client</p>
<h2 id="arguments">Arguments</h2>
<p>refresh
(bool)&ndash;
Whether to refresh the dictionary contents
If True
get associated environments, will fetch the latest associations and return them</p>
<h2 id="returns">Returns</h2>
<dl>
<dt>associated_tables
(list)&ndash;
List of tables associated with the client</dt>
<dt>Format:</dt>
<dt>Each list element will be a dictionary denoting that particular
table</dt>
<dt>Dictionary keys/ format will be:</dt>
<dt><code>
name </code></dt>
<dd>
<p>name of the table
environment_name : name of the environment to which the table belongs to
plan: Dynamics 365 plan used for content association
is_environment: False for a Table
userAccountInfo:
Metadata info for that table</p>
<p>Sample Response:
{
'name': 'account',
'environment_name': 'sample-environment-name',
'userAccountInfo':
{
'aliasName': 'https://<org-url-name>.crm.dynamics.com/api/data/v9.1/account',
'displayName': 'Account,
'ParentWebGuid': 'org-environment-name',
'lastBackupJobRanTime': {'time': <last-backup-time>},
'IdxCollectionTime': {'time': <last-index-time>},
'user': {
'<em>type</em>': 13,
'userGUID': '<table-GUID>&gt;'
}
},
'plan': {
'planName': '<PLAN-NAME>', 'planId': <plan-id>},
'is_environment': False
}
Environment name/ URL in the sample response is for description purpose only</p>
</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L249-L294" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_associated_tables(self, refresh: bool = False):
    &#34;&#34;&#34;
        Method to get the tables associated with a Dynamics 365 CRM client

        Arguments:
            refresh                     (bool)--    Whether to refresh the dictionary contents
                If True
                    get associated environments, will fetch the latest associations and return them

        Returns:
            associated_tables     (list)--    List of tables associated with the client
                Format:
                    Each list element will be a dictionary denoting that particular  table
                    Dictionary keys/ format will be:
                        name : name of the table
                        environment_name : name of the environment to which the table belongs to
                        plan: Dynamics 365 plan used for content association
                        is_environment: False for a Table
                        userAccountInfo:    Metadata info for that table

                Sample Response:
                    {
                        &#39;name&#39;: &#39;account&#39;,
                        &#39;environment_name&#39;: &#39;sample-environment-name&#39;,
                        &#39;userAccountInfo&#39;:
                            {
                            &#39;aliasName&#39;: &#39;https://&lt;org-url-name&gt;.crm.dynamics.com/api/data/v9.1/account&#39;,
                            &#39;displayName&#39;: &#39;Account,
                            &#39;ParentWebGuid&#39;: &#39;org-environment-name&#39;,
                            &#39;lastBackupJobRanTime&#39;: {&#39;time&#39;: &lt;last-backup-time&gt;},
                            &#39;IdxCollectionTime&#39;: {&#39;time&#39;: &lt;last-index-time&gt;},
                            &#39;user&#39;: {
                                &#39;_type_&#39;: 13,
                                 &#39;userGUID&#39;: &#39;&lt;table-GUID&gt;&gt;&#39;
                                 }
                            },
                        &#39;plan&#39;: {
                            &#39;planName&#39;: &#39;&lt;PLAN-NAME&gt;&#39;, &#39;planId&#39;: &lt;plan-id&gt;},
                        &#39;is_environment&#39;: False
                    }
                Environment name/ URL in the sample response is for description purpose only

    &#34;&#34;&#34;
    if refresh is True:
        self._associated_tables = self._get_associated_content(is_environment=False)
    return self._associated_tables</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.launch_client_level_full_backup"><code class="name flex">
<span>def <span class="ident">launch_client_level_full_backup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to run full backup for the Dynamics 365 subclient</p>
<h2 id="returns">Returns</h2>
<p>backup_job
(Job)&ndash;
CVPySDK.Job class instance for that particular backup job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L675-L682" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def launch_client_level_full_backup(self):
    &#34;&#34;&#34;
        Method to run full backup for the Dynamics 365 subclient

        Returns:
            backup_job          (Job)--     CVPySDK.Job class instance for that particular backup job
    &#34;&#34;&#34;
    return self._run_backup(backup_content=[], is_environment=False, force_full_backup=True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.launch_d365_licensing"><code class="name flex">
<span>def <span class="ident">launch_d365_licensing</span></span>(<span>self, run_for_all_clients=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to launch Licensing API call.</p>
<h2 id="arguments">Arguments</h2>
<p>run_for_all_clients(bool)
&ndash;
True if thread is to be run on all clients, False otherwise
default: False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L920-L956" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def launch_d365_licensing(self, run_for_all_clients=False):
    &#34;&#34;&#34;
        Method to launch Licensing API call.
        Arguments:
            run_for_all_clients(bool)      --  True if thread is to be run on all clients, False otherwise
                default: False
    &#34;&#34;&#34;

    _LAUNCH_LICENSING = self._services[&#39;LAUNCH_O365_LICENSING&#39;]

    request_json = {
        &#34;subClient&#34;: {
            &#34;clientId&#34;: int(self._client_object.client_id)
        },
        &#34;runForAllClients&#34;: run_for_all_clients,
        &#34;appType&#34;: 6
    }

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, _LAUNCH_LICENSING, request_json
    )

    if flag:
        try:
            if response.json():
                if response.json().get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    output_string = &#39;Failed to Launch Licensing Thread\nError: &#34;{0}&#34;&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message))
                else:
                    self.refresh()
        except ValueError:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, restore_content: list = None, restore_path: list = None, is_environment: bool = False, overwrite: bool = True, job_id: int = None)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to run in- place restore for the content specified.</p>
<h2 id="arguments">Arguments</h2>
<p>restore_content
(str)&ndash;
List of the content to restore
If content is environment,
List format:
list of strings, with each string corresponding to the environments display name, in lower case
Sample Input:
[ 'testenv1' , 'testenv2' , 'testenv3' ]</p>
<pre><code>If content is tables:
    List format:
        list of tuples, with each tuple, of the form: "environment_name","table_name"
            where environment name if the name of the environment to which the table belongs to
    Sample input:
        [ ("testenv1" , "account") , ("testenv2","note") , ("testenv1","attachments")]
</code></pre>
<p>restore_path
(list)&ndash;
List of the paths of the items to restore
Instead of passing, the restore content, restore path can be passed
Restore path, is the path for each item, that is to be restored.
Path is returned by the browse operation</p>
<p>is_environment
(bool)&ndash;
Whether to content to be restored is a table or an environment
overwrite
(bool)&ndash;
Skip or overwrite content
job_id
(int)&ndash;
Job ID for point in time restores</p>
<h2 id="returns">Returns</h2>
<p>restore_job
(job)&ndash;
Instance of CVPySDK.Job for the restore job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L870-L918" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        restore_content: list = None,
        restore_path: list = None,
        is_environment: bool = False,
        overwrite: bool = True,
        job_id: int = None):
    &#34;&#34;&#34;
        Method to run in- place restore for the content specified.

        Arguments:
            restore_content         (str)--     List of the content to restore
                If content is environment,
                    List format:
                        list of strings, with each string corresponding to the environments display name, in lower case
                    Sample Input:
                        [ &#39;testenv1&#39; , &#39;testenv2&#39; , &#39;testenv3&#39; ]

                If content is tables:
                    List format:
                        list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                            where environment name if the name of the environment to which the table belongs to
                    Sample input:
                        [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

            restore_path            (list)--    List of the paths of the items to restore
                Instead of passing, the restore content, restore path can be passed
                Restore path, is the path for each item, that is to be restored.
                    Path is returned by the browse operation

            is_environment          (bool)--    Whether to content to be restored is a table or an environment
            overwrite               (bool)--    Skip or overwrite content
            job_id                  (int)--     Job ID for point in time restores
        Returns:
            restore_job             (job)--     Instance of CVPySDK.Job for the restore job
    &#34;&#34;&#34;

    if restore_content is None and restore_path is None:
        raise SDKException(&#34;Subclient&#34;, &#34;101&#34;, &#34;Need to have either of restore content or restore path&#34;)

    _restore_json = self._prepare_restore_json(
        restore_content=restore_content,
        restore_path=restore_path,
        is_environment=is_environment,
        job_id=job_id,
        overwrite=overwrite,
        is_out_of_place_restore=False)

    return self._process_restore_response(_restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, restore_content: list = None, restore_path: list = None, is_environment: bool = False, overwrite: bool = True, job_id: int = None, destination_environment: str = '')</span>
</code></dt>
<dd>
<div class="desc"><p>Method to run out-of-place restore for the content specified.</p>
<h2 id="arguments">Arguments</h2>
<p>restore_content
(str)&ndash;
List of the content to restore
If content is environment,
List format:
list of strings, with each string corresponding to the environments display name, in lower case
Sample Input:
[ 'testenv1' , 'testenv2' , 'testenv3' ]</p>
<pre><code>If content is tables:
    List format:
        list of tuples, with each tuple, of the form: "environment_name","table_name"
            where environment name if the name of the environment to which the table belongs to
    Sample input:
        [ ("testenv1" , "account") , ("testenv2","note") , ("testenv1","attachments")]
</code></pre>
<p>restore_path
(list)&ndash;
List of the paths of the items to restore
Instead of passing, the restore content, restore path can be passed
Restore path, is the path for each item, that is to be restored.
Path is returned by the browse operation</p>
<p>is_environment
(bool)&ndash;
Whether to content to be restored is a table or an environment
overwrite
(bool)&ndash;
Skip or overwrite content
job_id
(int)&ndash;
Job ID for point in time restores
destination_environment (str)&ndash;
Destination environment name</p>
<h2 id="returns">Returns</h2>
<p>restore_job
(job)&ndash;
Instance of CVPySDK.Job for the restore job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L975-L1026" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        restore_content: list = None,
        restore_path: list = None,
        is_environment: bool = False,
        overwrite: bool = True,
        job_id: int = None,
        destination_environment: str = str()):
    &#34;&#34;&#34;
        Method to run out-of-place restore for the content specified.

        Arguments:
            restore_content         (str)--     List of the content to restore
                If content is environment,
                    List format:
                        list of strings, with each string corresponding to the environments display name, in lower case
                    Sample Input:
                        [ &#39;testenv1&#39; , &#39;testenv2&#39; , &#39;testenv3&#39; ]

                If content is tables:
                    List format:
                        list of tuples, with each tuple, of the form: &#34;environment_name&#34;,&#34;table_name&#34;
                            where environment name if the name of the environment to which the table belongs to
                    Sample input:
                        [ (&#34;testenv1&#34; , &#34;account&#34;) , (&#34;testenv2&#34;,&#34;note&#34;) , (&#34;testenv1&#34;,&#34;attachments&#34;)]

            restore_path            (list)--    List of the paths of the items to restore
                Instead of passing, the restore content, restore path can be passed
                Restore path, is the path for each item, that is to be restored.
                    Path is returned by the browse operation

            is_environment          (bool)--    Whether to content to be restored is a table or an environment
            overwrite               (bool)--    Skip or overwrite content
            job_id                  (int)--     Job ID for point in time restores
            destination_environment (str)--     Destination environment name
        Returns:
            restore_job             (job)--     Instance of CVPySDK.Job for the restore job
    &#34;&#34;&#34;

    if restore_content is None and restore_path is None:
        raise SDKException(&#34;Subclient&#34;, &#34;101&#34;, &#34;Need to have either of restore content or restore path&#34;)

    _restore_json = self._prepare_restore_json(
        restore_content=restore_content,
        restore_path=restore_path,
        is_environment=is_environment,
        job_id=job_id,
        overwrite=overwrite,
        is_out_of_place_restore=True,
        destination_environment=destination_environment)

    return self._process_restore_response(_restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.set_environment_associations"><code class="name flex">
<span>def <span class="ident">set_environment_associations</span></span>(<span>self, environments_name: list, plan_name: str = '')</span>
</code></dt>
<dd>
<div class="desc"><p>Method to add environment associations
to a Dynamics 365 CRM client.</p>
<h2 id="arguments">Arguments</h2>
<p>environments_name
(list)&ndash;
List of environments to be associated to the content
List Format:
Each list element should be a string of the name of the environment
Sample Values:
['testenv1' , 'testenv2', 'testenv3']</p>
<p>plan_name
(str)&ndash;
Name of the Dynamics 365 Plan to be used for content association</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L508-L536" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_environment_associations(self, environments_name: list, plan_name: str = str()):
    &#34;&#34;&#34;
        Method to add environment associations
        to a Dynamics 365 CRM client.

        Arguments:
            environments_name     (list)--    List of environments to be associated to the content
                List Format:
                    Each list element should be a string of the name of the environment
                Sample Values:
                    [&#39;testenv1&#39; , &#39;testenv2&#39;, &#39;testenv3&#39;]

            plan_name       (str)--     Name of the Dynamics 365 Plan to be used for content association
    &#34;&#34;&#34;
    environments_info: list = self._environment_association_info_json(environments_name=environments_name)

    if self._commcell_object.plans.has_plan(plan_name.lower()):
        plan_id = int(self._commcell_object.plans[plan_name.lower()])

    else:
        raise SDKException(&#34;Subclient&#34;, &#34;101&#34;,
                           &#34;Dynamics 365 Plan does not exist&#34;)

    _env_association_json = self._set_association_json(is_environment=True)
    _env_association_json[&#34;cloudAppAssociation&#34;][&#34;plan&#34;] = {
        &#34;planId&#34;: plan_id
    }
    _env_association_json[&#34;cloudAppAssociation&#34;][&#34;cloudAppDiscoverinfo&#34;][&#34;userAccounts&#34;] = environments_info
    self._set_content_association(content_json=_env_association_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.set_table_associations"><code class="name flex">
<span>def <span class="ident">set_table_associations</span></span>(<span>self, tables_list: list, plan_name: str = '')</span>
</code></dt>
<dd>
<div class="desc"><p>Method to add table associations
to a Dynamics 365 CRM client.</p>
<h2 id="arguments">Arguments</h2>
<p>tables_list
(list)&ndash;
List of tables to be associated to the content
List Format:
Each list element should be a tuple of the format:
("table_name", "environment_name")
environment_name is the name of the environment to which the table belongs to
table_name is the name of the table to be associated
Sample input:
[ ("account", "testenv1") , ("note", "testenv2") , ("attachments", "testenv1")]</p>
<p>plan_name
(str)&ndash;
Name of the Dynamics 365 Plan to be used for content association</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/dynamics365_subclient.py#L444-L471" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_table_associations(self, tables_list: list, plan_name: str = str()):
    &#34;&#34;&#34;
        Method to add table associations
        to a Dynamics 365 CRM client.

        Arguments:
            tables_list     (list)--    List of tables to be associated to the content
                List Format:
                    Each list element should be a tuple of the format:
                        (&#34;table_name&#34;, &#34;environment_name&#34;)
                            environment_name is the name of the environment to which the table belongs to
                            table_name is the name of the table to be associated
                Sample input:
                    [ (&#34;account&#34;, &#34;testenv1&#34;) , (&#34;note&#34;, &#34;testenv2&#34;) , (&#34;attachments&#34;, &#34;testenv1&#34;)]

            plan_name       (str)--     Name of the Dynamics 365 Plan to be used for content association
    &#34;&#34;&#34;

    plan_id = int(self._commcell_object.plans[plan_name.lower()])

    tables_info = self._table_association_info_json(tables_list=tables_list)

    _table_association_json = self._set_association_json(is_environment=False)
    _table_association_json[&#34;cloudAppAssociation&#34;][&#34;plan&#34;] = {
        &#34;planId&#34;: plan_id
    }
    _table_association_json[&#34;cloudAppAssociation&#34;][&#34;cloudAppDiscoverinfo&#34;][&#34;userAccounts&#34;] = tables_info
    self._set_content_association(content_json=_table_association_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient" href="../o365apps_subclient.html#cvpysdk.subclients.o365apps_subclient.O365AppsSubclient">O365AppsSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.allow_multiple_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.backup" href="../../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.data_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.deduplication_options" href="../../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.description" href="../../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.disable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.disable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.display_name" href="../../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.do_web_search" href="../o365apps_subclient.html#cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.do_web_search">do_web_search</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.enable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.enable_backup_at_time" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.enable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.enable_trueup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.enable_trueup_days" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.encryption_flag" href="../../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.exclude_from_sla" href="../../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.find" href="../../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.find_latest_job" href="../../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.get_ma_associated_storagepolicy" href="../../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.is_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.is_blocklevel_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.is_default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.is_intelli_snap_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.is_on_demand_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.is_trueup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.last_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.list_media" href="../../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.name" href="../../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.network_agent" href="../../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.next_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.plan" href="../../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.process_index_retention" href="../o365apps_subclient.html#cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.process_index_retention">process_index_retention</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.properties" href="../../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.read_buffer_size" href="../../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.refresh" href="../../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.run_content_indexing" href="../../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.set_backup_nodes" href="../../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.set_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.snapshot_engine_name" href="../../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.software_compression" href="../../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.storage_ma" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.storage_ma_id" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.storage_policy" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.subclient_guid" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.subclient_id" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.subclient_name" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.unset_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.update_properties" href="../../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.cloudapps" href="index.html">cvpysdk.subclients.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient">MSDynamics365Subclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.backup_environments" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.backup_environments">backup_environments</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.backup_tables" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.backup_tables">backup_tables</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.browse" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.browse_item_type" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.browse_item_type">browse_item_type</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discover_environments" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discover_environments">discover_environments</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discover_tables" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discover_tables">discover_tables</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discovered_environments" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discovered_environments">discovered_environments</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discovered_tables" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.discovered_tables">discovered_tables</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.get_associated_environments" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.get_associated_environments">get_associated_environments</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.get_associated_tables" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.get_associated_tables">get_associated_tables</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.launch_client_level_full_backup" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.launch_client_level_full_backup">launch_client_level_full_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.launch_d365_licensing" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.launch_d365_licensing">launch_d365_licensing</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.restore_in_place" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.restore_out_of_place" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.set_environment_associations" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.set_environment_associations">set_environment_associations</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.set_table_associations" href="#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient.set_table_associations">set_table_associations</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>