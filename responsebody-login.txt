(Commvault) C:\Users\<USER>\MyProject\Commvault>python login.py          
Status Code: 200
Response Body: {'aliasName': '1', 'userGUID': '2AE69CA3-1DDA-4AB3-848D-DFCB06EC195D', 'loginAttempts': 0, 'remainingLockTime': 0, 'smtpAddress': '<EMAIL>', 'userName': 'admin', 'providerType': 1, 'ccn': 0, 'token': 'QSDK 3a63874c8abd5e922c879a82f0d9180670e17fe14d6b75b342bb5e96e28889efc37e8e574387403617b4ea1cad8d5475c40ddffc10f242d6d9b605658786af3b4781e89a5a0bea354e4a3fd764e94cf4d75446c0c6272d6b670b925ed0ed3e7b1de7f683aa444756b0dab71345371dd8afe39bbe7cbe0bc98bcbe420fa43d593af72598ccf93a9f4bb5c830a6c8e105f5ac1a71cd4e95d9e6dd5b4de88772d7d7c098892d7dccb4bdd9fe2ee7b8057f087fdee29fef6e53b062cb7607f209b51dc561321aaaab3d02ff650af437ddb95d8d0fc2beeae9bc44', 'capability': *************, 'forcePasswordChange': False, 'isAccountLocked': False, 'ownerOrganization': {'GUID': '95189d4e-4a7f-4cb3-969f-80d5871ac13f', 'providerId': 0, 'providerDomainName': 'Commcell'}, 'additionalResp': {'nameValues': [{'name': 'USERNAME', 'value': 'admin'}, {'name': 'autoLoginType'}, {'name': 'fullName', 'value': 'Administrator'}]}, 'providerOrganization': {'GUID': '95189d4e-4a7f-4cb3-969f-80d5871ac13f', 'providerId': 0, 'providerDomainName': 'Commcell'}, 'errList': [], 'company': {'providerId': 0, 'providerDomainName': 'Commcell'}}

Login successful! Authentication token:
QSDK 3a63874c8abd5e922c879a82f0d9180670e17fe14d6b75b342bb5e96e28889efc37e8e574387403617b4ea1cad8d5475c40ddffc10f242d6d9b605658786af3b4781e89a5a0bea354e4a3fd764e94cf4d75446c0c6272d6b670b925ed0ed3e7b1de7f683aa444756b0dab71345371dd8afe39bbe7cbe0bc98bcbe420fa43d593af72598ccf93a9f4bb5c830a6c8e105f5ac1a71cd4e95d9e6dd5b4de88772d7d7c098892d7dccb4bdd9fe2ee7b8057f087fdee29fef6e53b062cb7607f209b51dc561321aaaab3d02ff650af437ddb95d8d0fc2beeae9bc44


QSDK 3739b399d1154f7fc324c7da4ebfadf10e2bd22c4ab55974eb9b1d12c4375acef8e435287285ad6615f324a073e676f5f569184a6b28b4d820536c592837288867ea6dd06a1cf62616a2f4e00846ed6df5a4e58f2ae87b5a86b59d37e1340d7562ba6eb0cbf504ad35c506947b587ba4db56c73fec4bdc68a36140f4fa3d058520f6b91b2d00111f926b386fe0eb37abfafd1162247acb473e6f1c07b7798a4fc7

QSDK 332a062ad89e2bdf34fe538c392303126abaeca55787ce236c511a577399645caf46104086eb6674399c0f5fd22733fdf297d717ffa8aa5ddcae216a23062a9c109bcf0617af3f42dcaffffc339c8c7ae3f42a73b94dc660fe46715ef0cc8b423990a8fd4f4eab1b270c49c2f87130e6809b3fe35ec26892531a181cd801fb52050eadde4ab0b22ed4ada15a6fa2820d4470e665534c0ffe682f5474c129c6a6970abe064a45cd3c555acae56301a43ad1cbf30195af1b7e1a8dfb29deabceb035246cb2ac152cbc9e8aa0d407021d77b6e7966bbaa7de472

















