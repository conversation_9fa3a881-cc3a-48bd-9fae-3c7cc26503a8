<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.constants API documentation</title>
<meta name="description" content="Helper file to maintain all the constants used in the SDK …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.constants</code></h1>
</header>
<section id="section-intro">
<p>Helper file to maintain all the constants used in the SDK</p>
<p>TIMEZONES
&ndash;
Dict which maintains the list of all timezones</p>
<p>ENTITY_TYPE_MAP
&ndash;
Dict containing mapping of entity type codes (returned by company API) to Entity Name</p>
<p>HypervisorType
&ndash;
Enum which maintains the list of all the hypervisors supported by SDK</p>
<p>AppIDAType
&ndash;
Enum which maintains the list of all the IDA type values</p>
<p>InstanceBackupType
&ndash;
Enum for backup type for instance</p>
<p>SQLDefines
&ndash;
Class which maintains the defines list for SQL Server</p>
<p>AdvancedJobDetailType
&ndash;
Enum to maintain advanced job details info type</p>
<p>VSALiveSyncStatus
&ndash;
Enum to maintain status of the VSA Live sync</p>
<p>VSAFailOverStatus
&ndash;
Enum to maintain Failover status of the VSA Live sync</p>
<p>ApplicationGroup
&ndash;
Enum to maintain Application Group Types.</p>
<p>VsInstanceType
&ndash;
Class to store VsInstance dict</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L1-L818" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright 2020 Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Helper file to maintain all the constants used in the SDK

TIMEZONES               --  Dict which maintains the list of all timezones

ENTITY_TYPE_MAP         --  Dict containing mapping of entity type codes (returned by company API) to Entity Name

HypervisorType          --  Enum which maintains the list of all the hypervisors supported by SDK

AppIDAType              --  Enum which maintains the list of all the IDA type values

InstanceBackupType      --  Enum for backup type for instance

SQLDefines              --  Class which maintains the defines list for SQL Server

AdvancedJobDetailType   --  Enum to maintain advanced job details info type

VSALiveSyncStatus       --  Enum to maintain status of the VSA Live sync

VSAFailOverStatus       --  Enum to maintain Failover status of the VSA Live sync

ApplicationGroup        --  Enum to maintain Application Group Types.

VsInstanceType         --  Class to store VsInstance dict
&#34;&#34;&#34;

from enum import Enum, auto


TIMEZONES = {
    &#34;Olson TZID&#34;: &#34;Windows Timezone ID&#34;,
    &#34;Pacific/Apia&#34;: &#34;Samoa Standard Time&#34;,
    &#34;Pacific/Midway&#34;: &#34;Hawaiian Standard Time&#34;,
    &#34;Pacific/Niue&#34;: &#34;Samoa Standard Time&#34;,
    &#34;Pacific/Pago_Pago&#34;: &#34;Samoa Standard Time&#34;,
    &#34;America/Adak&#34;: &#34;Hawaiian Standard Time&#34;,
    &#34;Pacific/Fakaofo&#34;: &#34;Hawaiian Standard Time&#34;,
    &#34;Pacific/Honolulu&#34;: &#34;Hawaiian Standard Time&#34;,
    &#34;Pacific/Johnston&#34;: &#34;Hawaiian Standard Time&#34;,
    &#34;Pacific/Rarotonga&#34;: &#34;Hawaiian Standard Time&#34;,
    &#34;Pacific/Tahiti&#34;: &#34;Hawaiian Standard Time&#34;,
    &#34;Pacific/Marquesas&#34;: &#34;Hawaiian Standard Time&#34;,
    &#34;America/Anchorage&#34;: &#34;Alaskan Standard Time&#34;,
    &#34;America/Juneau&#34;: &#34;Alaskan Standard Time&#34;,
    &#34;America/Nome&#34;: &#34;Alaskan Standard Time&#34;,
    &#34;America/Yakutat&#34;: &#34;Alaskan Standard Time&#34;,
    &#34;Pacific/Gambier&#34;: &#34;Alaskan Standard Time&#34;,
    &#34;America/Dawson&#34;: &#34;Pacific Standard Time&#34;,
    &#34;America/Los_Angeles&#34;: &#34;Pacific Standard Time&#34;,
    &#34;America/Santa_Isabel&#34;: &#34;Pacific Standard Time (Mexico)&#34;,
    &#34;America/Tijuana&#34;: &#34;Pacific Standard Time (Mexico)&#34;,
    &#34;America/Vancouver&#34;: &#34;Pacific Standard Time&#34;,
    &#34;America/Whitehorse&#34;: &#34;Pacific Standard Time&#34;,
    &#34;Pacific/Pitcairn&#34;: &#34;Pacific Standard Time&#34;,
    &#34;America/Boise&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Cambridge_Bay&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Chihuahua&#34;: &#34;Mountain Standard Time (Mexico)&#34;,
    &#34;America/Dawson_Creek&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Denver&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Edmonton&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Hermosillo&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Inuvik&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Mazatlan&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Ojinaga&#34;: &#34;US Mountain Standard Time&#34;,
    &#34;America/Phoenix&#34;: &#34;US Mountain Standard Time&#34;,
    &#34;America/Shiprock&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Yellowknife&#34;: &#34;Mountain Standard Time&#34;,
    &#34;America/Belize&#34;: &#34;Central America Standard Time&#34;,
    &#34;America/Cancun&#34;: &#34;Central Standard Time (Mexico)&#34;,
    &#34;America/Chicago&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Costa_Rica&#34;: &#34;Central America Standard Time&#34;,
    &#34;America/El_Salvador&#34;: &#34;Central America Standard Time&#34;,
    &#34;America/Guatemala&#34;: &#34;Central America Standard Time&#34;,
    &#34;America/Indiana/Knox&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Indiana/Tell_City&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Managua&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Matamoros&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Menominee&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Merida&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Mexico_City&#34;: &#34;Central Standard Time (Mexico)&#34;,
    &#34;America/Monterrey&#34;: &#34;Central Standard Time (Mexico)&#34;,
    &#34;America/North_Dakota/Center&#34;: &#34;Central Standard Time&#34;,
    &#34;America/North_Dakota/New_Salem&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Rainy_River&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Rankin_Inlet&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Regina&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Swift_Current&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Tegucigalpa&#34;: &#34;Central Standard Time&#34;,
    &#34;America/Winnipeg&#34;: &#34;Central Standard Time&#34;,
    &#34;Pacific/Easter&#34;: &#34;Central America Standard Time&#34;,
    &#34;Pacific/Galapagos&#34;: &#34;Central America Standard Time&#34;,
    &#34;America/Atikokan&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Bogota&#34;: &#34;SA Pacific Standard Time&#34;,
    &#34;America/Cayman&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Detroit&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Grand_Turk&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Guayaquil&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Havana&#34;: &#34;SA Pacific Standard Time&#34;,
    &#34;America/Indiana/Indianapolis&#34;: &#34;US Eastern Standard Time&#34;,
    &#34;America/Indiana/Marengo&#34;: &#34;US Eastern Standard Time&#34;,
    &#34;America/Indiana/Petersburg&#34;: &#34;US Eastern Standard Time&#34;,
    &#34;America/Indiana/Vevay&#34;: &#34;US Eastern Standard Time&#34;,
    &#34;America/Indiana/Vincennes&#34;: &#34;US Eastern Standard Time&#34;,
    &#34;America/Indiana/Winamac&#34;: &#34;US Eastern Standard Time&#34;,
    &#34;America/Iqaluit&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Jamaica&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Kentucky/Louisville&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Kentucky/Monticello&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Lima&#34;: &#34;SA Pacific Standard Time&#34;,
    &#34;America/Montreal&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Nassau&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/New_York&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Nipigon&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Panama&#34;: &#34;SA Pacific Standard Time&#34;,
    &#34;America/Pangnirtung&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Port-au-Prince&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Resolute&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Thunder_Bay&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Toronto&#34;: &#34;Eastern Standard Time&#34;,
    &#34;America/Caracas&#34;: &#34;Venezuela Standard Time&#34;,
    &#34;America/Anguilla&#34;: &#34;Pacific SA Standard Time&#34;,
    &#34;America/Antigua&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Aruba&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Asuncion&#34;: &#34;Paraguay Standard Time&#34;,
    &#34;America/Barbados&#34;: &#34;Pacific SA Standard Time&#34;,
    &#34;America/Blanc-Sablon&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Boa_Vista&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Campo_Grande&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Cuiaba&#34;: &#34;Central Brazilian Standard Time&#34;,
    &#34;America/Curacao&#34;: &#34;Central Brazilian Standard Time&#34;,
    &#34;America/Dominica&#34;: &#34;Pacific SA Standard Time&#34;,
    &#34;America/Eirunepe&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Glace_Bay&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Goose_Bay&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Grenada&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Guadeloupe&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Guyana&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Halifax&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/La_Paz&#34;: &#34;SA Western Standard Time&#34;,
    &#34;America/Manaus&#34;: &#34;SA Western Standard Time&#34;,
    &#34;America/Marigot&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Martinique&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Moncton&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Montserrat&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Port_of_Spain&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Porto_Velho&#34;: &#34;Central Brazilian Standard Time&#34;,
    &#34;America/Puerto_Rico&#34;: &#34;Central Brazilian Standard Time&#34;,
    &#34;America/Rio_Branco&#34;: &#34;Central Brazilian Standard Time&#34;,
    &#34;America/Santiago&#34;: &#34;Pacific SA Standard Time&#34;,
    &#34;America/Santo_Domingo&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/St_Barthelemy&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/St_Kitts&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/St_Lucia&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/St_Thomas&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/St_Vincent&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Thule&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/Tortola&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;Antarctica/Palmer&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;Atlantic/Bermuda&#34;: &#34;Atlantic Standard Time&#34;,
    &#34;America/St_Johns&#34;: &#34;Newfoundland Standard Time&#34;,
    &#34;America/Araguaina&#34;: &#34;E. South America Standard Time&#34;,
    &#34;America/Argentina/Buenos_Aires&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/Catamarca&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/Cordoba&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/Jujuy&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/La_Rioja&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/Mendoza&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/Rio_Gallegos&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/Salta&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/San_Juan&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/San_Luis&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/Tucuman&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Argentina/Ushuaia&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Bahia&#34;: &#34;E. South America Standard Time&#34;,
    &#34;America/Belem&#34;: &#34;E. South America Standard Time&#34;,
    &#34;America/Cayenne&#34;: &#34;SA Eastern Standard Time&#34;,
    &#34;America/Fortaleza&#34;: &#34;SA Eastern Standard Time&#34;,
    &#34;America/Godthab&#34;: &#34;Greenland Standard Time&#34;,
    &#34;America/Maceio&#34;: &#34;E. South America Standard Time&#34;,
    &#34;America/Miquelon&#34;: &#34;E. South America Standard Time&#34;,
    &#34;America/Montevideo&#34;: &#34;Montevideo Standard Time&#34;,
    &#34;America/Paramaribo&#34;: &#34;SA Eastern Standard Time&#34;,
    &#34;America/Recife&#34;: &#34;SA Eastern Standard Time&#34;,
    &#34;America/Santarem&#34;: &#34;E. South America Standard Time&#34;,
    &#34;America/Sao_Paulo&#34;: &#34;E. South America Standard Time&#34;,
    &#34;Antarctica/Rothera&#34;: &#34;Argentina Standard Time&#34;,
    &#34;Atlantic/Stanley&#34;: &#34;Argentina Standard Time&#34;,
    &#34;America/Noronha&#34;: &#34;Mid-Atlantic Standard Time&#34;,
    &#34;Atlantic/South_Georgia&#34;: &#34;Mid-Atlantic Standard Time&#34;,
    &#34;America/Scoresbysund&#34;: &#34;Azores Standard Time&#34;,
    &#34;Atlantic/Azores&#34;: &#34;Azores Standard Time&#34;,
    &#34;Atlantic/Cape_Verde&#34;: &#34;Cape Verde Standard Time&#34;,
    &#34;Africa/Abidjan&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Accra&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Bamako&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Banjul&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Bissau&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Casablanca&#34;: &#34;Morocco Standard Time&#34;,
    &#34;Africa/Conakry&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Dakar&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/El_Aaiun&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Freetown&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Lome&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Monrovia&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Nouakchott&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Ouagadougou&#34;: &#34;GMT Standard Time&#34;,
    &#34;Africa/Sao_Tome&#34;: &#34;GMT Standard Time&#34;,
    &#34;America/Danmarkshavn&#34;: &#34;GMT Standard Time&#34;,
    &#34;Antarctica/Vostok&#34;: &#34;GMT Standard Time&#34;,
    &#34;Atlantic/Canary&#34;: &#34;GMT Standard Time&#34;,
    &#34;Atlantic/Faroe&#34;: &#34;GMT Standard Time&#34;,
    &#34;Atlantic/Madeira&#34;: &#34;GMT Standard Time&#34;,
    &#34;Atlantic/Reykjavik&#34;: &#34;Greenwich Standard Time&#34;,
    &#34;Atlantic/St_Helena&#34;: &#34;Greenwich Standard Time&#34;,
    &#34;Europe/Dublin&#34;: &#34;GMT Standard Time&#34;,
    &#34;Europe/Guernsey&#34;: &#34;GMT Standard Time&#34;,
    &#34;Europe/Isle_of_Man&#34;: &#34;GMT Standard Time&#34;,
    &#34;Europe/Jersey&#34;: &#34;GMT Standard Time&#34;,
    &#34;Europe/Lisbon&#34;: &#34;GMT Standard Time&#34;,
    &#34;Europe/London&#34;: &#34;GMT Standard Time&#34;,
    &#34;GMT&#34;: &#34;GMT Standard Time&#34;,
    &#34;UTC&#34;: &#34;UTC&#34;,
    &#34;Africa/Algiers&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Bangui&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Brazzaville&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Ceuta&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Douala&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Kinshasa&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Lagos&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Libreville&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Luanda&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Malabo&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Ndjamena&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Niamey&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Porto-Novo&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Tunis&#34;: &#34;W. Central Africa Standard Time&#34;,
    &#34;Africa/Windhoek&#34;: &#34;Namibia Standard Time&#34;,
    &#34;Arctic/Longyearbyen&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Amsterdam&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Andorra&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Belgrade&#34;: &#34;Central Europe Standard Time&#34;,
    &#34;Europe/Berlin&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Bratislava&#34;: &#34;Central Europe Standard Time&#34;,
    &#34;Europe/Brussels&#34;: &#34;Romance Standard Time&#34;,
    &#34;Europe/Budapest&#34;: &#34;Central Europe Standard Time&#34;,
    &#34;Europe/Copenhagen&#34;: &#34;Romance Standard Time&#34;,
    &#34;Europe/Gibraltar&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Ljubljana&#34;: &#34;Central Europe Standard Time&#34;,
    &#34;Europe/Luxembourg&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Madrid&#34;: &#34;Romance Standard Time&#34;,
    &#34;Europe/Malta&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Monaco&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Oslo&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Paris&#34;: &#34;Romance Standard Time&#34;,
    &#34;Europe/Podgorica&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Prague&#34;: &#34;Central Europe Standard Time&#34;,
    &#34;Europe/Rome&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/San_Marino&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Sarajevo&#34;: &#34;Central Europe Standard Time&#34;,
    &#34;Europe/Skopje&#34;: &#34;Central Europe Standard Time&#34;,
    &#34;Europe/Stockholm&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Tirane&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Vaduz&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Vatican&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Vienna&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Europe/Warsaw&#34;: &#34;Central Europe Standard Time&#34;,
    &#34;Europe/Zagreb&#34;: &#34;Central Europe Standard Time&#34;,
    &#34;Europe/Zurich&#34;: &#34;W. Europe Standard Time&#34;,
    &#34;Africa/Blantyre&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Bujumbura&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Cairo&#34;: &#34;Egypt Standard Time&#34;,
    &#34;Africa/Gaborone&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Harare&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Johannesburg&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Kigali&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Lubumbashi&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Lusaka&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Maputo&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Maseru&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Mbabane&#34;: &#34;South Africa Standard Time&#34;,
    &#34;Africa/Tripoli&#34;: &#34;Middle East Standard Time&#34;,
    &#34;Asia/Amman&#34;: &#34;Jordan Standard Time&#34;,
    &#34;Asia/Beirut&#34;: &#34;Middle East Standard Time&#34;,
    &#34;Asia/Damascus&#34;: &#34;Syria Standard Time&#34;,
    &#34;Asia/Gaza&#34;: &#34;Middle East Standard Time&#34;,
    &#34;Asia/Jerusalem&#34;: &#34;Israel Standard Time&#34;,
    &#34;Asia/Nicosia&#34;: &#34;GTB Standard Time&#34;,
    &#34;Europe/Athens&#34;: &#34;GTB Standard Time&#34;,
    &#34;Europe/Bucharest&#34;: &#34;GTB Standard Time&#34;,
    &#34;Europe/Chisinau&#34;: &#34;E. Europe Standard Time&#34;,
    &#34;Europe/Helsinki&#34;: &#34;FLE Standard Time&#34;,
    &#34;Europe/Istanbul&#34;: &#34;Turkey Standard Time&#34;,
    &#34;Europe/Kiev&#34;: &#34;E. Europe Standard Time&#34;,
    &#34;Europe/Mariehamn&#34;: &#34;E. Europe Standard Time&#34;,
    &#34;Europe/Riga&#34;: &#34;FLE Standard Time&#34;,
    &#34;Europe/Simferopol&#34;: &#34;E. Europe Standard Time&#34;,
    &#34;Europe/Sofia&#34;: &#34;FLE Standard Time&#34;,
    &#34;Europe/Tallinn&#34;: &#34;FLE Standard Time&#34;,
    &#34;Europe/Uzhgorod&#34;: &#34;E. Europe Standard Time&#34;,
    &#34;Europe/Vilnius&#34;: &#34;FLE Standard Time&#34;,
    &#34;Europe/Zaporozhye&#34;: &#34;E. Europe Standard Time&#34;,
    &#34;Africa/Addis_Ababa&#34;: &#34;E. Africa Standard Time&#34;,
    &#34;Africa/Asmara&#34;: &#34;E. Africa Standard Time&#34;,
    &#34;Africa/Dar_es_Salaam&#34;: &#34;E. Africa Standard Time&#34;,
    &#34;Africa/Djibouti&#34;: &#34;E. Africa Standard Time&#34;,
    &#34;Africa/Juba&#34;: &#34;E. Africa Standard Time&#34;,
    &#34;Africa/Kampala&#34;: &#34;E. Africa Standard Time&#34;,
    &#34;Africa/Khartoum&#34;: &#34;E. Africa Standard Time&#34;,
    &#34;Africa/Mogadishu&#34;: &#34;E. Africa Standard Time&#34;,
    &#34;Africa/Nairobi&#34;: &#34;E. Africa Standard Time&#34;,
    &#34;Antarctica/Syowa&#34;: &#34;Kaliningrad Standard Time&#34;,
    &#34;Asia/Aden&#34;: &#34;Kaliningrad Standard Time&#34;,
    &#34;Asia/Baghdad&#34;: &#34;Arabic Standard Time&#34;,
    &#34;Asia/Bahrain&#34;: &#34;Arabic Standard Time&#34;,
    &#34;Asia/Kuwait&#34;: &#34;Arab Standard Time&#34;,
    &#34;Asia/Qatar&#34;: &#34;Arab Standard Time&#34;,
    &#34;Asia/Riyadh&#34;: &#34;Arab Standard Time&#34;,
    &#34;Europe/Kaliningrad&#34;: &#34;Kaliningrad Standard Time&#34;,
    &#34;Europe/Minsk&#34;: &#34;Kaliningrad Standard Time&#34;,
    &#34;Indian/Antananarivo&#34;: &#34;Arab Standard Time&#34;,
    &#34;Indian/Comoro&#34;: &#34;Arab Standard Time&#34;,
    &#34;Indian/Mayotte&#34;: &#34;Arab Standard Time&#34;,
    &#34;Asia/Tehran&#34;: &#34;Iran Standard Time&#34;,
    &#34;Asia/Baku&#34;: &#34;Caucasus Standard Time&#34;,
    &#34;Asia/Dubai&#34;: &#34;Arabian Standard Time&#34;,
    &#34;Asia/Muscat&#34;: &#34;Arabian Standard Time&#34;,
    &#34;Asia/Tbilisi&#34;: &#34;Georgian Standard Time&#34;,
    &#34;Asia/Yerevan&#34;: &#34;Caucasus Standard Time&#34;,
    &#34;Europe/Moscow&#34;: &#34;Russian Standard Time&#34;,
    &#34;Europe/Samara&#34;: &#34;Russian Standard Time&#34;,
    &#34;Europe/Volgograd&#34;: &#34;Russian Standard Time&#34;,
    &#34;Indian/Mahe&#34;: &#34;Mauritius Standard Time&#34;,
    &#34;Indian/Mauritius&#34;: &#34;Mauritius Standard Time&#34;,
    &#34;Indian/Reunion&#34;: &#34;Mauritius Standard Time&#34;,
    &#34;Asia/Kabul&#34;: &#34;Afghanistan Standard Time&#34;,
    &#34;Asia/Aqtau&#34;: &#34;West Asia Standard Time&#34;,
    &#34;Asia/Aqtobe&#34;: &#34;West Asia Standard Time&#34;,
    &#34;Asia/Ashgabat&#34;: &#34;West Asia Standard Time&#34;,
    &#34;Asia/Dushanbe&#34;: &#34;West Asia Standard Time&#34;,
    &#34;Asia/Karachi&#34;: &#34;Pakistan Standard Time&#34;,
    &#34;Asia/Oral&#34;: &#34;West Asia Standard Time&#34;,
    &#34;Asia/Samarkand&#34;: &#34;West Asia Standard Time&#34;,
    &#34;Asia/Tashkent&#34;: &#34;West Asia Standard Time&#34;,
    &#34;Indian/Kerguelen&#34;: &#34;West Asia Standard Time&#34;,
    &#34;Indian/Maldives&#34;: &#34;West Asia Standard Time&#34;,
    &#34;Asia/Colombo&#34;: &#34;Sri Lanka Standard Time&#34;,
    &#34;Asia/Kolkata&#34;: &#34;India Standard Time&#34;,
    &#34;Asia/Kathmandu&#34;: &#34;Nepal Standard Time&#34;,
    &#34;Antarctica/Mawson&#34;: &#34;Central Asia Standard Time&#34;,
    &#34;Asia/Almaty&#34;: &#34;Central Asia Standard Time&#34;,
    &#34;Asia/Bishkek&#34;: &#34;Central Asia Standard Time&#34;,
    &#34;Asia/Dhaka&#34;: &#34;Bangladesh Standard Time&#34;,
    &#34;Asia/Qyzylorda&#34;: &#34;Central Asia Standard Time&#34;,
    &#34;Asia/Thimphu&#34;: &#34;Central Asia Standard Time&#34;,
    &#34;Asia/Yekaterinburg&#34;: &#34;Ekaterinburg Standard Time&#34;,
    &#34;Indian/Chagos&#34;: &#34;Central Asia Standard Time&#34;,
    &#34;Asia/Rangoon&#34;: &#34;Myanmar Standard Time&#34;,
    &#34;Indian/Cocos&#34;: &#34;Myanmar Standard Time&#34;,
    &#34;Antarctica/Davis&#34;: &#34;SE Asia Standard Time&#34;,
    &#34;Asia/Bangkok&#34;: &#34;SE Asia Standard Time&#34;,
    &#34;Asia/Ho_Chi_Minh&#34;: &#34;SE Asia Standard Time&#34;,
    &#34;Asia/Hovd&#34;: &#34;N. Central Asia Standard Time&#34;,
    &#34;Asia/Jakarta&#34;: &#34;SE Asia Standard Time&#34;,
    &#34;Asia/Novokuznetsk&#34;: &#34;N. Central Asia Standard Time&#34;,
    &#34;Asia/Novosibirsk&#34;: &#34;N. Central Asia Standard Time&#34;,
    &#34;Asia/Omsk&#34;: &#34;N. Central Asia Standard Time&#34;,
    &#34;Asia/Phnom_Penh&#34;: &#34;SE Asia Standard Time&#34;,
    &#34;Asia/Pontianak&#34;: &#34;SE Asia Standard Time&#34;,
    &#34;Asia/Vientiane&#34;: &#34;SE Asia Standard Time&#34;,
    &#34;Indian/Christmas&#34;: &#34;SE Asia Standard Time&#34;,
    &#34;Antarctica/Casey&#34;: &#34;W. Australia Standard Time&#34;,
    &#34;Asia/Brunei&#34;: &#34;North Asia Standard Time&#34;,
    &#34;Asia/Choibalsan&#34;: &#34;China Standard Time&#34;,
    &#34;Asia/Chongqing&#34;: &#34;China Standard Time&#34;,
    &#34;Asia/Harbin&#34;: &#34;China Standard Time&#34;,
    &#34;Asia/Hong_Kong&#34;: &#34;China Standard Time&#34;,
    &#34;Asia/Kashgar&#34;: &#34;North Asia Standard Time&#34;,
    &#34;Asia/Krasnoyarsk&#34;: &#34;North Asia Standard Time&#34;,
    &#34;Asia/Kuala_Lumpur&#34;: &#34;Singapore Standard Time&#34;,
    &#34;Asia/Kuching&#34;: &#34;North Asia Standard Time&#34;,
    &#34;Asia/Macau&#34;: &#34;North Asia Standard Time&#34;,
    &#34;Asia/Makassar&#34;: &#34;North Asia Standard Time&#34;,
    &#34;Asia/Manila&#34;: &#34;North Asia Standard Time&#34;,
    &#34;Asia/Shanghai&#34;: &#34;China Standard Time&#34;,
    &#34;Asia/Singapore&#34;: &#34;Singapore Standard Time&#34;,
    &#34;Asia/Taipei&#34;: &#34;Taipei Standard Time&#34;,
    &#34;Asia/Ulaanbaatar&#34;: &#34;Ulaanbaatar Standard Time&#34;,
    &#34;Asia/Urumqi&#34;: &#34;China Standard Time&#34;,
    &#34;Australia/Perth&#34;: &#34;W. Australia Standard Time&#34;,
    &#34;Australia/Eucla&#34;: &#34;W. Australia Standard Time&#34;,
    &#34;Asia/Dili&#34;: &#34;North Asia East Standard Time&#34;,
    &#34;Asia/Irkutsk&#34;: &#34;North Asia East Standard Time&#34;,
    &#34;Asia/Jayapura&#34;: &#34;North Asia East Standard Time&#34;,
    &#34;Asia/Pyongyang&#34;: &#34;Korea Standard Time&#34;,
    &#34;Asia/Seoul&#34;: &#34;Korea Standard Time&#34;,
    &#34;Asia/Tokyo&#34;: &#34;Tokyo Standard Time&#34;,
    &#34;Pacific/Palau&#34;: &#34;North Asia East Standard Time&#34;,
    &#34;Australia/Adelaide&#34;: &#34;Cen. Australia Standard Time&#34;,
    &#34;Australia/Broken_Hill&#34;: &#34;AUS Central Standard Time&#34;,
    &#34;Australia/Darwin&#34;: &#34;AUS Central Standard Time&#34;,
    &#34;Antarctica/DumontDUrville&#34;: &#34;E. Australia Standard Time&#34;,
    &#34;Asia/Yakutsk&#34;: &#34;Yakutsk Standard Time&#34;,
    &#34;Australia/Brisbane&#34;: &#34;E. Australia Standard Time&#34;,
    &#34;Australia/Currie&#34;: &#34;Tasmania Standard Time&#34;,
    &#34;Australia/Hobart&#34;: &#34;Tasmania Standard Time&#34;,
    &#34;Australia/Lindeman&#34;: &#34;E. Australia Standard Time&#34;,
    &#34;Australia/Melbourne&#34;: &#34;AUS Eastern Standard Time&#34;,
    &#34;Australia/Sydney&#34;: &#34;AUS Eastern Standard Time&#34;,
    &#34;Pacific/Guam&#34;: &#34;West Pacific Standard Time&#34;,
    &#34;Pacific/Port_Moresby&#34;: &#34;West Pacific Standard Time&#34;,
    &#34;Pacific/Saipan&#34;: &#34;West Pacific Standard Time&#34;,
    &#34;Pacific/Truk&#34;: &#34;West Pacific Standard Time&#34;,
    &#34;Australia/Lord_Howe&#34;: &#34;E. Australia Standard Time&#34;,
    &#34;Asia/Sakhalin&#34;: &#34;Vladivostok Standard Time&#34;,
    &#34;Asia/Vladivostok&#34;: &#34;Vladivostok Standard Time&#34;,
    &#34;Pacific/Efate&#34;: &#34;Central Pacific Standard Time&#34;,
    &#34;Pacific/Guadalcanal&#34;: &#34;Central Pacific Standard Time&#34;,
    &#34;Pacific/Kosrae&#34;: &#34;Central Pacific Standard Time&#34;,
    &#34;Pacific/Noumea&#34;: &#34;Central Pacific Standard Time&#34;,
    &#34;Pacific/Ponape&#34;: &#34;Central Pacific Standard Time&#34;,
    &#34;Pacific/Norfolk&#34;: &#34;Central Pacific Standard Time&#34;,
    &#34;Antarctica/McMurdo&#34;: &#34;UTC+12&#34;,
    &#34;Antarctica/South_Pole&#34;: &#34;UTC+12&#34;,
    &#34;Asia/Anadyr&#34;: &#34;Magadan Standard Time&#34;,
    &#34;Asia/Kamchatka&#34;: &#34;Magadan Standard Time&#34;,
    &#34;Asia/Magadan&#34;: &#34;Magadan Standard Time&#34;,
    &#34;Pacific/Auckland&#34;: &#34;New Zealand Standard Time&#34;,
    &#34;Pacific/Fiji&#34;: &#34;Fiji Standard Time&#34;,
    &#34;Pacific/Funafuti&#34;: &#34;Fiji Standard Time&#34;,
    &#34;Pacific/Kwajalein&#34;: &#34;Fiji Standard Time&#34;,
    &#34;Pacific/Majuro&#34;: &#34;Fiji Standard Time&#34;,
    &#34;Pacific/Nauru&#34;: &#34;Fiji Standard Time&#34;,
    &#34;Pacific/Tarawa&#34;: &#34;Fiji Standard Time&#34;,
    &#34;Pacific/Wake&#34;: &#34;Fiji Standard Time&#34;,
    &#34;Pacific/Wallis&#34;: &#34;Fiji Standard Time&#34;,
    &#34;Pacific/Chatham&#34;: &#34;Tonga Standard Time&#34;,
    &#34;Pacific/Enderbury&#34;: &#34;Tonga Standard Time&#34;,
    &#34;Pacific/Tongatapu&#34;: &#34;Tonga Standard Time&#34;,
    &#34;Pacific/Kiritimati&#34;: &#34;Tonga Standard Time&#34;
}

ENTITY_TYPE_MAP = {
    1: &#34;file server&#34;,
    2: &#34;laptop&#34;,
    3: &#34;hypervisor&#34;,
    4: &#34;virtual machine&#34;,
    5: &#34;windows cluster&#34;,
    6: &#34;unix cluster&#34;,
    7: &#34;archiver file server&#34;,
    8: &#34;distributed database&#34;,
    9: &#34;ibm iseries client&#34;,
    10: &#34;edge drive&#34;,
    11: &#34;distributed fs&#34;,
    12: &#34;distributed ida&#34;,
    13: &#34;client&#34;,
    14: &#34;instance&#34;,
    15: &#34;backupset&#34;,
    16: &#34;subclient&#34;,
    17: &#34;library&#34;,
    18: &#34;array&#34;,
    19: &#34;media agent&#34;,
    20: &#34;storage pool disk&#34;,
    21: &#34;storage policy&#34;,
    22: &#34;schedule policy&#34;,
    23: &#34;subclient policy&#34;,
    24: &#34;storage pool cloud&#34;,
    25: &#34;storage pool hyperscale&#34;,
    26: &#34;storage pool tape&#34;,
    27: &#34;database instance&#34;,
    30: &#34;user&#34;,
    31: &#34;user group&#34;,
    32: &#34;client group&#34;,
    33: &#34;plan&#34;,
    34: &#34;region&#34;,
    35: &#34;alert&#34;,
    36: &#34;key management server&#34;,
    37: &#34;report plus&#34;,
    38: &#34;vm groups&#34;,
    39: &#34;role&#34;,
    40: &#34;epicclient&#34;,
    41: &#34;k8 app&#34;,
    42: &#34;k8 appgroup&#34;,
    43: &#34;k8 cluster&#34;,
    44: &#34;object storage&#34;,
    46: &#34;exchange app&#34;,
    47: &#34;o365 exchange mailboxes app&#34;,
    48: &#34;g suite app&#34;,
    49: &#34;active directory app&#34;,
    50: &#34;salesforce cloud app&#34;,
    51: &#34;o365 onedrive users app&#34;,
    52: &#34;o365 sharepoint sites app&#34;,
}


class HypervisorType(Enum):
    &#34;&#34;&#34;Class to maintain all the hypervisor related constants.&#34;&#34;&#34;
    VIRTUAL_CENTER = &#34;VMware&#34;
    MS_VIRTUAL_SERVER = &#34;Hyper-V&#34;
    AZURE = &#34;Azure&#34;
    AZURE_V2 = &#34;Azure Resource Manager&#34;
    FUSION_COMPUTE = &#34;FusionCompute&#34;
    ORACLE_VM = &#34;OracleVM&#34;
    ALIBABA_CLOUD = &#34;Alibaba Cloud&#34;
    ORACLE_CLOUD = &#34;Oracle Cloud&#34;
    OPENSTACK = &#34;OpenStack&#34;
    GOOGLE_CLOUD = &#34;Google Cloud Platform&#34;
    Azure_Stack = &#34;Azure Stack&#34;
    Rhev = &#34;Red Hat Virtualization&#34;
    AMAZON_AWS = &#34;Amazon Web Services&#34;
    VCLOUD = &#34;vCloud Director&#34;
    Nutanix = &#34;Nutanix AHV&#34;
    ORACLE_CLOUD_INFRASTRUCTURE = &#34;Oracle Cloud Infrastructure&#34;
    OPENSHIFT = &#34;Red Hat OpenShift&#34;
    PROXMOX = &#34;Proxmox ve&#34;


class AppIDAType(Enum):
    &#34;&#34;&#34;Class to maintain all the app ida constants&#34;&#34;&#34;
    WINDOWS_FILE_SYSTEM = 33
    LINUX_FILE_SYSTEM = 29
    VIRTUAL_SERVER = 106
    CLOUD_APP = 134


class AppIDAName(Enum):
    &#34;&#34;&#34;Class to maintain the app IDA names&#34;&#34;&#34;
    FILE_SYSTEM = &#39;File System&#39;
    VIRTUAL_SERVER = &#39;Virtual Server&#39;
    BIG_DATA_APPS = &#39;big data apps&#39;


class VSAObjects(Enum):
    &#34;&#34;&#34;Mapping for VSA Objects.&#34;&#34;&#34;
    SERVER = 1
    RESOURCE_POOL = 2
    VAPP = 3
    DATACENTER = 4
    FOLDER = 5
    CLUSTER = 6
    DATASTORE = 7
    DATASTORE_CLUSTER = 8
    VM = 9
    VMName = 10
    VMGuestOS = 11
    VMGuestHostName = 12
    ClusterSharedVolumes = 13
    LocalDisk = 14
    ClusterDisk = 15
    UnprotectedVMs = 16
    ROOT = 17
    FileServer = 18
    SMBShare = 19
    TypesFolder = 20
    VMFolder = 21
    ServerFolder = 22
    TemplateFolder = 23
    StorageRepositoryFolder = 24
    VAppFolder = 25
    DatacenterFolder = 26
    ClusterFolder = 27
    VMPowerState = 28
    VMNotes = 29
    VMCustomAttribute = 30
    Network = 31
    User = 32
    VMTemplate = 33
    Tag = 34
    TagCategory = 35
    Subclient = 36
    ClientGroup = 37
    ProtectionDomain = 38
    ConsistencyGroup = 39
    InstanceSize = 40
    Organization = 41
    Selector = 47


class InstanceBackupType(Enum):
    &#34;&#34;&#34;Class to maintain type of instance backup&#34;&#34;&#34;
    FULL = &#39;full&#39;
    INCREMENTAL = &#39;incremental&#39;
    CUMULATIVE = &#39;incremental&#39;      # cumulative backups pull incremental backup JSON


class SQLDefines:
    &#34;&#34;&#34;Class to maintain SQL Defines&#34;&#34;&#34;

    # sql restore types
    DATABASE_RESTORE = &#39;DATABASE_RESTORE&#39;
    STEP_RESTORE = &#39;STEP_RESTORE&#39;
    RECOVER_ONLY = &#39;RECOVER_ONLY&#39;

    # sql recovery types
    STATE_RECOVER = &#39;STATE_RECOVER&#39;
    STATE_NORECOVER = &#39;STATE_NORECOVER&#39;
    STATE_STANDBY = &#39;STATE_STANDBY&#39;


class SharepointDefines:
    &#34;&#34;&#34;Class to maintiain Sharepoint Defines&#34;&#34;&#34;

    # sharepoint strings
    CONTENT_WEBAPP = &#39;\\MB\\Farm\\Microsoft SharePoint Foundation Web Application\\{0}&#39;
    CONTENT_DB = &#39;\\MB\\Farm\\Microsoft SharePoint Foundation Web Application\\{0}\\{1}&#39;


class AdvancedJobDetailType(Enum):
    &#34;&#34;&#34;Class to maintain advanced job details info type
    &#34;&#34;&#34;

    RETENTION_INFO = 1
    REFERNCE_COPY_INFO = 2
    DASH_COPY_INFO = 4
    ADMIN_DATA_INFO = 8
    BKUP_INFO = 16


class VSALiveSyncStatus(Enum):
    &#34;&#34;&#34;Class to maintain status of the VSA Live sync&#34;&#34;&#34;
    NEVER_HAS_BEEN_SYNCED = 0
    IN_SYNC = 1
    NEEDS_SYNC = 2
    SYNC_IN_PROGRESS = 3
    SYNC_PAUSED = 4
    SYNC_FAILED = 5
    SYNC_DISABLED = 6
    SYNC_ENABLED = 7
    VALIDATION_FAILED = 8
    SYNC_QUEUED = 9
    REVERT_FAILED = 10
    SYNC_STARTING = 11


class VSAFailOverStatus(Enum):
    &#34;&#34;&#34;Class to maintain Failover status of the VSA Live sync&#34;&#34;&#34;
    NONE = 0
    FAILOVER_COMPLETE = 1
    FAILOVER_RUNNING = 2
    FAILOVER_PAUSED = 3
    FAILOVER_FAILED = 4
    FAILBACK_COMPLETE = 5
    FAILBACK_RUNNING = 6
    FAILBACK_PAUSED = 7
    FAILBACK_FAILED = 8
    FAILBACK_PARTIAL = 9
    FAILOVER_PARTIAL = 10


class ApplicationGroup(Enum):
    &#34;&#34;&#34;Class to maintain application group types.&#34;&#34;&#34;

    WINDOWS = auto()
    UNIX = auto()
    IBMi = auto()
    OPENVMS = auto()
    CLOUDAPPS = auto()
    MSSQLSERVER = auto()
    SHAREPOINTSERVER = auto()


class StoragePoolConstants:
    &#34;&#34;&#34;Class to maintain storage policy constants&#34;&#34;&#34;

    AIR_GAP_PROTECT_STORAGE_TYPES = {
        &#34;MICROSOFT AZURE STORAGE&#34;: {
            &#34;HOT&#34;: {
                &#34;vendorId&#34;: 3,
                &#34;displayVendorId&#34;: 401,
            },

            &#34;COOL&#34;: {
                &#34;vendorId&#34;: 3,
                &#34;displayVendorId&#34;: 402,
            },
        },

        &#34;ORACLE CLOUD INFRASTRUCTURE OBJECT STORAGE&#34;: {
            &#34;INFREQUENT ACCESS&#34;: {
                &#34;vendorId&#34;: 26,
                &#34;displayVendorId&#34;: 404,
            },
        }
    }


class CommcellEntity(Enum):
    &#34;&#34;&#34;Class to maintain Commcell Entity types&#34;&#34;&#34;

    COMMCELL_ENTITY = 1
    CLIENT_ENTITY = 3
    APPTYPE_ENTITY = 4
    INSTANCE_ENTITY = 5
    BACKUPSET_ENTITY = 6
    SUBCLIENT_ENTITY = 7
    LIBRARIES_ENTITY = 8
    LIBRARY_ENTITY = 9
    MEDIAAGENT_ENTITY = 11
    USER_ENTITY = 13
    USERGROUP_ENTITY = 15
    STORAGE_POLICIES_ENTITY = 16
    STORAGE_POLICY_ENTITY = 17
    STORAGE_POLICY_COPY_ENTITY = 18
    ALL_USERS_ENTITY = 12
    ALL_USERGROUPS_ENTITY = 14
    TRACKING_POLICIES_ENTITY = 20
    TRACKING_POLICY_ENTITY = 21
    VAULTRACKER_JOBS = 22
    DATAPROTECTION_JOBS = 23
    AUX_COPY_JOBS = 24
    MACHINE_ENTITIY = 26
    CLIENT_GROUP_ENTITY = 28
    SRM_REPORT_SET = 29
    SRM_REPORT_ENTITY = 30
    LEGAL_HOLD = 31
    SRM_REPORTS_SET = 32
    SRM_REPORTS_ENTITY = 33
    SCHEDULE_POLICY_ENTITY = 35
    SECURITY = 36
    HOLIDAYS = 37
    OPERATION_WINDOW = 38
    CALENDAR = 39
    GLOBALFILTERS = 40
    MEDIA_LOCATIONS = 41
    CALENDARS = 44
    APPID_ENTITY = 45
    MEDIA_ENTITY = 46
    DRIVE_POOL_ENTITY = 47
    SCRATCH_POOL_ENTITY = 48
    LIBRARY_MEDIA_TYPE_ENTITY = 49
    MEDIA_SIDE_ENTITY = 50
    DRIVE_ENTITY = 51
    MOUNT_PATH_ENTITY = 52
    LOCATION_ENTITY = 53
    TIME_RANGE = 54
    TIME_ZONE = 55
    DATE_TIME = 56
    SHELF_ENTITY = 57
    GALAXYRELEASE_ENTITY = 58
    LICENSE_ENTITY = 59
    COMPONENT_ENTITY = 60
    PROVIDER_ENTITY = 61
    EXTERNAL_GROUP_ENTITY = 62
    CALENDAR_ENTITY = 63
    ALERT_ENTITY = 64
    SUBCLIENT_POLICY_ENTITY = 65
    LOCALE_ENTITY = 66
    SPARE_MEDIA_GROUP_ENTITY = 67
    SUBTASK_ENTITY = 68
    TASK_ENTITY = 69
    MEDIA_TYPE_ENTITY = 70


class Credential_Type(Enum):
    &#34;&#34;&#34;Class to maintain Credential types&#34;&#34;&#34;

    WINDOWSACCOUNT = 1
    LINUXACCOUNT = 2
    SSHACCOUNT = 3
    AZUREACCOUNT = 4
    VMWAREACCOUNT = 5
    MICROSOFT_AZURE = 103


class ResourcePoolAppType(Enum):
    &#34;&#34;&#34;Class to maintain ResourcePool AppType&#34;&#34;&#34;
    O365 = 1
    EXCHANGE = 3
    SHAREPOINT = 4
    ONEDRIVE = 5
    TEAMS = 6


class OSType(Enum):
    &#34;&#34;&#34;Class to maintain OS Types&#34;&#34;&#34;
    WINDOWS = 1
    UNIX = 2

class VsInstanceType:
    &#34;&#34;&#34;Class to store vsinstance dict&#34;&#34;&#34;

    VSINSTANCE_TYPE = {
        101: &#34;vmware&#34;,
        201: &#34;xen&#34;,
        102: &#34;hyperv&#34;,
        301: &#34;amazon_web_services&#34;,
        401: &#34;azure&#34;,
        402: &#34;azure_resource_manager&#34;,
        403: &#34;azure_stack&#34;,
        501: &#34;red_hat_virtualization&#34;,
        601: &#34;nutanix_ahv&#34;,
        701: &#34;oraclevm&#34;,
        801: &#34;fusioncompute&#34;,
        901: &#34;openstack&#34;,
        1101: &#34;oracle_cloud&#34;,
        1102: &#34;oracle_cloud_infrastructure&#34;,
        1301: &#34;google_cloud_platform&#34;,
        1401: &#34;alibaba_cloud&#34;,
        1503: &#34;vcloud_director&#34;,
        1501: &#34;kubernetes&#34;,
        1600: &#34;proxmox_ve&#34;
    }</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.constants.AdvancedJobDetailType"><code class="flex name class">
<span>class <span class="ident">AdvancedJobDetailType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain advanced job details info type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L624-L632" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AdvancedJobDetailType(Enum):
    &#34;&#34;&#34;Class to maintain advanced job details info type
    &#34;&#34;&#34;

    RETENTION_INFO = 1
    REFERNCE_COPY_INFO = 2
    DASH_COPY_INFO = 4
    ADMIN_DATA_INFO = 8
    BKUP_INFO = 16</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.AdvancedJobDetailType.ADMIN_DATA_INFO"><code class="name">var <span class="ident">ADMIN_DATA_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.AdvancedJobDetailType.BKUP_INFO"><code class="name">var <span class="ident">BKUP_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.AdvancedJobDetailType.DASH_COPY_INFO"><code class="name">var <span class="ident">DASH_COPY_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.AdvancedJobDetailType.REFERNCE_COPY_INFO"><code class="name">var <span class="ident">REFERNCE_COPY_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.AdvancedJobDetailType.RETENTION_INFO"><code class="name">var <span class="ident">RETENTION_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.AppIDAName"><code class="flex name class">
<span>class <span class="ident">AppIDAName</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain the app IDA names</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L542-L546" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AppIDAName(Enum):
    &#34;&#34;&#34;Class to maintain the app IDA names&#34;&#34;&#34;
    FILE_SYSTEM = &#39;File System&#39;
    VIRTUAL_SERVER = &#39;Virtual Server&#39;
    BIG_DATA_APPS = &#39;big data apps&#39;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.AppIDAName.BIG_DATA_APPS"><code class="name">var <span class="ident">BIG_DATA_APPS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.AppIDAName.FILE_SYSTEM"><code class="name">var <span class="ident">FILE_SYSTEM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.AppIDAName.VIRTUAL_SERVER"><code class="name">var <span class="ident">VIRTUAL_SERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.AppIDAType"><code class="flex name class">
<span>class <span class="ident">AppIDAType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain all the app ida constants</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L534-L539" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AppIDAType(Enum):
    &#34;&#34;&#34;Class to maintain all the app ida constants&#34;&#34;&#34;
    WINDOWS_FILE_SYSTEM = 33
    LINUX_FILE_SYSTEM = 29
    VIRTUAL_SERVER = 106
    CLOUD_APP = 134</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.AppIDAType.CLOUD_APP"><code class="name">var <span class="ident">CLOUD_APP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.AppIDAType.LINUX_FILE_SYSTEM"><code class="name">var <span class="ident">LINUX_FILE_SYSTEM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.AppIDAType.VIRTUAL_SERVER"><code class="name">var <span class="ident">VIRTUAL_SERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.AppIDAType.WINDOWS_FILE_SYSTEM"><code class="name">var <span class="ident">WINDOWS_FILE_SYSTEM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.ApplicationGroup"><code class="flex name class">
<span>class <span class="ident">ApplicationGroup</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain application group types.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L666-L675" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ApplicationGroup(Enum):
    &#34;&#34;&#34;Class to maintain application group types.&#34;&#34;&#34;

    WINDOWS = auto()
    UNIX = auto()
    IBMi = auto()
    OPENVMS = auto()
    CLOUDAPPS = auto()
    MSSQLSERVER = auto()
    SHAREPOINTSERVER = auto()</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.ApplicationGroup.CLOUDAPPS"><code class="name">var <span class="ident">CLOUDAPPS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ApplicationGroup.IBMi"><code class="name">var <span class="ident">IBMi</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ApplicationGroup.MSSQLSERVER"><code class="name">var <span class="ident">MSSQLSERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ApplicationGroup.OPENVMS"><code class="name">var <span class="ident">OPENVMS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ApplicationGroup.SHAREPOINTSERVER"><code class="name">var <span class="ident">SHAREPOINTSERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ApplicationGroup.UNIX"><code class="name">var <span class="ident">UNIX</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ApplicationGroup.WINDOWS"><code class="name">var <span class="ident">WINDOWS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.CommcellEntity"><code class="flex name class">
<span>class <span class="ident">CommcellEntity</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain Commcell Entity types</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L703-L767" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CommcellEntity(Enum):
    &#34;&#34;&#34;Class to maintain Commcell Entity types&#34;&#34;&#34;

    COMMCELL_ENTITY = 1
    CLIENT_ENTITY = 3
    APPTYPE_ENTITY = 4
    INSTANCE_ENTITY = 5
    BACKUPSET_ENTITY = 6
    SUBCLIENT_ENTITY = 7
    LIBRARIES_ENTITY = 8
    LIBRARY_ENTITY = 9
    MEDIAAGENT_ENTITY = 11
    USER_ENTITY = 13
    USERGROUP_ENTITY = 15
    STORAGE_POLICIES_ENTITY = 16
    STORAGE_POLICY_ENTITY = 17
    STORAGE_POLICY_COPY_ENTITY = 18
    ALL_USERS_ENTITY = 12
    ALL_USERGROUPS_ENTITY = 14
    TRACKING_POLICIES_ENTITY = 20
    TRACKING_POLICY_ENTITY = 21
    VAULTRACKER_JOBS = 22
    DATAPROTECTION_JOBS = 23
    AUX_COPY_JOBS = 24
    MACHINE_ENTITIY = 26
    CLIENT_GROUP_ENTITY = 28
    SRM_REPORT_SET = 29
    SRM_REPORT_ENTITY = 30
    LEGAL_HOLD = 31
    SRM_REPORTS_SET = 32
    SRM_REPORTS_ENTITY = 33
    SCHEDULE_POLICY_ENTITY = 35
    SECURITY = 36
    HOLIDAYS = 37
    OPERATION_WINDOW = 38
    CALENDAR = 39
    GLOBALFILTERS = 40
    MEDIA_LOCATIONS = 41
    CALENDARS = 44
    APPID_ENTITY = 45
    MEDIA_ENTITY = 46
    DRIVE_POOL_ENTITY = 47
    SCRATCH_POOL_ENTITY = 48
    LIBRARY_MEDIA_TYPE_ENTITY = 49
    MEDIA_SIDE_ENTITY = 50
    DRIVE_ENTITY = 51
    MOUNT_PATH_ENTITY = 52
    LOCATION_ENTITY = 53
    TIME_RANGE = 54
    TIME_ZONE = 55
    DATE_TIME = 56
    SHELF_ENTITY = 57
    GALAXYRELEASE_ENTITY = 58
    LICENSE_ENTITY = 59
    COMPONENT_ENTITY = 60
    PROVIDER_ENTITY = 61
    EXTERNAL_GROUP_ENTITY = 62
    CALENDAR_ENTITY = 63
    ALERT_ENTITY = 64
    SUBCLIENT_POLICY_ENTITY = 65
    LOCALE_ENTITY = 66
    SPARE_MEDIA_GROUP_ENTITY = 67
    SUBTASK_ENTITY = 68
    TASK_ENTITY = 69
    MEDIA_TYPE_ENTITY = 70</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.CommcellEntity.ALERT_ENTITY"><code class="name">var <span class="ident">ALERT_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.ALL_USERGROUPS_ENTITY"><code class="name">var <span class="ident">ALL_USERGROUPS_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.ALL_USERS_ENTITY"><code class="name">var <span class="ident">ALL_USERS_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.APPID_ENTITY"><code class="name">var <span class="ident">APPID_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.APPTYPE_ENTITY"><code class="name">var <span class="ident">APPTYPE_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.AUX_COPY_JOBS"><code class="name">var <span class="ident">AUX_COPY_JOBS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.BACKUPSET_ENTITY"><code class="name">var <span class="ident">BACKUPSET_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.CALENDAR"><code class="name">var <span class="ident">CALENDAR</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.CALENDARS"><code class="name">var <span class="ident">CALENDARS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.CALENDAR_ENTITY"><code class="name">var <span class="ident">CALENDAR_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.CLIENT_ENTITY"><code class="name">var <span class="ident">CLIENT_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.CLIENT_GROUP_ENTITY"><code class="name">var <span class="ident">CLIENT_GROUP_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.COMMCELL_ENTITY"><code class="name">var <span class="ident">COMMCELL_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.COMPONENT_ENTITY"><code class="name">var <span class="ident">COMPONENT_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.DATAPROTECTION_JOBS"><code class="name">var <span class="ident">DATAPROTECTION_JOBS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.DATE_TIME"><code class="name">var <span class="ident">DATE_TIME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.DRIVE_ENTITY"><code class="name">var <span class="ident">DRIVE_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.DRIVE_POOL_ENTITY"><code class="name">var <span class="ident">DRIVE_POOL_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.EXTERNAL_GROUP_ENTITY"><code class="name">var <span class="ident">EXTERNAL_GROUP_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.GALAXYRELEASE_ENTITY"><code class="name">var <span class="ident">GALAXYRELEASE_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.GLOBALFILTERS"><code class="name">var <span class="ident">GLOBALFILTERS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.HOLIDAYS"><code class="name">var <span class="ident">HOLIDAYS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.INSTANCE_ENTITY"><code class="name">var <span class="ident">INSTANCE_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.LEGAL_HOLD"><code class="name">var <span class="ident">LEGAL_HOLD</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.LIBRARIES_ENTITY"><code class="name">var <span class="ident">LIBRARIES_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.LIBRARY_ENTITY"><code class="name">var <span class="ident">LIBRARY_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.LIBRARY_MEDIA_TYPE_ENTITY"><code class="name">var <span class="ident">LIBRARY_MEDIA_TYPE_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.LICENSE_ENTITY"><code class="name">var <span class="ident">LICENSE_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.LOCALE_ENTITY"><code class="name">var <span class="ident">LOCALE_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.LOCATION_ENTITY"><code class="name">var <span class="ident">LOCATION_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.MACHINE_ENTITIY"><code class="name">var <span class="ident">MACHINE_ENTITIY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.MEDIAAGENT_ENTITY"><code class="name">var <span class="ident">MEDIAAGENT_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.MEDIA_ENTITY"><code class="name">var <span class="ident">MEDIA_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.MEDIA_LOCATIONS"><code class="name">var <span class="ident">MEDIA_LOCATIONS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.MEDIA_SIDE_ENTITY"><code class="name">var <span class="ident">MEDIA_SIDE_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.MEDIA_TYPE_ENTITY"><code class="name">var <span class="ident">MEDIA_TYPE_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.MOUNT_PATH_ENTITY"><code class="name">var <span class="ident">MOUNT_PATH_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.OPERATION_WINDOW"><code class="name">var <span class="ident">OPERATION_WINDOW</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.PROVIDER_ENTITY"><code class="name">var <span class="ident">PROVIDER_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SCHEDULE_POLICY_ENTITY"><code class="name">var <span class="ident">SCHEDULE_POLICY_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SCRATCH_POOL_ENTITY"><code class="name">var <span class="ident">SCRATCH_POOL_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SECURITY"><code class="name">var <span class="ident">SECURITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SHELF_ENTITY"><code class="name">var <span class="ident">SHELF_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SPARE_MEDIA_GROUP_ENTITY"><code class="name">var <span class="ident">SPARE_MEDIA_GROUP_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SRM_REPORTS_ENTITY"><code class="name">var <span class="ident">SRM_REPORTS_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SRM_REPORTS_SET"><code class="name">var <span class="ident">SRM_REPORTS_SET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SRM_REPORT_ENTITY"><code class="name">var <span class="ident">SRM_REPORT_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SRM_REPORT_SET"><code class="name">var <span class="ident">SRM_REPORT_SET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.STORAGE_POLICIES_ENTITY"><code class="name">var <span class="ident">STORAGE_POLICIES_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.STORAGE_POLICY_COPY_ENTITY"><code class="name">var <span class="ident">STORAGE_POLICY_COPY_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.STORAGE_POLICY_ENTITY"><code class="name">var <span class="ident">STORAGE_POLICY_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SUBCLIENT_ENTITY"><code class="name">var <span class="ident">SUBCLIENT_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SUBCLIENT_POLICY_ENTITY"><code class="name">var <span class="ident">SUBCLIENT_POLICY_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.SUBTASK_ENTITY"><code class="name">var <span class="ident">SUBTASK_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.TASK_ENTITY"><code class="name">var <span class="ident">TASK_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.TIME_RANGE"><code class="name">var <span class="ident">TIME_RANGE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.TIME_ZONE"><code class="name">var <span class="ident">TIME_ZONE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.TRACKING_POLICIES_ENTITY"><code class="name">var <span class="ident">TRACKING_POLICIES_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.TRACKING_POLICY_ENTITY"><code class="name">var <span class="ident">TRACKING_POLICY_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.USERGROUP_ENTITY"><code class="name">var <span class="ident">USERGROUP_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.USER_ENTITY"><code class="name">var <span class="ident">USER_ENTITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.CommcellEntity.VAULTRACKER_JOBS"><code class="name">var <span class="ident">VAULTRACKER_JOBS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.Credential_Type"><code class="flex name class">
<span>class <span class="ident">Credential_Type</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain Credential types</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L770-L778" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Credential_Type(Enum):
    &#34;&#34;&#34;Class to maintain Credential types&#34;&#34;&#34;

    WINDOWSACCOUNT = 1
    LINUXACCOUNT = 2
    SSHACCOUNT = 3
    AZUREACCOUNT = 4
    VMWAREACCOUNT = 5
    MICROSOFT_AZURE = 103</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.Credential_Type.AZUREACCOUNT"><code class="name">var <span class="ident">AZUREACCOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.Credential_Type.LINUXACCOUNT"><code class="name">var <span class="ident">LINUXACCOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.Credential_Type.MICROSOFT_AZURE"><code class="name">var <span class="ident">MICROSOFT_AZURE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.Credential_Type.SSHACCOUNT"><code class="name">var <span class="ident">SSHACCOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.Credential_Type.VMWAREACCOUNT"><code class="name">var <span class="ident">VMWAREACCOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.Credential_Type.WINDOWSACCOUNT"><code class="name">var <span class="ident">WINDOWSACCOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.HypervisorType"><code class="flex name class">
<span>class <span class="ident">HypervisorType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain all the hypervisor related constants.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L512-L531" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class HypervisorType(Enum):
    &#34;&#34;&#34;Class to maintain all the hypervisor related constants.&#34;&#34;&#34;
    VIRTUAL_CENTER = &#34;VMware&#34;
    MS_VIRTUAL_SERVER = &#34;Hyper-V&#34;
    AZURE = &#34;Azure&#34;
    AZURE_V2 = &#34;Azure Resource Manager&#34;
    FUSION_COMPUTE = &#34;FusionCompute&#34;
    ORACLE_VM = &#34;OracleVM&#34;
    ALIBABA_CLOUD = &#34;Alibaba Cloud&#34;
    ORACLE_CLOUD = &#34;Oracle Cloud&#34;
    OPENSTACK = &#34;OpenStack&#34;
    GOOGLE_CLOUD = &#34;Google Cloud Platform&#34;
    Azure_Stack = &#34;Azure Stack&#34;
    Rhev = &#34;Red Hat Virtualization&#34;
    AMAZON_AWS = &#34;Amazon Web Services&#34;
    VCLOUD = &#34;vCloud Director&#34;
    Nutanix = &#34;Nutanix AHV&#34;
    ORACLE_CLOUD_INFRASTRUCTURE = &#34;Oracle Cloud Infrastructure&#34;
    OPENSHIFT = &#34;Red Hat OpenShift&#34;
    PROXMOX = &#34;Proxmox ve&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.HypervisorType.ALIBABA_CLOUD"><code class="name">var <span class="ident">ALIBABA_CLOUD</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.AMAZON_AWS"><code class="name">var <span class="ident">AMAZON_AWS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.AZURE"><code class="name">var <span class="ident">AZURE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.AZURE_V2"><code class="name">var <span class="ident">AZURE_V2</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.Azure_Stack"><code class="name">var <span class="ident">Azure_Stack</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.FUSION_COMPUTE"><code class="name">var <span class="ident">FUSION_COMPUTE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.GOOGLE_CLOUD"><code class="name">var <span class="ident">GOOGLE_CLOUD</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.MS_VIRTUAL_SERVER"><code class="name">var <span class="ident">MS_VIRTUAL_SERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.Nutanix"><code class="name">var <span class="ident">Nutanix</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.OPENSHIFT"><code class="name">var <span class="ident">OPENSHIFT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.OPENSTACK"><code class="name">var <span class="ident">OPENSTACK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.ORACLE_CLOUD"><code class="name">var <span class="ident">ORACLE_CLOUD</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.ORACLE_CLOUD_INFRASTRUCTURE"><code class="name">var <span class="ident">ORACLE_CLOUD_INFRASTRUCTURE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.ORACLE_VM"><code class="name">var <span class="ident">ORACLE_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.PROXMOX"><code class="name">var <span class="ident">PROXMOX</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.Rhev"><code class="name">var <span class="ident">Rhev</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.VCLOUD"><code class="name">var <span class="ident">VCLOUD</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.HypervisorType.VIRTUAL_CENTER"><code class="name">var <span class="ident">VIRTUAL_CENTER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.InstanceBackupType"><code class="flex name class">
<span>class <span class="ident">InstanceBackupType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain type of instance backup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L595-L599" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class InstanceBackupType(Enum):
    &#34;&#34;&#34;Class to maintain type of instance backup&#34;&#34;&#34;
    FULL = &#39;full&#39;
    INCREMENTAL = &#39;incremental&#39;
    CUMULATIVE = &#39;incremental&#39;      # cumulative backups pull incremental backup JSON</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.InstanceBackupType.CUMULATIVE"><code class="name">var <span class="ident">CUMULATIVE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.InstanceBackupType.FULL"><code class="name">var <span class="ident">FULL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.InstanceBackupType.INCREMENTAL"><code class="name">var <span class="ident">INCREMENTAL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.OSType"><code class="flex name class">
<span>class <span class="ident">OSType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain OS Types</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L790-L793" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OSType(Enum):
    &#34;&#34;&#34;Class to maintain OS Types&#34;&#34;&#34;
    WINDOWS = 1
    UNIX = 2</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.OSType.UNIX"><code class="name">var <span class="ident">UNIX</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.OSType.WINDOWS"><code class="name">var <span class="ident">WINDOWS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.ResourcePoolAppType"><code class="flex name class">
<span>class <span class="ident">ResourcePoolAppType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain ResourcePool AppType</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L781-L787" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ResourcePoolAppType(Enum):
    &#34;&#34;&#34;Class to maintain ResourcePool AppType&#34;&#34;&#34;
    O365 = 1
    EXCHANGE = 3
    SHAREPOINT = 4
    ONEDRIVE = 5
    TEAMS = 6</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.ResourcePoolAppType.EXCHANGE"><code class="name">var <span class="ident">EXCHANGE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ResourcePoolAppType.O365"><code class="name">var <span class="ident">O365</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ResourcePoolAppType.ONEDRIVE"><code class="name">var <span class="ident">ONEDRIVE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ResourcePoolAppType.SHAREPOINT"><code class="name">var <span class="ident">SHAREPOINT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.ResourcePoolAppType.TEAMS"><code class="name">var <span class="ident">TEAMS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.SQLDefines"><code class="flex name class">
<span>class <span class="ident">SQLDefines</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain SQL Defines</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L602-L613" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SQLDefines:
    &#34;&#34;&#34;Class to maintain SQL Defines&#34;&#34;&#34;

    # sql restore types
    DATABASE_RESTORE = &#39;DATABASE_RESTORE&#39;
    STEP_RESTORE = &#39;STEP_RESTORE&#39;
    RECOVER_ONLY = &#39;RECOVER_ONLY&#39;

    # sql recovery types
    STATE_RECOVER = &#39;STATE_RECOVER&#39;
    STATE_NORECOVER = &#39;STATE_NORECOVER&#39;
    STATE_STANDBY = &#39;STATE_STANDBY&#39;</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.SQLDefines.DATABASE_RESTORE"><code class="name">var <span class="ident">DATABASE_RESTORE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.SQLDefines.RECOVER_ONLY"><code class="name">var <span class="ident">RECOVER_ONLY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.SQLDefines.STATE_NORECOVER"><code class="name">var <span class="ident">STATE_NORECOVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.SQLDefines.STATE_RECOVER"><code class="name">var <span class="ident">STATE_RECOVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.SQLDefines.STATE_STANDBY"><code class="name">var <span class="ident">STATE_STANDBY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.SQLDefines.STEP_RESTORE"><code class="name">var <span class="ident">STEP_RESTORE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.SharepointDefines"><code class="flex name class">
<span>class <span class="ident">SharepointDefines</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintiain Sharepoint Defines</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L616-L621" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SharepointDefines:
    &#34;&#34;&#34;Class to maintiain Sharepoint Defines&#34;&#34;&#34;

    # sharepoint strings
    CONTENT_WEBAPP = &#39;\\MB\\Farm\\Microsoft SharePoint Foundation Web Application\\{0}&#39;
    CONTENT_DB = &#39;\\MB\\Farm\\Microsoft SharePoint Foundation Web Application\\{0}\\{1}&#39;</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.SharepointDefines.CONTENT_DB"><code class="name">var <span class="ident">CONTENT_DB</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.SharepointDefines.CONTENT_WEBAPP"><code class="name">var <span class="ident">CONTENT_WEBAPP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.StoragePoolConstants"><code class="flex name class">
<span>class <span class="ident">StoragePoolConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain storage policy constants</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L678-L700" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class StoragePoolConstants:
    &#34;&#34;&#34;Class to maintain storage policy constants&#34;&#34;&#34;

    AIR_GAP_PROTECT_STORAGE_TYPES = {
        &#34;MICROSOFT AZURE STORAGE&#34;: {
            &#34;HOT&#34;: {
                &#34;vendorId&#34;: 3,
                &#34;displayVendorId&#34;: 401,
            },

            &#34;COOL&#34;: {
                &#34;vendorId&#34;: 3,
                &#34;displayVendorId&#34;: 402,
            },
        },

        &#34;ORACLE CLOUD INFRASTRUCTURE OBJECT STORAGE&#34;: {
            &#34;INFREQUENT ACCESS&#34;: {
                &#34;vendorId&#34;: 26,
                &#34;displayVendorId&#34;: 404,
            },
        }
    }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.StoragePoolConstants.AIR_GAP_PROTECT_STORAGE_TYPES"><code class="name">var <span class="ident">AIR_GAP_PROTECT_STORAGE_TYPES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus"><code class="flex name class">
<span>class <span class="ident">VSAFailOverStatus</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain Failover status of the VSA Live sync</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L651-L663" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VSAFailOverStatus(Enum):
    &#34;&#34;&#34;Class to maintain Failover status of the VSA Live sync&#34;&#34;&#34;
    NONE = 0
    FAILOVER_COMPLETE = 1
    FAILOVER_RUNNING = 2
    FAILOVER_PAUSED = 3
    FAILOVER_FAILED = 4
    FAILBACK_COMPLETE = 5
    FAILBACK_RUNNING = 6
    FAILBACK_PAUSED = 7
    FAILBACK_FAILED = 8
    FAILBACK_PARTIAL = 9
    FAILOVER_PARTIAL = 10</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILBACK_COMPLETE"><code class="name">var <span class="ident">FAILBACK_COMPLETE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILBACK_FAILED"><code class="name">var <span class="ident">FAILBACK_FAILED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILBACK_PARTIAL"><code class="name">var <span class="ident">FAILBACK_PARTIAL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILBACK_PAUSED"><code class="name">var <span class="ident">FAILBACK_PAUSED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILBACK_RUNNING"><code class="name">var <span class="ident">FAILBACK_RUNNING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILOVER_COMPLETE"><code class="name">var <span class="ident">FAILOVER_COMPLETE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILOVER_FAILED"><code class="name">var <span class="ident">FAILOVER_FAILED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILOVER_PARTIAL"><code class="name">var <span class="ident">FAILOVER_PARTIAL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILOVER_PAUSED"><code class="name">var <span class="ident">FAILOVER_PAUSED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.FAILOVER_RUNNING"><code class="name">var <span class="ident">FAILOVER_RUNNING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAFailOverStatus.NONE"><code class="name">var <span class="ident">NONE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus"><code class="flex name class">
<span>class <span class="ident">VSALiveSyncStatus</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain status of the VSA Live sync</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L635-L648" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VSALiveSyncStatus(Enum):
    &#34;&#34;&#34;Class to maintain status of the VSA Live sync&#34;&#34;&#34;
    NEVER_HAS_BEEN_SYNCED = 0
    IN_SYNC = 1
    NEEDS_SYNC = 2
    SYNC_IN_PROGRESS = 3
    SYNC_PAUSED = 4
    SYNC_FAILED = 5
    SYNC_DISABLED = 6
    SYNC_ENABLED = 7
    VALIDATION_FAILED = 8
    SYNC_QUEUED = 9
    REVERT_FAILED = 10
    SYNC_STARTING = 11</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.VSALiveSyncStatus.IN_SYNC"><code class="name">var <span class="ident">IN_SYNC</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.NEEDS_SYNC"><code class="name">var <span class="ident">NEEDS_SYNC</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.NEVER_HAS_BEEN_SYNCED"><code class="name">var <span class="ident">NEVER_HAS_BEEN_SYNCED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.REVERT_FAILED"><code class="name">var <span class="ident">REVERT_FAILED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.SYNC_DISABLED"><code class="name">var <span class="ident">SYNC_DISABLED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.SYNC_ENABLED"><code class="name">var <span class="ident">SYNC_ENABLED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.SYNC_FAILED"><code class="name">var <span class="ident">SYNC_FAILED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.SYNC_IN_PROGRESS"><code class="name">var <span class="ident">SYNC_IN_PROGRESS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.SYNC_PAUSED"><code class="name">var <span class="ident">SYNC_PAUSED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.SYNC_QUEUED"><code class="name">var <span class="ident">SYNC_QUEUED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.SYNC_STARTING"><code class="name">var <span class="ident">SYNC_STARTING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSALiveSyncStatus.VALIDATION_FAILED"><code class="name">var <span class="ident">VALIDATION_FAILED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.VSAObjects"><code class="flex name class">
<span>class <span class="ident">VSAObjects</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Mapping for VSA Objects.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L549-L592" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VSAObjects(Enum):
    &#34;&#34;&#34;Mapping for VSA Objects.&#34;&#34;&#34;
    SERVER = 1
    RESOURCE_POOL = 2
    VAPP = 3
    DATACENTER = 4
    FOLDER = 5
    CLUSTER = 6
    DATASTORE = 7
    DATASTORE_CLUSTER = 8
    VM = 9
    VMName = 10
    VMGuestOS = 11
    VMGuestHostName = 12
    ClusterSharedVolumes = 13
    LocalDisk = 14
    ClusterDisk = 15
    UnprotectedVMs = 16
    ROOT = 17
    FileServer = 18
    SMBShare = 19
    TypesFolder = 20
    VMFolder = 21
    ServerFolder = 22
    TemplateFolder = 23
    StorageRepositoryFolder = 24
    VAppFolder = 25
    DatacenterFolder = 26
    ClusterFolder = 27
    VMPowerState = 28
    VMNotes = 29
    VMCustomAttribute = 30
    Network = 31
    User = 32
    VMTemplate = 33
    Tag = 34
    TagCategory = 35
    Subclient = 36
    ClientGroup = 37
    ProtectionDomain = 38
    ConsistencyGroup = 39
    InstanceSize = 40
    Organization = 41
    Selector = 47</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.VSAObjects.CLUSTER"><code class="name">var <span class="ident">CLUSTER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.ClientGroup"><code class="name">var <span class="ident">ClientGroup</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.ClusterDisk"><code class="name">var <span class="ident">ClusterDisk</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.ClusterFolder"><code class="name">var <span class="ident">ClusterFolder</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.ClusterSharedVolumes"><code class="name">var <span class="ident">ClusterSharedVolumes</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.ConsistencyGroup"><code class="name">var <span class="ident">ConsistencyGroup</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.DATACENTER"><code class="name">var <span class="ident">DATACENTER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.DATASTORE"><code class="name">var <span class="ident">DATASTORE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.DATASTORE_CLUSTER"><code class="name">var <span class="ident">DATASTORE_CLUSTER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.DatacenterFolder"><code class="name">var <span class="ident">DatacenterFolder</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.FOLDER"><code class="name">var <span class="ident">FOLDER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.FileServer"><code class="name">var <span class="ident">FileServer</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.InstanceSize"><code class="name">var <span class="ident">InstanceSize</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.LocalDisk"><code class="name">var <span class="ident">LocalDisk</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.Network"><code class="name">var <span class="ident">Network</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.Organization"><code class="name">var <span class="ident">Organization</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.ProtectionDomain"><code class="name">var <span class="ident">ProtectionDomain</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.RESOURCE_POOL"><code class="name">var <span class="ident">RESOURCE_POOL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.ROOT"><code class="name">var <span class="ident">ROOT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.SERVER"><code class="name">var <span class="ident">SERVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.SMBShare"><code class="name">var <span class="ident">SMBShare</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.Selector"><code class="name">var <span class="ident">Selector</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.ServerFolder"><code class="name">var <span class="ident">ServerFolder</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.StorageRepositoryFolder"><code class="name">var <span class="ident">StorageRepositoryFolder</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.Subclient"><code class="name">var <span class="ident">Subclient</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.Tag"><code class="name">var <span class="ident">Tag</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.TagCategory"><code class="name">var <span class="ident">TagCategory</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.TemplateFolder"><code class="name">var <span class="ident">TemplateFolder</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.TypesFolder"><code class="name">var <span class="ident">TypesFolder</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.UnprotectedVMs"><code class="name">var <span class="ident">UnprotectedVMs</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.User"><code class="name">var <span class="ident">User</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VAPP"><code class="name">var <span class="ident">VAPP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VAppFolder"><code class="name">var <span class="ident">VAppFolder</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VM"><code class="name">var <span class="ident">VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VMCustomAttribute"><code class="name">var <span class="ident">VMCustomAttribute</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VMFolder"><code class="name">var <span class="ident">VMFolder</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VMGuestHostName"><code class="name">var <span class="ident">VMGuestHostName</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VMGuestOS"><code class="name">var <span class="ident">VMGuestOS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VMName"><code class="name">var <span class="ident">VMName</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VMNotes"><code class="name">var <span class="ident">VMNotes</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VMPowerState"><code class="name">var <span class="ident">VMPowerState</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.constants.VSAObjects.VMTemplate"><code class="name">var <span class="ident">VMTemplate</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.constants.VsInstanceType"><code class="flex name class">
<span>class <span class="ident">VsInstanceType</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to store vsinstance dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/constants.py#L795-L818" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VsInstanceType:
    &#34;&#34;&#34;Class to store vsinstance dict&#34;&#34;&#34;

    VSINSTANCE_TYPE = {
        101: &#34;vmware&#34;,
        201: &#34;xen&#34;,
        102: &#34;hyperv&#34;,
        301: &#34;amazon_web_services&#34;,
        401: &#34;azure&#34;,
        402: &#34;azure_resource_manager&#34;,
        403: &#34;azure_stack&#34;,
        501: &#34;red_hat_virtualization&#34;,
        601: &#34;nutanix_ahv&#34;,
        701: &#34;oraclevm&#34;,
        801: &#34;fusioncompute&#34;,
        901: &#34;openstack&#34;,
        1101: &#34;oracle_cloud&#34;,
        1102: &#34;oracle_cloud_infrastructure&#34;,
        1301: &#34;google_cloud_platform&#34;,
        1401: &#34;alibaba_cloud&#34;,
        1503: &#34;vcloud_director&#34;,
        1501: &#34;kubernetes&#34;,
        1600: &#34;proxmox_ve&#34;
    }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.constants.VsInstanceType.VSINSTANCE_TYPE"><code class="name">var <span class="ident">VSINSTANCE_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.constants.AdvancedJobDetailType" href="#cvpysdk.constants.AdvancedJobDetailType">AdvancedJobDetailType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.AdvancedJobDetailType.ADMIN_DATA_INFO" href="#cvpysdk.constants.AdvancedJobDetailType.ADMIN_DATA_INFO">ADMIN_DATA_INFO</a></code></li>
<li><code><a title="cvpysdk.constants.AdvancedJobDetailType.BKUP_INFO" href="#cvpysdk.constants.AdvancedJobDetailType.BKUP_INFO">BKUP_INFO</a></code></li>
<li><code><a title="cvpysdk.constants.AdvancedJobDetailType.DASH_COPY_INFO" href="#cvpysdk.constants.AdvancedJobDetailType.DASH_COPY_INFO">DASH_COPY_INFO</a></code></li>
<li><code><a title="cvpysdk.constants.AdvancedJobDetailType.REFERNCE_COPY_INFO" href="#cvpysdk.constants.AdvancedJobDetailType.REFERNCE_COPY_INFO">REFERNCE_COPY_INFO</a></code></li>
<li><code><a title="cvpysdk.constants.AdvancedJobDetailType.RETENTION_INFO" href="#cvpysdk.constants.AdvancedJobDetailType.RETENTION_INFO">RETENTION_INFO</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.AppIDAName" href="#cvpysdk.constants.AppIDAName">AppIDAName</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.AppIDAName.BIG_DATA_APPS" href="#cvpysdk.constants.AppIDAName.BIG_DATA_APPS">BIG_DATA_APPS</a></code></li>
<li><code><a title="cvpysdk.constants.AppIDAName.FILE_SYSTEM" href="#cvpysdk.constants.AppIDAName.FILE_SYSTEM">FILE_SYSTEM</a></code></li>
<li><code><a title="cvpysdk.constants.AppIDAName.VIRTUAL_SERVER" href="#cvpysdk.constants.AppIDAName.VIRTUAL_SERVER">VIRTUAL_SERVER</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.AppIDAType" href="#cvpysdk.constants.AppIDAType">AppIDAType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.AppIDAType.CLOUD_APP" href="#cvpysdk.constants.AppIDAType.CLOUD_APP">CLOUD_APP</a></code></li>
<li><code><a title="cvpysdk.constants.AppIDAType.LINUX_FILE_SYSTEM" href="#cvpysdk.constants.AppIDAType.LINUX_FILE_SYSTEM">LINUX_FILE_SYSTEM</a></code></li>
<li><code><a title="cvpysdk.constants.AppIDAType.VIRTUAL_SERVER" href="#cvpysdk.constants.AppIDAType.VIRTUAL_SERVER">VIRTUAL_SERVER</a></code></li>
<li><code><a title="cvpysdk.constants.AppIDAType.WINDOWS_FILE_SYSTEM" href="#cvpysdk.constants.AppIDAType.WINDOWS_FILE_SYSTEM">WINDOWS_FILE_SYSTEM</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.ApplicationGroup" href="#cvpysdk.constants.ApplicationGroup">ApplicationGroup</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.constants.ApplicationGroup.CLOUDAPPS" href="#cvpysdk.constants.ApplicationGroup.CLOUDAPPS">CLOUDAPPS</a></code></li>
<li><code><a title="cvpysdk.constants.ApplicationGroup.IBMi" href="#cvpysdk.constants.ApplicationGroup.IBMi">IBMi</a></code></li>
<li><code><a title="cvpysdk.constants.ApplicationGroup.MSSQLSERVER" href="#cvpysdk.constants.ApplicationGroup.MSSQLSERVER">MSSQLSERVER</a></code></li>
<li><code><a title="cvpysdk.constants.ApplicationGroup.OPENVMS" href="#cvpysdk.constants.ApplicationGroup.OPENVMS">OPENVMS</a></code></li>
<li><code><a title="cvpysdk.constants.ApplicationGroup.SHAREPOINTSERVER" href="#cvpysdk.constants.ApplicationGroup.SHAREPOINTSERVER">SHAREPOINTSERVER</a></code></li>
<li><code><a title="cvpysdk.constants.ApplicationGroup.UNIX" href="#cvpysdk.constants.ApplicationGroup.UNIX">UNIX</a></code></li>
<li><code><a title="cvpysdk.constants.ApplicationGroup.WINDOWS" href="#cvpysdk.constants.ApplicationGroup.WINDOWS">WINDOWS</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.CommcellEntity" href="#cvpysdk.constants.CommcellEntity">CommcellEntity</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.CommcellEntity.ALERT_ENTITY" href="#cvpysdk.constants.CommcellEntity.ALERT_ENTITY">ALERT_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.ALL_USERGROUPS_ENTITY" href="#cvpysdk.constants.CommcellEntity.ALL_USERGROUPS_ENTITY">ALL_USERGROUPS_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.ALL_USERS_ENTITY" href="#cvpysdk.constants.CommcellEntity.ALL_USERS_ENTITY">ALL_USERS_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.APPID_ENTITY" href="#cvpysdk.constants.CommcellEntity.APPID_ENTITY">APPID_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.APPTYPE_ENTITY" href="#cvpysdk.constants.CommcellEntity.APPTYPE_ENTITY">APPTYPE_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.AUX_COPY_JOBS" href="#cvpysdk.constants.CommcellEntity.AUX_COPY_JOBS">AUX_COPY_JOBS</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.BACKUPSET_ENTITY" href="#cvpysdk.constants.CommcellEntity.BACKUPSET_ENTITY">BACKUPSET_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.CALENDAR" href="#cvpysdk.constants.CommcellEntity.CALENDAR">CALENDAR</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.CALENDARS" href="#cvpysdk.constants.CommcellEntity.CALENDARS">CALENDARS</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.CALENDAR_ENTITY" href="#cvpysdk.constants.CommcellEntity.CALENDAR_ENTITY">CALENDAR_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.CLIENT_ENTITY" href="#cvpysdk.constants.CommcellEntity.CLIENT_ENTITY">CLIENT_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.CLIENT_GROUP_ENTITY" href="#cvpysdk.constants.CommcellEntity.CLIENT_GROUP_ENTITY">CLIENT_GROUP_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.COMMCELL_ENTITY" href="#cvpysdk.constants.CommcellEntity.COMMCELL_ENTITY">COMMCELL_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.COMPONENT_ENTITY" href="#cvpysdk.constants.CommcellEntity.COMPONENT_ENTITY">COMPONENT_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.DATAPROTECTION_JOBS" href="#cvpysdk.constants.CommcellEntity.DATAPROTECTION_JOBS">DATAPROTECTION_JOBS</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.DATE_TIME" href="#cvpysdk.constants.CommcellEntity.DATE_TIME">DATE_TIME</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.DRIVE_ENTITY" href="#cvpysdk.constants.CommcellEntity.DRIVE_ENTITY">DRIVE_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.DRIVE_POOL_ENTITY" href="#cvpysdk.constants.CommcellEntity.DRIVE_POOL_ENTITY">DRIVE_POOL_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.EXTERNAL_GROUP_ENTITY" href="#cvpysdk.constants.CommcellEntity.EXTERNAL_GROUP_ENTITY">EXTERNAL_GROUP_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.GALAXYRELEASE_ENTITY" href="#cvpysdk.constants.CommcellEntity.GALAXYRELEASE_ENTITY">GALAXYRELEASE_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.GLOBALFILTERS" href="#cvpysdk.constants.CommcellEntity.GLOBALFILTERS">GLOBALFILTERS</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.HOLIDAYS" href="#cvpysdk.constants.CommcellEntity.HOLIDAYS">HOLIDAYS</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.INSTANCE_ENTITY" href="#cvpysdk.constants.CommcellEntity.INSTANCE_ENTITY">INSTANCE_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.LEGAL_HOLD" href="#cvpysdk.constants.CommcellEntity.LEGAL_HOLD">LEGAL_HOLD</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.LIBRARIES_ENTITY" href="#cvpysdk.constants.CommcellEntity.LIBRARIES_ENTITY">LIBRARIES_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.LIBRARY_ENTITY" href="#cvpysdk.constants.CommcellEntity.LIBRARY_ENTITY">LIBRARY_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.LIBRARY_MEDIA_TYPE_ENTITY" href="#cvpysdk.constants.CommcellEntity.LIBRARY_MEDIA_TYPE_ENTITY">LIBRARY_MEDIA_TYPE_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.LICENSE_ENTITY" href="#cvpysdk.constants.CommcellEntity.LICENSE_ENTITY">LICENSE_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.LOCALE_ENTITY" href="#cvpysdk.constants.CommcellEntity.LOCALE_ENTITY">LOCALE_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.LOCATION_ENTITY" href="#cvpysdk.constants.CommcellEntity.LOCATION_ENTITY">LOCATION_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.MACHINE_ENTITIY" href="#cvpysdk.constants.CommcellEntity.MACHINE_ENTITIY">MACHINE_ENTITIY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.MEDIAAGENT_ENTITY" href="#cvpysdk.constants.CommcellEntity.MEDIAAGENT_ENTITY">MEDIAAGENT_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.MEDIA_ENTITY" href="#cvpysdk.constants.CommcellEntity.MEDIA_ENTITY">MEDIA_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.MEDIA_LOCATIONS" href="#cvpysdk.constants.CommcellEntity.MEDIA_LOCATIONS">MEDIA_LOCATIONS</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.MEDIA_SIDE_ENTITY" href="#cvpysdk.constants.CommcellEntity.MEDIA_SIDE_ENTITY">MEDIA_SIDE_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.MEDIA_TYPE_ENTITY" href="#cvpysdk.constants.CommcellEntity.MEDIA_TYPE_ENTITY">MEDIA_TYPE_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.MOUNT_PATH_ENTITY" href="#cvpysdk.constants.CommcellEntity.MOUNT_PATH_ENTITY">MOUNT_PATH_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.OPERATION_WINDOW" href="#cvpysdk.constants.CommcellEntity.OPERATION_WINDOW">OPERATION_WINDOW</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.PROVIDER_ENTITY" href="#cvpysdk.constants.CommcellEntity.PROVIDER_ENTITY">PROVIDER_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SCHEDULE_POLICY_ENTITY" href="#cvpysdk.constants.CommcellEntity.SCHEDULE_POLICY_ENTITY">SCHEDULE_POLICY_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SCRATCH_POOL_ENTITY" href="#cvpysdk.constants.CommcellEntity.SCRATCH_POOL_ENTITY">SCRATCH_POOL_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SECURITY" href="#cvpysdk.constants.CommcellEntity.SECURITY">SECURITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SHELF_ENTITY" href="#cvpysdk.constants.CommcellEntity.SHELF_ENTITY">SHELF_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SPARE_MEDIA_GROUP_ENTITY" href="#cvpysdk.constants.CommcellEntity.SPARE_MEDIA_GROUP_ENTITY">SPARE_MEDIA_GROUP_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SRM_REPORTS_ENTITY" href="#cvpysdk.constants.CommcellEntity.SRM_REPORTS_ENTITY">SRM_REPORTS_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SRM_REPORTS_SET" href="#cvpysdk.constants.CommcellEntity.SRM_REPORTS_SET">SRM_REPORTS_SET</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SRM_REPORT_ENTITY" href="#cvpysdk.constants.CommcellEntity.SRM_REPORT_ENTITY">SRM_REPORT_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SRM_REPORT_SET" href="#cvpysdk.constants.CommcellEntity.SRM_REPORT_SET">SRM_REPORT_SET</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.STORAGE_POLICIES_ENTITY" href="#cvpysdk.constants.CommcellEntity.STORAGE_POLICIES_ENTITY">STORAGE_POLICIES_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.STORAGE_POLICY_COPY_ENTITY" href="#cvpysdk.constants.CommcellEntity.STORAGE_POLICY_COPY_ENTITY">STORAGE_POLICY_COPY_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.STORAGE_POLICY_ENTITY" href="#cvpysdk.constants.CommcellEntity.STORAGE_POLICY_ENTITY">STORAGE_POLICY_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SUBCLIENT_ENTITY" href="#cvpysdk.constants.CommcellEntity.SUBCLIENT_ENTITY">SUBCLIENT_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SUBCLIENT_POLICY_ENTITY" href="#cvpysdk.constants.CommcellEntity.SUBCLIENT_POLICY_ENTITY">SUBCLIENT_POLICY_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.SUBTASK_ENTITY" href="#cvpysdk.constants.CommcellEntity.SUBTASK_ENTITY">SUBTASK_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.TASK_ENTITY" href="#cvpysdk.constants.CommcellEntity.TASK_ENTITY">TASK_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.TIME_RANGE" href="#cvpysdk.constants.CommcellEntity.TIME_RANGE">TIME_RANGE</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.TIME_ZONE" href="#cvpysdk.constants.CommcellEntity.TIME_ZONE">TIME_ZONE</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.TRACKING_POLICIES_ENTITY" href="#cvpysdk.constants.CommcellEntity.TRACKING_POLICIES_ENTITY">TRACKING_POLICIES_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.TRACKING_POLICY_ENTITY" href="#cvpysdk.constants.CommcellEntity.TRACKING_POLICY_ENTITY">TRACKING_POLICY_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.USERGROUP_ENTITY" href="#cvpysdk.constants.CommcellEntity.USERGROUP_ENTITY">USERGROUP_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.USER_ENTITY" href="#cvpysdk.constants.CommcellEntity.USER_ENTITY">USER_ENTITY</a></code></li>
<li><code><a title="cvpysdk.constants.CommcellEntity.VAULTRACKER_JOBS" href="#cvpysdk.constants.CommcellEntity.VAULTRACKER_JOBS">VAULTRACKER_JOBS</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.Credential_Type" href="#cvpysdk.constants.Credential_Type">Credential_Type</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.constants.Credential_Type.AZUREACCOUNT" href="#cvpysdk.constants.Credential_Type.AZUREACCOUNT">AZUREACCOUNT</a></code></li>
<li><code><a title="cvpysdk.constants.Credential_Type.LINUXACCOUNT" href="#cvpysdk.constants.Credential_Type.LINUXACCOUNT">LINUXACCOUNT</a></code></li>
<li><code><a title="cvpysdk.constants.Credential_Type.MICROSOFT_AZURE" href="#cvpysdk.constants.Credential_Type.MICROSOFT_AZURE">MICROSOFT_AZURE</a></code></li>
<li><code><a title="cvpysdk.constants.Credential_Type.SSHACCOUNT" href="#cvpysdk.constants.Credential_Type.SSHACCOUNT">SSHACCOUNT</a></code></li>
<li><code><a title="cvpysdk.constants.Credential_Type.VMWAREACCOUNT" href="#cvpysdk.constants.Credential_Type.VMWAREACCOUNT">VMWAREACCOUNT</a></code></li>
<li><code><a title="cvpysdk.constants.Credential_Type.WINDOWSACCOUNT" href="#cvpysdk.constants.Credential_Type.WINDOWSACCOUNT">WINDOWSACCOUNT</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.HypervisorType" href="#cvpysdk.constants.HypervisorType">HypervisorType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.HypervisorType.ALIBABA_CLOUD" href="#cvpysdk.constants.HypervisorType.ALIBABA_CLOUD">ALIBABA_CLOUD</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.AMAZON_AWS" href="#cvpysdk.constants.HypervisorType.AMAZON_AWS">AMAZON_AWS</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.AZURE" href="#cvpysdk.constants.HypervisorType.AZURE">AZURE</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.AZURE_V2" href="#cvpysdk.constants.HypervisorType.AZURE_V2">AZURE_V2</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.Azure_Stack" href="#cvpysdk.constants.HypervisorType.Azure_Stack">Azure_Stack</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.FUSION_COMPUTE" href="#cvpysdk.constants.HypervisorType.FUSION_COMPUTE">FUSION_COMPUTE</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.GOOGLE_CLOUD" href="#cvpysdk.constants.HypervisorType.GOOGLE_CLOUD">GOOGLE_CLOUD</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.MS_VIRTUAL_SERVER" href="#cvpysdk.constants.HypervisorType.MS_VIRTUAL_SERVER">MS_VIRTUAL_SERVER</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.Nutanix" href="#cvpysdk.constants.HypervisorType.Nutanix">Nutanix</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.OPENSHIFT" href="#cvpysdk.constants.HypervisorType.OPENSHIFT">OPENSHIFT</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.OPENSTACK" href="#cvpysdk.constants.HypervisorType.OPENSTACK">OPENSTACK</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.ORACLE_CLOUD" href="#cvpysdk.constants.HypervisorType.ORACLE_CLOUD">ORACLE_CLOUD</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.ORACLE_CLOUD_INFRASTRUCTURE" href="#cvpysdk.constants.HypervisorType.ORACLE_CLOUD_INFRASTRUCTURE">ORACLE_CLOUD_INFRASTRUCTURE</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.ORACLE_VM" href="#cvpysdk.constants.HypervisorType.ORACLE_VM">ORACLE_VM</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.PROXMOX" href="#cvpysdk.constants.HypervisorType.PROXMOX">PROXMOX</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.Rhev" href="#cvpysdk.constants.HypervisorType.Rhev">Rhev</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.VCLOUD" href="#cvpysdk.constants.HypervisorType.VCLOUD">VCLOUD</a></code></li>
<li><code><a title="cvpysdk.constants.HypervisorType.VIRTUAL_CENTER" href="#cvpysdk.constants.HypervisorType.VIRTUAL_CENTER">VIRTUAL_CENTER</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.InstanceBackupType" href="#cvpysdk.constants.InstanceBackupType">InstanceBackupType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.InstanceBackupType.CUMULATIVE" href="#cvpysdk.constants.InstanceBackupType.CUMULATIVE">CUMULATIVE</a></code></li>
<li><code><a title="cvpysdk.constants.InstanceBackupType.FULL" href="#cvpysdk.constants.InstanceBackupType.FULL">FULL</a></code></li>
<li><code><a title="cvpysdk.constants.InstanceBackupType.INCREMENTAL" href="#cvpysdk.constants.InstanceBackupType.INCREMENTAL">INCREMENTAL</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.OSType" href="#cvpysdk.constants.OSType">OSType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.OSType.UNIX" href="#cvpysdk.constants.OSType.UNIX">UNIX</a></code></li>
<li><code><a title="cvpysdk.constants.OSType.WINDOWS" href="#cvpysdk.constants.OSType.WINDOWS">WINDOWS</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.ResourcePoolAppType" href="#cvpysdk.constants.ResourcePoolAppType">ResourcePoolAppType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.ResourcePoolAppType.EXCHANGE" href="#cvpysdk.constants.ResourcePoolAppType.EXCHANGE">EXCHANGE</a></code></li>
<li><code><a title="cvpysdk.constants.ResourcePoolAppType.O365" href="#cvpysdk.constants.ResourcePoolAppType.O365">O365</a></code></li>
<li><code><a title="cvpysdk.constants.ResourcePoolAppType.ONEDRIVE" href="#cvpysdk.constants.ResourcePoolAppType.ONEDRIVE">ONEDRIVE</a></code></li>
<li><code><a title="cvpysdk.constants.ResourcePoolAppType.SHAREPOINT" href="#cvpysdk.constants.ResourcePoolAppType.SHAREPOINT">SHAREPOINT</a></code></li>
<li><code><a title="cvpysdk.constants.ResourcePoolAppType.TEAMS" href="#cvpysdk.constants.ResourcePoolAppType.TEAMS">TEAMS</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.SQLDefines" href="#cvpysdk.constants.SQLDefines">SQLDefines</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.constants.SQLDefines.DATABASE_RESTORE" href="#cvpysdk.constants.SQLDefines.DATABASE_RESTORE">DATABASE_RESTORE</a></code></li>
<li><code><a title="cvpysdk.constants.SQLDefines.RECOVER_ONLY" href="#cvpysdk.constants.SQLDefines.RECOVER_ONLY">RECOVER_ONLY</a></code></li>
<li><code><a title="cvpysdk.constants.SQLDefines.STATE_NORECOVER" href="#cvpysdk.constants.SQLDefines.STATE_NORECOVER">STATE_NORECOVER</a></code></li>
<li><code><a title="cvpysdk.constants.SQLDefines.STATE_RECOVER" href="#cvpysdk.constants.SQLDefines.STATE_RECOVER">STATE_RECOVER</a></code></li>
<li><code><a title="cvpysdk.constants.SQLDefines.STATE_STANDBY" href="#cvpysdk.constants.SQLDefines.STATE_STANDBY">STATE_STANDBY</a></code></li>
<li><code><a title="cvpysdk.constants.SQLDefines.STEP_RESTORE" href="#cvpysdk.constants.SQLDefines.STEP_RESTORE">STEP_RESTORE</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.SharepointDefines" href="#cvpysdk.constants.SharepointDefines">SharepointDefines</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.SharepointDefines.CONTENT_DB" href="#cvpysdk.constants.SharepointDefines.CONTENT_DB">CONTENT_DB</a></code></li>
<li><code><a title="cvpysdk.constants.SharepointDefines.CONTENT_WEBAPP" href="#cvpysdk.constants.SharepointDefines.CONTENT_WEBAPP">CONTENT_WEBAPP</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.StoragePoolConstants" href="#cvpysdk.constants.StoragePoolConstants">StoragePoolConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.StoragePoolConstants.AIR_GAP_PROTECT_STORAGE_TYPES" href="#cvpysdk.constants.StoragePoolConstants.AIR_GAP_PROTECT_STORAGE_TYPES">AIR_GAP_PROTECT_STORAGE_TYPES</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.VSAFailOverStatus" href="#cvpysdk.constants.VSAFailOverStatus">VSAFailOverStatus</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILBACK_COMPLETE" href="#cvpysdk.constants.VSAFailOverStatus.FAILBACK_COMPLETE">FAILBACK_COMPLETE</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILBACK_FAILED" href="#cvpysdk.constants.VSAFailOverStatus.FAILBACK_FAILED">FAILBACK_FAILED</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILBACK_PARTIAL" href="#cvpysdk.constants.VSAFailOverStatus.FAILBACK_PARTIAL">FAILBACK_PARTIAL</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILBACK_PAUSED" href="#cvpysdk.constants.VSAFailOverStatus.FAILBACK_PAUSED">FAILBACK_PAUSED</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILBACK_RUNNING" href="#cvpysdk.constants.VSAFailOverStatus.FAILBACK_RUNNING">FAILBACK_RUNNING</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILOVER_COMPLETE" href="#cvpysdk.constants.VSAFailOverStatus.FAILOVER_COMPLETE">FAILOVER_COMPLETE</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILOVER_FAILED" href="#cvpysdk.constants.VSAFailOverStatus.FAILOVER_FAILED">FAILOVER_FAILED</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILOVER_PARTIAL" href="#cvpysdk.constants.VSAFailOverStatus.FAILOVER_PARTIAL">FAILOVER_PARTIAL</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILOVER_PAUSED" href="#cvpysdk.constants.VSAFailOverStatus.FAILOVER_PAUSED">FAILOVER_PAUSED</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.FAILOVER_RUNNING" href="#cvpysdk.constants.VSAFailOverStatus.FAILOVER_RUNNING">FAILOVER_RUNNING</a></code></li>
<li><code><a title="cvpysdk.constants.VSAFailOverStatus.NONE" href="#cvpysdk.constants.VSAFailOverStatus.NONE">NONE</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.VSALiveSyncStatus" href="#cvpysdk.constants.VSALiveSyncStatus">VSALiveSyncStatus</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.IN_SYNC" href="#cvpysdk.constants.VSALiveSyncStatus.IN_SYNC">IN_SYNC</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.NEEDS_SYNC" href="#cvpysdk.constants.VSALiveSyncStatus.NEEDS_SYNC">NEEDS_SYNC</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.NEVER_HAS_BEEN_SYNCED" href="#cvpysdk.constants.VSALiveSyncStatus.NEVER_HAS_BEEN_SYNCED">NEVER_HAS_BEEN_SYNCED</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.REVERT_FAILED" href="#cvpysdk.constants.VSALiveSyncStatus.REVERT_FAILED">REVERT_FAILED</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.SYNC_DISABLED" href="#cvpysdk.constants.VSALiveSyncStatus.SYNC_DISABLED">SYNC_DISABLED</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.SYNC_ENABLED" href="#cvpysdk.constants.VSALiveSyncStatus.SYNC_ENABLED">SYNC_ENABLED</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.SYNC_FAILED" href="#cvpysdk.constants.VSALiveSyncStatus.SYNC_FAILED">SYNC_FAILED</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.SYNC_IN_PROGRESS" href="#cvpysdk.constants.VSALiveSyncStatus.SYNC_IN_PROGRESS">SYNC_IN_PROGRESS</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.SYNC_PAUSED" href="#cvpysdk.constants.VSALiveSyncStatus.SYNC_PAUSED">SYNC_PAUSED</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.SYNC_QUEUED" href="#cvpysdk.constants.VSALiveSyncStatus.SYNC_QUEUED">SYNC_QUEUED</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.SYNC_STARTING" href="#cvpysdk.constants.VSALiveSyncStatus.SYNC_STARTING">SYNC_STARTING</a></code></li>
<li><code><a title="cvpysdk.constants.VSALiveSyncStatus.VALIDATION_FAILED" href="#cvpysdk.constants.VSALiveSyncStatus.VALIDATION_FAILED">VALIDATION_FAILED</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.VSAObjects" href="#cvpysdk.constants.VSAObjects">VSAObjects</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.VSAObjects.CLUSTER" href="#cvpysdk.constants.VSAObjects.CLUSTER">CLUSTER</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.ClientGroup" href="#cvpysdk.constants.VSAObjects.ClientGroup">ClientGroup</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.ClusterDisk" href="#cvpysdk.constants.VSAObjects.ClusterDisk">ClusterDisk</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.ClusterFolder" href="#cvpysdk.constants.VSAObjects.ClusterFolder">ClusterFolder</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.ClusterSharedVolumes" href="#cvpysdk.constants.VSAObjects.ClusterSharedVolumes">ClusterSharedVolumes</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.ConsistencyGroup" href="#cvpysdk.constants.VSAObjects.ConsistencyGroup">ConsistencyGroup</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.DATACENTER" href="#cvpysdk.constants.VSAObjects.DATACENTER">DATACENTER</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.DATASTORE" href="#cvpysdk.constants.VSAObjects.DATASTORE">DATASTORE</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.DATASTORE_CLUSTER" href="#cvpysdk.constants.VSAObjects.DATASTORE_CLUSTER">DATASTORE_CLUSTER</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.DatacenterFolder" href="#cvpysdk.constants.VSAObjects.DatacenterFolder">DatacenterFolder</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.FOLDER" href="#cvpysdk.constants.VSAObjects.FOLDER">FOLDER</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.FileServer" href="#cvpysdk.constants.VSAObjects.FileServer">FileServer</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.InstanceSize" href="#cvpysdk.constants.VSAObjects.InstanceSize">InstanceSize</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.LocalDisk" href="#cvpysdk.constants.VSAObjects.LocalDisk">LocalDisk</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.Network" href="#cvpysdk.constants.VSAObjects.Network">Network</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.Organization" href="#cvpysdk.constants.VSAObjects.Organization">Organization</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.ProtectionDomain" href="#cvpysdk.constants.VSAObjects.ProtectionDomain">ProtectionDomain</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.RESOURCE_POOL" href="#cvpysdk.constants.VSAObjects.RESOURCE_POOL">RESOURCE_POOL</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.ROOT" href="#cvpysdk.constants.VSAObjects.ROOT">ROOT</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.SERVER" href="#cvpysdk.constants.VSAObjects.SERVER">SERVER</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.SMBShare" href="#cvpysdk.constants.VSAObjects.SMBShare">SMBShare</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.Selector" href="#cvpysdk.constants.VSAObjects.Selector">Selector</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.ServerFolder" href="#cvpysdk.constants.VSAObjects.ServerFolder">ServerFolder</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.StorageRepositoryFolder" href="#cvpysdk.constants.VSAObjects.StorageRepositoryFolder">StorageRepositoryFolder</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.Subclient" href="#cvpysdk.constants.VSAObjects.Subclient">Subclient</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.Tag" href="#cvpysdk.constants.VSAObjects.Tag">Tag</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.TagCategory" href="#cvpysdk.constants.VSAObjects.TagCategory">TagCategory</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.TemplateFolder" href="#cvpysdk.constants.VSAObjects.TemplateFolder">TemplateFolder</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.TypesFolder" href="#cvpysdk.constants.VSAObjects.TypesFolder">TypesFolder</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.UnprotectedVMs" href="#cvpysdk.constants.VSAObjects.UnprotectedVMs">UnprotectedVMs</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.User" href="#cvpysdk.constants.VSAObjects.User">User</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VAPP" href="#cvpysdk.constants.VSAObjects.VAPP">VAPP</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VAppFolder" href="#cvpysdk.constants.VSAObjects.VAppFolder">VAppFolder</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VM" href="#cvpysdk.constants.VSAObjects.VM">VM</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VMCustomAttribute" href="#cvpysdk.constants.VSAObjects.VMCustomAttribute">VMCustomAttribute</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VMFolder" href="#cvpysdk.constants.VSAObjects.VMFolder">VMFolder</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VMGuestHostName" href="#cvpysdk.constants.VSAObjects.VMGuestHostName">VMGuestHostName</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VMGuestOS" href="#cvpysdk.constants.VSAObjects.VMGuestOS">VMGuestOS</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VMName" href="#cvpysdk.constants.VSAObjects.VMName">VMName</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VMNotes" href="#cvpysdk.constants.VSAObjects.VMNotes">VMNotes</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VMPowerState" href="#cvpysdk.constants.VSAObjects.VMPowerState">VMPowerState</a></code></li>
<li><code><a title="cvpysdk.constants.VSAObjects.VMTemplate" href="#cvpysdk.constants.VSAObjects.VMTemplate">VMTemplate</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.constants.VsInstanceType" href="#cvpysdk.constants.VsInstanceType">VsInstanceType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.constants.VsInstanceType.VSINSTANCE_TYPE" href="#cvpysdk.constants.VsInstanceType.VSINSTANCE_TYPE">VSINSTANCE_TYPE</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>