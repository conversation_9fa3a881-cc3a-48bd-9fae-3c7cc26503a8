import requests
import urllib3



url = "https://192.168.100.55/webconsole/proxy/Login" # Replace with your target URL
headers = {"X-CSRF-Token": "fetch"} # Often required for the server to send the token

try:
    response = requests.get(url, headers=headers, verify=False)
    response.raise_for_status() # Raise an exception for bad status codes

    csrf_token = response.headers.get("X-CSRF-Token")
    if csrf_token:
        print(f"X-CSRF-Token: {csrf_token}")
    else:
        print("X-CSRF-Token not found in response headers.")

except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")

####################################################################################
# # Suppress warnings for self-signed certificates in a lab environment
# urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# # Step 1: Make a GET request to get the X-CSRF-Token
# # The endpoint might vary, but /webconsole/ is a good starting point for a session
# token_url = "https://192.168.100.55/webconsole"
# token_response = requests.get(token_url, verify=False)
# token_response.raise_for_status()

# # Step 2: Extract the token from the response headers
# # Commvault's API often returns the token in the 'X-CSRF-Token' header on an initial GET.
# csrf_token = token_response.headers.get("X-CSRF-Token")

# if not csrf_token:
#     print("Error: X-CSRF-Token not found in the response headers.")
#     exit()

# print("Successfully retrieved X-CSRF-Token:", csrf_token)

# # Step 3: Use the token in a subsequent POST request (e.g., for login)
# login_url = "https://192.168.100.55/webconsole/proxy/Login"
# login_headers = {
#     "accept": "application/json",
#     "Content-Type": "application/json",
#     "X-CSRF-Token": csrf_token  # Include the token here
# }

# login_data = {
#     "username": "admin",
#     "password": "UEBzc3cwcmQ="  # Base64-encoded password
# }

# try:
#     login_response = requests.post(login_url, headers=login_headers, json=login_data, verify=False)
#     login_response.raise_for_status()

#     print("\nLogin successful! Response body:")
#     print(login_response.json())

# except requests.exceptions.RequestException as e:
#     print(f"\nAn error occurred during login: {e}")