import requests
import urllib3

# Suppress SSL warnings for self-signed certificates in a lab environment
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# The URL for the login proxy endpoint
url = "https://192.168.100.55/webconsole/proxy/Login"

# Define the request headers, including the CSRF token
headers = {
    "accept": "application/json",
    "Content-Type": "application/json"
    "X-CSRF-Token": "9650dbcdbd9e4b12a4cd6255df4afbee"
}

# The JSON payload for the request body
data = {
    "username": "admin",
    "password": "UEBzc3cwcmQ="
}

try:
    # Make the POST request with the specified URL, headers, and JSON data
    response = requests.post(url, headers=headers, json=data, verify=False)

    # Raise an HTTPError for bad responses (4xx or 5xx)
    response.raise_for_status()

    # Print the response content
    print("Status Code:", response.status_code)
    print("Response Body:", response.json())

    # Extract the token if the login is successful
    auth_token = response.json().get('token')
    if auth_token:
        print("\nLogin successful! Token received:")
        print(auth_token)

except requests.exceptions.RequestException as e:
    print(f"An error occurred: {e}")