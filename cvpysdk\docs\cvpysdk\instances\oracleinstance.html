<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.oracleinstance API documentation</title>
<meta name="description" content="File for operating on a Oracle Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.oracleinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Oracle Instance.</p>
<p>OracleInstance is the only class defined in this file.</p>
<p>OracleInstance: Derived class from Instance Base class, representing an
oracle instance, and to perform operations on that instance</p>
<h2 id="oracleinstance">Oracleinstance</h2>
<p><strong>init</strong>()
&ndash;
Constructor for the class</p>
<p>restore_to_disk
&ndash;
Performs restore to disk(app free restore)</p>
<p>_get_instance_properties()
&ndash;
gets the properties of this instance</p>
<p>_get_instance_properties_json()
&ndash;
gets all the instance related properties
of Oracle instance</p>
<p>_restore_common_options_json()
&ndash;
Setter for the Common options in restore JSON</p>
<p>_restore_destination_json()
&ndash;
Setter for the Oracle destination options in restore JSON</p>
<p>_get_live_sync_oracleopt_json()
&ndash;
Constructs JSON with oracle agent specific options
for configuring live sync</p>
<p>_live_sync_restore_json()
&ndash;
Constructs oracle live sync restore JSON
by combining common and agent specific options</p>
<p>create_live_sync_schedule()
&ndash;
Creates live sync schedule for the given
destination oracle instance</p>
<p>configure_data_masking_policy()
&ndash;
Configures data masking
policy with given parameters</p>
<p>get_masking_policy_id()
&ndash;
To get policy id of
given data masking policy</p>
<p>standalone_data_masking()
&ndash;
Launch standalone data masking
job on given instance</p>
<p>delete_data_masking_policy()
&ndash;
Deletes given data masking policy</p>
<p>_get_browse_options
&ndash;
To get browse options for oracle instance</p>
<p>_process_browse_response
&ndash;
To process browse response</p>
<p>log_stream()
&ndash;
Getter for fetching archive log stream count</p>
<p>oracle_home()
&ndash;
Getter for $ORACLE_HOME of this instance</p>
<p>version()
&ndash;
Getter for oracle database version</p>
<p>is_catalog_enabled()
&ndash;
Getter to check if catalog is enabled for backups</p>
<p>catalog_user()
&ndash;
Getter for getting catalog user</p>
<p>catalog_db()
&ndash;
Getter for catalog database name</p>
<p>archive_log_dest()
&ndash;
Getter for archivelog destination</p>
<p>os_user()
&ndash;
Getter for OS user owning oracle software</p>
<p>cmd_sp()
&ndash;
Getter for command line storage policy</p>
<p>log_sp()
&ndash;
Getter for log storage policy</p>
<p>is_autobackup_on()
&ndash;
Getter to check if autobackup is enabled</p>
<p>db_user()
&ndash;
Getter for SYS database user name</p>
<p>tns_name()
&ndash;
Getter for TNS connect string</p>
<p>dbid()
&ndash;
Getter for getting DBID of database</p>
<p>restore()
&ndash;
Performs restore on the instance</p>
<p>_restore_db_dump_option_json()
&ndash;
setter for the oracle dbdump Restore option in restore JSON</p>
<p>_restore_oracle_option_json()
&ndash;
setter for the oracle Restore option in restore JSON</p>
<p>_restore_json()
&ndash;
returns the JSON request to pass to the API as per
the options selected by the user</p>
<p>restore_in_place()
&ndash;
restore for oracle logical dump</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L1-L1344" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------
&#34;&#34;&#34;
File for operating on a Oracle Instance.

OracleInstance is the only class defined in this file.

OracleInstance: Derived class from Instance Base class, representing an
                            oracle instance, and to perform operations on that instance

OracleInstance:

    __init__()                          --  Constructor for the class

    restore_to_disk                     --  Performs restore to disk(app free restore)

    _get_instance_properties()          --  gets the properties of this instance

    _get_instance_properties_json()     --  gets all the instance related properties
                                            of Oracle instance

    _restore_common_options_json()      --  Setter for the Common options in restore JSON

    _restore_destination_json()         --  Setter for the Oracle destination options in restore JSON


    _get_live_sync_oracleopt_json()     --  Constructs JSON with oracle agent specific options
                                            for configuring live sync

    _live_sync_restore_json()           --  Constructs oracle live sync restore JSON
                                            by combining common and agent specific options

    create_live_sync_schedule()         --  Creates live sync schedule for the given
                                            destination oracle instance

    configure_data_masking_policy()     --  Configures data masking
                                            policy with given parameters

    get_masking_policy_id()             --  To get policy id of
                                            given data masking policy

    standalone_data_masking()           --  Launch standalone data masking
                                            job on given instance

    delete_data_masking_policy()        --  Deletes given data masking policy

    _get_browse_options                 --  To get browse options for oracle instance

    _process_browse_response            --  To process browse response

    log_stream()                        --  Getter for fetching archive log stream count

    oracle_home()                       --  Getter for $ORACLE_HOME of this instance

    version()                           --  Getter for oracle database version

    is_catalog_enabled()                --  Getter to check if catalog is enabled for backups

    catalog_user()                      --  Getter for getting catalog user

    catalog_db()                        --  Getter for catalog database name

    archive_log_dest()                  --  Getter for archivelog destination

    os_user()                           --  Getter for OS user owning oracle software

    cmd_sp()                            --  Getter for command line storage policy

    log_sp()                            --  Getter for log storage policy

    is_autobackup_on()                  --  Getter to check if autobackup is enabled

    db_user()                           --  Getter for SYS database user name

    tns_name()                          --  Getter for TNS connect string

    dbid()                              --  Getter for getting DBID of database

    restore()                           --  Performs restore on the instance
    
    _restore_db_dump_option_json()       --  setter for the oracle dbdump Restore option in restore JSON
    
    _restore_oracle_option_json()       --  setter for the oracle Restore option in restore JSON
    
    _restore_json()                     --  returns the JSON request to pass to the API as per
    the options selected by the user
    
    restore_in_place()                  --  restore for oracle logical dump

&#34;&#34;&#34;
from __future__ import unicode_literals
from base64 import b64encode
import json
from ..exception import SDKException
from .dbinstance import DatabaseInstance



class OracleInstance(DatabaseInstance):
    &#34;&#34;&#34;
    Class to represent a standalone Oracle Instance
    &#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            agent_object    -- instance of the Agent class
            instance_name   -- name of the instance
            instance_id     --  id of the instance

        &#34;&#34;&#34;
        super(OracleInstance, self).__init__(
            agent_object, instance_name, instance_id)
        self._LIVE_SYNC = self._commcell_object._services[&#39;LIVE_SYNC&#39;]
        self._dbDump_restore_json = None
        self._oracle_restore_json = None

    def restore_to_disk(self,
                        destination_client,
                        destination_path,
                        backup_job_ids,
                        user_name,
                        password):
        &#34;&#34;&#34;
        Perform restore to disk [Application free restore] for Oracle

            Args:
                destination_client          (str)   --  destination client name

                destination_path:           (str)   --  destination path

                backup_job_ids              (list)  --  list of backup job IDs
                                                        to be used for disk restore

                user_name                   (str)   --  impersonation user name to
                                                        restore to destination client

                password                    (str)   --  impersonation user password

            Returns:
                object -     Job containing restore details

            Raises:
                SDKException
                    if backup_job_ids not given as list of items

        &#34;&#34;&#34;
        if not isinstance(backup_job_ids, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        request_json = self._get_restore_to_disk_json(destination_client,
                                                      destination_path,
                                                      backup_job_ids,
                                                      user_name,
                                                      password)
        return self._process_restore_response(request_json)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(OracleInstance, self)._get_instance_properties()
        self._instanceprop = self._properties[&#39;oracleInstance&#39;]

    def _get_instance_properties_json(self):
        &#34;&#34;&#34; Gets all the instance related properties of Informix instance.

           Returns:
                dict - all instance properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;:
                {
                    &#34;instance&#34;: self._instance,
                    &#34;oracleInstance&#34;: self._instanceprop
                }
        }
        return instance_json

    @property
    def log_stream(self):
        &#34;&#34;&#34;
        Getter to fetch log stream count at instance level

            Returns:
                    int     --  log stream count atinstance level

        &#34;&#34;&#34;
        return self._instanceprop.get(&#34;numberOfArchiveLogBackupStreams&#34;)

    @log_stream.setter
    def log_stream(self, log_stream=1):
        &#34;&#34;&#34;
        Setter to set log stream count at instance level

            Args:
                log_stream    (int)    --  log stream count at instance level
                                           default = 1
        &#34;&#34;&#34;
        self._set_instance_properties(
            &#34;_instanceprop[&#39;numberOfArchiveLogBackupStreams&#39;]&#34;, log_stream)

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;
        Setter for the Common options in restore JSON

            Args:
                value   (dict)  --  dict of common options
                                    for restore json

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        super()._restore_common_options_json(value)
        if value.get(&#34;baseline_jobid&#34;):
            self._commonoption_restore_json = (
                {
                    &#34;clusterDBBackedup&#34;: value.get(&#34;clusterDBBackedup&#34;, False),
                    &#34;restoreToDisk&#34;: value.get(&#34;restoreToDisk&#34;, False),
                    &#34;baselineBackup&#34;: 1,
                    &#34;baselineRefTime&#34;: value.get(&#34;baseline_ref_time&#34;, &#34;&#34;),
                    &#34;isDBArchiveRestore&#34;: value.get(&#34;isDBArchiveRestore&#34;, False),
                    &#34;baselineJobId&#34;: value.get(&#34;baseline_jobid&#34;, &#34;&#34;),
                    &#34;copyToObjectStore&#34;: value.get(&#34;copyToObjectStore&#34;, False),
                    &#34;onePassRestore&#34;: value.get(&#34;onePassRestore&#34;, False),
                    &#34;syncRestore&#34;: value.get(&#34;syncRestore&#34;, True)
                })

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;
        Setter for the Oracle destination options in restore JSON

            Args:
                    value   (dict)  --  dict of values for destination option

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._destination_restore_json = (
            {
                &#34;noOfStreams&#34;: value.get(&#34;number_of_streams&#34;, 2),
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;)
                              },
                &#34;destinationInstance&#34;: {
                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;),
                    &#34;instanceName&#34;: value.get(&#34;destination_instance&#34;, &#34;&#34;),
                    &#34;appName&#34;: value.get(&#34;app_name&#34;, &#34;Oracle&#34;)
                                       }

            })

    def _get_live_sync_oracleopt_json(self, **kwargs):
        &#34;&#34;&#34;
               Constructs JSON with oracle agent specific options
               for configuring live sync

               Args:
                    **kwargs    (dict)  -- dict of keyword arguments as follows:

                                           redirect_path   (str)- Path on destination client
                                                        to redirect tablespaces and datafiles

        &#34;&#34;&#34;

        self._oracle_options = {&#34;renamePathForAllTablespaces&#34;:&#34;&#34;,
                                &#34;redirectAllItemsSelected&#34;: False,
                                &#34;validate&#34;: False,
                                &#34;ctrlRestoreFrom&#34;: True,
                                &#34;noCatalog&#34;: True,
                                &#34;cloneEnv&#34;:False,
                                &#34;ctrlFileBackupType&#34;: 0,
                                &#34;restoreControlFile&#34;: True,
                                &#34;duplicate&#34;: False,
                                &#34;tableViewRestore&#34;: False,
                                &#34;osID&#34;:2,
                                &#34;partialRestore&#34;: False,
                                &#34;restoreStream&#34;:2,
                                &#34;restoreSPFile&#34;: False,
                                &#34;recover&#34;: True,
                                &#34;oraExtendedRstOptions&#34;: 0,
                                &#34;recoverFrom&#34;: 3,
                                &#34;archiveLog&#34;: False,
                                &#34;restoreData&#34;: True,
                                &#34;restoreFrom&#34;: 3,
                                &#34;crossmachineRestoreOptions&#34;: {
                                &#34;onlineLogDest&#34;: &#34;&#34;
                                    },
                                &#34;liveSyncOpt&#34;:{
                                    &#34;restoreInStandby&#34;:False
                                }
                                }
        if kwargs.get(&#39;redirect_path&#39;, None) is not None:
            self._oracle_options.update({&#34;renamePathForAllTablespaces&#34;: kwargs.get(&#39;redirect_path&#39;),
                                         &#34;redirectAllItemsSelected&#34;: True,
                                         &#34;redirectItemsPresent&#34;: True
                                         })

    def _live_sync_restore_json(self, dest_client, dest_instance, baseline_jobid, baseline_ref_time,
                                schedule_name, source_backupset_id, **kwargs):
        &#34;&#34;&#34;
               Constructs oracle live sync restore JSON by combining common
               and agent specific options

                   Args:
                       dest_client  (str)   --  The destination client name for live sync

                       dest_instance    (str)   --  The destination instance name for live sync

                       baseline_jobid   (int)   --  The jobid of the baseline backup job

                       baseline_ref_time    (int)   --  The reference time/start time
                                                        of the baseline backup

                       schedule_name    (str)   --  The name of the live sync schedule to be created

                       source_backupset_id  (int)   --  The ID of the source backupset
                                                        of source oracle instance for which
                                                        live sync needs to be configured

                       **kwargs    (dict)  -- dict of keyword arguments as follows:

                                           redirect_path   (str)- Path on destination client
                                                        to redirect tablespaces and datafiles

                    Returns:
                        (str)  --   The live sync restore JSON that is constructed
                                    using oracle and common options

        &#34;&#34;&#34;

        restore_json = super()._restore_json(destination_client=dest_client,
                                             destination_instance=dest_instance,
                                             baseline_jobid=baseline_jobid,
                                             baseline_ref_time=baseline_ref_time,
                                             syncRestore=True,
                                             no_of_streams=2,
                                            )
        restore_option = {}
        if restore_json.get(&#34;restore_option&#34;):
            restore_option = restore_json[&#34;restore_option&#34;]
            for key in restore_json:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = restore_json[key]
        else:
            restore_option.update(restore_json)

        self._get_live_sync_oracleopt_json(**kwargs)
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientId&#39;] = -1
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetId&#39;] = source_backupset_id
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = &#34;&#34;
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetName&#39;] = &#34;&#34;
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;_type_&#39;] = 5
        restore_json[&#39;taskInfo&#39;][&#39;task&#39;][&#39;taskType&#39;] = 2
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;operationType&#39;] = 1007
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;subTaskName&#39;] = schedule_name
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;] = {
            &#34;freq_type&#34;: 4096
        }
        destinationInstance = {
            &#34;clientName&#34;:dest_client,
            &#34;instanceName&#34;:dest_instance,
            &#34;appName&#34;:&#34;Oracle&#34;
        }
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;destination&#34;].update({&#34;destinationInstance&#34;:destinationInstance})
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;oracleOpt&#34;] = self._oracle_options
        return restore_json

    def create_live_sync_schedule(self, dest_client, dest_instance, schedule_name,
                                  **kwargs):
        &#34;&#34;&#34;
               Runs full backup on source oracle instance and
               Creates live sync schdule for the given destination oracle instance

                   Args:
                       dest_client  (str)   --  The destination client name for live sync

                       dest_instance    (str)   --  The destination instance name for live sync

                       schedule_name    (str)   --  The name of the live sync schedule to be created

                       **kwargs    (dict)  -- dict of keyword arguments as follows:

                                            redirect_path   (str) --  Path on destination client
                                                            to redirect tablespaces and datafiles

                   Returns:
                        (object)  --   The job object of the baseline backup that will be replicated

        &#34;&#34;&#34;
        source_backupset_id = int(self.backupsets.get(&#39;default&#39;).backupset_id)
        subclient_obj = self.subclients.get(&#39;default&#39;)
        baseline_job_object = subclient_obj.backup(backup_level=&#39;full&#39;)
        if not baseline_job_object.wait_for_completion():
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, baseline_job_object.delay_reason)
        baseline_ref_time = baseline_job_object.summary[&#39;jobStartTime&#39;]
        baseline_jobid = int(baseline_job_object.job_id)
        request_json = self._live_sync_restore_json(dest_client, dest_instance, baseline_jobid,
                                                    baseline_ref_time, schedule_name,
                                                    source_backupset_id, **kwargs)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._LIVE_SYNC, request_json)
        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    return baseline_job_object
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    error_message = &#39;Live Sync configuration failed\nError: &#34;{0}&#34;&#39;.format(
                        error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to create schedule&#39;)
            else:
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;, self._update_response_(response.text))

    def configure_data_masking_policy(self, policy_name, table_list_of_dict):
        &#34;&#34;&#34;Configures data masking policy with given parameters
        Args:
            policy_name         (str)   --  string representing policy name
            table_list_of_dict  list(dict)  -- list containing one dict item representing
                                                rules for single table
            Sample  list
            Tables:
                    [
                    {
                    &#34;name&#34;:&#34;schema_name.table_name&#34;,
                    &#34;columns&#34;: [ {&#34;name&#34;:&#34;column_name&#34;, &#34;type&#34;:&#34;algorithm_type&#34;},
                                &#34;arguments&#34;:[list of strings]…]
                    }
                    ]
                    Sample :
                    [
                    {
                    &#34;name&#34;:&#34;HR.NUMNEW&#34;,
                    &#34;columns&#34;:[{&#34;name&#34;:&#34;N1&#34;,&#34;type&#34;:0},{&#34;name&#34;:&#34;N2&#34;,&#34;type&#34;:2,
                    &#34;arguments&#34;:[&#34;1000&#34;,&#34;2000&#34;]}]
                    },
                    {
                    &#34;name&#34;:&#34;HR.CHANGE&#34;,
                    &#34;columns&#34;:[{&#34;name&#34;:&#34;C1&#34;,&#34;type&#34;:1},{&#34;name&#34;:&#34;C2&#34;,&#34;type&#34;:1}]
                    }
                    ]
            schema_name , table_name, column_name: str
            Column type key in main dict takes list of dict as value :
            This list of dict represents each column name and type of algorithm
            and arguments if any for that algorithm
            arguments : list of strings

            Choose appropriate algorithm type and pass necessary arguments based
            on column type

            Algorithm       Arguments mandatory       Arguments Format        Algorithm type number

            Shuffling           NA                              NA                      0
            Numeric Range       [min, max]                  [&#34;1000&#34;,&#34;2000&#34;]             2
            Numeric Variance    [variance percentage]           [&#34;50&#34;]                  3
            FPE                 NA                              NA                      1
            Fixed String        string_to_replace           [&#34;string_to_replace&#34;]       4


            Supported Algorithms :

            Column Type     Algorithms Supported

            Numeric         Shuffling, FPE, Numeric Range, Numeric Variance
            Char            Shuffling , FPE , Fixed String
            Varchar         Shuffling , FPE , Fixed String

        &#34;&#34;&#34;
        request_json = {
            &#34;opType&#34;: 2,
            &#34;policy&#34;: {
                &#34;association&#34;: {&#34;instanceId&#34;: int(self.instance_id)},
                &#34;config&#34;: {&#34;tables&#34;: table_list_of_dict},
                &#34;policy&#34;: {&#34;policyName&#34;: policy_name}
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;MASKING_POLICY&#39;], request_json
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;errorMessage&#39;]
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating Data masking policy\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    return True

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def get_masking_policy_id(self, policy_name):
        &#34;&#34;&#34;Returns policy id of given data masking policy
        Args:
            policy_name          (str)       -- data masking policy name

        Returns:
            policy_id            (int)       -- data masking policy ID

        &#34;&#34;&#34;
        instance_id = int(self.instance_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[&#39;MASKING_POLICY&#39;])
        response_json = response.json()
        policy_list = response_json[&#34;policies&#34;]
        policy_id = None
        for i in policy_list:
            pname = i[&#34;policy&#34;][&#34;policyName&#34;]
            associated_instance_id = i[&#34;association&#34;][&#34;instanceId&#34;]
            if (pname == policy_name) and (associated_instance_id == instance_id):
                policy_id = int(i[&#34;policy&#34;][&#34;policyId&#34;])
                break
            else:
                continue
        return policy_id

    def delete_data_masking_policy(self, policy_name):
        &#34;&#34;&#34;Deletes given data masking policy
        Args:
            policy_name         (str)       --  data masking policy name to be deleted

        Returns:
            bool                            -- returns true when deletion succeeds

        Raises:
            Exception

                When deletion of policy fails

                When Invalid policy name under given instance is provided
        &#34;&#34;&#34;
        source_instance_id = int(self.instance_id)
        policy_id = self.get_masking_policy_id(policy_name)
        if policy_id is None:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;106&#39;)

        request_json = {
            &#34;opType&#34;: 3,
            &#34;policy&#34;: {
                &#34;association&#34;: {&#34;instanceId&#34;: source_instance_id},
                &#34;policy&#34;: {&#34;policyId&#34;: policy_id, &#34;policyName&#34;: policy_name}
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;MASKING_POLICY&#39;], request_json
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if error_code != 0:
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while deleting Data masking policy\nError&#39;)
                else:
                    return True

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def standalone_data_masking(
            self,
            policy_name,
            destination_client=None,
            destination_instance=None):
        &#34;&#34;&#34;Launch standalone data masking job on given instance

        Args:

            policy_name          (str)       -- data masking policy name

            destination_client   (str)       -- destination client in which destination
            instance exists

            destination_instance (str)       -- destination instance to which masking
            to be applied

        Returns:
            object -- Job containing data masking job details


        Raises:
            SDKException
                if policy ID retrieved is None

        &#34;&#34;&#34;
        if destination_client is None:
            destination_client = self._properties[&#39;instance&#39;][&#39;clientName&#39;]
        if destination_instance is None:
            destination_instance = self.instance_name
        destination_client_object = self._commcell_object.clients.get(
            destination_client)
        destination_agent_object = destination_client_object.agents.get(
            &#39;oracle&#39;)
        destination_instance_object = destination_agent_object.instances.get(
            destination_instance)
        destination_instance_id = int(destination_instance_object.instance_id)
        source_instance_id = int(self.instance_id)
        policy_id = self.get_masking_policy_id(policy_name)
        if policy_id is None:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;106&#39;)
        request_json = self._restore_json(paths=r&#39;/&#39;)
        destination_instance_json = {
            &#34;clientName&#34;: destination_client,
            &#34;instanceName&#34;: destination_instance,
            &#34;instanceId&#34;: destination_instance_id
        }
        data_masking_options = {
            &#34;isStandalone&#34;: True,
            &#34;enabled&#34;: True,
            &#34;dbDMPolicy&#34;: {
                &#34;association&#34;: {
                    &#34;instanceId&#34;: source_instance_id},
                &#34;policy&#34;: {
                    &#34;policyId&#34;: policy_id,
                    &#34;policyName&#34;: policy_name}}}
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;destination&#34;][&#34;destClient&#34;][&#34;clientName&#34;] = destination_client
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;destination&#34;][&#34;destinationInstance&#34;] = destination_instance_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;dbDataMaskingOptions&#34;] = data_masking_options
        del request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;fileOption&#34;]
        return self._process_restore_response(request_json)

    def _get_oracle_restore_json(self, destination_client,
                                 instance_name, tablespaces,
                                 files, browse_option,
                                 common_options, oracle_options, destination=None):
        &#34;&#34;&#34;
        Gets the basic restore JSON from base class and modifies it for oracle

        Args:
            destination_client  (str)   --  Destination client name

            instance_name   (str)   --  instance name to restore

            tablespaces (list)  --  tablespace name list

            files   (dict)  --  fileOptions

            browse_option   (dict)  --  dict containing browse options

            common_options  (dict)  --  dict containing common options

            oracle_options  (dict)  --  dict containing other oracle options

            destination     (dict)  --  dictionary with destination client and instance names
                default&#34; None

        Returns:
            (dict) -- JSON formatted options to restore the oracle database

        Raises:
            SDKException:
                if tablespace is passed as a list
                if files is not passed as a dictionary

        &#34;&#34;&#34;
        if not isinstance(tablespaces, list):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;101&#39;, &#39;Expecting a list for tablespaces&#39;)
        if files is not None:
            if not isinstance(files, dict):
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;101&#39;, &#39;Expecting a dict for files&#39;)

        destination_id = int(self._commcell_object.clients.get(
            destination_client).client_id)
        tslist = [&#34;SID: {0} Tablespace: {1}&#34;.format(
            instance_name, ts) for ts in tablespaces]
        restore_json = self._restore_json(paths=r&#39;/&#39;)
        if common_options is not None:
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;commonOptions&#34;] = common_options
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;oracleOpt&#34;] = oracle_options
        if destination:
            if not isinstance(destination, dict):
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;101&#39;, &#39;Expecting a dict for destination details&#39;)
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;destination&#34;] = destination
        if files is None:
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;fileOption&#34;] = {
                &#34;sourceItem&#34;: tslist
            }
        else:
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#34;fileOption&#34;] = files

        if browse_option is not None:
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;browseOption&#34;] = browse_option
        return restore_json

    def _get_browse_options(self):
        &#34;&#34;&#34;Returns the database instance properties for browse and restore&#34;&#34;&#34;
        return {
            &#34;path&#34;: &#34;/&#34;,
            &#34;entity&#34;: {
                &#34;appName&#34;: self._properties[&#39;instance&#39;][&#39;appName&#39;],
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;applicationId&#34;: int(self._properties[&#39;instance&#39;][&#39;applicationId&#39;]),
                &#34;clientId&#34;: int(self._properties[&#39;instance&#39;][&#39;clientId&#39;]),
                &#34;instanceName&#34;: self._properties[&#39;instance&#39;][&#39;instanceName&#39;],
                &#34;clientName&#34;: self._properties[&#39;instance&#39;][&#39;clientName&#39;]
            }
        }

    def _process_browse_response(self, request_json):
        &#34;&#34;&#34;Runs the DBBrowse API with the request JSON provided for Browse,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                list - list containing tablespaces for the instance

            Raises:
                SDKException:
                    if browse job failed

                    if browse is empty

                    if browse is not success
        &#34;&#34;&#34;
        if &#39;tablespaces&#39; in self._instanceprop:
            return self._instanceprop[&#39;tablespaces&#39;]

        browse_service = self._commcell_object._services[&#39;ORACLE_INSTANCE_BROWSE&#39;] % (
            self.instance_id
        )

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, browse_service, request_json
        )

        if flag:
            response_data = json.loads(response.text)
            if response_data:
                if &#34;oracleContent&#34; in response_data:
                    self._instanceprop[&#39;tablespaces&#39;] = response_data[&#34;oracleContent&#34;]
                    return self._instanceprop[&#39;tablespaces&#39;]
                elif &#34;errorCode&#34; in response_data:
                    error_message = response_data[&#39;errorMessage&#39;]
                    o_str = &#39;Browse job failed\nError: &#34;{0}&#34;&#39;.format(
                        error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def oracle_home(self):
        &#34;&#34;&#34;
        getter for oracle home

        Returns:
            string - string of oracle_home

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;oracleHome&#39;]

    @property
    def is_catalog_enabled(self):
        &#34;&#34;&#34;
        Getter to check if catalog has been enabled

        Returns:
            Bool - True if catalog is enabled. Else False.

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;useCatalogConnect&#39;]

    @property
    def catalog_user(self):
        &#34;&#34;&#34;
        Getter for catalog user

        Returns:
            string  - String containing catalog user

        Raises:
            SDKException:
                if not set

                if catalog is not enabled

        &#34;&#34;&#34;
        if not self.is_catalog_enabled:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;, &#39;Catalog is not enabled.&#39;)
        try:
            return self._properties[&#39;oracleInstance&#39;][&#39;catalogConnect&#39;][&#39;userName&#39;]
        except KeyError as error_str:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                               &#39;Catalog user not set - {}&#39;.format(error_str))

    @property
    def catalog_db(self):
        &#34;&#34;&#34;
        Getter for catalog database

        Returns:
            string  - String containing catalog database

        Raises:
            SDKException:
                if not set

                if catalog is not enabled

        &#34;&#34;&#34;
        if not self.is_catalog_enabled:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;, &#39;Catalog is not enabled.&#39;)
        try:
            return self._properties[&#39;oracleInstance&#39;][&#39;catalogConnect&#39;][&#39;domainName&#39;]
        except KeyError as error_str:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                               &#39;Catalog database not set - {}&#39;.format(error_str))

    @property
    def os_user(self):
        &#34;&#34;&#34;
        Getter for oracle software owner

        Returns:
            string - string of oracle software owner

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;oracleUser&#39;][&#39;userName&#39;]

    @property
    def version(self):
        &#34;&#34;&#34;
        Getter for oracle version

        Returns:
            string - string of oracle instance version

        &#34;&#34;&#34;
        return self._properties[&#39;version&#39;]

    @property
    def archive_log_dest(self):
        &#34;&#34;&#34;
        Getter for the instance&#39;s archive log dest

        Returns:
            string - string for archivelog location

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;archiveLogDest&#39;]

    @property
    def cmd_sp(self):
        &#34;&#34;&#34;
        Getter for Command Line storage policy

        Returns:
            string - string for command line storage policy

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;oracleStorageDevice&#39;][
            &#39;commandLineStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def log_sp(self):
        &#34;&#34;&#34;
        Oracle Instance&#39;s Log Storage Poplicy

        Returns:
            string  -- string containing log storage policy

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;oracleStorageDevice&#39;][
            &#39;logBackupStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def is_autobackup_on(self):
        &#34;&#34;&#34;
        Getter to check whether autobackup is set to ON

        Returns:
            Bool - True if autobackup is set to ON. Else False.

        &#34;&#34;&#34;
        return True if self._properties[&#39;oracleInstance&#39;][&#39;ctrlFileAutoBackup&#39;] == 1 else False

    @property
    def db_user(self):
        &#34;&#34;&#34;
        Getter to get the database user used to log into the database

        Returns: Oracle database user for the instance

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;sqlConnect&#39;][&#39;userName&#39;]

    @property
    def tns_name(self):
        &#34;&#34;&#34;
        Getter to get the TNS Names of the database

        Returns:
            string  -- TNS name of the instance configured

        Raises:
            SDKException:
                if not set

        &#34;&#34;&#34;
        try:
            return self._properties[&#39;oracleInstance&#39;][&#39;sqlConnect&#39;][&#39;domainName&#39;]
        except KeyError as error_str:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                               &#39;Instance TNS Entry not set - {}&#39;.format(error_str))

    @property
    def dbid(self):
        &#34;&#34;&#34;
        Getter to get the DBID of the database instance

        Returns: DBID of the oracle database

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;DBID&#39;]

    @property
    def tablespaces(self):
        &#34;&#34;&#34;
        Getter for listing out all tablespaces for the instance

        Returns:
            list -- list containing tablespace names for the database

        &#34;&#34;&#34;
        return [ts[&#39;tableSpace&#39;] for ts in self.browse()]

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Overridden method to browse oracle database tablespaces&#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        elif kwargs:
            options = kwargs
        else:
            options = self._get_browse_options()
        return self._process_browse_response(options)

    def backup(self, subclient_name=r&#34;default&#34;):
        &#34;&#34;&#34;Uses the default subclient to backup the database

        Args:
            subclient_name (str) -- name of subclient to use
                default: default
        &#34;&#34;&#34;
        return self.subclients.get(subclient_name).backup(r&#39;full&#39;)

    def restore(
            self,
            files=None,
            destination_client=None,
            common_options=None,
            browse_option=None,
            oracle_options=None,
            tag=None,
            destination_instance=None,
            streams=2):
        &#34;&#34;&#34;
        Perform restore full/partial database using latest backup or backup copy

        Args:
            files               (dict)      --  fileOption for restore

            destination_client  (str)       --  destination client name

            common_options      (dict)      --  dictionary containing common options
                default:    None

            browse_option       (dict)      --  dictionary containing browse options

            oracle_options      (dict)      --  dictionary containing other oracle options
                default:    By default it restores the controlfile and datafiles from latest backup

                Example:{
                            &#34;resetLogs&#34;: 1,
                            &#34;switchDatabaseMode&#34;: True,
                            &#34;noCatalog&#34;: True,
                            &#34;restoreControlFile&#34;: True,
                            &#34;recover&#34;: True,
                            &#34;recoverFrom&#34;: 3,
                            &#34;restoreData&#34;: True,
                            &#34;restoreFrom&#34;: 3
                        }

            tag                 (str)       --  Type of the restore to be performed
                default:    None

            destination_instance(str)       --  destination instance name
                default:    None (in place restore)

            streams             (int)       --  number of streams for restore
                default: 2


        Returns:
            object  --  Job containing restore details

        Raises:
            SDKException:
                If oracle options can&#39;t be set

                If destination_client can&#39;t be set

        &#34;&#34;&#34;
        options = {
            &#34;resetLogs&#34;: 1,
            &#34;switchDatabaseMode&#34;: True,
            &#34;noCatalog&#34;: True,
            &#34;recover&#34;: True,
            &#34;recoverFrom&#34;: 3,
            &#34;restoreData&#34;: True,
            &#34;restoreFrom&#34;: 3
        }
        if oracle_options is None:
            oracle_options = {}
        options.update(oracle_options)
        oracle_options = options.copy()

        if tag and tag.lower() == &#39;snap&#39;:
            opt = {
                &#34;useSnapRestore&#34;: True,
                &#34;cleanupAuxiliary&#34;: True,
                &#34;restoreControlFile&#34;: True,
            }
            oracle_options.update(opt)

        try:
            if destination_client is None or destination_instance is None:
                destination_client = self._properties[&#39;instance&#39;][&#39;clientName&#39;]
                destination_instance = self._properties[&#39;instance&#39;][&#39;instanceName&#39;]
            destination = {
                &#34;destination_client&#34;: destination_client,
                &#34;destination_instance&#34;: destination_instance
            }
            if tag and tag.lower()==&#34;rac&#34;:
                stream_allocation = self._get_rac_stream_allocation(
                    destination_client, destination_instance, streams)
                oracle_options.update(stream_allocation)
                destination[&#34;app_name&#34;] = &#34;Oracle RAC&#34;
            self._restore_destination_json(destination)
        except SDKException:
            raise SDKException(&#34;Instance&#34;, &#34;105&#34;)
        else:
            # subclient = self.subclients.get(subclient_name)
            if destination_client and destination_instance:
                options = self._get_oracle_restore_json(destination_client=destination_client,
                                                        destination=self._destination_restore_json,
                                                        instance_name=self.instance_name,
                                                        tablespaces=self.tablespaces,
                                                        files=files,
                                                        browse_option=browse_option,
                                                        common_options=common_options,
                                                        oracle_options=oracle_options)
            else:
                options = self._get_oracle_restore_json(destination_client=destination_client,
                                                        instance_name=self.instance_name,
                                                        tablespaces=self.tablespaces,
                                                        files=files,
                                                        browse_option=browse_option,
                                                        common_options=common_options,
                                                        oracle_options=oracle_options)
            return self._process_restore_response(options)
            
    def _get_rac_stream_allocation(self, destination_client, destination_instance, streams):
        &#34;&#34;&#34;setter for RAC stream allocation on nodes data population in oracle options for restore
            Args:
                destination_client  (str) -- Name of the destination client
                destination_instance(str) -- Name of the destination RAC instance
                streams             (int)    -- Number of streams for restore
        &#34;&#34;&#34;
        destination_client_obj = self._commcell_object.clients.get(destination_client)
        destination_instance_obj = destination_client_obj.agents.get(&#34;Oracle RAC&#34;).instances.get(destination_instance)
        rac_stream_allocation = {&#34;racDataStreamAllcation&#34;: []}
        for node in destination_instance_obj.properties[&#39;oracleRACInstance&#39;][&#39;racDBInstance&#39;]:
            rac_stream_allocation[&#34;racDataStreamAllcation&#34;].append(f&#34;{node[&#39;racDbInstanceId&#39;]} {streams}&#34;)
        return rac_stream_allocation

    def _restore_db_dump_option_json(self,value):
        &#34;&#34;&#34;setter for the oracle dbdump Restore option in restore JSON
            Args:
                value   (dict)  --  Dictionary of options need to be set for restore
        &#34;&#34;&#34;
        if not isinstance(value,dict):
            raise SDKException(&#39;Instance&#39;,&#39;101&#39;)
            
        self._db_dump_restore_json = {
            &#34;importToDatabase&#34;: True,
            &#34;parallelism&#34;: 2,
            &#34;restorePath&#34;: value.get(&#34;destination_path&#34;, &#34;&#34;),
            &#34;overwriteTable&#34;: False,
            &#34;enabled&#34;: True,
            &#34;connectDetails&#34;: {
                &#34;password&#34;: b64encode(value.get(&#34;db_password&#34;, &#34;&#34;).encode()).decode(),
                &#34;domainName&#34;: (self._properties.get(&#34;oracleInstance&#34;, {}).
                    get(&#34;sqlConnect&#34;, {}).get(&#34;domainName&#34;, &#34;&#34;)),
                &#34;userName&#34;: (self._properties.get(&#34;oracleInstance&#34;, {}).
                    get(&#34;sqlConnect&#34;, {}).get(&#34;userName&#34;, &#34;&#34;))
            }
        }


    def _restore_oracle_option_json(self, value):
        &#34;&#34;&#34;setter for the oracle Restore option in restore JSON
        Args:
                value   (dict)  --  Dictionary of options need to be set for restore
        &#34;&#34;&#34;
        if not isinstance(value,dict):
            raise SDKException(&#39;Instance&#39;,&#39;101&#39;)

        self._oracle_restore_json = {
            &#34;validate&#34;: False,
            &#34;noCatalog&#34;: False,
            &#34;duplicateToName&#34;: &#34;&#34;,
            &#34;cloneEnv&#34;: False,
            &#34;restoreControlFile&#34;: False,
            &#34;duplicate&#34;: False,
            &#34;tableViewRestore&#34;: False,
            &#34;osID&#34;: 2,
            &#34;partialRestore&#34;: False,
            &#34;restoreStream&#34;: 2,
            &#34;restoreSPFile&#34;: False,
            &#34;recover&#34;: True,
            &#34;recoverFrom&#34;: 4,
            &#34;archiveLog&#34;: False,
            &#34;restoreData&#34;: True,
            &#34;restoreFrom&#34;: 0,
            &#34;timeZone&#34;: {
                &#34;TimeZoneName&#34;: &#34;(UTC) Coordinated Universal Time&#34;
            },
            &#34;recoverTime&#34;: {},
            &#34;sourcePaths&#34;: [
                &#34;//**&#34;
            ],
            &#34;restoreTime&#34;: {}
        }

        if value.get(&#34;restore_oracle_options_type&#34;) == &#34;restore_archivelogs_norecover&#34;:
            self._oracle_restore_json = {
                &#34;resetLogs&#34;: 0,
                &#34;backupValidationOnly&#34;: False,
                &#34;threadId&#34;: 1,
                &#34;deviceType&#34;: 0,
                &#34;restoreFailover&#34;: True,
                &#34;resetDatabase&#34;: False,
                &#34;noCatalog&#34;: True,
                &#34;ctrlRestoreFrom&#34;: False,
                &#34;controlFilePath&#34;: &#34;&#34;,
                &#34;specifyControlFileTime&#34;: False,
                &#34;restoreDataTag&#34;: False,
                &#34;useEndLSN&#34;: False,
                &#34;useStartLSN&#34;: False,
                &#34;restoreTablespace&#34;: False,
                &#34;archiveLogBy&#34;: 1,
                &#34;ctrlFileBackupType&#34;: 0,
                &#34;restoreControlFile&#34;: False,
                &#34;restoreInstanceLog&#34;: False,
                &#34;duplicate&#34;: False,
                &#34;startLSNNum&#34;: &#34;&#34;,
                &#34;checkReadOnly&#34;: False,
                &#34;osID&#34;: 2,
                &#34;specifyControlFile&#34;: False,
                &#34;setDBId&#34;: False,
                &#34;partialRestore&#34;: False,
                &#34;restoreStream&#34;: 2,
                &#34;specifySPFile&#34;: False,
                &#34;restoreSPFile&#34;: False,
                &#34;recover&#34;: False,
                &#34;recoverFrom&#34;: 4,
                &#34;archiveLog&#34;: True,
                &#34;endLSNNum&#34;: &#34;&#34;,
                &#34;autoDetectDevice&#34;: True,
                &#34;useEndLog&#34;: False,
                &#34;isDeviceTypeSelected&#34;: False,
                &#34;useStartLog&#34;: True,
                &#34;logTarget&#34;: &#34;&#34;,
                &#34;restoreData&#34;: False,
                &#34;restoreFrom&#34;: 0,
                &#34;duplicateToSkipReadOnly&#34;: False
            }
            if value.get(&#34;start_lsn&#34;, None):
                self._oracle_restore_json[&#34;useStartLSN&#34;] = True
                self._oracle_restore_json[&#34;startLSNNum&#34;] = value.get(&#34;start_lsn&#34;)
            if value.get(&#34;end_lsn&#34;, None):
                self._oracle_restore_json[&#34;useEndLSN&#34;] = True
                self._oracle_restore_json[&#34;endLSNNum&#34;] = value.get(&#34;end_lsn&#34;)
            if value.get(&#34;log_dest&#34;, None):
                self._oracle_restore_json[&#34;logTarget&#34;] = value.get(&#34;log_dest&#34;)

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (dict)  --  Dictionary of options need to be set for restore

            Returns:
                dict             -- JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(OracleInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        self._restore_db_dump_option_json(restore_option)
        self._restore_oracle_option_json(restore_option)
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;dbDumpOptions&#34;] = self._db_dump_restore_json
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;oracleOpt&#34;] = self._oracle_restore_json

        return rest_json

    def restore_in_place(
            self,
            db_password,
            path,
            dest_client_name,
            dest_instance_name,
            dest_path=None,
            restore_oracle_options_type=None,
            start_lsn=None, end_lsn=None,
            log_dest=None):
        &#34;&#34;&#34;Restores the oracle logical dump data/log files specified in the input paths
        list to the same location.

            Args:
                
                db_password             (str)  -- password for oracle database
                
                path                    (list)  --  list of database/databases to be restored

                dest_client_name        (str)   --  destination client name where files are to be
                restored

                dest_instance_name      (str)   --  destination postgres instance name of
                destination client

                dest_path        (str)   --  destinath path for restore

                    default: None

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(path, list) and
                isinstance(db_password, str)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        if not path:
            raise SDKException(&#39;Instance&#39;,&#39;103&#39;)
        

        request_json = self._restore_json(
            db_password=db_password,
            paths=path,
            destination_client=dest_client_name,
            destination_instance=dest_instance_name,
            destination_path=dest_path,
            restore_oracle_options_type=restore_oracle_options_type,
            start_lsn=start_lsn, end_lsn=end_lsn,
            log_dest=log_dest)

        return self._process_restore_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance"><code class="flex name class">
<span>class <span class="ident">OracleInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent a standalone Oracle Instance</p>
<p>Constructor for the class</p>
<h2 id="args">Args</h2>
<p>agent_object
&ndash; instance of the Agent class
instance_name
&ndash; name of the instance
instance_id
&ndash;
id of the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L114-L1344" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OracleInstance(DatabaseInstance):
    &#34;&#34;&#34;
    Class to represent a standalone Oracle Instance
    &#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            agent_object    -- instance of the Agent class
            instance_name   -- name of the instance
            instance_id     --  id of the instance

        &#34;&#34;&#34;
        super(OracleInstance, self).__init__(
            agent_object, instance_name, instance_id)
        self._LIVE_SYNC = self._commcell_object._services[&#39;LIVE_SYNC&#39;]
        self._dbDump_restore_json = None
        self._oracle_restore_json = None

    def restore_to_disk(self,
                        destination_client,
                        destination_path,
                        backup_job_ids,
                        user_name,
                        password):
        &#34;&#34;&#34;
        Perform restore to disk [Application free restore] for Oracle

            Args:
                destination_client          (str)   --  destination client name

                destination_path:           (str)   --  destination path

                backup_job_ids              (list)  --  list of backup job IDs
                                                        to be used for disk restore

                user_name                   (str)   --  impersonation user name to
                                                        restore to destination client

                password                    (str)   --  impersonation user password

            Returns:
                object -     Job containing restore details

            Raises:
                SDKException
                    if backup_job_ids not given as list of items

        &#34;&#34;&#34;
        if not isinstance(backup_job_ids, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        request_json = self._get_restore_to_disk_json(destination_client,
                                                      destination_path,
                                                      backup_job_ids,
                                                      user_name,
                                                      password)
        return self._process_restore_response(request_json)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(OracleInstance, self)._get_instance_properties()
        self._instanceprop = self._properties[&#39;oracleInstance&#39;]

    def _get_instance_properties_json(self):
        &#34;&#34;&#34; Gets all the instance related properties of Informix instance.

           Returns:
                dict - all instance properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;:
                {
                    &#34;instance&#34;: self._instance,
                    &#34;oracleInstance&#34;: self._instanceprop
                }
        }
        return instance_json

    @property
    def log_stream(self):
        &#34;&#34;&#34;
        Getter to fetch log stream count at instance level

            Returns:
                    int     --  log stream count atinstance level

        &#34;&#34;&#34;
        return self._instanceprop.get(&#34;numberOfArchiveLogBackupStreams&#34;)

    @log_stream.setter
    def log_stream(self, log_stream=1):
        &#34;&#34;&#34;
        Setter to set log stream count at instance level

            Args:
                log_stream    (int)    --  log stream count at instance level
                                           default = 1
        &#34;&#34;&#34;
        self._set_instance_properties(
            &#34;_instanceprop[&#39;numberOfArchiveLogBackupStreams&#39;]&#34;, log_stream)

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;
        Setter for the Common options in restore JSON

            Args:
                value   (dict)  --  dict of common options
                                    for restore json

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        super()._restore_common_options_json(value)
        if value.get(&#34;baseline_jobid&#34;):
            self._commonoption_restore_json = (
                {
                    &#34;clusterDBBackedup&#34;: value.get(&#34;clusterDBBackedup&#34;, False),
                    &#34;restoreToDisk&#34;: value.get(&#34;restoreToDisk&#34;, False),
                    &#34;baselineBackup&#34;: 1,
                    &#34;baselineRefTime&#34;: value.get(&#34;baseline_ref_time&#34;, &#34;&#34;),
                    &#34;isDBArchiveRestore&#34;: value.get(&#34;isDBArchiveRestore&#34;, False),
                    &#34;baselineJobId&#34;: value.get(&#34;baseline_jobid&#34;, &#34;&#34;),
                    &#34;copyToObjectStore&#34;: value.get(&#34;copyToObjectStore&#34;, False),
                    &#34;onePassRestore&#34;: value.get(&#34;onePassRestore&#34;, False),
                    &#34;syncRestore&#34;: value.get(&#34;syncRestore&#34;, True)
                })

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;
        Setter for the Oracle destination options in restore JSON

            Args:
                    value   (dict)  --  dict of values for destination option

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._destination_restore_json = (
            {
                &#34;noOfStreams&#34;: value.get(&#34;number_of_streams&#34;, 2),
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;)
                              },
                &#34;destinationInstance&#34;: {
                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;),
                    &#34;instanceName&#34;: value.get(&#34;destination_instance&#34;, &#34;&#34;),
                    &#34;appName&#34;: value.get(&#34;app_name&#34;, &#34;Oracle&#34;)
                                       }

            })

    def _get_live_sync_oracleopt_json(self, **kwargs):
        &#34;&#34;&#34;
               Constructs JSON with oracle agent specific options
               for configuring live sync

               Args:
                    **kwargs    (dict)  -- dict of keyword arguments as follows:

                                           redirect_path   (str)- Path on destination client
                                                        to redirect tablespaces and datafiles

        &#34;&#34;&#34;

        self._oracle_options = {&#34;renamePathForAllTablespaces&#34;:&#34;&#34;,
                                &#34;redirectAllItemsSelected&#34;: False,
                                &#34;validate&#34;: False,
                                &#34;ctrlRestoreFrom&#34;: True,
                                &#34;noCatalog&#34;: True,
                                &#34;cloneEnv&#34;:False,
                                &#34;ctrlFileBackupType&#34;: 0,
                                &#34;restoreControlFile&#34;: True,
                                &#34;duplicate&#34;: False,
                                &#34;tableViewRestore&#34;: False,
                                &#34;osID&#34;:2,
                                &#34;partialRestore&#34;: False,
                                &#34;restoreStream&#34;:2,
                                &#34;restoreSPFile&#34;: False,
                                &#34;recover&#34;: True,
                                &#34;oraExtendedRstOptions&#34;: 0,
                                &#34;recoverFrom&#34;: 3,
                                &#34;archiveLog&#34;: False,
                                &#34;restoreData&#34;: True,
                                &#34;restoreFrom&#34;: 3,
                                &#34;crossmachineRestoreOptions&#34;: {
                                &#34;onlineLogDest&#34;: &#34;&#34;
                                    },
                                &#34;liveSyncOpt&#34;:{
                                    &#34;restoreInStandby&#34;:False
                                }
                                }
        if kwargs.get(&#39;redirect_path&#39;, None) is not None:
            self._oracle_options.update({&#34;renamePathForAllTablespaces&#34;: kwargs.get(&#39;redirect_path&#39;),
                                         &#34;redirectAllItemsSelected&#34;: True,
                                         &#34;redirectItemsPresent&#34;: True
                                         })

    def _live_sync_restore_json(self, dest_client, dest_instance, baseline_jobid, baseline_ref_time,
                                schedule_name, source_backupset_id, **kwargs):
        &#34;&#34;&#34;
               Constructs oracle live sync restore JSON by combining common
               and agent specific options

                   Args:
                       dest_client  (str)   --  The destination client name for live sync

                       dest_instance    (str)   --  The destination instance name for live sync

                       baseline_jobid   (int)   --  The jobid of the baseline backup job

                       baseline_ref_time    (int)   --  The reference time/start time
                                                        of the baseline backup

                       schedule_name    (str)   --  The name of the live sync schedule to be created

                       source_backupset_id  (int)   --  The ID of the source backupset
                                                        of source oracle instance for which
                                                        live sync needs to be configured

                       **kwargs    (dict)  -- dict of keyword arguments as follows:

                                           redirect_path   (str)- Path on destination client
                                                        to redirect tablespaces and datafiles

                    Returns:
                        (str)  --   The live sync restore JSON that is constructed
                                    using oracle and common options

        &#34;&#34;&#34;

        restore_json = super()._restore_json(destination_client=dest_client,
                                             destination_instance=dest_instance,
                                             baseline_jobid=baseline_jobid,
                                             baseline_ref_time=baseline_ref_time,
                                             syncRestore=True,
                                             no_of_streams=2,
                                            )
        restore_option = {}
        if restore_json.get(&#34;restore_option&#34;):
            restore_option = restore_json[&#34;restore_option&#34;]
            for key in restore_json:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = restore_json[key]
        else:
            restore_option.update(restore_json)

        self._get_live_sync_oracleopt_json(**kwargs)
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientId&#39;] = -1
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetId&#39;] = source_backupset_id
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = &#34;&#34;
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetName&#39;] = &#34;&#34;
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;_type_&#39;] = 5
        restore_json[&#39;taskInfo&#39;][&#39;task&#39;][&#39;taskType&#39;] = 2
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;operationType&#39;] = 1007
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;subTaskName&#39;] = schedule_name
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;pattern&#39;] = {
            &#34;freq_type&#34;: 4096
        }
        destinationInstance = {
            &#34;clientName&#34;:dest_client,
            &#34;instanceName&#34;:dest_instance,
            &#34;appName&#34;:&#34;Oracle&#34;
        }
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;destination&#34;].update({&#34;destinationInstance&#34;:destinationInstance})
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;oracleOpt&#34;] = self._oracle_options
        return restore_json

    def create_live_sync_schedule(self, dest_client, dest_instance, schedule_name,
                                  **kwargs):
        &#34;&#34;&#34;
               Runs full backup on source oracle instance and
               Creates live sync schdule for the given destination oracle instance

                   Args:
                       dest_client  (str)   --  The destination client name for live sync

                       dest_instance    (str)   --  The destination instance name for live sync

                       schedule_name    (str)   --  The name of the live sync schedule to be created

                       **kwargs    (dict)  -- dict of keyword arguments as follows:

                                            redirect_path   (str) --  Path on destination client
                                                            to redirect tablespaces and datafiles

                   Returns:
                        (object)  --   The job object of the baseline backup that will be replicated

        &#34;&#34;&#34;
        source_backupset_id = int(self.backupsets.get(&#39;default&#39;).backupset_id)
        subclient_obj = self.subclients.get(&#39;default&#39;)
        baseline_job_object = subclient_obj.backup(backup_level=&#39;full&#39;)
        if not baseline_job_object.wait_for_completion():
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, baseline_job_object.delay_reason)
        baseline_ref_time = baseline_job_object.summary[&#39;jobStartTime&#39;]
        baseline_jobid = int(baseline_job_object.job_id)
        request_json = self._live_sync_restore_json(dest_client, dest_instance, baseline_jobid,
                                                    baseline_ref_time, schedule_name,
                                                    source_backupset_id, **kwargs)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._LIVE_SYNC, request_json)
        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    return baseline_job_object
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    error_message = &#39;Live Sync configuration failed\nError: &#34;{0}&#34;&#39;.format(
                        error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to create schedule&#39;)
            else:
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;, self._update_response_(response.text))

    def configure_data_masking_policy(self, policy_name, table_list_of_dict):
        &#34;&#34;&#34;Configures data masking policy with given parameters
        Args:
            policy_name         (str)   --  string representing policy name
            table_list_of_dict  list(dict)  -- list containing one dict item representing
                                                rules for single table
            Sample  list
            Tables:
                    [
                    {
                    &#34;name&#34;:&#34;schema_name.table_name&#34;,
                    &#34;columns&#34;: [ {&#34;name&#34;:&#34;column_name&#34;, &#34;type&#34;:&#34;algorithm_type&#34;},
                                &#34;arguments&#34;:[list of strings]…]
                    }
                    ]
                    Sample :
                    [
                    {
                    &#34;name&#34;:&#34;HR.NUMNEW&#34;,
                    &#34;columns&#34;:[{&#34;name&#34;:&#34;N1&#34;,&#34;type&#34;:0},{&#34;name&#34;:&#34;N2&#34;,&#34;type&#34;:2,
                    &#34;arguments&#34;:[&#34;1000&#34;,&#34;2000&#34;]}]
                    },
                    {
                    &#34;name&#34;:&#34;HR.CHANGE&#34;,
                    &#34;columns&#34;:[{&#34;name&#34;:&#34;C1&#34;,&#34;type&#34;:1},{&#34;name&#34;:&#34;C2&#34;,&#34;type&#34;:1}]
                    }
                    ]
            schema_name , table_name, column_name: str
            Column type key in main dict takes list of dict as value :
            This list of dict represents each column name and type of algorithm
            and arguments if any for that algorithm
            arguments : list of strings

            Choose appropriate algorithm type and pass necessary arguments based
            on column type

            Algorithm       Arguments mandatory       Arguments Format        Algorithm type number

            Shuffling           NA                              NA                      0
            Numeric Range       [min, max]                  [&#34;1000&#34;,&#34;2000&#34;]             2
            Numeric Variance    [variance percentage]           [&#34;50&#34;]                  3
            FPE                 NA                              NA                      1
            Fixed String        string_to_replace           [&#34;string_to_replace&#34;]       4


            Supported Algorithms :

            Column Type     Algorithms Supported

            Numeric         Shuffling, FPE, Numeric Range, Numeric Variance
            Char            Shuffling , FPE , Fixed String
            Varchar         Shuffling , FPE , Fixed String

        &#34;&#34;&#34;
        request_json = {
            &#34;opType&#34;: 2,
            &#34;policy&#34;: {
                &#34;association&#34;: {&#34;instanceId&#34;: int(self.instance_id)},
                &#34;config&#34;: {&#34;tables&#34;: table_list_of_dict},
                &#34;policy&#34;: {&#34;policyName&#34;: policy_name}
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;MASKING_POLICY&#39;], request_json
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;errorMessage&#39;]
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while creating Data masking policy\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                    )
                else:
                    return True

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def get_masking_policy_id(self, policy_name):
        &#34;&#34;&#34;Returns policy id of given data masking policy
        Args:
            policy_name          (str)       -- data masking policy name

        Returns:
            policy_id            (int)       -- data masking policy ID

        &#34;&#34;&#34;
        instance_id = int(self.instance_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[&#39;MASKING_POLICY&#39;])
        response_json = response.json()
        policy_list = response_json[&#34;policies&#34;]
        policy_id = None
        for i in policy_list:
            pname = i[&#34;policy&#34;][&#34;policyName&#34;]
            associated_instance_id = i[&#34;association&#34;][&#34;instanceId&#34;]
            if (pname == policy_name) and (associated_instance_id == instance_id):
                policy_id = int(i[&#34;policy&#34;][&#34;policyId&#34;])
                break
            else:
                continue
        return policy_id

    def delete_data_masking_policy(self, policy_name):
        &#34;&#34;&#34;Deletes given data masking policy
        Args:
            policy_name         (str)       --  data masking policy name to be deleted

        Returns:
            bool                            -- returns true when deletion succeeds

        Raises:
            Exception

                When deletion of policy fails

                When Invalid policy name under given instance is provided
        &#34;&#34;&#34;
        source_instance_id = int(self.instance_id)
        policy_id = self.get_masking_policy_id(policy_name)
        if policy_id is None:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;106&#39;)

        request_json = {
            &#34;opType&#34;: 3,
            &#34;policy&#34;: {
                &#34;association&#34;: {&#34;instanceId&#34;: source_instance_id},
                &#34;policy&#34;: {&#34;policyId&#34;: policy_id, &#34;policyName&#34;: policy_name}
            }
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;MASKING_POLICY&#39;], request_json
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if error_code != 0:
                    raise SDKException(
                        &#39;Instance&#39;,
                        &#39;102&#39;,
                        &#39;Error while deleting Data masking policy\nError&#39;)
                else:
                    return True

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def standalone_data_masking(
            self,
            policy_name,
            destination_client=None,
            destination_instance=None):
        &#34;&#34;&#34;Launch standalone data masking job on given instance

        Args:

            policy_name          (str)       -- data masking policy name

            destination_client   (str)       -- destination client in which destination
            instance exists

            destination_instance (str)       -- destination instance to which masking
            to be applied

        Returns:
            object -- Job containing data masking job details


        Raises:
            SDKException
                if policy ID retrieved is None

        &#34;&#34;&#34;
        if destination_client is None:
            destination_client = self._properties[&#39;instance&#39;][&#39;clientName&#39;]
        if destination_instance is None:
            destination_instance = self.instance_name
        destination_client_object = self._commcell_object.clients.get(
            destination_client)
        destination_agent_object = destination_client_object.agents.get(
            &#39;oracle&#39;)
        destination_instance_object = destination_agent_object.instances.get(
            destination_instance)
        destination_instance_id = int(destination_instance_object.instance_id)
        source_instance_id = int(self.instance_id)
        policy_id = self.get_masking_policy_id(policy_name)
        if policy_id is None:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;106&#39;)
        request_json = self._restore_json(paths=r&#39;/&#39;)
        destination_instance_json = {
            &#34;clientName&#34;: destination_client,
            &#34;instanceName&#34;: destination_instance,
            &#34;instanceId&#34;: destination_instance_id
        }
        data_masking_options = {
            &#34;isStandalone&#34;: True,
            &#34;enabled&#34;: True,
            &#34;dbDMPolicy&#34;: {
                &#34;association&#34;: {
                    &#34;instanceId&#34;: source_instance_id},
                &#34;policy&#34;: {
                    &#34;policyId&#34;: policy_id,
                    &#34;policyName&#34;: policy_name}}}
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;destination&#34;][&#34;destClient&#34;][&#34;clientName&#34;] = destination_client
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;destination&#34;][&#34;destinationInstance&#34;] = destination_instance_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;dbDataMaskingOptions&#34;] = data_masking_options
        del request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;fileOption&#34;]
        return self._process_restore_response(request_json)

    def _get_oracle_restore_json(self, destination_client,
                                 instance_name, tablespaces,
                                 files, browse_option,
                                 common_options, oracle_options, destination=None):
        &#34;&#34;&#34;
        Gets the basic restore JSON from base class and modifies it for oracle

        Args:
            destination_client  (str)   --  Destination client name

            instance_name   (str)   --  instance name to restore

            tablespaces (list)  --  tablespace name list

            files   (dict)  --  fileOptions

            browse_option   (dict)  --  dict containing browse options

            common_options  (dict)  --  dict containing common options

            oracle_options  (dict)  --  dict containing other oracle options

            destination     (dict)  --  dictionary with destination client and instance names
                default&#34; None

        Returns:
            (dict) -- JSON formatted options to restore the oracle database

        Raises:
            SDKException:
                if tablespace is passed as a list
                if files is not passed as a dictionary

        &#34;&#34;&#34;
        if not isinstance(tablespaces, list):
            raise SDKException(
                &#39;Instance&#39;,
                &#39;101&#39;, &#39;Expecting a list for tablespaces&#39;)
        if files is not None:
            if not isinstance(files, dict):
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;101&#39;, &#39;Expecting a dict for files&#39;)

        destination_id = int(self._commcell_object.clients.get(
            destination_client).client_id)
        tslist = [&#34;SID: {0} Tablespace: {1}&#34;.format(
            instance_name, ts) for ts in tablespaces]
        restore_json = self._restore_json(paths=r&#39;/&#39;)
        if common_options is not None:
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;commonOptions&#34;] = common_options
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;oracleOpt&#34;] = oracle_options
        if destination:
            if not isinstance(destination, dict):
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;101&#39;, &#39;Expecting a dict for destination details&#39;)
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;destination&#34;] = destination
        if files is None:
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;fileOption&#34;] = {
                &#34;sourceItem&#34;: tslist
            }
        else:
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#34;fileOption&#34;] = files

        if browse_option is not None:
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;browseOption&#34;] = browse_option
        return restore_json

    def _get_browse_options(self):
        &#34;&#34;&#34;Returns the database instance properties for browse and restore&#34;&#34;&#34;
        return {
            &#34;path&#34;: &#34;/&#34;,
            &#34;entity&#34;: {
                &#34;appName&#34;: self._properties[&#39;instance&#39;][&#39;appName&#39;],
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;applicationId&#34;: int(self._properties[&#39;instance&#39;][&#39;applicationId&#39;]),
                &#34;clientId&#34;: int(self._properties[&#39;instance&#39;][&#39;clientId&#39;]),
                &#34;instanceName&#34;: self._properties[&#39;instance&#39;][&#39;instanceName&#39;],
                &#34;clientName&#34;: self._properties[&#39;instance&#39;][&#39;clientName&#39;]
            }
        }

    def _process_browse_response(self, request_json):
        &#34;&#34;&#34;Runs the DBBrowse API with the request JSON provided for Browse,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                list - list containing tablespaces for the instance

            Raises:
                SDKException:
                    if browse job failed

                    if browse is empty

                    if browse is not success
        &#34;&#34;&#34;
        if &#39;tablespaces&#39; in self._instanceprop:
            return self._instanceprop[&#39;tablespaces&#39;]

        browse_service = self._commcell_object._services[&#39;ORACLE_INSTANCE_BROWSE&#39;] % (
            self.instance_id
        )

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, browse_service, request_json
        )

        if flag:
            response_data = json.loads(response.text)
            if response_data:
                if &#34;oracleContent&#34; in response_data:
                    self._instanceprop[&#39;tablespaces&#39;] = response_data[&#34;oracleContent&#34;]
                    return self._instanceprop[&#39;tablespaces&#39;]
                elif &#34;errorCode&#34; in response_data:
                    error_message = response_data[&#39;errorMessage&#39;]
                    o_str = &#39;Browse job failed\nError: &#34;{0}&#34;&#39;.format(
                        error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def oracle_home(self):
        &#34;&#34;&#34;
        getter for oracle home

        Returns:
            string - string of oracle_home

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;oracleHome&#39;]

    @property
    def is_catalog_enabled(self):
        &#34;&#34;&#34;
        Getter to check if catalog has been enabled

        Returns:
            Bool - True if catalog is enabled. Else False.

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;useCatalogConnect&#39;]

    @property
    def catalog_user(self):
        &#34;&#34;&#34;
        Getter for catalog user

        Returns:
            string  - String containing catalog user

        Raises:
            SDKException:
                if not set

                if catalog is not enabled

        &#34;&#34;&#34;
        if not self.is_catalog_enabled:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;, &#39;Catalog is not enabled.&#39;)
        try:
            return self._properties[&#39;oracleInstance&#39;][&#39;catalogConnect&#39;][&#39;userName&#39;]
        except KeyError as error_str:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                               &#39;Catalog user not set - {}&#39;.format(error_str))

    @property
    def catalog_db(self):
        &#34;&#34;&#34;
        Getter for catalog database

        Returns:
            string  - String containing catalog database

        Raises:
            SDKException:
                if not set

                if catalog is not enabled

        &#34;&#34;&#34;
        if not self.is_catalog_enabled:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;, &#39;Catalog is not enabled.&#39;)
        try:
            return self._properties[&#39;oracleInstance&#39;][&#39;catalogConnect&#39;][&#39;domainName&#39;]
        except KeyError as error_str:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                               &#39;Catalog database not set - {}&#39;.format(error_str))

    @property
    def os_user(self):
        &#34;&#34;&#34;
        Getter for oracle software owner

        Returns:
            string - string of oracle software owner

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;oracleUser&#39;][&#39;userName&#39;]

    @property
    def version(self):
        &#34;&#34;&#34;
        Getter for oracle version

        Returns:
            string - string of oracle instance version

        &#34;&#34;&#34;
        return self._properties[&#39;version&#39;]

    @property
    def archive_log_dest(self):
        &#34;&#34;&#34;
        Getter for the instance&#39;s archive log dest

        Returns:
            string - string for archivelog location

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;archiveLogDest&#39;]

    @property
    def cmd_sp(self):
        &#34;&#34;&#34;
        Getter for Command Line storage policy

        Returns:
            string - string for command line storage policy

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;oracleStorageDevice&#39;][
            &#39;commandLineStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def log_sp(self):
        &#34;&#34;&#34;
        Oracle Instance&#39;s Log Storage Poplicy

        Returns:
            string  -- string containing log storage policy

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;oracleStorageDevice&#39;][
            &#39;logBackupStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def is_autobackup_on(self):
        &#34;&#34;&#34;
        Getter to check whether autobackup is set to ON

        Returns:
            Bool - True if autobackup is set to ON. Else False.

        &#34;&#34;&#34;
        return True if self._properties[&#39;oracleInstance&#39;][&#39;ctrlFileAutoBackup&#39;] == 1 else False

    @property
    def db_user(self):
        &#34;&#34;&#34;
        Getter to get the database user used to log into the database

        Returns: Oracle database user for the instance

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;sqlConnect&#39;][&#39;userName&#39;]

    @property
    def tns_name(self):
        &#34;&#34;&#34;
        Getter to get the TNS Names of the database

        Returns:
            string  -- TNS name of the instance configured

        Raises:
            SDKException:
                if not set

        &#34;&#34;&#34;
        try:
            return self._properties[&#39;oracleInstance&#39;][&#39;sqlConnect&#39;][&#39;domainName&#39;]
        except KeyError as error_str:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                               &#39;Instance TNS Entry not set - {}&#39;.format(error_str))

    @property
    def dbid(self):
        &#34;&#34;&#34;
        Getter to get the DBID of the database instance

        Returns: DBID of the oracle database

        &#34;&#34;&#34;
        return self._properties[&#39;oracleInstance&#39;][&#39;DBID&#39;]

    @property
    def tablespaces(self):
        &#34;&#34;&#34;
        Getter for listing out all tablespaces for the instance

        Returns:
            list -- list containing tablespace names for the database

        &#34;&#34;&#34;
        return [ts[&#39;tableSpace&#39;] for ts in self.browse()]

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Overridden method to browse oracle database tablespaces&#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        elif kwargs:
            options = kwargs
        else:
            options = self._get_browse_options()
        return self._process_browse_response(options)

    def backup(self, subclient_name=r&#34;default&#34;):
        &#34;&#34;&#34;Uses the default subclient to backup the database

        Args:
            subclient_name (str) -- name of subclient to use
                default: default
        &#34;&#34;&#34;
        return self.subclients.get(subclient_name).backup(r&#39;full&#39;)

    def restore(
            self,
            files=None,
            destination_client=None,
            common_options=None,
            browse_option=None,
            oracle_options=None,
            tag=None,
            destination_instance=None,
            streams=2):
        &#34;&#34;&#34;
        Perform restore full/partial database using latest backup or backup copy

        Args:
            files               (dict)      --  fileOption for restore

            destination_client  (str)       --  destination client name

            common_options      (dict)      --  dictionary containing common options
                default:    None

            browse_option       (dict)      --  dictionary containing browse options

            oracle_options      (dict)      --  dictionary containing other oracle options
                default:    By default it restores the controlfile and datafiles from latest backup

                Example:{
                            &#34;resetLogs&#34;: 1,
                            &#34;switchDatabaseMode&#34;: True,
                            &#34;noCatalog&#34;: True,
                            &#34;restoreControlFile&#34;: True,
                            &#34;recover&#34;: True,
                            &#34;recoverFrom&#34;: 3,
                            &#34;restoreData&#34;: True,
                            &#34;restoreFrom&#34;: 3
                        }

            tag                 (str)       --  Type of the restore to be performed
                default:    None

            destination_instance(str)       --  destination instance name
                default:    None (in place restore)

            streams             (int)       --  number of streams for restore
                default: 2


        Returns:
            object  --  Job containing restore details

        Raises:
            SDKException:
                If oracle options can&#39;t be set

                If destination_client can&#39;t be set

        &#34;&#34;&#34;
        options = {
            &#34;resetLogs&#34;: 1,
            &#34;switchDatabaseMode&#34;: True,
            &#34;noCatalog&#34;: True,
            &#34;recover&#34;: True,
            &#34;recoverFrom&#34;: 3,
            &#34;restoreData&#34;: True,
            &#34;restoreFrom&#34;: 3
        }
        if oracle_options is None:
            oracle_options = {}
        options.update(oracle_options)
        oracle_options = options.copy()

        if tag and tag.lower() == &#39;snap&#39;:
            opt = {
                &#34;useSnapRestore&#34;: True,
                &#34;cleanupAuxiliary&#34;: True,
                &#34;restoreControlFile&#34;: True,
            }
            oracle_options.update(opt)

        try:
            if destination_client is None or destination_instance is None:
                destination_client = self._properties[&#39;instance&#39;][&#39;clientName&#39;]
                destination_instance = self._properties[&#39;instance&#39;][&#39;instanceName&#39;]
            destination = {
                &#34;destination_client&#34;: destination_client,
                &#34;destination_instance&#34;: destination_instance
            }
            if tag and tag.lower()==&#34;rac&#34;:
                stream_allocation = self._get_rac_stream_allocation(
                    destination_client, destination_instance, streams)
                oracle_options.update(stream_allocation)
                destination[&#34;app_name&#34;] = &#34;Oracle RAC&#34;
            self._restore_destination_json(destination)
        except SDKException:
            raise SDKException(&#34;Instance&#34;, &#34;105&#34;)
        else:
            # subclient = self.subclients.get(subclient_name)
            if destination_client and destination_instance:
                options = self._get_oracle_restore_json(destination_client=destination_client,
                                                        destination=self._destination_restore_json,
                                                        instance_name=self.instance_name,
                                                        tablespaces=self.tablespaces,
                                                        files=files,
                                                        browse_option=browse_option,
                                                        common_options=common_options,
                                                        oracle_options=oracle_options)
            else:
                options = self._get_oracle_restore_json(destination_client=destination_client,
                                                        instance_name=self.instance_name,
                                                        tablespaces=self.tablespaces,
                                                        files=files,
                                                        browse_option=browse_option,
                                                        common_options=common_options,
                                                        oracle_options=oracle_options)
            return self._process_restore_response(options)
            
    def _get_rac_stream_allocation(self, destination_client, destination_instance, streams):
        &#34;&#34;&#34;setter for RAC stream allocation on nodes data population in oracle options for restore
            Args:
                destination_client  (str) -- Name of the destination client
                destination_instance(str) -- Name of the destination RAC instance
                streams             (int)    -- Number of streams for restore
        &#34;&#34;&#34;
        destination_client_obj = self._commcell_object.clients.get(destination_client)
        destination_instance_obj = destination_client_obj.agents.get(&#34;Oracle RAC&#34;).instances.get(destination_instance)
        rac_stream_allocation = {&#34;racDataStreamAllcation&#34;: []}
        for node in destination_instance_obj.properties[&#39;oracleRACInstance&#39;][&#39;racDBInstance&#39;]:
            rac_stream_allocation[&#34;racDataStreamAllcation&#34;].append(f&#34;{node[&#39;racDbInstanceId&#39;]} {streams}&#34;)
        return rac_stream_allocation

    def _restore_db_dump_option_json(self,value):
        &#34;&#34;&#34;setter for the oracle dbdump Restore option in restore JSON
            Args:
                value   (dict)  --  Dictionary of options need to be set for restore
        &#34;&#34;&#34;
        if not isinstance(value,dict):
            raise SDKException(&#39;Instance&#39;,&#39;101&#39;)
            
        self._db_dump_restore_json = {
            &#34;importToDatabase&#34;: True,
            &#34;parallelism&#34;: 2,
            &#34;restorePath&#34;: value.get(&#34;destination_path&#34;, &#34;&#34;),
            &#34;overwriteTable&#34;: False,
            &#34;enabled&#34;: True,
            &#34;connectDetails&#34;: {
                &#34;password&#34;: b64encode(value.get(&#34;db_password&#34;, &#34;&#34;).encode()).decode(),
                &#34;domainName&#34;: (self._properties.get(&#34;oracleInstance&#34;, {}).
                    get(&#34;sqlConnect&#34;, {}).get(&#34;domainName&#34;, &#34;&#34;)),
                &#34;userName&#34;: (self._properties.get(&#34;oracleInstance&#34;, {}).
                    get(&#34;sqlConnect&#34;, {}).get(&#34;userName&#34;, &#34;&#34;))
            }
        }


    def _restore_oracle_option_json(self, value):
        &#34;&#34;&#34;setter for the oracle Restore option in restore JSON
        Args:
                value   (dict)  --  Dictionary of options need to be set for restore
        &#34;&#34;&#34;
        if not isinstance(value,dict):
            raise SDKException(&#39;Instance&#39;,&#39;101&#39;)

        self._oracle_restore_json = {
            &#34;validate&#34;: False,
            &#34;noCatalog&#34;: False,
            &#34;duplicateToName&#34;: &#34;&#34;,
            &#34;cloneEnv&#34;: False,
            &#34;restoreControlFile&#34;: False,
            &#34;duplicate&#34;: False,
            &#34;tableViewRestore&#34;: False,
            &#34;osID&#34;: 2,
            &#34;partialRestore&#34;: False,
            &#34;restoreStream&#34;: 2,
            &#34;restoreSPFile&#34;: False,
            &#34;recover&#34;: True,
            &#34;recoverFrom&#34;: 4,
            &#34;archiveLog&#34;: False,
            &#34;restoreData&#34;: True,
            &#34;restoreFrom&#34;: 0,
            &#34;timeZone&#34;: {
                &#34;TimeZoneName&#34;: &#34;(UTC) Coordinated Universal Time&#34;
            },
            &#34;recoverTime&#34;: {},
            &#34;sourcePaths&#34;: [
                &#34;//**&#34;
            ],
            &#34;restoreTime&#34;: {}
        }

        if value.get(&#34;restore_oracle_options_type&#34;) == &#34;restore_archivelogs_norecover&#34;:
            self._oracle_restore_json = {
                &#34;resetLogs&#34;: 0,
                &#34;backupValidationOnly&#34;: False,
                &#34;threadId&#34;: 1,
                &#34;deviceType&#34;: 0,
                &#34;restoreFailover&#34;: True,
                &#34;resetDatabase&#34;: False,
                &#34;noCatalog&#34;: True,
                &#34;ctrlRestoreFrom&#34;: False,
                &#34;controlFilePath&#34;: &#34;&#34;,
                &#34;specifyControlFileTime&#34;: False,
                &#34;restoreDataTag&#34;: False,
                &#34;useEndLSN&#34;: False,
                &#34;useStartLSN&#34;: False,
                &#34;restoreTablespace&#34;: False,
                &#34;archiveLogBy&#34;: 1,
                &#34;ctrlFileBackupType&#34;: 0,
                &#34;restoreControlFile&#34;: False,
                &#34;restoreInstanceLog&#34;: False,
                &#34;duplicate&#34;: False,
                &#34;startLSNNum&#34;: &#34;&#34;,
                &#34;checkReadOnly&#34;: False,
                &#34;osID&#34;: 2,
                &#34;specifyControlFile&#34;: False,
                &#34;setDBId&#34;: False,
                &#34;partialRestore&#34;: False,
                &#34;restoreStream&#34;: 2,
                &#34;specifySPFile&#34;: False,
                &#34;restoreSPFile&#34;: False,
                &#34;recover&#34;: False,
                &#34;recoverFrom&#34;: 4,
                &#34;archiveLog&#34;: True,
                &#34;endLSNNum&#34;: &#34;&#34;,
                &#34;autoDetectDevice&#34;: True,
                &#34;useEndLog&#34;: False,
                &#34;isDeviceTypeSelected&#34;: False,
                &#34;useStartLog&#34;: True,
                &#34;logTarget&#34;: &#34;&#34;,
                &#34;restoreData&#34;: False,
                &#34;restoreFrom&#34;: 0,
                &#34;duplicateToSkipReadOnly&#34;: False
            }
            if value.get(&#34;start_lsn&#34;, None):
                self._oracle_restore_json[&#34;useStartLSN&#34;] = True
                self._oracle_restore_json[&#34;startLSNNum&#34;] = value.get(&#34;start_lsn&#34;)
            if value.get(&#34;end_lsn&#34;, None):
                self._oracle_restore_json[&#34;useEndLSN&#34;] = True
                self._oracle_restore_json[&#34;endLSNNum&#34;] = value.get(&#34;end_lsn&#34;)
            if value.get(&#34;log_dest&#34;, None):
                self._oracle_restore_json[&#34;logTarget&#34;] = value.get(&#34;log_dest&#34;)

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (dict)  --  Dictionary of options need to be set for restore

            Returns:
                dict             -- JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(OracleInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        self._restore_db_dump_option_json(restore_option)
        self._restore_oracle_option_json(restore_option)
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;dbDumpOptions&#34;] = self._db_dump_restore_json
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;oracleOpt&#34;] = self._oracle_restore_json

        return rest_json

    def restore_in_place(
            self,
            db_password,
            path,
            dest_client_name,
            dest_instance_name,
            dest_path=None,
            restore_oracle_options_type=None,
            start_lsn=None, end_lsn=None,
            log_dest=None):
        &#34;&#34;&#34;Restores the oracle logical dump data/log files specified in the input paths
        list to the same location.

            Args:
                
                db_password             (str)  -- password for oracle database
                
                path                    (list)  --  list of database/databases to be restored

                dest_client_name        (str)   --  destination client name where files are to be
                restored

                dest_instance_name      (str)   --  destination postgres instance name of
                destination client

                dest_path        (str)   --  destinath path for restore

                    default: None

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(path, list) and
                isinstance(db_password, str)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        if not path:
            raise SDKException(&#39;Instance&#39;,&#39;103&#39;)
        

        request_json = self._restore_json(
            db_password=db_password,
            paths=path,
            destination_client=dest_client_name,
            destination_instance=dest_instance_name,
            destination_path=dest_path,
            restore_oracle_options_type=restore_oracle_options_type,
            start_lsn=start_lsn, end_lsn=end_lsn,
            log_dest=log_dest)

        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.dbinstance.DatabaseInstance" href="dbinstance.html#cvpysdk.instances.dbinstance.DatabaseInstance">DatabaseInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.archive_log_dest"><code class="name">var <span class="ident">archive_log_dest</span></code></dt>
<dd>
<div class="desc"><p>Getter for the instance's archive log dest</p>
<h2 id="returns">Returns</h2>
<p>string - string for archivelog location</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L899-L908" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archive_log_dest(self):
    &#34;&#34;&#34;
    Getter for the instance&#39;s archive log dest

    Returns:
        string - string for archivelog location

    &#34;&#34;&#34;
    return self._properties[&#39;oracleInstance&#39;][&#39;archiveLogDest&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.catalog_db"><code class="name">var <span class="ident">catalog_db</span></code></dt>
<dd>
<div class="desc"><p>Getter for catalog database</p>
<h2 id="returns">Returns</h2>
<p>string
- String containing catalog database</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if not set</p>
<pre><code>if catalog is not enabled
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L854-L875" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def catalog_db(self):
    &#34;&#34;&#34;
    Getter for catalog database

    Returns:
        string  - String containing catalog database

    Raises:
        SDKException:
            if not set

            if catalog is not enabled

    &#34;&#34;&#34;
    if not self.is_catalog_enabled:
        raise SDKException(&#39;Instance&#39;, r&#39;102&#39;, &#39;Catalog is not enabled.&#39;)
    try:
        return self._properties[&#39;oracleInstance&#39;][&#39;catalogConnect&#39;][&#39;domainName&#39;]
    except KeyError as error_str:
        raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                           &#39;Catalog database not set - {}&#39;.format(error_str))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.catalog_user"><code class="name">var <span class="ident">catalog_user</span></code></dt>
<dd>
<div class="desc"><p>Getter for catalog user</p>
<h2 id="returns">Returns</h2>
<p>string
- String containing catalog user</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if not set</p>
<pre><code>if catalog is not enabled
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L831-L852" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def catalog_user(self):
    &#34;&#34;&#34;
    Getter for catalog user

    Returns:
        string  - String containing catalog user

    Raises:
        SDKException:
            if not set

            if catalog is not enabled

    &#34;&#34;&#34;
    if not self.is_catalog_enabled:
        raise SDKException(&#39;Instance&#39;, r&#39;102&#39;, &#39;Catalog is not enabled.&#39;)
    try:
        return self._properties[&#39;oracleInstance&#39;][&#39;catalogConnect&#39;][&#39;userName&#39;]
    except KeyError as error_str:
        raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                           &#39;Catalog user not set - {}&#39;.format(error_str))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.cmd_sp"><code class="name">var <span class="ident">cmd_sp</span></code></dt>
<dd>
<div class="desc"><p>Getter for Command Line storage policy</p>
<h2 id="returns">Returns</h2>
<p>string - string for command line storage policy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L910-L920" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cmd_sp(self):
    &#34;&#34;&#34;
    Getter for Command Line storage policy

    Returns:
        string - string for command line storage policy

    &#34;&#34;&#34;
    return self._properties[&#39;oracleInstance&#39;][&#39;oracleStorageDevice&#39;][
        &#39;commandLineStoragePolicy&#39;][&#39;storagePolicyName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.db_user"><code class="name">var <span class="ident">db_user</span></code></dt>
<dd>
<div class="desc"><p>Getter to get the database user used to log into the database</p>
<p>Returns: Oracle database user for the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L945-L953" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def db_user(self):
    &#34;&#34;&#34;
    Getter to get the database user used to log into the database

    Returns: Oracle database user for the instance

    &#34;&#34;&#34;
    return self._properties[&#39;oracleInstance&#39;][&#39;sqlConnect&#39;][&#39;userName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.dbid"><code class="name">var <span class="ident">dbid</span></code></dt>
<dd>
<div class="desc"><p>Getter to get the DBID of the database instance</p>
<p>Returns: DBID of the oracle database</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L974-L982" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def dbid(self):
    &#34;&#34;&#34;
    Getter to get the DBID of the database instance

    Returns: DBID of the oracle database

    &#34;&#34;&#34;
    return self._properties[&#39;oracleInstance&#39;][&#39;DBID&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.is_autobackup_on"><code class="name">var <span class="ident">is_autobackup_on</span></code></dt>
<dd>
<div class="desc"><p>Getter to check whether autobackup is set to ON</p>
<h2 id="returns">Returns</h2>
<p>Bool - True if autobackup is set to ON. Else False.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L934-L943" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_autobackup_on(self):
    &#34;&#34;&#34;
    Getter to check whether autobackup is set to ON

    Returns:
        Bool - True if autobackup is set to ON. Else False.

    &#34;&#34;&#34;
    return True if self._properties[&#39;oracleInstance&#39;][&#39;ctrlFileAutoBackup&#39;] == 1 else False</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.is_catalog_enabled"><code class="name">var <span class="ident">is_catalog_enabled</span></code></dt>
<dd>
<div class="desc"><p>Getter to check if catalog has been enabled</p>
<h2 id="returns">Returns</h2>
<p>Bool - True if catalog is enabled. Else False.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L820-L829" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_catalog_enabled(self):
    &#34;&#34;&#34;
    Getter to check if catalog has been enabled

    Returns:
        Bool - True if catalog is enabled. Else False.

    &#34;&#34;&#34;
    return self._properties[&#39;oracleInstance&#39;][&#39;useCatalogConnect&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.log_sp"><code class="name">var <span class="ident">log_sp</span></code></dt>
<dd>
<div class="desc"><p>Oracle Instance's Log Storage Poplicy</p>
<h2 id="returns">Returns</h2>
<p>string
&ndash; string containing log storage policy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L922-L932" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def log_sp(self):
    &#34;&#34;&#34;
    Oracle Instance&#39;s Log Storage Poplicy

    Returns:
        string  -- string containing log storage policy

    &#34;&#34;&#34;
    return self._properties[&#39;oracleInstance&#39;][&#39;oracleStorageDevice&#39;][
        &#39;logBackupStoragePolicy&#39;][&#39;storagePolicyName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.log_stream"><code class="name">var <span class="ident">log_stream</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch log stream count at instance level</p>
<pre><code>Returns:
        int     --  log stream count atinstance level
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L203-L212" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def log_stream(self):
    &#34;&#34;&#34;
    Getter to fetch log stream count at instance level

        Returns:
                int     --  log stream count atinstance level

    &#34;&#34;&#34;
    return self._instanceprop.get(&#34;numberOfArchiveLogBackupStreams&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.oracle_home"><code class="name">var <span class="ident">oracle_home</span></code></dt>
<dd>
<div class="desc"><p>getter for oracle home</p>
<h2 id="returns">Returns</h2>
<p>string - string of oracle_home</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L809-L818" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def oracle_home(self):
    &#34;&#34;&#34;
    getter for oracle home

    Returns:
        string - string of oracle_home

    &#34;&#34;&#34;
    return self._properties[&#39;oracleInstance&#39;][&#39;oracleHome&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.os_user"><code class="name">var <span class="ident">os_user</span></code></dt>
<dd>
<div class="desc"><p>Getter for oracle software owner</p>
<h2 id="returns">Returns</h2>
<p>string - string of oracle software owner</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L877-L886" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def os_user(self):
    &#34;&#34;&#34;
    Getter for oracle software owner

    Returns:
        string - string of oracle software owner

    &#34;&#34;&#34;
    return self._properties[&#39;oracleInstance&#39;][&#39;oracleUser&#39;][&#39;userName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.tablespaces"><code class="name">var <span class="ident">tablespaces</span></code></dt>
<dd>
<div class="desc"><p>Getter for listing out all tablespaces for the instance</p>
<h2 id="returns">Returns</h2>
<p>list &ndash; list containing tablespace names for the database</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L984-L993" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tablespaces(self):
    &#34;&#34;&#34;
    Getter for listing out all tablespaces for the instance

    Returns:
        list -- list containing tablespace names for the database

    &#34;&#34;&#34;
    return [ts[&#39;tableSpace&#39;] for ts in self.browse()]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.tns_name"><code class="name">var <span class="ident">tns_name</span></code></dt>
<dd>
<div class="desc"><p>Getter to get the TNS Names of the database</p>
<h2 id="returns">Returns</h2>
<p>string
&ndash; TNS name of the instance configured</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if not set</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L955-L972" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tns_name(self):
    &#34;&#34;&#34;
    Getter to get the TNS Names of the database

    Returns:
        string  -- TNS name of the instance configured

    Raises:
        SDKException:
            if not set

    &#34;&#34;&#34;
    try:
        return self._properties[&#39;oracleInstance&#39;][&#39;sqlConnect&#39;][&#39;domainName&#39;]
    except KeyError as error_str:
        raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                           &#39;Instance TNS Entry not set - {}&#39;.format(error_str))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.version"><code class="name">var <span class="ident">version</span></code></dt>
<dd>
<div class="desc"><p>Getter for oracle version</p>
<h2 id="returns">Returns</h2>
<p>string - string of oracle instance version</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L888-L897" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def version(self):
    &#34;&#34;&#34;
    Getter for oracle version

    Returns:
        string - string of oracle instance version

    &#34;&#34;&#34;
    return self._properties[&#39;version&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, subclient_name='default')</span>
</code></dt>
<dd>
<div class="desc"><p>Uses the default subclient to backup the database</p>
<h2 id="args">Args</h2>
<p>subclient_name (str) &ndash; name of subclient to use
default: default</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L1005-L1012" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self, subclient_name=r&#34;default&#34;):
    &#34;&#34;&#34;Uses the default subclient to backup the database

    Args:
        subclient_name (str) -- name of subclient to use
            default: default
    &#34;&#34;&#34;
    return self.subclients.get(subclient_name).backup(r&#39;full&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Overridden method to browse oracle database tablespaces</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L995-L1003" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, *args, **kwargs):
    &#34;&#34;&#34;Overridden method to browse oracle database tablespaces&#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    elif kwargs:
        options = kwargs
    else:
        options = self._get_browse_options()
    return self._process_browse_response(options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.configure_data_masking_policy"><code class="name flex">
<span>def <span class="ident">configure_data_masking_policy</span></span>(<span>self, policy_name, table_list_of_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Configures data masking policy with given parameters</p>
<h2 id="args">Args</h2>
<p>policy_name
(str)
&ndash;
string representing policy name
table_list_of_dict
list(dict)
&ndash; list containing one dict item representing
rules for single table
Sample
list
Tables:
[
{
"name":"schema_name.table_name",
"columns": [ {"name":"column_name", "type":"algorithm_type"},
"arguments":[list of strings]…]
}
]
Sample :
[
{
"name":"HR.NUMNEW",
"columns":[{"name":"N1","type":0},{"name":"N2","type":2,
"arguments":["1000","2000"]}]
},
{
"name":"HR.CHANGE",
"columns":[{"name":"C1","type":1},{"name":"C2","type":1}]
}
]
schema_name , table_name, column_name: str
Column type key in main dict takes list of dict as value :
This list of dict represents each column name and type of algorithm
and arguments if any for that algorithm
arguments : list of strings</p>
<p>Choose appropriate algorithm type and pass necessary arguments based
on column type</p>
<p>Algorithm
Arguments mandatory
Arguments Format
Algorithm type number</p>
<p>Shuffling
NA
NA
0
Numeric Range
[min, max]
["1000","2000"]
2
Numeric Variance
[variance percentage]
["50"]
3
FPE
NA
NA
1
Fixed String
string_to_replace
["string_to_replace"]
4</p>
<p>Supported Algorithms :</p>
<p>Column Type
Algorithms Supported</p>
<p>Numeric
Shuffling, FPE, Numeric Range, Numeric Variance
Char
Shuffling , FPE , Fixed String
Varchar
Shuffling , FPE , Fixed String</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L445-L530" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_data_masking_policy(self, policy_name, table_list_of_dict):
    &#34;&#34;&#34;Configures data masking policy with given parameters
    Args:
        policy_name         (str)   --  string representing policy name
        table_list_of_dict  list(dict)  -- list containing one dict item representing
                                            rules for single table
        Sample  list
        Tables:
                [
                {
                &#34;name&#34;:&#34;schema_name.table_name&#34;,
                &#34;columns&#34;: [ {&#34;name&#34;:&#34;column_name&#34;, &#34;type&#34;:&#34;algorithm_type&#34;},
                            &#34;arguments&#34;:[list of strings]…]
                }
                ]
                Sample :
                [
                {
                &#34;name&#34;:&#34;HR.NUMNEW&#34;,
                &#34;columns&#34;:[{&#34;name&#34;:&#34;N1&#34;,&#34;type&#34;:0},{&#34;name&#34;:&#34;N2&#34;,&#34;type&#34;:2,
                &#34;arguments&#34;:[&#34;1000&#34;,&#34;2000&#34;]}]
                },
                {
                &#34;name&#34;:&#34;HR.CHANGE&#34;,
                &#34;columns&#34;:[{&#34;name&#34;:&#34;C1&#34;,&#34;type&#34;:1},{&#34;name&#34;:&#34;C2&#34;,&#34;type&#34;:1}]
                }
                ]
        schema_name , table_name, column_name: str
        Column type key in main dict takes list of dict as value :
        This list of dict represents each column name and type of algorithm
        and arguments if any for that algorithm
        arguments : list of strings

        Choose appropriate algorithm type and pass necessary arguments based
        on column type

        Algorithm       Arguments mandatory       Arguments Format        Algorithm type number

        Shuffling           NA                              NA                      0
        Numeric Range       [min, max]                  [&#34;1000&#34;,&#34;2000&#34;]             2
        Numeric Variance    [variance percentage]           [&#34;50&#34;]                  3
        FPE                 NA                              NA                      1
        Fixed String        string_to_replace           [&#34;string_to_replace&#34;]       4


        Supported Algorithms :

        Column Type     Algorithms Supported

        Numeric         Shuffling, FPE, Numeric Range, Numeric Variance
        Char            Shuffling , FPE , Fixed String
        Varchar         Shuffling , FPE , Fixed String

    &#34;&#34;&#34;
    request_json = {
        &#34;opType&#34;: 2,
        &#34;policy&#34;: {
            &#34;association&#34;: {&#34;instanceId&#34;: int(self.instance_id)},
            &#34;config&#34;: {&#34;tables&#34;: table_list_of_dict},
            &#34;policy&#34;: {&#34;policyName&#34;: policy_name}
        }
    }

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;MASKING_POLICY&#39;], request_json
    )
    if flag:
        if response.json():
            error_code = response.json()[&#39;errorCode&#39;]

            if error_code != 0:
                error_string = response.json()[&#39;errorMessage&#39;]
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;Error while creating Data masking policy\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                )
            else:
                return True

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.create_live_sync_schedule"><code class="name flex">
<span>def <span class="ident">create_live_sync_schedule</span></span>(<span>self, dest_client, dest_instance, schedule_name, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs full backup on source oracle instance and
Creates live sync schdule for the given destination oracle instance</p>
<pre><code>Args:
    dest_client  (str)   --  The destination client name for live sync

    dest_instance    (str)   --  The destination instance name for live sync

    schedule_name    (str)   --  The name of the live sync schedule to be created

    **kwargs    (dict)  -- dict of keyword arguments as follows:

                         redirect_path   (str) --  Path on destination client
                                         to redirect tablespaces and datafiles

Returns:
     (object)  --   The job object of the baseline backup that will be replicated
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L396-L443" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_live_sync_schedule(self, dest_client, dest_instance, schedule_name,
                              **kwargs):
    &#34;&#34;&#34;
           Runs full backup on source oracle instance and
           Creates live sync schdule for the given destination oracle instance

               Args:
                   dest_client  (str)   --  The destination client name for live sync

                   dest_instance    (str)   --  The destination instance name for live sync

                   schedule_name    (str)   --  The name of the live sync schedule to be created

                   **kwargs    (dict)  -- dict of keyword arguments as follows:

                                        redirect_path   (str) --  Path on destination client
                                                        to redirect tablespaces and datafiles

               Returns:
                    (object)  --   The job object of the baseline backup that will be replicated

    &#34;&#34;&#34;
    source_backupset_id = int(self.backupsets.get(&#39;default&#39;).backupset_id)
    subclient_obj = self.subclients.get(&#39;default&#39;)
    baseline_job_object = subclient_obj.backup(backup_level=&#39;full&#39;)
    if not baseline_job_object.wait_for_completion():
        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, baseline_job_object.delay_reason)
    baseline_ref_time = baseline_job_object.summary[&#39;jobStartTime&#39;]
    baseline_jobid = int(baseline_job_object.job_id)
    request_json = self._live_sync_restore_json(dest_client, dest_instance, baseline_jobid,
                                                baseline_ref_time, schedule_name,
                                                source_backupset_id, **kwargs)
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._LIVE_SYNC, request_json)
    if flag:
        if response.json():
            if &#34;taskId&#34; in response.json():
                return baseline_job_object
            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]
                error_message = &#39;Live Sync configuration failed\nError: &#34;{0}&#34;&#39;.format(
                    error_message)
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to create schedule&#39;)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.delete_data_masking_policy"><code class="name flex">
<span>def <span class="ident">delete_data_masking_policy</span></span>(<span>self, policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes given data masking policy</p>
<h2 id="args">Args</h2>
<p>policy_name
(str)
&ndash;
data masking policy name to be deleted</p>
<h2 id="returns">Returns</h2>
<p>bool
&ndash; returns true when deletion succeeds</p>
<h2 id="raises">Raises</h2>
<p>Exception</p>
<pre><code>When deletion of policy fails

When Invalid policy name under given instance is provided
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L557-L606" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_data_masking_policy(self, policy_name):
    &#34;&#34;&#34;Deletes given data masking policy
    Args:
        policy_name         (str)       --  data masking policy name to be deleted

    Returns:
        bool                            -- returns true when deletion succeeds

    Raises:
        Exception

            When deletion of policy fails

            When Invalid policy name under given instance is provided
    &#34;&#34;&#34;
    source_instance_id = int(self.instance_id)
    policy_id = self.get_masking_policy_id(policy_name)
    if policy_id is None:
        raise SDKException(
            &#39;Instance&#39;,
            &#39;106&#39;)

    request_json = {
        &#34;opType&#34;: 3,
        &#34;policy&#34;: {
            &#34;association&#34;: {&#34;instanceId&#34;: source_instance_id},
            &#34;policy&#34;: {&#34;policyId&#34;: policy_id, &#34;policyName&#34;: policy_name}
        }
    }

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;MASKING_POLICY&#39;], request_json
    )
    if flag:
        if response.json():
            error_code = response.json()[&#39;errorCode&#39;]

            if error_code != 0:
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;Error while deleting Data masking policy\nError&#39;)
            else:
                return True

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.get_masking_policy_id"><code class="name flex">
<span>def <span class="ident">get_masking_policy_id</span></span>(<span>self, policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns policy id of given data masking policy</p>
<h2 id="args">Args</h2>
<p>policy_name
(str)
&ndash; data masking policy name</p>
<h2 id="returns">Returns</h2>
<p>policy_id
(int)
&ndash; data masking policy ID</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L532-L555" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_masking_policy_id(self, policy_name):
    &#34;&#34;&#34;Returns policy id of given data masking policy
    Args:
        policy_name          (str)       -- data masking policy name

    Returns:
        policy_id            (int)       -- data masking policy ID

    &#34;&#34;&#34;
    instance_id = int(self.instance_id)
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self._services[&#39;MASKING_POLICY&#39;])
    response_json = response.json()
    policy_list = response_json[&#34;policies&#34;]
    policy_id = None
    for i in policy_list:
        pname = i[&#34;policy&#34;][&#34;policyName&#34;]
        associated_instance_id = i[&#34;association&#34;][&#34;instanceId&#34;]
        if (pname == policy_name) and (associated_instance_id == instance_id):
            policy_id = int(i[&#34;policy&#34;][&#34;policyId&#34;])
            break
        else:
            continue
    return policy_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.restore"><code class="name flex">
<span>def <span class="ident">restore</span></span>(<span>self, files=None, destination_client=None, common_options=None, browse_option=None, oracle_options=None, tag=None, destination_instance=None, streams=2)</span>
</code></dt>
<dd>
<div class="desc"><p>Perform restore full/partial database using latest backup or backup copy</p>
<h2 id="args">Args</h2>
<p>files
(dict)
&ndash;
fileOption for restore</p>
<p>destination_client
(str)
&ndash;
destination client name</p>
<p>common_options
(dict)
&ndash;
dictionary containing common options
default:
None</p>
<p>browse_option
(dict)
&ndash;
dictionary containing browse options</p>
<p>oracle_options
(dict)
&ndash;
dictionary containing other oracle options
default:
By default it restores the controlfile and datafiles from latest backup</p>
<pre><code>Example:{
            "resetLogs": 1,
            "switchDatabaseMode": True,
            "noCatalog": True,
            "restoreControlFile": True,
            "recover": True,
            "recoverFrom": 3,
            "restoreData": True,
            "restoreFrom": 3
        }
</code></pre>
<p>tag
(str)
&ndash;
Type of the restore to be performed
default:
None</p>
<p>destination_instance(str)
&ndash;
destination instance name
default:
None (in place restore)</p>
<p>streams
(int)
&ndash;
number of streams for restore
default: 2</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
Job containing restore details</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
If oracle options can't be set</p>
<pre><code>If destination_client can't be set
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L1014-L1128" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore(
        self,
        files=None,
        destination_client=None,
        common_options=None,
        browse_option=None,
        oracle_options=None,
        tag=None,
        destination_instance=None,
        streams=2):
    &#34;&#34;&#34;
    Perform restore full/partial database using latest backup or backup copy

    Args:
        files               (dict)      --  fileOption for restore

        destination_client  (str)       --  destination client name

        common_options      (dict)      --  dictionary containing common options
            default:    None

        browse_option       (dict)      --  dictionary containing browse options

        oracle_options      (dict)      --  dictionary containing other oracle options
            default:    By default it restores the controlfile and datafiles from latest backup

            Example:{
                        &#34;resetLogs&#34;: 1,
                        &#34;switchDatabaseMode&#34;: True,
                        &#34;noCatalog&#34;: True,
                        &#34;restoreControlFile&#34;: True,
                        &#34;recover&#34;: True,
                        &#34;recoverFrom&#34;: 3,
                        &#34;restoreData&#34;: True,
                        &#34;restoreFrom&#34;: 3
                    }

        tag                 (str)       --  Type of the restore to be performed
            default:    None

        destination_instance(str)       --  destination instance name
            default:    None (in place restore)

        streams             (int)       --  number of streams for restore
            default: 2


    Returns:
        object  --  Job containing restore details

    Raises:
        SDKException:
            If oracle options can&#39;t be set

            If destination_client can&#39;t be set

    &#34;&#34;&#34;
    options = {
        &#34;resetLogs&#34;: 1,
        &#34;switchDatabaseMode&#34;: True,
        &#34;noCatalog&#34;: True,
        &#34;recover&#34;: True,
        &#34;recoverFrom&#34;: 3,
        &#34;restoreData&#34;: True,
        &#34;restoreFrom&#34;: 3
    }
    if oracle_options is None:
        oracle_options = {}
    options.update(oracle_options)
    oracle_options = options.copy()

    if tag and tag.lower() == &#39;snap&#39;:
        opt = {
            &#34;useSnapRestore&#34;: True,
            &#34;cleanupAuxiliary&#34;: True,
            &#34;restoreControlFile&#34;: True,
        }
        oracle_options.update(opt)

    try:
        if destination_client is None or destination_instance is None:
            destination_client = self._properties[&#39;instance&#39;][&#39;clientName&#39;]
            destination_instance = self._properties[&#39;instance&#39;][&#39;instanceName&#39;]
        destination = {
            &#34;destination_client&#34;: destination_client,
            &#34;destination_instance&#34;: destination_instance
        }
        if tag and tag.lower()==&#34;rac&#34;:
            stream_allocation = self._get_rac_stream_allocation(
                destination_client, destination_instance, streams)
            oracle_options.update(stream_allocation)
            destination[&#34;app_name&#34;] = &#34;Oracle RAC&#34;
        self._restore_destination_json(destination)
    except SDKException:
        raise SDKException(&#34;Instance&#34;, &#34;105&#34;)
    else:
        # subclient = self.subclients.get(subclient_name)
        if destination_client and destination_instance:
            options = self._get_oracle_restore_json(destination_client=destination_client,
                                                    destination=self._destination_restore_json,
                                                    instance_name=self.instance_name,
                                                    tablespaces=self.tablespaces,
                                                    files=files,
                                                    browse_option=browse_option,
                                                    common_options=common_options,
                                                    oracle_options=oracle_options)
        else:
            options = self._get_oracle_restore_json(destination_client=destination_client,
                                                    instance_name=self.instance_name,
                                                    tablespaces=self.tablespaces,
                                                    files=files,
                                                    browse_option=browse_option,
                                                    common_options=common_options,
                                                    oracle_options=oracle_options)
        return self._process_restore_response(options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, db_password, path, dest_client_name, dest_instance_name, dest_path=None, restore_oracle_options_type=None, start_lsn=None, end_lsn=None, log_dest=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the oracle logical dump data/log files specified in the input paths
list to the same location.</p>
<pre><code>Args:

    db_password             (str)  -- password for oracle database

    path                    (list)  --  list of database/databases to be restored

    dest_client_name        (str)   --  destination client name where files are to be
    restored

    dest_instance_name      (str)   --  destination postgres instance name of
    destination client

    dest_path        (str)   --  destinath path for restore

        default: None

Returns:
    object - instance of the Job class for this restore job

Raises:
    SDKException:
        if paths is not a list

        if failed to initialize job

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L1284-L1344" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        db_password,
        path,
        dest_client_name,
        dest_instance_name,
        dest_path=None,
        restore_oracle_options_type=None,
        start_lsn=None, end_lsn=None,
        log_dest=None):
    &#34;&#34;&#34;Restores the oracle logical dump data/log files specified in the input paths
    list to the same location.

        Args:
            
            db_password             (str)  -- password for oracle database
            
            path                    (list)  --  list of database/databases to be restored

            dest_client_name        (str)   --  destination client name where files are to be
            restored

            dest_instance_name      (str)   --  destination postgres instance name of
            destination client

            dest_path        (str)   --  destinath path for restore

                default: None

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not (isinstance(path, list) and
            isinstance(db_password, str)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
    if not path:
        raise SDKException(&#39;Instance&#39;,&#39;103&#39;)
    

    request_json = self._restore_json(
        db_password=db_password,
        paths=path,
        destination_client=dest_client_name,
        destination_instance=dest_instance_name,
        destination_path=dest_path,
        restore_oracle_options_type=restore_oracle_options_type,
        start_lsn=start_lsn, end_lsn=end_lsn,
        log_dest=log_dest)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.restore_to_disk"><code class="name flex">
<span>def <span class="ident">restore_to_disk</span></span>(<span>self, destination_client, destination_path, backup_job_ids, user_name, password)</span>
</code></dt>
<dd>
<div class="desc"><p>Perform restore to disk [Application free restore] for Oracle</p>
<pre><code>Args:
    destination_client          (str)   --  destination client name

    destination_path:           (str)   --  destination path

    backup_job_ids              (list)  --  list of backup job IDs
                                            to be used for disk restore

    user_name                   (str)   --  impersonation user name to
                                            restore to destination client

    password                    (str)   --  impersonation user password

Returns:
    object -     Job containing restore details

Raises:
    SDKException
        if backup_job_ids not given as list of items
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L135-L172" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_disk(self,
                    destination_client,
                    destination_path,
                    backup_job_ids,
                    user_name,
                    password):
    &#34;&#34;&#34;
    Perform restore to disk [Application free restore] for Oracle

        Args:
            destination_client          (str)   --  destination client name

            destination_path:           (str)   --  destination path

            backup_job_ids              (list)  --  list of backup job IDs
                                                    to be used for disk restore

            user_name                   (str)   --  impersonation user name to
                                                    restore to destination client

            password                    (str)   --  impersonation user password

        Returns:
            object -     Job containing restore details

        Raises:
            SDKException
                if backup_job_ids not given as list of items

    &#34;&#34;&#34;
    if not isinstance(backup_job_ids, list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
    request_json = self._get_restore_to_disk_json(destination_client,
                                                  destination_path,
                                                  backup_job_ids,
                                                  user_name,
                                                  password)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.oracleinstance.OracleInstance.standalone_data_masking"><code class="name flex">
<span>def <span class="ident">standalone_data_masking</span></span>(<span>self, policy_name, destination_client=None, destination_instance=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Launch standalone data masking job on given instance</p>
<h2 id="args">Args</h2>
<p>policy_name
(str)
&ndash; data masking policy name</p>
<p>destination_client
(str)
&ndash; destination client in which destination
instance exists</p>
<p>destination_instance (str)
&ndash; destination instance to which masking
to be applied</p>
<h2 id="returns">Returns</h2>
<p>object &ndash; Job containing data masking job details</p>
<h2 id="raises">Raises</h2>
<p>SDKException
if policy ID retrieved is None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/oracleinstance.py#L608-L673" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def standalone_data_masking(
        self,
        policy_name,
        destination_client=None,
        destination_instance=None):
    &#34;&#34;&#34;Launch standalone data masking job on given instance

    Args:

        policy_name          (str)       -- data masking policy name

        destination_client   (str)       -- destination client in which destination
        instance exists

        destination_instance (str)       -- destination instance to which masking
        to be applied

    Returns:
        object -- Job containing data masking job details


    Raises:
        SDKException
            if policy ID retrieved is None

    &#34;&#34;&#34;
    if destination_client is None:
        destination_client = self._properties[&#39;instance&#39;][&#39;clientName&#39;]
    if destination_instance is None:
        destination_instance = self.instance_name
    destination_client_object = self._commcell_object.clients.get(
        destination_client)
    destination_agent_object = destination_client_object.agents.get(
        &#39;oracle&#39;)
    destination_instance_object = destination_agent_object.instances.get(
        destination_instance)
    destination_instance_id = int(destination_instance_object.instance_id)
    source_instance_id = int(self.instance_id)
    policy_id = self.get_masking_policy_id(policy_name)
    if policy_id is None:
        raise SDKException(
            &#39;Instance&#39;,
            &#39;106&#39;)
    request_json = self._restore_json(paths=r&#39;/&#39;)
    destination_instance_json = {
        &#34;clientName&#34;: destination_client,
        &#34;instanceName&#34;: destination_instance,
        &#34;instanceId&#34;: destination_instance_id
    }
    data_masking_options = {
        &#34;isStandalone&#34;: True,
        &#34;enabled&#34;: True,
        &#34;dbDMPolicy&#34;: {
            &#34;association&#34;: {
                &#34;instanceId&#34;: source_instance_id},
            &#34;policy&#34;: {
                &#34;policyId&#34;: policy_id,
                &#34;policyName&#34;: policy_name}}}
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
        &#34;restoreOptions&#34;][&#34;destination&#34;][&#34;destClient&#34;][&#34;clientName&#34;] = destination_client
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
        &#34;restoreOptions&#34;][&#34;destination&#34;][&#34;destinationInstance&#34;] = destination_instance_json
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
        &#34;restoreOptions&#34;][&#34;dbDataMaskingOptions&#34;] = data_masking_options
    del request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;fileOption&#34;]
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.dbinstance.DatabaseInstance" href="dbinstance.html#cvpysdk.instances.dbinstance.DatabaseInstance">DatabaseInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.oracleinstance.OracleInstance" href="#cvpysdk.instances.oracleinstance.OracleInstance">OracleInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.archive_log_dest" href="#cvpysdk.instances.oracleinstance.OracleInstance.archive_log_dest">archive_log_dest</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.backup" href="#cvpysdk.instances.oracleinstance.OracleInstance.backup">backup</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.browse" href="#cvpysdk.instances.oracleinstance.OracleInstance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.catalog_db" href="#cvpysdk.instances.oracleinstance.OracleInstance.catalog_db">catalog_db</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.catalog_user" href="#cvpysdk.instances.oracleinstance.OracleInstance.catalog_user">catalog_user</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.cmd_sp" href="#cvpysdk.instances.oracleinstance.OracleInstance.cmd_sp">cmd_sp</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.configure_data_masking_policy" href="#cvpysdk.instances.oracleinstance.OracleInstance.configure_data_masking_policy">configure_data_masking_policy</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.create_live_sync_schedule" href="#cvpysdk.instances.oracleinstance.OracleInstance.create_live_sync_schedule">create_live_sync_schedule</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.db_user" href="#cvpysdk.instances.oracleinstance.OracleInstance.db_user">db_user</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.dbid" href="#cvpysdk.instances.oracleinstance.OracleInstance.dbid">dbid</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.delete_data_masking_policy" href="#cvpysdk.instances.oracleinstance.OracleInstance.delete_data_masking_policy">delete_data_masking_policy</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.get_masking_policy_id" href="#cvpysdk.instances.oracleinstance.OracleInstance.get_masking_policy_id">get_masking_policy_id</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.is_autobackup_on" href="#cvpysdk.instances.oracleinstance.OracleInstance.is_autobackup_on">is_autobackup_on</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.is_catalog_enabled" href="#cvpysdk.instances.oracleinstance.OracleInstance.is_catalog_enabled">is_catalog_enabled</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.log_sp" href="#cvpysdk.instances.oracleinstance.OracleInstance.log_sp">log_sp</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.log_stream" href="#cvpysdk.instances.oracleinstance.OracleInstance.log_stream">log_stream</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.oracle_home" href="#cvpysdk.instances.oracleinstance.OracleInstance.oracle_home">oracle_home</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.os_user" href="#cvpysdk.instances.oracleinstance.OracleInstance.os_user">os_user</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.restore" href="#cvpysdk.instances.oracleinstance.OracleInstance.restore">restore</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.restore_in_place" href="#cvpysdk.instances.oracleinstance.OracleInstance.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.restore_to_disk" href="#cvpysdk.instances.oracleinstance.OracleInstance.restore_to_disk">restore_to_disk</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.standalone_data_masking" href="#cvpysdk.instances.oracleinstance.OracleInstance.standalone_data_masking">standalone_data_masking</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.tablespaces" href="#cvpysdk.instances.oracleinstance.OracleInstance.tablespaces">tablespaces</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.tns_name" href="#cvpysdk.instances.oracleinstance.OracleInstance.tns_name">tns_name</a></code></li>
<li><code><a title="cvpysdk.instances.oracleinstance.OracleInstance.version" href="#cvpysdk.instances.oracleinstance.OracleInstance.version">version</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>