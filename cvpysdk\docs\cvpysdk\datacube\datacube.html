<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.datacube.datacube API documentation</title>
<meta name="description" content="Main file for performing operations related to Datacube APIs …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.datacube.datacube</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing operations related to Datacube APIs.</p>
<p>The class <code><a title="cvpysdk.datacube.datacube.Datacube" href="#cvpysdk.datacube.datacube.Datacube">Datacube</a></code> is defined here in this file,
that will directly interact with all the Datacube APIs.</p>
<h2 id="datacube">Datacube</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialise object of the Datacube class</p>
<p><strong>repr</strong>()
&ndash;
returns the string representation of an instance of this class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_analytics_engines()
&ndash;
returns the list of all Content Indexing (CI) Servers</p>
<p>datasources()
&ndash;
returns an instance of the Datasources class</p>
<p>get_jdbc_drivers()
&ndash;
gets the list all jdbc_drivers associated with the datacube.</p>
<p>refresh()
&ndash;
refresh the datasources associated with the Datacube Engine</p>
<p>refresh_engine()
&ndash;
refresh the index server associated with datacube</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/datacube/datacube.py#L1-L193" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing operations related to Datacube APIs.

The class `Datacube` is defined here in this file,
that will directly interact with all the Datacube APIs.


Datacube:

    __init__(commcell_object)   --  initialise object of the Datacube class

    __repr__()                  --  returns the string representation of an instance of this class

    _response_not_success()     --  parses through the exception response, and raises SDKException

    _get_analytics_engines()    --  returns the list of all Content Indexing (CI) Servers

    datasources()               --  returns an instance of the Datasources class

    get_jdbc_drivers()          --  gets the list all jdbc_drivers associated with the datacube.

    refresh()                   --  refresh the datasources associated with the Datacube Engine

    refresh_engine()            --  refresh the index server associated with datacube

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from .datasource import Datasources

from ..exception import SDKException


USER_LOGGED_OUT_MESSAGE = &#39;User Logged Out. Please initialize the Commcell object again.&#39;
&#34;&#34;&#34;str:     Message to be returned to the user, when trying the get the value of an attribute
of the Commcell class, after the user was logged out.

&#34;&#34;&#34;


class Datacube(object):

    &#34;&#34;&#34; Represents a datacube running on the commcell &#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize an instance of the Datacube class.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of the Datacube class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._ANALYTICS_ENGINES = self._services[&#39;GET_ALL_INDEX_SERVERS&#39;]
        self._ALL_DATASOURCES = self._services[&#39;GET_ALL_DATASOURCES&#39;]
        self._GET_JDBC_DRIVERS = None

        self._analytics_engines = self._get_analytics_engines()
        self._datasources = None

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.

            Returns:
                str     -   string consisting of the details of the instance of this class

        &#34;&#34;&#34;
        o_str = &#34;Datacube class instance for CommServ &#39;{0}&#39;&#34;.format(
            self._commcell_object.commserv_name
        )

        return o_str

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_analytics_engines(self):
        &#34;&#34;&#34;Gets the list all the analytics engines associated with the datacube.

            Returns:
                list    -   array consisting details of all analytics engines

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._ANALYTICS_ENGINES)

        if flag:
            if &#39;listOfCIServer&#39; in response.json():
                return response.json()[&#39;listOfCIServer&#39;]
            return []
        self._response_not_success(response)

    @property
    def analytics_engines(self):
        &#34;&#34;&#34;Returns the value of the analytics engines attributes.&#34;&#34;&#34;
        return self._analytics_engines

    @property
    def datasources(self):
        &#34;&#34;&#34;Returns the instance of the Datasources class.&#34;&#34;&#34;
        try:
            if self._datasources is None:
                self._datasources = Datasources(self)

            return self._datasources
        except AttributeError:
            return USER_LOGGED_OUT_MESSAGE

    def get_jdbc_drivers(self, analytics_engine):
        &#34;&#34;&#34;Gets the list all jdbc_drivers associated with the datacube.

            Args:
                analytics_engine (str) -- client name of analytics_engine

            Returns:
                list    -   consists of all jdbc_drivers in the datacube

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(analytics_engine, str):
            raise SDKException(&#39;Datacube&#39;, &#39;101&#39;)

        engine_index = (
            self.analytics_engines.index(engine)
            for engine in self.analytics_engines
            if engine[&#34;clientName&#34;] == analytics_engine
        ).next()

        self._GET_JDBC_DRIVERS = self._services[&#39;GET_JDBC_DRIVERS&#39;] % (
            self.analytics_engines[engine_index][&#34;cloudID&#34;]
        )

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._GET_JDBC_DRIVERS)

        if flag:
            if response.json() and &#39;drivers&#39; in response.json():
                return response.json()[&#39;drivers&#39;]
            else:
                raise SDKException(&#39;Datacube&#39;, &#39;103&#39;)
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the datasources associated to the Datacube Engine.&#34;&#34;&#34;
        self._datasources = None

    def refresh_engine(self):
        &#34;&#34;&#34;Refresh the Index server associated to the Datacube.&#34;&#34;&#34;
        self._analytics_engines = self._get_analytics_engines()</code></pre>
</details>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-variables">Global variables</h2>
<dl>
<dt id="cvpysdk.datacube.datacube.USER_LOGGED_OUT_MESSAGE"><code class="name">var <span class="ident">USER_LOGGED_OUT_MESSAGE</span></code></dt>
<dd>
<div class="desc"><p>str:
Message to be returned to the user, when trying the get the value of an attribute
of the Commcell class, after the user was logged out.</p></div>
</dd>
</dl>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.datacube.datacube.Datacube"><code class="flex name class">
<span>class <span class="ident">Datacube</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Represents a datacube running on the commcell </p>
<p>Initialize an instance of the Datacube class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Datacube class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/datacube/datacube.py#L60-L193" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Datacube(object):

    &#34;&#34;&#34; Represents a datacube running on the commcell &#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize an instance of the Datacube class.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of the Datacube class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._ANALYTICS_ENGINES = self._services[&#39;GET_ALL_INDEX_SERVERS&#39;]
        self._ALL_DATASOURCES = self._services[&#39;GET_ALL_DATASOURCES&#39;]
        self._GET_JDBC_DRIVERS = None

        self._analytics_engines = self._get_analytics_engines()
        self._datasources = None

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.

            Returns:
                str     -   string consisting of the details of the instance of this class

        &#34;&#34;&#34;
        o_str = &#34;Datacube class instance for CommServ &#39;{0}&#39;&#34;.format(
            self._commcell_object.commserv_name
        )

        return o_str

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_analytics_engines(self):
        &#34;&#34;&#34;Gets the list all the analytics engines associated with the datacube.

            Returns:
                list    -   array consisting details of all analytics engines

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._ANALYTICS_ENGINES)

        if flag:
            if &#39;listOfCIServer&#39; in response.json():
                return response.json()[&#39;listOfCIServer&#39;]
            return []
        self._response_not_success(response)

    @property
    def analytics_engines(self):
        &#34;&#34;&#34;Returns the value of the analytics engines attributes.&#34;&#34;&#34;
        return self._analytics_engines

    @property
    def datasources(self):
        &#34;&#34;&#34;Returns the instance of the Datasources class.&#34;&#34;&#34;
        try:
            if self._datasources is None:
                self._datasources = Datasources(self)

            return self._datasources
        except AttributeError:
            return USER_LOGGED_OUT_MESSAGE

    def get_jdbc_drivers(self, analytics_engine):
        &#34;&#34;&#34;Gets the list all jdbc_drivers associated with the datacube.

            Args:
                analytics_engine (str) -- client name of analytics_engine

            Returns:
                list    -   consists of all jdbc_drivers in the datacube

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(analytics_engine, str):
            raise SDKException(&#39;Datacube&#39;, &#39;101&#39;)

        engine_index = (
            self.analytics_engines.index(engine)
            for engine in self.analytics_engines
            if engine[&#34;clientName&#34;] == analytics_engine
        ).next()

        self._GET_JDBC_DRIVERS = self._services[&#39;GET_JDBC_DRIVERS&#39;] % (
            self.analytics_engines[engine_index][&#34;cloudID&#34;]
        )

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._GET_JDBC_DRIVERS)

        if flag:
            if response.json() and &#39;drivers&#39; in response.json():
                return response.json()[&#39;drivers&#39;]
            else:
                raise SDKException(&#39;Datacube&#39;, &#39;103&#39;)
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the datasources associated to the Datacube Engine.&#34;&#34;&#34;
        self._datasources = None

    def refresh_engine(self):
        &#34;&#34;&#34;Refresh the Index server associated to the Datacube.&#34;&#34;&#34;
        self._analytics_engines = self._get_analytics_engines()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.datacube.datacube.Datacube.analytics_engines"><code class="name">var <span class="ident">analytics_engines</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of the analytics engines attributes.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/datacube/datacube.py#L132-L135" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def analytics_engines(self):
    &#34;&#34;&#34;Returns the value of the analytics engines attributes.&#34;&#34;&#34;
    return self._analytics_engines</code></pre>
</details>
</dd>
<dt id="cvpysdk.datacube.datacube.Datacube.datasources"><code class="name">var <span class="ident">datasources</span></code></dt>
<dd>
<div class="desc"><p>Returns the instance of the Datasources class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/datacube/datacube.py#L137-L146" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def datasources(self):
    &#34;&#34;&#34;Returns the instance of the Datasources class.&#34;&#34;&#34;
    try:
        if self._datasources is None:
            self._datasources = Datasources(self)

        return self._datasources
    except AttributeError:
        return USER_LOGGED_OUT_MESSAGE</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.datacube.datacube.Datacube.get_jdbc_drivers"><code class="name flex">
<span>def <span class="ident">get_jdbc_drivers</span></span>(<span>self, analytics_engine)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the list all jdbc_drivers associated with the datacube.</p>
<h2 id="args">Args</h2>
<p>analytics_engine (str) &ndash; client name of analytics_engine</p>
<h2 id="returns">Returns</h2>
<p>list
-
consists of all jdbc_drivers in the datacube</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/datacube/datacube.py#L148-L185" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_jdbc_drivers(self, analytics_engine):
    &#34;&#34;&#34;Gets the list all jdbc_drivers associated with the datacube.

        Args:
            analytics_engine (str) -- client name of analytics_engine

        Returns:
            list    -   consists of all jdbc_drivers in the datacube

        Raises:
            SDKException:
                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not isinstance(analytics_engine, str):
        raise SDKException(&#39;Datacube&#39;, &#39;101&#39;)

    engine_index = (
        self.analytics_engines.index(engine)
        for engine in self.analytics_engines
        if engine[&#34;clientName&#34;] == analytics_engine
    ).next()

    self._GET_JDBC_DRIVERS = self._services[&#39;GET_JDBC_DRIVERS&#39;] % (
        self.analytics_engines[engine_index][&#34;cloudID&#34;]
    )

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._GET_JDBC_DRIVERS)

    if flag:
        if response.json() and &#39;drivers&#39; in response.json():
            return response.json()[&#39;drivers&#39;]
        else:
            raise SDKException(&#39;Datacube&#39;, &#39;103&#39;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.datacube.datacube.Datacube.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the datasources associated to the Datacube Engine.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/datacube/datacube.py#L187-L189" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the datasources associated to the Datacube Engine.&#34;&#34;&#34;
    self._datasources = None</code></pre>
</details>
</dd>
<dt id="cvpysdk.datacube.datacube.Datacube.refresh_engine"><code class="name flex">
<span>def <span class="ident">refresh_engine</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the Index server associated to the Datacube.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/datacube/datacube.py#L191-L193" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh_engine(self):
    &#34;&#34;&#34;Refresh the Index server associated to the Datacube.&#34;&#34;&#34;
    self._analytics_engines = self._get_analytics_engines()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.datacube" href="index.html">cvpysdk.datacube</a></code></li>
</ul>
</li>
<li><h3><a href="#header-variables">Global variables</a></h3>
<ul class="">
<li><code><a title="cvpysdk.datacube.datacube.USER_LOGGED_OUT_MESSAGE" href="#cvpysdk.datacube.datacube.USER_LOGGED_OUT_MESSAGE">USER_LOGGED_OUT_MESSAGE</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.datacube.datacube.Datacube" href="#cvpysdk.datacube.datacube.Datacube">Datacube</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.datacube.datacube.Datacube.analytics_engines" href="#cvpysdk.datacube.datacube.Datacube.analytics_engines">analytics_engines</a></code></li>
<li><code><a title="cvpysdk.datacube.datacube.Datacube.datasources" href="#cvpysdk.datacube.datacube.Datacube.datasources">datasources</a></code></li>
<li><code><a title="cvpysdk.datacube.datacube.Datacube.get_jdbc_drivers" href="#cvpysdk.datacube.datacube.Datacube.get_jdbc_drivers">get_jdbc_drivers</a></code></li>
<li><code><a title="cvpysdk.datacube.datacube.Datacube.refresh" href="#cvpysdk.datacube.datacube.Datacube.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.datacube.datacube.Datacube.refresh_engine" href="#cvpysdk.datacube.datacube.Datacube.refresh_engine">refresh_engine</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>