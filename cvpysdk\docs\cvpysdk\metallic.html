<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.metallic API documentation</title>
<meta name="description" content="Main file for performing Metallic Integration steps with existing commcell
…" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.metallic</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing Metallic Integration steps with existing commcell .</p>
<p>This file has all the classes related to Metallic Integration Operations.</p>
<p>Metallic:
Class for representing all the metallic integration steps</p>
<h2 id="metallic">Metallic</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialize the Metallic class
instance for the commcell</p>
<p>_metallic_commcell_object()
&ndash;
returns the metallic commcell object</p>
<p>metallic_subscribe()
&ndash;
linking on metallic side</p>
<p>_cv_metallic_subscribe()
&ndash;
linking on commvault side</p>
<p>is_metallic_registered()
&ndash;
returns boolean value
true - if metallic is subscribed for a user
false - if metallic is not subscribed for a user</p>
<p>metallic_completed_solutions()
&ndash;
returns all the completed solutions on linked company of metalic</p>
<p>metallic_unsubscribe()
&ndash;
unlinking on metallic side</p>
<p>_cv_metallic_unsubscibe()
&ndash;
unlinking on commvault side</p>
<p>_get_eligible_metallic_commcells()
&ndash;
gets the eligible metallic commcells for the logged in user</p>
<p>Metallic instance Attributes:</p>
<pre><code>**cloudservices_details**       --  returns cloudServices details if metallic service is registered in
                                    onprem/ MSP commcell

**cloud_hostname**              --  returns cloud commcell hostname
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metallic.py#L1-L532" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing Metallic Integration steps with existing commcell .

This file has all the classes related to Metallic Integration Operations.

Metallic:      Class for representing all the metallic integration steps

Metallic:

    __init__(commcell_object)       --  initialize the Metallic class
                                            instance for the commcell

    _metallic_commcell_object()     --  returns the metallic commcell object

    metallic_subscribe()            --  linking on metallic side

    _cv_metallic_subscribe()        --  linking on commvault side

    is_metallic_registered()        --  returns boolean value
                                            true - if metallic is subscribed for a user
                                            false - if metallic is not subscribed for a user

    metallic_completed_solutions()        --  returns all the completed solutions on linked company of metalic

    metallic_unsubscribe()          --  unlinking on metallic side

    _cv_metallic_unsubscibe()       --  unlinking on commvault side

    _get_eligible_metallic_commcells()     --  gets the eligible metallic commcells for the logged in user


Metallic instance Attributes:

    **cloudservices_details**       --  returns cloudServices details if metallic service is registered in
                                        onprem/ MSP commcell

    **cloud_hostname**              --  returns cloud commcell hostname

&#34;&#34;&#34;

from .exception import SDKException
from .organization import Organization
from .security.user import User


class Metallic(object):
    &#34;&#34;&#34;Class for representing Metallic related operations.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Intializes object of the Metallic class.

            Args:
                commcell_object (object) -instance of the commcell class

            Returns:
                object - instance of the Metallic class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._update_response_ = self._commcell_object._update_response_
        self._metallic_details = None
        self._metallic_web_url = None
        self._metallic_obj = None
        self._cloudservices_details = None

    def _metallic_commcell_object(self, cloud_webconsole_hostname, cloud_username, cloud_password):
        &#34;&#34;&#34;Gets the metallic commcell object.

            Args:
                cloud_webconsole_hostname (str) -- hostname of the cloud
                cloud_username (str) -- username of the cloud
                cloud_password (str) -- password of the cloud

            Raises:
                SDKException:

                    if inputs are not valid

                    if failed to create the object

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(cloud_webconsole_hostname, str) and
                isinstance(cloud_username, str) and
                isinstance(cloud_password, str)):
            raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
        from cvpysdk.commcell import Commcell
        metallic_cell = self._get_eligible_metallic_commcells(cloud_username, cloud_webconsole_hostname)
        if (len(metallic_cell)) &gt; 0:
            cloud_webconsole_hostname = metallic_cell[0]
        self._metallic_obj = Commcell(cloud_webconsole_hostname, cloud_username, cloud_password)

    def metallic_subscribe(self, cloud_webconsole_hostname, cloud_username, cloud_password, msp_company_name=None):
        &#34;&#34;&#34;Adds a new Monitoring Policy to the Commcell.

            Args:
                cloud_webconsole_hostname (str) -- hostname of the cloud
                cloud_username (str) -- username of the cloud
                cloud_password (str) -- password of the cloud
                msp_company_name (str or object) -- name of the company or company object
                    default: None

            Raises:
                SDKException:
                    if metallic is already subscribed

                    if inputs are not valid

                    if failed to subscribe to metallic

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        if not (isinstance(cloud_webconsole_hostname, str) and
                isinstance(cloud_username, str) and
                isinstance(cloud_password, str)):
            raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
        if msp_company_name and not (isinstance(msp_company_name, str)):
            raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
        self._metallic_commcell_object(cloud_webconsole_hostname, cloud_username, cloud_password)
        if msp_company_name and not isinstance(msp_company_name, Organization):
            msp_company_name = msp_company_name.lower()
            msp_company_obj = self._commcell_object.organizations.get(msp_company_name)
        request = {
            &#34;thirdpartyAppReq&#34;: {
                &#34;opType&#34;: 1,
                &#34;clientThirdPartyApps&#34;: [
                    {
                        &#34;isCloudApp&#34;: False,
                        &#34;appName&#34;: self._commcell_object.commserv_guid,
                        &#34;appDisplayName&#34;: self._commcell_object.commserv_name,
                        &#34;flags&#34;: 0,
                        &#34;isCloudServiceSubscription&#34;: True,
                        &#34;appType&#34;: 3,
                        &#34;isEnabled&#34;: True,
                        &#34;props&#34;: {
                            &#34;nameValues&#34;: [
                                {
                                    &#34;name&#34;: &#34;RedirectUrl&#34;,
                                    &#34;value&#34;: self._commcell_object.commserv_metadata[&#39;commserv_redirect_url&#39;]
                                },
                                {
                                    &#34;name&#34;: &#34;SP Certificate Data&#34;,
                                    &#34;value&#34;: self._commcell_object.commserv_metadata[&#39;commserv_certificate&#39;]
                                },
                                {
                                    &#34;name&#34;: &#34;CommcellId&#34;,
                                    &#34;value&#34;: str(self._commcell_object.commcell_id)
                                },
                                {
                                    &#34;name&#34;: &#34;Enable Sso Redirect&#34;,
                                    &#34;value&#34;: &#34;1&#34;
                                }
                            ]
                        }
                    }
                ]
            }
        }

        if msp_company_name:
            test_dict = {
                &#39;subscriberCompany&#39;: {
                    &#39;GUID&#39;: self._commcell_object.organizations.all_organizations_props[msp_company_name][&#39;GUID&#39;],
                    &#39;providerDomainName&#39;: msp_company_obj.organization_name
                }
            }
            request.update(test_dict)

        flag, response = self._metallic_obj._cvpysdk_object.make_request(
            &#39;POST&#39;, self._metallic_obj._services[&#39;METALLIC_LINKING&#39;], request)

        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                self._metallic_details = {}
                if (error_code == 2 or error_code == 0) and &#39;cloudServiceDetails&#39; in response.json():
                    self._metallic_details = response.json()[&#39;cloudServiceDetails&#39;]
                if error_code &lt; 0:
                    error_string = response.json()[&#39;errorMessage&#39;]
                    raise SDKException(
                        &#39;Metallic&#39;,
                        &#39;102&#39;,
                        &#39;Failed to create TPA\nError: &#34;{0}&#34;&#39;.format(
                            error_string
                        )
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if msp_company_name:
            self._cv_metallic_subscribe(msp_company_name)
        else:
            self._cv_metallic_subscribe()

    def _cv_metallic_subscribe(self, msp_company_name=None):
        &#34;&#34;&#34;Subscribing on on-prim or msp side.

            Args:
                msp_company_name (str) -- name of the company or company object
                    default: None

            Raises:
                SDKException:

                    if inputs are not valid

                    if failed to subscribe on on-prim or msp side

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        if msp_company_name and not (isinstance(msp_company_name, str)):
            raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
        if msp_company_name and not isinstance(msp_company_name, Organization):
            msp_company_obj = self._commcell_object.organizations.get(msp_company_name)
        request = {
            &#34;opType&#34;: 3,
            &#34;cloudServiceDetails&#34;: self._metallic_details
        }

        if msp_company_name:
            request[&#39;subscriberCompany&#39;] = {&#39;providerId&#39;: int(msp_company_obj.organization_id)}

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CV_METALLIC_LINKING&#39;], request)

        if flag:
            if response and response.json():
                error_code = response.json().get(&#39;error&#39;, {}).get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_string = response.json()[&#39;error&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Metallic&#39;,
                        &#39;102&#39;,
                        &#39;Failed to update linking details on onprim/msp: &#34;{0}&#34;&#39;.format(
                            error_string
                        )
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def is_metallic_registered(self):
        &#34;&#34;&#34;This function says whether metallic is registered for a user or not.

            Args:
                username (str) -- name of the user to which we need to check if metallic is registered

            Returns:
                Boolean --  True if metallic is returned in response
                            False if metallic is not returned in response

            Raises:
                SDKException:

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;METALLIC_REGISTERED&#39;]
        )

        if flag:
            if response.json():
                if &#39;cloudServices&#39; in response.json():
                    if response.json().get(&#39;cloudServices&#39;, [])[0].get(&#39;cloudService&#39;, {}).get(&#39;redirectUrl&#39;, {}):
                        self._metallic_web_url = \
                            response.json().get(&#39;cloudServices&#39;, [])[0].get(&#39;cloudService&#39;, {}).get(&#39;redirectUrl&#39;, {})
                        self._cloudservices_details = response.json()
                        return True
                return False
            else:
                return False
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def metallic_completed_solutions(self):
        &#34;&#34;&#34;This function returns the completed solutions for metallic.

            Returns:
                dict of completed solutions

            Raises:
                SDKException:

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;METALLIC_COMPLETED_SETUPS&#39;]
        )

        if flag:
            if response.json() and &#39;completedSetupsDetails&#39; in response.json():
                completed_solns = response.json()[&#39;completedSetupsDetails&#39;][0][&#39;completedSetups&#39;]
                return completed_solns
            else:
                raise SDKException(&#39;Metallic&#39;, &#39;102&#39;, &#39;No metallic solutions are configured&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def metallic_unsubscribe(self):
        &#34;&#34;&#34;This function is for unsubscribing metallic

            Raises:
                SDKException:

                    if failed to unsubcribe on metallic

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        saml_token_for_user = self._commcell_object.get_saml_token()
        user_obj = User(self._commcell_object, self._commcell_object.commcell_username)
        company_name = user_obj.user_company_name
        if company_name == &#39;commcell&#39;:
            company_name = None
        request = {
            &#34;cloudServiceDetails&#34;: {
                &#34;cloudService&#34;: {
                    &#34;redirectUrl&#34;: self._metallic_web_url if self.is_metallic_registered() else None,
                    &#34;appName&#34;: self._commcell_object.commserv_guid
                }
            }
        }

        if company_name:
            test_dict = {
                &#39;subscriberCompany&#39;: {
                    &#39;GUID&#39;: self._commcell_object.organizations.all_organizations_props[company_name][&#39;GUID&#39;],
                    &#39;providerId&#39;: self._commcell_object.organizations.all_organizations[company_name],
                    &#39;providerDomainName&#39;: company_name
                }
            }
            request.update(test_dict)

        url1 = self._metallic_web_url + &#34;/api/CloudService/Unsubscribe&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;POST&#39;,
            url=url1,
            payload=request,
            headers={&#39;Authtoken&#39;: saml_token_for_user,
                     &#39;Accept&#39;: &#39;application/json&#39;}
        )

        if flag:
            if response.json() and &#39;cloudServiceDetails&#39; in response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                error_message = response.json()[&#39;error&#39;][&#39;errorString&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Metallic&#39;, &#39;102&#39;, error_message)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self._cv_metallic_unsubscribe(user_obj)

    def _cv_metallic_unsubscribe(self, user):
        &#34;&#34;&#34;This function says whether metallic is registered for a user or not.

            Args:
                user (str or object) -- username or user object who has rights to unsubscribe on on-prim or msp side

            Returns:
                Boolean --  True if metallic is returned in response
                            False if metallic is not returned in response

            Raises:
                SDKException:

                    if failed to unsubscribe on on-prim or msp side

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;

        if not isinstance(user, User):
            user = self._commcell_object.users.get(self._commcell_object.commcell_username)
        company_name = user.user_company_name
        if company_name == &#39;commcell&#39;:
            company_name = None
        request = {
            &#34;opType&#34;: 4,
            &#34;cloudServiceDetails&#34;: {
                &#34;cloudService&#34;: {
                    &#34;redirectUrl&#34;: self._metallic_web_url if self.is_metallic_registered() else None
                }
            }
        }

        if company_name:
            request[&#39;subscriberCompany&#39;] = \
                {&#39;providerId&#39;: self._commcell_object.organizations.all_organizations[company_name]}

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CV_METALLIC_LINKING&#39;], request)

        if flag:
            if response.json():
                error_code = response.json().get(&#39;error&#39;, {}).get(&#39;errorCode&#39;)
                error_message = response.json().get(&#39;error&#39;, {}).get(&#39;errorString&#39;)
                if not error_code == 0:
                    raise SDKException(&#39;Metallic&#39;, &#39;102&#39;, error_message)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_eligible_metallic_commcells(self, login_name_or_email=None, cloud_webconsole_hostname=None):
        &#34;&#34;&#34;
        Gets the redirect metallic commcells based on login_name or email provided

        Args:

            login_name_or_email      (str)   -- Login name or email of the user

                default: current logged in user

            cloud_webconsole_hostname (str) -- cloud webconsole hostname

                default: None

        Raises:

            if the response is empty
            if there is no response

        Returns:

            list_of_metallic_commcells   (list)  -- list of metallic commcells

        &#34;&#34;&#34;

        login_name_or_email = login_name_or_email.lower()
        url1 = r&#39;http://{0}/webconsole/api/CloudService/Routing?username={1}&#39;.format(
            cloud_webconsole_hostname, login_name_or_email)
        flag, response = self._commcell_object._cvpysdk_object.make_request(method=&#39;GET&#39;, url=url1)
        if flag:
            if response.json() and &#39;cloudServiceCommcells&#39; in response.json():
                cloud_commcell_list = []
                for ser_comm in response.json()[&#39;cloudServiceCommcells&#39;]:
                    cloud_commcell_list.append(ser_comm[&#39;url&#39;])
                return cloud_commcell_list
            else:
                return []
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def cloud_hostname(self):
        &#34;&#34;&#34; Returns cloudhostname&#34;&#34;&#34;
        return self._cloud_hostname

    @cloud_hostname.setter
    def cloud_hostname(self, value):
        &#34;&#34;&#34; Sets cloud hostname &#34;&#34;&#34;
        self._cloud_hostname = value

    @property
    def cloudservices_details(self):
        &#34;&#34;&#34;
        Get cloudServices details if metallic service is registered in onprem/ MSP commcell

        Returns:
             cloudservices_details (dict) --
                {
                &#39;cloudServices&#39;:
                    [
                        {
                        &#39;associatedCompany&#39;:
                            {
                                &#39;companyAlias&#39;: &#39; &#39;,
                                &#39;GUID&#39;: &#39; &#39;
                            },
                        &#39;cloudService&#39;:
                            {
                                &#39;redirectUrl&#39;: &#39; &#39;,
                                &#39;commcellName&#39;: &#39; &#39;
                            }
                        }
                    ]
                }

        &#34;&#34;&#34;
        if self._cloudservices_details is None:
            self._commserv_metadata = self.is_metallic_registered()

        return self._cloudservices_details</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.metallic.Metallic"><code class="flex name class">
<span>class <span class="ident">Metallic</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing Metallic related operations.</p>
<p>Intializes object of the Metallic class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object) -instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Metallic class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metallic.py#L63-L532" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Metallic(object):
    &#34;&#34;&#34;Class for representing Metallic related operations.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Intializes object of the Metallic class.

            Args:
                commcell_object (object) -instance of the commcell class

            Returns:
                object - instance of the Metallic class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._update_response_ = self._commcell_object._update_response_
        self._metallic_details = None
        self._metallic_web_url = None
        self._metallic_obj = None
        self._cloudservices_details = None

    def _metallic_commcell_object(self, cloud_webconsole_hostname, cloud_username, cloud_password):
        &#34;&#34;&#34;Gets the metallic commcell object.

            Args:
                cloud_webconsole_hostname (str) -- hostname of the cloud
                cloud_username (str) -- username of the cloud
                cloud_password (str) -- password of the cloud

            Raises:
                SDKException:

                    if inputs are not valid

                    if failed to create the object

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(cloud_webconsole_hostname, str) and
                isinstance(cloud_username, str) and
                isinstance(cloud_password, str)):
            raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
        from cvpysdk.commcell import Commcell
        metallic_cell = self._get_eligible_metallic_commcells(cloud_username, cloud_webconsole_hostname)
        if (len(metallic_cell)) &gt; 0:
            cloud_webconsole_hostname = metallic_cell[0]
        self._metallic_obj = Commcell(cloud_webconsole_hostname, cloud_username, cloud_password)

    def metallic_subscribe(self, cloud_webconsole_hostname, cloud_username, cloud_password, msp_company_name=None):
        &#34;&#34;&#34;Adds a new Monitoring Policy to the Commcell.

            Args:
                cloud_webconsole_hostname (str) -- hostname of the cloud
                cloud_username (str) -- username of the cloud
                cloud_password (str) -- password of the cloud
                msp_company_name (str or object) -- name of the company or company object
                    default: None

            Raises:
                SDKException:
                    if metallic is already subscribed

                    if inputs are not valid

                    if failed to subscribe to metallic

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        if not (isinstance(cloud_webconsole_hostname, str) and
                isinstance(cloud_username, str) and
                isinstance(cloud_password, str)):
            raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
        if msp_company_name and not (isinstance(msp_company_name, str)):
            raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
        self._metallic_commcell_object(cloud_webconsole_hostname, cloud_username, cloud_password)
        if msp_company_name and not isinstance(msp_company_name, Organization):
            msp_company_name = msp_company_name.lower()
            msp_company_obj = self._commcell_object.organizations.get(msp_company_name)
        request = {
            &#34;thirdpartyAppReq&#34;: {
                &#34;opType&#34;: 1,
                &#34;clientThirdPartyApps&#34;: [
                    {
                        &#34;isCloudApp&#34;: False,
                        &#34;appName&#34;: self._commcell_object.commserv_guid,
                        &#34;appDisplayName&#34;: self._commcell_object.commserv_name,
                        &#34;flags&#34;: 0,
                        &#34;isCloudServiceSubscription&#34;: True,
                        &#34;appType&#34;: 3,
                        &#34;isEnabled&#34;: True,
                        &#34;props&#34;: {
                            &#34;nameValues&#34;: [
                                {
                                    &#34;name&#34;: &#34;RedirectUrl&#34;,
                                    &#34;value&#34;: self._commcell_object.commserv_metadata[&#39;commserv_redirect_url&#39;]
                                },
                                {
                                    &#34;name&#34;: &#34;SP Certificate Data&#34;,
                                    &#34;value&#34;: self._commcell_object.commserv_metadata[&#39;commserv_certificate&#39;]
                                },
                                {
                                    &#34;name&#34;: &#34;CommcellId&#34;,
                                    &#34;value&#34;: str(self._commcell_object.commcell_id)
                                },
                                {
                                    &#34;name&#34;: &#34;Enable Sso Redirect&#34;,
                                    &#34;value&#34;: &#34;1&#34;
                                }
                            ]
                        }
                    }
                ]
            }
        }

        if msp_company_name:
            test_dict = {
                &#39;subscriberCompany&#39;: {
                    &#39;GUID&#39;: self._commcell_object.organizations.all_organizations_props[msp_company_name][&#39;GUID&#39;],
                    &#39;providerDomainName&#39;: msp_company_obj.organization_name
                }
            }
            request.update(test_dict)

        flag, response = self._metallic_obj._cvpysdk_object.make_request(
            &#39;POST&#39;, self._metallic_obj._services[&#39;METALLIC_LINKING&#39;], request)

        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                self._metallic_details = {}
                if (error_code == 2 or error_code == 0) and &#39;cloudServiceDetails&#39; in response.json():
                    self._metallic_details = response.json()[&#39;cloudServiceDetails&#39;]
                if error_code &lt; 0:
                    error_string = response.json()[&#39;errorMessage&#39;]
                    raise SDKException(
                        &#39;Metallic&#39;,
                        &#39;102&#39;,
                        &#39;Failed to create TPA\nError: &#34;{0}&#34;&#39;.format(
                            error_string
                        )
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if msp_company_name:
            self._cv_metallic_subscribe(msp_company_name)
        else:
            self._cv_metallic_subscribe()

    def _cv_metallic_subscribe(self, msp_company_name=None):
        &#34;&#34;&#34;Subscribing on on-prim or msp side.

            Args:
                msp_company_name (str) -- name of the company or company object
                    default: None

            Raises:
                SDKException:

                    if inputs are not valid

                    if failed to subscribe on on-prim or msp side

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        if msp_company_name and not (isinstance(msp_company_name, str)):
            raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
        if msp_company_name and not isinstance(msp_company_name, Organization):
            msp_company_obj = self._commcell_object.organizations.get(msp_company_name)
        request = {
            &#34;opType&#34;: 3,
            &#34;cloudServiceDetails&#34;: self._metallic_details
        }

        if msp_company_name:
            request[&#39;subscriberCompany&#39;] = {&#39;providerId&#39;: int(msp_company_obj.organization_id)}

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CV_METALLIC_LINKING&#39;], request)

        if flag:
            if response and response.json():
                error_code = response.json().get(&#39;error&#39;, {}).get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_string = response.json()[&#39;error&#39;][&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Metallic&#39;,
                        &#39;102&#39;,
                        &#39;Failed to update linking details on onprim/msp: &#34;{0}&#34;&#39;.format(
                            error_string
                        )
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def is_metallic_registered(self):
        &#34;&#34;&#34;This function says whether metallic is registered for a user or not.

            Args:
                username (str) -- name of the user to which we need to check if metallic is registered

            Returns:
                Boolean --  True if metallic is returned in response
                            False if metallic is not returned in response

            Raises:
                SDKException:

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;METALLIC_REGISTERED&#39;]
        )

        if flag:
            if response.json():
                if &#39;cloudServices&#39; in response.json():
                    if response.json().get(&#39;cloudServices&#39;, [])[0].get(&#39;cloudService&#39;, {}).get(&#39;redirectUrl&#39;, {}):
                        self._metallic_web_url = \
                            response.json().get(&#39;cloudServices&#39;, [])[0].get(&#39;cloudService&#39;, {}).get(&#39;redirectUrl&#39;, {})
                        self._cloudservices_details = response.json()
                        return True
                return False
            else:
                return False
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def metallic_completed_solutions(self):
        &#34;&#34;&#34;This function returns the completed solutions for metallic.

            Returns:
                dict of completed solutions

            Raises:
                SDKException:

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;METALLIC_COMPLETED_SETUPS&#39;]
        )

        if flag:
            if response.json() and &#39;completedSetupsDetails&#39; in response.json():
                completed_solns = response.json()[&#39;completedSetupsDetails&#39;][0][&#39;completedSetups&#39;]
                return completed_solns
            else:
                raise SDKException(&#39;Metallic&#39;, &#39;102&#39;, &#39;No metallic solutions are configured&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def metallic_unsubscribe(self):
        &#34;&#34;&#34;This function is for unsubscribing metallic

            Raises:
                SDKException:

                    if failed to unsubcribe on metallic

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        saml_token_for_user = self._commcell_object.get_saml_token()
        user_obj = User(self._commcell_object, self._commcell_object.commcell_username)
        company_name = user_obj.user_company_name
        if company_name == &#39;commcell&#39;:
            company_name = None
        request = {
            &#34;cloudServiceDetails&#34;: {
                &#34;cloudService&#34;: {
                    &#34;redirectUrl&#34;: self._metallic_web_url if self.is_metallic_registered() else None,
                    &#34;appName&#34;: self._commcell_object.commserv_guid
                }
            }
        }

        if company_name:
            test_dict = {
                &#39;subscriberCompany&#39;: {
                    &#39;GUID&#39;: self._commcell_object.organizations.all_organizations_props[company_name][&#39;GUID&#39;],
                    &#39;providerId&#39;: self._commcell_object.organizations.all_organizations[company_name],
                    &#39;providerDomainName&#39;: company_name
                }
            }
            request.update(test_dict)

        url1 = self._metallic_web_url + &#34;/api/CloudService/Unsubscribe&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;POST&#39;,
            url=url1,
            payload=request,
            headers={&#39;Authtoken&#39;: saml_token_for_user,
                     &#39;Accept&#39;: &#39;application/json&#39;}
        )

        if flag:
            if response.json() and &#39;cloudServiceDetails&#39; in response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                error_message = response.json()[&#39;error&#39;][&#39;errorString&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Metallic&#39;, &#39;102&#39;, error_message)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self._cv_metallic_unsubscribe(user_obj)

    def _cv_metallic_unsubscribe(self, user):
        &#34;&#34;&#34;This function says whether metallic is registered for a user or not.

            Args:
                user (str or object) -- username or user object who has rights to unsubscribe on on-prim or msp side

            Returns:
                Boolean --  True if metallic is returned in response
                            False if metallic is not returned in response

            Raises:
                SDKException:

                    if failed to unsubscribe on on-prim or msp side

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;

        if not isinstance(user, User):
            user = self._commcell_object.users.get(self._commcell_object.commcell_username)
        company_name = user.user_company_name
        if company_name == &#39;commcell&#39;:
            company_name = None
        request = {
            &#34;opType&#34;: 4,
            &#34;cloudServiceDetails&#34;: {
                &#34;cloudService&#34;: {
                    &#34;redirectUrl&#34;: self._metallic_web_url if self.is_metallic_registered() else None
                }
            }
        }

        if company_name:
            request[&#39;subscriberCompany&#39;] = \
                {&#39;providerId&#39;: self._commcell_object.organizations.all_organizations[company_name]}

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CV_METALLIC_LINKING&#39;], request)

        if flag:
            if response.json():
                error_code = response.json().get(&#39;error&#39;, {}).get(&#39;errorCode&#39;)
                error_message = response.json().get(&#39;error&#39;, {}).get(&#39;errorString&#39;)
                if not error_code == 0:
                    raise SDKException(&#39;Metallic&#39;, &#39;102&#39;, error_message)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_eligible_metallic_commcells(self, login_name_or_email=None, cloud_webconsole_hostname=None):
        &#34;&#34;&#34;
        Gets the redirect metallic commcells based on login_name or email provided

        Args:

            login_name_or_email      (str)   -- Login name or email of the user

                default: current logged in user

            cloud_webconsole_hostname (str) -- cloud webconsole hostname

                default: None

        Raises:

            if the response is empty
            if there is no response

        Returns:

            list_of_metallic_commcells   (list)  -- list of metallic commcells

        &#34;&#34;&#34;

        login_name_or_email = login_name_or_email.lower()
        url1 = r&#39;http://{0}/webconsole/api/CloudService/Routing?username={1}&#39;.format(
            cloud_webconsole_hostname, login_name_or_email)
        flag, response = self._commcell_object._cvpysdk_object.make_request(method=&#39;GET&#39;, url=url1)
        if flag:
            if response.json() and &#39;cloudServiceCommcells&#39; in response.json():
                cloud_commcell_list = []
                for ser_comm in response.json()[&#39;cloudServiceCommcells&#39;]:
                    cloud_commcell_list.append(ser_comm[&#39;url&#39;])
                return cloud_commcell_list
            else:
                return []
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def cloud_hostname(self):
        &#34;&#34;&#34; Returns cloudhostname&#34;&#34;&#34;
        return self._cloud_hostname

    @cloud_hostname.setter
    def cloud_hostname(self, value):
        &#34;&#34;&#34; Sets cloud hostname &#34;&#34;&#34;
        self._cloud_hostname = value

    @property
    def cloudservices_details(self):
        &#34;&#34;&#34;
        Get cloudServices details if metallic service is registered in onprem/ MSP commcell

        Returns:
             cloudservices_details (dict) --
                {
                &#39;cloudServices&#39;:
                    [
                        {
                        &#39;associatedCompany&#39;:
                            {
                                &#39;companyAlias&#39;: &#39; &#39;,
                                &#39;GUID&#39;: &#39; &#39;
                            },
                        &#39;cloudService&#39;:
                            {
                                &#39;redirectUrl&#39;: &#39; &#39;,
                                &#39;commcellName&#39;: &#39; &#39;
                            }
                        }
                    ]
                }

        &#34;&#34;&#34;
        if self._cloudservices_details is None:
            self._commserv_metadata = self.is_metallic_registered()

        return self._cloudservices_details</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.metallic.Metallic.cloud_hostname"><code class="name">var <span class="ident">cloud_hostname</span></code></dt>
<dd>
<div class="desc"><p>Returns cloudhostname</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metallic.py#L493-L496" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cloud_hostname(self):
    &#34;&#34;&#34; Returns cloudhostname&#34;&#34;&#34;
    return self._cloud_hostname</code></pre>
</details>
</dd>
<dt id="cvpysdk.metallic.Metallic.cloudservices_details"><code class="name">var <span class="ident">cloudservices_details</span></code></dt>
<dd>
<div class="desc"><p>Get cloudServices details if metallic service is registered in onprem/ MSP commcell</p>
<h2 id="returns">Returns</h2>
<p>cloudservices_details (dict) &ndash;
{
'cloudServices':
[
{
'associatedCompany':
{
'companyAlias': ' ',
'GUID': ' '
},
'cloudService':
{
'redirectUrl': ' ',
'commcellName': ' '
}
}
]
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metallic.py#L503-L532" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cloudservices_details(self):
    &#34;&#34;&#34;
    Get cloudServices details if metallic service is registered in onprem/ MSP commcell

    Returns:
         cloudservices_details (dict) --
            {
            &#39;cloudServices&#39;:
                [
                    {
                    &#39;associatedCompany&#39;:
                        {
                            &#39;companyAlias&#39;: &#39; &#39;,
                            &#39;GUID&#39;: &#39; &#39;
                        },
                    &#39;cloudService&#39;:
                        {
                            &#39;redirectUrl&#39;: &#39; &#39;,
                            &#39;commcellName&#39;: &#39; &#39;
                        }
                    }
                ]
            }

    &#34;&#34;&#34;
    if self._cloudservices_details is None:
        self._commserv_metadata = self.is_metallic_registered()

    return self._cloudservices_details</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.metallic.Metallic.is_metallic_registered"><code class="name flex">
<span>def <span class="ident">is_metallic_registered</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>This function says whether metallic is registered for a user or not.</p>
<h2 id="args">Args</h2>
<p>username (str) &ndash; name of the user to which we need to check if metallic is registered</p>
<h2 id="returns">Returns</h2>
<p>Boolean &ndash;
True if metallic is returned in response
False if metallic is not returned in response</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metallic.py#L273-L309" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def is_metallic_registered(self):
    &#34;&#34;&#34;This function says whether metallic is registered for a user or not.

        Args:
            username (str) -- name of the user to which we need to check if metallic is registered

        Returns:
            Boolean --  True if metallic is returned in response
                        False if metallic is not returned in response

        Raises:
            SDKException:

                if response is empty

                if response is not success


    &#34;&#34;&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;GET&#39;, self._commcell_object._services[&#39;METALLIC_REGISTERED&#39;]
    )

    if flag:
        if response.json():
            if &#39;cloudServices&#39; in response.json():
                if response.json().get(&#39;cloudServices&#39;, [])[0].get(&#39;cloudService&#39;, {}).get(&#39;redirectUrl&#39;, {}):
                    self._metallic_web_url = \
                        response.json().get(&#39;cloudServices&#39;, [])[0].get(&#39;cloudService&#39;, {}).get(&#39;redirectUrl&#39;, {})
                    self._cloudservices_details = response.json()
                    return True
            return False
        else:
            return False
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metallic.Metallic.metallic_completed_solutions"><code class="name flex">
<span>def <span class="ident">metallic_completed_solutions</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>This function returns the completed solutions for metallic.</p>
<h2 id="returns">Returns</h2>
<p>dict of completed solutions</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metallic.py#L311-L338" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def metallic_completed_solutions(self):
    &#34;&#34;&#34;This function returns the completed solutions for metallic.

        Returns:
            dict of completed solutions

        Raises:
            SDKException:

                if response is empty

                if response is not success


    &#34;&#34;&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;GET&#39;, self._commcell_object._services[&#39;METALLIC_COMPLETED_SETUPS&#39;]
    )

    if flag:
        if response.json() and &#39;completedSetupsDetails&#39; in response.json():
            completed_solns = response.json()[&#39;completedSetupsDetails&#39;][0][&#39;completedSetups&#39;]
            return completed_solns
        else:
            raise SDKException(&#39;Metallic&#39;, &#39;102&#39;, &#39;No metallic solutions are configured&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metallic.Metallic.metallic_subscribe"><code class="name flex">
<span>def <span class="ident">metallic_subscribe</span></span>(<span>self, cloud_webconsole_hostname, cloud_username, cloud_password, msp_company_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new Monitoring Policy to the Commcell.</p>
<h2 id="args">Args</h2>
<p>cloud_webconsole_hostname (str) &ndash; hostname of the cloud
cloud_username (str) &ndash; username of the cloud
cloud_password (str) &ndash; password of the cloud
msp_company_name (str or object) &ndash; name of the company or company object
default: None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if metallic is already subscribed</p>
<pre><code>if inputs are not valid

if failed to subscribe to metallic

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metallic.py#L113-L220" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def metallic_subscribe(self, cloud_webconsole_hostname, cloud_username, cloud_password, msp_company_name=None):
    &#34;&#34;&#34;Adds a new Monitoring Policy to the Commcell.

        Args:
            cloud_webconsole_hostname (str) -- hostname of the cloud
            cloud_username (str) -- username of the cloud
            cloud_password (str) -- password of the cloud
            msp_company_name (str or object) -- name of the company or company object
                default: None

        Raises:
            SDKException:
                if metallic is already subscribed

                if inputs are not valid

                if failed to subscribe to metallic

                if response is empty

                if response is not success


    &#34;&#34;&#34;
    if not (isinstance(cloud_webconsole_hostname, str) and
            isinstance(cloud_username, str) and
            isinstance(cloud_password, str)):
        raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
    if msp_company_name and not (isinstance(msp_company_name, str)):
        raise SDKException(&#39;Metallic&#39;, &#39;101&#39;)
    self._metallic_commcell_object(cloud_webconsole_hostname, cloud_username, cloud_password)
    if msp_company_name and not isinstance(msp_company_name, Organization):
        msp_company_name = msp_company_name.lower()
        msp_company_obj = self._commcell_object.organizations.get(msp_company_name)
    request = {
        &#34;thirdpartyAppReq&#34;: {
            &#34;opType&#34;: 1,
            &#34;clientThirdPartyApps&#34;: [
                {
                    &#34;isCloudApp&#34;: False,
                    &#34;appName&#34;: self._commcell_object.commserv_guid,
                    &#34;appDisplayName&#34;: self._commcell_object.commserv_name,
                    &#34;flags&#34;: 0,
                    &#34;isCloudServiceSubscription&#34;: True,
                    &#34;appType&#34;: 3,
                    &#34;isEnabled&#34;: True,
                    &#34;props&#34;: {
                        &#34;nameValues&#34;: [
                            {
                                &#34;name&#34;: &#34;RedirectUrl&#34;,
                                &#34;value&#34;: self._commcell_object.commserv_metadata[&#39;commserv_redirect_url&#39;]
                            },
                            {
                                &#34;name&#34;: &#34;SP Certificate Data&#34;,
                                &#34;value&#34;: self._commcell_object.commserv_metadata[&#39;commserv_certificate&#39;]
                            },
                            {
                                &#34;name&#34;: &#34;CommcellId&#34;,
                                &#34;value&#34;: str(self._commcell_object.commcell_id)
                            },
                            {
                                &#34;name&#34;: &#34;Enable Sso Redirect&#34;,
                                &#34;value&#34;: &#34;1&#34;
                            }
                        ]
                    }
                }
            ]
        }
    }

    if msp_company_name:
        test_dict = {
            &#39;subscriberCompany&#39;: {
                &#39;GUID&#39;: self._commcell_object.organizations.all_organizations_props[msp_company_name][&#39;GUID&#39;],
                &#39;providerDomainName&#39;: msp_company_obj.organization_name
            }
        }
        request.update(test_dict)

    flag, response = self._metallic_obj._cvpysdk_object.make_request(
        &#39;POST&#39;, self._metallic_obj._services[&#39;METALLIC_LINKING&#39;], request)

    if flag:
        if response.json():
            error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
            self._metallic_details = {}
            if (error_code == 2 or error_code == 0) and &#39;cloudServiceDetails&#39; in response.json():
                self._metallic_details = response.json()[&#39;cloudServiceDetails&#39;]
            if error_code &lt; 0:
                error_string = response.json()[&#39;errorMessage&#39;]
                raise SDKException(
                    &#39;Metallic&#39;,
                    &#39;102&#39;,
                    &#39;Failed to create TPA\nError: &#34;{0}&#34;&#39;.format(
                        error_string
                    )
                )
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    if msp_company_name:
        self._cv_metallic_subscribe(msp_company_name)
    else:
        self._cv_metallic_subscribe()</code></pre>
</details>
</dd>
<dt id="cvpysdk.metallic.Metallic.metallic_unsubscribe"><code class="name flex">
<span>def <span class="ident">metallic_unsubscribe</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>This function is for unsubscribing metallic</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to unsubcribe on metallic

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metallic.py#L340-L397" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def metallic_unsubscribe(self):
    &#34;&#34;&#34;This function is for unsubscribing metallic

        Raises:
            SDKException:

                if failed to unsubcribe on metallic

                if response is empty

                if response is not success


    &#34;&#34;&#34;
    saml_token_for_user = self._commcell_object.get_saml_token()
    user_obj = User(self._commcell_object, self._commcell_object.commcell_username)
    company_name = user_obj.user_company_name
    if company_name == &#39;commcell&#39;:
        company_name = None
    request = {
        &#34;cloudServiceDetails&#34;: {
            &#34;cloudService&#34;: {
                &#34;redirectUrl&#34;: self._metallic_web_url if self.is_metallic_registered() else None,
                &#34;appName&#34;: self._commcell_object.commserv_guid
            }
        }
    }

    if company_name:
        test_dict = {
            &#39;subscriberCompany&#39;: {
                &#39;GUID&#39;: self._commcell_object.organizations.all_organizations_props[company_name][&#39;GUID&#39;],
                &#39;providerId&#39;: self._commcell_object.organizations.all_organizations[company_name],
                &#39;providerDomainName&#39;: company_name
            }
        }
        request.update(test_dict)

    url1 = self._metallic_web_url + &#34;/api/CloudService/Unsubscribe&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        method=&#39;POST&#39;,
        url=url1,
        payload=request,
        headers={&#39;Authtoken&#39;: saml_token_for_user,
                 &#39;Accept&#39;: &#39;application/json&#39;}
    )

    if flag:
        if response.json() and &#39;cloudServiceDetails&#39; in response.json():
            error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
            error_message = response.json()[&#39;error&#39;][&#39;errorString&#39;]
            if not error_code == 0:
                raise SDKException(&#39;Metallic&#39;, &#39;102&#39;, error_message)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    self._cv_metallic_unsubscribe(user_obj)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.metallic.Metallic" href="#cvpysdk.metallic.Metallic">Metallic</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.metallic.Metallic.cloud_hostname" href="#cvpysdk.metallic.Metallic.cloud_hostname">cloud_hostname</a></code></li>
<li><code><a title="cvpysdk.metallic.Metallic.cloudservices_details" href="#cvpysdk.metallic.Metallic.cloudservices_details">cloudservices_details</a></code></li>
<li><code><a title="cvpysdk.metallic.Metallic.is_metallic_registered" href="#cvpysdk.metallic.Metallic.is_metallic_registered">is_metallic_registered</a></code></li>
<li><code><a title="cvpysdk.metallic.Metallic.metallic_completed_solutions" href="#cvpysdk.metallic.Metallic.metallic_completed_solutions">metallic_completed_solutions</a></code></li>
<li><code><a title="cvpysdk.metallic.Metallic.metallic_subscribe" href="#cvpysdk.metallic.Metallic.metallic_subscribe">metallic_subscribe</a></code></li>
<li><code><a title="cvpysdk.metallic.Metallic.metallic_unsubscribe" href="#cvpysdk.metallic.Metallic.metallic_unsubscribe">metallic_unsubscribe</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>