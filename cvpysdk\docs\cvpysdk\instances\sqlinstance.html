<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.sqlinstance API documentation</title>
<meta name="description" content="File for operating on a SQL Server Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.sqlinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a SQL Server Instance.</p>
<p>SQLServerInstance is the only class defined in this file.</p>
<p>SQLServerInstance: Derived class from Instance Base class, representing a sql server instance,
and to perform operations on that instance</p>
<h2 id="sqlserverinstance">Sqlserverinstance</h2>
<p>_get_instance_properties()
&ndash;
gets the instance related properties of SQL instance.</p>
<p>_get_instance_properties_json() &ndash;
gets all the instance related properties of SQL instance.</p>
<p>_restore_request_json()
&ndash;
returns the restore request json</p>
<p>_process_restore_response()
&ndash;
processes response received for the Restore request</p>
<p>_get_sql_restore_options()
&ndash;
returns the dict containing destination sql server names</p>
<p>_run_backup()
&ndash;
runs full backup for this subclients and appends the
job object to the return list</p>
<p>_process_browse_request()
&ndash;
processes response received for Browse request</p>
<p>_recoverypoint_request_json()
&ndash;
returns a json to be sent to server to create
a recovery point</p>
<p>_get_database_list()
&ndash;
gets list of databases and its properties</p>
<p>_process_recovery_point_request() &ndash;
starts the recovery point job and process
the response</p>
<p>_table_level_restore_request_json() &ndash;
returns a json to be sent to the server for
table level restore job</p>
<p>_get_ag_groups()
&ndash;
gets available Availability Groups from the primary replica and returns it</p>
<p>_get_ag_group_replicas()
&ndash;
gets replicas list from the Availability Group and returns it</p>
<p>get_recovery_points()
&ndash;
lists all the recovery points</p>
<p>backup()
&ndash;
runs full backup for all subclients associated
with this instance</p>
<p>browse()
&ndash;
gets the content of the backup for this instance</p>
<p>browse_in_time()
&ndash;
gets the content of the backup for this instance
in the time range specified</p>
<p>restore()
&ndash;
runs the restore job for specified</p>
<p>restore_to_destination_server() &ndash;
restores the database on destination server</p>
<p>create_recovery_point()
&ndash;
creates a recovery point on destination server</p>
<p>table_level_restore()
&ndash;
starts the table level restore job</p>
<p>mssql_instance_prop()
&ndash;
sets instance properties for the mssql instance</p>
<p>vss_option()
&ndash;
enables or disables VSS option on SQL instance</p>
<p>vdi_timeout()
&ndash;
sets the SQL VDI timeout value on SQL instance</p>
<p>impersonation()
&ndash;
sets impersonation on SQL instance with local system account or provided credentials</p>
<p>create_sql_ag()
&ndash;
creates a new SQL Availability Group client and instance</p>
<p>database_details()
&ndash;
gets the database details</p>
<p>SQLServerInstance Attributes:</p>
<pre><code>mssql_instance_prop     --  returns the mssql instance properties

ag_group_name           --  returns the Availability Group Name

ag_primary_replica      --  returns the Availability Group Primary Replica

ag_replicas_list        --  returns the Availability Group Replicas List

ag_listener_list        --  returns the Availability Group Listener List

database_list           --  returns the list of protected databases
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1-L1627" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a SQL Server Instance.

SQLServerInstance is the only class defined in this file.

SQLServerInstance: Derived class from Instance Base class, representing a sql server instance,
                       and to perform operations on that instance

SQLServerInstance:

    _get_instance_properties()      --  gets the instance related properties of SQL instance.

    _get_instance_properties_json() --  gets all the instance related properties of SQL instance.

    _restore_request_json()         --  returns the restore request json

    _process_restore_response()     --  processes response received for the Restore request

    _get_sql_restore_options()      --  returns the dict containing destination sql server names

    _run_backup()                   --  runs full backup for this subclients and appends the
    job object to the return list

    _process_browse_request()       --  processes response received for Browse request

    _recoverypoint_request_json()   --  returns a json to be sent to server to create
    a recovery point

    _get_database_list()            --  gets list of databases and its properties

    _process_recovery_point_request() --  starts the recovery point job and process
    the response

    _table_level_restore_request_json() --  returns a json to be sent to the server for
    table level restore job

    _get_ag_groups()    --  gets available Availability Groups from the primary replica and returns it

    _get_ag_group_replicas()    --  gets replicas list from the Availability Group and returns it

    get_recovery_points()           --  lists all the recovery points

    backup()                        --  runs full backup for all subclients associated
    with this instance

    browse()                        --  gets the content of the backup for this instance

    browse_in_time()                --  gets the content of the backup for this instance
    in the time range specified

    restore()                       --  runs the restore job for specified

    restore_to_destination_server() --  restores the database on destination server

    create_recovery_point()         --  creates a recovery point on destination server

    table_level_restore()           --  starts the table level restore job

    mssql_instance_prop()       --  sets instance properties for the mssql instance

    vss_option()        --  enables or disables VSS option on SQL instance

    vdi_timeout()       --  sets the SQL VDI timeout value on SQL instance

    impersonation()     --  sets impersonation on SQL instance with local system account or provided credentials

    create_sql_ag()     --  creates a new SQL Availability Group client and instance

    database_details()  --  gets the database details

SQLServerInstance Attributes:

    mssql_instance_prop     --  returns the mssql instance properties

    ag_group_name           --  returns the Availability Group Name

    ag_primary_replica      --  returns the Availability Group Primary Replica

    ag_replicas_list        --  returns the Availability Group Replicas List

    ag_listener_list        --  returns the Availability Group Listener List

    database_list           --  returns the list of protected databases

&#34;&#34;&#34;

import re
import time
import datetime
import threading
from base64 import b64encode

from ..instance import Instance
from ..exception import SDKException
from ..job import Job
from ..constants import SQLDefines
from ..schedules import Schedules
from ..schedules import SchedulePattern


class SQLServerInstance(Instance):
    &#34;&#34;&#34;Derived class from Instance Base class, representing a SQL Server instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    @property
    def ag_group_name(self):
        &#34;&#34;&#34;Returns the Availability Group Name&#34;&#34;&#34;
        return self._ag_group_name

    @property
    def ag_primary_replica(self):
        &#34;&#34;&#34;Returns the Availability Group Primary Replica&#34;&#34;&#34;
        return self._ag_primary_replica

    @property
    def ag_replicas_list(self):
        &#34;&#34;&#34;Returns the Availability Group Replicas List&#34;&#34;&#34;
        return self._ag_replicas_list

    @property
    def ag_listener_list(self):
        &#34;&#34;&#34;Returns the Availability Group Listener List&#34;&#34;&#34;
        return self._ag_listener_list

    @property
    def database_list(self):
        &#34;&#34;&#34;Returns the list of protected databases&#34;&#34;&#34;
        return self._get_database_list()

    @property
    def mssql_instance_prop(self):
        &#34;&#34;&#34; getter for sql server instance properties &#34;&#34;&#34;
        return self._mssql_instance_prop

    @mssql_instance_prop.setter
    def mssql_instance_prop(self, value):
        &#34;&#34;&#34;Setter for SQL server instance properties

            Args:
                value (list)  --  list of the category and properties to update on the instance

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Instance API
        &#34;&#34;&#34;
        category, prop = value

        self._set_instance_properties(category, prop)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        super(SQLServerInstance, self)._get_instance_properties()

        self._ag_group_name = None
        self._ag_primary_replica = None
        self._ag_replicas_list = []
        self._ag_group_listener_list = []

        self._mssql_instance_prop = self._properties.get(&#39;mssqlInstance&#39;, {})

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;INSTANCE&#39;] %self._instance_id + &#34;?propertyLevel=20&#34;
        )

        if flag:
            if response.json():
                self._mssql_instance_prop = response.json()[&#39;instanceProperties&#39;][0][&#39;mssqlInstance&#39;]

        if &#39;agProperties&#39; in self._mssql_instance_prop:
            self._ag_group_name = self.mssql_instance_prop.get(
                &#39;agProperties&#39;, {}).get(&#39;availabilityGroup&#39;, {}).get(&#39;name&#39;)
            self._ag_primary_replica = self.mssql_instance_prop.get(
                &#39;agProperties&#39;, {}).get(&#39;availabilityGroup&#39;, {}).get(&#39;primaryReplicaServerName&#39;)

            listener_list_tmp = []
            listener_list = self.mssql_instance_prop.get(
                &#39;agProperties&#39;, {}).get(&#39;availabilityGroup&#39;, {}).get(&#39;SQLAvailabilityGroupListenerList&#39;, {})
            for listener in listener_list:
                listener_list_tmp.append(listener[&#39;availabilityGroupListenerName&#39;])
            self._ag_listener_list = listener_list_tmp

            replica_list_tmp = []
            replica_list = self.mssql_instance_prop.get(
                &#39;agProperties&#39;, {}).get(&#39;SQLAvailabilityReplicasList&#39;, {})
            if replica_list:
                for replica in replica_list[&#39;SQLAvailabilityReplicasList&#39;]:
                    replica_dict = {
                        &#34;serverName&#34; : replica[&#39;name&#39;],
                        &#34;clientId&#34; : replica[&#39;replicaClient&#39;][&#39;clientId&#39;],
                        &#34;clientName&#34;: replica[&#39;replicaClient&#39;][&#39;clientName&#39;]
                    }
                    replica_list_tmp.append(replica_dict)
                self._ag_replicas_list = replica_list_tmp

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;get the all instance related properties of this instance.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;:
                {
                    &#34;instance&#34;: self._instance,
                    &#34;instanceActivityControl&#34;: self._instanceActivityControl,
                    &#34;mssqlInstance&#34;: self._mssql_instance_prop,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return instance_json

    def _get_database_list(self):
        &#34;&#34;&#34;Gets list of databases with corresponding database ids and last backup times

            Returns:
                dict - database names with details (database id and last backup time)

            Raises:
                SDKException:
                    if response is empty

        &#34;&#34;&#34;
        databases_details = []
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;SQL_DATABASE_LIST&#39;] %int(self.instance_id), None
        )
        if flag:
            response_json = response.json()
            if &#34;SqlDatabase&#34; in response_json:
                for database in response_json[&#39;SqlDatabase&#39;]:
                    database_name = database[&#39;dbName&#39;]
                    database_id = database[&#39;dbId&#39;]
                    backup_time = datetime.datetime.fromtimestamp(
                        int(database[&#39;bkpTime&#39;])
                    ).strftime(&#39;%d-%m-%Y %H:%M:%S&#39;)

                    temp = {
                        database_name: [database_id, backup_time]
                    }

                    databases_details.append(temp)
                return databases_details
            return None
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _restore_request_json(
            self,
            content_to_restore,
            restore_path=None,
            drop_connections_to_databse=False,
            overwrite=True,
            destination_instance=None,
            to_time=None,
            sql_restore_type=SQLDefines.DATABASE_RESTORE,
            sql_recover_type=SQLDefines.STATE_RECOVER,
            undo_path=None,
            restricted_user=None,
            **kwargs
    ):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                content_to_restore (list): databases list to restore

                restore_path (list, optional): list of dicts for restore paths of database files

                drop_connections_to_databse (bool, optional): drop connections to database during restore

                overwrite (bool, optional): overwrite database on restore

                destination_instance (str): restore databases to this sql instance

                to_time (int/str, optional): Restore to time. Can be integer value or string as &#39;yyyy-MM-dd HH:mm:ss&#39;.
                Defaults to None.

                sql_restore_type (str, optional): type of sql restore state
                (DATABASE_RESTORE, STEP_RESTORE, RECOVER_ONLY)

                sql_recover_type (str, optional): type of sql restore state
                (STATE_RECOVER, STATE_NORECOVER, STATE_STANDBY)

                undo_path (str, optional): file path for undo path for sql server standby restore

                restricted_user (bool, optional): Restore database in restricted user mode

            Keyword Args:
                point_in_time (int, optional): Time value to use as point in time restore

                schedule_pattern (dict): Schedule pattern to associate to the restore request

                hardware_revert (bool): Does hardware revert restore

                log_shipping (bool): Restores log backups on database in standby or no recovery state.

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;

        self._get_sql_restore_options(content_to_restore)

        if destination_instance is None:
            destination_instance = (self.instance_name).lower()

        if destination_instance not in self.destination_instances_dict:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;SQL Instance [{0}] not suitable for restore destination or does not exist.&#39;
                    .format(destination_instance)
            )

        destination_client_id = int(
            self.destination_instances_dict[destination_instance][&#39;clientId&#39;]
        )

        destination_instance_id = int(
            self.destination_instances_dict[destination_instance][&#39;instanceId&#39;]
        )

        point_in_time = kwargs.get(&#39;point_in_time&#39;, None)
        schedule_pattern = kwargs.get(&#39;schedule_pattern&#39;, None)
        hardware_revert = kwargs.get(&#39;hardware_revert&#39;, False)
        log_shipping = (
                kwargs.get(&#39;log_shipping&#39;, False) and
                       (sql_recover_type == SQLDefines.STATE_STANDBY or sql_recover_type == SQLDefines.STATE_NORECOVER)
        )

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [{
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self.instance_name
                }],
                &#34;task&#34;: {
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskType&#34;: 1
                },
                &#34;subTasks&#34;: [{
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 3,
                        &#34;operationType&#34;: 1001
                    },
                    &#34;options&#34;: {
                        &#34;restoreOptions&#34;: {
                            &#34;sqlServerRstOption&#34;: {
                                &#34;sqlRecoverType&#34;: sql_recover_type,
                                &#34;dropConnectionsToDatabase&#34;: drop_connections_to_databse,
                                &#34;overWrite&#34;: overwrite,
                                &#34;sqlRestoreType&#34;: sql_restore_type,
                                &#34;database&#34;: content_to_restore,
                                &#34;restoreSource&#34;: content_to_restore,
                                &#34;logShippingOnly&#34;: log_shipping
                            },
                            &#34;commonOptions&#34;: {
                                &#34;revert&#34;: hardware_revert
                            },
                            &#34;destination&#34;: {
                                &#34;destinationInstance&#34;: {
                                    &#34;clientId&#34;: destination_client_id,
                                    &#34;instanceName&#34;: destination_instance,
                                    &#34;instanceId&#34;: destination_instance_id
                                },
                                &#34;destClient&#34;: {
                                    &#34;clientId&#34;: destination_client_id
                                }
                            },
                            &#34;browseOption&#34;: {
                                &#34;timeZone&#34;: {
                                    &#34;TimeZoneName&#34;: self._agent_object._client_object.timezone
                                }
                            }
                        }
                    }
                }]
            }
        }

        if sql_recover_type == SQLDefines.STATE_STANDBY:
            if undo_path is not None:
                undo_path_dict = {
                    &#34;fileOption&#34;: {
                        &#34;mapFiles&#34;: {
                            &#34;renameFilesSuffix&#34;: undo_path
                        }
                    }
                }
                request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;].update(undo_path_dict)
            else:
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to set Undo Path for Standby Restore.&#39;)

        if restore_path is not None:
            restore_path_dict = {
                &#34;device&#34;:
                    restore_path
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;sqlServerRstOption&#39;]\
                .update(restore_path_dict)

        if restricted_user is not None:
            restricted_user_dict = {
                &#34;dbOnly&#34;:
                    restricted_user
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;sqlServerRstOption&#39;]\
                .update(restricted_user_dict)

        if point_in_time:
            to_time = point_in_time
            pit_dict = {
                &#34;pointOfTimeRst&#34;: True,
                &#34;pointInTime&#34;: {
                    &#34;time&#34;: point_in_time
                }
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;sqlServerRstOption&#39;]\
                .update(pit_dict)

        if to_time is not None:
            to_time_type = &#34;toTimeValue&#34;
            if isinstance(to_time, int):
                to_time_type = &#34;toTime&#34;
            to_time_dict = {
                &#34;timeRange&#34;: {
                    &#34;toTimeValue&#34;: to_time
                }
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;browseOption&#39;].update(to_time_dict)

        if schedule_pattern is not None:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        return request_json

    def _process_restore_response(self, request_json):
        &#34;&#34;&#34;Runs the CreateTask API with the request JSON provided for Restore,
            and returns the contents after parsing the response.

            Args:
                request_json (dict):  JSON request to run for the API

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if restore job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;RESTORE&#39;], request_json
        )

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    time.sleep(1)
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_sql_restore_options(self, content_to_restore):
        &#34;&#34;&#34;Runs the SQL/Restoreoptions API with the request JSON provided,
            and returns the contents after parsing the response.

            Args:
                content_to_restore (list):  Databases list to restore

            Returns:
                dict - dictionary consisting of the sql destination server options

            Raises:
                SDKException:
                    if failed to get SQL instances

                    if no instance exits on commcell

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        contents_dict = []

        for content in content_to_restore:
            database_dict = {
                &#34;databaseName&#34;: content
            }
            contents_dict.append(database_dict)

        request_json = {
            &#34;restoreDbType&#34;: 0,
            &#34;sourceInstanceId&#34;: int(self.instance_id),
            &#34;selectedDatabases&#34;: contents_dict
        }

        webservice = self._commcell_object._services[&#39;SQL_RESTORE_OPTIONS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#34;POST&#34;, webservice, request_json
        )

        self.destination_instances_dict = {}

        if flag:
            if response.json():
                if &#39;sqlDestinationInstances&#39; in response.json():
                    for instance in response.json()[&#39;sqlDestinationInstances&#39;]:
                        instances_dict = {
                            instance[&#39;genericEntity&#39;][&#39;instanceName&#39;].lower(): {
                                &#34;instanceId&#34;: int(instance[&#39;genericEntity&#39;][&#39;instanceId&#39;]),
                                &#34;clientId&#34;: int(instance[&#39;genericEntity&#39;][&#39;clientId&#39;])
                            }
                        }
                        self.destination_instances_dict.update(instances_dict)
                elif &#39;error&#39; in response.json():
                    if &#39;errorMessage&#39; in response.json()[&#39;error&#39;]:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, error_message)
                    else:
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists on commcell&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        return response.json()

    def _run_backup(self, subclient_name, return_list):
        &#34;&#34;&#34;Triggers full backup job for the given subclient, and appends its Job object to list
            The SDKExcpetion class instance is appended to the list,
            if any exception is raised while running the backup job for the Subclient.

            Args:
                subclient_name (str):  Name of the subclient to trigger the backup for

                return_list (list):  List to append the job object to
        &#34;&#34;&#34;
        try:
            job = self.subclients.get(subclient_name).backup(&#39;Full&#39;)
            if job:
                return_list.append(job)
        except SDKException as excp:
            return_list.append(excp)

    def _process_browse_request(self, browse_request, get_full_details=False):
        &#34;&#34;&#34;Runs the SQL Instance Browse API with the request JSON provided for the operation
            specified, and returns the contents after parsing the response.

            Args:
                browse_request (dict):  JSON request to be sent to Server

            Returns:
                list - list of all databases

                dict - database names along with details like backup created time
                           and database version

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, browse_request)

        full_result = []
        databases = []

        if flag:
            if response.json():
                if &#39;sqlDatabase&#39; in response.json():
                    # returns whole dict if requested
                    if get_full_details:
                        return response.json()[&#34;sqlDatabase&#34;]

                    for database in response.json()[&#39;sqlDatabase&#39;]:

                        database_name = database[&#39;databaseName&#39;]

                        created_time = datetime.datetime.fromtimestamp(
                            int(database[&#39;createdTime&#39;])
                        ).strftime(&#39;%d-%m-%Y %H:%M:%S&#39;)

                        version = database[&#39;version&#39;]

                        temp = {
                            database_name: [created_time, version]
                        }

                        databases.append(database_name)
                        full_result.append(temp)

                return databases, full_result
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _recoverypoint_request_json(self,
                                    dbname,
                                    expire_days=1,
                                    recovery_point_name=None,
                                    point_in_time=0,
                                    destination_instance=None,
                                    snap=False
                                    ):
        &#34;&#34;&#34;
            creates and returns a request json for the recovery point creation

            Args:
                dbname (str) -- database to be restored

                expire_days (int)   -- days for which the database will be restored
                        default 1,. 1 day
                recovery_point_name (str)  -- name of the recovery point to be created
                        default None. creates a db with db_name + &lt;timestamp&gt;

                point_in_time   (timestamp) -- unix time for the point in time recovery point creation
                        default 0.  performs restore to last backup

                destination_instance (str)  -- name of the destination instance in which recovery point is to be
                                                created.
                                default None. creates in the same instance

                snap    (bool)      -- If the recovery point to be created is for snap setup
                            default False
            returns:
                request_json (Dict) --   request json for create recovery points
        &#34;&#34;&#34;

        if recovery_point_name is None:
            timestamp = datetime.datetime.timestamp(datetime.datetime.now())
            recovery_point_name = dbname + str(int(timestamp))

        instance = self
        if destination_instance != self.instance_name:
            instance = SQLServerInstance(self._agent_object, destination_instance)

        # fetching db details
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#34;SQL_DATABASES&#34;] % dbname, None
        )
        if flag:
            response = response.json()
            db_id = response[&#34;SqlDatabase&#34;][0][&#34;dbId&#34;]
        else:
            raise SDKException(&#39;Response&#39;, 102, &#34;failed to fetch db details&#34;)

        # fetching full database details
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#34;SQL_DATABASE_DETAILS&#34;] %(self.instance_id, db_id), None
        )
        if flag:
            response = response.json()
            db_details = response[&#34;SqlDatabase&#34;][0]
        else:
            raise SDKException(&#39;Response&#39;, 102, &#34;failed to fetch db details&#34;)

        fullbackup_job = db_details[&#34;fBkpJob&#34;]
        if fullbackup_job is None:
            raise Exception(&#34;failed to get last full backup job details&#34;)

        job = self._commcell_object.job_controller.get(fullbackup_job)

        # retrieving the physical paths and logical file names
        restore_options = self._get_sql_restore_options([dbname])
        physical_files = []
        logical_files = []
        for files in restore_options[&#34;sqlDbdeviceItem&#34;]:
            physical_files.append(files[&#34;fileName&#34;])
            logical_files.append(files[&#34;logicalFileName&#34;])

        request_json = {
            &#34;opType&#34;: 0,
            &#34;session&#34;: {},
            &#34;queries&#34;: [
                {
                    &#34;type&#34;: 0,
                    &#34;queryId&#34;: &#34;0&#34;
                }
            ],
            &#34;mode&#34;: {
                &#34;mode&#34;: 3
            },
            &#34;advOptions&#34;: {
                &#34;copyPrecedence&#34;: 0,
                &#34;advConfig&#34;: {
                    &#34;extendedConfig&#34;: {
                        &#34;browseAdvConfigLiveBrowse&#34;: {
                            &#34;useISCSIMount&#34;: False
                        }
                    },
                    &#34;applicationMining&#34;: {
                        &#34;appType&#34;: 81,
                        &#34;agentVersion&#34;: 0,
                        &#34;isApplicationMiningReq&#34;: True,
                        &#34;browseInitReq&#34;: {
                            &#34;database&#34;: dbname,
                            &#34;bCreateRecoveryPoint&#34;: True,
                            &#34;destDatabase&#34;: recovery_point_name,
                            &#34;appMinType&#34;: 2 if not snap else 0,
                            &#34;expireDays&#34;: expire_days,
                            &#34;instance&#34;: {
                                &#34;clientId&#34;: instance.properties[&#34;instance&#34;][&#34;clientId&#34;],
                                &#34;instanceName&#34;: instance.instance_name,
                                &#34;instanceId&#34;: int(instance.instance_id),
                                &#34;applicationId&#34;: 81
                            },
                            &#34;miningJobs&#34;: [fullbackup_job],
                            &#34;client&#34;: {
                                &#34;clientId&#34;: self.properties[&#34;instance&#34;][&#34;clientId&#34;]
                            },
                            &#34;phyfileRename&#34;: physical_files,
                            &#34;logfileRename&#34;: logical_files,
                        }
                    }
                }
            },
            &#34;ma&#34;: {
                &#34;clientId&#34;: self.properties[&#34;instance&#34;][&#34;clientId&#34;]
            },
            &#34;options&#34;: {
                &#34;instantSend&#34;: True,
                &#34;skipIndexRestore&#34;: False
            },
            &#34;entity&#34;: {
                &#34;drivePoolId&#34;: 0,
                &#34;subclientId&#34;: job.details[&#34;jobDetail&#34;][&#34;generalInfo&#34;][&#34;subclient&#34;][&#34;subclientId&#34;],
                &#34;applicationId&#34;: 81,
                &#34;libraryId&#34;: job.details[&#34;jobDetail&#34;][&#34;generalInfo&#34;][&#34;mediaLibrary&#34;][&#34;libraryId&#34;],
                &#34;backupsetId&#34;: job.details[&#34;jobDetail&#34;][&#34;generalInfo&#34;][&#34;subclient&#34;][&#34;backupsetId&#34;],
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;clientId&#34;: self.properties[&#34;instance&#34;][&#34;clientId&#34;]
            },
            &#34;timeRange&#34;: {
                &#34;fromTime&#34;: 0,
                &#34;toTime&#34;: point_in_time
            }
        }

        return request_json

    def _process_recovery_point_request(self, request_json):
        &#34;&#34;&#34;
            process the create recovery job browse request
            Args:
                request_json (dict):  JSON request to run for the API

            Returns:
                object (Job) - instance of the Job class for this restore job

                recovery point Id (int) : id to uniquely access the recovery point

                dbname (str) - name of the db that is created.

            Raises:
                SDKException:
                    if restore job failed

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;BROWSE&#39;], request_json
        )

        if flag:
            response_json = response.json()
            if response_json:
                if &#34;browseResponses&#34; in response_json:
                    d = response_json[&#39;browseResponses&#39;][0][&#34;browseResult&#34;][&#34;advConfig&#34;][&#34;applicationMining&#34;][&#34;browseInitResp&#34;]
                    try:
                        return Job(self._commcell_object, d[&#34;recoveryPointJobID&#34;]), d[&#34;recoveryPointID&#34;], d[&#34;edbPath&#34;]

                    except Exception as msg:
                        # server code 102 response is empty or doesn&#39;t contain required parameters
                        raise SDKException(&#39;Instance&#39;, 102, msg)

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;create recovery point job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _table_level_restore_request_json(self,
                                          src_db,
                                          tables_to_restore,
                                          destination_db,
                                          rp_name,
                                          include_child_tables,
                                          include_parent_tables):
        &#34;&#34;&#34;Creates and returns a request json for table level restore

        Args:
            src_db(str) : Name of the source database

            tables_to_restore(list) : List of tables to restore

            destination_db(str) : Destination database name

            rp_name(str) : Name of the corresponding recovery point

            include_child_tables(bool) : Includes all child tables in restore.

            include_parent_tables(bool) : Includes all parent tables in restore.

        Returns:

            request_json(dict) : Request json for table level restore&#34;&#34;&#34;

        client_name = self._agent_object._client_object.client_name
        client_id = int(self._agent_object._client_object.client_id)
        instance_name = self.instance_name
        instance_id = int(self.instance_id)

        source_item = []
        for table in tables_to_restore:
            source_item.append(&#39;/&#39; + table)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;subclientId&#34;: -1,
                        &#34;copyId&#34;: 0,
                        &#34;applicationId&#34;: 81,
                        &#34;clientName&#34;: client_name,
                        &#34;backupsetId&#34;: -1,
                        &#34;instanceId&#34;: instance_id,
                        &#34;clientId&#34;: client_id,
                        &#34;instanceName&#34;: instance_name,
                        &#34;_type_&#34;: 5,
                        &#34;appName&#34;: self._agent_object.agent_name
                    }
                ],
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;admin&#34;,
                    &#34;sequenceNumber&#34;: 0,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;policyType&#34;: 0,
                    &#34;taskId&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;isEZOperation&#34;: False,
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 3,
                            &#34;operationType&#34;: 1001
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: {
                                    &#34;subClientBasedAnalytics&#34;: False
                                }
                            },
                            &#34;restoreOptions&#34;: {
                                &#34;virtualServerRstOption&#34;: {
                                    &#34;isBlockLevelReplication&#34;: False
                                },
                                &#34;sqlServerRstOption&#34;: {
                                    &#34;cloneEnv&#34;: False,
                                    &#34;ffgRestore&#34;: False,
                                    &#34;cloneResrvTimePeriod&#34;: 0,
                                    &#34;vSSBackup&#34;: False,
                                },
                                &#34;dbArchiveRestoreOptions&#34;: {
                                    &#34;restoreAllDependentTables&#34;: include_child_tables,
                                    &#34;isTableLevelRestore&#34;: True,
                                    &#34;destDatabaseName&#34;: destination_db,
                                    &#34;restoreToSourceDatabase&#34;: True,
                                    &#34;restoreToHistoryDatabase&#34;: False,
                                    &#34;restoreAllParentTables&#34;: include_parent_tables,
                                    &#34;databaseName&#34;: {
                                        &#34;clientId&#34;: client_id,
                                        &#34;instanceName&#34;: instance_name,
                                        &#34;instanceId&#34;: instance_id,
                                        &#34;applicationId&#34;: 81
                                    },
                                    &#34;sqlArchiveOptions&#34;: {
                                        &#34;sourceDBName&#34;: src_db,
                                        &#34;sourceDatabaseInfo&#34;: {
                                            &#34;dbName&#34;: rp_name,
                                            &#34;instance&#34;: {
                                                &#34;clientId&#34;: client_id,
                                                &#34;instanceName&#34;: instance_name,
                                                &#34;instanceId&#34;: instance_id,
                                                &#34;applicationId&#34;: 81
                                            }
                                        }
                                    }
                                },
                                &#34;browseOption&#34;: {
                                    &#34;listMedia&#34;: False,
                                    &#34;useExactIndex&#34;: False,
                                    &#34;noImage&#34;: True,
                                    &#34;commCellId&#34;: self._commcell_object.commcell_id,
                                    &#34;mediaOption&#34;: {
                                        &#34;useISCSIMount&#34;: False,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentId&#34;: 0,
                                            &#34;_type_&#34;: 11
                                        },
                                        &#34;library&#34;: {
                                            &#34;_type_&#34;: 9,
                                            &#34;libraryId&#34;: 0
                                        },
                                        &#34;copyPrecedence&#34;: {
                                            &#34;copyPrecedenceApplicable&#34;: False
                                        },
                                        &#34;drivePool&#34;: {
                                            &#34;_type_&#34;: 47,
                                            &#34;drivePoolId&#34;: 0
                                        }
                                    },
                                    &#34;backupset&#34;: {
                                        &#34;backupsetId&#34;: -1,
                                        &#34;clientId&#34;: client_id
                                    },
                                    &#34;timeRange&#34;: {}
                                },
                                &#34;commonOptions&#34;: {
                                    &#34;clusterDBBackedup&#34;: False,
                                    &#34;restoreToDisk&#34;: False,
                                    &#34;isDBArchiveRestore&#34;: True,
                                    &#34;copyToObjectStore&#34;: False,
                                    &#34;onePassRestore&#34;: False,
                                    &#34;syncRestore&#34;: False
                                },
                                &#34;destination&#34;: {
                                    &#34;destClient&#34;: {
                                        &#34;clientId&#34;: client_id,
                                        &#34;clientName&#34;: client_name
                                    }
                                },
                                &#34;fileOption&#34;: {
                                    &#34;sourceItem&#34;: source_item,
                                    &#34;browseFilters&#34;: [
                                        &#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&#34;
                                        &#34;&lt;databrowse_Query type=\&#34;0\&#34; queryId=\&#34;0\&#34; /&gt;&#34;
                                    ]
                                },
                                &#34;dbDataMaskingOptions&#34;: {
                                    &#34;isStandalone&#34;: False
                                }
                            },
                            &#34;commonOpts&#34;: {
                                &#34;notifyUserOnJobCompletion&#34;: False,
                                &#34;perfJobOpts&#34;: {
                                    &#34;rstPerfJobOpts&#34;: {
                                        &#34;mediaReadSpeed&#34;: False,
                                        &#34;pipelineTransmittingSpeed&#34;: False
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }
        return request_json

    def _get_ag_groups(self):
        &#34;&#34;&#34;Gets available Availability Groups from the primary replica and returns it.

            Returns:
                dict - dictionary consisting of the sql destination server options

            Raises:
                SDKException: if given AG group name does not exist for instance

        &#34;&#34;&#34;

        instance_id = int(self.instance_id)
        client_id = int(self.properties[&#39;instance&#39;][&#39;clientId&#39;])

        webservice = self._commcell_object._services[&#39;SQL_AG_GROUPS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#34;GET&#34;, webservice %(client_id, instance_id)
        )

        if flag:
            if response.json():
                if &#39;SQLAvailabilityGroupList&#39; in response.json():
                    return response.json()[&#39;SQLAvailabilityGroupList&#39;]
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No Availability Groups exist for given primary replica &#39;
                                                      &#39;or SQL services are down on target server.&#39;)

    def _get_ag_group_replicas(self, ag_group_name):
        &#34;&#34;&#34;Gets replicas list from the Availability Group and returns it.

            Args:
                ag_group_name (str)  --  name of the Availability Group

            Returns:
                dict - dictionary consisting of the replicas of the SQL AG group

            Raises:
                SDKException: if no replicas exist for given AG group

        &#34;&#34;&#34;

        instance_id = int(self.instance_id)
        client_id = int(self.properties[&#39;instance&#39;][&#39;clientId&#39;])

        webservice = self._commcell_object._services[&#39;SQL_AG_GROUP_REPLICAS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#34;GET&#34;, webservice %(client_id, instance_id, ag_group_name)
        )

        if flag:
            if response.json():
                if &#39;SQLAvailabilityReplicasList&#39; in response.json():
                    return response.json()
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No replicas exist for given Availability Group &#39;
                                                          &#39;or SQL services are down on target server.&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def backup(self):
        &#34;&#34;&#34;Run full backup job for all subclients in this instance.

            Returns:
                list - list containing the job objects for the full backup jobs started for
                           the subclients in the backupset
        &#34;&#34;&#34;
        return_list = []
        thread_list = []

        all_subclients = self.subclients._subclients

        if all_subclients:
            for subclient in all_subclients:
                thread = threading.Thread(
                    target=self._run_backup, args=(subclient, return_list)
                )
                thread_list.append(thread)
                thread.start()

        for thread in thread_list:
            thread.join()

        return return_list

    def browse(self, get_full_details=False):
        &#34;&#34;&#34;Gets the list of the backed up databases for this instance.
            Args:
                get_full_details (bool) - if True returns dict with all databases
                            with last full backupjob details, default false
            Returns:
                list - list of all databases

                dict - database names along with details like backup created time
                           and database version

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_request = self._commcell_object._services[&#39;INSTANCE_BROWSE&#39;] % (
            self._agent_object._client_object.client_id, &#34;SQL&#34;, self.instance_id
        )

        return self._process_browse_request(browse_request, get_full_details=get_full_details)

    def browse_in_time(self, from_date=None, to_date=None, full_details=None):
        &#34;&#34;&#34;Gets the list of the backed up databases for this instance in the given time frame.

            Args:
                from_date (str/int): start date to browse for backups. Get backups from this date/time.
                Format: dd/MM/YYYY, dd/MM/YYYY HH:MM:SS or integer timestamp
                Gets contents from 01/01/1970 if not specified.  Defaults to None.

                to_date (str): end date to browse for backups. Get backups until this date/time.
                Format: dd/MM/YYYY, dd/MM/YYYY HH:MM:SS or integer timestamp
                Gets contents till current day if not specified.  Defaults to None.

                full_details (bool): flag whether to get full details on the databases in the browse

            Returns:
                list - list of all databases

                dict - database names along with details like backup created timen
                           and database version

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        regex_date = r&#34;\d{1,2}/\d{1,2}/\d{4}&#34;
        regex_datetime = regex_date + r&#34;\s+\d{2}:\d{2}:\d{2}&#34;
        if not isinstance(from_date, int):
            if from_date and bool(re.search(regex_datetime, from_date)):
                from_date = int(time.mktime(time.strptime(from_date, &#39;%d/%m/%Y %H:%M:%S&#39; )))
            elif from_date and bool(re.search(regex_date, from_date)):
                from_date = int(time.mktime(time.strptime(from_date, &#39;%d/%m/%Y&#39;)))
            else:
                from_date = 0
        if not isinstance(to_date, int):
            if to_date and bool(re.search(regex_datetime, to_date)):
                to_date = int(time.mktime(time.strptime(to_date, &#39;%d/%m/%Y %H:%M:%S&#39;)))
            elif to_date and bool(re.search(regex_date, to_date)):
                to_date = int(time.mktime(time.strptime(to_date, &#39;%d/%m/%Y&#39;)))
            else:
                to_date = int(time.time())

        browse_request = self._commcell_object._services[&#39;INSTANCE_BROWSE&#39;] % (
            self._agent_object._client_object.client_id, &#34;SQL&#34;, self.instance_id
        )

        browse_request += &#39;?fromTime={0}&amp;toTime={1}&#39;.format(from_date, to_date)

        return self._process_browse_request(browse_request, full_details)

    def restore(
            self,
            content_to_restore,
            drop_connections_to_databse=False,
            overwrite=True,
            restore_path=None,
            to_time=None,
            sql_restore_type=SQLDefines.DATABASE_RESTORE,
            sql_recover_type=SQLDefines.STATE_RECOVER,
            undo_path=None,
            restricted_user=None,
            destination_instance=None,
            **kwargs
    ):
        &#34;&#34;&#34;Restores the databases specified in the input paths list.

            Args:
                content_to_restore (list):  List of databases to restore.

                drop_connections_to_databse (bool):  Drop connections to database.  Defaults to False.

                overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

                restore_path (str):  Existing path on disk to restore.  Defaults to None.

                to_time (int/str):  Restore to time. Can be integer value or string as &#39;yyyy-MM-dd HH:mm:ss&#39;.
                Defaults to None.

                sql_recover_type (str):  Type of sql recovery state. (STATE_RECOVER, STATE_NORECOVER, STATE_STANDBY)
                Defaults to STATE_RECOVER.

                sql_restore_type (str):  Type of sql restore state.  (DATABASE_RESTORE, STEP_RESTORE, RECOVER_ONLY)
                Defaults to DATABASE_RESTORE.

                undo_path (str):  File path for undo path for sql standby restores.  Defaults to None.

                restricted_user (bool):  Restore database in restricted user mode.  Defaults to None.

                destination_instance (str):  Destination instance to restore too.  Defaults to None.

            Keyword Args:
                point_in_time (int, optional): Time value to use as point in time restore

                schedule_pattern (dict):    Please refer SchedulePattern.create_schedule in schedules.py
                for the types of patterns that can be sent

                hardware_revert (bool): Does hardware revert. Default value is False

                log_shipping (bool): Restores log backups on database in standby or no recovery state.

            Returns:
                object - instance of the Job class for this restore job
                object - instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if content_to_restore is not a list

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(content_to_restore, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if destination_instance is not None:
            destination_instance = destination_instance.lower()

        request_json = self._restore_request_json(
            content_to_restore,
            drop_connections_to_databse=drop_connections_to_databse,
            overwrite=overwrite,
            restore_path=restore_path,
            to_time=to_time,
            sql_restore_type=sql_restore_type,
            sql_recover_type=sql_recover_type,
            undo_path=undo_path,
            restricted_user=restricted_user,
            destination_instance=destination_instance,
            **kwargs
        )

        return self._process_restore_response(request_json)

    def restore_to_destination_server(
            self,
            content_to_restore,
            destination_server,
            drop_connections_to_databse=False,
            overwrite=True,
            restore_path=None):
        &#34;&#34;&#34;Restores the databases specified in the input paths list.

            Args:
                content_to_restore (list):  List of databases to restore.

                destination_server (str):  Destination server(instance) name.

                drop_connections_to_databse (bool): Drop connections to database.  Defaults to False.

                overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

                restore_path (str):  Existing path on disk to restore.  Default to None.

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if content_to_restore is not a list

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(content_to_restore, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._restore_request_json(
            content_to_restore,
            drop_connections_to_databse=drop_connections_to_databse,
            overwrite=overwrite,
            restore_path=restore_path,
            destination_instance=destination_server
        )

        return self._process_restore_response(request_json)

    def get_recovery_points(self):
        &#34;&#34;&#34;
        lists all the recovery points.

        returns:
            object (list) - list of all the recovery points and clones
    &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#34;SQL_CLONES&#34;], None
        )
        if flag:
            response_json = response.json()
            if &#34;rpObjectList&#34; in response_json:
                return response_json[&#34;total&#34;], response_json[&#34;rpObjectList&#34;]
            return 0, None
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;failed to get recovery points&#34;)

    def create_recovery_point(self,
                              database_name,
                              new_database_name=None,
                              destination_instance=None,
                              expire_days=1,
                              snap=False
                              ):
        &#34;&#34;&#34;stats a granular restore or recovery point job and creates a on demand restore of a database

        agrs:
            database_name (str) :   Name of the database for granular restore

            new_database_name (str) :   Name of the newly created database database
                    default: None   creates a database with original dbname+ &lt;TIMESTAMP&gt;

            destination_instance (str):  Destination server(instance) name.
                    default None .creates a database in the same instance

            expire_days (int) :    days for which the database will be available
                    default 1 day.

            snap (bool)     : create recovery point for the snap setup
                    dafault False

        returns:
             object (Job) : instance of the Job class for this restore job

             recovery point Id (int) : id to uniquely access the recovery point

            recovery_point_name (str) : name of the database created

        &#34;&#34;&#34;
        # write a wrapper over this to allow creating more than one recovery points at a time is neccessary
        if not isinstance(database_name, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if destination_instance is None:
            destination_instance = self.instance_name
        else:
            destination_instance = destination_instance.lower()

        recoverypoint_request = self._recoverypoint_request_json(
            database_name,
            expire_days=expire_days,
            recovery_point_name=new_database_name,
            destination_instance=destination_instance,
            snap=snap
        )
        return self._process_recovery_point_request(recoverypoint_request)

    def table_level_restore(self,
                            src_db_name,
                            tables_to_restore,
                            destination_db_name,
                            rp_name,
                            include_child_tables,
                            include_parent_tables):
        &#34;&#34;&#34;Starts a table level restore

        Args:

            src_db_name(str) : Name of the source database

            tables_to_restore(list) : List of tables to restore

            destination_db_name(str) : Destination database name

            rp_name(str) : Name of recovery point

            include_child_tables(bool) : Includes all child tables in restore.

            include_parent_tables(bool) : Includes all parent tables in restore.

        Returns:

            job : Instance of Job class for this restore job&#34;&#34;&#34;

        if not (isinstance(src_db_name, str)
                or isinstance(tables_to_restore, list)
                or isinstance(destination_db_name, str)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._table_level_restore_request_json(
            src_db_name,
            tables_to_restore,
            destination_db_name,
            rp_name,
            include_child_tables,
            include_parent_tables
        )

        return self._process_restore_response(request_json)

    def vss_option(self, value):
        &#34;&#34;&#34;Enables or disables VSS option on SQL instance

            Args:
                value (bool)  --  Boolean value whether to set VSS option on or off

        &#34;&#34;&#34;

        request_json = {
            &#34;useVss&#34;: value
        }

        self._set_instance_properties(&#34;_mssql_instance_prop&#34;, request_json)

    def vdi_timeout(self, value):
        &#34;&#34;&#34;Sets the SQL VDI timeout value on SQL instance

            Args:
                value (int)  --  value of vdi timeout for sql vdi operations

        &#34;&#34;&#34;

        request_json = {
            &#34;vDITimeOut&#34;: value
        }

        self._set_instance_properties(&#34;_mssql_instance_prop&#34;, request_json)

    def impersonation(self, enable, credentials=None):
        &#34;&#34;&#34;Sets impersonation on SQL instance with local system account or provided credentials.

            Args:
                enable (bool)  --  boolean value whether to set impersonation

                credentials (str, optional)   --  credentials to set for impersonation.
                Defaults to local system account if enabled is True and credential name not provided.

        &#34;&#34;&#34;

        if enable and credentials is None:
            impersonate_json = {
                &#34;overrideHigherLevelSettings&#34;: {
                    &#34;overrideGlobalAuthentication&#34;: True,
                    &#34;useLocalSystemAccount&#34;: True
                }
            }
        elif enable and credentials is not None:
            impersonate_json = {
                &#34;overrideHigherLevelSettings&#34;: {
                    &#34;overrideGlobalAuthentication&#34;: True,
                    &#34;useLocalSystemAccount&#34;: False
                },
                &#34;MSSQLCredentialinfo&#34;: {
                    &#34;credentialName&#34;: credentials
                }
            }
        else:
            impersonate_json = {
                &#34;overrideHigherLevelSettings&#34;: {
                    &#34;overrideGlobalAuthentication&#34;: True,
                    &#34;useLocalSystemAccount&#34;: False
                }
            }

        self._set_instance_properties(&#34;_mssql_instance_prop&#34;, impersonate_json)

    def create_sql_ag(self, client_name, ag_group_name, credentials=None):
        &#34;&#34;&#34;Creates a new SQL Availability Group client and instance.

            Args:
                client_name (str)  --  name to use for Availability Group client

                ag_group_name (str)   --  name of the Availability Group to create

                credentials (str, optional)   --  name of credentials to use as impersonation
                Default is no impersonation if credentials name is not provided.

            Returns:
                object - instance of the Instance class for the newly created Availability Group

            Raises:
                SDKException:
                    if Availability Group for given primary replica does not exist
                    if Availability Group client/instance fails to be created.
                    if Credentials for impersonation does not exist

        &#34;&#34;&#34;
        # If credentials passed, verify it exists
        if credentials:
            if not credentials in self._commcell_object.credentials.all_credentials:
                raise SDKException(
                    &#39;Credential&#39;, &#39;102&#39;, &#39;Credential name provided does not exist in the commcell.&#39;
                )

        # Get the available AG groups configured on SQL Instance
        ag_groups_resp = self._get_ag_groups()

        # Verify the provided AG group exists from available AG groups on primary replica
        if not any(ag[&#39;name&#39;] == ag_group_name for ag in ag_groups_resp):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Availability Group with provided name does not exist for given replica.&#39;
            )
        for ag_group in ag_groups_resp:
            if ag_group[&#39;name&#39;].lower() == ag_group_name.lower():
                ag_group_endpointURL = ag_group[&#39;endpointURL&#39;]
                ag_group_backupPref = ag_group[&#39;backupPreference&#39;]
                ag_primary_replica_server = ag_group[&#39;primaryReplicaServerName&#39;]

                ag_group_listener_list = []
                if &#39;SQLAvailabilityGroupListenerList&#39; in ag_group:
                    for listener in ag_group[&#39;SQLAvailabilityGroupListenerList&#39;]:
                        listener_details = {
                            &#39;availabilityGroupListenerName&#39;: listener[&#39;availabilityGroupListenerName&#39;]
                        }
                        ag_group_listener_list.append(listener_details)

        # Get the replicas from the provided AG group
        ag_group_replicas_resp = self._get_ag_group_replicas(ag_group_name)

        request_json = {
            &#34;App_CreatePseudoClientRequest&#34;: {
                &#34;clientInfo&#34;: {
                    &#34;clientType&#34;: 20,
                    &#34;mssqlagClientProperties&#34;: {
                        &#34;SQLServerInstance&#34;: {
                            &#34;clientId&#34;: int(self.properties[&#39;instance&#39;][&#39;clientId&#39;]),
                            &#34;instanceId&#34;: int(self.instance_id)
                        },
                        &#34;availabilityGroup&#34;: {
                            &#34;name&#34;: ag_group_name,
                            &#34;primaryReplicaServerName&#34;: ag_primary_replica_server,
                            &#34;backupPreference&#34;: ag_group_backupPref,
                            &#34;endpointURL&#34;: ag_group_endpointURL
                        },
                        &#34;SQLAvailabilityReplicasList&#34;: ag_group_replicas_resp,
                    },
                },
                &#34;entity&#34;: {
                    &#34;clientName&#34;: client_name
                }
            }
        }
        if ag_group_listener_list:
            request_json[&#39;App_CreatePseudoClientRequest&#39;][&#39;clientInfo&#39;][&#39;mssqlagClientProperties&#39;]\
            [&#39;availabilityGroup&#39;][&#39;SQLAvailabilityGroupListenerList&#39;] = ag_group_listener_list

        webservice = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, webservice, request_json)

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to create client\nError: &#34;{0}&#34;&#39;.format(error_string)

                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, o_str)
                    else:
                        self._commcell_object.refresh()

                        # Get newly created AG instance
                        ag_client = self._commcell_object.clients.get(
                            response.json()[&#39;response&#39;][&#39;entity&#39;][&#39;clientName&#39;]
                        )
                        agent = ag_client.agents.get(self._agent_object.agent_name)
                        if ag_group_listener_list:
                            ag_instance_name = ag_group_listener_list[0][&#39;availabilityGroupListenerName&#39;] \
                                               + &#39;/&#39; + ag_group_name
                        else:
                            ag_instance_name = ag_group_name
                        ag_instance = agent.instances.get(ag_instance_name)
                        if credentials is not None:
                            ag_instance.impersonation(True, credentials)

                        return ag_instance
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create client\nError: &#34;{0}&#34;&#39;.format(error_string)

                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def database_details(self, database_name):
        &#34;&#34;&#34;Gets the database details

            Args:
                database_name (str)  --  name of database to get database details

            Returns:
                dict - dictionary of database and details

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;SQL_DATABASE&#39;] %(self.instance_id, database_name), None
        )
        if flag:
            response_json = response.json()
            if &#39;SqlDatabase&#39; in response_json:
                for database in response_json[&#39;SqlDatabase&#39;]:
                    if database_name == database[&#39;dbName&#39;]:
                        return database
            return None
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;Failed to get the database details&#34;)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance"><code class="flex name class">
<span>class <span class="ident">SQLServerInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Instance Base class, representing a SQL Server instance,
and to perform operations on that Instance.</p>
<p>Initialise the instance object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L118-L1627" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SQLServerInstance(Instance):
    &#34;&#34;&#34;Derived class from Instance Base class, representing a SQL Server instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    @property
    def ag_group_name(self):
        &#34;&#34;&#34;Returns the Availability Group Name&#34;&#34;&#34;
        return self._ag_group_name

    @property
    def ag_primary_replica(self):
        &#34;&#34;&#34;Returns the Availability Group Primary Replica&#34;&#34;&#34;
        return self._ag_primary_replica

    @property
    def ag_replicas_list(self):
        &#34;&#34;&#34;Returns the Availability Group Replicas List&#34;&#34;&#34;
        return self._ag_replicas_list

    @property
    def ag_listener_list(self):
        &#34;&#34;&#34;Returns the Availability Group Listener List&#34;&#34;&#34;
        return self._ag_listener_list

    @property
    def database_list(self):
        &#34;&#34;&#34;Returns the list of protected databases&#34;&#34;&#34;
        return self._get_database_list()

    @property
    def mssql_instance_prop(self):
        &#34;&#34;&#34; getter for sql server instance properties &#34;&#34;&#34;
        return self._mssql_instance_prop

    @mssql_instance_prop.setter
    def mssql_instance_prop(self, value):
        &#34;&#34;&#34;Setter for SQL server instance properties

            Args:
                value (list)  --  list of the category and properties to update on the instance

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Instance API
        &#34;&#34;&#34;
        category, prop = value

        self._set_instance_properties(category, prop)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        super(SQLServerInstance, self)._get_instance_properties()

        self._ag_group_name = None
        self._ag_primary_replica = None
        self._ag_replicas_list = []
        self._ag_group_listener_list = []

        self._mssql_instance_prop = self._properties.get(&#39;mssqlInstance&#39;, {})

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;INSTANCE&#39;] %self._instance_id + &#34;?propertyLevel=20&#34;
        )

        if flag:
            if response.json():
                self._mssql_instance_prop = response.json()[&#39;instanceProperties&#39;][0][&#39;mssqlInstance&#39;]

        if &#39;agProperties&#39; in self._mssql_instance_prop:
            self._ag_group_name = self.mssql_instance_prop.get(
                &#39;agProperties&#39;, {}).get(&#39;availabilityGroup&#39;, {}).get(&#39;name&#39;)
            self._ag_primary_replica = self.mssql_instance_prop.get(
                &#39;agProperties&#39;, {}).get(&#39;availabilityGroup&#39;, {}).get(&#39;primaryReplicaServerName&#39;)

            listener_list_tmp = []
            listener_list = self.mssql_instance_prop.get(
                &#39;agProperties&#39;, {}).get(&#39;availabilityGroup&#39;, {}).get(&#39;SQLAvailabilityGroupListenerList&#39;, {})
            for listener in listener_list:
                listener_list_tmp.append(listener[&#39;availabilityGroupListenerName&#39;])
            self._ag_listener_list = listener_list_tmp

            replica_list_tmp = []
            replica_list = self.mssql_instance_prop.get(
                &#39;agProperties&#39;, {}).get(&#39;SQLAvailabilityReplicasList&#39;, {})
            if replica_list:
                for replica in replica_list[&#39;SQLAvailabilityReplicasList&#39;]:
                    replica_dict = {
                        &#34;serverName&#34; : replica[&#39;name&#39;],
                        &#34;clientId&#34; : replica[&#39;replicaClient&#39;][&#39;clientId&#39;],
                        &#34;clientName&#34;: replica[&#39;replicaClient&#39;][&#39;clientName&#39;]
                    }
                    replica_list_tmp.append(replica_dict)
                self._ag_replicas_list = replica_list_tmp

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;get the all instance related properties of this instance.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;:
                {
                    &#34;instance&#34;: self._instance,
                    &#34;instanceActivityControl&#34;: self._instanceActivityControl,
                    &#34;mssqlInstance&#34;: self._mssql_instance_prop,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return instance_json

    def _get_database_list(self):
        &#34;&#34;&#34;Gets list of databases with corresponding database ids and last backup times

            Returns:
                dict - database names with details (database id and last backup time)

            Raises:
                SDKException:
                    if response is empty

        &#34;&#34;&#34;
        databases_details = []
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;SQL_DATABASE_LIST&#39;] %int(self.instance_id), None
        )
        if flag:
            response_json = response.json()
            if &#34;SqlDatabase&#34; in response_json:
                for database in response_json[&#39;SqlDatabase&#39;]:
                    database_name = database[&#39;dbName&#39;]
                    database_id = database[&#39;dbId&#39;]
                    backup_time = datetime.datetime.fromtimestamp(
                        int(database[&#39;bkpTime&#39;])
                    ).strftime(&#39;%d-%m-%Y %H:%M:%S&#39;)

                    temp = {
                        database_name: [database_id, backup_time]
                    }

                    databases_details.append(temp)
                return databases_details
            return None
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _restore_request_json(
            self,
            content_to_restore,
            restore_path=None,
            drop_connections_to_databse=False,
            overwrite=True,
            destination_instance=None,
            to_time=None,
            sql_restore_type=SQLDefines.DATABASE_RESTORE,
            sql_recover_type=SQLDefines.STATE_RECOVER,
            undo_path=None,
            restricted_user=None,
            **kwargs
    ):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                content_to_restore (list): databases list to restore

                restore_path (list, optional): list of dicts for restore paths of database files

                drop_connections_to_databse (bool, optional): drop connections to database during restore

                overwrite (bool, optional): overwrite database on restore

                destination_instance (str): restore databases to this sql instance

                to_time (int/str, optional): Restore to time. Can be integer value or string as &#39;yyyy-MM-dd HH:mm:ss&#39;.
                Defaults to None.

                sql_restore_type (str, optional): type of sql restore state
                (DATABASE_RESTORE, STEP_RESTORE, RECOVER_ONLY)

                sql_recover_type (str, optional): type of sql restore state
                (STATE_RECOVER, STATE_NORECOVER, STATE_STANDBY)

                undo_path (str, optional): file path for undo path for sql server standby restore

                restricted_user (bool, optional): Restore database in restricted user mode

            Keyword Args:
                point_in_time (int, optional): Time value to use as point in time restore

                schedule_pattern (dict): Schedule pattern to associate to the restore request

                hardware_revert (bool): Does hardware revert restore

                log_shipping (bool): Restores log backups on database in standby or no recovery state.

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;

        self._get_sql_restore_options(content_to_restore)

        if destination_instance is None:
            destination_instance = (self.instance_name).lower()

        if destination_instance not in self.destination_instances_dict:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;SQL Instance [{0}] not suitable for restore destination or does not exist.&#39;
                    .format(destination_instance)
            )

        destination_client_id = int(
            self.destination_instances_dict[destination_instance][&#39;clientId&#39;]
        )

        destination_instance_id = int(
            self.destination_instances_dict[destination_instance][&#39;instanceId&#39;]
        )

        point_in_time = kwargs.get(&#39;point_in_time&#39;, None)
        schedule_pattern = kwargs.get(&#39;schedule_pattern&#39;, None)
        hardware_revert = kwargs.get(&#39;hardware_revert&#39;, False)
        log_shipping = (
                kwargs.get(&#39;log_shipping&#39;, False) and
                       (sql_recover_type == SQLDefines.STATE_STANDBY or sql_recover_type == SQLDefines.STATE_NORECOVER)
        )

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [{
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self.instance_name
                }],
                &#34;task&#34;: {
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskType&#34;: 1
                },
                &#34;subTasks&#34;: [{
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 3,
                        &#34;operationType&#34;: 1001
                    },
                    &#34;options&#34;: {
                        &#34;restoreOptions&#34;: {
                            &#34;sqlServerRstOption&#34;: {
                                &#34;sqlRecoverType&#34;: sql_recover_type,
                                &#34;dropConnectionsToDatabase&#34;: drop_connections_to_databse,
                                &#34;overWrite&#34;: overwrite,
                                &#34;sqlRestoreType&#34;: sql_restore_type,
                                &#34;database&#34;: content_to_restore,
                                &#34;restoreSource&#34;: content_to_restore,
                                &#34;logShippingOnly&#34;: log_shipping
                            },
                            &#34;commonOptions&#34;: {
                                &#34;revert&#34;: hardware_revert
                            },
                            &#34;destination&#34;: {
                                &#34;destinationInstance&#34;: {
                                    &#34;clientId&#34;: destination_client_id,
                                    &#34;instanceName&#34;: destination_instance,
                                    &#34;instanceId&#34;: destination_instance_id
                                },
                                &#34;destClient&#34;: {
                                    &#34;clientId&#34;: destination_client_id
                                }
                            },
                            &#34;browseOption&#34;: {
                                &#34;timeZone&#34;: {
                                    &#34;TimeZoneName&#34;: self._agent_object._client_object.timezone
                                }
                            }
                        }
                    }
                }]
            }
        }

        if sql_recover_type == SQLDefines.STATE_STANDBY:
            if undo_path is not None:
                undo_path_dict = {
                    &#34;fileOption&#34;: {
                        &#34;mapFiles&#34;: {
                            &#34;renameFilesSuffix&#34;: undo_path
                        }
                    }
                }
                request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;].update(undo_path_dict)
            else:
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to set Undo Path for Standby Restore.&#39;)

        if restore_path is not None:
            restore_path_dict = {
                &#34;device&#34;:
                    restore_path
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;sqlServerRstOption&#39;]\
                .update(restore_path_dict)

        if restricted_user is not None:
            restricted_user_dict = {
                &#34;dbOnly&#34;:
                    restricted_user
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;sqlServerRstOption&#39;]\
                .update(restricted_user_dict)

        if point_in_time:
            to_time = point_in_time
            pit_dict = {
                &#34;pointOfTimeRst&#34;: True,
                &#34;pointInTime&#34;: {
                    &#34;time&#34;: point_in_time
                }
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;sqlServerRstOption&#39;]\
                .update(pit_dict)

        if to_time is not None:
            to_time_type = &#34;toTimeValue&#34;
            if isinstance(to_time, int):
                to_time_type = &#34;toTime&#34;
            to_time_dict = {
                &#34;timeRange&#34;: {
                    &#34;toTimeValue&#34;: to_time
                }
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;browseOption&#39;].update(to_time_dict)

        if schedule_pattern is not None:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        return request_json

    def _process_restore_response(self, request_json):
        &#34;&#34;&#34;Runs the CreateTask API with the request JSON provided for Restore,
            and returns the contents after parsing the response.

            Args:
                request_json (dict):  JSON request to run for the API

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if restore job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;RESTORE&#39;], request_json
        )

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    time.sleep(1)
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_sql_restore_options(self, content_to_restore):
        &#34;&#34;&#34;Runs the SQL/Restoreoptions API with the request JSON provided,
            and returns the contents after parsing the response.

            Args:
                content_to_restore (list):  Databases list to restore

            Returns:
                dict - dictionary consisting of the sql destination server options

            Raises:
                SDKException:
                    if failed to get SQL instances

                    if no instance exits on commcell

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        contents_dict = []

        for content in content_to_restore:
            database_dict = {
                &#34;databaseName&#34;: content
            }
            contents_dict.append(database_dict)

        request_json = {
            &#34;restoreDbType&#34;: 0,
            &#34;sourceInstanceId&#34;: int(self.instance_id),
            &#34;selectedDatabases&#34;: contents_dict
        }

        webservice = self._commcell_object._services[&#39;SQL_RESTORE_OPTIONS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#34;POST&#34;, webservice, request_json
        )

        self.destination_instances_dict = {}

        if flag:
            if response.json():
                if &#39;sqlDestinationInstances&#39; in response.json():
                    for instance in response.json()[&#39;sqlDestinationInstances&#39;]:
                        instances_dict = {
                            instance[&#39;genericEntity&#39;][&#39;instanceName&#39;].lower(): {
                                &#34;instanceId&#34;: int(instance[&#39;genericEntity&#39;][&#39;instanceId&#39;]),
                                &#34;clientId&#34;: int(instance[&#39;genericEntity&#39;][&#39;clientId&#39;])
                            }
                        }
                        self.destination_instances_dict.update(instances_dict)
                elif &#39;error&#39; in response.json():
                    if &#39;errorMessage&#39; in response.json()[&#39;error&#39;]:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, error_message)
                    else:
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists on commcell&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        return response.json()

    def _run_backup(self, subclient_name, return_list):
        &#34;&#34;&#34;Triggers full backup job for the given subclient, and appends its Job object to list
            The SDKExcpetion class instance is appended to the list,
            if any exception is raised while running the backup job for the Subclient.

            Args:
                subclient_name (str):  Name of the subclient to trigger the backup for

                return_list (list):  List to append the job object to
        &#34;&#34;&#34;
        try:
            job = self.subclients.get(subclient_name).backup(&#39;Full&#39;)
            if job:
                return_list.append(job)
        except SDKException as excp:
            return_list.append(excp)

    def _process_browse_request(self, browse_request, get_full_details=False):
        &#34;&#34;&#34;Runs the SQL Instance Browse API with the request JSON provided for the operation
            specified, and returns the contents after parsing the response.

            Args:
                browse_request (dict):  JSON request to be sent to Server

            Returns:
                list - list of all databases

                dict - database names along with details like backup created time
                           and database version

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, browse_request)

        full_result = []
        databases = []

        if flag:
            if response.json():
                if &#39;sqlDatabase&#39; in response.json():
                    # returns whole dict if requested
                    if get_full_details:
                        return response.json()[&#34;sqlDatabase&#34;]

                    for database in response.json()[&#39;sqlDatabase&#39;]:

                        database_name = database[&#39;databaseName&#39;]

                        created_time = datetime.datetime.fromtimestamp(
                            int(database[&#39;createdTime&#39;])
                        ).strftime(&#39;%d-%m-%Y %H:%M:%S&#39;)

                        version = database[&#39;version&#39;]

                        temp = {
                            database_name: [created_time, version]
                        }

                        databases.append(database_name)
                        full_result.append(temp)

                return databases, full_result
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _recoverypoint_request_json(self,
                                    dbname,
                                    expire_days=1,
                                    recovery_point_name=None,
                                    point_in_time=0,
                                    destination_instance=None,
                                    snap=False
                                    ):
        &#34;&#34;&#34;
            creates and returns a request json for the recovery point creation

            Args:
                dbname (str) -- database to be restored

                expire_days (int)   -- days for which the database will be restored
                        default 1,. 1 day
                recovery_point_name (str)  -- name of the recovery point to be created
                        default None. creates a db with db_name + &lt;timestamp&gt;

                point_in_time   (timestamp) -- unix time for the point in time recovery point creation
                        default 0.  performs restore to last backup

                destination_instance (str)  -- name of the destination instance in which recovery point is to be
                                                created.
                                default None. creates in the same instance

                snap    (bool)      -- If the recovery point to be created is for snap setup
                            default False
            returns:
                request_json (Dict) --   request json for create recovery points
        &#34;&#34;&#34;

        if recovery_point_name is None:
            timestamp = datetime.datetime.timestamp(datetime.datetime.now())
            recovery_point_name = dbname + str(int(timestamp))

        instance = self
        if destination_instance != self.instance_name:
            instance = SQLServerInstance(self._agent_object, destination_instance)

        # fetching db details
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#34;SQL_DATABASES&#34;] % dbname, None
        )
        if flag:
            response = response.json()
            db_id = response[&#34;SqlDatabase&#34;][0][&#34;dbId&#34;]
        else:
            raise SDKException(&#39;Response&#39;, 102, &#34;failed to fetch db details&#34;)

        # fetching full database details
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#34;SQL_DATABASE_DETAILS&#34;] %(self.instance_id, db_id), None
        )
        if flag:
            response = response.json()
            db_details = response[&#34;SqlDatabase&#34;][0]
        else:
            raise SDKException(&#39;Response&#39;, 102, &#34;failed to fetch db details&#34;)

        fullbackup_job = db_details[&#34;fBkpJob&#34;]
        if fullbackup_job is None:
            raise Exception(&#34;failed to get last full backup job details&#34;)

        job = self._commcell_object.job_controller.get(fullbackup_job)

        # retrieving the physical paths and logical file names
        restore_options = self._get_sql_restore_options([dbname])
        physical_files = []
        logical_files = []
        for files in restore_options[&#34;sqlDbdeviceItem&#34;]:
            physical_files.append(files[&#34;fileName&#34;])
            logical_files.append(files[&#34;logicalFileName&#34;])

        request_json = {
            &#34;opType&#34;: 0,
            &#34;session&#34;: {},
            &#34;queries&#34;: [
                {
                    &#34;type&#34;: 0,
                    &#34;queryId&#34;: &#34;0&#34;
                }
            ],
            &#34;mode&#34;: {
                &#34;mode&#34;: 3
            },
            &#34;advOptions&#34;: {
                &#34;copyPrecedence&#34;: 0,
                &#34;advConfig&#34;: {
                    &#34;extendedConfig&#34;: {
                        &#34;browseAdvConfigLiveBrowse&#34;: {
                            &#34;useISCSIMount&#34;: False
                        }
                    },
                    &#34;applicationMining&#34;: {
                        &#34;appType&#34;: 81,
                        &#34;agentVersion&#34;: 0,
                        &#34;isApplicationMiningReq&#34;: True,
                        &#34;browseInitReq&#34;: {
                            &#34;database&#34;: dbname,
                            &#34;bCreateRecoveryPoint&#34;: True,
                            &#34;destDatabase&#34;: recovery_point_name,
                            &#34;appMinType&#34;: 2 if not snap else 0,
                            &#34;expireDays&#34;: expire_days,
                            &#34;instance&#34;: {
                                &#34;clientId&#34;: instance.properties[&#34;instance&#34;][&#34;clientId&#34;],
                                &#34;instanceName&#34;: instance.instance_name,
                                &#34;instanceId&#34;: int(instance.instance_id),
                                &#34;applicationId&#34;: 81
                            },
                            &#34;miningJobs&#34;: [fullbackup_job],
                            &#34;client&#34;: {
                                &#34;clientId&#34;: self.properties[&#34;instance&#34;][&#34;clientId&#34;]
                            },
                            &#34;phyfileRename&#34;: physical_files,
                            &#34;logfileRename&#34;: logical_files,
                        }
                    }
                }
            },
            &#34;ma&#34;: {
                &#34;clientId&#34;: self.properties[&#34;instance&#34;][&#34;clientId&#34;]
            },
            &#34;options&#34;: {
                &#34;instantSend&#34;: True,
                &#34;skipIndexRestore&#34;: False
            },
            &#34;entity&#34;: {
                &#34;drivePoolId&#34;: 0,
                &#34;subclientId&#34;: job.details[&#34;jobDetail&#34;][&#34;generalInfo&#34;][&#34;subclient&#34;][&#34;subclientId&#34;],
                &#34;applicationId&#34;: 81,
                &#34;libraryId&#34;: job.details[&#34;jobDetail&#34;][&#34;generalInfo&#34;][&#34;mediaLibrary&#34;][&#34;libraryId&#34;],
                &#34;backupsetId&#34;: job.details[&#34;jobDetail&#34;][&#34;generalInfo&#34;][&#34;subclient&#34;][&#34;backupsetId&#34;],
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;clientId&#34;: self.properties[&#34;instance&#34;][&#34;clientId&#34;]
            },
            &#34;timeRange&#34;: {
                &#34;fromTime&#34;: 0,
                &#34;toTime&#34;: point_in_time
            }
        }

        return request_json

    def _process_recovery_point_request(self, request_json):
        &#34;&#34;&#34;
            process the create recovery job browse request
            Args:
                request_json (dict):  JSON request to run for the API

            Returns:
                object (Job) - instance of the Job class for this restore job

                recovery point Id (int) : id to uniquely access the recovery point

                dbname (str) - name of the db that is created.

            Raises:
                SDKException:
                    if restore job failed

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;BROWSE&#39;], request_json
        )

        if flag:
            response_json = response.json()
            if response_json:
                if &#34;browseResponses&#34; in response_json:
                    d = response_json[&#39;browseResponses&#39;][0][&#34;browseResult&#34;][&#34;advConfig&#34;][&#34;applicationMining&#34;][&#34;browseInitResp&#34;]
                    try:
                        return Job(self._commcell_object, d[&#34;recoveryPointJobID&#34;]), d[&#34;recoveryPointID&#34;], d[&#34;edbPath&#34;]

                    except Exception as msg:
                        # server code 102 response is empty or doesn&#39;t contain required parameters
                        raise SDKException(&#39;Instance&#39;, 102, msg)

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;create recovery point job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _table_level_restore_request_json(self,
                                          src_db,
                                          tables_to_restore,
                                          destination_db,
                                          rp_name,
                                          include_child_tables,
                                          include_parent_tables):
        &#34;&#34;&#34;Creates and returns a request json for table level restore

        Args:
            src_db(str) : Name of the source database

            tables_to_restore(list) : List of tables to restore

            destination_db(str) : Destination database name

            rp_name(str) : Name of the corresponding recovery point

            include_child_tables(bool) : Includes all child tables in restore.

            include_parent_tables(bool) : Includes all parent tables in restore.

        Returns:

            request_json(dict) : Request json for table level restore&#34;&#34;&#34;

        client_name = self._agent_object._client_object.client_name
        client_id = int(self._agent_object._client_object.client_id)
        instance_name = self.instance_name
        instance_id = int(self.instance_id)

        source_item = []
        for table in tables_to_restore:
            source_item.append(&#39;/&#39; + table)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;subclientId&#34;: -1,
                        &#34;copyId&#34;: 0,
                        &#34;applicationId&#34;: 81,
                        &#34;clientName&#34;: client_name,
                        &#34;backupsetId&#34;: -1,
                        &#34;instanceId&#34;: instance_id,
                        &#34;clientId&#34;: client_id,
                        &#34;instanceName&#34;: instance_name,
                        &#34;_type_&#34;: 5,
                        &#34;appName&#34;: self._agent_object.agent_name
                    }
                ],
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;admin&#34;,
                    &#34;sequenceNumber&#34;: 0,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;policyType&#34;: 0,
                    &#34;taskId&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;isEZOperation&#34;: False,
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 3,
                            &#34;operationType&#34;: 1001
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: {
                                    &#34;subClientBasedAnalytics&#34;: False
                                }
                            },
                            &#34;restoreOptions&#34;: {
                                &#34;virtualServerRstOption&#34;: {
                                    &#34;isBlockLevelReplication&#34;: False
                                },
                                &#34;sqlServerRstOption&#34;: {
                                    &#34;cloneEnv&#34;: False,
                                    &#34;ffgRestore&#34;: False,
                                    &#34;cloneResrvTimePeriod&#34;: 0,
                                    &#34;vSSBackup&#34;: False,
                                },
                                &#34;dbArchiveRestoreOptions&#34;: {
                                    &#34;restoreAllDependentTables&#34;: include_child_tables,
                                    &#34;isTableLevelRestore&#34;: True,
                                    &#34;destDatabaseName&#34;: destination_db,
                                    &#34;restoreToSourceDatabase&#34;: True,
                                    &#34;restoreToHistoryDatabase&#34;: False,
                                    &#34;restoreAllParentTables&#34;: include_parent_tables,
                                    &#34;databaseName&#34;: {
                                        &#34;clientId&#34;: client_id,
                                        &#34;instanceName&#34;: instance_name,
                                        &#34;instanceId&#34;: instance_id,
                                        &#34;applicationId&#34;: 81
                                    },
                                    &#34;sqlArchiveOptions&#34;: {
                                        &#34;sourceDBName&#34;: src_db,
                                        &#34;sourceDatabaseInfo&#34;: {
                                            &#34;dbName&#34;: rp_name,
                                            &#34;instance&#34;: {
                                                &#34;clientId&#34;: client_id,
                                                &#34;instanceName&#34;: instance_name,
                                                &#34;instanceId&#34;: instance_id,
                                                &#34;applicationId&#34;: 81
                                            }
                                        }
                                    }
                                },
                                &#34;browseOption&#34;: {
                                    &#34;listMedia&#34;: False,
                                    &#34;useExactIndex&#34;: False,
                                    &#34;noImage&#34;: True,
                                    &#34;commCellId&#34;: self._commcell_object.commcell_id,
                                    &#34;mediaOption&#34;: {
                                        &#34;useISCSIMount&#34;: False,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentId&#34;: 0,
                                            &#34;_type_&#34;: 11
                                        },
                                        &#34;library&#34;: {
                                            &#34;_type_&#34;: 9,
                                            &#34;libraryId&#34;: 0
                                        },
                                        &#34;copyPrecedence&#34;: {
                                            &#34;copyPrecedenceApplicable&#34;: False
                                        },
                                        &#34;drivePool&#34;: {
                                            &#34;_type_&#34;: 47,
                                            &#34;drivePoolId&#34;: 0
                                        }
                                    },
                                    &#34;backupset&#34;: {
                                        &#34;backupsetId&#34;: -1,
                                        &#34;clientId&#34;: client_id
                                    },
                                    &#34;timeRange&#34;: {}
                                },
                                &#34;commonOptions&#34;: {
                                    &#34;clusterDBBackedup&#34;: False,
                                    &#34;restoreToDisk&#34;: False,
                                    &#34;isDBArchiveRestore&#34;: True,
                                    &#34;copyToObjectStore&#34;: False,
                                    &#34;onePassRestore&#34;: False,
                                    &#34;syncRestore&#34;: False
                                },
                                &#34;destination&#34;: {
                                    &#34;destClient&#34;: {
                                        &#34;clientId&#34;: client_id,
                                        &#34;clientName&#34;: client_name
                                    }
                                },
                                &#34;fileOption&#34;: {
                                    &#34;sourceItem&#34;: source_item,
                                    &#34;browseFilters&#34;: [
                                        &#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&#34;
                                        &#34;&lt;databrowse_Query type=\&#34;0\&#34; queryId=\&#34;0\&#34; /&gt;&#34;
                                    ]
                                },
                                &#34;dbDataMaskingOptions&#34;: {
                                    &#34;isStandalone&#34;: False
                                }
                            },
                            &#34;commonOpts&#34;: {
                                &#34;notifyUserOnJobCompletion&#34;: False,
                                &#34;perfJobOpts&#34;: {
                                    &#34;rstPerfJobOpts&#34;: {
                                        &#34;mediaReadSpeed&#34;: False,
                                        &#34;pipelineTransmittingSpeed&#34;: False
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }
        return request_json

    def _get_ag_groups(self):
        &#34;&#34;&#34;Gets available Availability Groups from the primary replica and returns it.

            Returns:
                dict - dictionary consisting of the sql destination server options

            Raises:
                SDKException: if given AG group name does not exist for instance

        &#34;&#34;&#34;

        instance_id = int(self.instance_id)
        client_id = int(self.properties[&#39;instance&#39;][&#39;clientId&#39;])

        webservice = self._commcell_object._services[&#39;SQL_AG_GROUPS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#34;GET&#34;, webservice %(client_id, instance_id)
        )

        if flag:
            if response.json():
                if &#39;SQLAvailabilityGroupList&#39; in response.json():
                    return response.json()[&#39;SQLAvailabilityGroupList&#39;]
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No Availability Groups exist for given primary replica &#39;
                                                      &#39;or SQL services are down on target server.&#39;)

    def _get_ag_group_replicas(self, ag_group_name):
        &#34;&#34;&#34;Gets replicas list from the Availability Group and returns it.

            Args:
                ag_group_name (str)  --  name of the Availability Group

            Returns:
                dict - dictionary consisting of the replicas of the SQL AG group

            Raises:
                SDKException: if no replicas exist for given AG group

        &#34;&#34;&#34;

        instance_id = int(self.instance_id)
        client_id = int(self.properties[&#39;instance&#39;][&#39;clientId&#39;])

        webservice = self._commcell_object._services[&#39;SQL_AG_GROUP_REPLICAS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#34;GET&#34;, webservice %(client_id, instance_id, ag_group_name)
        )

        if flag:
            if response.json():
                if &#39;SQLAvailabilityReplicasList&#39; in response.json():
                    return response.json()
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No replicas exist for given Availability Group &#39;
                                                          &#39;or SQL services are down on target server.&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def backup(self):
        &#34;&#34;&#34;Run full backup job for all subclients in this instance.

            Returns:
                list - list containing the job objects for the full backup jobs started for
                           the subclients in the backupset
        &#34;&#34;&#34;
        return_list = []
        thread_list = []

        all_subclients = self.subclients._subclients

        if all_subclients:
            for subclient in all_subclients:
                thread = threading.Thread(
                    target=self._run_backup, args=(subclient, return_list)
                )
                thread_list.append(thread)
                thread.start()

        for thread in thread_list:
            thread.join()

        return return_list

    def browse(self, get_full_details=False):
        &#34;&#34;&#34;Gets the list of the backed up databases for this instance.
            Args:
                get_full_details (bool) - if True returns dict with all databases
                            with last full backupjob details, default false
            Returns:
                list - list of all databases

                dict - database names along with details like backup created time
                           and database version

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_request = self._commcell_object._services[&#39;INSTANCE_BROWSE&#39;] % (
            self._agent_object._client_object.client_id, &#34;SQL&#34;, self.instance_id
        )

        return self._process_browse_request(browse_request, get_full_details=get_full_details)

    def browse_in_time(self, from_date=None, to_date=None, full_details=None):
        &#34;&#34;&#34;Gets the list of the backed up databases for this instance in the given time frame.

            Args:
                from_date (str/int): start date to browse for backups. Get backups from this date/time.
                Format: dd/MM/YYYY, dd/MM/YYYY HH:MM:SS or integer timestamp
                Gets contents from 01/01/1970 if not specified.  Defaults to None.

                to_date (str): end date to browse for backups. Get backups until this date/time.
                Format: dd/MM/YYYY, dd/MM/YYYY HH:MM:SS or integer timestamp
                Gets contents till current day if not specified.  Defaults to None.

                full_details (bool): flag whether to get full details on the databases in the browse

            Returns:
                list - list of all databases

                dict - database names along with details like backup created timen
                           and database version

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        regex_date = r&#34;\d{1,2}/\d{1,2}/\d{4}&#34;
        regex_datetime = regex_date + r&#34;\s+\d{2}:\d{2}:\d{2}&#34;
        if not isinstance(from_date, int):
            if from_date and bool(re.search(regex_datetime, from_date)):
                from_date = int(time.mktime(time.strptime(from_date, &#39;%d/%m/%Y %H:%M:%S&#39; )))
            elif from_date and bool(re.search(regex_date, from_date)):
                from_date = int(time.mktime(time.strptime(from_date, &#39;%d/%m/%Y&#39;)))
            else:
                from_date = 0
        if not isinstance(to_date, int):
            if to_date and bool(re.search(regex_datetime, to_date)):
                to_date = int(time.mktime(time.strptime(to_date, &#39;%d/%m/%Y %H:%M:%S&#39;)))
            elif to_date and bool(re.search(regex_date, to_date)):
                to_date = int(time.mktime(time.strptime(to_date, &#39;%d/%m/%Y&#39;)))
            else:
                to_date = int(time.time())

        browse_request = self._commcell_object._services[&#39;INSTANCE_BROWSE&#39;] % (
            self._agent_object._client_object.client_id, &#34;SQL&#34;, self.instance_id
        )

        browse_request += &#39;?fromTime={0}&amp;toTime={1}&#39;.format(from_date, to_date)

        return self._process_browse_request(browse_request, full_details)

    def restore(
            self,
            content_to_restore,
            drop_connections_to_databse=False,
            overwrite=True,
            restore_path=None,
            to_time=None,
            sql_restore_type=SQLDefines.DATABASE_RESTORE,
            sql_recover_type=SQLDefines.STATE_RECOVER,
            undo_path=None,
            restricted_user=None,
            destination_instance=None,
            **kwargs
    ):
        &#34;&#34;&#34;Restores the databases specified in the input paths list.

            Args:
                content_to_restore (list):  List of databases to restore.

                drop_connections_to_databse (bool):  Drop connections to database.  Defaults to False.

                overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

                restore_path (str):  Existing path on disk to restore.  Defaults to None.

                to_time (int/str):  Restore to time. Can be integer value or string as &#39;yyyy-MM-dd HH:mm:ss&#39;.
                Defaults to None.

                sql_recover_type (str):  Type of sql recovery state. (STATE_RECOVER, STATE_NORECOVER, STATE_STANDBY)
                Defaults to STATE_RECOVER.

                sql_restore_type (str):  Type of sql restore state.  (DATABASE_RESTORE, STEP_RESTORE, RECOVER_ONLY)
                Defaults to DATABASE_RESTORE.

                undo_path (str):  File path for undo path for sql standby restores.  Defaults to None.

                restricted_user (bool):  Restore database in restricted user mode.  Defaults to None.

                destination_instance (str):  Destination instance to restore too.  Defaults to None.

            Keyword Args:
                point_in_time (int, optional): Time value to use as point in time restore

                schedule_pattern (dict):    Please refer SchedulePattern.create_schedule in schedules.py
                for the types of patterns that can be sent

                hardware_revert (bool): Does hardware revert. Default value is False

                log_shipping (bool): Restores log backups on database in standby or no recovery state.

            Returns:
                object - instance of the Job class for this restore job
                object - instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if content_to_restore is not a list

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(content_to_restore, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if destination_instance is not None:
            destination_instance = destination_instance.lower()

        request_json = self._restore_request_json(
            content_to_restore,
            drop_connections_to_databse=drop_connections_to_databse,
            overwrite=overwrite,
            restore_path=restore_path,
            to_time=to_time,
            sql_restore_type=sql_restore_type,
            sql_recover_type=sql_recover_type,
            undo_path=undo_path,
            restricted_user=restricted_user,
            destination_instance=destination_instance,
            **kwargs
        )

        return self._process_restore_response(request_json)

    def restore_to_destination_server(
            self,
            content_to_restore,
            destination_server,
            drop_connections_to_databse=False,
            overwrite=True,
            restore_path=None):
        &#34;&#34;&#34;Restores the databases specified in the input paths list.

            Args:
                content_to_restore (list):  List of databases to restore.

                destination_server (str):  Destination server(instance) name.

                drop_connections_to_databse (bool): Drop connections to database.  Defaults to False.

                overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

                restore_path (str):  Existing path on disk to restore.  Default to None.

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if content_to_restore is not a list

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(content_to_restore, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._restore_request_json(
            content_to_restore,
            drop_connections_to_databse=drop_connections_to_databse,
            overwrite=overwrite,
            restore_path=restore_path,
            destination_instance=destination_server
        )

        return self._process_restore_response(request_json)

    def get_recovery_points(self):
        &#34;&#34;&#34;
        lists all the recovery points.

        returns:
            object (list) - list of all the recovery points and clones
    &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#34;SQL_CLONES&#34;], None
        )
        if flag:
            response_json = response.json()
            if &#34;rpObjectList&#34; in response_json:
                return response_json[&#34;total&#34;], response_json[&#34;rpObjectList&#34;]
            return 0, None
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;failed to get recovery points&#34;)

    def create_recovery_point(self,
                              database_name,
                              new_database_name=None,
                              destination_instance=None,
                              expire_days=1,
                              snap=False
                              ):
        &#34;&#34;&#34;stats a granular restore or recovery point job and creates a on demand restore of a database

        agrs:
            database_name (str) :   Name of the database for granular restore

            new_database_name (str) :   Name of the newly created database database
                    default: None   creates a database with original dbname+ &lt;TIMESTAMP&gt;

            destination_instance (str):  Destination server(instance) name.
                    default None .creates a database in the same instance

            expire_days (int) :    days for which the database will be available
                    default 1 day.

            snap (bool)     : create recovery point for the snap setup
                    dafault False

        returns:
             object (Job) : instance of the Job class for this restore job

             recovery point Id (int) : id to uniquely access the recovery point

            recovery_point_name (str) : name of the database created

        &#34;&#34;&#34;
        # write a wrapper over this to allow creating more than one recovery points at a time is neccessary
        if not isinstance(database_name, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if destination_instance is None:
            destination_instance = self.instance_name
        else:
            destination_instance = destination_instance.lower()

        recoverypoint_request = self._recoverypoint_request_json(
            database_name,
            expire_days=expire_days,
            recovery_point_name=new_database_name,
            destination_instance=destination_instance,
            snap=snap
        )
        return self._process_recovery_point_request(recoverypoint_request)

    def table_level_restore(self,
                            src_db_name,
                            tables_to_restore,
                            destination_db_name,
                            rp_name,
                            include_child_tables,
                            include_parent_tables):
        &#34;&#34;&#34;Starts a table level restore

        Args:

            src_db_name(str) : Name of the source database

            tables_to_restore(list) : List of tables to restore

            destination_db_name(str) : Destination database name

            rp_name(str) : Name of recovery point

            include_child_tables(bool) : Includes all child tables in restore.

            include_parent_tables(bool) : Includes all parent tables in restore.

        Returns:

            job : Instance of Job class for this restore job&#34;&#34;&#34;

        if not (isinstance(src_db_name, str)
                or isinstance(tables_to_restore, list)
                or isinstance(destination_db_name, str)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._table_level_restore_request_json(
            src_db_name,
            tables_to_restore,
            destination_db_name,
            rp_name,
            include_child_tables,
            include_parent_tables
        )

        return self._process_restore_response(request_json)

    def vss_option(self, value):
        &#34;&#34;&#34;Enables or disables VSS option on SQL instance

            Args:
                value (bool)  --  Boolean value whether to set VSS option on or off

        &#34;&#34;&#34;

        request_json = {
            &#34;useVss&#34;: value
        }

        self._set_instance_properties(&#34;_mssql_instance_prop&#34;, request_json)

    def vdi_timeout(self, value):
        &#34;&#34;&#34;Sets the SQL VDI timeout value on SQL instance

            Args:
                value (int)  --  value of vdi timeout for sql vdi operations

        &#34;&#34;&#34;

        request_json = {
            &#34;vDITimeOut&#34;: value
        }

        self._set_instance_properties(&#34;_mssql_instance_prop&#34;, request_json)

    def impersonation(self, enable, credentials=None):
        &#34;&#34;&#34;Sets impersonation on SQL instance with local system account or provided credentials.

            Args:
                enable (bool)  --  boolean value whether to set impersonation

                credentials (str, optional)   --  credentials to set for impersonation.
                Defaults to local system account if enabled is True and credential name not provided.

        &#34;&#34;&#34;

        if enable and credentials is None:
            impersonate_json = {
                &#34;overrideHigherLevelSettings&#34;: {
                    &#34;overrideGlobalAuthentication&#34;: True,
                    &#34;useLocalSystemAccount&#34;: True
                }
            }
        elif enable and credentials is not None:
            impersonate_json = {
                &#34;overrideHigherLevelSettings&#34;: {
                    &#34;overrideGlobalAuthentication&#34;: True,
                    &#34;useLocalSystemAccount&#34;: False
                },
                &#34;MSSQLCredentialinfo&#34;: {
                    &#34;credentialName&#34;: credentials
                }
            }
        else:
            impersonate_json = {
                &#34;overrideHigherLevelSettings&#34;: {
                    &#34;overrideGlobalAuthentication&#34;: True,
                    &#34;useLocalSystemAccount&#34;: False
                }
            }

        self._set_instance_properties(&#34;_mssql_instance_prop&#34;, impersonate_json)

    def create_sql_ag(self, client_name, ag_group_name, credentials=None):
        &#34;&#34;&#34;Creates a new SQL Availability Group client and instance.

            Args:
                client_name (str)  --  name to use for Availability Group client

                ag_group_name (str)   --  name of the Availability Group to create

                credentials (str, optional)   --  name of credentials to use as impersonation
                Default is no impersonation if credentials name is not provided.

            Returns:
                object - instance of the Instance class for the newly created Availability Group

            Raises:
                SDKException:
                    if Availability Group for given primary replica does not exist
                    if Availability Group client/instance fails to be created.
                    if Credentials for impersonation does not exist

        &#34;&#34;&#34;
        # If credentials passed, verify it exists
        if credentials:
            if not credentials in self._commcell_object.credentials.all_credentials:
                raise SDKException(
                    &#39;Credential&#39;, &#39;102&#39;, &#39;Credential name provided does not exist in the commcell.&#39;
                )

        # Get the available AG groups configured on SQL Instance
        ag_groups_resp = self._get_ag_groups()

        # Verify the provided AG group exists from available AG groups on primary replica
        if not any(ag[&#39;name&#39;] == ag_group_name for ag in ag_groups_resp):
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Availability Group with provided name does not exist for given replica.&#39;
            )
        for ag_group in ag_groups_resp:
            if ag_group[&#39;name&#39;].lower() == ag_group_name.lower():
                ag_group_endpointURL = ag_group[&#39;endpointURL&#39;]
                ag_group_backupPref = ag_group[&#39;backupPreference&#39;]
                ag_primary_replica_server = ag_group[&#39;primaryReplicaServerName&#39;]

                ag_group_listener_list = []
                if &#39;SQLAvailabilityGroupListenerList&#39; in ag_group:
                    for listener in ag_group[&#39;SQLAvailabilityGroupListenerList&#39;]:
                        listener_details = {
                            &#39;availabilityGroupListenerName&#39;: listener[&#39;availabilityGroupListenerName&#39;]
                        }
                        ag_group_listener_list.append(listener_details)

        # Get the replicas from the provided AG group
        ag_group_replicas_resp = self._get_ag_group_replicas(ag_group_name)

        request_json = {
            &#34;App_CreatePseudoClientRequest&#34;: {
                &#34;clientInfo&#34;: {
                    &#34;clientType&#34;: 20,
                    &#34;mssqlagClientProperties&#34;: {
                        &#34;SQLServerInstance&#34;: {
                            &#34;clientId&#34;: int(self.properties[&#39;instance&#39;][&#39;clientId&#39;]),
                            &#34;instanceId&#34;: int(self.instance_id)
                        },
                        &#34;availabilityGroup&#34;: {
                            &#34;name&#34;: ag_group_name,
                            &#34;primaryReplicaServerName&#34;: ag_primary_replica_server,
                            &#34;backupPreference&#34;: ag_group_backupPref,
                            &#34;endpointURL&#34;: ag_group_endpointURL
                        },
                        &#34;SQLAvailabilityReplicasList&#34;: ag_group_replicas_resp,
                    },
                },
                &#34;entity&#34;: {
                    &#34;clientName&#34;: client_name
                }
            }
        }
        if ag_group_listener_list:
            request_json[&#39;App_CreatePseudoClientRequest&#39;][&#39;clientInfo&#39;][&#39;mssqlagClientProperties&#39;]\
            [&#39;availabilityGroup&#39;][&#39;SQLAvailabilityGroupListenerList&#39;] = ag_group_listener_list

        webservice = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, webservice, request_json)

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to create client\nError: &#34;{0}&#34;&#39;.format(error_string)

                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, o_str)
                    else:
                        self._commcell_object.refresh()

                        # Get newly created AG instance
                        ag_client = self._commcell_object.clients.get(
                            response.json()[&#39;response&#39;][&#39;entity&#39;][&#39;clientName&#39;]
                        )
                        agent = ag_client.agents.get(self._agent_object.agent_name)
                        if ag_group_listener_list:
                            ag_instance_name = ag_group_listener_list[0][&#39;availabilityGroupListenerName&#39;] \
                                               + &#39;/&#39; + ag_group_name
                        else:
                            ag_instance_name = ag_group_name
                        ag_instance = agent.instances.get(ag_instance_name)
                        if credentials is not None:
                            ag_instance.impersonation(True, credentials)

                        return ag_instance
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create client\nError: &#34;{0}&#34;&#39;.format(error_string)

                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def database_details(self, database_name):
        &#34;&#34;&#34;Gets the database details

            Args:
                database_name (str)  --  name of database to get database details

            Returns:
                dict - dictionary of database and details

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;SQL_DATABASE&#39;] %(self.instance_id, database_name), None
        )
        if flag:
            response_json = response.json()
            if &#39;SqlDatabase&#39; in response_json:
                for database in response_json[&#39;SqlDatabase&#39;]:
                    if database_name == database[&#39;dbName&#39;]:
                        return database
            return None
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;Failed to get the database details&#34;)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.ag_group_name"><code class="name">var <span class="ident">ag_group_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the Availability Group Name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L122-L125" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ag_group_name(self):
    &#34;&#34;&#34;Returns the Availability Group Name&#34;&#34;&#34;
    return self._ag_group_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.ag_listener_list"><code class="name">var <span class="ident">ag_listener_list</span></code></dt>
<dd>
<div class="desc"><p>Returns the Availability Group Listener List</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L137-L140" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ag_listener_list(self):
    &#34;&#34;&#34;Returns the Availability Group Listener List&#34;&#34;&#34;
    return self._ag_listener_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.ag_primary_replica"><code class="name">var <span class="ident">ag_primary_replica</span></code></dt>
<dd>
<div class="desc"><p>Returns the Availability Group Primary Replica</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L127-L130" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ag_primary_replica(self):
    &#34;&#34;&#34;Returns the Availability Group Primary Replica&#34;&#34;&#34;
    return self._ag_primary_replica</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.ag_replicas_list"><code class="name">var <span class="ident">ag_replicas_list</span></code></dt>
<dd>
<div class="desc"><p>Returns the Availability Group Replicas List</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L132-L135" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ag_replicas_list(self):
    &#34;&#34;&#34;Returns the Availability Group Replicas List&#34;&#34;&#34;
    return self._ag_replicas_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.database_list"><code class="name">var <span class="ident">database_list</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of protected databases</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L142-L145" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def database_list(self):
    &#34;&#34;&#34;Returns the list of protected databases&#34;&#34;&#34;
    return self._get_database_list()</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.mssql_instance_prop"><code class="name">var <span class="ident">mssql_instance_prop</span></code></dt>
<dd>
<div class="desc"><p>getter for sql server instance properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L147-L150" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def mssql_instance_prop(self):
    &#34;&#34;&#34; getter for sql server instance properties &#34;&#34;&#34;
    return self._mssql_instance_prop</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Run full backup job for all subclients in this instance.</p>
<h2 id="returns">Returns</h2>
<p>list - list containing the job objects for the full backup jobs started for
the subclients in the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1079-L1102" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self):
    &#34;&#34;&#34;Run full backup job for all subclients in this instance.

        Returns:
            list - list containing the job objects for the full backup jobs started for
                       the subclients in the backupset
    &#34;&#34;&#34;
    return_list = []
    thread_list = []

    all_subclients = self.subclients._subclients

    if all_subclients:
        for subclient in all_subclients:
            thread = threading.Thread(
                target=self._run_backup, args=(subclient, return_list)
            )
            thread_list.append(thread)
            thread.start()

    for thread in thread_list:
        thread.join()

    return return_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, get_full_details=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the list of the backed up databases for this instance.</p>
<h2 id="args">Args</h2>
<p>get_full_details (bool) - if True returns dict with all databases
with last full backupjob details, default false</p>
<h2 id="returns">Returns</h2>
<p>list - list of all databases</p>
<p>dict - database names along with details like backup created time
and database version</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1104-L1125" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, get_full_details=False):
    &#34;&#34;&#34;Gets the list of the backed up databases for this instance.
        Args:
            get_full_details (bool) - if True returns dict with all databases
                        with last full backupjob details, default false
        Returns:
            list - list of all databases

            dict - database names along with details like backup created time
                       and database version

        Raises:
            SDKException:
                if response is empty

                if response is not success
    &#34;&#34;&#34;
    browse_request = self._commcell_object._services[&#39;INSTANCE_BROWSE&#39;] % (
        self._agent_object._client_object.client_id, &#34;SQL&#34;, self.instance_id
    )

    return self._process_browse_request(browse_request, get_full_details=get_full_details)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.browse_in_time"><code class="name flex">
<span>def <span class="ident">browse_in_time</span></span>(<span>self, from_date=None, to_date=None, full_details=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the list of the backed up databases for this instance in the given time frame.</p>
<h2 id="args">Args</h2>
<dl>
<dt>from_date (str/int): start date to browse for backups. Get backups from this date/time.</dt>
<dt><strong><code>Format</code></strong></dt>
<dd>dd/MM/YYYY, dd/MM/YYYY HH:MM:SS or integer timestamp</dd>
</dl>
<p>Gets contents from 01/01/1970 if not specified.
Defaults to None.</p>
<dl>
<dt><strong><code>to_date</code></strong> :&ensp;<code>str</code></dt>
<dd>end date to browse for backups. Get backups until this date/time.</dd>
<dt><strong><code>Format</code></strong></dt>
<dd>dd/MM/YYYY, dd/MM/YYYY HH:MM:SS or integer timestamp</dd>
</dl>
<p>Gets contents till current day if not specified.
Defaults to None.</p>
<dl>
<dt><strong><code>full_details</code></strong> :&ensp;<code>bool</code></dt>
<dd>flag whether to get full details on the databases in the browse</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>list - list of all databases</p>
<p>dict - database names along with details like backup created timen
and database version</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1127-L1176" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse_in_time(self, from_date=None, to_date=None, full_details=None):
    &#34;&#34;&#34;Gets the list of the backed up databases for this instance in the given time frame.

        Args:
            from_date (str/int): start date to browse for backups. Get backups from this date/time.
            Format: dd/MM/YYYY, dd/MM/YYYY HH:MM:SS or integer timestamp
            Gets contents from 01/01/1970 if not specified.  Defaults to None.

            to_date (str): end date to browse for backups. Get backups until this date/time.
            Format: dd/MM/YYYY, dd/MM/YYYY HH:MM:SS or integer timestamp
            Gets contents till current day if not specified.  Defaults to None.

            full_details (bool): flag whether to get full details on the databases in the browse

        Returns:
            list - list of all databases

            dict - database names along with details like backup created timen
                       and database version

        Raises:
            SDKException:
                if response is empty

                if response is not success
    &#34;&#34;&#34;
    regex_date = r&#34;\d{1,2}/\d{1,2}/\d{4}&#34;
    regex_datetime = regex_date + r&#34;\s+\d{2}:\d{2}:\d{2}&#34;
    if not isinstance(from_date, int):
        if from_date and bool(re.search(regex_datetime, from_date)):
            from_date = int(time.mktime(time.strptime(from_date, &#39;%d/%m/%Y %H:%M:%S&#39; )))
        elif from_date and bool(re.search(regex_date, from_date)):
            from_date = int(time.mktime(time.strptime(from_date, &#39;%d/%m/%Y&#39;)))
        else:
            from_date = 0
    if not isinstance(to_date, int):
        if to_date and bool(re.search(regex_datetime, to_date)):
            to_date = int(time.mktime(time.strptime(to_date, &#39;%d/%m/%Y %H:%M:%S&#39;)))
        elif to_date and bool(re.search(regex_date, to_date)):
            to_date = int(time.mktime(time.strptime(to_date, &#39;%d/%m/%Y&#39;)))
        else:
            to_date = int(time.time())

    browse_request = self._commcell_object._services[&#39;INSTANCE_BROWSE&#39;] % (
        self._agent_object._client_object.client_id, &#34;SQL&#34;, self.instance_id
    )

    browse_request += &#39;?fromTime={0}&amp;toTime={1}&#39;.format(from_date, to_date)

    return self._process_browse_request(browse_request, full_details)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.create_recovery_point"><code class="name flex">
<span>def <span class="ident">create_recovery_point</span></span>(<span>self, database_name, new_database_name=None, destination_instance=None, expire_days=1, snap=False)</span>
</code></dt>
<dd>
<div class="desc"><p>stats a granular restore or recovery point job and creates a on demand restore of a database</p>
<p>agrs:
database_name (str) :
Name of the database for granular restore</p>
<pre><code>new_database_name (str) :   Name of the newly created database database
        default: None   creates a database with original dbname+ &lt;TIMESTAMP&gt;

destination_instance (str):  Destination server(instance) name.
        default None .creates a database in the same instance

expire_days (int) :    days for which the database will be available
        default 1 day.

snap (bool)     : create recovery point for the snap setup
        dafault False
</code></pre>
<p>returns:
object (Job) : instance of the Job class for this restore job</p>
<pre><code> recovery point Id (int) : id to uniquely access the recovery point

recovery_point_name (str) : name of the database created
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1323-L1371" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_recovery_point(self,
                          database_name,
                          new_database_name=None,
                          destination_instance=None,
                          expire_days=1,
                          snap=False
                          ):
    &#34;&#34;&#34;stats a granular restore or recovery point job and creates a on demand restore of a database

    agrs:
        database_name (str) :   Name of the database for granular restore

        new_database_name (str) :   Name of the newly created database database
                default: None   creates a database with original dbname+ &lt;TIMESTAMP&gt;

        destination_instance (str):  Destination server(instance) name.
                default None .creates a database in the same instance

        expire_days (int) :    days for which the database will be available
                default 1 day.

        snap (bool)     : create recovery point for the snap setup
                dafault False

    returns:
         object (Job) : instance of the Job class for this restore job

         recovery point Id (int) : id to uniquely access the recovery point

        recovery_point_name (str) : name of the database created

    &#34;&#34;&#34;
    # write a wrapper over this to allow creating more than one recovery points at a time is neccessary
    if not isinstance(database_name, str):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if destination_instance is None:
        destination_instance = self.instance_name
    else:
        destination_instance = destination_instance.lower()

    recoverypoint_request = self._recoverypoint_request_json(
        database_name,
        expire_days=expire_days,
        recovery_point_name=new_database_name,
        destination_instance=destination_instance,
        snap=snap
    )
    return self._process_recovery_point_request(recoverypoint_request)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.create_sql_ag"><code class="name flex">
<span>def <span class="ident">create_sql_ag</span></span>(<span>self, client_name, ag_group_name, credentials=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates a new SQL Availability Group client and instance.</p>
<h2 id="args">Args</h2>
<p>client_name (str)
&ndash;
name to use for Availability Group client</p>
<p>ag_group_name (str)
&ndash;
name of the Availability Group to create</p>
<p>credentials (str, optional)
&ndash;
name of credentials to use as impersonation
Default is no impersonation if credentials name is not provided.</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class for the newly created Availability Group</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if Availability Group for given primary replica does not exist
if Availability Group client/instance fails to be created.
if Credentials for impersonation does not exist</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1482-L1605" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_sql_ag(self, client_name, ag_group_name, credentials=None):
    &#34;&#34;&#34;Creates a new SQL Availability Group client and instance.

        Args:
            client_name (str)  --  name to use for Availability Group client

            ag_group_name (str)   --  name of the Availability Group to create

            credentials (str, optional)   --  name of credentials to use as impersonation
            Default is no impersonation if credentials name is not provided.

        Returns:
            object - instance of the Instance class for the newly created Availability Group

        Raises:
            SDKException:
                if Availability Group for given primary replica does not exist
                if Availability Group client/instance fails to be created.
                if Credentials for impersonation does not exist

    &#34;&#34;&#34;
    # If credentials passed, verify it exists
    if credentials:
        if not credentials in self._commcell_object.credentials.all_credentials:
            raise SDKException(
                &#39;Credential&#39;, &#39;102&#39;, &#39;Credential name provided does not exist in the commcell.&#39;
            )

    # Get the available AG groups configured on SQL Instance
    ag_groups_resp = self._get_ag_groups()

    # Verify the provided AG group exists from available AG groups on primary replica
    if not any(ag[&#39;name&#39;] == ag_group_name for ag in ag_groups_resp):
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;Availability Group with provided name does not exist for given replica.&#39;
        )
    for ag_group in ag_groups_resp:
        if ag_group[&#39;name&#39;].lower() == ag_group_name.lower():
            ag_group_endpointURL = ag_group[&#39;endpointURL&#39;]
            ag_group_backupPref = ag_group[&#39;backupPreference&#39;]
            ag_primary_replica_server = ag_group[&#39;primaryReplicaServerName&#39;]

            ag_group_listener_list = []
            if &#39;SQLAvailabilityGroupListenerList&#39; in ag_group:
                for listener in ag_group[&#39;SQLAvailabilityGroupListenerList&#39;]:
                    listener_details = {
                        &#39;availabilityGroupListenerName&#39;: listener[&#39;availabilityGroupListenerName&#39;]
                    }
                    ag_group_listener_list.append(listener_details)

    # Get the replicas from the provided AG group
    ag_group_replicas_resp = self._get_ag_group_replicas(ag_group_name)

    request_json = {
        &#34;App_CreatePseudoClientRequest&#34;: {
            &#34;clientInfo&#34;: {
                &#34;clientType&#34;: 20,
                &#34;mssqlagClientProperties&#34;: {
                    &#34;SQLServerInstance&#34;: {
                        &#34;clientId&#34;: int(self.properties[&#39;instance&#39;][&#39;clientId&#39;]),
                        &#34;instanceId&#34;: int(self.instance_id)
                    },
                    &#34;availabilityGroup&#34;: {
                        &#34;name&#34;: ag_group_name,
                        &#34;primaryReplicaServerName&#34;: ag_primary_replica_server,
                        &#34;backupPreference&#34;: ag_group_backupPref,
                        &#34;endpointURL&#34;: ag_group_endpointURL
                    },
                    &#34;SQLAvailabilityReplicasList&#34;: ag_group_replicas_resp,
                },
            },
            &#34;entity&#34;: {
                &#34;clientName&#34;: client_name
            }
        }
    }
    if ag_group_listener_list:
        request_json[&#39;App_CreatePseudoClientRequest&#39;][&#39;clientInfo&#39;][&#39;mssqlagClientProperties&#39;]\
        [&#39;availabilityGroup&#39;][&#39;SQLAvailabilityGroupListenerList&#39;] = ag_group_listener_list

    webservice = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, webservice, request_json)

    if flag:
        if response.json():
            if &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]

                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    o_str = &#39;Failed to create client\nError: &#34;{0}&#34;&#39;.format(error_string)

                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, o_str)
                else:
                    self._commcell_object.refresh()

                    # Get newly created AG instance
                    ag_client = self._commcell_object.clients.get(
                        response.json()[&#39;response&#39;][&#39;entity&#39;][&#39;clientName&#39;]
                    )
                    agent = ag_client.agents.get(self._agent_object.agent_name)
                    if ag_group_listener_list:
                        ag_instance_name = ag_group_listener_list[0][&#39;availabilityGroupListenerName&#39;] \
                                           + &#39;/&#39; + ag_group_name
                    else:
                        ag_instance_name = ag_group_name
                    ag_instance = agent.instances.get(ag_instance_name)
                    if credentials is not None:
                        ag_instance.impersonation(True, credentials)

                    return ag_instance
            elif &#39;errorMessage&#39; in response.json():
                error_string = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Failed to create client\nError: &#34;{0}&#34;&#39;.format(error_string)

                raise SDKException(&#39;Client&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.database_details"><code class="name flex">
<span>def <span class="ident">database_details</span></span>(<span>self, database_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the database details</p>
<h2 id="args">Args</h2>
<p>database_name (str)
&ndash;
name of database to get database details</p>
<h2 id="returns">Returns</h2>
<p>dict - dictionary of database and details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1607-L1627" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def database_details(self, database_name):
    &#34;&#34;&#34;Gets the database details

        Args:
            database_name (str)  --  name of database to get database details

        Returns:
            dict - dictionary of database and details

    &#34;&#34;&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;GET&#39;, self._commcell_object._services[&#39;SQL_DATABASE&#39;] %(self.instance_id, database_name), None
    )
    if flag:
        response_json = response.json()
        if &#39;SqlDatabase&#39; in response_json:
            for database in response_json[&#39;SqlDatabase&#39;]:
                if database_name == database[&#39;dbName&#39;]:
                    return database
        return None
    raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;Failed to get the database details&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.get_recovery_points"><code class="name flex">
<span>def <span class="ident">get_recovery_points</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>lists all the recovery points.</p>
<p>returns:
object (list) - list of all the recovery points and clones</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1306-L1321" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_recovery_points(self):
    &#34;&#34;&#34;
    lists all the recovery points.

    returns:
        object (list) - list of all the recovery points and clones
&#34;&#34;&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;GET&#39;, self._commcell_object._services[&#34;SQL_CLONES&#34;], None
    )
    if flag:
        response_json = response.json()
        if &#34;rpObjectList&#34; in response_json:
            return response_json[&#34;total&#34;], response_json[&#34;rpObjectList&#34;]
        return 0, None
    raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;failed to get recovery points&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.impersonation"><code class="name flex">
<span>def <span class="ident">impersonation</span></span>(<span>self, enable, credentials=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets impersonation on SQL instance with local system account or provided credentials.</p>
<h2 id="args">Args</h2>
<p>enable (bool)
&ndash;
boolean value whether to set impersonation</p>
<p>credentials (str, optional)
&ndash;
credentials to set for impersonation.
Defaults to local system account if enabled is True and credential name not provided.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1444-L1480" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def impersonation(self, enable, credentials=None):
    &#34;&#34;&#34;Sets impersonation on SQL instance with local system account or provided credentials.

        Args:
            enable (bool)  --  boolean value whether to set impersonation

            credentials (str, optional)   --  credentials to set for impersonation.
            Defaults to local system account if enabled is True and credential name not provided.

    &#34;&#34;&#34;

    if enable and credentials is None:
        impersonate_json = {
            &#34;overrideHigherLevelSettings&#34;: {
                &#34;overrideGlobalAuthentication&#34;: True,
                &#34;useLocalSystemAccount&#34;: True
            }
        }
    elif enable and credentials is not None:
        impersonate_json = {
            &#34;overrideHigherLevelSettings&#34;: {
                &#34;overrideGlobalAuthentication&#34;: True,
                &#34;useLocalSystemAccount&#34;: False
            },
            &#34;MSSQLCredentialinfo&#34;: {
                &#34;credentialName&#34;: credentials
            }
        }
    else:
        impersonate_json = {
            &#34;overrideHigherLevelSettings&#34;: {
                &#34;overrideGlobalAuthentication&#34;: True,
                &#34;useLocalSystemAccount&#34;: False
            }
        }

    self._set_instance_properties(&#34;_mssql_instance_prop&#34;, impersonate_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.restore"><code class="name flex">
<span>def <span class="ident">restore</span></span>(<span>self, content_to_restore, drop_connections_to_databse=False, overwrite=True, restore_path=None, to_time=None, sql_restore_type='DATABASE_RESTORE', sql_recover_type='STATE_RECOVER', undo_path=None, restricted_user=None, destination_instance=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the databases specified in the input paths list.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>content_to_restore</code></strong> :&ensp;<code>list</code></dt>
<dd>List of databases to restore.</dd>
<dt><strong><code>drop_connections_to_databse</code></strong> :&ensp;<code>bool</code></dt>
<dd>Drop connections to database.
Defaults to False.</dd>
<dt><strong><code>overwrite</code></strong> :&ensp;<code>bool</code></dt>
<dd>Unconditional overwrite files during restore.
Defaults to True.</dd>
<dt><strong><code>restore_path</code></strong> :&ensp;<code>str</code></dt>
<dd>Existing path on disk to restore.
Defaults to None.</dd>
</dl>
<p>to_time (int/str):
Restore to time. Can be integer value or string as 'yyyy-MM-dd HH:mm:ss'.
Defaults to None.</p>
<dl>
<dt><strong><code>sql_recover_type</code></strong> :&ensp;<code>str</code></dt>
<dd>Type of sql recovery state. (STATE_RECOVER, STATE_NORECOVER, STATE_STANDBY)</dd>
</dl>
<p>Defaults to STATE_RECOVER.</p>
<dl>
<dt><strong><code>sql_restore_type</code></strong> :&ensp;<code>str</code></dt>
<dd>Type of sql restore state.
(DATABASE_RESTORE, STEP_RESTORE, RECOVER_ONLY)</dd>
</dl>
<p>Defaults to DATABASE_RESTORE.</p>
<dl>
<dt><strong><code>undo_path</code></strong> :&ensp;<code>str</code></dt>
<dd>File path for undo path for sql standby restores.
Defaults to None.</dd>
<dt><strong><code>restricted_user</code></strong> :&ensp;<code>bool</code></dt>
<dd>Restore database in restricted user mode.
Defaults to None.</dd>
<dt><strong><code>destination_instance</code></strong> :&ensp;<code>str</code></dt>
<dd>Destination instance to restore too.
Defaults to None.</dd>
</dl>
<p>Keyword Args:
point_in_time (int, optional): Time value to use as point in time restore</p>
<pre><code>schedule_pattern (dict):    Please refer SchedulePattern.create_schedule in schedules.py
for the types of patterns that can be sent

hardware_revert (bool): Does hardware revert. Default value is False

log_shipping (bool): Restores log backups on database in standby or no recovery state.
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job
object - instance of the Schedule class for this restore job if its a scheduled Job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if content_to_restore is not a list</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1178-L1260" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore(
        self,
        content_to_restore,
        drop_connections_to_databse=False,
        overwrite=True,
        restore_path=None,
        to_time=None,
        sql_restore_type=SQLDefines.DATABASE_RESTORE,
        sql_recover_type=SQLDefines.STATE_RECOVER,
        undo_path=None,
        restricted_user=None,
        destination_instance=None,
        **kwargs
):
    &#34;&#34;&#34;Restores the databases specified in the input paths list.

        Args:
            content_to_restore (list):  List of databases to restore.

            drop_connections_to_databse (bool):  Drop connections to database.  Defaults to False.

            overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

            restore_path (str):  Existing path on disk to restore.  Defaults to None.

            to_time (int/str):  Restore to time. Can be integer value or string as &#39;yyyy-MM-dd HH:mm:ss&#39;.
            Defaults to None.

            sql_recover_type (str):  Type of sql recovery state. (STATE_RECOVER, STATE_NORECOVER, STATE_STANDBY)
            Defaults to STATE_RECOVER.

            sql_restore_type (str):  Type of sql restore state.  (DATABASE_RESTORE, STEP_RESTORE, RECOVER_ONLY)
            Defaults to DATABASE_RESTORE.

            undo_path (str):  File path for undo path for sql standby restores.  Defaults to None.

            restricted_user (bool):  Restore database in restricted user mode.  Defaults to None.

            destination_instance (str):  Destination instance to restore too.  Defaults to None.

        Keyword Args:
            point_in_time (int, optional): Time value to use as point in time restore

            schedule_pattern (dict):    Please refer SchedulePattern.create_schedule in schedules.py
            for the types of patterns that can be sent

            hardware_revert (bool): Does hardware revert. Default value is False

            log_shipping (bool): Restores log backups on database in standby or no recovery state.

        Returns:
            object - instance of the Job class for this restore job
            object - instance of the Schedule class for this restore job if its a scheduled Job

        Raises:
            SDKException:
                if content_to_restore is not a list

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if not isinstance(content_to_restore, list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if destination_instance is not None:
        destination_instance = destination_instance.lower()

    request_json = self._restore_request_json(
        content_to_restore,
        drop_connections_to_databse=drop_connections_to_databse,
        overwrite=overwrite,
        restore_path=restore_path,
        to_time=to_time,
        sql_restore_type=sql_restore_type,
        sql_recover_type=sql_recover_type,
        undo_path=undo_path,
        restricted_user=restricted_user,
        destination_instance=destination_instance,
        **kwargs
    )

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.restore_to_destination_server"><code class="name flex">
<span>def <span class="ident">restore_to_destination_server</span></span>(<span>self, content_to_restore, destination_server, drop_connections_to_databse=False, overwrite=True, restore_path=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the databases specified in the input paths list.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>content_to_restore</code></strong> :&ensp;<code>list</code></dt>
<dd>List of databases to restore.</dd>
<dt><strong><code>destination_server</code></strong> :&ensp;<code>str</code></dt>
<dd>Destination server(instance) name.</dd>
<dt><strong><code>drop_connections_to_databse</code></strong> :&ensp;<code>bool</code></dt>
<dd>Drop connections to database.
Defaults to False.</dd>
<dt><strong><code>overwrite</code></strong> :&ensp;<code>bool</code></dt>
<dd>Unconditional overwrite files during restore.
Defaults to True.</dd>
<dt><strong><code>restore_path</code></strong> :&ensp;<code>str</code></dt>
<dd>Existing path on disk to restore.
Default to None.</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if content_to_restore is not a list</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1262-L1304" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_destination_server(
        self,
        content_to_restore,
        destination_server,
        drop_connections_to_databse=False,
        overwrite=True,
        restore_path=None):
    &#34;&#34;&#34;Restores the databases specified in the input paths list.

        Args:
            content_to_restore (list):  List of databases to restore.

            destination_server (str):  Destination server(instance) name.

            drop_connections_to_databse (bool): Drop connections to database.  Defaults to False.

            overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

            restore_path (str):  Existing path on disk to restore.  Default to None.

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if content_to_restore is not a list

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if not isinstance(content_to_restore, list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    request_json = self._restore_request_json(
        content_to_restore,
        drop_connections_to_databse=drop_connections_to_databse,
        overwrite=overwrite,
        restore_path=restore_path,
        destination_instance=destination_server
    )

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.table_level_restore"><code class="name flex">
<span>def <span class="ident">table_level_restore</span></span>(<span>self, src_db_name, tables_to_restore, destination_db_name, rp_name, include_child_tables, include_parent_tables)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts a table level restore</p>
<h2 id="args">Args</h2>
<p>src_db_name(str) : Name of the source database</p>
<p>tables_to_restore(list) : List of tables to restore</p>
<p>destination_db_name(str) : Destination database name</p>
<p>rp_name(str) : Name of recovery point</p>
<p>include_child_tables(bool) : Includes all child tables in restore.</p>
<p>include_parent_tables(bool) : Includes all parent tables in restore.</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>job </code></dt>
<dd>Instance of Job class for this restore job</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1373-L1414" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def table_level_restore(self,
                        src_db_name,
                        tables_to_restore,
                        destination_db_name,
                        rp_name,
                        include_child_tables,
                        include_parent_tables):
    &#34;&#34;&#34;Starts a table level restore

    Args:

        src_db_name(str) : Name of the source database

        tables_to_restore(list) : List of tables to restore

        destination_db_name(str) : Destination database name

        rp_name(str) : Name of recovery point

        include_child_tables(bool) : Includes all child tables in restore.

        include_parent_tables(bool) : Includes all parent tables in restore.

    Returns:

        job : Instance of Job class for this restore job&#34;&#34;&#34;

    if not (isinstance(src_db_name, str)
            or isinstance(tables_to_restore, list)
            or isinstance(destination_db_name, str)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    request_json = self._table_level_restore_request_json(
        src_db_name,
        tables_to_restore,
        destination_db_name,
        rp_name,
        include_child_tables,
        include_parent_tables
    )

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.vdi_timeout"><code class="name flex">
<span>def <span class="ident">vdi_timeout</span></span>(<span>self, value)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the SQL VDI timeout value on SQL instance</p>
<h2 id="args">Args</h2>
<p>value (int)
&ndash;
value of vdi timeout for sql vdi operations</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1430-L1442" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def vdi_timeout(self, value):
    &#34;&#34;&#34;Sets the SQL VDI timeout value on SQL instance

        Args:
            value (int)  --  value of vdi timeout for sql vdi operations

    &#34;&#34;&#34;

    request_json = {
        &#34;vDITimeOut&#34;: value
    }

    self._set_instance_properties(&#34;_mssql_instance_prop&#34;, request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sqlinstance.SQLServerInstance.vss_option"><code class="name flex">
<span>def <span class="ident">vss_option</span></span>(<span>self, value)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables or disables VSS option on SQL instance</p>
<h2 id="args">Args</h2>
<p>value (bool)
&ndash;
Boolean value whether to set VSS option on or off</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sqlinstance.py#L1416-L1428" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def vss_option(self, value):
    &#34;&#34;&#34;Enables or disables VSS option on SQL instance

        Args:
            value (bool)  --  Boolean value whether to set VSS option on or off

    &#34;&#34;&#34;

    request_json = {
        &#34;useVss&#34;: value
    }

    self._set_instance_properties(&#34;_mssql_instance_prop&#34;, request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instance.Instance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance" href="#cvpysdk.instances.sqlinstance.SQLServerInstance">SQLServerInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.ag_group_name" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.ag_group_name">ag_group_name</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.ag_listener_list" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.ag_listener_list">ag_listener_list</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.ag_primary_replica" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.ag_primary_replica">ag_primary_replica</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.ag_replicas_list" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.ag_replicas_list">ag_replicas_list</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.backup" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.backup">backup</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.browse" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.browse_in_time" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.browse_in_time">browse_in_time</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.create_recovery_point" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.create_recovery_point">create_recovery_point</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.create_sql_ag" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.create_sql_ag">create_sql_ag</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.database_details" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.database_details">database_details</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.database_list" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.database_list">database_list</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.get_recovery_points" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.get_recovery_points">get_recovery_points</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.impersonation" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.impersonation">impersonation</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.mssql_instance_prop" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.mssql_instance_prop">mssql_instance_prop</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.restore" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.restore">restore</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.restore_to_destination_server" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.restore_to_destination_server">restore_to_destination_server</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.table_level_restore" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.table_level_restore">table_level_restore</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.vdi_timeout" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.vdi_timeout">vdi_timeout</a></code></li>
<li><code><a title="cvpysdk.instances.sqlinstance.SQLServerInstance.vss_option" href="#cvpysdk.instances.sqlinstance.SQLServerInstance.vss_option">vss_option</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>