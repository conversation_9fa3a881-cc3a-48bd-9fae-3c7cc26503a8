<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.schedules API documentation</title>
<meta name="description" content="Main file for performing schedule related operations for client/agent/backupset/subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.schedules</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing schedule related operations for client/agent/backupset/subclient.</p>
<p>SchedulePattern: Class for creating the necessary pattern for a schedule creation/modification</p>
<h2 id="schedulepattern">Schedulepattern</h2>
<p><strong>init</strong>(class_object)
&ndash;
initialise object of the SchedulePattern
class</p>
<p>_time_converter(_time_string, time_format)
&ndash; converts utc to epoch and vice versa</p>
<p>_pattern_json(pattern_option_dict)
&ndash; forms the pattern json based on the
dict provided
_one_time(pattern_dict)
&ndash; sets the one time schedule pattern</p>
<p>_daily(pattern_dict)
&ndash; sets the daily schedule pattern</p>
<p>_weekly(pattern_dict)
&ndash; sets the weekly schedule pattern</p>
<p>_monthly(pattern_dict)
&ndash; sets the monthly schedule pattern</p>
<p>_monthly_relative(pattern_dict)
&ndash; set the monthly_relative schedule pattern</p>
<p>_yearly(pattern_dict)
&ndash; sets the yearly schedule pattern</p>
<p>_yearly_relative(pattern_dict)
&ndash; sets the yearly_relative schedule pattern</p>
<p>_continuous(pattern_dict)
&ndash; sets the continuous schedule pattern</p>
<p>_automatic(pattern_dict)
&ndash; sets the automatic schedule pattern</p>
<p>_after_job_completes(pattern_dict)
&ndash; sets the after job completes schedule pattern</p>
<p>create_schedule_pattern(pattern_dict)
&ndash; creates a schedule pattern for the user
given pattern</p>
<p>create_schedule(task_req,pattern_dict)
&ndash; creates a scheduling request from the
pattern provided</p>
<p>Schedules: Initializes instance of all schedules for a commcell entity.</p>
<h2 id="schedules">Schedules</h2>
<p><strong>init</strong>(class_object)
&ndash;
initialise object of the Schedules class</p>
<p><strong>str</strong>()
&ndash;
string of all schedules associated with the commcell entity</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the Schedules class</p>
<p>_get_sch_id_from_task_id()
&ndash;
gets the schedule id from the provided task id</p>
<p>_get_schedule_id()
&ndash;
gets the schedule if with the provided inputs</p>
<p>_get_schedules()
&ndash;
gets all the schedules associated with the commcell entity</p>
<p>has_schedule(schedule_name)
&ndash;
checks if schedule exists for the comcell entity or not</p>
<p>delete(schedule_name)
&ndash;
deletes the given schedule</p>
<p>refresh()
&ndash;
refresh the schedules associated with the commcell entity</p>
<p>Schedule: Class for performing operations for a specific Schedule.</p>
<h2 id="schedule">Schedule</h2>
<p><strong>init</strong>(class_object)
&ndash;
initialise object of the Schedule class</p>
<p>_get_schedule_properties
&ndash; get all schedule properties</p>
<p>is_disabled
&ndash; Get the schedule status whether its disabled</p>
<p>schedule_freq_type
&ndash; gets the schedule frequence type</p>
<p>name
&ndash; gets the name of the schedule</p>
<p>name(new_name)
&ndash; sets the name of the schedule</p>
<p>one_time
&ndash; gets the one time schedule pattern dict</p>
<p>one_time(pattern_dict)
&ndash; sets the one time schedule pattern</p>
<p>daily
&ndash; gets the daily schedule pattern</p>
<p>daily(pattern_dict)
&ndash; sets the daily schedule pattern</p>
<p>weekly
&ndash; gets the weekly schedule pattern</p>
<p>weekly(pattern_dict)
&ndash; sets the weekly schedule pattern</p>
<p>monthly
&ndash; gets the monthly schedule pattern</p>
<p>monthly(pattern_dict)
&ndash; gets the monthly schedule pattern</p>
<p>monthly_relative
&ndash; gets the monthly_relative schedule pattern</p>
<p>monthly_relative(pattern_dict)
&ndash; set the monthly_relative schedule pattern</p>
<p>yearly
&ndash; gets the yearly schedule pattern</p>
<p>yearly(pattern_dict)
&ndash; sets the yearly schedule pattern</p>
<p>yearly_relative
&ndash; gets the yearly_relative schedule pattern</p>
<p>yearly_relative(pattern_dict)
&ndash; sets the yearly_relative schedule pattern</p>
<p>continuous
&ndash; gets the continuous schedule pattern</p>
<p>continuous(pattern_dict)
&ndash; sets the continuous schedule pattern</p>
<p>automatic
&ndash; gets the automatic schedule pattern</p>
<p>automatic(pattern_dict)
&ndash; sets the automatic schedule pattern</p>
<p>active_start_date
&ndash; gets the start date of schedule pattern</p>
<p>active_start_date(active_start_date)
&ndash; sets the start date of schedule pattern</p>
<p>active_start_time
&ndash; gets the start time of schedule pattern</p>
<p>active_start_time(active_start_time)
&ndash; sets the start time of schedule pattern</p>
<p>enable()
&ndash; enables the schedule</p>
<p>disable()
&ndash; disables the schedule</p>
<p>run_now()
&ndash; Triggers the schedule immediately</p>
<p>_modify_task_properties
&ndash; modifies the schedule properties
based on the setters</p>
<p>_process_schedule_update_response
&ndash; processes the response and
gives the error_code and message</p>
<p>refresh()
&ndash; refresh the properties of the schedule</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1-L2489" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#class Schedules
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing schedule related operations for client/agent/backupset/subclient.

SchedulePattern: Class for creating the necessary pattern for a schedule creation/modification

SchedulePattern:
    __init__(class_object)                          --  initialise object of the SchedulePattern
                                                        class

    _time_converter(_time_string, time_format)      -- converts utc to epoch and vice versa

    _pattern_json(pattern_option_dict)              -- forms the pattern json based on the
                                                                                 dict provided
    _one_time(pattern_dict)                         -- sets the one time schedule pattern

    _daily(pattern_dict)                            -- sets the daily schedule pattern

    _weekly(pattern_dict)                           -- sets the weekly schedule pattern

    _monthly(pattern_dict)                          -- sets the monthly schedule pattern

    _monthly_relative(pattern_dict)                 -- set the monthly_relative schedule pattern

    _yearly(pattern_dict)                           -- sets the yearly schedule pattern

    _yearly_relative(pattern_dict)                  -- sets the yearly_relative schedule pattern

    _continuous(pattern_dict)                       -- sets the continuous schedule pattern

    _automatic(pattern_dict)                        -- sets the automatic schedule pattern

    _after_job_completes(pattern_dict)              -- sets the after job completes schedule pattern

    create_schedule_pattern(pattern_dict)           -- creates a schedule pattern for the user
                    given pattern

    create_schedule(task_req,pattern_dict)          -- creates a scheduling request from the
                                                                            pattern provided


Schedules: Initializes instance of all schedules for a commcell entity.

Schedules:
    __init__(class_object)          --  initialise object of the Schedules class

    __str__()                       --  string of all schedules associated with the commcell entity

    __repr__()                      --  returns the string for the instance of the Schedules class

    _get_sch_id_from_task_id()      --  gets the schedule id from the provided task id

    _get_schedule_id()              --  gets the schedule if with the provided inputs

    _get_schedules()                --  gets all the schedules associated with the commcell entity

    has_schedule(schedule_name)     --  checks if schedule exists for the comcell entity or not

    delete(schedule_name)           --  deletes the given schedule

    refresh()                       --  refresh the schedules associated with the commcell entity


Schedule: Class for performing operations for a specific Schedule.

Schedule:
    __init__(class_object)                          --  initialise object of the Schedule class

    _get_schedule_properties                        -- get all schedule properties

    is_disabled                                     -- Get the schedule status whether its disabled

    schedule_freq_type                              -- gets the schedule frequence type

    name                                            -- gets the name of the schedule

    name(new_name)                                  -- sets the name of the schedule

    one_time                                        -- gets the one time schedule pattern dict

    one_time(pattern_dict)                          -- sets the one time schedule pattern

    daily                                           -- gets the daily schedule pattern

    daily(pattern_dict)                             -- sets the daily schedule pattern

    weekly                                          -- gets the weekly schedule pattern

    weekly(pattern_dict)                            -- sets the weekly schedule pattern

    monthly                                         -- gets the monthly schedule pattern

    monthly(pattern_dict)                           -- gets the monthly schedule pattern

    monthly_relative                               -- gets the monthly_relative schedule pattern

    monthly_relative(pattern_dict)                 -- set the monthly_relative schedule pattern

    yearly                                         -- gets the yearly schedule pattern

    yearly(pattern_dict)                           -- sets the yearly schedule pattern

    yearly_relative                                -- gets the yearly_relative schedule pattern

    yearly_relative(pattern_dict)                  -- sets the yearly_relative schedule pattern

    continuous                                     -- gets the continuous schedule pattern

    continuous(pattern_dict)                       -- sets the continuous schedule pattern

    automatic                                      -- gets the automatic schedule pattern

    automatic(pattern_dict)                        -- sets the automatic schedule pattern

    active_start_date                               -- gets the start date of schedule pattern

    active_start_date(active_start_date)            -- sets the start date of schedule pattern

    active_start_time                               -- gets the start time of schedule pattern

    active_start_time(active_start_time)            -- sets the start time of schedule pattern

    enable()                                        -- enables the schedule

    disable()                                        -- disables the schedule

    run_now()                                       -- Triggers the schedule immediately

    _modify_task_properties                         -- modifies the schedule properties
                                                                            based on the setters

    _process_schedule_update_response               -- processes the response and
                                                                gives the error_code and message

    refresh()                                       -- refresh the properties of the schedule

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals
from datetime import datetime
import calendar
from .exception import SDKException


class OperationType:
    &#34;&#34;&#34; Operation Types supported to get schedules of particular optype&#34;&#34;&#34;
    REPORTS = &#39;Reports&#39;
    DATA_AGING = &#39;DATA_AGING&#39;


class SchedulePattern:
    &#34;&#34;&#34;Class for getting the schedule pattern&#34;&#34;&#34;

    _days_to_run = {
        2: &#39;monday&#39;,
        4: &#39;tuesday&#39;,
        8: &#39;wednesday&#39;,
        16: &#39;thursday&#39;,
        32: &#39;friday&#39;,
        64: &#39;saturday&#39;,
        1: &#39;sunday&#39;,
    }

    _relative_weekday = {
        1: &#39;sunday&#39;,
        2: &#39;monday&#39;,
        3: &#39;tuesday&#39;,
        4: &#39;wednesday&#39;,
        5: &#39;thursday&#39;,
        6: &#39;friday&#39;,
        7: &#39;saturday&#39;,
        8: &#39;days&#39;,
        9: &#39;weekday&#39;,
        10: &#39;weekend_day&#39;
    }

    _relative_day = {
        1: &#39;first&#39;,
        2: &#39;second&#39;,
        3: &#39;third&#39;,
        4: &#39;fourth&#39;,
        5: &#39;last&#39;
    }

    def __init__(self, schedule_pattern=None):
        &#34;&#34;&#34;initialise object of the SchedulePattern class&#34;&#34;&#34;
        if not schedule_pattern:
            self._pattern = {&#39;freq_type&#39;: &#39;Init&#39;}
        else:
            self._pattern = schedule_pattern

    @staticmethod
    def _time_converter(_time, time_format, utc_to_epoch=True):
        &#34;&#34;&#34;
        converts a time string to epoch time based on the time format provided
        Args:

            _time  (str/int) -- UTC time or EPOCH time
            time_format (str) -- format of the time you need process

        Raises:
            SDKException if time format is wrong

        &#34;&#34;&#34;
        try:

            if utc_to_epoch:
                date_time = datetime.strptime(_time, time_format)
                return int(
                    (date_time - datetime.utcfromtimestamp(0)).total_seconds())

            utc_time = datetime.utcfromtimestamp(_time)
            return utc_time.strftime(time_format)

        except ValueError:
            raise SDKException(
                &#39;Schedules&#39;,
                &#39;102&#39;,
                &#34;Incorrect data format, should be {0}&#34;.format(time_format))

    def _pattern_json(self, pattern_option_dict):
        &#34;&#34;&#34;
        forms a pattern json and set the class variable
        Args:
             pattern_option_dict (dict) -- dictionary with the parameters needed for
                                                            forming the corresponding pattern
                                            {&#39;freq_type&#39;,
                                            &#39;active_start_date&#39;,
                                            &#39;active_start_time&#39;,
                                            &#39;freq_recurrence_factor&#39;,
                                            &#39;freq_interval&#39;}
        &#34;&#34;&#34;

        if (&#39;freq_type&#39; not in pattern_option_dict) or (
                pattern_option_dict[&#39;freq_type&#39;] == self._pattern[&#39;freq_type&#39;]):
            for key, value in pattern_option_dict.items():
                if key in (&#39;active_start_date&#39;, &#39;active_end_date&#39;):
                    self._pattern[key] = self._time_converter(
                        pattern_option_dict[key] + &#39; 00:00&#39;, &#39;%m/%d/%Y %H:%M&#39;)
                elif key in (&#39;active_start_time&#39;, &#39;active_end_time&#39;):
                    self._pattern[key] = self._time_converter(
                        &#39;1/1/1970 &#39; + pattern_option_dict[key], &#39;%m/%d/%Y %H:%M&#39;)
                else:
                    self._pattern[key] = value

        else:

            if pattern_option_dict[&#39;freq_type&#39;] == &#39;One_Time&#39;:
                default_start_time = str(datetime.now().strftime(&#39;%H:%M&#39;))

            else:
                default_start_time = &#39;09:00&#39;

            _active_start_date = pattern_option_dict.get(
                &#39;active_start_date&#39;, str(datetime.now().strftime(&#39;%m/%d/%Y&#39;)))
            _active_start_time = pattern_option_dict.get(
                &#39;active_start_time&#39;, default_start_time)

            self._pattern = {
                &#39;freq_type&#39;: pattern_option_dict[&#39;freq_type&#39;],
                &#39;active_start_date&#39;: self._time_converter(
                    _active_start_date + &#39; 00:00&#39;,
                    &#39;%m/%d/%Y %H:%M&#39;),
                &#39;active_start_time&#39;: self._time_converter(
                    &#39;1/1/1970 &#39; + _active_start_time,
                    &#39;%m/%d/%Y %H:%M&#39;),
                &#39;freq_recurrence_factor&#39;: pattern_option_dict.get(
                    &#39;freq_recurrence_factor&#39;,
                    0),
                &#39;freq_interval&#39;: pattern_option_dict.get(
                    &#39;freq_interval&#39;,
                    0),
                &#39;freq_relative_interval&#39;: pattern_option_dict.get(
                    &#39;freq_relative_interval&#39;,
                    0),
                &#39;timeZone&#39;: {
                    &#39;TimeZoneName&#39;: pattern_option_dict.get(
                        &#39;time_zone&#39;,
                        &#39;&#39;)}}

            if &#34;active_end_date&#34; in pattern_option_dict:
                self._pattern[&#34;active_end_date&#34;] = self._time_converter(
                    pattern_option_dict[&#34;active_end_date&#34;] + &#39; 00:00&#39;, &#39;%m/%d/%Y %H:%M&#39;)

        if &#34;exception_dates&#34; in pattern_option_dict:
            self._pattern[&#34;repeatPattern&#34;] = [{&#34;exception&#34;: True,
                                               &#34;onDayNumber&#34;: self.exception_dates(
                                                   pattern_option_dict[&#34;exception_dates&#34;])}
                                              ]

        if &#34;end_after&#34; in pattern_option_dict:
            self._pattern[&#34;active_end_occurence&#34;] = pattern_option_dict[&#34;end_after&#34;]

        if &#34;repeat_every&#34; in pattern_option_dict:
            self._pattern.update(self._repeat_pattern(pattern_option_dict))

    @staticmethod
    def _repeat_pattern(pattern_dict):
        &#34;&#34;&#34;
        Forms repeat pattern json based on the pattern dict provided
        Args:
            pattern_dict (dict) -- Dictionary containing repeat_every and repeat_end parameters
                                    {
                                    &#34;repeat_every&#34;: &#34;08:00&#34;,
                                    &#34;repeat_end&#34;: &#34;23:00&#34;
                                    }
        Returns:
                Dict with subdayinterval and endtime information to plug into the pattern json
        &#34;&#34;&#34;
        _repeat_time = datetime.strptime(
            pattern_dict.get(
                &#34;repeat_every&#34;, &#34;08:00&#34;), &#34;%H:%M&#34;)
        _freq_subday = (_repeat_time.hour * 3600 + _repeat_time.minute * 60)
        return {&#39;freq_subday_interval&#39;: _freq_subday,
                &#39;active_end_time&#39;: SchedulePattern._time_converter(
                    &#39;1/1/1970 &#39; + pattern_dict[&#34;repeat_end&#34;], &#39;%m/%d/%Y %H:%M&#39;)
                }

    def _one_time(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
        send only required keys to change only those values
        Args:
             pattern_dict (dict) -- Dictonary with the schedule pattern
                {
                                 &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                                 &#34;active_start_time&#34;: time_in_%h:%m (str)
                }
        &#34;&#34;&#34;
        pattern_dict[&#39;freq_type&#39;] = 1
        self._pattern_json(pattern_dict)

    def _daily(self, pattern_dict):
        &#34;&#34;&#34;
                sets the pattern type as daily with the parameters provided
                send only required keys to change only those values
                Args:
                     pattern_dict (dict) -- Dictionary with the schedule pattern
                  {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                  }
        &#34;&#34;&#34;
        _repeat_days = 1
        if self._pattern[&#39;freq_type&#39;] == 4:
            _repeat_days = self._pattern[&#39;freq_recurrence_factor&#39;]

        _freq_recurrence_factor = pattern_dict.get(&#39;repeat_days&#39;, _repeat_days)

        pattern_dict[&#39;freq_type&#39;] = 4
        pattern_dict[&#39;freq_recurrence_factor&#39;] = 1 if not isinstance(
            _freq_recurrence_factor, int) else _freq_recurrence_factor
        self._pattern_json(pattern_dict)

    def _weekly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as weekly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                        }
        &#34;&#34;&#34;
        try:
            _repeat_weeks = 1
            _freq_interval = 0
            if self._pattern[&#39;freq_type&#39;] == 8:
                _repeat_weeks = self._pattern[&#39;freq_recurrence_factor&#39;]
                _freq_interval = self._pattern[&#39;freq_interval&#39;]

            pattern_dict[&#39;freq_type&#39;] = 8
            # encoding
            if &#39;weekdays&#39; in pattern_dict:
                _freq_interval_list = pattern_dict[&#39;weekdays&#39;]
                for weekday in _freq_interval_list:
                    _freq_interval += (
                        list(
                            self._days_to_run.keys())[
                            list(
                                self._days_to_run.values()).index(
                                weekday.lower())])
            elif _freq_interval == 0:
                o_str = &#39;Weekdays need to be specified&#39;
                raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, o_str)

            _freq_recurrence_factor = pattern_dict.get(
                &#39;_repeat_weeks&#39;, _repeat_weeks)

            pattern_dict[&#39;freq_interval&#39;] = _freq_interval
            pattern_dict[&#39;freq_recurrence_factor&#39;] = 1 if not isinstance(
                _freq_recurrence_factor, int) else _freq_recurrence_factor

            self._pattern_json(pattern_dict)

        except ValueError:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               &#34;Incorrect weekday specified&#34;)

    def _monthly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;repeat_months&#34;: months_to_repeat (int)
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
        &#34;&#34;&#34;
        _repeat_months = 1
        _on_day = 10
        if self._pattern[&#39;freq_type&#39;] == 16:
            _repeat_months = self._pattern[&#39;freq_recurrence_factor&#39;]
            _on_day = self._pattern[&#39;freq_interval&#39;]

        _freq_recurrence_factor = pattern_dict.get(
            &#39;repeat_months&#39;, _repeat_months)
        _freq_interval = pattern_dict.get(&#39;on_day&#39;, _on_day)

        pattern_dict[&#39;freq_recurrence_factor&#39;] = 1 if not isinstance(
            _freq_recurrence_factor, int) else _freq_recurrence_factor
        pattern_dict[&#39;freq_interval&#39;] = 1 if not isinstance(
            _freq_interval, int) else _freq_interval
        pattern_dict[&#39;freq_type&#39;] = 16
        self._pattern_json(pattern_dict)

    def _monthly_relative(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly_relative with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;repeat_months&#34;: months_to_repeat
                        }
        &#34;&#34;&#34;
        _freq_recurrence_factor = 1
        _freq_interval = 1
        _freq_relative_interval = 1

        try:

            if self._pattern[&#39;freq_type&#39;] == 32:
                _freq_recurrence_factor = self._pattern[&#39;freq_recurrence_factor&#39;]
                _freq_interval = self._pattern[&#39;freq_interval&#39;]
                _freq_relative_interval = self._pattern[&#39;freq_relative_interval&#39;]

            if &#39;relative_time&#39; in pattern_dict:
                _freq_relative_interval = (
                    list(
                        self._relative_day.keys())[
                        list(
                            self._relative_day.values()).index(
                            pattern_dict[&#39;relative_time&#39;].lower())])

            if &#39;relative_weekday&#39; in pattern_dict:
                _freq_interval = (
                    list(
                        self._relative_weekday.keys())[
                        list(
                            self._relative_weekday.values()).index(
                            pattern_dict[&#39;relative_weekday&#39;].lower())])

            _freq_recurrence_factor = pattern_dict.get(
                &#39;repeat_months&#39;, _freq_recurrence_factor)

            pattern_dict[&#39;freq_recurrence_factor&#39;] = 1 if not isinstance(
                _freq_recurrence_factor, int) else _freq_recurrence_factor

            pattern_dict[&#39;freq_interval&#39;] = _freq_interval
            pattern_dict[&#39;freq_relative_interval&#39;] = _freq_relative_interval

            pattern_dict[&#39;freq_type&#39;] = 32
            self._pattern_json(pattern_dict)

        except ValueError as v_error:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               str(v_error))

    def _yearly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
        &#34;&#34;&#34;
        try:

            _freq_recurrence_factor = 1
            _freq_interval = 10
            if self._pattern[&#39;freq_type&#39;] == 64:
                _freq_recurrence_factor = self._pattern[&#39;freq_recurrence_factor&#39;]
                _freq_interval = self._pattern[&#39;freq_interval&#39;]

            if &#39;on_month&#39; in pattern_dict:
                _freq_recurrence_factor = list(
                    calendar.month_name).index(
                    pattern_dict[&#39;on_month&#39;].title())

            _freq_interval = pattern_dict.get(&#39;on_day&#39;, _freq_interval)

            pattern_dict[&#39;freq_recurrence_factor&#39;] = _freq_recurrence_factor
            pattern_dict[&#39;freq_interval&#39;] = 1 if not isinstance(
                _freq_interval, int) else _freq_interval
            pattern_dict[&#39;freq_type&#39;] = 64
            self._pattern_json(pattern_dict)

        except ValueError as ve:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               str(ve))

    def _yearly_relative(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly_relative with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;on_month&#34;: month to run the schedule(str) January, February...
                        }
        &#34;&#34;&#34;
        _freq_recurrence_factor = 1
        _freq_interval = 1
        _freq_relative_interval = 1

        try:

            if self._pattern[&#39;freq_type&#39;] == 128:
                _freq_recurrence_factor = self._pattern[&#39;freq_recurrence_factor&#39;]
                _freq_interval = self._pattern[&#39;freq_interval&#39;]
                _freq_relative_interval = self._pattern[&#39;freq_relative_interval&#39;]

            if &#39;relative_time&#39; in pattern_dict:
                _freq_relative_interval = (
                    list(
                        self._relative_day.keys())[
                        list(
                            self._relative_day.values()).index(
                            pattern_dict[&#39;relative_time&#39;].lower())])

            if &#39;relative_weekday&#39; in pattern_dict:
                _freq_interval = (
                    list(
                        self._relative_weekday.keys())[
                        list(
                            self._relative_weekday.values()).index(
                            pattern_dict[&#39;relative_weekday&#39;].lower())])

            if &#39;on_month&#39; in pattern_dict:
                _freq_recurrence_factor = list(
                    calendar.month_name).index(
                    pattern_dict[&#39;on_month&#39;].title())
            pattern_dict[&#39;freq_recurrence_factor&#39;] = _freq_recurrence_factor
            pattern_dict[&#39;freq_interval&#39;] = _freq_interval
            pattern_dict[&#39;freq_relative_interval&#39;] = _freq_relative_interval

            pattern_dict[&#39;freq_type&#39;] = 128
            self._pattern_json(pattern_dict)

        except ValueError as ve:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               str(ve))

    def _continuous(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                {
                                 job_interval: interval between jobs in mins(int)
                }
        &#34;&#34;&#34;

        _freq_recurrence_factor = pattern_dict.get(&#39;job_interval&#39;, 30)
        pattern_dict[&#39;freq_interval&#39;] = 30 if not isinstance(
            _freq_recurrence_factor, int) else _freq_recurrence_factor
        pattern_dict[&#39;freq_type&#39;] = 4096
        self._pattern_json(pattern_dict)

    def _automatic(self, pattern_dict):
        &#34;&#34;&#34;
                sets the pattern type as one time with the parameters provided,
                send only required keys to change only those values
                Args:
                     pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                         min_interval_hours: minimum hours between jobs(int)
                                         min_interval_minutes: minimum minutes between jobs(int)
                                         max_interval_hours: maximum hours between jobs(int)
                                         max_interval_minutes: maximum minutes between jobs(int)
                                         min_sync_interval_hours: minimum sync hours
                                                                                between jobs(int)
                                         min_sync_interval_minutes: minimum sync minutes
                                                                                between jobs(int)
                                         ignore_opwindow_past_maxinterval: (bool)
                                         wired_network_connection: (bool)
                                         min_network_bandwidth: (int) kbps
                                         specific_network: (dict){ip_address:(str),subnet:(int)}
                                         dont_use_metered_network: (bool)
                                         ac_power: (bool)
                                         stop_if_on_battery: (bool)
                                         stop_sleep_if_runningjob: (bool)
                                         cpu_utilization_below : (int)%
                                         cpu_utilization_above : (int)%
                                         disk_use_threshold: (int)%
                                         number_of_log_files: (int)
                        }
        &#34;&#34;&#34;
        automatic_pattern = {
            &#34;maxBackupInterval&#34;: pattern_dict.get(&#34;max_interval_hours&#34;,
                                                  self._pattern.get(&#34;maxBackupInterval&#34;, 72)),
            &#34;ignoreOpWindowPastMaxInterval&#34;: pattern_dict.get(&#34;ignore_opwindow_past_maxinterval&#34;,
                                                              self._pattern.get(
                                                                  &#34;ignoreOpWindowPastMaxInterval&#34;,
                                                                  False)),
            &#34;minBackupIntervalMinutes&#34;: pattern_dict.get(&#34;min_interval_minutes&#34;,
                                                         self._pattern.get(
                                                             &#34;minBackupIntervalMinutes&#34;, 15)),
            &#34;maxBackupIntervalMinutes&#34;: pattern_dict.get(&#34;max_interval_minutes&#34;,
                                                         self._pattern.get(
                                                             &#34;maxBackupIntervalMinutes&#34;, 0)),
            &#34;minSyncInterval&#34;: pattern_dict.get(&#34;min_sync_interval_hours&#34;,
                                                self._pattern.get(&#34;minSyncInterval&#34;, 0)),
            &#34;minBackupInterval&#34;: pattern_dict.get(&#34;min_interval_hours&#34;,
                                                  self._pattern.get(&#34;minBackupInterval&#34;, 0)),
            &#34;minSyncIntervalMinutes&#34;: pattern_dict.get(&#34;min_sync_interval_minutes&#34;,
                                                       self._pattern.get(&#34;minSyncIntervalMinutes&#34;,
                                                                         2)),
            &#34;stopIfOnBattery&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;stop_if_on_battery&#34;,
                                            self._pattern.get(&#34;stopIfOnBattery&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])
            },
            &#34;acPower&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;ac_power&#34;,
                                            self._pattern.get(&#34;acPower&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])
            },
            &#34;specfificNetwork&#34;: {
                &#34;enabled&#34;: True if &#39;specific_network&#39; in pattern_dict
                else (self._pattern.get(&#34;specfificNetwork&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;ipAddress&#34;: {
                    &#34;family&#34;: 32,
                    &#34;address&#34;: pattern_dict.get(&#39;specific_network&#39;,
                                                {&#34;ip_address&#34;: &#34;0.0.0.0&#34;})[&#34;ip_address&#34;],
                    &#34;subnet&#34;: pattern_dict.get(&#39;specific_network&#39;,
                                               {&#34;subnet&#34;: 24})[&#34;subnet&#34;],
                }

            },
            &#34;stopSleepIfBackUp&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;stop_sleep_if_runningjob&#34;,
                                            self._pattern.get(&#34;stopSleepIfBackUp&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])

            },
            &#34;emergencyBackup&#34;: {
                &#34;emergencyBackupCommandName&#34;: &#34;&#34;,
                &#34;emergencyBackup&#34;: {
                    &#34;enabled&#34;: False
                }
            },
            &#34;cpuUtilization&#34;: {
                &#34;enabled&#34;: True if &#39;cpu_utilization_below&#39; in pattern_dict
                else (self._pattern.get(&#34;cpuUtilization&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;cpu_utilization_below&#34;,
                                              self._pattern.get(&#34;cpuUtilization&#34;,
                                                                {&#39;threshold&#39;: 10})[&#39;threshold&#39;])
            },
            &#34;dontUseMeteredNetwork&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;dont_use_metered_network&#34;,
                                            self._pattern.get(&#34;dontUseMeteredNetwork&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])
            },
            &#34;cpuUtilizationAbove&#34;: {
                &#34;enabled&#34;: True if &#39;cpu_utilization_above&#39; in pattern_dict
                else (self._pattern.get(&#34;cpuUtilizationAbove&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;cpu_utilization_above&#34;,
                                              self._pattern.get(&#34;cpuUtilizationAbove&#34;,
                                                                {&#39;threshold&#39;: 10})[&#39;threshold&#39;])
            },
            &#34;wiredNetworkConnection&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;wired_network_connection&#34;,
                                            self._pattern.get(&#34;wiredNetworkConnection&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])
            },
            &#34;minNetworkBandwidth&#34;: {
                &#34;enabled&#34;: True if &#39;min_network_bandwidth&#39; in pattern_dict
                else (self._pattern.get(&#34;minNetworkBandwidth&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;min_network_bandwidth&#34;,
                                              self._pattern.get(&#34;minNetworkBandwidth&#34;,
                                                                {&#39;threshold&#39;: 128})[&#39;threshold&#39;])
            },
            &#34;sweepStartTime&#34;: pattern_dict.get(&#34;sweep_start_time&#34;,
                                               self._pattern.get(&#34;sweepStartTime&#34;, 3600)
                                               ),
            &#34;useStorageSpaceFromMA&#34;: pattern_dict.get(&#34;use_storage_space_ma&#34;,
                                               self._pattern.get(&#34;useStorageSpaceFromMA&#34;, False)
                                               ),
            &#34;diskUsedPercent&#34;: {
                &#34;enabled&#34;: True if &#39;disk_use_threshold&#39; in pattern_dict
                else (self._pattern.get(&#34;diskUsedPercent&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;disk_use_threshold&#34;,
                                              self._pattern.get(&#34;diskUsedPercent&#34;,
                                                                {&#39;threshold&#39;: 80})[&#39;threshold&#39;])
            },
            &#34;logFileNum&#34;: {
                &#34;enabled&#34;: True if &#39;number_of_log_files&#39; in pattern_dict
                else (self._pattern.get(&#34;logFileNum&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;number_of_log_files&#34;,
                                              self._pattern.get(&#34;logFileNum&#34;,
                                                                {&#39;threshold&#39;: 50})[&#39;threshold&#39;])
            }
        }

        self._pattern = automatic_pattern

    def _after_job_completes(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as after job completes with the parameters provided,
        send only required keys to change only those values

        Args:
            pattern_dict        (dict) -- Dictionary with the schedule pattern
                {
                        &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                        &#34;repeat_days&#34;: days_to_repeat (int)
                }

        &#34;&#34;&#34;
        pattern_dict[&#39;freq_type&#39;] = &#39;After_Job_Completes&#39;

        pattern_dict[&#39;freq_recurrence_factor&#39;] = pattern_dict.get(&#39;repeat_days&#39;, 4096)

        self._pattern_json(pattern_dict)

    @staticmethod
    def exception_dates(day_list):
        &#34;&#34;&#34;
        Provided a Scheduler version of exception as an on day number
        Args:
            day_list (list) -- List of exception dates [1,2,3]

        Returns (int) -- on_day number for the pattern json

        &#34;&#34;&#34;
        on_day = 0
        for value in day_list:
            on_day |= (1 &lt;&lt; (value - 1))
        return on_day

    def create_schedule_pattern(self, pattern_dict):
        &#34;&#34;&#34;
        calls the required type of schedule module and forms the pattern json
        Args:
        pattern_dict (Dict) --

        freq_type is mandatory, all other fields specified below can be skipped and system
                                                                            defaults will be set

        for reference on pattern_dict check create_schedule

        Returns:
             pattern which can be plugged into the create or modify task request to
                                                                        create or modify schedules
        &#34;&#34;&#34;

        if &#39;freq_type&#39; not in pattern_dict:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               &#34;Frequency type is required to create pattern&#34;)

        try:
            getattr(
                self,
                &#39;_&#39; +
                pattern_dict[&#39;freq_type&#39;].lower())(pattern_dict)
            return self._pattern

        except AttributeError:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               &#34;freq_type specified is wrong&#34;)

    def create_schedule(self, task_req, pattern_dict, schedule_id=None):
        &#34;&#34;&#34;
        returns a schedule task_req after including pattern
        Args:
        task_req: task_req for immediate job operation to be converted to a schedule

        freq_type is mandatory, all other fields specified below can be skipped and system
                                                                            defaults will be set

        with the same dict, pass
        time_zone: Time Zone Name(default is taken as COmmServe Time Zone)
            Common Time Zones examples -- &#39;CommServe Time Zone&#39;, &#39;Client Time Zone&#39;, &#39;UTC&#39;

        for one_time: {
                                 &#34;freq_type&#34;: &#39;one_time&#39;,
                                 &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                                 &#34;active_start_time&#34;: time_in_%h:%m (str)
                        }

        for daily: {
                         &#34;freq_type&#34;: &#39;daily&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                  }

        for weekly: {
                         &#34;freq_type&#34;: &#39;weekly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                        }

        for monthly: {
                                 &#34;freq_type&#34;: &#39;monthly&#39;,
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;repeat_months&#34;: weeks_to_repeat (int)
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }

        for monthly_relative:    {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;repeat_months&#34;: months_to_repeat
                                }

        for yearly: {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }

        for yearly_relative: {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;on_month&#34;: month to run the schedule(str) January, Febuary...
                        }

        for continuous: {
                                 job_interval: interval between jobs in mins(int)
                }

        for automatic: {
                                         min_interval_hours: minimum hours between jobs(int)
                                         min_interval_minutes: minimum minutes between jobs(int)
                                         max_interval_hours: maximum hours between jobs(int)
                                         max_interval_minutes: maximum minutes between jobs(int)
                                         min_sync_interval_hours: minimum sync hours
                                                                                between jobs(int)
                                         min_sync_interval_minutes: minimum sync minutes
                                                                                between jobs(int)
                                         ignore_opwindow_past_maxinterval: (bool)
                                         wired_network_connection: (bool)
                                         min_network_bandwidth: (int) kbps
                                         specific_network: (dict){ip_address:(str),subnet:(int)}
                                         dont_use_metered_network: (bool)
                                         ac_power: (bool)
                                         stop_if_on_battery: (bool)
                                         stop_sleep_if_runningjob: (bool)
                                         cpu_utilization_below : (int)%
                                         cpu_utilization_above : (int)%
                                         disk_use_threshold: (int)%
                                         number_of_log_files: (int)
                        }

        for after_job_completes :   {
                                        &#34;freq_type&#34;: &#39;after_job_completes&#39;,
                                        &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                                        &#34;repeat_days&#34;: days_to_repeat (int)
                                    }

        Sample Usage inside the individual operation function:
        Add a schedule_pattern parameter to the function and include the below line before making
        the sdk make_request call

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(task_req,schedule_pattern)

        pattern_dict (Dict) -- schedule pattern to be merged with the task request
        Returns:
             Schedule task request
        &#34;&#34;&#34;

        _automatic_pattern = {}

        if pattern_dict[&#34;freq_type&#34;] == &#39;automatic&#39;:
            _pattern = {&#34;freq_type&#34;: 1024}
            _automatic_pattern = self.create_schedule_pattern(pattern_dict)
        else:
            _pattern = self.create_schedule_pattern(pattern_dict)

        _task_info = task_req[&#34;taskInfo&#34;]
        if _task_info.get(&#34;task&#34;):
            _task_info[&#34;task&#34;][&#34;taskType&#34;] = 2
        for subtask in _task_info[&#39;subTasks&#39;]:
            if schedule_id:
                if subtask[&#34;subTask&#34;][&#39;subTaskId&#39;] != schedule_id:
                    continue
            else:
                subtask[&#34;subTask&#34;][&#39;subTaskName&#39;] = pattern_dict.get(
                    &#39;schedule_name&#39;, &#39;&#39;)
            subtask[&#34;pattern&#34;] = _pattern
            if pattern_dict[&#34;freq_type&#34;] == &#39;automatic&#39;:
                if &#39;options&#39; in subtask:
                    _task_options = subtask[&#39;options&#39;]
                    if &#39;commonOpts&#39; in _task_options:
                        _task_options[&#34;commonOpts&#34;][&#34;automaticSchedulePattern&#34;] = _automatic_pattern
                    else:
                        _task_options[&#34;commonOpts&#34;] = \
                            {&#34;automaticSchedulePattern&#34;: _automatic_pattern}

                    if &#39;run_synthetic_full&#39; in pattern_dict:
                        synthetic_pattern = pattern_dict[&#39;run_synthetic_full&#39;]

                        if synthetic_pattern == &#39;every_x_days&#39;:
                            synthetic_interval = pattern_dict.get(
                                &#39;days_between_synthetic_full&#39;, 30)
                        else:
                            synthetic_interval = 30

                        _data_opt = {
                            &#39;autoCopy&#39;: True,
                            &#39;daysBetweenSyntheticBackup&#39;: synthetic_interval,
                            &#39;useAutomaticIntervalForSyntheticFull&#39;: (
                                    synthetic_pattern == &#39;extended_retention&#39;),
                            &#39;enableRunFullConsolidationBackup&#39;: (
                                    synthetic_pattern == &#39;space_reclaim&#39;)
                        }

                        if &#39;backupOpts&#39; in _task_options:
                            if &#39;dataOpt&#39; in _task_options[&#34;backupOpts&#34;]:
                                _task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;].update(_data_opt)
                            else:
                                _task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;] = _data_opt
                        else:
                            _task_options[&#39;backupOpts&#39;] = {
                                &#39;dataOpt&#39;: _data_opt
                            }

                else:
                    subtask[&#39;options&#39;] = {
                        &#39;commonOpts&#39;: {
                            &#39;automaticSchedulePattern&#39;: _automatic_pattern}}

        task_req[&#34;taskInfo&#34;] = _task_info
        return task_req


class Schedules:
    &#34;&#34;&#34;Class for getting the schedules of a commcell entity.&#34;&#34;&#34;

    def __init__(self, class_object, operation_type=None):
        &#34;&#34;&#34;Initialise the Schedules class instance.

            Args:
                class_object(object) -- instance of client/agent/backupset/subclient/CommCell class
                operation_type        -- required when commcell object is passed
                                        refer OperationType class for supported op types
            Returns:
                object - instance of the Schedule class

            Raises:
                SDKException:
                    if class object does not belong to any of the Client or Agent or Backupset or
                        Subclient class
        &#34;&#34;&#34;
        # imports inside the __init__ method definition to avoid cyclic imports
        from .commcell import Commcell
        from .client import Client
        from .agent import Agent
        from .backupset import Backupset
        from .subclient import Subclient
        from .instance import Instance
        from .activateapps.inventory_manager import Inventory
        from .activateapps.file_storage_optimization import FsoServer
        from .activateapps.sensitive_data_governance import Project

        self.class_object = class_object
        self._single_scheduled_entity = False
        self._task_flags = {}
        self._repr_str = &#34;&#34;

        if isinstance(class_object, Commcell):
            self._commcell_object = class_object
            if operation_type == OperationType.REPORTS:
                self._SCHEDULES = class_object._services[&#39;REPORT_SCHEDULES&#39;]
                self._repr_str = &#34;Reports in Commcell: {0}&#34;.format(
                    class_object.commserv_name)
            elif operation_type == OperationType.DATA_AGING:
                self._SCHEDULES = class_object._services[&#39;OPTYPE_SCHEDULES&#39;] % (
                    operation_type)
                self._repr_str = &#34;Dataging in Commcell: {0}&#34;.format(
                    class_object.commserv_name)
            elif not operation_type:
                self._SCHEDULES = class_object._services[&#39;COMMCELL_SCHEDULES&#39;]
                self._repr_str = &#34;Schedules in Commcell: {0}&#34;.format(
                    class_object.commserv_name)
            else:
                raise SDKException(&#39;Schedules&#39;, &#39;103&#39;)
        elif isinstance(class_object, FsoServer):
            self._SCHEDULES = class_object._commcell_object._services[&#39;CLIENT_SCHEDULES&#39;] % (
                class_object.server_id)
            self._repr_str = &#34;Fso Server: {0}&#34;.format(class_object.server_id)
            self._commcell_object = class_object._commcell_object
            self._single_scheduled_entity = True
            self._task_flags[&#39;isEdiscovery&#39;] = True

        elif isinstance(class_object,Project):
            self._SCHEDULES = class_object._commcell_object._services[&#39;CLIENT_SCHEDULES&#39;] % (
                class_object.project_id)
            self._repr_str = &#34;SDG Project: {0}&#34;.format(class_object.project_id)
            self._commcell_object = class_object._commcell_object
            self._single_scheduled_entity = True
            self._task_flags[&#39;isEdiscovery&#39;] = True

        elif isinstance(class_object, Inventory):
            self._SCHEDULES = class_object._commcell_object._services[&#39;INVENTORY_SCHEDULES&#39;] % (
                class_object.inventory_id)
            self._repr_str = &#34;Inventory: {0}&#34;.format(class_object.inventory_name)
            self._commcell_object = class_object._commcell_object
            # set below flag to denote inventory type entity will always have only one schedule associated to it
            self._single_scheduled_entity = True

        elif isinstance(class_object, Client):
            self._SCHEDULES = class_object._commcell_object._services[&#39;CLIENT_SCHEDULES&#39;] % (
                class_object.client_id)
            self._repr_str = &#34;Client: {0}&#34;.format(class_object.client_name)
            self._commcell_object = class_object._commcell_object

        elif isinstance(class_object, Agent):
            self._SCHEDULES = class_object._commcell_object._services[&#39;AGENT_SCHEDULES&#39;] % (
                class_object._client_object.client_id, class_object.agent_id)
            self._repr_str = &#34;Agent: {0}&#34;.format(class_object.agent_name)
            self._commcell_object = class_object._commcell_object

        elif isinstance(class_object, Instance):
            self._SCHEDULES = class_object._commcell_object._services[&#39;INSTANCE_SCHEDULES&#39;] % (
                class_object._agent_object._client_object.client_id,
                class_object._agent_object.agent_id,
                class_object.instance_id
            )
            self._repr_str = &#34;Instance: {0}&#34;.format(
                class_object.instance_name)
            self._commcell_object = class_object._commcell_object

        elif isinstance(class_object, Backupset):
            self._SCHEDULES = class_object._commcell_object._services[&#39;BACKUPSET_SCHEDULES&#39;] % (
                class_object._agent_object._client_object.client_id,
                class_object._agent_object.agent_id,
                class_object.backupset_id
            )
            self._repr_str = &#34;Backupset: {0}&#34;.format(
                class_object.backupset_name)
            self._commcell_object = class_object._commcell_object

        elif isinstance(class_object, Subclient):
            self._SCHEDULES = class_object._commcell_object._services[&#39;SUBCLIENT_SCHEDULES&#39;] % (
                class_object._backupset_object._agent_object._client_object.client_id,
                class_object._backupset_object._agent_object.agent_id,
                class_object._backupset_object.backupset_id,
                class_object.subclient_id
            )
            self._repr_str = &#34;Subclient: {0}&#34;.format(
                class_object.subclient_name)
            self._commcell_object = class_object._commcell_object
        else:
            raise SDKException(&#39;Schedules&#39;, &#39;101&#39;)

        self.schedules = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all schedules of the commcell entity.

            Returns:
                str - string of all the schedules associated with the commcell entity
        &#34;&#34;&#34;
        if self.schedules:
            representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(
                &#39;S. No.&#39;, &#39;Schedule&#39;)

            for index, schedule in enumerate(self.schedules):
                sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, schedule)
                representation_string += sub_str
        else:
            representation_string = &#39;No Schedules are associated to this Commcell Entity&#39;

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Schedules class.&#34;&#34;&#34;
        return &#34;Schedules class instance for {0}&#34;.format(self._repr_str)

    def _get_schedules(self):
        &#34;&#34;&#34;Gets the schedules associated with the input commcell entity.
            Client / Agent / Backupset / Subclient

            Returns:
                dict - consists of all schedules for the commcell entity
                    {
                         &#34;schedule_id&#34;: {
                                &#39;task_id&#39;: task_id,
                                &#39;schedule_name&#39;: schedule_name,
                                &#39;description&#39;: description
                            }

                         &#34;schedule_id&#34;: {
                                &#39;task_id&#39;: task_id,
                                &#39;schedule_name&#39;: schedule_name,
                                &#39;description&#39;: description
                            }
                    }

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SCHEDULES)

        if flag:
            if response.json() and &#39;taskDetail&#39; in response.json():
                subtask_dict = {}
                for schedule in response.json()[&#39;taskDetail&#39;]:
                    task_id = schedule[&#39;task&#39;][&#39;taskId&#39;]
                    description = &#39;&#39;
                    task_flags = schedule[&#39;task&#39;].get(&#39;taskFlags&#39;,0)
                    if &#39;subTasks&#39; in schedule:
                        for subtask in schedule[&#39;subTasks&#39;]:
                            schedule_id = subtask[&#39;subTask&#39;][&#39;subTaskId&#39;]
                            if &#39;description&#39; in subtask[&#39;subTask&#39;]:
                                description = subtask[&#39;pattern&#39;][&#39;description&#39;].lower(
                                )
                            if &#39;subTaskName&#39; in subtask[&#39;subTask&#39;]:
                                subtask_name = subtask[&#39;subTask&#39;][&#39;subTaskName&#39;].lower(
                                )
                            elif description:
                                subtask_name = description
                            else:
                                subtask_name = str(schedule_id)
#change schedule_id as key
                            subtask_dict[schedule_id] = {
                                &#39;task_id&#39;: task_id,
                                &#39;schedule_name&#39;: subtask_name,
                                &#39;description&#39;: description,
                                &#39;task_flags&#39;: task_flags
                            }

                return subtask_dict
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_sch_id_from_task_id(self, task_id):
        &#34;&#34;&#34;
        Gets the schedule id from the task id

        Args:
        task_id (int): task id of the schedule

        Returns:
            (int) schedule id of the schedule
        &#34;&#34;&#34;
        task_ids = [k for k, v in self.schedules.items() if v[&#39;task_id&#39;] == task_id]
        if task_ids:
            return task_ids[0]
        else:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, &#39;Schedule id not found for corresponding task id&#39;)

    def _get_schedule_id(self, schedule_name=None, schedule_id=None, task_id=None):
        &#34;&#34;&#34;Gets the schedule id from the provided inputs.

            Args:
                schedule_name (str)  --  name of the schedule
                schedule_id (int) -- id of the schedule
                task_id (int)   -- task id of the schedule

            Returns:
            (int) schedule id of the schedule
        &#34;&#34;&#34;

        if self._single_scheduled_entity:
            # if flag set, then entity will have only one schedule associated to it so return first one from dict
            # if flag set along with task flags, then find schedule with that flags set and return that from dict
            for subtask_id, subtask_dict in self.schedules.items():
                if len(self._task_flags) == 0:
                    return subtask_id
                else:
                    task_flags = subtask_dict[&#39;task_flags&#39;]
                    match = True
                    for flag, value in self._task_flags.items():
                        if flag in task_flags:
                            if task_flags[flag] != value:
                                match = False
                        else:
                            match = False
                    if match:
                        return subtask_id
            return None

        if not task_id and not schedule_name and not schedule_id:
            raise SDKException(
                &#39;Schedules&#39;,
                &#39;102&#39;,
                &#39;Either Schedule Name or Schedule Id is needed&#39;)

        if schedule_name and not isinstance(schedule_name, str):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_id and not isinstance(schedule_id, int):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if task_id and not isinstance(task_id, int):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_name:
            schedule_name = schedule_name.lower()
            for subtask_id, subtask_dict in self.schedules.items():
                if subtask_dict[&#39;schedule_name&#39;] == schedule_name:
                    schedule_id = subtask_id

        elif task_id:
            schedule_id = self._get_sch_id_from_task_id(task_id)

        if self.schedules and schedule_id in self.schedules:
            return schedule_id

    def has_schedule(self, schedule_name=None, schedule_id=None, task_id=None):
        &#34;&#34;&#34;Checks if a schedule exists for the commcell entity with the input schedule name.

            Args:
                schedule_name (str)  --  name of the schedule
                schedule_id (int) -- id of the schedule
                task_id (int)   -- task id of the schedule

            Returns:
                bool - boolean output whether the schedule exists for the commcell entity or not

            Raises:
                SDKException:
                    if type of the schedule name argument is not string
        &#34;&#34;&#34;
        if self._get_schedule_id(schedule_name, schedule_id, task_id):
            return True
        return False

    def get(self, schedule_name=None, schedule_id=None, task_id=None):
        &#34;&#34;&#34;Returns a schedule object of the specified schedule name.

            Args:
                schedule_name (str)  --  name of the Schedule
                schedule_id (int) -- id of the schedule
                task_id (int)   -- task id of the schedule

            Returns:
                object - instance of the schedule class for the given schedule name

            Raises:
                SDKException:
                    if type of the schedule name argument is not string

                    if no schedule exists with the given name
        &#34;&#34;&#34;

        schedule_id = self._get_schedule_id(schedule_name, schedule_id, task_id)
        if schedule_id:
            return Schedule(self.class_object, schedule_id=schedule_id, task_id=self.schedules[schedule_id][&#39;task_id&#39;])

        raise SDKException(&#39;Schedules&#39;,&#39;105&#39;)

    def delete(self, schedule_name=None, schedule_id=None, task_id=None):
        &#34;&#34;&#34;deletes the specified schedule name.

                    Args:
                        schedule_name (str)  --  name of the Schedule
                        schedule_id (int) -- id of the schedule
                        task_id (int)   -- task id of the schedule

                    Raises:
                        SDKException:
                            if type of the schedule name argument is not string
                            if no schedule exists with the given name
        &#34;&#34;&#34;

        schedule_id = self._get_schedule_id(schedule_name, schedule_id, task_id)
        if schedule_id:
            request_json = {
                &#34;TMMsg_TaskOperationReq&#34;:
                    {
                        &#34;opType&#34;: 3,
                        &#34;subtaskEntity&#34;:
                            [
                                {
                                    &#34;_type_&#34;: 68,
                                    &#34;subtaskId&#34;: schedule_id
                                }
                            ]
                    }
            }

            modify_schedule = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, modify_schedule, request_json)

            if flag:
                if response.json():
                    if &#39;errorCode&#39; in response.json():
                        if response.json()[&#39;errorCode&#39;] == 0:
                            self.refresh()
                        else:
                            raise SDKException(
                                &#39;Schedules&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(
                    response.text)
                exception_message = &#39;Failed to delete schedule\nError: &#34;{0}&#34;&#39;.format(
                    response_string)

                raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, exception_message)
        else:
            raise SDKException(&#39;Schedules&#39;,&#39;105&#39;)

    def refresh(self):
        &#34;&#34;&#34;Refresh the Schedules associated with the Client / Agent / Backupset / Subclient.&#34;&#34;&#34;
        self.schedules = self._get_schedules()


class Schedule:
    &#34;&#34;&#34;Class for performing operations for a specific Schedule.&#34;&#34;&#34;

    def __init__(self, class_object, schedule_name=None, schedule_id=None, task_id=None):

        &#34;&#34;&#34;Initialise the Schedule class instance.

            Args:
                class_object (object)     --  instance of Class Object

                schedule_name      (str)     --  name of the Schedule

                schedule_id        (int)     --   task ids of the Schedule



            Returns:
                object - instance of the Schedule class
        &#34;&#34;&#34;

        from .commcell import Commcell
        self.class_object = class_object

        if isinstance(class_object, Commcell):
            self._commcell_object = class_object
        else:
            self._commcell_object = class_object._commcell_object
        self.schedule_name = &#39;&#39;

        if not schedule_name and not schedule_id:
            raise SDKException(
                &#39;Schedules&#39;,
                &#39;102&#39;,
                &#39;Either Schedule Name or Schedule Id is needed&#39;)

        if schedule_name:
            self.schedule_name = schedule_name.lower()

        if schedule_id:
            self.schedule_id = schedule_id
        else:
            self.schedule_id = self._get_schedule_id()

        if task_id:
            self.task_id = task_id
        else:
            self.task_id = self._get_task_id()

        self._SCHEDULE = self._commcell_object._services[&#39;SCHEDULE&#39;] % (
            self.task_id)
        self._MODIFYSCHEDULE = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        self._freq_type = {
            1: &#39;One_Time&#39;,
            2: &#39;On_Demand&#39;,
            4: &#39;Daily&#39;,
            8: &#39;Weekly&#39;,
            16: &#39;Monthly&#39;,
            32: &#39;Monthly_Relative&#39;,
            64: &#39;Yearly&#39;,
            128: &#39;Yearly_Relative&#39;,
            1024: &#39;Automatic&#39;,
            4096: &#39;Continuous&#39;
        }

        self._week_of_month = {
            &#39;1&#39;: &#39;First&#39;,
            &#39;2&#39;: &#39;Second&#39;,
            &#39;3&#39;: &#39;Third&#39;,
            &#39;4&#39;: &#39;Fourth&#39;,
            &#39;5&#39;: &#39;Last&#39;
        }

        self.task_operation_type = {
            1: &#39;ALL_BACKUP_JOBS&#39;,
            2: &#39;BACKUP&#39;,
            1001: &#39; RESTORE&#39;,
            2000: &#39;ADMIN&#39;,
            2001: &#39;WORK_FLOW&#39;,
            4002: &#39;DRBACKUP&#39;,
            4003: &#39;AUX_COPY&#39;,
            4004: &#39;REPORT&#39;,
            4018: &#39;DATA_AGING&#39;,
            4019: &#39;DOWNLOAD_UPDATES&#39;,
            4020: &#39;INSTALL_UPDATES&#39;
        }

        self._criteria = {}
        self._pattern = {}
        self._task_options = {}
        self._associations_json = {}
        self._description = None
        self._alert_type = None
        self._sub_task_option = None
        self._automatic_pattern = {}
        self.virtualServerRstOptions = None
        self._schedule_disabled = None
        self.refresh()

    @property
    def subtask_id(self):
        &#34;&#34;&#34;
        Property which returns subtask id of the schedule
        Returns (int) -- Subtask id
        &#34;&#34;&#34;
        return self.schedule_id

    def _get_schedule_id(self):
        &#34;&#34;&#34;
        Gets a schedule ID dict for the schedule
        Returns (int) -- schedule ID
        &#34;&#34;&#34;
        schedules_obj = Schedules(self.class_object)
        return schedules_obj.get(self.schedule_name).schedule_id

    def _get_task_id(self):
        &#34;&#34;&#34;
        Gets a schedule ID dict for the schedule
        Returns (int) -- schedule ID
        &#34;&#34;&#34;
        schedules_obj = Schedules(self.class_object)
        return schedules_obj.schedules.get(self.schedule_id).get(&#39;task_id&#39;)

    def _get_schedule_properties(self):
        &#34;&#34;&#34;Gets the properties of this Schedule.

            Returns:
                dict - dictionary consisting of the properties of this Schedule

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SCHEDULE)

        if flag:
            if response.json() and &#39;taskInfo&#39; in response.json():
                _task_info = response.json()[&#39;taskInfo&#39;]

                if &#39;associations&#39; in _task_info:
                    self._associations_json = _task_info[&#39;associations&#39;]

                if &#39;task&#39; in _task_info:
                    self._task_json = _task_info[&#39;task&#39;]

                    # Get status of schedule enabled/disabled
                    self._schedule_disabled = self._task_json.get(&#39;taskFlags&#39;, {}).get(&#39;disabled&#39;)

                for subtask in _task_info[&#39;subTasks&#39;]:
                    self._sub_task_option = subtask[&#39;subTask&#39;]
                    if self._sub_task_option[&#39;subTaskId&#39;] == self.schedule_id:
                        self.schedule_name = self._sub_task_option[&#39;subTaskName&#39;]
                        if &#39;operationType&#39; in subtask[&#39;subTask&#39;]:
                            self.operation_type = subtask[&#39;subTask&#39;][&#39;operationType&#39;]
                        else:
                            continue

                        if &#39;pattern&#39; in subtask:
                            self._pattern = subtask[&#39;pattern&#39;]
                        else:
                            continue

                        if &#39;options&#39; in subtask:
                            self._task_options = subtask[&#39;options&#39;]
                            if &#39;restoreOptions&#39; in self._task_options:
                                if &#39;virtualServerRstOption&#39; in self._task_options[&#39;restoreOptions&#39;]:
                                    self.virtualServerRstOptions = self._task_options[&#39;restoreOptions&#39;][
                                        &#39;virtualServerRstOption&#39;]

                            if &#39;commonOpts&#39; in self._task_options:
                                if &#39;automaticSchedulePattern&#39; in self._task_options[&#34;commonOpts&#34;]:
                                    self._automatic_pattern = self._task_options[
                                        &#34;commonOpts&#34;][&#39;automaticSchedulePattern&#39;]

                            if &#39;backupOpts&#39; in self._task_options:
                                if &#39;dataOpt&#39; in self._task_options[&#39;backupOpts&#39;]:
                                    if isinstance(self._automatic_pattern, dict):
                                        _data_opt = self._task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;]
                                        self._automatic_pattern.update(_data_opt)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def is_disabled(self):
        &#34;&#34;&#34;
        Get the schedule status
        Returns:
             (Bool):True if the schedule is disabled otherwise returns False
        &#34;&#34;&#34;
        return self._schedule_disabled

    @property
    def schedule_freq_type(self):
        &#34;&#34;&#34;
        get the schedule frequency type
        Returns:
            (str) the schedule frequency type
        &#34;&#34;&#34;
        return self._freq_type[self._pattern[&#39;freq_type&#39;]]

    @property
    def name(self):
        &#34;&#34;&#34;
                gets the name of the schedule
                Returns:
                     (str) The schedule name
        &#34;&#34;&#34;
        return self.schedule_name

    @name.setter
    def name(self, new_name):
        &#34;&#34;&#34;
                sets the name of the schedule
                Args:
                     new_name (str) -- New name for the schedule
        &#34;&#34;&#34;
        self.schedule_name = new_name
        self._sub_task_option[&#39;subTaskName&#39;] = new_name
        self._sub_task_option[&#39;subTask&#39;][&#39;subtaskName&#39;] = new_name
        self._modify_task_properties()

    @property
    def one_time(self):
        &#34;&#34;&#34;
        gets the one time schedule pattern
        Returns:
             (dict) The schedule pattern
                {
                     &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                     &#34;active_start_time&#34;: time_in_%h:%m (str)
                }

                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;One_Time&#39;:
            return {
                &#39;active_start_date&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_date&#39;],
                    &#39;%m/%d/%Y&#39;, False),
                &#39;active_start_time&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_time&#39;],
                    &#39;%H:%M&#39;, False)
            }

        return False

    @one_time.setter
    def one_time(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
        send only required keys to change only those values
        Args:
             pattern_dict (dict) -- Dictonary with the schedule pattern
                {
                                 &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                                 &#34;active_start_time&#34;: time_in_%h:%m (str)
                }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;one_time&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def daily(self):
        &#34;&#34;&#34;
            gets the daily schedule
            Returns: (dict) -- The schedule pattern
                    {
                                     &#34;active_start_time&#34;: time_in_%H/%S (str),
                                     &#34;repeat_days&#34;: days_to_repeat (int)
                    }
            False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Daily&#39;:
            return {&#39;active_start_time&#39;: SchedulePattern._time_converter(
                self._pattern[&#39;active_start_time&#39;], &#39;%H:%M&#39;, False),
                &#39;repeat_days&#39;: self._pattern[&#39;freq_recurrence_factor&#39;]
            }
        return False

    @daily.setter
    def daily(self, pattern_dict):
        &#34;&#34;&#34;
                sets the pattern type as daily with the parameters provided
                send only required keys to change only those values
                Args:
                     pattern_dict (dict) -- Dictionary with the schedule pattern
                  {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                  }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;daily&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def weekly(self):
        &#34;&#34;&#34;
        gets the weekly schedule
        Returns (dict) -- The schedule pattern
                {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                }
        False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Weekly&#39;:
            _freq = self._pattern[&#39;freq_interval&#39;]
            return {
                &#39;active_start_time&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_time&#39;],
                    &#39;%H:%M&#39;,
                    False),
                &#39;repeat_weeks&#39;: self._pattern[&#39;freq_recurrence_factor&#39;],
                &#39;weekdays&#39;: [
                    SchedulePattern._days_to_run[x] for x in list(
                        SchedulePattern._days_to_run.keys()) if _freq &amp; x &gt; 0]}
        return False

    @weekly.setter
    def weekly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as weekly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                        }
        &#34;&#34;&#34;

        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;weekly&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def monthly(self):
        &#34;&#34;&#34;
        gets the monthly schedule
        Returns: (dict) -- the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;repeat_months&#34;: months_to_repeat (int)
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Monthly&#39;:
            return {
                &#39;active_start_time&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_time&#39;],
                    &#39;%H:%M&#39;,
                    False),
                &#39;repeat_months&#39;: self._pattern[&#39;freq_recurrence_factor&#39;],
                &#39;on_day&#39;: self._pattern[&#39;freq_interval&#39;]}
        return False

    @monthly.setter
    def monthly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;repeat_months&#34;: months_to_repeat (int)
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;monthly&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def monthly_relative(self):
        &#34;&#34;&#34;
        gets the monthly_relative schedule
            Returns: (dict) -- The schedule pattern
                        {
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;relative_time&#34;: relative day of the schedule (str)&#39;first&#39;,&#39;second&#39;,..
                             &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                             &#34;repeat_months&#34;: months_to_repeat
                        }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Monthly_Relative&#39;:
            return {
                &#39;active_start_time&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_time&#39;],
                    &#39;%H:%M&#39;,
                    False),
                &#39;relative_time&#39;: SchedulePattern._relative_day[
                    self._pattern[&#39;freq_relative_interval&#39;]],
                &#39;relative_weekday&#39;: SchedulePattern._relative_weekday[
                    self._pattern[&#39;freq_interval&#39;]],
                &#39;repeat_months&#39;: self._pattern[&#39;freq_recurrence_factor&#39;]}
        return False

    @monthly_relative.setter
    def monthly_relative(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly_relative with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;repeat_months&#34;: months_to_repeat
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;monthly_relative&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def yearly(self):
        &#34;&#34;&#34;
        gets the yearly schedule
                Returns: (dict) -- The schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Yearly&#39;:
            return {&#39;active_start_time&#39;:
                    SchedulePattern._time_converter(self._pattern[&#39;active_start_time&#39;],
                                                    &#39;%H:%M&#39;, False),
                    &#39;on_month&#39;: calendar.month_name[self._pattern[&#39;freq_recurrence_factor&#39;]],
                    &#39;on_day&#39;: self._pattern[&#39;freq_interval&#39;]
                    }
        return False

    @yearly.setter
    def yearly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;yearly&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def yearly_relative(self):
        &#34;&#34;&#34;
        gets the yearly_relative schedule
                Returns: (dict) The schedule pattern
                    {
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;relative_time&#34;: relative day of the schedule (str)&#39;first&#39;,&#39;second&#39;,..
                             &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                             &#34;on_month&#34;: month to run the schedule(str) January, Febuary...
                    }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Yearly_Relative&#39;:
            return {&#39;active_start_time&#39;:
                    SchedulePattern._time_converter(self._pattern[&#39;active_start_time&#39;],
                                                    &#39;%H:%M&#39;, False),
                    &#39;relative_time&#39;: SchedulePattern._relative_day
                    [self._pattern[&#39;freq_relative_interval&#39;]],
                    &#39;relative_weekday&#39;: SchedulePattern._relative_weekday
                    [self._pattern[&#39;freq_interval&#39;]],
                    &#39;on_month&#39;: calendar.month_name[self._pattern[&#39;freq_recurrence_factor&#39;]]
                    }
        return False

    @yearly_relative.setter
    def yearly_relative(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly_relative with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;on_month&#34;: month to run the schedule(str) January, February...
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;yearly_relative&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def continuous(self):
        &#34;&#34;&#34;
        gets the continuous schedule
                Returns: (dict) -- The schedule pattern
                        {
                                 job_interval: interval between jobs in mins(int)
                }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Continuous&#39;:
            return {
                &#39;job_interval&#39;: self._pattern[&#39;freq_interval&#39;]
            }
        return False

    @continuous.setter
    def continuous(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                {
                                 job_interval: interval between jobs in mins(int)
                }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;continuous&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def automatic(self):
        &#34;&#34;&#34;
        gets the automatic schedule
                Returns: (dict) -- The schedule pattern
                        {
                                 min_interval_hours: minimum hours between jobs(int)
                                 min_interval_minutes: minimum minutes between jobs(int)
                                 max_interval_hours: maximum hours between jobs(int)
                                 max_interval_minutes: maximum minutes between jobs(int)
                                 min_sync_interval_hours: minimum sync hours
                                                                        between jobs(int)
                                 min_sync_interval_minutes: minimum sync minutes
                                                                        between jobs(int)
                                 ignore_opwindow_past_maxinterval: (bool)
                                 wired_network_connection: (bool)
                                 min_network_bandwidth: (int) kbps
                                 specific_network: (dict){ip_address:(str),subnet:(int)}
                                 dont_use_metered_network: (bool)
                                 ac_power: (bool)
                                 stop_if_on_battery: (bool)
                                 stop_sleep_if_runningjob: (bool)
                                 cpu_utilization_below : (int)%
                                 cpu_utilization_above : (int)%
                                 run_synthetic_full : (str: every_x_days/extended_retention/
                                 space_reclaim)
                                 days_between_synthetic_full : (int)
                        }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Automatic&#39;:
            pattern = {
                &#34;min_interval_hours&#34;: self._automatic_pattern[&#39;minBackupInterval&#39;],
                &#34;min_interval_minutes&#34;: self._automatic_pattern[&#39;minBackupIntervalMinutes&#39;],
                &#34;max_interval_hours&#34;: self._automatic_pattern[&#39;maxBackupInterval&#39;],
                &#34;max_interval_minutes&#34;: self._automatic_pattern[&#39;maxBackupIntervalMinutes&#39;],
                &#34;min_sync_interval_hours&#34;: self._automatic_pattern[&#39;minSyncInterval&#39;],
                &#34;min_sync_interval_minutes&#34;: self._automatic_pattern[&#39;minSyncIntervalMinutes&#39;],
                &#34;ignore_opwindow_past_maxinterval&#34;: self._automatic_pattern[&#39;ignoreOpWindowPastMaxInterval&#39;],
                &#34;wired_network_connection&#34;: self._automatic_pattern.get(&#39;wiredNetworkConnection&#39;,
                                                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;min_network_bandwidth&#34;: self._automatic_pattern.get(&#39;minNetworkBandwidth&#39;,
                                                                     {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;specific_network&#34;: self._automatic_pattern.get(&#39;specfificNetwork&#39;,
                                                                {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;dont_use_metered_network&#34;: self._automatic_pattern.get(&#39;dontUseMeteredNetwork&#39;,
                                                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;ac_power&#34;: self._automatic_pattern.get(&#39;acPower&#39;,
                                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;stop_if_on_battery&#34;: self._automatic_pattern.get(&#39;stopIfOnBattery&#39;,
                                                                  {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;stop_sleep_if_runningjob&#34;: self._automatic_pattern.get(&#39;stopSleepIfBackUp&#39;,
                                                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;cpu_utilization_below&#34;: self._automatic_pattern.get(&#39;cpuUtilization&#39;,
                                                                     {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;cpu_utilization_above&#34;: self._automatic_pattern.get(&#39;cpuUtilizationAbove&#39;,
                                                                     {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;run_synthetic_full&#34;: &#39;every_x_days&#39;
            }

            if (&#39;useAutomaticIntervalForSyntheticFull&#39; in self._automatic_pattern and
                    self._automatic_pattern[&#39;useAutomaticIntervalForSyntheticFull&#39;]):
                pattern[&#39;run_synthetic_full&#39;] = &#39;extended_retention&#39;

            if (&#39;enableRunFullConsolidationBackup&#39; in self._automatic_pattern and
                    self._automatic_pattern[&#39;enableRunFullConsolidationBackup&#39;]):
                pattern[&#39;run_synthetic_full&#39;] = &#39;space_reclaim&#39;

            if (&#39;daysBetweenSyntheticBackup&#39; in self._automatic_pattern and
                    self._automatic_pattern[&#39;daysBetweenSyntheticBackup&#39;]):
                pattern[&#39;days_between_synthetic_full&#39;] = self._automatic_pattern[
                    &#39;daysBetweenSyntheticBackup&#39;]

            return pattern
        return False

    @automatic.setter
    def automatic(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
                send only required keys to change only those values
                Args:
                     pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                             min_interval_hours: minimum hours between jobs(int)
                             min_interval_minutes: minimum minutes between jobs(int)
                             max_interval_hours: maximum hours between jobs(int)
                             max_interval_minutes: maximum minutes between jobs(int)
                             min_sync_interval_hours: minimum sync hours
                                                                    between jobs(int)
                             min_sync_interval_minutes: minimum sync minutes
                                                                    between jobs(int)
                             ignore_opwindow_past_maxinterval: (bool)
                             wired_network_connection: (bool)
                             min_network_bandwidth: (int) kbps
                             specific_network: (dict){ip_address:(str),subnet:(int)}
                             dont_use_metered_network: (bool)
                             ac_power: (bool)
                             stop_if_on_battery: (bool)
                             stop_sleep_if_runningjob: (bool)
                             cpu_utilization_below : (int)%
                             cpu_utilization_above : (int)%
                             run_synthetic_full : (str: every_x_days/extended_retention/
                             space_reclaim)
                             days_between_synthetic_full : (int)
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;automatic&#39;
        schedule_pattern = SchedulePattern(self._automatic_pattern)
        self._pattern = {&#34;freq_type&#34;: 1024}
        if &#39;commonOpts&#39; in self._task_options:
            self._task_options[&#34;commonOpts&#34;][&#34;automaticSchedulePattern&#34;] = \
                schedule_pattern.create_schedule_pattern(pattern_dict)
        else:
            self._task_options[&#34;commonOpts&#34;] = \
                {&#34;automaticSchedulePattern&#34;: schedule_pattern.create_schedule_pattern(
                    pattern_dict)}

        if &#39;run_synthetic_full&#39; in pattern_dict:
            synthetic_pattern = pattern_dict[&#39;run_synthetic_full&#39;]

            if synthetic_pattern == &#39;every_x_days&#39;:
                synthetic_interval = pattern_dict.get(
                    &#39;days_between_synthetic_full&#39;, 30)
            else:
                synthetic_interval = 30

            _data_opt = {
                &#39;autoCopy&#39;: True,
                &#39;daysBetweenSyntheticBackup&#39;: synthetic_interval,
                &#39;useAutomaticIntervalForSyntheticFull&#39;: (
                        synthetic_pattern == &#39;extended_retention&#39;),
                &#39;enableRunFullConsolidationBackup&#39;: (
                        synthetic_pattern == &#39;space_reclaim&#39;)
            }

            if &#39;backupOpts&#39; in self._task_options:
                if &#39;dataOpt&#39; in self._task_options[&#34;backupOpts&#34;]:
                    self._task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;].update(_data_opt)
                else:
                    self._task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;] = _data_opt
            else:
                self._task_options[&#39;backupOpts&#39;] = {
                    &#39;dataOpt&#39;: _data_opt
                }

        self._modify_task_properties()

    @property
    def active_start_date(self):
        &#34;&#34;&#34;
        gets the start date of the schedule
        Returns: (str) -- date in %m/%d/%Y
        &#34;&#34;&#34;
        return SchedulePattern._time_converter(
            self._pattern[&#39;active_start_date&#39;], &#39;%m/%d/%Y&#39;, False)

    @active_start_date.setter
    def active_start_date(self, active_start_date):
        &#34;&#34;&#34;
        sets the start date of the schedule
        Args:
            active_start_date (str) -- date in %m/%d/%Y
        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;active_start_date&#39;] = active_start_date
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def active_start_time(self):
        &#34;&#34;&#34;
                gets the start time of the schedule
                Returns: (str) -- time in %H/%S
        &#34;&#34;&#34;
        return SchedulePattern._time_converter(
            self._pattern[&#39;active_start_time&#39;], &#39;%H:%M&#39;, False)

    @active_start_time.setter
    def active_start_time(self, active_start_time):
        &#34;&#34;&#34;
        sets the start time of the schedule
        Args:
            active_start_time (str) -- time in %H/%S
        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;active_start_time&#39;] = active_start_time
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def active_end_date(self):
        &#34;&#34;&#34;
        gets the end date of the schedule if present
        Returns: (str) -- date in %m/%d/%Y
        &#34;&#34;&#34;
        if &#34;active_end_date&#34; in self._pattern:
            if self._pattern[&#34;active_end_date&#34;]:
                return SchedulePattern._time_converter(
                    self._pattern[&#39;active_end_date&#39;], &#39;%m/%d/%Y&#39;, False)
        return False

    @active_end_date.setter
    def active_end_date(self, active_start_date):
        &#34;&#34;&#34;
        sets the end date for the schedule
        Args:
        active_start_date (str) -- date in %m/%d/%Y
        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;active_end_date&#39;] = active_start_date
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def exception_dates(self):
        &#34;&#34;&#34;
        returns a list of exception days if present
        Returns:
            (list) -- exception days in a schedule
        &#34;&#34;&#34;

        if &#34;repeatPattern&#34; in self._pattern:
            for repeat_pattern in self._pattern[&#34;repeatPattern&#34;]:
                if repeat_pattern.get(&#34;exception&#34;):
                    _on_day_number = repeat_pattern.get(&#34;onDayNumber&#34;)
                    day = 1
                    exceptions = []
                    while day &lt;= 31 and _on_day_number != 0:
                        if _on_day_number &amp; 1 == 1:
                            exceptions.append(day)
                        _on_day_number = _on_day_number &gt;&gt; 1
                        day += 1
                    return exceptions
        return False

    @exception_dates.setter
    def exception_dates(self, day_list):
        &#34;&#34;&#34;
        sets exception days provided as input for the schedule
        Args:
            day_list: (list) -- exception days to set for the schedule

        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;exception_dates&#39;] = day_list
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def end_after(self):
        &#34;&#34;&#34;
        gets the maximum occurence of the schedule if present
        Returns: (int) -- end occurence
        &#34;&#34;&#34;
        return self._pattern.get(&#34;active_end_occurence&#34;, False)

    @end_after.setter
    def end_after(self, end_after):
        &#34;&#34;&#34;
        sets the end date for the schedule
        Args:
        end_after: (int) -- number of times the schedule should run

        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;end_after&#39;] = end_after
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def repeat_pattern(self):
        &#34;&#34;&#34;
        gets the repeat pattern in a schedule if present
        Returns: (dict) -- the repeat pattern
                {
                    &#34;repeat_every&#34;: repeat_every,
                    &#34;repeat_end&#34;: repeat_end
                    }
        &#34;&#34;&#34;

        if self._pattern.get(&#34;freq_subday_interval&#34;, 0):
            _subday_interval = self._pattern[&#34;freq_subday_interval&#34;]
            repeat_every = &#34;{0}:0{1}&#34;.format(int(_subday_interval / 3600), int(
                ((_subday_interval / 60) - ((_subday_interval / 3600) * 60))))
            repeat_end = SchedulePattern._time_converter(
                self._pattern[&#34;active_end_time&#34;], &#34;%H:%M&#34;, utc_to_epoch=False)
            return {
                &#34;repeat_every&#34;: repeat_every,
                &#34;repeat_end&#34;: repeat_end
            }
        return False

    @repeat_pattern.setter
    def repeat_pattern(self, pattern_json):
        &#34;&#34;&#34;
        sets a repeat pattern for the schedule
        Args:
            pattern_json: (Dict) -- containing the repeat every and repeat end parameters

        &#34;&#34;&#34;
        pattern_json[&#39;freq_type&#39;] = self.schedule_freq_type
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_json)
        self._modify_task_properties()

    def run_now(self, return_multiple_jobs=False):
        &#34;&#34;&#34;
        Triggers the schedule to run immediately

        Returns: job id/ Job IDs.

        Args: return_multiple_jobs (bool) -- if set to True, return multiple jobs, default: False.

        Raises:
            SDKException:
                Response received is empty.
                If no job id is found.
        &#34;&#34;&#34;
        request_json = {
            &#34;TMMsg_TaskOperationReq&#34;:
                {
                    &#34;opType&#34;: 5,
                    &#34;subtaskEntity&#34;:
                        [
                            {
                                &#34;_type_&#34;: 68,
                                &#34;subtaskId&#34;: self.subtask_id,
                                &#34;taskName&#34;: &#34;&#34;,
                                &#34;subtaskName&#34;: self.schedule_name,
                                &#34;taskId&#34;: self.task_id
                            }
                        ],
                    &#34;taskIds&#34;:
                        [
                            self.task_id
                        ]
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._MODIFYSCHEDULE, request_json
        )
        if response.json():
            if &#34;jobIds&#34; in response.json():
                if(return_multiple_jobs):
                    job_id = [int(i) for i in response.json()[&#39;jobIds&#39;]]
                else:
                    job_id = str(response.json()[&#34;jobIds&#34;][0])

                return job_id
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, &#39;JobID not found in response&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def _modify_task_properties(self):
        &#34;&#34;&#34;
        modifies the task properties of the schedule
        Exception:
            if modification of the schedule failed
        &#34;&#34;&#34;
        request_json = {
            &#39;TMMsg_ModifyTaskReq&#39;:
                {
                    &#39;taskInfo&#39;:
                        {
                            &#39;associations&#39;: self._associations_json,
                            &#39;task&#39;: self._task_json,
                            &#39;subTasks&#39;:
                                [
                                    {

                                        &#39;subTask&#39;: self._sub_task_option,
                                        &#39;pattern&#39;: self._pattern,
                                        &#39;options&#39;: self._task_options

                                    }
                                ]
                        }
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._MODIFYSCHEDULE, request_json
        )
        output = self._process_schedule_update_response(flag, response)
        self.refresh()

        if output[0]:
            return

        o_str = &#39;Failed to update properties of Schedule\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, o_str.format(output[2]))

    def enable(self):
        &#34;&#34;&#34;Enable a schedule.

                    Raises:
                        SDKException:
                            if failed to enable schedule

                            if response is empty

                            if response is not success
                &#34;&#34;&#34;
        enable_request = self._commcell_object._services[&#39;ENABLE_SCHEDULE&#39;]
        request_text = &#34;taskId={0}&#34;.format(self.task_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, enable_request, request_text)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Schedules&#39;,
                            &#39;102&#39;,
                            &#39;Failed to enable Schedule\nError: &#34;{0}&#34;&#39;.format(error_message))
                    else:
                        raise SDKException(
                            &#39;Schedules&#39;, &#39;102&#39;, &#34;Failed to enable Schedule&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def disable(self):
        &#34;&#34;&#34;Disable a Schedule.

            Raises:
                SDKException:
                    if failed to disable Schedule

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        disable_request = self._commcell_object._services[&#39;DISABLE_SCHEDULE&#39;]

        request_text = &#34;taskId={0}&#34;.format(self.task_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, disable_request, request_text)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Schedules&#39;,
                            &#39;102&#39;,
                            &#39;Failed to disable Schedule\nError: &#34;{0}&#34;&#39;.format(error_message))
                    else:
                        raise SDKException(
                            &#39;Schedules&#39;, &#39;102&#39;, &#34;Failed to disable Schedule&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_schedule_update_response(self, flag, response):
        &#34;&#34;&#34;
        processes the response received post update request
        Args:
        flag: (bool) -- True or false based on response
        response: (dict) response from modify request
        Returns:
            flag: (Bool) -- based on success and failure
            error_code: (int) -- error_code from response
            error_message: (str) -- error_message from the response if any
        &#34;&#34;&#34;
        task_id = None
        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    task_id = str(response.json()[&#34;taskId&#34;])

                    if task_id:
                        return True, &#34;0&#34;, &#34;&#34;

                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return True, &#34;0&#34;, &#34;&#34;

                    if error_message:
                        return False, error_code, error_message
                    else:
                        return False, error_code, &#34;&#34;
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Schedule.&#34;&#34;&#34;
        self._get_schedule_properties()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.schedules.OperationType"><code class="flex name class">
<span>class <span class="ident">OperationType</span></span>
</code></dt>
<dd>
<div class="desc"><p>Operation Types supported to get schedules of particular optype</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L162-L165" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OperationType:
    &#34;&#34;&#34; Operation Types supported to get schedules of particular optype&#34;&#34;&#34;
    REPORTS = &#39;Reports&#39;
    DATA_AGING = &#39;DATA_AGING&#39;</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.schedules.OperationType.DATA_AGING"><code class="name">var <span class="ident">DATA_AGING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.schedules.OperationType.REPORTS"><code class="name">var <span class="ident">REPORTS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.schedules.Schedule"><code class="flex name class">
<span>class <span class="ident">Schedule</span></span>
<span>(</span><span>class_object, schedule_name=None, schedule_id=None, task_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations for a specific Schedule.</p>
<p>Initialise the Schedule class instance.</p>
<h2 id="args">Args</h2>
<p>class_object (object)
&ndash;
instance of Class Object</p>
<p>schedule_name
(str)
&ndash;
name of the Schedule</p>
<p>schedule_id
(int)
&ndash;
task ids of the Schedule</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Schedule class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1382-L2489" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Schedule:
    &#34;&#34;&#34;Class for performing operations for a specific Schedule.&#34;&#34;&#34;

    def __init__(self, class_object, schedule_name=None, schedule_id=None, task_id=None):

        &#34;&#34;&#34;Initialise the Schedule class instance.

            Args:
                class_object (object)     --  instance of Class Object

                schedule_name      (str)     --  name of the Schedule

                schedule_id        (int)     --   task ids of the Schedule



            Returns:
                object - instance of the Schedule class
        &#34;&#34;&#34;

        from .commcell import Commcell
        self.class_object = class_object

        if isinstance(class_object, Commcell):
            self._commcell_object = class_object
        else:
            self._commcell_object = class_object._commcell_object
        self.schedule_name = &#39;&#39;

        if not schedule_name and not schedule_id:
            raise SDKException(
                &#39;Schedules&#39;,
                &#39;102&#39;,
                &#39;Either Schedule Name or Schedule Id is needed&#39;)

        if schedule_name:
            self.schedule_name = schedule_name.lower()

        if schedule_id:
            self.schedule_id = schedule_id
        else:
            self.schedule_id = self._get_schedule_id()

        if task_id:
            self.task_id = task_id
        else:
            self.task_id = self._get_task_id()

        self._SCHEDULE = self._commcell_object._services[&#39;SCHEDULE&#39;] % (
            self.task_id)
        self._MODIFYSCHEDULE = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        self._freq_type = {
            1: &#39;One_Time&#39;,
            2: &#39;On_Demand&#39;,
            4: &#39;Daily&#39;,
            8: &#39;Weekly&#39;,
            16: &#39;Monthly&#39;,
            32: &#39;Monthly_Relative&#39;,
            64: &#39;Yearly&#39;,
            128: &#39;Yearly_Relative&#39;,
            1024: &#39;Automatic&#39;,
            4096: &#39;Continuous&#39;
        }

        self._week_of_month = {
            &#39;1&#39;: &#39;First&#39;,
            &#39;2&#39;: &#39;Second&#39;,
            &#39;3&#39;: &#39;Third&#39;,
            &#39;4&#39;: &#39;Fourth&#39;,
            &#39;5&#39;: &#39;Last&#39;
        }

        self.task_operation_type = {
            1: &#39;ALL_BACKUP_JOBS&#39;,
            2: &#39;BACKUP&#39;,
            1001: &#39; RESTORE&#39;,
            2000: &#39;ADMIN&#39;,
            2001: &#39;WORK_FLOW&#39;,
            4002: &#39;DRBACKUP&#39;,
            4003: &#39;AUX_COPY&#39;,
            4004: &#39;REPORT&#39;,
            4018: &#39;DATA_AGING&#39;,
            4019: &#39;DOWNLOAD_UPDATES&#39;,
            4020: &#39;INSTALL_UPDATES&#39;
        }

        self._criteria = {}
        self._pattern = {}
        self._task_options = {}
        self._associations_json = {}
        self._description = None
        self._alert_type = None
        self._sub_task_option = None
        self._automatic_pattern = {}
        self.virtualServerRstOptions = None
        self._schedule_disabled = None
        self.refresh()

    @property
    def subtask_id(self):
        &#34;&#34;&#34;
        Property which returns subtask id of the schedule
        Returns (int) -- Subtask id
        &#34;&#34;&#34;
        return self.schedule_id

    def _get_schedule_id(self):
        &#34;&#34;&#34;
        Gets a schedule ID dict for the schedule
        Returns (int) -- schedule ID
        &#34;&#34;&#34;
        schedules_obj = Schedules(self.class_object)
        return schedules_obj.get(self.schedule_name).schedule_id

    def _get_task_id(self):
        &#34;&#34;&#34;
        Gets a schedule ID dict for the schedule
        Returns (int) -- schedule ID
        &#34;&#34;&#34;
        schedules_obj = Schedules(self.class_object)
        return schedules_obj.schedules.get(self.schedule_id).get(&#39;task_id&#39;)

    def _get_schedule_properties(self):
        &#34;&#34;&#34;Gets the properties of this Schedule.

            Returns:
                dict - dictionary consisting of the properties of this Schedule

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SCHEDULE)

        if flag:
            if response.json() and &#39;taskInfo&#39; in response.json():
                _task_info = response.json()[&#39;taskInfo&#39;]

                if &#39;associations&#39; in _task_info:
                    self._associations_json = _task_info[&#39;associations&#39;]

                if &#39;task&#39; in _task_info:
                    self._task_json = _task_info[&#39;task&#39;]

                    # Get status of schedule enabled/disabled
                    self._schedule_disabled = self._task_json.get(&#39;taskFlags&#39;, {}).get(&#39;disabled&#39;)

                for subtask in _task_info[&#39;subTasks&#39;]:
                    self._sub_task_option = subtask[&#39;subTask&#39;]
                    if self._sub_task_option[&#39;subTaskId&#39;] == self.schedule_id:
                        self.schedule_name = self._sub_task_option[&#39;subTaskName&#39;]
                        if &#39;operationType&#39; in subtask[&#39;subTask&#39;]:
                            self.operation_type = subtask[&#39;subTask&#39;][&#39;operationType&#39;]
                        else:
                            continue

                        if &#39;pattern&#39; in subtask:
                            self._pattern = subtask[&#39;pattern&#39;]
                        else:
                            continue

                        if &#39;options&#39; in subtask:
                            self._task_options = subtask[&#39;options&#39;]
                            if &#39;restoreOptions&#39; in self._task_options:
                                if &#39;virtualServerRstOption&#39; in self._task_options[&#39;restoreOptions&#39;]:
                                    self.virtualServerRstOptions = self._task_options[&#39;restoreOptions&#39;][
                                        &#39;virtualServerRstOption&#39;]

                            if &#39;commonOpts&#39; in self._task_options:
                                if &#39;automaticSchedulePattern&#39; in self._task_options[&#34;commonOpts&#34;]:
                                    self._automatic_pattern = self._task_options[
                                        &#34;commonOpts&#34;][&#39;automaticSchedulePattern&#39;]

                            if &#39;backupOpts&#39; in self._task_options:
                                if &#39;dataOpt&#39; in self._task_options[&#39;backupOpts&#39;]:
                                    if isinstance(self._automatic_pattern, dict):
                                        _data_opt = self._task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;]
                                        self._automatic_pattern.update(_data_opt)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def is_disabled(self):
        &#34;&#34;&#34;
        Get the schedule status
        Returns:
             (Bool):True if the schedule is disabled otherwise returns False
        &#34;&#34;&#34;
        return self._schedule_disabled

    @property
    def schedule_freq_type(self):
        &#34;&#34;&#34;
        get the schedule frequency type
        Returns:
            (str) the schedule frequency type
        &#34;&#34;&#34;
        return self._freq_type[self._pattern[&#39;freq_type&#39;]]

    @property
    def name(self):
        &#34;&#34;&#34;
                gets the name of the schedule
                Returns:
                     (str) The schedule name
        &#34;&#34;&#34;
        return self.schedule_name

    @name.setter
    def name(self, new_name):
        &#34;&#34;&#34;
                sets the name of the schedule
                Args:
                     new_name (str) -- New name for the schedule
        &#34;&#34;&#34;
        self.schedule_name = new_name
        self._sub_task_option[&#39;subTaskName&#39;] = new_name
        self._sub_task_option[&#39;subTask&#39;][&#39;subtaskName&#39;] = new_name
        self._modify_task_properties()

    @property
    def one_time(self):
        &#34;&#34;&#34;
        gets the one time schedule pattern
        Returns:
             (dict) The schedule pattern
                {
                     &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                     &#34;active_start_time&#34;: time_in_%h:%m (str)
                }

                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;One_Time&#39;:
            return {
                &#39;active_start_date&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_date&#39;],
                    &#39;%m/%d/%Y&#39;, False),
                &#39;active_start_time&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_time&#39;],
                    &#39;%H:%M&#39;, False)
            }

        return False

    @one_time.setter
    def one_time(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
        send only required keys to change only those values
        Args:
             pattern_dict (dict) -- Dictonary with the schedule pattern
                {
                                 &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                                 &#34;active_start_time&#34;: time_in_%h:%m (str)
                }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;one_time&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def daily(self):
        &#34;&#34;&#34;
            gets the daily schedule
            Returns: (dict) -- The schedule pattern
                    {
                                     &#34;active_start_time&#34;: time_in_%H/%S (str),
                                     &#34;repeat_days&#34;: days_to_repeat (int)
                    }
            False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Daily&#39;:
            return {&#39;active_start_time&#39;: SchedulePattern._time_converter(
                self._pattern[&#39;active_start_time&#39;], &#39;%H:%M&#39;, False),
                &#39;repeat_days&#39;: self._pattern[&#39;freq_recurrence_factor&#39;]
            }
        return False

    @daily.setter
    def daily(self, pattern_dict):
        &#34;&#34;&#34;
                sets the pattern type as daily with the parameters provided
                send only required keys to change only those values
                Args:
                     pattern_dict (dict) -- Dictionary with the schedule pattern
                  {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                  }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;daily&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def weekly(self):
        &#34;&#34;&#34;
        gets the weekly schedule
        Returns (dict) -- The schedule pattern
                {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                }
        False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Weekly&#39;:
            _freq = self._pattern[&#39;freq_interval&#39;]
            return {
                &#39;active_start_time&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_time&#39;],
                    &#39;%H:%M&#39;,
                    False),
                &#39;repeat_weeks&#39;: self._pattern[&#39;freq_recurrence_factor&#39;],
                &#39;weekdays&#39;: [
                    SchedulePattern._days_to_run[x] for x in list(
                        SchedulePattern._days_to_run.keys()) if _freq &amp; x &gt; 0]}
        return False

    @weekly.setter
    def weekly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as weekly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                        }
        &#34;&#34;&#34;

        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;weekly&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def monthly(self):
        &#34;&#34;&#34;
        gets the monthly schedule
        Returns: (dict) -- the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;repeat_months&#34;: months_to_repeat (int)
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Monthly&#39;:
            return {
                &#39;active_start_time&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_time&#39;],
                    &#39;%H:%M&#39;,
                    False),
                &#39;repeat_months&#39;: self._pattern[&#39;freq_recurrence_factor&#39;],
                &#39;on_day&#39;: self._pattern[&#39;freq_interval&#39;]}
        return False

    @monthly.setter
    def monthly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;repeat_months&#34;: months_to_repeat (int)
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;monthly&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def monthly_relative(self):
        &#34;&#34;&#34;
        gets the monthly_relative schedule
            Returns: (dict) -- The schedule pattern
                        {
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;relative_time&#34;: relative day of the schedule (str)&#39;first&#39;,&#39;second&#39;,..
                             &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                             &#34;repeat_months&#34;: months_to_repeat
                        }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Monthly_Relative&#39;:
            return {
                &#39;active_start_time&#39;: SchedulePattern._time_converter(
                    self._pattern[&#39;active_start_time&#39;],
                    &#39;%H:%M&#39;,
                    False),
                &#39;relative_time&#39;: SchedulePattern._relative_day[
                    self._pattern[&#39;freq_relative_interval&#39;]],
                &#39;relative_weekday&#39;: SchedulePattern._relative_weekday[
                    self._pattern[&#39;freq_interval&#39;]],
                &#39;repeat_months&#39;: self._pattern[&#39;freq_recurrence_factor&#39;]}
        return False

    @monthly_relative.setter
    def monthly_relative(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly_relative with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;repeat_months&#34;: months_to_repeat
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;monthly_relative&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def yearly(self):
        &#34;&#34;&#34;
        gets the yearly schedule
                Returns: (dict) -- The schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Yearly&#39;:
            return {&#39;active_start_time&#39;:
                    SchedulePattern._time_converter(self._pattern[&#39;active_start_time&#39;],
                                                    &#39;%H:%M&#39;, False),
                    &#39;on_month&#39;: calendar.month_name[self._pattern[&#39;freq_recurrence_factor&#39;]],
                    &#39;on_day&#39;: self._pattern[&#39;freq_interval&#39;]
                    }
        return False

    @yearly.setter
    def yearly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;yearly&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def yearly_relative(self):
        &#34;&#34;&#34;
        gets the yearly_relative schedule
                Returns: (dict) The schedule pattern
                    {
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;relative_time&#34;: relative day of the schedule (str)&#39;first&#39;,&#39;second&#39;,..
                             &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                             &#34;on_month&#34;: month to run the schedule(str) January, Febuary...
                    }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Yearly_Relative&#39;:
            return {&#39;active_start_time&#39;:
                    SchedulePattern._time_converter(self._pattern[&#39;active_start_time&#39;],
                                                    &#39;%H:%M&#39;, False),
                    &#39;relative_time&#39;: SchedulePattern._relative_day
                    [self._pattern[&#39;freq_relative_interval&#39;]],
                    &#39;relative_weekday&#39;: SchedulePattern._relative_weekday
                    [self._pattern[&#39;freq_interval&#39;]],
                    &#39;on_month&#39;: calendar.month_name[self._pattern[&#39;freq_recurrence_factor&#39;]]
                    }
        return False

    @yearly_relative.setter
    def yearly_relative(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly_relative with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;on_month&#34;: month to run the schedule(str) January, February...
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;yearly_relative&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def continuous(self):
        &#34;&#34;&#34;
        gets the continuous schedule
                Returns: (dict) -- The schedule pattern
                        {
                                 job_interval: interval between jobs in mins(int)
                }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Continuous&#39;:
            return {
                &#39;job_interval&#39;: self._pattern[&#39;freq_interval&#39;]
            }
        return False

    @continuous.setter
    def continuous(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                {
                                 job_interval: interval between jobs in mins(int)
                }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;continuous&#39;
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def automatic(self):
        &#34;&#34;&#34;
        gets the automatic schedule
                Returns: (dict) -- The schedule pattern
                        {
                                 min_interval_hours: minimum hours between jobs(int)
                                 min_interval_minutes: minimum minutes between jobs(int)
                                 max_interval_hours: maximum hours between jobs(int)
                                 max_interval_minutes: maximum minutes between jobs(int)
                                 min_sync_interval_hours: minimum sync hours
                                                                        between jobs(int)
                                 min_sync_interval_minutes: minimum sync minutes
                                                                        between jobs(int)
                                 ignore_opwindow_past_maxinterval: (bool)
                                 wired_network_connection: (bool)
                                 min_network_bandwidth: (int) kbps
                                 specific_network: (dict){ip_address:(str),subnet:(int)}
                                 dont_use_metered_network: (bool)
                                 ac_power: (bool)
                                 stop_if_on_battery: (bool)
                                 stop_sleep_if_runningjob: (bool)
                                 cpu_utilization_below : (int)%
                                 cpu_utilization_above : (int)%
                                 run_synthetic_full : (str: every_x_days/extended_retention/
                                 space_reclaim)
                                 days_between_synthetic_full : (int)
                        }
                False: if schedule type is wrong
        &#34;&#34;&#34;
        if self.schedule_freq_type == &#39;Automatic&#39;:
            pattern = {
                &#34;min_interval_hours&#34;: self._automatic_pattern[&#39;minBackupInterval&#39;],
                &#34;min_interval_minutes&#34;: self._automatic_pattern[&#39;minBackupIntervalMinutes&#39;],
                &#34;max_interval_hours&#34;: self._automatic_pattern[&#39;maxBackupInterval&#39;],
                &#34;max_interval_minutes&#34;: self._automatic_pattern[&#39;maxBackupIntervalMinutes&#39;],
                &#34;min_sync_interval_hours&#34;: self._automatic_pattern[&#39;minSyncInterval&#39;],
                &#34;min_sync_interval_minutes&#34;: self._automatic_pattern[&#39;minSyncIntervalMinutes&#39;],
                &#34;ignore_opwindow_past_maxinterval&#34;: self._automatic_pattern[&#39;ignoreOpWindowPastMaxInterval&#39;],
                &#34;wired_network_connection&#34;: self._automatic_pattern.get(&#39;wiredNetworkConnection&#39;,
                                                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;min_network_bandwidth&#34;: self._automatic_pattern.get(&#39;minNetworkBandwidth&#39;,
                                                                     {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;specific_network&#34;: self._automatic_pattern.get(&#39;specfificNetwork&#39;,
                                                                {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;dont_use_metered_network&#34;: self._automatic_pattern.get(&#39;dontUseMeteredNetwork&#39;,
                                                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;ac_power&#34;: self._automatic_pattern.get(&#39;acPower&#39;,
                                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;stop_if_on_battery&#34;: self._automatic_pattern.get(&#39;stopIfOnBattery&#39;,
                                                                  {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;stop_sleep_if_runningjob&#34;: self._automatic_pattern.get(&#39;stopSleepIfBackUp&#39;,
                                                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;cpu_utilization_below&#34;: self._automatic_pattern.get(&#39;cpuUtilization&#39;,
                                                                     {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;cpu_utilization_above&#34;: self._automatic_pattern.get(&#39;cpuUtilizationAbove&#39;,
                                                                     {&#39;enabled&#39;: False})[&#39;enabled&#39;],
                &#34;run_synthetic_full&#34;: &#39;every_x_days&#39;
            }

            if (&#39;useAutomaticIntervalForSyntheticFull&#39; in self._automatic_pattern and
                    self._automatic_pattern[&#39;useAutomaticIntervalForSyntheticFull&#39;]):
                pattern[&#39;run_synthetic_full&#39;] = &#39;extended_retention&#39;

            if (&#39;enableRunFullConsolidationBackup&#39; in self._automatic_pattern and
                    self._automatic_pattern[&#39;enableRunFullConsolidationBackup&#39;]):
                pattern[&#39;run_synthetic_full&#39;] = &#39;space_reclaim&#39;

            if (&#39;daysBetweenSyntheticBackup&#39; in self._automatic_pattern and
                    self._automatic_pattern[&#39;daysBetweenSyntheticBackup&#39;]):
                pattern[&#39;days_between_synthetic_full&#39;] = self._automatic_pattern[
                    &#39;daysBetweenSyntheticBackup&#39;]

            return pattern
        return False

    @automatic.setter
    def automatic(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
                send only required keys to change only those values
                Args:
                     pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                             min_interval_hours: minimum hours between jobs(int)
                             min_interval_minutes: minimum minutes between jobs(int)
                             max_interval_hours: maximum hours between jobs(int)
                             max_interval_minutes: maximum minutes between jobs(int)
                             min_sync_interval_hours: minimum sync hours
                                                                    between jobs(int)
                             min_sync_interval_minutes: minimum sync minutes
                                                                    between jobs(int)
                             ignore_opwindow_past_maxinterval: (bool)
                             wired_network_connection: (bool)
                             min_network_bandwidth: (int) kbps
                             specific_network: (dict){ip_address:(str),subnet:(int)}
                             dont_use_metered_network: (bool)
                             ac_power: (bool)
                             stop_if_on_battery: (bool)
                             stop_sleep_if_runningjob: (bool)
                             cpu_utilization_below : (int)%
                             cpu_utilization_above : (int)%
                             run_synthetic_full : (str: every_x_days/extended_retention/
                             space_reclaim)
                             days_between_synthetic_full : (int)
                        }
        &#34;&#34;&#34;
        if isinstance(pattern_dict, bool):
            pattern_dict = {}
        pattern_dict[&#39;freq_type&#39;] = &#39;automatic&#39;
        schedule_pattern = SchedulePattern(self._automatic_pattern)
        self._pattern = {&#34;freq_type&#34;: 1024}
        if &#39;commonOpts&#39; in self._task_options:
            self._task_options[&#34;commonOpts&#34;][&#34;automaticSchedulePattern&#34;] = \
                schedule_pattern.create_schedule_pattern(pattern_dict)
        else:
            self._task_options[&#34;commonOpts&#34;] = \
                {&#34;automaticSchedulePattern&#34;: schedule_pattern.create_schedule_pattern(
                    pattern_dict)}

        if &#39;run_synthetic_full&#39; in pattern_dict:
            synthetic_pattern = pattern_dict[&#39;run_synthetic_full&#39;]

            if synthetic_pattern == &#39;every_x_days&#39;:
                synthetic_interval = pattern_dict.get(
                    &#39;days_between_synthetic_full&#39;, 30)
            else:
                synthetic_interval = 30

            _data_opt = {
                &#39;autoCopy&#39;: True,
                &#39;daysBetweenSyntheticBackup&#39;: synthetic_interval,
                &#39;useAutomaticIntervalForSyntheticFull&#39;: (
                        synthetic_pattern == &#39;extended_retention&#39;),
                &#39;enableRunFullConsolidationBackup&#39;: (
                        synthetic_pattern == &#39;space_reclaim&#39;)
            }

            if &#39;backupOpts&#39; in self._task_options:
                if &#39;dataOpt&#39; in self._task_options[&#34;backupOpts&#34;]:
                    self._task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;].update(_data_opt)
                else:
                    self._task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;] = _data_opt
            else:
                self._task_options[&#39;backupOpts&#39;] = {
                    &#39;dataOpt&#39;: _data_opt
                }

        self._modify_task_properties()

    @property
    def active_start_date(self):
        &#34;&#34;&#34;
        gets the start date of the schedule
        Returns: (str) -- date in %m/%d/%Y
        &#34;&#34;&#34;
        return SchedulePattern._time_converter(
            self._pattern[&#39;active_start_date&#39;], &#39;%m/%d/%Y&#39;, False)

    @active_start_date.setter
    def active_start_date(self, active_start_date):
        &#34;&#34;&#34;
        sets the start date of the schedule
        Args:
            active_start_date (str) -- date in %m/%d/%Y
        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;active_start_date&#39;] = active_start_date
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def active_start_time(self):
        &#34;&#34;&#34;
                gets the start time of the schedule
                Returns: (str) -- time in %H/%S
        &#34;&#34;&#34;
        return SchedulePattern._time_converter(
            self._pattern[&#39;active_start_time&#39;], &#39;%H:%M&#39;, False)

    @active_start_time.setter
    def active_start_time(self, active_start_time):
        &#34;&#34;&#34;
        sets the start time of the schedule
        Args:
            active_start_time (str) -- time in %H/%S
        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;active_start_time&#39;] = active_start_time
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def active_end_date(self):
        &#34;&#34;&#34;
        gets the end date of the schedule if present
        Returns: (str) -- date in %m/%d/%Y
        &#34;&#34;&#34;
        if &#34;active_end_date&#34; in self._pattern:
            if self._pattern[&#34;active_end_date&#34;]:
                return SchedulePattern._time_converter(
                    self._pattern[&#39;active_end_date&#39;], &#39;%m/%d/%Y&#39;, False)
        return False

    @active_end_date.setter
    def active_end_date(self, active_start_date):
        &#34;&#34;&#34;
        sets the end date for the schedule
        Args:
        active_start_date (str) -- date in %m/%d/%Y
        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;active_end_date&#39;] = active_start_date
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def exception_dates(self):
        &#34;&#34;&#34;
        returns a list of exception days if present
        Returns:
            (list) -- exception days in a schedule
        &#34;&#34;&#34;

        if &#34;repeatPattern&#34; in self._pattern:
            for repeat_pattern in self._pattern[&#34;repeatPattern&#34;]:
                if repeat_pattern.get(&#34;exception&#34;):
                    _on_day_number = repeat_pattern.get(&#34;onDayNumber&#34;)
                    day = 1
                    exceptions = []
                    while day &lt;= 31 and _on_day_number != 0:
                        if _on_day_number &amp; 1 == 1:
                            exceptions.append(day)
                        _on_day_number = _on_day_number &gt;&gt; 1
                        day += 1
                    return exceptions
        return False

    @exception_dates.setter
    def exception_dates(self, day_list):
        &#34;&#34;&#34;
        sets exception days provided as input for the schedule
        Args:
            day_list: (list) -- exception days to set for the schedule

        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;exception_dates&#39;] = day_list
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def end_after(self):
        &#34;&#34;&#34;
        gets the maximum occurence of the schedule if present
        Returns: (int) -- end occurence
        &#34;&#34;&#34;
        return self._pattern.get(&#34;active_end_occurence&#34;, False)

    @end_after.setter
    def end_after(self, end_after):
        &#34;&#34;&#34;
        sets the end date for the schedule
        Args:
        end_after: (int) -- number of times the schedule should run

        &#34;&#34;&#34;
        pattern_dict = dict()
        pattern_dict[&#39;freq_type&#39;] = self.schedule_freq_type
        pattern_dict[&#39;end_after&#39;] = end_after
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_dict)
        self._modify_task_properties()

    @property
    def repeat_pattern(self):
        &#34;&#34;&#34;
        gets the repeat pattern in a schedule if present
        Returns: (dict) -- the repeat pattern
                {
                    &#34;repeat_every&#34;: repeat_every,
                    &#34;repeat_end&#34;: repeat_end
                    }
        &#34;&#34;&#34;

        if self._pattern.get(&#34;freq_subday_interval&#34;, 0):
            _subday_interval = self._pattern[&#34;freq_subday_interval&#34;]
            repeat_every = &#34;{0}:0{1}&#34;.format(int(_subday_interval / 3600), int(
                ((_subday_interval / 60) - ((_subday_interval / 3600) * 60))))
            repeat_end = SchedulePattern._time_converter(
                self._pattern[&#34;active_end_time&#34;], &#34;%H:%M&#34;, utc_to_epoch=False)
            return {
                &#34;repeat_every&#34;: repeat_every,
                &#34;repeat_end&#34;: repeat_end
            }
        return False

    @repeat_pattern.setter
    def repeat_pattern(self, pattern_json):
        &#34;&#34;&#34;
        sets a repeat pattern for the schedule
        Args:
            pattern_json: (Dict) -- containing the repeat every and repeat end parameters

        &#34;&#34;&#34;
        pattern_json[&#39;freq_type&#39;] = self.schedule_freq_type
        schedule_pattern = SchedulePattern(self._pattern)
        self._pattern = schedule_pattern.create_schedule_pattern(pattern_json)
        self._modify_task_properties()

    def run_now(self, return_multiple_jobs=False):
        &#34;&#34;&#34;
        Triggers the schedule to run immediately

        Returns: job id/ Job IDs.

        Args: return_multiple_jobs (bool) -- if set to True, return multiple jobs, default: False.

        Raises:
            SDKException:
                Response received is empty.
                If no job id is found.
        &#34;&#34;&#34;
        request_json = {
            &#34;TMMsg_TaskOperationReq&#34;:
                {
                    &#34;opType&#34;: 5,
                    &#34;subtaskEntity&#34;:
                        [
                            {
                                &#34;_type_&#34;: 68,
                                &#34;subtaskId&#34;: self.subtask_id,
                                &#34;taskName&#34;: &#34;&#34;,
                                &#34;subtaskName&#34;: self.schedule_name,
                                &#34;taskId&#34;: self.task_id
                            }
                        ],
                    &#34;taskIds&#34;:
                        [
                            self.task_id
                        ]
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._MODIFYSCHEDULE, request_json
        )
        if response.json():
            if &#34;jobIds&#34; in response.json():
                if(return_multiple_jobs):
                    job_id = [int(i) for i in response.json()[&#39;jobIds&#39;]]
                else:
                    job_id = str(response.json()[&#34;jobIds&#34;][0])

                return job_id
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, &#39;JobID not found in response&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def _modify_task_properties(self):
        &#34;&#34;&#34;
        modifies the task properties of the schedule
        Exception:
            if modification of the schedule failed
        &#34;&#34;&#34;
        request_json = {
            &#39;TMMsg_ModifyTaskReq&#39;:
                {
                    &#39;taskInfo&#39;:
                        {
                            &#39;associations&#39;: self._associations_json,
                            &#39;task&#39;: self._task_json,
                            &#39;subTasks&#39;:
                                [
                                    {

                                        &#39;subTask&#39;: self._sub_task_option,
                                        &#39;pattern&#39;: self._pattern,
                                        &#39;options&#39;: self._task_options

                                    }
                                ]
                        }
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._MODIFYSCHEDULE, request_json
        )
        output = self._process_schedule_update_response(flag, response)
        self.refresh()

        if output[0]:
            return

        o_str = &#39;Failed to update properties of Schedule\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, o_str.format(output[2]))

    def enable(self):
        &#34;&#34;&#34;Enable a schedule.

                    Raises:
                        SDKException:
                            if failed to enable schedule

                            if response is empty

                            if response is not success
                &#34;&#34;&#34;
        enable_request = self._commcell_object._services[&#39;ENABLE_SCHEDULE&#39;]
        request_text = &#34;taskId={0}&#34;.format(self.task_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, enable_request, request_text)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Schedules&#39;,
                            &#39;102&#39;,
                            &#39;Failed to enable Schedule\nError: &#34;{0}&#34;&#39;.format(error_message))
                    else:
                        raise SDKException(
                            &#39;Schedules&#39;, &#39;102&#39;, &#34;Failed to enable Schedule&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def disable(self):
        &#34;&#34;&#34;Disable a Schedule.

            Raises:
                SDKException:
                    if failed to disable Schedule

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        disable_request = self._commcell_object._services[&#39;DISABLE_SCHEDULE&#39;]

        request_text = &#34;taskId={0}&#34;.format(self.task_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, disable_request, request_text)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Schedules&#39;,
                            &#39;102&#39;,
                            &#39;Failed to disable Schedule\nError: &#34;{0}&#34;&#39;.format(error_message))
                    else:
                        raise SDKException(
                            &#39;Schedules&#39;, &#39;102&#39;, &#34;Failed to disable Schedule&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_schedule_update_response(self, flag, response):
        &#34;&#34;&#34;
        processes the response received post update request
        Args:
        flag: (bool) -- True or false based on response
        response: (dict) response from modify request
        Returns:
            flag: (Bool) -- based on success and failure
            error_code: (int) -- error_code from response
            error_message: (str) -- error_message from the response if any
        &#34;&#34;&#34;
        task_id = None
        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    task_id = str(response.json()[&#34;taskId&#34;])

                    if task_id:
                        return True, &#34;0&#34;, &#34;&#34;

                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return True, &#34;0&#34;, &#34;&#34;

                    if error_message:
                        return False, error_code, error_message
                    else:
                        return False, error_code, &#34;&#34;
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Schedule.&#34;&#34;&#34;
        self._get_schedule_properties()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.schedules.Schedule.active_end_date"><code class="name">var <span class="ident">active_end_date</span></code></dt>
<dd>
<div class="desc"><p>gets the end date of the schedule if present
Returns: (str) &ndash; date in %m/%d/%Y</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2146-L2156" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def active_end_date(self):
    &#34;&#34;&#34;
    gets the end date of the schedule if present
    Returns: (str) -- date in %m/%d/%Y
    &#34;&#34;&#34;
    if &#34;active_end_date&#34; in self._pattern:
        if self._pattern[&#34;active_end_date&#34;]:
            return SchedulePattern._time_converter(
                self._pattern[&#39;active_end_date&#39;], &#39;%m/%d/%Y&#39;, False)
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.active_start_date"><code class="name">var <span class="ident">active_start_date</span></code></dt>
<dd>
<div class="desc"><p>gets the start date of the schedule
Returns: (str) &ndash; date in %m/%d/%Y</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2100-L2107" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def active_start_date(self):
    &#34;&#34;&#34;
    gets the start date of the schedule
    Returns: (str) -- date in %m/%d/%Y
    &#34;&#34;&#34;
    return SchedulePattern._time_converter(
        self._pattern[&#39;active_start_date&#39;], &#39;%m/%d/%Y&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.active_start_time"><code class="name">var <span class="ident">active_start_time</span></code></dt>
<dd>
<div class="desc"><p>gets the start time of the schedule
Returns: (str) &ndash; time in %H/%S</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2123-L2130" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def active_start_time(self):
    &#34;&#34;&#34;
            gets the start time of the schedule
            Returns: (str) -- time in %H/%S
    &#34;&#34;&#34;
    return SchedulePattern._time_converter(
        self._pattern[&#39;active_start_time&#39;], &#39;%H:%M&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.automatic"><code class="name">var <span class="ident">automatic</span></code></dt>
<dd>
<div class="desc"><p>gets the automatic schedule
Returns: (dict) &ndash; The schedule pattern
{
min_interval_hours: minimum hours between jobs(int)
min_interval_minutes: minimum minutes between jobs(int)
max_interval_hours: maximum hours between jobs(int)
max_interval_minutes: maximum minutes between jobs(int)
min_sync_interval_hours: minimum sync hours
between jobs(int)
min_sync_interval_minutes: minimum sync minutes
between jobs(int)
ignore_opwindow_past_maxinterval: (bool)
wired_network_connection: (bool)
min_network_bandwidth: (int) kbps
specific_network: (dict){ip_address:(str),subnet:(int)}
dont_use_metered_network: (bool)
ac_power: (bool)
stop_if_on_battery: (bool)
stop_sleep_if_runningjob: (bool)
cpu_utilization_below : (int)%
cpu_utilization_above : (int)%
run_synthetic_full : (str: every_x_days/extended_retention/
space_reclaim)
days_between_synthetic_full : (int)
}
False: if schedule type is wrong</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1950-L2024" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def automatic(self):
    &#34;&#34;&#34;
    gets the automatic schedule
            Returns: (dict) -- The schedule pattern
                    {
                             min_interval_hours: minimum hours between jobs(int)
                             min_interval_minutes: minimum minutes between jobs(int)
                             max_interval_hours: maximum hours between jobs(int)
                             max_interval_minutes: maximum minutes between jobs(int)
                             min_sync_interval_hours: minimum sync hours
                                                                    between jobs(int)
                             min_sync_interval_minutes: minimum sync minutes
                                                                    between jobs(int)
                             ignore_opwindow_past_maxinterval: (bool)
                             wired_network_connection: (bool)
                             min_network_bandwidth: (int) kbps
                             specific_network: (dict){ip_address:(str),subnet:(int)}
                             dont_use_metered_network: (bool)
                             ac_power: (bool)
                             stop_if_on_battery: (bool)
                             stop_sleep_if_runningjob: (bool)
                             cpu_utilization_below : (int)%
                             cpu_utilization_above : (int)%
                             run_synthetic_full : (str: every_x_days/extended_retention/
                             space_reclaim)
                             days_between_synthetic_full : (int)
                    }
            False: if schedule type is wrong
    &#34;&#34;&#34;
    if self.schedule_freq_type == &#39;Automatic&#39;:
        pattern = {
            &#34;min_interval_hours&#34;: self._automatic_pattern[&#39;minBackupInterval&#39;],
            &#34;min_interval_minutes&#34;: self._automatic_pattern[&#39;minBackupIntervalMinutes&#39;],
            &#34;max_interval_hours&#34;: self._automatic_pattern[&#39;maxBackupInterval&#39;],
            &#34;max_interval_minutes&#34;: self._automatic_pattern[&#39;maxBackupIntervalMinutes&#39;],
            &#34;min_sync_interval_hours&#34;: self._automatic_pattern[&#39;minSyncInterval&#39;],
            &#34;min_sync_interval_minutes&#34;: self._automatic_pattern[&#39;minSyncIntervalMinutes&#39;],
            &#34;ignore_opwindow_past_maxinterval&#34;: self._automatic_pattern[&#39;ignoreOpWindowPastMaxInterval&#39;],
            &#34;wired_network_connection&#34;: self._automatic_pattern.get(&#39;wiredNetworkConnection&#39;,
                                                                    {&#39;enabled&#39;: False})[&#39;enabled&#39;],
            &#34;min_network_bandwidth&#34;: self._automatic_pattern.get(&#39;minNetworkBandwidth&#39;,
                                                                 {&#39;enabled&#39;: False})[&#39;enabled&#39;],
            &#34;specific_network&#34;: self._automatic_pattern.get(&#39;specfificNetwork&#39;,
                                                            {&#39;enabled&#39;: False})[&#39;enabled&#39;],
            &#34;dont_use_metered_network&#34;: self._automatic_pattern.get(&#39;dontUseMeteredNetwork&#39;,
                                                                    {&#39;enabled&#39;: False})[&#39;enabled&#39;],
            &#34;ac_power&#34;: self._automatic_pattern.get(&#39;acPower&#39;,
                                                    {&#39;enabled&#39;: False})[&#39;enabled&#39;],
            &#34;stop_if_on_battery&#34;: self._automatic_pattern.get(&#39;stopIfOnBattery&#39;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;],
            &#34;stop_sleep_if_runningjob&#34;: self._automatic_pattern.get(&#39;stopSleepIfBackUp&#39;,
                                                                    {&#39;enabled&#39;: False})[&#39;enabled&#39;],
            &#34;cpu_utilization_below&#34;: self._automatic_pattern.get(&#39;cpuUtilization&#39;,
                                                                 {&#39;enabled&#39;: False})[&#39;enabled&#39;],
            &#34;cpu_utilization_above&#34;: self._automatic_pattern.get(&#39;cpuUtilizationAbove&#39;,
                                                                 {&#39;enabled&#39;: False})[&#39;enabled&#39;],
            &#34;run_synthetic_full&#34;: &#39;every_x_days&#39;
        }

        if (&#39;useAutomaticIntervalForSyntheticFull&#39; in self._automatic_pattern and
                self._automatic_pattern[&#39;useAutomaticIntervalForSyntheticFull&#39;]):
            pattern[&#39;run_synthetic_full&#39;] = &#39;extended_retention&#39;

        if (&#39;enableRunFullConsolidationBackup&#39; in self._automatic_pattern and
                self._automatic_pattern[&#39;enableRunFullConsolidationBackup&#39;]):
            pattern[&#39;run_synthetic_full&#39;] = &#39;space_reclaim&#39;

        if (&#39;daysBetweenSyntheticBackup&#39; in self._automatic_pattern and
                self._automatic_pattern[&#39;daysBetweenSyntheticBackup&#39;]):
            pattern[&#39;days_between_synthetic_full&#39;] = self._automatic_pattern[
                &#39;daysBetweenSyntheticBackup&#39;]

        return pattern
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.continuous"><code class="name">var <span class="ident">continuous</span></code></dt>
<dd>
<div class="desc"><p>gets the continuous schedule
Returns: (dict) &ndash; The schedule pattern
{
job_interval: interval between jobs in mins(int)
}
False: if schedule type is wrong</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1916-L1930" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def continuous(self):
    &#34;&#34;&#34;
    gets the continuous schedule
            Returns: (dict) -- The schedule pattern
                    {
                             job_interval: interval between jobs in mins(int)
            }
            False: if schedule type is wrong
    &#34;&#34;&#34;
    if self.schedule_freq_type == &#39;Continuous&#39;:
        return {
            &#39;job_interval&#39;: self._pattern[&#39;freq_interval&#39;]
        }
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.daily"><code class="name">var <span class="ident">daily</span></code></dt>
<dd>
<div class="desc"><p>gets the daily schedule
Returns: (dict) &ndash; The schedule pattern
{
"active_start_time": time_in_%H/%S (str),
"repeat_days": days_to_repeat (int)
}
False: if schedule type is wrong</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1655-L1671" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def daily(self):
    &#34;&#34;&#34;
        gets the daily schedule
        Returns: (dict) -- The schedule pattern
                {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;repeat_days&#34;: days_to_repeat (int)
                }
        False: if schedule type is wrong
    &#34;&#34;&#34;
    if self.schedule_freq_type == &#39;Daily&#39;:
        return {&#39;active_start_time&#39;: SchedulePattern._time_converter(
            self._pattern[&#39;active_start_time&#39;], &#39;%H:%M&#39;, False),
            &#39;repeat_days&#39;: self._pattern[&#39;freq_recurrence_factor&#39;]
        }
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.end_after"><code class="name">var <span class="ident">end_after</span></code></dt>
<dd>
<div class="desc"><p>gets the maximum occurence of the schedule if present
Returns: (int) &ndash; end occurence</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2209-L2215" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def end_after(self):
    &#34;&#34;&#34;
    gets the maximum occurence of the schedule if present
    Returns: (int) -- end occurence
    &#34;&#34;&#34;
    return self._pattern.get(&#34;active_end_occurence&#34;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.exception_dates"><code class="name">var <span class="ident">exception_dates</span></code></dt>
<dd>
<div class="desc"><p>returns a list of exception days if present</p>
<h2 id="returns">Returns</h2>
<p>(list) &ndash; exception days in a schedule</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2172-L2192" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exception_dates(self):
    &#34;&#34;&#34;
    returns a list of exception days if present
    Returns:
        (list) -- exception days in a schedule
    &#34;&#34;&#34;

    if &#34;repeatPattern&#34; in self._pattern:
        for repeat_pattern in self._pattern[&#34;repeatPattern&#34;]:
            if repeat_pattern.get(&#34;exception&#34;):
                _on_day_number = repeat_pattern.get(&#34;onDayNumber&#34;)
                day = 1
                exceptions = []
                while day &lt;= 31 and _on_day_number != 0:
                    if _on_day_number &amp; 1 == 1:
                        exceptions.append(day)
                    _on_day_number = _on_day_number &gt;&gt; 1
                    day += 1
                return exceptions
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.is_disabled"><code class="name">var <span class="ident">is_disabled</span></code></dt>
<dd>
<div class="desc"><p>Get the schedule status</p>
<h2 id="returns">Returns</h2>
<p>(Bool):True if the schedule is disabled otherwise returns False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1572-L1579" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_disabled(self):
    &#34;&#34;&#34;
    Get the schedule status
    Returns:
         (Bool):True if the schedule is disabled otherwise returns False
    &#34;&#34;&#34;
    return self._schedule_disabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.monthly"><code class="name">var <span class="ident">monthly</span></code></dt>
<dd>
<div class="desc"><p>gets the monthly schedule
Returns: (dict) &ndash; the schedule pattern
{
"active_start_time": time_in_%H/%S (str),
"repeat_months": months_to_repeat (int)
"on_day": Day to run schedule (int)
}
False: if schedule type is wrong</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1738-L1758" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def monthly(self):
    &#34;&#34;&#34;
    gets the monthly schedule
    Returns: (dict) -- the schedule pattern
                    {
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;repeat_months&#34;: months_to_repeat (int)
                             &#34;on_day&#34;: Day to run schedule (int)
                    }
            False: if schedule type is wrong
    &#34;&#34;&#34;
    if self.schedule_freq_type == &#39;Monthly&#39;:
        return {
            &#39;active_start_time&#39;: SchedulePattern._time_converter(
                self._pattern[&#39;active_start_time&#39;],
                &#39;%H:%M&#39;,
                False),
            &#39;repeat_months&#39;: self._pattern[&#39;freq_recurrence_factor&#39;],
            &#39;on_day&#39;: self._pattern[&#39;freq_interval&#39;]}
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.monthly_relative"><code class="name">var <span class="ident">monthly_relative</span></code></dt>
<dd>
<div class="desc"><p>gets the monthly_relative schedule
Returns: (dict) &ndash; The schedule pattern
{
"active_start_time": time_in_%H/%S (str),
"relative_time": relative day of the schedule (str)'first','second',..
"relative_weekday": Day to run schedule (str) 'sunday','monday'&hellip;
"repeat_months": months_to_repeat
}
False: if schedule type is wrong</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1780-L1804" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def monthly_relative(self):
    &#34;&#34;&#34;
    gets the monthly_relative schedule
        Returns: (dict) -- The schedule pattern
                    {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;relative_time&#34;: relative day of the schedule (str)&#39;first&#39;,&#39;second&#39;,..
                         &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                         &#34;repeat_months&#34;: months_to_repeat
                    }
            False: if schedule type is wrong
    &#34;&#34;&#34;
    if self.schedule_freq_type == &#39;Monthly_Relative&#39;:
        return {
            &#39;active_start_time&#39;: SchedulePattern._time_converter(
                self._pattern[&#39;active_start_time&#39;],
                &#39;%H:%M&#39;,
                False),
            &#39;relative_time&#39;: SchedulePattern._relative_day[
                self._pattern[&#39;freq_relative_interval&#39;]],
            &#39;relative_weekday&#39;: SchedulePattern._relative_weekday[
                self._pattern[&#39;freq_interval&#39;]],
            &#39;repeat_months&#39;: self._pattern[&#39;freq_recurrence_factor&#39;]}
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>gets the name of the schedule</p>
<h2 id="returns">Returns</h2>
<p>(str) The schedule name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1590-L1597" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;
            gets the name of the schedule
            Returns:
                 (str) The schedule name
    &#34;&#34;&#34;
    return self.schedule_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.one_time"><code class="name">var <span class="ident">one_time</span></code></dt>
<dd>
<div class="desc"><p>gets the one time schedule pattern</p>
<h2 id="returns">Returns</h2>
<p>(dict) The schedule pattern
{
"active_start_date": date_in_%m/%d/%y (str),
"active_start_time": time_in_%h:%m (str)
}</p>
<dl>
<dt><code>
False</code></dt>
<dd>if schedule type is wrong</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1611-L1634" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def one_time(self):
    &#34;&#34;&#34;
    gets the one time schedule pattern
    Returns:
         (dict) The schedule pattern
            {
                 &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                 &#34;active_start_time&#34;: time_in_%h:%m (str)
            }

            False: if schedule type is wrong
    &#34;&#34;&#34;
    if self.schedule_freq_type == &#39;One_Time&#39;:
        return {
            &#39;active_start_date&#39;: SchedulePattern._time_converter(
                self._pattern[&#39;active_start_date&#39;],
                &#39;%m/%d/%Y&#39;, False),
            &#39;active_start_time&#39;: SchedulePattern._time_converter(
                self._pattern[&#39;active_start_time&#39;],
                &#39;%H:%M&#39;, False)
        }

    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.repeat_pattern"><code class="name">var <span class="ident">repeat_pattern</span></code></dt>
<dd>
<div class="desc"><p>gets the repeat pattern in a schedule if present
Returns: (dict) &ndash; the repeat pattern
{
"repeat_every": repeat_every,
"repeat_end": repeat_end
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2232-L2253" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def repeat_pattern(self):
    &#34;&#34;&#34;
    gets the repeat pattern in a schedule if present
    Returns: (dict) -- the repeat pattern
            {
                &#34;repeat_every&#34;: repeat_every,
                &#34;repeat_end&#34;: repeat_end
                }
    &#34;&#34;&#34;

    if self._pattern.get(&#34;freq_subday_interval&#34;, 0):
        _subday_interval = self._pattern[&#34;freq_subday_interval&#34;]
        repeat_every = &#34;{0}:0{1}&#34;.format(int(_subday_interval / 3600), int(
            ((_subday_interval / 60) - ((_subday_interval / 3600) * 60))))
        repeat_end = SchedulePattern._time_converter(
            self._pattern[&#34;active_end_time&#34;], &#34;%H:%M&#34;, utc_to_epoch=False)
        return {
            &#34;repeat_every&#34;: repeat_every,
            &#34;repeat_end&#34;: repeat_end
        }
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.schedule_freq_type"><code class="name">var <span class="ident">schedule_freq_type</span></code></dt>
<dd>
<div class="desc"><p>get the schedule frequency type</p>
<h2 id="returns">Returns</h2>
<p>(str) the schedule frequency type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1581-L1588" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def schedule_freq_type(self):
    &#34;&#34;&#34;
    get the schedule frequency type
    Returns:
        (str) the schedule frequency type
    &#34;&#34;&#34;
    return self._freq_type[self._pattern[&#39;freq_type&#39;]]</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.subtask_id"><code class="name">var <span class="ident">subtask_id</span></code></dt>
<dd>
<div class="desc"><p>Property which returns subtask id of the schedule
Returns (int) &ndash; Subtask id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1481-L1487" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subtask_id(self):
    &#34;&#34;&#34;
    Property which returns subtask id of the schedule
    Returns (int) -- Subtask id
    &#34;&#34;&#34;
    return self.schedule_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.weekly"><code class="name">var <span class="ident">weekly</span></code></dt>
<dd>
<div class="desc"><p>gets the weekly schedule
Returns (dict) &ndash; The schedule pattern
{
"active_start_time": time_in_%H/%S (str),
"repeat_weeks": weeks_to_repeat (int)
"weekdays": list of weekdays ['Monday','Tuesday']
}
False: if schedule type is wrong</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1692-L1715" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def weekly(self):
    &#34;&#34;&#34;
    gets the weekly schedule
    Returns (dict) -- The schedule pattern
            {
                     &#34;active_start_time&#34;: time_in_%H/%S (str),
                     &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                     &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
            }
    False: if schedule type is wrong
    &#34;&#34;&#34;
    if self.schedule_freq_type == &#39;Weekly&#39;:
        _freq = self._pattern[&#39;freq_interval&#39;]
        return {
            &#39;active_start_time&#39;: SchedulePattern._time_converter(
                self._pattern[&#39;active_start_time&#39;],
                &#39;%H:%M&#39;,
                False),
            &#39;repeat_weeks&#39;: self._pattern[&#39;freq_recurrence_factor&#39;],
            &#39;weekdays&#39;: [
                SchedulePattern._days_to_run[x] for x in list(
                    SchedulePattern._days_to_run.keys()) if _freq &amp; x &gt; 0]}
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.yearly"><code class="name">var <span class="ident">yearly</span></code></dt>
<dd>
<div class="desc"><p>gets the yearly schedule
Returns: (dict) &ndash; The schedule pattern
{
"active_start_time": time_in_%H/%S (str),
"on_month": month to run schedule (str) January, Febuary&hellip;
"on_day": Day to run schedule (int)
}
False: if schedule type is wrong</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1828-L1847" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def yearly(self):
    &#34;&#34;&#34;
    gets the yearly schedule
            Returns: (dict) -- The schedule pattern
                    {
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                             &#34;on_day&#34;: Day to run schedule (int)
                    }
            False: if schedule type is wrong
    &#34;&#34;&#34;
    if self.schedule_freq_type == &#39;Yearly&#39;:
        return {&#39;active_start_time&#39;:
                SchedulePattern._time_converter(self._pattern[&#39;active_start_time&#39;],
                                                &#39;%H:%M&#39;, False),
                &#39;on_month&#39;: calendar.month_name[self._pattern[&#39;freq_recurrence_factor&#39;]],
                &#39;on_day&#39;: self._pattern[&#39;freq_interval&#39;]
                }
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.yearly_relative"><code class="name">var <span class="ident">yearly_relative</span></code></dt>
<dd>
<div class="desc"><p>gets the yearly_relative schedule
Returns: (dict) The schedule pattern
{
"active_start_time": time_in_%H/%S (str),
"relative_time": relative day of the schedule (str)'first','second',..
"relative_weekday": Day to run schedule (str) 'sunday','monday'&hellip;
"on_month": month to run the schedule(str) January, Febuary&hellip;
}
False: if schedule type is wrong</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1869-L1892" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def yearly_relative(self):
    &#34;&#34;&#34;
    gets the yearly_relative schedule
            Returns: (dict) The schedule pattern
                {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;relative_time&#34;: relative day of the schedule (str)&#39;first&#39;,&#39;second&#39;,..
                         &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                         &#34;on_month&#34;: month to run the schedule(str) January, Febuary...
                }
            False: if schedule type is wrong
    &#34;&#34;&#34;
    if self.schedule_freq_type == &#39;Yearly_Relative&#39;:
        return {&#39;active_start_time&#39;:
                SchedulePattern._time_converter(self._pattern[&#39;active_start_time&#39;],
                                                &#39;%H:%M&#39;, False),
                &#39;relative_time&#39;: SchedulePattern._relative_day
                [self._pattern[&#39;freq_relative_interval&#39;]],
                &#39;relative_weekday&#39;: SchedulePattern._relative_weekday
                [self._pattern[&#39;freq_interval&#39;]],
                &#39;on_month&#39;: calendar.month_name[self._pattern[&#39;freq_recurrence_factor&#39;]]
                }
    return False</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.schedules.Schedule.disable"><code class="name flex">
<span>def <span class="ident">disable</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disable a Schedule.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable Schedule</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2402-L2445" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable(self):
    &#34;&#34;&#34;Disable a Schedule.

        Raises:
            SDKException:
                if failed to disable Schedule

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    disable_request = self._commcell_object._services[&#39;DISABLE_SCHEDULE&#39;]

    request_text = &#34;taskId={0}&#34;.format(self.task_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, disable_request, request_text)

    if flag:
        if response.json():
            error_code = str(response.json()[&#39;errorCode&#39;])

            if error_code == &#34;0&#34;:
                return
            else:
                error_message = &#34;&#34;

                if &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                if error_message:
                    raise SDKException(
                        &#39;Schedules&#39;,
                        &#39;102&#39;,
                        &#39;Failed to disable Schedule\nError: &#34;{0}&#34;&#39;.format(error_message))
                else:
                    raise SDKException(
                        &#39;Schedules&#39;, &#39;102&#39;, &#34;Failed to disable Schedule&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    response_string = self._commcell_object._update_response_(
        response.text)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.enable"><code class="name flex">
<span>def <span class="ident">enable</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable a schedule.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable schedule</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2358-L2400" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable(self):
    &#34;&#34;&#34;Enable a schedule.

                Raises:
                    SDKException:
                        if failed to enable schedule

                        if response is empty

                        if response is not success
            &#34;&#34;&#34;
    enable_request = self._commcell_object._services[&#39;ENABLE_SCHEDULE&#39;]
    request_text = &#34;taskId={0}&#34;.format(self.task_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, enable_request, request_text)

    if flag:
        if response.json():
            error_code = str(response.json()[&#39;errorCode&#39;])

            if error_code == &#34;0&#34;:
                return
            else:
                error_message = &#34;&#34;

                if &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                if error_message:
                    raise SDKException(
                        &#39;Schedules&#39;,
                        &#39;102&#39;,
                        &#39;Failed to enable Schedule\nError: &#34;{0}&#34;&#39;.format(error_message))
                else:
                    raise SDKException(
                        &#39;Schedules&#39;, &#39;102&#39;, &#34;Failed to enable Schedule&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    response_string = self._commcell_object._update_response_(
        response.text)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the Schedule.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2487-L2489" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the Schedule.&#34;&#34;&#34;
    self._get_schedule_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedule.run_now"><code class="name flex">
<span>def <span class="ident">run_now</span></span>(<span>self, return_multiple_jobs=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Triggers the schedule to run immediately</p>
<p>Returns: job id/ Job IDs.</p>
<p>Args: return_multiple_jobs (bool) &ndash; if set to True, return multiple jobs, default: False.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Response received is empty.
If no job id is found.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L2268-L2317" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_now(self, return_multiple_jobs=False):
    &#34;&#34;&#34;
    Triggers the schedule to run immediately

    Returns: job id/ Job IDs.

    Args: return_multiple_jobs (bool) -- if set to True, return multiple jobs, default: False.

    Raises:
        SDKException:
            Response received is empty.
            If no job id is found.
    &#34;&#34;&#34;
    request_json = {
        &#34;TMMsg_TaskOperationReq&#34;:
            {
                &#34;opType&#34;: 5,
                &#34;subtaskEntity&#34;:
                    [
                        {
                            &#34;_type_&#34;: 68,
                            &#34;subtaskId&#34;: self.subtask_id,
                            &#34;taskName&#34;: &#34;&#34;,
                            &#34;subtaskName&#34;: self.schedule_name,
                            &#34;taskId&#34;: self.task_id
                        }
                    ],
                &#34;taskIds&#34;:
                    [
                        self.task_id
                    ]
            }
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._MODIFYSCHEDULE, request_json
    )
    if response.json():
        if &#34;jobIds&#34; in response.json():
            if(return_multiple_jobs):
                job_id = [int(i) for i in response.json()[&#39;jobIds&#39;]]
            else:
                job_id = str(response.json()[&#34;jobIds&#34;][0])

            return job_id
        else:
            raise SDKException(
                &#39;Response&#39;, &#39;102&#39;, &#39;JobID not found in response&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.schedules.SchedulePattern"><code class="flex name class">
<span>class <span class="ident">SchedulePattern</span></span>
<span>(</span><span>schedule_pattern=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting the schedule pattern</p>
<p>initialise object of the SchedulePattern class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L168-L994" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SchedulePattern:
    &#34;&#34;&#34;Class for getting the schedule pattern&#34;&#34;&#34;

    _days_to_run = {
        2: &#39;monday&#39;,
        4: &#39;tuesday&#39;,
        8: &#39;wednesday&#39;,
        16: &#39;thursday&#39;,
        32: &#39;friday&#39;,
        64: &#39;saturday&#39;,
        1: &#39;sunday&#39;,
    }

    _relative_weekday = {
        1: &#39;sunday&#39;,
        2: &#39;monday&#39;,
        3: &#39;tuesday&#39;,
        4: &#39;wednesday&#39;,
        5: &#39;thursday&#39;,
        6: &#39;friday&#39;,
        7: &#39;saturday&#39;,
        8: &#39;days&#39;,
        9: &#39;weekday&#39;,
        10: &#39;weekend_day&#39;
    }

    _relative_day = {
        1: &#39;first&#39;,
        2: &#39;second&#39;,
        3: &#39;third&#39;,
        4: &#39;fourth&#39;,
        5: &#39;last&#39;
    }

    def __init__(self, schedule_pattern=None):
        &#34;&#34;&#34;initialise object of the SchedulePattern class&#34;&#34;&#34;
        if not schedule_pattern:
            self._pattern = {&#39;freq_type&#39;: &#39;Init&#39;}
        else:
            self._pattern = schedule_pattern

    @staticmethod
    def _time_converter(_time, time_format, utc_to_epoch=True):
        &#34;&#34;&#34;
        converts a time string to epoch time based on the time format provided
        Args:

            _time  (str/int) -- UTC time or EPOCH time
            time_format (str) -- format of the time you need process

        Raises:
            SDKException if time format is wrong

        &#34;&#34;&#34;
        try:

            if utc_to_epoch:
                date_time = datetime.strptime(_time, time_format)
                return int(
                    (date_time - datetime.utcfromtimestamp(0)).total_seconds())

            utc_time = datetime.utcfromtimestamp(_time)
            return utc_time.strftime(time_format)

        except ValueError:
            raise SDKException(
                &#39;Schedules&#39;,
                &#39;102&#39;,
                &#34;Incorrect data format, should be {0}&#34;.format(time_format))

    def _pattern_json(self, pattern_option_dict):
        &#34;&#34;&#34;
        forms a pattern json and set the class variable
        Args:
             pattern_option_dict (dict) -- dictionary with the parameters needed for
                                                            forming the corresponding pattern
                                            {&#39;freq_type&#39;,
                                            &#39;active_start_date&#39;,
                                            &#39;active_start_time&#39;,
                                            &#39;freq_recurrence_factor&#39;,
                                            &#39;freq_interval&#39;}
        &#34;&#34;&#34;

        if (&#39;freq_type&#39; not in pattern_option_dict) or (
                pattern_option_dict[&#39;freq_type&#39;] == self._pattern[&#39;freq_type&#39;]):
            for key, value in pattern_option_dict.items():
                if key in (&#39;active_start_date&#39;, &#39;active_end_date&#39;):
                    self._pattern[key] = self._time_converter(
                        pattern_option_dict[key] + &#39; 00:00&#39;, &#39;%m/%d/%Y %H:%M&#39;)
                elif key in (&#39;active_start_time&#39;, &#39;active_end_time&#39;):
                    self._pattern[key] = self._time_converter(
                        &#39;1/1/1970 &#39; + pattern_option_dict[key], &#39;%m/%d/%Y %H:%M&#39;)
                else:
                    self._pattern[key] = value

        else:

            if pattern_option_dict[&#39;freq_type&#39;] == &#39;One_Time&#39;:
                default_start_time = str(datetime.now().strftime(&#39;%H:%M&#39;))

            else:
                default_start_time = &#39;09:00&#39;

            _active_start_date = pattern_option_dict.get(
                &#39;active_start_date&#39;, str(datetime.now().strftime(&#39;%m/%d/%Y&#39;)))
            _active_start_time = pattern_option_dict.get(
                &#39;active_start_time&#39;, default_start_time)

            self._pattern = {
                &#39;freq_type&#39;: pattern_option_dict[&#39;freq_type&#39;],
                &#39;active_start_date&#39;: self._time_converter(
                    _active_start_date + &#39; 00:00&#39;,
                    &#39;%m/%d/%Y %H:%M&#39;),
                &#39;active_start_time&#39;: self._time_converter(
                    &#39;1/1/1970 &#39; + _active_start_time,
                    &#39;%m/%d/%Y %H:%M&#39;),
                &#39;freq_recurrence_factor&#39;: pattern_option_dict.get(
                    &#39;freq_recurrence_factor&#39;,
                    0),
                &#39;freq_interval&#39;: pattern_option_dict.get(
                    &#39;freq_interval&#39;,
                    0),
                &#39;freq_relative_interval&#39;: pattern_option_dict.get(
                    &#39;freq_relative_interval&#39;,
                    0),
                &#39;timeZone&#39;: {
                    &#39;TimeZoneName&#39;: pattern_option_dict.get(
                        &#39;time_zone&#39;,
                        &#39;&#39;)}}

            if &#34;active_end_date&#34; in pattern_option_dict:
                self._pattern[&#34;active_end_date&#34;] = self._time_converter(
                    pattern_option_dict[&#34;active_end_date&#34;] + &#39; 00:00&#39;, &#39;%m/%d/%Y %H:%M&#39;)

        if &#34;exception_dates&#34; in pattern_option_dict:
            self._pattern[&#34;repeatPattern&#34;] = [{&#34;exception&#34;: True,
                                               &#34;onDayNumber&#34;: self.exception_dates(
                                                   pattern_option_dict[&#34;exception_dates&#34;])}
                                              ]

        if &#34;end_after&#34; in pattern_option_dict:
            self._pattern[&#34;active_end_occurence&#34;] = pattern_option_dict[&#34;end_after&#34;]

        if &#34;repeat_every&#34; in pattern_option_dict:
            self._pattern.update(self._repeat_pattern(pattern_option_dict))

    @staticmethod
    def _repeat_pattern(pattern_dict):
        &#34;&#34;&#34;
        Forms repeat pattern json based on the pattern dict provided
        Args:
            pattern_dict (dict) -- Dictionary containing repeat_every and repeat_end parameters
                                    {
                                    &#34;repeat_every&#34;: &#34;08:00&#34;,
                                    &#34;repeat_end&#34;: &#34;23:00&#34;
                                    }
        Returns:
                Dict with subdayinterval and endtime information to plug into the pattern json
        &#34;&#34;&#34;
        _repeat_time = datetime.strptime(
            pattern_dict.get(
                &#34;repeat_every&#34;, &#34;08:00&#34;), &#34;%H:%M&#34;)
        _freq_subday = (_repeat_time.hour * 3600 + _repeat_time.minute * 60)
        return {&#39;freq_subday_interval&#39;: _freq_subday,
                &#39;active_end_time&#39;: SchedulePattern._time_converter(
                    &#39;1/1/1970 &#39; + pattern_dict[&#34;repeat_end&#34;], &#39;%m/%d/%Y %H:%M&#39;)
                }

    def _one_time(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
        send only required keys to change only those values
        Args:
             pattern_dict (dict) -- Dictonary with the schedule pattern
                {
                                 &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                                 &#34;active_start_time&#34;: time_in_%h:%m (str)
                }
        &#34;&#34;&#34;
        pattern_dict[&#39;freq_type&#39;] = 1
        self._pattern_json(pattern_dict)

    def _daily(self, pattern_dict):
        &#34;&#34;&#34;
                sets the pattern type as daily with the parameters provided
                send only required keys to change only those values
                Args:
                     pattern_dict (dict) -- Dictionary with the schedule pattern
                  {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                  }
        &#34;&#34;&#34;
        _repeat_days = 1
        if self._pattern[&#39;freq_type&#39;] == 4:
            _repeat_days = self._pattern[&#39;freq_recurrence_factor&#39;]

        _freq_recurrence_factor = pattern_dict.get(&#39;repeat_days&#39;, _repeat_days)

        pattern_dict[&#39;freq_type&#39;] = 4
        pattern_dict[&#39;freq_recurrence_factor&#39;] = 1 if not isinstance(
            _freq_recurrence_factor, int) else _freq_recurrence_factor
        self._pattern_json(pattern_dict)

    def _weekly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as weekly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                        }
        &#34;&#34;&#34;
        try:
            _repeat_weeks = 1
            _freq_interval = 0
            if self._pattern[&#39;freq_type&#39;] == 8:
                _repeat_weeks = self._pattern[&#39;freq_recurrence_factor&#39;]
                _freq_interval = self._pattern[&#39;freq_interval&#39;]

            pattern_dict[&#39;freq_type&#39;] = 8
            # encoding
            if &#39;weekdays&#39; in pattern_dict:
                _freq_interval_list = pattern_dict[&#39;weekdays&#39;]
                for weekday in _freq_interval_list:
                    _freq_interval += (
                        list(
                            self._days_to_run.keys())[
                            list(
                                self._days_to_run.values()).index(
                                weekday.lower())])
            elif _freq_interval == 0:
                o_str = &#39;Weekdays need to be specified&#39;
                raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, o_str)

            _freq_recurrence_factor = pattern_dict.get(
                &#39;_repeat_weeks&#39;, _repeat_weeks)

            pattern_dict[&#39;freq_interval&#39;] = _freq_interval
            pattern_dict[&#39;freq_recurrence_factor&#39;] = 1 if not isinstance(
                _freq_recurrence_factor, int) else _freq_recurrence_factor

            self._pattern_json(pattern_dict)

        except ValueError:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               &#34;Incorrect weekday specified&#34;)

    def _monthly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;repeat_months&#34;: months_to_repeat (int)
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
        &#34;&#34;&#34;
        _repeat_months = 1
        _on_day = 10
        if self._pattern[&#39;freq_type&#39;] == 16:
            _repeat_months = self._pattern[&#39;freq_recurrence_factor&#39;]
            _on_day = self._pattern[&#39;freq_interval&#39;]

        _freq_recurrence_factor = pattern_dict.get(
            &#39;repeat_months&#39;, _repeat_months)
        _freq_interval = pattern_dict.get(&#39;on_day&#39;, _on_day)

        pattern_dict[&#39;freq_recurrence_factor&#39;] = 1 if not isinstance(
            _freq_recurrence_factor, int) else _freq_recurrence_factor
        pattern_dict[&#39;freq_interval&#39;] = 1 if not isinstance(
            _freq_interval, int) else _freq_interval
        pattern_dict[&#39;freq_type&#39;] = 16
        self._pattern_json(pattern_dict)

    def _monthly_relative(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly_relative with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;repeat_months&#34;: months_to_repeat
                        }
        &#34;&#34;&#34;
        _freq_recurrence_factor = 1
        _freq_interval = 1
        _freq_relative_interval = 1

        try:

            if self._pattern[&#39;freq_type&#39;] == 32:
                _freq_recurrence_factor = self._pattern[&#39;freq_recurrence_factor&#39;]
                _freq_interval = self._pattern[&#39;freq_interval&#39;]
                _freq_relative_interval = self._pattern[&#39;freq_relative_interval&#39;]

            if &#39;relative_time&#39; in pattern_dict:
                _freq_relative_interval = (
                    list(
                        self._relative_day.keys())[
                        list(
                            self._relative_day.values()).index(
                            pattern_dict[&#39;relative_time&#39;].lower())])

            if &#39;relative_weekday&#39; in pattern_dict:
                _freq_interval = (
                    list(
                        self._relative_weekday.keys())[
                        list(
                            self._relative_weekday.values()).index(
                            pattern_dict[&#39;relative_weekday&#39;].lower())])

            _freq_recurrence_factor = pattern_dict.get(
                &#39;repeat_months&#39;, _freq_recurrence_factor)

            pattern_dict[&#39;freq_recurrence_factor&#39;] = 1 if not isinstance(
                _freq_recurrence_factor, int) else _freq_recurrence_factor

            pattern_dict[&#39;freq_interval&#39;] = _freq_interval
            pattern_dict[&#39;freq_relative_interval&#39;] = _freq_relative_interval

            pattern_dict[&#39;freq_type&#39;] = 32
            self._pattern_json(pattern_dict)

        except ValueError as v_error:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               str(v_error))

    def _yearly(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }
        &#34;&#34;&#34;
        try:

            _freq_recurrence_factor = 1
            _freq_interval = 10
            if self._pattern[&#39;freq_type&#39;] == 64:
                _freq_recurrence_factor = self._pattern[&#39;freq_recurrence_factor&#39;]
                _freq_interval = self._pattern[&#39;freq_interval&#39;]

            if &#39;on_month&#39; in pattern_dict:
                _freq_recurrence_factor = list(
                    calendar.month_name).index(
                    pattern_dict[&#39;on_month&#39;].title())

            _freq_interval = pattern_dict.get(&#39;on_day&#39;, _freq_interval)

            pattern_dict[&#39;freq_recurrence_factor&#39;] = _freq_recurrence_factor
            pattern_dict[&#39;freq_interval&#39;] = 1 if not isinstance(
                _freq_interval, int) else _freq_interval
            pattern_dict[&#39;freq_type&#39;] = 64
            self._pattern_json(pattern_dict)

        except ValueError as ve:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               str(ve))

    def _yearly_relative(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as monthly_relative with the parameters provided
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;on_month&#34;: month to run the schedule(str) January, February...
                        }
        &#34;&#34;&#34;
        _freq_recurrence_factor = 1
        _freq_interval = 1
        _freq_relative_interval = 1

        try:

            if self._pattern[&#39;freq_type&#39;] == 128:
                _freq_recurrence_factor = self._pattern[&#39;freq_recurrence_factor&#39;]
                _freq_interval = self._pattern[&#39;freq_interval&#39;]
                _freq_relative_interval = self._pattern[&#39;freq_relative_interval&#39;]

            if &#39;relative_time&#39; in pattern_dict:
                _freq_relative_interval = (
                    list(
                        self._relative_day.keys())[
                        list(
                            self._relative_day.values()).index(
                            pattern_dict[&#39;relative_time&#39;].lower())])

            if &#39;relative_weekday&#39; in pattern_dict:
                _freq_interval = (
                    list(
                        self._relative_weekday.keys())[
                        list(
                            self._relative_weekday.values()).index(
                            pattern_dict[&#39;relative_weekday&#39;].lower())])

            if &#39;on_month&#39; in pattern_dict:
                _freq_recurrence_factor = list(
                    calendar.month_name).index(
                    pattern_dict[&#39;on_month&#39;].title())
            pattern_dict[&#39;freq_recurrence_factor&#39;] = _freq_recurrence_factor
            pattern_dict[&#39;freq_interval&#39;] = _freq_interval
            pattern_dict[&#39;freq_relative_interval&#39;] = _freq_relative_interval

            pattern_dict[&#39;freq_type&#39;] = 128
            self._pattern_json(pattern_dict)

        except ValueError as ve:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               str(ve))

    def _continuous(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as one time with the parameters provided,
        send only required keys to change only those values
        Args:
            pattern_dict (dict) -- Dictionary with the schedule pattern
                {
                                 job_interval: interval between jobs in mins(int)
                }
        &#34;&#34;&#34;

        _freq_recurrence_factor = pattern_dict.get(&#39;job_interval&#39;, 30)
        pattern_dict[&#39;freq_interval&#39;] = 30 if not isinstance(
            _freq_recurrence_factor, int) else _freq_recurrence_factor
        pattern_dict[&#39;freq_type&#39;] = 4096
        self._pattern_json(pattern_dict)

    def _automatic(self, pattern_dict):
        &#34;&#34;&#34;
                sets the pattern type as one time with the parameters provided,
                send only required keys to change only those values
                Args:
                     pattern_dict (dict) -- Dictionary with the schedule pattern
                        {
                                         min_interval_hours: minimum hours between jobs(int)
                                         min_interval_minutes: minimum minutes between jobs(int)
                                         max_interval_hours: maximum hours between jobs(int)
                                         max_interval_minutes: maximum minutes between jobs(int)
                                         min_sync_interval_hours: minimum sync hours
                                                                                between jobs(int)
                                         min_sync_interval_minutes: minimum sync minutes
                                                                                between jobs(int)
                                         ignore_opwindow_past_maxinterval: (bool)
                                         wired_network_connection: (bool)
                                         min_network_bandwidth: (int) kbps
                                         specific_network: (dict){ip_address:(str),subnet:(int)}
                                         dont_use_metered_network: (bool)
                                         ac_power: (bool)
                                         stop_if_on_battery: (bool)
                                         stop_sleep_if_runningjob: (bool)
                                         cpu_utilization_below : (int)%
                                         cpu_utilization_above : (int)%
                                         disk_use_threshold: (int)%
                                         number_of_log_files: (int)
                        }
        &#34;&#34;&#34;
        automatic_pattern = {
            &#34;maxBackupInterval&#34;: pattern_dict.get(&#34;max_interval_hours&#34;,
                                                  self._pattern.get(&#34;maxBackupInterval&#34;, 72)),
            &#34;ignoreOpWindowPastMaxInterval&#34;: pattern_dict.get(&#34;ignore_opwindow_past_maxinterval&#34;,
                                                              self._pattern.get(
                                                                  &#34;ignoreOpWindowPastMaxInterval&#34;,
                                                                  False)),
            &#34;minBackupIntervalMinutes&#34;: pattern_dict.get(&#34;min_interval_minutes&#34;,
                                                         self._pattern.get(
                                                             &#34;minBackupIntervalMinutes&#34;, 15)),
            &#34;maxBackupIntervalMinutes&#34;: pattern_dict.get(&#34;max_interval_minutes&#34;,
                                                         self._pattern.get(
                                                             &#34;maxBackupIntervalMinutes&#34;, 0)),
            &#34;minSyncInterval&#34;: pattern_dict.get(&#34;min_sync_interval_hours&#34;,
                                                self._pattern.get(&#34;minSyncInterval&#34;, 0)),
            &#34;minBackupInterval&#34;: pattern_dict.get(&#34;min_interval_hours&#34;,
                                                  self._pattern.get(&#34;minBackupInterval&#34;, 0)),
            &#34;minSyncIntervalMinutes&#34;: pattern_dict.get(&#34;min_sync_interval_minutes&#34;,
                                                       self._pattern.get(&#34;minSyncIntervalMinutes&#34;,
                                                                         2)),
            &#34;stopIfOnBattery&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;stop_if_on_battery&#34;,
                                            self._pattern.get(&#34;stopIfOnBattery&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])
            },
            &#34;acPower&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;ac_power&#34;,
                                            self._pattern.get(&#34;acPower&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])
            },
            &#34;specfificNetwork&#34;: {
                &#34;enabled&#34;: True if &#39;specific_network&#39; in pattern_dict
                else (self._pattern.get(&#34;specfificNetwork&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;ipAddress&#34;: {
                    &#34;family&#34;: 32,
                    &#34;address&#34;: pattern_dict.get(&#39;specific_network&#39;,
                                                {&#34;ip_address&#34;: &#34;0.0.0.0&#34;})[&#34;ip_address&#34;],
                    &#34;subnet&#34;: pattern_dict.get(&#39;specific_network&#39;,
                                               {&#34;subnet&#34;: 24})[&#34;subnet&#34;],
                }

            },
            &#34;stopSleepIfBackUp&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;stop_sleep_if_runningjob&#34;,
                                            self._pattern.get(&#34;stopSleepIfBackUp&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])

            },
            &#34;emergencyBackup&#34;: {
                &#34;emergencyBackupCommandName&#34;: &#34;&#34;,
                &#34;emergencyBackup&#34;: {
                    &#34;enabled&#34;: False
                }
            },
            &#34;cpuUtilization&#34;: {
                &#34;enabled&#34;: True if &#39;cpu_utilization_below&#39; in pattern_dict
                else (self._pattern.get(&#34;cpuUtilization&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;cpu_utilization_below&#34;,
                                              self._pattern.get(&#34;cpuUtilization&#34;,
                                                                {&#39;threshold&#39;: 10})[&#39;threshold&#39;])
            },
            &#34;dontUseMeteredNetwork&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;dont_use_metered_network&#34;,
                                            self._pattern.get(&#34;dontUseMeteredNetwork&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])
            },
            &#34;cpuUtilizationAbove&#34;: {
                &#34;enabled&#34;: True if &#39;cpu_utilization_above&#39; in pattern_dict
                else (self._pattern.get(&#34;cpuUtilizationAbove&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;cpu_utilization_above&#34;,
                                              self._pattern.get(&#34;cpuUtilizationAbove&#34;,
                                                                {&#39;threshold&#39;: 10})[&#39;threshold&#39;])
            },
            &#34;wiredNetworkConnection&#34;: {
                &#34;enabled&#34;: pattern_dict.get(&#34;wired_network_connection&#34;,
                                            self._pattern.get(&#34;wiredNetworkConnection&#34;,
                                                              {&#39;enabled&#39;: False})[&#39;enabled&#39;])
            },
            &#34;minNetworkBandwidth&#34;: {
                &#34;enabled&#34;: True if &#39;min_network_bandwidth&#39; in pattern_dict
                else (self._pattern.get(&#34;minNetworkBandwidth&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;min_network_bandwidth&#34;,
                                              self._pattern.get(&#34;minNetworkBandwidth&#34;,
                                                                {&#39;threshold&#39;: 128})[&#39;threshold&#39;])
            },
            &#34;sweepStartTime&#34;: pattern_dict.get(&#34;sweep_start_time&#34;,
                                               self._pattern.get(&#34;sweepStartTime&#34;, 3600)
                                               ),
            &#34;useStorageSpaceFromMA&#34;: pattern_dict.get(&#34;use_storage_space_ma&#34;,
                                               self._pattern.get(&#34;useStorageSpaceFromMA&#34;, False)
                                               ),
            &#34;diskUsedPercent&#34;: {
                &#34;enabled&#34;: True if &#39;disk_use_threshold&#39; in pattern_dict
                else (self._pattern.get(&#34;diskUsedPercent&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;disk_use_threshold&#34;,
                                              self._pattern.get(&#34;diskUsedPercent&#34;,
                                                                {&#39;threshold&#39;: 80})[&#39;threshold&#39;])
            },
            &#34;logFileNum&#34;: {
                &#34;enabled&#34;: True if &#39;number_of_log_files&#39; in pattern_dict
                else (self._pattern.get(&#34;logFileNum&#34;,
                                        {&#39;enabled&#39;: False})[&#39;enabled&#39;]),
                &#34;threshold&#34;: pattern_dict.get(&#34;number_of_log_files&#34;,
                                              self._pattern.get(&#34;logFileNum&#34;,
                                                                {&#39;threshold&#39;: 50})[&#39;threshold&#39;])
            }
        }

        self._pattern = automatic_pattern

    def _after_job_completes(self, pattern_dict):
        &#34;&#34;&#34;
        sets the pattern type as after job completes with the parameters provided,
        send only required keys to change only those values

        Args:
            pattern_dict        (dict) -- Dictionary with the schedule pattern
                {
                        &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                        &#34;repeat_days&#34;: days_to_repeat (int)
                }

        &#34;&#34;&#34;
        pattern_dict[&#39;freq_type&#39;] = &#39;After_Job_Completes&#39;

        pattern_dict[&#39;freq_recurrence_factor&#39;] = pattern_dict.get(&#39;repeat_days&#39;, 4096)

        self._pattern_json(pattern_dict)

    @staticmethod
    def exception_dates(day_list):
        &#34;&#34;&#34;
        Provided a Scheduler version of exception as an on day number
        Args:
            day_list (list) -- List of exception dates [1,2,3]

        Returns (int) -- on_day number for the pattern json

        &#34;&#34;&#34;
        on_day = 0
        for value in day_list:
            on_day |= (1 &lt;&lt; (value - 1))
        return on_day

    def create_schedule_pattern(self, pattern_dict):
        &#34;&#34;&#34;
        calls the required type of schedule module and forms the pattern json
        Args:
        pattern_dict (Dict) --

        freq_type is mandatory, all other fields specified below can be skipped and system
                                                                            defaults will be set

        for reference on pattern_dict check create_schedule

        Returns:
             pattern which can be plugged into the create or modify task request to
                                                                        create or modify schedules
        &#34;&#34;&#34;

        if &#39;freq_type&#39; not in pattern_dict:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               &#34;Frequency type is required to create pattern&#34;)

        try:
            getattr(
                self,
                &#39;_&#39; +
                pattern_dict[&#39;freq_type&#39;].lower())(pattern_dict)
            return self._pattern

        except AttributeError:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                               &#34;freq_type specified is wrong&#34;)

    def create_schedule(self, task_req, pattern_dict, schedule_id=None):
        &#34;&#34;&#34;
        returns a schedule task_req after including pattern
        Args:
        task_req: task_req for immediate job operation to be converted to a schedule

        freq_type is mandatory, all other fields specified below can be skipped and system
                                                                            defaults will be set

        with the same dict, pass
        time_zone: Time Zone Name(default is taken as COmmServe Time Zone)
            Common Time Zones examples -- &#39;CommServe Time Zone&#39;, &#39;Client Time Zone&#39;, &#39;UTC&#39;

        for one_time: {
                                 &#34;freq_type&#34;: &#39;one_time&#39;,
                                 &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                                 &#34;active_start_time&#34;: time_in_%h:%m (str)
                        }

        for daily: {
                         &#34;freq_type&#34;: &#39;daily&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                  }

        for weekly: {
                         &#34;freq_type&#34;: &#39;weekly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                        }

        for monthly: {
                                 &#34;freq_type&#34;: &#39;monthly&#39;,
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;repeat_months&#34;: weeks_to_repeat (int)
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }

        for monthly_relative:    {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;repeat_months&#34;: months_to_repeat
                                }

        for yearly: {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                                 &#34;on_day&#34;: Day to run schedule (int)
                        }

        for yearly_relative: {
                                 &#34;active_start_time&#34;: time_in_%H/%S (str),
                                 &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                        &#39;second&#39;,..
                                 &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                                 &#34;on_month&#34;: month to run the schedule(str) January, Febuary...
                        }

        for continuous: {
                                 job_interval: interval between jobs in mins(int)
                }

        for automatic: {
                                         min_interval_hours: minimum hours between jobs(int)
                                         min_interval_minutes: minimum minutes between jobs(int)
                                         max_interval_hours: maximum hours between jobs(int)
                                         max_interval_minutes: maximum minutes between jobs(int)
                                         min_sync_interval_hours: minimum sync hours
                                                                                between jobs(int)
                                         min_sync_interval_minutes: minimum sync minutes
                                                                                between jobs(int)
                                         ignore_opwindow_past_maxinterval: (bool)
                                         wired_network_connection: (bool)
                                         min_network_bandwidth: (int) kbps
                                         specific_network: (dict){ip_address:(str),subnet:(int)}
                                         dont_use_metered_network: (bool)
                                         ac_power: (bool)
                                         stop_if_on_battery: (bool)
                                         stop_sleep_if_runningjob: (bool)
                                         cpu_utilization_below : (int)%
                                         cpu_utilization_above : (int)%
                                         disk_use_threshold: (int)%
                                         number_of_log_files: (int)
                        }

        for after_job_completes :   {
                                        &#34;freq_type&#34;: &#39;after_job_completes&#39;,
                                        &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                                        &#34;repeat_days&#34;: days_to_repeat (int)
                                    }

        Sample Usage inside the individual operation function:
        Add a schedule_pattern parameter to the function and include the below line before making
        the sdk make_request call

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(task_req,schedule_pattern)

        pattern_dict (Dict) -- schedule pattern to be merged with the task request
        Returns:
             Schedule task request
        &#34;&#34;&#34;

        _automatic_pattern = {}

        if pattern_dict[&#34;freq_type&#34;] == &#39;automatic&#39;:
            _pattern = {&#34;freq_type&#34;: 1024}
            _automatic_pattern = self.create_schedule_pattern(pattern_dict)
        else:
            _pattern = self.create_schedule_pattern(pattern_dict)

        _task_info = task_req[&#34;taskInfo&#34;]
        if _task_info.get(&#34;task&#34;):
            _task_info[&#34;task&#34;][&#34;taskType&#34;] = 2
        for subtask in _task_info[&#39;subTasks&#39;]:
            if schedule_id:
                if subtask[&#34;subTask&#34;][&#39;subTaskId&#39;] != schedule_id:
                    continue
            else:
                subtask[&#34;subTask&#34;][&#39;subTaskName&#39;] = pattern_dict.get(
                    &#39;schedule_name&#39;, &#39;&#39;)
            subtask[&#34;pattern&#34;] = _pattern
            if pattern_dict[&#34;freq_type&#34;] == &#39;automatic&#39;:
                if &#39;options&#39; in subtask:
                    _task_options = subtask[&#39;options&#39;]
                    if &#39;commonOpts&#39; in _task_options:
                        _task_options[&#34;commonOpts&#34;][&#34;automaticSchedulePattern&#34;] = _automatic_pattern
                    else:
                        _task_options[&#34;commonOpts&#34;] = \
                            {&#34;automaticSchedulePattern&#34;: _automatic_pattern}

                    if &#39;run_synthetic_full&#39; in pattern_dict:
                        synthetic_pattern = pattern_dict[&#39;run_synthetic_full&#39;]

                        if synthetic_pattern == &#39;every_x_days&#39;:
                            synthetic_interval = pattern_dict.get(
                                &#39;days_between_synthetic_full&#39;, 30)
                        else:
                            synthetic_interval = 30

                        _data_opt = {
                            &#39;autoCopy&#39;: True,
                            &#39;daysBetweenSyntheticBackup&#39;: synthetic_interval,
                            &#39;useAutomaticIntervalForSyntheticFull&#39;: (
                                    synthetic_pattern == &#39;extended_retention&#39;),
                            &#39;enableRunFullConsolidationBackup&#39;: (
                                    synthetic_pattern == &#39;space_reclaim&#39;)
                        }

                        if &#39;backupOpts&#39; in _task_options:
                            if &#39;dataOpt&#39; in _task_options[&#34;backupOpts&#34;]:
                                _task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;].update(_data_opt)
                            else:
                                _task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;] = _data_opt
                        else:
                            _task_options[&#39;backupOpts&#39;] = {
                                &#39;dataOpt&#39;: _data_opt
                            }

                else:
                    subtask[&#39;options&#39;] = {
                        &#39;commonOpts&#39;: {
                            &#39;automaticSchedulePattern&#39;: _automatic_pattern}}

        task_req[&#34;taskInfo&#34;] = _task_info
        return task_req</code></pre>
</details>
<h3>Static methods</h3>
<dl>
<dt id="cvpysdk.schedules.SchedulePattern.exception_dates"><code class="name flex">
<span>def <span class="ident">exception_dates</span></span>(<span>day_list)</span>
</code></dt>
<dd>
<div class="desc"><p>Provided a Scheduler version of exception as an on day number</p>
<h2 id="args">Args</h2>
<p>day_list (list) &ndash; List of exception dates [1,2,3]
Returns (int) &ndash; on_day number for the pattern json</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L779-L792" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@staticmethod
def exception_dates(day_list):
    &#34;&#34;&#34;
    Provided a Scheduler version of exception as an on day number
    Args:
        day_list (list) -- List of exception dates [1,2,3]

    Returns (int) -- on_day number for the pattern json

    &#34;&#34;&#34;
    on_day = 0
    for value in day_list:
        on_day |= (1 &lt;&lt; (value - 1))
    return on_day</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.schedules.SchedulePattern.create_schedule"><code class="name flex">
<span>def <span class="ident">create_schedule</span></span>(<span>self, task_req, pattern_dict, schedule_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>returns a schedule task_req after including pattern
Args:
task_req: task_req for immediate job operation to be converted to a schedule</p>
<p>freq_type is mandatory, all other fields specified below can be skipped and system
defaults will be set</p>
<p>with the same dict, pass
time_zone: Time Zone Name(default is taken as COmmServe Time Zone)
Common Time Zones examples &ndash; 'CommServe Time Zone', 'Client Time Zone', 'UTC'</p>
<p>for one_time: {
"freq_type": 'one_time',
"active_start_date": date_in_%m/%d/%y (str),
"active_start_time": time_in_%h:%m (str)
}</p>
<p>for daily: {
"freq_type": 'daily',
"active_start_time": time_in_%H/%S (str),
"repeat_days": days_to_repeat (int)
}</p>
<p>for weekly: {
"freq_type": 'weekly',
"active_start_time": time_in_%H/%S (str),
"repeat_weeks": weeks_to_repeat (int)
"weekdays": list of weekdays ['Monday','Tuesday']
}</p>
<p>for monthly: {
"freq_type": 'monthly',
"active_start_time": time_in_%H/%S (str),
"repeat_months": weeks_to_repeat (int)
"on_day": Day to run schedule (int)
}</p>
<p>for monthly_relative:
{
"active_start_time": time_in_%H/%S (str),
"relative_time": relative day of the schedule (str) 'first',
'second',..
"relative_weekday": Day to run schedule (str) 'sunday','monday'&hellip;
"repeat_months": months_to_repeat
}</p>
<p>for yearly: {
"active_start_time": time_in_%H/%S (str),
"on_month": month to run schedule (str) January, Febuary&hellip;
"on_day": Day to run schedule (int)
}</p>
<p>for yearly_relative: {
"active_start_time": time_in_%H/%S (str),
"relative_time": relative day of the schedule (str) 'first',
'second',..
"relative_weekday": Day to run schedule (str) 'sunday','monday'&hellip;
"on_month": month to run the schedule(str) January, Febuary&hellip;
}</p>
<p>for continuous: {
job_interval: interval between jobs in mins(int)
}</p>
<p>for automatic: {
min_interval_hours: minimum hours between jobs(int)
min_interval_minutes: minimum minutes between jobs(int)
max_interval_hours: maximum hours between jobs(int)
max_interval_minutes: maximum minutes between jobs(int)
min_sync_interval_hours: minimum sync hours
between jobs(int)
min_sync_interval_minutes: minimum sync minutes
between jobs(int)
ignore_opwindow_past_maxinterval: (bool)
wired_network_connection: (bool)
min_network_bandwidth: (int) kbps
specific_network: (dict){ip_address:(str),subnet:(int)}
dont_use_metered_network: (bool)
ac_power: (bool)
stop_if_on_battery: (bool)
stop_sleep_if_runningjob: (bool)
cpu_utilization_below : (int)%
cpu_utilization_above : (int)%
disk_use_threshold: (int)%
number_of_log_files: (int)
}</p>
<p>for after_job_completes :
{
"freq_type": 'after_job_completes',
"active_start_date": date_in_%m/%d/%y (str),
"active_start_time": time_in_%H/%S (str),
"repeat_days": days_to_repeat (int)
}</p>
<p>Sample Usage inside the individual operation function:
Add a schedule_pattern parameter to the function and include the below line before making
the sdk make_request call</p>
<p>if schedule_pattern:
request_json = SchedulePattern().create_schedule(task_req,schedule_pattern)</p>
<p>pattern_dict (Dict) &ndash; schedule pattern to be merged with the task request</p>
<h2 id="returns">Returns</h2>
<p>Schedule task request</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L825-L994" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_schedule(self, task_req, pattern_dict, schedule_id=None):
    &#34;&#34;&#34;
    returns a schedule task_req after including pattern
    Args:
    task_req: task_req for immediate job operation to be converted to a schedule

    freq_type is mandatory, all other fields specified below can be skipped and system
                                                                        defaults will be set

    with the same dict, pass
    time_zone: Time Zone Name(default is taken as COmmServe Time Zone)
        Common Time Zones examples -- &#39;CommServe Time Zone&#39;, &#39;Client Time Zone&#39;, &#39;UTC&#39;

    for one_time: {
                             &#34;freq_type&#34;: &#39;one_time&#39;,
                             &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                             &#34;active_start_time&#34;: time_in_%h:%m (str)
                    }

    for daily: {
                     &#34;freq_type&#34;: &#39;daily&#39;,
                     &#34;active_start_time&#34;: time_in_%H/%S (str),
                     &#34;repeat_days&#34;: days_to_repeat (int)
              }

    for weekly: {
                     &#34;freq_type&#34;: &#39;weekly&#39;,
                     &#34;active_start_time&#34;: time_in_%H/%S (str),
                     &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                     &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                    }

    for monthly: {
                             &#34;freq_type&#34;: &#39;monthly&#39;,
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;repeat_months&#34;: weeks_to_repeat (int)
                             &#34;on_day&#34;: Day to run schedule (int)
                    }

    for monthly_relative:    {
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                    &#39;second&#39;,..
                             &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                             &#34;repeat_months&#34;: months_to_repeat
                            }

    for yearly: {
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                             &#34;on_day&#34;: Day to run schedule (int)
                    }

    for yearly_relative: {
                             &#34;active_start_time&#34;: time_in_%H/%S (str),
                             &#34;relative_time&#34;: relative day of the schedule (str) &#39;first&#39;,
                                                                                    &#39;second&#39;,..
                             &#34;relative_weekday&#34;: Day to run schedule (str) &#39;sunday&#39;,&#39;monday&#39;...
                             &#34;on_month&#34;: month to run the schedule(str) January, Febuary...
                    }

    for continuous: {
                             job_interval: interval between jobs in mins(int)
            }

    for automatic: {
                                     min_interval_hours: minimum hours between jobs(int)
                                     min_interval_minutes: minimum minutes between jobs(int)
                                     max_interval_hours: maximum hours between jobs(int)
                                     max_interval_minutes: maximum minutes between jobs(int)
                                     min_sync_interval_hours: minimum sync hours
                                                                            between jobs(int)
                                     min_sync_interval_minutes: minimum sync minutes
                                                                            between jobs(int)
                                     ignore_opwindow_past_maxinterval: (bool)
                                     wired_network_connection: (bool)
                                     min_network_bandwidth: (int) kbps
                                     specific_network: (dict){ip_address:(str),subnet:(int)}
                                     dont_use_metered_network: (bool)
                                     ac_power: (bool)
                                     stop_if_on_battery: (bool)
                                     stop_sleep_if_runningjob: (bool)
                                     cpu_utilization_below : (int)%
                                     cpu_utilization_above : (int)%
                                     disk_use_threshold: (int)%
                                     number_of_log_files: (int)
                    }

    for after_job_completes :   {
                                    &#34;freq_type&#34;: &#39;after_job_completes&#39;,
                                    &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                                    &#34;active_start_time&#34;: time_in_%H/%S (str),
                                    &#34;repeat_days&#34;: days_to_repeat (int)
                                }

    Sample Usage inside the individual operation function:
    Add a schedule_pattern parameter to the function and include the below line before making
    the sdk make_request call

    if schedule_pattern:
        request_json = SchedulePattern().create_schedule(task_req,schedule_pattern)

    pattern_dict (Dict) -- schedule pattern to be merged with the task request
    Returns:
         Schedule task request
    &#34;&#34;&#34;

    _automatic_pattern = {}

    if pattern_dict[&#34;freq_type&#34;] == &#39;automatic&#39;:
        _pattern = {&#34;freq_type&#34;: 1024}
        _automatic_pattern = self.create_schedule_pattern(pattern_dict)
    else:
        _pattern = self.create_schedule_pattern(pattern_dict)

    _task_info = task_req[&#34;taskInfo&#34;]
    if _task_info.get(&#34;task&#34;):
        _task_info[&#34;task&#34;][&#34;taskType&#34;] = 2
    for subtask in _task_info[&#39;subTasks&#39;]:
        if schedule_id:
            if subtask[&#34;subTask&#34;][&#39;subTaskId&#39;] != schedule_id:
                continue
        else:
            subtask[&#34;subTask&#34;][&#39;subTaskName&#39;] = pattern_dict.get(
                &#39;schedule_name&#39;, &#39;&#39;)
        subtask[&#34;pattern&#34;] = _pattern
        if pattern_dict[&#34;freq_type&#34;] == &#39;automatic&#39;:
            if &#39;options&#39; in subtask:
                _task_options = subtask[&#39;options&#39;]
                if &#39;commonOpts&#39; in _task_options:
                    _task_options[&#34;commonOpts&#34;][&#34;automaticSchedulePattern&#34;] = _automatic_pattern
                else:
                    _task_options[&#34;commonOpts&#34;] = \
                        {&#34;automaticSchedulePattern&#34;: _automatic_pattern}

                if &#39;run_synthetic_full&#39; in pattern_dict:
                    synthetic_pattern = pattern_dict[&#39;run_synthetic_full&#39;]

                    if synthetic_pattern == &#39;every_x_days&#39;:
                        synthetic_interval = pattern_dict.get(
                            &#39;days_between_synthetic_full&#39;, 30)
                    else:
                        synthetic_interval = 30

                    _data_opt = {
                        &#39;autoCopy&#39;: True,
                        &#39;daysBetweenSyntheticBackup&#39;: synthetic_interval,
                        &#39;useAutomaticIntervalForSyntheticFull&#39;: (
                                synthetic_pattern == &#39;extended_retention&#39;),
                        &#39;enableRunFullConsolidationBackup&#39;: (
                                synthetic_pattern == &#39;space_reclaim&#39;)
                    }

                    if &#39;backupOpts&#39; in _task_options:
                        if &#39;dataOpt&#39; in _task_options[&#34;backupOpts&#34;]:
                            _task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;].update(_data_opt)
                        else:
                            _task_options[&#39;backupOpts&#39;][&#39;dataOpt&#39;] = _data_opt
                    else:
                        _task_options[&#39;backupOpts&#39;] = {
                            &#39;dataOpt&#39;: _data_opt
                        }

            else:
                subtask[&#39;options&#39;] = {
                    &#39;commonOpts&#39;: {
                        &#39;automaticSchedulePattern&#39;: _automatic_pattern}}

    task_req[&#34;taskInfo&#34;] = _task_info
    return task_req</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.SchedulePattern.create_schedule_pattern"><code class="name flex">
<span>def <span class="ident">create_schedule_pattern</span></span>(<span>self, pattern_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>calls the required type of schedule module and forms the pattern json
Args:
pattern_dict (Dict) &ndash;</p>
<p>freq_type is mandatory, all other fields specified below can be skipped and system
defaults will be set</p>
<p>for reference on pattern_dict check create_schedule</p>
<h2 id="returns">Returns</h2>
<p>pattern which can be plugged into the create or modify task request to
create or modify schedules</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L794-L823" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_schedule_pattern(self, pattern_dict):
    &#34;&#34;&#34;
    calls the required type of schedule module and forms the pattern json
    Args:
    pattern_dict (Dict) --

    freq_type is mandatory, all other fields specified below can be skipped and system
                                                                        defaults will be set

    for reference on pattern_dict check create_schedule

    Returns:
         pattern which can be plugged into the create or modify task request to
                                                                    create or modify schedules
    &#34;&#34;&#34;

    if &#39;freq_type&#39; not in pattern_dict:
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                           &#34;Frequency type is required to create pattern&#34;)

    try:
        getattr(
            self,
            &#39;_&#39; +
            pattern_dict[&#39;freq_type&#39;].lower())(pattern_dict)
        return self._pattern

    except AttributeError:
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;,
                           &#34;freq_type specified is wrong&#34;)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.schedules.Schedules"><code class="flex name class">
<span>class <span class="ident">Schedules</span></span>
<span>(</span><span>class_object, operation_type=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting the schedules of a commcell entity.</p>
<p>Initialise the Schedules class instance.</p>
<h2 id="args">Args</h2>
<p>class_object(object) &ndash; instance of client/agent/backupset/subclient/CommCell class
operation_type
&ndash; required when commcell object is passed
refer OperationType class for supported op types</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Schedule class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if class object does not belong to any of the Client or Agent or Backupset or
Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L997-L1379" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Schedules:
    &#34;&#34;&#34;Class for getting the schedules of a commcell entity.&#34;&#34;&#34;

    def __init__(self, class_object, operation_type=None):
        &#34;&#34;&#34;Initialise the Schedules class instance.

            Args:
                class_object(object) -- instance of client/agent/backupset/subclient/CommCell class
                operation_type        -- required when commcell object is passed
                                        refer OperationType class for supported op types
            Returns:
                object - instance of the Schedule class

            Raises:
                SDKException:
                    if class object does not belong to any of the Client or Agent or Backupset or
                        Subclient class
        &#34;&#34;&#34;
        # imports inside the __init__ method definition to avoid cyclic imports
        from .commcell import Commcell
        from .client import Client
        from .agent import Agent
        from .backupset import Backupset
        from .subclient import Subclient
        from .instance import Instance
        from .activateapps.inventory_manager import Inventory
        from .activateapps.file_storage_optimization import FsoServer
        from .activateapps.sensitive_data_governance import Project

        self.class_object = class_object
        self._single_scheduled_entity = False
        self._task_flags = {}
        self._repr_str = &#34;&#34;

        if isinstance(class_object, Commcell):
            self._commcell_object = class_object
            if operation_type == OperationType.REPORTS:
                self._SCHEDULES = class_object._services[&#39;REPORT_SCHEDULES&#39;]
                self._repr_str = &#34;Reports in Commcell: {0}&#34;.format(
                    class_object.commserv_name)
            elif operation_type == OperationType.DATA_AGING:
                self._SCHEDULES = class_object._services[&#39;OPTYPE_SCHEDULES&#39;] % (
                    operation_type)
                self._repr_str = &#34;Dataging in Commcell: {0}&#34;.format(
                    class_object.commserv_name)
            elif not operation_type:
                self._SCHEDULES = class_object._services[&#39;COMMCELL_SCHEDULES&#39;]
                self._repr_str = &#34;Schedules in Commcell: {0}&#34;.format(
                    class_object.commserv_name)
            else:
                raise SDKException(&#39;Schedules&#39;, &#39;103&#39;)
        elif isinstance(class_object, FsoServer):
            self._SCHEDULES = class_object._commcell_object._services[&#39;CLIENT_SCHEDULES&#39;] % (
                class_object.server_id)
            self._repr_str = &#34;Fso Server: {0}&#34;.format(class_object.server_id)
            self._commcell_object = class_object._commcell_object
            self._single_scheduled_entity = True
            self._task_flags[&#39;isEdiscovery&#39;] = True

        elif isinstance(class_object,Project):
            self._SCHEDULES = class_object._commcell_object._services[&#39;CLIENT_SCHEDULES&#39;] % (
                class_object.project_id)
            self._repr_str = &#34;SDG Project: {0}&#34;.format(class_object.project_id)
            self._commcell_object = class_object._commcell_object
            self._single_scheduled_entity = True
            self._task_flags[&#39;isEdiscovery&#39;] = True

        elif isinstance(class_object, Inventory):
            self._SCHEDULES = class_object._commcell_object._services[&#39;INVENTORY_SCHEDULES&#39;] % (
                class_object.inventory_id)
            self._repr_str = &#34;Inventory: {0}&#34;.format(class_object.inventory_name)
            self._commcell_object = class_object._commcell_object
            # set below flag to denote inventory type entity will always have only one schedule associated to it
            self._single_scheduled_entity = True

        elif isinstance(class_object, Client):
            self._SCHEDULES = class_object._commcell_object._services[&#39;CLIENT_SCHEDULES&#39;] % (
                class_object.client_id)
            self._repr_str = &#34;Client: {0}&#34;.format(class_object.client_name)
            self._commcell_object = class_object._commcell_object

        elif isinstance(class_object, Agent):
            self._SCHEDULES = class_object._commcell_object._services[&#39;AGENT_SCHEDULES&#39;] % (
                class_object._client_object.client_id, class_object.agent_id)
            self._repr_str = &#34;Agent: {0}&#34;.format(class_object.agent_name)
            self._commcell_object = class_object._commcell_object

        elif isinstance(class_object, Instance):
            self._SCHEDULES = class_object._commcell_object._services[&#39;INSTANCE_SCHEDULES&#39;] % (
                class_object._agent_object._client_object.client_id,
                class_object._agent_object.agent_id,
                class_object.instance_id
            )
            self._repr_str = &#34;Instance: {0}&#34;.format(
                class_object.instance_name)
            self._commcell_object = class_object._commcell_object

        elif isinstance(class_object, Backupset):
            self._SCHEDULES = class_object._commcell_object._services[&#39;BACKUPSET_SCHEDULES&#39;] % (
                class_object._agent_object._client_object.client_id,
                class_object._agent_object.agent_id,
                class_object.backupset_id
            )
            self._repr_str = &#34;Backupset: {0}&#34;.format(
                class_object.backupset_name)
            self._commcell_object = class_object._commcell_object

        elif isinstance(class_object, Subclient):
            self._SCHEDULES = class_object._commcell_object._services[&#39;SUBCLIENT_SCHEDULES&#39;] % (
                class_object._backupset_object._agent_object._client_object.client_id,
                class_object._backupset_object._agent_object.agent_id,
                class_object._backupset_object.backupset_id,
                class_object.subclient_id
            )
            self._repr_str = &#34;Subclient: {0}&#34;.format(
                class_object.subclient_name)
            self._commcell_object = class_object._commcell_object
        else:
            raise SDKException(&#39;Schedules&#39;, &#39;101&#39;)

        self.schedules = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all schedules of the commcell entity.

            Returns:
                str - string of all the schedules associated with the commcell entity
        &#34;&#34;&#34;
        if self.schedules:
            representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(
                &#39;S. No.&#39;, &#39;Schedule&#39;)

            for index, schedule in enumerate(self.schedules):
                sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, schedule)
                representation_string += sub_str
        else:
            representation_string = &#39;No Schedules are associated to this Commcell Entity&#39;

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Schedules class.&#34;&#34;&#34;
        return &#34;Schedules class instance for {0}&#34;.format(self._repr_str)

    def _get_schedules(self):
        &#34;&#34;&#34;Gets the schedules associated with the input commcell entity.
            Client / Agent / Backupset / Subclient

            Returns:
                dict - consists of all schedules for the commcell entity
                    {
                         &#34;schedule_id&#34;: {
                                &#39;task_id&#39;: task_id,
                                &#39;schedule_name&#39;: schedule_name,
                                &#39;description&#39;: description
                            }

                         &#34;schedule_id&#34;: {
                                &#39;task_id&#39;: task_id,
                                &#39;schedule_name&#39;: schedule_name,
                                &#39;description&#39;: description
                            }
                    }

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SCHEDULES)

        if flag:
            if response.json() and &#39;taskDetail&#39; in response.json():
                subtask_dict = {}
                for schedule in response.json()[&#39;taskDetail&#39;]:
                    task_id = schedule[&#39;task&#39;][&#39;taskId&#39;]
                    description = &#39;&#39;
                    task_flags = schedule[&#39;task&#39;].get(&#39;taskFlags&#39;,0)
                    if &#39;subTasks&#39; in schedule:
                        for subtask in schedule[&#39;subTasks&#39;]:
                            schedule_id = subtask[&#39;subTask&#39;][&#39;subTaskId&#39;]
                            if &#39;description&#39; in subtask[&#39;subTask&#39;]:
                                description = subtask[&#39;pattern&#39;][&#39;description&#39;].lower(
                                )
                            if &#39;subTaskName&#39; in subtask[&#39;subTask&#39;]:
                                subtask_name = subtask[&#39;subTask&#39;][&#39;subTaskName&#39;].lower(
                                )
                            elif description:
                                subtask_name = description
                            else:
                                subtask_name = str(schedule_id)
#change schedule_id as key
                            subtask_dict[schedule_id] = {
                                &#39;task_id&#39;: task_id,
                                &#39;schedule_name&#39;: subtask_name,
                                &#39;description&#39;: description,
                                &#39;task_flags&#39;: task_flags
                            }

                return subtask_dict
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_sch_id_from_task_id(self, task_id):
        &#34;&#34;&#34;
        Gets the schedule id from the task id

        Args:
        task_id (int): task id of the schedule

        Returns:
            (int) schedule id of the schedule
        &#34;&#34;&#34;
        task_ids = [k for k, v in self.schedules.items() if v[&#39;task_id&#39;] == task_id]
        if task_ids:
            return task_ids[0]
        else:
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, &#39;Schedule id not found for corresponding task id&#39;)

    def _get_schedule_id(self, schedule_name=None, schedule_id=None, task_id=None):
        &#34;&#34;&#34;Gets the schedule id from the provided inputs.

            Args:
                schedule_name (str)  --  name of the schedule
                schedule_id (int) -- id of the schedule
                task_id (int)   -- task id of the schedule

            Returns:
            (int) schedule id of the schedule
        &#34;&#34;&#34;

        if self._single_scheduled_entity:
            # if flag set, then entity will have only one schedule associated to it so return first one from dict
            # if flag set along with task flags, then find schedule with that flags set and return that from dict
            for subtask_id, subtask_dict in self.schedules.items():
                if len(self._task_flags) == 0:
                    return subtask_id
                else:
                    task_flags = subtask_dict[&#39;task_flags&#39;]
                    match = True
                    for flag, value in self._task_flags.items():
                        if flag in task_flags:
                            if task_flags[flag] != value:
                                match = False
                        else:
                            match = False
                    if match:
                        return subtask_id
            return None

        if not task_id and not schedule_name and not schedule_id:
            raise SDKException(
                &#39;Schedules&#39;,
                &#39;102&#39;,
                &#39;Either Schedule Name or Schedule Id is needed&#39;)

        if schedule_name and not isinstance(schedule_name, str):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_id and not isinstance(schedule_id, int):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if task_id and not isinstance(task_id, int):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_name:
            schedule_name = schedule_name.lower()
            for subtask_id, subtask_dict in self.schedules.items():
                if subtask_dict[&#39;schedule_name&#39;] == schedule_name:
                    schedule_id = subtask_id

        elif task_id:
            schedule_id = self._get_sch_id_from_task_id(task_id)

        if self.schedules and schedule_id in self.schedules:
            return schedule_id

    def has_schedule(self, schedule_name=None, schedule_id=None, task_id=None):
        &#34;&#34;&#34;Checks if a schedule exists for the commcell entity with the input schedule name.

            Args:
                schedule_name (str)  --  name of the schedule
                schedule_id (int) -- id of the schedule
                task_id (int)   -- task id of the schedule

            Returns:
                bool - boolean output whether the schedule exists for the commcell entity or not

            Raises:
                SDKException:
                    if type of the schedule name argument is not string
        &#34;&#34;&#34;
        if self._get_schedule_id(schedule_name, schedule_id, task_id):
            return True
        return False

    def get(self, schedule_name=None, schedule_id=None, task_id=None):
        &#34;&#34;&#34;Returns a schedule object of the specified schedule name.

            Args:
                schedule_name (str)  --  name of the Schedule
                schedule_id (int) -- id of the schedule
                task_id (int)   -- task id of the schedule

            Returns:
                object - instance of the schedule class for the given schedule name

            Raises:
                SDKException:
                    if type of the schedule name argument is not string

                    if no schedule exists with the given name
        &#34;&#34;&#34;

        schedule_id = self._get_schedule_id(schedule_name, schedule_id, task_id)
        if schedule_id:
            return Schedule(self.class_object, schedule_id=schedule_id, task_id=self.schedules[schedule_id][&#39;task_id&#39;])

        raise SDKException(&#39;Schedules&#39;,&#39;105&#39;)

    def delete(self, schedule_name=None, schedule_id=None, task_id=None):
        &#34;&#34;&#34;deletes the specified schedule name.

                    Args:
                        schedule_name (str)  --  name of the Schedule
                        schedule_id (int) -- id of the schedule
                        task_id (int)   -- task id of the schedule

                    Raises:
                        SDKException:
                            if type of the schedule name argument is not string
                            if no schedule exists with the given name
        &#34;&#34;&#34;

        schedule_id = self._get_schedule_id(schedule_name, schedule_id, task_id)
        if schedule_id:
            request_json = {
                &#34;TMMsg_TaskOperationReq&#34;:
                    {
                        &#34;opType&#34;: 3,
                        &#34;subtaskEntity&#34;:
                            [
                                {
                                    &#34;_type_&#34;: 68,
                                    &#34;subtaskId&#34;: schedule_id
                                }
                            ]
                    }
            }

            modify_schedule = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, modify_schedule, request_json)

            if flag:
                if response.json():
                    if &#39;errorCode&#39; in response.json():
                        if response.json()[&#39;errorCode&#39;] == 0:
                            self.refresh()
                        else:
                            raise SDKException(
                                &#39;Schedules&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(
                    response.text)
                exception_message = &#39;Failed to delete schedule\nError: &#34;{0}&#34;&#39;.format(
                    response_string)

                raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, exception_message)
        else:
            raise SDKException(&#39;Schedules&#39;,&#39;105&#39;)

    def refresh(self):
        &#34;&#34;&#34;Refresh the Schedules associated with the Client / Agent / Backupset / Subclient.&#34;&#34;&#34;
        self.schedules = self._get_schedules()</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.schedules.Schedules.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, schedule_name=None, schedule_id=None, task_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>deletes the specified schedule name.</p>
<h2 id="args">Args</h2>
<p>schedule_name (str)
&ndash;
name of the Schedule
schedule_id (int) &ndash; id of the schedule
task_id (int)
&ndash; task id of the schedule</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the schedule name argument is not string
if no schedule exists with the given name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1322-L1375" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, schedule_name=None, schedule_id=None, task_id=None):
    &#34;&#34;&#34;deletes the specified schedule name.

                Args:
                    schedule_name (str)  --  name of the Schedule
                    schedule_id (int) -- id of the schedule
                    task_id (int)   -- task id of the schedule

                Raises:
                    SDKException:
                        if type of the schedule name argument is not string
                        if no schedule exists with the given name
    &#34;&#34;&#34;

    schedule_id = self._get_schedule_id(schedule_name, schedule_id, task_id)
    if schedule_id:
        request_json = {
            &#34;TMMsg_TaskOperationReq&#34;:
                {
                    &#34;opType&#34;: 3,
                    &#34;subtaskEntity&#34;:
                        [
                            {
                                &#34;_type_&#34;: 68,
                                &#34;subtaskId&#34;: schedule_id
                            }
                        ]
                }
        }

        modify_schedule = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, modify_schedule, request_json)

        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json():
                    if response.json()[&#39;errorCode&#39;] == 0:
                        self.refresh()
                    else:
                        raise SDKException(
                            &#39;Schedules&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            exception_message = &#39;Failed to delete schedule\nError: &#34;{0}&#34;&#39;.format(
                response_string)

            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, exception_message)
    else:
        raise SDKException(&#39;Schedules&#39;,&#39;105&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedules.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, schedule_name=None, schedule_id=None, task_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a schedule object of the specified schedule name.</p>
<h2 id="args">Args</h2>
<p>schedule_name (str)
&ndash;
name of the Schedule
schedule_id (int) &ndash; id of the schedule
task_id (int)
&ndash; task id of the schedule</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the schedule class for the given schedule name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the schedule name argument is not string</p>
<pre><code>if no schedule exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1298-L1320" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, schedule_name=None, schedule_id=None, task_id=None):
    &#34;&#34;&#34;Returns a schedule object of the specified schedule name.

        Args:
            schedule_name (str)  --  name of the Schedule
            schedule_id (int) -- id of the schedule
            task_id (int)   -- task id of the schedule

        Returns:
            object - instance of the schedule class for the given schedule name

        Raises:
            SDKException:
                if type of the schedule name argument is not string

                if no schedule exists with the given name
    &#34;&#34;&#34;

    schedule_id = self._get_schedule_id(schedule_name, schedule_id, task_id)
    if schedule_id:
        return Schedule(self.class_object, schedule_id=schedule_id, task_id=self.schedules[schedule_id][&#39;task_id&#39;])

    raise SDKException(&#39;Schedules&#39;,&#39;105&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedules.has_schedule"><code class="name flex">
<span>def <span class="ident">has_schedule</span></span>(<span>self, schedule_name=None, schedule_id=None, task_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a schedule exists for the commcell entity with the input schedule name.</p>
<h2 id="args">Args</h2>
<p>schedule_name (str)
&ndash;
name of the schedule
schedule_id (int) &ndash; id of the schedule
task_id (int)
&ndash; task id of the schedule</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the schedule exists for the commcell entity or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the schedule name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1279-L1296" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_schedule(self, schedule_name=None, schedule_id=None, task_id=None):
    &#34;&#34;&#34;Checks if a schedule exists for the commcell entity with the input schedule name.

        Args:
            schedule_name (str)  --  name of the schedule
            schedule_id (int) -- id of the schedule
            task_id (int)   -- task id of the schedule

        Returns:
            bool - boolean output whether the schedule exists for the commcell entity or not

        Raises:
            SDKException:
                if type of the schedule name argument is not string
    &#34;&#34;&#34;
    if self._get_schedule_id(schedule_name, schedule_id, task_id):
        return True
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.schedules.Schedules.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the Schedules associated with the Client / Agent / Backupset / Subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/schedules.py#L1377-L1379" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the Schedules associated with the Client / Agent / Backupset / Subclient.&#34;&#34;&#34;
    self.schedules = self._get_schedules()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.schedules.OperationType" href="#cvpysdk.schedules.OperationType">OperationType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.schedules.OperationType.DATA_AGING" href="#cvpysdk.schedules.OperationType.DATA_AGING">DATA_AGING</a></code></li>
<li><code><a title="cvpysdk.schedules.OperationType.REPORTS" href="#cvpysdk.schedules.OperationType.REPORTS">REPORTS</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.schedules.Schedule" href="#cvpysdk.schedules.Schedule">Schedule</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.schedules.Schedule.active_end_date" href="#cvpysdk.schedules.Schedule.active_end_date">active_end_date</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.active_start_date" href="#cvpysdk.schedules.Schedule.active_start_date">active_start_date</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.active_start_time" href="#cvpysdk.schedules.Schedule.active_start_time">active_start_time</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.automatic" href="#cvpysdk.schedules.Schedule.automatic">automatic</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.continuous" href="#cvpysdk.schedules.Schedule.continuous">continuous</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.daily" href="#cvpysdk.schedules.Schedule.daily">daily</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.disable" href="#cvpysdk.schedules.Schedule.disable">disable</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.enable" href="#cvpysdk.schedules.Schedule.enable">enable</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.end_after" href="#cvpysdk.schedules.Schedule.end_after">end_after</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.exception_dates" href="#cvpysdk.schedules.Schedule.exception_dates">exception_dates</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.is_disabled" href="#cvpysdk.schedules.Schedule.is_disabled">is_disabled</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.monthly" href="#cvpysdk.schedules.Schedule.monthly">monthly</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.monthly_relative" href="#cvpysdk.schedules.Schedule.monthly_relative">monthly_relative</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.name" href="#cvpysdk.schedules.Schedule.name">name</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.one_time" href="#cvpysdk.schedules.Schedule.one_time">one_time</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.refresh" href="#cvpysdk.schedules.Schedule.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.repeat_pattern" href="#cvpysdk.schedules.Schedule.repeat_pattern">repeat_pattern</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.run_now" href="#cvpysdk.schedules.Schedule.run_now">run_now</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.schedule_freq_type" href="#cvpysdk.schedules.Schedule.schedule_freq_type">schedule_freq_type</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.subtask_id" href="#cvpysdk.schedules.Schedule.subtask_id">subtask_id</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.weekly" href="#cvpysdk.schedules.Schedule.weekly">weekly</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.yearly" href="#cvpysdk.schedules.Schedule.yearly">yearly</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedule.yearly_relative" href="#cvpysdk.schedules.Schedule.yearly_relative">yearly_relative</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.schedules.SchedulePattern" href="#cvpysdk.schedules.SchedulePattern">SchedulePattern</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.schedules.SchedulePattern.create_schedule" href="#cvpysdk.schedules.SchedulePattern.create_schedule">create_schedule</a></code></li>
<li><code><a title="cvpysdk.schedules.SchedulePattern.create_schedule_pattern" href="#cvpysdk.schedules.SchedulePattern.create_schedule_pattern">create_schedule_pattern</a></code></li>
<li><code><a title="cvpysdk.schedules.SchedulePattern.exception_dates" href="#cvpysdk.schedules.SchedulePattern.exception_dates">exception_dates</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.schedules.Schedules" href="#cvpysdk.schedules.Schedules">Schedules</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.schedules.Schedules.delete" href="#cvpysdk.schedules.Schedules.delete">delete</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedules.get" href="#cvpysdk.schedules.Schedules.get">get</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedules.has_schedule" href="#cvpysdk.schedules.Schedules.has_schedule">has_schedule</a></code></li>
<li><code><a title="cvpysdk.schedules.Schedules.refresh" href="#cvpysdk.schedules.Schedules.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>