
curl -X 'POST' \
  'https://commserv01.lab.local/webconsole/proxy/Login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -H 'X-CSRF-Token: 9650dbcdbd9e4b12a4cd6255df4afbee' \
  -d '{
  "password": "UEBzc3cwcmQ=",
  "username": "admin"
}'


### Response body ###

{
  "aliasName": "4",
  "userGUID": "572ED010-C857-4190-B229-BC68F8E93254",
  "loginAttempts": 0,
  "remainingLockTime": 0,
  "smtpAddress": "<EMAIL>",
  "userName": "rasadmin",
  "providerType": 1,
  "ccn": 0,
  "token": "QSDK 37c20c547851cf6796d1f91a7a8978ac903b8197b2199345a7d85ba9716b56fd1669b30ad69f4811696f9edd2a2b3985e8f0d6cc54752fa5a55f5b3d73f65007802722ec5e2eccf72e0351dc96136a4bb4b923cba34e9f66c253ef580b7d894a631aa2d64a20c50ce2bd7a8aa6dd64d0dca8fa1d3a1f2c22c27acd906ccc16a27d6a91bd9a8951c1d10d518ed82a28335138a5959ed7491bbd7224d07556a653f384ff5e3b89395d80eec1e89dde5196399819d7f5433c96ce5336e340550be599ad53f1bc5bd1a5864892a863d855c2aea26b5723d64d1bf",
  "capability": *************,
  "forcePasswordChange": false,
  "isAccountLocked": false,
  "ownerOrganization": {
    "GUID": "95189d4e-4a7f-4cb3-969f-80d5871ac13f",
    "providerId": 0,
    "providerDomainName": "Commcell"
  },
  "additionalResp": {
    "nameValues": [
      {
        "name": "USERNAME",
        "value": "rasadmin"
      },
      {
        "name": "autoLoginType"
      },
      {
        "name": "fullName",
        "value": ""
      }
    ]
  },
  "providerOrganization": {
    "GUID": "95189d4e-4a7f-4cb3-969f-80d5871ac13f",
    "providerId": 0,
    "providerDomainName": "Commcell"
  },
  "errList": [],
  "company": {
    "providerId": 0,
    "providerDomainName": "Commcell"
  }
}