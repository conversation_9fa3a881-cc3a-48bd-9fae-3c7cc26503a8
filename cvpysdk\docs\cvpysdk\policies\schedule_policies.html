<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.policies.schedule_policies API documentation</title>
<meta name="description" content="Main file for performing schedule policy related operations on the commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.policies.schedule_policies</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing schedule policy related operations on the commcell.</p>
<p>This file has all the classes related to Schedule Policy operations.</p>
<p>SchedulePolicies: Class for representing all the Schedule Policies associated to the commcell.</p>
<p>SchedulePolicy: Class for representing Schedule Policy</p>
<h2 id="schedulepolicies">Schedulepolicies</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialize the SchedulePolicies instance for the commcell</p>
<p><strong>str</strong>()
&ndash;
returns all the schedule policies associated with the commcell</p>
<p><strong>repr</strong>()
&ndash;
returns a string for instance of the SchedulePolicies class</p>
<p>_get_policies()
&ndash;
gets all the schedule policies of the commcell</p>
<p>all_schedule_policies()
&ndash;
returns the dict of all the schedule policies on commcell</p>
<p>has_policy(policy_name)
&ndash;
checks if a schedule policy exists with the given name</p>
<p>subtasks_json()
&ndash;
gets the subtask in schedule policy JSON</p>
<p>schedule_json()
&ndash;
forms the schedule policy subtask with patterns and options for a schedule</p>
<p>add()
&ndash;
Adds a schedule policy</p>
<p>get()
&ndash;
Returns a schedule policy object of the specified schedule policy name</p>
<p>delete()
&ndash;
deletes the specified schedule policy name</p>
<p>refresh()
&ndash;
refresh the schedule policies associated with the commcell</p>
<p>_process_schedule_policy_response &ndash; processes the response received schedule policy creation request</p>
<h2 id="schedulepolicy">Schedulepolicy</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
Initialise the Schedule Policy class instance</p>
<p>_get_schedule_policy_id
&ndash;
Gets a schedule policy ID</p>
<p>policy_type
&ndash;
Gets the policy type of the schedule policy</p>
<p>_get_schedule_policy_properties &ndash; Gets the properties of this Schedule Policy</p>
<p>update_associations
&ndash;
Updates the schedule policy associations</p>
<p>all_schedules
&ndash; returns all the schedules associated to the schedule policy</p>
<p>_update_pattern
&ndash; Updates the schedule pattern for the provided schedule id</p>
<p>get_option
&ndash; gets the schedule options for the provided option</p>
<p>_update_option
&ndash; Updates the option for the provided schedule id</p>
<p>get_schedule
&ndash; returns the subtask dict for the provided schedule id or name</p>
<p>add_schedule
&ndash; Adds a new schedule to the schedule policy</p>
<p>modify_schedule
&ndash; Modifies the schedule with the given schedule json inputs for the given schedule
id or name</p>
<p>delete_schedule
&ndash; Deletes the schedule from the schedule policy</p>
<p>update_app_groups
&ndash; Update the appgroups for the provided schedule policy</p>
<p>_modify_schedule_policy_properties &ndash; Modifies the task properties of the schedule policy</p>
<p>_process_schedule_policy_update_response &ndash; processes the response received post update request</p>
<p>refresh
&ndash; Refresh the properties of the Schedule Policy</p>
<p>enable
&ndash; Enables a schedule policy</p>
<p>disable
&ndash; Disables a schedule policy</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L1-L1138" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing schedule policy related operations on the commcell.

This file has all the classes related to Schedule Policy operations.

SchedulePolicies: Class for representing all the Schedule Policies associated to the commcell.

SchedulePolicy: Class for representing Schedule Policy

SchedulePolicies:
    __init__(commcell_object)    --  initialize the SchedulePolicies instance for the commcell

    __str__()                    --  returns all the schedule policies associated with the commcell

    __repr__()                   --  returns a string for instance of the SchedulePolicies class

    _get_policies()              --  gets all the schedule policies of the commcell

    all_schedule_policies()      --  returns the dict of all the schedule policies on commcell

    has_policy(policy_name)      --  checks if a schedule policy exists with the given name

    subtasks_json()              --  gets the subtask in schedule policy JSON

    schedule_json()              --  forms the schedule policy subtask with patterns and options for a schedule

    add()                        --  Adds a schedule policy

    get()                        --  Returns a schedule policy object of the specified schedule policy name

    delete()                     --  deletes the specified schedule policy name

    refresh()                    --  refresh the schedule policies associated with the commcell

    _process_schedule_policy_response -- processes the response received schedule policy creation request

SchedulePolicy:

     __init__(commcell_object)      --  Initialise the Schedule Policy class instance

     _get_schedule_policy_id        --   Gets a schedule policy ID

    policy_type                     --   Gets the policy type of the schedule policy

    _get_schedule_policy_properties -- Gets the properties of this Schedule Policy

    update_associations             --  Updates the schedule policy associations

    all_schedules                   -- returns all the schedules associated to the schedule policy

    _update_pattern                 -- Updates the schedule pattern for the provided schedule id

    get_option                      -- gets the schedule options for the provided option

    _update_option                  -- Updates the option for the provided schedule id

    get_schedule                    -- returns the subtask dict for the provided schedule id or name

    add_schedule                    -- Adds a new schedule to the schedule policy

    modify_schedule                 -- Modifies the schedule with the given schedule json inputs for the given schedule
                                       id or name

    delete_schedule                 -- Deletes the schedule from the schedule policy

    update_app_groups               -- Update the appgroups for the provided schedule policy

    _modify_schedule_policy_properties -- Modifies the task properties of the schedule policy

    _process_schedule_policy_update_response -- processes the response received post update request

    refresh                         -- Refresh the properties of the Schedule Policy

    enable                          -- Enables a schedule policy

    disable                         -- Disables a schedule policy



&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from ..exception import SDKException
from .schedule_options import ScheduleOptions
from ..schedules import SchedulePattern

class OperationType:
    &#34;&#34;&#34;Operation Type for schedule policy associations and appGroups&#34;&#34;&#34;
    INCLUDE = &#39;include&#39;
    EXCLUDE = &#39;exclude&#39;,
    DELETE = &#39;deleted&#39;

class SchedulePolicies:
    &#34;&#34;&#34;Class for getting all the schedule policies associated with the commcell.&#34;&#34;&#34;

    policy_to_subtask_map = {

        &#39;Data Protection&#39;: [2, 2], #[subtaskType, OperationType]
        &#39;Auxiliary Copy&#39;: [1, 4003]
    }

    policy_types = {
        &#34;Data Protection&#34;: 0,
        &#34;Auxiliary Copy&#34;: 1,
        &#34;Primary Storage Reports&#34;: 2,
        &#34;Backup Copy&#34;: 3,
        &#34;SRM Data Collection&#34;: 4,
        &#34;Subclient Filter for BackupJob Report&#34;: 5,
        &#34;Offline Content Indexing&#34;: 6,
        &#34;Install Updates&#34;: 7,
        &#34;Network Throttle&#34;: 8,
    }

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the SchedulePolicies class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the SchedulePolicies class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._POLICY = self._commcell_object._services[&#39;SCHEDULE_POLICY&#39;]
        self._CREATE_POLICY = self._commcell_object._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]
        self._policies = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all schedule policies of the commcell.

            Returns:
                str - string of all the schedule policies associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Schedule Policy&#39;)

        for index, policy in enumerate(self._policies):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, policy)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the SchedulePolicies class.&#34;&#34;&#34;
        return &#34;SchedulePolicies class instance for Commcell&#34;

    def _get_policies(self):
        &#34;&#34;&#34;Gets all the schedule policies associated to the commcell specified by commcell object.

            Returns:
                dict - consists of all schedule policies of the commcell
                    {
                         &#34;schedule_policy1_name&#34;: schedule_policy1_id,
                         &#34;schedule_policy2_name&#34;: schedule_policy2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._POLICY)

        if flag:
            if response and response.json():
                if response.json() and &#39;taskDetail&#39; in response.json():
                    policies = response.json()[&#39;taskDetail&#39;]
                    policies_dict = {}

                    for policy in policies:
                        temp_name = policy[&#39;task&#39;][&#39;taskName&#39;].lower()
                        temp_id = str(policy[&#39;task&#39;][&#39;taskId&#39;]).lower()
                        policies_dict[temp_name] = temp_id

                    return policies_dict
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_schedule_policies(self):
        &#34;&#34;&#34;Returns the schedule policies on this commcell

            dict - consists of all schedule policies of the commcell
                    {
                         &#34;schedule_policy1_name&#34;: schedule_policy1_id,
                         &#34;schedule_policy2_name&#34;: schedule_policy2_id
                    }
        &#34;&#34;&#34;
        return self._policies

    def has_policy(self, policy_name):
        &#34;&#34;&#34;Checks if a schedule policy exists in the commcell with the input schedule policy name.

            Args:
                policy_name (str)  --  name of the schedule policy

            Returns:
                bool - boolean output whether the schedule policy exists in the commcell or not

            Raises:
                SDKException:
                    if type of the schedule policy name argument is not string
        &#34;&#34;&#34;
        if not isinstance(policy_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        return self._policies and policy_name.lower() in self._policies

    @staticmethod
    def subtasks_json(policy_type):
        &#34;&#34;&#34;
        gets the subtask in schedule policy JSON
        Args:
            policy_type (str) -- Type of the schedule policy from &#39;policy_types&#39; dict

        Returns:
            returns schedule policy Subtask
        &#34;&#34;&#34;

        _backup_subtask = {
            &#34;subTaskType&#34;: SchedulePolicies.policy_to_subtask_map[policy_type][0],
            &#34;operationType&#34;: SchedulePolicies.policy_to_subtask_map[policy_type][1]
        }

        return _backup_subtask

    @staticmethod
    def schedule_json(policy_type, schedule_dict):
        &#34;&#34;&#34;
            Returns the schedule json for the given schedule options and pattern

        Args:
            policy_type (str) -- Type of the schedule policy from &#39;policy_types&#39; dict
            schedule_dict (dict) -- with the below format, check add() module for more documentation on the below dict

                                            {
                                                pattern : {},
                                                options: {}
                                            }

        Returns:
            Returns the schedule json for the given schedule options and pattern

        &#34;&#34;&#34;
        schedule_options = ScheduleOptions(ScheduleOptions.policy_to_options_map[policy_type]
                                           ).options_json(schedule_dict.get(&#39;options&#39;, None))
        sub_task = SchedulePolicies.subtasks_json(policy_type)
        sub_task[&#39;subTaskName&#39;] = schedule_dict.get(&#39;name&#39;, &#39;&#39;)
        sub_task = {
            &#34;subTaskOperation&#34;: 1,
            &#34;subTask&#34;: sub_task,
            &#34;options&#34;: schedule_options
        }

        freq_type = schedule_dict.get(&#39;pattern&#39;, {}).get(&#39;freq_type&#39;, &#39;daily&#39;)

        try:
            schedule_dict[&#34;pattern&#34;][&#34;freq_type&#34;] = freq_type
        except KeyError:
            schedule_dict[&#34;pattern&#34;] = {&#34;freq_type&#34;: freq_type}

        task_json = SchedulePattern().create_schedule({&#39;taskInfo&#39;:
                                                           {&#39;subTasks&#39;: [sub_task]
                                                            }
                                                       }, schedule_dict.get(&#39;pattern&#39;))
        return task_json.get(&#39;taskInfo&#39;).get(&#39;subTasks&#39;)[0]


    def add(self, name, policy_type, associations, schedules, agent_type=None):
        &#34;&#34;&#34;

        Adds a schedule policy

        Args:
            name (str) -- Name of the Schedule Policy
            policy_type (str) -- Type of the schedule policy from &#39;policy_types&#39; dict
            associations (str) -- List of schedule associations
            [
            {
                &#34;clientName&#34;: &#34;scheduleclient1&#34;
            },
            {
                &#34;clientGroupName&#34;: &#34;scheduleclient2&#34;
            }
            ]

            schedules (List) -- schedules to be associated to the schedule policy

            [
                {
                    pattern : {}, -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                     pattern to be sent

                                     eg: {
                                            &#34;freq_type&#34;: &#39;daily&#39;,
                                            &#34;active_start_time&#34;: time_in_%H/%S (str),
                                            &#34;repeat_days&#34;: days_to_repeat (int)
                                         }

                    options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                                    eg:  {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: True,
                                        &#34;totalJobsToProcess&#34;: 1000,
                                        &#34;allCopies&#34;: True,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                        }
                                    }
                }
            ]



            agent_type (List) -- Agent Types to be associated to the schedule policy

                      eg:    [
                                {
                                    &#34;appGroupName&#34;: &#34;Protected Files&#34;
                                },
                                {
                                    &#34;appGroupName&#34;: &#34;Archived Files&#34;
                                }
                            ]

        sample:

                    apptype = [{&#39;appGroupName&#39;: &#39;DB2&#39;}]
                    associations = [{&#39;clientName&#39;: &#39;testclient&#39;}]
                    schedule = [
                        {
                            &#39;name&#39;: &#39;trying&#39;,
                            &#39;pattern&#39;: {
                                            &#39;freq_type&#39;: &#39;Daily&#39;
                                        }
                        }
                    ]
                    commcellobj.schedule_policies.add(&#39;testsch1&#39;, &#39;Data Protection&#39;, associations, schedule, apptype)

        Returns: schedule policy object on successful completion

        Raises:

            SDKExceptions on wrong input types and failure to create schedule policy

        &#34;&#34;&#34;

        if not isinstance(schedules, list):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, &#39;schedules should be a list&#39;)

        sub_tasks = []
        for schedule in schedules:
            sub_tasks.append(self.schedule_json(policy_type, schedule))


        schedule_policy = {
            &#34;taskInfo&#34;:
                {
                    &#34;associations&#34;: associations,
                    &#34;task&#34;:
                        {
                            &#34;description&#34;: &#34;&#34;, &#34;taskType&#34;: 4, &#34;initiatedFrom&#34;: 2,
                            &#34;policyType&#34;: self.policy_types[policy_type],
                            &#34;taskName&#34;: name,
                            &#34;securityAssociations&#34;: {},
                            &#34;taskSecurity&#34;: {},
                            &#34;alert&#34;: {&#34;alertName&#34;: &#34;&#34;},
                            &#34;taskFlags&#34;: {&#34;isEdgeDrive&#34;: False, &#34;disabled&#34;: False}
                        },
                    &#34;appGroup&#34;:
                        {
                            &#34;appGroups&#34;: agent_type if agent_type else [],
                        },
                    &#34;subTasks&#34;: sub_tasks

                }
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_POLICY, schedule_policy
        )
        output = self._process_schedule_policy_response(flag, response)
        self.refresh()

        if output[0]:
            return self.get(name)

        o_str = &#39;Failed to update properties of Schedule\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, o_str.format(output[2]))

    def get(self, schedule_policy_name, schedule_policy_id=None):
        &#34;&#34;&#34;Returns a schedule policy object of the specified schedule policy name.

            Args:
                schedule_policy_name (str)  --  name of the Schedule Policy
                schedule_policy_id (int) -- id of the schedule Policy

            Sample: sch_pol_obj = commcellobj.schedule_policies.get(&#39;testschp&#39;)

            Returns:
                object - instance of the schedule policy class for the given schedule name

            Raises:
                SDKException:
                    if type of the schedule policy name argument is not string

                    if no schedule policy exists with the given name
        &#34;&#34;&#34;

        if schedule_policy_name and not isinstance(schedule_policy_name, str):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_policy_id and not isinstance(schedule_policy_id, int):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        schedule_policy_name = schedule_policy_name.lower()
        schedule_policy_id = self.all_schedule_policies.get(schedule_policy_name)
        if self.has_policy(schedule_policy_name):
            return SchedulePolicy(
                self._commcell_object, schedule_policy_name, schedule_policy_id
            )

        raise SDKException(
            &#39;Schedules&#39;,
            &#39;102&#39;,
            &#39;No Schedule Policy exists with name: {0}&#39;.format(schedule_policy_name))

    def delete(self, schedule_policy_name):
        &#34;&#34;&#34;deletes the specified schedule policy name.

                    Args:
                        schedule_policy_name (str)  --  name of the Schedule Policy

                    Sample:  commcellobj.schedule_policies.delete(&#39;testschp&#39;)

                    Raises:
                        SDKException:
                            if type of the schedule policy name argument is not string
                            if no schedule policy exists with the given name
        &#34;&#34;&#34;

        if schedule_policy_name and not isinstance(schedule_policy_name, str):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        schedule_policy_name = schedule_policy_name.lower()
        schedule_policy_id = self.all_schedule_policies.get(schedule_policy_name)

        if schedule_policy_id:
            request_json = {
                &#34;TMMsg_TaskOperationReq&#34;:
                    {
                        &#34;opType&#34;: 3,
                        &#34;taskEntities&#34;:
                            [
                                {
                                    &#34;_type_&#34;: 69,
                                    &#34;taskId&#34;: schedule_policy_id
                                }
                            ]
                    }
            }

            modify_schedule = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, modify_schedule, request_json)

            if flag:
                if response.json():
                    if &#39;errorCode&#39; in response.json():
                        if response.json()[&#39;errorCode&#39;] == 0:
                            self.refresh()
                        else:
                            raise SDKException(
                                &#39;Schedules&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(
                    response.text)
                exception_message = &#39;Failed to delete schedule policy\nError: &#34;{0}&#34;&#39;.format(
                    response_string)

                raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, exception_message)
        else:
            raise SDKException(
                &#39;Schedules&#39;, &#39;102&#39;, &#39;No schedule policy exists for: {0}&#39;.format(
                    schedule_policy_id)
            )

    def refresh(self):
        &#34;&#34;&#34;Refresh the Schedule Policies associated with the Commcell.&#34;&#34;&#34;
        self._policies = self._get_policies()

    def _process_schedule_policy_response(self, flag, response):
        &#34;&#34;&#34;
        processes the response received post create request
        Args:
        flag: (bool) -- True or false based on response
        response: (dict) response from modify request
        Returns:
            flag: (Bool) -- based on success and failure
            error_code: (int) -- error_code from response
            error_message: (str) -- error_message from the response if any
        &#34;&#34;&#34;

        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    task_id = str(response.json()[&#34;taskId&#34;])

                    if task_id:
                        return True, &#34;0&#34;, &#34;&#34;

                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return True, &#34;0&#34;, &#34;&#34;

                    if error_message:
                        return False, error_code, error_message
                    else:
                        return False, error_code, &#34;&#34;
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

class SchedulePolicy:
    &#34;&#34;&#34;Class for performing operations for a specific Schedule.&#34;&#34;&#34;

    def __init__(self, commcell_obj, schedule_policy_name, schedule_policy_id=None):

        &#34;&#34;&#34;Initialise the Schedule Policy class instance.

            Args:
                class_object (object)     --  instance of Class Object

                schedule_policy_name      (str)     --  name of the Schedule

                schedule_policy_id        (int)     --   task ids of the Schedule



            Returns:
                object - instance of the Schedule Policy class
        &#34;&#34;&#34;

        self._commcell_object = commcell_obj

        self.schedule_policy_name = schedule_policy_name

        if schedule_policy_id:
            self.schedule_policy_id = schedule_policy_id
        else:
            self.schedule_policy_id = self._get_schedule_policy_id()


        self._SCHEDULE_POLICY = self._commcell_object._services[&#39;GET_SCHEDULE_POLICY&#39;] % (
            self.schedule_policy_id)
        self._MODIFY_SCHEDULE_POLICY = self._commcell_object._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]

        self._associations = []
        self._subtasks = []
        self._app_groups = []
        self._task_json = {}
        self._task_name = None
        self._all_schedules = []
        self.refresh()

    def _get_schedule_policy_id(self):
        &#34;&#34;&#34;
        Gets a schedule ID of the schedule policy
        Returns (int) -- schedule policy ID
        &#34;&#34;&#34;
        schedule_policies = SchedulePolicies(self._commcell_object)
        return schedule_policies.get(self.schedule_policy_name).schedule_policy_id

    @property
    def policy_type(self):
        &#34;&#34;&#34;
        Get the policy Type of the schedule policy

        Sample:  sch_pol_obj.policy_type

        Returns (str) -- Type of the schedule policy from &#39;policy_types&#39; dict

        &#34;&#34;&#34;
        return (
                list(
                SchedulePolicies.policy_types.keys())[
                list(
                    SchedulePolicies.policy_types.values()).index(self._task_json[&#39;policyType&#39;])])


    def _get_schedule_policy_properties(self):
        &#34;&#34;&#34;Gets the properties of this Schedule Policy.

            Returns:
                dict - dictionary consisting of the properties of this Schedule Policy

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SCHEDULE_POLICY)

        if flag:
            if response.json() and &#39;taskInfo&#39; in response.json():
                _task_info = response.json()[&#39;taskInfo&#39;]

                if &#39;associations&#39; in _task_info:
                    self._associations = _task_info[&#39;associations&#39;]

                if &#39;task&#39; in _task_info:
                    self._task_json = _task_info[&#39;task&#39;]

                self._app_groups = _task_info[&#39;appGroup&#39;].get(&#39;appGroups&#39;)

                self._subtasks = _task_info[&#39;subTasks&#39;]

                for subtask in self._subtasks:
                    self._all_schedules.append({
                                                &#34;schedule_name&#34; : subtask[&#34;subTask&#34;].get(&#34;subTaskName&#34;, &#39;&#39;),
                                                &#34;schedule_id&#34;: subtask[&#34;subTask&#34;][&#34;subTaskId&#34;]
                                                })


            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def update_associations(self, associations, operation_type):
        &#34;&#34;&#34;
        Updates the schedule policy associations
        Args:
            associations (str) -- List of schedule associations
            [
            {
                &#34;clientName&#34;: &#34;scheduleclient1&#34;
            },
            {
                &#34;clientGroupName&#34;: &#34;scheduleclient2&#34;
            }
            ]
            operation_type (OperationType) -- Please check OperationType class present in this file

        Sample:
            associations = [{&#39;clientName&#39;: &#39;client2&#39;}]
            sch_pol_obj.update_associations(associations, OperationType.DELETE)

        &#34;&#34;&#34;

        for app_group in associations:
            app_group[&#34;flags&#34;] = {
                                    operation_type: True
                                 }
        self._associations = associations
        self._modify_schedule_policy_properties()

    @property
    def all_schedules(self):
        &#34;&#34;&#34;
        Gets all the schedules of the schedule policy

        Sample:  sch_pol_obj.all_schedules

        Returns (dict) -- schedules in the below format

            {
                    &#34;schedule_name&#34; : (str),
                    &#34;schedule_id&#34;: (int)
            }

        &#34;&#34;&#34;
        return self._all_schedules


    def _update_pattern(self, schedule_id, pattern_dict):

        &#34;&#34;&#34;

        Updates the schedule pattern for the provided schedule id (internal function)

        Args:
            schedule_id (int) -- id of the schedule
            pattern_dict (dict) -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                     pattern to be sent

                                     eg: {
                                            &#34;freq_type&#34;: &#39;daily&#39;,
                                            &#34;active_start_time&#34;: time_in_%H/%S (str),
                                            &#34;repeat_days&#34;: days_to_repeat (int)
                                         }


        &#34;&#34;&#34;

        existing_pattern = {}

        for subtask in self._subtasks:
            if subtask[&#34;subTask&#34;][&#34;subTaskId&#34;] == schedule_id:

                if &#39;pattern&#39; in subtask:
                    existing_pattern = subtask[&#39;pattern&#39;]

                if &#39;options&#39; in subtask:
                    _options = subtask[&#39;options&#39;]
                    if &#39;commonOpts&#39; in _options:
                        if &#39;automaticSchedulePattern&#39; in _options[&#34;commonOpts&#34;]:
                            existing_pattern = _options[
                                &#34;commonOpts&#34;][&#39;automaticSchedulePattern&#39;]

                    if &#39;backupOpts&#39; in _options:
                        if &#39;dataOpt&#39; in _options[&#39;backupOpts&#39;]:
                            if isinstance(existing_pattern, dict):
                                _data_opt = _options[&#39;backupOpts&#39;][&#39;dataOpt&#39;]
                                existing_pattern.update(_data_opt)
                break


        task_json = SchedulePattern(existing_pattern).create_schedule({&#39;taskInfo&#39;:
                                                                           {&#39;subTasks&#39;: self._subtasks
                                                                            }
                                                                       }, pattern_dict, schedule_id)

        self._subtasks = task_json.get(&#39;taskInfo&#39;).get(&#39;subTasks&#39;)

    @staticmethod
    def get_option(option_dict, option):
        &#34;&#34;&#34;
        gets the schedule options for the provided option
        Args:
            option_dict: the complete options dict
            option: option for which the dict has to be fetched

        Returns (dict) -- Option dict for the provided option

        &#34;&#34;&#34;
        if isinstance(option_dict, dict) and option in option_dict:
            return option_dict[option]
        elif not isinstance(option_dict, dict):
            return None
        else:
            for value in option_dict.values():
                result = SchedulePolicy.get_option(value, option)
                if result is not None:
                    return result

    def _update_option(self, schedule_id, options):
        &#34;&#34;&#34;
        Updates the option for the provided schedule id (internal)

        Args:
             schedule_id (int) -- id of the schedule
            options (dict) -- Please refer ScheduleOptions.py classes for respective schedule options

                                    eg:  {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: True,
                                        &#34;totalJobsToProcess&#34;: 1000,
                                        &#34;allCopies&#34;: True,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                        }
                                    }

        &#34;&#34;&#34;

        option_allowed = ScheduleOptions.policy_to_options_map[self.policy_type]
        for subtask in self._subtasks:
            if subtask[&#34;subTask&#34;][&#34;subTaskId&#34;] == schedule_id:
                if &#39;options&#39; in subtask:
                    existing_options = self.get_option(subtask[&#39;options&#39;], option_allowed)
                    if not existing_options:
                        raise SDKException(&#39;Schedules&#39;, &#39;104&#39;)

                    subtask[&#39;options&#39;] = ScheduleOptions(option_allowed, existing_options).options_json(options)
                    self._subtasks[self._subtasks.index(subtask)] = subtask
                    break

    def get_schedule(self, schedule_id=None, schedule_name=None):
        &#34;&#34;&#34;
        returns the subtask dict for the provided schedule id or name
        Args:
            schedule_id (int) -- id of the schedule
            schedule_name (str) -- name of the schedule

        Sample:  sch_pol_obj.get_schedule(schedule_id=10)

        Returns (dict) -- subtask dict

        &#34;&#34;&#34;
        if not schedule_name and not schedule_id:
            raise SDKException(
                &#39;Schedules&#39;,
                &#39;102&#39;,
                &#39;Either Schedule Name or Schedule Id is needed&#39;)

        if schedule_name and not isinstance(schedule_name, str):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_id and not isinstance(schedule_id, int):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_name:
            search_dict = (&#34;subTaskName&#34;, schedule_name)
        else:
            search_dict = (&#34;subTaskId&#34;, schedule_id)

        for sub_task in self._subtasks:
            if search_dict in sub_task[&#34;subTask&#34;].items():
                return sub_task

    def add_schedule(self, schedule_dict):
        &#34;&#34;&#34;
        Adds a new schedule to the schedule policy
        Args:
            schedule_dict (dict) -- {
                    pattern : {}, -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                     pattern to be sent

                                     eg: {
                                            &#34;freq_type&#34;: &#39;daily&#39;,
                                            &#34;active_start_time&#34;: time_in_%H/%S (str),
                                            &#34;repeat_days&#34;: days_to_repeat (int)
                                         }

                    options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                                    eg:  {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: True,
                                        &#34;totalJobsToProcess&#34;: 1000,
                                        &#34;allCopies&#34;: True,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                        }
                                    }
                }

        Sample:

                sch_pol_obj.add_schedule({&#39;pattern&#39;:{&#39;freq_type&#39;: &#39;monthly&#39;}})

        &#34;&#34;&#34;
        sub_task = SchedulePolicies.schedule_json(self.policy_type, schedule_dict)
        sub_task[&#34;subTaskOperation&#34;] = 2
        self._subtasks.append(sub_task)
        self._modify_schedule_policy_properties()

    def modify_schedule(self, schedule_json, schedule_id=None, schedule_name=None):
        &#34;&#34;&#34;
        Modifies the schedule with the given schedule json inputs for the given schedule id or name
        Args:
            schedule_dict (dict) -- {
                    pattern : {}, -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                     pattern to be sent

                                     eg: {
                                            &#34;freq_type&#34;: &#39;daily&#39;,
                                            &#34;active_start_time&#34;: time_in_%H/%S (str),
                                            &#34;repeat_days&#34;: days_to_repeat (int)
                                         }

                    options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                                    eg:  {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: True,
                                        &#34;totalJobsToProcess&#34;: 1000,
                                        &#34;allCopies&#34;: True,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                        }
                                    }
                }
            schedule_id (int) -- id of the schedule
            schedule_name (str) -- name of the schedule

        Sample:
                Change Pattern:
                    sch_pol_obj.change_schedule({&#39;pattern&#39;:{&#39;freq_type&#39;: &#39;monthly&#39;}}, schedule_id=77)

                Change Options:
                    sch_pol_obj.change_schedule({&#39;options&#39;:{&#39;maxNumberOfStreams&#39;: 10}}, schedule_id=77)

        &#34;&#34;&#34;
        sub_task = self.get_schedule(schedule_id, schedule_name)
        if not sub_task:
            raise SDKException(&#39;Schedules&#39;, &#39;105&#39;)
        if &#39;pattern&#39; in schedule_json:
            self._update_pattern(sub_task[&#34;subTask&#34;][&#34;subTaskId&#34;], schedule_json.get(&#39;pattern&#39;))
        if &#39;options&#39; in schedule_json:
            self._update_option(sub_task[&#34;subTask&#34;][&#34;subTaskId&#34;], schedule_json.get(&#39;options&#39;))
        self._modify_schedule_policy_properties()

    def delete_schedule(self, schedule_id=None, schedule_name=None):
        &#34;&#34;&#34;
        Deletes the schedule from the schedule policy
        Args:
            schedule_id (int) -- id of the schedule
            schedule_name (str) -- name of the schedule

        Sample:
            sch_pol_obj.delete_schedule(schedule_name=&#39;testsch&#39;)

        &#34;&#34;&#34;
        sub_task = self.get_schedule(schedule_id, schedule_name)
        if not sub_task:
            raise SDKException(&#39;Schedules&#39;, &#39;105&#39;)
        sub_task[&#34;subTaskOperation&#34;] = 3
        self._subtasks[self._subtasks.index(sub_task)] = sub_task
        self._modify_schedule_policy_properties()

    def update_app_groups(self, app_groups, operation_type):
        &#34;&#34;&#34;
        Update the appgroups for the provided schedule policy
        Args:
            app_groups(List) -- Agent Types to be associated to the schedule policy

                      eg:    [
                                {
                                    &#34;appGroupName&#34;: &#34;Protected Files&#34;
                                },
                                {
                                    &#34;appGroupName&#34;: &#34;Archived Files&#34;
                                }
                            ]
            operation_type (OperationType) -- Please check OperationType class present in this file

        Sample:

            apptype = [{&#39;appGroupName&#39;: &#39;DB2&#39;}]
            sch_pol_obj.update_app_groups(apptype, OperationType.INCLUDE)

        &#34;&#34;&#34;
        for app_group in app_groups:
            app_group[&#34;flags&#34;] = {
                operation_type: True
            }
        self._app_groups = app_groups
        self._modify_schedule_policy_properties()

    def _modify_schedule_policy_properties(self):
        &#34;&#34;&#34;
         Modifies the task properties of the schedule policy
        Exception:
            if modification of the schedule policy failed

        &#34;&#34;&#34;
        request_json = {
            &#39;taskInfo&#39;:
                {
                    &#39;taskOperation&#39;: 1,
                    &#39;associations&#39;: self._associations,
                    &#39;task&#39;: self._task_json,
                    &#34;appGroup&#34;:
                        {
                            &#34;appGroups&#34;: self._app_groups if self._app_groups else [],
                        },
                    &#39;subTasks&#39;: self._subtasks
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MODIFY_SCHEDULE_POLICY, request_json
        )
        output = self._process_schedule_policy_update_response(flag, response)
        self.refresh()

        if output[0]:
            return

        o_str = &#39;Failed to update properties of Schedule Policy\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, o_str.format(output[2]))

    def enable(self):
        &#34;&#34;&#34;Enable a schedule policy.

                    Raises:
                        SDKException:
                            if failed to enable schedule policy

                            if response is empty

                            if response is not success
                &#34;&#34;&#34;
        enable_request = self._commcell_object._services[&#39;ENABLE_SCHEDULE&#39;]
        request_text = &#34;taskId={0}&#34;.format(self.schedule_policy_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, enable_request, request_text)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#39;Failed to enable Schedule Policy&#39;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = &#34;{0}\nError: {1}&#34;.format(error_message, response.json()[&#39;errorMessage&#39;])

                    raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, error_message)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def disable(self):
        &#34;&#34;&#34;Disable a Schedule Policy.

            Raises:
                SDKException:
                    if failed to disable Schedule Policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        disable_request = self._commcell_object._services[&#39;DISABLE_SCHEDULE&#39;]

        request_text = &#34;taskId={0}&#34;.format(self.schedule_policy_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, disable_request, request_text)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#39;Failed to disable Schedule Policy&#39;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = &#34;{0}\nError: {1}&#34;.format(error_message, response.json()[&#39;errorMessage&#39;])

                    raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, error_message)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_schedule_policy_update_response(self, flag, response):
        &#34;&#34;&#34;
        processes the response received post update request
        Args:
        flag: (bool) -- True or false based on response
        response: (dict) response from modify request
        Returns:
            flag: (Bool) -- based on success and failure
            error_code: (int) -- error_code from response
            error_message: (str) -- error_message from the response if any
        &#34;&#34;&#34;
        task_id = None
        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    task_id = str(response.json()[&#34;taskId&#34;])

                    if task_id:
                        return True, &#34;0&#34;, &#34;&#34;

                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return True, &#34;0&#34;, &#34;&#34;

                    if error_message:
                        return False, error_code, error_message
                    else:
                        return False, error_code, &#34;&#34;
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Schedule Policy.&#34;&#34;&#34;
        self._get_schedule_policy_properties()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.policies.schedule_policies.OperationType"><code class="flex name class">
<span>class <span class="ident">OperationType</span></span>
</code></dt>
<dd>
<div class="desc"><p>Operation Type for schedule policy associations and appGroups</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L106-L110" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OperationType:
    &#34;&#34;&#34;Operation Type for schedule policy associations and appGroups&#34;&#34;&#34;
    INCLUDE = &#39;include&#39;
    EXCLUDE = &#39;exclude&#39;,
    DELETE = &#39;deleted&#39;</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.policies.schedule_policies.OperationType.DELETE"><code class="name">var <span class="ident">DELETE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.policies.schedule_policies.OperationType.EXCLUDE"><code class="name">var <span class="ident">EXCLUDE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.policies.schedule_policies.OperationType.INCLUDE"><code class="name">var <span class="ident">INCLUDE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies"><code class="flex name class">
<span>class <span class="ident">SchedulePolicies</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the schedule policies associated with the commcell.</p>
<p>Initialize object of the SchedulePolicies class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the SchedulePolicies class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L112-L559" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SchedulePolicies:
    &#34;&#34;&#34;Class for getting all the schedule policies associated with the commcell.&#34;&#34;&#34;

    policy_to_subtask_map = {

        &#39;Data Protection&#39;: [2, 2], #[subtaskType, OperationType]
        &#39;Auxiliary Copy&#39;: [1, 4003]
    }

    policy_types = {
        &#34;Data Protection&#34;: 0,
        &#34;Auxiliary Copy&#34;: 1,
        &#34;Primary Storage Reports&#34;: 2,
        &#34;Backup Copy&#34;: 3,
        &#34;SRM Data Collection&#34;: 4,
        &#34;Subclient Filter for BackupJob Report&#34;: 5,
        &#34;Offline Content Indexing&#34;: 6,
        &#34;Install Updates&#34;: 7,
        &#34;Network Throttle&#34;: 8,
    }

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the SchedulePolicies class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the SchedulePolicies class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._POLICY = self._commcell_object._services[&#39;SCHEDULE_POLICY&#39;]
        self._CREATE_POLICY = self._commcell_object._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]
        self._policies = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all schedule policies of the commcell.

            Returns:
                str - string of all the schedule policies associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Schedule Policy&#39;)

        for index, policy in enumerate(self._policies):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, policy)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the SchedulePolicies class.&#34;&#34;&#34;
        return &#34;SchedulePolicies class instance for Commcell&#34;

    def _get_policies(self):
        &#34;&#34;&#34;Gets all the schedule policies associated to the commcell specified by commcell object.

            Returns:
                dict - consists of all schedule policies of the commcell
                    {
                         &#34;schedule_policy1_name&#34;: schedule_policy1_id,
                         &#34;schedule_policy2_name&#34;: schedule_policy2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._POLICY)

        if flag:
            if response and response.json():
                if response.json() and &#39;taskDetail&#39; in response.json():
                    policies = response.json()[&#39;taskDetail&#39;]
                    policies_dict = {}

                    for policy in policies:
                        temp_name = policy[&#39;task&#39;][&#39;taskName&#39;].lower()
                        temp_id = str(policy[&#39;task&#39;][&#39;taskId&#39;]).lower()
                        policies_dict[temp_name] = temp_id

                    return policies_dict
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_schedule_policies(self):
        &#34;&#34;&#34;Returns the schedule policies on this commcell

            dict - consists of all schedule policies of the commcell
                    {
                         &#34;schedule_policy1_name&#34;: schedule_policy1_id,
                         &#34;schedule_policy2_name&#34;: schedule_policy2_id
                    }
        &#34;&#34;&#34;
        return self._policies

    def has_policy(self, policy_name):
        &#34;&#34;&#34;Checks if a schedule policy exists in the commcell with the input schedule policy name.

            Args:
                policy_name (str)  --  name of the schedule policy

            Returns:
                bool - boolean output whether the schedule policy exists in the commcell or not

            Raises:
                SDKException:
                    if type of the schedule policy name argument is not string
        &#34;&#34;&#34;
        if not isinstance(policy_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        return self._policies and policy_name.lower() in self._policies

    @staticmethod
    def subtasks_json(policy_type):
        &#34;&#34;&#34;
        gets the subtask in schedule policy JSON
        Args:
            policy_type (str) -- Type of the schedule policy from &#39;policy_types&#39; dict

        Returns:
            returns schedule policy Subtask
        &#34;&#34;&#34;

        _backup_subtask = {
            &#34;subTaskType&#34;: SchedulePolicies.policy_to_subtask_map[policy_type][0],
            &#34;operationType&#34;: SchedulePolicies.policy_to_subtask_map[policy_type][1]
        }

        return _backup_subtask

    @staticmethod
    def schedule_json(policy_type, schedule_dict):
        &#34;&#34;&#34;
            Returns the schedule json for the given schedule options and pattern

        Args:
            policy_type (str) -- Type of the schedule policy from &#39;policy_types&#39; dict
            schedule_dict (dict) -- with the below format, check add() module for more documentation on the below dict

                                            {
                                                pattern : {},
                                                options: {}
                                            }

        Returns:
            Returns the schedule json for the given schedule options and pattern

        &#34;&#34;&#34;
        schedule_options = ScheduleOptions(ScheduleOptions.policy_to_options_map[policy_type]
                                           ).options_json(schedule_dict.get(&#39;options&#39;, None))
        sub_task = SchedulePolicies.subtasks_json(policy_type)
        sub_task[&#39;subTaskName&#39;] = schedule_dict.get(&#39;name&#39;, &#39;&#39;)
        sub_task = {
            &#34;subTaskOperation&#34;: 1,
            &#34;subTask&#34;: sub_task,
            &#34;options&#34;: schedule_options
        }

        freq_type = schedule_dict.get(&#39;pattern&#39;, {}).get(&#39;freq_type&#39;, &#39;daily&#39;)

        try:
            schedule_dict[&#34;pattern&#34;][&#34;freq_type&#34;] = freq_type
        except KeyError:
            schedule_dict[&#34;pattern&#34;] = {&#34;freq_type&#34;: freq_type}

        task_json = SchedulePattern().create_schedule({&#39;taskInfo&#39;:
                                                           {&#39;subTasks&#39;: [sub_task]
                                                            }
                                                       }, schedule_dict.get(&#39;pattern&#39;))
        return task_json.get(&#39;taskInfo&#39;).get(&#39;subTasks&#39;)[0]


    def add(self, name, policy_type, associations, schedules, agent_type=None):
        &#34;&#34;&#34;

        Adds a schedule policy

        Args:
            name (str) -- Name of the Schedule Policy
            policy_type (str) -- Type of the schedule policy from &#39;policy_types&#39; dict
            associations (str) -- List of schedule associations
            [
            {
                &#34;clientName&#34;: &#34;scheduleclient1&#34;
            },
            {
                &#34;clientGroupName&#34;: &#34;scheduleclient2&#34;
            }
            ]

            schedules (List) -- schedules to be associated to the schedule policy

            [
                {
                    pattern : {}, -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                     pattern to be sent

                                     eg: {
                                            &#34;freq_type&#34;: &#39;daily&#39;,
                                            &#34;active_start_time&#34;: time_in_%H/%S (str),
                                            &#34;repeat_days&#34;: days_to_repeat (int)
                                         }

                    options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                                    eg:  {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: True,
                                        &#34;totalJobsToProcess&#34;: 1000,
                                        &#34;allCopies&#34;: True,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                        }
                                    }
                }
            ]



            agent_type (List) -- Agent Types to be associated to the schedule policy

                      eg:    [
                                {
                                    &#34;appGroupName&#34;: &#34;Protected Files&#34;
                                },
                                {
                                    &#34;appGroupName&#34;: &#34;Archived Files&#34;
                                }
                            ]

        sample:

                    apptype = [{&#39;appGroupName&#39;: &#39;DB2&#39;}]
                    associations = [{&#39;clientName&#39;: &#39;testclient&#39;}]
                    schedule = [
                        {
                            &#39;name&#39;: &#39;trying&#39;,
                            &#39;pattern&#39;: {
                                            &#39;freq_type&#39;: &#39;Daily&#39;
                                        }
                        }
                    ]
                    commcellobj.schedule_policies.add(&#39;testsch1&#39;, &#39;Data Protection&#39;, associations, schedule, apptype)

        Returns: schedule policy object on successful completion

        Raises:

            SDKExceptions on wrong input types and failure to create schedule policy

        &#34;&#34;&#34;

        if not isinstance(schedules, list):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, &#39;schedules should be a list&#39;)

        sub_tasks = []
        for schedule in schedules:
            sub_tasks.append(self.schedule_json(policy_type, schedule))


        schedule_policy = {
            &#34;taskInfo&#34;:
                {
                    &#34;associations&#34;: associations,
                    &#34;task&#34;:
                        {
                            &#34;description&#34;: &#34;&#34;, &#34;taskType&#34;: 4, &#34;initiatedFrom&#34;: 2,
                            &#34;policyType&#34;: self.policy_types[policy_type],
                            &#34;taskName&#34;: name,
                            &#34;securityAssociations&#34;: {},
                            &#34;taskSecurity&#34;: {},
                            &#34;alert&#34;: {&#34;alertName&#34;: &#34;&#34;},
                            &#34;taskFlags&#34;: {&#34;isEdgeDrive&#34;: False, &#34;disabled&#34;: False}
                        },
                    &#34;appGroup&#34;:
                        {
                            &#34;appGroups&#34;: agent_type if agent_type else [],
                        },
                    &#34;subTasks&#34;: sub_tasks

                }
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_POLICY, schedule_policy
        )
        output = self._process_schedule_policy_response(flag, response)
        self.refresh()

        if output[0]:
            return self.get(name)

        o_str = &#39;Failed to update properties of Schedule\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, o_str.format(output[2]))

    def get(self, schedule_policy_name, schedule_policy_id=None):
        &#34;&#34;&#34;Returns a schedule policy object of the specified schedule policy name.

            Args:
                schedule_policy_name (str)  --  name of the Schedule Policy
                schedule_policy_id (int) -- id of the schedule Policy

            Sample: sch_pol_obj = commcellobj.schedule_policies.get(&#39;testschp&#39;)

            Returns:
                object - instance of the schedule policy class for the given schedule name

            Raises:
                SDKException:
                    if type of the schedule policy name argument is not string

                    if no schedule policy exists with the given name
        &#34;&#34;&#34;

        if schedule_policy_name and not isinstance(schedule_policy_name, str):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_policy_id and not isinstance(schedule_policy_id, int):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        schedule_policy_name = schedule_policy_name.lower()
        schedule_policy_id = self.all_schedule_policies.get(schedule_policy_name)
        if self.has_policy(schedule_policy_name):
            return SchedulePolicy(
                self._commcell_object, schedule_policy_name, schedule_policy_id
            )

        raise SDKException(
            &#39;Schedules&#39;,
            &#39;102&#39;,
            &#39;No Schedule Policy exists with name: {0}&#39;.format(schedule_policy_name))

    def delete(self, schedule_policy_name):
        &#34;&#34;&#34;deletes the specified schedule policy name.

                    Args:
                        schedule_policy_name (str)  --  name of the Schedule Policy

                    Sample:  commcellobj.schedule_policies.delete(&#39;testschp&#39;)

                    Raises:
                        SDKException:
                            if type of the schedule policy name argument is not string
                            if no schedule policy exists with the given name
        &#34;&#34;&#34;

        if schedule_policy_name and not isinstance(schedule_policy_name, str):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        schedule_policy_name = schedule_policy_name.lower()
        schedule_policy_id = self.all_schedule_policies.get(schedule_policy_name)

        if schedule_policy_id:
            request_json = {
                &#34;TMMsg_TaskOperationReq&#34;:
                    {
                        &#34;opType&#34;: 3,
                        &#34;taskEntities&#34;:
                            [
                                {
                                    &#34;_type_&#34;: 69,
                                    &#34;taskId&#34;: schedule_policy_id
                                }
                            ]
                    }
            }

            modify_schedule = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, modify_schedule, request_json)

            if flag:
                if response.json():
                    if &#39;errorCode&#39; in response.json():
                        if response.json()[&#39;errorCode&#39;] == 0:
                            self.refresh()
                        else:
                            raise SDKException(
                                &#39;Schedules&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(
                    response.text)
                exception_message = &#39;Failed to delete schedule policy\nError: &#34;{0}&#34;&#39;.format(
                    response_string)

                raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, exception_message)
        else:
            raise SDKException(
                &#39;Schedules&#39;, &#39;102&#39;, &#39;No schedule policy exists for: {0}&#39;.format(
                    schedule_policy_id)
            )

    def refresh(self):
        &#34;&#34;&#34;Refresh the Schedule Policies associated with the Commcell.&#34;&#34;&#34;
        self._policies = self._get_policies()

    def _process_schedule_policy_response(self, flag, response):
        &#34;&#34;&#34;
        processes the response received post create request
        Args:
        flag: (bool) -- True or false based on response
        response: (dict) response from modify request
        Returns:
            flag: (Bool) -- based on success and failure
            error_code: (int) -- error_code from response
            error_message: (str) -- error_message from the response if any
        &#34;&#34;&#34;

        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    task_id = str(response.json()[&#34;taskId&#34;])

                    if task_id:
                        return True, &#34;0&#34;, &#34;&#34;

                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return True, &#34;0&#34;, &#34;&#34;

                    if error_message:
                        return False, error_code, error_message
                    else:
                        return False, error_code, &#34;&#34;
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.policy_to_subtask_map"><code class="name">var <span class="ident">policy_to_subtask_map</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.policy_types"><code class="name">var <span class="ident">policy_types</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
<h3>Static methods</h3>
<dl>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.schedule_json"><code class="name flex">
<span>def <span class="ident">schedule_json</span></span>(<span>policy_type, schedule_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the schedule json for the given schedule options and pattern</p>
<h2 id="args">Args</h2>
<p>policy_type (str) &ndash; Type of the schedule policy from 'policy_types' dict
schedule_dict (dict) &ndash; with the below format, check add() module for more documentation on the below dict</p>
<pre><code>                            {
                                pattern : {},
                                options: {}
                            }
</code></pre>
<h2 id="returns">Returns</h2>
<p>Returns the schedule json for the given schedule options and pattern</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L252-L291" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@staticmethod
def schedule_json(policy_type, schedule_dict):
    &#34;&#34;&#34;
        Returns the schedule json for the given schedule options and pattern

    Args:
        policy_type (str) -- Type of the schedule policy from &#39;policy_types&#39; dict
        schedule_dict (dict) -- with the below format, check add() module for more documentation on the below dict

                                        {
                                            pattern : {},
                                            options: {}
                                        }

    Returns:
        Returns the schedule json for the given schedule options and pattern

    &#34;&#34;&#34;
    schedule_options = ScheduleOptions(ScheduleOptions.policy_to_options_map[policy_type]
                                       ).options_json(schedule_dict.get(&#39;options&#39;, None))
    sub_task = SchedulePolicies.subtasks_json(policy_type)
    sub_task[&#39;subTaskName&#39;] = schedule_dict.get(&#39;name&#39;, &#39;&#39;)
    sub_task = {
        &#34;subTaskOperation&#34;: 1,
        &#34;subTask&#34;: sub_task,
        &#34;options&#34;: schedule_options
    }

    freq_type = schedule_dict.get(&#39;pattern&#39;, {}).get(&#39;freq_type&#39;, &#39;daily&#39;)

    try:
        schedule_dict[&#34;pattern&#34;][&#34;freq_type&#34;] = freq_type
    except KeyError:
        schedule_dict[&#34;pattern&#34;] = {&#34;freq_type&#34;: freq_type}

    task_json = SchedulePattern().create_schedule({&#39;taskInfo&#39;:
                                                       {&#39;subTasks&#39;: [sub_task]
                                                        }
                                                   }, schedule_dict.get(&#39;pattern&#39;))
    return task_json.get(&#39;taskInfo&#39;).get(&#39;subTasks&#39;)[0]</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.subtasks_json"><code class="name flex">
<span>def <span class="ident">subtasks_json</span></span>(<span>policy_type)</span>
</code></dt>
<dd>
<div class="desc"><p>gets the subtask in schedule policy JSON</p>
<h2 id="args">Args</h2>
<p>policy_type (str) &ndash; Type of the schedule policy from 'policy_types' dict</p>
<h2 id="returns">Returns</h2>
<p>returns schedule policy Subtask</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L234-L250" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@staticmethod
def subtasks_json(policy_type):
    &#34;&#34;&#34;
    gets the subtask in schedule policy JSON
    Args:
        policy_type (str) -- Type of the schedule policy from &#39;policy_types&#39; dict

    Returns:
        returns schedule policy Subtask
    &#34;&#34;&#34;

    _backup_subtask = {
        &#34;subTaskType&#34;: SchedulePolicies.policy_to_subtask_map[policy_type][0],
        &#34;operationType&#34;: SchedulePolicies.policy_to_subtask_map[policy_type][1]
    }

    return _backup_subtask</code></pre>
</details>
</dd>
</dl>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.all_schedule_policies"><code class="name">var <span class="ident">all_schedule_policies</span></code></dt>
<dd>
<div class="desc"><p>Returns the schedule policies on this commcell</p>
<p>dict - consists of all schedule policies of the commcell
{
"schedule_policy1_name": schedule_policy1_id,
"schedule_policy2_name": schedule_policy2_id
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L204-L214" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_schedule_policies(self):
    &#34;&#34;&#34;Returns the schedule policies on this commcell

        dict - consists of all schedule policies of the commcell
                {
                     &#34;schedule_policy1_name&#34;: schedule_policy1_id,
                     &#34;schedule_policy2_name&#34;: schedule_policy2_id
                }
    &#34;&#34;&#34;
    return self._policies</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, name, policy_type, associations, schedules, agent_type=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a schedule policy</p>
<h2 id="args">Args</h2>
<p>name (str) &ndash; Name of the Schedule Policy
policy_type (str) &ndash; Type of the schedule policy from 'policy_types' dict
associations (str) &ndash; List of schedule associations
[
{
"clientName": "scheduleclient1"
},
{
"clientGroupName": "scheduleclient2"
}
]</p>
<p>schedules (List) &ndash; schedules to be associated to the schedule policy</p>
<p>[
{
pattern : {}, &ndash; Please refer SchedulePattern.create_schedule in schedules.py for the types of
pattern to be sent</p>
<pre><code>                     eg: {
                            "freq_type": 'daily',
                            "active_start_time": time_in_%H/%S (str),
                            "repeat_days": days_to_repeat (int)
                         }

    options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                    eg:  {
                        "maxNumberOfStreams": 0,
                        "useMaximumStreams": True,
                        "useScallableResourceManagement": True,
                        "totalJobsToProcess": 1000,
                        "allCopies": True,
                        "mediaAgent": {
                            "mediaAgentName": "&lt;ANY MEDIAAGENT&gt;"
                        }
                    }
}
</code></pre>
<p>]</p>
<p>agent_type (List) &ndash; Agent Types to be associated to the schedule policy</p>
<pre><code>      eg:    [
                {
                    "appGroupName": "Protected Files"
                },
                {
                    "appGroupName": "Archived Files"
                }
            ]
</code></pre>
<p>sample:</p>
<pre><code>        apptype = [{'appGroupName': 'DB2'}]
        associations = [{'clientName': 'testclient'}]
        schedule = [
            {
                'name': 'trying',
                'pattern': {
                                'freq_type': 'Daily'
                            }
            }
        ]
        commcellobj.schedule_policies.add('testsch1', 'Data Protection', associations, schedule, apptype)
</code></pre>
<p>Returns: schedule policy object on successful completion</p>
<h2 id="raises">Raises</h2>
<p>SDKExceptions on wrong input types and failure to create schedule policy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L294-L415" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, name, policy_type, associations, schedules, agent_type=None):
    &#34;&#34;&#34;

    Adds a schedule policy

    Args:
        name (str) -- Name of the Schedule Policy
        policy_type (str) -- Type of the schedule policy from &#39;policy_types&#39; dict
        associations (str) -- List of schedule associations
        [
        {
            &#34;clientName&#34;: &#34;scheduleclient1&#34;
        },
        {
            &#34;clientGroupName&#34;: &#34;scheduleclient2&#34;
        }
        ]

        schedules (List) -- schedules to be associated to the schedule policy

        [
            {
                pattern : {}, -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                 pattern to be sent

                                 eg: {
                                        &#34;freq_type&#34;: &#39;daily&#39;,
                                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                                        &#34;repeat_days&#34;: days_to_repeat (int)
                                     }

                options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                                eg:  {
                                    &#34;maxNumberOfStreams&#34;: 0,
                                    &#34;useMaximumStreams&#34;: True,
                                    &#34;useScallableResourceManagement&#34;: True,
                                    &#34;totalJobsToProcess&#34;: 1000,
                                    &#34;allCopies&#34;: True,
                                    &#34;mediaAgent&#34;: {
                                        &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                    }
                                }
            }
        ]



        agent_type (List) -- Agent Types to be associated to the schedule policy

                  eg:    [
                            {
                                &#34;appGroupName&#34;: &#34;Protected Files&#34;
                            },
                            {
                                &#34;appGroupName&#34;: &#34;Archived Files&#34;
                            }
                        ]

    sample:

                apptype = [{&#39;appGroupName&#39;: &#39;DB2&#39;}]
                associations = [{&#39;clientName&#39;: &#39;testclient&#39;}]
                schedule = [
                    {
                        &#39;name&#39;: &#39;trying&#39;,
                        &#39;pattern&#39;: {
                                        &#39;freq_type&#39;: &#39;Daily&#39;
                                    }
                    }
                ]
                commcellobj.schedule_policies.add(&#39;testsch1&#39;, &#39;Data Protection&#39;, associations, schedule, apptype)

    Returns: schedule policy object on successful completion

    Raises:

        SDKExceptions on wrong input types and failure to create schedule policy

    &#34;&#34;&#34;

    if not isinstance(schedules, list):
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, &#39;schedules should be a list&#39;)

    sub_tasks = []
    for schedule in schedules:
        sub_tasks.append(self.schedule_json(policy_type, schedule))


    schedule_policy = {
        &#34;taskInfo&#34;:
            {
                &#34;associations&#34;: associations,
                &#34;task&#34;:
                    {
                        &#34;description&#34;: &#34;&#34;, &#34;taskType&#34;: 4, &#34;initiatedFrom&#34;: 2,
                        &#34;policyType&#34;: self.policy_types[policy_type],
                        &#34;taskName&#34;: name,
                        &#34;securityAssociations&#34;: {},
                        &#34;taskSecurity&#34;: {},
                        &#34;alert&#34;: {&#34;alertName&#34;: &#34;&#34;},
                        &#34;taskFlags&#34;: {&#34;isEdgeDrive&#34;: False, &#34;disabled&#34;: False}
                    },
                &#34;appGroup&#34;:
                    {
                        &#34;appGroups&#34;: agent_type if agent_type else [],
                    },
                &#34;subTasks&#34;: sub_tasks

            }
    }
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._CREATE_POLICY, schedule_policy
    )
    output = self._process_schedule_policy_response(flag, response)
    self.refresh()

    if output[0]:
        return self.get(name)

    o_str = &#39;Failed to update properties of Schedule\nError: &#34;{0}&#34;&#39;
    raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, o_str.format(output[2]))</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, schedule_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>deletes the specified schedule policy name.</p>
<h2 id="args">Args</h2>
<p>schedule_policy_name (str)
&ndash;
name of the Schedule Policy
Sample:
commcellobj.schedule_policies.delete('testschp')</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the schedule policy name argument is not string
if no schedule policy exists with the given name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L454-L515" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, schedule_policy_name):
    &#34;&#34;&#34;deletes the specified schedule policy name.

                Args:
                    schedule_policy_name (str)  --  name of the Schedule Policy

                Sample:  commcellobj.schedule_policies.delete(&#39;testschp&#39;)

                Raises:
                    SDKException:
                        if type of the schedule policy name argument is not string
                        if no schedule policy exists with the given name
    &#34;&#34;&#34;

    if schedule_policy_name and not isinstance(schedule_policy_name, str):
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

    schedule_policy_name = schedule_policy_name.lower()
    schedule_policy_id = self.all_schedule_policies.get(schedule_policy_name)

    if schedule_policy_id:
        request_json = {
            &#34;TMMsg_TaskOperationReq&#34;:
                {
                    &#34;opType&#34;: 3,
                    &#34;taskEntities&#34;:
                        [
                            {
                                &#34;_type_&#34;: 69,
                                &#34;taskId&#34;: schedule_policy_id
                            }
                        ]
                }
        }

        modify_schedule = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, modify_schedule, request_json)

        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json():
                    if response.json()[&#39;errorCode&#39;] == 0:
                        self.refresh()
                    else:
                        raise SDKException(
                            &#39;Schedules&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            exception_message = &#39;Failed to delete schedule policy\nError: &#34;{0}&#34;&#39;.format(
                response_string)

            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, exception_message)
    else:
        raise SDKException(
            &#39;Schedules&#39;, &#39;102&#39;, &#39;No schedule policy exists for: {0}&#39;.format(
                schedule_policy_id)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, schedule_policy_name, schedule_policy_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a schedule policy object of the specified schedule policy name.</p>
<h2 id="args">Args</h2>
<p>schedule_policy_name (str)
&ndash;
name of the Schedule Policy
schedule_policy_id (int) &ndash; id of the schedule Policy
Sample: sch_pol_obj = commcellobj.schedule_policies.get('testschp')</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the schedule policy class for the given schedule name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the schedule policy name argument is not string</p>
<pre><code>if no schedule policy exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L417-L452" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, schedule_policy_name, schedule_policy_id=None):
    &#34;&#34;&#34;Returns a schedule policy object of the specified schedule policy name.

        Args:
            schedule_policy_name (str)  --  name of the Schedule Policy
            schedule_policy_id (int) -- id of the schedule Policy

        Sample: sch_pol_obj = commcellobj.schedule_policies.get(&#39;testschp&#39;)

        Returns:
            object - instance of the schedule policy class for the given schedule name

        Raises:
            SDKException:
                if type of the schedule policy name argument is not string

                if no schedule policy exists with the given name
    &#34;&#34;&#34;

    if schedule_policy_name and not isinstance(schedule_policy_name, str):
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

    if schedule_policy_id and not isinstance(schedule_policy_id, int):
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

    schedule_policy_name = schedule_policy_name.lower()
    schedule_policy_id = self.all_schedule_policies.get(schedule_policy_name)
    if self.has_policy(schedule_policy_name):
        return SchedulePolicy(
            self._commcell_object, schedule_policy_name, schedule_policy_id
        )

    raise SDKException(
        &#39;Schedules&#39;,
        &#39;102&#39;,
        &#39;No Schedule Policy exists with name: {0}&#39;.format(schedule_policy_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.has_policy"><code class="name flex">
<span>def <span class="ident">has_policy</span></span>(<span>self, policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a schedule policy exists in the commcell with the input schedule policy name.</p>
<h2 id="args">Args</h2>
<p>policy_name (str)
&ndash;
name of the schedule policy</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the schedule policy exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the schedule policy name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L216-L232" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_policy(self, policy_name):
    &#34;&#34;&#34;Checks if a schedule policy exists in the commcell with the input schedule policy name.

        Args:
            policy_name (str)  --  name of the schedule policy

        Returns:
            bool - boolean output whether the schedule policy exists in the commcell or not

        Raises:
            SDKException:
                if type of the schedule policy name argument is not string
    &#34;&#34;&#34;
    if not isinstance(policy_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    return self._policies and policy_name.lower() in self._policies</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicies.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the Schedule Policies associated with the Commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L517-L519" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the Schedule Policies associated with the Commcell.&#34;&#34;&#34;
    self._policies = self._get_policies()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy"><code class="flex name class">
<span>class <span class="ident">SchedulePolicy</span></span>
<span>(</span><span>commcell_obj, schedule_policy_name, schedule_policy_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations for a specific Schedule.</p>
<p>Initialise the Schedule Policy class instance.</p>
<h2 id="args">Args</h2>
<p>class_object (object)
&ndash;
instance of Class Object</p>
<p>schedule_policy_name
(str)
&ndash;
name of the Schedule</p>
<p>schedule_policy_id
(int)
&ndash;
task ids of the Schedule</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Schedule Policy class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L561-L1138" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SchedulePolicy:
    &#34;&#34;&#34;Class for performing operations for a specific Schedule.&#34;&#34;&#34;

    def __init__(self, commcell_obj, schedule_policy_name, schedule_policy_id=None):

        &#34;&#34;&#34;Initialise the Schedule Policy class instance.

            Args:
                class_object (object)     --  instance of Class Object

                schedule_policy_name      (str)     --  name of the Schedule

                schedule_policy_id        (int)     --   task ids of the Schedule



            Returns:
                object - instance of the Schedule Policy class
        &#34;&#34;&#34;

        self._commcell_object = commcell_obj

        self.schedule_policy_name = schedule_policy_name

        if schedule_policy_id:
            self.schedule_policy_id = schedule_policy_id
        else:
            self.schedule_policy_id = self._get_schedule_policy_id()


        self._SCHEDULE_POLICY = self._commcell_object._services[&#39;GET_SCHEDULE_POLICY&#39;] % (
            self.schedule_policy_id)
        self._MODIFY_SCHEDULE_POLICY = self._commcell_object._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]

        self._associations = []
        self._subtasks = []
        self._app_groups = []
        self._task_json = {}
        self._task_name = None
        self._all_schedules = []
        self.refresh()

    def _get_schedule_policy_id(self):
        &#34;&#34;&#34;
        Gets a schedule ID of the schedule policy
        Returns (int) -- schedule policy ID
        &#34;&#34;&#34;
        schedule_policies = SchedulePolicies(self._commcell_object)
        return schedule_policies.get(self.schedule_policy_name).schedule_policy_id

    @property
    def policy_type(self):
        &#34;&#34;&#34;
        Get the policy Type of the schedule policy

        Sample:  sch_pol_obj.policy_type

        Returns (str) -- Type of the schedule policy from &#39;policy_types&#39; dict

        &#34;&#34;&#34;
        return (
                list(
                SchedulePolicies.policy_types.keys())[
                list(
                    SchedulePolicies.policy_types.values()).index(self._task_json[&#39;policyType&#39;])])


    def _get_schedule_policy_properties(self):
        &#34;&#34;&#34;Gets the properties of this Schedule Policy.

            Returns:
                dict - dictionary consisting of the properties of this Schedule Policy

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SCHEDULE_POLICY)

        if flag:
            if response.json() and &#39;taskInfo&#39; in response.json():
                _task_info = response.json()[&#39;taskInfo&#39;]

                if &#39;associations&#39; in _task_info:
                    self._associations = _task_info[&#39;associations&#39;]

                if &#39;task&#39; in _task_info:
                    self._task_json = _task_info[&#39;task&#39;]

                self._app_groups = _task_info[&#39;appGroup&#39;].get(&#39;appGroups&#39;)

                self._subtasks = _task_info[&#39;subTasks&#39;]

                for subtask in self._subtasks:
                    self._all_schedules.append({
                                                &#34;schedule_name&#34; : subtask[&#34;subTask&#34;].get(&#34;subTaskName&#34;, &#39;&#39;),
                                                &#34;schedule_id&#34;: subtask[&#34;subTask&#34;][&#34;subTaskId&#34;]
                                                })


            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def update_associations(self, associations, operation_type):
        &#34;&#34;&#34;
        Updates the schedule policy associations
        Args:
            associations (str) -- List of schedule associations
            [
            {
                &#34;clientName&#34;: &#34;scheduleclient1&#34;
            },
            {
                &#34;clientGroupName&#34;: &#34;scheduleclient2&#34;
            }
            ]
            operation_type (OperationType) -- Please check OperationType class present in this file

        Sample:
            associations = [{&#39;clientName&#39;: &#39;client2&#39;}]
            sch_pol_obj.update_associations(associations, OperationType.DELETE)

        &#34;&#34;&#34;

        for app_group in associations:
            app_group[&#34;flags&#34;] = {
                                    operation_type: True
                                 }
        self._associations = associations
        self._modify_schedule_policy_properties()

    @property
    def all_schedules(self):
        &#34;&#34;&#34;
        Gets all the schedules of the schedule policy

        Sample:  sch_pol_obj.all_schedules

        Returns (dict) -- schedules in the below format

            {
                    &#34;schedule_name&#34; : (str),
                    &#34;schedule_id&#34;: (int)
            }

        &#34;&#34;&#34;
        return self._all_schedules


    def _update_pattern(self, schedule_id, pattern_dict):

        &#34;&#34;&#34;

        Updates the schedule pattern for the provided schedule id (internal function)

        Args:
            schedule_id (int) -- id of the schedule
            pattern_dict (dict) -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                     pattern to be sent

                                     eg: {
                                            &#34;freq_type&#34;: &#39;daily&#39;,
                                            &#34;active_start_time&#34;: time_in_%H/%S (str),
                                            &#34;repeat_days&#34;: days_to_repeat (int)
                                         }


        &#34;&#34;&#34;

        existing_pattern = {}

        for subtask in self._subtasks:
            if subtask[&#34;subTask&#34;][&#34;subTaskId&#34;] == schedule_id:

                if &#39;pattern&#39; in subtask:
                    existing_pattern = subtask[&#39;pattern&#39;]

                if &#39;options&#39; in subtask:
                    _options = subtask[&#39;options&#39;]
                    if &#39;commonOpts&#39; in _options:
                        if &#39;automaticSchedulePattern&#39; in _options[&#34;commonOpts&#34;]:
                            existing_pattern = _options[
                                &#34;commonOpts&#34;][&#39;automaticSchedulePattern&#39;]

                    if &#39;backupOpts&#39; in _options:
                        if &#39;dataOpt&#39; in _options[&#39;backupOpts&#39;]:
                            if isinstance(existing_pattern, dict):
                                _data_opt = _options[&#39;backupOpts&#39;][&#39;dataOpt&#39;]
                                existing_pattern.update(_data_opt)
                break


        task_json = SchedulePattern(existing_pattern).create_schedule({&#39;taskInfo&#39;:
                                                                           {&#39;subTasks&#39;: self._subtasks
                                                                            }
                                                                       }, pattern_dict, schedule_id)

        self._subtasks = task_json.get(&#39;taskInfo&#39;).get(&#39;subTasks&#39;)

    @staticmethod
    def get_option(option_dict, option):
        &#34;&#34;&#34;
        gets the schedule options for the provided option
        Args:
            option_dict: the complete options dict
            option: option for which the dict has to be fetched

        Returns (dict) -- Option dict for the provided option

        &#34;&#34;&#34;
        if isinstance(option_dict, dict) and option in option_dict:
            return option_dict[option]
        elif not isinstance(option_dict, dict):
            return None
        else:
            for value in option_dict.values():
                result = SchedulePolicy.get_option(value, option)
                if result is not None:
                    return result

    def _update_option(self, schedule_id, options):
        &#34;&#34;&#34;
        Updates the option for the provided schedule id (internal)

        Args:
             schedule_id (int) -- id of the schedule
            options (dict) -- Please refer ScheduleOptions.py classes for respective schedule options

                                    eg:  {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: True,
                                        &#34;totalJobsToProcess&#34;: 1000,
                                        &#34;allCopies&#34;: True,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                        }
                                    }

        &#34;&#34;&#34;

        option_allowed = ScheduleOptions.policy_to_options_map[self.policy_type]
        for subtask in self._subtasks:
            if subtask[&#34;subTask&#34;][&#34;subTaskId&#34;] == schedule_id:
                if &#39;options&#39; in subtask:
                    existing_options = self.get_option(subtask[&#39;options&#39;], option_allowed)
                    if not existing_options:
                        raise SDKException(&#39;Schedules&#39;, &#39;104&#39;)

                    subtask[&#39;options&#39;] = ScheduleOptions(option_allowed, existing_options).options_json(options)
                    self._subtasks[self._subtasks.index(subtask)] = subtask
                    break

    def get_schedule(self, schedule_id=None, schedule_name=None):
        &#34;&#34;&#34;
        returns the subtask dict for the provided schedule id or name
        Args:
            schedule_id (int) -- id of the schedule
            schedule_name (str) -- name of the schedule

        Sample:  sch_pol_obj.get_schedule(schedule_id=10)

        Returns (dict) -- subtask dict

        &#34;&#34;&#34;
        if not schedule_name and not schedule_id:
            raise SDKException(
                &#39;Schedules&#39;,
                &#39;102&#39;,
                &#39;Either Schedule Name or Schedule Id is needed&#39;)

        if schedule_name and not isinstance(schedule_name, str):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_id and not isinstance(schedule_id, int):
            raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

        if schedule_name:
            search_dict = (&#34;subTaskName&#34;, schedule_name)
        else:
            search_dict = (&#34;subTaskId&#34;, schedule_id)

        for sub_task in self._subtasks:
            if search_dict in sub_task[&#34;subTask&#34;].items():
                return sub_task

    def add_schedule(self, schedule_dict):
        &#34;&#34;&#34;
        Adds a new schedule to the schedule policy
        Args:
            schedule_dict (dict) -- {
                    pattern : {}, -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                     pattern to be sent

                                     eg: {
                                            &#34;freq_type&#34;: &#39;daily&#39;,
                                            &#34;active_start_time&#34;: time_in_%H/%S (str),
                                            &#34;repeat_days&#34;: days_to_repeat (int)
                                         }

                    options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                                    eg:  {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: True,
                                        &#34;totalJobsToProcess&#34;: 1000,
                                        &#34;allCopies&#34;: True,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                        }
                                    }
                }

        Sample:

                sch_pol_obj.add_schedule({&#39;pattern&#39;:{&#39;freq_type&#39;: &#39;monthly&#39;}})

        &#34;&#34;&#34;
        sub_task = SchedulePolicies.schedule_json(self.policy_type, schedule_dict)
        sub_task[&#34;subTaskOperation&#34;] = 2
        self._subtasks.append(sub_task)
        self._modify_schedule_policy_properties()

    def modify_schedule(self, schedule_json, schedule_id=None, schedule_name=None):
        &#34;&#34;&#34;
        Modifies the schedule with the given schedule json inputs for the given schedule id or name
        Args:
            schedule_dict (dict) -- {
                    pattern : {}, -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                     pattern to be sent

                                     eg: {
                                            &#34;freq_type&#34;: &#39;daily&#39;,
                                            &#34;active_start_time&#34;: time_in_%H/%S (str),
                                            &#34;repeat_days&#34;: days_to_repeat (int)
                                         }

                    options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                                    eg:  {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: True,
                                        &#34;totalJobsToProcess&#34;: 1000,
                                        &#34;allCopies&#34;: True,
                                        &#34;mediaAgent&#34;: {
                                            &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                        }
                                    }
                }
            schedule_id (int) -- id of the schedule
            schedule_name (str) -- name of the schedule

        Sample:
                Change Pattern:
                    sch_pol_obj.change_schedule({&#39;pattern&#39;:{&#39;freq_type&#39;: &#39;monthly&#39;}}, schedule_id=77)

                Change Options:
                    sch_pol_obj.change_schedule({&#39;options&#39;:{&#39;maxNumberOfStreams&#39;: 10}}, schedule_id=77)

        &#34;&#34;&#34;
        sub_task = self.get_schedule(schedule_id, schedule_name)
        if not sub_task:
            raise SDKException(&#39;Schedules&#39;, &#39;105&#39;)
        if &#39;pattern&#39; in schedule_json:
            self._update_pattern(sub_task[&#34;subTask&#34;][&#34;subTaskId&#34;], schedule_json.get(&#39;pattern&#39;))
        if &#39;options&#39; in schedule_json:
            self._update_option(sub_task[&#34;subTask&#34;][&#34;subTaskId&#34;], schedule_json.get(&#39;options&#39;))
        self._modify_schedule_policy_properties()

    def delete_schedule(self, schedule_id=None, schedule_name=None):
        &#34;&#34;&#34;
        Deletes the schedule from the schedule policy
        Args:
            schedule_id (int) -- id of the schedule
            schedule_name (str) -- name of the schedule

        Sample:
            sch_pol_obj.delete_schedule(schedule_name=&#39;testsch&#39;)

        &#34;&#34;&#34;
        sub_task = self.get_schedule(schedule_id, schedule_name)
        if not sub_task:
            raise SDKException(&#39;Schedules&#39;, &#39;105&#39;)
        sub_task[&#34;subTaskOperation&#34;] = 3
        self._subtasks[self._subtasks.index(sub_task)] = sub_task
        self._modify_schedule_policy_properties()

    def update_app_groups(self, app_groups, operation_type):
        &#34;&#34;&#34;
        Update the appgroups for the provided schedule policy
        Args:
            app_groups(List) -- Agent Types to be associated to the schedule policy

                      eg:    [
                                {
                                    &#34;appGroupName&#34;: &#34;Protected Files&#34;
                                },
                                {
                                    &#34;appGroupName&#34;: &#34;Archived Files&#34;
                                }
                            ]
            operation_type (OperationType) -- Please check OperationType class present in this file

        Sample:

            apptype = [{&#39;appGroupName&#39;: &#39;DB2&#39;}]
            sch_pol_obj.update_app_groups(apptype, OperationType.INCLUDE)

        &#34;&#34;&#34;
        for app_group in app_groups:
            app_group[&#34;flags&#34;] = {
                operation_type: True
            }
        self._app_groups = app_groups
        self._modify_schedule_policy_properties()

    def _modify_schedule_policy_properties(self):
        &#34;&#34;&#34;
         Modifies the task properties of the schedule policy
        Exception:
            if modification of the schedule policy failed

        &#34;&#34;&#34;
        request_json = {
            &#39;taskInfo&#39;:
                {
                    &#39;taskOperation&#39;: 1,
                    &#39;associations&#39;: self._associations,
                    &#39;task&#39;: self._task_json,
                    &#34;appGroup&#34;:
                        {
                            &#34;appGroups&#34;: self._app_groups if self._app_groups else [],
                        },
                    &#39;subTasks&#39;: self._subtasks
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MODIFY_SCHEDULE_POLICY, request_json
        )
        output = self._process_schedule_policy_update_response(flag, response)
        self.refresh()

        if output[0]:
            return

        o_str = &#39;Failed to update properties of Schedule Policy\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, o_str.format(output[2]))

    def enable(self):
        &#34;&#34;&#34;Enable a schedule policy.

                    Raises:
                        SDKException:
                            if failed to enable schedule policy

                            if response is empty

                            if response is not success
                &#34;&#34;&#34;
        enable_request = self._commcell_object._services[&#39;ENABLE_SCHEDULE&#39;]
        request_text = &#34;taskId={0}&#34;.format(self.schedule_policy_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, enable_request, request_text)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#39;Failed to enable Schedule Policy&#39;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = &#34;{0}\nError: {1}&#34;.format(error_message, response.json()[&#39;errorMessage&#39;])

                    raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, error_message)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def disable(self):
        &#34;&#34;&#34;Disable a Schedule Policy.

            Raises:
                SDKException:
                    if failed to disable Schedule Policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        disable_request = self._commcell_object._services[&#39;DISABLE_SCHEDULE&#39;]

        request_text = &#34;taskId={0}&#34;.format(self.schedule_policy_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, disable_request, request_text)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#39;Failed to disable Schedule Policy&#39;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = &#34;{0}\nError: {1}&#34;.format(error_message, response.json()[&#39;errorMessage&#39;])

                    raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, error_message)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_schedule_policy_update_response(self, flag, response):
        &#34;&#34;&#34;
        processes the response received post update request
        Args:
        flag: (bool) -- True or false based on response
        response: (dict) response from modify request
        Returns:
            flag: (Bool) -- based on success and failure
            error_code: (int) -- error_code from response
            error_message: (str) -- error_message from the response if any
        &#34;&#34;&#34;
        task_id = None
        if flag:
            if response.json():
                if &#34;taskId&#34; in response.json():
                    task_id = str(response.json()[&#34;taskId&#34;])

                    if task_id:
                        return True, &#34;0&#34;, &#34;&#34;

                elif &#34;errorCode&#34; in response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if error_code == &#34;0&#34;:
                        return True, &#34;0&#34;, &#34;&#34;

                    if error_message:
                        return False, error_code, error_message
                    else:
                        return False, error_code, &#34;&#34;
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Schedule Policy.&#34;&#34;&#34;
        self._get_schedule_policy_properties()</code></pre>
</details>
<h3>Static methods</h3>
<dl>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.get_option"><code class="name flex">
<span>def <span class="ident">get_option</span></span>(<span>option_dict, option)</span>
</code></dt>
<dd>
<div class="desc"><p>gets the schedule options for the provided option</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>option_dict</code></strong></dt>
<dd>the complete options dict</dd>
<dt><strong><code>option</code></strong></dt>
<dd>option for which the dict has to be fetched</dd>
</dl>
<p>Returns (dict) &ndash; Option dict for the provided option</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L767-L786" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@staticmethod
def get_option(option_dict, option):
    &#34;&#34;&#34;
    gets the schedule options for the provided option
    Args:
        option_dict: the complete options dict
        option: option for which the dict has to be fetched

    Returns (dict) -- Option dict for the provided option

    &#34;&#34;&#34;
    if isinstance(option_dict, dict) and option in option_dict:
        return option_dict[option]
    elif not isinstance(option_dict, dict):
        return None
    else:
        for value in option_dict.values():
            result = SchedulePolicy.get_option(value, option)
            if result is not None:
                return result</code></pre>
</details>
</dd>
</dl>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.all_schedules"><code class="name">var <span class="ident">all_schedules</span></code></dt>
<dd>
<div class="desc"><p>Gets all the schedules of the schedule policy</p>
<p>Sample:
sch_pol_obj.all_schedules</p>
<p>Returns (dict) &ndash; schedules in the below format</p>
<pre><code>{
        "schedule_name" : (str),
        "schedule_id": (int)
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L699-L714" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_schedules(self):
    &#34;&#34;&#34;
    Gets all the schedules of the schedule policy

    Sample:  sch_pol_obj.all_schedules

    Returns (dict) -- schedules in the below format

        {
                &#34;schedule_name&#34; : (str),
                &#34;schedule_id&#34;: (int)
        }

    &#34;&#34;&#34;
    return self._all_schedules</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.policy_type"><code class="name">var <span class="ident">policy_type</span></code></dt>
<dd>
<div class="desc"><p>Get the policy Type of the schedule policy</p>
<p>Sample:
sch_pol_obj.policy_type</p>
<p>Returns (str) &ndash; Type of the schedule policy from 'policy_types' dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L611-L625" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def policy_type(self):
    &#34;&#34;&#34;
    Get the policy Type of the schedule policy

    Sample:  sch_pol_obj.policy_type

    Returns (str) -- Type of the schedule policy from &#39;policy_types&#39; dict

    &#34;&#34;&#34;
    return (
            list(
            SchedulePolicies.policy_types.keys())[
            list(
                SchedulePolicies.policy_types.values()).index(self._task_json[&#39;policyType&#39;])])</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.add_schedule"><code class="name flex">
<span>def <span class="ident">add_schedule</span></span>(<span>self, schedule_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new schedule to the schedule policy</p>
<h2 id="args">Args</h2>
<p>schedule_dict (dict) &ndash; {
pattern : {}, &ndash; Please refer SchedulePattern.create_schedule in schedules.py for the types of
pattern to be sent</p>
<pre><code>                     eg: {
                            "freq_type": 'daily',
                            "active_start_time": time_in_%H/%S (str),
                            "repeat_days": days_to_repeat (int)
                         }

    options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                    eg:  {
                        "maxNumberOfStreams": 0,
                        "useMaximumStreams": True,
                        "useScallableResourceManagement": True,
                        "totalJobsToProcess": 1000,
                        "allCopies": True,
                        "mediaAgent": {
                            "mediaAgentName": "&lt;ANY MEDIAAGENT&gt;"
                        }
                    }
}
</code></pre>
<h2 id="sample">Sample</h2>
<p>sch_pol_obj.add_schedule({'pattern':{'freq_type': 'monthly'}})</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L854-L890" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_schedule(self, schedule_dict):
    &#34;&#34;&#34;
    Adds a new schedule to the schedule policy
    Args:
        schedule_dict (dict) -- {
                pattern : {}, -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                 pattern to be sent

                                 eg: {
                                        &#34;freq_type&#34;: &#39;daily&#39;,
                                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                                        &#34;repeat_days&#34;: days_to_repeat (int)
                                     }

                options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                                eg:  {
                                    &#34;maxNumberOfStreams&#34;: 0,
                                    &#34;useMaximumStreams&#34;: True,
                                    &#34;useScallableResourceManagement&#34;: True,
                                    &#34;totalJobsToProcess&#34;: 1000,
                                    &#34;allCopies&#34;: True,
                                    &#34;mediaAgent&#34;: {
                                        &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                    }
                                }
            }

    Sample:

            sch_pol_obj.add_schedule({&#39;pattern&#39;:{&#39;freq_type&#39;: &#39;monthly&#39;}})

    &#34;&#34;&#34;
    sub_task = SchedulePolicies.schedule_json(self.policy_type, schedule_dict)
    sub_task[&#34;subTaskOperation&#34;] = 2
    self._subtasks.append(sub_task)
    self._modify_schedule_policy_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.delete_schedule"><code class="name flex">
<span>def <span class="ident">delete_schedule</span></span>(<span>self, schedule_id=None, schedule_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the schedule from the schedule policy</p>
<h2 id="args">Args</h2>
<p>schedule_id (int) &ndash; id of the schedule
schedule_name (str) &ndash; name of the schedule</p>
<h2 id="sample">Sample</h2>
<p>sch_pol_obj.delete_schedule(schedule_name='testsch')</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L939-L955" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_schedule(self, schedule_id=None, schedule_name=None):
    &#34;&#34;&#34;
    Deletes the schedule from the schedule policy
    Args:
        schedule_id (int) -- id of the schedule
        schedule_name (str) -- name of the schedule

    Sample:
        sch_pol_obj.delete_schedule(schedule_name=&#39;testsch&#39;)

    &#34;&#34;&#34;
    sub_task = self.get_schedule(schedule_id, schedule_name)
    if not sub_task:
        raise SDKException(&#39;Schedules&#39;, &#39;105&#39;)
    sub_task[&#34;subTaskOperation&#34;] = 3
    self._subtasks[self._subtasks.index(sub_task)] = sub_task
    self._modify_schedule_policy_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.disable"><code class="name flex">
<span>def <span class="ident">disable</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disable a Schedule Policy.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable Schedule Policy</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L1057-L1094" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable(self):
    &#34;&#34;&#34;Disable a Schedule Policy.

        Raises:
            SDKException:
                if failed to disable Schedule Policy

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    disable_request = self._commcell_object._services[&#39;DISABLE_SCHEDULE&#39;]

    request_text = &#34;taskId={0}&#34;.format(self.schedule_policy_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, disable_request, request_text)

    if flag:
        if response.json():
            error_code = str(response.json()[&#39;errorCode&#39;])

            if error_code == &#34;0&#34;:
                return
            else:
                error_message = &#39;Failed to disable Schedule Policy&#39;

                if &#39;errorMessage&#39; in response.json():
                    error_message = &#34;{0}\nError: {1}&#34;.format(error_message, response.json()[&#39;errorMessage&#39;])

                raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, error_message)

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    response_string = self._commcell_object._update_response_(
        response.text)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.enable"><code class="name flex">
<span>def <span class="ident">enable</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable a schedule policy.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable schedule policy</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L1019-L1055" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable(self):
    &#34;&#34;&#34;Enable a schedule policy.

                Raises:
                    SDKException:
                        if failed to enable schedule policy

                        if response is empty

                        if response is not success
            &#34;&#34;&#34;
    enable_request = self._commcell_object._services[&#39;ENABLE_SCHEDULE&#39;]
    request_text = &#34;taskId={0}&#34;.format(self.schedule_policy_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, enable_request, request_text)

    if flag:
        if response.json():
            error_code = str(response.json()[&#39;errorCode&#39;])

            if error_code == &#34;0&#34;:
                return
            else:
                error_message = &#39;Failed to enable Schedule Policy&#39;

                if &#39;errorMessage&#39; in response.json():
                    error_message = &#34;{0}\nError: {1}&#34;.format(error_message, response.json()[&#39;errorMessage&#39;])

                raise SDKException(&#39;Schedules&#39;, &#39;102&#39;, error_message)

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    response_string = self._commcell_object._update_response_(
        response.text)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.get_schedule"><code class="name flex">
<span>def <span class="ident">get_schedule</span></span>(<span>self, schedule_id=None, schedule_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the subtask dict for the provided schedule id or name</p>
<h2 id="args">Args</h2>
<p>schedule_id (int) &ndash; id of the schedule
schedule_name (str) &ndash; name of the schedule
Sample:
sch_pol_obj.get_schedule(schedule_id=10)</p>
<p>Returns (dict) &ndash; subtask dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L821-L852" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_schedule(self, schedule_id=None, schedule_name=None):
    &#34;&#34;&#34;
    returns the subtask dict for the provided schedule id or name
    Args:
        schedule_id (int) -- id of the schedule
        schedule_name (str) -- name of the schedule

    Sample:  sch_pol_obj.get_schedule(schedule_id=10)

    Returns (dict) -- subtask dict

    &#34;&#34;&#34;
    if not schedule_name and not schedule_id:
        raise SDKException(
            &#39;Schedules&#39;,
            &#39;102&#39;,
            &#39;Either Schedule Name or Schedule Id is needed&#39;)

    if schedule_name and not isinstance(schedule_name, str):
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

    if schedule_id and not isinstance(schedule_id, int):
        raise SDKException(&#39;Schedules&#39;, &#39;102&#39;)

    if schedule_name:
        search_dict = (&#34;subTaskName&#34;, schedule_name)
    else:
        search_dict = (&#34;subTaskId&#34;, schedule_id)

    for sub_task in self._subtasks:
        if search_dict in sub_task[&#34;subTask&#34;].items():
            return sub_task</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.modify_schedule"><code class="name flex">
<span>def <span class="ident">modify_schedule</span></span>(<span>self, schedule_json, schedule_id=None, schedule_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies the schedule with the given schedule json inputs for the given schedule id or name</p>
<h2 id="args">Args</h2>
<p>schedule_dict (dict) &ndash; {
pattern : {}, &ndash; Please refer SchedulePattern.create_schedule in schedules.py for the types of
pattern to be sent</p>
<pre><code>                     eg: {
                            "freq_type": 'daily',
                            "active_start_time": time_in_%H/%S (str),
                            "repeat_days": days_to_repeat (int)
                         }

    options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                    eg:  {
                        "maxNumberOfStreams": 0,
                        "useMaximumStreams": True,
                        "useScallableResourceManagement": True,
                        "totalJobsToProcess": 1000,
                        "allCopies": True,
                        "mediaAgent": {
                            "mediaAgentName": "&lt;ANY MEDIAAGENT&gt;"
                        }
                    }
}
</code></pre>
<p>schedule_id (int) &ndash; id of the schedule
schedule_name (str) &ndash; name of the schedule</p>
<h2 id="sample">Sample</h2>
<p>Change Pattern:
sch_pol_obj.change_schedule({'pattern':{'freq_type': 'monthly'}}, schedule_id=77)</p>
<p>Change Options:
sch_pol_obj.change_schedule({'options':{'maxNumberOfStreams': 10}}, schedule_id=77)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L892-L937" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_schedule(self, schedule_json, schedule_id=None, schedule_name=None):
    &#34;&#34;&#34;
    Modifies the schedule with the given schedule json inputs for the given schedule id or name
    Args:
        schedule_dict (dict) -- {
                pattern : {}, -- Please refer SchedulePattern.create_schedule in schedules.py for the types of
                                 pattern to be sent

                                 eg: {
                                        &#34;freq_type&#34;: &#39;daily&#39;,
                                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                                        &#34;repeat_days&#34;: days_to_repeat (int)
                                     }

                options: {} -- Please refer ScheduleOptions.py classes for respective schedule options

                                eg:  {
                                    &#34;maxNumberOfStreams&#34;: 0,
                                    &#34;useMaximumStreams&#34;: True,
                                    &#34;useScallableResourceManagement&#34;: True,
                                    &#34;totalJobsToProcess&#34;: 1000,
                                    &#34;allCopies&#34;: True,
                                    &#34;mediaAgent&#34;: {
                                        &#34;mediaAgentName&#34;: &#34;&lt;ANY MEDIAAGENT&gt;&#34;
                                    }
                                }
            }
        schedule_id (int) -- id of the schedule
        schedule_name (str) -- name of the schedule

    Sample:
            Change Pattern:
                sch_pol_obj.change_schedule({&#39;pattern&#39;:{&#39;freq_type&#39;: &#39;monthly&#39;}}, schedule_id=77)

            Change Options:
                sch_pol_obj.change_schedule({&#39;options&#39;:{&#39;maxNumberOfStreams&#39;: 10}}, schedule_id=77)

    &#34;&#34;&#34;
    sub_task = self.get_schedule(schedule_id, schedule_name)
    if not sub_task:
        raise SDKException(&#39;Schedules&#39;, &#39;105&#39;)
    if &#39;pattern&#39; in schedule_json:
        self._update_pattern(sub_task[&#34;subTask&#34;][&#34;subTaskId&#34;], schedule_json.get(&#39;pattern&#39;))
    if &#39;options&#39; in schedule_json:
        self._update_option(sub_task[&#34;subTask&#34;][&#34;subTaskId&#34;], schedule_json.get(&#39;options&#39;))
    self._modify_schedule_policy_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the Schedule Policy.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L1136-L1138" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the Schedule Policy.&#34;&#34;&#34;
    self._get_schedule_policy_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.update_app_groups"><code class="name flex">
<span>def <span class="ident">update_app_groups</span></span>(<span>self, app_groups, operation_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Update the appgroups for the provided schedule policy</p>
<h2 id="args">Args</h2>
<p>app_groups(List) &ndash; Agent Types to be associated to the schedule policy</p>
<pre><code>      eg:    [
                {
                    "appGroupName": "Protected Files"
                },
                {
                    "appGroupName": "Archived Files"
                }
            ]
</code></pre>
<p>operation_type (OperationType) &ndash; Please check OperationType class present in this file</p>
<h2 id="sample">Sample</h2>
<p>apptype = [{'appGroupName': 'DB2'}]
sch_pol_obj.update_app_groups(apptype, OperationType.INCLUDE)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L957-L984" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_app_groups(self, app_groups, operation_type):
    &#34;&#34;&#34;
    Update the appgroups for the provided schedule policy
    Args:
        app_groups(List) -- Agent Types to be associated to the schedule policy

                  eg:    [
                            {
                                &#34;appGroupName&#34;: &#34;Protected Files&#34;
                            },
                            {
                                &#34;appGroupName&#34;: &#34;Archived Files&#34;
                            }
                        ]
        operation_type (OperationType) -- Please check OperationType class present in this file

    Sample:

        apptype = [{&#39;appGroupName&#39;: &#39;DB2&#39;}]
        sch_pol_obj.update_app_groups(apptype, OperationType.INCLUDE)

    &#34;&#34;&#34;
    for app_group in app_groups:
        app_group[&#34;flags&#34;] = {
            operation_type: True
        }
    self._app_groups = app_groups
    self._modify_schedule_policy_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.schedule_policies.SchedulePolicy.update_associations"><code class="name flex">
<span>def <span class="ident">update_associations</span></span>(<span>self, associations, operation_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the schedule policy associations</p>
<h2 id="args">Args</h2>
<p>associations (str) &ndash; List of schedule associations
[
{
"clientName": "scheduleclient1"
},
{
"clientGroupName": "scheduleclient2"
}
]
operation_type (OperationType) &ndash; Please check OperationType class present in this file</p>
<h2 id="sample">Sample</h2>
<p>associations = [{'clientName': 'client2'}]
sch_pol_obj.update_associations(associations, OperationType.DELETE)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/schedule_policies.py#L671-L697" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_associations(self, associations, operation_type):
    &#34;&#34;&#34;
    Updates the schedule policy associations
    Args:
        associations (str) -- List of schedule associations
        [
        {
            &#34;clientName&#34;: &#34;scheduleclient1&#34;
        },
        {
            &#34;clientGroupName&#34;: &#34;scheduleclient2&#34;
        }
        ]
        operation_type (OperationType) -- Please check OperationType class present in this file

    Sample:
        associations = [{&#39;clientName&#39;: &#39;client2&#39;}]
        sch_pol_obj.update_associations(associations, OperationType.DELETE)

    &#34;&#34;&#34;

    for app_group in associations:
        app_group[&#34;flags&#34;] = {
                                operation_type: True
                             }
    self._associations = associations
    self._modify_schedule_policy_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.policies" href="index.html">cvpysdk.policies</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.policies.schedule_policies.OperationType" href="#cvpysdk.policies.schedule_policies.OperationType">OperationType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.policies.schedule_policies.OperationType.DELETE" href="#cvpysdk.policies.schedule_policies.OperationType.DELETE">DELETE</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.OperationType.EXCLUDE" href="#cvpysdk.policies.schedule_policies.OperationType.EXCLUDE">EXCLUDE</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.OperationType.INCLUDE" href="#cvpysdk.policies.schedule_policies.OperationType.INCLUDE">INCLUDE</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies" href="#cvpysdk.policies.schedule_policies.SchedulePolicies">SchedulePolicies</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.add" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.add">add</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.all_schedule_policies" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.all_schedule_policies">all_schedule_policies</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.delete" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.delete">delete</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.get" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.get">get</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.has_policy" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.has_policy">has_policy</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.policy_to_subtask_map" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.policy_to_subtask_map">policy_to_subtask_map</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.policy_types" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.policy_types">policy_types</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.refresh" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.schedule_json" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.schedule_json">schedule_json</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicies.subtasks_json" href="#cvpysdk.policies.schedule_policies.SchedulePolicies.subtasks_json">subtasks_json</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy" href="#cvpysdk.policies.schedule_policies.SchedulePolicy">SchedulePolicy</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.add_schedule" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.add_schedule">add_schedule</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.all_schedules" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.all_schedules">all_schedules</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.delete_schedule" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.delete_schedule">delete_schedule</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.disable" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.disable">disable</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.enable" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.enable">enable</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.get_option" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.get_option">get_option</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.get_schedule" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.get_schedule">get_schedule</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.modify_schedule" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.modify_schedule">modify_schedule</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.policy_type" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.policy_type">policy_type</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.refresh" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.update_app_groups" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.update_app_groups">update_app_groups</a></code></li>
<li><code><a title="cvpysdk.policies.schedule_policies.SchedulePolicy.update_associations" href="#cvpysdk.policies.schedule_policies.SchedulePolicy.update_associations">update_associations</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>