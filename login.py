import requests
import urllib3

# Suppress warnings for self-signed certificates in a lab environment
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Commvault API endpoint
url = "http://192.168.100.55:81/SearchSvc/CVWebService.svc/Login"

# The XML payload as a Python string
# The password needs to be Base64 encoded as specified in the HTTP request.
xml_payload = """<DM2ContentIndexing_CheckCredentialReq mode="Webconsole" username="admin" password="UEBzc3cwcmQ=" />"""

# The headers for the request
headers = {
    "Accept": "application/json",
    "Content-type": "application/xml"
}

# Make the POST request with the XML data
try:
    response = requests.post(url, headers=headers, data=xml_payload, verify=False)
    response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)

    # Print the response from the server, which is expected to be JSON
    print("Status Code:", response.status_code)
    print("Response Body:", response.json())

    # You can then extract the token from the response
    auth_token = response.json().get('token')
    if auth_token:
        print("\nLogin successful! Authentication token:")
        print(auth_token)
    else:
        print("\nLogin failed: No token received in the response.")

except requests.exceptions.RequestException as e:
    print(f"An error occurred: {e}")