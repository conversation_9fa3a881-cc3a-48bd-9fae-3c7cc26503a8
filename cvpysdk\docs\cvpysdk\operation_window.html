<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.operation_window API documentation</title>
<meta name="description" content="File for performing Operation Window related operations on given Commcell entity …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.operation_window</code></h1>
</header>
<section id="section-intro">
<p>File for performing Operation Window related operations on given Commcell entity.</p>
<p>OperationWindow and OperationWindowDetails are 2 classes defined in this class.</p>
<p>OperationWindow: Class for performing Operation Window related operations on given Commcell entity.</p>
<p>OperationWindowDetails: Class for modifying an existing operation window</p>
<h1 id="operationwindow">OperationWindow:</h1>
<pre><code>__init__()                          --  Initialize instance of the OperationWindow class

create_operation_window()           --  Creates a Operation rule on the given commcell entity

delete_operation_window()           --  Deletes a Operation rule on the commcell entity(Using rule_id/name)

list_operation_window()             --  Lists all the operation rule associated with given commcell entity

get()                               --  Returns instance of OperationWindowDetails class(Using rule_id/name)
</code></pre>
<h1 id="operationwindowdetails">OperationWindowDetails:</h1>
<pre><code>__init__()                          --  Initialize instance of OperationWindowDetails class

modify_operation_window()           --  Modifies a Operation window

_refresh()                          --  Refreshes the properties of a rule


_get_rule_properties()              -- Assigns the properties of an operation by getting the rule using rule id
</code></pre>
<h1 id="operationwindowdetails-instance-attributes">OperationWindowDetails Instance Attributes:</h1>
<pre><code>**name**                            --  Returns/Modifies the name of the operation window

**start_date**                      --  Returns/Modifies the start date of the operation window

**end_date**                        --  Returns/Modifies the end date of the operation window

**operations**                      --  Returns/Modifies the operations of the operation window

**day_of_week**                     --  Returns/Modifies the day of week of the operation window

**start_time**                      --  Returns/Modifies the start time of the operation window

**end_time**                        --  Returns/Modifies the end time of the operation window

**rule_id**                         --  Returns rule id of the operation window

**commcell_id**                     --  Returns commcell id of the entity object

**clientgroup_id**                  --  Returns client group id of the entity object

**client_id**                       --  Returns client id of the entity object

**agent_id**                        --  Returns agent id of the entity object

**instance_id**                     --  Returns instance id of the entity object

**backupset_id**                    --  Returns backupset id of the entity object

**subclient_id**                    --  Returns subclient id of the entity object

**entity_level**                    --  Returns entity level of the entity object
</code></pre>
<p>Example with client entity:
from cvpysdk.commcell import Commcell
commcell = Commcell(<CS>, username, password)
client = commcell.clients.get(<client Name>)
from cvpysdk.operation_window import OperationWindow
client_operation_window = OperationWindow(client)
client_operation_window.list_operation_window()
client_operation_window_details = client_operation_window.create_operation_window(name="operation
window example on clientLevel")
client_operation_window.delete_operation_window(rule_id=client_operation_window_details.rule_id)
client_operation_window_details = client_operation_window.get(rule_id=client_operation_window_details.rule_id)
client_operation_window_details.modify_operation_window(name="Modified operation window example on clientLevel")</p>
<p>Example for modifying a rule:
client_operation_window = OperationWindow(client)
rules = client_operation_window.list_operation_window()
ruleId = rules[0]['ruleId']
client_operation_window_details = OperationWindowDetails(client, ruleId, client_operation_window.entity_details)
# You can use get(OperationWindow) method to modify a rule too.
client_operation_window_details.modify_operation_window(name="Modified operation window example on clientLevel")</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1-L1055" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for performing Operation Window related operations on given Commcell entity.

OperationWindow and OperationWindowDetails are 2 classes defined in this class.

OperationWindow: Class for performing Operation Window related operations on given Commcell entity.

OperationWindowDetails: Class for modifying an existing operation window


OperationWindow:
===============

    __init__()                          --  Initialize instance of the OperationWindow class

    create_operation_window()           --  Creates a Operation rule on the given commcell entity

    delete_operation_window()           --  Deletes a Operation rule on the commcell entity(Using rule_id/name)

    list_operation_window()             --  Lists all the operation rule associated with given commcell entity

    get()                               --  Returns instance of OperationWindowDetails class(Using rule_id/name)

OperationWindowDetails:
======================

    __init__()                          --  Initialize instance of OperationWindowDetails class

    modify_operation_window()           --  Modifies a Operation window

    _refresh()                          --  Refreshes the properties of a rule


    _get_rule_properties()              -- Assigns the properties of an operation by getting the rule using rule id


OperationWindowDetails Instance Attributes:
==========================================
    **name**                            --  Returns/Modifies the name of the operation window

    **start_date**                      --  Returns/Modifies the start date of the operation window

    **end_date**                        --  Returns/Modifies the end date of the operation window

    **operations**                      --  Returns/Modifies the operations of the operation window

    **day_of_week**                     --  Returns/Modifies the day of week of the operation window

    **start_time**                      --  Returns/Modifies the start time of the operation window

    **end_time**                        --  Returns/Modifies the end time of the operation window

    **rule_id**                         --  Returns rule id of the operation window

    **commcell_id**                     --  Returns commcell id of the entity object

    **clientgroup_id**                  --  Returns client group id of the entity object

    **client_id**                       --  Returns client id of the entity object

    **agent_id**                        --  Returns agent id of the entity object

    **instance_id**                     --  Returns instance id of the entity object

    **backupset_id**                    --  Returns backupset id of the entity object

    **subclient_id**                    --  Returns subclient id of the entity object

    **entity_level**                    --  Returns entity level of the entity object

Example with client entity:
        from cvpysdk.commcell import Commcell
        commcell = Commcell(&lt;CS&gt;, username, password)
        client = commcell.clients.get(&lt;client Name&gt;)
        from cvpysdk.operation_window import OperationWindow
        client_operation_window = OperationWindow(client)
        client_operation_window.list_operation_window()
        client_operation_window_details = client_operation_window.create_operation_window(name=&#34;operation
                                                                                        window example on clientLevel&#34;)
        client_operation_window.delete_operation_window(rule_id=client_operation_window_details.rule_id)
        client_operation_window_details = client_operation_window.get(rule_id=client_operation_window_details.rule_id)
        client_operation_window_details.modify_operation_window(name=&#34;Modified operation window example on clientLevel&#34;)

Example for modifying a rule:
        client_operation_window = OperationWindow(client)
        rules = client_operation_window.list_operation_window()
        ruleId = rules[0][&#39;ruleId&#39;]
        client_operation_window_details = OperationWindowDetails(client, ruleId, client_operation_window.entity_details)
        # You can use get(OperationWindow) method to modify a rule too.
        client_operation_window_details.modify_operation_window(name=&#34;Modified operation window example on clientLevel&#34;)
&#34;&#34;&#34;

from __future__ import absolute_import
import time
import datetime
import calendar
from datetime import timedelta
from .exception import SDKException
from .clientgroup import ClientGroup
from .client import Client
from .agent import Agent
from .instance import Instance
from .backupset import Backupset
from .subclient import Subclient

DAY_OF_WEEK_MAPPING = [&#39;sunday&#39;, &#39;monday&#39;, &#39;tuesday&#39;, &#39;wednesday&#39;, &#39;thursday&#39;, &#39;friday&#39;, &#39;saturday&#39;]
WEEK_OF_THE_MONTH_MAPPING = {&#34;all&#34;: 32,
                             &#34;first&#34;: 1,
                             &#34;second&#34;: 2,
                             &#34;third&#34;: 4,
                             &#34;fourth&#34;: 8,
                             &#34;last&#34;: 16}
OPERATION_MAPPING = {&#34;FULL_DATA_MANAGEMENT&#34;: 1,
                     &#34;NON_FULL_DATA_MANAGEMENT&#34;: 2,
                     &#34;SYNTHETIC_FULL&#34;: 4,
                     &#34;DATA_RECOVERY&#34;: 8,
                     &#34;AUX_COPY&#34;: 16,
                     &#34;DR_BACKUP&#34;: 32,
                     &#34;DATA_VERIFICATION&#34;: 64,
                     &#34;ERASE_SPARE_MEDIA&#34;: 128,
                     &#34;SHELF_MANAGEMENT&#34;: 256,
                     &#34;DELETE_DATA_BY_BROWSING&#34;: 512,
                     &#34;DELETE_ARCHIVED_DATA&#34;: 1024,
                     &#34;OFFLINE_CONTENT_INDEXING&#34;: 2048,
                     &#34;ONLINE_CONTENT_INDEXING&#34;: 4096,
                     &#34;SRM&#34;: 8192,
                     &#34;INFORMATION_MANAGEMENT&#34;: 16384,
                     &#34;MEDIA_REFRESHING&#34;: 32768,
                     &#34;DATA_ANALYTICS&#34;: 65536,
                     &#34;DATA_PRUNING&#34;: 131072,
                     &#34;BACKUP_COPY&#34;: 262144,
                     &#34;UPDATE_SOFTWARE&#34;: 2097152,
                     &#34;CLEANUP_OPERATION&#34;: 524288,
                     &#34;ALL&#34;: 1048576}


class OperationWindow:
    &#34;&#34;&#34;Class for representing all operation window related operations&#34;&#34;&#34;

    def __init__(self, generic_entity_obj):
        &#34;&#34;&#34;Initialize the OperationWindow class instance for
           performing Operation Window related operations.

            Args:
                generic_entity_obj     (object)    --  Commcell entity object
                    Expected value : commcell/Client/Agent/Instance/BackupSet/Subclient/Clientgroup Instance

            Returns:
                object  -   instance of the OperationWindow class

            Raises:
                Exception:
                    If invalid instance is passed
        &#34;&#34;&#34;
        # imports inside the __init__ method definition to avoid cyclic imports
        from .commcell import Commcell

        if isinstance(generic_entity_obj, Commcell):
            self._commcell_object = generic_entity_obj
        else:
            self._commcell_object = generic_entity_obj._commcell_object

        self._commcell_services = self._commcell_object._services
        self._operation_window = self._commcell_services[&#39;OPERATION_WINDOW&#39;]
        self._list_operation_window = self._commcell_services[&#39;LIST_OPERATION_WINDOW&#39;]
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._update_response = self._commcell_object._update_response_

        self.clientgroup_id = 0
        self.client_id = 0
        self.agent_id = 0
        self.instance_id = 0
        self.backupset_id = 0
        self.subclient_id = 0
        self.entity_type = &#39;&#39;
        self.entity_id = &#39;&#39;
        self.entity_details = dict()

        self.generic_entity_obj = generic_entity_obj

        # we will derive all the entity id&#39;s based on the input entity type
        if isinstance(generic_entity_obj, Commcell):
            self.entity_details[&#34;entity_level&#34;] = &#34;commserv&#34;
        elif isinstance(generic_entity_obj, ClientGroup):
            self.clientgroup_id = generic_entity_obj.clientgroup_id
            self.entity_type = &#34;clientgroupId&#34;
            self.entity_id = self.clientgroup_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        elif isinstance(generic_entity_obj, Client):
            self.client_id = generic_entity_obj.client_id
            self.entity_type = &#34;clientId&#34;
            self.entity_id = self.client_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        elif isinstance(generic_entity_obj, Agent):
            self.client_id = generic_entity_obj._client_object.client_id
            self.agent_id = generic_entity_obj.agent_id
            self.entity_type = &#34;applicationId&#34;
            self.entity_id = self.agent_id
            self.entity_details[&#34;entity_level&#34;] = &#34;agent&#34;
        elif isinstance(generic_entity_obj, Instance):
            self.client_id = generic_entity_obj._agent_object._client_object.client_id
            self.agent_id = generic_entity_obj._agent_object.agent_id
            self.instance_id = generic_entity_obj.instance_id
            self.entity_type = &#34;instanceId&#34;
            self.entity_id = self.instance_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        elif isinstance(generic_entity_obj, Backupset):
            self.client_id = generic_entity_obj._instance_object._agent_object. \
                _client_object.client_id
            self.agent_id = generic_entity_obj._instance_object._agent_object.agent_id
            self.instance_id = generic_entity_obj._instance_object.instance_id
            self.backupset_id = generic_entity_obj.backupset_id
            self.entity_type = &#34;backupsetId&#34;
            self.entity_id = self.backupset_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        elif isinstance(generic_entity_obj, Subclient):
            self.client_id = generic_entity_obj._backupset_object._instance_object. \
                _agent_object._client_object.client_id
            self.agent_id = generic_entity_obj._backupset_object. \
                _instance_object._agent_object.agent_id
            self.instance_id = generic_entity_obj._backupset_object._instance_object.instance_id
            self.backupset_id = generic_entity_obj._backupset_object.backupset_id
            self.subclient_id = generic_entity_obj.subclient_id
            self.entity_type = &#34;subclientId&#34;
            self.entity_id = self.subclient_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#34;Invalid instance passed&#34;)

        self.entity_details.update({&#34;clientGroupId&#34;: self.clientgroup_id,
                                    &#34;clientId&#34;: self.client_id,
                                    &#34;applicationId&#34;: self.agent_id,
                                    &#34;instanceId&#34;: self.instance_id,
                                    &#34;backupsetId&#34;: self.backupset_id,
                                    &#34;subclientId&#34;: self.subclient_id})

        # append the entity type and entity id to end of list operation window REST API.
        # For commcell it will empty string
        self.connect_string = self._list_operation_window.split(&#39;?&#39;)[0] + &#39;?&#39; + self.entity_type + &#34;=&#34; + self.entity_id

    def create_operation_window(
            self,
            name,
            start_date=None,
            end_date=None,
            operations=None,
            day_of_week=None,
            start_time=None,
            end_time=None,
            week_of_the_month=None,
            do_not_submit_job=False):
        &#34;&#34;&#34; Creates operation rule on the initialized commcell entity

            Args:
                name          (str)   --  Name of the Operation rule

                start_date    (int)   -- The start date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - current date

                end_date      (int)   -- The end date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 365 days

                operations (list)         --   List of operations for which the operation
                                               window is created

                    Acceptable Values:
                        FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
                        DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
                        SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
                        OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
                        MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION

                week_of_the_month(list)     -- List of week of the month on which the operation rule applies to
                        Acceptable Values:
                            all/first/second/third/fourth/last

                            default - None

                day_of_week (list)    -- List of days of the week on which the operation rule applies to
                    Acceptable Values:
                        sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday

                    default- Weekdays

                start_time  (int)     -- The start time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 28800 (8 AM)
                    Must specify one timestamp for start time for all the weekdays, otherwise
                    make a list for each weekday mentioned in the day_of_week list.

                start_time (list)    -- The list of start timestamps for each weekday mentioned
                    in the day_of_week list.

                end_time    (int)     -- The end time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 86400 (6 PM)
                    Must specify one timestamp for end time for all the weekdays, otherwise
                    make a list for each weekday mentioned in the day_of_week list.

                end_time   (list)    -- The list of end timestamps for each weekday mentioned
                    in the day_of_week list.

                Example:
                    1. day_of_week : [&#34;sunday&#34;, &#34;thursday&#34;, &#34;saturday&#34;]
                       start_time  : 28800
                       end_time    : 86400
                       The above inputs specify that for all the three days mentioned, start_time and end_time of
                       operation window would be same
                    2. day_of_week : [&#34;monday&#34;,&#34;friday&#34;]
                       start_time  : [3600, 28800]
                       end_time    : [18000, 86400]
                       The above input specify that on monday operation window starts at 3600 and ends at 18000 whereas
                       on friday, the operation window starts at 28800 and ends at 86400

                do_not_submit_job   (bool) -- doNotSubmitJob of the operation rule

            Returns:
                Returns the instance of created Operation window details

            Raises:
                SDKException:
                    if the Operation window could not be created

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if start_date is None:
            start_date = int(calendar.timegm(datetime.date.today().timetuple()))
        if end_date is None:
            end_date = start_date
        if start_time is None:
            start_time = int(timedelta(hours=8).total_seconds())
        if end_time is None:
            end_time = int(timedelta(hours=18).total_seconds())

        operations_list = []
        if operations is None:
            operations_list = [OPERATION_MAPPING[&#34;FULL_DATA_MANAGEMENT&#34;]]
        else:
            for operation in operations:
                if operation not in OPERATION_MAPPING:
                    response_string = &#34;Invalid input %s for operation is passed&#34; % operation
                    raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
                operations_list.append(OPERATION_MAPPING[operation.upper()])

        day_of_week_list = []
        if day_of_week is None:
            day_of_week_list = [1, 2, 3, 4, 5]      # defaults to weekdays
        else:
            for day in day_of_week:
                if day.lower() not in DAY_OF_WEEK_MAPPING:
                    response_string = &#34;Invalid input value %s for day_of_week&#34; % day
                    raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
                day_of_week_list.append(DAY_OF_WEEK_MAPPING.index(day.lower()))

        week_of_the_month_list = []
        if week_of_the_month:
            for week in week_of_the_month:
                if week.lower() not in WEEK_OF_THE_MONTH_MAPPING:
                    response_string = &#34;Invalid input %s for week_of_the_month&#34; % week
                    raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
                week_of_the_month_list.append(WEEK_OF_THE_MONTH_MAPPING[week.lower()])

        daytime_list = []
        num_of_days = len(day_of_week_list)
        if isinstance(start_time, int) and isinstance(end_time, int):
            daytime_list.append(
                {
                    &#34;startTime&#34;: start_time,
                    &#34;endTime&#34;: end_time,
                    &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                    &#34;dayOfWeek&#34;: day_of_week_list
                }
            )
        elif isinstance(start_time, list) and isinstance(end_time, list):
            if not(num_of_days == len(start_time) == len(end_time)):
                response_string = &#34;did not specify start time and end time for all the given week days&#34;
                raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
            for week_day in range(num_of_days):
                daytime_list.append(
                    {
                        &#34;startTime&#34;: start_time[week_day],
                        &#34;endTime&#34;: end_time[week_day],
                        &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                        &#34;dayOfWeek&#34;: [day_of_week_list[week_day]]
                    }
                )
        else:
            response_string = &#34;Both start_time and end_time should be of same type.&#34;
            raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)

        payload = {
            &#34;operationWindow&#34;: {
                &#34;ruleEnabled&#34;: True,
                &#34;doNotSubmitJob&#34;: do_not_submit_job,
                &#34;startDate&#34;: start_date,
                &#34;endDate&#34;: end_date,
                &#34;name&#34;: name,
                &#34;operations&#34;: operations_list,
                &#34;dayTime&#34;: daytime_list
            },
            &#34;entity&#34;: {
                &#34;clientGroupId&#34;: int(self.clientgroup_id),
                &#34;clientId&#34;: int(self.client_id),
                &#34;applicationId&#34;: int(self.agent_id),
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;backupsetId&#34;: int(self.backupset_id),
                &#34;subclientId&#34;: int(self.subclient_id)
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._operation_window, payload=payload)
        if flag:
            if response.json():
                error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
                if int(error_code) == 0:
                    return self.get(rule_id=int(response.json().get(&#39;operationWindow&#39;, {}).get(&#39;ruleId&#39;)))
                raise SDKException(&#39;OperationWindow&#39;, &#39;101&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._update_response(response.text)
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)

    def delete_operation_window(self, rule_id=None, name=None):
        &#34;&#34;&#34;Deletes the operation rule associated with given rule Id/Name.

            Args:
                rule_id       (int)   --  Rule Id of the operation window

                name           (str)   --  Name of the operation window

            Raises:
                SDKException:
                    if the Operation window could not be deleted

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not name and not rule_id:
            raise SDKException(
                &#39;OperationWindow&#39;,
                &#39;102&#39;,
                &#39;Either Name or Rule Id is needed&#39;)

        if name and not isinstance(name, str) or rule_id and not isinstance(rule_id, int):
            raise SDKException(&#39;OperationWindow&#39;, &#39;106&#39;)

        if name:
            rule_id = self.get(name=name).rule_id

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._operation_window + &#39;/&#39; + str(rule_id))
        if flag:
            if response.json():
                error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
                if int(error_code):
                    raise SDKException(&#39;OperationWindow&#39;, &#39;103&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)

    def list_operation_window(self):
        &#34;&#34;&#34;Lists the operation rules for the associated commcell entity.

            Returns:
                Returns the List of operation rules (dictionary) associated with given commcell entity

                Example --

                    [{&#39;ruleEnabled&#39;: True,
                      &#39;doNotSubmitJob&#39;: False,
                      &#39;endDate&#39;: 0,
                      &#39;level&#39;: 0,
                      &#39;name&#39;: &#39;Rule1&#39;,
                      &#39;ruleId&#39;: 1,
                      &#39;startDate&#39;: 0,
                      &#39;operations&#39;: [&#39;FULL_DATA_MANAGEMENT&#39;, &#39;NON_FULL_DATA_MANAGEMENT&#39;],
                      &#39;company&#39;: {&#39;_type_&#39;: 61,
                                  &#39;providerId&#39;: 0,
                                  &#39;providerDomainName&#39;: &#39;&#39;},
                      &#39;dayTime&#39;: [{&#39;startTime&#39;: 28800,
                                   &#39;endTime&#39;: 64800,
                                   &#39;weekOfTheMonth&#39;: [&#39;first&#39;,&#39;third&#39;],
                                   &#39;dayOfWeek&#39;: [&#39;sunday&#39;,&#39;monday&#39;]}]}
                    ]

            Raises:
                SDKException:
                    if the Operation rules could not be Listed

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self.connect_string)
        if flag:
            if response.json():
                error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
                if int(error_code) == 0:
                    list_of_rules = response.json().get(&#34;operationWindow&#34;)
                    operation_reverse_mapping = {value: key for key, value in OPERATION_MAPPING.items()}
                    wotm_reverse_mapping = {value: key for key, value in WEEK_OF_THE_MONTH_MAPPING.items()}
                    if list_of_rules is not None:
                        for operation_rule in list_of_rules:
                            operations = operation_rule.get(&#34;operations&#34;)
                            if operations is not None:
                                operation_rule[&#34;operations&#34;] = [operation_reverse_mapping[operation] for operation in
                                                                operations]
                            day_time_list = operation_rule.get(&#34;dayTime&#34;, [])
                            for day_time in day_time_list:
                                if day_time.get(&#34;weekOfTheMonth&#34;): # if we have weekOfTheMonth, we replace it with name.
                                    day_time[&#39;weekOfTheMonth&#39;] = [wotm_reverse_mapping[week] for week in day_time.get(&#34;weekOfTheMonth&#34;)]

                                if day_time.get(&#34;dayTime&#34;): # if we have dayTime, we replace it with name.
                                    day_time[&#39;dayTime&#39;] = [DAY_OF_WEEK_MAPPING[day] for day in day_time[&#39;dayTime&#39;]]
                            operation_rule[&#39;dayTime&#39;] = day_time_list
                    return list_of_rules
                raise SDKException(&#39;OperationWindow&#39;, &#39;104&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._update_response(response.text)
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)

    def get(self, rule_id=None, name=None):
        &#34;&#34;&#34;Returns the operation rule object for a given rule

         Args:
            rule_id               (int)   --  Rule Id of an operation Window

            name                  (str)   --  Name of the operation window

         Returns:
                object - instance of the OperationWindowDetails class
                            for the given operation window name/rule
         Raises:
                SDKException:
                    if type of the operation window name argument is not string

                    if no operation window exists with such name
        &#34;&#34;&#34;
        if not name and not rule_id:
            raise SDKException(
                &#39;OperationWindow&#39;,
                &#39;102&#39;,
                &#39;Either Name or Rule Id is needed&#39;)

        if name and not isinstance(name, str) or rule_id and not isinstance(rule_id, int):
            raise SDKException(&#39;OperationWindow&#39;, &#39;106&#39;)

        list_of_rules = self.list_operation_window()
        if rule_id:
            for operation_rule in list_of_rules:
                if operation_rule.get(&#34;ruleId&#34;) == rule_id:
                    return OperationWindowDetails(self.generic_entity_obj, rule_id, self.entity_details)
            raise Exception(&#34;No such operation window with rule id as {0} exists&#34;.format(rule_id))
        if name:
            rules = [operation_rule.get(&#34;ruleId&#34;) for operation_rule in list_of_rules
                     if operation_rule.get(&#34;name&#34;) == name]
            if not rules:
                raise Exception(&#34;No such operation window with name as {0} exists&#34;.format(name))
            if len(rules) == 1:
                return OperationWindowDetails(self.generic_entity_obj, rules[0], self.entity_details)
            raise Exception(&#34;More than one operation window are named as {0} exists&#34;.format(name))


class OperationWindowDetails:
    &#34;&#34;&#34;Helper class for modifying operation window&#34;&#34;&#34;

    def __init__(self, generic_entity_obj, rule_id, entity_details):
        &#34;&#34;&#34;
        Initialize the OperationWindowDetails class instance for
           modifying OperationWindow.

            Args:
                generic_entity_obj     (object)    --  Commcell entity object
                    Expected value : commcell/Client/Agent/Instance/BackupSet/Subclient/Clientgroup Entity

                rule_id (int)   -- Rule id of the operation window to be modified

                entity_details -- Details related to the entity

            Usually gets initialized from OperationWindow class

            Returns:
                object  -   instance of the OperationWindowDetails class
        &#34;&#34;&#34;
        from .commcell import Commcell
        if isinstance(generic_entity_obj, Commcell):
            self._commcell_object = generic_entity_obj
        else:
            self._commcell_object = generic_entity_obj._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._update_response = self._commcell_object._update_response_
        self._commcell_services = self._commcell_object._services
        self._operation_window = self._commcell_services[&#39;OPERATION_WINDOW&#39;]

        self._rule_id = rule_id
        self._name = None
        self._start_date = None
        self._end_date = None
        self._operations = None
        self._week_of_the_month = None
        self._day_of_week = None
        self._start_time = None
        self._end_time = None
        self._do_not_submit_job = False

        self._commcell_id = self._commcell_object.commcell_id
        self._clientgroup_id = entity_details[&#34;clientGroupId&#34;]
        self._client_id = entity_details[&#34;clientId&#34;]
        self._agent_id = entity_details[&#34;applicationId&#34;]
        self._instance_id = entity_details[&#34;instanceId&#34;]
        self._backupset_id = entity_details[&#34;backupsetId&#34;]
        self._subclient_id = entity_details[&#34;subclientId&#34;]
        self._entity_level = entity_details[&#34;entity_level&#34;]

        self._refresh()

    def modify_operation_window(self, **modify_options):
        &#34;&#34;&#34;Modifies the Operation rule.

            Args:
                modify_options(dict)  -- Arbitrary keyword arguments.

                modify_options Args:
                    name          (str)   --  Name of the Operation rule

                    start_date    (int)   -- The start date for the operation rule.
                        Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                        default - current date

                    end_date      (int)   -- The end date for the operation rule.
                        Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                        default - 365 days

                    operations (list)         --   List of operations for which the operation
                                                   window is created

                        Acceptable Values:
                            FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
                            DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
                            SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
                            OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
                            MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION

                    week_of_the_month(list)     -- List of week of the month on which the operation rule applies to
                        Acceptable Values:
                            all/first/second/third/fourth/last

                            default - None

                    day_of_week (list)    -- List of days of the week on which the operation rule applies to
                        Acceptable Values:
                            sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday

                        default- Weekdays

                    start_time  (int)     -- The start time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 28800 (8 AM)
                    Must specify one timestamp for start time for all the weekdays, otherwise
                    make a list for each weekday mentioned in the day_of_week list.

                start_time (list)    -- The list of start timestamps for each weekday mentioned
                    in the day_of_week list.

                end_time    (int)     -- The end time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 86400 (6 PM)
                    Must specify one timestamp for end time for all the weekdays, otherwise
                    make a list for each weekday mentioned in the day_of_week list.

                end_time   (list)    -- The list of end timestamps for each weekday mentioned
                    in the day_of_week list.

                Example:
                    1. day_of_week : [&#34;sunday&#34;, &#34;thursday&#34;, &#34;saturday&#34;]
                       start_time  : 28800
                       end_time    : 86400
                       The above inputs specify that for all the three days mentioned, start_time and end_time of
                       operation window would be same
                    2. day_of_week : [&#34;monday&#34;,&#34;friday&#34;]
                       start_time  : [3600, 28800]
                       end_time    : [18000, 86400]
                       The above input specify that on monday operation window starts at 3600 and ends at 18000 whereas
                       on friday, the operation window starts at 28800 and ends at 86400

                    do_not_submit_job   (bool)  -- doNotSubmitJob of the operation rule

            Raises:
                SDKException:
                    if the Operation window could not be Modified

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        start_date = modify_options.get(&#34;start_date&#34;, self.start_date)
        end_date = modify_options.get(&#34;end_date&#34;, self.end_date)
        start_time = modify_options.get(&#34;start_time&#34;, self.start_time)
        end_time = modify_options.get(&#34;end_time&#34;, self.end_time)
        name = modify_options.get(&#34;name&#34;, self.name)
        operations = modify_options.get(&#34;operations&#34;, self.operations)
        week_of_the_month = modify_options.get(&#34;week_of_the_month&#34;, self.week_of_the_month)
        day_of_week = modify_options.get(&#34;day_of_week&#34;, self.day_of_week)
        do_not_submit_job = modify_options.get(&#34;do_not_submit_job&#34;, self.do_not_submit_job)

        if not operations:
            # Empty list can be passed
            operations_list = operations
        else:
            operations_list = [OPERATION_MAPPING[operation.upper()] for operation in operations]

        week_of_the_month_list = []
        if week_of_the_month:
            week_of_the_month_list = [WEEK_OF_THE_MONTH_MAPPING[week.lower()] for week in week_of_the_month]

        day_of_week_list = [DAY_OF_WEEK_MAPPING.index(day.lower()) for day in day_of_week]
        daytime_list = []
        num_of_days = len(day_of_week_list)
        if isinstance(start_time, int) and isinstance(end_time, int):
            daytime_list.append(
                {
                    &#34;startTime&#34;: start_time,
                    &#34;endTime&#34;: end_time,
                    &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                    &#34;dayOfWeek&#34;: day_of_week_list
                }
            )
        elif isinstance(start_time, list) and isinstance(end_time, list):
            if not (num_of_days == len(start_time) == len(end_time)):
                response_string = &#34;did not specify start time and end time for all the given week days&#34;
                raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
            for week_day in range(num_of_days):
                daytime_list.append(
                    {
                        &#34;startTime&#34;: start_time[week_day],
                        &#34;endTime&#34;: end_time[week_day],
                        &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                        &#34;dayOfWeek&#34;: [day_of_week_list[week_day]]
                    }
                )
        else:
            response_string = &#34;Both start_time and end_time should be of same type.&#34;
            raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
        payload = {
            &#34;operationWindow&#34;: {
                &#34;ruleEnabled&#34;: True,
                &#34;doNotSubmitJob&#34;: do_not_submit_job,
                &#34;startDate&#34;: start_date,
                &#34;endDate&#34;: end_date,
                &#34;name&#34;: name,
                &#34;ruleId&#34;: int(self.rule_id),
                &#34;operations&#34;: operations_list,
                &#34;dayTime&#34;: daytime_list
            },
            &#34;entity&#34;: {
                &#34;clientGroupId&#34;: int(self._clientgroup_id),
                &#34;clientId&#34;: int(self._client_id),
                &#34;applicationId&#34;: int(self._agent_id),
                &#34;instanceId&#34;: int(self._instance_id),
                &#34;backupsetId&#34;: int(self._backupset_id),
                &#34;subclientId&#34;: int(self._subclient_id)
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._operation_window, payload=payload)
        if flag:
            if response.json():
                error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
                if int(error_code) == 0:
                    int(response.json().get(&#39;operationWindow&#39;, {}).get(&#39;ruleId&#39;))
                    self._refresh()
                else:
                    raise SDKException(&#39;OperationWindow&#39;, &#39;105&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _refresh(self):
        &#34;&#34;&#34;Refreshes the properties of a rule&#34;&#34;&#34;
        self._get_rule_properties()

    def _get_rule_properties(self):
        &#34;&#34;&#34;
        Assigns the properties of an operation rule by getting the rule using rule id
        &#34;&#34;&#34;
        xml = &#34;&lt;Api_GetOperationWindowReq&gt;&lt;ruleId&gt;&#34; + str(self.rule_id) + &#34;&lt;/ruleId&gt;&lt;/Api_GetOperationWindowReq&gt;&#34;
        response_json = self._commcell_object._qoperation_execute(xml)
        if response_json:
            error_code = response_json.get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
            if int(error_code) == 0:
                response_json = response_json.get(&#39;operationWindow&#39;, {})[0]
                self._do_not_submit_job = response_json.get(&#39;doNotSubmitJob&#39;)
                self._name = response_json.get(&#39;name&#39;)
                self._start_date = response_json.get(&#39;startDate&#39;)
                self._end_date = response_json.get(&#39;endDate&#39;)
                operations = response_json.get(&#39;operations&#39;)
                operation_reverse_mapping = {value: key for key, value in OPERATION_MAPPING.items()}
                self._operations = [operation_reverse_mapping[operation] for operation in operations]
                week_of_the_month = response_json.get(&#34;dayTime&#34;, [{}])[0].get(&#39;weekOfTheMonth&#39;, [])
                if len(response_json.get(&#34;dayTime&#34;, [])) == 1:
                    start_time = response_json.get(&#34;dayTime&#34;, [{}])[0].get(&#39;startTime&#39;)
                    end_time = response_json.get(&#34;dayTime&#34;, [{}])[0].get(&#39;endTime&#39;)
                    day_of_week = response_json.get(&#34;dayTime&#34;, [{}])[0].get(&#39;dayOfWeek&#39;)
                else:
                    day_of_week = []
                    start_time = []
                    end_time = []
                    for week_day in response_json.get(&#34;dayTime&#34;, [{}]):
                        if week_day.get(&#34;dayOfWeek&#34;):
                            day_of_week.append(week_day.get(&#34;dayOfWeek&#34;)[0])
                        if week_day.get(&#34;startTime&#34;) is not None:
                            start_time.append(week_day.get(&#34;startTime&#34;))
                        if week_day.get(&#34;endTime&#34;) is not None:
                            end_time.append(week_day.get(&#34;endTime&#34;))
                wotm_reverse_mapping = {value: key for key, value in WEEK_OF_THE_MONTH_MAPPING.items()}
                self._week_of_the_month = [wotm_reverse_mapping[week] for week in week_of_the_month]
                self._day_of_week = [DAY_OF_WEEK_MAPPING[day] for day in day_of_week]
                self._start_time = start_time
                self._end_time = end_time
            else:
                raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;,
                                   response_json.get(&#34;error&#34;, {}).get(&#39;errorMessage&#39;))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def do_not_submit_job(self):
        &#34;&#34;&#34;Treats do_not_submit_job as a read-only attribute.&#34;&#34;&#34;
        return self._do_not_submit_job

    @do_not_submit_job.setter
    def do_not_submit_job(self, do_not_submit_job):
        &#34;&#34;&#34;
        Modifies do_not_submit_job of the operation rule
        Args:
             do_not_submit_job: (bool) -- do_not_submit_job of the operation rule to be modified&#34;&#34;&#34;
        self.modify_operation_window(do_not_submit_job=do_not_submit_job)

    @property
    def name(self):
        &#34;&#34;&#34;Treats name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        &#34;&#34;&#34;
        Modifies the name of the operation rule
        Args:
             name: (str) --Name of the operation rule to be modified
        &#34;&#34;&#34;
        self.modify_operation_window(name=name)

    @property
    def start_date(self):
        &#34;&#34;&#34;Treats start_date as a read-only attribute.&#34;&#34;&#34;
        return self._start_date

    @start_date.setter
    def start_date(self, start_date):
        &#34;&#34;&#34;
        Modifies the start_date of the operation rule
        Args:
            start_date: (int) --The start date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(start_date=start_date)

    @property
    def end_date(self):
        &#34;&#34;&#34;Treats end_date as a read-only attribute.&#34;&#34;&#34;
        return self._end_date

    @end_date.setter
    def end_date(self, end_date):
        &#34;&#34;&#34;
        Modifies the end_date of the operation rule
        Args:
            end_date: (int)   -- The end date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(end_date=end_date)

    @property
    def operations(self):
        &#34;&#34;&#34;Treats opearations as a read-only attribute.&#34;&#34;&#34;
        return self._operations

    @operations.setter
    def operations(self, operations):
        &#34;&#34;&#34;
        Modifies the operations of the operation rule
        Args:
            operations: (list)         --   List of operations for which the operation
                                               window is created
                    Acceptable Values:
                        FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
                        DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
                        SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
                        OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
                        MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION

        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(operations=operations)

    @property
    def week_of_the_month(self):
        &#34;&#34;&#34;Treats week_of_the_month as a read-only attribute.&#34;&#34;&#34;
        return self._week_of_the_month

    @week_of_the_month.setter
    def week_of_the_month(self, week_of_the_month):
        &#34;&#34;&#34;
        Modifies the week_of_the_month of the operation rule
        Args:
            week_of_the_month: (list)         --   List of week of the month on which the operation rule applies to
                     Acceptable Values:
                            all/first/second/third/fourth/fifth
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(week_of_the_month=week_of_the_month)

    @property
    def day_of_week(self):
        &#34;&#34;&#34;Treats day_of_week as a read-only attribute.&#34;&#34;&#34;
        return self._day_of_week

    @day_of_week.setter
    def day_of_week(self, day_of_week):
        &#34;&#34;&#34;
        Modifies the day_of_week of the operation rule
        Args:
            day_of_week: (list)    -- List of days of the week on which the operation rule applies to
                    Acceptable Values:
                        sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(day_of_week=day_of_week)

    @property
    def start_time(self):
        &#34;&#34;&#34;Treats start_time as a read-only attribute.&#34;&#34;&#34;
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        &#34;&#34;&#34;
        Modifies the start_time of the operation rule
        Args:
            start_time: (int)     -- The start time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                        (list)    -- The list of start timestamps for each weekday mentioned
                    in the day_of_week list.
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(start_time=start_time)

    @property
    def end_time(self):
        &#34;&#34;&#34;Treats end_time as a read-only attribute.&#34;&#34;&#34;
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        &#34;&#34;&#34;
        Modifies the end_time of the operation rule
        Args:
            end_time: (int)     -- The end time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                      (list)    -- The list of end timestamps for each weekday mentioned
                    in the day_of_week list.
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(end_time=end_time)

    @property
    def rule_id(self):
        &#34;&#34;&#34;Treats rule_id as read-only attribute&#34;&#34;&#34;
        return self._rule_id

    @property
    def commcell_id(self):
        &#34;&#34;&#34;Treats the commcell id as a read-only attribute.&#34;&#34;&#34;
        return self._commcell_id

    @property
    def clientgroup_id(self):
        &#34;&#34;&#34;Treats the client group id as a read-only attribute.&#34;&#34;&#34;
        return self._clientgroup_id

    @property
    def client_id(self):
        &#34;&#34;&#34;Treats the client id as a read-only attribute.&#34;&#34;&#34;
        return self._client_id

    @property
    def agent_id(self):
        &#34;&#34;&#34;Treats the agent id as a read-only attribute.&#34;&#34;&#34;
        return self._agent_id

    @property
    def instance_id(self):
        &#34;&#34;&#34;Treats the instance id as a read-only attribute.&#34;&#34;&#34;
        return self._instance_id

    @property
    def backupset_id(self):
        &#34;&#34;&#34;Treats the backupset id as a read-only attribute.&#34;&#34;&#34;
        return self._backupset_id

    @property
    def subclient_id(self):
        &#34;&#34;&#34;Treats the sub client id as a read-only attribute.&#34;&#34;&#34;
        return self._subclient_id

    @property
    def entity_level(self):
        &#34;&#34;&#34;Treats the entity level as a read-only attribute.&#34;&#34;&#34;
        return self._entity_level</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.operation_window.OperationWindow"><code class="flex name class">
<span>class <span class="ident">OperationWindow</span></span>
<span>(</span><span>generic_entity_obj)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all operation window related operations</p>
<p>Initialize the OperationWindow class instance for
performing Operation Window related operations.</p>
<p>Args:
generic_entity_obj
(object)
&ndash;
Commcell entity object
Expected value : commcell/Client/Agent/Instance/BackupSet/Subclient/Clientgroup Instance</p>
<p>Returns:
object
-
instance of the OperationWindow class</p>
<p>Raises:
Exception:
If invalid instance is passed</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L154-L590" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OperationWindow:
    &#34;&#34;&#34;Class for representing all operation window related operations&#34;&#34;&#34;

    def __init__(self, generic_entity_obj):
        &#34;&#34;&#34;Initialize the OperationWindow class instance for
           performing Operation Window related operations.

            Args:
                generic_entity_obj     (object)    --  Commcell entity object
                    Expected value : commcell/Client/Agent/Instance/BackupSet/Subclient/Clientgroup Instance

            Returns:
                object  -   instance of the OperationWindow class

            Raises:
                Exception:
                    If invalid instance is passed
        &#34;&#34;&#34;
        # imports inside the __init__ method definition to avoid cyclic imports
        from .commcell import Commcell

        if isinstance(generic_entity_obj, Commcell):
            self._commcell_object = generic_entity_obj
        else:
            self._commcell_object = generic_entity_obj._commcell_object

        self._commcell_services = self._commcell_object._services
        self._operation_window = self._commcell_services[&#39;OPERATION_WINDOW&#39;]
        self._list_operation_window = self._commcell_services[&#39;LIST_OPERATION_WINDOW&#39;]
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._update_response = self._commcell_object._update_response_

        self.clientgroup_id = 0
        self.client_id = 0
        self.agent_id = 0
        self.instance_id = 0
        self.backupset_id = 0
        self.subclient_id = 0
        self.entity_type = &#39;&#39;
        self.entity_id = &#39;&#39;
        self.entity_details = dict()

        self.generic_entity_obj = generic_entity_obj

        # we will derive all the entity id&#39;s based on the input entity type
        if isinstance(generic_entity_obj, Commcell):
            self.entity_details[&#34;entity_level&#34;] = &#34;commserv&#34;
        elif isinstance(generic_entity_obj, ClientGroup):
            self.clientgroup_id = generic_entity_obj.clientgroup_id
            self.entity_type = &#34;clientgroupId&#34;
            self.entity_id = self.clientgroup_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        elif isinstance(generic_entity_obj, Client):
            self.client_id = generic_entity_obj.client_id
            self.entity_type = &#34;clientId&#34;
            self.entity_id = self.client_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        elif isinstance(generic_entity_obj, Agent):
            self.client_id = generic_entity_obj._client_object.client_id
            self.agent_id = generic_entity_obj.agent_id
            self.entity_type = &#34;applicationId&#34;
            self.entity_id = self.agent_id
            self.entity_details[&#34;entity_level&#34;] = &#34;agent&#34;
        elif isinstance(generic_entity_obj, Instance):
            self.client_id = generic_entity_obj._agent_object._client_object.client_id
            self.agent_id = generic_entity_obj._agent_object.agent_id
            self.instance_id = generic_entity_obj.instance_id
            self.entity_type = &#34;instanceId&#34;
            self.entity_id = self.instance_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        elif isinstance(generic_entity_obj, Backupset):
            self.client_id = generic_entity_obj._instance_object._agent_object. \
                _client_object.client_id
            self.agent_id = generic_entity_obj._instance_object._agent_object.agent_id
            self.instance_id = generic_entity_obj._instance_object.instance_id
            self.backupset_id = generic_entity_obj.backupset_id
            self.entity_type = &#34;backupsetId&#34;
            self.entity_id = self.backupset_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        elif isinstance(generic_entity_obj, Subclient):
            self.client_id = generic_entity_obj._backupset_object._instance_object. \
                _agent_object._client_object.client_id
            self.agent_id = generic_entity_obj._backupset_object. \
                _instance_object._agent_object.agent_id
            self.instance_id = generic_entity_obj._backupset_object._instance_object.instance_id
            self.backupset_id = generic_entity_obj._backupset_object.backupset_id
            self.subclient_id = generic_entity_obj.subclient_id
            self.entity_type = &#34;subclientId&#34;
            self.entity_id = self.subclient_id
            self.entity_details[&#34;entity_level&#34;] = self.entity_type[:-2]
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#34;Invalid instance passed&#34;)

        self.entity_details.update({&#34;clientGroupId&#34;: self.clientgroup_id,
                                    &#34;clientId&#34;: self.client_id,
                                    &#34;applicationId&#34;: self.agent_id,
                                    &#34;instanceId&#34;: self.instance_id,
                                    &#34;backupsetId&#34;: self.backupset_id,
                                    &#34;subclientId&#34;: self.subclient_id})

        # append the entity type and entity id to end of list operation window REST API.
        # For commcell it will empty string
        self.connect_string = self._list_operation_window.split(&#39;?&#39;)[0] + &#39;?&#39; + self.entity_type + &#34;=&#34; + self.entity_id

    def create_operation_window(
            self,
            name,
            start_date=None,
            end_date=None,
            operations=None,
            day_of_week=None,
            start_time=None,
            end_time=None,
            week_of_the_month=None,
            do_not_submit_job=False):
        &#34;&#34;&#34; Creates operation rule on the initialized commcell entity

            Args:
                name          (str)   --  Name of the Operation rule

                start_date    (int)   -- The start date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - current date

                end_date      (int)   -- The end date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 365 days

                operations (list)         --   List of operations for which the operation
                                               window is created

                    Acceptable Values:
                        FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
                        DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
                        SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
                        OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
                        MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION

                week_of_the_month(list)     -- List of week of the month on which the operation rule applies to
                        Acceptable Values:
                            all/first/second/third/fourth/last

                            default - None

                day_of_week (list)    -- List of days of the week on which the operation rule applies to
                    Acceptable Values:
                        sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday

                    default- Weekdays

                start_time  (int)     -- The start time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 28800 (8 AM)
                    Must specify one timestamp for start time for all the weekdays, otherwise
                    make a list for each weekday mentioned in the day_of_week list.

                start_time (list)    -- The list of start timestamps for each weekday mentioned
                    in the day_of_week list.

                end_time    (int)     -- The end time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 86400 (6 PM)
                    Must specify one timestamp for end time for all the weekdays, otherwise
                    make a list for each weekday mentioned in the day_of_week list.

                end_time   (list)    -- The list of end timestamps for each weekday mentioned
                    in the day_of_week list.

                Example:
                    1. day_of_week : [&#34;sunday&#34;, &#34;thursday&#34;, &#34;saturday&#34;]
                       start_time  : 28800
                       end_time    : 86400
                       The above inputs specify that for all the three days mentioned, start_time and end_time of
                       operation window would be same
                    2. day_of_week : [&#34;monday&#34;,&#34;friday&#34;]
                       start_time  : [3600, 28800]
                       end_time    : [18000, 86400]
                       The above input specify that on monday operation window starts at 3600 and ends at 18000 whereas
                       on friday, the operation window starts at 28800 and ends at 86400

                do_not_submit_job   (bool) -- doNotSubmitJob of the operation rule

            Returns:
                Returns the instance of created Operation window details

            Raises:
                SDKException:
                    if the Operation window could not be created

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if start_date is None:
            start_date = int(calendar.timegm(datetime.date.today().timetuple()))
        if end_date is None:
            end_date = start_date
        if start_time is None:
            start_time = int(timedelta(hours=8).total_seconds())
        if end_time is None:
            end_time = int(timedelta(hours=18).total_seconds())

        operations_list = []
        if operations is None:
            operations_list = [OPERATION_MAPPING[&#34;FULL_DATA_MANAGEMENT&#34;]]
        else:
            for operation in operations:
                if operation not in OPERATION_MAPPING:
                    response_string = &#34;Invalid input %s for operation is passed&#34; % operation
                    raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
                operations_list.append(OPERATION_MAPPING[operation.upper()])

        day_of_week_list = []
        if day_of_week is None:
            day_of_week_list = [1, 2, 3, 4, 5]      # defaults to weekdays
        else:
            for day in day_of_week:
                if day.lower() not in DAY_OF_WEEK_MAPPING:
                    response_string = &#34;Invalid input value %s for day_of_week&#34; % day
                    raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
                day_of_week_list.append(DAY_OF_WEEK_MAPPING.index(day.lower()))

        week_of_the_month_list = []
        if week_of_the_month:
            for week in week_of_the_month:
                if week.lower() not in WEEK_OF_THE_MONTH_MAPPING:
                    response_string = &#34;Invalid input %s for week_of_the_month&#34; % week
                    raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
                week_of_the_month_list.append(WEEK_OF_THE_MONTH_MAPPING[week.lower()])

        daytime_list = []
        num_of_days = len(day_of_week_list)
        if isinstance(start_time, int) and isinstance(end_time, int):
            daytime_list.append(
                {
                    &#34;startTime&#34;: start_time,
                    &#34;endTime&#34;: end_time,
                    &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                    &#34;dayOfWeek&#34;: day_of_week_list
                }
            )
        elif isinstance(start_time, list) and isinstance(end_time, list):
            if not(num_of_days == len(start_time) == len(end_time)):
                response_string = &#34;did not specify start time and end time for all the given week days&#34;
                raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
            for week_day in range(num_of_days):
                daytime_list.append(
                    {
                        &#34;startTime&#34;: start_time[week_day],
                        &#34;endTime&#34;: end_time[week_day],
                        &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                        &#34;dayOfWeek&#34;: [day_of_week_list[week_day]]
                    }
                )
        else:
            response_string = &#34;Both start_time and end_time should be of same type.&#34;
            raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)

        payload = {
            &#34;operationWindow&#34;: {
                &#34;ruleEnabled&#34;: True,
                &#34;doNotSubmitJob&#34;: do_not_submit_job,
                &#34;startDate&#34;: start_date,
                &#34;endDate&#34;: end_date,
                &#34;name&#34;: name,
                &#34;operations&#34;: operations_list,
                &#34;dayTime&#34;: daytime_list
            },
            &#34;entity&#34;: {
                &#34;clientGroupId&#34;: int(self.clientgroup_id),
                &#34;clientId&#34;: int(self.client_id),
                &#34;applicationId&#34;: int(self.agent_id),
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;backupsetId&#34;: int(self.backupset_id),
                &#34;subclientId&#34;: int(self.subclient_id)
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._operation_window, payload=payload)
        if flag:
            if response.json():
                error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
                if int(error_code) == 0:
                    return self.get(rule_id=int(response.json().get(&#39;operationWindow&#39;, {}).get(&#39;ruleId&#39;)))
                raise SDKException(&#39;OperationWindow&#39;, &#39;101&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._update_response(response.text)
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)

    def delete_operation_window(self, rule_id=None, name=None):
        &#34;&#34;&#34;Deletes the operation rule associated with given rule Id/Name.

            Args:
                rule_id       (int)   --  Rule Id of the operation window

                name           (str)   --  Name of the operation window

            Raises:
                SDKException:
                    if the Operation window could not be deleted

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not name and not rule_id:
            raise SDKException(
                &#39;OperationWindow&#39;,
                &#39;102&#39;,
                &#39;Either Name or Rule Id is needed&#39;)

        if name and not isinstance(name, str) or rule_id and not isinstance(rule_id, int):
            raise SDKException(&#39;OperationWindow&#39;, &#39;106&#39;)

        if name:
            rule_id = self.get(name=name).rule_id

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._operation_window + &#39;/&#39; + str(rule_id))
        if flag:
            if response.json():
                error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
                if int(error_code):
                    raise SDKException(&#39;OperationWindow&#39;, &#39;103&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)

    def list_operation_window(self):
        &#34;&#34;&#34;Lists the operation rules for the associated commcell entity.

            Returns:
                Returns the List of operation rules (dictionary) associated with given commcell entity

                Example --

                    [{&#39;ruleEnabled&#39;: True,
                      &#39;doNotSubmitJob&#39;: False,
                      &#39;endDate&#39;: 0,
                      &#39;level&#39;: 0,
                      &#39;name&#39;: &#39;Rule1&#39;,
                      &#39;ruleId&#39;: 1,
                      &#39;startDate&#39;: 0,
                      &#39;operations&#39;: [&#39;FULL_DATA_MANAGEMENT&#39;, &#39;NON_FULL_DATA_MANAGEMENT&#39;],
                      &#39;company&#39;: {&#39;_type_&#39;: 61,
                                  &#39;providerId&#39;: 0,
                                  &#39;providerDomainName&#39;: &#39;&#39;},
                      &#39;dayTime&#39;: [{&#39;startTime&#39;: 28800,
                                   &#39;endTime&#39;: 64800,
                                   &#39;weekOfTheMonth&#39;: [&#39;first&#39;,&#39;third&#39;],
                                   &#39;dayOfWeek&#39;: [&#39;sunday&#39;,&#39;monday&#39;]}]}
                    ]

            Raises:
                SDKException:
                    if the Operation rules could not be Listed

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self.connect_string)
        if flag:
            if response.json():
                error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
                if int(error_code) == 0:
                    list_of_rules = response.json().get(&#34;operationWindow&#34;)
                    operation_reverse_mapping = {value: key for key, value in OPERATION_MAPPING.items()}
                    wotm_reverse_mapping = {value: key for key, value in WEEK_OF_THE_MONTH_MAPPING.items()}
                    if list_of_rules is not None:
                        for operation_rule in list_of_rules:
                            operations = operation_rule.get(&#34;operations&#34;)
                            if operations is not None:
                                operation_rule[&#34;operations&#34;] = [operation_reverse_mapping[operation] for operation in
                                                                operations]
                            day_time_list = operation_rule.get(&#34;dayTime&#34;, [])
                            for day_time in day_time_list:
                                if day_time.get(&#34;weekOfTheMonth&#34;): # if we have weekOfTheMonth, we replace it with name.
                                    day_time[&#39;weekOfTheMonth&#39;] = [wotm_reverse_mapping[week] for week in day_time.get(&#34;weekOfTheMonth&#34;)]

                                if day_time.get(&#34;dayTime&#34;): # if we have dayTime, we replace it with name.
                                    day_time[&#39;dayTime&#39;] = [DAY_OF_WEEK_MAPPING[day] for day in day_time[&#39;dayTime&#39;]]
                            operation_rule[&#39;dayTime&#39;] = day_time_list
                    return list_of_rules
                raise SDKException(&#39;OperationWindow&#39;, &#39;104&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._update_response(response.text)
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)

    def get(self, rule_id=None, name=None):
        &#34;&#34;&#34;Returns the operation rule object for a given rule

         Args:
            rule_id               (int)   --  Rule Id of an operation Window

            name                  (str)   --  Name of the operation window

         Returns:
                object - instance of the OperationWindowDetails class
                            for the given operation window name/rule
         Raises:
                SDKException:
                    if type of the operation window name argument is not string

                    if no operation window exists with such name
        &#34;&#34;&#34;
        if not name and not rule_id:
            raise SDKException(
                &#39;OperationWindow&#39;,
                &#39;102&#39;,
                &#39;Either Name or Rule Id is needed&#39;)

        if name and not isinstance(name, str) or rule_id and not isinstance(rule_id, int):
            raise SDKException(&#39;OperationWindow&#39;, &#39;106&#39;)

        list_of_rules = self.list_operation_window()
        if rule_id:
            for operation_rule in list_of_rules:
                if operation_rule.get(&#34;ruleId&#34;) == rule_id:
                    return OperationWindowDetails(self.generic_entity_obj, rule_id, self.entity_details)
            raise Exception(&#34;No such operation window with rule id as {0} exists&#34;.format(rule_id))
        if name:
            rules = [operation_rule.get(&#34;ruleId&#34;) for operation_rule in list_of_rules
                     if operation_rule.get(&#34;name&#34;) == name]
            if not rules:
                raise Exception(&#34;No such operation window with name as {0} exists&#34;.format(name))
            if len(rules) == 1:
                return OperationWindowDetails(self.generic_entity_obj, rules[0], self.entity_details)
            raise Exception(&#34;More than one operation window are named as {0} exists&#34;.format(name))</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.operation_window.OperationWindow.create_operation_window"><code class="name flex">
<span>def <span class="ident">create_operation_window</span></span>(<span>self, name, start_date=None, end_date=None, operations=None, day_of_week=None, start_time=None, end_time=None, week_of_the_month=None, do_not_submit_job=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates operation rule on the initialized commcell entity</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
Name of the Operation rule</p>
<p>start_date
(int)
&ndash; The start date for the operation rule.
Valid values are UNIX-style timestamps (seconds since January 1, 1970).
default - current date</p>
<p>end_date
(int)
&ndash; The end date for the operation rule.
Valid values are UNIX-style timestamps (seconds since January 1, 1970).
default - 365 days</p>
<p>operations (list)
&ndash;
List of operations for which the operation
window is created</p>
<pre><code>Acceptable Values:
    FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
    DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
    SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
    OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
    MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION
</code></pre>
<p>week_of_the_month(list)
&ndash; List of week of the month on which the operation rule applies to
Acceptable Values:
all/first/second/third/fourth/last</p>
<pre><code>        default - None
</code></pre>
<p>day_of_week (list)
&ndash; List of days of the week on which the operation rule applies to
Acceptable Values:
sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday</p>
<pre><code>default- Weekdays
</code></pre>
<p>start_time
(int)
&ndash; The start time for the "do not run" interval.
Valid values are UNIX-style timestamps (seconds since January 1, 1970).
default - 28800 (8 AM)
Must specify one timestamp for start time for all the weekdays, otherwise
make a list for each weekday mentioned in the day_of_week list.</p>
<p>start_time (list)
&ndash; The list of start timestamps for each weekday mentioned
in the day_of_week list.</p>
<p>end_time
(int)
&ndash; The end time for the "do not run" interval.
Valid values are UNIX-style timestamps (seconds since January 1, 1970).
default - 86400 (6 PM)
Must specify one timestamp for end time for all the weekdays, otherwise
make a list for each weekday mentioned in the day_of_week list.</p>
<p>end_time
(list)
&ndash; The list of end timestamps for each weekday mentioned
in the day_of_week list.</p>
<p>Example:
1. day_of_week : ["sunday", "thursday", "saturday"]
start_time
: 28800
end_time
: 86400
The above inputs specify that for all the three days mentioned, start_time and end_time of
operation window would be same
2. day_of_week : ["monday","friday"]
start_time
: [3600, 28800]
end_time
: [18000, 86400]
The above input specify that on monday operation window starts at 3600 and ends at 18000 whereas
on friday, the operation window starts at 28800 and ends at 86400</p>
<p>do_not_submit_job
(bool) &ndash; doNotSubmitJob of the operation rule</p>
<h2 id="returns">Returns</h2>
<p>Returns the instance of created Operation window details</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if the Operation window could not be created</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L258-L442" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_operation_window(
        self,
        name,
        start_date=None,
        end_date=None,
        operations=None,
        day_of_week=None,
        start_time=None,
        end_time=None,
        week_of_the_month=None,
        do_not_submit_job=False):
    &#34;&#34;&#34; Creates operation rule on the initialized commcell entity

        Args:
            name          (str)   --  Name of the Operation rule

            start_date    (int)   -- The start date for the operation rule.
                Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                default - current date

            end_date      (int)   -- The end date for the operation rule.
                Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                default - 365 days

            operations (list)         --   List of operations for which the operation
                                           window is created

                Acceptable Values:
                    FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
                    DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
                    SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
                    OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
                    MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION

            week_of_the_month(list)     -- List of week of the month on which the operation rule applies to
                    Acceptable Values:
                        all/first/second/third/fourth/last

                        default - None

            day_of_week (list)    -- List of days of the week on which the operation rule applies to
                Acceptable Values:
                    sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday

                default- Weekdays

            start_time  (int)     -- The start time for the &#34;do not run&#34; interval.
                Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                default - 28800 (8 AM)
                Must specify one timestamp for start time for all the weekdays, otherwise
                make a list for each weekday mentioned in the day_of_week list.

            start_time (list)    -- The list of start timestamps for each weekday mentioned
                in the day_of_week list.

            end_time    (int)     -- The end time for the &#34;do not run&#34; interval.
                Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                default - 86400 (6 PM)
                Must specify one timestamp for end time for all the weekdays, otherwise
                make a list for each weekday mentioned in the day_of_week list.

            end_time   (list)    -- The list of end timestamps for each weekday mentioned
                in the day_of_week list.

            Example:
                1. day_of_week : [&#34;sunday&#34;, &#34;thursday&#34;, &#34;saturday&#34;]
                   start_time  : 28800
                   end_time    : 86400
                   The above inputs specify that for all the three days mentioned, start_time and end_time of
                   operation window would be same
                2. day_of_week : [&#34;monday&#34;,&#34;friday&#34;]
                   start_time  : [3600, 28800]
                   end_time    : [18000, 86400]
                   The above input specify that on monday operation window starts at 3600 and ends at 18000 whereas
                   on friday, the operation window starts at 28800 and ends at 86400

            do_not_submit_job   (bool) -- doNotSubmitJob of the operation rule

        Returns:
            Returns the instance of created Operation window details

        Raises:
            SDKException:
                if the Operation window could not be created

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if start_date is None:
        start_date = int(calendar.timegm(datetime.date.today().timetuple()))
    if end_date is None:
        end_date = start_date
    if start_time is None:
        start_time = int(timedelta(hours=8).total_seconds())
    if end_time is None:
        end_time = int(timedelta(hours=18).total_seconds())

    operations_list = []
    if operations is None:
        operations_list = [OPERATION_MAPPING[&#34;FULL_DATA_MANAGEMENT&#34;]]
    else:
        for operation in operations:
            if operation not in OPERATION_MAPPING:
                response_string = &#34;Invalid input %s for operation is passed&#34; % operation
                raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
            operations_list.append(OPERATION_MAPPING[operation.upper()])

    day_of_week_list = []
    if day_of_week is None:
        day_of_week_list = [1, 2, 3, 4, 5]      # defaults to weekdays
    else:
        for day in day_of_week:
            if day.lower() not in DAY_OF_WEEK_MAPPING:
                response_string = &#34;Invalid input value %s for day_of_week&#34; % day
                raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
            day_of_week_list.append(DAY_OF_WEEK_MAPPING.index(day.lower()))

    week_of_the_month_list = []
    if week_of_the_month:
        for week in week_of_the_month:
            if week.lower() not in WEEK_OF_THE_MONTH_MAPPING:
                response_string = &#34;Invalid input %s for week_of_the_month&#34; % week
                raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
            week_of_the_month_list.append(WEEK_OF_THE_MONTH_MAPPING[week.lower()])

    daytime_list = []
    num_of_days = len(day_of_week_list)
    if isinstance(start_time, int) and isinstance(end_time, int):
        daytime_list.append(
            {
                &#34;startTime&#34;: start_time,
                &#34;endTime&#34;: end_time,
                &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                &#34;dayOfWeek&#34;: day_of_week_list
            }
        )
    elif isinstance(start_time, list) and isinstance(end_time, list):
        if not(num_of_days == len(start_time) == len(end_time)):
            response_string = &#34;did not specify start time and end time for all the given week days&#34;
            raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
        for week_day in range(num_of_days):
            daytime_list.append(
                {
                    &#34;startTime&#34;: start_time[week_day],
                    &#34;endTime&#34;: end_time[week_day],
                    &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                    &#34;dayOfWeek&#34;: [day_of_week_list[week_day]]
                }
            )
    else:
        response_string = &#34;Both start_time and end_time should be of same type.&#34;
        raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)

    payload = {
        &#34;operationWindow&#34;: {
            &#34;ruleEnabled&#34;: True,
            &#34;doNotSubmitJob&#34;: do_not_submit_job,
            &#34;startDate&#34;: start_date,
            &#34;endDate&#34;: end_date,
            &#34;name&#34;: name,
            &#34;operations&#34;: operations_list,
            &#34;dayTime&#34;: daytime_list
        },
        &#34;entity&#34;: {
            &#34;clientGroupId&#34;: int(self.clientgroup_id),
            &#34;clientId&#34;: int(self.client_id),
            &#34;applicationId&#34;: int(self.agent_id),
            &#34;instanceId&#34;: int(self.instance_id),
            &#34;backupsetId&#34;: int(self.backupset_id),
            &#34;subclientId&#34;: int(self.subclient_id)
        }
    }
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._operation_window, payload=payload)
    if flag:
        if response.json():
            error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
            if int(error_code) == 0:
                return self.get(rule_id=int(response.json().get(&#39;operationWindow&#39;, {}).get(&#39;ruleId&#39;)))
            raise SDKException(&#39;OperationWindow&#39;, &#39;101&#39;)
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    response_string = self._update_response(response.text)
    raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindow.delete_operation_window"><code class="name flex">
<span>def <span class="ident">delete_operation_window</span></span>(<span>self, rule_id=None, name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the operation rule associated with given rule Id/Name.</p>
<h2 id="args">Args</h2>
<p>rule_id
(int)
&ndash;
Rule Id of the operation window</p>
<p>name
(str)
&ndash;
Name of the operation window</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if the Operation window could not be deleted</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L444-L486" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_operation_window(self, rule_id=None, name=None):
    &#34;&#34;&#34;Deletes the operation rule associated with given rule Id/Name.

        Args:
            rule_id       (int)   --  Rule Id of the operation window

            name           (str)   --  Name of the operation window

        Raises:
            SDKException:
                if the Operation window could not be deleted

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    if not name and not rule_id:
        raise SDKException(
            &#39;OperationWindow&#39;,
            &#39;102&#39;,
            &#39;Either Name or Rule Id is needed&#39;)

    if name and not isinstance(name, str) or rule_id and not isinstance(rule_id, int):
        raise SDKException(&#39;OperationWindow&#39;, &#39;106&#39;)

    if name:
        rule_id = self.get(name=name).rule_id

    flag, response = self._cvpysdk_object.make_request(
        &#39;DELETE&#39;, self._operation_window + &#39;/&#39; + str(rule_id))
    if flag:
        if response.json():
            error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
            if int(error_code):
                raise SDKException(&#39;OperationWindow&#39;, &#39;103&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindow.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, rule_id=None, name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the operation rule object for a given rule</p>
<h2 id="args">Args</h2>
<p>rule_id
(int)
&ndash;
Rule Id of an operation Window</p>
<p>name
(str)
&ndash;
Name of the operation window</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the OperationWindowDetails class
for the given operation window name/rule</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the operation window name argument is not string</p>
<pre><code>if no operation window exists with such name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L551-L590" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, rule_id=None, name=None):
    &#34;&#34;&#34;Returns the operation rule object for a given rule

     Args:
        rule_id               (int)   --  Rule Id of an operation Window

        name                  (str)   --  Name of the operation window

     Returns:
            object - instance of the OperationWindowDetails class
                        for the given operation window name/rule
     Raises:
            SDKException:
                if type of the operation window name argument is not string

                if no operation window exists with such name
    &#34;&#34;&#34;
    if not name and not rule_id:
        raise SDKException(
            &#39;OperationWindow&#39;,
            &#39;102&#39;,
            &#39;Either Name or Rule Id is needed&#39;)

    if name and not isinstance(name, str) or rule_id and not isinstance(rule_id, int):
        raise SDKException(&#39;OperationWindow&#39;, &#39;106&#39;)

    list_of_rules = self.list_operation_window()
    if rule_id:
        for operation_rule in list_of_rules:
            if operation_rule.get(&#34;ruleId&#34;) == rule_id:
                return OperationWindowDetails(self.generic_entity_obj, rule_id, self.entity_details)
        raise Exception(&#34;No such operation window with rule id as {0} exists&#34;.format(rule_id))
    if name:
        rules = [operation_rule.get(&#34;ruleId&#34;) for operation_rule in list_of_rules
                 if operation_rule.get(&#34;name&#34;) == name]
        if not rules:
            raise Exception(&#34;No such operation window with name as {0} exists&#34;.format(name))
        if len(rules) == 1:
            return OperationWindowDetails(self.generic_entity_obj, rules[0], self.entity_details)
        raise Exception(&#34;More than one operation window are named as {0} exists&#34;.format(name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindow.list_operation_window"><code class="name flex">
<span>def <span class="ident">list_operation_window</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Lists the operation rules for the associated commcell entity.</p>
<h2 id="returns">Returns</h2>
<p>Returns the List of operation rules (dictionary) associated with given commcell entity</p>
<p>Example &ndash;</p>
<pre><code>[{'ruleEnabled': True,
  'doNotSubmitJob': False,
  'endDate': 0,
  'level': 0,
  'name': 'Rule1',
  'ruleId': 1,
  'startDate': 0,
  'operations': ['FULL_DATA_MANAGEMENT', 'NON_FULL_DATA_MANAGEMENT'],
  'company': {'_type_': 61,
              'providerId': 0,
              'providerDomainName': ''},
  'dayTime': [{'startTime': 28800,
               'endTime': 64800,
               'weekOfTheMonth': ['first','third'],
               'dayOfWeek': ['sunday','monday']}]}
]
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if the Operation rules could not be Listed</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L488-L549" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def list_operation_window(self):
    &#34;&#34;&#34;Lists the operation rules for the associated commcell entity.

        Returns:
            Returns the List of operation rules (dictionary) associated with given commcell entity

            Example --

                [{&#39;ruleEnabled&#39;: True,
                  &#39;doNotSubmitJob&#39;: False,
                  &#39;endDate&#39;: 0,
                  &#39;level&#39;: 0,
                  &#39;name&#39;: &#39;Rule1&#39;,
                  &#39;ruleId&#39;: 1,
                  &#39;startDate&#39;: 0,
                  &#39;operations&#39;: [&#39;FULL_DATA_MANAGEMENT&#39;, &#39;NON_FULL_DATA_MANAGEMENT&#39;],
                  &#39;company&#39;: {&#39;_type_&#39;: 61,
                              &#39;providerId&#39;: 0,
                              &#39;providerDomainName&#39;: &#39;&#39;},
                  &#39;dayTime&#39;: [{&#39;startTime&#39;: 28800,
                               &#39;endTime&#39;: 64800,
                               &#39;weekOfTheMonth&#39;: [&#39;first&#39;,&#39;third&#39;],
                               &#39;dayOfWeek&#39;: [&#39;sunday&#39;,&#39;monday&#39;]}]}
                ]

        Raises:
            SDKException:
                if the Operation rules could not be Listed

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self.connect_string)
    if flag:
        if response.json():
            error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
            if int(error_code) == 0:
                list_of_rules = response.json().get(&#34;operationWindow&#34;)
                operation_reverse_mapping = {value: key for key, value in OPERATION_MAPPING.items()}
                wotm_reverse_mapping = {value: key for key, value in WEEK_OF_THE_MONTH_MAPPING.items()}
                if list_of_rules is not None:
                    for operation_rule in list_of_rules:
                        operations = operation_rule.get(&#34;operations&#34;)
                        if operations is not None:
                            operation_rule[&#34;operations&#34;] = [operation_reverse_mapping[operation] for operation in
                                                            operations]
                        day_time_list = operation_rule.get(&#34;dayTime&#34;, [])
                        for day_time in day_time_list:
                            if day_time.get(&#34;weekOfTheMonth&#34;): # if we have weekOfTheMonth, we replace it with name.
                                day_time[&#39;weekOfTheMonth&#39;] = [wotm_reverse_mapping[week] for week in day_time.get(&#34;weekOfTheMonth&#34;)]

                            if day_time.get(&#34;dayTime&#34;): # if we have dayTime, we replace it with name.
                                day_time[&#39;dayTime&#39;] = [DAY_OF_WEEK_MAPPING[day] for day in day_time[&#39;dayTime&#39;]]
                        operation_rule[&#39;dayTime&#39;] = day_time_list
                return list_of_rules
            raise SDKException(&#39;OperationWindow&#39;, &#39;104&#39;)
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    response_string = self._update_response(response.text)
    raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails"><code class="flex name class">
<span>class <span class="ident">OperationWindowDetails</span></span>
<span>(</span><span>generic_entity_obj, rule_id, entity_details)</span>
</code></dt>
<dd>
<div class="desc"><p>Helper class for modifying operation window</p>
<p>Initialize the OperationWindowDetails class instance for
modifying OperationWindow.</p>
<pre><code>Args:
    generic_entity_obj     (object)    --  Commcell entity object
        Expected value : commcell/Client/Agent/Instance/BackupSet/Subclient/Clientgroup Entity

    rule_id (int)   -- Rule id of the operation window to be modified

    entity_details -- Details related to the entity

Usually gets initialized from OperationWindow class

Returns:
    object  -   instance of the OperationWindowDetails class
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L593-L1055" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OperationWindowDetails:
    &#34;&#34;&#34;Helper class for modifying operation window&#34;&#34;&#34;

    def __init__(self, generic_entity_obj, rule_id, entity_details):
        &#34;&#34;&#34;
        Initialize the OperationWindowDetails class instance for
           modifying OperationWindow.

            Args:
                generic_entity_obj     (object)    --  Commcell entity object
                    Expected value : commcell/Client/Agent/Instance/BackupSet/Subclient/Clientgroup Entity

                rule_id (int)   -- Rule id of the operation window to be modified

                entity_details -- Details related to the entity

            Usually gets initialized from OperationWindow class

            Returns:
                object  -   instance of the OperationWindowDetails class
        &#34;&#34;&#34;
        from .commcell import Commcell
        if isinstance(generic_entity_obj, Commcell):
            self._commcell_object = generic_entity_obj
        else:
            self._commcell_object = generic_entity_obj._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._update_response = self._commcell_object._update_response_
        self._commcell_services = self._commcell_object._services
        self._operation_window = self._commcell_services[&#39;OPERATION_WINDOW&#39;]

        self._rule_id = rule_id
        self._name = None
        self._start_date = None
        self._end_date = None
        self._operations = None
        self._week_of_the_month = None
        self._day_of_week = None
        self._start_time = None
        self._end_time = None
        self._do_not_submit_job = False

        self._commcell_id = self._commcell_object.commcell_id
        self._clientgroup_id = entity_details[&#34;clientGroupId&#34;]
        self._client_id = entity_details[&#34;clientId&#34;]
        self._agent_id = entity_details[&#34;applicationId&#34;]
        self._instance_id = entity_details[&#34;instanceId&#34;]
        self._backupset_id = entity_details[&#34;backupsetId&#34;]
        self._subclient_id = entity_details[&#34;subclientId&#34;]
        self._entity_level = entity_details[&#34;entity_level&#34;]

        self._refresh()

    def modify_operation_window(self, **modify_options):
        &#34;&#34;&#34;Modifies the Operation rule.

            Args:
                modify_options(dict)  -- Arbitrary keyword arguments.

                modify_options Args:
                    name          (str)   --  Name of the Operation rule

                    start_date    (int)   -- The start date for the operation rule.
                        Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                        default - current date

                    end_date      (int)   -- The end date for the operation rule.
                        Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                        default - 365 days

                    operations (list)         --   List of operations for which the operation
                                                   window is created

                        Acceptable Values:
                            FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
                            DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
                            SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
                            OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
                            MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION

                    week_of_the_month(list)     -- List of week of the month on which the operation rule applies to
                        Acceptable Values:
                            all/first/second/third/fourth/last

                            default - None

                    day_of_week (list)    -- List of days of the week on which the operation rule applies to
                        Acceptable Values:
                            sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday

                        default- Weekdays

                    start_time  (int)     -- The start time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 28800 (8 AM)
                    Must specify one timestamp for start time for all the weekdays, otherwise
                    make a list for each weekday mentioned in the day_of_week list.

                start_time (list)    -- The list of start timestamps for each weekday mentioned
                    in the day_of_week list.

                end_time    (int)     -- The end time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 86400 (6 PM)
                    Must specify one timestamp for end time for all the weekdays, otherwise
                    make a list for each weekday mentioned in the day_of_week list.

                end_time   (list)    -- The list of end timestamps for each weekday mentioned
                    in the day_of_week list.

                Example:
                    1. day_of_week : [&#34;sunday&#34;, &#34;thursday&#34;, &#34;saturday&#34;]
                       start_time  : 28800
                       end_time    : 86400
                       The above inputs specify that for all the three days mentioned, start_time and end_time of
                       operation window would be same
                    2. day_of_week : [&#34;monday&#34;,&#34;friday&#34;]
                       start_time  : [3600, 28800]
                       end_time    : [18000, 86400]
                       The above input specify that on monday operation window starts at 3600 and ends at 18000 whereas
                       on friday, the operation window starts at 28800 and ends at 86400

                    do_not_submit_job   (bool)  -- doNotSubmitJob of the operation rule

            Raises:
                SDKException:
                    if the Operation window could not be Modified

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        start_date = modify_options.get(&#34;start_date&#34;, self.start_date)
        end_date = modify_options.get(&#34;end_date&#34;, self.end_date)
        start_time = modify_options.get(&#34;start_time&#34;, self.start_time)
        end_time = modify_options.get(&#34;end_time&#34;, self.end_time)
        name = modify_options.get(&#34;name&#34;, self.name)
        operations = modify_options.get(&#34;operations&#34;, self.operations)
        week_of_the_month = modify_options.get(&#34;week_of_the_month&#34;, self.week_of_the_month)
        day_of_week = modify_options.get(&#34;day_of_week&#34;, self.day_of_week)
        do_not_submit_job = modify_options.get(&#34;do_not_submit_job&#34;, self.do_not_submit_job)

        if not operations:
            # Empty list can be passed
            operations_list = operations
        else:
            operations_list = [OPERATION_MAPPING[operation.upper()] for operation in operations]

        week_of_the_month_list = []
        if week_of_the_month:
            week_of_the_month_list = [WEEK_OF_THE_MONTH_MAPPING[week.lower()] for week in week_of_the_month]

        day_of_week_list = [DAY_OF_WEEK_MAPPING.index(day.lower()) for day in day_of_week]
        daytime_list = []
        num_of_days = len(day_of_week_list)
        if isinstance(start_time, int) and isinstance(end_time, int):
            daytime_list.append(
                {
                    &#34;startTime&#34;: start_time,
                    &#34;endTime&#34;: end_time,
                    &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                    &#34;dayOfWeek&#34;: day_of_week_list
                }
            )
        elif isinstance(start_time, list) and isinstance(end_time, list):
            if not (num_of_days == len(start_time) == len(end_time)):
                response_string = &#34;did not specify start time and end time for all the given week days&#34;
                raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
            for week_day in range(num_of_days):
                daytime_list.append(
                    {
                        &#34;startTime&#34;: start_time[week_day],
                        &#34;endTime&#34;: end_time[week_day],
                        &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                        &#34;dayOfWeek&#34;: [day_of_week_list[week_day]]
                    }
                )
        else:
            response_string = &#34;Both start_time and end_time should be of same type.&#34;
            raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
        payload = {
            &#34;operationWindow&#34;: {
                &#34;ruleEnabled&#34;: True,
                &#34;doNotSubmitJob&#34;: do_not_submit_job,
                &#34;startDate&#34;: start_date,
                &#34;endDate&#34;: end_date,
                &#34;name&#34;: name,
                &#34;ruleId&#34;: int(self.rule_id),
                &#34;operations&#34;: operations_list,
                &#34;dayTime&#34;: daytime_list
            },
            &#34;entity&#34;: {
                &#34;clientGroupId&#34;: int(self._clientgroup_id),
                &#34;clientId&#34;: int(self._client_id),
                &#34;applicationId&#34;: int(self._agent_id),
                &#34;instanceId&#34;: int(self._instance_id),
                &#34;backupsetId&#34;: int(self._backupset_id),
                &#34;subclientId&#34;: int(self._subclient_id)
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._operation_window, payload=payload)
        if flag:
            if response.json():
                error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
                if int(error_code) == 0:
                    int(response.json().get(&#39;operationWindow&#39;, {}).get(&#39;ruleId&#39;))
                    self._refresh()
                else:
                    raise SDKException(&#39;OperationWindow&#39;, &#39;105&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _refresh(self):
        &#34;&#34;&#34;Refreshes the properties of a rule&#34;&#34;&#34;
        self._get_rule_properties()

    def _get_rule_properties(self):
        &#34;&#34;&#34;
        Assigns the properties of an operation rule by getting the rule using rule id
        &#34;&#34;&#34;
        xml = &#34;&lt;Api_GetOperationWindowReq&gt;&lt;ruleId&gt;&#34; + str(self.rule_id) + &#34;&lt;/ruleId&gt;&lt;/Api_GetOperationWindowReq&gt;&#34;
        response_json = self._commcell_object._qoperation_execute(xml)
        if response_json:
            error_code = response_json.get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
            if int(error_code) == 0:
                response_json = response_json.get(&#39;operationWindow&#39;, {})[0]
                self._do_not_submit_job = response_json.get(&#39;doNotSubmitJob&#39;)
                self._name = response_json.get(&#39;name&#39;)
                self._start_date = response_json.get(&#39;startDate&#39;)
                self._end_date = response_json.get(&#39;endDate&#39;)
                operations = response_json.get(&#39;operations&#39;)
                operation_reverse_mapping = {value: key for key, value in OPERATION_MAPPING.items()}
                self._operations = [operation_reverse_mapping[operation] for operation in operations]
                week_of_the_month = response_json.get(&#34;dayTime&#34;, [{}])[0].get(&#39;weekOfTheMonth&#39;, [])
                if len(response_json.get(&#34;dayTime&#34;, [])) == 1:
                    start_time = response_json.get(&#34;dayTime&#34;, [{}])[0].get(&#39;startTime&#39;)
                    end_time = response_json.get(&#34;dayTime&#34;, [{}])[0].get(&#39;endTime&#39;)
                    day_of_week = response_json.get(&#34;dayTime&#34;, [{}])[0].get(&#39;dayOfWeek&#39;)
                else:
                    day_of_week = []
                    start_time = []
                    end_time = []
                    for week_day in response_json.get(&#34;dayTime&#34;, [{}]):
                        if week_day.get(&#34;dayOfWeek&#34;):
                            day_of_week.append(week_day.get(&#34;dayOfWeek&#34;)[0])
                        if week_day.get(&#34;startTime&#34;) is not None:
                            start_time.append(week_day.get(&#34;startTime&#34;))
                        if week_day.get(&#34;endTime&#34;) is not None:
                            end_time.append(week_day.get(&#34;endTime&#34;))
                wotm_reverse_mapping = {value: key for key, value in WEEK_OF_THE_MONTH_MAPPING.items()}
                self._week_of_the_month = [wotm_reverse_mapping[week] for week in week_of_the_month]
                self._day_of_week = [DAY_OF_WEEK_MAPPING[day] for day in day_of_week]
                self._start_time = start_time
                self._end_time = end_time
            else:
                raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;,
                                   response_json.get(&#34;error&#34;, {}).get(&#39;errorMessage&#39;))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def do_not_submit_job(self):
        &#34;&#34;&#34;Treats do_not_submit_job as a read-only attribute.&#34;&#34;&#34;
        return self._do_not_submit_job

    @do_not_submit_job.setter
    def do_not_submit_job(self, do_not_submit_job):
        &#34;&#34;&#34;
        Modifies do_not_submit_job of the operation rule
        Args:
             do_not_submit_job: (bool) -- do_not_submit_job of the operation rule to be modified&#34;&#34;&#34;
        self.modify_operation_window(do_not_submit_job=do_not_submit_job)

    @property
    def name(self):
        &#34;&#34;&#34;Treats name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        &#34;&#34;&#34;
        Modifies the name of the operation rule
        Args:
             name: (str) --Name of the operation rule to be modified
        &#34;&#34;&#34;
        self.modify_operation_window(name=name)

    @property
    def start_date(self):
        &#34;&#34;&#34;Treats start_date as a read-only attribute.&#34;&#34;&#34;
        return self._start_date

    @start_date.setter
    def start_date(self, start_date):
        &#34;&#34;&#34;
        Modifies the start_date of the operation rule
        Args:
            start_date: (int) --The start date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(start_date=start_date)

    @property
    def end_date(self):
        &#34;&#34;&#34;Treats end_date as a read-only attribute.&#34;&#34;&#34;
        return self._end_date

    @end_date.setter
    def end_date(self, end_date):
        &#34;&#34;&#34;
        Modifies the end_date of the operation rule
        Args:
            end_date: (int)   -- The end date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(end_date=end_date)

    @property
    def operations(self):
        &#34;&#34;&#34;Treats opearations as a read-only attribute.&#34;&#34;&#34;
        return self._operations

    @operations.setter
    def operations(self, operations):
        &#34;&#34;&#34;
        Modifies the operations of the operation rule
        Args:
            operations: (list)         --   List of operations for which the operation
                                               window is created
                    Acceptable Values:
                        FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
                        DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
                        SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
                        OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
                        MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION

        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(operations=operations)

    @property
    def week_of_the_month(self):
        &#34;&#34;&#34;Treats week_of_the_month as a read-only attribute.&#34;&#34;&#34;
        return self._week_of_the_month

    @week_of_the_month.setter
    def week_of_the_month(self, week_of_the_month):
        &#34;&#34;&#34;
        Modifies the week_of_the_month of the operation rule
        Args:
            week_of_the_month: (list)         --   List of week of the month on which the operation rule applies to
                     Acceptable Values:
                            all/first/second/third/fourth/fifth
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(week_of_the_month=week_of_the_month)

    @property
    def day_of_week(self):
        &#34;&#34;&#34;Treats day_of_week as a read-only attribute.&#34;&#34;&#34;
        return self._day_of_week

    @day_of_week.setter
    def day_of_week(self, day_of_week):
        &#34;&#34;&#34;
        Modifies the day_of_week of the operation rule
        Args:
            day_of_week: (list)    -- List of days of the week on which the operation rule applies to
                    Acceptable Values:
                        sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(day_of_week=day_of_week)

    @property
    def start_time(self):
        &#34;&#34;&#34;Treats start_time as a read-only attribute.&#34;&#34;&#34;
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        &#34;&#34;&#34;
        Modifies the start_time of the operation rule
        Args:
            start_time: (int)     -- The start time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                        (list)    -- The list of start timestamps for each weekday mentioned
                    in the day_of_week list.
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(start_time=start_time)

    @property
    def end_time(self):
        &#34;&#34;&#34;Treats end_time as a read-only attribute.&#34;&#34;&#34;
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        &#34;&#34;&#34;
        Modifies the end_time of the operation rule
        Args:
            end_time: (int)     -- The end time for the &#34;do not run&#34; interval.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                      (list)    -- The list of end timestamps for each weekday mentioned
                    in the day_of_week list.
        Returns: None
        &#34;&#34;&#34;
        self.modify_operation_window(end_time=end_time)

    @property
    def rule_id(self):
        &#34;&#34;&#34;Treats rule_id as read-only attribute&#34;&#34;&#34;
        return self._rule_id

    @property
    def commcell_id(self):
        &#34;&#34;&#34;Treats the commcell id as a read-only attribute.&#34;&#34;&#34;
        return self._commcell_id

    @property
    def clientgroup_id(self):
        &#34;&#34;&#34;Treats the client group id as a read-only attribute.&#34;&#34;&#34;
        return self._clientgroup_id

    @property
    def client_id(self):
        &#34;&#34;&#34;Treats the client id as a read-only attribute.&#34;&#34;&#34;
        return self._client_id

    @property
    def agent_id(self):
        &#34;&#34;&#34;Treats the agent id as a read-only attribute.&#34;&#34;&#34;
        return self._agent_id

    @property
    def instance_id(self):
        &#34;&#34;&#34;Treats the instance id as a read-only attribute.&#34;&#34;&#34;
        return self._instance_id

    @property
    def backupset_id(self):
        &#34;&#34;&#34;Treats the backupset id as a read-only attribute.&#34;&#34;&#34;
        return self._backupset_id

    @property
    def subclient_id(self):
        &#34;&#34;&#34;Treats the sub client id as a read-only attribute.&#34;&#34;&#34;
        return self._subclient_id

    @property
    def entity_level(self):
        &#34;&#34;&#34;Treats the entity level as a read-only attribute.&#34;&#34;&#34;
        return self._entity_level</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.operation_window.OperationWindowDetails.agent_id"><code class="name">var <span class="ident">agent_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the agent id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1032-L1035" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def agent_id(self):
    &#34;&#34;&#34;Treats the agent id as a read-only attribute.&#34;&#34;&#34;
    return self._agent_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.backupset_id"><code class="name">var <span class="ident">backupset_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the backupset id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1042-L1045" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backupset_id(self):
    &#34;&#34;&#34;Treats the backupset id as a read-only attribute.&#34;&#34;&#34;
    return self._backupset_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.client_id"><code class="name">var <span class="ident">client_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the client id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1027-L1030" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_id(self):
    &#34;&#34;&#34;Treats the client id as a read-only attribute.&#34;&#34;&#34;
    return self._client_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.clientgroup_id"><code class="name">var <span class="ident">clientgroup_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the client group id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1022-L1025" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def clientgroup_id(self):
    &#34;&#34;&#34;Treats the client group id as a read-only attribute.&#34;&#34;&#34;
    return self._clientgroup_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.commcell_id"><code class="name">var <span class="ident">commcell_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the commcell id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1017-L1020" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def commcell_id(self):
    &#34;&#34;&#34;Treats the commcell id as a read-only attribute.&#34;&#34;&#34;
    return self._commcell_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.day_of_week"><code class="name">var <span class="ident">day_of_week</span></code></dt>
<dd>
<div class="desc"><p>Treats day_of_week as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L959-L962" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def day_of_week(self):
    &#34;&#34;&#34;Treats day_of_week as a read-only attribute.&#34;&#34;&#34;
    return self._day_of_week</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.do_not_submit_job"><code class="name">var <span class="ident">do_not_submit_job</span></code></dt>
<dd>
<div class="desc"><p>Treats do_not_submit_job as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L860-L863" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def do_not_submit_job(self):
    &#34;&#34;&#34;Treats do_not_submit_job as a read-only attribute.&#34;&#34;&#34;
    return self._do_not_submit_job</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.end_date"><code class="name">var <span class="ident">end_date</span></code></dt>
<dd>
<div class="desc"><p>Treats end_date as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L903-L906" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def end_date(self):
    &#34;&#34;&#34;Treats end_date as a read-only attribute.&#34;&#34;&#34;
    return self._end_date</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.end_time"><code class="name">var <span class="ident">end_time</span></code></dt>
<dd>
<div class="desc"><p>Treats end_time as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L994-L997" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def end_time(self):
    &#34;&#34;&#34;Treats end_time as a read-only attribute.&#34;&#34;&#34;
    return self._end_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.entity_level"><code class="name">var <span class="ident">entity_level</span></code></dt>
<dd>
<div class="desc"><p>Treats the entity level as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1052-L1055" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_level(self):
    &#34;&#34;&#34;Treats the entity level as a read-only attribute.&#34;&#34;&#34;
    return self._entity_level</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.instance_id"><code class="name">var <span class="ident">instance_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the instance id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1037-L1040" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def instance_id(self):
    &#34;&#34;&#34;Treats the instance id as a read-only attribute.&#34;&#34;&#34;
    return self._instance_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Treats name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L873-L876" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Treats name as a read-only attribute.&#34;&#34;&#34;
    return self._name</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.operations"><code class="name">var <span class="ident">operations</span></code></dt>
<dd>
<div class="desc"><p>Treats opearations as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L919-L922" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def operations(self):
    &#34;&#34;&#34;Treats opearations as a read-only attribute.&#34;&#34;&#34;
    return self._operations</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.rule_id"><code class="name">var <span class="ident">rule_id</span></code></dt>
<dd>
<div class="desc"><p>Treats rule_id as read-only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1012-L1015" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def rule_id(self):
    &#34;&#34;&#34;Treats rule_id as read-only attribute&#34;&#34;&#34;
    return self._rule_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.start_date"><code class="name">var <span class="ident">start_date</span></code></dt>
<dd>
<div class="desc"><p>Treats start_date as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L887-L890" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def start_date(self):
    &#34;&#34;&#34;Treats start_date as a read-only attribute.&#34;&#34;&#34;
    return self._start_date</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.start_time"><code class="name">var <span class="ident">start_time</span></code></dt>
<dd>
<div class="desc"><p>Treats start_time as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L976-L979" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def start_time(self):
    &#34;&#34;&#34;Treats start_time as a read-only attribute.&#34;&#34;&#34;
    return self._start_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.subclient_id"><code class="name">var <span class="ident">subclient_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the sub client id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L1047-L1050" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclient_id(self):
    &#34;&#34;&#34;Treats the sub client id as a read-only attribute.&#34;&#34;&#34;
    return self._subclient_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.operation_window.OperationWindowDetails.week_of_the_month"><code class="name">var <span class="ident">week_of_the_month</span></code></dt>
<dd>
<div class="desc"><p>Treats week_of_the_month as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L942-L945" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def week_of_the_month(self):
    &#34;&#34;&#34;Treats week_of_the_month as a read-only attribute.&#34;&#34;&#34;
    return self._week_of_the_month</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.operation_window.OperationWindowDetails.modify_operation_window"><code class="name flex">
<span>def <span class="ident">modify_operation_window</span></span>(<span>self, **modify_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies the Operation rule.</p>
<h2 id="args">Args</h2>
<p>modify_options(dict)
&ndash; Arbitrary keyword arguments.</p>
<p>modify_options Args:
name
(str)
&ndash;
Name of the Operation rule</p>
<pre><code>start_date    (int)   -- The start date for the operation rule.
    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
    default - current date

end_date      (int)   -- The end date for the operation rule.
    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
    default - 365 days

operations (list)         --   List of operations for which the operation
                               window is created

    Acceptable Values:
        FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
        DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
        SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
        OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
        MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION

week_of_the_month(list)     -- List of week of the month on which the operation rule applies to
    Acceptable Values:
        all/first/second/third/fourth/last

        default - None

day_of_week (list)    -- List of days of the week on which the operation rule applies to
    Acceptable Values:
        sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday

    default- Weekdays

start_time  (int)     -- The start time for the "do not run" interval.
Valid values are UNIX-style timestamps (seconds since January 1, 1970).
default - 28800 (8 AM)
Must specify one timestamp for start time for all the weekdays, otherwise
make a list for each weekday mentioned in the day_of_week list.
</code></pre>
<p>start_time (list)
&ndash; The list of start timestamps for each weekday mentioned
in the day_of_week list.</p>
<p>end_time
(int)
&ndash; The end time for the "do not run" interval.
Valid values are UNIX-style timestamps (seconds since January 1, 1970).
default - 86400 (6 PM)
Must specify one timestamp for end time for all the weekdays, otherwise
make a list for each weekday mentioned in the day_of_week list.</p>
<p>end_time
(list)
&ndash; The list of end timestamps for each weekday mentioned
in the day_of_week list.</p>
<p>Example:
1. day_of_week : ["sunday", "thursday", "saturday"]
start_time
: 28800
end_time
: 86400
The above inputs specify that for all the three days mentioned, start_time and end_time of
operation window would be same
2. day_of_week : ["monday","friday"]
start_time
: [3600, 28800]
end_time
: [18000, 86400]
The above input specify that on monday operation window starts at 3600 and ends at 18000 whereas
on friday, the operation window starts at 28800 and ends at 86400</p>
<pre><code>do_not_submit_job   (bool)  -- doNotSubmitJob of the operation rule
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if the Operation window could not be Modified</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/operation_window.py#L647-L810" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_operation_window(self, **modify_options):
    &#34;&#34;&#34;Modifies the Operation rule.

        Args:
            modify_options(dict)  -- Arbitrary keyword arguments.

            modify_options Args:
                name          (str)   --  Name of the Operation rule

                start_date    (int)   -- The start date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - current date

                end_date      (int)   -- The end date for the operation rule.
                    Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                    default - 365 days

                operations (list)         --   List of operations for which the operation
                                               window is created

                    Acceptable Values:
                        FULL_DATA_MANAGEMENT/NON_FULL_DATA_MANAGEMENT/SYNTHETIC_FULL/
                        DATA_RECOVERY/AUX_COPY/DR_BACKUP/DATA_VERIFICATION/ERASE_SPARE_MEDIA/
                        SHELF_MANAGEMENT/DELETE_DATA_BY_BROWSING/DELETE_ARCHIVED_DATA/
                        OFFLINE_CONTENT_INDEXING/ONLINE_CONTENT_INDEXING/SRM/INFORMATION_MANAGEMENT/
                        MEDIA_REFRESHING/DATA_ANALYTICS/DATA_PRUNING/BACKUP_COPY/CLEANUP_OPERATION

                week_of_the_month(list)     -- List of week of the month on which the operation rule applies to
                    Acceptable Values:
                        all/first/second/third/fourth/last

                        default - None

                day_of_week (list)    -- List of days of the week on which the operation rule applies to
                    Acceptable Values:
                        sunday/ monday/ tuesday/ wednesday/ thursday/ friday/ saturday

                    default- Weekdays

                start_time  (int)     -- The start time for the &#34;do not run&#34; interval.
                Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                default - 28800 (8 AM)
                Must specify one timestamp for start time for all the weekdays, otherwise
                make a list for each weekday mentioned in the day_of_week list.

            start_time (list)    -- The list of start timestamps for each weekday mentioned
                in the day_of_week list.

            end_time    (int)     -- The end time for the &#34;do not run&#34; interval.
                Valid values are UNIX-style timestamps (seconds since January 1, 1970).
                default - 86400 (6 PM)
                Must specify one timestamp for end time for all the weekdays, otherwise
                make a list for each weekday mentioned in the day_of_week list.

            end_time   (list)    -- The list of end timestamps for each weekday mentioned
                in the day_of_week list.

            Example:
                1. day_of_week : [&#34;sunday&#34;, &#34;thursday&#34;, &#34;saturday&#34;]
                   start_time  : 28800
                   end_time    : 86400
                   The above inputs specify that for all the three days mentioned, start_time and end_time of
                   operation window would be same
                2. day_of_week : [&#34;monday&#34;,&#34;friday&#34;]
                   start_time  : [3600, 28800]
                   end_time    : [18000, 86400]
                   The above input specify that on monday operation window starts at 3600 and ends at 18000 whereas
                   on friday, the operation window starts at 28800 and ends at 86400

                do_not_submit_job   (bool)  -- doNotSubmitJob of the operation rule

        Raises:
            SDKException:
                if the Operation window could not be Modified

                if response is empty

                if response is not success


    &#34;&#34;&#34;
    start_date = modify_options.get(&#34;start_date&#34;, self.start_date)
    end_date = modify_options.get(&#34;end_date&#34;, self.end_date)
    start_time = modify_options.get(&#34;start_time&#34;, self.start_time)
    end_time = modify_options.get(&#34;end_time&#34;, self.end_time)
    name = modify_options.get(&#34;name&#34;, self.name)
    operations = modify_options.get(&#34;operations&#34;, self.operations)
    week_of_the_month = modify_options.get(&#34;week_of_the_month&#34;, self.week_of_the_month)
    day_of_week = modify_options.get(&#34;day_of_week&#34;, self.day_of_week)
    do_not_submit_job = modify_options.get(&#34;do_not_submit_job&#34;, self.do_not_submit_job)

    if not operations:
        # Empty list can be passed
        operations_list = operations
    else:
        operations_list = [OPERATION_MAPPING[operation.upper()] for operation in operations]

    week_of_the_month_list = []
    if week_of_the_month:
        week_of_the_month_list = [WEEK_OF_THE_MONTH_MAPPING[week.lower()] for week in week_of_the_month]

    day_of_week_list = [DAY_OF_WEEK_MAPPING.index(day.lower()) for day in day_of_week]
    daytime_list = []
    num_of_days = len(day_of_week_list)
    if isinstance(start_time, int) and isinstance(end_time, int):
        daytime_list.append(
            {
                &#34;startTime&#34;: start_time,
                &#34;endTime&#34;: end_time,
                &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                &#34;dayOfWeek&#34;: day_of_week_list
            }
        )
    elif isinstance(start_time, list) and isinstance(end_time, list):
        if not (num_of_days == len(start_time) == len(end_time)):
            response_string = &#34;did not specify start time and end time for all the given week days&#34;
            raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
        for week_day in range(num_of_days):
            daytime_list.append(
                {
                    &#34;startTime&#34;: start_time[week_day],
                    &#34;endTime&#34;: end_time[week_day],
                    &#34;weekOfTheMonth&#34;: week_of_the_month_list,
                    &#34;dayOfWeek&#34;: [day_of_week_list[week_day]]
                }
            )
    else:
        response_string = &#34;Both start_time and end_time should be of same type.&#34;
        raise SDKException(&#39;OperationWindow&#39;, &#39;102&#39;, response_string)
    payload = {
        &#34;operationWindow&#34;: {
            &#34;ruleEnabled&#34;: True,
            &#34;doNotSubmitJob&#34;: do_not_submit_job,
            &#34;startDate&#34;: start_date,
            &#34;endDate&#34;: end_date,
            &#34;name&#34;: name,
            &#34;ruleId&#34;: int(self.rule_id),
            &#34;operations&#34;: operations_list,
            &#34;dayTime&#34;: daytime_list
        },
        &#34;entity&#34;: {
            &#34;clientGroupId&#34;: int(self._clientgroup_id),
            &#34;clientId&#34;: int(self._client_id),
            &#34;applicationId&#34;: int(self._agent_id),
            &#34;instanceId&#34;: int(self._instance_id),
            &#34;backupsetId&#34;: int(self._backupset_id),
            &#34;subclientId&#34;: int(self._subclient_id)
        }
    }
    flag, response = self._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._operation_window, payload=payload)
    if flag:
        if response.json():
            error_code = response.json().get(&#34;error&#34;, {}).get(&#39;errorCode&#39;)
            if int(error_code) == 0:
                int(response.json().get(&#39;operationWindow&#39;, {}).get(&#39;ruleId&#39;))
                self._refresh()
            else:
                raise SDKException(&#39;OperationWindow&#39;, &#39;105&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#operationwindow">OperationWindow:</a></li>
<li><a href="#operationwindowdetails">OperationWindowDetails:</a></li>
<li><a href="#operationwindowdetails-instance-attributes">OperationWindowDetails Instance Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.operation_window.OperationWindow" href="#cvpysdk.operation_window.OperationWindow">OperationWindow</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.operation_window.OperationWindow.create_operation_window" href="#cvpysdk.operation_window.OperationWindow.create_operation_window">create_operation_window</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindow.delete_operation_window" href="#cvpysdk.operation_window.OperationWindow.delete_operation_window">delete_operation_window</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindow.get" href="#cvpysdk.operation_window.OperationWindow.get">get</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindow.list_operation_window" href="#cvpysdk.operation_window.OperationWindow.list_operation_window">list_operation_window</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.operation_window.OperationWindowDetails" href="#cvpysdk.operation_window.OperationWindowDetails">OperationWindowDetails</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.agent_id" href="#cvpysdk.operation_window.OperationWindowDetails.agent_id">agent_id</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.backupset_id" href="#cvpysdk.operation_window.OperationWindowDetails.backupset_id">backupset_id</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.client_id" href="#cvpysdk.operation_window.OperationWindowDetails.client_id">client_id</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.clientgroup_id" href="#cvpysdk.operation_window.OperationWindowDetails.clientgroup_id">clientgroup_id</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.commcell_id" href="#cvpysdk.operation_window.OperationWindowDetails.commcell_id">commcell_id</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.day_of_week" href="#cvpysdk.operation_window.OperationWindowDetails.day_of_week">day_of_week</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.do_not_submit_job" href="#cvpysdk.operation_window.OperationWindowDetails.do_not_submit_job">do_not_submit_job</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.end_date" href="#cvpysdk.operation_window.OperationWindowDetails.end_date">end_date</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.end_time" href="#cvpysdk.operation_window.OperationWindowDetails.end_time">end_time</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.entity_level" href="#cvpysdk.operation_window.OperationWindowDetails.entity_level">entity_level</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.instance_id" href="#cvpysdk.operation_window.OperationWindowDetails.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.modify_operation_window" href="#cvpysdk.operation_window.OperationWindowDetails.modify_operation_window">modify_operation_window</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.name" href="#cvpysdk.operation_window.OperationWindowDetails.name">name</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.operations" href="#cvpysdk.operation_window.OperationWindowDetails.operations">operations</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.rule_id" href="#cvpysdk.operation_window.OperationWindowDetails.rule_id">rule_id</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.start_date" href="#cvpysdk.operation_window.OperationWindowDetails.start_date">start_date</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.start_time" href="#cvpysdk.operation_window.OperationWindowDetails.start_time">start_time</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.subclient_id" href="#cvpysdk.operation_window.OperationWindowDetails.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.operation_window.OperationWindowDetails.week_of_the_month" href="#cvpysdk.operation_window.OperationWindowDetails.week_of_the_month">week_of_the_month</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>