<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.drorchestration.blr_pairs API documentation</title>
<meta name="description" content="Main file for performing BLR pair specific operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.drorchestration.blr_pairs</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing BLR pair specific operations.</p>
<p>BLRPairs and BLRPair are classes defined in this file.</p>
<p>BLRPairs:
Class for representing all the BLR pairs</p>
<pre><code>    PairStatus:     Enum for all possible pair status

    EndPointTypes:  Enum for all allowed end points for BLR pairs

    PendingStatus:  Enum for all pending status codes

    RecoveryType:   Enum of all possible recovery point types

    DROperations:   Enum of DR Operations that can be applied on the BLR pair
</code></pre>
<p>BLRPair:
Class for a single BLR Pair</p>
<h2 id="blrpairs">Blrpairs</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
Initialise object of BLRPairs class</p>
<p><strong>repr</strong>()
&ndash;
Returns the string for the instance of the
BLRPairs class</p>
<p>blr_pairs()
&ndash;
Returns a dictionary of BLR pair names mapping with their IDs</p>
<p>has_blr_pair(
source_name, destination_name)
&ndash;
Checks if BLR pair exists with the given source and
destination client name</p>
<p>create_fsblr_pair(source_client_id,
&ndash;
Creates FSBLR replication pair for given set of options
destination_client_id,
source_volumes,
destination_volumes,
recovery_type,
**kwargs)</p>
<p>get(source_name, destination_name)
&ndash;
Returns the BLRPair class object for the source and
destination client name</p>
<p>delete(source_name, destination_name)
&ndash;
Delete BLR pair with the source and
destination client name</p>
<p>refresh()
&ndash;
Refresh all BLR pairs created on the commcell</p>
<p>get_rpstore_id(rpstore_name)
&ndash;
Get the RPStore ID for the given name</p>
<p>get_rpstore_mountpath(rpstore_name)
&ndash;
Get the RPstore mounth path for the given name</p>
<h4 id="internal-methods">internal methods</h4>
<p>_update_data()
&ndash; REST API call to get all BLR pairs in the commcell</p>
<h2 id="blrpair">Blrpair</h2>
<p><strong>init</strong>(commcell_object, pair_name)
&ndash;
Initialise object of BLRPair class for pair name</p>
<p><strong>repr</strong>()
&ndash;
Returns the string for the instance of the
BLRPair class</p>
<p>pair_properties
&ndash;
Returns the properties of the pair</p>
<p>pair_status
&ndash;
Returns the status of the pair</p>
<p>source
&ndash;
Returns the dictionary for all source client properties</p>
<p>destination
&ndash;
Returns the dictionary for all destination client properties</p>
<p>lag_time
&ndash;
Returns the replication lag time in minutes</p>
<p>replication_group_name
&ndash; (VSABLR) Returns the replication group name of the pair</p>
<p>pending_status
&ndash;
Returns the reason for replication lag</p>
<p>pair_flag
&ndash;
Returns the integer for the pair's flag</p>
<p>subclient_props
&ndash;
Returns the properties of the subclient associated with the pair</p>
<p>pair_recovery_type
&ndash;
Returns the enum for recovery type of the pair</p>
<p>pair_rpstore_intervals
&ndash;
Returns the RPStore interval options set for the pair</p>
<p>pair_volume_map
&ndash;
(FSBLR) Returns the mapping of volumes for FSBLR pairs</p>
<p>pair_latest_stats
&ndash;
Returns the pair's latest data stats</p>
<p>get_pair_stats()
&ndash;
Returns the pair's data stats</p>
<p>get_recovery_point_stores()
&ndash;
Returns the RPstore points for the pair</p>
<p>create_replica_copy()
&ndash;
Creates a replica copy task and return the job/task ID</p>
<p>refresh()
&ndash;
Refresh the BLR pair properties</p>
<h4 id="internal-methods_1">internal methods</h4>
<p>_get_pair_id()
&ndash;
Returns the BLR pair ID from the BLR pairs dictionary</p>
<p>_get_pair_properties()
&ndash;
Returns the BLR pair properties</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L1-L1152" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing BLR pair specific operations.

BLRPairs and BLRPair are classes defined in this file.

BLRPairs:       Class for representing all the BLR pairs

        PairStatus:     Enum for all possible pair status

        EndPointTypes:  Enum for all allowed end points for BLR pairs

        PendingStatus:  Enum for all pending status codes

        RecoveryType:   Enum of all possible recovery point types

        DROperations:   Enum of DR Operations that can be applied on the BLR pair

BLRPair:        Class for a single BLR Pair


BLRPairs:
    __init__(commcell_object)                   --  Initialise object of BLRPairs class

    __repr__()                                  --  Returns the string for the instance of the
                                                    BLRPairs class

    blr_pairs()                                 --  Returns a dictionary of BLR pair names mapping with their IDs

    has_blr_pair(
            source_name, destination_name)      --  Checks if BLR pair exists with the given source and
                                                    destination client name

    create_fsblr_pair(source_client_id,         --  Creates FSBLR replication pair for given set of options
            destination_client_id,
            source_volumes,
            destination_volumes,
            recovery_type,
            **kwargs)

    get(source_name, destination_name)          --  Returns the BLRPair class object for the source and
                                                    destination client name

    delete(source_name, destination_name)       --  Delete BLR pair with the source and
                                                    destination client name

    refresh()                                   --  Refresh all BLR pairs created on the commcell

    get_rpstore_id(rpstore_name)                --  Get the RPStore ID for the given name

    get_rpstore_mountpath(rpstore_name)         --  Get the RPstore mounth path for the given name

    #### internal methods ###
    _update_data()                              -- REST API call to get all BLR pairs in the commcell

BLRPair:
    __init__(commcell_object, pair_name)    --  Initialise object of BLRPair class for pair name

    __repr__()                                  --  Returns the string for the instance of the
                                                    BLRPair class

    pair_properties                             --  Returns the properties of the pair

    pair_status                                 --  Returns the status of the pair

    source                                      --  Returns the dictionary for all source client properties

    destination                                 --  Returns the dictionary for all destination client properties

    lag_time                                    --  Returns the replication lag time in minutes

    replication_group_name                      -- (VSABLR) Returns the replication group name of the pair

    pending_status                              --  Returns the reason for replication lag

    pair_flag                                   --  Returns the integer for the pair&#39;s flag

    subclient_props                             --  Returns the properties of the subclient associated with the pair

    pair_recovery_type                          --  Returns the enum for recovery type of the pair

    pair_rpstore_intervals                      --  Returns the RPStore interval options set for the pair

    pair_volume_map                             --  (FSBLR) Returns the mapping of volumes for FSBLR pairs

    pair_latest_stats                           --  Returns the pair&#39;s latest data stats

    get_pair_stats()                            --  Returns the pair&#39;s data stats

    get_recovery_point_stores()                 --  Returns the RPstore points for the pair

    create_replica_copy()                       --  Creates a replica copy task and return the job/task ID

    refresh()                                   --  Refresh the BLR pair properties

    #### internal methods ###
    _get_pair_id()                              --  Returns the BLR pair ID from the BLR pairs dictionary

    _get_pair_properties()                      --  Returns the BLR pair properties
&#34;&#34;&#34;
import time

from cvpysdk.job import Job
from cvpysdk.schedules import Schedules

from ..exception import SDKException
from enum import Enum
import xmltodict


class BLRPairs:
    &#34;&#34;&#34;Class for getting all BLR pairs in commcell.&#34;&#34;&#34;

    class PairStatus(Enum):
        NOT_SYNCED = 0
        BACKING_UP = 1
        RESTORING = 2
        RESYNCING = 3
        REPLICATING = 4
        SUSPENDED = 5
        STOPPED = 6
        VERIFYING = 7
        PROBLEM = 8
        FAILED = 9
        STARTING = 10
        STOPPING = 11
        SUSPENDING = 12
        RESUMING = 13
        FAILING_OVER = 14
        FAILOVER_FAILED = 15
        FAILOVER_DONE = 16
        FAILING_BACK = 17
        FAILBACK_FAILED = 18
        SWITCHING_ROLES = 19
        SWITCH_ROLES_FAILED = 20

    class EndPointTypes(Enum):
        VIRTUALIZATION = 1
        FILESYSTEM = 2
        DATABASE = 3

    class AgentTypes(Enum):
        VIRTUALIZATION = &#39;Virtual Server&#39;
        FILESYSTEM = &#39;File system&#39;
        DATABASE = &#39;Database&#39;

    class PendingStatus(Enum):
        NOLAG = 0

    class RecoveryType(Enum):
        LIVE = 1
        SNAPSHOT = 2
        GRANULAR = 3
        GRANULARV2 = 4

    class DROperations(Enum):
        TEST_BOOT = 1,
        VSA_FAIL_OVER = 2
        DELETE = 3
        PERMANENT_BOOT = 4
        TEST_BOOT_EXTEND = 5
        MANAGE_TEST_BOOT = 6
        TEST_FS_MOUNT = 7
        PERM_FS_MOUNT = 8
        TEST_FSMOUNT_EXTEND = 9
        MANAGE_TEST_MOUNT = 10
        VSA_FAIL_BACK = 11
        FS_FAIL_OVER = 12
        FS_FAIL_BACK = 13
        RESUME_VSA_FAILOVER = 14
        CANCEL_VSA_FAILOVER = 15
        RESUME_FS_FAILOVER = 16
        CANCEL_FS_FAILOVER = 17
        ABORT_VSA_FAILBACK = 18
        ABORT_FS_FAILBACK = 19

    def __init__(self, commcell_object, replication_group_name: str = &#39;&#39;):
        &#34;&#34;&#34;Initialize object of the BLR Pairs
            Args:
                commcell_object (Commcell)  --  instance of the Commcell class
                replication_group_name (str)--  Name of the replication group (only for VSA BLR)
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._replication_group_name = replication_group_name.lower()
        self._replication_group_id = None
        if self._replication_group_name:
            self._replication_group_id = self._get_replication_group_id()

        self._LIST_BLR_PAIRS = self._services[&#39;GET_BLR_PAIRS&#39;]

        self._DELETE_BLR = self._services[&#39;DELETE_BLR_PAIR&#39;]
        self._QEXEC = self._services[&#39;EXEC_QCOMMAND&#39;]

        self._site_info = None
        self._summary = None
        self._rpstore_list = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the BLR Pairs class.&#34;&#34;&#34;
        if not self._replication_group_name:
            return &#34;BLR Pairs for Commserv: &#39;{0}&#39;&#34;.format(self._commcell_object.commserv_name)
        else:
            return &#34;BLR Pairs for Replication group: &#39;{0}&#39;&#34;.format(self._replication_group_name)

    def _get_replication_group_id(self):
        &#34;&#34;&#34;Returns the replication group ID
            Args:
            Returns:
                replication_group_id (str):  The ID of the associated replication group
            Raises:
        &#34;&#34;&#34;
        if not self._replication_group_name:
            return &#39;&#39;
        return (self._commcell_object.replication_groups.replication_groups
                .get(self._replication_group_name, {}).get(&#39;id&#39;))

    def _update_data(self):
        &#34;&#34;&#34;REST API call for getting all the info for all pairs in the commcell.
            Args:
            Returns:
            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._LIST_BLR_PAIRS)
        if flag:
            if response.json():
                self._summary = response.json().get(&#39;summary&#39;, {})
                if self._replication_group_id:
                    self._site_info = [site_info for site_info in response.json().get(&#39;siteInfo&#39;, [])
                                       if str(site_info.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupId&#39;, 0))
                                       == self._replication_group_id]
                else:
                    self._site_info = response.json().get(&#39;siteInfo&#39;, [])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update_rpstorelist(self):
        &#34;&#34;&#34;REST API call for getting all the rp store in the commcell.
            Args:
            Returns:
            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        response = self._commcell_object.qoperation_execute(&#39;&lt;EVGui_GetLibraryListWCReq libraryType=&#34;RPSTORE&#34; /&gt;&#39;)

        if response and &#39;libraryList&#39; in response:
            self._rpstore_list = response.get(&#39;libraryList&#39;, [])
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def blr_pairs(self):
        &#34;&#34;&#34;REST API call for getting all the BLR pairs in the commcell.
            Args:

            Returns:
                dict - consists of all BLR pairs
                    {
                         &#34;blr_id_1&#34;: {
                             &#34;sourceName&#34;: &#34;vm1&#34;,
                             &#34;destinationName&#34;: &#34;DRvm1&#34;,
                             &#34;subclientName&#34;: &#34;BLR_vm1(&lt;guid&gt;)&#34;
                         },
                         &#34;blr_id_2&#34;: {
                             &#34;sourceName&#34;: &#34;vm1&#34;,
                             &#34;destinationName&#34;: &#34;DRvm1&#34;,
                             &#34;subclientName&#34;: &#34;BLRSC_vm1_DRvm1_E:&#34;
                         },
                    }
        &#34;&#34;&#34;
        pairs = {}
        for pair_row in self._site_info:
            pair_id = pair_row.get(&#39;id&#39;)
            if pair_id:
                pairs[str(pair_id)] = {
                    &#39;sourceName&#39;: pair_row.get(&#39;sourceName&#39;, &#39;&#39;),
                    &#39;destinationName&#39;: pair_row.get(&#39;destinationName&#39;, &#39;&#39;),
                    &#39;subclientName&#39;: pair_row.get(&#39;entity&#39;, {}).get(&#39;subclientName&#39;, &#39;&#39;)
                }
        return pairs

    def has_blr_pair(self, source_name, destination_name):
        &#34;&#34;&#34;Checks if BLR pair exists or not
            Args:
                source_name (str): Name of the source client
                destination_name (str): Name of the destination client
            Returns:
                bool - boolean output whether BLR pair exists or not

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(source_name, str) or not isinstance(destination_name, str):
            raise SDKException(&#39;BLRPairs&#39;, &#39;101&#39;)
        source_name = source_name.lower()
        destination_name = destination_name.lower()
        for _, pair_row in self.blr_pairs.items():
            if (source_name == pair_row.get(&#39;sourceName&#39;, &#39;&#39;).lower() and
                    destination_name == pair_row.get(&#39;destinationName&#39;, &#39;&#39;).lower()):
                return True
        else:
            return False

    def create_fsblr_pair(self,
                          source_client_id,
                          destination_client_id,
                          source_volumes,
                          destination_volumes,
                          recovery_type,
                          **kwargs):
        &#34;&#34;&#34;Creates a new FSBLR pair on the commcell with the specified options
            Args:
                source_client_id (str)      : The source client&#39;s ID
                destination_client_id (str) : The destination client&#39;s ID
                source_volumes (list)       : The list of all source volumes
                destination_volumes (list)  : The list of all destination volumes
                recovery_type (RecoveryType): The enum to specify what type of recovery pair is supposed to be
                **kwargs (dict)             : Only used for granular type FSBLR pairs
                    rpstore_id (str)        : The ID of the RPstore to be used
                    rpstore_name (str)      : The name of the RPStore
                    ccrp_interval (int)     : The number of minutes after which CCRP is taken
                    acrp_interval (int)     : The number of minutes after which ACRP is taken
                    max_rp_interval (int)   : The number of minutes after which RP store&#39;s retention is ended
                    rp_merge_delay (int)    : Merge recovery points older than time in minutes
                    rp_retention (int)      : The number of minutes for which RPstore is retained for
                    rpstore_switch_live(int): The time in minutes after which pair is switch to
                                                live if RPstore is offline
                    merge_only_off_peak(bool):Whether to merge RPstore only during off-peak time
        &#34;&#34;&#34;
        blr_options = {
            &#39;BlockReplication_BLRRecoveryOptions&#39;:
                {
                    &#39;@recoveryType&#39;: recovery_type.value,
                    &#39;granularV2&#39;: {
                        &#39;@ccrpInterval&#39;: kwargs.get(&#39;ccrp_interval&#39;, 300),
                        &#39;@acrpInterval&#39;: kwargs.get(&#39;acrp_interval&#39;, 0),
                        &#39;@maxRpInterval&#39;: kwargs.get(&#39;max_rp_interval&#39;, 21600),
                        &#39;@rpMergeDelay&#39;: kwargs.get(&#39;rp_merge_delay&#39;, 172800),
                        &#39;@rpRetention&#39;: kwargs.get(&#39;rp_retention&#39;, 604800),
                        &#39;@maxRpStoreOfflineTime&#39;: kwargs.get(&#39;rpstore_switch_live&#39;, 0),
                        &#39;@useOffPeakSchedule&#39;: int(kwargs.get(&#39;merge_only_off_peak&#39;, False)),
                    }},
        }
        if kwargs.get(&#39;rpstore_id&#39;) and kwargs.get(&#39;rpstore_name&#39;):
            granularv2 = blr_options[&#39;BlockReplication_BLRRecoveryOptions&#39;][&#39;granularV2&#39;]
            granularv2[&#39;@rpStoreId&#39;] = int(kwargs.get(&#39;rpstore_id&#39;, 0))
            granularv2[&#39;@rpStoreName&#39;] = kwargs.get(&#39;rpstore_name&#39;)
            blr_options[&#39;BlockReplication_BLRRecoveryOptions&#39;][&#39;granularV2&#39;] = granularv2

        source_client = self._commcell_object.clients.get(int(source_client_id))
        destination_client = self._commcell_object.clients.get(int(destination_client_id))
        source_client_volumes = source_client.get_mount_volumes(source_volumes)
        destination_client_volumes = destination_client.get_mount_volumes(destination_volumes)

        request_json = {
            &#34;destEndPointType&#34;: self.EndPointTypes.FILESYSTEM.value,
            &#34;blrRecoveryOpts&#34;: xmltodict.unparse(blr_options, short_empty_elements=True).replace(&#39;\n&#39;, &#39;&#39;),
            &#34;srcEndPointType&#34;: self.EndPointTypes.FILESYSTEM.value,
            &#34;srcDestVolumeMap&#34;: [],
            &#34;destEntity&#34;: {
                &#34;client&#34;: {
                    &#34;clientId&#34;: int(destination_client_id),
                    &#34;clientName&#34;: destination_client.client_name,
                    &#34;hasDrivesInPair&#34;: True,
                    &#34;tabLevel&#34;: &#34;level-0&#34;,
                    &#34;checked&#34;: True,
                }
            },
            &#34;sourceEntity&#34;: {
                &#34;client&#34;: {
                    &#34;clientId&#34;: int(source_client_id),
                    &#34;clientName&#34;: source_client.client_name,
                    &#34;hasDrivesInPair&#34;: True,
                    &#34;tabLevel&#34;: &#34;level-0&#34;,
                    &#34;checked&#34;: True,
                }
            }
        }
        for source, destination in zip(source_client_volumes, destination_client_volumes):
            request_json[&#39;srcDestVolumeMap&#39;].append({
                &#34;sourceVolumeGUID&#34;: source[&#39;guid&#39;],
                &#34;sourceVolume&#34;: source[&#39;accessPathList&#39;][0],
                &#34;destVolumeGUID&#34;: destination[&#39;guid&#39;],
                &#34;destVolume&#34;: destination[&#39;accessPathList&#39;][0],
                &#34;sourceVolumeSize&#34;: source[&#39;size&#39;],
                &#34;disabled&#34;: &#34;&#34;,
            })

        flag, response = (self._commcell_object._cvpysdk_object
                          .make_request(&#39;POST&#39;, self._services[&#39;CREATE_BLR_PAIR&#39;], request_json))

        if flag:
            if response and response.json():
                if response.json().get(&#39;errorCode&#39;, 0) != 0:
                    response_string = self._commcell_object._update_response_(
                        response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def get(self, source_name, destination_name):
        &#34;&#34;&#34;Get pair name on the basis of source and destination name and return pair object
        Args:
            source_name (str): Name of the source client
            destination_name (str): Name of the destination client
        Returns: BLRPair object for source and destination

        Raises:
            SDKException:
                if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(source_name, str) or not isinstance(destination_name, str):
            raise SDKException(&#39;BLRPairs&#39;, &#39;101&#39;)
        source_name = source_name.lower()
        destination_name = destination_name.lower()
        for _, pair_row in self.blr_pairs.items():
            if (source_name == pair_row.get(&#39;sourceName&#39;, &#39;&#39;).lower() and
                    destination_name == pair_row.get(&#39;destinationName&#39;, &#39;&#39;).lower()):
                return BLRPair(self._commcell_object, source_name, destination_name)
        else:
            raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;,
                               &#39;No BLR pair exists with source: &#34;{0}&#34; and destination: &#34;{1}&#34;&#39;
                               .format(source_name, destination_name))

    def delete(self, source_name, destination_name):
        &#34;&#34;&#34; Deletes the blr pair with source and destination names
        Args:
            source_name (str): Name of the source client
            destination_name (str): Name of the destination client
        Returns: BLRPair object for source and destination

        Raises:
            SDKException:
                if proper inputs are not provided
                if response is empty
                if response is not success
        &#34;&#34;&#34;
        if not isinstance(source_name, str) or not isinstance(destination_name, str):
            raise SDKException(&#39;BLRPairs&#39;, &#39;101&#39;)
        source_name = source_name.lower()
        destination_name = destination_name.lower()
        if self.has_blr_pair(source_name, destination_name):
            for pair_id, pair_row in self.blr_pairs.items():
                if (source_name == pair_row.get(&#39;sourceName&#39;, &#39;&#39;).lower() and
                        destination_name == pair_row.get(&#39;destinationName&#39;, &#39;&#39;).lower()):
                    flag, response = self._commcell_object._cvpysdk_object.make_request(
                        method=&#39;DELETE&#39;, url=self._DELETE_BLR % pair_id)
                    if flag:
                        if &#39;error&#39; in response.json():
                            error_message = response.json(
                            )[&#39;error&#39;][&#39;errorMessage&#39;]
                            o_str = &#39;Failed to delete Source: {0} and Destination: {1} \nError: &#34;{2}&#34;&#39; \
                                .format(source_name, destination_name, error_message)

                            raise SDKException(&#39;Response&#39;, &#39;101&#39;, o_str)
                        else:
                            self.refresh()
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    break
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;,
                               &#39;No BLR pair exists with source: &#34;{0}&#34; and destination: &#34;{1}&#34;&#39;
                               .format(source_name, destination_name))

    def refresh(self):
        &#34;&#34;&#34; Refresh the BLR pairs created in the commcell.
        Args:

        Returns:

        Raises:

        &#34;&#34;&#34;
        self._update_data()
        self._update_rpstorelist()

    def get_rpstore_id(self, rpstore_name):
        &#34;&#34;&#34;Gets the RPStore ID for the given name
            Args:
                rpstore_name (str)  : The name of the RP store
        &#34;&#34;&#34;

        for rpstore in self._rpstore_list:
            if rpstore.get(&#39;library&#39;, {}).get(&#39;libraryName&#39;) == rpstore_name and rpstore.get(&#39;MountPathList&#39;, &#39;&#39;):
                return str(rpstore.get(&#39;MountPathList&#39;)[0]
                           .get(&#39;rpStoreLibraryInfo&#39;, {}).get(&#39;rpStoreId&#39;, &#39;&#39;))
        else:
            raise SDKException(&#39;BLRPairs&#39;, &#39;103&#39;, f&#39;No RP Store found with name {rpstore_name}&#39;)

    def get_rpstore_mountpath(self, rpstore_name):
        &#34;&#34;&#34;Gets the RPStore mount path for the given name
            Args:
                rpstore_name (str)  : The name of the RP store
        &#34;&#34;&#34;

        for rpstore in self._rpstore_list:
            if rpstore.get(&#39;library&#39;, {}).get(&#39;libraryName&#39;) == rpstore_name:
                mount_path = str(rpstore.get(&#39;MountPathList&#39;, {})[0][&#39;mountPathName&#39;])
                return mount_path.split()[1].strip()
        else:
            raise SDKException(&#39;BLRPairs&#39;, &#39;103&#39;, f&#39;No RP Store found with name {rpstore_name}&#39;)


class BLRPair:
    class PairOperationsStatus(Enum):
        SUSPEND = BLRPairs.PairStatus.SUSPENDED
        START = BLRPairs.PairStatus.REPLICATING
        RESUME = BLRPairs.PairStatus.REPLICATING
        STOP = BLRPairs.PairStatus.STOPPED
        RESYNC = BLRPairs.PairStatus.RESYNCING

    def __init__(self, commcell_object, source_name, destination_name):
        &#34;&#34;&#34;Initialise the ReplicationGroup object for the given group name
            Args:
                commcell_object (Commcell)      --  instance of the Commcell class
                source_name (str)               --  Name of the source of BLR pair
                destination_name (str)          --  Name of the destination of BLR pair
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services

        self._source_name = source_name.lower()
        self._destination_name = destination_name.lower()
        self._pair_id = self._get_pair_id()
        self._pair_properties = None
        self._source_instance = None
        self._destination_instance = None

        self._GET_PAIR = self._services[&#39;GET_BLR_PAIR&#39;]
        self._PAIR_STATS = self._services[&#39;GET_BLR_PAIR_STATS&#39;]
        self._GRANULAR_RP_STORES = self._services[&#39;GRANULAR_BLR_POINTS&#39;]
        self._BOOT_DETAILS = self._services[&#39;BLR_BOOT_DETAILS&#39;]

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of the BLR pair&#34;&#34;&#34;
        representation_string = &#39;BLR pair class instance for pair: &#34;{0} -&gt; {1}&#34;&#39;
        return representation_string.format(self._source_name, self._destination_name)

    def __str__(self):
        &#34;&#34;&#34;String representation of the instance of BLR pair&#34;&#34;&#34;
        return f&#39;BLR Pair: {self._source_name} -&gt; {self._destination_name}&#39;

    def _get_pair_id(self):
        &#34;&#34;&#34; Gets BLR pair Id from the BLRPairs class&#34;&#34;&#34;
        for pair_id, blr_pair in self._commcell_object.blr_pairs.blr_pairs.items():
            if (blr_pair.get(&#39;sourceName&#39;).lower() == self._source_name and
                    blr_pair.get(&#39;destinationName&#39;).lower() == self._destination_name):
                return pair_id
        raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;)

    def _get_pair_properties(self):
        &#34;&#34;&#34; Gets BLR pair properties
            Args:

            Returns: Gets the BLR pair properties dictionary

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._GET_PAIR % self._pair_id)
        if flag:
            if response.json() and &#39;siteInfo&#39; in response.json():
                self._pair_properties = response.json().get(&#39;siteInfo&#39;)[0]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _perform_action(self, operation_type):
        &#34;&#34;&#34;Performs an action on BLR pair
            Args:
                operation_type (PairOperations): An enum of pair operations of BLRPair class
            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        status_to_be_set = operation_type.value
        site_info = [self._pair_properties.copy()]
        site_info[0][&#39;status&#39;] = status_to_be_set.value
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;,
                                                                            self._services[&#39;GET_BLR_PAIRS&#39;],
                                                                            {&#39;siteInfo&#39;: site_info})
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, -1)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def pair_properties(self):
        &#34;&#34;&#34;Returns a dictionary of the pair properties&#34;&#34;&#34;
        return self._pair_properties

    @property
    def pair_status(self):
        &#34;&#34;&#34;Returns the status of the pair according the to PairStatus enum&#34;&#34;&#34;
        self.refresh()
        return BLRPairs.PairStatus(self._pair_properties.get(&#39;status&#39;))

    @property
    def source(self):
        &#34;&#34;&#34;Returns: (dict) The properties of the source client
        eg: {
            name: &#39;clientName&#39;,
            client_id: &#39;client id&#39;
            proxy_client_id: &#39;head proxy_id&#39;,
            guid: &#39;client guid&#39;,
            endpoint: &#39;endpoint enum&#39;
        }
        &#34;&#34;&#34;
        return {
            &#34;name&#34;: self.pair_properties.get(&#39;sourceName&#39;),
            &#34;client_id&#34;: str(self.pair_properties.get(&#39;srcClientId&#39;)),
            &#34;proxy_client_id&#34;: str(self.pair_properties.get(&#39;headClientId&#39;)),
            &#34;guid&#34;: self.pair_properties.get(&#39;sourceGuid&#39;),
            &#34;endpoint&#34;: BLRPairs.EndPointTypes(self.pair_properties.get(&#39;srcEndPointType&#39;)),
        }

    @property
    def source_vm(self):
        &#34;&#34;&#34;Returns (str): the source VM name&#34;&#34;&#34;
        return self.source.get(&#39;name&#39;)

    @property
    def destination(self):
        &#34;&#34;&#34;Returns: (dict) The properties of the destination client
        eg: {
            name: &#39;clientName&#39;,
            proxy_client_id: &#39;tail proxy_id&#39;,
            guid: &#39;client guid&#39;,
            endpoint: &#39;endpoint enum&#39;
        }
        &#34;&#34;&#34;
        return {
            &#34;name&#34;: self.pair_properties.get(&#39;destinationName&#39;),
            &#34;client_id&#34;: str(self.pair_properties.get(&#39;destClientId&#39;)),
            &#34;proxy_client_id&#34;: str(self.pair_properties.get(&#39;tailClientId&#39;)),
            &#34;guid&#34;: self.pair_properties.get(&#39;destinationGuid&#39;),
            &#34;endpoint&#34;: BLRPairs.EndPointTypes(self.pair_properties.get(&#39;destEndPointType&#39;)),
        }

    @property
    def destination_vm(self):
        &#34;&#34;&#34;Returns (str): the destination VM name&#34;&#34;&#34;
        return self.destination.get(&#39;name&#39;)

    @property
    def lag_time(self):
        &#34;&#34;&#34;Returns: (int) The replication lag for pair in minutes&#34;&#34;&#34;
        return self.pair_properties.get(&#39;lagTime&#39;)

    @property
    def replication_group_name(self):
        &#34;&#34;&#34;Returns: (str) The name of the replication group&#34;&#34;&#34;
        return self.pair_properties.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupName&#39;)

    @property
    def pending_status(self):
        &#34;&#34;&#34;Returns: (enum) The pair pending Status&#34;&#34;&#34;
        return BLRPairs.PendingStatus(self.pair_properties.get(&#39;pendingStatusCode&#39;))

    @property
    def pair_flag(self):
        &#34;&#34;&#34;Returns: (int) The pair&#39;s flag status&#34;&#34;&#34;
        return self.pair_properties.get(&#39;flags&#39;)

    @property
    def subclient_props(self):
        &#34;&#34;&#34;Returns: (dict) The subclient associated with pair&#39;s properties
        eg: {
            &#34;subclientName&#34;: &#34;subclient name&#34;,
            &#34;subclientId&#34;: subclient ID,
            &#34;instanceId&#34;: instance ID,
            &#34;backupsetId&#34;: backupset ID,
            &#34;clientId&#34;: client ID
        }
        &#34;&#34;&#34;
        return self.pair_properties.get(&#39;entity&#39;)

    @property
    def pair_recovery_type(self):
        &#34;&#34;&#34;Returns: (enum) Returns whether the pair is granular or live&#34;&#34;&#34;
        return BLRPairs.RecoveryType(self.pair_properties.get(&#39;blrRecoveryOpts&#39;, {}).get(&#39;recoveryType&#39;))

    @property
    def pair_rpstore_intervals(self):
        &#34;&#34;&#34;Returns: (dict) A dictionary of intervals set for RPstores
        eg: {
            &#39;ccrpInterval&#39;: 15,
            &#39;maxRpStoreOfflineTime&#39;: 900,
            &#39;useOffPeakSchedule&#39;: True,
            &#39;acrpInterval&#39;: 3600,
            &#39;rpMergeDelay&#39;: 43200,
            &#39;maxRpInterval&#39;: 21600,
            &#39;rpStoreId&#39;: 0,
            &#39;rpRetention&#39;: 604800,
            &#39;rpStoreName&#39;: &#39;N/A&#39;
        }
        &#34;&#34;&#34;
        return self.pair_properties.get(&#39;blrRecoveryOpts&#39;, {}).get(&#39;granularV2&#39;, {})

    @property
    def pair_volume_map(self):
        &#34;&#34;&#34;Returns: (list) Returns a list of volume mappings for FSBLR
        eg: [{
            &#39;sourceVolumeGUID&#39;: &#39;F961A090-90B3-403A-8629-10203C81517F&#39;,
            &#39;destVolume&#39;: &#39;F:&#39;,
            &#39;destVolumeGUID&#39;: &#39;0A800478-57E2-42B2-80BA-F7BA1B2E0BE1&#39;,
            &#39;sourceVolume&#39;: &#39;E:&#39;
        }]
        &#34;&#34;&#34;
        return self.pair_properties.get(&#39;srcDestVolumeMap&#39;, [])

    @property
    def pair_latest_stats(self):
        &#34;&#34;&#34;Returns: (list) A list of dictionary of latest statistics for BLR pair
        eg: [{
            &#39;repDataDeltaActual&#39;: 226051,
            &#39;ioDelta&#39;: 303935,
            &#39;repSetSize&#39;: 10522460160,
            &#39;iopsDelta&#39;: 160,
            &#39;sizeInRpStore&#39;: 0,
            &#39;id&#39;: 14195,
            &#39;repDataDeltaComp&#39;: 226051,
            &#39;retention&#39;: 0,
            &#39;timeStamp&#39;: {&#39;time&#39;: 1622714743}
        }]
        &#34;&#34;&#34;
        return self.pair_properties.get(&#39;statsList&#39;)

    def get_pair_stats(self):
        &#34;&#34;&#34;Returns: (list) A list of dictionary of statistics for BLR pair
        eg: [{
            &#39;repDataDeltaActual&#39;: 226051,
            &#39;ioDelta&#39;: 303935,
            &#39;repSetSize&#39;: 10522460160,
            &#39;iopsDelta&#39;: 160,
            &#39;sizeInRpStore&#39;: 0,
            &#39;id&#39;: 14195,
            &#39;repDataDeltaComp&#39;: 226051,
            &#39;retention&#39;: 0,
            &#39;timeStamp&#39;: {&#39;time&#39;: 1622714743}
        }]
        &#34;&#34;&#34;
        flag, response = (self._commcell_object._cvpysdk_object
                          .make_request(&#39;GET&#39;, self._PAIR_STATS % self._pair_id))
        if flag:
            if response.json() and &#39;statsList&#39; in response.json():
                return response.json().get(&#39;statsList&#39;, [])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_recovery_point_stores(self):
        &#34;&#34;&#34;Returns a list of all recovery point stores for the BLR pair
            Args:

            Returns: Gets the BLR rpstores list

            Raises:
            SDKException:
            if response is empty

            if response is not success
        &#34;&#34;&#34;
        destination_client = self._commcell_object.clients.get(int(self.destination[&#39;client_id&#39;]))
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._GRANULAR_RP_STORES %
                                                                            (self.destination[&#39;proxy_client_id&#39;],
                                                                             str(self.subclient_props[&#39;subclientId&#39;]),
                                                                             destination_client.client_guid))
        if flag:
            if response.json() and &#39;vmScale&#39; in response.json():
                return response.json().get(&#39;vmScale&#39;, {}).get(&#39;restorePoints&#39;, [])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_test_failover_vms(self, boot_type: int = 1):
        &#34;&#34;&#34;Returns the BLRTestFailovers object for all test failover VMs of the BLR pair
        Args:
            boot_type (int): Refer to BLRTestFailover.BLRBootType
        Returns:
            BLRTestFailovers object
        &#34;&#34;&#34;
        boot_type = BLRTestFailovers.BLRBootType(boot_type)
        return BLRTestFailovers(self._commcell_object, self._pair_id, boot_type)

    def stop(self):
        &#34;&#34;&#34;Stops the BLR Pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.STOP)

    def start(self):
        &#34;&#34;&#34;Starts the BLR Pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.START)

    def resume(self):
        &#34;&#34;&#34;Resumes the BLR Pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.RESUME)

    def resync(self):
        &#34;&#34;&#34;Resyncs the BLR pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.RESYNC)

    def suspend(self):
        &#34;&#34;&#34;Suspends the BLR Pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.SUSPEND)

    def wait_for_pair_status(self, expected_status, timeout=30):
        &#34;&#34;&#34;
        Waits for the BLR pair to reach the expected status
            Args:
                expected_status (enum or str): Enum of PairStatus or string value for pair status
                timeout (int)                : The amount of time in minutes to wait for pair to
                                                    reach status before exiting
            Returns:
                True, if the expected status is met
                False, if the expected status was not met in given time
        &#34;&#34;&#34;
        if isinstance(expected_status, str):
            expected_status = BLRPairs.PairStatus[expected_status.upper()]
        start_time = time.time()
        self.refresh()

        while not self.pair_status == expected_status:
            time.sleep(30)

            if time.time() - start_time &gt; timeout * 60:
                break
        return self.pair_status == expected_status

    def create_replica_copy(self, destination_volumes, copy_volumes, timestamp=None):
        &#34;&#34;&#34;Perform the DR operation for the BLR pair
            Args:
                destination_volumes (list)  : The destination volumes list
                copy_volumes (list)         : The copy volumes list
                timestamp (int)             : The timestamp of the RPstore, only for granular pairs
            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        restore_point = None
        if self.pair_recovery_type == BLRPairs.RecoveryType.GRANULARV2:
            if timestamp is not None:
                restore_point = [replica_point for replica_point in self.get_recovery_point_stores()
                                 if int(replica_point[&#39;timeStamp&#39;]) == timestamp][0]
            else:
                restore_point = self.get_recovery_point_stores()[-1]

        destination_client = self._commcell_object.clients.get(int(self.destination[&#39;client_id&#39;]))
        destination_volumes = destination_client.get_mount_volumes(destination_volumes)
        copy_volumes = destination_client.get_mount_volumes(copy_volumes)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;&#34;,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4047
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: {
                                    &#34;auxcopyJobOption&#34;: {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;allCopies&#34;: True,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: False
                                    }
                                }
                            },
                            &#34;adminOpts&#34;: {
                                &#34;blockOperation&#34;: {
                                    &#34;operations&#34;: [
                                        {
                                            &#34;appId&#34;: int(self.subclient_props[&#39;subclientId&#39;]),
                                            &#34;opType&#34;: 8,
                                            &#34;dstProxyClientId&#34;: int(self.destination[&#39;client_id&#39;]),
                                            &#34;fsMountInfo&#34;: {
                                                &#34;doLiveMount&#34;: True,
                                                &#34;lifeTimeInSec&#34;: 7200,
                                                &#34;blrPairId&#34;: int(self._pair_id),
                                                &#34;mountPathPairs&#34;: [{
                                                    &#34;mountPath&#34;: copy[&#39;accessPathList&#39;][0],
                                                    &#34;srcPath&#34;: destination[&#39;accessPathList&#39;][0],
                                                    &#34;srcGuid&#34;: destination[&#39;guid&#39;],
                                                    &#34;dstGuid&#34;: copy[&#39;guid&#39;]} for destination, copy in
                                                    zip(destination_volumes, copy_volumes)],
                                                &#34;rp&#34;: {
                                                    &#34;timeStamp&#34;: int(restore_point[&#39;timeStamp&#39;]),
                                                    &#34;sequenceNumber&#34;: int(restore_point[&#39;sequenceNumber&#39;]),
                                                    &#34;rpType&#34;: 1,
                                                    &#34;appConsistent&#34;: False,
                                                    &#34;dataChangedSize&#34;: int(restore_point[&#39;dataChangedSize&#39;])
                                                } if restore_point is not None else None
                                            }
                                        }
                                    ]
                                }
                            },
                        }
                    }
                ]
            }
        }
        if restore_point is None:
            admin_opts = request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;]
            del admin_opts[&#39;blockOperation&#39;][&#39;operations&#39;][0][&#39;fsMountInfo&#39;][&#39;rp&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;,
                                                                            self._services[&#39;RESTORE&#39;],
                                                                            request_json)
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34; Refresh the BLR pair properties &#34;&#34;&#34;
        self._get_pair_properties()


class BLRTestFailovers:
    &#34;&#34;&#34;Class for getting all test failover VMs for BLR pair&#34;&#34;&#34;
    class BLRBootType(Enum):
        TESTBOOT = 1
        PERMANENT = 2

    class BootStatus(Enum):
        NONE = 0
        IN_PROGRESS = 1
        SUCCESS = 2
        FAILED = 3
        ABOUT_TO_EXPIRE = 4
        EXPIRED = 5
        USER_DELETED = 6
        DELETE_FAILED = 7
        PARTIAL_SUCCESS = 8

    def __init__(self, commcell_object, pair_id, boot_type: BLRBootType):
        &#34;&#34;&#34;Method for making initial data members
            Args:
                commcell_object (Commcell)      --  instance of the Commcell class
                pair_id (str)                   --  Id of the BLR pair
                boot_type (BLRBootType)         --  The type of the boot for VMs
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services

        self._pair_id = pair_id
        self._boot_type = boot_type
        self._boot_vm_dict = None

        self._GET_BOOT_VMS = self._services[&#39;BLR_BOOT_DETAILS&#39;]

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of BLRTestFailovers&#34;&#34;&#34;
        representation_string = &#39;BLR Test failover VMs class instance for pair id: {0}&#39;
        return representation_string.format(self._pair_id)

    def _get_test_failover_vms(self):
        &#34;&#34;&#34;Gets the list of all test failover VMs
            Args:
            Returns: Gets the BLR test failover VMs list
            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._GET_BOOT_VMS % (self._pair_id, self._boot_type.value))
        if flag:
            if response.json() and &#39;siteInfo&#39; in response.json():
                return response.json()[&#39;siteInfo&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the VMs list for BLR pair&#34;&#34;&#34;
        self._boot_vm_dict = {boot_dict.get(&#39;name&#39;): boot_dict
                              for boot_dict in self._get_test_failover_vms()}

    @property
    def all_boot_vms(self):
        &#34;&#34;&#34;Returns a list of all boot VMs&#34;&#34;&#34;
        return list(self._boot_vm_dict.keys())

    def get_boot_vm_details(self, vm_name):
        &#34;&#34;&#34;Get the boot VM details for a given vm name
        Args:
            vm_name (str): The name of the VM to fetch details for
        Returns:
            dict:  {
                id (int)                : The ID of the test failover VM
                uuid (str)              : The UUID of the test failover VM
                name (str)              : The name of the test failover VM
                creation_time (int)     : The timestamp of VM creation
                vm_status (BootStatus)  : The status of the test failover VM
                status_message (str)    : The description of the VM status
            }
        Raises:
            BLRPair not found: If test boot VM name is not found
        &#34;&#34;&#34;
        vm_dict = self._boot_vm_dict.get(vm_name)
        if not vm_dict:
            raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;, f&#39;BLR test boot VM with name {vm_name} not found&#39;)
        return {
            &#39;id&#39;: vm_dict.get(&#39;id&#39;),
            &#39;uuid&#39;: vm_dict.get(&#39;uuid&#39;),
            &#39;name&#39;: vm_dict.get(&#39;name&#39;),
            &#39;creation_time&#39;: vm_dict.get(&#39;creationTime&#39;),
            &#39;vm_status&#39;: self.BootStatus(vm_dict.get(&#39;status&#39;)) if vm_dict.get(&#39;status&#39;) else None,
            &#39;status_message&#39;: vm_dict.get(&#39;statusMessage&#39;),
            &#39;expiration_time&#39;: vm_dict.get(&#39;bestBefore&#39;)
        }</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair"><code class="flex name class">
<span>class <span class="ident">BLRPair</span></span>
<span>(</span><span>commcell_object, source_name, destination_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Initialise the ReplicationGroup object for the given group name</p>
<h2 id="args">Args</h2>
<p>commcell_object (Commcell)
&ndash;
instance of the Commcell class
source_name (str)
&ndash;
Name of the source of BLR pair
destination_name (str)
&ndash;
Name of the destination of BLR pair</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L537-L1051" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class BLRPair:
    class PairOperationsStatus(Enum):
        SUSPEND = BLRPairs.PairStatus.SUSPENDED
        START = BLRPairs.PairStatus.REPLICATING
        RESUME = BLRPairs.PairStatus.REPLICATING
        STOP = BLRPairs.PairStatus.STOPPED
        RESYNC = BLRPairs.PairStatus.RESYNCING

    def __init__(self, commcell_object, source_name, destination_name):
        &#34;&#34;&#34;Initialise the ReplicationGroup object for the given group name
            Args:
                commcell_object (Commcell)      --  instance of the Commcell class
                source_name (str)               --  Name of the source of BLR pair
                destination_name (str)          --  Name of the destination of BLR pair
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services

        self._source_name = source_name.lower()
        self._destination_name = destination_name.lower()
        self._pair_id = self._get_pair_id()
        self._pair_properties = None
        self._source_instance = None
        self._destination_instance = None

        self._GET_PAIR = self._services[&#39;GET_BLR_PAIR&#39;]
        self._PAIR_STATS = self._services[&#39;GET_BLR_PAIR_STATS&#39;]
        self._GRANULAR_RP_STORES = self._services[&#39;GRANULAR_BLR_POINTS&#39;]
        self._BOOT_DETAILS = self._services[&#39;BLR_BOOT_DETAILS&#39;]

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of the BLR pair&#34;&#34;&#34;
        representation_string = &#39;BLR pair class instance for pair: &#34;{0} -&gt; {1}&#34;&#39;
        return representation_string.format(self._source_name, self._destination_name)

    def __str__(self):
        &#34;&#34;&#34;String representation of the instance of BLR pair&#34;&#34;&#34;
        return f&#39;BLR Pair: {self._source_name} -&gt; {self._destination_name}&#39;

    def _get_pair_id(self):
        &#34;&#34;&#34; Gets BLR pair Id from the BLRPairs class&#34;&#34;&#34;
        for pair_id, blr_pair in self._commcell_object.blr_pairs.blr_pairs.items():
            if (blr_pair.get(&#39;sourceName&#39;).lower() == self._source_name and
                    blr_pair.get(&#39;destinationName&#39;).lower() == self._destination_name):
                return pair_id
        raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;)

    def _get_pair_properties(self):
        &#34;&#34;&#34; Gets BLR pair properties
            Args:

            Returns: Gets the BLR pair properties dictionary

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._GET_PAIR % self._pair_id)
        if flag:
            if response.json() and &#39;siteInfo&#39; in response.json():
                self._pair_properties = response.json().get(&#39;siteInfo&#39;)[0]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _perform_action(self, operation_type):
        &#34;&#34;&#34;Performs an action on BLR pair
            Args:
                operation_type (PairOperations): An enum of pair operations of BLRPair class
            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        status_to_be_set = operation_type.value
        site_info = [self._pair_properties.copy()]
        site_info[0][&#39;status&#39;] = status_to_be_set.value
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;,
                                                                            self._services[&#39;GET_BLR_PAIRS&#39;],
                                                                            {&#39;siteInfo&#39;: site_info})
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, -1)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def pair_properties(self):
        &#34;&#34;&#34;Returns a dictionary of the pair properties&#34;&#34;&#34;
        return self._pair_properties

    @property
    def pair_status(self):
        &#34;&#34;&#34;Returns the status of the pair according the to PairStatus enum&#34;&#34;&#34;
        self.refresh()
        return BLRPairs.PairStatus(self._pair_properties.get(&#39;status&#39;))

    @property
    def source(self):
        &#34;&#34;&#34;Returns: (dict) The properties of the source client
        eg: {
            name: &#39;clientName&#39;,
            client_id: &#39;client id&#39;
            proxy_client_id: &#39;head proxy_id&#39;,
            guid: &#39;client guid&#39;,
            endpoint: &#39;endpoint enum&#39;
        }
        &#34;&#34;&#34;
        return {
            &#34;name&#34;: self.pair_properties.get(&#39;sourceName&#39;),
            &#34;client_id&#34;: str(self.pair_properties.get(&#39;srcClientId&#39;)),
            &#34;proxy_client_id&#34;: str(self.pair_properties.get(&#39;headClientId&#39;)),
            &#34;guid&#34;: self.pair_properties.get(&#39;sourceGuid&#39;),
            &#34;endpoint&#34;: BLRPairs.EndPointTypes(self.pair_properties.get(&#39;srcEndPointType&#39;)),
        }

    @property
    def source_vm(self):
        &#34;&#34;&#34;Returns (str): the source VM name&#34;&#34;&#34;
        return self.source.get(&#39;name&#39;)

    @property
    def destination(self):
        &#34;&#34;&#34;Returns: (dict) The properties of the destination client
        eg: {
            name: &#39;clientName&#39;,
            proxy_client_id: &#39;tail proxy_id&#39;,
            guid: &#39;client guid&#39;,
            endpoint: &#39;endpoint enum&#39;
        }
        &#34;&#34;&#34;
        return {
            &#34;name&#34;: self.pair_properties.get(&#39;destinationName&#39;),
            &#34;client_id&#34;: str(self.pair_properties.get(&#39;destClientId&#39;)),
            &#34;proxy_client_id&#34;: str(self.pair_properties.get(&#39;tailClientId&#39;)),
            &#34;guid&#34;: self.pair_properties.get(&#39;destinationGuid&#39;),
            &#34;endpoint&#34;: BLRPairs.EndPointTypes(self.pair_properties.get(&#39;destEndPointType&#39;)),
        }

    @property
    def destination_vm(self):
        &#34;&#34;&#34;Returns (str): the destination VM name&#34;&#34;&#34;
        return self.destination.get(&#39;name&#39;)

    @property
    def lag_time(self):
        &#34;&#34;&#34;Returns: (int) The replication lag for pair in minutes&#34;&#34;&#34;
        return self.pair_properties.get(&#39;lagTime&#39;)

    @property
    def replication_group_name(self):
        &#34;&#34;&#34;Returns: (str) The name of the replication group&#34;&#34;&#34;
        return self.pair_properties.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupName&#39;)

    @property
    def pending_status(self):
        &#34;&#34;&#34;Returns: (enum) The pair pending Status&#34;&#34;&#34;
        return BLRPairs.PendingStatus(self.pair_properties.get(&#39;pendingStatusCode&#39;))

    @property
    def pair_flag(self):
        &#34;&#34;&#34;Returns: (int) The pair&#39;s flag status&#34;&#34;&#34;
        return self.pair_properties.get(&#39;flags&#39;)

    @property
    def subclient_props(self):
        &#34;&#34;&#34;Returns: (dict) The subclient associated with pair&#39;s properties
        eg: {
            &#34;subclientName&#34;: &#34;subclient name&#34;,
            &#34;subclientId&#34;: subclient ID,
            &#34;instanceId&#34;: instance ID,
            &#34;backupsetId&#34;: backupset ID,
            &#34;clientId&#34;: client ID
        }
        &#34;&#34;&#34;
        return self.pair_properties.get(&#39;entity&#39;)

    @property
    def pair_recovery_type(self):
        &#34;&#34;&#34;Returns: (enum) Returns whether the pair is granular or live&#34;&#34;&#34;
        return BLRPairs.RecoveryType(self.pair_properties.get(&#39;blrRecoveryOpts&#39;, {}).get(&#39;recoveryType&#39;))

    @property
    def pair_rpstore_intervals(self):
        &#34;&#34;&#34;Returns: (dict) A dictionary of intervals set for RPstores
        eg: {
            &#39;ccrpInterval&#39;: 15,
            &#39;maxRpStoreOfflineTime&#39;: 900,
            &#39;useOffPeakSchedule&#39;: True,
            &#39;acrpInterval&#39;: 3600,
            &#39;rpMergeDelay&#39;: 43200,
            &#39;maxRpInterval&#39;: 21600,
            &#39;rpStoreId&#39;: 0,
            &#39;rpRetention&#39;: 604800,
            &#39;rpStoreName&#39;: &#39;N/A&#39;
        }
        &#34;&#34;&#34;
        return self.pair_properties.get(&#39;blrRecoveryOpts&#39;, {}).get(&#39;granularV2&#39;, {})

    @property
    def pair_volume_map(self):
        &#34;&#34;&#34;Returns: (list) Returns a list of volume mappings for FSBLR
        eg: [{
            &#39;sourceVolumeGUID&#39;: &#39;F961A090-90B3-403A-8629-10203C81517F&#39;,
            &#39;destVolume&#39;: &#39;F:&#39;,
            &#39;destVolumeGUID&#39;: &#39;0A800478-57E2-42B2-80BA-F7BA1B2E0BE1&#39;,
            &#39;sourceVolume&#39;: &#39;E:&#39;
        }]
        &#34;&#34;&#34;
        return self.pair_properties.get(&#39;srcDestVolumeMap&#39;, [])

    @property
    def pair_latest_stats(self):
        &#34;&#34;&#34;Returns: (list) A list of dictionary of latest statistics for BLR pair
        eg: [{
            &#39;repDataDeltaActual&#39;: 226051,
            &#39;ioDelta&#39;: 303935,
            &#39;repSetSize&#39;: 10522460160,
            &#39;iopsDelta&#39;: 160,
            &#39;sizeInRpStore&#39;: 0,
            &#39;id&#39;: 14195,
            &#39;repDataDeltaComp&#39;: 226051,
            &#39;retention&#39;: 0,
            &#39;timeStamp&#39;: {&#39;time&#39;: 1622714743}
        }]
        &#34;&#34;&#34;
        return self.pair_properties.get(&#39;statsList&#39;)

    def get_pair_stats(self):
        &#34;&#34;&#34;Returns: (list) A list of dictionary of statistics for BLR pair
        eg: [{
            &#39;repDataDeltaActual&#39;: 226051,
            &#39;ioDelta&#39;: 303935,
            &#39;repSetSize&#39;: 10522460160,
            &#39;iopsDelta&#39;: 160,
            &#39;sizeInRpStore&#39;: 0,
            &#39;id&#39;: 14195,
            &#39;repDataDeltaComp&#39;: 226051,
            &#39;retention&#39;: 0,
            &#39;timeStamp&#39;: {&#39;time&#39;: 1622714743}
        }]
        &#34;&#34;&#34;
        flag, response = (self._commcell_object._cvpysdk_object
                          .make_request(&#39;GET&#39;, self._PAIR_STATS % self._pair_id))
        if flag:
            if response.json() and &#39;statsList&#39; in response.json():
                return response.json().get(&#39;statsList&#39;, [])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_recovery_point_stores(self):
        &#34;&#34;&#34;Returns a list of all recovery point stores for the BLR pair
            Args:

            Returns: Gets the BLR rpstores list

            Raises:
            SDKException:
            if response is empty

            if response is not success
        &#34;&#34;&#34;
        destination_client = self._commcell_object.clients.get(int(self.destination[&#39;client_id&#39;]))
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._GRANULAR_RP_STORES %
                                                                            (self.destination[&#39;proxy_client_id&#39;],
                                                                             str(self.subclient_props[&#39;subclientId&#39;]),
                                                                             destination_client.client_guid))
        if flag:
            if response.json() and &#39;vmScale&#39; in response.json():
                return response.json().get(&#39;vmScale&#39;, {}).get(&#39;restorePoints&#39;, [])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_test_failover_vms(self, boot_type: int = 1):
        &#34;&#34;&#34;Returns the BLRTestFailovers object for all test failover VMs of the BLR pair
        Args:
            boot_type (int): Refer to BLRTestFailover.BLRBootType
        Returns:
            BLRTestFailovers object
        &#34;&#34;&#34;
        boot_type = BLRTestFailovers.BLRBootType(boot_type)
        return BLRTestFailovers(self._commcell_object, self._pair_id, boot_type)

    def stop(self):
        &#34;&#34;&#34;Stops the BLR Pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.STOP)

    def start(self):
        &#34;&#34;&#34;Starts the BLR Pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.START)

    def resume(self):
        &#34;&#34;&#34;Resumes the BLR Pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.RESUME)

    def resync(self):
        &#34;&#34;&#34;Resyncs the BLR pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.RESYNC)

    def suspend(self):
        &#34;&#34;&#34;Suspends the BLR Pair
            Args:

            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        self._perform_action(self.PairOperationsStatus.SUSPEND)

    def wait_for_pair_status(self, expected_status, timeout=30):
        &#34;&#34;&#34;
        Waits for the BLR pair to reach the expected status
            Args:
                expected_status (enum or str): Enum of PairStatus or string value for pair status
                timeout (int)                : The amount of time in minutes to wait for pair to
                                                    reach status before exiting
            Returns:
                True, if the expected status is met
                False, if the expected status was not met in given time
        &#34;&#34;&#34;
        if isinstance(expected_status, str):
            expected_status = BLRPairs.PairStatus[expected_status.upper()]
        start_time = time.time()
        self.refresh()

        while not self.pair_status == expected_status:
            time.sleep(30)

            if time.time() - start_time &gt; timeout * 60:
                break
        return self.pair_status == expected_status

    def create_replica_copy(self, destination_volumes, copy_volumes, timestamp=None):
        &#34;&#34;&#34;Perform the DR operation for the BLR pair
            Args:
                destination_volumes (list)  : The destination volumes list
                copy_volumes (list)         : The copy volumes list
                timestamp (int)             : The timestamp of the RPstore, only for granular pairs
            Returns:

            Raises:
            SDKException:
            if response is empty
            if response is not success
        &#34;&#34;&#34;
        restore_point = None
        if self.pair_recovery_type == BLRPairs.RecoveryType.GRANULARV2:
            if timestamp is not None:
                restore_point = [replica_point for replica_point in self.get_recovery_point_stores()
                                 if int(replica_point[&#39;timeStamp&#39;]) == timestamp][0]
            else:
                restore_point = self.get_recovery_point_stores()[-1]

        destination_client = self._commcell_object.clients.get(int(self.destination[&#39;client_id&#39;]))
        destination_volumes = destination_client.get_mount_volumes(destination_volumes)
        copy_volumes = destination_client.get_mount_volumes(copy_volumes)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;&#34;,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4047
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: {
                                    &#34;auxcopyJobOption&#34;: {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;allCopies&#34;: True,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: False
                                    }
                                }
                            },
                            &#34;adminOpts&#34;: {
                                &#34;blockOperation&#34;: {
                                    &#34;operations&#34;: [
                                        {
                                            &#34;appId&#34;: int(self.subclient_props[&#39;subclientId&#39;]),
                                            &#34;opType&#34;: 8,
                                            &#34;dstProxyClientId&#34;: int(self.destination[&#39;client_id&#39;]),
                                            &#34;fsMountInfo&#34;: {
                                                &#34;doLiveMount&#34;: True,
                                                &#34;lifeTimeInSec&#34;: 7200,
                                                &#34;blrPairId&#34;: int(self._pair_id),
                                                &#34;mountPathPairs&#34;: [{
                                                    &#34;mountPath&#34;: copy[&#39;accessPathList&#39;][0],
                                                    &#34;srcPath&#34;: destination[&#39;accessPathList&#39;][0],
                                                    &#34;srcGuid&#34;: destination[&#39;guid&#39;],
                                                    &#34;dstGuid&#34;: copy[&#39;guid&#39;]} for destination, copy in
                                                    zip(destination_volumes, copy_volumes)],
                                                &#34;rp&#34;: {
                                                    &#34;timeStamp&#34;: int(restore_point[&#39;timeStamp&#39;]),
                                                    &#34;sequenceNumber&#34;: int(restore_point[&#39;sequenceNumber&#39;]),
                                                    &#34;rpType&#34;: 1,
                                                    &#34;appConsistent&#34;: False,
                                                    &#34;dataChangedSize&#34;: int(restore_point[&#39;dataChangedSize&#39;])
                                                } if restore_point is not None else None
                                            }
                                        }
                                    ]
                                }
                            },
                        }
                    }
                ]
            }
        }
        if restore_point is None:
            admin_opts = request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;]
            del admin_opts[&#39;blockOperation&#39;][&#39;operations&#39;][0][&#39;fsMountInfo&#39;][&#39;rp&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;,
                                                                            self._services[&#39;RESTORE&#39;],
                                                                            request_json)
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34; Refresh the BLR pair properties &#34;&#34;&#34;
        self._get_pair_properties()</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.PairOperationsStatus"><code class="name">var <span class="ident">PairOperationsStatus</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
</dl>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.destination"><code class="name">var <span class="ident">destination</span></code></dt>
<dd>
<div class="desc"><p>Returns: (dict) The properties of the destination client
eg: {
name: 'clientName',
proxy_client_id: 'tail proxy_id',
guid: 'client guid',
endpoint: 'endpoint enum'
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L674-L690" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination(self):
    &#34;&#34;&#34;Returns: (dict) The properties of the destination client
    eg: {
        name: &#39;clientName&#39;,
        proxy_client_id: &#39;tail proxy_id&#39;,
        guid: &#39;client guid&#39;,
        endpoint: &#39;endpoint enum&#39;
    }
    &#34;&#34;&#34;
    return {
        &#34;name&#34;: self.pair_properties.get(&#39;destinationName&#39;),
        &#34;client_id&#34;: str(self.pair_properties.get(&#39;destClientId&#39;)),
        &#34;proxy_client_id&#34;: str(self.pair_properties.get(&#39;tailClientId&#39;)),
        &#34;guid&#34;: self.pair_properties.get(&#39;destinationGuid&#39;),
        &#34;endpoint&#34;: BLRPairs.EndPointTypes(self.pair_properties.get(&#39;destEndPointType&#39;)),
    }</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.destination_vm"><code class="name">var <span class="ident">destination_vm</span></code></dt>
<dd>
<div class="desc"><p>Returns (str): the destination VM name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L692-L695" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_vm(self):
    &#34;&#34;&#34;Returns (str): the destination VM name&#34;&#34;&#34;
    return self.destination.get(&#39;name&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.lag_time"><code class="name">var <span class="ident">lag_time</span></code></dt>
<dd>
<div class="desc"><p>Returns: (int) The replication lag for pair in minutes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L697-L700" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def lag_time(self):
    &#34;&#34;&#34;Returns: (int) The replication lag for pair in minutes&#34;&#34;&#34;
    return self.pair_properties.get(&#39;lagTime&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_flag"><code class="name">var <span class="ident">pair_flag</span></code></dt>
<dd>
<div class="desc"><p>Returns: (int) The pair's flag status</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L712-L715" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pair_flag(self):
    &#34;&#34;&#34;Returns: (int) The pair&#39;s flag status&#34;&#34;&#34;
    return self.pair_properties.get(&#39;flags&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_latest_stats"><code class="name">var <span class="ident">pair_latest_stats</span></code></dt>
<dd>
<div class="desc"><p>Returns: (list) A list of dictionary of latest statistics for BLR pair
eg: [{
'repDataDeltaActual': 226051,
'ioDelta': 303935,
'repSetSize': 10522460160,
'iopsDelta': 160,
'sizeInRpStore': 0,
'id': 14195,
'repDataDeltaComp': 226051,
'retention': 0,
'timeStamp': {'time': 1622714743}
}]</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L764-L779" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pair_latest_stats(self):
    &#34;&#34;&#34;Returns: (list) A list of dictionary of latest statistics for BLR pair
    eg: [{
        &#39;repDataDeltaActual&#39;: 226051,
        &#39;ioDelta&#39;: 303935,
        &#39;repSetSize&#39;: 10522460160,
        &#39;iopsDelta&#39;: 160,
        &#39;sizeInRpStore&#39;: 0,
        &#39;id&#39;: 14195,
        &#39;repDataDeltaComp&#39;: 226051,
        &#39;retention&#39;: 0,
        &#39;timeStamp&#39;: {&#39;time&#39;: 1622714743}
    }]
    &#34;&#34;&#34;
    return self.pair_properties.get(&#39;statsList&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_properties"><code class="name">var <span class="ident">pair_properties</span></code></dt>
<dd>
<div class="desc"><p>Returns a dictionary of the pair properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L639-L642" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pair_properties(self):
    &#34;&#34;&#34;Returns a dictionary of the pair properties&#34;&#34;&#34;
    return self._pair_properties</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_recovery_type"><code class="name">var <span class="ident">pair_recovery_type</span></code></dt>
<dd>
<div class="desc"><p>Returns: (enum) Returns whether the pair is granular or live</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L730-L733" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pair_recovery_type(self):
    &#34;&#34;&#34;Returns: (enum) Returns whether the pair is granular or live&#34;&#34;&#34;
    return BLRPairs.RecoveryType(self.pair_properties.get(&#39;blrRecoveryOpts&#39;, {}).get(&#39;recoveryType&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_rpstore_intervals"><code class="name">var <span class="ident">pair_rpstore_intervals</span></code></dt>
<dd>
<div class="desc"><p>Returns: (dict) A dictionary of intervals set for RPstores
eg: {
'ccrpInterval': 15,
'maxRpStoreOfflineTime': 900,
'useOffPeakSchedule': True,
'acrpInterval': 3600,
'rpMergeDelay': 43200,
'maxRpInterval': 21600,
'rpStoreId': 0,
'rpRetention': 604800,
'rpStoreName': 'N/A'
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L735-L750" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pair_rpstore_intervals(self):
    &#34;&#34;&#34;Returns: (dict) A dictionary of intervals set for RPstores
    eg: {
        &#39;ccrpInterval&#39;: 15,
        &#39;maxRpStoreOfflineTime&#39;: 900,
        &#39;useOffPeakSchedule&#39;: True,
        &#39;acrpInterval&#39;: 3600,
        &#39;rpMergeDelay&#39;: 43200,
        &#39;maxRpInterval&#39;: 21600,
        &#39;rpStoreId&#39;: 0,
        &#39;rpRetention&#39;: 604800,
        &#39;rpStoreName&#39;: &#39;N/A&#39;
    }
    &#34;&#34;&#34;
    return self.pair_properties.get(&#39;blrRecoveryOpts&#39;, {}).get(&#39;granularV2&#39;, {})</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_status"><code class="name">var <span class="ident">pair_status</span></code></dt>
<dd>
<div class="desc"><p>Returns the status of the pair according the to PairStatus enum</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L644-L648" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pair_status(self):
    &#34;&#34;&#34;Returns the status of the pair according the to PairStatus enum&#34;&#34;&#34;
    self.refresh()
    return BLRPairs.PairStatus(self._pair_properties.get(&#39;status&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_volume_map"><code class="name">var <span class="ident">pair_volume_map</span></code></dt>
<dd>
<div class="desc"><p>Returns: (list) Returns a list of volume mappings for FSBLR
eg: [{
'sourceVolumeGUID': 'F961A090-90B3-403A-8629-10203C81517F',
'destVolume': 'F:',
'destVolumeGUID': '0A800478-57E2-42B2-80BA-F7BA1B2E0BE1',
'sourceVolume': 'E:'
}]</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L752-L762" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pair_volume_map(self):
    &#34;&#34;&#34;Returns: (list) Returns a list of volume mappings for FSBLR
    eg: [{
        &#39;sourceVolumeGUID&#39;: &#39;F961A090-90B3-403A-8629-10203C81517F&#39;,
        &#39;destVolume&#39;: &#39;F:&#39;,
        &#39;destVolumeGUID&#39;: &#39;0A800478-57E2-42B2-80BA-F7BA1B2E0BE1&#39;,
        &#39;sourceVolume&#39;: &#39;E:&#39;
    }]
    &#34;&#34;&#34;
    return self.pair_properties.get(&#39;srcDestVolumeMap&#39;, [])</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.pending_status"><code class="name">var <span class="ident">pending_status</span></code></dt>
<dd>
<div class="desc"><p>Returns: (enum) The pair pending Status</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L707-L710" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pending_status(self):
    &#34;&#34;&#34;Returns: (enum) The pair pending Status&#34;&#34;&#34;
    return BLRPairs.PendingStatus(self.pair_properties.get(&#39;pendingStatusCode&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.replication_group_name"><code class="name">var <span class="ident">replication_group_name</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) The name of the replication group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L702-L705" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def replication_group_name(self):
    &#34;&#34;&#34;Returns: (str) The name of the replication group&#34;&#34;&#34;
    return self.pair_properties.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.source"><code class="name">var <span class="ident">source</span></code></dt>
<dd>
<div class="desc"><p>Returns: (dict) The properties of the source client
eg: {
name: 'clientName',
client_id: 'client id'
proxy_client_id: 'head proxy_id',
guid: 'client guid',
endpoint: 'endpoint enum'
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L650-L667" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def source(self):
    &#34;&#34;&#34;Returns: (dict) The properties of the source client
    eg: {
        name: &#39;clientName&#39;,
        client_id: &#39;client id&#39;
        proxy_client_id: &#39;head proxy_id&#39;,
        guid: &#39;client guid&#39;,
        endpoint: &#39;endpoint enum&#39;
    }
    &#34;&#34;&#34;
    return {
        &#34;name&#34;: self.pair_properties.get(&#39;sourceName&#39;),
        &#34;client_id&#34;: str(self.pair_properties.get(&#39;srcClientId&#39;)),
        &#34;proxy_client_id&#34;: str(self.pair_properties.get(&#39;headClientId&#39;)),
        &#34;guid&#34;: self.pair_properties.get(&#39;sourceGuid&#39;),
        &#34;endpoint&#34;: BLRPairs.EndPointTypes(self.pair_properties.get(&#39;srcEndPointType&#39;)),
    }</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.source_vm"><code class="name">var <span class="ident">source_vm</span></code></dt>
<dd>
<div class="desc"><p>Returns (str): the source VM name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L669-L672" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def source_vm(self):
    &#34;&#34;&#34;Returns (str): the source VM name&#34;&#34;&#34;
    return self.source.get(&#39;name&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.subclient_props"><code class="name">var <span class="ident">subclient_props</span></code></dt>
<dd>
<div class="desc"><p>Returns: (dict) The subclient associated with pair's properties
eg: {
"subclientName": "subclient name",
"subclientId": subclient ID,
"instanceId": instance ID,
"backupsetId": backupset ID,
"clientId": client ID
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L717-L728" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclient_props(self):
    &#34;&#34;&#34;Returns: (dict) The subclient associated with pair&#39;s properties
    eg: {
        &#34;subclientName&#34;: &#34;subclient name&#34;,
        &#34;subclientId&#34;: subclient ID,
        &#34;instanceId&#34;: instance ID,
        &#34;backupsetId&#34;: backupset ID,
        &#34;clientId&#34;: client ID
    }
    &#34;&#34;&#34;
    return self.pair_properties.get(&#39;entity&#39;)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.create_replica_copy"><code class="name flex">
<span>def <span class="ident">create_replica_copy</span></span>(<span>self, destination_volumes, copy_volumes, timestamp=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Perform the DR operation for the BLR pair</p>
<h2 id="args">Args</h2>
<p>destination_volumes (list)
: The destination volumes list
copy_volumes (list)
: The copy volumes list
timestamp (int)
: The timestamp of the RPstore, only for granular pairs
Returns:</p>
<p>Raises:
SDKException:
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L932-L1047" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_replica_copy(self, destination_volumes, copy_volumes, timestamp=None):
    &#34;&#34;&#34;Perform the DR operation for the BLR pair
        Args:
            destination_volumes (list)  : The destination volumes list
            copy_volumes (list)         : The copy volumes list
            timestamp (int)             : The timestamp of the RPstore, only for granular pairs
        Returns:

        Raises:
        SDKException:
        if response is empty
        if response is not success
    &#34;&#34;&#34;
    restore_point = None
    if self.pair_recovery_type == BLRPairs.RecoveryType.GRANULARV2:
        if timestamp is not None:
            restore_point = [replica_point for replica_point in self.get_recovery_point_stores()
                             if int(replica_point[&#39;timeStamp&#39;]) == timestamp][0]
        else:
            restore_point = self.get_recovery_point_stores()[-1]

    destination_client = self._commcell_object.clients.get(int(self.destination[&#39;client_id&#39;]))
    destination_volumes = destination_client.get_mount_volumes(destination_volumes)
    copy_volumes = destination_client.get_mount_volumes(copy_volumes)

    request_json = {
        &#34;taskInfo&#34;: {
            &#34;task&#34;: {
                &#34;ownerId&#34;: 1,
                &#34;taskType&#34;: 1,
                &#34;ownerName&#34;: &#34;&#34;,
                &#34;initiatedFrom&#34;: 1,
                &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 4047
                    },
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {
                            &#34;mediaOpt&#34;: {
                                &#34;auxcopyJobOption&#34;: {
                                    &#34;maxNumberOfStreams&#34;: 0,
                                    &#34;allCopies&#34;: True,
                                    &#34;useMaximumStreams&#34;: True,
                                    &#34;useScallableResourceManagement&#34;: False
                                }
                            }
                        },
                        &#34;adminOpts&#34;: {
                            &#34;blockOperation&#34;: {
                                &#34;operations&#34;: [
                                    {
                                        &#34;appId&#34;: int(self.subclient_props[&#39;subclientId&#39;]),
                                        &#34;opType&#34;: 8,
                                        &#34;dstProxyClientId&#34;: int(self.destination[&#39;client_id&#39;]),
                                        &#34;fsMountInfo&#34;: {
                                            &#34;doLiveMount&#34;: True,
                                            &#34;lifeTimeInSec&#34;: 7200,
                                            &#34;blrPairId&#34;: int(self._pair_id),
                                            &#34;mountPathPairs&#34;: [{
                                                &#34;mountPath&#34;: copy[&#39;accessPathList&#39;][0],
                                                &#34;srcPath&#34;: destination[&#39;accessPathList&#39;][0],
                                                &#34;srcGuid&#34;: destination[&#39;guid&#39;],
                                                &#34;dstGuid&#34;: copy[&#39;guid&#39;]} for destination, copy in
                                                zip(destination_volumes, copy_volumes)],
                                            &#34;rp&#34;: {
                                                &#34;timeStamp&#34;: int(restore_point[&#39;timeStamp&#39;]),
                                                &#34;sequenceNumber&#34;: int(restore_point[&#39;sequenceNumber&#39;]),
                                                &#34;rpType&#34;: 1,
                                                &#34;appConsistent&#34;: False,
                                                &#34;dataChangedSize&#34;: int(restore_point[&#39;dataChangedSize&#39;])
                                            } if restore_point is not None else None
                                        }
                                    }
                                ]
                            }
                        },
                    }
                }
            ]
        }
    }
    if restore_point is None:
        admin_opts = request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;]
        del admin_opts[&#39;blockOperation&#39;][&#39;operations&#39;][0][&#39;fsMountInfo&#39;][&#39;rp&#39;]

    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;,
                                                                        self._services[&#39;RESTORE&#39;],
                                                                        request_json)
    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

            elif &#34;taskId&#34; in response.json():
                return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]

                o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;Job&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.get_pair_stats"><code class="name flex">
<span>def <span class="ident">get_pair_stats</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns: (list) A list of dictionary of statistics for BLR pair
eg: [{
'repDataDeltaActual': 226051,
'ioDelta': 303935,
'repSetSize': 10522460160,
'iopsDelta': 160,
'sizeInRpStore': 0,
'id': 14195,
'repDataDeltaComp': 226051,
'retention': 0,
'timeStamp': {'time': 1622714743}
}]</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L781-L805" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_pair_stats(self):
    &#34;&#34;&#34;Returns: (list) A list of dictionary of statistics for BLR pair
    eg: [{
        &#39;repDataDeltaActual&#39;: 226051,
        &#39;ioDelta&#39;: 303935,
        &#39;repSetSize&#39;: 10522460160,
        &#39;iopsDelta&#39;: 160,
        &#39;sizeInRpStore&#39;: 0,
        &#39;id&#39;: 14195,
        &#39;repDataDeltaComp&#39;: 226051,
        &#39;retention&#39;: 0,
        &#39;timeStamp&#39;: {&#39;time&#39;: 1622714743}
    }]
    &#34;&#34;&#34;
    flag, response = (self._commcell_object._cvpysdk_object
                      .make_request(&#39;GET&#39;, self._PAIR_STATS % self._pair_id))
    if flag:
        if response.json() and &#39;statsList&#39; in response.json():
            return response.json().get(&#39;statsList&#39;, [])
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.get_recovery_point_stores"><code class="name flex">
<span>def <span class="ident">get_recovery_point_stores</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a list of all recovery point stores for the BLR pair
Args:</p>
<p>Returns: Gets the BLR rpstores list</p>
<p>Raises:
SDKException:
if response is empty</p>
<p>if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L807-L832" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_recovery_point_stores(self):
    &#34;&#34;&#34;Returns a list of all recovery point stores for the BLR pair
        Args:

        Returns: Gets the BLR rpstores list

        Raises:
        SDKException:
        if response is empty

        if response is not success
    &#34;&#34;&#34;
    destination_client = self._commcell_object.clients.get(int(self.destination[&#39;client_id&#39;]))
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._GRANULAR_RP_STORES %
                                                                        (self.destination[&#39;proxy_client_id&#39;],
                                                                         str(self.subclient_props[&#39;subclientId&#39;]),
                                                                         destination_client.client_guid))
    if flag:
        if response.json() and &#39;vmScale&#39; in response.json():
            return response.json().get(&#39;vmScale&#39;, {}).get(&#39;restorePoints&#39;, [])
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.get_test_failover_vms"><code class="name flex">
<span>def <span class="ident">get_test_failover_vms</span></span>(<span>self, boot_type: int = 1)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the BLRTestFailovers object for all test failover VMs of the BLR pair</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>boot_type</code></strong> :&ensp;<code>int</code></dt>
<dd>Refer to BLRTestFailover.BLRBootType</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>BLRTestFailovers object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L834-L842" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_test_failover_vms(self, boot_type: int = 1):
    &#34;&#34;&#34;Returns the BLRTestFailovers object for all test failover VMs of the BLR pair
    Args:
        boot_type (int): Refer to BLRTestFailover.BLRBootType
    Returns:
        BLRTestFailovers object
    &#34;&#34;&#34;
    boot_type = BLRTestFailovers.BLRBootType(boot_type)
    return BLRTestFailovers(self._commcell_object, self._pair_id, boot_type)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the BLR pair properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L1049-L1051" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34; Refresh the BLR pair properties &#34;&#34;&#34;
    self._get_pair_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.resume"><code class="name flex">
<span>def <span class="ident">resume</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Resumes the BLR Pair
Args:</p>
<p>Returns:</p>
<p>Raises:
SDKException:
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L870-L881" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def resume(self):
    &#34;&#34;&#34;Resumes the BLR Pair
        Args:

        Returns:

        Raises:
        SDKException:
        if response is empty
        if response is not success
    &#34;&#34;&#34;
    self._perform_action(self.PairOperationsStatus.RESUME)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.resync"><code class="name flex">
<span>def <span class="ident">resync</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Resyncs the BLR pair
Args:</p>
<p>Returns:</p>
<p>Raises:
SDKException:
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L883-L894" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def resync(self):
    &#34;&#34;&#34;Resyncs the BLR pair
        Args:

        Returns:

        Raises:
        SDKException:
        if response is empty
        if response is not success
    &#34;&#34;&#34;
    self._perform_action(self.PairOperationsStatus.RESYNC)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.start"><code class="name flex">
<span>def <span class="ident">start</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts the BLR Pair
Args:</p>
<p>Returns:</p>
<p>Raises:
SDKException:
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L857-L868" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def start(self):
    &#34;&#34;&#34;Starts the BLR Pair
        Args:

        Returns:

        Raises:
        SDKException:
        if response is empty
        if response is not success
    &#34;&#34;&#34;
    self._perform_action(self.PairOperationsStatus.START)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.stop"><code class="name flex">
<span>def <span class="ident">stop</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Stops the BLR Pair
Args:</p>
<p>Returns:</p>
<p>Raises:
SDKException:
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L844-L855" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def stop(self):
    &#34;&#34;&#34;Stops the BLR Pair
        Args:

        Returns:

        Raises:
        SDKException:
        if response is empty
        if response is not success
    &#34;&#34;&#34;
    self._perform_action(self.PairOperationsStatus.STOP)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.suspend"><code class="name flex">
<span>def <span class="ident">suspend</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Suspends the BLR Pair
Args:</p>
<p>Returns:</p>
<p>Raises:
SDKException:
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L896-L907" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def suspend(self):
    &#34;&#34;&#34;Suspends the BLR Pair
        Args:

        Returns:

        Raises:
        SDKException:
        if response is empty
        if response is not success
    &#34;&#34;&#34;
    self._perform_action(self.PairOperationsStatus.SUSPEND)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPair.wait_for_pair_status"><code class="name flex">
<span>def <span class="ident">wait_for_pair_status</span></span>(<span>self, expected_status, timeout=30)</span>
</code></dt>
<dd>
<div class="desc"><p>Waits for the BLR pair to reach the expected status
Args:
expected_status (enum or str): Enum of PairStatus or string value for pair status
timeout (int)
: The amount of time in minutes to wait for pair to
reach status before exiting
Returns:
True, if the expected status is met
False, if the expected status was not met in given time</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L909-L930" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def wait_for_pair_status(self, expected_status, timeout=30):
    &#34;&#34;&#34;
    Waits for the BLR pair to reach the expected status
        Args:
            expected_status (enum or str): Enum of PairStatus or string value for pair status
            timeout (int)                : The amount of time in minutes to wait for pair to
                                                reach status before exiting
        Returns:
            True, if the expected status is met
            False, if the expected status was not met in given time
    &#34;&#34;&#34;
    if isinstance(expected_status, str):
        expected_status = BLRPairs.PairStatus[expected_status.upper()]
    start_time = time.time()
    self.refresh()

    while not self.pair_status == expected_status:
        time.sleep(30)

        if time.time() - start_time &gt; timeout * 60:
            break
    return self.pair_status == expected_status</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs"><code class="flex name class">
<span>class <span class="ident">BLRPairs</span></span>
<span>(</span><span>commcell_object, replication_group_name: str = '')</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all BLR pairs in commcell.</p>
<p>Initialize object of the BLR Pairs</p>
<h2 id="args">Args</h2>
<p>commcell_object (Commcell)
&ndash;
instance of the Commcell class
replication_group_name (str)&ndash;
Name of the replication group (only for VSA BLR)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L127-L534" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class BLRPairs:
    &#34;&#34;&#34;Class for getting all BLR pairs in commcell.&#34;&#34;&#34;

    class PairStatus(Enum):
        NOT_SYNCED = 0
        BACKING_UP = 1
        RESTORING = 2
        RESYNCING = 3
        REPLICATING = 4
        SUSPENDED = 5
        STOPPED = 6
        VERIFYING = 7
        PROBLEM = 8
        FAILED = 9
        STARTING = 10
        STOPPING = 11
        SUSPENDING = 12
        RESUMING = 13
        FAILING_OVER = 14
        FAILOVER_FAILED = 15
        FAILOVER_DONE = 16
        FAILING_BACK = 17
        FAILBACK_FAILED = 18
        SWITCHING_ROLES = 19
        SWITCH_ROLES_FAILED = 20

    class EndPointTypes(Enum):
        VIRTUALIZATION = 1
        FILESYSTEM = 2
        DATABASE = 3

    class AgentTypes(Enum):
        VIRTUALIZATION = &#39;Virtual Server&#39;
        FILESYSTEM = &#39;File system&#39;
        DATABASE = &#39;Database&#39;

    class PendingStatus(Enum):
        NOLAG = 0

    class RecoveryType(Enum):
        LIVE = 1
        SNAPSHOT = 2
        GRANULAR = 3
        GRANULARV2 = 4

    class DROperations(Enum):
        TEST_BOOT = 1,
        VSA_FAIL_OVER = 2
        DELETE = 3
        PERMANENT_BOOT = 4
        TEST_BOOT_EXTEND = 5
        MANAGE_TEST_BOOT = 6
        TEST_FS_MOUNT = 7
        PERM_FS_MOUNT = 8
        TEST_FSMOUNT_EXTEND = 9
        MANAGE_TEST_MOUNT = 10
        VSA_FAIL_BACK = 11
        FS_FAIL_OVER = 12
        FS_FAIL_BACK = 13
        RESUME_VSA_FAILOVER = 14
        CANCEL_VSA_FAILOVER = 15
        RESUME_FS_FAILOVER = 16
        CANCEL_FS_FAILOVER = 17
        ABORT_VSA_FAILBACK = 18
        ABORT_FS_FAILBACK = 19

    def __init__(self, commcell_object, replication_group_name: str = &#39;&#39;):
        &#34;&#34;&#34;Initialize object of the BLR Pairs
            Args:
                commcell_object (Commcell)  --  instance of the Commcell class
                replication_group_name (str)--  Name of the replication group (only for VSA BLR)
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._replication_group_name = replication_group_name.lower()
        self._replication_group_id = None
        if self._replication_group_name:
            self._replication_group_id = self._get_replication_group_id()

        self._LIST_BLR_PAIRS = self._services[&#39;GET_BLR_PAIRS&#39;]

        self._DELETE_BLR = self._services[&#39;DELETE_BLR_PAIR&#39;]
        self._QEXEC = self._services[&#39;EXEC_QCOMMAND&#39;]

        self._site_info = None
        self._summary = None
        self._rpstore_list = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the BLR Pairs class.&#34;&#34;&#34;
        if not self._replication_group_name:
            return &#34;BLR Pairs for Commserv: &#39;{0}&#39;&#34;.format(self._commcell_object.commserv_name)
        else:
            return &#34;BLR Pairs for Replication group: &#39;{0}&#39;&#34;.format(self._replication_group_name)

    def _get_replication_group_id(self):
        &#34;&#34;&#34;Returns the replication group ID
            Args:
            Returns:
                replication_group_id (str):  The ID of the associated replication group
            Raises:
        &#34;&#34;&#34;
        if not self._replication_group_name:
            return &#39;&#39;
        return (self._commcell_object.replication_groups.replication_groups
                .get(self._replication_group_name, {}).get(&#39;id&#39;))

    def _update_data(self):
        &#34;&#34;&#34;REST API call for getting all the info for all pairs in the commcell.
            Args:
            Returns:
            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._LIST_BLR_PAIRS)
        if flag:
            if response.json():
                self._summary = response.json().get(&#39;summary&#39;, {})
                if self._replication_group_id:
                    self._site_info = [site_info for site_info in response.json().get(&#39;siteInfo&#39;, [])
                                       if str(site_info.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupId&#39;, 0))
                                       == self._replication_group_id]
                else:
                    self._site_info = response.json().get(&#39;siteInfo&#39;, [])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update_rpstorelist(self):
        &#34;&#34;&#34;REST API call for getting all the rp store in the commcell.
            Args:
            Returns:
            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        response = self._commcell_object.qoperation_execute(&#39;&lt;EVGui_GetLibraryListWCReq libraryType=&#34;RPSTORE&#34; /&gt;&#39;)

        if response and &#39;libraryList&#39; in response:
            self._rpstore_list = response.get(&#39;libraryList&#39;, [])
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def blr_pairs(self):
        &#34;&#34;&#34;REST API call for getting all the BLR pairs in the commcell.
            Args:

            Returns:
                dict - consists of all BLR pairs
                    {
                         &#34;blr_id_1&#34;: {
                             &#34;sourceName&#34;: &#34;vm1&#34;,
                             &#34;destinationName&#34;: &#34;DRvm1&#34;,
                             &#34;subclientName&#34;: &#34;BLR_vm1(&lt;guid&gt;)&#34;
                         },
                         &#34;blr_id_2&#34;: {
                             &#34;sourceName&#34;: &#34;vm1&#34;,
                             &#34;destinationName&#34;: &#34;DRvm1&#34;,
                             &#34;subclientName&#34;: &#34;BLRSC_vm1_DRvm1_E:&#34;
                         },
                    }
        &#34;&#34;&#34;
        pairs = {}
        for pair_row in self._site_info:
            pair_id = pair_row.get(&#39;id&#39;)
            if pair_id:
                pairs[str(pair_id)] = {
                    &#39;sourceName&#39;: pair_row.get(&#39;sourceName&#39;, &#39;&#39;),
                    &#39;destinationName&#39;: pair_row.get(&#39;destinationName&#39;, &#39;&#39;),
                    &#39;subclientName&#39;: pair_row.get(&#39;entity&#39;, {}).get(&#39;subclientName&#39;, &#39;&#39;)
                }
        return pairs

    def has_blr_pair(self, source_name, destination_name):
        &#34;&#34;&#34;Checks if BLR pair exists or not
            Args:
                source_name (str): Name of the source client
                destination_name (str): Name of the destination client
            Returns:
                bool - boolean output whether BLR pair exists or not

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(source_name, str) or not isinstance(destination_name, str):
            raise SDKException(&#39;BLRPairs&#39;, &#39;101&#39;)
        source_name = source_name.lower()
        destination_name = destination_name.lower()
        for _, pair_row in self.blr_pairs.items():
            if (source_name == pair_row.get(&#39;sourceName&#39;, &#39;&#39;).lower() and
                    destination_name == pair_row.get(&#39;destinationName&#39;, &#39;&#39;).lower()):
                return True
        else:
            return False

    def create_fsblr_pair(self,
                          source_client_id,
                          destination_client_id,
                          source_volumes,
                          destination_volumes,
                          recovery_type,
                          **kwargs):
        &#34;&#34;&#34;Creates a new FSBLR pair on the commcell with the specified options
            Args:
                source_client_id (str)      : The source client&#39;s ID
                destination_client_id (str) : The destination client&#39;s ID
                source_volumes (list)       : The list of all source volumes
                destination_volumes (list)  : The list of all destination volumes
                recovery_type (RecoveryType): The enum to specify what type of recovery pair is supposed to be
                **kwargs (dict)             : Only used for granular type FSBLR pairs
                    rpstore_id (str)        : The ID of the RPstore to be used
                    rpstore_name (str)      : The name of the RPStore
                    ccrp_interval (int)     : The number of minutes after which CCRP is taken
                    acrp_interval (int)     : The number of minutes after which ACRP is taken
                    max_rp_interval (int)   : The number of minutes after which RP store&#39;s retention is ended
                    rp_merge_delay (int)    : Merge recovery points older than time in minutes
                    rp_retention (int)      : The number of minutes for which RPstore is retained for
                    rpstore_switch_live(int): The time in minutes after which pair is switch to
                                                live if RPstore is offline
                    merge_only_off_peak(bool):Whether to merge RPstore only during off-peak time
        &#34;&#34;&#34;
        blr_options = {
            &#39;BlockReplication_BLRRecoveryOptions&#39;:
                {
                    &#39;@recoveryType&#39;: recovery_type.value,
                    &#39;granularV2&#39;: {
                        &#39;@ccrpInterval&#39;: kwargs.get(&#39;ccrp_interval&#39;, 300),
                        &#39;@acrpInterval&#39;: kwargs.get(&#39;acrp_interval&#39;, 0),
                        &#39;@maxRpInterval&#39;: kwargs.get(&#39;max_rp_interval&#39;, 21600),
                        &#39;@rpMergeDelay&#39;: kwargs.get(&#39;rp_merge_delay&#39;, 172800),
                        &#39;@rpRetention&#39;: kwargs.get(&#39;rp_retention&#39;, 604800),
                        &#39;@maxRpStoreOfflineTime&#39;: kwargs.get(&#39;rpstore_switch_live&#39;, 0),
                        &#39;@useOffPeakSchedule&#39;: int(kwargs.get(&#39;merge_only_off_peak&#39;, False)),
                    }},
        }
        if kwargs.get(&#39;rpstore_id&#39;) and kwargs.get(&#39;rpstore_name&#39;):
            granularv2 = blr_options[&#39;BlockReplication_BLRRecoveryOptions&#39;][&#39;granularV2&#39;]
            granularv2[&#39;@rpStoreId&#39;] = int(kwargs.get(&#39;rpstore_id&#39;, 0))
            granularv2[&#39;@rpStoreName&#39;] = kwargs.get(&#39;rpstore_name&#39;)
            blr_options[&#39;BlockReplication_BLRRecoveryOptions&#39;][&#39;granularV2&#39;] = granularv2

        source_client = self._commcell_object.clients.get(int(source_client_id))
        destination_client = self._commcell_object.clients.get(int(destination_client_id))
        source_client_volumes = source_client.get_mount_volumes(source_volumes)
        destination_client_volumes = destination_client.get_mount_volumes(destination_volumes)

        request_json = {
            &#34;destEndPointType&#34;: self.EndPointTypes.FILESYSTEM.value,
            &#34;blrRecoveryOpts&#34;: xmltodict.unparse(blr_options, short_empty_elements=True).replace(&#39;\n&#39;, &#39;&#39;),
            &#34;srcEndPointType&#34;: self.EndPointTypes.FILESYSTEM.value,
            &#34;srcDestVolumeMap&#34;: [],
            &#34;destEntity&#34;: {
                &#34;client&#34;: {
                    &#34;clientId&#34;: int(destination_client_id),
                    &#34;clientName&#34;: destination_client.client_name,
                    &#34;hasDrivesInPair&#34;: True,
                    &#34;tabLevel&#34;: &#34;level-0&#34;,
                    &#34;checked&#34;: True,
                }
            },
            &#34;sourceEntity&#34;: {
                &#34;client&#34;: {
                    &#34;clientId&#34;: int(source_client_id),
                    &#34;clientName&#34;: source_client.client_name,
                    &#34;hasDrivesInPair&#34;: True,
                    &#34;tabLevel&#34;: &#34;level-0&#34;,
                    &#34;checked&#34;: True,
                }
            }
        }
        for source, destination in zip(source_client_volumes, destination_client_volumes):
            request_json[&#39;srcDestVolumeMap&#39;].append({
                &#34;sourceVolumeGUID&#34;: source[&#39;guid&#39;],
                &#34;sourceVolume&#34;: source[&#39;accessPathList&#39;][0],
                &#34;destVolumeGUID&#34;: destination[&#39;guid&#39;],
                &#34;destVolume&#34;: destination[&#39;accessPathList&#39;][0],
                &#34;sourceVolumeSize&#34;: source[&#39;size&#39;],
                &#34;disabled&#34;: &#34;&#34;,
            })

        flag, response = (self._commcell_object._cvpysdk_object
                          .make_request(&#39;POST&#39;, self._services[&#39;CREATE_BLR_PAIR&#39;], request_json))

        if flag:
            if response and response.json():
                if response.json().get(&#39;errorCode&#39;, 0) != 0:
                    response_string = self._commcell_object._update_response_(
                        response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def get(self, source_name, destination_name):
        &#34;&#34;&#34;Get pair name on the basis of source and destination name and return pair object
        Args:
            source_name (str): Name of the source client
            destination_name (str): Name of the destination client
        Returns: BLRPair object for source and destination

        Raises:
            SDKException:
                if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(source_name, str) or not isinstance(destination_name, str):
            raise SDKException(&#39;BLRPairs&#39;, &#39;101&#39;)
        source_name = source_name.lower()
        destination_name = destination_name.lower()
        for _, pair_row in self.blr_pairs.items():
            if (source_name == pair_row.get(&#39;sourceName&#39;, &#39;&#39;).lower() and
                    destination_name == pair_row.get(&#39;destinationName&#39;, &#39;&#39;).lower()):
                return BLRPair(self._commcell_object, source_name, destination_name)
        else:
            raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;,
                               &#39;No BLR pair exists with source: &#34;{0}&#34; and destination: &#34;{1}&#34;&#39;
                               .format(source_name, destination_name))

    def delete(self, source_name, destination_name):
        &#34;&#34;&#34; Deletes the blr pair with source and destination names
        Args:
            source_name (str): Name of the source client
            destination_name (str): Name of the destination client
        Returns: BLRPair object for source and destination

        Raises:
            SDKException:
                if proper inputs are not provided
                if response is empty
                if response is not success
        &#34;&#34;&#34;
        if not isinstance(source_name, str) or not isinstance(destination_name, str):
            raise SDKException(&#39;BLRPairs&#39;, &#39;101&#39;)
        source_name = source_name.lower()
        destination_name = destination_name.lower()
        if self.has_blr_pair(source_name, destination_name):
            for pair_id, pair_row in self.blr_pairs.items():
                if (source_name == pair_row.get(&#39;sourceName&#39;, &#39;&#39;).lower() and
                        destination_name == pair_row.get(&#39;destinationName&#39;, &#39;&#39;).lower()):
                    flag, response = self._commcell_object._cvpysdk_object.make_request(
                        method=&#39;DELETE&#39;, url=self._DELETE_BLR % pair_id)
                    if flag:
                        if &#39;error&#39; in response.json():
                            error_message = response.json(
                            )[&#39;error&#39;][&#39;errorMessage&#39;]
                            o_str = &#39;Failed to delete Source: {0} and Destination: {1} \nError: &#34;{2}&#34;&#39; \
                                .format(source_name, destination_name, error_message)

                            raise SDKException(&#39;Response&#39;, &#39;101&#39;, o_str)
                        else:
                            self.refresh()
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    break
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;,
                               &#39;No BLR pair exists with source: &#34;{0}&#34; and destination: &#34;{1}&#34;&#39;
                               .format(source_name, destination_name))

    def refresh(self):
        &#34;&#34;&#34; Refresh the BLR pairs created in the commcell.
        Args:

        Returns:

        Raises:

        &#34;&#34;&#34;
        self._update_data()
        self._update_rpstorelist()

    def get_rpstore_id(self, rpstore_name):
        &#34;&#34;&#34;Gets the RPStore ID for the given name
            Args:
                rpstore_name (str)  : The name of the RP store
        &#34;&#34;&#34;

        for rpstore in self._rpstore_list:
            if rpstore.get(&#39;library&#39;, {}).get(&#39;libraryName&#39;) == rpstore_name and rpstore.get(&#39;MountPathList&#39;, &#39;&#39;):
                return str(rpstore.get(&#39;MountPathList&#39;)[0]
                           .get(&#39;rpStoreLibraryInfo&#39;, {}).get(&#39;rpStoreId&#39;, &#39;&#39;))
        else:
            raise SDKException(&#39;BLRPairs&#39;, &#39;103&#39;, f&#39;No RP Store found with name {rpstore_name}&#39;)

    def get_rpstore_mountpath(self, rpstore_name):
        &#34;&#34;&#34;Gets the RPStore mount path for the given name
            Args:
                rpstore_name (str)  : The name of the RP store
        &#34;&#34;&#34;

        for rpstore in self._rpstore_list:
            if rpstore.get(&#39;library&#39;, {}).get(&#39;libraryName&#39;) == rpstore_name:
                mount_path = str(rpstore.get(&#39;MountPathList&#39;, {})[0][&#39;mountPathName&#39;])
                return mount_path.split()[1].strip()
        else:
            raise SDKException(&#39;BLRPairs&#39;, &#39;103&#39;, f&#39;No RP Store found with name {rpstore_name}&#39;)</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.AgentTypes"><code class="name">var <span class="ident">AgentTypes</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.DROperations"><code class="name">var <span class="ident">DROperations</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.EndPointTypes"><code class="name">var <span class="ident">EndPointTypes</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.PairStatus"><code class="name">var <span class="ident">PairStatus</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.PendingStatus"><code class="name">var <span class="ident">PendingStatus</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.RecoveryType"><code class="name">var <span class="ident">RecoveryType</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
</dl>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.blr_pairs"><code class="name">var <span class="ident">blr_pairs</span></code></dt>
<dd>
<div class="desc"><p>REST API call for getting all the BLR pairs in the commcell.
Args:</p>
<h2 id="returns">Returns</h2>
<p>dict - consists of all BLR pairs
{
"blr_id_1": {
"sourceName": "vm1",
"destinationName": "DRvm1",
"subclientName": "BLR_vm1(<guid>)"
},
"blr_id_2": {
"sourceName": "vm1",
"destinationName": "DRvm1",
"subclientName": "BLRSC_vm1_DRvm1_E:"
},
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L278-L307" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def blr_pairs(self):
    &#34;&#34;&#34;REST API call for getting all the BLR pairs in the commcell.
        Args:

        Returns:
            dict - consists of all BLR pairs
                {
                     &#34;blr_id_1&#34;: {
                         &#34;sourceName&#34;: &#34;vm1&#34;,
                         &#34;destinationName&#34;: &#34;DRvm1&#34;,
                         &#34;subclientName&#34;: &#34;BLR_vm1(&lt;guid&gt;)&#34;
                     },
                     &#34;blr_id_2&#34;: {
                         &#34;sourceName&#34;: &#34;vm1&#34;,
                         &#34;destinationName&#34;: &#34;DRvm1&#34;,
                         &#34;subclientName&#34;: &#34;BLRSC_vm1_DRvm1_E:&#34;
                     },
                }
    &#34;&#34;&#34;
    pairs = {}
    for pair_row in self._site_info:
        pair_id = pair_row.get(&#39;id&#39;)
        if pair_id:
            pairs[str(pair_id)] = {
                &#39;sourceName&#39;: pair_row.get(&#39;sourceName&#39;, &#39;&#39;),
                &#39;destinationName&#39;: pair_row.get(&#39;destinationName&#39;, &#39;&#39;),
                &#39;subclientName&#39;: pair_row.get(&#39;entity&#39;, {}).get(&#39;subclientName&#39;, &#39;&#39;)
            }
    return pairs</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.create_fsblr_pair"><code class="name flex">
<span>def <span class="ident">create_fsblr_pair</span></span>(<span>self, source_client_id, destination_client_id, source_volumes, destination_volumes, recovery_type, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates a new FSBLR pair on the commcell with the specified options</p>
<h2 id="args">Args</h2>
<dl>
<dt>source_client_id (str)
: The source client's ID</dt>
<dt>destination_client_id (str) : The destination client's ID</dt>
<dt>source_volumes (list)
: The list of all source volumes</dt>
<dt>destination_volumes (list)
: The list of all destination volumes</dt>
<dt><strong><code>recovery_type</code></strong> :&ensp;<code>RecoveryType</code></dt>
<dd>The enum to specify what type of recovery pair is supposed to be</dd>
</dl>
<p>**kwargs (dict)
: Only used for granular type FSBLR pairs
rpstore_id (str)
: The ID of the RPstore to be used
rpstore_name (str)
: The name of the RPStore
ccrp_interval (int)
: The number of minutes after which CCRP is taken
acrp_interval (int)
: The number of minutes after which ACRP is taken
max_rp_interval (int)
: The number of minutes after which RP store's retention is ended
rp_merge_delay (int)
: Merge recovery points older than time in minutes
rp_retention (int)
: The number of minutes for which RPstore is retained for
rpstore_switch_live(int): The time in minutes after which pair is switch to
live if RPstore is offline
merge_only_off_peak(bool):Whether to merge RPstore only during off-peak time</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L332-L429" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_fsblr_pair(self,
                      source_client_id,
                      destination_client_id,
                      source_volumes,
                      destination_volumes,
                      recovery_type,
                      **kwargs):
    &#34;&#34;&#34;Creates a new FSBLR pair on the commcell with the specified options
        Args:
            source_client_id (str)      : The source client&#39;s ID
            destination_client_id (str) : The destination client&#39;s ID
            source_volumes (list)       : The list of all source volumes
            destination_volumes (list)  : The list of all destination volumes
            recovery_type (RecoveryType): The enum to specify what type of recovery pair is supposed to be
            **kwargs (dict)             : Only used for granular type FSBLR pairs
                rpstore_id (str)        : The ID of the RPstore to be used
                rpstore_name (str)      : The name of the RPStore
                ccrp_interval (int)     : The number of minutes after which CCRP is taken
                acrp_interval (int)     : The number of minutes after which ACRP is taken
                max_rp_interval (int)   : The number of minutes after which RP store&#39;s retention is ended
                rp_merge_delay (int)    : Merge recovery points older than time in minutes
                rp_retention (int)      : The number of minutes for which RPstore is retained for
                rpstore_switch_live(int): The time in minutes after which pair is switch to
                                            live if RPstore is offline
                merge_only_off_peak(bool):Whether to merge RPstore only during off-peak time
    &#34;&#34;&#34;
    blr_options = {
        &#39;BlockReplication_BLRRecoveryOptions&#39;:
            {
                &#39;@recoveryType&#39;: recovery_type.value,
                &#39;granularV2&#39;: {
                    &#39;@ccrpInterval&#39;: kwargs.get(&#39;ccrp_interval&#39;, 300),
                    &#39;@acrpInterval&#39;: kwargs.get(&#39;acrp_interval&#39;, 0),
                    &#39;@maxRpInterval&#39;: kwargs.get(&#39;max_rp_interval&#39;, 21600),
                    &#39;@rpMergeDelay&#39;: kwargs.get(&#39;rp_merge_delay&#39;, 172800),
                    &#39;@rpRetention&#39;: kwargs.get(&#39;rp_retention&#39;, 604800),
                    &#39;@maxRpStoreOfflineTime&#39;: kwargs.get(&#39;rpstore_switch_live&#39;, 0),
                    &#39;@useOffPeakSchedule&#39;: int(kwargs.get(&#39;merge_only_off_peak&#39;, False)),
                }},
    }
    if kwargs.get(&#39;rpstore_id&#39;) and kwargs.get(&#39;rpstore_name&#39;):
        granularv2 = blr_options[&#39;BlockReplication_BLRRecoveryOptions&#39;][&#39;granularV2&#39;]
        granularv2[&#39;@rpStoreId&#39;] = int(kwargs.get(&#39;rpstore_id&#39;, 0))
        granularv2[&#39;@rpStoreName&#39;] = kwargs.get(&#39;rpstore_name&#39;)
        blr_options[&#39;BlockReplication_BLRRecoveryOptions&#39;][&#39;granularV2&#39;] = granularv2

    source_client = self._commcell_object.clients.get(int(source_client_id))
    destination_client = self._commcell_object.clients.get(int(destination_client_id))
    source_client_volumes = source_client.get_mount_volumes(source_volumes)
    destination_client_volumes = destination_client.get_mount_volumes(destination_volumes)

    request_json = {
        &#34;destEndPointType&#34;: self.EndPointTypes.FILESYSTEM.value,
        &#34;blrRecoveryOpts&#34;: xmltodict.unparse(blr_options, short_empty_elements=True).replace(&#39;\n&#39;, &#39;&#39;),
        &#34;srcEndPointType&#34;: self.EndPointTypes.FILESYSTEM.value,
        &#34;srcDestVolumeMap&#34;: [],
        &#34;destEntity&#34;: {
            &#34;client&#34;: {
                &#34;clientId&#34;: int(destination_client_id),
                &#34;clientName&#34;: destination_client.client_name,
                &#34;hasDrivesInPair&#34;: True,
                &#34;tabLevel&#34;: &#34;level-0&#34;,
                &#34;checked&#34;: True,
            }
        },
        &#34;sourceEntity&#34;: {
            &#34;client&#34;: {
                &#34;clientId&#34;: int(source_client_id),
                &#34;clientName&#34;: source_client.client_name,
                &#34;hasDrivesInPair&#34;: True,
                &#34;tabLevel&#34;: &#34;level-0&#34;,
                &#34;checked&#34;: True,
            }
        }
    }
    for source, destination in zip(source_client_volumes, destination_client_volumes):
        request_json[&#39;srcDestVolumeMap&#39;].append({
            &#34;sourceVolumeGUID&#34;: source[&#39;guid&#39;],
            &#34;sourceVolume&#34;: source[&#39;accessPathList&#39;][0],
            &#34;destVolumeGUID&#34;: destination[&#39;guid&#39;],
            &#34;destVolume&#34;: destination[&#39;accessPathList&#39;][0],
            &#34;sourceVolumeSize&#34;: source[&#39;size&#39;],
            &#34;disabled&#34;: &#34;&#34;,
        })

    flag, response = (self._commcell_object._cvpysdk_object
                      .make_request(&#39;POST&#39;, self._services[&#39;CREATE_BLR_PAIR&#39;], request_json))

    if flag:
        if response and response.json():
            if response.json().get(&#39;errorCode&#39;, 0) != 0:
                response_string = self._commcell_object._update_response_(
                    response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, source_name, destination_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the blr pair with source and destination names</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>source_name</code></strong> :&ensp;<code>str</code></dt>
<dd>Name of the source client</dd>
<dt><strong><code>destination_name</code></strong> :&ensp;<code>str</code></dt>
<dd>Name of the destination client</dd>
</dl>
<p>Returns: BLRPair object for source and destination</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L455-L496" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, source_name, destination_name):
    &#34;&#34;&#34; Deletes the blr pair with source and destination names
    Args:
        source_name (str): Name of the source client
        destination_name (str): Name of the destination client
    Returns: BLRPair object for source and destination

    Raises:
        SDKException:
            if proper inputs are not provided
            if response is empty
            if response is not success
    &#34;&#34;&#34;
    if not isinstance(source_name, str) or not isinstance(destination_name, str):
        raise SDKException(&#39;BLRPairs&#39;, &#39;101&#39;)
    source_name = source_name.lower()
    destination_name = destination_name.lower()
    if self.has_blr_pair(source_name, destination_name):
        for pair_id, pair_row in self.blr_pairs.items():
            if (source_name == pair_row.get(&#39;sourceName&#39;, &#39;&#39;).lower() and
                    destination_name == pair_row.get(&#39;destinationName&#39;, &#39;&#39;).lower()):
                flag, response = self._commcell_object._cvpysdk_object.make_request(
                    method=&#39;DELETE&#39;, url=self._DELETE_BLR % pair_id)
                if flag:
                    if &#39;error&#39; in response.json():
                        error_message = response.json(
                        )[&#39;error&#39;][&#39;errorMessage&#39;]
                        o_str = &#39;Failed to delete Source: {0} and Destination: {1} \nError: &#34;{2}&#34;&#39; \
                            .format(source_name, destination_name, error_message)

                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, o_str)
                    else:
                        self.refresh()
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                break
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;,
                           &#39;No BLR pair exists with source: &#34;{0}&#34; and destination: &#34;{1}&#34;&#39;
                           .format(source_name, destination_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, source_name, destination_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Get pair name on the basis of source and destination name and return pair object</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>source_name</code></strong> :&ensp;<code>str</code></dt>
<dd>Name of the source client</dd>
<dt><strong><code>destination_name</code></strong> :&ensp;<code>str</code></dt>
<dd>Name of the destination client</dd>
</dl>
<p>Returns: BLRPair object for source and destination</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L431-L453" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, source_name, destination_name):
    &#34;&#34;&#34;Get pair name on the basis of source and destination name and return pair object
    Args:
        source_name (str): Name of the source client
        destination_name (str): Name of the destination client
    Returns: BLRPair object for source and destination

    Raises:
        SDKException:
            if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(source_name, str) or not isinstance(destination_name, str):
        raise SDKException(&#39;BLRPairs&#39;, &#39;101&#39;)
    source_name = source_name.lower()
    destination_name = destination_name.lower()
    for _, pair_row in self.blr_pairs.items():
        if (source_name == pair_row.get(&#39;sourceName&#39;, &#39;&#39;).lower() and
                destination_name == pair_row.get(&#39;destinationName&#39;, &#39;&#39;).lower()):
            return BLRPair(self._commcell_object, source_name, destination_name)
    else:
        raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;,
                           &#39;No BLR pair exists with source: &#34;{0}&#34; and destination: &#34;{1}&#34;&#39;
                           .format(source_name, destination_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.get_rpstore_id"><code class="name flex">
<span>def <span class="ident">get_rpstore_id</span></span>(<span>self, rpstore_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the RPStore ID for the given name</p>
<h2 id="args">Args</h2>
<p>rpstore_name (str)
: The name of the RP store</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L510-L521" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_rpstore_id(self, rpstore_name):
    &#34;&#34;&#34;Gets the RPStore ID for the given name
        Args:
            rpstore_name (str)  : The name of the RP store
    &#34;&#34;&#34;

    for rpstore in self._rpstore_list:
        if rpstore.get(&#39;library&#39;, {}).get(&#39;libraryName&#39;) == rpstore_name and rpstore.get(&#39;MountPathList&#39;, &#39;&#39;):
            return str(rpstore.get(&#39;MountPathList&#39;)[0]
                       .get(&#39;rpStoreLibraryInfo&#39;, {}).get(&#39;rpStoreId&#39;, &#39;&#39;))
    else:
        raise SDKException(&#39;BLRPairs&#39;, &#39;103&#39;, f&#39;No RP Store found with name {rpstore_name}&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.get_rpstore_mountpath"><code class="name flex">
<span>def <span class="ident">get_rpstore_mountpath</span></span>(<span>self, rpstore_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the RPStore mount path for the given name</p>
<h2 id="args">Args</h2>
<p>rpstore_name (str)
: The name of the RP store</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L523-L534" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_rpstore_mountpath(self, rpstore_name):
    &#34;&#34;&#34;Gets the RPStore mount path for the given name
        Args:
            rpstore_name (str)  : The name of the RP store
    &#34;&#34;&#34;

    for rpstore in self._rpstore_list:
        if rpstore.get(&#39;library&#39;, {}).get(&#39;libraryName&#39;) == rpstore_name:
            mount_path = str(rpstore.get(&#39;MountPathList&#39;, {})[0][&#39;mountPathName&#39;])
            return mount_path.split()[1].strip()
    else:
        raise SDKException(&#39;BLRPairs&#39;, &#39;103&#39;, f&#39;No RP Store found with name {rpstore_name}&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.has_blr_pair"><code class="name flex">
<span>def <span class="ident">has_blr_pair</span></span>(<span>self, source_name, destination_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if BLR pair exists or not</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>source_name</code></strong> :&ensp;<code>str</code></dt>
<dd>Name of the source client</dd>
<dt><strong><code>destination_name</code></strong> :&ensp;<code>str</code></dt>
<dd>Name of the destination client</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether BLR pair exists or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L309-L330" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_blr_pair(self, source_name, destination_name):
    &#34;&#34;&#34;Checks if BLR pair exists or not
        Args:
            source_name (str): Name of the source client
            destination_name (str): Name of the destination client
        Returns:
            bool - boolean output whether BLR pair exists or not

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(source_name, str) or not isinstance(destination_name, str):
        raise SDKException(&#39;BLRPairs&#39;, &#39;101&#39;)
    source_name = source_name.lower()
    destination_name = destination_name.lower()
    for _, pair_row in self.blr_pairs.items():
        if (source_name == pair_row.get(&#39;sourceName&#39;, &#39;&#39;).lower() and
                destination_name == pair_row.get(&#39;destinationName&#39;, &#39;&#39;).lower()):
            return True
    else:
        return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRPairs.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the BLR pairs created in the commcell.
Args:</p>
<p>Returns:</p>
<p>Raises:</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L498-L508" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34; Refresh the BLR pairs created in the commcell.
    Args:

    Returns:

    Raises:

    &#34;&#34;&#34;
    self._update_data()
    self._update_rpstorelist()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers"><code class="flex name class">
<span>class <span class="ident">BLRTestFailovers</span></span>
<span>(</span><span>commcell_object, pair_id, boot_type: <a title="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.BLRBootType" href="#cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.BLRBootType">BLRTestFailovers.BLRBootType</a>)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all test failover VMs for BLR pair</p>
<p>Method for making initial data members</p>
<h2 id="args">Args</h2>
<p>commcell_object (Commcell)
&ndash;
instance of the Commcell class
pair_id (str)
&ndash;
Id of the BLR pair
boot_type (BLRBootType)
&ndash;
The type of the boot for VMs</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L1054-L1152" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class BLRTestFailovers:
    &#34;&#34;&#34;Class for getting all test failover VMs for BLR pair&#34;&#34;&#34;
    class BLRBootType(Enum):
        TESTBOOT = 1
        PERMANENT = 2

    class BootStatus(Enum):
        NONE = 0
        IN_PROGRESS = 1
        SUCCESS = 2
        FAILED = 3
        ABOUT_TO_EXPIRE = 4
        EXPIRED = 5
        USER_DELETED = 6
        DELETE_FAILED = 7
        PARTIAL_SUCCESS = 8

    def __init__(self, commcell_object, pair_id, boot_type: BLRBootType):
        &#34;&#34;&#34;Method for making initial data members
            Args:
                commcell_object (Commcell)      --  instance of the Commcell class
                pair_id (str)                   --  Id of the BLR pair
                boot_type (BLRBootType)         --  The type of the boot for VMs
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services

        self._pair_id = pair_id
        self._boot_type = boot_type
        self._boot_vm_dict = None

        self._GET_BOOT_VMS = self._services[&#39;BLR_BOOT_DETAILS&#39;]

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of BLRTestFailovers&#34;&#34;&#34;
        representation_string = &#39;BLR Test failover VMs class instance for pair id: {0}&#39;
        return representation_string.format(self._pair_id)

    def _get_test_failover_vms(self):
        &#34;&#34;&#34;Gets the list of all test failover VMs
            Args:
            Returns: Gets the BLR test failover VMs list
            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._GET_BOOT_VMS % (self._pair_id, self._boot_type.value))
        if flag:
            if response.json() and &#39;siteInfo&#39; in response.json():
                return response.json()[&#39;siteInfo&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the VMs list for BLR pair&#34;&#34;&#34;
        self._boot_vm_dict = {boot_dict.get(&#39;name&#39;): boot_dict
                              for boot_dict in self._get_test_failover_vms()}

    @property
    def all_boot_vms(self):
        &#34;&#34;&#34;Returns a list of all boot VMs&#34;&#34;&#34;
        return list(self._boot_vm_dict.keys())

    def get_boot_vm_details(self, vm_name):
        &#34;&#34;&#34;Get the boot VM details for a given vm name
        Args:
            vm_name (str): The name of the VM to fetch details for
        Returns:
            dict:  {
                id (int)                : The ID of the test failover VM
                uuid (str)              : The UUID of the test failover VM
                name (str)              : The name of the test failover VM
                creation_time (int)     : The timestamp of VM creation
                vm_status (BootStatus)  : The status of the test failover VM
                status_message (str)    : The description of the VM status
            }
        Raises:
            BLRPair not found: If test boot VM name is not found
        &#34;&#34;&#34;
        vm_dict = self._boot_vm_dict.get(vm_name)
        if not vm_dict:
            raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;, f&#39;BLR test boot VM with name {vm_name} not found&#39;)
        return {
            &#39;id&#39;: vm_dict.get(&#39;id&#39;),
            &#39;uuid&#39;: vm_dict.get(&#39;uuid&#39;),
            &#39;name&#39;: vm_dict.get(&#39;name&#39;),
            &#39;creation_time&#39;: vm_dict.get(&#39;creationTime&#39;),
            &#39;vm_status&#39;: self.BootStatus(vm_dict.get(&#39;status&#39;)) if vm_dict.get(&#39;status&#39;) else None,
            &#39;status_message&#39;: vm_dict.get(&#39;statusMessage&#39;),
            &#39;expiration_time&#39;: vm_dict.get(&#39;bestBefore&#39;)
        }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.BLRBootType"><code class="name">var <span class="ident">BLRBootType</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.BootStatus"><code class="name">var <span class="ident">BootStatus</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
</dl>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.all_boot_vms"><code class="name">var <span class="ident">all_boot_vms</span></code></dt>
<dd>
<div class="desc"><p>Returns a list of all boot VMs</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L1120-L1123" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_boot_vms(self):
    &#34;&#34;&#34;Returns a list of all boot VMs&#34;&#34;&#34;
    return list(self._boot_vm_dict.keys())</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.get_boot_vm_details"><code class="name flex">
<span>def <span class="ident">get_boot_vm_details</span></span>(<span>self, vm_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Get the boot VM details for a given vm name</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>vm_name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the VM to fetch details for</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>dict</code></dt>
<dd>{
id (int)
: The ID of the test failover VM
uuid (str)
: The UUID of the test failover VM
name (str)
: The name of the test failover VM
creation_time (int)
: The timestamp of VM creation
vm_status (BootStatus)
: The status of the test failover VM
status_message (str)
: The description of the VM status</dd>
</dl>
<p>}</p>
<h2 id="raises">Raises</h2>
<dl>
<dt><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair" href="#cvpysdk.drorchestration.blr_pairs.BLRPair">BLRPair</a> not found</code></dt>
<dd>If test boot VM name is not found</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L1125-L1152" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_boot_vm_details(self, vm_name):
    &#34;&#34;&#34;Get the boot VM details for a given vm name
    Args:
        vm_name (str): The name of the VM to fetch details for
    Returns:
        dict:  {
            id (int)                : The ID of the test failover VM
            uuid (str)              : The UUID of the test failover VM
            name (str)              : The name of the test failover VM
            creation_time (int)     : The timestamp of VM creation
            vm_status (BootStatus)  : The status of the test failover VM
            status_message (str)    : The description of the VM status
        }
    Raises:
        BLRPair not found: If test boot VM name is not found
    &#34;&#34;&#34;
    vm_dict = self._boot_vm_dict.get(vm_name)
    if not vm_dict:
        raise SDKException(&#39;BLRPairs&#39;, &#39;102&#39;, f&#39;BLR test boot VM with name {vm_name} not found&#39;)
    return {
        &#39;id&#39;: vm_dict.get(&#39;id&#39;),
        &#39;uuid&#39;: vm_dict.get(&#39;uuid&#39;),
        &#39;name&#39;: vm_dict.get(&#39;name&#39;),
        &#39;creation_time&#39;: vm_dict.get(&#39;creationTime&#39;),
        &#39;vm_status&#39;: self.BootStatus(vm_dict.get(&#39;status&#39;)) if vm_dict.get(&#39;status&#39;) else None,
        &#39;status_message&#39;: vm_dict.get(&#39;statusMessage&#39;),
        &#39;expiration_time&#39;: vm_dict.get(&#39;bestBefore&#39;)
    }</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the VMs list for BLR pair</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/blr_pairs.py#L1115-L1118" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the VMs list for BLR pair&#34;&#34;&#34;
    self._boot_vm_dict = {boot_dict.get(&#39;name&#39;): boot_dict
                          for boot_dict in self._get_test_failover_vms()}</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.drorchestration" href="index.html">cvpysdk.drorchestration</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair" href="#cvpysdk.drorchestration.blr_pairs.BLRPair">BLRPair</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.PairOperationsStatus" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.PairOperationsStatus">PairOperationsStatus</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.create_replica_copy" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.create_replica_copy">create_replica_copy</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.destination" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.destination">destination</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.destination_vm" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.destination_vm">destination_vm</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.get_pair_stats" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.get_pair_stats">get_pair_stats</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.get_recovery_point_stores" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.get_recovery_point_stores">get_recovery_point_stores</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.get_test_failover_vms" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.get_test_failover_vms">get_test_failover_vms</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.lag_time" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.lag_time">lag_time</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_flag" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.pair_flag">pair_flag</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_latest_stats" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.pair_latest_stats">pair_latest_stats</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_properties" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.pair_properties">pair_properties</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_recovery_type" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.pair_recovery_type">pair_recovery_type</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_rpstore_intervals" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.pair_rpstore_intervals">pair_rpstore_intervals</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_status" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.pair_status">pair_status</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.pair_volume_map" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.pair_volume_map">pair_volume_map</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.pending_status" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.pending_status">pending_status</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.refresh" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.replication_group_name" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.replication_group_name">replication_group_name</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.resume" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.resume">resume</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.resync" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.resync">resync</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.source" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.source">source</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.source_vm" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.source_vm">source_vm</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.start" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.start">start</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.stop" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.stop">stop</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.subclient_props" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.subclient_props">subclient_props</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.suspend" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.suspend">suspend</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPair.wait_for_pair_status" href="#cvpysdk.drorchestration.blr_pairs.BLRPair.wait_for_pair_status">wait_for_pair_status</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs">BLRPairs</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.AgentTypes" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.AgentTypes">AgentTypes</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.DROperations" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.DROperations">DROperations</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.EndPointTypes" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.EndPointTypes">EndPointTypes</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.PairStatus" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.PairStatus">PairStatus</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.PendingStatus" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.PendingStatus">PendingStatus</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.RecoveryType" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.RecoveryType">RecoveryType</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.blr_pairs" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.blr_pairs">blr_pairs</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.create_fsblr_pair" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.create_fsblr_pair">create_fsblr_pair</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.delete" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.delete">delete</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.get" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.get">get</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.get_rpstore_id" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.get_rpstore_id">get_rpstore_id</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.get_rpstore_mountpath" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.get_rpstore_mountpath">get_rpstore_mountpath</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.has_blr_pair" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.has_blr_pair">has_blr_pair</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRPairs.refresh" href="#cvpysdk.drorchestration.blr_pairs.BLRPairs.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers" href="#cvpysdk.drorchestration.blr_pairs.BLRTestFailovers">BLRTestFailovers</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.BLRBootType" href="#cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.BLRBootType">BLRBootType</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.BootStatus" href="#cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.BootStatus">BootStatus</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.all_boot_vms" href="#cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.all_boot_vms">all_boot_vms</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.get_boot_vm_details" href="#cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.get_boot_vm_details">get_boot_vm_details</a></code></li>
<li><code><a title="cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.refresh" href="#cvpysdk.drorchestration.blr_pairs.BLRTestFailovers.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>