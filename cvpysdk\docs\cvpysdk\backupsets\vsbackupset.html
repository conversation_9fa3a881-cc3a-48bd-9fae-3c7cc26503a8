<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.backupsets.vsbackupset API documentation</title>
<meta name="description" content="Module for performing operations on a Backupset for the **Virtual Server** Agent …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.backupsets.vsbackupset</code></h1>
</header>
<section id="section-intro">
<p>Module for performing operations on a Backupset for the <strong>Virtual Server</strong> Agent.</p>
<p>VSBackupset is the only class defined in this file.</p>
<h2 id="vsbackupset">Vsbackupset</h2>
<p>browse()
&ndash; browse the content of the backupset
_process_browse_response()
&ndash; retrieves the items from browse response</p>
<p>To add a new Virtual Backupset, create a class in a new module under _virtual_server sub package</p>
<p>The new module which is created has to named in the following manner:
1. Name the module with the name of the Virtual Server without special characters
2.Spaces alone must be replaced with underscores('_')</p>
<p>For eg:</p>
<pre><code>The Virtual Server 'Red Hat Virtualization' is named as 'red_hat_virtualization.py'

The Virtual Server 'Hyper-V' is named as 'hyperv.py'
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/vsbackupset.py#L1-L390" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Module for performing operations on a Backupset for the **Virtual Server** Agent.

VSBackupset is the only class defined in this file.

VSBackupset:

    browse()                        -- browse the content of the backupset
    _process_browse_response()      -- retrieves the items from browse response

    To add a new Virtual Backupset, create a class in a new module under _virtual_server sub package


The new module which is created has to named in the following manner:
1. Name the module with the name of the Virtual Server without special characters
2.Spaces alone must be replaced with underscores(&#39;_&#39;)

For eg:

    The Virtual Server &#39;Red Hat Virtualization&#39; is named as &#39;red_hat_virtualization.py&#39;

    The Virtual Server &#39;Hyper-V&#39; is named as &#39;hyperv.py&#39;
&#34;&#34;&#34;

from __future__ import unicode_literals

import re
import time
from importlib import import_module
from inspect import isabstract, isclass, getmembers

from ..backupset import Backupset
from ..client import Client
from ..exception import SDKException
from ..subclient import Subclient as subclient


class VSBackupset(Backupset):
    &#34;&#34;&#34;Derived class from Backupset Base class, representing a vs backupset,
            and to perform operations on that backupset.&#34;&#34;&#34;

    def __new__(cls, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;
        instance_name = instance_object.instance_name
        instance_name = re.sub(&#39;[^A-Za-z0-9_]+&#39;, &#39;&#39;, instance_name.replace(&#34; &#34;, &#34;_&#34;))

        try:
            backupset_module = import_module(&#34;cvpysdk.backupsets._virtual_server.{}&#34;.format(instance_name))
        except ImportError:
            return object.__new__(cls)

        classes = getmembers(backupset_module, lambda m: isclass(m) and not isabstract(m))

        for name, _class in classes:
            if issubclass(_class, Backupset) and _class.__module__.rsplit(&#34;.&#34;, 1)[-1] == instance_name:
                return object.__new__(_class)

    @property
    def hidden_subclient(self):
        &#34;&#34;&#34;Creates the object for the hidden subclient
        Returns:
                _hidden_subclient - object of the subclient

        &#34;&#34;&#34;
        if not self._hidden_subclient:
            hidden_subclient_service = self._commcell_object._services[&#39;VSA_HIDDEN_SUBCLIENT&#39;] % (
                self._client_object.client_name, self.backupset_name)
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#34;GET&#34;, hidden_subclient_service)
            if flag:
                if response.json():
                    hidden_subclient_id = response.json().get(&#39;subclientId&#39;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
            self._hidden_subclient = subclient(self, &#39;Do Not Backup&#39;, hidden_subclient_id)
        return self._hidden_subclient

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Backupset.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation
        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;retry_count&#39;] = 0
        return self._do_browse(options)

    def _process_browse_response(self, flag, response, options):
        &#34;&#34;&#34;Retrieves the items from browse response.

                Args:
                    flag        (bool)  --  boolean, whether the response was success or not

                    response    (dict)  --  JSON response received for the request from the Server

                    options     (dict)  --  The browse options dictionary

                Returns:
                    list - List of only the file / folder paths from the browse response

                    dict - Dictionary of all the paths with additional metadata retrieved from browse

                Raises:
                    SDKException:
                        if failed to browse/search for content

                        if response is empty

                        if response is not success
                &#34;&#34;&#34;
        paths_dict = {}
        paths = []
        result_set = None
        browse_result = None
        error_message = None
        options[&#39;retry_count&#39;] = options[&#39;retry_count&#39;] + 1
        show_deleted = options.get(&#39;show_deleted&#39;, False)

        if flag:
            response_json = response.json()
            if response_json and &#39;browseResponses&#39; in response_json:
                _browse_responses = response_json[&#39;browseResponses&#39;]
                if not isinstance(_browse_responses, list):
                    _browse_responses = [_browse_responses]
                for browse_response in _browse_responses:
                    resp_type = browse_response[&#39;respType&#39;]
                    if &#39;messages&#39; in browse_response:
                        # checking if it is not a list, then converting it to list
                        if not isinstance(browse_response[&#39;messages&#39;], list):
                            browse_response[&#39;messages&#39;] = [browse_response[&#39;messages&#39;]]
                        message = browse_response[&#39;messages&#39;][0]
                        error_message = message[&#39;errorMessage&#39;]
                        if resp_type == 2 or resp_type == 3 and &#39;No items found in the index, possibly index is being rebuilt&#39; in \
                                error_message:
                            if options[&#39;retry_count&#39;] &lt;= 3:
                                time.sleep(180)
                                return self._do_browse(options)
                            else:
                                err = &#34;Maximum browse attemps exhausted. Browse did not give full results&#34;
                                raise Exception(err)
                    if &#34;browseResult&#34; in browse_response:
                        browse_result = browse_response[&#39;browseResult&#39;]
                        if &#39;dataResultSet&#39; in browse_result:
                            result_set = browse_result[&#39;dataResultSet&#39;]
                            if not isinstance(result_set, list):
                                result_set = [result_set]
                            break
                if not browse_result:
                    if not isinstance(response_json[&#39;browseResponses&#39;], list):
                        response_json[&#39;browseResponses&#39;] = [response_json[&#39;browseResponses&#39;]]
                    if &#39;messages&#39; in response_json[&#39;browseResponses&#39;][0]:
                        if not isinstance(response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;], list):
                            response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;] = [
                                response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;]]
                        message = response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;][0]
                        error_message = message[&#39;errorMessage&#39;]
                        if error_message == &#39;Please note that this is a live browse operation. Live browse operations can take some time before the results appear in the browse window.&#39;:
                            return [], {}
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, str(error_message))

                    else:
                        return [], {}

                if not result_set:
                    raise SDKException(&#39;Backupset&#39;, &#39;110&#39;, &#34;Failed to browse for subclient backup content&#34;)

                if &#39;all_versions&#39; in options[&#39;operation&#39;]:
                    return self._process_browse_all_versions_response(result_set,options)

                for result in result_set:
                    name = result.get(&#39;displayName&#39;)
                    snap_display_name = result.get(&#39;name&#39;)

                    if &#39;path&#39; in result:
                        path = result[&#39;path&#39;]
                    else:
                        path = &#39;\\&#39;.join([options[&#39;path&#39;], name])

                    if &#39;modificationTime&#39; in result and int(result[&#39;modificationTime&#39;]) &gt; 0:
                        mod_time = time.localtime(int(result[&#39;modificationTime&#39;]))
                        mod_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, mod_time)
                    else:
                        mod_time = None

                    if &#39;backupTime&#39; in result[&#39;advancedData&#39;] and int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]) &gt; 0:
                        bkp_time = time.localtime(int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]))
                        bkp_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, bkp_time)
                    else:
                        bkp_time = None

                    if &#39;file&#39; in result[&#39;flags&#39;]:
                        if result[&#39;flags&#39;][&#39;file&#39;] is True or result[&#39;flags&#39;][&#39;file&#39;] == &#34;1&#34;:
                            file_or_folder = &#39;File&#39;
                        else:
                            file_or_folder = &#39;Folder&#39;
                    else:
                        file_or_folder = &#39;Folder&#39;

                    if &#39;size&#39; in result:
                        size = result[&#39;size&#39;]
                    else:
                        size = None
                        
                    if show_deleted and &#39;deleted&#39; in result.get(&#39;flags&#39;):
                        deleted = True if result[&#39;flags&#39;].get(&#39;deleted&#39;) in (True, &#39;1&#39;) else False
                    else:
                        deleted = None

                    paths_dict[path] = {
                        &#39;name&#39;: name,
                        &#39;snap_display_name&#39;: snap_display_name,
                        &#39;size&#39;: size,
                        &#39;modified_time&#39;: mod_time,
                        &#39;type&#39;: file_or_folder,
                        &#39;backup_time&#39;: bkp_time,
                        &#39;advanced_data&#39;: result[&#39;advancedData&#39;],
                        &#39;deleted&#39;: deleted
                    }

                    paths.append(path)

                return paths, paths_dict

        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def index_server(self):
        &#34;&#34;&#34;Returns the index server client set for the backupset&#34;&#34;&#34;

        client_name = None

        if &#39;indexSettings&#39; in self._properties:
            if &#39;currentIndexServer&#39; in self._properties[&#39;indexSettings&#39;]:
                client_name = self._properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]

        if client_name is not None:
            return Client(self._commcell_object, client_name=client_name)

        return None

    @index_server.setter
    def index_server(self, value):
        &#34;&#34;&#34;Sets index server client for the backupset. Property value should be a client object

            Args:

                value     (obj)    --  The cvpysdk client object of the index server client

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(value, Client):
            raise SDKException(&#39;Backupset&#39;, &#39;106&#39;)

        properties = self._properties
        index_server_id = int(value.client_id)
        index_server_name = value.client_name

        if &#39;indexSettings&#39; in properties:
            qualified_index_servers = []
            if &#39;qualifyingIndexServers&#39; in properties[&#39;indexSettings&#39;]:
                for index_server in properties[&#39;indexSettings&#39;][&#39;qualifyingIndexServers&#39;]:
                    qualified_index_servers.append(index_server[&#39;clientId&#39;])

            if index_server_id in qualified_index_servers:
                properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;] = {
                    &#39;clientId&#39;: index_server_id,
                    &#39;clientName&#39;: index_server_name
                }
            else:
                raise SDKException(
                    &#39;Backupset&#39;, &#39;102&#39;, &#39;{0} is not a qualified IndexServer client&#39;.format(
                        index_server_name))
        else:
            properties[&#39;indexSettings&#39;] = {
                &#39;currentIndexServer&#39;: {
                    &#39;clientId&#39;: index_server_id,
                    &#39;clientName&#39;: index_server_name
                }
            }

        request_json = {
            &#39;backupsetProperties&#39;: properties
        }

        self._process_update_reponse(request_json)

    @property
    def vm_filter(self):
        &#34;&#34;&#34;Returns the vm filters set at the backupset level
        Returns:
                list - list of content associated as the filters with the backupset
        &#34;&#34;&#34;

        return self.hidden_subclient.content

    @vm_filter.setter
    def vm_filter(self, content):
        &#34;&#34;&#34;
        Creates the list of content JSON to pass to the API to update
           content of the vm filter of the backupset
        Args:
            content (list)  --  list of the content to add as the filters to the
                Backupset. list should contain name and type

        &#34;&#34;&#34;
        self.hidden_subclient.content = content
        self.hidden_subclient.refresh()

    @property
    def vm_disk_filter(self):
        &#34;&#34;&#34;Returns the vm disk filters set at the backupset level
        Returns:
                list - list of content associated as the disk filters with the backupset
        &#34;&#34;&#34;
        return self.hidden_subclient.vm_diskfilter

    @vm_disk_filter.setter
    def vm_disk_filter(self, vm_diskfilter):
        &#34;&#34;&#34;
        Creates the list of disk filter content JSON to pass to the API to update
           content of the vm disk filter of the backupset
        Args:
            vm_diskfilter (list) --     list of the Disk filter to add
                                                 to the backupset

        &#34;&#34;&#34;
        self.hidden_subclient.vm_diskfilter = vm_diskfilter
        self.hidden_subclient.refresh()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.backupsets.vsbackupset.VSBackupset"><code class="flex name class">
<span>class <span class="ident">VSBackupset</span></span>
<span>(</span><span>instance_object, backupset_name, backupset_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Backupset Base class, representing a vs backupset,
and to perform operations on that backupset.</p>
<p>Initialise the backupset object.</p>
<h2 id="args">Args</h2>
<p>instance_object
(object)
&ndash;
instance of the Instance class</p>
<p>backupset_name
(str)
&ndash;
name of the backupset</p>
<p>backupset_id
(str)
&ndash;
id of the backupset
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Backupset class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/vsbackupset.py#L55-L390" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VSBackupset(Backupset):
    &#34;&#34;&#34;Derived class from Backupset Base class, representing a vs backupset,
            and to perform operations on that backupset.&#34;&#34;&#34;

    def __new__(cls, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;
        instance_name = instance_object.instance_name
        instance_name = re.sub(&#39;[^A-Za-z0-9_]+&#39;, &#39;&#39;, instance_name.replace(&#34; &#34;, &#34;_&#34;))

        try:
            backupset_module = import_module(&#34;cvpysdk.backupsets._virtual_server.{}&#34;.format(instance_name))
        except ImportError:
            return object.__new__(cls)

        classes = getmembers(backupset_module, lambda m: isclass(m) and not isabstract(m))

        for name, _class in classes:
            if issubclass(_class, Backupset) and _class.__module__.rsplit(&#34;.&#34;, 1)[-1] == instance_name:
                return object.__new__(_class)

    @property
    def hidden_subclient(self):
        &#34;&#34;&#34;Creates the object for the hidden subclient
        Returns:
                _hidden_subclient - object of the subclient

        &#34;&#34;&#34;
        if not self._hidden_subclient:
            hidden_subclient_service = self._commcell_object._services[&#39;VSA_HIDDEN_SUBCLIENT&#39;] % (
                self._client_object.client_name, self.backupset_name)
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#34;GET&#34;, hidden_subclient_service)
            if flag:
                if response.json():
                    hidden_subclient_id = response.json().get(&#39;subclientId&#39;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
            self._hidden_subclient = subclient(self, &#39;Do Not Backup&#39;, hidden_subclient_id)
        return self._hidden_subclient

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Backupset.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation
        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;retry_count&#39;] = 0
        return self._do_browse(options)

    def _process_browse_response(self, flag, response, options):
        &#34;&#34;&#34;Retrieves the items from browse response.

                Args:
                    flag        (bool)  --  boolean, whether the response was success or not

                    response    (dict)  --  JSON response received for the request from the Server

                    options     (dict)  --  The browse options dictionary

                Returns:
                    list - List of only the file / folder paths from the browse response

                    dict - Dictionary of all the paths with additional metadata retrieved from browse

                Raises:
                    SDKException:
                        if failed to browse/search for content

                        if response is empty

                        if response is not success
                &#34;&#34;&#34;
        paths_dict = {}
        paths = []
        result_set = None
        browse_result = None
        error_message = None
        options[&#39;retry_count&#39;] = options[&#39;retry_count&#39;] + 1
        show_deleted = options.get(&#39;show_deleted&#39;, False)

        if flag:
            response_json = response.json()
            if response_json and &#39;browseResponses&#39; in response_json:
                _browse_responses = response_json[&#39;browseResponses&#39;]
                if not isinstance(_browse_responses, list):
                    _browse_responses = [_browse_responses]
                for browse_response in _browse_responses:
                    resp_type = browse_response[&#39;respType&#39;]
                    if &#39;messages&#39; in browse_response:
                        # checking if it is not a list, then converting it to list
                        if not isinstance(browse_response[&#39;messages&#39;], list):
                            browse_response[&#39;messages&#39;] = [browse_response[&#39;messages&#39;]]
                        message = browse_response[&#39;messages&#39;][0]
                        error_message = message[&#39;errorMessage&#39;]
                        if resp_type == 2 or resp_type == 3 and &#39;No items found in the index, possibly index is being rebuilt&#39; in \
                                error_message:
                            if options[&#39;retry_count&#39;] &lt;= 3:
                                time.sleep(180)
                                return self._do_browse(options)
                            else:
                                err = &#34;Maximum browse attemps exhausted. Browse did not give full results&#34;
                                raise Exception(err)
                    if &#34;browseResult&#34; in browse_response:
                        browse_result = browse_response[&#39;browseResult&#39;]
                        if &#39;dataResultSet&#39; in browse_result:
                            result_set = browse_result[&#39;dataResultSet&#39;]
                            if not isinstance(result_set, list):
                                result_set = [result_set]
                            break
                if not browse_result:
                    if not isinstance(response_json[&#39;browseResponses&#39;], list):
                        response_json[&#39;browseResponses&#39;] = [response_json[&#39;browseResponses&#39;]]
                    if &#39;messages&#39; in response_json[&#39;browseResponses&#39;][0]:
                        if not isinstance(response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;], list):
                            response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;] = [
                                response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;]]
                        message = response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;][0]
                        error_message = message[&#39;errorMessage&#39;]
                        if error_message == &#39;Please note that this is a live browse operation. Live browse operations can take some time before the results appear in the browse window.&#39;:
                            return [], {}
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, str(error_message))

                    else:
                        return [], {}

                if not result_set:
                    raise SDKException(&#39;Backupset&#39;, &#39;110&#39;, &#34;Failed to browse for subclient backup content&#34;)

                if &#39;all_versions&#39; in options[&#39;operation&#39;]:
                    return self._process_browse_all_versions_response(result_set,options)

                for result in result_set:
                    name = result.get(&#39;displayName&#39;)
                    snap_display_name = result.get(&#39;name&#39;)

                    if &#39;path&#39; in result:
                        path = result[&#39;path&#39;]
                    else:
                        path = &#39;\\&#39;.join([options[&#39;path&#39;], name])

                    if &#39;modificationTime&#39; in result and int(result[&#39;modificationTime&#39;]) &gt; 0:
                        mod_time = time.localtime(int(result[&#39;modificationTime&#39;]))
                        mod_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, mod_time)
                    else:
                        mod_time = None

                    if &#39;backupTime&#39; in result[&#39;advancedData&#39;] and int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]) &gt; 0:
                        bkp_time = time.localtime(int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]))
                        bkp_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, bkp_time)
                    else:
                        bkp_time = None

                    if &#39;file&#39; in result[&#39;flags&#39;]:
                        if result[&#39;flags&#39;][&#39;file&#39;] is True or result[&#39;flags&#39;][&#39;file&#39;] == &#34;1&#34;:
                            file_or_folder = &#39;File&#39;
                        else:
                            file_or_folder = &#39;Folder&#39;
                    else:
                        file_or_folder = &#39;Folder&#39;

                    if &#39;size&#39; in result:
                        size = result[&#39;size&#39;]
                    else:
                        size = None
                        
                    if show_deleted and &#39;deleted&#39; in result.get(&#39;flags&#39;):
                        deleted = True if result[&#39;flags&#39;].get(&#39;deleted&#39;) in (True, &#39;1&#39;) else False
                    else:
                        deleted = None

                    paths_dict[path] = {
                        &#39;name&#39;: name,
                        &#39;snap_display_name&#39;: snap_display_name,
                        &#39;size&#39;: size,
                        &#39;modified_time&#39;: mod_time,
                        &#39;type&#39;: file_or_folder,
                        &#39;backup_time&#39;: bkp_time,
                        &#39;advanced_data&#39;: result[&#39;advancedData&#39;],
                        &#39;deleted&#39;: deleted
                    }

                    paths.append(path)

                return paths, paths_dict

        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def index_server(self):
        &#34;&#34;&#34;Returns the index server client set for the backupset&#34;&#34;&#34;

        client_name = None

        if &#39;indexSettings&#39; in self._properties:
            if &#39;currentIndexServer&#39; in self._properties[&#39;indexSettings&#39;]:
                client_name = self._properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]

        if client_name is not None:
            return Client(self._commcell_object, client_name=client_name)

        return None

    @index_server.setter
    def index_server(self, value):
        &#34;&#34;&#34;Sets index server client for the backupset. Property value should be a client object

            Args:

                value     (obj)    --  The cvpysdk client object of the index server client

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(value, Client):
            raise SDKException(&#39;Backupset&#39;, &#39;106&#39;)

        properties = self._properties
        index_server_id = int(value.client_id)
        index_server_name = value.client_name

        if &#39;indexSettings&#39; in properties:
            qualified_index_servers = []
            if &#39;qualifyingIndexServers&#39; in properties[&#39;indexSettings&#39;]:
                for index_server in properties[&#39;indexSettings&#39;][&#39;qualifyingIndexServers&#39;]:
                    qualified_index_servers.append(index_server[&#39;clientId&#39;])

            if index_server_id in qualified_index_servers:
                properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;] = {
                    &#39;clientId&#39;: index_server_id,
                    &#39;clientName&#39;: index_server_name
                }
            else:
                raise SDKException(
                    &#39;Backupset&#39;, &#39;102&#39;, &#39;{0} is not a qualified IndexServer client&#39;.format(
                        index_server_name))
        else:
            properties[&#39;indexSettings&#39;] = {
                &#39;currentIndexServer&#39;: {
                    &#39;clientId&#39;: index_server_id,
                    &#39;clientName&#39;: index_server_name
                }
            }

        request_json = {
            &#39;backupsetProperties&#39;: properties
        }

        self._process_update_reponse(request_json)

    @property
    def vm_filter(self):
        &#34;&#34;&#34;Returns the vm filters set at the backupset level
        Returns:
                list - list of content associated as the filters with the backupset
        &#34;&#34;&#34;

        return self.hidden_subclient.content

    @vm_filter.setter
    def vm_filter(self, content):
        &#34;&#34;&#34;
        Creates the list of content JSON to pass to the API to update
           content of the vm filter of the backupset
        Args:
            content (list)  --  list of the content to add as the filters to the
                Backupset. list should contain name and type

        &#34;&#34;&#34;
        self.hidden_subclient.content = content
        self.hidden_subclient.refresh()

    @property
    def vm_disk_filter(self):
        &#34;&#34;&#34;Returns the vm disk filters set at the backupset level
        Returns:
                list - list of content associated as the disk filters with the backupset
        &#34;&#34;&#34;
        return self.hidden_subclient.vm_diskfilter

    @vm_disk_filter.setter
    def vm_disk_filter(self, vm_diskfilter):
        &#34;&#34;&#34;
        Creates the list of disk filter content JSON to pass to the API to update
           content of the vm disk filter of the backupset
        Args:
            vm_diskfilter (list) --     list of the Disk filter to add
                                                 to the backupset

        &#34;&#34;&#34;
        self.hidden_subclient.vm_diskfilter = vm_diskfilter
        self.hidden_subclient.refresh()</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.backupset.Backupset" href="../backupset.html#cvpysdk.backupset.Backupset">Backupset</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.backupsets.vsbackupset.VSBackupset.hidden_subclient"><code class="name">var <span class="ident">hidden_subclient</span></code></dt>
<dd>
<div class="desc"><p>Creates the object for the hidden subclient</p>
<h2 id="returns">Returns</h2>
<p>_hidden_subclient - object of the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/vsbackupset.py#L75-L95" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def hidden_subclient(self):
    &#34;&#34;&#34;Creates the object for the hidden subclient
    Returns:
            _hidden_subclient - object of the subclient

    &#34;&#34;&#34;
    if not self._hidden_subclient:
        hidden_subclient_service = self._commcell_object._services[&#39;VSA_HIDDEN_SUBCLIENT&#39;] % (
            self._client_object.client_name, self.backupset_name)
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#34;GET&#34;, hidden_subclient_service)
        if flag:
            if response.json():
                hidden_subclient_id = response.json().get(&#39;subclientId&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        self._hidden_subclient = subclient(self, &#39;Do Not Backup&#39;, hidden_subclient_id)
    return self._hidden_subclient</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.vsbackupset.VSBackupset.index_server"><code class="name">var <span class="ident">index_server</span></code></dt>
<dd>
<div class="desc"><p>Returns the index server client set for the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/vsbackupset.py#L282-L295" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_server(self):
    &#34;&#34;&#34;Returns the index server client set for the backupset&#34;&#34;&#34;

    client_name = None

    if &#39;indexSettings&#39; in self._properties:
        if &#39;currentIndexServer&#39; in self._properties[&#39;indexSettings&#39;]:
            client_name = self._properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]

    if client_name is not None:
        return Client(self._commcell_object, client_name=client_name)

    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.vsbackupset.VSBackupset.vm_disk_filter"><code class="name">var <span class="ident">vm_disk_filter</span></code></dt>
<dd>
<div class="desc"><p>Returns the vm disk filters set at the backupset level</p>
<h2 id="returns">Returns</h2>
<p>list - list of content associated as the disk filters with the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/vsbackupset.py#L371-L377" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_disk_filter(self):
    &#34;&#34;&#34;Returns the vm disk filters set at the backupset level
    Returns:
            list - list of content associated as the disk filters with the backupset
    &#34;&#34;&#34;
    return self.hidden_subclient.vm_diskfilter</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.vsbackupset.VSBackupset.vm_filter"><code class="name">var <span class="ident">vm_filter</span></code></dt>
<dd>
<div class="desc"><p>Returns the vm filters set at the backupset level</p>
<h2 id="returns">Returns</h2>
<p>list - list of content associated as the filters with the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/vsbackupset.py#L349-L356" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_filter(self):
    &#34;&#34;&#34;Returns the vm filters set at the backupset level
    Returns:
            list - list of content associated as the filters with the backupset
    &#34;&#34;&#34;

    return self.hidden_subclient.content</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.backupsets.vsbackupset.VSBackupset.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the content of the Backupset.</p>
<h2 id="args">Args</h2>
<p>Dictionary of browse options:
Example:</p>
<pre><code>    browse({
        'path': 'c:\\hello',

        'show_deleted': True,

        'from_time': '2014-04-20 12:00:00',

        'to_time': '2016-04-21 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of browse options:
Example:</p>
<pre><code>    browse(
        path='c:\hello',

        show_deleted=True,

        from_time='2014-04-20 12:00:00',

        to_time='2016-04-21 12:00:00'
    )
</code></pre>
<h2 id="returns">Returns</h2>
<p>(list, dict)
list
-
List of only the file, folder paths from the browse response</p>
<pre><code>dict    -   Dictionary of all the paths with additional metadata retrieved
from browse operation
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/vsbackupset.py#L97-L141" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, *args, **kwargs):
    &#34;&#34;&#34;Browses the content of the Backupset.

        Args:
            Dictionary of browse options:
                Example:

                    browse({
                        &#39;path&#39;: &#39;c:\\\\hello&#39;,

                        &#39;show_deleted&#39;: True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of browse options:
                Example:

                    browse(
                        path=&#39;c:\\hello&#39;,

                        show_deleted=True,

                        from_time=&#39;2014-04-20 12:00:00&#39;,

                        to_time=&#39;2016-04-21 12:00:00&#39;
                    )

        Returns:
            (list, dict)
                list    -   List of only the file, folder paths from the browse response

                dict    -   Dictionary of all the paths with additional metadata retrieved
                from browse operation
    &#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs

    options[&#39;retry_count&#39;] = 0
    return self._do_browse(options)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.backupset.Backupset" href="../backupset.html#cvpysdk.backupset.Backupset">Backupset</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.backupset.Backupset.backed_up_files_count" href="../backupset.html#cvpysdk.backupset.Backupset.backed_up_files_count">backed_up_files_count</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backup" href="../backupset.html#cvpysdk.backupset.Backupset.backup">backup</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_id" href="../backupset.html#cvpysdk.backupset.Backupset.backupset_id">backupset_id</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_name" href="../backupset.html#cvpysdk.backupset.Backupset.backupset_name">backupset_name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.delete_data" href="../backupset.html#cvpysdk.backupset.Backupset.delete_data">delete_data</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.description" href="../backupset.html#cvpysdk.backupset.Backupset.description">description</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.find" href="../backupset.html#cvpysdk.backupset.Backupset.find">find</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.guid" href="../backupset.html#cvpysdk.backupset.Backupset.guid">guid</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_default_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.is_default_backupset">is_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_on_demand_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.is_on_demand_backupset">is_on_demand_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.list_media" href="../backupset.html#cvpysdk.backupset.Backupset.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.name" href="../backupset.html#cvpysdk.backupset.Backupset.name">name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.plan" href="../backupset.html#cvpysdk.backupset.Backupset.plan">plan</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.properties" href="../backupset.html#cvpysdk.backupset.Backupset.properties">properties</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.refresh" href="../backupset.html#cvpysdk.backupset.Backupset.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.set_default_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.set_default_backupset">set_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.update_properties" href="../backupset.html#cvpysdk.backupset.Backupset.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.backupsets" href="index.html">cvpysdk.backupsets</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.backupsets.vsbackupset.VSBackupset" href="#cvpysdk.backupsets.vsbackupset.VSBackupset">VSBackupset</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.backupsets.vsbackupset.VSBackupset.browse" href="#cvpysdk.backupsets.vsbackupset.VSBackupset.browse">browse</a></code></li>
<li><code><a title="cvpysdk.backupsets.vsbackupset.VSBackupset.hidden_subclient" href="#cvpysdk.backupsets.vsbackupset.VSBackupset.hidden_subclient">hidden_subclient</a></code></li>
<li><code><a title="cvpysdk.backupsets.vsbackupset.VSBackupset.index_server" href="#cvpysdk.backupsets.vsbackupset.VSBackupset.index_server">index_server</a></code></li>
<li><code><a title="cvpysdk.backupsets.vsbackupset.VSBackupset.vm_disk_filter" href="#cvpysdk.backupsets.vsbackupset.VSBackupset.vm_disk_filter">vm_disk_filter</a></code></li>
<li><code><a title="cvpysdk.backupsets.vsbackupset.VSBackupset.vm_filter" href="#cvpysdk.backupsets.vsbackupset.VSBackupset.vm_filter">vm_filter</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>