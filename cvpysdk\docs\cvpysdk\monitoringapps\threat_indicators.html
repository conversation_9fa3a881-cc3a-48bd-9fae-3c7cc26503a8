<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.monitoringapps.threat_indicators API documentation</title>
<meta name="description" content="Main file for performing threat/file type analysis on clients/laptops …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.monitoringapps.threat_indicators</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing threat/file type analysis on clients/laptops</p>
<p>TAServers , TAServer, AnomalyType are the three classes defined in this file</p>
<p>TAServers - class to represent all servers in threat indicators</p>
<p>TAServer
- class to represent single server in threat indicators</p>
<p>AnomalyType - class to represent different types of anomaly</p>
<h2 id="taservers">Taservers</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the TAServers class</p>
<p>_get_clients_count()
&ndash;
returns total client on threat indicators for this CS</p>
<p>_get_monitored_vm_count()
&ndash;
returns monitored vm count on threat indicators for this CS</p>
<p>_get_threat_indicators()
&ndash;
returns the list of threat indicators client for this CS</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refreshes the threat indicators servers for this CS</p>
<p>has()
&ndash;
Checks whether given server name exists in threat indicators or not</p>
<p>get()
&ndash;
returns the server class object for given server name</p>
<p>run_scan()
&ndash;
runs anomaly scan on given server</p>
<p>TAServers Attributes:</p>
<pre><code>**clients_count**       --  returns the total clients stats from threat indicators from CS

**monitored_vms**       --  returns the monitored vms stats from threat indicators from CS
</code></pre>
<h2 id="taserver">Taserver</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the TAServer class</p>
<p>_get_anomalies_stats()
&ndash;
returns the anomalies stats for this client</p>
<p>_get_anomaly_records()
&ndash;
returns list containing files anomaly record details of this client</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refreshes the server anomalies</p>
<p>clear_anomaly()
&ndash;
clears the anomalies present for this client</p>
<p>Server Attributes:</p>
<pre><code>**anomaly_records**             --  returns the list of anomaly records for this client

**threat_anomaly_stats**        --  returns the dict of threat anomalies stats for this client

**anomaly_stats**               --  returns the dict of file types/data anomalies stats for this client

**datasource_id**               --  associated data source id for threat scan / analysis server

**anomaly_file_count**          --  returns the total anomaly file count for this client
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L1-L602" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing threat/file type analysis on clients/laptops

TAServers , TAServer, AnomalyType are the three classes defined in this file

TAServers - class to represent all servers in threat indicators

TAServer  - class to represent single server in threat indicators

AnomalyType - class to represent different types of anomaly

TAServers:

    __init__()                          --  initialise object of the TAServers class

    _get_clients_count()                --  returns total client on threat indicators for this CS

    _get_monitored_vm_count()           --  returns monitored vm count on threat indicators for this CS

    _get_threat_indicators()            --  returns the list of threat indicators client for this CS

    _response_not_success()             --  parses through the exception response, and raises SDKException

    refresh()                           --  refreshes the threat indicators servers for this CS

    has()                               --  Checks whether given server name exists in threat indicators or not

    get()                               --  returns the server class object for given server name

    run_scan()                          --  runs anomaly scan on given server

TAServers Attributes:

    **clients_count**       --  returns the total clients stats from threat indicators from CS

    **monitored_vms**       --  returns the monitored vms stats from threat indicators from CS

TAServer:

    __init__()                          --  initialise object of the TAServer class

    _get_anomalies_stats()              --  returns the anomalies stats for this client

    _get_anomaly_records()              --  returns list containing files anomaly record details of this client

    _response_not_success()             --  parses through the exception response, and raises SDKException

    refresh()                           --  refreshes the server anomalies

    clear_anomaly()                     --  clears the anomalies present for this client

Server Attributes:

    **anomaly_records**             --  returns the list of anomaly records for this client

    **threat_anomaly_stats**        --  returns the dict of threat anomalies stats for this client

    **anomaly_stats**               --  returns the dict of file types/data anomalies stats for this client

    **datasource_id**               --  associated data source id for threat scan / analysis server

    **anomaly_file_count**          --  returns the total anomaly file count for this client


&#34;&#34;&#34;
import copy
import datetime
import enum
import time

from ..exception import SDKException
from ..monitoringapps.constants import ThreatConstants, FileTypeConstants, RequestConstants


class AnomalyType(enum.Enum):
    &#34;&#34;&#34;Enum class for Anomaly type&#34;&#34;&#34;
    FILE_ACTIVITY = 16
    FILE_TYPE = 32
    THREAT_ANALYSIS = 64
    FILE_DATA = 128
    EXTENSION_BASED = 512
    DATA_WRITTEN = 4096


class TAServers():
    &#34;&#34;&#34;class to represent all servers in threat indicators&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the TAServers class.

            Args:

                commcell_object     (object)    --  instance of the commcell class

            Returns:

                object  -   instance of the Servers class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._threat_indicators = []
        self._servers = []
        self._total_clients = None
        self._monitored_vms = None
        self._API_GET_ALL_INDICATORS = self._services[&#39;GET_THREAT_INDICATORS&#39;]
        self._API_RUN_SCAN = self._services[&#39;RUN_ANOMALY_SCAN&#39;]
        self._API_CLIENTS_COUNT = self._services[&#39;ANOMALY_CLIENTS_COUNT&#39;]
        self._API_MONITORED_VMS = self._services[&#39;MONITORED_VM_COUNT&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_clients_count(self):
        &#34;&#34;&#34;returns the client count details for Threat inidcators on this CS

            Args:

                None

            Returns:

                dict  - Containing total client stats [stats for client type = fileserver,vm,laptop]

            Raises:

                SDKException:

                    if failed to fetch details
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_CLIENTS_COUNT)
        if flag:
            if response.json():
                return response.json()
            elif bool(response.json()):
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;110&#39;)
        self._response_not_success(response)

    def _get_monitored_vm_count(self):
        &#34;&#34;&#34;returns the monitored vm count stats for Threat inidcators on this CS

            Args:

                None

            Returns:

                dict  - Containing total monitored vm stats

            Raises:

                SDKException:

                    if failed to fetch details
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_MONITORED_VMS)
        if flag:
            if response.json():
                return response.json()
            elif bool(response.json()):
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;111&#39;)
        self._response_not_success(response)


    def _get_threat_indicators(self):
        &#34;&#34;&#34;returns the list of threat indicators for this CS

            Args:

                None

            Returns:

                list(dict)      - server threat details

                        Eg:-

                                {
                                  &#34;anomalyType&#34;: 8,
                                  &#34;modCount&#34;: 13705,
                                  &#34;renameCount&#34;: 1,
                                  &#34;isVMeSupported&#34;: true,
                                  &#34;refTime&#34;: 1727519811,
                                  &#34;deleteCount&#34;: 73280,
                                  &#34;createCount&#34;: 19,
                                  &#34;location&#34;: &#34;&#34;,
                                  &#34;osInfo&#34;: {
                                    &#34;osInfo&#34;: {
                                      &#34;Type&#34;: &#34;Windows&#34;,
                                      &#34;SubType&#34;: &#34;Server&#34;,
                                      &#34;osId&#34;: 210,
                                      &#34;OsDisplayInfo&#34;: {
                                        &#34;ProcessorType&#34;: &#34;WinX64&#34;,
                                        &#34;OSName&#34;: &#34;Windows Server 2019 Datacenter&#34;
                                      }
                                    }
                                  },
                                  &#34;client&#34;: {
                                    &#34;clientId&#34;: 9,
                                    &#34;clientName&#34;: &#34;xx&#34;,
                                    &#34;displayName&#34;: &#34;xx_dn&#34;
                                  }
                                }
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_GET_ALL_INDICATORS)
        _threat_indicators = []
        if flag:
            if response.json() and &#39;anomalyClients&#39; in response.json():
                _threat_indicators = response.json()[&#39;anomalyClients&#39;]
                for _client in _threat_indicators:
                    if &#39;client&#39; in _client:
                        _display_name = _client[&#39;client&#39;].get(&#39;displayName&#39;, &#39;&#39;)
                        self._servers.append(_display_name.lower())
            elif bool(response.json()):
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;103&#39;)
            return _threat_indicators
        self._response_not_success(response)

    def has(self, name):
        &#34;&#34;&#34;Checks whether given server name exists in threat indicators or not

            Args:

                name        (str)       --  Name of the server

            Returns:

                bool    --  True if server name exists in threat indicators

        &#34;&#34;&#34;
        if name.lower() in self._servers:
            return True
        return False

    def run_scan(
            self,
            server_name,
            anomaly_types,
            index_server_name=None,
            storage_pool=None,
            from_time=None,
            to_time=None):
        &#34;&#34;&#34;runs anomaly scan on given server name

            Args:

                server_name     (str)           --  Server name to analyze

                anomaly_types    (list)         --  list of anomaly to analyze on client

                index_server_name   (str)       --  Index server name to be used for scan

                storage_pool        (str)       --  Storage pool name to be used for scan

                from_time           (int)       --  epoch timestamp from when scan will analyze in backup

                to_time             (int)       --  epoch timestamp to which scan will analyze in backup

            Returns:

                int --  job id of scan job launched

            Raises:

                SDKException:

                    if failed to start job

                    if input data type is not valid

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
        if not isinstance(anomaly_types, list):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
        if not self._commcell_object.clients.has_client(server_name):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;102&#39;, &#39;Given server is not found in CS&#39;)
        req_json = copy.deepcopy(RequestConstants.RUN_SCAN_JSON)
        req_json[&#39;client&#39;][&#39;clientId&#39;] = int(self._commcell_object.clients.get(server_name).client_id)
        ta_flag = 0
        for each_anomaly in anomaly_types:
            if each_anomaly.name == AnomalyType.FILE_DATA.name:
                ta_flag = ta_flag + 2
            elif each_anomaly.name == AnomalyType.THREAT_ANALYSIS.name:
                ta_flag = ta_flag + 1
        req_json[&#39;threatAnalysisFlags&#39;] = int(ta_flag)
        if not from_time and not to_time:
            to_time = int(time.time())
            req_json[&#39;timeRange&#39;][&#39;toTime&#39;] = int(to_time)
            to_time = datetime.datetime.fromtimestamp(to_time)
            from_time = to_time - datetime.timedelta(days=7)
            req_json[&#39;timeRange&#39;][&#39;fromTime&#39;] = int(from_time.timestamp())
        else:
            req_json[&#39;timeRange&#39;][&#39;toTime&#39;] = to_time
            req_json[&#39;timeRange&#39;][&#39;fromTime&#39;] = from_time

        if int(self._commcell_object.commserv_oem_id) == 119:
            if not storage_pool:
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;107&#39;)
            req_json.pop(&#39;indexServer&#39;)
            spool_obj = self._commcell_object.storage_pools.get(storage_pool)
            req_json[&#39;backupDetails&#39;][0][&#39;copyId&#39;] = int(spool_obj.copy_id)
            req_json[&#39;backupDetails&#39;][0][&#39;storagePoolId&#39;] = int(spool_obj.storage_pool_id)
        else:
            req_json.pop(&#39;backupDetails&#39;)
            if not index_server_name:
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;108&#39;)
            req_json[&#39;indexServer&#39;][&#39;clientId&#39;] = int(
                self._commcell_object.index_servers.get(index_server_name).index_server_client_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_RUN_SCAN, req_json
        )
        if flag:
            if response.json() and &#39;jobId&#39; in response.json():
                return response.json()[&#39;jobId&#39;]
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;109&#39;)
        self._response_not_success(response)

    def get(self, server_name):
        &#34;&#34;&#34;returns Server class object for given server name

                Args:

                    server_name        (str)       --  client name

                Returns:

                    obj --  Instance of Server class

                Raises:

                    SDKException:

                            if failed to find given server

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
        if not self.has(server_name):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;105&#39;)
        return TAServer(commcell_object=self._commcell_object, server_name=server_name)

    def refresh(self):
        &#34;&#34;&#34;Refresh the threat indicator servers associated with CS&#34;&#34;&#34;
        self._servers = []
        self._total_clients = None
        self._monitored_vms = None
        self._threat_indicators = self._get_threat_indicators()
        self._total_clients = self._get_clients_count()
        self._monitored_vms = self._get_monitored_vm_count()

    @property
    def monitored_vms(self):
        &#34;&#34;&#34;returns the monitored vms stats from threat indicators on this CS

            Returns:

                dict --  client stats

        &#34;&#34;&#34;
        return self._monitored_vms

    @property
    def clients_count(self):
        &#34;&#34;&#34;returns the client stats from threat indicators on this CS

            Returns:

                dict --  client stats

        &#34;&#34;&#34;
        return self._total_clients



class TAServer:

    def __init__(self, commcell_object, server_name):
        &#34;&#34;&#34;Initializes an instance of the TAServer class.

            Args:

                commcell_object     (object)    --  instance of the commcell class

                server_name         (str)       --  Name of the server


            Returns:

                object  -   instance of the Server class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._server_name = server_name
        self._server_id = self._commcell_object.clients.get(server_name).client_id
        self._anomaly_records = None
        self._threat_anomaly_stats = None
        self._anomaly_stats = None
        self._threat_dsid = None
        self._API_GET_ALL_INDICATORS = self._services[&#39;GET_THREAT_INDICATORS&#39;]
        self._API_GET_ALL_ANOMALIES = self._services[&#39;GET_ALL_CLIENT_ANOMALIES&#39;]
        self._API_CLEAR_ANOMALIES = self._services[&#39;CLEAR_ANOMALIES&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_anomalies_stats(self):
        &#34;&#34;&#34;returns the anomalies stats for this client&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_GET_ALL_INDICATORS)
        threat_stats = {}
        stats = {}
        if flag:
            if response.json() and &#39;anomalyClients&#39; in response.json():
                _resp = response.json()[&#39;anomalyClients&#39;]
                for _client in _resp:
                    if &#39;client&#39; in _client:
                        if _client[&#39;client&#39;].get(&#39;displayName&#39;, &#39;&#39;).lower() == self._server_name.lower():
                            threat_stats[ThreatConstants.FIELD_INFECTED_COUNT] = _client.get(
                                ThreatConstants.FIELD_INFECTED_COUNT, 0)
                            threat_stats[ThreatConstants.FIELD_FINGERPRINT_COUNT] = _client.get(
                                ThreatConstants.FIELD_FINGERPRINT_COUNT, 0)
                            stats[FileTypeConstants.FIELD_CREATE_COUNT] = _client.get(
                                FileTypeConstants.FIELD_CREATE_COUNT, 0)
                            stats[FileTypeConstants.FIELD_DELETE_COUNT] = _client.get(
                                FileTypeConstants.FIELD_DELETE_COUNT, 0)
                            stats[FileTypeConstants.FIELD_MODIFIED_COUNT] = _client.get(
                                FileTypeConstants.FIELD_MODIFIED_COUNT, 0)
                            stats[FileTypeConstants.FIELD_RENAME_COUNT] = _client.get(
                                FileTypeConstants.FIELD_RENAME_COUNT, 0)
                            self._threat_dsid = _client.get(&#39;dataSourceId&#39;, 0)
            elif bool(response.json()):
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;103&#39;)
            return stats, threat_stats
        self._response_not_success(response)

    def _get_anamoly_records(self):
        &#34;&#34;&#34;returns file type anomaly records for this client&#34;&#34;&#34;
        api = self._API_GET_ALL_ANOMALIES % (0, self._server_id)  # filter=0 to fetch all anomalies types
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        if flag:
            if response.json() and &#39;clientInfo&#39; in response.json():
                _resp = response.json()[&#39;clientInfo&#39;][0]
                if &#39;anomalyRecordList&#39; in _resp:
                    return _resp[&#39;anomalyRecordList&#39;]
                else:
                    # for fingerprint analysis, record list will be empty
                    return []
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the anomalies details associated with this server&#34;&#34;&#34;
        self._threat_dsid = 0
        self._anomaly_records = self._get_anamoly_records()
        self._anomaly_stats, self._threat_anomaly_stats = self._get_anomalies_stats()

    def clear_anomaly(self, anomaly_types):
        &#34;&#34;&#34;clears the anomalies for this server

            Args:

                anomaly_types       (list)      --  list of anomalies to clear (Refer to AnomalyType class)

            Returns:

                None

            Raises:

                SDKException:

                    if failed to clear anomaly

                    if input is not valid

        &#34;&#34;&#34;
        if not isinstance(anomaly_types, list):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
        anomalies_to_clear = []
        for each_anomaly in anomaly_types:
            anomalies_to_clear.append(each_anomaly.name)
        _req_json = copy.deepcopy(RequestConstants.CLEAR_ANOMALY_JSON)
        _req_json[&#39;clients&#39;][0][&#39;clientId&#39;] = int(self._server_id)
        _req_json[&#39;clients&#39;][0][&#39;displayName&#39;] = self._server_name
        _req_json[&#39;anomalyTypes&#39;] = anomalies_to_clear
        if AnomalyType.FILE_DATA.name in anomalies_to_clear or AnomalyType.THREAT_ANALYSIS.name in anomalies_to_clear:
            _req_json[&#39;clients&#39;][0][&#39;dataSourceId&#39;] = self.datasource_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CLEAR_ANOMALIES, _req_json
        )
        if flag:
            if response.json() and &#39;error&#39; in response.json():
                response = response.json()[&#39;error&#39;]
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;ThreatIndicators&#39;,
                        &#39;106&#39;)
                elif &#39;errorCode&#39; not in response:
                    raise SDKException(
                        &#39;ThreatIndicators&#39;,
                        &#39;102&#39;,
                        f&#39;Something went wrong during clear anomaly - {response.json()}&#39;)
                self.refresh()
                return
        self._response_not_success(response)

    @property
    def anomaly_records(self):
        &#34;&#34;&#34;returns the anomaly records list

            Returns:

                list --  anomaly records for this client

        &#34;&#34;&#34;
        return self._anomaly_records

    @property
    def threat_anomaly_stats(self):
        &#34;&#34;&#34;returns the threat anomaly stats

            Returns:

                dict --  threat anomaly stats for this client

        &#34;&#34;&#34;
        return self._threat_anomaly_stats

    @property
    def anomaly_stats(self):
        &#34;&#34;&#34;returns the file type/data anomaly stats

            Returns:

                dict --  file/data anomaly stats for this client

        &#34;&#34;&#34;
        return self._anomaly_stats

    @property
    def datasource_id(self):
        &#34;&#34;&#34;returns the threat datasource_id associated with this server

            Returns:

                int --  datasource id

        &#34;&#34;&#34;
        return int(self._threat_dsid)

    @property
    def anomaly_file_count(self):
        &#34;&#34;&#34;returns the total anomalies file count for this server

            Returns:

                int --  anomaly file count

        &#34;&#34;&#34;
        _total_files = 0
        _total_files = _total_files + sum(self._anomaly_stats.values())
        _total_files = _total_files + sum(self._threat_anomaly_stats.values())
        return _total_files</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.monitoringapps.threat_indicators.AnomalyType"><code class="flex name class">
<span>class <span class="ident">AnomalyType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Enum class for Anomaly type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L89-L96" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AnomalyType(enum.Enum):
    &#34;&#34;&#34;Enum class for Anomaly type&#34;&#34;&#34;
    FILE_ACTIVITY = 16
    FILE_TYPE = 32
    THREAT_ANALYSIS = 64
    FILE_DATA = 128
    EXTENSION_BASED = 512
    DATA_WRITTEN = 4096</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.monitoringapps.threat_indicators.AnomalyType.DATA_WRITTEN"><code class="name">var <span class="ident">DATA_WRITTEN</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.AnomalyType.EXTENSION_BASED"><code class="name">var <span class="ident">EXTENSION_BASED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.AnomalyType.FILE_ACTIVITY"><code class="name">var <span class="ident">FILE_ACTIVITY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.AnomalyType.FILE_DATA"><code class="name">var <span class="ident">FILE_DATA</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.AnomalyType.FILE_TYPE"><code class="name">var <span class="ident">FILE_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.AnomalyType.THREAT_ANALYSIS"><code class="name">var <span class="ident">THREAT_ANALYSIS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServer"><code class="flex name class">
<span>class <span class="ident">TAServer</span></span>
<span>(</span><span>commcell_object, server_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Initializes an instance of the TAServer class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>server_name
(str)
&ndash;
Name of the server</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Server class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L403-L602" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TAServer:

    def __init__(self, commcell_object, server_name):
        &#34;&#34;&#34;Initializes an instance of the TAServer class.

            Args:

                commcell_object     (object)    --  instance of the commcell class

                server_name         (str)       --  Name of the server


            Returns:

                object  -   instance of the Server class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._server_name = server_name
        self._server_id = self._commcell_object.clients.get(server_name).client_id
        self._anomaly_records = None
        self._threat_anomaly_stats = None
        self._anomaly_stats = None
        self._threat_dsid = None
        self._API_GET_ALL_INDICATORS = self._services[&#39;GET_THREAT_INDICATORS&#39;]
        self._API_GET_ALL_ANOMALIES = self._services[&#39;GET_ALL_CLIENT_ANOMALIES&#39;]
        self._API_CLEAR_ANOMALIES = self._services[&#39;CLEAR_ANOMALIES&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_anomalies_stats(self):
        &#34;&#34;&#34;returns the anomalies stats for this client&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_GET_ALL_INDICATORS)
        threat_stats = {}
        stats = {}
        if flag:
            if response.json() and &#39;anomalyClients&#39; in response.json():
                _resp = response.json()[&#39;anomalyClients&#39;]
                for _client in _resp:
                    if &#39;client&#39; in _client:
                        if _client[&#39;client&#39;].get(&#39;displayName&#39;, &#39;&#39;).lower() == self._server_name.lower():
                            threat_stats[ThreatConstants.FIELD_INFECTED_COUNT] = _client.get(
                                ThreatConstants.FIELD_INFECTED_COUNT, 0)
                            threat_stats[ThreatConstants.FIELD_FINGERPRINT_COUNT] = _client.get(
                                ThreatConstants.FIELD_FINGERPRINT_COUNT, 0)
                            stats[FileTypeConstants.FIELD_CREATE_COUNT] = _client.get(
                                FileTypeConstants.FIELD_CREATE_COUNT, 0)
                            stats[FileTypeConstants.FIELD_DELETE_COUNT] = _client.get(
                                FileTypeConstants.FIELD_DELETE_COUNT, 0)
                            stats[FileTypeConstants.FIELD_MODIFIED_COUNT] = _client.get(
                                FileTypeConstants.FIELD_MODIFIED_COUNT, 0)
                            stats[FileTypeConstants.FIELD_RENAME_COUNT] = _client.get(
                                FileTypeConstants.FIELD_RENAME_COUNT, 0)
                            self._threat_dsid = _client.get(&#39;dataSourceId&#39;, 0)
            elif bool(response.json()):
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;103&#39;)
            return stats, threat_stats
        self._response_not_success(response)

    def _get_anamoly_records(self):
        &#34;&#34;&#34;returns file type anomaly records for this client&#34;&#34;&#34;
        api = self._API_GET_ALL_ANOMALIES % (0, self._server_id)  # filter=0 to fetch all anomalies types
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, api)
        if flag:
            if response.json() and &#39;clientInfo&#39; in response.json():
                _resp = response.json()[&#39;clientInfo&#39;][0]
                if &#39;anomalyRecordList&#39; in _resp:
                    return _resp[&#39;anomalyRecordList&#39;]
                else:
                    # for fingerprint analysis, record list will be empty
                    return []
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the anomalies details associated with this server&#34;&#34;&#34;
        self._threat_dsid = 0
        self._anomaly_records = self._get_anamoly_records()
        self._anomaly_stats, self._threat_anomaly_stats = self._get_anomalies_stats()

    def clear_anomaly(self, anomaly_types):
        &#34;&#34;&#34;clears the anomalies for this server

            Args:

                anomaly_types       (list)      --  list of anomalies to clear (Refer to AnomalyType class)

            Returns:

                None

            Raises:

                SDKException:

                    if failed to clear anomaly

                    if input is not valid

        &#34;&#34;&#34;
        if not isinstance(anomaly_types, list):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
        anomalies_to_clear = []
        for each_anomaly in anomaly_types:
            anomalies_to_clear.append(each_anomaly.name)
        _req_json = copy.deepcopy(RequestConstants.CLEAR_ANOMALY_JSON)
        _req_json[&#39;clients&#39;][0][&#39;clientId&#39;] = int(self._server_id)
        _req_json[&#39;clients&#39;][0][&#39;displayName&#39;] = self._server_name
        _req_json[&#39;anomalyTypes&#39;] = anomalies_to_clear
        if AnomalyType.FILE_DATA.name in anomalies_to_clear or AnomalyType.THREAT_ANALYSIS.name in anomalies_to_clear:
            _req_json[&#39;clients&#39;][0][&#39;dataSourceId&#39;] = self.datasource_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_CLEAR_ANOMALIES, _req_json
        )
        if flag:
            if response.json() and &#39;error&#39; in response.json():
                response = response.json()[&#39;error&#39;]
                if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                    raise SDKException(
                        &#39;ThreatIndicators&#39;,
                        &#39;106&#39;)
                elif &#39;errorCode&#39; not in response:
                    raise SDKException(
                        &#39;ThreatIndicators&#39;,
                        &#39;102&#39;,
                        f&#39;Something went wrong during clear anomaly - {response.json()}&#39;)
                self.refresh()
                return
        self._response_not_success(response)

    @property
    def anomaly_records(self):
        &#34;&#34;&#34;returns the anomaly records list

            Returns:

                list --  anomaly records for this client

        &#34;&#34;&#34;
        return self._anomaly_records

    @property
    def threat_anomaly_stats(self):
        &#34;&#34;&#34;returns the threat anomaly stats

            Returns:

                dict --  threat anomaly stats for this client

        &#34;&#34;&#34;
        return self._threat_anomaly_stats

    @property
    def anomaly_stats(self):
        &#34;&#34;&#34;returns the file type/data anomaly stats

            Returns:

                dict --  file/data anomaly stats for this client

        &#34;&#34;&#34;
        return self._anomaly_stats

    @property
    def datasource_id(self):
        &#34;&#34;&#34;returns the threat datasource_id associated with this server

            Returns:

                int --  datasource id

        &#34;&#34;&#34;
        return int(self._threat_dsid)

    @property
    def anomaly_file_count(self):
        &#34;&#34;&#34;returns the total anomalies file count for this server

            Returns:

                int --  anomaly file count

        &#34;&#34;&#34;
        _total_files = 0
        _total_files = _total_files + sum(self._anomaly_stats.values())
        _total_files = _total_files + sum(self._threat_anomaly_stats.values())
        return _total_files</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServer.anomaly_file_count"><code class="name">var <span class="ident">anomaly_file_count</span></code></dt>
<dd>
<div class="desc"><p>returns the total anomalies file count for this server</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
anomaly file count</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L590-L602" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def anomaly_file_count(self):
    &#34;&#34;&#34;returns the total anomalies file count for this server

        Returns:

            int --  anomaly file count

    &#34;&#34;&#34;
    _total_files = 0
    _total_files = _total_files + sum(self._anomaly_stats.values())
    _total_files = _total_files + sum(self._threat_anomaly_stats.values())
    return _total_files</code></pre>
</details>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServer.anomaly_records"><code class="name">var <span class="ident">anomaly_records</span></code></dt>
<dd>
<div class="desc"><p>returns the anomaly records list</p>
<h2 id="returns">Returns</h2>
<p>list &ndash;
anomaly records for this client</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L546-L555" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def anomaly_records(self):
    &#34;&#34;&#34;returns the anomaly records list

        Returns:

            list --  anomaly records for this client

    &#34;&#34;&#34;
    return self._anomaly_records</code></pre>
</details>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServer.anomaly_stats"><code class="name">var <span class="ident">anomaly_stats</span></code></dt>
<dd>
<div class="desc"><p>returns the file type/data anomaly stats</p>
<h2 id="returns">Returns</h2>
<p>dict &ndash;
file/data anomaly stats for this client</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L568-L577" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def anomaly_stats(self):
    &#34;&#34;&#34;returns the file type/data anomaly stats

        Returns:

            dict --  file/data anomaly stats for this client

    &#34;&#34;&#34;
    return self._anomaly_stats</code></pre>
</details>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServer.datasource_id"><code class="name">var <span class="ident">datasource_id</span></code></dt>
<dd>
<div class="desc"><p>returns the threat datasource_id associated with this server</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
datasource id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L579-L588" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def datasource_id(self):
    &#34;&#34;&#34;returns the threat datasource_id associated with this server

        Returns:

            int --  datasource id

    &#34;&#34;&#34;
    return int(self._threat_dsid)</code></pre>
</details>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServer.threat_anomaly_stats"><code class="name">var <span class="ident">threat_anomaly_stats</span></code></dt>
<dd>
<div class="desc"><p>returns the threat anomaly stats</p>
<h2 id="returns">Returns</h2>
<p>dict &ndash;
threat anomaly stats for this client</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L557-L566" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def threat_anomaly_stats(self):
    &#34;&#34;&#34;returns the threat anomaly stats

        Returns:

            dict --  threat anomaly stats for this client

    &#34;&#34;&#34;
    return self._threat_anomaly_stats</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServer.clear_anomaly"><code class="name flex">
<span>def <span class="ident">clear_anomaly</span></span>(<span>self, anomaly_types)</span>
</code></dt>
<dd>
<div class="desc"><p>clears the anomalies for this server</p>
<h2 id="args">Args</h2>
<p>anomaly_types
(list)
&ndash;
list of anomalies to clear (Refer to AnomalyType class)</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to clear anomaly

if input is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L496-L544" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def clear_anomaly(self, anomaly_types):
    &#34;&#34;&#34;clears the anomalies for this server

        Args:

            anomaly_types       (list)      --  list of anomalies to clear (Refer to AnomalyType class)

        Returns:

            None

        Raises:

            SDKException:

                if failed to clear anomaly

                if input is not valid

    &#34;&#34;&#34;
    if not isinstance(anomaly_types, list):
        raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
    anomalies_to_clear = []
    for each_anomaly in anomaly_types:
        anomalies_to_clear.append(each_anomaly.name)
    _req_json = copy.deepcopy(RequestConstants.CLEAR_ANOMALY_JSON)
    _req_json[&#39;clients&#39;][0][&#39;clientId&#39;] = int(self._server_id)
    _req_json[&#39;clients&#39;][0][&#39;displayName&#39;] = self._server_name
    _req_json[&#39;anomalyTypes&#39;] = anomalies_to_clear
    if AnomalyType.FILE_DATA.name in anomalies_to_clear or AnomalyType.THREAT_ANALYSIS.name in anomalies_to_clear:
        _req_json[&#39;clients&#39;][0][&#39;dataSourceId&#39;] = self.datasource_id
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_CLEAR_ANOMALIES, _req_json
    )
    if flag:
        if response.json() and &#39;error&#39; in response.json():
            response = response.json()[&#39;error&#39;]
            if &#39;errorCode&#39; in response and response[&#39;errorCode&#39;] != 0:
                raise SDKException(
                    &#39;ThreatIndicators&#39;,
                    &#39;106&#39;)
            elif &#39;errorCode&#39; not in response:
                raise SDKException(
                    &#39;ThreatIndicators&#39;,
                    &#39;102&#39;,
                    f&#39;Something went wrong during clear anomaly - {response.json()}&#39;)
            self.refresh()
            return
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServer.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the anomalies details associated with this server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L490-L494" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the anomalies details associated with this server&#34;&#34;&#34;
    self._threat_dsid = 0
    self._anomaly_records = self._get_anamoly_records()
    self._anomaly_stats, self._threat_anomaly_stats = self._get_anomalies_stats()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServers"><code class="flex name class">
<span>class <span class="ident">TAServers</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>class to represent all servers in threat indicators</p>
<p>Initializes an instance of the TAServers class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Servers class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L99-L399" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TAServers():
    &#34;&#34;&#34;class to represent all servers in threat indicators&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the TAServers class.

            Args:

                commcell_object     (object)    --  instance of the commcell class

            Returns:

                object  -   instance of the Servers class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._threat_indicators = []
        self._servers = []
        self._total_clients = None
        self._monitored_vms = None
        self._API_GET_ALL_INDICATORS = self._services[&#39;GET_THREAT_INDICATORS&#39;]
        self._API_RUN_SCAN = self._services[&#39;RUN_ANOMALY_SCAN&#39;]
        self._API_CLIENTS_COUNT = self._services[&#39;ANOMALY_CLIENTS_COUNT&#39;]
        self._API_MONITORED_VMS = self._services[&#39;MONITORED_VM_COUNT&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_clients_count(self):
        &#34;&#34;&#34;returns the client count details for Threat inidcators on this CS

            Args:

                None

            Returns:

                dict  - Containing total client stats [stats for client type = fileserver,vm,laptop]

            Raises:

                SDKException:

                    if failed to fetch details
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_CLIENTS_COUNT)
        if flag:
            if response.json():
                return response.json()
            elif bool(response.json()):
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;110&#39;)
        self._response_not_success(response)

    def _get_monitored_vm_count(self):
        &#34;&#34;&#34;returns the monitored vm count stats for Threat inidcators on this CS

            Args:

                None

            Returns:

                dict  - Containing total monitored vm stats

            Raises:

                SDKException:

                    if failed to fetch details
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_MONITORED_VMS)
        if flag:
            if response.json():
                return response.json()
            elif bool(response.json()):
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;111&#39;)
        self._response_not_success(response)


    def _get_threat_indicators(self):
        &#34;&#34;&#34;returns the list of threat indicators for this CS

            Args:

                None

            Returns:

                list(dict)      - server threat details

                        Eg:-

                                {
                                  &#34;anomalyType&#34;: 8,
                                  &#34;modCount&#34;: 13705,
                                  &#34;renameCount&#34;: 1,
                                  &#34;isVMeSupported&#34;: true,
                                  &#34;refTime&#34;: 1727519811,
                                  &#34;deleteCount&#34;: 73280,
                                  &#34;createCount&#34;: 19,
                                  &#34;location&#34;: &#34;&#34;,
                                  &#34;osInfo&#34;: {
                                    &#34;osInfo&#34;: {
                                      &#34;Type&#34;: &#34;Windows&#34;,
                                      &#34;SubType&#34;: &#34;Server&#34;,
                                      &#34;osId&#34;: 210,
                                      &#34;OsDisplayInfo&#34;: {
                                        &#34;ProcessorType&#34;: &#34;WinX64&#34;,
                                        &#34;OSName&#34;: &#34;Windows Server 2019 Datacenter&#34;
                                      }
                                    }
                                  },
                                  &#34;client&#34;: {
                                    &#34;clientId&#34;: 9,
                                    &#34;clientName&#34;: &#34;xx&#34;,
                                    &#34;displayName&#34;: &#34;xx_dn&#34;
                                  }
                                }
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._API_GET_ALL_INDICATORS)
        _threat_indicators = []
        if flag:
            if response.json() and &#39;anomalyClients&#39; in response.json():
                _threat_indicators = response.json()[&#39;anomalyClients&#39;]
                for _client in _threat_indicators:
                    if &#39;client&#39; in _client:
                        _display_name = _client[&#39;client&#39;].get(&#39;displayName&#39;, &#39;&#39;)
                        self._servers.append(_display_name.lower())
            elif bool(response.json()):
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;103&#39;)
            return _threat_indicators
        self._response_not_success(response)

    def has(self, name):
        &#34;&#34;&#34;Checks whether given server name exists in threat indicators or not

            Args:

                name        (str)       --  Name of the server

            Returns:

                bool    --  True if server name exists in threat indicators

        &#34;&#34;&#34;
        if name.lower() in self._servers:
            return True
        return False

    def run_scan(
            self,
            server_name,
            anomaly_types,
            index_server_name=None,
            storage_pool=None,
            from_time=None,
            to_time=None):
        &#34;&#34;&#34;runs anomaly scan on given server name

            Args:

                server_name     (str)           --  Server name to analyze

                anomaly_types    (list)         --  list of anomaly to analyze on client

                index_server_name   (str)       --  Index server name to be used for scan

                storage_pool        (str)       --  Storage pool name to be used for scan

                from_time           (int)       --  epoch timestamp from when scan will analyze in backup

                to_time             (int)       --  epoch timestamp to which scan will analyze in backup

            Returns:

                int --  job id of scan job launched

            Raises:

                SDKException:

                    if failed to start job

                    if input data type is not valid

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
        if not isinstance(anomaly_types, list):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
        if not self._commcell_object.clients.has_client(server_name):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;102&#39;, &#39;Given server is not found in CS&#39;)
        req_json = copy.deepcopy(RequestConstants.RUN_SCAN_JSON)
        req_json[&#39;client&#39;][&#39;clientId&#39;] = int(self._commcell_object.clients.get(server_name).client_id)
        ta_flag = 0
        for each_anomaly in anomaly_types:
            if each_anomaly.name == AnomalyType.FILE_DATA.name:
                ta_flag = ta_flag + 2
            elif each_anomaly.name == AnomalyType.THREAT_ANALYSIS.name:
                ta_flag = ta_flag + 1
        req_json[&#39;threatAnalysisFlags&#39;] = int(ta_flag)
        if not from_time and not to_time:
            to_time = int(time.time())
            req_json[&#39;timeRange&#39;][&#39;toTime&#39;] = int(to_time)
            to_time = datetime.datetime.fromtimestamp(to_time)
            from_time = to_time - datetime.timedelta(days=7)
            req_json[&#39;timeRange&#39;][&#39;fromTime&#39;] = int(from_time.timestamp())
        else:
            req_json[&#39;timeRange&#39;][&#39;toTime&#39;] = to_time
            req_json[&#39;timeRange&#39;][&#39;fromTime&#39;] = from_time

        if int(self._commcell_object.commserv_oem_id) == 119:
            if not storage_pool:
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;107&#39;)
            req_json.pop(&#39;indexServer&#39;)
            spool_obj = self._commcell_object.storage_pools.get(storage_pool)
            req_json[&#39;backupDetails&#39;][0][&#39;copyId&#39;] = int(spool_obj.copy_id)
            req_json[&#39;backupDetails&#39;][0][&#39;storagePoolId&#39;] = int(spool_obj.storage_pool_id)
        else:
            req_json.pop(&#39;backupDetails&#39;)
            if not index_server_name:
                raise SDKException(&#39;ThreatIndicators&#39;, &#39;108&#39;)
            req_json[&#39;indexServer&#39;][&#39;clientId&#39;] = int(
                self._commcell_object.index_servers.get(index_server_name).index_server_client_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._API_RUN_SCAN, req_json
        )
        if flag:
            if response.json() and &#39;jobId&#39; in response.json():
                return response.json()[&#39;jobId&#39;]
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;109&#39;)
        self._response_not_success(response)

    def get(self, server_name):
        &#34;&#34;&#34;returns Server class object for given server name

                Args:

                    server_name        (str)       --  client name

                Returns:

                    obj --  Instance of Server class

                Raises:

                    SDKException:

                            if failed to find given server

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
        if not self.has(server_name):
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;105&#39;)
        return TAServer(commcell_object=self._commcell_object, server_name=server_name)

    def refresh(self):
        &#34;&#34;&#34;Refresh the threat indicator servers associated with CS&#34;&#34;&#34;
        self._servers = []
        self._total_clients = None
        self._monitored_vms = None
        self._threat_indicators = self._get_threat_indicators()
        self._total_clients = self._get_clients_count()
        self._monitored_vms = self._get_monitored_vm_count()

    @property
    def monitored_vms(self):
        &#34;&#34;&#34;returns the monitored vms stats from threat indicators on this CS

            Returns:

                dict --  client stats

        &#34;&#34;&#34;
        return self._monitored_vms

    @property
    def clients_count(self):
        &#34;&#34;&#34;returns the client stats from threat indicators on this CS

            Returns:

                dict --  client stats

        &#34;&#34;&#34;
        return self._total_clients</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServers.clients_count"><code class="name">var <span class="ident">clients_count</span></code></dt>
<dd>
<div class="desc"><p>returns the client stats from threat indicators on this CS</p>
<h2 id="returns">Returns</h2>
<p>dict &ndash;
client stats</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L390-L399" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def clients_count(self):
    &#34;&#34;&#34;returns the client stats from threat indicators on this CS

        Returns:

            dict --  client stats

    &#34;&#34;&#34;
    return self._total_clients</code></pre>
</details>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServers.monitored_vms"><code class="name">var <span class="ident">monitored_vms</span></code></dt>
<dd>
<div class="desc"><p>returns the monitored vms stats from threat indicators on this CS</p>
<h2 id="returns">Returns</h2>
<p>dict &ndash;
client stats</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L379-L388" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def monitored_vms(self):
    &#34;&#34;&#34;returns the monitored vms stats from threat indicators on this CS

        Returns:

            dict --  client stats

    &#34;&#34;&#34;
    return self._monitored_vms</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServers.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, server_name)</span>
</code></dt>
<dd>
<div class="desc"><p>returns Server class object for given server name</p>
<h2 id="args">Args</h2>
<p>server_name
(str)
&ndash;
client name</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of Server class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to find given server

    if input is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L344-L368" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, server_name):
    &#34;&#34;&#34;returns Server class object for given server name

            Args:

                server_name        (str)       --  client name

            Returns:

                obj --  Instance of Server class

            Raises:

                SDKException:

                        if failed to find given server

                        if input is not valid

    &#34;&#34;&#34;
    if not isinstance(server_name, str):
        raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
    if not self.has(server_name):
        raise SDKException(&#39;ThreatIndicators&#39;, &#39;105&#39;)
    return TAServer(commcell_object=self._commcell_object, server_name=server_name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServers.has"><code class="name flex">
<span>def <span class="ident">has</span></span>(<span>self, name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks whether given server name exists in threat indicators or not</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
Name of the server</p>
<h2 id="returns">Returns</h2>
<p>bool
&ndash;
True if server name exists in threat indicators</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L243-L257" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has(self, name):
    &#34;&#34;&#34;Checks whether given server name exists in threat indicators or not

        Args:

            name        (str)       --  Name of the server

        Returns:

            bool    --  True if server name exists in threat indicators

    &#34;&#34;&#34;
    if name.lower() in self._servers:
        return True
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServers.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the threat indicator servers associated with CS</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L370-L377" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the threat indicator servers associated with CS&#34;&#34;&#34;
    self._servers = []
    self._total_clients = None
    self._monitored_vms = None
    self._threat_indicators = self._get_threat_indicators()
    self._total_clients = self._get_clients_count()
    self._monitored_vms = self._get_monitored_vm_count()</code></pre>
</details>
</dd>
<dt id="cvpysdk.monitoringapps.threat_indicators.TAServers.run_scan"><code class="name flex">
<span>def <span class="ident">run_scan</span></span>(<span>self, server_name, anomaly_types, index_server_name=None, storage_pool=None, from_time=None, to_time=None)</span>
</code></dt>
<dd>
<div class="desc"><p>runs anomaly scan on given server name</p>
<h2 id="args">Args</h2>
<p>server_name
(str)
&ndash;
Server name to analyze</p>
<p>anomaly_types
(list)
&ndash;
list of anomaly to analyze on client</p>
<p>index_server_name
(str)
&ndash;
Index server name to be used for scan</p>
<p>storage_pool
(str)
&ndash;
Storage pool name to be used for scan</p>
<p>from_time
(int)
&ndash;
epoch timestamp from when scan will analyze in backup</p>
<p>to_time
(int)
&ndash;
epoch timestamp to which scan will analyze in backup</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
job id of scan job launched</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to start job

if input data type is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/monitoringapps/threat_indicators.py#L259-L342" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_scan(
        self,
        server_name,
        anomaly_types,
        index_server_name=None,
        storage_pool=None,
        from_time=None,
        to_time=None):
    &#34;&#34;&#34;runs anomaly scan on given server name

        Args:

            server_name     (str)           --  Server name to analyze

            anomaly_types    (list)         --  list of anomaly to analyze on client

            index_server_name   (str)       --  Index server name to be used for scan

            storage_pool        (str)       --  Storage pool name to be used for scan

            from_time           (int)       --  epoch timestamp from when scan will analyze in backup

            to_time             (int)       --  epoch timestamp to which scan will analyze in backup

        Returns:

            int --  job id of scan job launched

        Raises:

            SDKException:

                if failed to start job

                if input data type is not valid

    &#34;&#34;&#34;
    if not isinstance(server_name, str):
        raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
    if not isinstance(anomaly_types, list):
        raise SDKException(&#39;ThreatIndicators&#39;, &#39;101&#39;)
    if not self._commcell_object.clients.has_client(server_name):
        raise SDKException(&#39;ThreatIndicators&#39;, &#39;102&#39;, &#39;Given server is not found in CS&#39;)
    req_json = copy.deepcopy(RequestConstants.RUN_SCAN_JSON)
    req_json[&#39;client&#39;][&#39;clientId&#39;] = int(self._commcell_object.clients.get(server_name).client_id)
    ta_flag = 0
    for each_anomaly in anomaly_types:
        if each_anomaly.name == AnomalyType.FILE_DATA.name:
            ta_flag = ta_flag + 2
        elif each_anomaly.name == AnomalyType.THREAT_ANALYSIS.name:
            ta_flag = ta_flag + 1
    req_json[&#39;threatAnalysisFlags&#39;] = int(ta_flag)
    if not from_time and not to_time:
        to_time = int(time.time())
        req_json[&#39;timeRange&#39;][&#39;toTime&#39;] = int(to_time)
        to_time = datetime.datetime.fromtimestamp(to_time)
        from_time = to_time - datetime.timedelta(days=7)
        req_json[&#39;timeRange&#39;][&#39;fromTime&#39;] = int(from_time.timestamp())
    else:
        req_json[&#39;timeRange&#39;][&#39;toTime&#39;] = to_time
        req_json[&#39;timeRange&#39;][&#39;fromTime&#39;] = from_time

    if int(self._commcell_object.commserv_oem_id) == 119:
        if not storage_pool:
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;107&#39;)
        req_json.pop(&#39;indexServer&#39;)
        spool_obj = self._commcell_object.storage_pools.get(storage_pool)
        req_json[&#39;backupDetails&#39;][0][&#39;copyId&#39;] = int(spool_obj.copy_id)
        req_json[&#39;backupDetails&#39;][0][&#39;storagePoolId&#39;] = int(spool_obj.storage_pool_id)
    else:
        req_json.pop(&#39;backupDetails&#39;)
        if not index_server_name:
            raise SDKException(&#39;ThreatIndicators&#39;, &#39;108&#39;)
        req_json[&#39;indexServer&#39;][&#39;clientId&#39;] = int(
            self._commcell_object.index_servers.get(index_server_name).index_server_client_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._API_RUN_SCAN, req_json
    )
    if flag:
        if response.json() and &#39;jobId&#39; in response.json():
            return response.json()[&#39;jobId&#39;]
        raise SDKException(&#39;ThreatIndicators&#39;, &#39;109&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.monitoringapps" href="index.html">cvpysdk.monitoringapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.monitoringapps.threat_indicators.AnomalyType" href="#cvpysdk.monitoringapps.threat_indicators.AnomalyType">AnomalyType</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.AnomalyType.DATA_WRITTEN" href="#cvpysdk.monitoringapps.threat_indicators.AnomalyType.DATA_WRITTEN">DATA_WRITTEN</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.AnomalyType.EXTENSION_BASED" href="#cvpysdk.monitoringapps.threat_indicators.AnomalyType.EXTENSION_BASED">EXTENSION_BASED</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.AnomalyType.FILE_ACTIVITY" href="#cvpysdk.monitoringapps.threat_indicators.AnomalyType.FILE_ACTIVITY">FILE_ACTIVITY</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.AnomalyType.FILE_DATA" href="#cvpysdk.monitoringapps.threat_indicators.AnomalyType.FILE_DATA">FILE_DATA</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.AnomalyType.FILE_TYPE" href="#cvpysdk.monitoringapps.threat_indicators.AnomalyType.FILE_TYPE">FILE_TYPE</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.AnomalyType.THREAT_ANALYSIS" href="#cvpysdk.monitoringapps.threat_indicators.AnomalyType.THREAT_ANALYSIS">THREAT_ANALYSIS</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServer" href="#cvpysdk.monitoringapps.threat_indicators.TAServer">TAServer</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServer.anomaly_file_count" href="#cvpysdk.monitoringapps.threat_indicators.TAServer.anomaly_file_count">anomaly_file_count</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServer.anomaly_records" href="#cvpysdk.monitoringapps.threat_indicators.TAServer.anomaly_records">anomaly_records</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServer.anomaly_stats" href="#cvpysdk.monitoringapps.threat_indicators.TAServer.anomaly_stats">anomaly_stats</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServer.clear_anomaly" href="#cvpysdk.monitoringapps.threat_indicators.TAServer.clear_anomaly">clear_anomaly</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServer.datasource_id" href="#cvpysdk.monitoringapps.threat_indicators.TAServer.datasource_id">datasource_id</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServer.refresh" href="#cvpysdk.monitoringapps.threat_indicators.TAServer.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServer.threat_anomaly_stats" href="#cvpysdk.monitoringapps.threat_indicators.TAServer.threat_anomaly_stats">threat_anomaly_stats</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServers" href="#cvpysdk.monitoringapps.threat_indicators.TAServers">TAServers</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServers.clients_count" href="#cvpysdk.monitoringapps.threat_indicators.TAServers.clients_count">clients_count</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServers.get" href="#cvpysdk.monitoringapps.threat_indicators.TAServers.get">get</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServers.has" href="#cvpysdk.monitoringapps.threat_indicators.TAServers.has">has</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServers.monitored_vms" href="#cvpysdk.monitoringapps.threat_indicators.TAServers.monitored_vms">monitored_vms</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServers.refresh" href="#cvpysdk.monitoringapps.threat_indicators.TAServers.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.monitoringapps.threat_indicators.TAServers.run_scan" href="#cvpysdk.monitoringapps.threat_indicators.TAServers.run_scan">run_scan</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>