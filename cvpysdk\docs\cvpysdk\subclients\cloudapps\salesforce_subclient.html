<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.cloudapps.salesforce_subclient API documentation</title>
<meta name="description" content="File for operating on a Salesforce Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.cloudapps.salesforce_subclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Salesforce Subclient.</p>
<p>SalesforceSubclient is the only class defined in this file.</p>
<p>SalesforceSubclient:
Derived class from CloudAppsSubclient Base class, representing a
salesforce subclient, and to perform operations on that subclient</p>
<h2 id="salesforcesubclient">Salesforcesubclient</h2>
<p>_get_subclient_properties()
&ndash;
Subclient class method overwritten to add
salesforce subclient properties as well</p>
<p>_get_subclient_properties_json()
&ndash;
gets all the subclient
related properties of
salesforce subclient.
enable_files
&ndash;
Enables files option on subclient content</p>
<p>disable_files
&ndash;
Disables files option on subclient content</p>
<p>enable_metadata
&ndash;
Enables metadata option on subclient content</p>
<p>disable_metadata
&ndash;
Disables metadata option on subclient content</p>
<p>enable_archived_deleted
&ndash;
Enables backup archived and deleted option on subclient content</p>
<p>disable_archived_deleted
&ndash;
Disables backup archived and deleted
option on subclient content</p>
<p>browse()
&ndash;
Browses the salesforce content</p>
<p>_check_object_in_browse()
&ndash;
internal method to check the object exists
in browse content</p>
<p>_restore_salesforce_options_json()
&ndash;
internal method for salesforce options json</p>
<p>_restore_salesforce_destination_json()
&ndash;
internal method for salesforce destination option
json</p>
<p>restore_to_file_system()
&ndash;
restores the selected content to filesystem</p>
<p>restore_to_database()
&ndash;
restores the selected content to database</p>
<p>restore_to_salesforce_from_database()
&ndash;
restores the selected content to salesforce from
database</p>
<p>restore_to_salesforce_from_media()
&ndash;
restores the selected content to salesforce from
media</p>
<p>_prepare_salesforce_restore_json()
&ndash;
internal method which prepares entire restore
json for salesforce</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L1-L1206" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Salesforce Subclient.

SalesforceSubclient is the only class defined in this file.

SalesforceSubclient:     Derived class from CloudAppsSubclient Base class, representing a
                            salesforce subclient, and to perform operations on that subclient

SalesforceSubclient:

    _get_subclient_properties()               --  Subclient class method overwritten to add
                                                      salesforce subclient properties as well

    _get_subclient_properties_json()          --  gets all the subclient  related properties of
                                                      salesforce subclient.
    enable_files                              --  Enables files option on subclient content

    disable_files                             --  Disables files option on subclient content

    enable_metadata                           --  Enables metadata option on subclient content

    disable_metadata                          --  Disables metadata option on subclient content

    enable_archived_deleted                   --  Enables backup archived and deleted option on subclient content

    disable_archived_deleted                  --  Disables backup archived and deleted  option on subclient content

    browse()                                  --  Browses the salesforce content

    _check_object_in_browse()                 --  internal method to check the object exists
                                                      in browse content

    _restore_salesforce_options_json()        --  internal method for salesforce options json

    _restore_salesforce_destination_json()    --  internal method for salesforce destination option
                                                      json

    restore_to_file_system()                  --  restores the selected content to filesystem

    restore_to_database()                     --  restores the selected content to database

    restore_to_salesforce_from_database()     --  restores the selected content to salesforce from
                                                      database

    restore_to_salesforce_from_media()        --  restores the selected content to salesforce from
                                                      media

    _prepare_salesforce_restore_json()        --  internal method which prepares entire restore
                                                      json for salesforce

&#34;&#34;&#34;

from __future__ import unicode_literals

from base64 import b64encode

from ..casubclient import CloudAppsSubclient

from ...client import Client
from ...agent import Agent
from ...instance import Instance
from ...backupsets.cloudapps.salesforce_backupset import SalesforceBackupset
from ...exception import SDKException


class SalesforceSubclient(CloudAppsSubclient):
    &#34;&#34;&#34;Derived class from CloudAppsSubclient Base class, representing a Salesforce subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            backupset_object  (object)  -- instance of the Backupset class

            subclient_name    (str)     -- name of the subclient

            subclient_id      (str)     -- id of the subclient

        &#34;&#34;&#34;
        self.cloud_apps_subclient_prop = {}
        self._files = None
        self._metadata = None
        self._archived_deleted = None
        self._objects = None
        super(SalesforceSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the properties of this subclient.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(SalesforceSubclient, self)._get_subclient_properties()

        if &#39;cloudAppsSubClientProp&#39; in self._subclient_properties:
            self._cloud_apps_subclient_prop = self._subclient_properties[&#39;cloudAppsSubClientProp&#39;]
            if &#39;salesforceSubclient&#39; in self._cloud_apps_subclient_prop:
                sfsubclient = self._cloud_apps_subclient_prop[&#39;salesforceSubclient&#39;]
                self._objects = sfsubclient.get(&#39;backupSfObjects&#39;)
                self._files = sfsubclient.get(&#39;backupFileObjects&#39;)
                self._metadata = sfsubclient.get(&#39;backupSFMetadata&#39;)
                self._archived_deleted = sfsubclient.get(&#39;backupArchivedandDeletedRecs&#39;)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;: {
                &#34;proxyClient&#34;: self._proxyClient,
                &#34;subClientEntity&#34;: self._subClientEntity,
                &#34;cloudAppsSubClientProp&#34;: self._cloud_apps_subclient_prop,
                &#34;commonProperties&#34;: self._commonProperties,
                &#34;contentOperationType&#34;: 1
            }
        }

        return subclient_json

    @property
    def objects(self):
        &#34;&#34;&#34;getter for salesforce files option&#34;&#34;&#34;
        return self._objects

    @property
    def files(self):
        &#34;&#34;&#34;getter for salesforce files option&#34;&#34;&#34;
        return self._files

    @property
    def metadata(self):
        &#34;&#34;&#34;getter for salesforce metadata option &#34;&#34;&#34;
        return self._metadata

    @property
    def archived_deleted(self):
        &#34;&#34;&#34;getter for salesfoce backup archived and deleted data&#34;&#34;&#34;
        return self._archived_data

    def enable_files(self):
        &#34;&#34;&#34;
        Enables files option on subclient content
        &#34;&#34;&#34;
        if not self.files:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupFileObjects&#39;]&#34;, True)

    def enable_metadata(self):
        &#34;&#34;&#34;
        Enables metadata option on subclient content
        &#34;&#34;&#34;
        if not self.metadata:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupSFMetadata&#39;]&#34;, True)

    def enable_archived_deleted(self):
        &#34;&#34;&#34;
        Enables backup archived deleted option on subclient content
        &#34;&#34;&#34;
        if self.archived_deleted:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupArchivedandDeletedRecs&#39;]&#34;, False)

    def disable_files(self):
        &#34;&#34;&#34;
        Disables files option on subclient content
        &#34;&#34;&#34;
        if self.files:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupFileObjects&#39;]&#34;, False)

    def disable_metadata(self):
        &#34;&#34;&#34;
        Enables metadata option on subclient content
        &#34;&#34;&#34;
        if self.metadata:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupSFMetadata&#39;]&#34;, False)

    def disable_archived_deleted(self):
        &#34;&#34;&#34;
        Disables backup archived and deleted option on subclient content
        &#34;&#34;&#34;
        if not self.archived_deleted:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupArchivedandDeletedRecs&#39;]&#34;, True)

    def check_object_in_browse(self, object_to_restore, browse_data):
        &#34;&#34;&#34;Check if the particular object is present in browse of the subclient

            Args:
                object_to_restore     (str)   --  folder path whioch has to be restored

                browse_data           (str)   --  list of objects from browse response

            Raises:
                SDKException:
                    if object is not present in browse result
        &#34;&#34;&#34;
        source_item = None

        if (object_to_restore.find(&#34;/Objects&#34;) &lt; 0 and
                object_to_restore.find(&#34;/&#34;) &lt; 0 and
                object_to_restore.find(&#34;/Files&#34;) &lt; 0):
            restore_object_name = &#34;/Objects/&#34; + object_to_restore
        else:
            restore_object_name = object_to_restore

        for path in browse_data:
            if path.find(restore_object_name) &gt;= 0:
                source_item = path
                break

        if source_item is None:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

        return restore_object_name

    def _restore_salesforce_options_json(self, value):
        &#34;&#34;&#34;setter for the salesforce restore  options in restore json
            Raises:
                SDKException:
                    if input value is not dictionary
        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._salesforce_restore_option_json = {
            &#34;instanceType&#34;: &#39;SALESFORCE&#39;,
            &#34;salesforceRestoreOptions&#34;: {
                &#34;restoreToFileSystem&#34;: value.get(&#34;to_fs&#34;, True),
                &#34;pathToStoreCsv&#34;: value.get(&#34;staging_path&#34;, &#39;/tmp/&#39;),
                &#34;dependentRestoreLevel&#34;: value.get(&#34;dependent_level&#34;, 0),
                &#34;isMetadataRestore&#34;: value.get(&#34;is_metadata&#34;, False),
                &#34;restoreToSalesforce&#34;: value.get(&#34;to_cloud&#34;, False),
                &#34;restoreFromDatabase&#34;: value.get(&#34;from_database&#34;, False),
                &#34;overrideTable&#34;: value.get(&#34;override_table&#34;, True),
                &#34;syncDatabase&#34;: {
                    &#34;dbEnabled&#34;: value.get(&#34;db_enabled&#34;, False),
                    &#34;dbType&#34;: value.get(&#34;db_type&#34;, &#39;SQLSERVER&#39;),
                    &#34;dbHost&#34;: value.get(&#34;db_host_name&#34;, &#39;&#39;),
                    &#34;dbPort&#34;: value.get(&#34;db_port&#34;, &#39;1433&#39;),
                    &#34;dbName&#34;: value.get(&#34;db_name&#34;, &#39;&#39;),
                    &#34;dbInstance&#34;: value.get(&#34;db_instance&#34;, &#39;&#39;),
                    &#34;dbUserPassword&#34;: {
                        &#34;userName&#34;: value.get(&#34;db_user_name&#34;, &#39;&#39;),
                        &#34;password&#34;: value.get(&#34;db_user_password&#34;, &#39;&#39;)
                    },

                }

            }
        }

    def _restore_salesforce_destination_json(self, value):
        &#34;&#34;&#34;setter for  the salesforce destination restore option in restore JSON
            Raises:
                SDKException:
                    if input value is not dictionary
        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._destination_restore_json = {
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            },
            &#34;destinationInstance&#34;: {
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#39;Cloud Apps&#39;,
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            },
            &#34;destinationBackupset&#34;: {
                &#34;backupsetName&#34;: value.get(&#34;dest_backupset_name&#34;, &#34;&#34;),
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#39;Cloud Apps&#39;,
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            },
            &#34;noOfStreams&#34;: value.get(&#34;streams&#34;, 2)
        }

    def restore_to_file_system(
            self,
            objects_to_restore=None,
            destination_client=None,
            sf_options=None):
        &#34;&#34;&#34;perform restore to file system to the provided path

        Args:
            objects_to_restore  (str)   --  list of objects to restore

            destination_client  (str)   --  destination client name where cloud connector
                                                package exists if this value not provided,
                                                it will automatically use source backup client

            sf_options          (dict)

                destination_path    (str)   :   staging path for salesforce restore data.
                                                    if this value is not provided, uses download
                                                    cache path from source

                dependent_level     (int)   :   restore children based on selected level.
                                                    0   -   no Children
                                                    1   -   immediate children
                                                    -1  -   All children
                    default: 0

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_time           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets content from 01/01/1970 if not specified
                    default: 0

                to_time             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets content till latest if not specified
                    default: 0

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

         Raises:
                SDKException:
                    if from time value is incorrect

                    if to time value is incorrect

                    if to time is less than from time

                    if failed to browse content

                    if response is empty

                    if response is not success

                    if destination client does not exist on commcell

        &#34;&#34;&#34;

        file_restore_option = {}

        if sf_options is None:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._instance_object.proxy_client

        if isinstance(destination_client, Client):
            client = destination_client
        elif isinstance(destination_client, str):
            client = Client(self._commcell_object, destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        file_restore_option[&#34;client_name&#34;] = client.client_name
        file_restore_option[&#34;destination_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, self._backupset_object.download_cache_path
        )

        self._restore_destination_json(file_restore_option)

        # process the objects to restore
        if isinstance(objects_to_restore, list):
            objects_to_restore_list = objects_to_restore

        else:
            objects_to_restore_list = [objects_to_restore]

        file_restore_option[&#34;paths&#34;] = []
        browse_files, _ = self.browse(
            path=&#39;/Objects&#39;,
            from_time=sf_options.get(&#34;from_time&#34;, 0),
            to_time=sf_options.get(&#34;to_time&#34;, 0)
        )

        for each_object in objects_to_restore_list:
            if each_object.find(&#39;/Files&#39;) &lt; 0:
                file_restore_option[&#34;paths&#34;].append(
                    self.check_object_in_browse(&#34;%s&#34; % each_object, browse_files)
                )

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;,
            self._backupset_object.download_cache_path
        )
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;to_fs&#34;] = True
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)

        # set the browse option
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def restore_to_database(
            self,
            objects_to_restore=None,
            destination_client=None,
            sf_options=None):
        &#34;&#34;&#34;perform Restore to  Database

        Args:
            objects_to_restore  (str)   --  list of objects to restore

            destination_client  (str)   --  destination clientname where cloud connector package
                                                exists. if this value not provided, it will
                                                automatically use source backup client

            sf_options          (dict)

                destination_path    (str)   :   staging path for salesforce restore data.
                                                    if this value is not provided, it will
                                                    automatically use download cache path
                                                    from source

                db_type             (str)   :   database type. if database details does not
                                                    provided, it will use syncdb database
                                                    for restore
                    default: SQLSERVER

                db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

                db_instance         (str)   :   database instance name
                                                    (provide if applicable for that database type)

                db_name             (str)   :   database database name
                                                    (it is where data will be imported)

                db_port             (str)   :   database connection port
                    default: 1433

                db_user_name        (str)   :   database username
                                                    (it should have read/write permissions on db)

                db_user_password    (str)   :   database user password

                overrirde_table     (bool)  :   overrides the tables on  database
                    default: True

                dependent_level     (int)   :   restore dependent object based on selected level.
                                                    0   -   no Children
                                                    1   -   immediate children
                                                   -1   -   All children
                    default: 0

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_date           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets contents from 01/01/1970 if not specified
                    default: 0

                to_date             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets contents till current day if not specified
                    default: 0

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

        Raises:
            SDKException:
                if from time value is incorrect

                if to time value is incorrect

                if to time is less than from time

                if failed to browse content

                if response is empty

                if response is not success

                if destination client does not exist on commcell

                if all the database details not provided

        &#34;&#34;&#34;
        file_restore_option = {}

        if sf_options is None:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._instance_object.proxy_client

        if isinstance(destination_client, Client):
            dest_client = destination_client
        elif isinstance(destination_client, str):
            dest_client = Client(self._commcell_object, destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        if not (&#39;db_host_name&#39; in sf_options and
                &#39;db_instance&#39; in sf_options and
                &#39;db_name&#39; in sf_options and
                &#39;db_user_name&#39; in sf_options and
                &#39;db_user_password&#39; in sf_options):
            raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

        # set the destination client
        file_restore_option[&#34;client_name&#34;] = dest_client.client_name
        file_restore_option[&#34;destination_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, self._backupset_object.download_cache_path
        )

        self._restore_destination_json(file_restore_option)

        # process the objects to restore
        if isinstance(objects_to_restore, list):
            objects_to_restore_list = objects_to_restore

        else:
            objects_to_restore_list = [objects_to_restore]

        file_restore_option[&#34;paths&#34;] = []
        browse_files, _ = self.browse(
            path=&#39;/Objects&#39;,
            from_time=sf_options.get(&#34;from_time&#34;, 0),
            to_time=sf_options.get(&#34;to_time&#34;, 0)
        )

        for each_object in objects_to_restore_list:
            if each_object.find(&#39;/Files&#39;) &lt; 0:
                file_restore_option[&#34;paths&#34;].append(
                    self.check_object_in_browse(&#34;%s&#34; % each_object, browse_files)
                )

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, self._backupset_object.download_cache_path
        )
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
        file_restore_option[&#34;to_fs&#34;] = False
        file_restore_option[&#34;db_enabled&#34;] = True
        file_restore_option[&#34;db_type&#34;] = sf_options.get(&#34;db_type&#34;, &#39;SQLSERVER&#39;)
        file_restore_option[&#34;db_host_name&#34;] = sf_options.get(&#34;db_host_name&#34;, &#34;&#34;)
        file_restore_option[&#34;db_instance&#34;] = sf_options.get(&#34;db_instance&#34;, &#34;&#34;)
        file_restore_option[&#34;db_name&#34;] = sf_options.get(&#34;db_name&#34;, &#34;autorestoredb&#34;)
        file_restore_option[&#34;db_port&#34;] = sf_options.get(&#34;db_port&#34;, &#39;1433&#39;)
        file_restore_option[&#34;db_user_name&#34;] = sf_options.get(&#34;db_user_name&#34;, &#39;sa&#39;)
        db_base64_password = b64encode(sf_options[&#39;db_user_password&#39;].encode()).decode()
        file_restore_option[&#34;db_user_password&#34;] = db_base64_password
        file_restore_option[&#34;override_table&#34;] = sf_options.get(&#34;override_table&#34;, True)

        # set the browse option
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
        file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
        file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def restore_to_salesforce_from_database(
            self,
            objects_to_restore=None,
            destination_client=None,
            destination_instance=None,
            destination_backupset=None,
            sf_options=None):
        &#34;&#34;&#34;perform Restore to Salesforce from Database

        Args:
            objects_to_restore      (str)   --  list of objects to restore

            destination_client      (str)   --  destination pseudo client name.
                                                    if this value not provided, it will
                                                    automatically select source client

            destination_instance    (str)   --  destination instance name.
                                                    if this value not provided, it will
                                                    automatically select source instance name

            destination_backupset   (str)   --  destination backupset name.
                                                    if this value not provided, it will
                                                    automatically select source backupset
            sf_options              (dict)

                destination_path    (str)   :   staging path for salesforce restore data

                db_type             (str)   :   database type. if database details does not
                                                    provided, it will use syncdb database
                                                    for restore
                    default: SQLSERVER

                db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

                db_instance         (str)   :   database instance name
                                                    (provide if applicable for that database type)

                db_name             (str)   :   database database name
                                                    (it is where data will be imported)

                db_port             (str)   :   database connection port
                    default: 1433

                db_user_name        (str)   :   database username
                                                    (read/write permissions needed on db)

                db_user_password    (str)   :   database user password

                overrirde_table     (bool)  :   overrides the tables on  database
                    default: True

                dependent_level     (int)   :   restore children based on selected level.
                                                    0   -   no Children
                                                    1   -   immediate children
                                                    -1  -   All children
                    default: 0

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_time           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets contents from 01/01/1970 if not specified
                    default: None

                to_time             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets contents till current day if not specified
                    default: None

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

        Raises:
            SDKException:
                if from date value is incorrect

                if to date value is incorrect

                if to date is less than from date

                if failed to browse content

                if response is empty

                if response is not success

                if destination client does not exist

                if destination instance does not exist

                if destination backupset does not exist

                if syncdb is not enabled and user not provided the database details

        &#34;&#34;&#34;
        file_restore_option = {}

        if sf_options is None:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._agent_object._client_object

        if isinstance(destination_client, Client):
            dest_client = destination_client
        elif isinstance(destination_client, str):
            dest_client = Client(self._commcell_object, destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        dest_agent = Agent(dest_client, &#39;Cloud Apps&#39;, &#39;134&#39;)

        # check if instance name is correct
        if destination_instance is None:
            destination_instance = self._backupset_object._instance_object

        if isinstance(destination_instance, Instance):
            dest_instance = destination_instance
        elif isinstance(destination_instance, str):
            dest_instance = dest_agent.instances.get(destination_instance)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

        # check if backupset name is correct
        if destination_backupset is None:
            destination_backupset = self._backupset_object

        if isinstance(destination_backupset, SalesforceBackupset):
            dest_backupset = destination_backupset
        elif isinstance(destination_backupset, str):
            dest_backupset = SalesforceBackupset(dest_instance, destination_backupset)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;114&#39;)

        if not self._backupset_object.is_sync_db_enabled:
            if not (
                    &#39;db_host_name&#39; in sf_options and &#39;db_instance&#39; in sf_options and
                    &#39;db_name&#39; in sf_options and &#39;db_user_name&#39; in sf_options and
                    &#39;db_user_password&#39; in sf_options):
                raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

        # set salesforce destination client
        file_restore_option[&#34;dest_client_name&#34;] = dest_client.client_name
        file_restore_option[&#34;dest_instance_name&#34;] = dest_instance.instance_name
        file_restore_option[&#34;dest_backupset_name&#34;] = dest_backupset.backupset_name

        self._restore_salesforce_destination_json(file_restore_option)

        # process the objects to restore
        if isinstance(objects_to_restore, list):
            objects_to_restore_list = objects_to_restore

        else:
            objects_to_restore_list = [objects_to_restore]

        file_restore_option[&#34;paths&#34;] = []
        browse_files, _ = self.browse(
            path=&#39;/Objects&#39;, from_time=sf_options.get(&#34;from_time&#34;, 0),
            to_time=sf_options.get(&#34;to_time&#34;, 0))

        for each_object in objects_to_restore_list:
            if each_object.find(&#39;/Files&#39;) &lt; 0:
                file_restore_option[&#34;paths&#34;].append(
                    self.check_object_in_browse(
                        &#34;%s&#34; %
                        each_object,
                        browse_files))

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;,
            dest_backupset.download_cache_path)
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
        file_restore_option[&#34;to_fs&#34;] = False
        file_restore_option[&#34;to_cloud&#34;] = True
        file_restore_option[&#34;from_database&#34;] = True
        file_restore_option[&#34;db_enabled&#34;] = True
        if self._backupset_object.is_sync_db_enabled or (&#39;db_host_name&#39; in sf_options):
            if self._backupset_object.sync_db_type is None:
                dbtype = &#39;SQLSERVER&#39;
            else:
                dbtype = self._backupset_object.sync_db_type
            file_restore_option[&#34;db_type&#34;] = sf_options.get(&#34;db_type&#34;, dbtype)
            file_restore_option[&#34;db_host_name&#34;] = sf_options.get(
                &#34;db_host_name&#34;, self._backupset_object.sync_db_host
            )
            file_restore_option[&#34;db_instance&#34;] = sf_options.get(
                &#34;db_instance&#34;, self._backupset_object.sync_db_instance
            )
            file_restore_option[&#34;db_name&#34;] = sf_options.get(
                &#34;db_name&#34;, self._backupset_object.sync_db_name
            )
            file_restore_option[&#34;db_port&#34;] = sf_options.get(
                &#34;db_port&#34;, self._backupset_object.sync_db_port
            )
            file_restore_option[&#34;db_user_name&#34;] = sf_options.get(
                &#34;db_user_name&#34;, self._backupset_object.sync_db_user_name
            )

            if &#39;db_user_password&#39; in sf_options:
                sf_options[&#39;_db_base64_password&#39;] = b64encode(
                    sf_options[&#39;db_user_password&#39;].encode()).decode()

            file_restore_option[&#34;db_user_password&#34;] = sf_options.get(
                &#34;_db_base64_password&#34;,
                self._backupset_object._sync_db_user_password)
        else:
            raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

        file_restore_option[&#34;override_table&#34;] = sf_options.get(&#34;override_table&#34;, True)

        # set the browse option
        file_restore_option[&#34;client_name&#34;] = self._backupset_object._agent_object._client_object.client_name
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
        file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
        file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def restore_to_salesforce_from_media(
            self,
            objects_to_restore=None,
            destination_client=None,
            destination_instance=None,
            destination_backupset=None,
            sf_options=None):
        &#34;&#34;&#34;perform Restore to Salesforce from Media.

        Args:
            objects_to_restore      (str)   --  list of objects to restore

            destination_client      (str)   --  destination pseudo client name.
                                                    if this value not provided, it will
                                                    automatically select source client

            destination_instance    (str)   --  destination instance name.
                                                    if this value not provided, it will
                                                    automatically select source instance name

            destination_backupset   (str)   --  destination backupset name.
                                                    if this value not provided, it will
                                                    automatically select source backupset
            sf_options              (dict)

                destination_path    (str)   :   staging path for salesforce restore data

                db_type             (str)   :   database type. if database details does not
                                                    provided, it will use syncdb database
                                                    for restore
                default: SQLSERVER

                db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

                db_instance         (str)   :   database instance name
                                                    (provide if applicable for that database type)

                db_name             (str)   :   database database name
                                                    (it is where data will be imported)

                db_port             (str)   :   database connection port
                    default: 1433

                db_user_name        (str)   :   database username
                                                    (read/write permissions needed on db)

                db_user_password    (str)   :   database user password

                overrirde_table     (bool)  :   overrides the tables on  database
                    default: True

                dependent_level     (int)   :   restore children based on selected level.
                                                    0   -   no Children
                                                    1   -   immediate children
                                                    -1  -   All children
                    default: 0

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_time           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets contents from 01/01/1970 if not specified
                    default: None

                to_time             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets contents till current day if not specified
                    default: None

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

        Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success

                    if destination client does not exist

                    if destination instance does not exist

                    if destination backupset does not exist

                    if user does not provide staging database details

        &#34;&#34;&#34;

        file_restore_option = {}

        if sf_options is None:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._agent_object._client_object

        if isinstance(destination_client, Client):
            dest_client = destination_client
        elif isinstance(destination_client, str):
            dest_client = Client(self._commcell_object, destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        dest_agent = Agent(dest_client, &#39;Cloud Apps&#39;)

        # check if instance name is correct
        if destination_instance is None:
            destination_instance = self._backupset_object._instance_object

        if isinstance(destination_instance, Instance):
            dest_instance = destination_instance
        elif isinstance(destination_instance, str):
            dest_instance = dest_agent.instances.get(destination_instance)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

        # check if backupset name is correct
        if destination_backupset is None:
            destination_backupset = self._backupset_object

        if isinstance(destination_backupset, SalesforceBackupset):
            dest_backupset = destination_backupset
        elif isinstance(destination_backupset, str):
            dest_backupset = SalesforceBackupset(dest_instance, destination_backupset)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;114&#39;)

        if not (&#39;db_host_name&#39; in sf_options and
                &#39;db_instance&#39; in sf_options and
                &#39;db_name&#39; in sf_options and
                &#39;db_user_name&#39; in sf_options and
                &#39;db_user_password&#39; in sf_options):
            raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

        file_restore_option[&#34;dest_client_name&#34;] = dest_client.client_name
        file_restore_option[&#34;dest_instance_name&#34;] = dest_instance.instance_name
        file_restore_option[&#34;dest_backupset_name&#34;] = dest_backupset.backupset_name

        self._restore_salesforce_destination_json(file_restore_option)

        # process the objects to restore
        if isinstance(objects_to_restore, list):
            objects_to_restore_list = objects_to_restore

        else:
            objects_to_restore_list = [objects_to_restore]

        file_restore_option[&#34;paths&#34;] = []
        browse_files, _ = self.browse(
            path=&#39;/Objects&#39;,
            from_time=sf_options.get(&#34;from_time&#34;, 0),
            to_time=sf_options.get(&#34;to_time&#34;, 0)
        )

        for each_object in objects_to_restore_list:
            if each_object.find(&#39;/Files&#39;) &lt; 0:
                file_restore_option[&#34;paths&#34;].append(
                    self.check_object_in_browse(&#34;%s&#34; % each_object, browse_files)
                )

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, dest_backupset.download_cache_path
        )
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
        file_restore_option[&#34;to_fs&#34;] = False
        file_restore_option[&#34;to_cloud&#34;] = True
        file_restore_option[&#34;from_database&#34;] = False
        file_restore_option[&#34;db_enabled&#34;] = True
        file_restore_option[&#34;db_type&#34;] = sf_options.get(&#34;db_type&#34;, &#39;SQLSERVER&#39;)
        file_restore_option[&#34;db_host_name&#34;] = sf_options.get(&#34;db_host_name&#34;, &#34;&#34;)
        file_restore_option[&#34;db_instance&#34;] = sf_options.get(&#34;db_instance&#34;, &#34;&#34;)
        file_restore_option[&#34;db_name&#34;] = sf_options.get(&#34;db_name&#34;, &#39;autorestoredb&#39;)
        file_restore_option[&#34;db_port&#34;] = sf_options.get(&#34;db_port&#34;, &#39;1433&#39;)
        file_restore_option[&#34;db_user_name&#34;] = sf_options.get(&#34;db_user_name&#34;, &#39;sa&#39;)
        db_base64_password = b64encode(sf_options[&#39;db_user_password&#39;].encode()).decode()
        file_restore_option[&#34;db_user_password&#34;] = db_base64_password
        file_restore_option[&#34;override_table&#34;] = sf_options.get(&#34;override_table&#34;, True)

        # set the browse option
        file_restore_option[&#34;client_name&#34;] = self._backupset_object._agent_object._client_object.client_name
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
        file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
        file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def metadata_restore_to_salesforce(
            self,
            metadata_list=None,
            destination_client=None,
            destination_instance=None,
            destination_backupset=None,
            **sf_options):
        &#34;&#34;&#34;perform Restore to Salesforce from Media.

        Args:
            metadata_list           (list)  --  List of metadata components to restore like &#34;folder/component.type&#34;

            destination_client      (str)   --  destination pseudo client name.
                                                    if this value not provided, it will
                                                    automatically select source client

            destination_instance    (str)   --  destination instance name.
                                                    if this value not provided, it will
                                                    automatically select source instance name

            destination_backupset   (str)   --  destination backupset name.
                                                    if this value not provided, it will
                                                    automatically select source backupset
            sf_options:

                destination_path    (str)   :   staging path for salesforce restore data

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_time           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets contents from 01/01/1970 if not specified
                    default: None

                to_time             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets contents till current day if not specified
                    default: None

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

        Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success

                    if destination client does not exist

                    if destination instance does not exist

                    if destination backupset does not exist

        &#34;&#34;&#34;

        file_restore_option = {}

        if not sf_options:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._agent_object._client_object

        if isinstance(destination_client, Client):
            dest_client = destination_client
        elif isinstance(destination_client, str):
            dest_client = self._commcell_object.clients.get(destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        dest_agent = Agent(dest_client, &#39;Cloud Apps&#39;)

        # check if instance name is correct
        if destination_instance is None:
            destination_instance = self._backupset_object._instance_object

        if isinstance(destination_instance, Instance):
            dest_instance = destination_instance
        elif isinstance(destination_instance, str):
            dest_instance = dest_agent.instances.get(destination_instance)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

        # check if backupset name is correct
        if destination_backupset is None:
            destination_backupset = self._backupset_object

        if isinstance(destination_backupset, SalesforceBackupset):
            dest_backupset = destination_backupset
        elif isinstance(destination_backupset, str):
            dest_backupset = SalesforceBackupset(dest_instance, destination_backupset)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;114&#39;)

        file_restore_option[&#34;dest_client_name&#34;] = dest_client.client_name
        file_restore_option[&#34;dest_instance_name&#34;] = dest_instance.instance_name
        file_restore_option[&#34;dest_backupset_name&#34;] = dest_backupset.backupset_name

        self._restore_salesforce_destination_json(file_restore_option)

        file_restore_option[&#34;paths&#34;] = [f&#34;/Metadata/unpackaged/{metadata_path}&#34; for metadata_path in metadata_list]

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, dest_backupset.download_cache_path
        )
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
        file_restore_option[&#34;to_fs&#34;] = False
        file_restore_option[&#34;to_cloud&#34;] = True
        file_restore_option[&#34;from_database&#34;] = False
        file_restore_option[&#34;is_metadata&#34;] = True

        # set the browse option
        file_restore_option[&#34;client_name&#34;] = self._backupset_object._agent_object._client_object.client_name
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
        file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
        file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def _prepare_salesforce_restore_json(self, file_restore_option):
        &#34;&#34;&#34;prepares the  salesforce restore json from getters.&#34;&#34;&#34;
        self._restore_fileoption_json(file_restore_option)
        self._restore_salesforce_options_json(file_restore_option)
        self._restore_browse_option_json(file_restore_option)
        self._impersonation_json(file_restore_option)
        self._restore_common_options_json(file_restore_option)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [{
                    &#34;subTask&#34;: self._json_restore_subtask,
                    &#34;options&#34;: {
                        &#34;restoreOptions&#34;: {
                            &#34;impersonation&#34;: self._impersonation_json_,
                            &#34;cloudAppsRestoreOptions&#34;: self._salesforce_restore_option_json,
                            &#34;browseOption&#34;: self._browse_restore_json,
                            &#34;commonOptions&#34;: self._commonoption_restore_json,
                            &#34;destination&#34;: self._destination_restore_json,
                            &#34;fileOption&#34;: self._fileoption_restore_json,

                        }
                    }
                }]
            }
        }

        return request_json</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient"><code class="flex name class">
<span>class <span class="ident">SalesforceSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from CloudAppsSubclient Base class, representing a Salesforce subclient,
and to perform operations on that subclient.</p>
<p>Constructor for the class</p>
<h2 id="args">Args</h2>
<p>backupset_object
(object)
&ndash; instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash; name of the subclient</p>
<p>subclient_id
(str)
&ndash; id of the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L83-L1206" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SalesforceSubclient(CloudAppsSubclient):
    &#34;&#34;&#34;Derived class from CloudAppsSubclient Base class, representing a Salesforce subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            backupset_object  (object)  -- instance of the Backupset class

            subclient_name    (str)     -- name of the subclient

            subclient_id      (str)     -- id of the subclient

        &#34;&#34;&#34;
        self.cloud_apps_subclient_prop = {}
        self._files = None
        self._metadata = None
        self._archived_deleted = None
        self._objects = None
        super(SalesforceSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the properties of this subclient.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(SalesforceSubclient, self)._get_subclient_properties()

        if &#39;cloudAppsSubClientProp&#39; in self._subclient_properties:
            self._cloud_apps_subclient_prop = self._subclient_properties[&#39;cloudAppsSubClientProp&#39;]
            if &#39;salesforceSubclient&#39; in self._cloud_apps_subclient_prop:
                sfsubclient = self._cloud_apps_subclient_prop[&#39;salesforceSubclient&#39;]
                self._objects = sfsubclient.get(&#39;backupSfObjects&#39;)
                self._files = sfsubclient.get(&#39;backupFileObjects&#39;)
                self._metadata = sfsubclient.get(&#39;backupSFMetadata&#39;)
                self._archived_deleted = sfsubclient.get(&#39;backupArchivedandDeletedRecs&#39;)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;: {
                &#34;proxyClient&#34;: self._proxyClient,
                &#34;subClientEntity&#34;: self._subClientEntity,
                &#34;cloudAppsSubClientProp&#34;: self._cloud_apps_subclient_prop,
                &#34;commonProperties&#34;: self._commonProperties,
                &#34;contentOperationType&#34;: 1
            }
        }

        return subclient_json

    @property
    def objects(self):
        &#34;&#34;&#34;getter for salesforce files option&#34;&#34;&#34;
        return self._objects

    @property
    def files(self):
        &#34;&#34;&#34;getter for salesforce files option&#34;&#34;&#34;
        return self._files

    @property
    def metadata(self):
        &#34;&#34;&#34;getter for salesforce metadata option &#34;&#34;&#34;
        return self._metadata

    @property
    def archived_deleted(self):
        &#34;&#34;&#34;getter for salesfoce backup archived and deleted data&#34;&#34;&#34;
        return self._archived_data

    def enable_files(self):
        &#34;&#34;&#34;
        Enables files option on subclient content
        &#34;&#34;&#34;
        if not self.files:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupFileObjects&#39;]&#34;, True)

    def enable_metadata(self):
        &#34;&#34;&#34;
        Enables metadata option on subclient content
        &#34;&#34;&#34;
        if not self.metadata:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupSFMetadata&#39;]&#34;, True)

    def enable_archived_deleted(self):
        &#34;&#34;&#34;
        Enables backup archived deleted option on subclient content
        &#34;&#34;&#34;
        if self.archived_deleted:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupArchivedandDeletedRecs&#39;]&#34;, False)

    def disable_files(self):
        &#34;&#34;&#34;
        Disables files option on subclient content
        &#34;&#34;&#34;
        if self.files:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupFileObjects&#39;]&#34;, False)

    def disable_metadata(self):
        &#34;&#34;&#34;
        Enables metadata option on subclient content
        &#34;&#34;&#34;
        if self.metadata:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupSFMetadata&#39;]&#34;, False)

    def disable_archived_deleted(self):
        &#34;&#34;&#34;
        Disables backup archived and deleted option on subclient content
        &#34;&#34;&#34;
        if not self.archived_deleted:
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                           [&#39;salesforceSubclient&#39;][&#39;backupArchivedandDeletedRecs&#39;]&#34;, True)

    def check_object_in_browse(self, object_to_restore, browse_data):
        &#34;&#34;&#34;Check if the particular object is present in browse of the subclient

            Args:
                object_to_restore     (str)   --  folder path whioch has to be restored

                browse_data           (str)   --  list of objects from browse response

            Raises:
                SDKException:
                    if object is not present in browse result
        &#34;&#34;&#34;
        source_item = None

        if (object_to_restore.find(&#34;/Objects&#34;) &lt; 0 and
                object_to_restore.find(&#34;/&#34;) &lt; 0 and
                object_to_restore.find(&#34;/Files&#34;) &lt; 0):
            restore_object_name = &#34;/Objects/&#34; + object_to_restore
        else:
            restore_object_name = object_to_restore

        for path in browse_data:
            if path.find(restore_object_name) &gt;= 0:
                source_item = path
                break

        if source_item is None:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

        return restore_object_name

    def _restore_salesforce_options_json(self, value):
        &#34;&#34;&#34;setter for the salesforce restore  options in restore json
            Raises:
                SDKException:
                    if input value is not dictionary
        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._salesforce_restore_option_json = {
            &#34;instanceType&#34;: &#39;SALESFORCE&#39;,
            &#34;salesforceRestoreOptions&#34;: {
                &#34;restoreToFileSystem&#34;: value.get(&#34;to_fs&#34;, True),
                &#34;pathToStoreCsv&#34;: value.get(&#34;staging_path&#34;, &#39;/tmp/&#39;),
                &#34;dependentRestoreLevel&#34;: value.get(&#34;dependent_level&#34;, 0),
                &#34;isMetadataRestore&#34;: value.get(&#34;is_metadata&#34;, False),
                &#34;restoreToSalesforce&#34;: value.get(&#34;to_cloud&#34;, False),
                &#34;restoreFromDatabase&#34;: value.get(&#34;from_database&#34;, False),
                &#34;overrideTable&#34;: value.get(&#34;override_table&#34;, True),
                &#34;syncDatabase&#34;: {
                    &#34;dbEnabled&#34;: value.get(&#34;db_enabled&#34;, False),
                    &#34;dbType&#34;: value.get(&#34;db_type&#34;, &#39;SQLSERVER&#39;),
                    &#34;dbHost&#34;: value.get(&#34;db_host_name&#34;, &#39;&#39;),
                    &#34;dbPort&#34;: value.get(&#34;db_port&#34;, &#39;1433&#39;),
                    &#34;dbName&#34;: value.get(&#34;db_name&#34;, &#39;&#39;),
                    &#34;dbInstance&#34;: value.get(&#34;db_instance&#34;, &#39;&#39;),
                    &#34;dbUserPassword&#34;: {
                        &#34;userName&#34;: value.get(&#34;db_user_name&#34;, &#39;&#39;),
                        &#34;password&#34;: value.get(&#34;db_user_password&#34;, &#39;&#39;)
                    },

                }

            }
        }

    def _restore_salesforce_destination_json(self, value):
        &#34;&#34;&#34;setter for  the salesforce destination restore option in restore JSON
            Raises:
                SDKException:
                    if input value is not dictionary
        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._destination_restore_json = {
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            },
            &#34;destinationInstance&#34;: {
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#39;Cloud Apps&#39;,
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            },
            &#34;destinationBackupset&#34;: {
                &#34;backupsetName&#34;: value.get(&#34;dest_backupset_name&#34;, &#34;&#34;),
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#39;Cloud Apps&#39;,
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            },
            &#34;noOfStreams&#34;: value.get(&#34;streams&#34;, 2)
        }

    def restore_to_file_system(
            self,
            objects_to_restore=None,
            destination_client=None,
            sf_options=None):
        &#34;&#34;&#34;perform restore to file system to the provided path

        Args:
            objects_to_restore  (str)   --  list of objects to restore

            destination_client  (str)   --  destination client name where cloud connector
                                                package exists if this value not provided,
                                                it will automatically use source backup client

            sf_options          (dict)

                destination_path    (str)   :   staging path for salesforce restore data.
                                                    if this value is not provided, uses download
                                                    cache path from source

                dependent_level     (int)   :   restore children based on selected level.
                                                    0   -   no Children
                                                    1   -   immediate children
                                                    -1  -   All children
                    default: 0

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_time           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets content from 01/01/1970 if not specified
                    default: 0

                to_time             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets content till latest if not specified
                    default: 0

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

         Raises:
                SDKException:
                    if from time value is incorrect

                    if to time value is incorrect

                    if to time is less than from time

                    if failed to browse content

                    if response is empty

                    if response is not success

                    if destination client does not exist on commcell

        &#34;&#34;&#34;

        file_restore_option = {}

        if sf_options is None:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._instance_object.proxy_client

        if isinstance(destination_client, Client):
            client = destination_client
        elif isinstance(destination_client, str):
            client = Client(self._commcell_object, destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        file_restore_option[&#34;client_name&#34;] = client.client_name
        file_restore_option[&#34;destination_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, self._backupset_object.download_cache_path
        )

        self._restore_destination_json(file_restore_option)

        # process the objects to restore
        if isinstance(objects_to_restore, list):
            objects_to_restore_list = objects_to_restore

        else:
            objects_to_restore_list = [objects_to_restore]

        file_restore_option[&#34;paths&#34;] = []
        browse_files, _ = self.browse(
            path=&#39;/Objects&#39;,
            from_time=sf_options.get(&#34;from_time&#34;, 0),
            to_time=sf_options.get(&#34;to_time&#34;, 0)
        )

        for each_object in objects_to_restore_list:
            if each_object.find(&#39;/Files&#39;) &lt; 0:
                file_restore_option[&#34;paths&#34;].append(
                    self.check_object_in_browse(&#34;%s&#34; % each_object, browse_files)
                )

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;,
            self._backupset_object.download_cache_path
        )
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;to_fs&#34;] = True
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)

        # set the browse option
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def restore_to_database(
            self,
            objects_to_restore=None,
            destination_client=None,
            sf_options=None):
        &#34;&#34;&#34;perform Restore to  Database

        Args:
            objects_to_restore  (str)   --  list of objects to restore

            destination_client  (str)   --  destination clientname where cloud connector package
                                                exists. if this value not provided, it will
                                                automatically use source backup client

            sf_options          (dict)

                destination_path    (str)   :   staging path for salesforce restore data.
                                                    if this value is not provided, it will
                                                    automatically use download cache path
                                                    from source

                db_type             (str)   :   database type. if database details does not
                                                    provided, it will use syncdb database
                                                    for restore
                    default: SQLSERVER

                db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

                db_instance         (str)   :   database instance name
                                                    (provide if applicable for that database type)

                db_name             (str)   :   database database name
                                                    (it is where data will be imported)

                db_port             (str)   :   database connection port
                    default: 1433

                db_user_name        (str)   :   database username
                                                    (it should have read/write permissions on db)

                db_user_password    (str)   :   database user password

                overrirde_table     (bool)  :   overrides the tables on  database
                    default: True

                dependent_level     (int)   :   restore dependent object based on selected level.
                                                    0   -   no Children
                                                    1   -   immediate children
                                                   -1   -   All children
                    default: 0

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_date           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets contents from 01/01/1970 if not specified
                    default: 0

                to_date             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets contents till current day if not specified
                    default: 0

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

        Raises:
            SDKException:
                if from time value is incorrect

                if to time value is incorrect

                if to time is less than from time

                if failed to browse content

                if response is empty

                if response is not success

                if destination client does not exist on commcell

                if all the database details not provided

        &#34;&#34;&#34;
        file_restore_option = {}

        if sf_options is None:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._instance_object.proxy_client

        if isinstance(destination_client, Client):
            dest_client = destination_client
        elif isinstance(destination_client, str):
            dest_client = Client(self._commcell_object, destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        if not (&#39;db_host_name&#39; in sf_options and
                &#39;db_instance&#39; in sf_options and
                &#39;db_name&#39; in sf_options and
                &#39;db_user_name&#39; in sf_options and
                &#39;db_user_password&#39; in sf_options):
            raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

        # set the destination client
        file_restore_option[&#34;client_name&#34;] = dest_client.client_name
        file_restore_option[&#34;destination_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, self._backupset_object.download_cache_path
        )

        self._restore_destination_json(file_restore_option)

        # process the objects to restore
        if isinstance(objects_to_restore, list):
            objects_to_restore_list = objects_to_restore

        else:
            objects_to_restore_list = [objects_to_restore]

        file_restore_option[&#34;paths&#34;] = []
        browse_files, _ = self.browse(
            path=&#39;/Objects&#39;,
            from_time=sf_options.get(&#34;from_time&#34;, 0),
            to_time=sf_options.get(&#34;to_time&#34;, 0)
        )

        for each_object in objects_to_restore_list:
            if each_object.find(&#39;/Files&#39;) &lt; 0:
                file_restore_option[&#34;paths&#34;].append(
                    self.check_object_in_browse(&#34;%s&#34; % each_object, browse_files)
                )

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, self._backupset_object.download_cache_path
        )
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
        file_restore_option[&#34;to_fs&#34;] = False
        file_restore_option[&#34;db_enabled&#34;] = True
        file_restore_option[&#34;db_type&#34;] = sf_options.get(&#34;db_type&#34;, &#39;SQLSERVER&#39;)
        file_restore_option[&#34;db_host_name&#34;] = sf_options.get(&#34;db_host_name&#34;, &#34;&#34;)
        file_restore_option[&#34;db_instance&#34;] = sf_options.get(&#34;db_instance&#34;, &#34;&#34;)
        file_restore_option[&#34;db_name&#34;] = sf_options.get(&#34;db_name&#34;, &#34;autorestoredb&#34;)
        file_restore_option[&#34;db_port&#34;] = sf_options.get(&#34;db_port&#34;, &#39;1433&#39;)
        file_restore_option[&#34;db_user_name&#34;] = sf_options.get(&#34;db_user_name&#34;, &#39;sa&#39;)
        db_base64_password = b64encode(sf_options[&#39;db_user_password&#39;].encode()).decode()
        file_restore_option[&#34;db_user_password&#34;] = db_base64_password
        file_restore_option[&#34;override_table&#34;] = sf_options.get(&#34;override_table&#34;, True)

        # set the browse option
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
        file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
        file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def restore_to_salesforce_from_database(
            self,
            objects_to_restore=None,
            destination_client=None,
            destination_instance=None,
            destination_backupset=None,
            sf_options=None):
        &#34;&#34;&#34;perform Restore to Salesforce from Database

        Args:
            objects_to_restore      (str)   --  list of objects to restore

            destination_client      (str)   --  destination pseudo client name.
                                                    if this value not provided, it will
                                                    automatically select source client

            destination_instance    (str)   --  destination instance name.
                                                    if this value not provided, it will
                                                    automatically select source instance name

            destination_backupset   (str)   --  destination backupset name.
                                                    if this value not provided, it will
                                                    automatically select source backupset
            sf_options              (dict)

                destination_path    (str)   :   staging path for salesforce restore data

                db_type             (str)   :   database type. if database details does not
                                                    provided, it will use syncdb database
                                                    for restore
                    default: SQLSERVER

                db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

                db_instance         (str)   :   database instance name
                                                    (provide if applicable for that database type)

                db_name             (str)   :   database database name
                                                    (it is where data will be imported)

                db_port             (str)   :   database connection port
                    default: 1433

                db_user_name        (str)   :   database username
                                                    (read/write permissions needed on db)

                db_user_password    (str)   :   database user password

                overrirde_table     (bool)  :   overrides the tables on  database
                    default: True

                dependent_level     (int)   :   restore children based on selected level.
                                                    0   -   no Children
                                                    1   -   immediate children
                                                    -1  -   All children
                    default: 0

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_time           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets contents from 01/01/1970 if not specified
                    default: None

                to_time             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets contents till current day if not specified
                    default: None

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

        Raises:
            SDKException:
                if from date value is incorrect

                if to date value is incorrect

                if to date is less than from date

                if failed to browse content

                if response is empty

                if response is not success

                if destination client does not exist

                if destination instance does not exist

                if destination backupset does not exist

                if syncdb is not enabled and user not provided the database details

        &#34;&#34;&#34;
        file_restore_option = {}

        if sf_options is None:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._agent_object._client_object

        if isinstance(destination_client, Client):
            dest_client = destination_client
        elif isinstance(destination_client, str):
            dest_client = Client(self._commcell_object, destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        dest_agent = Agent(dest_client, &#39;Cloud Apps&#39;, &#39;134&#39;)

        # check if instance name is correct
        if destination_instance is None:
            destination_instance = self._backupset_object._instance_object

        if isinstance(destination_instance, Instance):
            dest_instance = destination_instance
        elif isinstance(destination_instance, str):
            dest_instance = dest_agent.instances.get(destination_instance)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

        # check if backupset name is correct
        if destination_backupset is None:
            destination_backupset = self._backupset_object

        if isinstance(destination_backupset, SalesforceBackupset):
            dest_backupset = destination_backupset
        elif isinstance(destination_backupset, str):
            dest_backupset = SalesforceBackupset(dest_instance, destination_backupset)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;114&#39;)

        if not self._backupset_object.is_sync_db_enabled:
            if not (
                    &#39;db_host_name&#39; in sf_options and &#39;db_instance&#39; in sf_options and
                    &#39;db_name&#39; in sf_options and &#39;db_user_name&#39; in sf_options and
                    &#39;db_user_password&#39; in sf_options):
                raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

        # set salesforce destination client
        file_restore_option[&#34;dest_client_name&#34;] = dest_client.client_name
        file_restore_option[&#34;dest_instance_name&#34;] = dest_instance.instance_name
        file_restore_option[&#34;dest_backupset_name&#34;] = dest_backupset.backupset_name

        self._restore_salesforce_destination_json(file_restore_option)

        # process the objects to restore
        if isinstance(objects_to_restore, list):
            objects_to_restore_list = objects_to_restore

        else:
            objects_to_restore_list = [objects_to_restore]

        file_restore_option[&#34;paths&#34;] = []
        browse_files, _ = self.browse(
            path=&#39;/Objects&#39;, from_time=sf_options.get(&#34;from_time&#34;, 0),
            to_time=sf_options.get(&#34;to_time&#34;, 0))

        for each_object in objects_to_restore_list:
            if each_object.find(&#39;/Files&#39;) &lt; 0:
                file_restore_option[&#34;paths&#34;].append(
                    self.check_object_in_browse(
                        &#34;%s&#34; %
                        each_object,
                        browse_files))

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;,
            dest_backupset.download_cache_path)
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
        file_restore_option[&#34;to_fs&#34;] = False
        file_restore_option[&#34;to_cloud&#34;] = True
        file_restore_option[&#34;from_database&#34;] = True
        file_restore_option[&#34;db_enabled&#34;] = True
        if self._backupset_object.is_sync_db_enabled or (&#39;db_host_name&#39; in sf_options):
            if self._backupset_object.sync_db_type is None:
                dbtype = &#39;SQLSERVER&#39;
            else:
                dbtype = self._backupset_object.sync_db_type
            file_restore_option[&#34;db_type&#34;] = sf_options.get(&#34;db_type&#34;, dbtype)
            file_restore_option[&#34;db_host_name&#34;] = sf_options.get(
                &#34;db_host_name&#34;, self._backupset_object.sync_db_host
            )
            file_restore_option[&#34;db_instance&#34;] = sf_options.get(
                &#34;db_instance&#34;, self._backupset_object.sync_db_instance
            )
            file_restore_option[&#34;db_name&#34;] = sf_options.get(
                &#34;db_name&#34;, self._backupset_object.sync_db_name
            )
            file_restore_option[&#34;db_port&#34;] = sf_options.get(
                &#34;db_port&#34;, self._backupset_object.sync_db_port
            )
            file_restore_option[&#34;db_user_name&#34;] = sf_options.get(
                &#34;db_user_name&#34;, self._backupset_object.sync_db_user_name
            )

            if &#39;db_user_password&#39; in sf_options:
                sf_options[&#39;_db_base64_password&#39;] = b64encode(
                    sf_options[&#39;db_user_password&#39;].encode()).decode()

            file_restore_option[&#34;db_user_password&#34;] = sf_options.get(
                &#34;_db_base64_password&#34;,
                self._backupset_object._sync_db_user_password)
        else:
            raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

        file_restore_option[&#34;override_table&#34;] = sf_options.get(&#34;override_table&#34;, True)

        # set the browse option
        file_restore_option[&#34;client_name&#34;] = self._backupset_object._agent_object._client_object.client_name
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
        file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
        file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def restore_to_salesforce_from_media(
            self,
            objects_to_restore=None,
            destination_client=None,
            destination_instance=None,
            destination_backupset=None,
            sf_options=None):
        &#34;&#34;&#34;perform Restore to Salesforce from Media.

        Args:
            objects_to_restore      (str)   --  list of objects to restore

            destination_client      (str)   --  destination pseudo client name.
                                                    if this value not provided, it will
                                                    automatically select source client

            destination_instance    (str)   --  destination instance name.
                                                    if this value not provided, it will
                                                    automatically select source instance name

            destination_backupset   (str)   --  destination backupset name.
                                                    if this value not provided, it will
                                                    automatically select source backupset
            sf_options              (dict)

                destination_path    (str)   :   staging path for salesforce restore data

                db_type             (str)   :   database type. if database details does not
                                                    provided, it will use syncdb database
                                                    for restore
                default: SQLSERVER

                db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

                db_instance         (str)   :   database instance name
                                                    (provide if applicable for that database type)

                db_name             (str)   :   database database name
                                                    (it is where data will be imported)

                db_port             (str)   :   database connection port
                    default: 1433

                db_user_name        (str)   :   database username
                                                    (read/write permissions needed on db)

                db_user_password    (str)   :   database user password

                overrirde_table     (bool)  :   overrides the tables on  database
                    default: True

                dependent_level     (int)   :   restore children based on selected level.
                                                    0   -   no Children
                                                    1   -   immediate children
                                                    -1  -   All children
                    default: 0

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_time           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets contents from 01/01/1970 if not specified
                    default: None

                to_time             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets contents till current day if not specified
                    default: None

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

        Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success

                    if destination client does not exist

                    if destination instance does not exist

                    if destination backupset does not exist

                    if user does not provide staging database details

        &#34;&#34;&#34;

        file_restore_option = {}

        if sf_options is None:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._agent_object._client_object

        if isinstance(destination_client, Client):
            dest_client = destination_client
        elif isinstance(destination_client, str):
            dest_client = Client(self._commcell_object, destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        dest_agent = Agent(dest_client, &#39;Cloud Apps&#39;)

        # check if instance name is correct
        if destination_instance is None:
            destination_instance = self._backupset_object._instance_object

        if isinstance(destination_instance, Instance):
            dest_instance = destination_instance
        elif isinstance(destination_instance, str):
            dest_instance = dest_agent.instances.get(destination_instance)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

        # check if backupset name is correct
        if destination_backupset is None:
            destination_backupset = self._backupset_object

        if isinstance(destination_backupset, SalesforceBackupset):
            dest_backupset = destination_backupset
        elif isinstance(destination_backupset, str):
            dest_backupset = SalesforceBackupset(dest_instance, destination_backupset)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;114&#39;)

        if not (&#39;db_host_name&#39; in sf_options and
                &#39;db_instance&#39; in sf_options and
                &#39;db_name&#39; in sf_options and
                &#39;db_user_name&#39; in sf_options and
                &#39;db_user_password&#39; in sf_options):
            raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

        file_restore_option[&#34;dest_client_name&#34;] = dest_client.client_name
        file_restore_option[&#34;dest_instance_name&#34;] = dest_instance.instance_name
        file_restore_option[&#34;dest_backupset_name&#34;] = dest_backupset.backupset_name

        self._restore_salesforce_destination_json(file_restore_option)

        # process the objects to restore
        if isinstance(objects_to_restore, list):
            objects_to_restore_list = objects_to_restore

        else:
            objects_to_restore_list = [objects_to_restore]

        file_restore_option[&#34;paths&#34;] = []
        browse_files, _ = self.browse(
            path=&#39;/Objects&#39;,
            from_time=sf_options.get(&#34;from_time&#34;, 0),
            to_time=sf_options.get(&#34;to_time&#34;, 0)
        )

        for each_object in objects_to_restore_list:
            if each_object.find(&#39;/Files&#39;) &lt; 0:
                file_restore_option[&#34;paths&#34;].append(
                    self.check_object_in_browse(&#34;%s&#34; % each_object, browse_files)
                )

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, dest_backupset.download_cache_path
        )
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
        file_restore_option[&#34;to_fs&#34;] = False
        file_restore_option[&#34;to_cloud&#34;] = True
        file_restore_option[&#34;from_database&#34;] = False
        file_restore_option[&#34;db_enabled&#34;] = True
        file_restore_option[&#34;db_type&#34;] = sf_options.get(&#34;db_type&#34;, &#39;SQLSERVER&#39;)
        file_restore_option[&#34;db_host_name&#34;] = sf_options.get(&#34;db_host_name&#34;, &#34;&#34;)
        file_restore_option[&#34;db_instance&#34;] = sf_options.get(&#34;db_instance&#34;, &#34;&#34;)
        file_restore_option[&#34;db_name&#34;] = sf_options.get(&#34;db_name&#34;, &#39;autorestoredb&#39;)
        file_restore_option[&#34;db_port&#34;] = sf_options.get(&#34;db_port&#34;, &#39;1433&#39;)
        file_restore_option[&#34;db_user_name&#34;] = sf_options.get(&#34;db_user_name&#34;, &#39;sa&#39;)
        db_base64_password = b64encode(sf_options[&#39;db_user_password&#39;].encode()).decode()
        file_restore_option[&#34;db_user_password&#34;] = db_base64_password
        file_restore_option[&#34;override_table&#34;] = sf_options.get(&#34;override_table&#34;, True)

        # set the browse option
        file_restore_option[&#34;client_name&#34;] = self._backupset_object._agent_object._client_object.client_name
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
        file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
        file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def metadata_restore_to_salesforce(
            self,
            metadata_list=None,
            destination_client=None,
            destination_instance=None,
            destination_backupset=None,
            **sf_options):
        &#34;&#34;&#34;perform Restore to Salesforce from Media.

        Args:
            metadata_list           (list)  --  List of metadata components to restore like &#34;folder/component.type&#34;

            destination_client      (str)   --  destination pseudo client name.
                                                    if this value not provided, it will
                                                    automatically select source client

            destination_instance    (str)   --  destination instance name.
                                                    if this value not provided, it will
                                                    automatically select source instance name

            destination_backupset   (str)   --  destination backupset name.
                                                    if this value not provided, it will
                                                    automatically select source backupset
            sf_options:

                destination_path    (str)   :   staging path for salesforce restore data

                streams             (int)   :   no of streams to use for restore
                    default: 2

                copy_precedence     (int)   :   copy number to use for restore
                    default: 0

                from_time           (str)   :   date to get the contents after
                                                    format: dd/MM/YYYY
                                                    gets contents from 01/01/1970 if not specified
                    default: None

                to_time             (str)   :   date to get the contents before
                                                    format: dd/MM/YYYY
                                                    gets contents till current day if not specified
                    default: None

                show_deleted_files  (bool)  :   include deleted files in the content or not
                    default: True

        Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success

                    if destination client does not exist

                    if destination instance does not exist

                    if destination backupset does not exist

        &#34;&#34;&#34;

        file_restore_option = {}

        if not sf_options:
            sf_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._agent_object._client_object

        if isinstance(destination_client, Client):
            dest_client = destination_client
        elif isinstance(destination_client, str):
            dest_client = self._commcell_object.clients.get(destination_client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        dest_agent = Agent(dest_client, &#39;Cloud Apps&#39;)

        # check if instance name is correct
        if destination_instance is None:
            destination_instance = self._backupset_object._instance_object

        if isinstance(destination_instance, Instance):
            dest_instance = destination_instance
        elif isinstance(destination_instance, str):
            dest_instance = dest_agent.instances.get(destination_instance)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

        # check if backupset name is correct
        if destination_backupset is None:
            destination_backupset = self._backupset_object

        if isinstance(destination_backupset, SalesforceBackupset):
            dest_backupset = destination_backupset
        elif isinstance(destination_backupset, str):
            dest_backupset = SalesforceBackupset(dest_instance, destination_backupset)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;114&#39;)

        file_restore_option[&#34;dest_client_name&#34;] = dest_client.client_name
        file_restore_option[&#34;dest_instance_name&#34;] = dest_instance.instance_name
        file_restore_option[&#34;dest_backupset_name&#34;] = dest_backupset.backupset_name

        self._restore_salesforce_destination_json(file_restore_option)

        file_restore_option[&#34;paths&#34;] = [f&#34;/Metadata/unpackaged/{metadata_path}&#34; for metadata_path in metadata_list]

        # set the salesforce options
        file_restore_option[&#34;staging_path&#34;] = sf_options.get(
            &#34;destination_path&#34;, dest_backupset.download_cache_path
        )
        file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
        file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
        file_restore_option[&#34;to_fs&#34;] = False
        file_restore_option[&#34;to_cloud&#34;] = True
        file_restore_option[&#34;from_database&#34;] = False
        file_restore_option[&#34;is_metadata&#34;] = True

        # set the browse option
        file_restore_option[&#34;client_name&#34;] = self._backupset_object._agent_object._client_object.client_name
        file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
        file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
        file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

        # prepare and execute the Json
        request_json = self._prepare_salesforce_restore_json(file_restore_option)

        return self._process_restore_response(request_json)

    def _prepare_salesforce_restore_json(self, file_restore_option):
        &#34;&#34;&#34;prepares the  salesforce restore json from getters.&#34;&#34;&#34;
        self._restore_fileoption_json(file_restore_option)
        self._restore_salesforce_options_json(file_restore_option)
        self._restore_browse_option_json(file_restore_option)
        self._impersonation_json(file_restore_option)
        self._restore_common_options_json(file_restore_option)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._subClientEntity],
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [{
                    &#34;subTask&#34;: self._json_restore_subtask,
                    &#34;options&#34;: {
                        &#34;restoreOptions&#34;: {
                            &#34;impersonation&#34;: self._impersonation_json_,
                            &#34;cloudAppsRestoreOptions&#34;: self._salesforce_restore_option_json,
                            &#34;browseOption&#34;: self._browse_restore_json,
                            &#34;commonOptions&#34;: self._commonoption_restore_json,
                            &#34;destination&#34;: self._destination_restore_json,
                            &#34;fileOption&#34;: self._fileoption_restore_json,

                        }
                    }
                }]
            }
        }

        return request_json</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="../casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.archived_deleted"><code class="name">var <span class="ident">archived_deleted</span></code></dt>
<dd>
<div class="desc"><p>getter for salesfoce backup archived and deleted data</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L162-L165" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archived_deleted(self):
    &#34;&#34;&#34;getter for salesfoce backup archived and deleted data&#34;&#34;&#34;
    return self._archived_data</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.files"><code class="name">var <span class="ident">files</span></code></dt>
<dd>
<div class="desc"><p>getter for salesforce files option</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L152-L155" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def files(self):
    &#34;&#34;&#34;getter for salesforce files option&#34;&#34;&#34;
    return self._files</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.metadata"><code class="name">var <span class="ident">metadata</span></code></dt>
<dd>
<div class="desc"><p>getter for salesforce metadata option</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L157-L160" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def metadata(self):
    &#34;&#34;&#34;getter for salesforce metadata option &#34;&#34;&#34;
    return self._metadata</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.objects"><code class="name">var <span class="ident">objects</span></code></dt>
<dd>
<div class="desc"><p>getter for salesforce files option</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L147-L150" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def objects(self):
    &#34;&#34;&#34;getter for salesforce files option&#34;&#34;&#34;
    return self._objects</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.check_object_in_browse"><code class="name flex">
<span>def <span class="ident">check_object_in_browse</span></span>(<span>self, object_to_restore, browse_data)</span>
</code></dt>
<dd>
<div class="desc"><p>Check if the particular object is present in browse of the subclient</p>
<h2 id="args">Args</h2>
<p>object_to_restore
(str)
&ndash;
folder path whioch has to be restored</p>
<p>browse_data
(str)
&ndash;
list of objects from browse response</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if object is not present in browse result</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L215-L244" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def check_object_in_browse(self, object_to_restore, browse_data):
    &#34;&#34;&#34;Check if the particular object is present in browse of the subclient

        Args:
            object_to_restore     (str)   --  folder path whioch has to be restored

            browse_data           (str)   --  list of objects from browse response

        Raises:
            SDKException:
                if object is not present in browse result
    &#34;&#34;&#34;
    source_item = None

    if (object_to_restore.find(&#34;/Objects&#34;) &lt; 0 and
            object_to_restore.find(&#34;/&#34;) &lt; 0 and
            object_to_restore.find(&#34;/Files&#34;) &lt; 0):
        restore_object_name = &#34;/Objects/&#34; + object_to_restore
    else:
        restore_object_name = object_to_restore

    for path in browse_data:
        if path.find(restore_object_name) &gt;= 0:
            source_item = path
            break

    if source_item is None:
        raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

    return restore_object_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.disable_archived_deleted"><code class="name flex">
<span>def <span class="ident">disable_archived_deleted</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables backup archived and deleted option on subclient content</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L207-L213" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_archived_deleted(self):
    &#34;&#34;&#34;
    Disables backup archived and deleted option on subclient content
    &#34;&#34;&#34;
    if not self.archived_deleted:
        self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                       [&#39;salesforceSubclient&#39;][&#39;backupArchivedandDeletedRecs&#39;]&#34;, True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.disable_files"><code class="name flex">
<span>def <span class="ident">disable_files</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables files option on subclient content</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L191-L197" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_files(self):
    &#34;&#34;&#34;
    Disables files option on subclient content
    &#34;&#34;&#34;
    if self.files:
        self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                       [&#39;salesforceSubclient&#39;][&#39;backupFileObjects&#39;]&#34;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.disable_metadata"><code class="name flex">
<span>def <span class="ident">disable_metadata</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables metadata option on subclient content</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L199-L205" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_metadata(self):
    &#34;&#34;&#34;
    Enables metadata option on subclient content
    &#34;&#34;&#34;
    if self.metadata:
        self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                       [&#39;salesforceSubclient&#39;][&#39;backupSFMetadata&#39;]&#34;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.enable_archived_deleted"><code class="name flex">
<span>def <span class="ident">enable_archived_deleted</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables backup archived deleted option on subclient content</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L183-L189" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_archived_deleted(self):
    &#34;&#34;&#34;
    Enables backup archived deleted option on subclient content
    &#34;&#34;&#34;
    if self.archived_deleted:
        self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                       [&#39;salesforceSubclient&#39;][&#39;backupArchivedandDeletedRecs&#39;]&#34;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.enable_files"><code class="name flex">
<span>def <span class="ident">enable_files</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables files option on subclient content</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L167-L173" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_files(self):
    &#34;&#34;&#34;
    Enables files option on subclient content
    &#34;&#34;&#34;
    if not self.files:
        self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                       [&#39;salesforceSubclient&#39;][&#39;backupFileObjects&#39;]&#34;, True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.enable_metadata"><code class="name flex">
<span>def <span class="ident">enable_metadata</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables metadata option on subclient content</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L175-L181" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_metadata(self):
    &#34;&#34;&#34;
    Enables metadata option on subclient content
    &#34;&#34;&#34;
    if not self.metadata:
        self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]\
                                       [&#39;salesforceSubclient&#39;][&#39;backupSFMetadata&#39;]&#34;, True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.metadata_restore_to_salesforce"><code class="name flex">
<span>def <span class="ident">metadata_restore_to_salesforce</span></span>(<span>self, metadata_list=None, destination_client=None, destination_instance=None, destination_backupset=None, **sf_options)</span>
</code></dt>
<dd>
<div class="desc"><p>perform Restore to Salesforce from Media.</p>
<h2 id="args">Args</h2>
<p>metadata_list
(list)
&ndash;
List of metadata components to restore like "folder/component.type"</p>
<p>destination_client
(str)
&ndash;
destination pseudo client name.
if this value not provided, it will
automatically select source client</p>
<p>destination_instance
(str)
&ndash;
destination instance name.
if this value not provided, it will
automatically select source instance name</p>
<p>destination_backupset
(str)
&ndash;
destination backupset name.
if this value not provided, it will
automatically select source backupset
sf_options:</p>
<pre><code>destination_path    (str)   :   staging path for salesforce restore data

streams             (int)   :   no of streams to use for restore
    default: 2

copy_precedence     (int)   :   copy number to use for restore
    default: 0

from_time           (str)   :   date to get the contents after
                                    format: dd/MM/YYYY
                                    gets contents from 01/01/1970 if not specified
    default: None

to_time             (str)   :   date to get the contents before
                                    format: dd/MM/YYYY
                                    gets contents till current day if not specified
    default: None

show_deleted_files  (bool)  :   include deleted files in the content or not
    default: True
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if from date value is incorrect</p>
<pre><code>if to date value is incorrect

if to date is less than from date

if failed to browse content

if response is empty

if response is not success

if destination client does not exist

if destination instance does not exist

if destination backupset does not exist
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L1038-L1175" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def metadata_restore_to_salesforce(
        self,
        metadata_list=None,
        destination_client=None,
        destination_instance=None,
        destination_backupset=None,
        **sf_options):
    &#34;&#34;&#34;perform Restore to Salesforce from Media.

    Args:
        metadata_list           (list)  --  List of metadata components to restore like &#34;folder/component.type&#34;

        destination_client      (str)   --  destination pseudo client name.
                                                if this value not provided, it will
                                                automatically select source client

        destination_instance    (str)   --  destination instance name.
                                                if this value not provided, it will
                                                automatically select source instance name

        destination_backupset   (str)   --  destination backupset name.
                                                if this value not provided, it will
                                                automatically select source backupset
        sf_options:

            destination_path    (str)   :   staging path for salesforce restore data

            streams             (int)   :   no of streams to use for restore
                default: 2

            copy_precedence     (int)   :   copy number to use for restore
                default: 0

            from_time           (str)   :   date to get the contents after
                                                format: dd/MM/YYYY
                                                gets contents from 01/01/1970 if not specified
                default: None

            to_time             (str)   :   date to get the contents before
                                                format: dd/MM/YYYY
                                                gets contents till current day if not specified
                default: None

            show_deleted_files  (bool)  :   include deleted files in the content or not
                default: True

    Raises:
            SDKException:
                if from date value is incorrect

                if to date value is incorrect

                if to date is less than from date

                if failed to browse content

                if response is empty

                if response is not success

                if destination client does not exist

                if destination instance does not exist

                if destination backupset does not exist

    &#34;&#34;&#34;

    file_restore_option = {}

    if not sf_options:
        sf_options = {}

    # check if client name is correct
    if destination_client is None:
        destination_client = self._backupset_object._agent_object._client_object

    if isinstance(destination_client, Client):
        dest_client = destination_client
    elif isinstance(destination_client, str):
        dest_client = self._commcell_object.clients.get(destination_client)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

    dest_agent = Agent(dest_client, &#39;Cloud Apps&#39;)

    # check if instance name is correct
    if destination_instance is None:
        destination_instance = self._backupset_object._instance_object

    if isinstance(destination_instance, Instance):
        dest_instance = destination_instance
    elif isinstance(destination_instance, str):
        dest_instance = dest_agent.instances.get(destination_instance)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

    # check if backupset name is correct
    if destination_backupset is None:
        destination_backupset = self._backupset_object

    if isinstance(destination_backupset, SalesforceBackupset):
        dest_backupset = destination_backupset
    elif isinstance(destination_backupset, str):
        dest_backupset = SalesforceBackupset(dest_instance, destination_backupset)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;114&#39;)

    file_restore_option[&#34;dest_client_name&#34;] = dest_client.client_name
    file_restore_option[&#34;dest_instance_name&#34;] = dest_instance.instance_name
    file_restore_option[&#34;dest_backupset_name&#34;] = dest_backupset.backupset_name

    self._restore_salesforce_destination_json(file_restore_option)

    file_restore_option[&#34;paths&#34;] = [f&#34;/Metadata/unpackaged/{metadata_path}&#34; for metadata_path in metadata_list]

    # set the salesforce options
    file_restore_option[&#34;staging_path&#34;] = sf_options.get(
        &#34;destination_path&#34;, dest_backupset.download_cache_path
    )
    file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
    file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
    file_restore_option[&#34;to_fs&#34;] = False
    file_restore_option[&#34;to_cloud&#34;] = True
    file_restore_option[&#34;from_database&#34;] = False
    file_restore_option[&#34;is_metadata&#34;] = True

    # set the browse option
    file_restore_option[&#34;client_name&#34;] = self._backupset_object._agent_object._client_object.client_name
    file_restore_option[&#34;copy_precedence_applicable&#34;] = True
    file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
    file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
    file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

    # prepare and execute the Json
    request_json = self._prepare_salesforce_restore_json(file_restore_option)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_database"><code class="name flex">
<span>def <span class="ident">restore_to_database</span></span>(<span>self, objects_to_restore=None, destination_client=None, sf_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>perform Restore to
Database</p>
<h2 id="args">Args</h2>
<p>objects_to_restore
(str)
&ndash;
list of objects to restore</p>
<p>destination_client
(str)
&ndash;
destination clientname where cloud connector package
exists. if this value not provided, it will
automatically use source backup client</p>
<p>sf_options
(dict)</p>
<pre><code>destination_path    (str)   :   staging path for salesforce restore data.
                                    if this value is not provided, it will
                                    automatically use download cache path
                                    from source

db_type             (str)   :   database type. if database details does not
                                    provided, it will use syncdb database
                                    for restore
    default: SQLSERVER

db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

db_instance         (str)   :   database instance name
                                    (provide if applicable for that database type)

db_name             (str)   :   database database name
                                    (it is where data will be imported)

db_port             (str)   :   database connection port
    default: 1433

db_user_name        (str)   :   database username
                                    (it should have read/write permissions on db)

db_user_password    (str)   :   database user password

overrirde_table     (bool)  :   overrides the tables on  database
    default: True

dependent_level     (int)   :   restore dependent object based on selected level.
                                    0   -   no Children
                                    1   -   immediate children
                                   -1   -   All children
    default: 0

streams             (int)   :   no of streams to use for restore
    default: 2

copy_precedence     (int)   :   copy number to use for restore
    default: 0

from_date           (str)   :   date to get the contents after
                                    format: dd/MM/YYYY
                                    gets contents from 01/01/1970 if not specified
    default: 0

to_date             (str)   :   date to get the contents before
                                    format: dd/MM/YYYY
                                    gets contents till current day if not specified
    default: 0

show_deleted_files  (bool)  :   include deleted files in the content or not
    default: True
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if from time value is incorrect</p>
<pre><code>if to time value is incorrect

if to time is less than from time

if failed to browse content

if response is empty

if response is not success

if destination client does not exist on commcell

if all the database details not provided
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L435-L602" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_database(
        self,
        objects_to_restore=None,
        destination_client=None,
        sf_options=None):
    &#34;&#34;&#34;perform Restore to  Database

    Args:
        objects_to_restore  (str)   --  list of objects to restore

        destination_client  (str)   --  destination clientname where cloud connector package
                                            exists. if this value not provided, it will
                                            automatically use source backup client

        sf_options          (dict)

            destination_path    (str)   :   staging path for salesforce restore data.
                                                if this value is not provided, it will
                                                automatically use download cache path
                                                from source

            db_type             (str)   :   database type. if database details does not
                                                provided, it will use syncdb database
                                                for restore
                default: SQLSERVER

            db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

            db_instance         (str)   :   database instance name
                                                (provide if applicable for that database type)

            db_name             (str)   :   database database name
                                                (it is where data will be imported)

            db_port             (str)   :   database connection port
                default: 1433

            db_user_name        (str)   :   database username
                                                (it should have read/write permissions on db)

            db_user_password    (str)   :   database user password

            overrirde_table     (bool)  :   overrides the tables on  database
                default: True

            dependent_level     (int)   :   restore dependent object based on selected level.
                                                0   -   no Children
                                                1   -   immediate children
                                               -1   -   All children
                default: 0

            streams             (int)   :   no of streams to use for restore
                default: 2

            copy_precedence     (int)   :   copy number to use for restore
                default: 0

            from_date           (str)   :   date to get the contents after
                                                format: dd/MM/YYYY
                                                gets contents from 01/01/1970 if not specified
                default: 0

            to_date             (str)   :   date to get the contents before
                                                format: dd/MM/YYYY
                                                gets contents till current day if not specified
                default: 0

            show_deleted_files  (bool)  :   include deleted files in the content or not
                default: True

    Raises:
        SDKException:
            if from time value is incorrect

            if to time value is incorrect

            if to time is less than from time

            if failed to browse content

            if response is empty

            if response is not success

            if destination client does not exist on commcell

            if all the database details not provided

    &#34;&#34;&#34;
    file_restore_option = {}

    if sf_options is None:
        sf_options = {}

    # check if client name is correct
    if destination_client is None:
        destination_client = self._backupset_object._instance_object.proxy_client

    if isinstance(destination_client, Client):
        dest_client = destination_client
    elif isinstance(destination_client, str):
        dest_client = Client(self._commcell_object, destination_client)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

    if not (&#39;db_host_name&#39; in sf_options and
            &#39;db_instance&#39; in sf_options and
            &#39;db_name&#39; in sf_options and
            &#39;db_user_name&#39; in sf_options and
            &#39;db_user_password&#39; in sf_options):
        raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

    # set the destination client
    file_restore_option[&#34;client_name&#34;] = dest_client.client_name
    file_restore_option[&#34;destination_path&#34;] = sf_options.get(
        &#34;destination_path&#34;, self._backupset_object.download_cache_path
    )

    self._restore_destination_json(file_restore_option)

    # process the objects to restore
    if isinstance(objects_to_restore, list):
        objects_to_restore_list = objects_to_restore

    else:
        objects_to_restore_list = [objects_to_restore]

    file_restore_option[&#34;paths&#34;] = []
    browse_files, _ = self.browse(
        path=&#39;/Objects&#39;,
        from_time=sf_options.get(&#34;from_time&#34;, 0),
        to_time=sf_options.get(&#34;to_time&#34;, 0)
    )

    for each_object in objects_to_restore_list:
        if each_object.find(&#39;/Files&#39;) &lt; 0:
            file_restore_option[&#34;paths&#34;].append(
                self.check_object_in_browse(&#34;%s&#34; % each_object, browse_files)
            )

    # set the salesforce options
    file_restore_option[&#34;staging_path&#34;] = sf_options.get(
        &#34;destination_path&#34;, self._backupset_object.download_cache_path
    )
    file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
    file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
    file_restore_option[&#34;to_fs&#34;] = False
    file_restore_option[&#34;db_enabled&#34;] = True
    file_restore_option[&#34;db_type&#34;] = sf_options.get(&#34;db_type&#34;, &#39;SQLSERVER&#39;)
    file_restore_option[&#34;db_host_name&#34;] = sf_options.get(&#34;db_host_name&#34;, &#34;&#34;)
    file_restore_option[&#34;db_instance&#34;] = sf_options.get(&#34;db_instance&#34;, &#34;&#34;)
    file_restore_option[&#34;db_name&#34;] = sf_options.get(&#34;db_name&#34;, &#34;autorestoredb&#34;)
    file_restore_option[&#34;db_port&#34;] = sf_options.get(&#34;db_port&#34;, &#39;1433&#39;)
    file_restore_option[&#34;db_user_name&#34;] = sf_options.get(&#34;db_user_name&#34;, &#39;sa&#39;)
    db_base64_password = b64encode(sf_options[&#39;db_user_password&#39;].encode()).decode()
    file_restore_option[&#34;db_user_password&#34;] = db_base64_password
    file_restore_option[&#34;override_table&#34;] = sf_options.get(&#34;override_table&#34;, True)

    # set the browse option
    file_restore_option[&#34;copy_precedence_applicable&#34;] = True
    file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
    file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
    file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

    # prepare and execute the Json
    request_json = self._prepare_salesforce_restore_json(file_restore_option)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_file_system"><code class="name flex">
<span>def <span class="ident">restore_to_file_system</span></span>(<span>self, objects_to_restore=None, destination_client=None, sf_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>perform restore to file system to the provided path</p>
<h2 id="args">Args</h2>
<p>objects_to_restore
(str)
&ndash;
list of objects to restore</p>
<p>destination_client
(str)
&ndash;
destination client name where cloud connector
package exists if this value not provided,
it will automatically use source backup client</p>
<p>sf_options
(dict)</p>
<pre><code>destination_path    (str)   :   staging path for salesforce restore data.
                                    if this value is not provided, uses download
                                    cache path from source

dependent_level     (int)   :   restore children based on selected level.
                                    0   -   no Children
                                    1   -   immediate children
                                    -1  -   All children
    default: 0

streams             (int)   :   no of streams to use for restore
    default: 2

copy_precedence     (int)   :   copy number to use for restore
    default: 0

from_time           (str)   :   date to get the contents after
                                    format: dd/MM/YYYY
                                    gets content from 01/01/1970 if not specified
    default: 0

to_time             (str)   :   date to get the contents before
                                    format: dd/MM/YYYY
                                    gets content till latest if not specified
    default: 0

show_deleted_files  (bool)  :   include deleted files in the content or not
    default: True
</code></pre>
<p>Raises:
SDKException:
if from time value is incorrect</p>
<pre><code>        if to time value is incorrect

        if to time is less than from time

        if failed to browse content

        if response is empty

        if response is not success

        if destination client does not exist on commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L311-L433" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_file_system(
        self,
        objects_to_restore=None,
        destination_client=None,
        sf_options=None):
    &#34;&#34;&#34;perform restore to file system to the provided path

    Args:
        objects_to_restore  (str)   --  list of objects to restore

        destination_client  (str)   --  destination client name where cloud connector
                                            package exists if this value not provided,
                                            it will automatically use source backup client

        sf_options          (dict)

            destination_path    (str)   :   staging path for salesforce restore data.
                                                if this value is not provided, uses download
                                                cache path from source

            dependent_level     (int)   :   restore children based on selected level.
                                                0   -   no Children
                                                1   -   immediate children
                                                -1  -   All children
                default: 0

            streams             (int)   :   no of streams to use for restore
                default: 2

            copy_precedence     (int)   :   copy number to use for restore
                default: 0

            from_time           (str)   :   date to get the contents after
                                                format: dd/MM/YYYY
                                                gets content from 01/01/1970 if not specified
                default: 0

            to_time             (str)   :   date to get the contents before
                                                format: dd/MM/YYYY
                                                gets content till latest if not specified
                default: 0

            show_deleted_files  (bool)  :   include deleted files in the content or not
                default: True

     Raises:
            SDKException:
                if from time value is incorrect

                if to time value is incorrect

                if to time is less than from time

                if failed to browse content

                if response is empty

                if response is not success

                if destination client does not exist on commcell

    &#34;&#34;&#34;

    file_restore_option = {}

    if sf_options is None:
        sf_options = {}

    # check if client name is correct
    if destination_client is None:
        destination_client = self._backupset_object._instance_object.proxy_client

    if isinstance(destination_client, Client):
        client = destination_client
    elif isinstance(destination_client, str):
        client = Client(self._commcell_object, destination_client)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

    file_restore_option[&#34;client_name&#34;] = client.client_name
    file_restore_option[&#34;destination_path&#34;] = sf_options.get(
        &#34;destination_path&#34;, self._backupset_object.download_cache_path
    )

    self._restore_destination_json(file_restore_option)

    # process the objects to restore
    if isinstance(objects_to_restore, list):
        objects_to_restore_list = objects_to_restore

    else:
        objects_to_restore_list = [objects_to_restore]

    file_restore_option[&#34;paths&#34;] = []
    browse_files, _ = self.browse(
        path=&#39;/Objects&#39;,
        from_time=sf_options.get(&#34;from_time&#34;, 0),
        to_time=sf_options.get(&#34;to_time&#34;, 0)
    )

    for each_object in objects_to_restore_list:
        if each_object.find(&#39;/Files&#39;) &lt; 0:
            file_restore_option[&#34;paths&#34;].append(
                self.check_object_in_browse(&#34;%s&#34; % each_object, browse_files)
            )

    # set the salesforce options
    file_restore_option[&#34;staging_path&#34;] = sf_options.get(
        &#34;destination_path&#34;,
        self._backupset_object.download_cache_path
    )
    file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
    file_restore_option[&#34;to_fs&#34;] = True
    file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)

    # set the browse option
    file_restore_option[&#34;copy_precedence_applicable&#34;] = True
    file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)

    # prepare and execute the Json
    request_json = self._prepare_salesforce_restore_json(file_restore_option)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_salesforce_from_database"><code class="name flex">
<span>def <span class="ident">restore_to_salesforce_from_database</span></span>(<span>self, objects_to_restore=None, destination_client=None, destination_instance=None, destination_backupset=None, sf_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>perform Restore to Salesforce from Database</p>
<h2 id="args">Args</h2>
<p>objects_to_restore
(str)
&ndash;
list of objects to restore</p>
<p>destination_client
(str)
&ndash;
destination pseudo client name.
if this value not provided, it will
automatically select source client</p>
<p>destination_instance
(str)
&ndash;
destination instance name.
if this value not provided, it will
automatically select source instance name</p>
<p>destination_backupset
(str)
&ndash;
destination backupset name.
if this value not provided, it will
automatically select source backupset
sf_options
(dict)</p>
<pre><code>destination_path    (str)   :   staging path for salesforce restore data

db_type             (str)   :   database type. if database details does not
                                    provided, it will use syncdb database
                                    for restore
    default: SQLSERVER

db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

db_instance         (str)   :   database instance name
                                    (provide if applicable for that database type)

db_name             (str)   :   database database name
                                    (it is where data will be imported)

db_port             (str)   :   database connection port
    default: 1433

db_user_name        (str)   :   database username
                                    (read/write permissions needed on db)

db_user_password    (str)   :   database user password

overrirde_table     (bool)  :   overrides the tables on  database
    default: True

dependent_level     (int)   :   restore children based on selected level.
                                    0   -   no Children
                                    1   -   immediate children
                                    -1  -   All children
    default: 0

streams             (int)   :   no of streams to use for restore
    default: 2

copy_precedence     (int)   :   copy number to use for restore
    default: 0

from_time           (str)   :   date to get the contents after
                                    format: dd/MM/YYYY
                                    gets contents from 01/01/1970 if not specified
    default: None

to_time             (str)   :   date to get the contents before
                                    format: dd/MM/YYYY
                                    gets contents till current day if not specified
    default: None

show_deleted_files  (bool)  :   include deleted files in the content or not
    default: True
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if from date value is incorrect</p>
<pre><code>if to date value is incorrect

if to date is less than from date

if failed to browse content

if response is empty

if response is not success

if destination client does not exist

if destination instance does not exist

if destination backupset does not exist

if syncdb is not enabled and user not provided the database details
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L604-L831" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_salesforce_from_database(
        self,
        objects_to_restore=None,
        destination_client=None,
        destination_instance=None,
        destination_backupset=None,
        sf_options=None):
    &#34;&#34;&#34;perform Restore to Salesforce from Database

    Args:
        objects_to_restore      (str)   --  list of objects to restore

        destination_client      (str)   --  destination pseudo client name.
                                                if this value not provided, it will
                                                automatically select source client

        destination_instance    (str)   --  destination instance name.
                                                if this value not provided, it will
                                                automatically select source instance name

        destination_backupset   (str)   --  destination backupset name.
                                                if this value not provided, it will
                                                automatically select source backupset
        sf_options              (dict)

            destination_path    (str)   :   staging path for salesforce restore data

            db_type             (str)   :   database type. if database details does not
                                                provided, it will use syncdb database
                                                for restore
                default: SQLSERVER

            db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

            db_instance         (str)   :   database instance name
                                                (provide if applicable for that database type)

            db_name             (str)   :   database database name
                                                (it is where data will be imported)

            db_port             (str)   :   database connection port
                default: 1433

            db_user_name        (str)   :   database username
                                                (read/write permissions needed on db)

            db_user_password    (str)   :   database user password

            overrirde_table     (bool)  :   overrides the tables on  database
                default: True

            dependent_level     (int)   :   restore children based on selected level.
                                                0   -   no Children
                                                1   -   immediate children
                                                -1  -   All children
                default: 0

            streams             (int)   :   no of streams to use for restore
                default: 2

            copy_precedence     (int)   :   copy number to use for restore
                default: 0

            from_time           (str)   :   date to get the contents after
                                                format: dd/MM/YYYY
                                                gets contents from 01/01/1970 if not specified
                default: None

            to_time             (str)   :   date to get the contents before
                                                format: dd/MM/YYYY
                                                gets contents till current day if not specified
                default: None

            show_deleted_files  (bool)  :   include deleted files in the content or not
                default: True

    Raises:
        SDKException:
            if from date value is incorrect

            if to date value is incorrect

            if to date is less than from date

            if failed to browse content

            if response is empty

            if response is not success

            if destination client does not exist

            if destination instance does not exist

            if destination backupset does not exist

            if syncdb is not enabled and user not provided the database details

    &#34;&#34;&#34;
    file_restore_option = {}

    if sf_options is None:
        sf_options = {}

    # check if client name is correct
    if destination_client is None:
        destination_client = self._backupset_object._agent_object._client_object

    if isinstance(destination_client, Client):
        dest_client = destination_client
    elif isinstance(destination_client, str):
        dest_client = Client(self._commcell_object, destination_client)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

    dest_agent = Agent(dest_client, &#39;Cloud Apps&#39;, &#39;134&#39;)

    # check if instance name is correct
    if destination_instance is None:
        destination_instance = self._backupset_object._instance_object

    if isinstance(destination_instance, Instance):
        dest_instance = destination_instance
    elif isinstance(destination_instance, str):
        dest_instance = dest_agent.instances.get(destination_instance)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

    # check if backupset name is correct
    if destination_backupset is None:
        destination_backupset = self._backupset_object

    if isinstance(destination_backupset, SalesforceBackupset):
        dest_backupset = destination_backupset
    elif isinstance(destination_backupset, str):
        dest_backupset = SalesforceBackupset(dest_instance, destination_backupset)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;114&#39;)

    if not self._backupset_object.is_sync_db_enabled:
        if not (
                &#39;db_host_name&#39; in sf_options and &#39;db_instance&#39; in sf_options and
                &#39;db_name&#39; in sf_options and &#39;db_user_name&#39; in sf_options and
                &#39;db_user_password&#39; in sf_options):
            raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

    # set salesforce destination client
    file_restore_option[&#34;dest_client_name&#34;] = dest_client.client_name
    file_restore_option[&#34;dest_instance_name&#34;] = dest_instance.instance_name
    file_restore_option[&#34;dest_backupset_name&#34;] = dest_backupset.backupset_name

    self._restore_salesforce_destination_json(file_restore_option)

    # process the objects to restore
    if isinstance(objects_to_restore, list):
        objects_to_restore_list = objects_to_restore

    else:
        objects_to_restore_list = [objects_to_restore]

    file_restore_option[&#34;paths&#34;] = []
    browse_files, _ = self.browse(
        path=&#39;/Objects&#39;, from_time=sf_options.get(&#34;from_time&#34;, 0),
        to_time=sf_options.get(&#34;to_time&#34;, 0))

    for each_object in objects_to_restore_list:
        if each_object.find(&#39;/Files&#39;) &lt; 0:
            file_restore_option[&#34;paths&#34;].append(
                self.check_object_in_browse(
                    &#34;%s&#34; %
                    each_object,
                    browse_files))

    # set the salesforce options
    file_restore_option[&#34;staging_path&#34;] = sf_options.get(
        &#34;destination_path&#34;,
        dest_backupset.download_cache_path)
    file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
    file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
    file_restore_option[&#34;to_fs&#34;] = False
    file_restore_option[&#34;to_cloud&#34;] = True
    file_restore_option[&#34;from_database&#34;] = True
    file_restore_option[&#34;db_enabled&#34;] = True
    if self._backupset_object.is_sync_db_enabled or (&#39;db_host_name&#39; in sf_options):
        if self._backupset_object.sync_db_type is None:
            dbtype = &#39;SQLSERVER&#39;
        else:
            dbtype = self._backupset_object.sync_db_type
        file_restore_option[&#34;db_type&#34;] = sf_options.get(&#34;db_type&#34;, dbtype)
        file_restore_option[&#34;db_host_name&#34;] = sf_options.get(
            &#34;db_host_name&#34;, self._backupset_object.sync_db_host
        )
        file_restore_option[&#34;db_instance&#34;] = sf_options.get(
            &#34;db_instance&#34;, self._backupset_object.sync_db_instance
        )
        file_restore_option[&#34;db_name&#34;] = sf_options.get(
            &#34;db_name&#34;, self._backupset_object.sync_db_name
        )
        file_restore_option[&#34;db_port&#34;] = sf_options.get(
            &#34;db_port&#34;, self._backupset_object.sync_db_port
        )
        file_restore_option[&#34;db_user_name&#34;] = sf_options.get(
            &#34;db_user_name&#34;, self._backupset_object.sync_db_user_name
        )

        if &#39;db_user_password&#39; in sf_options:
            sf_options[&#39;_db_base64_password&#39;] = b64encode(
                sf_options[&#39;db_user_password&#39;].encode()).decode()

        file_restore_option[&#34;db_user_password&#34;] = sf_options.get(
            &#34;_db_base64_password&#34;,
            self._backupset_object._sync_db_user_password)
    else:
        raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

    file_restore_option[&#34;override_table&#34;] = sf_options.get(&#34;override_table&#34;, True)

    # set the browse option
    file_restore_option[&#34;client_name&#34;] = self._backupset_object._agent_object._client_object.client_name
    file_restore_option[&#34;copy_precedence_applicable&#34;] = True
    file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
    file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
    file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

    # prepare and execute the Json
    request_json = self._prepare_salesforce_restore_json(file_restore_option)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_salesforce_from_media"><code class="name flex">
<span>def <span class="ident">restore_to_salesforce_from_media</span></span>(<span>self, objects_to_restore=None, destination_client=None, destination_instance=None, destination_backupset=None, sf_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>perform Restore to Salesforce from Media.</p>
<h2 id="args">Args</h2>
<p>objects_to_restore
(str)
&ndash;
list of objects to restore</p>
<p>destination_client
(str)
&ndash;
destination pseudo client name.
if this value not provided, it will
automatically select source client</p>
<p>destination_instance
(str)
&ndash;
destination instance name.
if this value not provided, it will
automatically select source instance name</p>
<p>destination_backupset
(str)
&ndash;
destination backupset name.
if this value not provided, it will
automatically select source backupset
sf_options
(dict)</p>
<pre><code>destination_path    (str)   :   staging path for salesforce restore data

db_type             (str)   :   database type. if database details does not
                                    provided, it will use syncdb database
                                    for restore
default: SQLSERVER

db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

db_instance         (str)   :   database instance name
                                    (provide if applicable for that database type)

db_name             (str)   :   database database name
                                    (it is where data will be imported)

db_port             (str)   :   database connection port
    default: 1433

db_user_name        (str)   :   database username
                                    (read/write permissions needed on db)

db_user_password    (str)   :   database user password

overrirde_table     (bool)  :   overrides the tables on  database
    default: True

dependent_level     (int)   :   restore children based on selected level.
                                    0   -   no Children
                                    1   -   immediate children
                                    -1  -   All children
    default: 0

streams             (int)   :   no of streams to use for restore
    default: 2

copy_precedence     (int)   :   copy number to use for restore
    default: 0

from_time           (str)   :   date to get the contents after
                                    format: dd/MM/YYYY
                                    gets contents from 01/01/1970 if not specified
    default: None

to_time             (str)   :   date to get the contents before
                                    format: dd/MM/YYYY
                                    gets contents till current day if not specified
    default: None

show_deleted_files  (bool)  :   include deleted files in the content or not
    default: True
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if from date value is incorrect</p>
<pre><code>if to date value is incorrect

if to date is less than from date

if failed to browse content

if response is empty

if response is not success

if destination client does not exist

if destination instance does not exist

if destination backupset does not exist

if user does not provide staging database details
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/salesforce_subclient.py#L833-L1036" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_salesforce_from_media(
        self,
        objects_to_restore=None,
        destination_client=None,
        destination_instance=None,
        destination_backupset=None,
        sf_options=None):
    &#34;&#34;&#34;perform Restore to Salesforce from Media.

    Args:
        objects_to_restore      (str)   --  list of objects to restore

        destination_client      (str)   --  destination pseudo client name.
                                                if this value not provided, it will
                                                automatically select source client

        destination_instance    (str)   --  destination instance name.
                                                if this value not provided, it will
                                                automatically select source instance name

        destination_backupset   (str)   --  destination backupset name.
                                                if this value not provided, it will
                                                automatically select source backupset
        sf_options              (dict)

            destination_path    (str)   :   staging path for salesforce restore data

            db_type             (str)   :   database type. if database details does not
                                                provided, it will use syncdb database
                                                for restore
            default: SQLSERVER

            db_host_name             (str)   :   database hostname (ex:dbhost.company.com)

            db_instance         (str)   :   database instance name
                                                (provide if applicable for that database type)

            db_name             (str)   :   database database name
                                                (it is where data will be imported)

            db_port             (str)   :   database connection port
                default: 1433

            db_user_name        (str)   :   database username
                                                (read/write permissions needed on db)

            db_user_password    (str)   :   database user password

            overrirde_table     (bool)  :   overrides the tables on  database
                default: True

            dependent_level     (int)   :   restore children based on selected level.
                                                0   -   no Children
                                                1   -   immediate children
                                                -1  -   All children
                default: 0

            streams             (int)   :   no of streams to use for restore
                default: 2

            copy_precedence     (int)   :   copy number to use for restore
                default: 0

            from_time           (str)   :   date to get the contents after
                                                format: dd/MM/YYYY
                                                gets contents from 01/01/1970 if not specified
                default: None

            to_time             (str)   :   date to get the contents before
                                                format: dd/MM/YYYY
                                                gets contents till current day if not specified
                default: None

            show_deleted_files  (bool)  :   include deleted files in the content or not
                default: True

    Raises:
            SDKException:
                if from date value is incorrect

                if to date value is incorrect

                if to date is less than from date

                if failed to browse content

                if response is empty

                if response is not success

                if destination client does not exist

                if destination instance does not exist

                if destination backupset does not exist

                if user does not provide staging database details

    &#34;&#34;&#34;

    file_restore_option = {}

    if sf_options is None:
        sf_options = {}

    # check if client name is correct
    if destination_client is None:
        destination_client = self._backupset_object._agent_object._client_object

    if isinstance(destination_client, Client):
        dest_client = destination_client
    elif isinstance(destination_client, str):
        dest_client = Client(self._commcell_object, destination_client)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

    dest_agent = Agent(dest_client, &#39;Cloud Apps&#39;)

    # check if instance name is correct
    if destination_instance is None:
        destination_instance = self._backupset_object._instance_object

    if isinstance(destination_instance, Instance):
        dest_instance = destination_instance
    elif isinstance(destination_instance, str):
        dest_instance = dest_agent.instances.get(destination_instance)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

    # check if backupset name is correct
    if destination_backupset is None:
        destination_backupset = self._backupset_object

    if isinstance(destination_backupset, SalesforceBackupset):
        dest_backupset = destination_backupset
    elif isinstance(destination_backupset, str):
        dest_backupset = SalesforceBackupset(dest_instance, destination_backupset)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;114&#39;)

    if not (&#39;db_host_name&#39; in sf_options and
            &#39;db_instance&#39; in sf_options and
            &#39;db_name&#39; in sf_options and
            &#39;db_user_name&#39; in sf_options and
            &#39;db_user_password&#39; in sf_options):
        raise SDKException(&#39;Salesforce&#39;, &#39;101&#39;)

    file_restore_option[&#34;dest_client_name&#34;] = dest_client.client_name
    file_restore_option[&#34;dest_instance_name&#34;] = dest_instance.instance_name
    file_restore_option[&#34;dest_backupset_name&#34;] = dest_backupset.backupset_name

    self._restore_salesforce_destination_json(file_restore_option)

    # process the objects to restore
    if isinstance(objects_to_restore, list):
        objects_to_restore_list = objects_to_restore

    else:
        objects_to_restore_list = [objects_to_restore]

    file_restore_option[&#34;paths&#34;] = []
    browse_files, _ = self.browse(
        path=&#39;/Objects&#39;,
        from_time=sf_options.get(&#34;from_time&#34;, 0),
        to_time=sf_options.get(&#34;to_time&#34;, 0)
    )

    for each_object in objects_to_restore_list:
        if each_object.find(&#39;/Files&#39;) &lt; 0:
            file_restore_option[&#34;paths&#34;].append(
                self.check_object_in_browse(&#34;%s&#34; % each_object, browse_files)
            )

    # set the salesforce options
    file_restore_option[&#34;staging_path&#34;] = sf_options.get(
        &#34;destination_path&#34;, dest_backupset.download_cache_path
    )
    file_restore_option[&#34;dependent_level&#34;] = sf_options.get(&#34;dependent_level&#34;, 0)
    file_restore_option[&#34;streams&#34;] = sf_options.get(&#34;streams&#34;, 2)
    file_restore_option[&#34;to_fs&#34;] = False
    file_restore_option[&#34;to_cloud&#34;] = True
    file_restore_option[&#34;from_database&#34;] = False
    file_restore_option[&#34;db_enabled&#34;] = True
    file_restore_option[&#34;db_type&#34;] = sf_options.get(&#34;db_type&#34;, &#39;SQLSERVER&#39;)
    file_restore_option[&#34;db_host_name&#34;] = sf_options.get(&#34;db_host_name&#34;, &#34;&#34;)
    file_restore_option[&#34;db_instance&#34;] = sf_options.get(&#34;db_instance&#34;, &#34;&#34;)
    file_restore_option[&#34;db_name&#34;] = sf_options.get(&#34;db_name&#34;, &#39;autorestoredb&#39;)
    file_restore_option[&#34;db_port&#34;] = sf_options.get(&#34;db_port&#34;, &#39;1433&#39;)
    file_restore_option[&#34;db_user_name&#34;] = sf_options.get(&#34;db_user_name&#34;, &#39;sa&#39;)
    db_base64_password = b64encode(sf_options[&#39;db_user_password&#39;].encode()).decode()
    file_restore_option[&#34;db_user_password&#34;] = db_base64_password
    file_restore_option[&#34;override_table&#34;] = sf_options.get(&#34;override_table&#34;, True)

    # set the browse option
    file_restore_option[&#34;client_name&#34;] = self._backupset_object._agent_object._client_object.client_name
    file_restore_option[&#34;copy_precedence_applicable&#34;] = True
    file_restore_option[&#34;copy_precedence&#34;] = sf_options.get(&#34;copy_precedence&#34;, 0)
    file_restore_option[&#34;from_time&#34;] = sf_options.get(&#34;from_time&#34;, 0)
    file_restore_option[&#34;to_time&#34;] = sf_options.get(&#34;to_time&#34;, 0)

    # prepare and execute the Json
    request_json = self._prepare_salesforce_restore_json(file_restore_option)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="../casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.allow_multiple_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.backup" href="../../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.browse" href="../../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.data_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.deduplication_options" href="../../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.description" href="../../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.display_name" href="../../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup_at_time" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup_days" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.encryption_flag" href="../../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.exclude_from_sla" href="../../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find" href="../../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find_latest_job" href="../../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.get_ma_associated_storagepolicy" href="../../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_blocklevel_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_intelli_snap_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_on_demand_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_trueup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.last_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.list_media" href="../../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.name" href="../../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.network_agent" href="../../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.next_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.plan" href="../../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.properties" href="../../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.read_buffer_size" href="../../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.refresh" href="../../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.restore_in_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.restore_out_of_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.run_content_indexing" href="../../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_backup_nodes" href="../../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.snapshot_engine_name" href="../../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.software_compression" href="../../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma_id" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_policy" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_guid" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_id" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_name" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.unset_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.update_properties" href="../../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.cloudapps" href="index.html">cvpysdk.subclients.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient">SalesforceSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.archived_deleted" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.archived_deleted">archived_deleted</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.check_object_in_browse" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.check_object_in_browse">check_object_in_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.disable_archived_deleted" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.disable_archived_deleted">disable_archived_deleted</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.disable_files" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.disable_files">disable_files</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.disable_metadata" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.disable_metadata">disable_metadata</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.enable_archived_deleted" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.enable_archived_deleted">enable_archived_deleted</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.enable_files" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.enable_files">enable_files</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.enable_metadata" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.enable_metadata">enable_metadata</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.files" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.files">files</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.metadata" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.metadata">metadata</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.metadata_restore_to_salesforce" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.metadata_restore_to_salesforce">metadata_restore_to_salesforce</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.objects" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.objects">objects</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_database" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_database">restore_to_database</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_file_system" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_file_system">restore_to_file_system</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_salesforce_from_database" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_salesforce_from_database">restore_to_salesforce_from_database</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_salesforce_from_media" href="#cvpysdk.subclients.cloudapps.salesforce_subclient.SalesforceSubclient.restore_to_salesforce_from_media">restore_to_salesforce_from_media</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>