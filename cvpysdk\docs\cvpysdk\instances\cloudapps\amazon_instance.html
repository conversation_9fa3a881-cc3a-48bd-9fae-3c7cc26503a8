<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.cloudapps.amazon_instance API documentation</title>
<meta name="description" content="File for operating on a amazon instances …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.cloudapps.amazon_instance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a amazon instances.</p>
<p>AmazonRDSInstance, AmazonRedshiftInstance, AmazonDocumentDBInstance, AmazonDynamoDBInstance
are the classes defined in this file.</p>
<p>AmazonRDSInstance:
Derived class from CloudDatabaseInstance Base class, representing a Cloud Database instance of
type Amazon RDS and to perform operations on that instance</p>
<h2 id="amazonrdsinstance">Amazonrdsinstance</h2>
<p><strong>init</strong>()
&ndash;
Initializes amazon rds instance object with associated
agent_object, instance name and instance id</p>
<p>_process_browse_request()
&ndash;
Process the response received from browse request</p>
<p>_restore_json()
&ndash; Generates Restore json with restore options</p>
<p>AmazonRedshiftInstance:
Derived class from CloudDatabaseInstance Base class, representing a
Cloud Database instance of type Amazon Redshift and to
perform operations on that instance</p>
<h2 id="amazonredshiftinstance">Amazonredshiftinstance</h2>
<p><strong>init</strong>()
&ndash;
Initializes amazon redshift instance object with associated
agent_object, instance name and instance id</p>
<p>_process_browse_request()
&ndash;
Process the response received from browse request</p>
<p>_restore_json()
&ndash; Generates Restore json with restore options</p>
<p>AmazonDocumentDBInstance: Derived class from CloudDatabaseInstance Base class, representing a
Cloud Database instance of type Amazon DocumentDB and to perform
operations on that instance</p>
<p>AmazonDocumentDBInstance</p>
<pre><code>__init__()                      -- Initializes amazon documentdb instance object with associated
agent_object, instance name and instance id

_process_browse_request()       --  Process the response received from browse request

_restore_json()                 -- Generates Restore json with restore options
</code></pre>
<p>AmazonDynamoDBInstance: Derived class from CloudDatabaseInstance Base class, representing a
Cloud Database instance of type Amazon DynamoDB and to
perform operations on that instance</p>
<h2 id="amazondynamodbinstance">Amazondynamodbinstance</h2>
<p>_restore_json()
&ndash; Generates Restore json with restore option</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/amazon_instance.py#L1-L561" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34; File for operating on a amazon instances.

AmazonRDSInstance, AmazonRedshiftInstance, AmazonDocumentDBInstance, AmazonDynamoDBInstance
are the classes defined in this file.

AmazonRDSInstance:  Derived class from CloudDatabaseInstance Base class, representing a Cloud Database instance of
                    type Amazon RDS and to perform operations on that instance

AmazonRDSInstance:

    __init__()                      --  Initializes amazon rds instance object with associated
    agent_object, instance name and instance id

    _process_browse_request()       --  Process the response received from browse request

    _restore_json()                 -- Generates Restore json with restore options


AmazonRedshiftInstance:   Derived class from CloudDatabaseInstance Base class, representing a
                        Cloud Database instance of type Amazon Redshift and to
                        perform operations on that instance

AmazonRedshiftInstance:

    __init__()                      --  Initializes amazon redshift instance object with associated
    agent_object, instance name and instance id

    _process_browse_request()       --  Process the response received from browse request

    _restore_json()                 -- Generates Restore json with restore options


AmazonDocumentDBInstance: Derived class from CloudDatabaseInstance Base class, representing a
                            Cloud Database instance of type Amazon DocumentDB and to perform
                            operations on that instance

AmazonDocumentDBInstance

    __init__()                      -- Initializes amazon documentdb instance object with associated
    agent_object, instance name and instance id

    _process_browse_request()       --  Process the response received from browse request

    _restore_json()                 -- Generates Restore json with restore options

AmazonDynamoDBInstance: Derived class from CloudDatabaseInstance Base class, representing a
                        Cloud Database instance of type Amazon DynamoDB and to
                        perform operations on that instance

AmazonDynamoDBInstance:

    _restore_json()                 -- Generates Restore json with restore option
&#34;&#34;&#34;

from __future__ import unicode_literals
from .cloud_database_instance import CloudDatabaseInstance
from ...exception import SDKException


class AmazonRDSInstance(CloudDatabaseInstance):
    &#34;&#34;&#34;Class for representing an Instance of Amazon RDS&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the AmazonRDSInstance class

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class

        &#34;&#34;&#34;
        super(
            AmazonRDSInstance,
            self).__init__(
                agent_object,
                instance_name,
                instance_id)

        self._browse_url = self._services[&#39;CLOUD_DATABASE_BROWSE&#39;]

    def _process_browse_response(self, flag, response):
        &#34;&#34;&#34; Process browse request response

            Args:

                flag -- indicates whether the rest API request is successful

                response -- response returned if the request was successful.

            Returns:

                dict    - The snapshot list JSON response received from the browse request

                Exception - If the browse request failed
        &#34;&#34;&#34;
        if flag:
            if response.json() and &#39;snapList&#39; in response.json():
                snapshot_list = response.json()[&#39;snapList&#39;]
            else:
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#34;Incorrect response from browse.\nResponse : {0}&#34;.format(
                        response.json()))
        else:
            o_str = &#39;Failed to browse content of this instance backups.\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(response))
        return snapshot_list

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

             Args:
                kwargs   (list)  --  list of options need to be set for restore

                Ex: For RDS Instance Cluster Restore following are the possible options
                    {
                        destination : &#39;instance/cluster&#39;,
                        source : &#39;snapshot&#39;,
                        options :   {
                                        &#39;archFileId&#39;: 123
                                        &#39;isMultiAZ&#39; : true,
                                        &#39;publicallyAccess&#39; : true,
                                        &#39;copyTagsToSnapshot&#39; : false,
                                        &#39;enableDeletionProtection&#39;: false,
                                        &#39;targetParameterGroupName&#39;: &#39;param&#39;,
                                        &#39;targetSubnetGroup&#39;: &#39;subnet&#39;,
                                        &#39;targetDBInstanceClass&#39;: &#39;dc-large-8&#39;,
                                        &#39;targetPort&#39;: 2990
                                    }
                    }

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        restore_json = super(
            AmazonRDSInstance,
            self)._restore_json(**kwargs)

        restore_options = {}
        if kwargs.get(&#34;restore_options&#34;):
            restore_options = kwargs[&#34;restore_options&#34;]
            for key in kwargs:
                if not key == &#34;restore_options&#34;:
                    restore_options[key] = kwargs[key]
        else:
            restore_options.update(kwargs)

        # Populate Redshift restore options
        rds_restore_json = {
            &#34;rdsRestoreOptions&#34;: {
                &#34;sourceSnap&#34;: {
                    &#34;snapShotName&#34;: restore_options[&#39;source&#39;]
                },
                &#34;targetDbName&#34;: restore_options[&#39;destination&#39;]
            }
        }

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;isMultiAZ&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;isMultiAZ&#39;, False)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;publicallyAccess&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;publicallyAccess&#39;, True)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;copyTagsToSnapshot&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;copyTagsToSnapshot&#39;, False)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;enableDeletionProtection&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;enableDeletionProtection&#39;, False)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;targetParameterGroupName&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetParameterGroupName&#39;, &#39;&#39;)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;targetSecurityGroupValue&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetSubnetGroup&#39;, &#39;&#39;)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;targetDBInstanceClass&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetDBInstanceClass&#39;, &#39;&#39;)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;targetPort&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetPort&#39;, 0)

        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = \
            rds_restore_json

        return restore_json


class AmazonRedshiftInstance(CloudDatabaseInstance):
    &#34;&#34;&#34;Class for representing an Instance of the Amazon Redshift&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the AmazonRedshiftInstance class

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class

        &#34;&#34;&#34;
        super(
            AmazonRedshiftInstance,
            self).__init__(
                agent_object,
                instance_name,
                instance_id)

        self._browse_url = self._services[&#39;CLOUD_DATABASE_BROWSE&#39;]

    def _process_browse_response(self, flag, response):
        &#34;&#34;&#34; Process browse request response

            Args:

                flag -- indicates whether the rest API request is successful

                response -- response returned if the request was successful.

            Returns:

                dict    - The snapshot list JSON response received from the browse request

                Exception - If the browse request failed
        &#34;&#34;&#34;
        if flag:
            if response.json() and &#39;snapList&#39; in response.json():
                snapshot_list = response.json()[&#39;snapList&#39;]
            else:
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#34;Incorrect response from browse.\nResponse : {0}&#34;.format(
                        response.json()))
        else:
            o_str = &#39;Failed to browse content of this instance backups.\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(response))
        return snapshot_list

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

             Args:
                kwargs   (list)  --  list of options need to be set for restore

                Ex: For Redshift Instance Cluster Restore following are the possible options
                    {
                        destination : &#39;cluster&#39;,
                        source : &#39;snapshot&#39;,
                        options :   {
                                        &#39;allowVersionUpgrade&#39; : true,
                                        &#39;publicallyAccessible&#39; : true,
                                        &#39;restoreTags&#39; : false,
                                        &#39;enableDeletionProtection&#39;: false,
                                        &#39;availabilityZone&#39;: &#39;us-east-2a&#39;,
                                        &#39;targetParameterGroup&#39;: &#39;param&#39;,
                                        &#39;targetSubnetGroup&#39;: &#39;subnet&#39;,
                                        &#39;nodeType&#39;: &#39;dc-large-8&#39;,
                                        &#39;targetPort&#39;: 2990,
                                        &#39;numberOfNodes&#39;: 1
                                    }
                    }

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        restore_json = super(
            AmazonRedshiftInstance,
            self)._restore_json(**kwargs)

        restore_options = {}
        if kwargs.get(&#34;restore_options&#34;):
            restore_options = kwargs[&#34;restore_options&#34;]
            for key in kwargs:
                if not key == &#34;restore_options&#34;:
                    restore_options[key] = kwargs[key]
        else:
            restore_options.update(kwargs)

        # Populate Redshift restore options
        redshift_restore_json = {
            &#34;redshiftRestoreOption&#34;: {
                &#34;targetInstanceId&#34;: restore_options[&#39;destination&#39;],
                &#34;restoreSnapshotId&#34;: restore_options[&#39;source&#39;]
            }
        }

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;allowVersionUpgrade&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;allowVersionUpgrade&#39;, True)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;publicallyAccessible&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;publicallyAccessible&#39;, True)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;restoreTags&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;restoreTags&#39;, False)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;enableDeletionProtection&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;enableDeletionProtection&#39;, False)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;availabilityZone&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;availabilityZone&#39;, &#39;&#39;)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;targetParameterGroupName&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetParameterGroupName&#39;, &#39;&#39;)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;targetSubnetGroup&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetSubnetGroup&#39;, &#39;&#39;)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;nodeType&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;nodeType&#39;, &#39;&#39;)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;targetPort&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetPort&#39;, 0)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;numberOfNodes&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;numberOfNodes&#39;, 1)

        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = \
            redshift_restore_json

        return restore_json


class AmazonDocumentDBInstance(CloudDatabaseInstance):
    &#34;&#34;&#34;Class for representing an Instance of the Amazon DocumentDB&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the AmazonDocumentDBInstance class

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class

        &#34;&#34;&#34;
        super(
            AmazonDocumentDBInstance,
            self).__init__(
                agent_object,
                instance_name,
                instance_id)

        self._browse_url = self._services[&#39;CLOUD_DATABASE_BROWSE&#39;]

    def _process_browse_response(self, flag, response):
        &#34;&#34;&#34; Process browse request response

            Args:

                flag -- indicates whether the rest API request is successful

                response -- response returned if the request was successful.

            Returns:

                dict    - The snapshot list JSON response received from the browse request

                Exception - If the browse request failed
        &#34;&#34;&#34;
        if flag:
            if response.json() and &#39;snapList&#39; in response.json():
                snapshot_list = response.json()[&#39;snapList&#39;]
            else:
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#34;Incorrect response from browse.\nResponse : {0}&#34;.format(
                        response.json()))
        else:
            o_str = &#39;Failed to browse content of this instance backups.\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(response))
        return snapshot_list

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

             Args:
                kwargs   (list)  --  list of options need to be set for restore

                Ex: For DocumentDB Instance Cluster Restore following are the possible options
                    {
                        destination : &#39;cluster&#39;,
                        source : &#39;snapshot&#39;,
                        options :   {
                                        &#39;restoreTags&#39; : false,
                                        &#39;enableDeletionProtection&#39;: false,
                                        &#39;availabilityZone&#39;: &#39;us-east-2a&#39;,
                                        &#39;targetSubnetGroup&#39;: &#39;subnet&#39;,
                                        &#39;targetInstanceClass&#39;: &#39;dc-large-8&#39;,
                                        &#39;targetPort&#39;: 2990,
                                        &#39;numberOfNodes&#39;: 1
                                    }
                    }

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        restore_json = super(
            AmazonDocumentDBInstance,
            self)._restore_json(**kwargs)

        restore_options = {}
        if kwargs.get(&#34;restore_options&#34;):
            restore_options = kwargs[&#34;restore_options&#34;]
            for key in kwargs:
                if not key == &#34;restore_options&#34;:
                    restore_options[key] = kwargs[key]
        else:
            restore_options.update(kwargs)

        # Populate DocumentDB restore options
        documentdb_restore_option = {
            &#34;documentDBRestoreOptions&#34;: {
                &#34;targetInstanceId&#34;: restore_options[&#39;destination&#39;],
                &#34;restoreSnapshotId&#34;: restore_options[&#39;source&#39;]
            }
        }

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;restoreTags&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;restoreTags&#39;, False)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;enableDeletionProtection&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;enableDeletionProtection&#39;, False)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;availabilityZone&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;availabilityZone&#39;, &#39;&#39;)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;targetSubnetGroup&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetSubnetGroup&#39;, &#39;&#39;)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;targetInstanceClass&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetInstanceClass&#39;, &#39;&#39;)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;targetPort&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetPort&#39;, 0)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;numberOfNodes&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;numberOfNodes&#39;, 1)

        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = \
            documentdb_restore_option

        return restore_json

class AmazonDynamoDBInstance(CloudDatabaseInstance):
    &#34;&#34;&#34;Class for representing an Instance of the Amazon DynamoDB&#34;&#34;&#34;

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

             Args:
                kwargs   (list)  --  list of options need to be set for restore

                For DynamoDB Instance Cluster Restore following are the required parameters
                        destination : &#34;&#34;,
                        source : &#34;&#34;,
                        options = {
                            &#39;paths&#39;:  (list of strings)
                            &#39;table_map&#39;: (list of dicts)
                            &#39;adjust_write_capacity&#39;: (int)
                            &#39;destination_client&#39;: (string))
                            &#39;destination_instance&#39;: string
                    }

                    }
                Example:
                        destination : &#34;&#34;,
                        source : &#34;&#34;,
                        options :   {
                                    &#39;paths&#39;: [&#39;/us-east-1/table_1&#39;],
                                    &#39;table_map&#39;: [{
                                    &#39;srcTable&#39;:{&#39;name&#39;: &#39;table_1&#39;, &#39;region&#39;:&#39;us-east-1&#39;},
                                    &#39;destTable&#39;:{&#39;name&#39;: &#39;table_2&#39;, &#39;region&#39;: &#39;us-east-2&#39;}
                                    }]
                                    &#39;adjust_write_capacity&#39;: 100,
                                    &#39;destination_client&#39;: &#39;client1&#39;,
                                    &#39;destination_instance&#39;: &#39;DynamoDB&#39;
                                    }
            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        restore_json = super(
            AmazonDynamoDBInstance,
            self)._restore_json(**kwargs)

        restore_options = {}
        if kwargs.get(&#39;options&#39;):
            restore_options = kwargs[&#39;options&#39;]
            for key in kwargs:
                if not key == &#39;options&#39;:
                    restore_options[key] = kwargs[key]
        else:
            restore_options.update(kwargs)

        source_backupset_id = int(self.backupsets.all_backupsets[&#39;defaultbackupset&#39;][&#39;id&#39;])
        dynamodb_restore_option = {
            &#34;dynamoDbRestoreOptions&#34;: {
                &#39;tempWriteThroughput&#39;: restore_options.get(&#39;adjust_write_capacity&#39;, &#39;&#39;),
                &#39;overwrite&#39;: restore_options.get(&#39;overwrite&#39;, False),
                &#39;destinationTableList&#39;: restore_options.get(&#39;table_map&#39;, [])
            }
        }
        destination_restore_json = (
            {
                &#34;noOfStreams&#34;: restore_options.get(&#34;number_of_streams&#34;, 2),
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: restore_options.get(&#34;destination_client&#34;, &#34;&#34;)
                },
                &#34;destinationInstance&#34;: {
                    &#34;clientName&#34;: restore_options.get(&#34;destination_client&#34;, &#34;&#34;),
                    &#34;instanceName&#34;: restore_options.get(&#34;destination_instance&#34;, &#34;&#34;),
                    &#34;appName&#34;: self._instance[&#39;appName&#39;]
                }

            })
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetId&#39;] = source_backupset_id
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#34;restoreOptions&#34;][&#34;destination&#34;] = destination_restore_json
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;] = dynamodb_restore_option
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;fileOption&#39;][&#39;sourceItem&#39;] = restore_options.get(&#34;paths&#34;, &#34;&#34;)
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;][&#39;instanceType&#39;] = 22

        return restore_json</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.cloudapps.amazon_instance.AmazonDocumentDBInstance"><code class="flex name class">
<span>class <span class="ident">AmazonDocumentDBInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of the Amazon DocumentDB</p>
<p>Initializes the object of the AmazonDocumentDBInstance class</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/amazon_instance.py#L353-L478" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AmazonDocumentDBInstance(CloudDatabaseInstance):
    &#34;&#34;&#34;Class for representing an Instance of the Amazon DocumentDB&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the AmazonDocumentDBInstance class

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class

        &#34;&#34;&#34;
        super(
            AmazonDocumentDBInstance,
            self).__init__(
                agent_object,
                instance_name,
                instance_id)

        self._browse_url = self._services[&#39;CLOUD_DATABASE_BROWSE&#39;]

    def _process_browse_response(self, flag, response):
        &#34;&#34;&#34; Process browse request response

            Args:

                flag -- indicates whether the rest API request is successful

                response -- response returned if the request was successful.

            Returns:

                dict    - The snapshot list JSON response received from the browse request

                Exception - If the browse request failed
        &#34;&#34;&#34;
        if flag:
            if response.json() and &#39;snapList&#39; in response.json():
                snapshot_list = response.json()[&#39;snapList&#39;]
            else:
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#34;Incorrect response from browse.\nResponse : {0}&#34;.format(
                        response.json()))
        else:
            o_str = &#39;Failed to browse content of this instance backups.\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(response))
        return snapshot_list

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

             Args:
                kwargs   (list)  --  list of options need to be set for restore

                Ex: For DocumentDB Instance Cluster Restore following are the possible options
                    {
                        destination : &#39;cluster&#39;,
                        source : &#39;snapshot&#39;,
                        options :   {
                                        &#39;restoreTags&#39; : false,
                                        &#39;enableDeletionProtection&#39;: false,
                                        &#39;availabilityZone&#39;: &#39;us-east-2a&#39;,
                                        &#39;targetSubnetGroup&#39;: &#39;subnet&#39;,
                                        &#39;targetInstanceClass&#39;: &#39;dc-large-8&#39;,
                                        &#39;targetPort&#39;: 2990,
                                        &#39;numberOfNodes&#39;: 1
                                    }
                    }

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        restore_json = super(
            AmazonDocumentDBInstance,
            self)._restore_json(**kwargs)

        restore_options = {}
        if kwargs.get(&#34;restore_options&#34;):
            restore_options = kwargs[&#34;restore_options&#34;]
            for key in kwargs:
                if not key == &#34;restore_options&#34;:
                    restore_options[key] = kwargs[key]
        else:
            restore_options.update(kwargs)

        # Populate DocumentDB restore options
        documentdb_restore_option = {
            &#34;documentDBRestoreOptions&#34;: {
                &#34;targetInstanceId&#34;: restore_options[&#39;destination&#39;],
                &#34;restoreSnapshotId&#34;: restore_options[&#39;source&#39;]
            }
        }

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;restoreTags&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;restoreTags&#39;, False)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;enableDeletionProtection&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;enableDeletionProtection&#39;, False)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;availabilityZone&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;availabilityZone&#39;, &#39;&#39;)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;targetSubnetGroup&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetSubnetGroup&#39;, &#39;&#39;)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;targetInstanceClass&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetInstanceClass&#39;, &#39;&#39;)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;targetPort&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetPort&#39;, 0)

        documentdb_restore_option[&#39;documentDBRestoreOptions&#39;][&#39;numberOfNodes&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;numberOfNodes&#39;, 1)

        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = \
            documentdb_restore_option

        return restore_json</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance">CloudDatabaseInstance</a></li>
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance">CloudDatabaseInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.browse" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.ca_instance_type" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.ca_instance_type">ca_instance_type</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.restore" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.restore">restore</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.instances.cloudapps.amazon_instance.AmazonDynamoDBInstance"><code class="flex name class">
<span>class <span class="ident">AmazonDynamoDBInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of the Amazon DynamoDB</p>
<p>Initializes the object of the CloudDatabaseInstance class</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/amazon_instance.py#L480-L561" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AmazonDynamoDBInstance(CloudDatabaseInstance):
    &#34;&#34;&#34;Class for representing an Instance of the Amazon DynamoDB&#34;&#34;&#34;

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

             Args:
                kwargs   (list)  --  list of options need to be set for restore

                For DynamoDB Instance Cluster Restore following are the required parameters
                        destination : &#34;&#34;,
                        source : &#34;&#34;,
                        options = {
                            &#39;paths&#39;:  (list of strings)
                            &#39;table_map&#39;: (list of dicts)
                            &#39;adjust_write_capacity&#39;: (int)
                            &#39;destination_client&#39;: (string))
                            &#39;destination_instance&#39;: string
                    }

                    }
                Example:
                        destination : &#34;&#34;,
                        source : &#34;&#34;,
                        options :   {
                                    &#39;paths&#39;: [&#39;/us-east-1/table_1&#39;],
                                    &#39;table_map&#39;: [{
                                    &#39;srcTable&#39;:{&#39;name&#39;: &#39;table_1&#39;, &#39;region&#39;:&#39;us-east-1&#39;},
                                    &#39;destTable&#39;:{&#39;name&#39;: &#39;table_2&#39;, &#39;region&#39;: &#39;us-east-2&#39;}
                                    }]
                                    &#39;adjust_write_capacity&#39;: 100,
                                    &#39;destination_client&#39;: &#39;client1&#39;,
                                    &#39;destination_instance&#39;: &#39;DynamoDB&#39;
                                    }
            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        restore_json = super(
            AmazonDynamoDBInstance,
            self)._restore_json(**kwargs)

        restore_options = {}
        if kwargs.get(&#39;options&#39;):
            restore_options = kwargs[&#39;options&#39;]
            for key in kwargs:
                if not key == &#39;options&#39;:
                    restore_options[key] = kwargs[key]
        else:
            restore_options.update(kwargs)

        source_backupset_id = int(self.backupsets.all_backupsets[&#39;defaultbackupset&#39;][&#39;id&#39;])
        dynamodb_restore_option = {
            &#34;dynamoDbRestoreOptions&#34;: {
                &#39;tempWriteThroughput&#39;: restore_options.get(&#39;adjust_write_capacity&#39;, &#39;&#39;),
                &#39;overwrite&#39;: restore_options.get(&#39;overwrite&#39;, False),
                &#39;destinationTableList&#39;: restore_options.get(&#39;table_map&#39;, [])
            }
        }
        destination_restore_json = (
            {
                &#34;noOfStreams&#34;: restore_options.get(&#34;number_of_streams&#34;, 2),
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: restore_options.get(&#34;destination_client&#34;, &#34;&#34;)
                },
                &#34;destinationInstance&#34;: {
                    &#34;clientName&#34;: restore_options.get(&#34;destination_client&#34;, &#34;&#34;),
                    &#34;instanceName&#34;: restore_options.get(&#34;destination_instance&#34;, &#34;&#34;),
                    &#34;appName&#34;: self._instance[&#39;appName&#39;]
                }

            })
        restore_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetId&#39;] = source_backupset_id
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#34;restoreOptions&#34;][&#34;destination&#34;] = destination_restore_json
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;] = dynamodb_restore_option
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;fileOption&#39;][&#39;sourceItem&#39;] = restore_options.get(&#34;paths&#34;, &#34;&#34;)
        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;][&#39;instanceType&#39;] = 22

        return restore_json</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance">CloudDatabaseInstance</a></li>
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance">CloudDatabaseInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.browse" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.ca_instance_type" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.ca_instance_type">ca_instance_type</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.restore" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.restore">restore</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.instances.cloudapps.amazon_instance.AmazonRDSInstance"><code class="flex name class">
<span>class <span class="ident">AmazonRDSInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of Amazon RDS</p>
<p>Initializes the object of the AmazonRDSInstance class</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/amazon_instance.py#L78-L210" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AmazonRDSInstance(CloudDatabaseInstance):
    &#34;&#34;&#34;Class for representing an Instance of Amazon RDS&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the AmazonRDSInstance class

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class

        &#34;&#34;&#34;
        super(
            AmazonRDSInstance,
            self).__init__(
                agent_object,
                instance_name,
                instance_id)

        self._browse_url = self._services[&#39;CLOUD_DATABASE_BROWSE&#39;]

    def _process_browse_response(self, flag, response):
        &#34;&#34;&#34; Process browse request response

            Args:

                flag -- indicates whether the rest API request is successful

                response -- response returned if the request was successful.

            Returns:

                dict    - The snapshot list JSON response received from the browse request

                Exception - If the browse request failed
        &#34;&#34;&#34;
        if flag:
            if response.json() and &#39;snapList&#39; in response.json():
                snapshot_list = response.json()[&#39;snapList&#39;]
            else:
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#34;Incorrect response from browse.\nResponse : {0}&#34;.format(
                        response.json()))
        else:
            o_str = &#39;Failed to browse content of this instance backups.\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(response))
        return snapshot_list

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

             Args:
                kwargs   (list)  --  list of options need to be set for restore

                Ex: For RDS Instance Cluster Restore following are the possible options
                    {
                        destination : &#39;instance/cluster&#39;,
                        source : &#39;snapshot&#39;,
                        options :   {
                                        &#39;archFileId&#39;: 123
                                        &#39;isMultiAZ&#39; : true,
                                        &#39;publicallyAccess&#39; : true,
                                        &#39;copyTagsToSnapshot&#39; : false,
                                        &#39;enableDeletionProtection&#39;: false,
                                        &#39;targetParameterGroupName&#39;: &#39;param&#39;,
                                        &#39;targetSubnetGroup&#39;: &#39;subnet&#39;,
                                        &#39;targetDBInstanceClass&#39;: &#39;dc-large-8&#39;,
                                        &#39;targetPort&#39;: 2990
                                    }
                    }

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        restore_json = super(
            AmazonRDSInstance,
            self)._restore_json(**kwargs)

        restore_options = {}
        if kwargs.get(&#34;restore_options&#34;):
            restore_options = kwargs[&#34;restore_options&#34;]
            for key in kwargs:
                if not key == &#34;restore_options&#34;:
                    restore_options[key] = kwargs[key]
        else:
            restore_options.update(kwargs)

        # Populate Redshift restore options
        rds_restore_json = {
            &#34;rdsRestoreOptions&#34;: {
                &#34;sourceSnap&#34;: {
                    &#34;snapShotName&#34;: restore_options[&#39;source&#39;]
                },
                &#34;targetDbName&#34;: restore_options[&#39;destination&#39;]
            }
        }

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;isMultiAZ&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;isMultiAZ&#39;, False)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;publicallyAccess&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;publicallyAccess&#39;, True)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;copyTagsToSnapshot&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;copyTagsToSnapshot&#39;, False)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;enableDeletionProtection&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;enableDeletionProtection&#39;, False)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;targetParameterGroupName&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetParameterGroupName&#39;, &#39;&#39;)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;targetSecurityGroupValue&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetSubnetGroup&#39;, &#39;&#39;)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;targetDBInstanceClass&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetDBInstanceClass&#39;, &#39;&#39;)

        rds_restore_json[&#39;rdsRestoreOptions&#39;][&#39;targetPort&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetPort&#39;, 0)

        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = \
            rds_restore_json

        return restore_json</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance">CloudDatabaseInstance</a></li>
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance">CloudDatabaseInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.browse" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.ca_instance_type" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.ca_instance_type">ca_instance_type</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.restore" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.restore">restore</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.instances.cloudapps.amazon_instance.AmazonRedshiftInstance"><code class="flex name class">
<span>class <span class="ident">AmazonRedshiftInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of the Amazon Redshift</p>
<p>Initializes the object of the AmazonRedshiftInstance class</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/amazon_instance.py#L213-L350" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AmazonRedshiftInstance(CloudDatabaseInstance):
    &#34;&#34;&#34;Class for representing an Instance of the Amazon Redshift&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the AmazonRedshiftInstance class

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class

        &#34;&#34;&#34;
        super(
            AmazonRedshiftInstance,
            self).__init__(
                agent_object,
                instance_name,
                instance_id)

        self._browse_url = self._services[&#39;CLOUD_DATABASE_BROWSE&#39;]

    def _process_browse_response(self, flag, response):
        &#34;&#34;&#34; Process browse request response

            Args:

                flag -- indicates whether the rest API request is successful

                response -- response returned if the request was successful.

            Returns:

                dict    - The snapshot list JSON response received from the browse request

                Exception - If the browse request failed
        &#34;&#34;&#34;
        if flag:
            if response.json() and &#39;snapList&#39; in response.json():
                snapshot_list = response.json()[&#39;snapList&#39;]
            else:
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#34;Incorrect response from browse.\nResponse : {0}&#34;.format(
                        response.json()))
        else:
            o_str = &#39;Failed to browse content of this instance backups.\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str.format(response))
        return snapshot_list

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

             Args:
                kwargs   (list)  --  list of options need to be set for restore

                Ex: For Redshift Instance Cluster Restore following are the possible options
                    {
                        destination : &#39;cluster&#39;,
                        source : &#39;snapshot&#39;,
                        options :   {
                                        &#39;allowVersionUpgrade&#39; : true,
                                        &#39;publicallyAccessible&#39; : true,
                                        &#39;restoreTags&#39; : false,
                                        &#39;enableDeletionProtection&#39;: false,
                                        &#39;availabilityZone&#39;: &#39;us-east-2a&#39;,
                                        &#39;targetParameterGroup&#39;: &#39;param&#39;,
                                        &#39;targetSubnetGroup&#39;: &#39;subnet&#39;,
                                        &#39;nodeType&#39;: &#39;dc-large-8&#39;,
                                        &#39;targetPort&#39;: 2990,
                                        &#39;numberOfNodes&#39;: 1
                                    }
                    }

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        restore_json = super(
            AmazonRedshiftInstance,
            self)._restore_json(**kwargs)

        restore_options = {}
        if kwargs.get(&#34;restore_options&#34;):
            restore_options = kwargs[&#34;restore_options&#34;]
            for key in kwargs:
                if not key == &#34;restore_options&#34;:
                    restore_options[key] = kwargs[key]
        else:
            restore_options.update(kwargs)

        # Populate Redshift restore options
        redshift_restore_json = {
            &#34;redshiftRestoreOption&#34;: {
                &#34;targetInstanceId&#34;: restore_options[&#39;destination&#39;],
                &#34;restoreSnapshotId&#34;: restore_options[&#39;source&#39;]
            }
        }

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;allowVersionUpgrade&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;allowVersionUpgrade&#39;, True)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;publicallyAccessible&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;publicallyAccessible&#39;, True)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;restoreTags&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;restoreTags&#39;, False)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;enableDeletionProtection&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;enableDeletionProtection&#39;, False)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;availabilityZone&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;availabilityZone&#39;, &#39;&#39;)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;targetParameterGroupName&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetParameterGroupName&#39;, &#39;&#39;)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;targetSubnetGroup&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetSubnetGroup&#39;, &#39;&#39;)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;nodeType&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;nodeType&#39;, &#39;&#39;)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;targetPort&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;targetPort&#39;, 0)

        redshift_restore_json[&#39;redshiftRestoreOption&#39;][&#39;numberOfNodes&#39;] = \
            restore_options.get(&#39;options&#39;, {}).get(&#39;numberOfNodes&#39;, 1)

        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = \
            redshift_restore_json

        return restore_json</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance">CloudDatabaseInstance</a></li>
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance">CloudDatabaseInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.browse" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.ca_instance_type" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.ca_instance_type">ca_instance_type</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.restore" href="cloud_database_instance.html#cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.restore">restore</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_database_instance.CloudDatabaseInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances.cloudapps" href="index.html">cvpysdk.instances.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.cloudapps.amazon_instance.AmazonDocumentDBInstance" href="#cvpysdk.instances.cloudapps.amazon_instance.AmazonDocumentDBInstance">AmazonDocumentDBInstance</a></code></h4>
</li>
<li>
<h4><code><a title="cvpysdk.instances.cloudapps.amazon_instance.AmazonDynamoDBInstance" href="#cvpysdk.instances.cloudapps.amazon_instance.AmazonDynamoDBInstance">AmazonDynamoDBInstance</a></code></h4>
</li>
<li>
<h4><code><a title="cvpysdk.instances.cloudapps.amazon_instance.AmazonRDSInstance" href="#cvpysdk.instances.cloudapps.amazon_instance.AmazonRDSInstance">AmazonRDSInstance</a></code></h4>
</li>
<li>
<h4><code><a title="cvpysdk.instances.cloudapps.amazon_instance.AmazonRedshiftInstance" href="#cvpysdk.instances.cloudapps.amazon_instance.AmazonRedshiftInstance">AmazonRedshiftInstance</a></code></h4>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>