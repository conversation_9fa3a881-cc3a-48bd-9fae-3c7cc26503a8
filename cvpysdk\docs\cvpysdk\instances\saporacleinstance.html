<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.saporacleinstance API documentation</title>
<meta name="description" content="File for operating on a SAP Oracle Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.saporacleinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a SAP Oracle Instance.</p>
<p>SAPOracleInstance is the only class defined in this file.</p>
<p>SAPOracleInstance: Derived class from Instance Base class, representing a SAPOracle instance,
and to perform operations on that instance</p>
<h2 id="saporacleinstance">Saporacleinstance</h2>
<p><strong>init</strong>()
&ndash; Constructor for the class</p>
<p>oracle_home()
&ndash; Getter for $ORACLE_HOME of this instance</p>
<p>sapdata_home()
&ndash; Getter for $SAPDATA_HOME of this instance</p>
<p>sapexepath()
&ndash; Getter for $SAPEXE of this instance</p>
<p>os_user()
&ndash; Getter for OS user owning oracle software</p>
<p>cmd_sp()
&ndash; Getter for command line storage policy</p>
<p>log_sp()
&ndash; Getter for log storage policy</p>
<p>db_user()
&ndash; Getter for SYS database user name</p>
<p>saporacle_db_connectstring()
&ndash; Getter for getting oracle database connect string</p>
<p>saporacle_blocksize()
&ndash; Getter for getting blocksize value</p>
<p>saporacle_sapsecurestore()
&ndash; Getter for getting sapsecure store option</p>
<p>saporacle_archivelogbackupstreams() &ndash; Getter for getting archivelog backup streams</p>
<p>saporacle_instanceid()
&ndash; Getter for getting InstanceId</p>
<p>saporacle_snapbackup_enable()
&ndash; Getter for getting Snap backup enabled or not</p>
<p>saporacle_snapengine_name()
&ndash; Getter for getting snap enginename</p>
<p>_restore_request_json()
&ndash; returns the restore request json</p>
<p>_process_restore_response()
&ndash; processes response received for the Restore request</p>
<p>restore_in_place()
&ndash; runs the restore job for specified instance</p>
<p>restore_outof_place()
&ndash; runs the restore job for specified client and instance</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L1-L429" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a SAP Oracle Instance.

SAPOracleInstance is the only class defined in this file.

SAPOracleInstance: Derived class from Instance Base class, representing a SAPOracle instance,
                       and to perform operations on that instance

SAPOracleInstance:
    __init__()                          -- Constructor for the class


    oracle_home()                       -- Getter for $ORACLE_HOME of this instance

    sapdata_home()                      -- Getter for $SAPDATA_HOME of this instance

    sapexepath()                        -- Getter for $SAPEXE of this instance

     os_user()                          -- Getter for OS user owning oracle software

    cmd_sp()                            -- Getter for command line storage policy

    log_sp()                            -- Getter for log storage policy

    db_user()                           -- Getter for SYS database user name

    saporacle_db_connectstring()        -- Getter for getting oracle database connect string

    saporacle_blocksize()               -- Getter for getting blocksize value

    saporacle_sapsecurestore()          -- Getter for getting sapsecure store option

    saporacle_archivelogbackupstreams() -- Getter for getting archivelog backup streams

    saporacle_instanceid()              -- Getter for getting InstanceId
    
    saporacle_snapbackup_enable()       -- Getter for getting Snap backup enabled or not
    
    saporacle_snapengine_name()         -- Getter for getting snap enginename

    _restore_request_json()             -- returns the restore request json

    _process_restore_response()         -- processes response received for the Restore request

    restore_in_place()                  -- runs the restore job for specified instance

    restore_outof_place()               -- runs the restore job for specified client and instance

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from ..agent import Agent
from ..instance import Instance
from ..client import Client
from ..exception import SDKException


class SAPOracleInstance(Instance):
    &#34;&#34;&#34;Derived class from Instance Base class, representing a SAPOracle instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            agent_object    -- instance of the Agent class
            instance_name   -- name of the instance
            instance_id     --  id of the instance

        &#34;&#34;&#34;
        super(SAPOracleInstance, self).__init__(agent_object, instance_name, instance_id)
        self._instanceprop = {}  # variable to hold instance properties to be changed

    @property
    def oracle_home(self):
        &#34;&#34;&#34;
        getter for oracle home
        Returns:
            string - string of oracle_home
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleHome&#39;]

    @property
    def sapdata_home(self):
        &#34;&#34;&#34;
        getter for sapdata home
        Returns:
            string - string of sapdata_home
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;sapDataPath&#39;]

    @property
    def sapexepath(self):
        &#34;&#34;&#34;
        getter for sapexepath
        Returns:
            string - string of sapexepath
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;sapExeFolder&#39;]

    @property
    def os_user(self):
        &#34;&#34;&#34;
        Getter for oracle software owner
        Returns:
            string - string of oracle software owner
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleUser&#39;][&#39;userName&#39;]

    @property
    def cmd_sp(self):
        &#34;&#34;&#34;
        Getter for Command Line storage policy
        Returns:
            string - string for command line storage policy
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleStorageDevice&#39;][
            &#39;commandLineStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def log_sp(self):
        &#34;&#34;&#34;
        Oracle Instance&#39;s Log Storage Poplicy
        Returns:
            string  -- string containing log storage policy
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleStorageDevice&#39;][
            &#39;logBackupStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def saporacle_db_user(self):
        &#34;&#34;&#34;
        Returns: Oracle database user for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;sqlConnect&#39;][&#39;userName&#39;]

    @property
    def saporacle_db_connectstring(self):
        &#34;&#34;&#34;
        Returns: Oracle database connect string for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;sqlConnect&#39;][&#39;domainName&#39;]

    @property
    def saporacle_blocksize(self):
        &#34;&#34;&#34;
        Returns: blocksize for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;blockSize&#39;]

    @property
    def saporacle_sapsecurestore(self):
        &#34;&#34;&#34;
        Returns: sapsecurestore option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;useSAPSecureStore&#39;]

    @property
    def saporacle_archivelogbackupstreams(self):
        &#34;&#34;&#34;
        Returns: archivelogbackupstreams option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;numberOfArchiveLogBackupStreams&#39;]

    @property
    def saporacle_instanceid(self):
        &#34;&#34;&#34;
        Returns: saporacle_instanceid option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;instance&#39;][&#39;instanceId&#39;]
    
    @property
    def saporacle_snapbackup_enable(self):
        &#34;&#34;&#34;
        Returns: saporacle_snapbackup_enable option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;snapProtectInfo&#39;][&#39;isSnapBackupEnabled&#39;]
    
    @property
    def saporacle_snapengine_name(self):
        &#34;&#34;&#34;
        Returns: saporacle_snapengine_name option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;snapProtectInfo&#39;][&#39;snapSelectedEngine&#39;][&#39;snapShotEngineName&#39;]

    def _restore_saporacle_request_json(self, value):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

        &#34;&#34;&#34;
        if self._restore_association is None:
            self._restore_association = self._instance
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._restore_association],
                &#34;task&#34;: self._task,
                &#34;subTasks&#34;: [{
                    &#34;subTask&#34;: self._restore_sub_task,
                    &#34;options&#34;: {
                        &#34;restoreOptions&#34;: {
                            &#34;oracleOpt&#34;: {
                                &#34;noCatalog&#34;: value.get(&#34;noCatalog&#34;, True),
                                &#34;backupValidationOnly&#34;: value.get(&#34;backupValidationOnly&#34;, False),
                                &#34;restoreData&#34;: value.get(&#34;restoreData&#34;, True),
                                &#34;archiveLog&#34;: value.get(&#34;archiveLog&#34;, True),
                                &#34;recover&#34;: value.get(&#34;recover&#34;, True),
                                &#34;switchDatabaseMode&#34;: value.get(&#34;switchDatabaseMode&#34;, True),
                                &#34;restoreStream&#34;: value.get(&#34;restoreStream&#34;, 1),
                                &#34;restoreControlFile&#34;: value.get(&#34;restoreControlFile&#34;, True),
                                &#34;partialRestore&#34;: value.get(&#34;partialRestore&#34;, False),
                                &#34;openDatabase&#34;: value.get(&#34;openDatabase&#34;, True),
                                &#34;resetLogs&#34;: value.get(&#34;resetLogs&#34;, 1),
                                &#34;restoreTablespace&#34;: value.get(&#34;restoreTablespace&#34;, False),
                                &#34;databaseCopy&#34;: value.get(&#34;databaseCopy&#34;, False),
                                &#34;archiveLogBy&#34;: value.get(&#34;archiveLogBy&#34;, &#39;default&#39;),
                                &#34;recoverTime&#34;:{
                                    &#34;time&#34;:value.get(&#34;point_in_time&#34;, 0)},
                            },
                            
                            &#34;destination&#34;: {
                                &#34;destinationInstance&#34;: {
                                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;),
                                    &#34;appName&#34;: self._agent_object.agent_name,
                                    &#34;instanceName&#34;: value.get(&#34;destination_instance&#34;)
                                },
                                &#34;destClient&#34;: {
                                    &#34;clientName&#34;:value.get(&#34;destination_client&#34;)
                                }
                            },
                            &#34;fileOption&#34;: {
                                &#34;sourceItem&#34;: value.get(&#34;sourceItem&#34;, [&#34;/+BROWSE+&#34;])
                            },
                            &#34;browseOption&#34;: {
                                &#34;backupset&#34;: {
                                    &#34;clientName&#34;: self._agent_object._client_object.client_name
                                },
                                &#34;mediaOption&#34;:{
                                     &#34;copyPrecedence&#34;: {
                                             &#34;copyPrecedenceApplicable&#34;: value.get(&#34;copyPrecedenceApplicable&#34;, False),
                                             &#34;copyPrecedence&#34;:value.get(&#34;copyPrecedence&#34;, 0)}}
                            }
                        }
                    }
                }]
            }
        }
        return request_json

    def restore_in_place(
            self,
            destination_client=None,
            destination_instance=None,
            sap_options=None):
        &#34;&#34;&#34;perform inplace restore and recover  of sap oracle database
         Args:

            destination_client        (str)         --  destination client name where saporacle
                                                          client package exists if this value
                                                          not provided,it will automatically
                                                          use source backup client
            destination_instance        (str)       --  destination instance name where saporacle
                                                        client package exists if this value not
                                                         provided,it will automatically use
                                                          source backup instance
            sap_options                (dict)

                backupset_name         (str)        --  backupset name of the instance to be
                                                            restored. If the instance is a single
                                                            DB instance then the backupset name is
                                                            ``default``.
                    default: default

                restoreData               (bool)   --  RestoreData  if true mean restore data
                                                          is selected.
                                                        true - restore data selected
                                                        false - restore data unselected

                    default:true

                streams                  (int)      :  no of streams to use for restore
                    default:2

                copyPrecedence          (int)      :  copy number to use for restore
                    default:0

                archiveLog               (bool)     :  Restore archive log
                                                        true - restore archive log selected
                                                        false - restore archive log unselected
                     default: True

                recover                  (bool)     :  recover database
                                                        true - recover database selected
                                                        false - recover database unselected
                     default: True

                switchDatabaseMode       (bool)     :  switchDatabaseMode option
                                                        true - switchDatabaseMode selected
                                                        false - switchDatabaseMode unselected
                     default: True

                restoreControlFile       (bool)     :  restoreControlFile option
                                                        true - restoreControlFile selected
                                                        false - restoreControlFile unselected
                     default: True

                partialRestore       (bool)         :  partialRestore option
                                                        true - partialRestore selected
                                                        false - partialRestore unselected
                     default: False

                openDatabase       (bool)           :  openDatabase option
                                                        true - openDatabase selected
                                                        false - openDatabase unselected
                     default: True

                resetLogs       (bool)              :  resetLogs option
                                                        true - resetLogs selected
                                                        false - resetLogs unselected
                     default: True

                point_in_time            (str)      :  date to use for restore and recover  database
                                                       format: dd/MM/YYYY
                                                       gets content from 01/01/1970 if not specified
                    default: 0

                backupValidationOnly       (bool)   :  backupValidationOnly option
                                                        true - backupValidationOnly selected
                                                        false - backupValidationOnly unselected
                     default: False

                 restoreTablespace       (bool)     :  restoreTablespace option
                                                        true - restoreTablespace selected
                                                        false - restoreTablespace unselected
                     default: False

                noCatalog       (bool)              :  noCatalog option
                                                        true - noCatalog selected
                                                        false - noCatalog unselected
                     default: True

                sourceItem       (list)              :  sourceItem means browse options for
                                                         sap oracle restores
                                                        /+BROWSE+ - means both data and logs
                                                        are selected
                                                        /+BROWSE+DATA -data only selected
                                                        /+BROWSE+LOG -log only selected
                     default: /+BROWSE+
                databaseCopy       (bool)            :  databaseCopy option
                                                        true - databaseCopy selected
                                                        false - databaseCopy unselected
                     default: False

                archiveLogBy       (str)            :  for restore archive log options,
                                                        default means restore archivelogall
                                                        is selected

                     default: default

         Raises:
                SDKException:

                    if failed to browse content

                    if response is empty

                    if response is not success

                    if destination client does not exist on commcell

                    if destination instance does not exist on commcell
        &#34;&#34;&#34;

        if sap_options is None:
            sap_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._agent_object._client_object.client_name
            

        if isinstance(destination_client, Client):
            destination_client = destination_client
            
        elif isinstance(destination_client, str):
            destination_client = Client(self._commcell_object, destination_client)
            #print(destination_client)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        dest_agent = Agent(destination_client, &#39;sap for oracle&#39;,&#39;61&#39;)

        # check if instance name is correct
        if destination_instance is None:
            destination_instance = self.instance_name

        if isinstance(destination_instance, Instance):
            destination_instance = destination_instance
        elif isinstance(destination_instance, str):
            destination_instance = dest_agent.instances.get(destination_instance)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        sap_options[&#34;destination_client&#34;] = destination_client.client_name
        sap_options[&#34;destination_instance&#34;] = destination_instance.instance_name
        #sap_options[&#34;copyPrecedence&#34;] = sap_options.get(&#34;copyPrecedence&#34;, &#34;0&#34;)

        # prepare and execute
        request_json = self._restore_saporacle_request_json(sap_options)
        return self._process_restore_response(request_json)
    
    </code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance"><code class="flex name class">
<span>class <span class="ident">SAPOracleInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Instance Base class, representing a SAPOracle instance,
and to perform operations on that Instance.</p>
<p>Constructor for the class</p>
<h2 id="args">Args</h2>
<p>agent_object
&ndash; instance of the Agent class
instance_name
&ndash; name of the instance
instance_id
&ndash;
id of the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L77-L427" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SAPOracleInstance(Instance):
    &#34;&#34;&#34;Derived class from Instance Base class, representing a SAPOracle instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            agent_object    -- instance of the Agent class
            instance_name   -- name of the instance
            instance_id     --  id of the instance

        &#34;&#34;&#34;
        super(SAPOracleInstance, self).__init__(agent_object, instance_name, instance_id)
        self._instanceprop = {}  # variable to hold instance properties to be changed

    @property
    def oracle_home(self):
        &#34;&#34;&#34;
        getter for oracle home
        Returns:
            string - string of oracle_home
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleHome&#39;]

    @property
    def sapdata_home(self):
        &#34;&#34;&#34;
        getter for sapdata home
        Returns:
            string - string of sapdata_home
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;sapDataPath&#39;]

    @property
    def sapexepath(self):
        &#34;&#34;&#34;
        getter for sapexepath
        Returns:
            string - string of sapexepath
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;sapExeFolder&#39;]

    @property
    def os_user(self):
        &#34;&#34;&#34;
        Getter for oracle software owner
        Returns:
            string - string of oracle software owner
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleUser&#39;][&#39;userName&#39;]

    @property
    def cmd_sp(self):
        &#34;&#34;&#34;
        Getter for Command Line storage policy
        Returns:
            string - string for command line storage policy
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleStorageDevice&#39;][
            &#39;commandLineStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def log_sp(self):
        &#34;&#34;&#34;
        Oracle Instance&#39;s Log Storage Poplicy
        Returns:
            string  -- string containing log storage policy
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleStorageDevice&#39;][
            &#39;logBackupStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def saporacle_db_user(self):
        &#34;&#34;&#34;
        Returns: Oracle database user for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;sqlConnect&#39;][&#39;userName&#39;]

    @property
    def saporacle_db_connectstring(self):
        &#34;&#34;&#34;
        Returns: Oracle database connect string for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;sqlConnect&#39;][&#39;domainName&#39;]

    @property
    def saporacle_blocksize(self):
        &#34;&#34;&#34;
        Returns: blocksize for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;blockSize&#39;]

    @property
    def saporacle_sapsecurestore(self):
        &#34;&#34;&#34;
        Returns: sapsecurestore option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;useSAPSecureStore&#39;]

    @property
    def saporacle_archivelogbackupstreams(self):
        &#34;&#34;&#34;
        Returns: archivelogbackupstreams option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;numberOfArchiveLogBackupStreams&#39;]

    @property
    def saporacle_instanceid(self):
        &#34;&#34;&#34;
        Returns: saporacle_instanceid option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;instance&#39;][&#39;instanceId&#39;]
    
    @property
    def saporacle_snapbackup_enable(self):
        &#34;&#34;&#34;
        Returns: saporacle_snapbackup_enable option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;snapProtectInfo&#39;][&#39;isSnapBackupEnabled&#39;]
    
    @property
    def saporacle_snapengine_name(self):
        &#34;&#34;&#34;
        Returns: saporacle_snapengine_name option for the instance
        &#34;&#34;&#34;
        return self._properties[&#39;sapOracleInstance&#39;][&#39;snapProtectInfo&#39;][&#39;snapSelectedEngine&#39;][&#39;snapShotEngineName&#39;]

    def _restore_saporacle_request_json(self, value):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

        &#34;&#34;&#34;
        if self._restore_association is None:
            self._restore_association = self._instance
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [self._restore_association],
                &#34;task&#34;: self._task,
                &#34;subTasks&#34;: [{
                    &#34;subTask&#34;: self._restore_sub_task,
                    &#34;options&#34;: {
                        &#34;restoreOptions&#34;: {
                            &#34;oracleOpt&#34;: {
                                &#34;noCatalog&#34;: value.get(&#34;noCatalog&#34;, True),
                                &#34;backupValidationOnly&#34;: value.get(&#34;backupValidationOnly&#34;, False),
                                &#34;restoreData&#34;: value.get(&#34;restoreData&#34;, True),
                                &#34;archiveLog&#34;: value.get(&#34;archiveLog&#34;, True),
                                &#34;recover&#34;: value.get(&#34;recover&#34;, True),
                                &#34;switchDatabaseMode&#34;: value.get(&#34;switchDatabaseMode&#34;, True),
                                &#34;restoreStream&#34;: value.get(&#34;restoreStream&#34;, 1),
                                &#34;restoreControlFile&#34;: value.get(&#34;restoreControlFile&#34;, True),
                                &#34;partialRestore&#34;: value.get(&#34;partialRestore&#34;, False),
                                &#34;openDatabase&#34;: value.get(&#34;openDatabase&#34;, True),
                                &#34;resetLogs&#34;: value.get(&#34;resetLogs&#34;, 1),
                                &#34;restoreTablespace&#34;: value.get(&#34;restoreTablespace&#34;, False),
                                &#34;databaseCopy&#34;: value.get(&#34;databaseCopy&#34;, False),
                                &#34;archiveLogBy&#34;: value.get(&#34;archiveLogBy&#34;, &#39;default&#39;),
                                &#34;recoverTime&#34;:{
                                    &#34;time&#34;:value.get(&#34;point_in_time&#34;, 0)},
                            },
                            
                            &#34;destination&#34;: {
                                &#34;destinationInstance&#34;: {
                                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;),
                                    &#34;appName&#34;: self._agent_object.agent_name,
                                    &#34;instanceName&#34;: value.get(&#34;destination_instance&#34;)
                                },
                                &#34;destClient&#34;: {
                                    &#34;clientName&#34;:value.get(&#34;destination_client&#34;)
                                }
                            },
                            &#34;fileOption&#34;: {
                                &#34;sourceItem&#34;: value.get(&#34;sourceItem&#34;, [&#34;/+BROWSE+&#34;])
                            },
                            &#34;browseOption&#34;: {
                                &#34;backupset&#34;: {
                                    &#34;clientName&#34;: self._agent_object._client_object.client_name
                                },
                                &#34;mediaOption&#34;:{
                                     &#34;copyPrecedence&#34;: {
                                             &#34;copyPrecedenceApplicable&#34;: value.get(&#34;copyPrecedenceApplicable&#34;, False),
                                             &#34;copyPrecedence&#34;:value.get(&#34;copyPrecedence&#34;, 0)}}
                            }
                        }
                    }
                }]
            }
        }
        return request_json

    def restore_in_place(
            self,
            destination_client=None,
            destination_instance=None,
            sap_options=None):
        &#34;&#34;&#34;perform inplace restore and recover  of sap oracle database
         Args:

            destination_client        (str)         --  destination client name where saporacle
                                                          client package exists if this value
                                                          not provided,it will automatically
                                                          use source backup client
            destination_instance        (str)       --  destination instance name where saporacle
                                                        client package exists if this value not
                                                         provided,it will automatically use
                                                          source backup instance
            sap_options                (dict)

                backupset_name         (str)        --  backupset name of the instance to be
                                                            restored. If the instance is a single
                                                            DB instance then the backupset name is
                                                            ``default``.
                    default: default

                restoreData               (bool)   --  RestoreData  if true mean restore data
                                                          is selected.
                                                        true - restore data selected
                                                        false - restore data unselected

                    default:true

                streams                  (int)      :  no of streams to use for restore
                    default:2

                copyPrecedence          (int)      :  copy number to use for restore
                    default:0

                archiveLog               (bool)     :  Restore archive log
                                                        true - restore archive log selected
                                                        false - restore archive log unselected
                     default: True

                recover                  (bool)     :  recover database
                                                        true - recover database selected
                                                        false - recover database unselected
                     default: True

                switchDatabaseMode       (bool)     :  switchDatabaseMode option
                                                        true - switchDatabaseMode selected
                                                        false - switchDatabaseMode unselected
                     default: True

                restoreControlFile       (bool)     :  restoreControlFile option
                                                        true - restoreControlFile selected
                                                        false - restoreControlFile unselected
                     default: True

                partialRestore       (bool)         :  partialRestore option
                                                        true - partialRestore selected
                                                        false - partialRestore unselected
                     default: False

                openDatabase       (bool)           :  openDatabase option
                                                        true - openDatabase selected
                                                        false - openDatabase unselected
                     default: True

                resetLogs       (bool)              :  resetLogs option
                                                        true - resetLogs selected
                                                        false - resetLogs unselected
                     default: True

                point_in_time            (str)      :  date to use for restore and recover  database
                                                       format: dd/MM/YYYY
                                                       gets content from 01/01/1970 if not specified
                    default: 0

                backupValidationOnly       (bool)   :  backupValidationOnly option
                                                        true - backupValidationOnly selected
                                                        false - backupValidationOnly unselected
                     default: False

                 restoreTablespace       (bool)     :  restoreTablespace option
                                                        true - restoreTablespace selected
                                                        false - restoreTablespace unselected
                     default: False

                noCatalog       (bool)              :  noCatalog option
                                                        true - noCatalog selected
                                                        false - noCatalog unselected
                     default: True

                sourceItem       (list)              :  sourceItem means browse options for
                                                         sap oracle restores
                                                        /+BROWSE+ - means both data and logs
                                                        are selected
                                                        /+BROWSE+DATA -data only selected
                                                        /+BROWSE+LOG -log only selected
                     default: /+BROWSE+
                databaseCopy       (bool)            :  databaseCopy option
                                                        true - databaseCopy selected
                                                        false - databaseCopy unselected
                     default: False

                archiveLogBy       (str)            :  for restore archive log options,
                                                        default means restore archivelogall
                                                        is selected

                     default: default

         Raises:
                SDKException:

                    if failed to browse content

                    if response is empty

                    if response is not success

                    if destination client does not exist on commcell

                    if destination instance does not exist on commcell
        &#34;&#34;&#34;

        if sap_options is None:
            sap_options = {}

        # check if client name is correct
        if destination_client is None:
            destination_client = self._agent_object._client_object.client_name
            

        if isinstance(destination_client, Client):
            destination_client = destination_client
            
        elif isinstance(destination_client, str):
            destination_client = Client(self._commcell_object, destination_client)
            #print(destination_client)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        dest_agent = Agent(destination_client, &#39;sap for oracle&#39;,&#39;61&#39;)

        # check if instance name is correct
        if destination_instance is None:
            destination_instance = self.instance_name

        if isinstance(destination_instance, Instance):
            destination_instance = destination_instance
        elif isinstance(destination_instance, str):
            destination_instance = dest_agent.instances.get(destination_instance)
        else:
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        sap_options[&#34;destination_client&#34;] = destination_client.client_name
        sap_options[&#34;destination_instance&#34;] = destination_instance.instance_name
        #sap_options[&#34;copyPrecedence&#34;] = sap_options.get(&#34;copyPrecedence&#34;, &#34;0&#34;)

        # prepare and execute
        request_json = self._restore_saporacle_request_json(sap_options)
        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.cmd_sp"><code class="name">var <span class="ident">cmd_sp</span></code></dt>
<dd>
<div class="desc"><p>Getter for Command Line storage policy</p>
<h2 id="returns">Returns</h2>
<p>string - string for command line storage policy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L130-L138" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cmd_sp(self):
    &#34;&#34;&#34;
    Getter for Command Line storage policy
    Returns:
        string - string for command line storage policy
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleStorageDevice&#39;][
        &#39;commandLineStoragePolicy&#39;][&#39;storagePolicyName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.log_sp"><code class="name">var <span class="ident">log_sp</span></code></dt>
<dd>
<div class="desc"><p>Oracle Instance's Log Storage Poplicy</p>
<h2 id="returns">Returns</h2>
<p>string
&ndash; string containing log storage policy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L140-L148" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def log_sp(self):
    &#34;&#34;&#34;
    Oracle Instance&#39;s Log Storage Poplicy
    Returns:
        string  -- string containing log storage policy
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleStorageDevice&#39;][
        &#39;logBackupStoragePolicy&#39;][&#39;storagePolicyName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.oracle_home"><code class="name">var <span class="ident">oracle_home</span></code></dt>
<dd>
<div class="desc"><p>getter for oracle home</p>
<h2 id="returns">Returns</h2>
<p>string - string of oracle_home</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L94-L101" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def oracle_home(self):
    &#34;&#34;&#34;
    getter for oracle home
    Returns:
        string - string of oracle_home
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleHome&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.os_user"><code class="name">var <span class="ident">os_user</span></code></dt>
<dd>
<div class="desc"><p>Getter for oracle software owner</p>
<h2 id="returns">Returns</h2>
<p>string - string of oracle software owner</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L121-L128" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def os_user(self):
    &#34;&#34;&#34;
    Getter for oracle software owner
    Returns:
        string - string of oracle software owner
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;oracleUser&#39;][&#39;userName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.sapdata_home"><code class="name">var <span class="ident">sapdata_home</span></code></dt>
<dd>
<div class="desc"><p>getter for sapdata home</p>
<h2 id="returns">Returns</h2>
<p>string - string of sapdata_home</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L103-L110" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sapdata_home(self):
    &#34;&#34;&#34;
    getter for sapdata home
    Returns:
        string - string of sapdata_home
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;sapDataPath&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.sapexepath"><code class="name">var <span class="ident">sapexepath</span></code></dt>
<dd>
<div class="desc"><p>getter for sapexepath</p>
<h2 id="returns">Returns</h2>
<p>string - string of sapexepath</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L112-L119" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sapexepath(self):
    &#34;&#34;&#34;
    getter for sapexepath
    Returns:
        string - string of sapexepath
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;sapExeFolder&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_archivelogbackupstreams"><code class="name">var <span class="ident">saporacle_archivelogbackupstreams</span></code></dt>
<dd>
<div class="desc"><p>Returns: archivelogbackupstreams option for the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L178-L183" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saporacle_archivelogbackupstreams(self):
    &#34;&#34;&#34;
    Returns: archivelogbackupstreams option for the instance
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;numberOfArchiveLogBackupStreams&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_blocksize"><code class="name">var <span class="ident">saporacle_blocksize</span></code></dt>
<dd>
<div class="desc"><p>Returns: blocksize for the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L164-L169" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saporacle_blocksize(self):
    &#34;&#34;&#34;
    Returns: blocksize for the instance
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;blockSize&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_db_connectstring"><code class="name">var <span class="ident">saporacle_db_connectstring</span></code></dt>
<dd>
<div class="desc"><p>Returns: Oracle database connect string for the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L157-L162" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saporacle_db_connectstring(self):
    &#34;&#34;&#34;
    Returns: Oracle database connect string for the instance
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;sqlConnect&#39;][&#39;domainName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_db_user"><code class="name">var <span class="ident">saporacle_db_user</span></code></dt>
<dd>
<div class="desc"><p>Returns: Oracle database user for the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L150-L155" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saporacle_db_user(self):
    &#34;&#34;&#34;
    Returns: Oracle database user for the instance
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;sqlConnect&#39;][&#39;userName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_instanceid"><code class="name">var <span class="ident">saporacle_instanceid</span></code></dt>
<dd>
<div class="desc"><p>Returns: saporacle_instanceid option for the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L185-L190" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saporacle_instanceid(self):
    &#34;&#34;&#34;
    Returns: saporacle_instanceid option for the instance
    &#34;&#34;&#34;
    return self._properties[&#39;instance&#39;][&#39;instanceId&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_sapsecurestore"><code class="name">var <span class="ident">saporacle_sapsecurestore</span></code></dt>
<dd>
<div class="desc"><p>Returns: sapsecurestore option for the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L171-L176" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saporacle_sapsecurestore(self):
    &#34;&#34;&#34;
    Returns: sapsecurestore option for the instance
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;useSAPSecureStore&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_snapbackup_enable"><code class="name">var <span class="ident">saporacle_snapbackup_enable</span></code></dt>
<dd>
<div class="desc"><p>Returns: saporacle_snapbackup_enable option for the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L192-L197" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saporacle_snapbackup_enable(self):
    &#34;&#34;&#34;
    Returns: saporacle_snapbackup_enable option for the instance
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;snapProtectInfo&#39;][&#39;isSnapBackupEnabled&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_snapengine_name"><code class="name">var <span class="ident">saporacle_snapengine_name</span></code></dt>
<dd>
<div class="desc"><p>Returns: saporacle_snapengine_name option for the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L199-L204" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saporacle_snapengine_name(self):
    &#34;&#34;&#34;
    Returns: saporacle_snapengine_name option for the instance
    &#34;&#34;&#34;
    return self._properties[&#39;sapOracleInstance&#39;][&#39;snapProtectInfo&#39;][&#39;snapSelectedEngine&#39;][&#39;snapShotEngineName&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.saporacleinstance.SAPOracleInstance.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, destination_client=None, destination_instance=None, sap_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>perform inplace restore and recover
of sap oracle database</p>
<h2 id="args">Args</h2>
<p>destination_client
(str)
&ndash;
destination client name where saporacle
client package exists if this value
not provided,it will automatically
use source backup client
destination_instance
(str)
&ndash;
destination instance name where saporacle
client package exists if this value not
provided,it will automatically use
source backup instance
sap_options
(dict)</p>
<pre><code>backupset_name         (str)        --  backupset name of the instance to be
                                            restored. If the instance is a single
                                            DB instance then the backupset name is
                                            &lt;code&gt;default&lt;/code&gt;.
    default: default

restoreData               (bool)   --  RestoreData  if true mean restore data
                                          is selected.
                                        true - restore data selected
                                        false - restore data unselected

    default:true

streams                  (int)      :  no of streams to use for restore
    default:2

copyPrecedence          (int)      :  copy number to use for restore
    default:0

archiveLog               (bool)     :  Restore archive log
                                        true - restore archive log selected
                                        false - restore archive log unselected
     default: True

recover                  (bool)     :  recover database
                                        true - recover database selected
                                        false - recover database unselected
     default: True

switchDatabaseMode       (bool)     :  switchDatabaseMode option
                                        true - switchDatabaseMode selected
                                        false - switchDatabaseMode unselected
     default: True

restoreControlFile       (bool)     :  restoreControlFile option
                                        true - restoreControlFile selected
                                        false - restoreControlFile unselected
     default: True

partialRestore       (bool)         :  partialRestore option
                                        true - partialRestore selected
                                        false - partialRestore unselected
     default: False

openDatabase       (bool)           :  openDatabase option
                                        true - openDatabase selected
                                        false - openDatabase unselected
     default: True

resetLogs       (bool)              :  resetLogs option
                                        true - resetLogs selected
                                        false - resetLogs unselected
     default: True

point_in_time            (str)      :  date to use for restore and recover  database
                                       format: dd/MM/YYYY
                                       gets content from 01/01/1970 if not specified
    default: 0

backupValidationOnly       (bool)   :  backupValidationOnly option
                                        true - backupValidationOnly selected
                                        false - backupValidationOnly unselected
     default: False

 restoreTablespace       (bool)     :  restoreTablespace option
                                        true - restoreTablespace selected
                                        false - restoreTablespace unselected
     default: False

noCatalog       (bool)              :  noCatalog option
                                        true - noCatalog selected
                                        false - noCatalog unselected
     default: True

sourceItem       (list)              :  sourceItem means browse options for
                                         sap oracle restores
                                        /+BROWSE+ - means both data and logs
                                        are selected
                                        /+BROWSE+DATA -data only selected
                                        /+BROWSE+LOG -log only selected
     default: /+BROWSE+
databaseCopy       (bool)            :  databaseCopy option
                                        true - databaseCopy selected
                                        false - databaseCopy unselected
     default: False

archiveLogBy       (str)            :  for restore archive log options,
                                        default means restore archivelogall
                                        is selected

     default: default
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to browse content

if response is empty

if response is not success

if destination client does not exist on commcell

if destination instance does not exist on commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/saporacleinstance.py#L268-L427" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        destination_client=None,
        destination_instance=None,
        sap_options=None):
    &#34;&#34;&#34;perform inplace restore and recover  of sap oracle database
     Args:

        destination_client        (str)         --  destination client name where saporacle
                                                      client package exists if this value
                                                      not provided,it will automatically
                                                      use source backup client
        destination_instance        (str)       --  destination instance name where saporacle
                                                    client package exists if this value not
                                                     provided,it will automatically use
                                                      source backup instance
        sap_options                (dict)

            backupset_name         (str)        --  backupset name of the instance to be
                                                        restored. If the instance is a single
                                                        DB instance then the backupset name is
                                                        ``default``.
                default: default

            restoreData               (bool)   --  RestoreData  if true mean restore data
                                                      is selected.
                                                    true - restore data selected
                                                    false - restore data unselected

                default:true

            streams                  (int)      :  no of streams to use for restore
                default:2

            copyPrecedence          (int)      :  copy number to use for restore
                default:0

            archiveLog               (bool)     :  Restore archive log
                                                    true - restore archive log selected
                                                    false - restore archive log unselected
                 default: True

            recover                  (bool)     :  recover database
                                                    true - recover database selected
                                                    false - recover database unselected
                 default: True

            switchDatabaseMode       (bool)     :  switchDatabaseMode option
                                                    true - switchDatabaseMode selected
                                                    false - switchDatabaseMode unselected
                 default: True

            restoreControlFile       (bool)     :  restoreControlFile option
                                                    true - restoreControlFile selected
                                                    false - restoreControlFile unselected
                 default: True

            partialRestore       (bool)         :  partialRestore option
                                                    true - partialRestore selected
                                                    false - partialRestore unselected
                 default: False

            openDatabase       (bool)           :  openDatabase option
                                                    true - openDatabase selected
                                                    false - openDatabase unselected
                 default: True

            resetLogs       (bool)              :  resetLogs option
                                                    true - resetLogs selected
                                                    false - resetLogs unselected
                 default: True

            point_in_time            (str)      :  date to use for restore and recover  database
                                                   format: dd/MM/YYYY
                                                   gets content from 01/01/1970 if not specified
                default: 0

            backupValidationOnly       (bool)   :  backupValidationOnly option
                                                    true - backupValidationOnly selected
                                                    false - backupValidationOnly unselected
                 default: False

             restoreTablespace       (bool)     :  restoreTablespace option
                                                    true - restoreTablespace selected
                                                    false - restoreTablespace unselected
                 default: False

            noCatalog       (bool)              :  noCatalog option
                                                    true - noCatalog selected
                                                    false - noCatalog unselected
                 default: True

            sourceItem       (list)              :  sourceItem means browse options for
                                                     sap oracle restores
                                                    /+BROWSE+ - means both data and logs
                                                    are selected
                                                    /+BROWSE+DATA -data only selected
                                                    /+BROWSE+LOG -log only selected
                 default: /+BROWSE+
            databaseCopy       (bool)            :  databaseCopy option
                                                    true - databaseCopy selected
                                                    false - databaseCopy unselected
                 default: False

            archiveLogBy       (str)            :  for restore archive log options,
                                                    default means restore archivelogall
                                                    is selected

                 default: default

     Raises:
            SDKException:

                if failed to browse content

                if response is empty

                if response is not success

                if destination client does not exist on commcell

                if destination instance does not exist on commcell
    &#34;&#34;&#34;

    if sap_options is None:
        sap_options = {}

    # check if client name is correct
    if destination_client is None:
        destination_client = self._agent_object._client_object.client_name
        

    if isinstance(destination_client, Client):
        destination_client = destination_client
        
    elif isinstance(destination_client, str):
        destination_client = Client(self._commcell_object, destination_client)
        #print(destination_client)
    else:
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    dest_agent = Agent(destination_client, &#39;sap for oracle&#39;,&#39;61&#39;)

    # check if instance name is correct
    if destination_instance is None:
        destination_instance = self.instance_name

    if isinstance(destination_instance, Instance):
        destination_instance = destination_instance
    elif isinstance(destination_instance, str):
        destination_instance = dest_agent.instances.get(destination_instance)
    else:
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
    sap_options[&#34;destination_client&#34;] = destination_client.client_name
    sap_options[&#34;destination_instance&#34;] = destination_instance.instance_name
    #sap_options[&#34;copyPrecedence&#34;] = sap_options.get(&#34;copyPrecedence&#34;, &#34;0&#34;)

    # prepare and execute
    request_json = self._restore_saporacle_request_json(sap_options)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instance.Instance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.browse" href="../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance">SAPOracleInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.cmd_sp" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.cmd_sp">cmd_sp</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.log_sp" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.log_sp">log_sp</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.oracle_home" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.oracle_home">oracle_home</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.os_user" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.os_user">os_user</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.restore_in_place" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.sapdata_home" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.sapdata_home">sapdata_home</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.sapexepath" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.sapexepath">sapexepath</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_archivelogbackupstreams" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_archivelogbackupstreams">saporacle_archivelogbackupstreams</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_blocksize" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_blocksize">saporacle_blocksize</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_db_connectstring" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_db_connectstring">saporacle_db_connectstring</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_db_user" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_db_user">saporacle_db_user</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_instanceid" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_instanceid">saporacle_instanceid</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_sapsecurestore" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_sapsecurestore">saporacle_sapsecurestore</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_snapbackup_enable" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_snapbackup_enable">saporacle_snapbackup_enable</a></code></li>
<li><code><a title="cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_snapengine_name" href="#cvpysdk.instances.saporacleinstance.SAPOracleInstance.saporacle_snapengine_name">saporacle_snapengine_name</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>