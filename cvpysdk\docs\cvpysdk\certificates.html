<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.certificates API documentation</title>
<meta name="description" content="" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.certificates</code></h1>
</header>
<section id="section-intro">
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/certificates.py#L1-L220" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------


class Certificate:
    &#34;&#34;&#34;Class for performing certificate related operations&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;
        Initialize the Certificate class object

        Args: 
            commcell: Commcell object
        &#34;&#34;&#34;

        self.commcell = commcell
        self.url = commcell._services[&#34;CERTIFICATES&#34;]

    def _make_request(self, body={}):
        &#34;&#34;&#34;
        Make a certificate API request

        Args:
            body (dict): Body to pass in post request

        Return: 
            Response of the request

        Example:
            _make_request({})
        &#34;&#34;&#34;
        success, resp = self.commcell._cvpysdk_object.make_request(&#34;POST&#34;, self.url, body)
        resp_data = resp.json()
        if resp.status_code != 200:
            raise Exception(f&#34;APIException: Response code {resp.status_code}.\n{resp_data}&#34;)
        else:
            return resp_data

    def revoke(self, cert_ids):
        &#34;&#34;&#34;
        Revoke the certificate by certificate id

        Args:
            cert_ids (List[int]): List of certificate id&#39;s

        Return: 
            bool: if request processed successfully

        Example:
            revoke([1, 2, 3])
        &#34;&#34;&#34;
        data = {
            &#34;operation&#34;: 3,
            &#34;certificateInfo&#34;: {
                &#34;certificates&#34;: [

                ]
            }
        }
        if type(cert_ids) == list:
            for id in cert_ids:
                data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                    {&#34;id&#34;: int(id)}
                )
        elif type(cert_ids) == int or type(cert_ids) == str:
            data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                {&#34;id&#34;: int(cert_ids)}
            )
        else:
            raise Exception(&#34;cert_ids should be of type list or int&#34;)
        resp_data = self._make_request(data)
        return True

    def renew(self, cert_ids):
        &#34;&#34;&#34;
        Renew the certificate by certificate id

        Args:
            cert_ids (List[int]): List of certificate id&#39;s

        Return: 
            bool: if request processed successfully

        Example:
            renew([1, 2, 3])
        &#34;&#34;&#34;
        data = {
            &#34;operation&#34;: 2,
            &#34;certificateInfo&#34;: {
                &#34;certificates&#34;: [

                ]
            }
        }
        if type(cert_ids) == list:
            for id in cert_ids:
                data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                    {&#34;id&#34;: int(id)}
                )
        elif type(cert_ids) == int or type(cert_ids) == str:
            data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                {&#34;id&#34;: int(cert_ids)}
            )
        else:
            raise Exception(&#34;cert_ids should be of type list or int&#34;)
        resp_data = self._make_request(data)
        return True

    def force_client_authentication(self, operation):
        &#34;&#34;&#34;
        Enable of disable the lockdown mode

        Args:
            operation (bool): Turn ON/OFF the lockdown mode. 

        Return: 
            bool: if request processed successfully

        Example:
            force_client_authentication(True)
            force_client_authentication(False)
        &#34;&#34;&#34;
        body = {
            &#34;operation&#34;: 0,
            &#34;certificateInfo&#34;: {

                &#34;forceClientAuth&#34;: operation

            }
        }
        try :
            resp_data = self._make_request(body)
            return True
        except Exception as e:
            raise Exception(str(e))

    def make_temp_certificate(self, client_id):
        &#34;&#34;&#34;
        Create temporary certificate of client

        Args:
            client_id (int): Client Id to generate certificate.

        Return: 
            str: Temp certificate for the client.

        Example:
            make_temp_certificate(5)
        &#34;&#34;&#34;
        body = {
            &#34;operation&#34;: 4,
            &#34;makeTempCertClientID&#34;: client_id
        }
        resp_data = self._make_request(body)
        return resp_data[&#34;certificateInfo&#34;][&#34;tempCertificateInfo&#34;]

    def client_certificate_rotation(self, months):
        &#34;&#34;&#34;
        Modify certificate rotation period.

        Args:
            months (int): Number of months.

        Return: 
            bool: if request processed successfully

        Example:
            client_certificate_rotation(12)
        &#34;&#34;&#34;
        body = {
            &#34;operation&#34;: 0,
            &#34;certificateInfo&#34;: {
                &#34;ClientCertificateRotation&#34;: int(months)
            }
        }
        try:
            resp_data = self._make_request(body)
            return True
        except Exception as e:
            raise Exception(str(e))

    def ca_certificate_rotation(self, years):
        &#34;&#34;&#34;
        Modify certificate rotation period.

        Args:
            years (int): Number of years.

        Return: 
            bool: if request processed successfully

        Example:
            ca_certificate_rotation(1)
        &#34;&#34;&#34;
        body = {
            &#34;operation&#34;: 0,
            &#34;certificateInfo&#34;: {
                &#34;CACertificateRotation&#34;: int(years)
            }
        }
        try:
            resp_data = self._make_request(body)
            return True
        except Exception as e:
            raise Exception(str(e))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.certificates.Certificate"><code class="flex name class">
<span>class <span class="ident">Certificate</span></span>
<span>(</span><span>commcell)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing certificate related operations</p>
<p>Initialize the Certificate class object</p>
<p>Args:
commcell: Commcell object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/certificates.py#L20-L220" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Certificate:
    &#34;&#34;&#34;Class for performing certificate related operations&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;
        Initialize the Certificate class object

        Args: 
            commcell: Commcell object
        &#34;&#34;&#34;

        self.commcell = commcell
        self.url = commcell._services[&#34;CERTIFICATES&#34;]

    def _make_request(self, body={}):
        &#34;&#34;&#34;
        Make a certificate API request

        Args:
            body (dict): Body to pass in post request

        Return: 
            Response of the request

        Example:
            _make_request({})
        &#34;&#34;&#34;
        success, resp = self.commcell._cvpysdk_object.make_request(&#34;POST&#34;, self.url, body)
        resp_data = resp.json()
        if resp.status_code != 200:
            raise Exception(f&#34;APIException: Response code {resp.status_code}.\n{resp_data}&#34;)
        else:
            return resp_data

    def revoke(self, cert_ids):
        &#34;&#34;&#34;
        Revoke the certificate by certificate id

        Args:
            cert_ids (List[int]): List of certificate id&#39;s

        Return: 
            bool: if request processed successfully

        Example:
            revoke([1, 2, 3])
        &#34;&#34;&#34;
        data = {
            &#34;operation&#34;: 3,
            &#34;certificateInfo&#34;: {
                &#34;certificates&#34;: [

                ]
            }
        }
        if type(cert_ids) == list:
            for id in cert_ids:
                data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                    {&#34;id&#34;: int(id)}
                )
        elif type(cert_ids) == int or type(cert_ids) == str:
            data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                {&#34;id&#34;: int(cert_ids)}
            )
        else:
            raise Exception(&#34;cert_ids should be of type list or int&#34;)
        resp_data = self._make_request(data)
        return True

    def renew(self, cert_ids):
        &#34;&#34;&#34;
        Renew the certificate by certificate id

        Args:
            cert_ids (List[int]): List of certificate id&#39;s

        Return: 
            bool: if request processed successfully

        Example:
            renew([1, 2, 3])
        &#34;&#34;&#34;
        data = {
            &#34;operation&#34;: 2,
            &#34;certificateInfo&#34;: {
                &#34;certificates&#34;: [

                ]
            }
        }
        if type(cert_ids) == list:
            for id in cert_ids:
                data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                    {&#34;id&#34;: int(id)}
                )
        elif type(cert_ids) == int or type(cert_ids) == str:
            data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                {&#34;id&#34;: int(cert_ids)}
            )
        else:
            raise Exception(&#34;cert_ids should be of type list or int&#34;)
        resp_data = self._make_request(data)
        return True

    def force_client_authentication(self, operation):
        &#34;&#34;&#34;
        Enable of disable the lockdown mode

        Args:
            operation (bool): Turn ON/OFF the lockdown mode. 

        Return: 
            bool: if request processed successfully

        Example:
            force_client_authentication(True)
            force_client_authentication(False)
        &#34;&#34;&#34;
        body = {
            &#34;operation&#34;: 0,
            &#34;certificateInfo&#34;: {

                &#34;forceClientAuth&#34;: operation

            }
        }
        try :
            resp_data = self._make_request(body)
            return True
        except Exception as e:
            raise Exception(str(e))

    def make_temp_certificate(self, client_id):
        &#34;&#34;&#34;
        Create temporary certificate of client

        Args:
            client_id (int): Client Id to generate certificate.

        Return: 
            str: Temp certificate for the client.

        Example:
            make_temp_certificate(5)
        &#34;&#34;&#34;
        body = {
            &#34;operation&#34;: 4,
            &#34;makeTempCertClientID&#34;: client_id
        }
        resp_data = self._make_request(body)
        return resp_data[&#34;certificateInfo&#34;][&#34;tempCertificateInfo&#34;]

    def client_certificate_rotation(self, months):
        &#34;&#34;&#34;
        Modify certificate rotation period.

        Args:
            months (int): Number of months.

        Return: 
            bool: if request processed successfully

        Example:
            client_certificate_rotation(12)
        &#34;&#34;&#34;
        body = {
            &#34;operation&#34;: 0,
            &#34;certificateInfo&#34;: {
                &#34;ClientCertificateRotation&#34;: int(months)
            }
        }
        try:
            resp_data = self._make_request(body)
            return True
        except Exception as e:
            raise Exception(str(e))

    def ca_certificate_rotation(self, years):
        &#34;&#34;&#34;
        Modify certificate rotation period.

        Args:
            years (int): Number of years.

        Return: 
            bool: if request processed successfully

        Example:
            ca_certificate_rotation(1)
        &#34;&#34;&#34;
        body = {
            &#34;operation&#34;: 0,
            &#34;certificateInfo&#34;: {
                &#34;CACertificateRotation&#34;: int(years)
            }
        }
        try:
            resp_data = self._make_request(body)
            return True
        except Exception as e:
            raise Exception(str(e))</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.certificates.Certificate.ca_certificate_rotation"><code class="name flex">
<span>def <span class="ident">ca_certificate_rotation</span></span>(<span>self, years)</span>
</code></dt>
<dd>
<div class="desc"><p>Modify certificate rotation period.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>years</code></strong> :&ensp;<code>int</code></dt>
<dd>Number of years.</dd>
</dl>
<p>Return:
bool: if request processed successfully</p>
<h2 id="example">Example</h2>
<p>ca_certificate_rotation(1)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/certificates.py#L197-L220" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def ca_certificate_rotation(self, years):
    &#34;&#34;&#34;
    Modify certificate rotation period.

    Args:
        years (int): Number of years.

    Return: 
        bool: if request processed successfully

    Example:
        ca_certificate_rotation(1)
    &#34;&#34;&#34;
    body = {
        &#34;operation&#34;: 0,
        &#34;certificateInfo&#34;: {
            &#34;CACertificateRotation&#34;: int(years)
        }
    }
    try:
        resp_data = self._make_request(body)
        return True
    except Exception as e:
        raise Exception(str(e))</code></pre>
</details>
</dd>
<dt id="cvpysdk.certificates.Certificate.client_certificate_rotation"><code class="name flex">
<span>def <span class="ident">client_certificate_rotation</span></span>(<span>self, months)</span>
</code></dt>
<dd>
<div class="desc"><p>Modify certificate rotation period.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>months</code></strong> :&ensp;<code>int</code></dt>
<dd>Number of months.</dd>
</dl>
<p>Return:
bool: if request processed successfully</p>
<h2 id="example">Example</h2>
<p>client_certificate_rotation(12)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/certificates.py#L172-L195" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def client_certificate_rotation(self, months):
    &#34;&#34;&#34;
    Modify certificate rotation period.

    Args:
        months (int): Number of months.

    Return: 
        bool: if request processed successfully

    Example:
        client_certificate_rotation(12)
    &#34;&#34;&#34;
    body = {
        &#34;operation&#34;: 0,
        &#34;certificateInfo&#34;: {
            &#34;ClientCertificateRotation&#34;: int(months)
        }
    }
    try:
        resp_data = self._make_request(body)
        return True
    except Exception as e:
        raise Exception(str(e))</code></pre>
</details>
</dd>
<dt id="cvpysdk.certificates.Certificate.force_client_authentication"><code class="name flex">
<span>def <span class="ident">force_client_authentication</span></span>(<span>self, operation)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable of disable the lockdown mode</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>operation</code></strong> :&ensp;<code>bool</code></dt>
<dd>Turn ON/OFF the lockdown mode. </dd>
</dl>
<p>Return:
bool: if request processed successfully</p>
<h2 id="example">Example</h2>
<p>force_client_authentication(True)
force_client_authentication(False)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/certificates.py#L124-L150" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def force_client_authentication(self, operation):
    &#34;&#34;&#34;
    Enable of disable the lockdown mode

    Args:
        operation (bool): Turn ON/OFF the lockdown mode. 

    Return: 
        bool: if request processed successfully

    Example:
        force_client_authentication(True)
        force_client_authentication(False)
    &#34;&#34;&#34;
    body = {
        &#34;operation&#34;: 0,
        &#34;certificateInfo&#34;: {

            &#34;forceClientAuth&#34;: operation

        }
    }
    try :
        resp_data = self._make_request(body)
        return True
    except Exception as e:
        raise Exception(str(e))</code></pre>
</details>
</dd>
<dt id="cvpysdk.certificates.Certificate.make_temp_certificate"><code class="name flex">
<span>def <span class="ident">make_temp_certificate</span></span>(<span>self, client_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Create temporary certificate of client</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>client_id</code></strong> :&ensp;<code>int</code></dt>
<dd>Client Id to generate certificate.</dd>
</dl>
<p>Return:
str: Temp certificate for the client.</p>
<h2 id="example">Example</h2>
<p>make_temp_certificate(5)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/certificates.py#L152-L170" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def make_temp_certificate(self, client_id):
    &#34;&#34;&#34;
    Create temporary certificate of client

    Args:
        client_id (int): Client Id to generate certificate.

    Return: 
        str: Temp certificate for the client.

    Example:
        make_temp_certificate(5)
    &#34;&#34;&#34;
    body = {
        &#34;operation&#34;: 4,
        &#34;makeTempCertClientID&#34;: client_id
    }
    resp_data = self._make_request(body)
    return resp_data[&#34;certificateInfo&#34;][&#34;tempCertificateInfo&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.certificates.Certificate.renew"><code class="name flex">
<span>def <span class="ident">renew</span></span>(<span>self, cert_ids)</span>
</code></dt>
<dd>
<div class="desc"><p>Renew the certificate by certificate id</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>cert_ids</code></strong> :&ensp;<code>List[int]</code></dt>
<dd>List of certificate id's</dd>
</dl>
<p>Return:
bool: if request processed successfully</p>
<h2 id="example">Example</h2>
<p>renew([1, 2, 3])</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/certificates.py#L89-L122" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def renew(self, cert_ids):
    &#34;&#34;&#34;
    Renew the certificate by certificate id

    Args:
        cert_ids (List[int]): List of certificate id&#39;s

    Return: 
        bool: if request processed successfully

    Example:
        renew([1, 2, 3])
    &#34;&#34;&#34;
    data = {
        &#34;operation&#34;: 2,
        &#34;certificateInfo&#34;: {
            &#34;certificates&#34;: [

            ]
        }
    }
    if type(cert_ids) == list:
        for id in cert_ids:
            data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                {&#34;id&#34;: int(id)}
            )
    elif type(cert_ids) == int or type(cert_ids) == str:
        data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
            {&#34;id&#34;: int(cert_ids)}
        )
    else:
        raise Exception(&#34;cert_ids should be of type list or int&#34;)
    resp_data = self._make_request(data)
    return True</code></pre>
</details>
</dd>
<dt id="cvpysdk.certificates.Certificate.revoke"><code class="name flex">
<span>def <span class="ident">revoke</span></span>(<span>self, cert_ids)</span>
</code></dt>
<dd>
<div class="desc"><p>Revoke the certificate by certificate id</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>cert_ids</code></strong> :&ensp;<code>List[int]</code></dt>
<dd>List of certificate id's</dd>
</dl>
<p>Return:
bool: if request processed successfully</p>
<h2 id="example">Example</h2>
<p>revoke([1, 2, 3])</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/certificates.py#L54-L87" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def revoke(self, cert_ids):
    &#34;&#34;&#34;
    Revoke the certificate by certificate id

    Args:
        cert_ids (List[int]): List of certificate id&#39;s

    Return: 
        bool: if request processed successfully

    Example:
        revoke([1, 2, 3])
    &#34;&#34;&#34;
    data = {
        &#34;operation&#34;: 3,
        &#34;certificateInfo&#34;: {
            &#34;certificates&#34;: [

            ]
        }
    }
    if type(cert_ids) == list:
        for id in cert_ids:
            data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
                {&#34;id&#34;: int(id)}
            )
    elif type(cert_ids) == int or type(cert_ids) == str:
        data[&#34;certificateInfo&#34;][&#34;certificates&#34;].append(
            {&#34;id&#34;: int(cert_ids)}
        )
    else:
        raise Exception(&#34;cert_ids should be of type list or int&#34;)
    resp_data = self._make_request(data)
    return True</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.certificates.Certificate" href="#cvpysdk.certificates.Certificate">Certificate</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.certificates.Certificate.ca_certificate_rotation" href="#cvpysdk.certificates.Certificate.ca_certificate_rotation">ca_certificate_rotation</a></code></li>
<li><code><a title="cvpysdk.certificates.Certificate.client_certificate_rotation" href="#cvpysdk.certificates.Certificate.client_certificate_rotation">client_certificate_rotation</a></code></li>
<li><code><a title="cvpysdk.certificates.Certificate.force_client_authentication" href="#cvpysdk.certificates.Certificate.force_client_authentication">force_client_authentication</a></code></li>
<li><code><a title="cvpysdk.certificates.Certificate.make_temp_certificate" href="#cvpysdk.certificates.Certificate.make_temp_certificate">make_temp_certificate</a></code></li>
<li><code><a title="cvpysdk.certificates.Certificate.renew" href="#cvpysdk.certificates.Certificate.renew">renew</a></code></li>
<li><code><a title="cvpysdk.certificates.Certificate.revoke" href="#cvpysdk.certificates.Certificate.revoke">revoke</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>