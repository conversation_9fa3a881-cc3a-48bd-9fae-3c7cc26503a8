<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.cloudapps.google_subclient API documentation</title>
<meta name="description" content="File for operating on a GMail/GDrive/OneDrive Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.cloudapps.google_subclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a GMail/GDrive/OneDrive Subclient.</p>
<p>GoogleSubclient is the only class defined in this file.</p>
<p>GoogleSubclient:
Derived class from CloudAppsSubclient Base class, representing a
GMail/GDrive/OneDrive subclient, and to perform operations on that subclient</p>
<h2 id="googlesubclient">Googlesubclient</h2>
<p>_get_subclient_properties()
&ndash;
gets the properties of Google Subclient</p>
<p>_get_subclient_properties_json()
&ndash;
gets the properties JSON of Google Subclient</p>
<p>content()
&ndash;
gets the content of the subclient</p>
<p>groups()
&ndash;
gets the groups associated with the subclient</p>
<p>restore_out_of_place()
&ndash;
runs out-of-place restore for the subclient</p>
<p>discover()
&ndash;
runs user discovery on subclient</p>
<p>add_AD_group()
&ndash;
adds AD group to the subclient</p>
<p>add_user()
&ndash;
adds user to the subclient</p>
<p>add_users()
&ndash;
Adds user to OneDrive for Business Client</p>
<p>add_shared_drives()
&ndash;
Adds given SharedDrives to client</p>
<p>search_for_user()
&ndash;
Searches for a specific user's details from discovered list</p>
<p>disk_restore()
&ndash;
Runs disk restore of selected users for OneDrive for Business Client</p>
<p>out_of_place_restore()
&ndash;
Runs out-of-place restore of selected users for OneDrive for Business Client</p>
<p>in_place_restore()
&ndash;
Runs in-place restore of selected users for OneDrive for Business Client</p>
<p>_get_user_guids()
&ndash;
Retrieve GUIDs for users specified</p>
<p>process_index_retention_rules()
&ndash;
Makes API call to process index retention rules</p>
<p>verify_user_discovery()
&ndash;
Makes API call to get discovered users of Google client</p>
<p>verify_shareddrive_discovery()
&ndash;
Makes API call to get discovered shared drives of GDrive client.</p>
<p>run_user_level_backup()
&ndash;
Runs Users level backup for google client</p>
<p>run_client_level_backup()
&ndash;
Runs client level backup for Google Client</p>
<p>browse_content()
&ndash;
Fetches discovered content based on discovery type</p>
<p>verify_groups_discovery()
&ndash;
Verifies that groups discovery is complete</p>
<p>search_for_shareddrive()
&ndash;
Searches for a specific shared drive details from discovered list</p>
<p>_association_users_json()
&ndash;
Constructs json for associated users to backup</p>
<p>_task_json_for_google_backup()
&ndash;
Constructs json for google backup for selected users</p>
<p>refresh_retention_stats()
&ndash;
Refreshes the retention stats for the client</p>
<p>refresh_stats_status()
&ndash;
refresh the client level or user level stats for the client</p>
<p>get_client_level_stats()
&ndash;
Returns the client level stats for the client</p>
<p>get_user_level_stats()
&ndash;
Returns the user level stats</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L1-L1382" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a GMail/GDrive/OneDrive Subclient.

GoogleSubclient is the only class defined in this file.

GoogleSubclient:    Derived class from CloudAppsSubclient Base class, representing a
GMail/GDrive/OneDrive subclient, and to perform operations on that subclient

GoogleSubclient:

    _get_subclient_properties()         --  gets the properties of Google Subclient

    _get_subclient_properties_json()    --  gets the properties JSON of Google Subclient

    content()                           --  gets the content of the subclient

    groups()                            --  gets the groups associated with the subclient

    restore_out_of_place()              --  runs out-of-place restore for the subclient

    discover()                          --  runs user discovery on subclient

    add_AD_group()                      --  adds AD group to the subclient

    add_user()                          --  adds user to the subclient

    add_users()                      --  Adds user to OneDrive for Business Client

    add_shared_drives()              --  Adds given SharedDrives to client

    search_for_user()                   --  Searches for a specific user&#39;s details from discovered list

    disk_restore()                   --  Runs disk restore of selected users for OneDrive for Business Client

    out_of_place_restore()           --  Runs out-of-place restore of selected users for OneDrive for Business Client

    in_place_restore()               --  Runs in-place restore of selected users for OneDrive for Business Client

    _get_user_guids()                   --  Retrieve GUIDs for users specified

    process_index_retention_rules()     --  Makes API call to process index retention rules

    verify_user_discovery()          --  Makes API call to get discovered users of Google client

    verify_shareddrive_discovery()   --  Makes API call to get discovered shared drives of GDrive client.

    run_user_level_backup()             --  Runs Users level backup for google client

    run_client_level_backup()           --  Runs client level backup for Google Client

    browse_content()                    --  Fetches discovered content based on discovery type

    verify_groups_discovery()           --  Verifies that groups discovery is complete

    search_for_shareddrive()            --  Searches for a specific shared drive details from discovered list

    _association_users_json()           --  Constructs json for associated users to backup

    _task_json_for_google_backup()      --  Constructs json for google backup for selected users
    
    refresh_retention_stats()           --  Refreshes the retention stats for the client

    refresh_stats_status()              --  refresh the client level or user level stats for the client

    get_client_level_stats()            --  Returns the client level stats for the client

    get_user_level_stats()              --  Returns the user level stats

&#34;&#34;&#34;

from __future__ import unicode_literals
from ...exception import SDKException
import time
from ..casubclient import CloudAppsSubclient
from ...constants import AppIDAType
from . import google_constants as constants


class GoogleSubclient(CloudAppsSubclient):
    &#34;&#34;&#34;Derived class from CloudAppsSubclient Base class, representing a GMail/GDrive/OneDrive subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of File System subclient..&#34;&#34;&#34;
        super(GoogleSubclient, self)._get_subclient_properties()
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

        content = []
        group_list = []

        for account in self._content:
            temp_account = account[&#34;cloudconnectorContent&#34;][&#34;includeAccounts&#34;]

            if temp_account[&#39;contentType&#39;] == AppIDAType.CLOUD_APP.value:
                content_dict = {
                    &#39;SMTPAddress&#39;: temp_account[&#34;contentName&#34;].split(&#34;;&#34;)[0],
                    &#39;display_name&#39;: temp_account[&#34;contentValue&#34;]
                }

                content.append(content_dict)
            if temp_account[&#39;contentType&#39;] == 135:
                group_list.append(temp_account[&#34;contentName&#34;])
        self._ca_content = content
        self._ca_groups = group_list

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;

        return {&#39;subClientProperties&#39;: self._subclient_properties}

    @property
    def content(self):
        &#34;&#34;&#34;Returns the subclient content dict&#34;&#34;&#34;
        return self._ca_content

    @property
    def groups(self):
        &#34;&#34;&#34;Returns the list of groups assigned to the subclient if any.
        Groups can be azure AD group or Google groups.
        Groups are assigned only if auto discovery is enabled for groups.

            Returns:

                list - list of groups associated with the subclient

        &#34;&#34;&#34;
        return self._ca_groups

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update content of a
            Cloud Apps Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        content = []

        try:
            for account in subclient_content:
                temp_content_dict = {
                    &#34;cloudconnectorContent&#34;: {
                        &#34;includeAccounts&#34;: {
                            &#34;contentValue&#34;: account[&#39;display_name&#39;],
                            &#34;contentType&#34;: AppIDAType.CLOUD_APP.value,
                            &#34;contentName&#34;: account[&#39;SMTPAddress&#39;]
                        }
                    }
                }

                content.append(temp_content_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        self._set_subclient_properties(&#34;_content&#34;, content)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            to_disk=False):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_disk             (bool)       --  If True, restore to disk will be performed

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity

        return self._instance_object.restore_out_of_place(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            to_disk=to_disk
        )

    def discover(self, discover_type=&#39;USERS&#39;):
        &#34;&#34;&#34;This method discovers the users/groups on Google GSuite Account/OneDrive

                Args:

                    discover_type (str)  --  Type of discovery

                        Valid Values are

                        -   USERS
                        -   GROUPS

                        Default: USERS

                Returns:

                    List (list)  --  List of users on GSuite account

                Raises:
                    SDKException:
                        if response is empty

                        if response is not success


        &#34;&#34;&#34;

        if discover_type.upper() == &#39;USERS&#39;:
            disc_type = 10
        elif discover_type.upper() == &#39;GROUPS&#39;:
            disc_type = 5
        _get_users = self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self._instance_object.instance_id,
                                                              self._client_object.client_id,
                                                              disc_type)

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _get_users)

        if flag:
            if response.json() and &#34;scDiscoveryContent&#34; in response.json():
                self._discover_properties = response.json()[
                    &#34;scDiscoveryContent&#34;][0]

                if &#34;contentInfo&#34; in self._discover_properties:
                    self._contentInfo = self._discover_properties[&#34;contentInfo&#34;]
                return self._contentInfo
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def set_auto_discovery(self, value):
        &#34;&#34;&#34;Sets the auto discovery value for subclient.
        You can either set a RegEx value or a user group,
        depending on the auto discovery type selected at instance level.

            Args:

                value   (list)  --  List of RegEx or user groups

        &#34;&#34;&#34;

        if not isinstance(value, list):
            raise SDKException(&#39;Subclient&#39;, &#39;116&#39;)

        if not self._instance_object.auto_discovery_status:
            raise SDKException(&#39;Subclient&#39;, &#39;117&#39;)

        subclient_prop = self._subclient_properties[&#39;cloudAppsSubClientProp&#39;].copy()
        if self._instance_object.auto_discovery_mode == 0:
            # RegEx based auto discovery is enabled on instance

            if subclient_prop[&#39;instanceType&#39;] == 7:
                subclient_prop[&#39;oneDriveSubclient&#39;][&#39;regularExp&#39;] = value
            else:
                subclient_prop[&#39;GoogleSubclient&#39;][&#39;regularExp&#39;] = value
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]&#34;, subclient_prop)
        else:
            # User group based auto discovery is enabled on instance
            grp_list = []
            groups = self.discover(discover_type=&#39;GROUPS&#39;)
            for item in value:
                for group in groups:
                    if group[&#39;contentName&#39;].lower() == item.lower():
                        grp_list.append({
                            &#34;cloudconnectorContent&#34;: {
                                &#34;includeAccounts&#34;: group
                            }
                        })
            self._content.extend(grp_list)
            self._set_subclient_properties(&#34;_subclient_properties[&#39;content&#39;]&#34;, self._content)
        self.refresh()

    def run_subclient_discovery(self):
        &#34;&#34;&#34;
            This method launches AutoDiscovery on the subclient
        &#34;&#34;&#34;

        discover_type = 15
        discover_users = self._services[&#39;GET_CLOUDAPPS_ONEDRIVE_USERS&#39;] % (self._instance_object.instance_id,
                                                                           self._client_object.client_id,
                                                                           discover_type,
                                                                           self.subclient_id)
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_users)
        if response.status_code != 200 and response.status_code != 500:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def add_AD_group(self, value):
        &#34;&#34;&#34;Adds the user group to the subclient if auto discovery type selected
            AD group at instance level.
                Args:
                    value   (list)  --  List of user groups
        &#34;&#34;&#34;
        grp_list = []
        groups = self.discover(discover_type=&#39;GROUPS&#39;)
        for item in value:
            for group in groups:
                if group[&#39;contentName&#39;].lower() == item.lower():
                    grp_list.append(group)

        contentinfo = []

        for grp in grp_list:
            info = {
                &#34;contentValue&#34;: grp[&#39;contentValue&#39;],
                &#34;contentType&#34;: grp[&#39;contentType&#39;],
                &#34;contentName&#34;: grp[&#39;contentName&#39;]
            }
            contentinfo.append(info)

        request_json = {
            &#34;App_DiscoveryContent&#34;: {
                &#34;scDiscoveryContent&#34;: [
                    {
                        &#34;scEntity&#34;: {
                            &#34;subclientId&#34;: self.subclient_id
                        },
                        &#34;contentInfo&#34;: contentinfo
                    }
                ]
            }
        }
        add_ADgroup = self._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, add_ADgroup, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_user(self, user_name):
        &#34;&#34;&#34;This method adds one drive user to the subclient
                Args:
                    user_name   (str)  --  Onedrive user name
        &#34;&#34;&#34;
        users = self.discover(discover_type=&#39;USERS&#39;)

        for user in users:
            if user[&#39;contentName&#39;].lower() == user_name.lower():
                user_dict = user
                break

        request_json = {
            &#34;App_DiscoveryContent&#34;: {
                &#34;scDiscoveryContent&#34;: [
                    {
                        &#34;scEntity&#34;: {
                            &#34;subclientId&#34;: self.subclient_id
                        },
                        &#34;contentInfo&#34;: [
                            {
                                &#34;contentValue&#34;: user_dict[&#39;contentValue&#39;],
                                &#34;contentType&#34;: user_dict[&#39;contentType&#39;],
                                &#34;contentName&#34;: user_dict[&#39;contentName&#39;]
                            }
                        ]
                    }
                ]
            }
        }

        add_user = self._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, add_user, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = &#39;Failed to user to the subclient\nError: &#34;{0}&#34;&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_subclient_users(self):
        &#34;&#34;&#34;Method to get the users in the subclient

            Returns:
                List of Users in subclient
        &#34;&#34;&#34;
        users = []
        result = self.content
        for user in result:
            users.append(user[&#39;SMTPAddress&#39;])
        return users

    @property
    def get_subclient_users(self):
        &#34;&#34;&#34;Returns the users in subclient&#34;&#34;&#34;
        return self._get_subclient_users()

    def add_users(self, users, plan_name):
        &#34;&#34;&#34; Adds given OneDrive users to v2 client

            Args:

                users (list) : List of user&#39;s SMTP address

                plan_name (str) : Google Workspace plan name to associate with users

            Raises:

                SDKException:

                    if response is not success

                    if response is returned with errors
        &#34;&#34;&#34;

        if not (isinstance(users, list) and isinstance(plan_name, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Get GoogleWorkspace plan
        plan_name = plan_name.strip()
        google_plan_object = self._commcell_object.plans.get(plan_name)
        google_plan_id = int(google_plan_object.plan_id)

        # Get client ID
        client_id = int(self._client_object.client_id)

        user_accounts = []

        for user_id in users:
            user_accounts.append(self.search_for_user(user_id))

        request_json = {
            &#34;LaunchAutoDiscovery&#34;: False,
            &#34;cloudAppAssociation&#34;: {
                &#34;accountStatus&#34;: 0,
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: client_id,
                    &#34;instanceId&#34;: int(self._instance_object.instance_id),
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    # GDrive : 24 | GMail : 22
                    &#34;discoverByType&#34;: 22 if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else 24,
                    &#34;userAccounts&#34;: user_accounts
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: google_plan_id
                }
            }
        }

        user_associations = self._services[
            &#39;GMAIL_UPDATE_USERS&#39;] if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else self._services[
            &#39;GDRIVE_UPDATE_USERS&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, user_associations, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add user\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_shared_drives(self, shared_drives, plan_name):
        &#34;&#34;&#34; Adds given SharedDrives to client

            Args:

                shared_drives (list) : List of SharedDrives

                plan_name (str) : Google Workspace plan name to associate with users

            Raises:

                SDKException:

                    if response is not success

                    if response is returned with errors
        &#34;&#34;&#34;

        if not (isinstance(shared_drives, list) and isinstance(plan_name, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Get GoogleWorkspace plan
        plan_name = plan_name.strip()
        google_plan_object = self._commcell_object.plans.get(plan_name)
        google_plan_id = int(google_plan_object.plan_id)

        # Get client ID
        client_id = int(self._client_object.client_id)

        drives = []

        for drive in shared_drives:
            response = self.search_for_shareddrive(drive)
            response[&#39;user&#39;] = {}
            response[&#39;displayName&#39;] = response[&#39;folderTitle&#39;]
            response[&#39;user&#39;][&#39;userGUID&#39;] = response[&#39;folderId&#39;]
            response[&#39;isAutoDiscoveredUser&#39;] = False
            drives.append(response)

        request_json = {
            &#34;LaunchAutoDiscovery&#34;: False,
            &#34;cloudAppAssociation&#34;: {
                &#34;accountStatus&#34;: 0,
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: client_id,
                    &#34;instanceId&#34;: int(self._instance_object.instance_id),
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 32,
                    &#34;userAccounts&#34;: drives
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: google_plan_id
                }
            }
        }

        user_associations = self._services[&#39;GDRIVE_UPDATE_USERS&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, user_associations, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add user\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def browse_content(self, discovery_type):
        &#34;&#34;&#34; Fetches discovered content based on discovery type
            Args:
                discovery_type: Type of content to be discovered.
                    discovery_type=8 - Users
                    discovery_type=25 - Shared Drives
                    discovery_type=5 - Groups

            Returns:
                    records (list):  content fetched, [] if no content fetched

            Raises:

                 SDKException:

                        if response is not success
        &#34;&#34;&#34;
        # Wait for sometime unitl disco discovery completes before checking actual content.
        attempt = 0
        while attempt &lt; 5:
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, (
                    self._services[&#39;GOOGLE_DISCOVERY_OVERVIEW&#39;] % (self._backupset_object.backupset_id)))
            if response.json()[&#39;office365ClientOverview&#39;][&#39;summary&#39;][&#39;discoverState&#39;][&#39;discoveryProgress&#39;] == 100:
                break
            attempt += 1
            time.sleep(10)

        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        # determines the number of accounts to return in response
        page_size = 500
        offset = 0

        records = []
        while True:
            discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&amp;offset={offset}&amp;eDiscoverType={discovery_type}&#39;
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_query)
            offset += 1
            if flag:
                if response and response.json():
                    if discovery_type == 8:
                        if &#39;userAccounts&#39; in response.json():
                            curr_records = response.json().get(&#39;userAccounts&#39;, [])
                            records.extend(curr_records)
                            if len(curr_records) &lt; page_size:
                                break
                    elif discovery_type == 25:
                        if &#39;folders&#39; in response.json():
                            curr_records = response.json().get(&#39;folders&#39;, [])
                            records.extend(curr_records)
                            if len(curr_records) &lt; page_size:
                                break
                    elif discovery_type == 5:
                        if &#39;groups&#39; in response.json():
                            curr_groups = response.json().get(&#39;groups&#39;, [])
                            records.extend(curr_groups)
                            if len(curr_groups) &lt; page_size:
                                break
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return records

    def verify_groups_discovery(self):
        &#34;&#34;&#34; Verifies that groups discovery is complete

            Returns:

                discovery_stats (tuple):

                    discovery_status (bool): True if users are discovered else returns False

                    user_accounts (int):     List of users fetched, returns [] if discovery is not complete

            Raises:

                 SDKException:

                        if response is not success

                        if response received does not contain pagining info
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        # determines the number of accounts to return in response
        page_size = 500
        offset = 0

        groups = []
        while True:
            discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&amp;offset={offset}&amp;eDiscoverType=5&#39;
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_query)
            offset += 1

            if flag:
                if response and response.json():
                    if &#39;groups&#39; in response.json():
                        curr_groups = response.json().get(&#39;groups&#39;, [])
                        groups.extend(curr_groups)
                        if len(curr_groups) &lt; page_size:
                            break
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return groups

    def verify_shareddrive_discovery(self):
        &#34;&#34;&#34; Verifies all shared drives discovery completed.

                    Returns:

                        discovery_stats (tuple):

                            discovery_status (bool): True if users are discovered else returns False

                            user_accounts (int):     List of shared drives fetched, returns [] if discovery is not complete

                    Raises:

                         SDKException:

                                if response is not success

                                if response received does not contain pagining info
                &#34;&#34;&#34;

        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        # determines the number of accounts to return in response
        page_size = 500
        discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&amp;eDiscoverType=25&#39;  # for shared drive discovery

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_query)

        if flag:
            no_of_records = -1
            if response and response.json():
                if &#39;pagingInfo&#39; in response.json():
                    no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                    if no_of_records &gt; 0:
                        shared_drives = response.json().get(&#39;folders&#39;, [])
                        return True, shared_drives
            return False, []
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _association_users_json(self, users_list):
        &#34;&#34;&#34;
            Args:
                users_list (list) : list of SMTP addresses of users
            Returns:
                users_json(list): Required details of users to backup
        &#34;&#34;&#34;
        users_json = []
        for user_smtp in users_list:
            user_details = self.search_for_user(user_smtp)
            user_info = {
                &#34;user&#34;: {
                    &#34;userGUID&#34;: user_details.get(&#39;user&#39;, {}).get(&#39;userGUID&#39;)
                }
            }
            users_json.append(user_info)
        return users_json

    def _task_json_for_google_backup(self, is_mailbox, users_list=None, **kwargs):
        &#34;&#34;&#34;
        Json for google backup for selected users

        Args:
                is_mailbox (boolean) : used to determine Gmail or GDrive
                users_list (list) : list of SMTP addresses of users
                **kwargs (dict) : contains some optional fields like full_backup, etc
                        Ex: **{
                            &#39;full_backup&#39;(boolean): Flag whether to run full backup or not.
                        }
        Returns:
                task_json(dict): Task request used as backup payload
        &#34;&#34;&#34;

        selected_items = []
        advanced_options_dict = None
        if users_list is not None:
            associated_users_json = self._association_users_json(users_list)
            advanced_options_dict = {
                &#39;cloudAppOptions&#39;: {
                    &#39;userAccounts&#39;: associated_users_json
                }
            }

            for user_smtp in users_list:
                details = self.search_for_user(user_smtp)
                item = {
                    &#34;itemName&#34;: details.get(&#39;displayName&#39;),
                    &#34;itemType&#34;: &#34;Mailbox&#34; if is_mailbox else &#34;User&#34;
                }
                selected_items.append(item)
        else:
            item = {
                &#34;itemName&#34;: &#34;All mailboxes&#34; if is_mailbox else &#34;All users&#34;,
                &#34;itemType&#34;: &#34;All mailboxes&#34; if is_mailbox else &#34;All users&#34;
            }
            selected_items.append(item)

        common_options_dict = {
            &#34;jobMetadata&#34;: [
                {
                    &#34;selectedItems&#34;: selected_items,
                    &#34;jobOptionItems&#34;: [
                        {
                            &#34;option&#34;: &#34;Total running time&#34;,
                            &#34;value&#34;: &#34;Disabled&#34;
                        },
                        {
                            &#34;option&#34;: &#34;Convert job to full&#34;,
                            &#34;value&#34;: &#34;Enabled&#34; if kwargs.get(&#39;full_backup&#39;, False) else &#34;Disabled&#34;
                        }
                    ]
                }
            ]
        }
        task_json = self._backup_json(backup_level=&#39;INCREMENTAL&#39;, incremental_backup=False,
                                      incremental_level=&#39;BEFORE_SYNTH&#39;, advanced_options=advanced_options_dict,
                                      common_backup_options=common_options_dict)
        return task_json

    def run_user_level_backup(self, users_list, is_mailbox, **kwargs):
        &#34;&#34;&#34;
        Runs the backup for the users in users list
        Args:
                users_list (list) : list of SMTP addresses of users

                is_mailbox (boolean) : flag to determine GMail Mailbox or not

                **kwargs (dict) : contains some optional fields like full_backup, etc
                        Ex: **{
                            &#39;full_backup&#39;(boolean): Flag whether to run full backup or not.
                        }

        Returns:
                object - instance of the Job class for this backup job

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        task_json = self._task_json_for_google_backup(is_mailbox, users_list=users_list, **kwargs)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def run_client_level_backup(self, is_mailbox, **kwargs):
        &#34;&#34;&#34;
                Runs the backup for the client
                Args:
                        is_mailbox (boolean) : flag to determine GMail Mailbox or not
                        **kwargs (dict) : contains some optional fields like full_backup, etc
                        Ex: **{
                            &#39;full_backup&#39;(boolean): Flag whether to run full backup or not.
                        }

                Returns:
                        object - instance of the Job class for this backup job

                Raises:
                    SDKException:
                        if response is empty

                        if response is not success

                &#34;&#34;&#34;
        task_json = self._task_json_for_google_backup(is_mailbox, **kwargs)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def search_for_user(self, user_id):
        &#34;&#34;&#34; Searches for a specific user&#39;s details from discovered list

            Args:
                user_id (str) : user&#39;s SMTP address

            Returns:

                user (list): user details&#39; list fetched from discovered content
                              eg: {
                                        &#39;displayName&#39;: &#39;&#39;,
                                        &#39;smtpAddress&#39;: &#39;&#39;,
                                        &#39;isSuperAdmin&#39;: False,
                                        &#39;isAutoDiscoveredUser&#39;: False,
                                        &#39;commonFlags&#39;: 0,
                                        &#39;user&#39;: {
                                            &#39;_type_&#39;: 13,
                                             &#39;userGUID&#39;: &#39;UserGuid&#39;
                                        }
                                 }

            Raises:

                SDKException:

                    if discovery is not complete

                    if invalid SMTP address is passed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        search_query = f&#39;{browse_content}&amp;search={user_id}&#39;

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

        if flag:
            if response and response.json():
                if &#39;userAccounts&#39; in response.json():
                    user_accounts = response.json().get(&#39;userAccounts&#39;, [])
                    if len(user_accounts) == 0:
                        error_string = &#39;Either discovery is not complete or user is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                    for user in user_accounts:
                        if user[&#39;smtpAddress&#39;] == user_id:
                            return user
                    else:
                        error_string = &#39;User is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Check if the user provided is valid&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def search_for_shareddrive(self, drive):
        &#34;&#34;&#34; Searches for a specific shared drive details from discovered list

            Args:
                drive (str) : Shared Drive iD

            Returns:

                drive (list): shared drive details&#39; list fetched from discovered content
                              eg: {
                                        &#39;folderTitle&#39;: &#39;&#39;,
                                        &#39;folderId&#39;:&#39;&#39;,
                                        &#39;user&#39;: {
                                             &#39;userGUID&#39;: &#39;UserGuid&#39;
                                        }
                                 }

            Raises:

                SDKException:

                    if discovery is not complete

                    if invalid SMTP address is passed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        search_query = f&#39;{browse_content}&amp;search={drive}&amp;eDiscoverType=25&#39;

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

        if flag:
            if response and response.json():
                if &#39;folders&#39; in response.json():
                    folders = response.json().get(&#39;folders&#39;, [])
                    if len(folders) == 0:
                        error_string = &#39;Either discovery is not complete or Shared Drive is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                    for folder in folders:
                        if folder[&#39;folderTitle&#39;] == drive:
                            return folder
                    else:
                        error_string = &#39;Shared Drive is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Check if the Shared Drive provided is valid&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def disk_restore(self, users, destination_client, destination_path, skip_file_permissions=False):
        &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                destination_client (str) : client where the users need to be restored
                destination_path (str) : Destination folder location
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;disk_restore&#39;: True,
            &#39;destination_path&#39;: destination_path,
            &#39;destination_client&#39;: destination_client,
            &#39;skip_file_permissions&#39;: skip_file_permissions
        }
        restore_json = self._instance_object._prepare_restore_json(source_user_list, **kwargs)
        return self._process_restore_response(restore_json)

    def out_of_place_restore(self, users, destination_path, **kwargs):
        &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                destination_path (str) : SMTP address of destination user
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
                    destination_type (str) : type of destination for OOP Restore
                    end_time (int) : The job end time for Point In Time restore (default: None)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)
        end_time = kwargs.get(&#39;end_time&#39;, None)

        if overwrite and restore_as_copy:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        accountInfo = {}
        destination_type = kwargs.get(&#34;destination_type&#34;)
        if  destination_type == &#39;USER&#39;:
            destination_user_info = self.search_for_user(destination_path)
            accountInfo[&#39;userDisplayName&#39;] = destination_user_info.get(&#39;displayName&#39;, &#39;&#39;)
            accountInfo[&#39;userGUID&#39;] = destination_user_info.get(&#39;user&#39;).get(&#39;userGUID&#39;, &#39;&#39;)
            accountInfo[&#39;userSMTP&#39;] = destination_user_info.get(&#39;smtpAddress&#39;, &#39;&#39;)
        else:
            destination_user_info = self.search_for_shareddrive(destination_path)
            accountInfo[&#39;userDisplayName&#39;] = destination_user_info.get(&#39;folderTitle&#39;, &#39;&#39;)
            accountInfo[&#39;userGUID&#39;] = destination_user_info.get(&#39;folderId&#39;, &#39;&#39;)

        kwargs = {
            &#39;out_of_place&#39;: True,
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions,
            &#39;accountInfo&#39;: accountInfo,
            &#39;destination_type&#39;: destination_type,
            &#39;destination_path&#39;: destination_path,
        }
        restore_json = self._instance_object._prepare_restore_json(source_user_list, **kwargs)
        if end_time:
            adv_search_bkp_time_dict = {
                &#34;field&#34;: &#34;BACKUPTIME&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;0&#34;,
                        str(end_time)
                    ]
                },
                &#34;intraFieldOp&#34;: &#34;FTOr&#34;
            }

            add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
            add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
            add_backup_time = \
                restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
                    &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
            add_backup_time.append(adv_search_bkp_time_dict)
        return self._process_restore_response(restore_json)

    def in_place_restore(self, users, **kwargs):
        &#34;&#34;&#34; Runs an in-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) :  List of SMTP addresses of users
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
                    include_deleted_items (bool) : If True, Deleted items are also included in restore (default: False)
                    end_time (int) : The job end time for Point In Time restore (default: None)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)
        end_time = kwargs.get(&#39;end_time&#39;, None)

        if overwrite and restore_as_copy:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions,
            &#39;include_deleted_items&#39;: include_deleted_items
        }
        restore_json = self._instance_object._prepare_restore_json(source_user_list, **kwargs)
        if end_time:
            adv_search_bkp_time_dict = {
                &#34;field&#34;: &#34;BACKUPTIME&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;0&#34;,
                        str(end_time)
                    ]
                },
                &#34;intraFieldOp&#34;: &#34;FTOr&#34;
            }

            add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
            add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
            add_backup_time = \
                restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
                    &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
            add_backup_time.append(adv_search_bkp_time_dict)

        return self._process_restore_response(restore_json)

    def _get_user_guids(self, users):
        &#34;&#34;&#34; Retrieve GUIDs for users specified

            Args:
                user (list) : List of SMTP addresses of users

            Returns:
                user_guid_list (list) : list of GUIDs of specified users

            Raises:
                SDKException:
                    if user details couldn&#39;t be found in discovered data
        &#34;&#34;&#34;
        user_guid_list = []
        for user_id in users:
            try:
                user = self.search_for_user(user_id)
                if len(user) != 0 and user.get(&#39;user&#39;, {}).get(&#39;userGUID&#39;) is not None:
                    user_guid_list.append(user.get(&#39;user&#39;).get(&#39;userGUID&#39;))
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;User details not found in discovered data&#39;)
            except SDKException:
                user = self.search_for_shareddrive(user_id)
                if len(user) != 0 and user.get(&#39;folderId&#39;) is not None:
                    user_guid_list.append(user.get(&#39;folderId&#39;))
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;User details not found in discovered data&#39;)
        return user_guid_list

    def process_index_retention_rules(self, index_app_type_id, index_server_client_name):
        &#34;&#34;&#34;
         Makes API call to process index retention rules

         Args:

            index_app_type_id           (int)   --   index app type id

            index_server_client_name    (str)   --  client name of index server

         Raises:

                SDKException:

                    if index server not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if self._commcell_object.clients.has_client(index_server_client_name):
            index_server_client_id = int(self._commcell_object.clients[index_server_client_name.lower()][&#39;id&#39;])
            request_json = {
                &#34;appType&#34;: index_app_type_id,
                &#34;indexServerClientId&#34;: index_server_client_id
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                            o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
    
    def refresh_retention_stats(self):
        &#34;&#34;&#34;
        refresh the retention stats for the client
        &#34;&#34;&#34;
        request_json = {
            &#34;appType&#34;: constants.GMAIL_INDEX_APP_TYPE if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else constants.GDRIVE_INDEX_APP_TYPE,
            &#34;subclientId&#34;: int(self.subclient_id)
        }
        refresh_retention = self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, refresh_retention, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                                self._update_response_(response.text))

    def refresh_stats_status(self, user_level):
        &#34;&#34;&#34;
        refresh the client level or user level stats for the client
            Args:
                 user_level (bool) :  Option to refresh client level or user level stats
        &#34;&#34;&#34;
        if self._instance_object.ca_instance_type == &#39;GMAIL&#39;:
            request_json = {
                &#34;appType&#34;: constants.GMAIL_INDEX_APP_TYPE,
                &#34;gmailIdxStatsReq&#34;:
                    [{
                        &#34;subclientId&#34;: int(self.subclient_id), &#34;type&#34;: 1 if user_level else 0}]
            }
        else:
            request_json = {
                &#34;appType&#34;: constants.GDRIVE_INDEX_APP_TYPE,
                &#34;googleDriveIdxStatsReq&#34;:
                    [{
                        &#34;subclientId&#34;: int(self.subclient_id), &#34;type&#34;: 1 if user_level else 0}]
            }
        refresh_backup_stats = self._services[&#39;OFFICE365_POPULATE_INDEX_STATS&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, refresh_backup_stats, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def get_client_level_stats(self):
        &#34;&#34;&#34;
        Returns the client level stats for the client

        Retruns:

            response(json)                : returns the client level stats as a json response
        &#34;&#34;&#34;
        get_backup_stats = self._services[&#39;OFFICE365_OVERVIEW_STATS&#39;] % self._backupset_object.backupset_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, get_backup_stats)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

        return response.json()

    def get_user_level_stats(self):
        &#34;&#34;&#34;
        Returns the user level stats

        Retruns:

            response(json)                : returns the entity level stats as a json response
        &#34;&#34;&#34;
        request_json = {
            &#34;bIncludeDeleted&#34;: False,
            &#34;pagingInfo&#34;: {
                &#34;pageNumber&#34;: 0,
                &#34;pageSize&#34;: 100
            },
            &#34;discoverByType&#34;: constants.GMAIL_DISCOVERY_TYPE if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else constants.GDRIVE_DISCOVERY_TYPE,
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                }
            }
        }
        if self._instance_object.ca_instance_type == &#39;GMAIL&#39;:
            get_backup_stats = self._services[&#39;GMAIL_GET_USERS&#39;]
        else:
            get_backup_stats = self._services[&#39;GDRIVE_GET_USERS&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, get_backup_stats, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

        return response.json()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient"><code class="flex name class">
<span>class <span class="ident">GoogleSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from CloudAppsSubclient Base class, representing a GMail/GDrive/OneDrive subclient,
and to perform operations on that subclient.</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L96-L1382" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class GoogleSubclient(CloudAppsSubclient):
    &#34;&#34;&#34;Derived class from CloudAppsSubclient Base class, representing a GMail/GDrive/OneDrive subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of File System subclient..&#34;&#34;&#34;
        super(GoogleSubclient, self)._get_subclient_properties()
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

        content = []
        group_list = []

        for account in self._content:
            temp_account = account[&#34;cloudconnectorContent&#34;][&#34;includeAccounts&#34;]

            if temp_account[&#39;contentType&#39;] == AppIDAType.CLOUD_APP.value:
                content_dict = {
                    &#39;SMTPAddress&#39;: temp_account[&#34;contentName&#34;].split(&#34;;&#34;)[0],
                    &#39;display_name&#39;: temp_account[&#34;contentValue&#34;]
                }

                content.append(content_dict)
            if temp_account[&#39;contentType&#39;] == 135:
                group_list.append(temp_account[&#34;contentName&#34;])
        self._ca_content = content
        self._ca_groups = group_list

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;

        return {&#39;subClientProperties&#39;: self._subclient_properties}

    @property
    def content(self):
        &#34;&#34;&#34;Returns the subclient content dict&#34;&#34;&#34;
        return self._ca_content

    @property
    def groups(self):
        &#34;&#34;&#34;Returns the list of groups assigned to the subclient if any.
        Groups can be azure AD group or Google groups.
        Groups are assigned only if auto discovery is enabled for groups.

            Returns:

                list - list of groups associated with the subclient

        &#34;&#34;&#34;
        return self._ca_groups

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update content of a
            Cloud Apps Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        content = []

        try:
            for account in subclient_content:
                temp_content_dict = {
                    &#34;cloudconnectorContent&#34;: {
                        &#34;includeAccounts&#34;: {
                            &#34;contentValue&#34;: account[&#39;display_name&#39;],
                            &#34;contentType&#34;: AppIDAType.CLOUD_APP.value,
                            &#34;contentName&#34;: account[&#39;SMTPAddress&#39;]
                        }
                    }
                }

                content.append(temp_content_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        self._set_subclient_properties(&#34;_content&#34;, content)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            to_disk=False):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_disk             (bool)       --  If True, restore to disk will be performed

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity

        return self._instance_object.restore_out_of_place(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            to_disk=to_disk
        )

    def discover(self, discover_type=&#39;USERS&#39;):
        &#34;&#34;&#34;This method discovers the users/groups on Google GSuite Account/OneDrive

                Args:

                    discover_type (str)  --  Type of discovery

                        Valid Values are

                        -   USERS
                        -   GROUPS

                        Default: USERS

                Returns:

                    List (list)  --  List of users on GSuite account

                Raises:
                    SDKException:
                        if response is empty

                        if response is not success


        &#34;&#34;&#34;

        if discover_type.upper() == &#39;USERS&#39;:
            disc_type = 10
        elif discover_type.upper() == &#39;GROUPS&#39;:
            disc_type = 5
        _get_users = self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self._instance_object.instance_id,
                                                              self._client_object.client_id,
                                                              disc_type)

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _get_users)

        if flag:
            if response.json() and &#34;scDiscoveryContent&#34; in response.json():
                self._discover_properties = response.json()[
                    &#34;scDiscoveryContent&#34;][0]

                if &#34;contentInfo&#34; in self._discover_properties:
                    self._contentInfo = self._discover_properties[&#34;contentInfo&#34;]
                return self._contentInfo
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def set_auto_discovery(self, value):
        &#34;&#34;&#34;Sets the auto discovery value for subclient.
        You can either set a RegEx value or a user group,
        depending on the auto discovery type selected at instance level.

            Args:

                value   (list)  --  List of RegEx or user groups

        &#34;&#34;&#34;

        if not isinstance(value, list):
            raise SDKException(&#39;Subclient&#39;, &#39;116&#39;)

        if not self._instance_object.auto_discovery_status:
            raise SDKException(&#39;Subclient&#39;, &#39;117&#39;)

        subclient_prop = self._subclient_properties[&#39;cloudAppsSubClientProp&#39;].copy()
        if self._instance_object.auto_discovery_mode == 0:
            # RegEx based auto discovery is enabled on instance

            if subclient_prop[&#39;instanceType&#39;] == 7:
                subclient_prop[&#39;oneDriveSubclient&#39;][&#39;regularExp&#39;] = value
            else:
                subclient_prop[&#39;GoogleSubclient&#39;][&#39;regularExp&#39;] = value
            self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]&#34;, subclient_prop)
        else:
            # User group based auto discovery is enabled on instance
            grp_list = []
            groups = self.discover(discover_type=&#39;GROUPS&#39;)
            for item in value:
                for group in groups:
                    if group[&#39;contentName&#39;].lower() == item.lower():
                        grp_list.append({
                            &#34;cloudconnectorContent&#34;: {
                                &#34;includeAccounts&#34;: group
                            }
                        })
            self._content.extend(grp_list)
            self._set_subclient_properties(&#34;_subclient_properties[&#39;content&#39;]&#34;, self._content)
        self.refresh()

    def run_subclient_discovery(self):
        &#34;&#34;&#34;
            This method launches AutoDiscovery on the subclient
        &#34;&#34;&#34;

        discover_type = 15
        discover_users = self._services[&#39;GET_CLOUDAPPS_ONEDRIVE_USERS&#39;] % (self._instance_object.instance_id,
                                                                           self._client_object.client_id,
                                                                           discover_type,
                                                                           self.subclient_id)
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_users)
        if response.status_code != 200 and response.status_code != 500:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def add_AD_group(self, value):
        &#34;&#34;&#34;Adds the user group to the subclient if auto discovery type selected
            AD group at instance level.
                Args:
                    value   (list)  --  List of user groups
        &#34;&#34;&#34;
        grp_list = []
        groups = self.discover(discover_type=&#39;GROUPS&#39;)
        for item in value:
            for group in groups:
                if group[&#39;contentName&#39;].lower() == item.lower():
                    grp_list.append(group)

        contentinfo = []

        for grp in grp_list:
            info = {
                &#34;contentValue&#34;: grp[&#39;contentValue&#39;],
                &#34;contentType&#34;: grp[&#39;contentType&#39;],
                &#34;contentName&#34;: grp[&#39;contentName&#39;]
            }
            contentinfo.append(info)

        request_json = {
            &#34;App_DiscoveryContent&#34;: {
                &#34;scDiscoveryContent&#34;: [
                    {
                        &#34;scEntity&#34;: {
                            &#34;subclientId&#34;: self.subclient_id
                        },
                        &#34;contentInfo&#34;: contentinfo
                    }
                ]
            }
        }
        add_ADgroup = self._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, add_ADgroup, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_user(self, user_name):
        &#34;&#34;&#34;This method adds one drive user to the subclient
                Args:
                    user_name   (str)  --  Onedrive user name
        &#34;&#34;&#34;
        users = self.discover(discover_type=&#39;USERS&#39;)

        for user in users:
            if user[&#39;contentName&#39;].lower() == user_name.lower():
                user_dict = user
                break

        request_json = {
            &#34;App_DiscoveryContent&#34;: {
                &#34;scDiscoveryContent&#34;: [
                    {
                        &#34;scEntity&#34;: {
                            &#34;subclientId&#34;: self.subclient_id
                        },
                        &#34;contentInfo&#34;: [
                            {
                                &#34;contentValue&#34;: user_dict[&#39;contentValue&#39;],
                                &#34;contentType&#34;: user_dict[&#39;contentType&#39;],
                                &#34;contentName&#34;: user_dict[&#39;contentName&#39;]
                            }
                        ]
                    }
                ]
            }
        }

        add_user = self._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, add_user, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = &#39;Failed to user to the subclient\nError: &#34;{0}&#34;&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_subclient_users(self):
        &#34;&#34;&#34;Method to get the users in the subclient

            Returns:
                List of Users in subclient
        &#34;&#34;&#34;
        users = []
        result = self.content
        for user in result:
            users.append(user[&#39;SMTPAddress&#39;])
        return users

    @property
    def get_subclient_users(self):
        &#34;&#34;&#34;Returns the users in subclient&#34;&#34;&#34;
        return self._get_subclient_users()

    def add_users(self, users, plan_name):
        &#34;&#34;&#34; Adds given OneDrive users to v2 client

            Args:

                users (list) : List of user&#39;s SMTP address

                plan_name (str) : Google Workspace plan name to associate with users

            Raises:

                SDKException:

                    if response is not success

                    if response is returned with errors
        &#34;&#34;&#34;

        if not (isinstance(users, list) and isinstance(plan_name, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Get GoogleWorkspace plan
        plan_name = plan_name.strip()
        google_plan_object = self._commcell_object.plans.get(plan_name)
        google_plan_id = int(google_plan_object.plan_id)

        # Get client ID
        client_id = int(self._client_object.client_id)

        user_accounts = []

        for user_id in users:
            user_accounts.append(self.search_for_user(user_id))

        request_json = {
            &#34;LaunchAutoDiscovery&#34;: False,
            &#34;cloudAppAssociation&#34;: {
                &#34;accountStatus&#34;: 0,
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: client_id,
                    &#34;instanceId&#34;: int(self._instance_object.instance_id),
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    # GDrive : 24 | GMail : 22
                    &#34;discoverByType&#34;: 22 if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else 24,
                    &#34;userAccounts&#34;: user_accounts
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: google_plan_id
                }
            }
        }

        user_associations = self._services[
            &#39;GMAIL_UPDATE_USERS&#39;] if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else self._services[
            &#39;GDRIVE_UPDATE_USERS&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, user_associations, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add user\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_shared_drives(self, shared_drives, plan_name):
        &#34;&#34;&#34; Adds given SharedDrives to client

            Args:

                shared_drives (list) : List of SharedDrives

                plan_name (str) : Google Workspace plan name to associate with users

            Raises:

                SDKException:

                    if response is not success

                    if response is returned with errors
        &#34;&#34;&#34;

        if not (isinstance(shared_drives, list) and isinstance(plan_name, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Get GoogleWorkspace plan
        plan_name = plan_name.strip()
        google_plan_object = self._commcell_object.plans.get(plan_name)
        google_plan_id = int(google_plan_object.plan_id)

        # Get client ID
        client_id = int(self._client_object.client_id)

        drives = []

        for drive in shared_drives:
            response = self.search_for_shareddrive(drive)
            response[&#39;user&#39;] = {}
            response[&#39;displayName&#39;] = response[&#39;folderTitle&#39;]
            response[&#39;user&#39;][&#39;userGUID&#39;] = response[&#39;folderId&#39;]
            response[&#39;isAutoDiscoveredUser&#39;] = False
            drives.append(response)

        request_json = {
            &#34;LaunchAutoDiscovery&#34;: False,
            &#34;cloudAppAssociation&#34;: {
                &#34;accountStatus&#34;: 0,
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: client_id,
                    &#34;instanceId&#34;: int(self._instance_object.instance_id),
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 32,
                    &#34;userAccounts&#34;: drives
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: google_plan_id
                }
            }
        }

        user_associations = self._services[&#39;GDRIVE_UPDATE_USERS&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, user_associations, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    output_string = f&#39;Failed to add user\nError: {error_message}&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def browse_content(self, discovery_type):
        &#34;&#34;&#34; Fetches discovered content based on discovery type
            Args:
                discovery_type: Type of content to be discovered.
                    discovery_type=8 - Users
                    discovery_type=25 - Shared Drives
                    discovery_type=5 - Groups

            Returns:
                    records (list):  content fetched, [] if no content fetched

            Raises:

                 SDKException:

                        if response is not success
        &#34;&#34;&#34;
        # Wait for sometime unitl disco discovery completes before checking actual content.
        attempt = 0
        while attempt &lt; 5:
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, (
                    self._services[&#39;GOOGLE_DISCOVERY_OVERVIEW&#39;] % (self._backupset_object.backupset_id)))
            if response.json()[&#39;office365ClientOverview&#39;][&#39;summary&#39;][&#39;discoverState&#39;][&#39;discoveryProgress&#39;] == 100:
                break
            attempt += 1
            time.sleep(10)

        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        # determines the number of accounts to return in response
        page_size = 500
        offset = 0

        records = []
        while True:
            discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&amp;offset={offset}&amp;eDiscoverType={discovery_type}&#39;
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_query)
            offset += 1
            if flag:
                if response and response.json():
                    if discovery_type == 8:
                        if &#39;userAccounts&#39; in response.json():
                            curr_records = response.json().get(&#39;userAccounts&#39;, [])
                            records.extend(curr_records)
                            if len(curr_records) &lt; page_size:
                                break
                    elif discovery_type == 25:
                        if &#39;folders&#39; in response.json():
                            curr_records = response.json().get(&#39;folders&#39;, [])
                            records.extend(curr_records)
                            if len(curr_records) &lt; page_size:
                                break
                    elif discovery_type == 5:
                        if &#39;groups&#39; in response.json():
                            curr_groups = response.json().get(&#39;groups&#39;, [])
                            records.extend(curr_groups)
                            if len(curr_groups) &lt; page_size:
                                break
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return records

    def verify_groups_discovery(self):
        &#34;&#34;&#34; Verifies that groups discovery is complete

            Returns:

                discovery_stats (tuple):

                    discovery_status (bool): True if users are discovered else returns False

                    user_accounts (int):     List of users fetched, returns [] if discovery is not complete

            Raises:

                 SDKException:

                        if response is not success

                        if response received does not contain pagining info
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        # determines the number of accounts to return in response
        page_size = 500
        offset = 0

        groups = []
        while True:
            discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&amp;offset={offset}&amp;eDiscoverType=5&#39;
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_query)
            offset += 1

            if flag:
                if response and response.json():
                    if &#39;groups&#39; in response.json():
                        curr_groups = response.json().get(&#39;groups&#39;, [])
                        groups.extend(curr_groups)
                        if len(curr_groups) &lt; page_size:
                            break
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        return groups

    def verify_shareddrive_discovery(self):
        &#34;&#34;&#34; Verifies all shared drives discovery completed.

                    Returns:

                        discovery_stats (tuple):

                            discovery_status (bool): True if users are discovered else returns False

                            user_accounts (int):     List of shared drives fetched, returns [] if discovery is not complete

                    Raises:

                         SDKException:

                                if response is not success

                                if response received does not contain pagining info
                &#34;&#34;&#34;

        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        # determines the number of accounts to return in response
        page_size = 500
        discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&amp;eDiscoverType=25&#39;  # for shared drive discovery

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_query)

        if flag:
            no_of_records = -1
            if response and response.json():
                if &#39;pagingInfo&#39; in response.json():
                    no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                    if no_of_records &gt; 0:
                        shared_drives = response.json().get(&#39;folders&#39;, [])
                        return True, shared_drives
            return False, []
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _association_users_json(self, users_list):
        &#34;&#34;&#34;
            Args:
                users_list (list) : list of SMTP addresses of users
            Returns:
                users_json(list): Required details of users to backup
        &#34;&#34;&#34;
        users_json = []
        for user_smtp in users_list:
            user_details = self.search_for_user(user_smtp)
            user_info = {
                &#34;user&#34;: {
                    &#34;userGUID&#34;: user_details.get(&#39;user&#39;, {}).get(&#39;userGUID&#39;)
                }
            }
            users_json.append(user_info)
        return users_json

    def _task_json_for_google_backup(self, is_mailbox, users_list=None, **kwargs):
        &#34;&#34;&#34;
        Json for google backup for selected users

        Args:
                is_mailbox (boolean) : used to determine Gmail or GDrive
                users_list (list) : list of SMTP addresses of users
                **kwargs (dict) : contains some optional fields like full_backup, etc
                        Ex: **{
                            &#39;full_backup&#39;(boolean): Flag whether to run full backup or not.
                        }
        Returns:
                task_json(dict): Task request used as backup payload
        &#34;&#34;&#34;

        selected_items = []
        advanced_options_dict = None
        if users_list is not None:
            associated_users_json = self._association_users_json(users_list)
            advanced_options_dict = {
                &#39;cloudAppOptions&#39;: {
                    &#39;userAccounts&#39;: associated_users_json
                }
            }

            for user_smtp in users_list:
                details = self.search_for_user(user_smtp)
                item = {
                    &#34;itemName&#34;: details.get(&#39;displayName&#39;),
                    &#34;itemType&#34;: &#34;Mailbox&#34; if is_mailbox else &#34;User&#34;
                }
                selected_items.append(item)
        else:
            item = {
                &#34;itemName&#34;: &#34;All mailboxes&#34; if is_mailbox else &#34;All users&#34;,
                &#34;itemType&#34;: &#34;All mailboxes&#34; if is_mailbox else &#34;All users&#34;
            }
            selected_items.append(item)

        common_options_dict = {
            &#34;jobMetadata&#34;: [
                {
                    &#34;selectedItems&#34;: selected_items,
                    &#34;jobOptionItems&#34;: [
                        {
                            &#34;option&#34;: &#34;Total running time&#34;,
                            &#34;value&#34;: &#34;Disabled&#34;
                        },
                        {
                            &#34;option&#34;: &#34;Convert job to full&#34;,
                            &#34;value&#34;: &#34;Enabled&#34; if kwargs.get(&#39;full_backup&#39;, False) else &#34;Disabled&#34;
                        }
                    ]
                }
            ]
        }
        task_json = self._backup_json(backup_level=&#39;INCREMENTAL&#39;, incremental_backup=False,
                                      incremental_level=&#39;BEFORE_SYNTH&#39;, advanced_options=advanced_options_dict,
                                      common_backup_options=common_options_dict)
        return task_json

    def run_user_level_backup(self, users_list, is_mailbox, **kwargs):
        &#34;&#34;&#34;
        Runs the backup for the users in users list
        Args:
                users_list (list) : list of SMTP addresses of users

                is_mailbox (boolean) : flag to determine GMail Mailbox or not

                **kwargs (dict) : contains some optional fields like full_backup, etc
                        Ex: **{
                            &#39;full_backup&#39;(boolean): Flag whether to run full backup or not.
                        }

        Returns:
                object - instance of the Job class for this backup job

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        task_json = self._task_json_for_google_backup(is_mailbox, users_list=users_list, **kwargs)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def run_client_level_backup(self, is_mailbox, **kwargs):
        &#34;&#34;&#34;
                Runs the backup for the client
                Args:
                        is_mailbox (boolean) : flag to determine GMail Mailbox or not
                        **kwargs (dict) : contains some optional fields like full_backup, etc
                        Ex: **{
                            &#39;full_backup&#39;(boolean): Flag whether to run full backup or not.
                        }

                Returns:
                        object - instance of the Job class for this backup job

                Raises:
                    SDKException:
                        if response is empty

                        if response is not success

                &#34;&#34;&#34;
        task_json = self._task_json_for_google_backup(is_mailbox, **kwargs)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def search_for_user(self, user_id):
        &#34;&#34;&#34; Searches for a specific user&#39;s details from discovered list

            Args:
                user_id (str) : user&#39;s SMTP address

            Returns:

                user (list): user details&#39; list fetched from discovered content
                              eg: {
                                        &#39;displayName&#39;: &#39;&#39;,
                                        &#39;smtpAddress&#39;: &#39;&#39;,
                                        &#39;isSuperAdmin&#39;: False,
                                        &#39;isAutoDiscoveredUser&#39;: False,
                                        &#39;commonFlags&#39;: 0,
                                        &#39;user&#39;: {
                                            &#39;_type_&#39;: 13,
                                             &#39;userGUID&#39;: &#39;UserGuid&#39;
                                        }
                                 }

            Raises:

                SDKException:

                    if discovery is not complete

                    if invalid SMTP address is passed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        search_query = f&#39;{browse_content}&amp;search={user_id}&#39;

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

        if flag:
            if response and response.json():
                if &#39;userAccounts&#39; in response.json():
                    user_accounts = response.json().get(&#39;userAccounts&#39;, [])
                    if len(user_accounts) == 0:
                        error_string = &#39;Either discovery is not complete or user is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                    for user in user_accounts:
                        if user[&#39;smtpAddress&#39;] == user_id:
                            return user
                    else:
                        error_string = &#39;User is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Check if the user provided is valid&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def search_for_shareddrive(self, drive):
        &#34;&#34;&#34; Searches for a specific shared drive details from discovered list

            Args:
                drive (str) : Shared Drive iD

            Returns:

                drive (list): shared drive details&#39; list fetched from discovered content
                              eg: {
                                        &#39;folderTitle&#39;: &#39;&#39;,
                                        &#39;folderId&#39;:&#39;&#39;,
                                        &#39;user&#39;: {
                                             &#39;userGUID&#39;: &#39;UserGuid&#39;
                                        }
                                 }

            Raises:

                SDKException:

                    if discovery is not complete

                    if invalid SMTP address is passed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                               self._client_object.client_id,
                                                               AppIDAType.CLOUD_APP.value))

        search_query = f&#39;{browse_content}&amp;search={drive}&amp;eDiscoverType=25&#39;

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

        if flag:
            if response and response.json():
                if &#39;folders&#39; in response.json():
                    folders = response.json().get(&#39;folders&#39;, [])
                    if len(folders) == 0:
                        error_string = &#39;Either discovery is not complete or Shared Drive is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                    for folder in folders:
                        if folder[&#39;folderTitle&#39;] == drive:
                            return folder
                    else:
                        error_string = &#39;Shared Drive is not available in discovered data&#39;
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Check if the Shared Drive provided is valid&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def disk_restore(self, users, destination_client, destination_path, skip_file_permissions=False):
        &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                destination_client (str) : client where the users need to be restored
                destination_path (str) : Destination folder location
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

            Returns:
                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;disk_restore&#39;: True,
            &#39;destination_path&#39;: destination_path,
            &#39;destination_client&#39;: destination_client,
            &#39;skip_file_permissions&#39;: skip_file_permissions
        }
        restore_json = self._instance_object._prepare_restore_json(source_user_list, **kwargs)
        return self._process_restore_response(restore_json)

    def out_of_place_restore(self, users, destination_path, **kwargs):
        &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) : list of SMTP addresses of users
                destination_path (str) : SMTP address of destination user
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
                    destination_type (str) : type of destination for OOP Restore
                    end_time (int) : The job end time for Point In Time restore (default: None)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)
        end_time = kwargs.get(&#39;end_time&#39;, None)

        if overwrite and restore_as_copy:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        accountInfo = {}
        destination_type = kwargs.get(&#34;destination_type&#34;)
        if  destination_type == &#39;USER&#39;:
            destination_user_info = self.search_for_user(destination_path)
            accountInfo[&#39;userDisplayName&#39;] = destination_user_info.get(&#39;displayName&#39;, &#39;&#39;)
            accountInfo[&#39;userGUID&#39;] = destination_user_info.get(&#39;user&#39;).get(&#39;userGUID&#39;, &#39;&#39;)
            accountInfo[&#39;userSMTP&#39;] = destination_user_info.get(&#39;smtpAddress&#39;, &#39;&#39;)
        else:
            destination_user_info = self.search_for_shareddrive(destination_path)
            accountInfo[&#39;userDisplayName&#39;] = destination_user_info.get(&#39;folderTitle&#39;, &#39;&#39;)
            accountInfo[&#39;userGUID&#39;] = destination_user_info.get(&#39;folderId&#39;, &#39;&#39;)

        kwargs = {
            &#39;out_of_place&#39;: True,
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions,
            &#39;accountInfo&#39;: accountInfo,
            &#39;destination_type&#39;: destination_type,
            &#39;destination_path&#39;: destination_path,
        }
        restore_json = self._instance_object._prepare_restore_json(source_user_list, **kwargs)
        if end_time:
            adv_search_bkp_time_dict = {
                &#34;field&#34;: &#34;BACKUPTIME&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;0&#34;,
                        str(end_time)
                    ]
                },
                &#34;intraFieldOp&#34;: &#34;FTOr&#34;
            }

            add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
            add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
            add_backup_time = \
                restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
                    &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
            add_backup_time.append(adv_search_bkp_time_dict)
        return self._process_restore_response(restore_json)

    def in_place_restore(self, users, **kwargs):
        &#34;&#34;&#34; Runs an in-place restore job for specified users on OneDrive for business client
            By default restore skips the files already present in destination

            Args:
                users (list) :  List of SMTP addresses of users
                **kwargs (dict) : Additional parameters
                    overwrite (bool) : unconditional overwrite files during restore (default: False)
                    restore_as_copy (bool) : restore files as copy during restore (default: False)
                    skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
                    include_deleted_items (bool) : If True, Deleted items are also included in restore (default: False)
                    end_time (int) : The job end time for Point In Time restore (default: None)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if overwrite and restore as copy file options are both selected
        &#34;&#34;&#34;
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)
        end_time = kwargs.get(&#39;end_time&#39;, None)

        if overwrite and restore_as_copy:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

        self._instance_object._restore_association = self._subClientEntity
        source_user_list = self._get_user_guids(users)
        kwargs = {
            &#39;overwrite&#39;: overwrite,
            &#39;restore_as_copy&#39;: restore_as_copy,
            &#39;skip_file_permissions&#39;: skip_file_permissions,
            &#39;include_deleted_items&#39;: include_deleted_items
        }
        restore_json = self._instance_object._prepare_restore_json(source_user_list, **kwargs)
        if end_time:
            adv_search_bkp_time_dict = {
                &#34;field&#34;: &#34;BACKUPTIME&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;0&#34;,
                        str(end_time)
                    ]
                },
                &#34;intraFieldOp&#34;: &#34;FTOr&#34;
            }

            add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
            add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
            add_backup_time = \
                restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
                    &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
            add_backup_time.append(adv_search_bkp_time_dict)

        return self._process_restore_response(restore_json)

    def _get_user_guids(self, users):
        &#34;&#34;&#34; Retrieve GUIDs for users specified

            Args:
                user (list) : List of SMTP addresses of users

            Returns:
                user_guid_list (list) : list of GUIDs of specified users

            Raises:
                SDKException:
                    if user details couldn&#39;t be found in discovered data
        &#34;&#34;&#34;
        user_guid_list = []
        for user_id in users:
            try:
                user = self.search_for_user(user_id)
                if len(user) != 0 and user.get(&#39;user&#39;, {}).get(&#39;userGUID&#39;) is not None:
                    user_guid_list.append(user.get(&#39;user&#39;).get(&#39;userGUID&#39;))
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;User details not found in discovered data&#39;)
            except SDKException:
                user = self.search_for_shareddrive(user_id)
                if len(user) != 0 and user.get(&#39;folderId&#39;) is not None:
                    user_guid_list.append(user.get(&#39;folderId&#39;))
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;User details not found in discovered data&#39;)
        return user_guid_list

    def process_index_retention_rules(self, index_app_type_id, index_server_client_name):
        &#34;&#34;&#34;
         Makes API call to process index retention rules

         Args:

            index_app_type_id           (int)   --   index app type id

            index_server_client_name    (str)   --  client name of index server

         Raises:

                SDKException:

                    if index server not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if self._commcell_object.clients.has_client(index_server_client_name):
            index_server_client_id = int(self._commcell_object.clients[index_server_client_name.lower()][&#39;id&#39;])
            request_json = {
                &#34;appType&#34;: index_app_type_id,
                &#34;indexServerClientId&#34;: index_server_client_id
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                            o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
    
    def refresh_retention_stats(self):
        &#34;&#34;&#34;
        refresh the retention stats for the client
        &#34;&#34;&#34;
        request_json = {
            &#34;appType&#34;: constants.GMAIL_INDEX_APP_TYPE if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else constants.GDRIVE_INDEX_APP_TYPE,
            &#34;subclientId&#34;: int(self.subclient_id)
        }
        refresh_retention = self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, refresh_retention, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                                self._update_response_(response.text))

    def refresh_stats_status(self, user_level):
        &#34;&#34;&#34;
        refresh the client level or user level stats for the client
            Args:
                 user_level (bool) :  Option to refresh client level or user level stats
        &#34;&#34;&#34;
        if self._instance_object.ca_instance_type == &#39;GMAIL&#39;:
            request_json = {
                &#34;appType&#34;: constants.GMAIL_INDEX_APP_TYPE,
                &#34;gmailIdxStatsReq&#34;:
                    [{
                        &#34;subclientId&#34;: int(self.subclient_id), &#34;type&#34;: 1 if user_level else 0}]
            }
        else:
            request_json = {
                &#34;appType&#34;: constants.GDRIVE_INDEX_APP_TYPE,
                &#34;googleDriveIdxStatsReq&#34;:
                    [{
                        &#34;subclientId&#34;: int(self.subclient_id), &#34;type&#34;: 1 if user_level else 0}]
            }
        refresh_backup_stats = self._services[&#39;OFFICE365_POPULATE_INDEX_STATS&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, refresh_backup_stats, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

    def get_client_level_stats(self):
        &#34;&#34;&#34;
        Returns the client level stats for the client

        Retruns:

            response(json)                : returns the client level stats as a json response
        &#34;&#34;&#34;
        get_backup_stats = self._services[&#39;OFFICE365_OVERVIEW_STATS&#39;] % self._backupset_object.backupset_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, get_backup_stats)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

        return response.json()

    def get_user_level_stats(self):
        &#34;&#34;&#34;
        Returns the user level stats

        Retruns:

            response(json)                : returns the entity level stats as a json response
        &#34;&#34;&#34;
        request_json = {
            &#34;bIncludeDeleted&#34;: False,
            &#34;pagingInfo&#34;: {
                &#34;pageNumber&#34;: 0,
                &#34;pageSize&#34;: 100
            },
            &#34;discoverByType&#34;: constants.GMAIL_DISCOVERY_TYPE if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else constants.GDRIVE_DISCOVERY_TYPE,
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
                }
            }
        }
        if self._instance_object.ca_instance_type == &#39;GMAIL&#39;:
            get_backup_stats = self._services[&#39;GMAIL_GET_USERS&#39;]
        else:
            get_backup_stats = self._services[&#39;GDRIVE_GET_USERS&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, get_backup_stats, request_json)

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._update_response_(response.text))

        return response.json()</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="../casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Returns the subclient content dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L134-L137" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Returns the subclient content dict&#34;&#34;&#34;
    return self._ca_content</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.get_subclient_users"><code class="name">var <span class="ident">get_subclient_users</span></code></dt>
<dd>
<div class="desc"><p>Returns the users in subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L471-L474" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def get_subclient_users(self):
    &#34;&#34;&#34;Returns the users in subclient&#34;&#34;&#34;
    return self._get_subclient_users()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.groups"><code class="name">var <span class="ident">groups</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of groups assigned to the subclient if any.
Groups can be azure AD group or Google groups.
Groups are assigned only if auto discovery is enabled for groups.</p>
<pre><code>Returns:

    list - list of groups associated with the subclient
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L139-L150" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def groups(self):
    &#34;&#34;&#34;Returns the list of groups assigned to the subclient if any.
    Groups can be azure AD group or Google groups.
    Groups are assigned only if auto discovery is enabled for groups.

        Returns:

            list - list of groups associated with the subclient

    &#34;&#34;&#34;
    return self._ca_groups</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_AD_group"><code class="name flex">
<span>def <span class="ident">add_AD_group</span></span>(<span>self, value)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the user group to the subclient if auto discovery type selected
AD group at instance level.
Args:
value
(list)
&ndash;
List of user groups</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L365-L411" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_AD_group(self, value):
    &#34;&#34;&#34;Adds the user group to the subclient if auto discovery type selected
        AD group at instance level.
            Args:
                value   (list)  --  List of user groups
    &#34;&#34;&#34;
    grp_list = []
    groups = self.discover(discover_type=&#39;GROUPS&#39;)
    for item in value:
        for group in groups:
            if group[&#39;contentName&#39;].lower() == item.lower():
                grp_list.append(group)

    contentinfo = []

    for grp in grp_list:
        info = {
            &#34;contentValue&#34;: grp[&#39;contentValue&#39;],
            &#34;contentType&#34;: grp[&#39;contentType&#39;],
            &#34;contentName&#34;: grp[&#39;contentName&#39;]
        }
        contentinfo.append(info)

    request_json = {
        &#34;App_DiscoveryContent&#34;: {
            &#34;scDiscoveryContent&#34;: [
                {
                    &#34;scEntity&#34;: {
                        &#34;subclientId&#34;: self.subclient_id
                    },
                    &#34;contentInfo&#34;: contentinfo
                }
            ]
        }
    }
    add_ADgroup = self._services[&#39;EXECUTE_QCOMMAND&#39;]
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, add_ADgroup, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_shared_drives"><code class="name flex">
<span>def <span class="ident">add_shared_drives</span></span>(<span>self, shared_drives, plan_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds given SharedDrives to client</p>
<h2 id="args">Args</h2>
<p>shared_drives (list) : List of SharedDrives</p>
<p>plan_name (str) : Google Workspace plan name to associate with users</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is not success

if response is returned with errors
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L546-L616" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_shared_drives(self, shared_drives, plan_name):
    &#34;&#34;&#34; Adds given SharedDrives to client

        Args:

            shared_drives (list) : List of SharedDrives

            plan_name (str) : Google Workspace plan name to associate with users

        Raises:

            SDKException:

                if response is not success

                if response is returned with errors
    &#34;&#34;&#34;

    if not (isinstance(shared_drives, list) and isinstance(plan_name, str)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    # Get GoogleWorkspace plan
    plan_name = plan_name.strip()
    google_plan_object = self._commcell_object.plans.get(plan_name)
    google_plan_id = int(google_plan_object.plan_id)

    # Get client ID
    client_id = int(self._client_object.client_id)

    drives = []

    for drive in shared_drives:
        response = self.search_for_shareddrive(drive)
        response[&#39;user&#39;] = {}
        response[&#39;displayName&#39;] = response[&#39;folderTitle&#39;]
        response[&#39;user&#39;][&#39;userGUID&#39;] = response[&#39;folderId&#39;]
        response[&#39;isAutoDiscoveredUser&#39;] = False
        drives.append(response)

    request_json = {
        &#34;LaunchAutoDiscovery&#34;: False,
        &#34;cloudAppAssociation&#34;: {
            &#34;accountStatus&#34;: 0,
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: int(self.subclient_id),
                &#34;clientId&#34;: client_id,
                &#34;instanceId&#34;: int(self._instance_object.instance_id),
                &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
            },
            &#34;cloudAppDiscoverinfo&#34;: {
                &#34;discoverByType&#34;: 32,
                &#34;userAccounts&#34;: drives
            },
            &#34;plan&#34;: {
                &#34;planId&#34;: google_plan_id
            }
        }
    }

    user_associations = self._services[&#39;GDRIVE_UPDATE_USERS&#39;]
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, user_associations, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to add user\nError: {error_message}&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_user"><code class="name flex">
<span>def <span class="ident">add_user</span></span>(<span>self, user_name)</span>
</code></dt>
<dd>
<div class="desc"><p>This method adds one drive user to the subclient</p>
<h2 id="args">Args</h2>
<p>user_name
(str)
&ndash;
Onedrive user name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L413-L457" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_user(self, user_name):
    &#34;&#34;&#34;This method adds one drive user to the subclient
            Args:
                user_name   (str)  --  Onedrive user name
    &#34;&#34;&#34;
    users = self.discover(discover_type=&#39;USERS&#39;)

    for user in users:
        if user[&#39;contentName&#39;].lower() == user_name.lower():
            user_dict = user
            break

    request_json = {
        &#34;App_DiscoveryContent&#34;: {
            &#34;scDiscoveryContent&#34;: [
                {
                    &#34;scEntity&#34;: {
                        &#34;subclientId&#34;: self.subclient_id
                    },
                    &#34;contentInfo&#34;: [
                        {
                            &#34;contentValue&#34;: user_dict[&#39;contentValue&#39;],
                            &#34;contentType&#34;: user_dict[&#39;contentType&#39;],
                            &#34;contentName&#34;: user_dict[&#39;contentName&#39;]
                        }
                    ]
                }
            ]
        }
    }

    add_user = self._services[&#39;EXECUTE_QCOMMAND&#39;]
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, add_user, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = &#39;Failed to user to the subclient\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_users"><code class="name flex">
<span>def <span class="ident">add_users</span></span>(<span>self, users, plan_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds given OneDrive users to v2 client</p>
<h2 id="args">Args</h2>
<p>users (list) : List of user's SMTP address</p>
<p>plan_name (str) : Google Workspace plan name to associate with users</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is not success

if response is returned with errors
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L476-L544" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_users(self, users, plan_name):
    &#34;&#34;&#34; Adds given OneDrive users to v2 client

        Args:

            users (list) : List of user&#39;s SMTP address

            plan_name (str) : Google Workspace plan name to associate with users

        Raises:

            SDKException:

                if response is not success

                if response is returned with errors
    &#34;&#34;&#34;

    if not (isinstance(users, list) and isinstance(plan_name, str)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    # Get GoogleWorkspace plan
    plan_name = plan_name.strip()
    google_plan_object = self._commcell_object.plans.get(plan_name)
    google_plan_id = int(google_plan_object.plan_id)

    # Get client ID
    client_id = int(self._client_object.client_id)

    user_accounts = []

    for user_id in users:
        user_accounts.append(self.search_for_user(user_id))

    request_json = {
        &#34;LaunchAutoDiscovery&#34;: False,
        &#34;cloudAppAssociation&#34;: {
            &#34;accountStatus&#34;: 0,
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: int(self.subclient_id),
                &#34;clientId&#34;: client_id,
                &#34;instanceId&#34;: int(self._instance_object.instance_id),
                &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
            },
            &#34;cloudAppDiscoverinfo&#34;: {
                # GDrive : 24 | GMail : 22
                &#34;discoverByType&#34;: 22 if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else 24,
                &#34;userAccounts&#34;: user_accounts
            },
            &#34;plan&#34;: {
                &#34;planId&#34;: google_plan_id
            }
        }
    }

    user_associations = self._services[
        &#39;GMAIL_UPDATE_USERS&#39;] if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else self._services[
        &#39;GDRIVE_UPDATE_USERS&#39;]
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, user_associations, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                output_string = f&#39;Failed to add user\nError: {error_message}&#39;
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, output_string)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.browse_content"><code class="name flex">
<span>def <span class="ident">browse_content</span></span>(<span>self, discovery_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Fetches discovered content based on discovery type</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>discovery_type</code></strong></dt>
<dd>Type of content to be discovered.
discovery_type=8 - Users
discovery_type=25 - Shared Drives
discovery_type=5 - Groups</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>records (list):
content fetched, [] if no content fetched</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>   if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L618-L680" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse_content(self, discovery_type):
    &#34;&#34;&#34; Fetches discovered content based on discovery type
        Args:
            discovery_type: Type of content to be discovered.
                discovery_type=8 - Users
                discovery_type=25 - Shared Drives
                discovery_type=5 - Groups

        Returns:
                records (list):  content fetched, [] if no content fetched

        Raises:

             SDKException:

                    if response is not success
    &#34;&#34;&#34;
    # Wait for sometime unitl disco discovery completes before checking actual content.
    attempt = 0
    while attempt &lt; 5:
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, (
                self._services[&#39;GOOGLE_DISCOVERY_OVERVIEW&#39;] % (self._backupset_object.backupset_id)))
        if response.json()[&#39;office365ClientOverview&#39;][&#39;summary&#39;][&#39;discoverState&#39;][&#39;discoveryProgress&#39;] == 100:
            break
        attempt += 1
        time.sleep(10)

    browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                           self._client_object.client_id,
                                                           AppIDAType.CLOUD_APP.value))

    # determines the number of accounts to return in response
    page_size = 500
    offset = 0

    records = []
    while True:
        discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&amp;offset={offset}&amp;eDiscoverType={discovery_type}&#39;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_query)
        offset += 1
        if flag:
            if response and response.json():
                if discovery_type == 8:
                    if &#39;userAccounts&#39; in response.json():
                        curr_records = response.json().get(&#39;userAccounts&#39;, [])
                        records.extend(curr_records)
                        if len(curr_records) &lt; page_size:
                            break
                elif discovery_type == 25:
                    if &#39;folders&#39; in response.json():
                        curr_records = response.json().get(&#39;folders&#39;, [])
                        records.extend(curr_records)
                        if len(curr_records) &lt; page_size:
                            break
                elif discovery_type == 5:
                    if &#39;groups&#39; in response.json():
                        curr_groups = response.json().get(&#39;groups&#39;, [])
                        records.extend(curr_groups)
                        if len(curr_groups) &lt; page_size:
                            break
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    return records</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.discover"><code class="name flex">
<span>def <span class="ident">discover</span></span>(<span>self, discover_type='USERS')</span>
</code></dt>
<dd>
<div class="desc"><p>This method discovers the users/groups on Google GSuite Account/OneDrive</p>
<h2 id="args">Args</h2>
<p>discover_type (str)
&ndash;
Type of discovery</p>
<pre><code>Valid Values are

-   USERS
-   GROUPS

Default: USERS
</code></pre>
<h2 id="returns">Returns</h2>
<p>List (list)
&ndash;
List of users on GSuite account</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L259-L307" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def discover(self, discover_type=&#39;USERS&#39;):
    &#34;&#34;&#34;This method discovers the users/groups on Google GSuite Account/OneDrive

            Args:

                discover_type (str)  --  Type of discovery

                    Valid Values are

                    -   USERS
                    -   GROUPS

                    Default: USERS

            Returns:

                List (list)  --  List of users on GSuite account

            Raises:
                SDKException:
                    if response is empty

                    if response is not success


    &#34;&#34;&#34;

    if discover_type.upper() == &#39;USERS&#39;:
        disc_type = 10
    elif discover_type.upper() == &#39;GROUPS&#39;:
        disc_type = 5
    _get_users = self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (self._instance_object.instance_id,
                                                          self._client_object.client_id,
                                                          disc_type)

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _get_users)

    if flag:
        if response.json() and &#34;scDiscoveryContent&#34; in response.json():
            self._discover_properties = response.json()[
                &#34;scDiscoveryContent&#34;][0]

            if &#34;contentInfo&#34; in self._discover_properties:
                self._contentInfo = self._discover_properties[&#34;contentInfo&#34;]
            return self._contentInfo
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.disk_restore"><code class="name flex">
<span>def <span class="ident">disk_restore</span></span>(<span>self, users, destination_client, destination_path, skip_file_permissions=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an out-of-place restore job for specified users on OneDrive for business client
By default restore skips the files already present in destination</p>
<h2 id="args">Args</h2>
<p>users (list) : list of SMTP addresses of users
destination_client (str) : client where the users need to be restored
destination_path (str) : Destination folder location
skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L1022-L1044" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disk_restore(self, users, destination_client, destination_path, skip_file_permissions=False):
    &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
        By default restore skips the files already present in destination

        Args:
            users (list) : list of SMTP addresses of users
            destination_client (str) : client where the users need to be restored
            destination_path (str) : Destination folder location
            skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)

        Returns:
            object - instance of the Job class for this restore job
    &#34;&#34;&#34;
    self._instance_object._restore_association = self._subClientEntity
    source_user_list = self._get_user_guids(users)
    kwargs = {
        &#39;disk_restore&#39;: True,
        &#39;destination_path&#39;: destination_path,
        &#39;destination_client&#39;: destination_client,
        &#39;skip_file_permissions&#39;: skip_file_permissions
    }
    restore_json = self._instance_object._prepare_restore_json(source_user_list, **kwargs)
    return self._process_restore_response(restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.get_client_level_stats"><code class="name flex">
<span>def <span class="ident">get_client_level_stats</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the client level stats for the client</p>
<h2 id="retruns">Retruns</h2>
<p>response(json)
: returns the client level stats as a json response</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L1315-L1339" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_client_level_stats(self):
    &#34;&#34;&#34;
    Returns the client level stats for the client

    Retruns:

        response(json)                : returns the client level stats as a json response
    &#34;&#34;&#34;
    get_backup_stats = self._services[&#39;OFFICE365_OVERVIEW_STATS&#39;] % self._backupset_object.backupset_id
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, get_backup_stats)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))

    return response.json()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.get_user_level_stats"><code class="name flex">
<span>def <span class="ident">get_user_level_stats</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the user level stats</p>
<h2 id="retruns">Retruns</h2>
<p>response(json)
: returns the entity level stats as a json response</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L1341-L1382" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_user_level_stats(self):
    &#34;&#34;&#34;
    Returns the user level stats

    Retruns:

        response(json)                : returns the entity level stats as a json response
    &#34;&#34;&#34;
    request_json = {
        &#34;bIncludeDeleted&#34;: False,
        &#34;pagingInfo&#34;: {
            &#34;pageNumber&#34;: 0,
            &#34;pageSize&#34;: 100
        },
        &#34;discoverByType&#34;: constants.GMAIL_DISCOVERY_TYPE if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else constants.GDRIVE_DISCOVERY_TYPE,
        &#34;cloudAppAssociation&#34;: {
            &#34;subclientEntity&#34;: {
                &#34;subclientId&#34;: int(self.subclient_id),
                &#34;applicationId&#34;: AppIDAType.CLOUD_APP.value
            }
        }
    }
    if self._instance_object.ca_instance_type == &#39;GMAIL&#39;:
        get_backup_stats = self._services[&#39;GMAIL_GET_USERS&#39;]
    else:
        get_backup_stats = self._services[&#39;GDRIVE_GET_USERS&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, get_backup_stats, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))

    return response.json()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.in_place_restore"><code class="name flex">
<span>def <span class="ident">in_place_restore</span></span>(<span>self, users, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an in-place restore job for specified users on OneDrive for business client
By default restore skips the files already present in destination</p>
<h2 id="args">Args</h2>
<p>users (list) :
List of SMTP addresses of users
**kwargs (dict) : Additional parameters
overwrite (bool) : unconditional overwrite files during restore (default: False)
restore_as_copy (bool) : restore files as copy during restore (default: False)
skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
include_deleted_items (bool) : If True, Deleted items are also included in restore (default: False)
end_time (int) : The job end time for Point In Time restore (default: None)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if overwrite and restore as copy file options are both selected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L1120-L1178" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def in_place_restore(self, users, **kwargs):
    &#34;&#34;&#34; Runs an in-place restore job for specified users on OneDrive for business client
        By default restore skips the files already present in destination

        Args:
            users (list) :  List of SMTP addresses of users
            **kwargs (dict) : Additional parameters
                overwrite (bool) : unconditional overwrite files during restore (default: False)
                restore_as_copy (bool) : restore files as copy during restore (default: False)
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
                include_deleted_items (bool) : If True, Deleted items are also included in restore (default: False)
                end_time (int) : The job end time for Point In Time restore (default: None)

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:

                if overwrite and restore as copy file options are both selected
    &#34;&#34;&#34;
    overwrite = kwargs.get(&#39;overwrite&#39;, False)
    restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
    skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)
    include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)
    end_time = kwargs.get(&#39;end_time&#39;, None)

    if overwrite and restore_as_copy:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

    self._instance_object._restore_association = self._subClientEntity
    source_user_list = self._get_user_guids(users)
    kwargs = {
        &#39;overwrite&#39;: overwrite,
        &#39;restore_as_copy&#39;: restore_as_copy,
        &#39;skip_file_permissions&#39;: skip_file_permissions,
        &#39;include_deleted_items&#39;: include_deleted_items
    }
    restore_json = self._instance_object._prepare_restore_json(source_user_list, **kwargs)
    if end_time:
        adv_search_bkp_time_dict = {
            &#34;field&#34;: &#34;BACKUPTIME&#34;,
            &#34;fieldValues&#34;: {
                &#34;values&#34;: [
                    &#34;0&#34;,
                    str(end_time)
                ]
            },
            &#34;intraFieldOp&#34;: &#34;FTOr&#34;
        }

        add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
        add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
        add_backup_time = \
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
                &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
        add_backup_time.append(adv_search_bkp_time_dict)

    return self._process_restore_response(restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.out_of_place_restore"><code class="name flex">
<span>def <span class="ident">out_of_place_restore</span></span>(<span>self, users, destination_path, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an out-of-place restore job for specified users on OneDrive for business client
By default restore skips the files already present in destination</p>
<h2 id="args">Args</h2>
<p>users (list) : list of SMTP addresses of users
destination_path (str) : SMTP address of destination user
**kwargs (dict) : Additional parameters
overwrite (bool) : unconditional overwrite files during restore (default: False)
restore_as_copy (bool) : restore files as copy during restore (default: False)
skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
destination_type (str) : type of destination for OOP Restore
end_time (int) : The job end time for Point In Time restore (default: None)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if overwrite and restore as copy file options are both selected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L1046-L1118" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def out_of_place_restore(self, users, destination_path, **kwargs):
    &#34;&#34;&#34; Runs an out-of-place restore job for specified users on OneDrive for business client
        By default restore skips the files already present in destination

        Args:
            users (list) : list of SMTP addresses of users
            destination_path (str) : SMTP address of destination user
            **kwargs (dict) : Additional parameters
                overwrite (bool) : unconditional overwrite files during restore (default: False)
                restore_as_copy (bool) : restore files as copy during restore (default: False)
                skip_file_permissions (bool) : If True, restore of file permissions are skipped (default: False)
                destination_type (str) : type of destination for OOP Restore
                end_time (int) : The job end time for Point In Time restore (default: None)

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:

                if overwrite and restore as copy file options are both selected
    &#34;&#34;&#34;
    overwrite = kwargs.get(&#39;overwrite&#39;, False)
    restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
    skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, False)
    end_time = kwargs.get(&#39;end_time&#39;, None)

    if overwrite and restore_as_copy:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Either select overwrite or restore as copy for file options&#39;)

    self._instance_object._restore_association = self._subClientEntity
    source_user_list = self._get_user_guids(users)
    accountInfo = {}
    destination_type = kwargs.get(&#34;destination_type&#34;)
    if  destination_type == &#39;USER&#39;:
        destination_user_info = self.search_for_user(destination_path)
        accountInfo[&#39;userDisplayName&#39;] = destination_user_info.get(&#39;displayName&#39;, &#39;&#39;)
        accountInfo[&#39;userGUID&#39;] = destination_user_info.get(&#39;user&#39;).get(&#39;userGUID&#39;, &#39;&#39;)
        accountInfo[&#39;userSMTP&#39;] = destination_user_info.get(&#39;smtpAddress&#39;, &#39;&#39;)
    else:
        destination_user_info = self.search_for_shareddrive(destination_path)
        accountInfo[&#39;userDisplayName&#39;] = destination_user_info.get(&#39;folderTitle&#39;, &#39;&#39;)
        accountInfo[&#39;userGUID&#39;] = destination_user_info.get(&#39;folderId&#39;, &#39;&#39;)

    kwargs = {
        &#39;out_of_place&#39;: True,
        &#39;overwrite&#39;: overwrite,
        &#39;restore_as_copy&#39;: restore_as_copy,
        &#39;skip_file_permissions&#39;: skip_file_permissions,
        &#39;accountInfo&#39;: accountInfo,
        &#39;destination_type&#39;: destination_type,
        &#39;destination_path&#39;: destination_path,
    }
    restore_json = self._instance_object._prepare_restore_json(source_user_list, **kwargs)
    if end_time:
        adv_search_bkp_time_dict = {
            &#34;field&#34;: &#34;BACKUPTIME&#34;,
            &#34;fieldValues&#34;: {
                &#34;values&#34;: [
                    &#34;0&#34;,
                    str(end_time)
                ]
            },
            &#34;intraFieldOp&#34;: &#34;FTOr&#34;
        }

        add_to_time = restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;]
        add_to_time[&#34;timeRange&#34;] = {&#34;toTime&#34;: end_time}
        add_backup_time = \
            restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
                &#34;googleRestoreOptions&#34;][&#34;findQuery&#34;][&#34;advSearchGrp&#34;][&#34;fileFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;]
        add_backup_time.append(adv_search_bkp_time_dict)
    return self._process_restore_response(restore_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.process_index_retention_rules"><code class="name flex">
<span>def <span class="ident">process_index_retention_rules</span></span>(<span>self, index_app_type_id, index_server_client_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Makes API call to process index retention rules</p>
<h2 id="args">Args</h2>
<p>index_app_type_id
(int)
&ndash;
index app type id</p>
<p>index_server_client_name
(str)
&ndash;
client name of index server</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if index server not found

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L1209-L1253" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def process_index_retention_rules(self, index_app_type_id, index_server_client_name):
    &#34;&#34;&#34;
     Makes API call to process index retention rules

     Args:

        index_app_type_id           (int)   --   index app type id

        index_server_client_name    (str)   --  client name of index server

     Raises:

            SDKException:

                if index server not found

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if self._commcell_object.clients.has_client(index_server_client_name):
        index_server_client_id = int(self._commcell_object.clients[index_server_client_name.lower()][&#39;id&#39;])
        request_json = {
            &#34;appType&#34;: index_app_type_id,
            &#34;indexServerClientId&#34;: index_server_client_id
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
        )
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    else:
        raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.refresh_retention_stats"><code class="name flex">
<span>def <span class="ident">refresh_retention_stats</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>refresh the retention stats for the client</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L1255-L1277" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh_retention_stats(self):
    &#34;&#34;&#34;
    refresh the retention stats for the client
    &#34;&#34;&#34;
    request_json = {
        &#34;appType&#34;: constants.GMAIL_INDEX_APP_TYPE if self._instance_object.ca_instance_type == &#39;GMAIL&#39; else constants.GDRIVE_INDEX_APP_TYPE,
        &#34;subclientId&#34;: int(self.subclient_id)
    }
    refresh_retention = self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, refresh_retention, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                            self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.refresh_stats_status"><code class="name flex">
<span>def <span class="ident">refresh_stats_status</span></span>(<span>self, user_level)</span>
</code></dt>
<dd>
<div class="desc"><p>refresh the client level or user level stats for the client
Args:
user_level (bool) :
Option to refresh client level or user level stats</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L1279-L1313" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh_stats_status(self, user_level):
    &#34;&#34;&#34;
    refresh the client level or user level stats for the client
        Args:
             user_level (bool) :  Option to refresh client level or user level stats
    &#34;&#34;&#34;
    if self._instance_object.ca_instance_type == &#39;GMAIL&#39;:
        request_json = {
            &#34;appType&#34;: constants.GMAIL_INDEX_APP_TYPE,
            &#34;gmailIdxStatsReq&#34;:
                [{
                    &#34;subclientId&#34;: int(self.subclient_id), &#34;type&#34;: 1 if user_level else 0}]
        }
    else:
        request_json = {
            &#34;appType&#34;: constants.GDRIVE_INDEX_APP_TYPE,
            &#34;googleDriveIdxStatsReq&#34;:
                [{
                    &#34;subclientId&#34;: int(self.subclient_id), &#34;type&#34;: 1 if user_level else 0}]
        }
    refresh_backup_stats = self._services[&#39;OFFICE365_POPULATE_INDEX_STATS&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, refresh_backup_stats, request_json)

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code != 0:
                error_message = response.json().get(&#39;errorMessage&#39;)
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, client, destination_path, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, to_disk=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destionation location.</p>
<h2 id="args">Args</h2>
<p>client
(str/object) &ndash;
either the name of the client or
the instance of the Client</p>
<p>destination_path
(str)
&ndash;
full path of the restore location on client</p>
<p>paths
(list)
&ndash;
list of full paths of
files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_disk
(bool)
&ndash;
If True, restore to disk will be performed</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client instance</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L183-L257" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        client,
        destination_path,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        to_disk=False):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destionation location.

        Args:
            client                (str/object) --  either the name of the client or
                                                       the instance of the Client

            destination_path      (str)        --  full path of the restore location on client

            paths                 (list)       --  list of full paths of
                                                       files/folders to restore

            overwrite             (bool)       --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl  (bool)       --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time           (str)       --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_disk             (bool)       --  If True, restore to disk will be performed

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or Client instance

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    self._instance_object._restore_association = self._subClientEntity

    return self._instance_object.restore_out_of_place(
        client=client,
        destination_path=destination_path,
        paths=paths,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        to_disk=to_disk
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.run_client_level_backup"><code class="name flex">
<span>def <span class="ident">run_client_level_backup</span></span>(<span>self, is_mailbox, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs the backup for the client</p>
<h2 id="args">Args</h2>
<dl>
<dt>is_mailbox (boolean) : flag to determine GMail Mailbox or not</dt>
<dt>**kwargs (dict) : contains some optional fields like full_backup, etc</dt>
<dt><strong><code>Ex</code></strong></dt>
<dd>**{
'full_backup'(boolean): Flag whether to run full backup or not.</dd>
</dl>
<p>}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L877-L902" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_client_level_backup(self, is_mailbox, **kwargs):
    &#34;&#34;&#34;
            Runs the backup for the client
            Args:
                    is_mailbox (boolean) : flag to determine GMail Mailbox or not
                    **kwargs (dict) : contains some optional fields like full_backup, etc
                    Ex: **{
                        &#39;full_backup&#39;(boolean): Flag whether to run full backup or not.
                    }

            Returns:
                    object - instance of the Job class for this backup job

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

            &#34;&#34;&#34;
    task_json = self._task_json_for_google_backup(is_mailbox, **kwargs)
    create_task = self._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, create_task, task_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.run_subclient_discovery"><code class="name flex">
<span>def <span class="ident">run_subclient_discovery</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>This method launches AutoDiscovery on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L351-L363" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_subclient_discovery(self):
    &#34;&#34;&#34;
        This method launches AutoDiscovery on the subclient
    &#34;&#34;&#34;

    discover_type = 15
    discover_users = self._services[&#39;GET_CLOUDAPPS_ONEDRIVE_USERS&#39;] % (self._instance_object.instance_id,
                                                                       self._client_object.client_id,
                                                                       discover_type,
                                                                       self.subclient_id)
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_users)
    if response.status_code != 200 and response.status_code != 500:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.run_user_level_backup"><code class="name flex">
<span>def <span class="ident">run_user_level_backup</span></span>(<span>self, users_list, is_mailbox, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs the backup for the users in users list</p>
<h2 id="args">Args</h2>
<p>users_list (list) : list of SMTP addresses of users</p>
<p>is_mailbox (boolean) : flag to determine GMail Mailbox or not</p>
<p><strong>kwargs (dict) : contains some optional fields like full_backup, etc
Ex: </strong>{
'full_backup'(boolean): Flag whether to run full backup or not.
}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L847-L875" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_user_level_backup(self, users_list, is_mailbox, **kwargs):
    &#34;&#34;&#34;
    Runs the backup for the users in users list
    Args:
            users_list (list) : list of SMTP addresses of users

            is_mailbox (boolean) : flag to determine GMail Mailbox or not

            **kwargs (dict) : contains some optional fields like full_backup, etc
                    Ex: **{
                        &#39;full_backup&#39;(boolean): Flag whether to run full backup or not.
                    }

    Returns:
            object - instance of the Job class for this backup job

    Raises:
        SDKException:
            if response is empty

            if response is not success

    &#34;&#34;&#34;
    task_json = self._task_json_for_google_backup(is_mailbox, users_list=users_list, **kwargs)
    create_task = self._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, create_task, task_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.search_for_shareddrive"><code class="name flex">
<span>def <span class="ident">search_for_shareddrive</span></span>(<span>self, drive)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches for a specific shared drive details from discovered list</p>
<h2 id="args">Args</h2>
<p>drive (str) : Shared Drive iD</p>
<h2 id="returns">Returns</h2>
<dl>
<dt>drive (list): shared drive details' list fetched from discovered content</dt>
<dt><code>
eg</code></dt>
<dd>{
'folderTitle': '',
'folderId':'',
'user': {
'userGUID': 'UserGuid'
}
}</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if discovery is not complete

if invalid SMTP address is passed

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L965-L1020" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def search_for_shareddrive(self, drive):
    &#34;&#34;&#34; Searches for a specific shared drive details from discovered list

        Args:
            drive (str) : Shared Drive iD

        Returns:

            drive (list): shared drive details&#39; list fetched from discovered content
                          eg: {
                                    &#39;folderTitle&#39;: &#39;&#39;,
                                    &#39;folderId&#39;:&#39;&#39;,
                                    &#39;user&#39;: {
                                         &#39;userGUID&#39;: &#39;UserGuid&#39;
                                    }
                             }

        Raises:

            SDKException:

                if discovery is not complete

                if invalid SMTP address is passed

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                           self._client_object.client_id,
                                                           AppIDAType.CLOUD_APP.value))

    search_query = f&#39;{browse_content}&amp;search={drive}&amp;eDiscoverType=25&#39;

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

    if flag:
        if response and response.json():
            if &#39;folders&#39; in response.json():
                folders = response.json().get(&#39;folders&#39;, [])
                if len(folders) == 0:
                    error_string = &#39;Either discovery is not complete or Shared Drive is not available in discovered data&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                for folder in folders:
                    if folder[&#39;folderTitle&#39;] == drive:
                        return folder
                else:
                    error_string = &#39;Shared Drive is not available in discovered data&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Check if the Shared Drive provided is valid&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.search_for_user"><code class="name flex">
<span>def <span class="ident">search_for_user</span></span>(<span>self, user_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches for a specific user's details from discovered list</p>
<h2 id="args">Args</h2>
<p>user_id (str) : user's SMTP address</p>
<h2 id="returns">Returns</h2>
<dl>
<dt>user (list): user details' list fetched from discovered content</dt>
<dt><code>
eg</code></dt>
<dd>{
'displayName': '',
'smtpAddress': '',
'isSuperAdmin': False,
'isAutoDiscoveredUser': False,
'commonFlags': 0,
'user': {
'<em>type</em>': 13,
'userGUID': 'UserGuid'
}
}</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if discovery is not complete

if invalid SMTP address is passed

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L904-L963" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def search_for_user(self, user_id):
    &#34;&#34;&#34; Searches for a specific user&#39;s details from discovered list

        Args:
            user_id (str) : user&#39;s SMTP address

        Returns:

            user (list): user details&#39; list fetched from discovered content
                          eg: {
                                    &#39;displayName&#39;: &#39;&#39;,
                                    &#39;smtpAddress&#39;: &#39;&#39;,
                                    &#39;isSuperAdmin&#39;: False,
                                    &#39;isAutoDiscoveredUser&#39;: False,
                                    &#39;commonFlags&#39;: 0,
                                    &#39;user&#39;: {
                                        &#39;_type_&#39;: 13,
                                         &#39;userGUID&#39;: &#39;UserGuid&#39;
                                    }
                             }

        Raises:

            SDKException:

                if discovery is not complete

                if invalid SMTP address is passed

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                           self._client_object.client_id,
                                                           AppIDAType.CLOUD_APP.value))

    search_query = f&#39;{browse_content}&amp;search={user_id}&#39;

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, search_query)

    if flag:
        if response and response.json():
            if &#39;userAccounts&#39; in response.json():
                user_accounts = response.json().get(&#39;userAccounts&#39;, [])
                if len(user_accounts) == 0:
                    error_string = &#39;Either discovery is not complete or user is not available in discovered data&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
                for user in user_accounts:
                    if user[&#39;smtpAddress&#39;] == user_id:
                        return user
                else:
                    error_string = &#39;User is not available in discovered data&#39;
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, error_string)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Check if the user provided is valid&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.set_auto_discovery"><code class="name flex">
<span>def <span class="ident">set_auto_discovery</span></span>(<span>self, value)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the auto discovery value for subclient.
You can either set a RegEx value or a user group,
depending on the auto discovery type selected at instance level.</p>
<pre><code>Args:

    value   (list)  --  List of RegEx or user groups
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L309-L349" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_auto_discovery(self, value):
    &#34;&#34;&#34;Sets the auto discovery value for subclient.
    You can either set a RegEx value or a user group,
    depending on the auto discovery type selected at instance level.

        Args:

            value   (list)  --  List of RegEx or user groups

    &#34;&#34;&#34;

    if not isinstance(value, list):
        raise SDKException(&#39;Subclient&#39;, &#39;116&#39;)

    if not self._instance_object.auto_discovery_status:
        raise SDKException(&#39;Subclient&#39;, &#39;117&#39;)

    subclient_prop = self._subclient_properties[&#39;cloudAppsSubClientProp&#39;].copy()
    if self._instance_object.auto_discovery_mode == 0:
        # RegEx based auto discovery is enabled on instance

        if subclient_prop[&#39;instanceType&#39;] == 7:
            subclient_prop[&#39;oneDriveSubclient&#39;][&#39;regularExp&#39;] = value
        else:
            subclient_prop[&#39;GoogleSubclient&#39;][&#39;regularExp&#39;] = value
        self._set_subclient_properties(&#34;_subclient_properties[&#39;cloudAppsSubClientProp&#39;]&#34;, subclient_prop)
    else:
        # User group based auto discovery is enabled on instance
        grp_list = []
        groups = self.discover(discover_type=&#39;GROUPS&#39;)
        for item in value:
            for group in groups:
                if group[&#39;contentName&#39;].lower() == item.lower():
                    grp_list.append({
                        &#34;cloudconnectorContent&#34;: {
                            &#34;includeAccounts&#34;: group
                        }
                    })
        self._content.extend(grp_list)
        self._set_subclient_properties(&#34;_subclient_properties[&#39;content&#39;]&#34;, self._content)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.verify_groups_discovery"><code class="name flex">
<span>def <span class="ident">verify_groups_discovery</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Verifies that groups discovery is complete</p>
<h2 id="returns">Returns</h2>
<p>discovery_stats (tuple):</p>
<pre><code>discovery_status (bool): True if users are discovered else returns False

user_accounts (int):     List of users fetched, returns [] if discovery is not complete
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>   if response is not success

   if response received does not contain pagining info
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L682-L724" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def verify_groups_discovery(self):
    &#34;&#34;&#34; Verifies that groups discovery is complete

        Returns:

            discovery_stats (tuple):

                discovery_status (bool): True if users are discovered else returns False

                user_accounts (int):     List of users fetched, returns [] if discovery is not complete

        Raises:

             SDKException:

                    if response is not success

                    if response received does not contain pagining info
    &#34;&#34;&#34;
    browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                           self._client_object.client_id,
                                                           AppIDAType.CLOUD_APP.value))

    # determines the number of accounts to return in response
    page_size = 500
    offset = 0

    groups = []
    while True:
        discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&amp;offset={offset}&amp;eDiscoverType=5&#39;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_query)
        offset += 1

        if flag:
            if response and response.json():
                if &#39;groups&#39; in response.json():
                    curr_groups = response.json().get(&#39;groups&#39;, [])
                    groups.extend(curr_groups)
                    if len(curr_groups) &lt; page_size:
                        break
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    return groups</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.verify_shareddrive_discovery"><code class="name flex">
<span>def <span class="ident">verify_shareddrive_discovery</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Verifies all shared drives discovery completed.</p>
<h2 id="returns">Returns</h2>
<p>discovery_stats (tuple):</p>
<pre><code>discovery_status (bool): True if users are discovered else returns False

user_accounts (int):     List of shared drives fetched, returns [] if discovery is not complete
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>   if response is not success

   if response received does not contain pagining info
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/google_subclient.py#L726-L766" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def verify_shareddrive_discovery(self):
    &#34;&#34;&#34; Verifies all shared drives discovery completed.

                Returns:

                    discovery_stats (tuple):

                        discovery_status (bool): True if users are discovered else returns False

                        user_accounts (int):     List of shared drives fetched, returns [] if discovery is not complete

                Raises:

                     SDKException:

                            if response is not success

                            if response received does not contain pagining info
            &#34;&#34;&#34;

    browse_content = (self._services[&#39;CLOUD_DISCOVERY&#39;] % (self._instance_object.instance_id,
                                                           self._client_object.client_id,
                                                           AppIDAType.CLOUD_APP.value))

    # determines the number of accounts to return in response
    page_size = 500
    discover_query = f&#39;{browse_content}&amp;pageSize={page_size}&amp;eDiscoverType=25&#39;  # for shared drive discovery

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, discover_query)

    if flag:
        no_of_records = -1
        if response and response.json():
            if &#39;pagingInfo&#39; in response.json():
                no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                if no_of_records &gt; 0:
                    shared_drives = response.json().get(&#39;folders&#39;, [])
                    return True, shared_drives
        return False, []
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="../casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.allow_multiple_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.backup" href="../../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.browse" href="../../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.data_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.deduplication_options" href="../../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.description" href="../../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.display_name" href="../../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup_at_time" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup_days" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.encryption_flag" href="../../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.exclude_from_sla" href="../../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find" href="../../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find_latest_job" href="../../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.get_ma_associated_storagepolicy" href="../../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_blocklevel_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_intelli_snap_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_on_demand_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_trueup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.last_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.list_media" href="../../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.name" href="../../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.network_agent" href="../../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.next_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.plan" href="../../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.properties" href="../../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.read_buffer_size" href="../../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.refresh" href="../../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.restore_in_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.run_content_indexing" href="../../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_backup_nodes" href="../../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.snapshot_engine_name" href="../../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.software_compression" href="../../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma_id" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_policy" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_guid" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_id" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_name" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.unset_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.update_properties" href="../../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.cloudapps" href="index.html">cvpysdk.subclients.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient">GoogleSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_AD_group" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_AD_group">add_AD_group</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_shared_drives" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_shared_drives">add_shared_drives</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_user" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_user">add_user</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_users" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.add_users">add_users</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.browse_content" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.browse_content">browse_content</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.content" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.discover" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.discover">discover</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.disk_restore" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.disk_restore">disk_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.get_client_level_stats" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.get_client_level_stats">get_client_level_stats</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.get_subclient_users" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.get_subclient_users">get_subclient_users</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.get_user_level_stats" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.get_user_level_stats">get_user_level_stats</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.groups" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.groups">groups</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.in_place_restore" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.in_place_restore">in_place_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.out_of_place_restore" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.out_of_place_restore">out_of_place_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.process_index_retention_rules" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.process_index_retention_rules">process_index_retention_rules</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.refresh_retention_stats" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.refresh_retention_stats">refresh_retention_stats</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.refresh_stats_status" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.refresh_stats_status">refresh_stats_status</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.restore_out_of_place" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.run_client_level_backup" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.run_client_level_backup">run_client_level_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.run_subclient_discovery" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.run_subclient_discovery">run_subclient_discovery</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.run_user_level_backup" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.run_user_level_backup">run_user_level_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.search_for_shareddrive" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.search_for_shareddrive">search_for_shareddrive</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.search_for_user" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.search_for_user">search_for_user</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.set_auto_discovery" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.set_auto_discovery">set_auto_discovery</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.verify_groups_discovery" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.verify_groups_discovery">verify_groups_discovery</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.verify_shareddrive_discovery" href="#cvpysdk.subclients.cloudapps.google_subclient.GoogleSubclient.verify_shareddrive_discovery">verify_shareddrive_discovery</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>