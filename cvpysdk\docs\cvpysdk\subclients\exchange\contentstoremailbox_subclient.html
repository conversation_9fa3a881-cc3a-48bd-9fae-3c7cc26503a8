<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.exchange.contentstoremailbox_subclient API documentation</title>
<meta name="description" content="File for operating on a ContentStoreMailbox Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.exchange.contentstoremailbox_subclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a ContentStoreMailbox Subclient.</p>
<p>ContentStoreMailboxSubclient is the only class defined in this file.</p>
<p>ContentStoreMailboxSubclient:
Derived class from ExchangeMailboxSubclient Base class,
representing a ContentStoreMailbox subclient, and to
perform operations on that subclient</p>
<h2 id="journalmailboxsubclient">Journalmailboxsubclient</h2>
<p>_get_subclient_properties()
&ndash;
gets the properties of UserMailbox Subclient</p>
<p>_get_subclient_properties_json()
&ndash;
gets the properties JSON of UserMailbox Subclient</p>
<p>users()
&ndash;
creates users association for subclient</p>
<p>Databases()
&ndash;
creates Db association for
the subclient</p>
<p>Adgroups()
&ndash;
creates Adgroup association for subclient</p>
<p>restore_in_place()
&ndash;
runs in-place restore for the subclient</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/contentstoremailbox_subclient.py#L1-L362" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a ContentStoreMailbox Subclient.

ContentStoreMailboxSubclient is the only class defined in this file.

ContentStoreMailboxSubclient:   Derived class from ExchangeMailboxSubclient Base class,
                                representing a ContentStoreMailbox subclient, and to
                                perform operations on that subclient

JournalMailboxSubclient:

    _get_subclient_properties()         --  gets the properties of UserMailbox Subclient

    _get_subclient_properties_json()    --  gets the properties JSON of UserMailbox Subclient

    users()                             --  creates users association for subclient

    Databases()                         --  creates Db association for  the subclient

    Adgroups()                          --  creates Adgroup association for subclient

    restore_in_place()                  --  runs in-place restore for the subclient

&#34;&#34;&#34;

from __future__ import unicode_literals

from ...exception import SDKException

from ..exchsubclient import ExchangeSubclient
from ...client import Client


class ContentStoreMailboxSubclient(ExchangeSubclient):
    &#34;&#34;&#34;Derived class from ExchangeSubclient Base class.

        This represents a contentstoremailbox subclient,
        and can perform discover and restore operations on only that subclient.

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given ContentStoreMailbox Subclient.

            Args:
                backupset_object    (object)    --  instance of the backupset class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(
            ContentStoreMailboxSubclient,
            self).__init__(backupset_object, subclient_name, subclient_id)

        self._instance_object = backupset_object._instance_object
        self._client_object = self._instance_object._agent_object._client_object
        self._SET_EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
            &#39;SET_EMAIL_POLICY_ASSOCIATIONS&#39;]

        self.refresh()

    def _get_content_store_assocaitions(self):
        &#34;&#34;&#34;Gets the appropriate content store associations from the Subclient.

            Returns:
                list    -   list of content store mailbox associated with the subclient

        &#34;&#34;&#34;
        users = []

        self._EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
            &#39;GET_EMAIL_POLICY_ASSOCIATIONS&#39;] % (self.subclient_id, &#39;ContentStore Mailbox&#39;)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._EMAIL_POLICY_ASSOCIATIONS
        )

        if flag:
            subclient_content = response.json()

            if &#39;associations&#39; in subclient_content:
                children = subclient_content[&#39;associations&#39;]

                for child in children:
                    journal_policy = None
                    retention_policy = None
                    display_name = str(child[&#39;contentStoreMailbox&#39;][&#39;displayName&#39;])
                    smtp_address = str(child[&#39;contentStoreMailbox&#39;][&#39;smtpAdrress&#39;])
                    user_guid = str(child[&#39;contentStoreMailbox&#39;][&#39;user&#39;][&#39;userGUID&#39;])
                    is_auto_discover_user = str(
                        child[&#39;contentStoreMailbox&#39;][&#39;isAutoDiscoveredUser&#39;])
                    for policy in child[&#39;policies&#39;][&#39;emailPolicies&#39;]:
                        if policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 4:
                            journal_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 3:
                            retention_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])

                    temp_dict = {
                        &#39;display_name&#39;: display_name,
                        &#39;smtp_address&#39;: smtp_address,
                        &#39;user_guid&#39;: user_guid,
                        &#39;is_auto_discover_user&#39;: is_auto_discover_user,
                        &#39;journal_policy&#39;: journal_policy,
                        &#39;retention_policy&#39;: retention_policy
                    }

                    users.append(temp_dict)

        return users

    @staticmethod
    def _get_client_dict(client_object):
        &#34;&#34;&#34;Returns the client dict for the client object to be appended to member server.

            Args:
                client_object   (object)    --  instance of the Client class

            Returns:
                dict    -   dictionary for a single client to be associated
        &#34;&#34;&#34;
        client_dict = {
            &#34;clientName&#34;: client_object.client_name,
            &#34;clientId&#34;: int(client_object.client_id),
        }

        return client_dict

    def _content_store_servers(self, clients_list):
        &#34;&#34;&#34;Returns the proxy clients to be associated .

            Args:
                clients_list (list)    --  list of the clients to associated

            Returns:
                list - list consisting of all member servers to be associated

            Raises:
                SDKException:
                    if type of clients list argument is not list
        &#34;&#34;&#34;
        if not isinstance(clients_list, list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        content_store_servers = []

        for client in clients_list:
            if isinstance(client, str):
                client = client.strip().lower()

                if self._commcell_object.clients.has_client(client):
                    temp_client = self._commcell_object.clients.get(client)

                    if temp_client.agents.has_agent(&#39;exchange mailbox (classic)&#39;):
                        client_dict = self._get_client_dict(temp_client)
                        content_store_client_dict = {
                            &#34;isActive&#34;: True,
                            &#34;client&#34;: client_dict
                        }
                        content_store_servers.append(content_store_client_dict)

                    del temp_client
            elif isinstance(client, Client):
                if client.agents.has_agent(&#39;exchange mailbox (classic)&#39;):
                    client_dict = self._get_client_dict(client)
                    content_store_client_dict = {
                        &#34;isActive&#34;: True,
                        &#34;client&#34;: client_dict
                    }
                    content_store_servers.append(content_store_client_dict)

        return content_store_servers

    @property
    def content_store_mailboxes(self):
        &#34;&#34;&#34;&#34;Returns the list of discovered users for the UserMailbox subclient.&#34;&#34;&#34;
        return self._content_store_mailboxes

    def set_contentstore_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Create User assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the Users to add to the subclient

                    subclient_content = {

                        &#39;mailboxNames&#39; : [&#34;List of mailbox alias&#34;],,

                        &#39;contentStoreClients&#39; : [List of Content Store clients],

                        -- if use_policies is True --
                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;

                        -- if use_policies is False --
                        &#39;plan_name&#39;: Plan Name,
                        &#39;plan_id&#39;: int or None (Optional)
                    }

                use_policies (bool) -- If True uses policies else uses Plan

        &#34;&#34;&#34;
        users = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        try:
            content_store_server = self._content_store_servers(
                subclient_content[&#39;contentStoreClients&#39;])

            for mailbox_item in subclient_content[&#39;mailboxNames&#39;]:
                mailbox_dict = {
                    &#39;smtpAdrress&#39;: mailbox_item[&#39;smtpAdrress&#39;],
                    &#39;mailBoxType&#39;: 3,
                    &#39;displayName&#39;: mailbox_item[&#39;displayName&#39;],
                    &#39;contentStoreClients&#39;: content_store_server

                }
                users.append(mailbox_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        if use_policies:
            from ...policies.configuration_policies import ConfigurationPolicy

            if not (isinstance(subclient_content[
                    &#39;journal_policy&#39;], (ConfigurationPolicy, str)) and
                    isinstance(subclient_content[
                        &#39;retention_policy&#39;], (ConfigurationPolicy, str)) and
                    isinstance(subclient_content[&#39;mailboxNames&#39;], list)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            if isinstance(subclient_content[&#39;journal_policy&#39;], ConfigurationPolicy):
                journal_policy = subclient_content[&#39;journal_policy&#39;]
            elif isinstance(subclient_content[&#39;journal_policy&#39;], str):
                journal_policy = ConfigurationPolicy(
                    self._commcell_object, subclient_content[&#39;journal_policy&#39;])

            if isinstance(subclient_content[&#39;retention_policy&#39;], ConfigurationPolicy):
                retention_policy = subclient_content[&#39;retention_policy&#39;]
            elif isinstance(subclient_content[&#39;retention_policy&#39;], str):
                retention_policy = ConfigurationPolicy(
                    self._commcell_object, subclient_content[&#39;retention_policy&#39;])
            associations_json = {
                &#34;emailAssociation&#34;: {
                    &#34;advanceOptions&#34;: {},
                    &#34;subclientEntity&#34;: self._subClientEntity,
                    &#34;emailDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 6,
                        &#34;contentStoreMailboxes&#34;: users
                    },
                    &#34;policies&#34;: {
                        &#34;emailPolicies&#34;: [
                            {
                                &#34;policyType&#34;: 1,
                                &#34;flags&#34;: 0,
                                &#34;agentType&#34;: {
                                    &#34;appTypeId&#34;: 137
                                },
                                &#34;detail&#34;: {
                                    &#34;emailPolicy&#34;: {
                                        &#34;emailPolicyType&#34;: 4
                                    }
                                },
                                &#34;policyEntity&#34;: {
                                    &#34;policyId&#34;: int(journal_policy.configuration_policy_id),
                                    &#34;policyName&#34;: journal_policy.configuration_policy_name
                                }

                            },
                            {
                                &#34;policyType&#34;: 1,
                                &#34;flags&#34;: 0,
                                &#34;agentType&#34;: {
                                    &#34;appTypeId&#34;: 137
                                },
                                &#34;detail&#34;: {
                                    &#34;emailPolicy&#34;: {
                                        &#34;emailPolicyType&#34;: 3
                                    }
                                },
                                &#34;policyEntity&#34;: {
                                    &#34;policyId&#34;: int(retention_policy._configuration_policy_id),
                                    &#34;policyName&#34;: retention_policy._configuration_policy_name
                                }
                            }
                        ]
                    }
                }
            }

        else:
            if &#39;plan_name&#39; not in subclient_content:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;&#39;plan_name&#39; not given in content&#34;)

            if not self._commcell_object.plans.has_plan(subclient_content[&#39;plan_name&#39;]):
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                   &#39;Plan Name {} not found&#39;.format(subclient_content[&#39;plan_name&#39;]))
            if &#39;plan_id&#39; not in subclient_content or subclient_content[&#39;plan_id&#39;] is None:
                plan_id = self._commcell_object.plans[subclient_content[&#39;plan_name&#39;].lower()]
            else:
                plan_id = subclient_content[&#39;plan_id&#39;]

            associations_json = {
                &#34;emailAssociation&#34;: {
                    &#34;advanceOptions&#34;: {&#34;enableAutoDiscovery&#34;: False},
                    &#34;subclientEntity&#34;: self._subClientEntity,
                    &#34;emailDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 6,
                        &#34;contentStoreMailboxes&#34;: users
                    },
                    &#34;emailStatus&#34;: 0,
                    &#34;plan&#34;: {&#34;planId&#34;: int(plan_id)}
                }
            }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._SET_EMAIL_POLICY_ASSOCIATIONS, associations_json
        )

        if flag:
            try:
                if response.json():
                    if response.json()[&#39;resp&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to create user assocaition\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Exchange Mailbox&#39;, &#39;102&#39;, output_string.format(error_message)
                        )
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the User Mailbox Subclient.&#34;&#34;&#34;
        self._get_subclient_properties()
        self._content_store_mailboxes = self._get_content_store_assocaitions()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient"><code class="flex name class">
<span>class <span class="ident">ContentStoreMailboxSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from ExchangeSubclient Base class.</p>
<p>This represents a contentstoremailbox subclient,
and can perform discover and restore operations on only that subclient.</p>
<p>Initialize the Instance object for the given ContentStoreMailbox Subclient.</p>
<h2 id="args">Args</h2>
<p>backupset_object
(object)
&ndash;
instance of the backupset class</p>
<p>subclient_name
(str)
&ndash;
subclient name</p>
<p>subclient_id
(int)
&ndash;
subclient id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/contentstoremailbox_subclient.py#L51-L362" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ContentStoreMailboxSubclient(ExchangeSubclient):
    &#34;&#34;&#34;Derived class from ExchangeSubclient Base class.

        This represents a contentstoremailbox subclient,
        and can perform discover and restore operations on only that subclient.

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given ContentStoreMailbox Subclient.

            Args:
                backupset_object    (object)    --  instance of the backupset class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(
            ContentStoreMailboxSubclient,
            self).__init__(backupset_object, subclient_name, subclient_id)

        self._instance_object = backupset_object._instance_object
        self._client_object = self._instance_object._agent_object._client_object
        self._SET_EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
            &#39;SET_EMAIL_POLICY_ASSOCIATIONS&#39;]

        self.refresh()

    def _get_content_store_assocaitions(self):
        &#34;&#34;&#34;Gets the appropriate content store associations from the Subclient.

            Returns:
                list    -   list of content store mailbox associated with the subclient

        &#34;&#34;&#34;
        users = []

        self._EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
            &#39;GET_EMAIL_POLICY_ASSOCIATIONS&#39;] % (self.subclient_id, &#39;ContentStore Mailbox&#39;)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._EMAIL_POLICY_ASSOCIATIONS
        )

        if flag:
            subclient_content = response.json()

            if &#39;associations&#39; in subclient_content:
                children = subclient_content[&#39;associations&#39;]

                for child in children:
                    journal_policy = None
                    retention_policy = None
                    display_name = str(child[&#39;contentStoreMailbox&#39;][&#39;displayName&#39;])
                    smtp_address = str(child[&#39;contentStoreMailbox&#39;][&#39;smtpAdrress&#39;])
                    user_guid = str(child[&#39;contentStoreMailbox&#39;][&#39;user&#39;][&#39;userGUID&#39;])
                    is_auto_discover_user = str(
                        child[&#39;contentStoreMailbox&#39;][&#39;isAutoDiscoveredUser&#39;])
                    for policy in child[&#39;policies&#39;][&#39;emailPolicies&#39;]:
                        if policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 4:
                            journal_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 3:
                            retention_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])

                    temp_dict = {
                        &#39;display_name&#39;: display_name,
                        &#39;smtp_address&#39;: smtp_address,
                        &#39;user_guid&#39;: user_guid,
                        &#39;is_auto_discover_user&#39;: is_auto_discover_user,
                        &#39;journal_policy&#39;: journal_policy,
                        &#39;retention_policy&#39;: retention_policy
                    }

                    users.append(temp_dict)

        return users

    @staticmethod
    def _get_client_dict(client_object):
        &#34;&#34;&#34;Returns the client dict for the client object to be appended to member server.

            Args:
                client_object   (object)    --  instance of the Client class

            Returns:
                dict    -   dictionary for a single client to be associated
        &#34;&#34;&#34;
        client_dict = {
            &#34;clientName&#34;: client_object.client_name,
            &#34;clientId&#34;: int(client_object.client_id),
        }

        return client_dict

    def _content_store_servers(self, clients_list):
        &#34;&#34;&#34;Returns the proxy clients to be associated .

            Args:
                clients_list (list)    --  list of the clients to associated

            Returns:
                list - list consisting of all member servers to be associated

            Raises:
                SDKException:
                    if type of clients list argument is not list
        &#34;&#34;&#34;
        if not isinstance(clients_list, list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        content_store_servers = []

        for client in clients_list:
            if isinstance(client, str):
                client = client.strip().lower()

                if self._commcell_object.clients.has_client(client):
                    temp_client = self._commcell_object.clients.get(client)

                    if temp_client.agents.has_agent(&#39;exchange mailbox (classic)&#39;):
                        client_dict = self._get_client_dict(temp_client)
                        content_store_client_dict = {
                            &#34;isActive&#34;: True,
                            &#34;client&#34;: client_dict
                        }
                        content_store_servers.append(content_store_client_dict)

                    del temp_client
            elif isinstance(client, Client):
                if client.agents.has_agent(&#39;exchange mailbox (classic)&#39;):
                    client_dict = self._get_client_dict(client)
                    content_store_client_dict = {
                        &#34;isActive&#34;: True,
                        &#34;client&#34;: client_dict
                    }
                    content_store_servers.append(content_store_client_dict)

        return content_store_servers

    @property
    def content_store_mailboxes(self):
        &#34;&#34;&#34;&#34;Returns the list of discovered users for the UserMailbox subclient.&#34;&#34;&#34;
        return self._content_store_mailboxes

    def set_contentstore_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Create User assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the Users to add to the subclient

                    subclient_content = {

                        &#39;mailboxNames&#39; : [&#34;List of mailbox alias&#34;],,

                        &#39;contentStoreClients&#39; : [List of Content Store clients],

                        -- if use_policies is True --
                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;

                        -- if use_policies is False --
                        &#39;plan_name&#39;: Plan Name,
                        &#39;plan_id&#39;: int or None (Optional)
                    }

                use_policies (bool) -- If True uses policies else uses Plan

        &#34;&#34;&#34;
        users = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        try:
            content_store_server = self._content_store_servers(
                subclient_content[&#39;contentStoreClients&#39;])

            for mailbox_item in subclient_content[&#39;mailboxNames&#39;]:
                mailbox_dict = {
                    &#39;smtpAdrress&#39;: mailbox_item[&#39;smtpAdrress&#39;],
                    &#39;mailBoxType&#39;: 3,
                    &#39;displayName&#39;: mailbox_item[&#39;displayName&#39;],
                    &#39;contentStoreClients&#39;: content_store_server

                }
                users.append(mailbox_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        if use_policies:
            from ...policies.configuration_policies import ConfigurationPolicy

            if not (isinstance(subclient_content[
                    &#39;journal_policy&#39;], (ConfigurationPolicy, str)) and
                    isinstance(subclient_content[
                        &#39;retention_policy&#39;], (ConfigurationPolicy, str)) and
                    isinstance(subclient_content[&#39;mailboxNames&#39;], list)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            if isinstance(subclient_content[&#39;journal_policy&#39;], ConfigurationPolicy):
                journal_policy = subclient_content[&#39;journal_policy&#39;]
            elif isinstance(subclient_content[&#39;journal_policy&#39;], str):
                journal_policy = ConfigurationPolicy(
                    self._commcell_object, subclient_content[&#39;journal_policy&#39;])

            if isinstance(subclient_content[&#39;retention_policy&#39;], ConfigurationPolicy):
                retention_policy = subclient_content[&#39;retention_policy&#39;]
            elif isinstance(subclient_content[&#39;retention_policy&#39;], str):
                retention_policy = ConfigurationPolicy(
                    self._commcell_object, subclient_content[&#39;retention_policy&#39;])
            associations_json = {
                &#34;emailAssociation&#34;: {
                    &#34;advanceOptions&#34;: {},
                    &#34;subclientEntity&#34;: self._subClientEntity,
                    &#34;emailDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 6,
                        &#34;contentStoreMailboxes&#34;: users
                    },
                    &#34;policies&#34;: {
                        &#34;emailPolicies&#34;: [
                            {
                                &#34;policyType&#34;: 1,
                                &#34;flags&#34;: 0,
                                &#34;agentType&#34;: {
                                    &#34;appTypeId&#34;: 137
                                },
                                &#34;detail&#34;: {
                                    &#34;emailPolicy&#34;: {
                                        &#34;emailPolicyType&#34;: 4
                                    }
                                },
                                &#34;policyEntity&#34;: {
                                    &#34;policyId&#34;: int(journal_policy.configuration_policy_id),
                                    &#34;policyName&#34;: journal_policy.configuration_policy_name
                                }

                            },
                            {
                                &#34;policyType&#34;: 1,
                                &#34;flags&#34;: 0,
                                &#34;agentType&#34;: {
                                    &#34;appTypeId&#34;: 137
                                },
                                &#34;detail&#34;: {
                                    &#34;emailPolicy&#34;: {
                                        &#34;emailPolicyType&#34;: 3
                                    }
                                },
                                &#34;policyEntity&#34;: {
                                    &#34;policyId&#34;: int(retention_policy._configuration_policy_id),
                                    &#34;policyName&#34;: retention_policy._configuration_policy_name
                                }
                            }
                        ]
                    }
                }
            }

        else:
            if &#39;plan_name&#39; not in subclient_content:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;&#39;plan_name&#39; not given in content&#34;)

            if not self._commcell_object.plans.has_plan(subclient_content[&#39;plan_name&#39;]):
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                   &#39;Plan Name {} not found&#39;.format(subclient_content[&#39;plan_name&#39;]))
            if &#39;plan_id&#39; not in subclient_content or subclient_content[&#39;plan_id&#39;] is None:
                plan_id = self._commcell_object.plans[subclient_content[&#39;plan_name&#39;].lower()]
            else:
                plan_id = subclient_content[&#39;plan_id&#39;]

            associations_json = {
                &#34;emailAssociation&#34;: {
                    &#34;advanceOptions&#34;: {&#34;enableAutoDiscovery&#34;: False},
                    &#34;subclientEntity&#34;: self._subClientEntity,
                    &#34;emailDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 6,
                        &#34;contentStoreMailboxes&#34;: users
                    },
                    &#34;emailStatus&#34;: 0,
                    &#34;plan&#34;: {&#34;planId&#34;: int(plan_id)}
                }
            }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._SET_EMAIL_POLICY_ASSOCIATIONS, associations_json
        )

        if flag:
            try:
                if response.json():
                    if response.json()[&#39;resp&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to create user assocaition\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Exchange Mailbox&#39;, &#39;102&#39;, output_string.format(error_message)
                        )
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the User Mailbox Subclient.&#34;&#34;&#34;
        self._get_subclient_properties()
        self._content_store_mailboxes = self._get_content_store_assocaitions()</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient">ExchangeSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient.content_store_mailboxes"><code class="name">var <span class="ident">content_store_mailboxes</span></code></dt>
<dd>
<div class="desc"><p>"Returns the list of discovered users for the UserMailbox subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/contentstoremailbox_subclient.py#L192-L195" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content_store_mailboxes(self):
    &#34;&#34;&#34;&#34;Returns the list of discovered users for the UserMailbox subclient.&#34;&#34;&#34;
    return self._content_store_mailboxes</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the User Mailbox Subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/contentstoremailbox_subclient.py#L359-L362" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the User Mailbox Subclient.&#34;&#34;&#34;
    self._get_subclient_properties()
    self._content_store_mailboxes = self._get_content_store_assocaitions()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient.set_contentstore_assocaition"><code class="name flex">
<span>def <span class="ident">set_contentstore_assocaition</span></span>(<span>self, subclient_content, use_policies=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Create User assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the Users to add to the subclient</p>
<pre><code>subclient_content = {

    'mailboxNames' : ["List of mailbox alias"],,

    'contentStoreClients' : [List of Content Store clients],

    -- if use_policies is True --
    'archive_policy' : "CIPLAN Archiving policy",

    'cleanup_policy' : 'CIPLAN Clean-up policy',

    'retention_policy': 'CIPLAN Retention policy'

    -- if use_policies is False --
    'plan_name': Plan Name,
    'plan_id': int or None (Optional)
}
</code></pre>
<p>use_policies (bool) &ndash; If True uses policies else uses Plan</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/contentstoremailbox_subclient.py#L197-L357" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_contentstore_assocaition(self, subclient_content, use_policies=True):
    &#34;&#34;&#34;Create User assocaition for UserMailboxSubclient.

        Args:
            subclient_content   (dict)  --  dict of the Users to add to the subclient

                subclient_content = {

                    &#39;mailboxNames&#39; : [&#34;List of mailbox alias&#34;],,

                    &#39;contentStoreClients&#39; : [List of Content Store clients],

                    -- if use_policies is True --
                    &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;

                    -- if use_policies is False --
                    &#39;plan_name&#39;: Plan Name,
                    &#39;plan_id&#39;: int or None (Optional)
                }

            use_policies (bool) -- If True uses policies else uses Plan

    &#34;&#34;&#34;
    users = []

    if not isinstance(subclient_content, dict):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
    try:
        content_store_server = self._content_store_servers(
            subclient_content[&#39;contentStoreClients&#39;])

        for mailbox_item in subclient_content[&#39;mailboxNames&#39;]:
            mailbox_dict = {
                &#39;smtpAdrress&#39;: mailbox_item[&#39;smtpAdrress&#39;],
                &#39;mailBoxType&#39;: 3,
                &#39;displayName&#39;: mailbox_item[&#39;displayName&#39;],
                &#39;contentStoreClients&#39;: content_store_server

            }
            users.append(mailbox_dict)
    except KeyError as err:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    if use_policies:
        from ...policies.configuration_policies import ConfigurationPolicy

        if not (isinstance(subclient_content[
                &#39;journal_policy&#39;], (ConfigurationPolicy, str)) and
                isinstance(subclient_content[
                    &#39;retention_policy&#39;], (ConfigurationPolicy, str)) and
                isinstance(subclient_content[&#39;mailboxNames&#39;], list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(subclient_content[&#39;journal_policy&#39;], ConfigurationPolicy):
            journal_policy = subclient_content[&#39;journal_policy&#39;]
        elif isinstance(subclient_content[&#39;journal_policy&#39;], str):
            journal_policy = ConfigurationPolicy(
                self._commcell_object, subclient_content[&#39;journal_policy&#39;])

        if isinstance(subclient_content[&#39;retention_policy&#39;], ConfigurationPolicy):
            retention_policy = subclient_content[&#39;retention_policy&#39;]
        elif isinstance(subclient_content[&#39;retention_policy&#39;], str):
            retention_policy = ConfigurationPolicy(
                self._commcell_object, subclient_content[&#39;retention_policy&#39;])
        associations_json = {
            &#34;emailAssociation&#34;: {
                &#34;advanceOptions&#34;: {},
                &#34;subclientEntity&#34;: self._subClientEntity,
                &#34;emailDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 6,
                    &#34;contentStoreMailboxes&#34;: users
                },
                &#34;policies&#34;: {
                    &#34;emailPolicies&#34;: [
                        {
                            &#34;policyType&#34;: 1,
                            &#34;flags&#34;: 0,
                            &#34;agentType&#34;: {
                                &#34;appTypeId&#34;: 137
                            },
                            &#34;detail&#34;: {
                                &#34;emailPolicy&#34;: {
                                    &#34;emailPolicyType&#34;: 4
                                }
                            },
                            &#34;policyEntity&#34;: {
                                &#34;policyId&#34;: int(journal_policy.configuration_policy_id),
                                &#34;policyName&#34;: journal_policy.configuration_policy_name
                            }

                        },
                        {
                            &#34;policyType&#34;: 1,
                            &#34;flags&#34;: 0,
                            &#34;agentType&#34;: {
                                &#34;appTypeId&#34;: 137
                            },
                            &#34;detail&#34;: {
                                &#34;emailPolicy&#34;: {
                                    &#34;emailPolicyType&#34;: 3
                                }
                            },
                            &#34;policyEntity&#34;: {
                                &#34;policyId&#34;: int(retention_policy._configuration_policy_id),
                                &#34;policyName&#34;: retention_policy._configuration_policy_name
                            }
                        }
                    ]
                }
            }
        }

    else:
        if &#39;plan_name&#39; not in subclient_content:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;&#39;plan_name&#39; not given in content&#34;)

        if not self._commcell_object.plans.has_plan(subclient_content[&#39;plan_name&#39;]):
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;Plan Name {} not found&#39;.format(subclient_content[&#39;plan_name&#39;]))
        if &#39;plan_id&#39; not in subclient_content or subclient_content[&#39;plan_id&#39;] is None:
            plan_id = self._commcell_object.plans[subclient_content[&#39;plan_name&#39;].lower()]
        else:
            plan_id = subclient_content[&#39;plan_id&#39;]

        associations_json = {
            &#34;emailAssociation&#34;: {
                &#34;advanceOptions&#34;: {&#34;enableAutoDiscovery&#34;: False},
                &#34;subclientEntity&#34;: self._subClientEntity,
                &#34;emailDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 6,
                    &#34;contentStoreMailboxes&#34;: users
                },
                &#34;emailStatus&#34;: 0,
                &#34;plan&#34;: {&#34;planId&#34;: int(plan_id)}
            }
        }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._SET_EMAIL_POLICY_ASSOCIATIONS, associations_json
    )

    if flag:
        try:
            if response.json():
                if response.json()[&#39;resp&#39;][&#39;errorCode&#39;] != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    output_string = &#39;Failed to create user assocaition\nError: &#34;{0}&#34;&#39;
                    raise SDKException(
                        &#39;Exchange Mailbox&#39;, &#39;102&#39;, output_string.format(error_message)
                    )
                else:
                    self.refresh()
        except ValueError:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient">ExchangeSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.ad_group_backup" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.ad_group_backup">ad_group_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.allow_multiple_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.backup" href="../../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.browse" href="../../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.cleanup" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.cleanup">cleanup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.data_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.deduplication_options" href="../../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.description" href="../../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.disable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.disable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.disk_restore" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.disk_restore">disk_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.display_name" href="../../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_backup_at_time" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_trueup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_trueup_days" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.encryption_flag" href="../../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.exclude_from_sla" href="../../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.find" href="../../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.find_latest_job" href="../../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_ma_associated_storagepolicy" href="../../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_backup_opt_json" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_backup_opt_json">get_pst_backup_opt_json</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_data_opt_json" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_data_opt_json">get_pst_data_opt_json</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_task_json" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_task_json">get_pst_task_json</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_blocklevel_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_intelli_snap_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_on_demand_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_trueup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.last_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.list_media" href="../../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.name" href="../../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.network_agent" href="../../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.next_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.out_of_place_restore" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.out_of_place_restore">out_of_place_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.plan" href="../../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.preview_backedup_file" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.preview_backedup_file">preview_backedup_file</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.properties" href="../../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_ingestion" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_ingestion">pst_ingestion</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_restore" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_restore">pst_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.read_buffer_size" href="../../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.restore_in_place" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.restore_out_of_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.run_content_indexing" href="../../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.set_backup_nodes" href="../../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.set_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.snapshot_engine_name" href="../../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.software_compression" href="../../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.storage_ma" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.storage_ma_id" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.storage_policy" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_content_indexing" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_content_indexing">subclient_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_guid" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_id" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_name" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.unset_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.update_properties" href="../../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.exchange" href="index.html">cvpysdk.subclients.exchange</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient" href="#cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient">ContentStoreMailboxSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient.content_store_mailboxes" href="#cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient.content_store_mailboxes">content_store_mailboxes</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient.refresh" href="#cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient.set_contentstore_assocaition" href="#cvpysdk.subclients.exchange.contentstoremailbox_subclient.ContentStoreMailboxSubclient.set_contentstore_assocaition">set_contentstore_assocaition</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>