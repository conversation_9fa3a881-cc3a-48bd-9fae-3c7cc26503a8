<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.cleanroom.cs_recovery API documentation</title>
<meta name="description" content="Main file for performing CommServe Recovery operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.cleanroom.cs_recovery</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing CommServe Recovery operations.</p>
<p>CommServeRecovery class is defined in this file.</p>
<p>CommServeRecovery:
Class for Commserve Recovery of a CS, and to perform operations related to Commserve Recovery</p>
<h2 id="commserverecovery">Commserverecovery</h2>
<p><strong>init</strong>()
&ndash;
initialize object of CommServeRecovery with the required CS GUID</p>
<p>_get_backupsets()
&ndash;
method to get the latest 5 backupsets that are shown on the cloud command</p>
<p>_get_active_recovery_requests()
&ndash;
method to get all the recovery requests submitted for the CS</p>
<p>_quota_details()
&ndash;
returns the current recovery license status of the Commserv</p>
<p>_create_cs_recovery_request()
&ndash;
returns the details of the given request ID</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L1-L297" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
Main file for performing CommServe Recovery operations.

CommServeRecovery class is defined in this file.

CommServeRecovery:      Class for Commserve Recovery of a CS, and to perform operations related to Commserve Recovery


CommServeRecovery:
    __init__()                          --   initialize object of CommServeRecovery with the required CS GUID

    _get_backupsets()                   --   method to get the latest 5 backupsets that are shown on the cloud command

    _get_active_recovery_requests()     --   method to get all the recovery requests submitted for the CS

    _quota_details()                    --   returns the current recovery license status of the Commserv

    _create_cs_recovery_request()       --   returns the details of the given request ID

&#34;&#34;&#34;
from __future__ import absolute_import
from __future__ import unicode_literals

from json import JSONDecodeError
from datetime import datetime
from cvpysdk.exception import SDKException
from cvpysdk.commcell import Commcell


class CommServeRecovery:
    &#34;&#34;&#34;Class to perform operations related to Commserve Recovery&#34;&#34;&#34;

    def __init__(self, commcell_object: Commcell, cs_guid: str):
        &#34;&#34;&#34;Initialize the instance of the CommServeRecovery class.

            Args:
                commcell_object   (commcell object)    --  instance of the Commcell class

                cs_guid           (str)                --  CS GUID

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self.cs_guid = cs_guid
        self._CS_RECOVERY_API = self._services[&#39;COMMSERVE_RECOVERY&#39;]
        self._CS_RECOVERY_LICENSE_API = self._services[&#39;GET_COMMSERVE_RECOVERY_LICENSE_DETAILS&#39;] % self.cs_guid
        self._CS_RECOVERY_RETENTION_API = self._services[&#39;GET_COMMSERVE_RECOVERY_RETENTION_DETAILS&#39;] % self.cs_guid
        self._BACKUPSET_API = self._services[&#39;GET_BACKUPSET_INFO&#39;] % self.cs_guid
        self._cleanup_lock_time = None
        self._is_licensed_commcell = self._quota_details().get(&#39;is_licensed&#39;)

        self._manual_retention_details()

    def _get_backupsets(self):
        &#34;&#34;&#34;Returns details of uploaded backupsets&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._BACKUPSET_API)

        if flag:
            try:
                companies = response.json().get(&#39;companies&#39;)
                if not companies:
                    raise SDKException(&#39;CommserveRecovery&#39;, &#39;101&#39;)
                return {
                    backupset_details[&#34;set_name&#34;]: {
                        &#34;set_id&#34;: backupset_details[&#34;set_id&#34;],
                        &#34;size&#34;: sum(int(files_details[&#39;size&#39;]) for files_details in backupset_details[&#39;files&#39;]),
                        &#34;backup_time&#34;: datetime.strptime(backupset_details[&#34;time_modified&#34;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                        &#34;manually_retained&#34;: bool(self._cleanup_lock_time),
                        &#34;retained_until&#34;: self._cleanup_lock_time if self._cleanup_lock_time else None
                    }
                    for backupset_details in companies[0].get(&#39;commcells&#39;)[0].get(&#39;sets&#39;)
                }


            except (JSONDecodeError, KeyError):
                raise SDKException(&#39;Responcsse&#39;, &#39;102&#39;, &#39;Job id not found in response&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _create_cs_recovery_request(self, set_name: str):
        &#34;&#34;&#34;submits a new commserve recovery request and returns the request id to track&#34;&#34;&#34;
        try:
            set_id, set_size = self.backupsets[set_name][&#39;set_id&#39;], self.backupsets[set_name][&#39;size&#39;]
        except KeyError:
            raise SDKException(&#34;CommserveRecovery&#34;, &#34;104&#34;)
        payload = {
            &#34;commcellGUID&#34;: self.cs_guid,
            &#34;setId&#34;: set_id,
            &#34;setName&#34;: set_name,
            &#34;setSize&#34;: set_size
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._CS_RECOVERY_API, payload)

        if flag:
            try:
                if not response.json()[&#39;success&#39;]:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#39;request creation not successful&#39;)
                return response.json()[&#39;requestId&#39;]
            except (JSONDecodeError, KeyError):
                raise SDKException(&#39;Responcsse&#39;, &#39;102&#39;, &#39;Job id not found in response&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _extend_recovery_request(self, request_id: int):
        &#34;&#34;&#34;Returns True if the request is submitted successfully, otherwise, False&#34;&#34;&#34;
        payload = {
            &#34;csGuid&#34;: self.cs_guid,
            &#34;requestId&#34;: request_id,
            &#34;operation&#34;: 2
        }
        flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, self._CS_RECOVERY_API, payload)

        if flag:
            if response.json()[&#39;errorCode&#39;] == 0:
                return True
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;request did not submit successfully&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_active_recovery_requests(self):

        url = f&#39;{self._CS_RECOVERY_API}?csGuid={self.cs_guid}&amp;showOnlyActiveRequests=true&#39;

        states = {
            1: &#39;SUBMITTED&#39;,
            2: &#39;CREATING_VM&#39;,
            3: &#39;VM_CREATED&#39;,
            4: &#39;STAGING_CS&#39;,
            5: &#39;CS_STAGED&#39;,
            6: &#39;FINISHED&#39;,
            7: &#39;FAILED&#39;,
            8: &#39;KILLED&#39;
        }

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, url)

        if flag:
            response = response.json()
            if response.get(&#34;errorCode&#34;) == 6:
                raise SDKException(&#39;CommserveRecovery&#39;, &#39;101&#39;)

            map_vm_info = lambda vm_info: {
                                    &#34;commandcenter_url&#34;: f&#34;https://{vm_info[&#39;ipAddress&#39;]}/commandcenter&#34;,
                                    &#34;vm_expiration_time&#34;: vm_info[&#39;vmExpirationTime&#39;],
                                    &#34;username&#34;: vm_info[&#39;credentials&#39;][&#39;sUsername&#39;],
                                    &#39;password&#39;: vm_info[&#39;credentials&#39;][&#39;sPassword&#39;]
                                }
            return {
                        request[&#34;id&#34;]: {
                            &#34;backupset&#34;: request.get(&#34;setName&#34;),
                            &#34;requestor&#34;: request[&#34;requestor&#34;][&#34;fullName&#34;],
                            &#34;version&#34;: request[&#34;servicePack&#34;],
                            &#34;start_time&#34;: request[&#34;createdTime&#34;],
                            &#34;end_time&#34;: request[&#34;vmInfo&#34;][&#39;vmExpirationTime&#39;],
                            &#34;status&#34;: states[request[&#34;status&#34;]],
                            &#34;vmInfo&#34;: map_vm_info(request[&#39;vmInfo&#39;]) if request[&#39;status&#39;] == 5 else {}
                        }
                        for request in response.get(&#39;requests&#39;, [])
                    }

        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_vm_details(self, requestid: int):
        &#34;&#34;&#34;Returns VM access details&#34;&#34;&#34;
        try:
            return self.active_recovery_requests[requestid][&#39;vmInfo&#39;]
        except KeyError:
            raise SDKException(&#39;CommserveRecovery&#39;, &#39;103&#39;)

    def _quota_details(self):
        &#34;&#34;&#34;
        Returns CS Recovery license quota details in a dictionary
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._CS_RECOVERY_LICENSE_API)

        if flag:
            response = response.json()
            return {
                &#34;is_licensed&#34;: response[&#39;license&#39;],
                &#34;start_date&#34;: datetime.strptime(response[&#39;quotaStartDate&#39;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                &#34;end_date&#34;: datetime.strptime(response[&#39;quotaEndDate&#39;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                &#39;used_recoveries_count&#39;: response[&#39;recoveriesCount&#39;],
                &#39;total_recoveries_allocated&#39;: response[&#39;maxRecoveries&#39;]
            }
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _manual_retention_details(self):
        &#34;&#34;&#34;Returns details of used vs allocated quota of manual retains in a dict&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._CS_RECOVERY_RETENTION_API)
        if flag:
            response = response.json()
            self._cleanup_lock_time = response[&#39;cleanup_lock_time&#39;]
            return {
                &#34;start_date&#34;: datetime.strptime(response[&#39;quota_start_date&#39;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                &#34;end_date&#34;: datetime.strptime(response[&#39;quota_end_date&#39;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                &#39;consumed_retains&#39;: response[&#39;consumed_retains&#39;],
                &#39;max_retains_allocated&#39;: response[&#39;max_retains&#39;]
            }
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    @property
    def is_licensed_commcell(self):
        return self._is_licensed_commcell

    @property
    def backupsets(self):
        return self._get_backupsets()

    @property
    def active_recovery_requests(self):
        return self._get_active_recovery_requests()

    @property
    def recovery_license_details(self):
        &#34;&#34;&#34;
        Returns a dict containing start date, expiry date of license, number of consumed recovery requests and max requests count.
        Ex:
        {
                &#34;is_licensed&#34;: 1,
                &#34;start_date&#34;: **********.0,
                &#34;end_date&#34;: **********.0,
                &#39;used_recoveries_count&#39;: 23,
                &#39;total_recoveries_allocated&#39;: 40
            }
        &#34;&#34;&#34;
        return self._quota_details()

    @property
    def manual_retention_details(self):
        &#34;&#34;&#34;
        Returns a dict containing start date, expiry date of license, number of consumed retains and max retains allocated.
        Ex:
        {
            &#34;start_date&#34;: **********,
            &#34;end_date&#34;: **********,
            &#39;consumed_retains&#39;: 4,
            &#39;max_retains_allocated&#39;: 10
        }
        &#34;&#34;&#34;
        return self._manual_retention_details()

    def start_recovery(self, backupset_name: str):
        &#34;&#34;&#34;
        Submits commserve recovery request for the given backupset
        Args:
            backupset_name (str) : name of the backup set to recover.
                                    Ex: SET_45
        Returns:
            Request id of the submitted request as an integer
        &#34;&#34;&#34;
        return self._create_cs_recovery_request(backupset_name)

    def extend_reservation(self, request_id: int):
        &#34;&#34;&#34;
        Extends the expiry time of a VM created for the given request id.
        Args:
            request_id (int) : commserve recovery request id
        Returns:
            True is the request is submitted successfully, otherwise, False
        &#34;&#34;&#34;
        return self._extend_recovery_request(request_id)

    def get_vm_details(self, request_id: int):
        &#34;&#34;&#34;
        Returns VM details for the given recovery request in a dict format.
        Ex:
        {
            &#34;commandcenter_url&#34;: &#34;https://20.235.143.244/commandcenter&#34;,
            &#34;vm_expiration_time&#34;: 1712311000,
            &#34;username&#34;: &#34;recoverymanager&#34;,
            &#34;password&#34;: &#34;&lt;REDACTED&gt;&#34;,
        }
        &#34;&#34;&#34;
        return self._get_vm_details(request_id)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.cleanroom.cs_recovery.CommServeRecovery"><code class="flex name class">
<span>class <span class="ident">CommServeRecovery</span></span>
<span>(</span><span>commcell_object: <a title="cvpysdk.commcell.Commcell" href="../commcell.html#cvpysdk.commcell.Commcell">Commcell</a>, cs_guid: str)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to perform operations related to Commserve Recovery</p>
<p>Initialize the instance of the CommServeRecovery class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(commcell object)
&ndash;
instance of the Commcell class</p>
<p>cs_guid
(str)
&ndash;
CS GUID</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L48-L297" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CommServeRecovery:
    &#34;&#34;&#34;Class to perform operations related to Commserve Recovery&#34;&#34;&#34;

    def __init__(self, commcell_object: Commcell, cs_guid: str):
        &#34;&#34;&#34;Initialize the instance of the CommServeRecovery class.

            Args:
                commcell_object   (commcell object)    --  instance of the Commcell class

                cs_guid           (str)                --  CS GUID

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self.cs_guid = cs_guid
        self._CS_RECOVERY_API = self._services[&#39;COMMSERVE_RECOVERY&#39;]
        self._CS_RECOVERY_LICENSE_API = self._services[&#39;GET_COMMSERVE_RECOVERY_LICENSE_DETAILS&#39;] % self.cs_guid
        self._CS_RECOVERY_RETENTION_API = self._services[&#39;GET_COMMSERVE_RECOVERY_RETENTION_DETAILS&#39;] % self.cs_guid
        self._BACKUPSET_API = self._services[&#39;GET_BACKUPSET_INFO&#39;] % self.cs_guid
        self._cleanup_lock_time = None
        self._is_licensed_commcell = self._quota_details().get(&#39;is_licensed&#39;)

        self._manual_retention_details()

    def _get_backupsets(self):
        &#34;&#34;&#34;Returns details of uploaded backupsets&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._BACKUPSET_API)

        if flag:
            try:
                companies = response.json().get(&#39;companies&#39;)
                if not companies:
                    raise SDKException(&#39;CommserveRecovery&#39;, &#39;101&#39;)
                return {
                    backupset_details[&#34;set_name&#34;]: {
                        &#34;set_id&#34;: backupset_details[&#34;set_id&#34;],
                        &#34;size&#34;: sum(int(files_details[&#39;size&#39;]) for files_details in backupset_details[&#39;files&#39;]),
                        &#34;backup_time&#34;: datetime.strptime(backupset_details[&#34;time_modified&#34;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                        &#34;manually_retained&#34;: bool(self._cleanup_lock_time),
                        &#34;retained_until&#34;: self._cleanup_lock_time if self._cleanup_lock_time else None
                    }
                    for backupset_details in companies[0].get(&#39;commcells&#39;)[0].get(&#39;sets&#39;)
                }


            except (JSONDecodeError, KeyError):
                raise SDKException(&#39;Responcsse&#39;, &#39;102&#39;, &#39;Job id not found in response&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _create_cs_recovery_request(self, set_name: str):
        &#34;&#34;&#34;submits a new commserve recovery request and returns the request id to track&#34;&#34;&#34;
        try:
            set_id, set_size = self.backupsets[set_name][&#39;set_id&#39;], self.backupsets[set_name][&#39;size&#39;]
        except KeyError:
            raise SDKException(&#34;CommserveRecovery&#34;, &#34;104&#34;)
        payload = {
            &#34;commcellGUID&#34;: self.cs_guid,
            &#34;setId&#34;: set_id,
            &#34;setName&#34;: set_name,
            &#34;setSize&#34;: set_size
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._CS_RECOVERY_API, payload)

        if flag:
            try:
                if not response.json()[&#39;success&#39;]:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#39;request creation not successful&#39;)
                return response.json()[&#39;requestId&#39;]
            except (JSONDecodeError, KeyError):
                raise SDKException(&#39;Responcsse&#39;, &#39;102&#39;, &#39;Job id not found in response&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _extend_recovery_request(self, request_id: int):
        &#34;&#34;&#34;Returns True if the request is submitted successfully, otherwise, False&#34;&#34;&#34;
        payload = {
            &#34;csGuid&#34;: self.cs_guid,
            &#34;requestId&#34;: request_id,
            &#34;operation&#34;: 2
        }
        flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, self._CS_RECOVERY_API, payload)

        if flag:
            if response.json()[&#39;errorCode&#39;] == 0:
                return True
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;request did not submit successfully&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_active_recovery_requests(self):

        url = f&#39;{self._CS_RECOVERY_API}?csGuid={self.cs_guid}&amp;showOnlyActiveRequests=true&#39;

        states = {
            1: &#39;SUBMITTED&#39;,
            2: &#39;CREATING_VM&#39;,
            3: &#39;VM_CREATED&#39;,
            4: &#39;STAGING_CS&#39;,
            5: &#39;CS_STAGED&#39;,
            6: &#39;FINISHED&#39;,
            7: &#39;FAILED&#39;,
            8: &#39;KILLED&#39;
        }

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, url)

        if flag:
            response = response.json()
            if response.get(&#34;errorCode&#34;) == 6:
                raise SDKException(&#39;CommserveRecovery&#39;, &#39;101&#39;)

            map_vm_info = lambda vm_info: {
                                    &#34;commandcenter_url&#34;: f&#34;https://{vm_info[&#39;ipAddress&#39;]}/commandcenter&#34;,
                                    &#34;vm_expiration_time&#34;: vm_info[&#39;vmExpirationTime&#39;],
                                    &#34;username&#34;: vm_info[&#39;credentials&#39;][&#39;sUsername&#39;],
                                    &#39;password&#39;: vm_info[&#39;credentials&#39;][&#39;sPassword&#39;]
                                }
            return {
                        request[&#34;id&#34;]: {
                            &#34;backupset&#34;: request.get(&#34;setName&#34;),
                            &#34;requestor&#34;: request[&#34;requestor&#34;][&#34;fullName&#34;],
                            &#34;version&#34;: request[&#34;servicePack&#34;],
                            &#34;start_time&#34;: request[&#34;createdTime&#34;],
                            &#34;end_time&#34;: request[&#34;vmInfo&#34;][&#39;vmExpirationTime&#39;],
                            &#34;status&#34;: states[request[&#34;status&#34;]],
                            &#34;vmInfo&#34;: map_vm_info(request[&#39;vmInfo&#39;]) if request[&#39;status&#39;] == 5 else {}
                        }
                        for request in response.get(&#39;requests&#39;, [])
                    }

        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _get_vm_details(self, requestid: int):
        &#34;&#34;&#34;Returns VM access details&#34;&#34;&#34;
        try:
            return self.active_recovery_requests[requestid][&#39;vmInfo&#39;]
        except KeyError:
            raise SDKException(&#39;CommserveRecovery&#39;, &#39;103&#39;)

    def _quota_details(self):
        &#34;&#34;&#34;
        Returns CS Recovery license quota details in a dictionary
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._CS_RECOVERY_LICENSE_API)

        if flag:
            response = response.json()
            return {
                &#34;is_licensed&#34;: response[&#39;license&#39;],
                &#34;start_date&#34;: datetime.strptime(response[&#39;quotaStartDate&#39;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                &#34;end_date&#34;: datetime.strptime(response[&#39;quotaEndDate&#39;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                &#39;used_recoveries_count&#39;: response[&#39;recoveriesCount&#39;],
                &#39;total_recoveries_allocated&#39;: response[&#39;maxRecoveries&#39;]
            }
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _manual_retention_details(self):
        &#34;&#34;&#34;Returns details of used vs allocated quota of manual retains in a dict&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._CS_RECOVERY_RETENTION_API)
        if flag:
            response = response.json()
            self._cleanup_lock_time = response[&#39;cleanup_lock_time&#39;]
            return {
                &#34;start_date&#34;: datetime.strptime(response[&#39;quota_start_date&#39;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                &#34;end_date&#34;: datetime.strptime(response[&#39;quota_end_date&#39;], &#34;%Y-%m-%dT%H:%M:%SZ&#34;).timestamp(),
                &#39;consumed_retains&#39;: response[&#39;consumed_retains&#39;],
                &#39;max_retains_allocated&#39;: response[&#39;max_retains&#39;]
            }
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    @property
    def is_licensed_commcell(self):
        return self._is_licensed_commcell

    @property
    def backupsets(self):
        return self._get_backupsets()

    @property
    def active_recovery_requests(self):
        return self._get_active_recovery_requests()

    @property
    def recovery_license_details(self):
        &#34;&#34;&#34;
        Returns a dict containing start date, expiry date of license, number of consumed recovery requests and max requests count.
        Ex:
        {
                &#34;is_licensed&#34;: 1,
                &#34;start_date&#34;: **********.0,
                &#34;end_date&#34;: **********.0,
                &#39;used_recoveries_count&#39;: 23,
                &#39;total_recoveries_allocated&#39;: 40
            }
        &#34;&#34;&#34;
        return self._quota_details()

    @property
    def manual_retention_details(self):
        &#34;&#34;&#34;
        Returns a dict containing start date, expiry date of license, number of consumed retains and max retains allocated.
        Ex:
        {
            &#34;start_date&#34;: **********,
            &#34;end_date&#34;: **********,
            &#39;consumed_retains&#39;: 4,
            &#39;max_retains_allocated&#39;: 10
        }
        &#34;&#34;&#34;
        return self._manual_retention_details()

    def start_recovery(self, backupset_name: str):
        &#34;&#34;&#34;
        Submits commserve recovery request for the given backupset
        Args:
            backupset_name (str) : name of the backup set to recover.
                                    Ex: SET_45
        Returns:
            Request id of the submitted request as an integer
        &#34;&#34;&#34;
        return self._create_cs_recovery_request(backupset_name)

    def extend_reservation(self, request_id: int):
        &#34;&#34;&#34;
        Extends the expiry time of a VM created for the given request id.
        Args:
            request_id (int) : commserve recovery request id
        Returns:
            True is the request is submitted successfully, otherwise, False
        &#34;&#34;&#34;
        return self._extend_recovery_request(request_id)

    def get_vm_details(self, request_id: int):
        &#34;&#34;&#34;
        Returns VM details for the given recovery request in a dict format.
        Ex:
        {
            &#34;commandcenter_url&#34;: &#34;https://20.235.143.244/commandcenter&#34;,
            &#34;vm_expiration_time&#34;: 1712311000,
            &#34;username&#34;: &#34;recoverymanager&#34;,
            &#34;password&#34;: &#34;&lt;REDACTED&gt;&#34;,
        }
        &#34;&#34;&#34;
        return self._get_vm_details(request_id)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.active_recovery_requests"><code class="name">var <span class="ident">active_recovery_requests</span></code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L232-L234" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def active_recovery_requests(self):
    return self._get_active_recovery_requests()</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.backupsets"><code class="name">var <span class="ident">backupsets</span></code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L228-L230" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backupsets(self):
    return self._get_backupsets()</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.is_licensed_commcell"><code class="name">var <span class="ident">is_licensed_commcell</span></code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L224-L226" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_licensed_commcell(self):
    return self._is_licensed_commcell</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.manual_retention_details"><code class="name">var <span class="ident">manual_retention_details</span></code></dt>
<dd>
<div class="desc"><p>Returns a dict containing start date, expiry date of license, number of consumed retains and max retains allocated.
Ex:
{
"start_date": **********,
"end_date": **********,
'consumed_retains': 4,
'max_retains_allocated': 10
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L251-L263" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def manual_retention_details(self):
    &#34;&#34;&#34;
    Returns a dict containing start date, expiry date of license, number of consumed retains and max retains allocated.
    Ex:
    {
        &#34;start_date&#34;: **********,
        &#34;end_date&#34;: **********,
        &#39;consumed_retains&#39;: 4,
        &#39;max_retains_allocated&#39;: 10
    }
    &#34;&#34;&#34;
    return self._manual_retention_details()</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.recovery_license_details"><code class="name">var <span class="ident">recovery_license_details</span></code></dt>
<dd>
<div class="desc"><p>Returns a dict containing start date, expiry date of license, number of consumed recovery requests and max requests count.
Ex:
{
"is_licensed": 1,
"start_date": **********.0,
"end_date": **********.0,
'used_recoveries_count': 23,
'total_recoveries_allocated': 40
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L236-L249" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def recovery_license_details(self):
    &#34;&#34;&#34;
    Returns a dict containing start date, expiry date of license, number of consumed recovery requests and max requests count.
    Ex:
    {
            &#34;is_licensed&#34;: 1,
            &#34;start_date&#34;: **********.0,
            &#34;end_date&#34;: **********.0,
            &#39;used_recoveries_count&#39;: 23,
            &#39;total_recoveries_allocated&#39;: 40
        }
    &#34;&#34;&#34;
    return self._quota_details()</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.extend_reservation"><code class="name flex">
<span>def <span class="ident">extend_reservation</span></span>(<span>self, request_id: int)</span>
</code></dt>
<dd>
<div class="desc"><p>Extends the expiry time of a VM created for the given request id.</p>
<h2 id="args">Args</h2>
<p>request_id (int) : commserve recovery request id</p>
<h2 id="returns">Returns</h2>
<p>True is the request is submitted successfully, otherwise, False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L276-L284" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def extend_reservation(self, request_id: int):
    &#34;&#34;&#34;
    Extends the expiry time of a VM created for the given request id.
    Args:
        request_id (int) : commserve recovery request id
    Returns:
        True is the request is submitted successfully, otherwise, False
    &#34;&#34;&#34;
    return self._extend_recovery_request(request_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.get_vm_details"><code class="name flex">
<span>def <span class="ident">get_vm_details</span></span>(<span>self, request_id: int)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns VM details for the given recovery request in a dict format.
Ex:
{
"commandcenter_url": "https://20.235.143.244/commandcenter",
"vm_expiration_time": 1712311000,
"username": "recoverymanager",
"password": "<REDACTED>",
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L286-L297" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_vm_details(self, request_id: int):
    &#34;&#34;&#34;
    Returns VM details for the given recovery request in a dict format.
    Ex:
    {
        &#34;commandcenter_url&#34;: &#34;https://20.235.143.244/commandcenter&#34;,
        &#34;vm_expiration_time&#34;: 1712311000,
        &#34;username&#34;: &#34;recoverymanager&#34;,
        &#34;password&#34;: &#34;&lt;REDACTED&gt;&#34;,
    }
    &#34;&#34;&#34;
    return self._get_vm_details(request_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.start_recovery"><code class="name flex">
<span>def <span class="ident">start_recovery</span></span>(<span>self, backupset_name: str)</span>
</code></dt>
<dd>
<div class="desc"><p>Submits commserve recovery request for the given backupset</p>
<h2 id="args">Args</h2>
<p>backupset_name (str) : name of the backup set to recover.
Ex: SET_45</p>
<h2 id="returns">Returns</h2>
<p>Request id of the submitted request as an integer</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/cs_recovery.py#L265-L274" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def start_recovery(self, backupset_name: str):
    &#34;&#34;&#34;
    Submits commserve recovery request for the given backupset
    Args:
        backupset_name (str) : name of the backup set to recover.
                                Ex: SET_45
    Returns:
        Request id of the submitted request as an integer
    &#34;&#34;&#34;
    return self._create_cs_recovery_request(backupset_name)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.cleanroom" href="index.html">cvpysdk.cleanroom</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.cleanroom.cs_recovery.CommServeRecovery" href="#cvpysdk.cleanroom.cs_recovery.CommServeRecovery">CommServeRecovery</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.active_recovery_requests" href="#cvpysdk.cleanroom.cs_recovery.CommServeRecovery.active_recovery_requests">active_recovery_requests</a></code></li>
<li><code><a title="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.backupsets" href="#cvpysdk.cleanroom.cs_recovery.CommServeRecovery.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.extend_reservation" href="#cvpysdk.cleanroom.cs_recovery.CommServeRecovery.extend_reservation">extend_reservation</a></code></li>
<li><code><a title="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.get_vm_details" href="#cvpysdk.cleanroom.cs_recovery.CommServeRecovery.get_vm_details">get_vm_details</a></code></li>
<li><code><a title="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.is_licensed_commcell" href="#cvpysdk.cleanroom.cs_recovery.CommServeRecovery.is_licensed_commcell">is_licensed_commcell</a></code></li>
<li><code><a title="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.manual_retention_details" href="#cvpysdk.cleanroom.cs_recovery.CommServeRecovery.manual_retention_details">manual_retention_details</a></code></li>
<li><code><a title="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.recovery_license_details" href="#cvpysdk.cleanroom.cs_recovery.CommServeRecovery.recovery_license_details">recovery_license_details</a></code></li>
<li><code><a title="cvpysdk.cleanroom.cs_recovery.CommServeRecovery.start_recovery" href="#cvpysdk.cleanroom.cs_recovery.CommServeRecovery.start_recovery">start_recovery</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>