<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.cleanroom.recovery_groups API documentation</title>
<meta name="description" content="Main file for performing Cleanroom recovery operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.cleanroom.recovery_groups</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing Cleanroom recovery operations</p>
<p>RecoveryGroups:
Class for representing all the recovery groups</p>
<p>RecoveryGroup:
Class for a single recovery group selected, and to perform operations on that recovery group</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L1-L317" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
#
# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------
#
&#34;&#34;&#34;
Main file for performing Cleanroom recovery operations

RecoveryGroups:     Class for representing all the recovery groups

RecoveryGroup:      Class for a single recovery group selected, and to perform operations on that recovery group

&#34;&#34;&#34;
from enum import Enum

from cvpysdk.exception import SDKException
from json.decoder import JSONDecodeError


class RecoveryGroups:
    &#34;&#34;&#34;Class representing all the cleanroom recovery groups&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the RecoveryGroups class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object

        self._RECOVERY_GROUPS_URL = commcell_object._services[&#39;ALL_RECOVERY_GROUPS&#39;]
        self._recovery_groups = None

        self.refresh()

    @property
    def all_groups(self):
        &#34;&#34;&#34;Returns dict of all recovery groups.

         Returns dict    -   consists of all groups

                {
                     &#34;group1_name&#34;: group1_id,
                     &#34;group2_name&#34;: group2_id
                }

        &#34;&#34;&#34;
        return self._recovery_groups

    def has_recovery_group(self, recovery_group_name):
        &#34;&#34;&#34;Checks if a recovery group is present in the commcell.

            Args:
                recovery_group_name (str)  --  name of the recovery group

            Returns:
                bool - boolean output whether the group is present in commcell or not

            Raises:
                SDKException:
                    if type of the group name argument is not string

        &#34;&#34;&#34;
        if not isinstance(recovery_group_name, str):
            raise SDKException(&#39;RecoveryGroup&#39;, &#39;101&#39;)

        return self._recovery_groups and recovery_group_name in self._recovery_groups

    def get(self, recovery_group_name):
        &#34;&#34;&#34;Returns a recovery group object.

            Args:
                recovery_group_name (str)  --  name of the recovery group

            Returns:
                object - instance of the recovery group class for the given group name

            Raises:
                SDKException:
                    if type of the group name argument is not string

                    if no group exists with the given name

        &#34;&#34;&#34;
        if not isinstance(recovery_group_name, str):
            raise SDKException(&#39;RecoveryGroup&#39;, &#39;101&#39;)
        else:
            if self.has_recovery_group(recovery_group_name):
                return RecoveryGroup(
                    self._commcell_object,
                    recovery_group_name,
                    self.all_groups[recovery_group_name]
                )

            raise SDKException(&#39;RecoveryGroup&#39;, &#39;102&#39;,
                               &#39;No recovery group exists with name: {0}&#39;.format(recovery_group_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the recovery groups&#34;&#34;&#34;
        self._recovery_groups = self._get_recovery_groups()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all recovery groups .

            Returns:
                str     -   string of all the groups

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;RecoveryGroup&#39;)

        for index, group in enumerate(self._recovery_groups):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(
                index + 1,
                group
            )
            representation_string += sub_str

        return representation_string.strip()

    def _get_recovery_groups(self):
        &#34;&#34;&#34;Gets all the recovery groups.

            Returns:
                dict - consists of all recovery groups in the client
                    {
                         &#34;group1_name&#34;: group1_id,
                         &#34;group2_name&#34;: group2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_GROUPS_URL)

        if flag:
            try:
                json_resp = response.json()

                group_name_id_dict = {group[&#39;name&#39;]: group[&#39;id&#39;] for group in json_resp[&#39;recoveryGroups&#39;]}

                return group_name_id_dict
            except (JSONDecodeError, KeyError):
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))


class RecoveryStatus(Enum):
    NO_STATUS = 0
    NONE = 0
    NOT_READY = 1
    READY = 2
    RECOVERED = 3
    FAILED = 4
    RECOVERED_WITH_ERRORS = 5
    IN_PROGRESS = 6
    CLEANED_UP = 7


class RecoveryGroup:
    &#34;&#34;&#34;Class to perform actions on a recovery group&#34;&#34;&#34;

    def __init__(self, commcell_object, recovery_group_name, recovery_group_id=None):
        &#34;&#34;&#34;Initialize the instance of the RecoveryGroup class.

            Args:
                commcell_object   (object)    --  instance of the Commcell class

                recovery_group_name      (str)       --  name of the target

                recovery_group_id        (str)       --  id of the target (default: None)

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object

        if recovery_group_id is not None:
            self._recovery_group_id = str(recovery_group_id)
        else:
            # get id from RecoveryGroups class
            self._recovery_group_id = RecoveryGroups(commcell_object).all_groups[recovery_group_name]

        self._RECOVERY_GROUP_URL = commcell_object._services[&#39;RECOVERY_GROUP&#39;] % self._recovery_group_id
        self._RECOVER_URL = commcell_object._services[&#39;RECOVERY_GROUP_RECOVER&#39;] % self._recovery_group_id

        # will be set when refresh is called
        self._properties = {}

        self.refresh()

    @property
    def id(self):
        &#34;&#34;&#34;recovery group id&#34;&#34;&#34;
        return int(self._recovery_group_id)

    @property
    def entities(self):
        &#34;&#34;&#34;list of entities in recovery group&#34;&#34;&#34;
        return self._properties[&#39;entities&#39;]

    def _recover_entities(self, entity_ids):
        &#34;&#34;&#34;
        Sends request to recover all entities with specified ids

        Args:
            entity_ids: iterable of entity ids

        Returns:
            job_id: job id of recovery

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._RECOVER_URL, payload={
            &#39;recoveryGroup&#39;: {
                &#39;id&#39;: self.id
            },
            &#39;entities&#39;: [{&#39;id&#39;: e_id} for e_id in entity_ids]
        })

        if flag:
            try:
                return response.json()[&#39;jobId&#39;]
            except (JSONDecodeError, KeyError):
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Job id not found in response&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def recover_all(self):
        &#34;&#34;&#34;
        Sends request to recover all entities

        Returns:
            job_id: job id of recovery

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        eligible_entities = [entity[&#39;id&#39;] for entity in self.entities if
                             entity[&#39;recoveryStatus&#39;] not in [RecoveryStatus.NOT_READY.value,
                                                              RecoveryStatus.IN_PROGRESS.value]]
        return self._recover_entities(eligible_entities)

    def refresh(self):
        &#34;&#34;&#34;Refresh the recovery group&#34;&#34;&#34;
        self._properties = self._get_recovery_group_properties()

    def _get_recovery_group_properties(self):
        &#34;&#34;&#34;Gets recovery group properties.

            Returns:
                dict - properties for the recovey group

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_GROUP_URL)

        if flag:
            try:
                return response.json()
            except JSONDecodeError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def delete(self):
        &#34;&#34;&#34;
        Sends a request to delete a replication Group

        Raises:
            SDKException:
                if response is empty

                if response is not success
        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._RECOVERY_GROUP_URL)

        if flag:
            try:
                return response.json()
            except JSONDecodeError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroup"><code class="flex name class">
<span>class <span class="ident">RecoveryGroup</span></span>
<span>(</span><span>commcell_object, recovery_group_name, recovery_group_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to perform actions on a recovery group</p>
<p>Initialize the instance of the RecoveryGroup class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>recovery_group_name
(str)
&ndash;
name of the target</p>
<p>recovery_group_id
(str)
&ndash;
id of the target (default: None)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L179-L317" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RecoveryGroup:
    &#34;&#34;&#34;Class to perform actions on a recovery group&#34;&#34;&#34;

    def __init__(self, commcell_object, recovery_group_name, recovery_group_id=None):
        &#34;&#34;&#34;Initialize the instance of the RecoveryGroup class.

            Args:
                commcell_object   (object)    --  instance of the Commcell class

                recovery_group_name      (str)       --  name of the target

                recovery_group_id        (str)       --  id of the target (default: None)

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object

        if recovery_group_id is not None:
            self._recovery_group_id = str(recovery_group_id)
        else:
            # get id from RecoveryGroups class
            self._recovery_group_id = RecoveryGroups(commcell_object).all_groups[recovery_group_name]

        self._RECOVERY_GROUP_URL = commcell_object._services[&#39;RECOVERY_GROUP&#39;] % self._recovery_group_id
        self._RECOVER_URL = commcell_object._services[&#39;RECOVERY_GROUP_RECOVER&#39;] % self._recovery_group_id

        # will be set when refresh is called
        self._properties = {}

        self.refresh()

    @property
    def id(self):
        &#34;&#34;&#34;recovery group id&#34;&#34;&#34;
        return int(self._recovery_group_id)

    @property
    def entities(self):
        &#34;&#34;&#34;list of entities in recovery group&#34;&#34;&#34;
        return self._properties[&#39;entities&#39;]

    def _recover_entities(self, entity_ids):
        &#34;&#34;&#34;
        Sends request to recover all entities with specified ids

        Args:
            entity_ids: iterable of entity ids

        Returns:
            job_id: job id of recovery

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._RECOVER_URL, payload={
            &#39;recoveryGroup&#39;: {
                &#39;id&#39;: self.id
            },
            &#39;entities&#39;: [{&#39;id&#39;: e_id} for e_id in entity_ids]
        })

        if flag:
            try:
                return response.json()[&#39;jobId&#39;]
            except (JSONDecodeError, KeyError):
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Job id not found in response&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def recover_all(self):
        &#34;&#34;&#34;
        Sends request to recover all entities

        Returns:
            job_id: job id of recovery

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        eligible_entities = [entity[&#39;id&#39;] for entity in self.entities if
                             entity[&#39;recoveryStatus&#39;] not in [RecoveryStatus.NOT_READY.value,
                                                              RecoveryStatus.IN_PROGRESS.value]]
        return self._recover_entities(eligible_entities)

    def refresh(self):
        &#34;&#34;&#34;Refresh the recovery group&#34;&#34;&#34;
        self._properties = self._get_recovery_group_properties()

    def _get_recovery_group_properties(self):
        &#34;&#34;&#34;Gets recovery group properties.

            Returns:
                dict - properties for the recovey group

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_GROUP_URL)

        if flag:
            try:
                return response.json()
            except JSONDecodeError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def delete(self):
        &#34;&#34;&#34;
        Sends a request to delete a replication Group

        Raises:
            SDKException:
                if response is empty

                if response is not success
        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._RECOVERY_GROUP_URL)

        if flag:
            try:
                return response.json()
            except JSONDecodeError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.entities"><code class="name">var <span class="ident">entities</span></code></dt>
<dd>
<div class="desc"><p>list of entities in recovery group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L215-L218" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entities(self):
    &#34;&#34;&#34;list of entities in recovery group&#34;&#34;&#34;
    return self._properties[&#39;entities&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.id"><code class="name">var <span class="ident">id</span></code></dt>
<dd>
<div class="desc"><p>recovery group id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L210-L213" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def id(self):
    &#34;&#34;&#34;recovery group id&#34;&#34;&#34;
    return int(self._recovery_group_id)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Sends a request to delete a replication Group</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L298-L317" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self):
    &#34;&#34;&#34;
    Sends a request to delete a replication Group

    Raises:
        SDKException:
            if response is empty

            if response is not success
    &#34;&#34;&#34;

    flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._RECOVERY_GROUP_URL)

    if flag:
        try:
            return response.json()
        except JSONDecodeError:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.recover_all"><code class="name flex">
<span>def <span class="ident">recover_all</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Sends request to recover all entities</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>job_id</code></dt>
<dd>job id of recovery</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L252-L269" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def recover_all(self):
    &#34;&#34;&#34;
    Sends request to recover all entities

    Returns:
        job_id: job id of recovery

    Raises:
        SDKException:
            if response is empty

            if response is not success

    &#34;&#34;&#34;
    eligible_entities = [entity[&#39;id&#39;] for entity in self.entities if
                         entity[&#39;recoveryStatus&#39;] not in [RecoveryStatus.NOT_READY.value,
                                                          RecoveryStatus.IN_PROGRESS.value]]
    return self._recover_entities(eligible_entities)</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the recovery group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L271-L273" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the recovery group&#34;&#34;&#34;
    self._properties = self._get_recovery_group_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroups"><code class="flex name class">
<span>class <span class="ident">RecoveryGroups</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class representing all the cleanroom recovery groups</p>
<p>Initialize object of the RecoveryGroups class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L33-L164" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RecoveryGroups:
    &#34;&#34;&#34;Class representing all the cleanroom recovery groups&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the RecoveryGroups class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object

        self._RECOVERY_GROUPS_URL = commcell_object._services[&#39;ALL_RECOVERY_GROUPS&#39;]
        self._recovery_groups = None

        self.refresh()

    @property
    def all_groups(self):
        &#34;&#34;&#34;Returns dict of all recovery groups.

         Returns dict    -   consists of all groups

                {
                     &#34;group1_name&#34;: group1_id,
                     &#34;group2_name&#34;: group2_id
                }

        &#34;&#34;&#34;
        return self._recovery_groups

    def has_recovery_group(self, recovery_group_name):
        &#34;&#34;&#34;Checks if a recovery group is present in the commcell.

            Args:
                recovery_group_name (str)  --  name of the recovery group

            Returns:
                bool - boolean output whether the group is present in commcell or not

            Raises:
                SDKException:
                    if type of the group name argument is not string

        &#34;&#34;&#34;
        if not isinstance(recovery_group_name, str):
            raise SDKException(&#39;RecoveryGroup&#39;, &#39;101&#39;)

        return self._recovery_groups and recovery_group_name in self._recovery_groups

    def get(self, recovery_group_name):
        &#34;&#34;&#34;Returns a recovery group object.

            Args:
                recovery_group_name (str)  --  name of the recovery group

            Returns:
                object - instance of the recovery group class for the given group name

            Raises:
                SDKException:
                    if type of the group name argument is not string

                    if no group exists with the given name

        &#34;&#34;&#34;
        if not isinstance(recovery_group_name, str):
            raise SDKException(&#39;RecoveryGroup&#39;, &#39;101&#39;)
        else:
            if self.has_recovery_group(recovery_group_name):
                return RecoveryGroup(
                    self._commcell_object,
                    recovery_group_name,
                    self.all_groups[recovery_group_name]
                )

            raise SDKException(&#39;RecoveryGroup&#39;, &#39;102&#39;,
                               &#39;No recovery group exists with name: {0}&#39;.format(recovery_group_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the recovery groups&#34;&#34;&#34;
        self._recovery_groups = self._get_recovery_groups()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all recovery groups .

            Returns:
                str     -   string of all the groups

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;RecoveryGroup&#39;)

        for index, group in enumerate(self._recovery_groups):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(
                index + 1,
                group
            )
            representation_string += sub_str

        return representation_string.strip()

    def _get_recovery_groups(self):
        &#34;&#34;&#34;Gets all the recovery groups.

            Returns:
                dict - consists of all recovery groups in the client
                    {
                         &#34;group1_name&#34;: group1_id,
                         &#34;group2_name&#34;: group2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_GROUPS_URL)

        if flag:
            try:
                json_resp = response.json()

                group_name_id_dict = {group[&#39;name&#39;]: group[&#39;id&#39;] for group in json_resp[&#39;recoveryGroups&#39;]}

                return group_name_id_dict
            except (JSONDecodeError, KeyError):
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroups.all_groups"><code class="name">var <span class="ident">all_groups</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all recovery groups.</p>
<p>Returns dict
-
consists of all groups</p>
<pre><code>   {
        "group1_name": group1_id,
        "group2_name": group2_id
   }
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L51-L63" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_groups(self):
    &#34;&#34;&#34;Returns dict of all recovery groups.

     Returns dict    -   consists of all groups

            {
                 &#34;group1_name&#34;: group1_id,
                 &#34;group2_name&#34;: group2_id
            }

    &#34;&#34;&#34;
    return self._recovery_groups</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroups.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, recovery_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a recovery group object.</p>
<h2 id="args">Args</h2>
<p>recovery_group_name (str)
&ndash;
name of the recovery group</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the recovery group class for the given group name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the group name argument is not string</p>
<pre><code>if no group exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L84-L111" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, recovery_group_name):
    &#34;&#34;&#34;Returns a recovery group object.

        Args:
            recovery_group_name (str)  --  name of the recovery group

        Returns:
            object - instance of the recovery group class for the given group name

        Raises:
            SDKException:
                if type of the group name argument is not string

                if no group exists with the given name

    &#34;&#34;&#34;
    if not isinstance(recovery_group_name, str):
        raise SDKException(&#39;RecoveryGroup&#39;, &#39;101&#39;)
    else:
        if self.has_recovery_group(recovery_group_name):
            return RecoveryGroup(
                self._commcell_object,
                recovery_group_name,
                self.all_groups[recovery_group_name]
            )

        raise SDKException(&#39;RecoveryGroup&#39;, &#39;102&#39;,
                           &#39;No recovery group exists with name: {0}&#39;.format(recovery_group_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroups.has_recovery_group"><code class="name flex">
<span>def <span class="ident">has_recovery_group</span></span>(<span>self, recovery_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a recovery group is present in the commcell.</p>
<h2 id="args">Args</h2>
<p>recovery_group_name (str)
&ndash;
name of the recovery group</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the group is present in commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the group name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L65-L82" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_recovery_group(self, recovery_group_name):
    &#34;&#34;&#34;Checks if a recovery group is present in the commcell.

        Args:
            recovery_group_name (str)  --  name of the recovery group

        Returns:
            bool - boolean output whether the group is present in commcell or not

        Raises:
            SDKException:
                if type of the group name argument is not string

    &#34;&#34;&#34;
    if not isinstance(recovery_group_name, str):
        raise SDKException(&#39;RecoveryGroup&#39;, &#39;101&#39;)

    return self._recovery_groups and recovery_group_name in self._recovery_groups</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryGroups.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the recovery groups</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L113-L115" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the recovery groups&#34;&#34;&#34;
    self._recovery_groups = self._get_recovery_groups()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus"><code class="flex name class">
<span>class <span class="ident">RecoveryStatus</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/recovery_groups.py#L167-L176" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RecoveryStatus(Enum):
    NO_STATUS = 0
    NONE = 0
    NOT_READY = 1
    READY = 2
    RECOVERED = 3
    FAILED = 4
    RECOVERED_WITH_ERRORS = 5
    IN_PROGRESS = 6
    CLEANED_UP = 7</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.CLEANED_UP"><code class="name">var <span class="ident">CLEANED_UP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.FAILED"><code class="name">var <span class="ident">FAILED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.IN_PROGRESS"><code class="name">var <span class="ident">IN_PROGRESS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.NONE"><code class="name">var <span class="ident">NONE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.NOT_READY"><code class="name">var <span class="ident">NOT_READY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.NO_STATUS"><code class="name">var <span class="ident">NO_STATUS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.READY"><code class="name">var <span class="ident">READY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.RECOVERED"><code class="name">var <span class="ident">RECOVERED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.RECOVERED_WITH_ERRORS"><code class="name">var <span class="ident">RECOVERED_WITH_ERRORS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.cleanroom" href="index.html">cvpysdk.cleanroom</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroup" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroup">RecoveryGroup</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.delete" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroup.delete">delete</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.entities" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroup.entities">entities</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.id" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroup.id">id</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.recover_all" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroup.recover_all">recover_all</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroup.refresh" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroup.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroups" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroups">RecoveryGroups</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroups.all_groups" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroups.all_groups">all_groups</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroups.get" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroups.get">get</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroups.has_recovery_group" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroups.has_recovery_group">has_recovery_group</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryGroups.refresh" href="#cvpysdk.cleanroom.recovery_groups.RecoveryGroups.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus">RecoveryStatus</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.CLEANED_UP" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus.CLEANED_UP">CLEANED_UP</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.FAILED" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus.FAILED">FAILED</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.IN_PROGRESS" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus.IN_PROGRESS">IN_PROGRESS</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.NONE" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus.NONE">NONE</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.NOT_READY" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus.NOT_READY">NOT_READY</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.NO_STATUS" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus.NO_STATUS">NO_STATUS</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.READY" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus.READY">READY</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.RECOVERED" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus.RECOVERED">RECOVERED</a></code></li>
<li><code><a title="cvpysdk.cleanroom.recovery_groups.RecoveryStatus.RECOVERED_WITH_ERRORS" href="#cvpysdk.cleanroom.recovery_groups.RecoveryStatus.RECOVERED_WITH_ERRORS">RECOVERED_WITH_ERRORS</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>