<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.informixinstance API documentation</title>
<meta name="description" content="File for operating on a Informix Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.informixinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Informix Instance.</p>
<p>InformixInstance is the only class defined in this file.</p>
<p>InformixInstance: Derived class from Instance Base class, representing an
Informix instance, and to perform operations on that instance</p>
<h1 id="informixinstance">InformixInstance:</h1>
<pre><code>__init__()                          -- initialize object of the Instances class

_get_instance_properties()          -- gets the properties of this instance

_get_instance_properties_json()     -- gets all the instance related properties
of Informix instance

_restore_json()                     -- returns the JSON request to pass to the API as
per the options selected by the user

restore_in_place()                  -- restores the informix data/log files specified in
the input paths list to the same location

restore_out_of_place()              -- restores the informix data/log files specified in
the input paths list to the different location

_restore_informix_option_json()     -- setter for the Informix option in restore JSON

_restore_destination_option_json()  -- setter for  the destination restore option
in restore JSON
</code></pre>
<h2 id="informixinstance-instance-attributes">Informixinstance Instance Attributes</h2>
<pre><code>**informix_directory**          --  returns the informix directory path of informix server

**informix_user**               --  returns the informix username

**on_config_file**              --  returns the on config file name of informix server

**sql_host_file**               --  returns the sql host file path of informix server

**log_storage_policy_name**     --  returns the log backup storage policy name

**log_storage_policy_id**       --  returns the log backup storage policy id

**command_line_sp_name**        --  returns command line storage policy name

**command_line_sp_id**          --  returns command line storage policy id
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L1-L447" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
File for operating on a Informix Instance.

InformixInstance is the only class defined in this file.

InformixInstance: Derived class from Instance Base class, representing an
                    Informix instance, and to perform operations on that instance

InformixInstance:
=================

    __init__()                          -- initialize object of the Instances class

    _get_instance_properties()          -- gets the properties of this instance

    _get_instance_properties_json()     -- gets all the instance related properties
    of Informix instance

    _restore_json()                     -- returns the JSON request to pass to the API as
    per the options selected by the user

    restore_in_place()                  -- restores the informix data/log files specified in
    the input paths list to the same location

    restore_out_of_place()              -- restores the informix data/log files specified in
    the input paths list to the different location

    _restore_informix_option_json()     -- setter for the Informix option in restore JSON

    _restore_destination_option_json()  -- setter for  the destination restore option
    in restore JSON


InformixInstance instance Attributes
------------------------------------

    **informix_directory**          --  returns the informix directory path of informix server

    **informix_user**               --  returns the informix username

    **on_config_file**              --  returns the on config file name of informix server

    **sql_host_file**               --  returns the sql host file path of informix server

    **log_storage_policy_name**     --  returns the log backup storage policy name

    **log_storage_policy_id**       --  returns the log backup storage policy id

    **command_line_sp_name**        --  returns command line storage policy name

    **command_line_sp_id**          --  returns command line storage policy id

&#34;&#34;&#34;

from __future__ import unicode_literals
from ..instance import Instance
from ..exception import SDKException


class InformixInstance(Instance):
    &#34;&#34;&#34;
    Class to represent a standalone Informix Instance
    &#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id):
        &#34;&#34;&#34;Initialize object of the Instances class.

            Args:
                agent_object (object)  --  instance of the Agent class

                instance_name          --   Name of the instance

                instance_id            --   ID of the instance

            Returns:
                object - instance of the Instances class

        &#34;&#34;&#34;
        self._instance = None
        self._destination_restore_json = None
        self.informix_restore_json = None
        self._informix_instance = None
        super(InformixInstance, self).__init__(agent_object, instance_name, instance_id)

    @property
    def informix_directory(self):
        &#34;&#34;&#34; Returns the informix directory path of informix server &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;].get(&#39;informixDir&#39;, None)

    @property
    def informix_user(self):
        &#34;&#34;&#34; Returns the informix username &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixUser&#39;].get(&#39;userName&#39;, None)

    @property
    def on_config_file(self):
        &#34;&#34;&#34; Returns the on config file name of informix server. &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;].get(&#39;onConfigFile&#39;, None)

    @property
    def sql_host_file(self):
        &#34;&#34;&#34; Returns the sql host file path of informix server. &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;].get(&#39;sqlHostfile&#39;, None)

    @property
    def log_storage_policy_name(self):
        &#34;&#34;&#34; Returns the log backup storage policy name &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
            &#39;logBackupStoragePolicy&#39;].get(&#39;storagePolicyName&#39;, None)

    @log_storage_policy_name.setter
    def log_storage_policy_name(self, storage_policy):
        &#34;&#34;&#34; Setter for informix instance log_storage_policy name

            Args:

                storage_policy (str)  -- storage_policy_name

        &#34;&#34;&#34;
        content = self._informix_instance[&#39;informixStorageDevice&#39;]
        content[&#39;logBackupStoragePolicy&#39;] = {
            &#39;storagePolicyName&#39;: storage_policy
        }
        content = {
            &#39;informixStorageDevice&#39;: content
        }
        self._set_instance_properties(&#39;_informix_instance&#39;, content)

    @property
    def log_storage_policy_id(self):
        &#34;&#34;&#34; Returns the log backup storage policy id &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
            &#39;logBackupStoragePolicy&#39;].get(&#39;storagePolicyId&#39;, None)

    @property
    def command_line_sp_name(self):
        &#34;&#34;&#34; Returns command line storage policy name &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
            &#39;commandLineStoragePolicy&#39;].get(&#39;storagePolicyName&#39;, None)

    @command_line_sp_name.setter
    def command_line_sp_name(self, storage_policy):
        &#34;&#34;&#34; Setter for informix instance command_line_sp name

            Args:

                storage_policy (str)  -- storage_policy_name

        &#34;&#34;&#34;
        content = self._informix_instance[&#39;informixStorageDevice&#39;]
        content[&#39;commandLineStoragePolicy&#39;] = {
            &#39;storagePolicyName&#39;: storage_policy
        }
        content = {
            &#39;informixStorageDevice&#39;: content
        }
        self._set_instance_properties(&#39;_informix_instance&#39;, content)

    @property
    def command_line_sp_id(self):
        &#34;&#34;&#34; Returns command line storage policy id &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
            &#39;commandLineStoragePolicy&#39;].get(&#39;storagePolicyId&#39;, None)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(InformixInstance, self)._get_instance_properties()
        self._informix_instance = self._properties[&#39;informixInstance&#39;]

    def _get_instance_properties_json(self):
        &#34;&#34;&#34; Gets all the instance related properties of Informix instance.

           Returns:
                dict - all instance properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;:
                {
                    &#34;instance&#34;: self._instance,
                    &#34;informixInstance&#34;: self._informix_instance
                }
        }
        return instance_json

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the
        options selected by the user

            Args:
                kwargs   (list)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(InformixInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        self._restore_informix_option_json(restore_option)
        if restore_option.get(&#39;out_of_place&#39;):
            self._restore_destination_option_json(restore_option)
            rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;destination&#34;] = self._destination_restore_json
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;informixOption&#34;] = self.informix_restore_json
        return rest_json

    def restore_in_place(
            self,
            path,
            restore_type=&#34;ENTIRE INSTANCE&#34;,
            copy_precedence=None,
            physical_restore=True,
            logical_restore=True,
            restore_option_type=&#34;NORMAL&#34;,
            to_time=None,
            upto_log=None):
        &#34;&#34;&#34;Restores the informix data/log files specified in the input\
                paths list to the same location.

            Args:

                path                (list)  --  List of dbspaces to be restored

                restore_type        (str)   --  Restore type for informix instance

                copy_precedence     (int)   --  Copy precedence associted with storage
                policy

                physical_restore    (bool)  --  Physical restore flag

                logical_restore     (bool)  --  Logical restore flag

                    Accepted Values:

                        ENTIRE INSTANCE/WHOLE SYSTEM

                restore_option_type (str)   -- Restore option type for Informix instance

                    Accepted values:

                        NORMAL/POINT_IN_TIME/UPTO_LOGICAL_LOG

                to_time             (str)   -- time range to perform point in time restore

                    Accepted Format:

                        YYYY-MM-DD HH:MM:SS

                upto_log            (int)   -- logical log number to perform restore
                upto that log

            Returns:

                object - instance of the Job class for this restore job

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(path, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if path == []:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        restore_types_dict = {
            &#34;ENTIRE INSTANCE&#34;:1,
            &#34;WHOLE SYSTEM&#34;:2
        }

        restore_option_type_dict = {
            &#34;NORMAL&#34;: 0,
            &#34;POINT_IN_TIME&#34;: 1,
            &#34;UPTO_LOGICAL_LOG&#34;: 2
        }

        request_json = self._restore_json(
            paths=path,
            restore_type=restore_types_dict[restore_type.upper()],
            copy_precedence=copy_precedence,
            physical_restore=physical_restore,
            logical_restore=logical_restore,
            restore_option_type=restore_option_type_dict[restore_option_type.upper()],
            to_time=to_time,
            upto_log=upto_log)
        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            path,
            dest_client_name,
            dest_instance_name,
            restore_type=&#34;ENTIRE INSTANCE&#34;,
            copy_precedence=None,
            physical_restore=True,
            logical_restore=True):
        &#34;&#34;&#34;Restores the informix data/log files specified in the input\
                paths list to the different location.

            Args:

                path                (list)  --  List of dbspaces to be restored

                dest_client_name    (str)   --  Name of the destination client

                dest_instance_name  (str)   --  name of destination instance

                restore_type        (str)   --  Restore type for informix instance

                copy_precedence     (int)   --  Copy precedence associted with storage
                policy

                physical_restore    (bool)  --  Physical restore flag

                logical_restore     (bool)  --  Logical restore flag

                    Accepted Values:

                        ENTIRE INSTANCE/WHOLE SYSTEM

            Returns:

                object - instance of the Job class for this restore job

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(path, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if path == []:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        restore_types_dict = {
            &#34;ENTIRE INSTANCE&#34;:1,
            &#34;WHOLE SYSTEM&#34;:2
        }

        request_json = self._restore_json(
            paths=path,
            restore_type=restore_types_dict[restore_type.upper()],
            copy_precedence=copy_precedence,
            physical_restore=physical_restore,
            logical_restore=logical_restore,
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            out_of_place=True)
        return self._process_restore_response(request_json)

    def _restore_informix_option_json(self, value):
        &#34;&#34;&#34;setter for the Informix option in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        restore_time_dict = {}
        if value.get(&#39;to_time&#39;):
            restore_time_dict[&#39;timeValue&#39;] = value.get(&#39;to_time&#39;)
        last_log_number = 0
        if value.get(&#39;upto_log&#39;):
            last_log_number = value.get(&#39;upto_log&#39;)
        self.informix_restore_json = {
            &#34;restoreOnConfigFile&#34;: True,
            &#34;informixRestoreOptionType&#34;: value.get(&#34;restore_option_type&#34;, 0),
            &#34;numRestoreStreams&#34;: 2,
            &#34;restoreEmergencyBootFile&#34;: True,
            &#34;informixRestoreType&#34;: value.get(&#34;restore_type&#34;, &#34;&#34;),
            &#34;logicalLogNumber&#34;: last_log_number,
            &#34;physical&#34;: value.get(&#34;physical_restore&#34;, &#34;&#34;),
            &#34;logical&#34;: value.get(&#34;logical_restore&#34;, &#34;&#34;),
            &#34;restoreTime&#34;: restore_time_dict,
            &#34;timeZone&#34;: {
                &#34;TimeZoneName&#34;: &#34;(UTC) Coordinated Universal Time&#34;
            }
        }

    def _restore_destination_option_json(self, value):
        &#34;&#34;&#34;setter for  the destination restore option in restore JSON&#34;&#34;&#34;
        instance_id = &#34;&#34;
        if value.get(&#34;dest_client_name&#34;) and value.get(&#34;dest_instance_name&#34;):
            instance_id = self._commcell_object.clients.get(
                value.get(&#34;dest_client_name&#34;)).agents.get(
                    &#39;informix&#39;).instances.all_instances[value.get(&#34;dest_instance_name&#34;)]
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        self._destination_restore_json = {
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;),
            },
            &#34;destinationInstance&#34;: {
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;instanceId&#34;: int(instance_id)
            }
        }</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.informixinstance.InformixInstance"><code class="flex name class">
<span>class <span class="ident">InformixInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent a standalone Informix Instance</p>
<p>Initialize object of the Instances class.</p>
<h2 id="args">Args</h2>
<p>agent_object (object)
&ndash;
instance of the Agent class</p>
<p>instance_name
&ndash;
Name of the instance</p>
<p>instance_id
&ndash;
ID of the instance</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instances class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L78-L447" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class InformixInstance(Instance):
    &#34;&#34;&#34;
    Class to represent a standalone Informix Instance
    &#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id):
        &#34;&#34;&#34;Initialize object of the Instances class.

            Args:
                agent_object (object)  --  instance of the Agent class

                instance_name          --   Name of the instance

                instance_id            --   ID of the instance

            Returns:
                object - instance of the Instances class

        &#34;&#34;&#34;
        self._instance = None
        self._destination_restore_json = None
        self.informix_restore_json = None
        self._informix_instance = None
        super(InformixInstance, self).__init__(agent_object, instance_name, instance_id)

    @property
    def informix_directory(self):
        &#34;&#34;&#34; Returns the informix directory path of informix server &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;].get(&#39;informixDir&#39;, None)

    @property
    def informix_user(self):
        &#34;&#34;&#34; Returns the informix username &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixUser&#39;].get(&#39;userName&#39;, None)

    @property
    def on_config_file(self):
        &#34;&#34;&#34; Returns the on config file name of informix server. &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;].get(&#39;onConfigFile&#39;, None)

    @property
    def sql_host_file(self):
        &#34;&#34;&#34; Returns the sql host file path of informix server. &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;].get(&#39;sqlHostfile&#39;, None)

    @property
    def log_storage_policy_name(self):
        &#34;&#34;&#34; Returns the log backup storage policy name &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
            &#39;logBackupStoragePolicy&#39;].get(&#39;storagePolicyName&#39;, None)

    @log_storage_policy_name.setter
    def log_storage_policy_name(self, storage_policy):
        &#34;&#34;&#34; Setter for informix instance log_storage_policy name

            Args:

                storage_policy (str)  -- storage_policy_name

        &#34;&#34;&#34;
        content = self._informix_instance[&#39;informixStorageDevice&#39;]
        content[&#39;logBackupStoragePolicy&#39;] = {
            &#39;storagePolicyName&#39;: storage_policy
        }
        content = {
            &#39;informixStorageDevice&#39;: content
        }
        self._set_instance_properties(&#39;_informix_instance&#39;, content)

    @property
    def log_storage_policy_id(self):
        &#34;&#34;&#34; Returns the log backup storage policy id &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
            &#39;logBackupStoragePolicy&#39;].get(&#39;storagePolicyId&#39;, None)

    @property
    def command_line_sp_name(self):
        &#34;&#34;&#34; Returns command line storage policy name &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
            &#39;commandLineStoragePolicy&#39;].get(&#39;storagePolicyName&#39;, None)

    @command_line_sp_name.setter
    def command_line_sp_name(self, storage_policy):
        &#34;&#34;&#34; Setter for informix instance command_line_sp name

            Args:

                storage_policy (str)  -- storage_policy_name

        &#34;&#34;&#34;
        content = self._informix_instance[&#39;informixStorageDevice&#39;]
        content[&#39;commandLineStoragePolicy&#39;] = {
            &#39;storagePolicyName&#39;: storage_policy
        }
        content = {
            &#39;informixStorageDevice&#39;: content
        }
        self._set_instance_properties(&#39;_informix_instance&#39;, content)

    @property
    def command_line_sp_id(self):
        &#34;&#34;&#34; Returns command line storage policy id &#34;&#34;&#34;
        return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
            &#39;commandLineStoragePolicy&#39;].get(&#39;storagePolicyId&#39;, None)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(InformixInstance, self)._get_instance_properties()
        self._informix_instance = self._properties[&#39;informixInstance&#39;]

    def _get_instance_properties_json(self):
        &#34;&#34;&#34; Gets all the instance related properties of Informix instance.

           Returns:
                dict - all instance properties put inside a dict

        &#34;&#34;&#34;
        instance_json = {
            &#34;instanceProperties&#34;:
                {
                    &#34;instance&#34;: self._instance,
                    &#34;informixInstance&#34;: self._informix_instance
                }
        }
        return instance_json

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the
        options selected by the user

            Args:
                kwargs   (list)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(InformixInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        self._restore_informix_option_json(restore_option)
        if restore_option.get(&#39;out_of_place&#39;):
            self._restore_destination_option_json(restore_option)
            rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;destination&#34;] = self._destination_restore_json
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;informixOption&#34;] = self.informix_restore_json
        return rest_json

    def restore_in_place(
            self,
            path,
            restore_type=&#34;ENTIRE INSTANCE&#34;,
            copy_precedence=None,
            physical_restore=True,
            logical_restore=True,
            restore_option_type=&#34;NORMAL&#34;,
            to_time=None,
            upto_log=None):
        &#34;&#34;&#34;Restores the informix data/log files specified in the input\
                paths list to the same location.

            Args:

                path                (list)  --  List of dbspaces to be restored

                restore_type        (str)   --  Restore type for informix instance

                copy_precedence     (int)   --  Copy precedence associted with storage
                policy

                physical_restore    (bool)  --  Physical restore flag

                logical_restore     (bool)  --  Logical restore flag

                    Accepted Values:

                        ENTIRE INSTANCE/WHOLE SYSTEM

                restore_option_type (str)   -- Restore option type for Informix instance

                    Accepted values:

                        NORMAL/POINT_IN_TIME/UPTO_LOGICAL_LOG

                to_time             (str)   -- time range to perform point in time restore

                    Accepted Format:

                        YYYY-MM-DD HH:MM:SS

                upto_log            (int)   -- logical log number to perform restore
                upto that log

            Returns:

                object - instance of the Job class for this restore job

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(path, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if path == []:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        restore_types_dict = {
            &#34;ENTIRE INSTANCE&#34;:1,
            &#34;WHOLE SYSTEM&#34;:2
        }

        restore_option_type_dict = {
            &#34;NORMAL&#34;: 0,
            &#34;POINT_IN_TIME&#34;: 1,
            &#34;UPTO_LOGICAL_LOG&#34;: 2
        }

        request_json = self._restore_json(
            paths=path,
            restore_type=restore_types_dict[restore_type.upper()],
            copy_precedence=copy_precedence,
            physical_restore=physical_restore,
            logical_restore=logical_restore,
            restore_option_type=restore_option_type_dict[restore_option_type.upper()],
            to_time=to_time,
            upto_log=upto_log)
        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            path,
            dest_client_name,
            dest_instance_name,
            restore_type=&#34;ENTIRE INSTANCE&#34;,
            copy_precedence=None,
            physical_restore=True,
            logical_restore=True):
        &#34;&#34;&#34;Restores the informix data/log files specified in the input\
                paths list to the different location.

            Args:

                path                (list)  --  List of dbspaces to be restored

                dest_client_name    (str)   --  Name of the destination client

                dest_instance_name  (str)   --  name of destination instance

                restore_type        (str)   --  Restore type for informix instance

                copy_precedence     (int)   --  Copy precedence associted with storage
                policy

                physical_restore    (bool)  --  Physical restore flag

                logical_restore     (bool)  --  Logical restore flag

                    Accepted Values:

                        ENTIRE INSTANCE/WHOLE SYSTEM

            Returns:

                object - instance of the Job class for this restore job

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(path, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if path == []:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        restore_types_dict = {
            &#34;ENTIRE INSTANCE&#34;:1,
            &#34;WHOLE SYSTEM&#34;:2
        }

        request_json = self._restore_json(
            paths=path,
            restore_type=restore_types_dict[restore_type.upper()],
            copy_precedence=copy_precedence,
            physical_restore=physical_restore,
            logical_restore=logical_restore,
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            out_of_place=True)
        return self._process_restore_response(request_json)

    def _restore_informix_option_json(self, value):
        &#34;&#34;&#34;setter for the Informix option in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        restore_time_dict = {}
        if value.get(&#39;to_time&#39;):
            restore_time_dict[&#39;timeValue&#39;] = value.get(&#39;to_time&#39;)
        last_log_number = 0
        if value.get(&#39;upto_log&#39;):
            last_log_number = value.get(&#39;upto_log&#39;)
        self.informix_restore_json = {
            &#34;restoreOnConfigFile&#34;: True,
            &#34;informixRestoreOptionType&#34;: value.get(&#34;restore_option_type&#34;, 0),
            &#34;numRestoreStreams&#34;: 2,
            &#34;restoreEmergencyBootFile&#34;: True,
            &#34;informixRestoreType&#34;: value.get(&#34;restore_type&#34;, &#34;&#34;),
            &#34;logicalLogNumber&#34;: last_log_number,
            &#34;physical&#34;: value.get(&#34;physical_restore&#34;, &#34;&#34;),
            &#34;logical&#34;: value.get(&#34;logical_restore&#34;, &#34;&#34;),
            &#34;restoreTime&#34;: restore_time_dict,
            &#34;timeZone&#34;: {
                &#34;TimeZoneName&#34;: &#34;(UTC) Coordinated Universal Time&#34;
            }
        }

    def _restore_destination_option_json(self, value):
        &#34;&#34;&#34;setter for  the destination restore option in restore JSON&#34;&#34;&#34;
        instance_id = &#34;&#34;
        if value.get(&#34;dest_client_name&#34;) and value.get(&#34;dest_instance_name&#34;):
            instance_id = self._commcell_object.clients.get(
                value.get(&#34;dest_client_name&#34;)).agents.get(
                    &#39;informix&#39;).instances.all_instances[value.get(&#34;dest_instance_name&#34;)]
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        self._destination_restore_json = {
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;),
            },
            &#34;destinationInstance&#34;: {
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;instanceId&#34;: int(instance_id)
            }
        }</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.command_line_sp_id"><code class="name">var <span class="ident">command_line_sp_id</span></code></dt>
<dd>
<div class="desc"><p>Returns command line storage policy id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L177-L181" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def command_line_sp_id(self):
    &#34;&#34;&#34; Returns command line storage policy id &#34;&#34;&#34;
    return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
        &#39;commandLineStoragePolicy&#39;].get(&#39;storagePolicyId&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.command_line_sp_name"><code class="name">var <span class="ident">command_line_sp_name</span></code></dt>
<dd>
<div class="desc"><p>Returns command line storage policy name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L153-L157" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def command_line_sp_name(self):
    &#34;&#34;&#34; Returns command line storage policy name &#34;&#34;&#34;
    return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
        &#39;commandLineStoragePolicy&#39;].get(&#39;storagePolicyName&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.informix_directory"><code class="name">var <span class="ident">informix_directory</span></code></dt>
<dd>
<div class="desc"><p>Returns the informix directory path of informix server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L103-L106" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def informix_directory(self):
    &#34;&#34;&#34; Returns the informix directory path of informix server &#34;&#34;&#34;
    return self._properties[&#39;informixInstance&#39;].get(&#39;informixDir&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.informix_user"><code class="name">var <span class="ident">informix_user</span></code></dt>
<dd>
<div class="desc"><p>Returns the informix username</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L108-L111" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def informix_user(self):
    &#34;&#34;&#34; Returns the informix username &#34;&#34;&#34;
    return self._properties[&#39;informixInstance&#39;][&#39;informixUser&#39;].get(&#39;userName&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.log_storage_policy_id"><code class="name">var <span class="ident">log_storage_policy_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the log backup storage policy id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L147-L151" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def log_storage_policy_id(self):
    &#34;&#34;&#34; Returns the log backup storage policy id &#34;&#34;&#34;
    return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
        &#39;logBackupStoragePolicy&#39;].get(&#39;storagePolicyId&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.log_storage_policy_name"><code class="name">var <span class="ident">log_storage_policy_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the log backup storage policy name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L123-L127" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def log_storage_policy_name(self):
    &#34;&#34;&#34; Returns the log backup storage policy name &#34;&#34;&#34;
    return self._properties[&#39;informixInstance&#39;][&#39;informixStorageDevice&#39;][
        &#39;logBackupStoragePolicy&#39;].get(&#39;storagePolicyName&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.on_config_file"><code class="name">var <span class="ident">on_config_file</span></code></dt>
<dd>
<div class="desc"><p>Returns the on config file name of informix server.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L113-L116" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def on_config_file(self):
    &#34;&#34;&#34; Returns the on config file name of informix server. &#34;&#34;&#34;
    return self._properties[&#39;informixInstance&#39;].get(&#39;onConfigFile&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.sql_host_file"><code class="name">var <span class="ident">sql_host_file</span></code></dt>
<dd>
<div class="desc"><p>Returns the sql host file path of informix server.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L118-L121" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sql_host_file(self):
    &#34;&#34;&#34; Returns the sql host file path of informix server. &#34;&#34;&#34;
    return self._properties[&#39;informixInstance&#39;].get(&#39;sqlHostfile&#39;, None)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, path, restore_type='ENTIRE INSTANCE', copy_precedence=None, physical_restore=True, logical_restore=True, restore_option_type='NORMAL', to_time=None, upto_log=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the informix data/log files specified in the input
paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>path
(list)
&ndash;
List of dbspaces to be restored</p>
<p>restore_type
(str)
&ndash;
Restore type for informix instance</p>
<p>copy_precedence
(int)
&ndash;
Copy precedence associted with storage
policy</p>
<p>physical_restore
(bool)
&ndash;
Physical restore flag</p>
<p>logical_restore
(bool)
&ndash;
Logical restore flag</p>
<pre><code>Accepted Values:

    ENTIRE INSTANCE/WHOLE SYSTEM
</code></pre>
<p>restore_option_type (str)
&ndash; Restore option type for Informix instance</p>
<pre><code>Accepted values:

    NORMAL/POINT_IN_TIME/UPTO_LOGICAL_LOG
</code></pre>
<p>to_time
(str)
&ndash; time range to perform point in time restore</p>
<pre><code>Accepted Format:

    YYYY-MM-DD HH:MM:SS
</code></pre>
<p>upto_log
(int)
&ndash; logical log number to perform restore
upto that log</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L242-L330" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        path,
        restore_type=&#34;ENTIRE INSTANCE&#34;,
        copy_precedence=None,
        physical_restore=True,
        logical_restore=True,
        restore_option_type=&#34;NORMAL&#34;,
        to_time=None,
        upto_log=None):
    &#34;&#34;&#34;Restores the informix data/log files specified in the input\
            paths list to the same location.

        Args:

            path                (list)  --  List of dbspaces to be restored

            restore_type        (str)   --  Restore type for informix instance

            copy_precedence     (int)   --  Copy precedence associted with storage
            policy

            physical_restore    (bool)  --  Physical restore flag

            logical_restore     (bool)  --  Logical restore flag

                Accepted Values:

                    ENTIRE INSTANCE/WHOLE SYSTEM

            restore_option_type (str)   -- Restore option type for Informix instance

                Accepted values:

                    NORMAL/POINT_IN_TIME/UPTO_LOGICAL_LOG

            to_time             (str)   -- time range to perform point in time restore

                Accepted Format:

                    YYYY-MM-DD HH:MM:SS

            upto_log            (int)   -- logical log number to perform restore
            upto that log

        Returns:

            object - instance of the Job class for this restore job

        Raises:

            SDKException:

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not isinstance(path, list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if path == []:
        raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

    restore_types_dict = {
        &#34;ENTIRE INSTANCE&#34;:1,
        &#34;WHOLE SYSTEM&#34;:2
    }

    restore_option_type_dict = {
        &#34;NORMAL&#34;: 0,
        &#34;POINT_IN_TIME&#34;: 1,
        &#34;UPTO_LOGICAL_LOG&#34;: 2
    }

    request_json = self._restore_json(
        paths=path,
        restore_type=restore_types_dict[restore_type.upper()],
        copy_precedence=copy_precedence,
        physical_restore=physical_restore,
        logical_restore=logical_restore,
        restore_option_type=restore_option_type_dict[restore_option_type.upper()],
        to_time=to_time,
        upto_log=upto_log)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.informixinstance.InformixInstance.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, path, dest_client_name, dest_instance_name, restore_type='ENTIRE INSTANCE', copy_precedence=None, physical_restore=True, logical_restore=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the informix data/log files specified in the input
paths list to the different location.</p>
<h2 id="args">Args</h2>
<p>path
(list)
&ndash;
List of dbspaces to be restored</p>
<p>dest_client_name
(str)
&ndash;
Name of the destination client</p>
<p>dest_instance_name
(str)
&ndash;
name of destination instance</p>
<p>restore_type
(str)
&ndash;
Restore type for informix instance</p>
<p>copy_precedence
(int)
&ndash;
Copy precedence associted with storage
policy</p>
<p>physical_restore
(bool)
&ndash;
Physical restore flag</p>
<p>logical_restore
(bool)
&ndash;
Logical restore flag</p>
<pre><code>Accepted Values:

    ENTIRE INSTANCE/WHOLE SYSTEM
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/informixinstance.py#L332-L402" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        path,
        dest_client_name,
        dest_instance_name,
        restore_type=&#34;ENTIRE INSTANCE&#34;,
        copy_precedence=None,
        physical_restore=True,
        logical_restore=True):
    &#34;&#34;&#34;Restores the informix data/log files specified in the input\
            paths list to the different location.

        Args:

            path                (list)  --  List of dbspaces to be restored

            dest_client_name    (str)   --  Name of the destination client

            dest_instance_name  (str)   --  name of destination instance

            restore_type        (str)   --  Restore type for informix instance

            copy_precedence     (int)   --  Copy precedence associted with storage
            policy

            physical_restore    (bool)  --  Physical restore flag

            logical_restore     (bool)  --  Logical restore flag

                Accepted Values:

                    ENTIRE INSTANCE/WHOLE SYSTEM

        Returns:

            object - instance of the Job class for this restore job

        Raises:

            SDKException:

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not isinstance(path, list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if path == []:
        raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

    restore_types_dict = {
        &#34;ENTIRE INSTANCE&#34;:1,
        &#34;WHOLE SYSTEM&#34;:2
    }

    request_json = self._restore_json(
        paths=path,
        restore_type=restore_types_dict[restore_type.upper()],
        copy_precedence=copy_precedence,
        physical_restore=physical_restore,
        logical_restore=logical_restore,
        dest_client_name=dest_client_name,
        dest_instance_name=dest_instance_name,
        out_of_place=True)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instance.Instance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.browse" href="../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#informixinstance">InformixInstance:</a><ul>
<li><a href="#informixinstance-instance-attributes">InformixInstance instance Attributes</a></li>
</ul>
</li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.informixinstance.InformixInstance" href="#cvpysdk.instances.informixinstance.InformixInstance">InformixInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.command_line_sp_id" href="#cvpysdk.instances.informixinstance.InformixInstance.command_line_sp_id">command_line_sp_id</a></code></li>
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.command_line_sp_name" href="#cvpysdk.instances.informixinstance.InformixInstance.command_line_sp_name">command_line_sp_name</a></code></li>
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.informix_directory" href="#cvpysdk.instances.informixinstance.InformixInstance.informix_directory">informix_directory</a></code></li>
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.informix_user" href="#cvpysdk.instances.informixinstance.InformixInstance.informix_user">informix_user</a></code></li>
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.log_storage_policy_id" href="#cvpysdk.instances.informixinstance.InformixInstance.log_storage_policy_id">log_storage_policy_id</a></code></li>
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.log_storage_policy_name" href="#cvpysdk.instances.informixinstance.InformixInstance.log_storage_policy_name">log_storage_policy_name</a></code></li>
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.on_config_file" href="#cvpysdk.instances.informixinstance.InformixInstance.on_config_file">on_config_file</a></code></li>
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.restore_in_place" href="#cvpysdk.instances.informixinstance.InformixInstance.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.restore_out_of_place" href="#cvpysdk.instances.informixinstance.InformixInstance.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.instances.informixinstance.InformixInstance.sql_host_file" href="#cvpysdk.instances.informixinstance.InformixInstance.sql_host_file">sql_host_file</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>