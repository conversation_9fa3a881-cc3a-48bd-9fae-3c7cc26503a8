<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.lotusnotes.lndbinstance API documentation</title>
<meta name="description" content="File for operating on a Lotus Notes Database Agent Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.lotusnotes.lndbinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Lotus Notes Database Agent Instance.</p>
<p>LNDBInstance is the only class defined in this file.</p>
<h2 id="lndbinstance">Lndbinstance</h2>
<p>restore_in_place()
&ndash;
performs an in place restore of the subclient</p>
<p>restore_out_of_place()
&ndash;
performs an out of place restore of the subclient</p>
<p>_restore_common_options_json()
&ndash;
setter for
the Common options in restore JSON</p>
<p>_restore_json()
&ndash;
returns the JSON request to pass to the API
as per the options selected by the user</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/lotusnotes/lndbinstance.py#L1-L374" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Lotus Notes Database Agent Instance.

LNDBInstance is the only class defined in this file.

LNDBInstance:
    restore_in_place()                  --  performs an in place restore of the subclient

    restore_out_of_place()              --  performs an out of place restore of the subclient

    _restore_common_options_json()      --  setter for  the Common options in restore JSON

    _restore_json()                     --  returns the JSON request to pass to the API
    as per the options selected by the user

&#34;&#34;&#34;

from __future__ import unicode_literals

from .lninstance import LNInstance
from ...exception import SDKException


class LNDBInstance(LNInstance):
    &#34;&#34;&#34;Derived class from LNInstance Base class, representing an LNDB instance,
        and to perform operations on that instance.&#34;&#34;&#34;

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;setter for  the Common options of in restore JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._commonoption_restore_json = {
            &#34;doNotReplayTransactLogs&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;doNotReplayTransactLogs&#39;, False
            ),
            &#34;clusterDBBackedup&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;clusterDBBackedup&#39;, False
            ),
            &#34;recoverWait&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverWait&#39;, False
            ),
            &#34;restoreToDisk&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;restoreToDisk&#39;, False
            ),
            &#34;offlineMiningRestore&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;offlineMiningRestore&#39;, False
            ),
            &#34;restoreToExchange&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;restoreToExchange&#39;, False
            ),
            &#34;recoverZapIfNecessary&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverZapIfNecessary&#39;, False
            ),
            &#34;recoverZapReplica&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverZapReplica&#39;, False
            ),
            &#34;copyToObjectStore&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;copyToObjectStore&#39;, False
            ),
            &#34;onePassRestore&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;onePassRestore&#39;, False
            ),
            &#34;recoverZap&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverZap&#39;, False
            ),
            &#34;recoverRefreshBackup&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverRefreshBackup&#39;, False
            ),
            &#34;unconditionalOverwrite&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;unconditionalOverwrite&#39;, False
            ),
            &#34;syncRestore&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;syncRestore&#39;, False
            ),
            &#34;recoverPointInTime&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverPointInTime&#39;, False
            )
        }

        if value.get(&#39;common_options_dict&#39;).get(&#39;disasterRecovery&#39;):
            self._commonoption_restore_json.update({
                &#34;restoreDeviceFilesAsRegularFiles&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;restoreDeviceFilesAsRegularFiles&#39;, False
                ),
                &#34;isFromBrowseBackup&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;isFromBrowseBackup&#39;, False
                ),
                &#34;ignoreNamespaceRequirements&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;ignoreNamespaceRequirements&#39;, False
                ),
                &#34;restoreSpaceRestrictions&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;restoreSpaceRestrictions&#39;, False
                ),
                &#34;skipErrorsAndContinue&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;skipErrorsAndContinue&#39;, False
                ),
                &#34;recoverAllProtectedMails&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;recoverAllProtectedMails&#39;, False
                ),
                &#34;validateOnly&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;validateOnly&#39;, False
                ),
                &#34;revert&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;revert&#39;, False
                ),
                &#34;disasterRecovery&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;disasterRecovery&#39;, True
                ),
                &#34;detectRegularExpression&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;detectRegularExpression&#39;, True
                ),
            })

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

                   Args:
                       kwargs   (list)  --  list of options need to be set for restore

                   Returns:
                       dict - JSON request to pass to the API
               &#34;&#34;&#34;
        restore_json = super(LNDBInstance, self)._restore_json(**kwargs)

        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;lotusNotesDBRestoreOption&#39;] = {
                &#34;disableReplication&#34;: kwargs.get(&#39;lndb_restore_options&#39;).get(
                    &#39;disableReplication&#39;, False
                ),
                &#34;disableBackgroundAgents&#34;: kwargs.get(&#39;lndb_restore_options&#39;).get(
                    &#39;disableBackgroundAgents&#39;, False
                )
            }

        if kwargs.get(&#39;common_options_dict&#39;).get(&#39;disasterRecovery&#39;):
            restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;commonOpts&#39;] = {
                &#39;jobDescription&#39;: &#39;&#39;,
                &#39;startUpOpts&#39;: {
                    &#39;startInSuspendedState&#39;: False,
                    &#39;useDefaultPriority&#39;: True,
                    &#39;priority&#39;: 166
                }
            }
            restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;] = {
                &#39;backupLevel&#39;: 2
            }
            restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;browseOption&#39;][&#39;mediaOption&#39;][&#39;copyPrecedence&#39;] = {
                    &#39;copyPrecedence&#39;: 0,
                    &#39;synchronousCopyPrecedence&#39;: 1,
                    &#39;copyPrecedenceApplicable&#39;: False
                }
        return restore_json

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            **kwargs):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time               (str)   --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                common_options_dict     (dict)  -- dictionary for all the common options
                    options:
                        unconditionalOverwrite              :   overwrite the files during restore
                        even if they exist

                        recoverWait                         :   Specifies whether this restore
                        operation must wait until resources become available if a database recovery
                        is already taking place

                        recoverZap                          :   Specifies whether the IBM Domino
                        must change the DBIID associated with the restored database

                        recoverZapReplica                   :   Specifies whether the restore
                        operation changes the replica id of the restored database

                        recoverZapIfNecessary               :   Specifies whether the IBM Domino
                        can change the DBIID associated with the restored database if necessary

                        doNotReplayTransactLogs             :   option to skip restoring or
                        replaying logs


                    Disaster Recovery special options:
                        skipErrorsAndContinue               :   enables a data recovery operation
                        to continue despite media errors

                        disasterRecovery                    :   run disaster recovery

                lndb_restore_options    (dict)  -- dictionary for all options specific
                to an lndb restore
                    options:
                        disableReplication      :   disable relpication on database

                        disableBackgroundAgents :   disable background agents

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return super(LNDBInstance, self).restore_in_place(
            paths,
            overwrite,
            restore_data_and_acl,
            copy_precedence,
            from_time,
            to_time,
            **kwargs)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            **kwargs):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore

                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files

                    default: True

                copy_precedence         (int)      --  copy precedence value of storage policy copy

                    default: None

                from_time               (str)       --  time to retore the contents after

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)       --  time to retore the contents before

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                 common_options_dict    (dict)      -- dictionary for all the common options
                    options:
                        unconditionalOverwrite              :   overwrite the files during restore
                        even if they exist

                        recoverWait                         :   Specifies whether this restore
                        operation must wait until resources become available if a database recovery
                        is already taking place

                        recoverZap                          :   Specifies whether the IBM Domino
                        must change the DBIID associated with the restored database

                        recoverZapReplica                   :   Specifies whether the restore
                        operation changes the replica id of the restored database

                        recoverZapIfNecessary               :   Specifies whether the IBM Domino
                        can change the DBIID associated with the restored database if necessary

                        doNotReplayTransactLogs             :   option to skip restoring or
                        replaying logs


                    Disaster Recovery special options:
                        skipErrorsAndContinue               :   enables a data recovery operation
                        to continue despite media errors

                        disasterRecovery                    :   run disaster recovery

                lndb_restore_options    (dict)      -- dictionary for all options specific
                to an lndb restore
                    options:
                        disableReplication      :   disable relpication on database

                        disableBackgroundAgents :   disable background agents

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return super(LNDBInstance, self).restore_out_of_place(
            client,
            destination_path,
            paths,
            overwrite,
            restore_data_and_acl,
            copy_precedence,
            from_time,
            to_time,
            **kwargs)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.lotusnotes.lndbinstance.LNDBInstance"><code class="flex name class">
<span>class <span class="ident">LNDBInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from LNInstance Base class, representing an LNDB instance,
and to perform operations on that instance.</p>
<p>Initialise the instance object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/lotusnotes/lndbinstance.py#L41-L374" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class LNDBInstance(LNInstance):
    &#34;&#34;&#34;Derived class from LNInstance Base class, representing an LNDB instance,
        and to perform operations on that instance.&#34;&#34;&#34;

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;setter for  the Common options of in restore JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._commonoption_restore_json = {
            &#34;doNotReplayTransactLogs&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;doNotReplayTransactLogs&#39;, False
            ),
            &#34;clusterDBBackedup&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;clusterDBBackedup&#39;, False
            ),
            &#34;recoverWait&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverWait&#39;, False
            ),
            &#34;restoreToDisk&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;restoreToDisk&#39;, False
            ),
            &#34;offlineMiningRestore&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;offlineMiningRestore&#39;, False
            ),
            &#34;restoreToExchange&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;restoreToExchange&#39;, False
            ),
            &#34;recoverZapIfNecessary&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverZapIfNecessary&#39;, False
            ),
            &#34;recoverZapReplica&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverZapReplica&#39;, False
            ),
            &#34;copyToObjectStore&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;copyToObjectStore&#39;, False
            ),
            &#34;onePassRestore&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;onePassRestore&#39;, False
            ),
            &#34;recoverZap&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverZap&#39;, False
            ),
            &#34;recoverRefreshBackup&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverRefreshBackup&#39;, False
            ),
            &#34;unconditionalOverwrite&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;unconditionalOverwrite&#39;, False
            ),
            &#34;syncRestore&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;syncRestore&#39;, False
            ),
            &#34;recoverPointInTime&#34;: value.get(&#39;common_options_dict&#39;).get(
                &#39;recoverPointInTime&#39;, False
            )
        }

        if value.get(&#39;common_options_dict&#39;).get(&#39;disasterRecovery&#39;):
            self._commonoption_restore_json.update({
                &#34;restoreDeviceFilesAsRegularFiles&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;restoreDeviceFilesAsRegularFiles&#39;, False
                ),
                &#34;isFromBrowseBackup&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;isFromBrowseBackup&#39;, False
                ),
                &#34;ignoreNamespaceRequirements&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;ignoreNamespaceRequirements&#39;, False
                ),
                &#34;restoreSpaceRestrictions&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;restoreSpaceRestrictions&#39;, False
                ),
                &#34;skipErrorsAndContinue&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;skipErrorsAndContinue&#39;, False
                ),
                &#34;recoverAllProtectedMails&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;recoverAllProtectedMails&#39;, False
                ),
                &#34;validateOnly&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;validateOnly&#39;, False
                ),
                &#34;revert&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;revert&#39;, False
                ),
                &#34;disasterRecovery&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;disasterRecovery&#39;, True
                ),
                &#34;detectRegularExpression&#34;: value.get(&#39;common_options_dict&#39;).get(
                    &#39;detectRegularExpression&#39;, True
                ),
            })

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

                   Args:
                       kwargs   (list)  --  list of options need to be set for restore

                   Returns:
                       dict - JSON request to pass to the API
               &#34;&#34;&#34;
        restore_json = super(LNDBInstance, self)._restore_json(**kwargs)

        restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;lotusNotesDBRestoreOption&#39;] = {
                &#34;disableReplication&#34;: kwargs.get(&#39;lndb_restore_options&#39;).get(
                    &#39;disableReplication&#39;, False
                ),
                &#34;disableBackgroundAgents&#34;: kwargs.get(&#39;lndb_restore_options&#39;).get(
                    &#39;disableBackgroundAgents&#39;, False
                )
            }

        if kwargs.get(&#39;common_options_dict&#39;).get(&#39;disasterRecovery&#39;):
            restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;commonOpts&#39;] = {
                &#39;jobDescription&#39;: &#39;&#39;,
                &#39;startUpOpts&#39;: {
                    &#39;startInSuspendedState&#39;: False,
                    &#39;useDefaultPriority&#39;: True,
                    &#39;priority&#39;: 166
                }
            }
            restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;] = {
                &#39;backupLevel&#39;: 2
            }
            restore_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;browseOption&#39;][&#39;mediaOption&#39;][&#39;copyPrecedence&#39;] = {
                    &#39;copyPrecedence&#39;: 0,
                    &#39;synchronousCopyPrecedence&#39;: 1,
                    &#39;copyPrecedenceApplicable&#39;: False
                }
        return restore_json

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            **kwargs):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time               (str)   --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                common_options_dict     (dict)  -- dictionary for all the common options
                    options:
                        unconditionalOverwrite              :   overwrite the files during restore
                        even if they exist

                        recoverWait                         :   Specifies whether this restore
                        operation must wait until resources become available if a database recovery
                        is already taking place

                        recoverZap                          :   Specifies whether the IBM Domino
                        must change the DBIID associated with the restored database

                        recoverZapReplica                   :   Specifies whether the restore
                        operation changes the replica id of the restored database

                        recoverZapIfNecessary               :   Specifies whether the IBM Domino
                        can change the DBIID associated with the restored database if necessary

                        doNotReplayTransactLogs             :   option to skip restoring or
                        replaying logs


                    Disaster Recovery special options:
                        skipErrorsAndContinue               :   enables a data recovery operation
                        to continue despite media errors

                        disasterRecovery                    :   run disaster recovery

                lndb_restore_options    (dict)  -- dictionary for all options specific
                to an lndb restore
                    options:
                        disableReplication      :   disable relpication on database

                        disableBackgroundAgents :   disable background agents

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return super(LNDBInstance, self).restore_in_place(
            paths,
            overwrite,
            restore_data_and_acl,
            copy_precedence,
            from_time,
            to_time,
            **kwargs)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            **kwargs):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore

                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files

                    default: True

                copy_precedence         (int)      --  copy precedence value of storage policy copy

                    default: None

                from_time               (str)       --  time to retore the contents after

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)       --  time to retore the contents before

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                 common_options_dict    (dict)      -- dictionary for all the common options
                    options:
                        unconditionalOverwrite              :   overwrite the files during restore
                        even if they exist

                        recoverWait                         :   Specifies whether this restore
                        operation must wait until resources become available if a database recovery
                        is already taking place

                        recoverZap                          :   Specifies whether the IBM Domino
                        must change the DBIID associated with the restored database

                        recoverZapReplica                   :   Specifies whether the restore
                        operation changes the replica id of the restored database

                        recoverZapIfNecessary               :   Specifies whether the IBM Domino
                        can change the DBIID associated with the restored database if necessary

                        doNotReplayTransactLogs             :   option to skip restoring or
                        replaying logs


                    Disaster Recovery special options:
                        skipErrorsAndContinue               :   enables a data recovery operation
                        to continue despite media errors

                        disasterRecovery                    :   run disaster recovery

                lndb_restore_options    (dict)      -- dictionary for all options specific
                to an lndb restore
                    options:
                        disableReplication      :   disable relpication on database

                        disableBackgroundAgents :   disable background agents

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return super(LNDBInstance, self).restore_out_of_place(
            client,
            destination_path,
            paths,
            overwrite,
            restore_data_and_acl,
            copy_precedence,
            from_time,
            to_time,
            **kwargs)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance" href="lninstance.html#cvpysdk.instances.lotusnotes.lninstance.LNInstance">LNInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.lotusnotes.lndbinstance.LNDBInstance.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of full paths of files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>common_options_dict
(dict)
&ndash; dictionary for all the common options
options:
unconditionalOverwrite
:
overwrite the files during restore
even if they exist</p>
<pre><code>    recoverWait                         :   Specifies whether this restore
    operation must wait until resources become available if a database recovery
    is already taking place

    recoverZap                          :   Specifies whether the IBM Domino
    must change the DBIID associated with the restored database

    recoverZapReplica                   :   Specifies whether the restore
    operation changes the replica id of the restored database

    recoverZapIfNecessary               :   Specifies whether the IBM Domino
    can change the DBIID associated with the restored database if necessary

    doNotReplayTransactLogs             :   option to skip restoring or
    replaying logs


Disaster Recovery special options:
    skipErrorsAndContinue               :   enables a data recovery operation
    to continue despite media errors

    disasterRecovery                    :   run disaster recovery
</code></pre>
<p>lndb_restore_options
(dict)
&ndash; dictionary for all options specific
to an lndb restore
options:
disableReplication
:
disable relpication on database</p>
<pre><code>    disableBackgroundAgents :   disable background agents
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/lotusnotes/lndbinstance.py#L176-L264" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        **kwargs):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

        Args:
            paths                   (list)  --  list of full paths of files/folders to restore

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl    (bool)  --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            common_options_dict     (dict)  -- dictionary for all the common options
                options:
                    unconditionalOverwrite              :   overwrite the files during restore
                    even if they exist

                    recoverWait                         :   Specifies whether this restore
                    operation must wait until resources become available if a database recovery
                    is already taking place

                    recoverZap                          :   Specifies whether the IBM Domino
                    must change the DBIID associated with the restored database

                    recoverZapReplica                   :   Specifies whether the restore
                    operation changes the replica id of the restored database

                    recoverZapIfNecessary               :   Specifies whether the IBM Domino
                    can change the DBIID associated with the restored database if necessary

                    doNotReplayTransactLogs             :   option to skip restoring or
                    replaying logs


                Disaster Recovery special options:
                    skipErrorsAndContinue               :   enables a data recovery operation
                    to continue despite media errors

                    disasterRecovery                    :   run disaster recovery

            lndb_restore_options    (dict)  -- dictionary for all options specific
            to an lndb restore
                options:
                    disableReplication      :   disable relpication on database

                    disableBackgroundAgents :   disable background agents

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    return super(LNDBInstance, self).restore_in_place(
        paths,
        overwrite,
        restore_data_and_acl,
        copy_precedence,
        from_time,
        to_time,
        **kwargs)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.lotusnotes.lndbinstance.LNDBInstance.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, client, destination_path, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destionation location.</p>
<h2 id="args">Args</h2>
<p>client
(str/object) &ndash;
either the name of the client or
the instance of the Client</p>
<p>destination_path
(str)
&ndash;
full path of the restore location on client</p>
<p>paths
(list)
&ndash;
list of full paths of
files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore</p>
<pre><code>default: True
</code></pre>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files</p>
<pre><code>default: True
</code></pre>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy</p>
<pre><code>default: None
</code></pre>
<p>from_time
(str)
&ndash;
time to retore the contents after</p>
<pre><code>    format: YYYY-MM-DD HH:MM:SS

default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before</p>
<pre><code>    format: YYYY-MM-DD HH:MM:SS

default: None
</code></pre>
<p>common_options_dict
(dict)
&ndash; dictionary for all the common options
options:
unconditionalOverwrite
:
overwrite the files during restore
even if they exist</p>
<pre><code>    recoverWait                         :   Specifies whether this restore
    operation must wait until resources become available if a database recovery
    is already taking place

    recoverZap                          :   Specifies whether the IBM Domino
    must change the DBIID associated with the restored database

    recoverZapReplica                   :   Specifies whether the restore
    operation changes the replica id of the restored database

    recoverZapIfNecessary               :   Specifies whether the IBM Domino
    can change the DBIID associated with the restored database if necessary

    doNotReplayTransactLogs             :   option to skip restoring or
    replaying logs


Disaster Recovery special options:
    skipErrorsAndContinue               :   enables a data recovery operation
    to continue despite media errors

    disasterRecovery                    :   run disaster recovery
</code></pre>
<p>lndb_restore_options
(dict)
&ndash; dictionary for all options specific
to an lndb restore
options:
disableReplication
:
disable relpication on database</p>
<pre><code>    disableBackgroundAgents :   disable background agents
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client instance</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/lotusnotes/lndbinstance.py#L266-L374" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        client,
        destination_path,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        **kwargs):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destionation location.

        Args:
            client                (str/object) --  either the name of the client or
            the instance of the Client

            destination_path      (str)        --  full path of the restore location on client

            paths                 (list)       --  list of full paths of
            files/folders to restore

            overwrite             (bool)       --  unconditional overwrite files during restore

                default: True

            restore_data_and_acl  (bool)       --  restore data and ACL files

                default: True

            copy_precedence         (int)      --  copy precedence value of storage policy copy

                default: None

            from_time               (str)       --  time to retore the contents after

                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time                 (str)       --  time to retore the contents before

                    format: YYYY-MM-DD HH:MM:SS

                default: None

             common_options_dict    (dict)      -- dictionary for all the common options
                options:
                    unconditionalOverwrite              :   overwrite the files during restore
                    even if they exist

                    recoverWait                         :   Specifies whether this restore
                    operation must wait until resources become available if a database recovery
                    is already taking place

                    recoverZap                          :   Specifies whether the IBM Domino
                    must change the DBIID associated with the restored database

                    recoverZapReplica                   :   Specifies whether the restore
                    operation changes the replica id of the restored database

                    recoverZapIfNecessary               :   Specifies whether the IBM Domino
                    can change the DBIID associated with the restored database if necessary

                    doNotReplayTransactLogs             :   option to skip restoring or
                    replaying logs


                Disaster Recovery special options:
                    skipErrorsAndContinue               :   enables a data recovery operation
                    to continue despite media errors

                    disasterRecovery                    :   run disaster recovery

            lndb_restore_options    (dict)      -- dictionary for all options specific
            to an lndb restore
                options:
                    disableReplication      :   disable relpication on database

                    disableBackgroundAgents :   disable background agents

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or Client instance

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    return super(LNDBInstance, self).restore_out_of_place(
        client,
        destination_path,
        paths,
        overwrite,
        restore_data_and_acl,
        copy_precedence,
        from_time,
        to_time,
        **kwargs)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance" href="lninstance.html#cvpysdk.instances.lotusnotes.lninstance.LNInstance">LNInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.browse" href="../../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lninstance.LNInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances.lotusnotes" href="index.html">cvpysdk.instances.lotusnotes</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.lotusnotes.lndbinstance.LNDBInstance" href="#cvpysdk.instances.lotusnotes.lndbinstance.LNDBInstance">LNDBInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.lotusnotes.lndbinstance.LNDBInstance.restore_in_place" href="#cvpysdk.instances.lotusnotes.lndbinstance.LNDBInstance.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.instances.lotusnotes.lndbinstance.LNDBInstance.restore_out_of_place" href="#cvpysdk.instances.lotusnotes.lndbinstance.LNDBInstance.restore_out_of_place">restore_out_of_place</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>