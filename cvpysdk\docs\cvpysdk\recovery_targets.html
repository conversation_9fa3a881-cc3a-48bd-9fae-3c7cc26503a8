<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.recovery_targets API documentation</title>
<meta name="description" content="Main file for performing Replication Target specific operations (Auto Recovery) …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.recovery_targets</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing Replication Target specific operations (Auto Recovery).</p>
<p>RecoveryTargets and RecoveryTarget are 2 classes defined in this file.</p>
<p>RecoveryTargets:
Class for representing all the recovery targets</p>
<p>RecoveryTarget:
Class for a single recovery target selected, and to perform operations on that recovery target</p>
<h2 id="recoverytargets">Recoverytargets</h2>
<p><strong>init</strong>()
&ndash;
initialize object of RecoveryTargets class</p>
<p><strong>str</strong>()
&ndash;
returns all the Recovery Targets</p>
<p>_get_recovery_targets()
&ndash; Gets all the recovery targets</p>
<p>has_recovery_target()
&ndash; Checks if a target is present in the commcell.</p>
<p>get()
&ndash;
returns the recovery target class object of the input target name</p>
<p>refresh()
&ndash;
refresh the targets present in the client</p>
<h2 id="recoverytargets-attributes">Recoverytargets Attributes</h2>
<pre><code>**all_targets**             --  returns the dictioanry consisting of all the targets that are
present in the commcell and their information such as id and name
</code></pre>
<h2 id="recoverytarget">Recoverytarget</h2>
<p><strong>init</strong>()
&ndash;
initialize object of RecoveryTarget with the specified recovery target name</p>
<p>_get_recovery_target_id()
&ndash;
method to get the recovery target id</p>
<p>_get_recovery_target_properties()
&ndash;
get the properties of this ecovery target</p>
<p>refresh()
&ndash;
refresh the object properties</p>
<h2 id="recoverytarget-attributes">Recoverytarget Attributes</h2>
<pre><code>**recovery_target_id**      -- Returns the id of the recovery target
**recovery_target_name**    -- Returns the name of the Recovery Target
**destination_hypervisor**  -- Returns the name of destination hypervisor
**vm_prefix**               -- Returns the prefix of the vm name
**destination_host**        -- Returns the destination host
** def datastore**          -- Returns the datastore host
**resource_pool**           -- Returns the resource_pool host
**destination_network**     -- Returns the destination_network host
**no_of_cpu**               -- Returns the no_of_cpu host
**no_of_vm**                -- Returns the no_of_vm hos
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L1-L634" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing Replication Target specific operations (Auto Recovery).

RecoveryTargets and RecoveryTarget are 2 classes defined in this file.

RecoveryTargets:     Class for representing all the recovery targets

RecoveryTarget:      Class for a single recovery target selected, and to perform operations on that recovery target


RecoveryTargets:
    __init__()                   --  initialize object of RecoveryTargets class

    __str__()                   --  returns all the Recovery Targets

    _get_recovery_targets()     -- Gets all the recovery targets

    has_recovery_target()       -- Checks if a target is present in the commcell.

    get()                        --  returns the recovery target class object of the input target name

    refresh()                   --  refresh the targets present in the client

RecoveryTargets Attributes
--------------------------

    **all_targets**             --  returns the dictioanry consisting of all the targets that are
    present in the commcell and their information such as id and name

RecoveryTarget:
    __init__()                   --   initialize object of RecoveryTarget with the specified recovery target name

    _get_recovery_target_id()   --   method to get the recovery target id

    _get_recovery_target_properties()  --   get the properties of this ecovery target

    refresh()                   --   refresh the object properties

RecoveryTarget Attributes
--------------------------

    **recovery_target_id**      -- Returns the id of the recovery target
    **recovery_target_name**    -- Returns the name of the Recovery Target
    **destination_hypervisor**  -- Returns the name of destination hypervisor
    **vm_prefix**               -- Returns the prefix of the vm name
    **destination_host**        -- Returns the destination host
    ** def datastore**          -- Returns the datastore host
    **resource_pool**           -- Returns the resource_pool host
    **destination_network**     -- Returns the destination_network host
    **no_of_cpu**               -- Returns the no_of_cpu host
    **no_of_vm**                -- Returns the no_of_vm hos

&#34;&#34;&#34;
from __future__ import absolute_import
from __future__ import unicode_literals

from cvpysdk.exception import SDKException


class RecoveryTargets:

    &#34;&#34;&#34; Class for representing all the recovery targets&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the RecoveryTargets class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._RECOVERY_TARGETS_API = self._services[&#39;GET_ALL_RECOVERY_TARGETS&#39;]

        self._recovery_targets = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all targets .

            Returns:
                str     -   string of all the targets

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;RecoveryTargets&#39;)

        for index, recovery_target in enumerate(self._recovery_targets):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(
                index + 1,
                recovery_target
            )
            representation_string += sub_str

        return representation_string.strip()


    def _get_recovery_targets(self):
        &#34;&#34;&#34;Gets all the recovery targets.

            Returns:
                dict - consists of all targets in the client
                    {
                         &#34;target1_name&#34;: target1_id,
                         &#34;target2_name&#34;: target2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_TARGETS_API)
        if flag:
            if response.json() and &#39;recoveryTargets&#39; in response.json():

                recovery_target_dict = {}
                for recoveryTarget in response.json()[&#39;recoveryTargets&#39;]:
                    if recoveryTarget[&#39;applicationType&#39;] != &#34;CLEAN_ROOM&#34;:
                        temp_name = recoveryTarget[&#39;name&#39;].lower()
                        recovery_target_dict[temp_name] = str(recoveryTarget[&#39;id&#39;])

                return recovery_target_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def all_targets(self):
        &#34;&#34;&#34;Returns dict of all the targets.

         Returns dict    -   consists of all targets

                {
                    &#34;target1_name&#34;: target1_id,

                    &#34;target2_name&#34;: target2_id
                }

        &#34;&#34;&#34;
        return self._recovery_targets

    def has_recovery_target(self, target_name):
        &#34;&#34;&#34;Checks if a target is present in the commcell.

            Args:
                target_name (str)  --  name of the target

            Returns:
                bool - boolean output whether the target is present in commcell or not

            Raises:
                SDKException:
                    if type of the target name argument is not string

        &#34;&#34;&#34;
        if not isinstance(target_name, str):
            raise SDKException(&#39;Target&#39;, &#39;101&#39;)

        return self._recovery_targets and target_name.lower() in self._recovery_targets

    def get(self, recovery_target_name):
        &#34;&#34;&#34;Returns a target object.

            Args:
                recovery_target_name (str)  --  name of the target

            Returns:
                object - instance of the target class for the given target name

            Raises:
                SDKException:
                    if type of the target name argument is not string

                    if no target exists with the given name

        &#34;&#34;&#34;
        if not isinstance(recovery_target_name, str):
            raise SDKException(&#39;Target&#39;, &#39;101&#39;)
        else:
            recovery_target_name = recovery_target_name.lower()

            if self.has_recovery_target(recovery_target_name):
                return RecoveryTarget(
                    self._commcell_object, recovery_target_name, self.all_targets[recovery_target_name])

            raise SDKException(&#39;RecoveryTarget&#39;, &#39;102&#39;, &#39;No target exists with name: {0}&#39;.format(recovery_target_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the recovery targets&#34;&#34;&#34;
        self._recovery_targets = self._get_recovery_targets()


class RecoveryTarget:

    &#34;&#34;&#34; Class for a single recovery target selected, and to perform operations on that recovery target&#34;&#34;&#34;

    def __init__(self, commcell_object, recovery_target_name, recovery_target_id=None):
        &#34;&#34;&#34;Initialize the instance of the RecoveryTarget class.

            Args:
                commcell_object   (object)    --  instance of the Commcell class

                recovery_target_name      (str)       --  name of the target

                recovery_target_id        (str)       --  id of the target -- default: None
        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._recovery_target_name = recovery_target_name.lower()

        if recovery_target_id:
            # Use the target id mentioned in the arguments
            self._recovery_target_id = str(recovery_target_id)
        else:
            # Get the target id if target id is not provided
            self._recovery_target_id = self._get_recovery_target_id()
        self._RECOVERY_TARGET_API = self._services[&#39;GET_RECOVERY_TARGET&#39;] %self._recovery_target_id

        self._recovery_target_properties = None

        self._policy_type = None
        self._application_type = None
        self._destination_hypervisor = None
        self._access_node = None
        self._access_node_client_group = None
        self._users = []
        self._user_groups = []
        self._vm_prefix = &#39;&#39;
        self._vm_suffix = &#39;&#39;

        self._destination_host = None
        self._vm_storage_policy = None
        self._datastore = None
        self._resource_pool = None
        self._destination_network = None
        self._expiration_time = None
        self._failover_ma = None
        self._isolated_network = None
        self._no_of_cpu = None
        self._no_of_vm = None
        self._iso_paths = []

        self._resource_group = None
        self._region = None
        self._availability_zone = None
        self._storage_account = None
        self._vm_size = None
        self._disk_type = None
        self._virtual_network = None
        self._vm_folder = None
        self._security_group = None
        self._create_public_ip = None
        self._restore_as_managed_vm = None
        self._test_virtual_network = None
        self._test_security_group = None
        self._test_vm_size = None

        # AWS
        self._volume_type = None
        self._encryption_key = None
        self._iam_role_id = None
        self._iam_role_name = None
        self._instance_type = None

        self.refresh()

    def _get_recovery_target_id(self):
        &#34;&#34;&#34;Gets the target id associated with this target.

            Returns:
                str - id associated with this target

        &#34;&#34;&#34;
        target = RecoveryTargets(self._commcell_object)
        return target.all_targets[self.recovery_target_name]

    def _set_policy_type(self, policy_type):
        &#34;&#34;&#34;Sets the policy type&#34;&#34;&#34;
        if policy_type == &#34;AMAZON&#34;:
            self._policy_type = 1
        elif policy_type == &#34;MICROSOFT&#34;:
            self._policy_type = 2
        elif policy_type == &#34;AZURE_RESOURCE_MANAGER&#34;:
            self._policy_type = 7
        elif policy_type in [&#34;VMW_BACKUP_LABTEMPLATE&#34;, &#34;VMW_LIVEMOUNT&#34;]:
            self._policy_type = 13
        else:
            self._policy_type = -1

    def _get_recovery_target_properties(self):
        &#34;&#34;&#34;Gets the target properties of this target.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_TARGET_API)
        if flag:
            if response.json() and &#39;entity&#39; in response.json():
                self._recovery_target_properties = response.json()
                self._application_type = self._recovery_target_properties.get(&#39;entity&#39;, {}).get(&#39;applicationType&#39;)
                self._destination_hypervisor = self._recovery_target_properties.get(&#39;entity&#39;, {}).get(&#39;destinationHypervisor&#39;, {}).get(&#39;name&#39;)
                self._vm_suffix = self._recovery_target_properties.get(&#39;vmDisplayName&#39;, {}).get(&#34;suffix&#34;, &#34;&#34;)
                self._vm_prefix = self._recovery_target_properties.get(&#39;vmDisplayName&#39;, {}).get(&#34;prefix&#34;, &#34;&#34;)
                self._access_node = self._recovery_target_properties.get(&#39;accessNode&#39;, {}).get(&#34;name&#34;, &#34;&#34;)
                self._access_node_client_group = self._access_node if self._recovery_target_properties.get(&#39;accessNode&#39;, {}).get(&#34;type&#34;, &#34;&#34;) == &#39;Group&#39; else &#39;&#39;
                self._users = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;users&#39;, [])
                self._user_groups = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;userGroups&#39;, [])
                policy_type = self._recovery_target_properties[&#34;entity&#34;].get(&#34;policyType&#34;, &#34;&#34;)
                self._set_policy_type(policy_type)

                if self._policy_type == 1:
                    self._availability_zone = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;availabilityZone&#39;)
                    self._volume_type = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;volumeType&#39;)
                    self._encryption_key = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;encryptionKey&#39;, {}).get(&#39;name&#39;)
                    self._encryption_key_id = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;encryptionKey&#39;, {}).get(&#39;id&#39;)
                    self._iam_role_name = self._recovery_target_properties.get(&#39;destinationOptions&#39;, {}).get(&#39;iamRole&#39;, {}).get(&#39;name&#39;)
                    self._iam_role_id = self._recovery_target_properties.get(&#39;destinationOptions&#39;, {}).get(&#39;iamRole&#39;, {}).get(&#39;id&#39;)
                    self._destination_network = self._recovery_target_properties.get(&#39;networkOptions&#39;, {}).get(&#39;networkCard&#39;, {}).get(&#39;networkDisplayName&#39;)
                    self._security_group = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;securityGroups&#39;, [{}])[0].get(&#39;name&#39;, &#39;&#39;)
                    self._instance_type = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;instanceTypes&#39;, [&#39;Auto&#39;])[0]
                    expiry_hours = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;minutesRetainUntil&#34;, &#34;&#34;)
                    expiry_days = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;daysRetainUntil&#34;, &#34;&#34;)
                    if expiry_hours:
                        self._expiration_time = f&#39;{expiry_hours} hours&#39;
                    elif expiry_days:
                        self._expiration_time = f&#39;{expiry_days} days&#39;
                    self._test_virtual_network = self._recovery_target_properties.get(&#39;networkOptions&#39;, {}).get(&#39;cloudNetwork&#39;, {}).get(&#39;label&#39;)
                    self._test_security_group = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;testSecurityGroups&#39;, [{}])[0].get(&#39;name&#39;, &#39;&#39;)
                    self._test_vm_size = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;vmInstanceType&#39;, &#39;Auto&#39;)

                elif self._policy_type == 2:
                    self._vm_folder = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;dataStore&#34;, &#34;&#34;)
                    self._destination_network = self._recovery_target_properties.get(&#34;networkOptions&#34;, {}).get(&#34;networkCard&#34;, {}).get(&#34;networkNames&#34;)[0]
                    self._destination_host = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;destinationHost&#34;, &#34;&#34;)
                elif self._policy_type == 7:
                    self._resource_group = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;destinationHost&#34;, &#34;&#34;)
                    self._region = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;region&#39;, {}).get(&#39;name&#39;)
                    self._availability_zone = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;,{}).get(&#39;availabilityZone&#39;)
                    self._storage_account = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;dataStore&#34;, &#34;&#34;)

                    self._vm_size = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;vmInstanceType&#39;)
                    self._disk_type = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;volumeType&#39;)
                    self._virtual_network = self._recovery_target_properties.get(&#39;networkOptions&#39;, {}).get(&#39;networkCard&#39;, {}).get(&#39;networkDisplayName&#39;)
                    self._security_group = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;securityGroups&#39;, [{}])[0].get(&#39;name&#39;, &#39;&#39;)
                    self._create_public_ip = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;publicIP&#39;)
                    self._restore_as_managed_vm = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;restoreAsManagedVM&#39;)

                    expiry_hours = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;minutesRetainUntil&#34;, &#34;&#34;)
                    expiry_days = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;daysRetainUntil&#34;, &#34;&#34;)
                    if expiry_hours:
                        self._expiration_time = f&#39;{expiry_hours} hours&#39;
                    elif expiry_days:
                        self._expiration_time = f&#39;{expiry_days} days&#39;
                    self._test_virtual_network = self._recovery_target_properties.get(&#39;networkOptions&#39;, {}).get(&#39;cloudNetwork&#39;, {}).get(&#39;label&#39;)
                    self._test_vm_size = (self._recovery_target_properties.get(&#39;amazonPolicy&#39;, {}).get(&#39;vmInstanceTypes&#39;, [{}])[0].get(&#39;vmInstanceTypeName&#39;,&#39;&#39;))
                elif self._policy_type == 13:
                    self._destination_host = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;destinationHost&#34;, &#34;&#34;)
                    self._datastore = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;dataStore&#34;, &#34;&#34;)
                    self._resource_pool = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;resourcePoolPath&#34;, &#34;&#34;)
                    self._vm_folder = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;vmFolder&#34;, &#34;&#34;)
                    self._destination_network = self._recovery_target_properties.get(&#34;networkOptions&#34;, {}).get(&#34;networkCard&#34;, {}).get(&#34;destinationNetworks&#34;, [])

                    self._vm_storage_policy = self._recovery_target_properties.get(&#39;vmStoragePolicyName&#39;)
                    expiry_hours = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;minutesRetainUntil&#34;, &#34;&#34;)
                    expiry_days = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;daysRetainUntil&#34;, &#34;&#34;)
                    if expiry_hours:
                        self._expiration_time = f&#39;{expiry_hours} hours&#39;
                    elif expiry_days:
                        self._expiration_time = f&#39;{expiry_days} days&#39;
                    if self._recovery_target_properties.get(&#39;mediaAgent&#39;, {}):
                        self._failover_ma = self._recovery_target_properties[&#39;mediaAgent&#39;][&#39;clientName&#39;]

                    self._isolated_network = self._recovery_target_properties.get(&#34;virtualLabOptions&#34;, {}).get(&#34;configureIsolatedNetwork&#34;)

                    self._no_of_cpu = self._recovery_target_properties.get(&#39;maxCores&#39;)
                    self._no_of_vm = self._recovery_target_properties.get(&#39;maxVMQuota&#39;)
                    self._iso_paths = [iso[&#39;isoPath&#39;] for iso in
                                       self._recovery_target_properties.get(&#39;isoInfo&#39;, [])]
                    if self._recovery_target_properties.get(&#39;associatedClientGroup&#39;):
                        self._server_group = (self._recovery_target_properties[&#34;associatedClientGroup&#34;]
                                              [&#34;clientGroupName&#34;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def recovery_target_id(self):
        &#34;&#34;&#34;Returns: (str) the id of the recovery target&#34;&#34;&#34;
        return self._recovery_target_id

    @property
    def recovery_target_name(self):
        &#34;&#34;&#34;Returns: (str) the display name of the recovery target&#34;&#34;&#34;
        return self._recovery_target_name

    @property
    def policy_type(self):
        &#34;&#34;&#34;Returns: (str) the policy type ID
            1  - AWS
            2  - Microsoft Hyper-V
            7  - Azure
            13 - VMware
        &#34;&#34;&#34;
        return self._policy_type

    @property
    def application_type(self):
        &#34;&#34;&#34;Returns: (str) the name of the application type
            0 - Replication type
            1 - Regular type
        &#34;&#34;&#34;
        return self._application_type

    @property
    def destination_hypervisor(self):
        &#34;&#34;&#34;Returns: (str) the client name of destination hypervisor&#34;&#34;&#34;
        return self._destination_hypervisor

    @property
    def access_node(self):
        &#34;&#34;&#34;Returns: (str) the client name of the access node/proxy of the recovery target&#34;&#34;&#34;
        return self._access_node

    @property
    def access_node_client_group(self):
        &#34;&#34;&#34;Returns: (str) The client group name set on the access node field of recovery target&#34;&#34;&#34;
        return self._access_node_client_group

    @property
    def security_user_names(self):
        &#34;&#34;&#34;Returns: list&lt;str&gt; the names of the users who are used for ownership of the hypervisor and VMs&#34;&#34;&#34;
        return [user[&#39;userName&#39;] for user in self._users]

    @property
    def vm_prefix(self):
        &#34;&#34;&#34;Returns: (str) the prefix of the vm name to be prefixed to the destination VM&#34;&#34;&#34;
        return self._vm_prefix

    @property
    def vm_suffix(self):
        &#34;&#34;&#34;Returns: (str) the suffix of the vm name to be suffixed to the destination VM&#34;&#34;&#34;
        return self._vm_suffix

    @property
    def destination_host(self):
        &#34;&#34;&#34;Returns: (str) VMware: the destination ESX host name&#34;&#34;&#34;
        return self._destination_host

    @property
    def vm_storage_policy(self):
        &#34;&#34;&#34;Returns: (str) VMware: the vm storage policy name&#34;&#34;&#34;
        return self._vm_storage_policy

    @property
    def datastore(self):
        &#34;&#34;&#34;Returns: (str) VMware: the datastore name&#34;&#34;&#34;
        return self._datastore

    @property
    def resource_pool(self):
        &#34;&#34;&#34;Returns: (str) VMware: the resource pool name&#34;&#34;&#34;
        return self._resource_pool

    @property
    def vm_folder(self):
        &#34;&#34;&#34;Returns: (str) VMware/Hyper-V: the destination VM folder&#34;&#34;&#34;
        return self._vm_folder

    @property
    def destination_network(self):
        &#34;&#34;&#34;Returns: (str) VMware/Hyper-V/AWS: the network name of the destination VM&#34;&#34;&#34;
        return self._destination_network

    @property
    def expiration_time(self):
        &#34;&#34;&#34;Returns: (str) VMware/Azure: the expiration time of the test boot VM/test failover VM
            eg: 4 hours or 3 days
        &#34;&#34;&#34;
        return self._expiration_time

    @property
    def failover_ma(self):
        &#34;&#34;&#34;Returns: (str) VMware: the preferred Media Agent to be used for test failover job&#34;&#34;&#34;
        return self._failover_ma

    @property
    def isolated_network(self):
        &#34;&#34;&#34;Returns: (bool) VMware: whether the target is configured to create isolated network or not&#34;&#34;&#34;
        return self._isolated_network

    @property
    def iso_path(self):
        &#34;&#34;&#34;Returns: list&lt;str&gt; VMware regular: the path of ISOs used for test boot operations&#34;&#34;&#34;
        return self._iso_paths

    @property
    def server_group(self):
        &#34;&#34;&#34;Returns: (str) VMware regular: the name of the server group to be associated with the recovery target&#34;&#34;&#34;
        return self._server_group

    @property
    def no_of_cpu(self):
        &#34;&#34;&#34;Returns: (str) VMware regular: the maximum number of CPU cores for live mount&#34;&#34;&#34;
        return self._no_of_cpu

    @property
    def no_of_vm(self):
        &#34;&#34;&#34;Returns: (str) VMware regular: the maximum number of VMs to be deployed for live mount&#34;&#34;&#34;
        return self._no_of_vm

    @property
    def resource_group(self):
        &#34;&#34;&#34;Returns: (str) Azure: the resource group name for destination VM&#34;&#34;&#34;
        return self._resource_group

    @property
    def region(self):
        &#34;&#34;&#34;Return: (str) Azure: the recovery target region for destination VM&#34;&#34;&#34;
        return self._region

    @property
    def availability_zone(self):
        &#34;&#34;&#34;Return: (str) Azure/AWS: the availability zone of the destination VM&#34;&#34;&#34;
        return self._availability_zone

    @property
    def storage_account(self):
        &#34;&#34;&#34;Returns: (str) Azure: the storage account name used to deploy the VM&#39;s storage&#34;&#34;&#34;
        return self._storage_account

    @property
    def vm_size(self):
        &#34;&#34;&#34;Returns: (str) Azure: the size of the destination VM. This defines the hardware config&#34;&#34;&#34;
        return self._vm_size

    @property
    def disk_type(self):
        &#34;&#34;&#34;Returns: (str) Azure: the disk type of the destination VM&#34;&#34;&#34;
        return self._disk_type

    @property
    def virtual_network(self):
        &#34;&#34;&#34;Returns: (str) Azure: the destination VM virtual network to assign NIC to&#34;&#34;&#34;
        return self._virtual_network

    @property
    def security_group(self):
        &#34;&#34;&#34;Returns: (str) Azure/AWS: the destination VM network security group name&#34;&#34;&#34;
        return self._security_group

    @property
    def create_public_ip(self):
        &#34;&#34;&#34;Returns: (bool) Azure: whether public IP will be created for destination VM&#34;&#34;&#34;
        return self._create_public_ip

    @property
    def restore_as_managed_vm(self):
        &#34;&#34;&#34;Returns: (bool) whether the destination VM will be a managed VM&#34;&#34;&#34;
        return self._restore_as_managed_vm

    @property
    def test_virtual_network(self):
        &#34;&#34;&#34;Returns: (str) Azure: the destination VM virtual network for test failover&#34;&#34;&#34;
        return self._test_virtual_network

    @property
    def test_security_group(self):
        &#34;&#34;&#34;Returns: (str) AWS: the clone VM security group for test failover&#34;&#34;&#34;
        return self._test_security_group

    @property
    def test_vm_size(self):
        &#34;&#34;&#34;Returns: (str) Azure: the destination VM size for test failover&#34;&#34;&#34;
        return self._test_vm_size

    @property
    def volume_type(self):
        &#34;&#34;&#34;Returns: (str) AWS: the destination VM volume type/disk type&#34;&#34;&#34;
        return self._volume_type

    @property
    def encryption_key(self):
        &#34;&#34;&#34;Returns: (str) AWS: the encryption key of the destination VM&#34;&#34;&#34;
        return self._encryption_key

    @property
    def iam_role_id(self):
        &#34;&#34;&#34;Returns: (str) AWS: the AWS IAM Role ID associated with the destination VM&#34;&#34;&#34;
        return self._iam_role_id

    @property
    def iam_role_name(self):
        &#34;&#34;&#34;Returns: (str) AWS: the AWS IAM Role name associated with the destination VM&#34;&#34;&#34;
        return self._iam_role_name

    @property
    def instance_type(self):
        &#34;&#34;&#34;Returns: (str) AWS: the AWS instance type which is used for defining hardware config&#34;&#34;&#34;
        return self._instance_type

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Recovery Target.&#34;&#34;&#34;
        self._get_recovery_target_properties()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.recovery_targets.RecoveryTarget"><code class="flex name class">
<span>class <span class="ident">RecoveryTarget</span></span>
<span>(</span><span>commcell_object, recovery_target_name, recovery_target_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for a single recovery target selected, and to perform operations on that recovery target</p>
<p>Initialize the instance of the RecoveryTarget class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>recovery_target_name
(str)
&ndash;
name of the target</p>
<p>recovery_target_id
(str)
&ndash;
id of the target &ndash; default: None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L216-L634" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RecoveryTarget:

    &#34;&#34;&#34; Class for a single recovery target selected, and to perform operations on that recovery target&#34;&#34;&#34;

    def __init__(self, commcell_object, recovery_target_name, recovery_target_id=None):
        &#34;&#34;&#34;Initialize the instance of the RecoveryTarget class.

            Args:
                commcell_object   (object)    --  instance of the Commcell class

                recovery_target_name      (str)       --  name of the target

                recovery_target_id        (str)       --  id of the target -- default: None
        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._recovery_target_name = recovery_target_name.lower()

        if recovery_target_id:
            # Use the target id mentioned in the arguments
            self._recovery_target_id = str(recovery_target_id)
        else:
            # Get the target id if target id is not provided
            self._recovery_target_id = self._get_recovery_target_id()
        self._RECOVERY_TARGET_API = self._services[&#39;GET_RECOVERY_TARGET&#39;] %self._recovery_target_id

        self._recovery_target_properties = None

        self._policy_type = None
        self._application_type = None
        self._destination_hypervisor = None
        self._access_node = None
        self._access_node_client_group = None
        self._users = []
        self._user_groups = []
        self._vm_prefix = &#39;&#39;
        self._vm_suffix = &#39;&#39;

        self._destination_host = None
        self._vm_storage_policy = None
        self._datastore = None
        self._resource_pool = None
        self._destination_network = None
        self._expiration_time = None
        self._failover_ma = None
        self._isolated_network = None
        self._no_of_cpu = None
        self._no_of_vm = None
        self._iso_paths = []

        self._resource_group = None
        self._region = None
        self._availability_zone = None
        self._storage_account = None
        self._vm_size = None
        self._disk_type = None
        self._virtual_network = None
        self._vm_folder = None
        self._security_group = None
        self._create_public_ip = None
        self._restore_as_managed_vm = None
        self._test_virtual_network = None
        self._test_security_group = None
        self._test_vm_size = None

        # AWS
        self._volume_type = None
        self._encryption_key = None
        self._iam_role_id = None
        self._iam_role_name = None
        self._instance_type = None

        self.refresh()

    def _get_recovery_target_id(self):
        &#34;&#34;&#34;Gets the target id associated with this target.

            Returns:
                str - id associated with this target

        &#34;&#34;&#34;
        target = RecoveryTargets(self._commcell_object)
        return target.all_targets[self.recovery_target_name]

    def _set_policy_type(self, policy_type):
        &#34;&#34;&#34;Sets the policy type&#34;&#34;&#34;
        if policy_type == &#34;AMAZON&#34;:
            self._policy_type = 1
        elif policy_type == &#34;MICROSOFT&#34;:
            self._policy_type = 2
        elif policy_type == &#34;AZURE_RESOURCE_MANAGER&#34;:
            self._policy_type = 7
        elif policy_type in [&#34;VMW_BACKUP_LABTEMPLATE&#34;, &#34;VMW_LIVEMOUNT&#34;]:
            self._policy_type = 13
        else:
            self._policy_type = -1

    def _get_recovery_target_properties(self):
        &#34;&#34;&#34;Gets the target properties of this target.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_TARGET_API)
        if flag:
            if response.json() and &#39;entity&#39; in response.json():
                self._recovery_target_properties = response.json()
                self._application_type = self._recovery_target_properties.get(&#39;entity&#39;, {}).get(&#39;applicationType&#39;)
                self._destination_hypervisor = self._recovery_target_properties.get(&#39;entity&#39;, {}).get(&#39;destinationHypervisor&#39;, {}).get(&#39;name&#39;)
                self._vm_suffix = self._recovery_target_properties.get(&#39;vmDisplayName&#39;, {}).get(&#34;suffix&#34;, &#34;&#34;)
                self._vm_prefix = self._recovery_target_properties.get(&#39;vmDisplayName&#39;, {}).get(&#34;prefix&#34;, &#34;&#34;)
                self._access_node = self._recovery_target_properties.get(&#39;accessNode&#39;, {}).get(&#34;name&#34;, &#34;&#34;)
                self._access_node_client_group = self._access_node if self._recovery_target_properties.get(&#39;accessNode&#39;, {}).get(&#34;type&#34;, &#34;&#34;) == &#39;Group&#39; else &#39;&#39;
                self._users = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;users&#39;, [])
                self._user_groups = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;userGroups&#39;, [])
                policy_type = self._recovery_target_properties[&#34;entity&#34;].get(&#34;policyType&#34;, &#34;&#34;)
                self._set_policy_type(policy_type)

                if self._policy_type == 1:
                    self._availability_zone = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;availabilityZone&#39;)
                    self._volume_type = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;volumeType&#39;)
                    self._encryption_key = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;encryptionKey&#39;, {}).get(&#39;name&#39;)
                    self._encryption_key_id = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;encryptionKey&#39;, {}).get(&#39;id&#39;)
                    self._iam_role_name = self._recovery_target_properties.get(&#39;destinationOptions&#39;, {}).get(&#39;iamRole&#39;, {}).get(&#39;name&#39;)
                    self._iam_role_id = self._recovery_target_properties.get(&#39;destinationOptions&#39;, {}).get(&#39;iamRole&#39;, {}).get(&#39;id&#39;)
                    self._destination_network = self._recovery_target_properties.get(&#39;networkOptions&#39;, {}).get(&#39;networkCard&#39;, {}).get(&#39;networkDisplayName&#39;)
                    self._security_group = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;securityGroups&#39;, [{}])[0].get(&#39;name&#39;, &#39;&#39;)
                    self._instance_type = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;instanceTypes&#39;, [&#39;Auto&#39;])[0]
                    expiry_hours = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;minutesRetainUntil&#34;, &#34;&#34;)
                    expiry_days = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;daysRetainUntil&#34;, &#34;&#34;)
                    if expiry_hours:
                        self._expiration_time = f&#39;{expiry_hours} hours&#39;
                    elif expiry_days:
                        self._expiration_time = f&#39;{expiry_days} days&#39;
                    self._test_virtual_network = self._recovery_target_properties.get(&#39;networkOptions&#39;, {}).get(&#39;cloudNetwork&#39;, {}).get(&#39;label&#39;)
                    self._test_security_group = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;testSecurityGroups&#39;, [{}])[0].get(&#39;name&#39;, &#39;&#39;)
                    self._test_vm_size = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;vmInstanceType&#39;, &#39;Auto&#39;)

                elif self._policy_type == 2:
                    self._vm_folder = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;dataStore&#34;, &#34;&#34;)
                    self._destination_network = self._recovery_target_properties.get(&#34;networkOptions&#34;, {}).get(&#34;networkCard&#34;, {}).get(&#34;networkNames&#34;)[0]
                    self._destination_host = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;destinationHost&#34;, &#34;&#34;)
                elif self._policy_type == 7:
                    self._resource_group = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;destinationHost&#34;, &#34;&#34;)
                    self._region = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;region&#39;, {}).get(&#39;name&#39;)
                    self._availability_zone = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;,{}).get(&#39;availabilityZone&#39;)
                    self._storage_account = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;dataStore&#34;, &#34;&#34;)

                    self._vm_size = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;vmInstanceType&#39;)
                    self._disk_type = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;volumeType&#39;)
                    self._virtual_network = self._recovery_target_properties.get(&#39;networkOptions&#39;, {}).get(&#39;networkCard&#39;, {}).get(&#39;networkDisplayName&#39;)
                    self._security_group = self._recovery_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;securityGroups&#39;, [{}])[0].get(&#39;name&#39;, &#39;&#39;)
                    self._create_public_ip = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;publicIP&#39;)
                    self._restore_as_managed_vm = self._recovery_target_properties.get(&#39;cloudDestinationOptions&#39;, {}).get(&#39;restoreAsManagedVM&#39;)

                    expiry_hours = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;minutesRetainUntil&#34;, &#34;&#34;)
                    expiry_days = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;daysRetainUntil&#34;, &#34;&#34;)
                    if expiry_hours:
                        self._expiration_time = f&#39;{expiry_hours} hours&#39;
                    elif expiry_days:
                        self._expiration_time = f&#39;{expiry_days} days&#39;
                    self._test_virtual_network = self._recovery_target_properties.get(&#39;networkOptions&#39;, {}).get(&#39;cloudNetwork&#39;, {}).get(&#39;label&#39;)
                    self._test_vm_size = (self._recovery_target_properties.get(&#39;amazonPolicy&#39;, {}).get(&#39;vmInstanceTypes&#39;, [{}])[0].get(&#39;vmInstanceTypeName&#39;,&#39;&#39;))
                elif self._policy_type == 13:
                    self._destination_host = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;destinationHost&#34;, &#34;&#34;)
                    self._datastore = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;dataStore&#34;, &#34;&#34;)
                    self._resource_pool = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;resourcePoolPath&#34;, &#34;&#34;)
                    self._vm_folder = self._recovery_target_properties.get(&#34;destinationOptions&#34;, {}).get(&#34;vmFolder&#34;, &#34;&#34;)
                    self._destination_network = self._recovery_target_properties.get(&#34;networkOptions&#34;, {}).get(&#34;networkCard&#34;, {}).get(&#34;destinationNetworks&#34;, [])

                    self._vm_storage_policy = self._recovery_target_properties.get(&#39;vmStoragePolicyName&#39;)
                    expiry_hours = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;minutesRetainUntil&#34;, &#34;&#34;)
                    expiry_days = self._recovery_target_properties.get(&#34;liveMountOptions&#34;, {}).get(&#34;expirationTime&#34;, {}).get(&#34;daysRetainUntil&#34;, &#34;&#34;)
                    if expiry_hours:
                        self._expiration_time = f&#39;{expiry_hours} hours&#39;
                    elif expiry_days:
                        self._expiration_time = f&#39;{expiry_days} days&#39;
                    if self._recovery_target_properties.get(&#39;mediaAgent&#39;, {}):
                        self._failover_ma = self._recovery_target_properties[&#39;mediaAgent&#39;][&#39;clientName&#39;]

                    self._isolated_network = self._recovery_target_properties.get(&#34;virtualLabOptions&#34;, {}).get(&#34;configureIsolatedNetwork&#34;)

                    self._no_of_cpu = self._recovery_target_properties.get(&#39;maxCores&#39;)
                    self._no_of_vm = self._recovery_target_properties.get(&#39;maxVMQuota&#39;)
                    self._iso_paths = [iso[&#39;isoPath&#39;] for iso in
                                       self._recovery_target_properties.get(&#39;isoInfo&#39;, [])]
                    if self._recovery_target_properties.get(&#39;associatedClientGroup&#39;):
                        self._server_group = (self._recovery_target_properties[&#34;associatedClientGroup&#34;]
                                              [&#34;clientGroupName&#34;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def recovery_target_id(self):
        &#34;&#34;&#34;Returns: (str) the id of the recovery target&#34;&#34;&#34;
        return self._recovery_target_id

    @property
    def recovery_target_name(self):
        &#34;&#34;&#34;Returns: (str) the display name of the recovery target&#34;&#34;&#34;
        return self._recovery_target_name

    @property
    def policy_type(self):
        &#34;&#34;&#34;Returns: (str) the policy type ID
            1  - AWS
            2  - Microsoft Hyper-V
            7  - Azure
            13 - VMware
        &#34;&#34;&#34;
        return self._policy_type

    @property
    def application_type(self):
        &#34;&#34;&#34;Returns: (str) the name of the application type
            0 - Replication type
            1 - Regular type
        &#34;&#34;&#34;
        return self._application_type

    @property
    def destination_hypervisor(self):
        &#34;&#34;&#34;Returns: (str) the client name of destination hypervisor&#34;&#34;&#34;
        return self._destination_hypervisor

    @property
    def access_node(self):
        &#34;&#34;&#34;Returns: (str) the client name of the access node/proxy of the recovery target&#34;&#34;&#34;
        return self._access_node

    @property
    def access_node_client_group(self):
        &#34;&#34;&#34;Returns: (str) The client group name set on the access node field of recovery target&#34;&#34;&#34;
        return self._access_node_client_group

    @property
    def security_user_names(self):
        &#34;&#34;&#34;Returns: list&lt;str&gt; the names of the users who are used for ownership of the hypervisor and VMs&#34;&#34;&#34;
        return [user[&#39;userName&#39;] for user in self._users]

    @property
    def vm_prefix(self):
        &#34;&#34;&#34;Returns: (str) the prefix of the vm name to be prefixed to the destination VM&#34;&#34;&#34;
        return self._vm_prefix

    @property
    def vm_suffix(self):
        &#34;&#34;&#34;Returns: (str) the suffix of the vm name to be suffixed to the destination VM&#34;&#34;&#34;
        return self._vm_suffix

    @property
    def destination_host(self):
        &#34;&#34;&#34;Returns: (str) VMware: the destination ESX host name&#34;&#34;&#34;
        return self._destination_host

    @property
    def vm_storage_policy(self):
        &#34;&#34;&#34;Returns: (str) VMware: the vm storage policy name&#34;&#34;&#34;
        return self._vm_storage_policy

    @property
    def datastore(self):
        &#34;&#34;&#34;Returns: (str) VMware: the datastore name&#34;&#34;&#34;
        return self._datastore

    @property
    def resource_pool(self):
        &#34;&#34;&#34;Returns: (str) VMware: the resource pool name&#34;&#34;&#34;
        return self._resource_pool

    @property
    def vm_folder(self):
        &#34;&#34;&#34;Returns: (str) VMware/Hyper-V: the destination VM folder&#34;&#34;&#34;
        return self._vm_folder

    @property
    def destination_network(self):
        &#34;&#34;&#34;Returns: (str) VMware/Hyper-V/AWS: the network name of the destination VM&#34;&#34;&#34;
        return self._destination_network

    @property
    def expiration_time(self):
        &#34;&#34;&#34;Returns: (str) VMware/Azure: the expiration time of the test boot VM/test failover VM
            eg: 4 hours or 3 days
        &#34;&#34;&#34;
        return self._expiration_time

    @property
    def failover_ma(self):
        &#34;&#34;&#34;Returns: (str) VMware: the preferred Media Agent to be used for test failover job&#34;&#34;&#34;
        return self._failover_ma

    @property
    def isolated_network(self):
        &#34;&#34;&#34;Returns: (bool) VMware: whether the target is configured to create isolated network or not&#34;&#34;&#34;
        return self._isolated_network

    @property
    def iso_path(self):
        &#34;&#34;&#34;Returns: list&lt;str&gt; VMware regular: the path of ISOs used for test boot operations&#34;&#34;&#34;
        return self._iso_paths

    @property
    def server_group(self):
        &#34;&#34;&#34;Returns: (str) VMware regular: the name of the server group to be associated with the recovery target&#34;&#34;&#34;
        return self._server_group

    @property
    def no_of_cpu(self):
        &#34;&#34;&#34;Returns: (str) VMware regular: the maximum number of CPU cores for live mount&#34;&#34;&#34;
        return self._no_of_cpu

    @property
    def no_of_vm(self):
        &#34;&#34;&#34;Returns: (str) VMware regular: the maximum number of VMs to be deployed for live mount&#34;&#34;&#34;
        return self._no_of_vm

    @property
    def resource_group(self):
        &#34;&#34;&#34;Returns: (str) Azure: the resource group name for destination VM&#34;&#34;&#34;
        return self._resource_group

    @property
    def region(self):
        &#34;&#34;&#34;Return: (str) Azure: the recovery target region for destination VM&#34;&#34;&#34;
        return self._region

    @property
    def availability_zone(self):
        &#34;&#34;&#34;Return: (str) Azure/AWS: the availability zone of the destination VM&#34;&#34;&#34;
        return self._availability_zone

    @property
    def storage_account(self):
        &#34;&#34;&#34;Returns: (str) Azure: the storage account name used to deploy the VM&#39;s storage&#34;&#34;&#34;
        return self._storage_account

    @property
    def vm_size(self):
        &#34;&#34;&#34;Returns: (str) Azure: the size of the destination VM. This defines the hardware config&#34;&#34;&#34;
        return self._vm_size

    @property
    def disk_type(self):
        &#34;&#34;&#34;Returns: (str) Azure: the disk type of the destination VM&#34;&#34;&#34;
        return self._disk_type

    @property
    def virtual_network(self):
        &#34;&#34;&#34;Returns: (str) Azure: the destination VM virtual network to assign NIC to&#34;&#34;&#34;
        return self._virtual_network

    @property
    def security_group(self):
        &#34;&#34;&#34;Returns: (str) Azure/AWS: the destination VM network security group name&#34;&#34;&#34;
        return self._security_group

    @property
    def create_public_ip(self):
        &#34;&#34;&#34;Returns: (bool) Azure: whether public IP will be created for destination VM&#34;&#34;&#34;
        return self._create_public_ip

    @property
    def restore_as_managed_vm(self):
        &#34;&#34;&#34;Returns: (bool) whether the destination VM will be a managed VM&#34;&#34;&#34;
        return self._restore_as_managed_vm

    @property
    def test_virtual_network(self):
        &#34;&#34;&#34;Returns: (str) Azure: the destination VM virtual network for test failover&#34;&#34;&#34;
        return self._test_virtual_network

    @property
    def test_security_group(self):
        &#34;&#34;&#34;Returns: (str) AWS: the clone VM security group for test failover&#34;&#34;&#34;
        return self._test_security_group

    @property
    def test_vm_size(self):
        &#34;&#34;&#34;Returns: (str) Azure: the destination VM size for test failover&#34;&#34;&#34;
        return self._test_vm_size

    @property
    def volume_type(self):
        &#34;&#34;&#34;Returns: (str) AWS: the destination VM volume type/disk type&#34;&#34;&#34;
        return self._volume_type

    @property
    def encryption_key(self):
        &#34;&#34;&#34;Returns: (str) AWS: the encryption key of the destination VM&#34;&#34;&#34;
        return self._encryption_key

    @property
    def iam_role_id(self):
        &#34;&#34;&#34;Returns: (str) AWS: the AWS IAM Role ID associated with the destination VM&#34;&#34;&#34;
        return self._iam_role_id

    @property
    def iam_role_name(self):
        &#34;&#34;&#34;Returns: (str) AWS: the AWS IAM Role name associated with the destination VM&#34;&#34;&#34;
        return self._iam_role_name

    @property
    def instance_type(self):
        &#34;&#34;&#34;Returns: (str) AWS: the AWS instance type which is used for defining hardware config&#34;&#34;&#34;
        return self._instance_type

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Recovery Target.&#34;&#34;&#34;
        self._get_recovery_target_properties()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.access_node"><code class="name">var <span class="ident">access_node</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the client name of the access node/proxy of the recovery target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L450-L453" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def access_node(self):
    &#34;&#34;&#34;Returns: (str) the client name of the access node/proxy of the recovery target&#34;&#34;&#34;
    return self._access_node</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.access_node_client_group"><code class="name">var <span class="ident">access_node_client_group</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) The client group name set on the access node field of recovery target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L455-L458" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def access_node_client_group(self):
    &#34;&#34;&#34;Returns: (str) The client group name set on the access node field of recovery target&#34;&#34;&#34;
    return self._access_node_client_group</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.application_type"><code class="name">var <span class="ident">application_type</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the name of the application type
0 - Replication type
1 - Regular type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L437-L443" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def application_type(self):
    &#34;&#34;&#34;Returns: (str) the name of the application type
        0 - Replication type
        1 - Regular type
    &#34;&#34;&#34;
    return self._application_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.availability_zone"><code class="name">var <span class="ident">availability_zone</span></code></dt>
<dd>
<div class="desc"><p>Return: (str) Azure/AWS: the availability zone of the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L552-L555" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def availability_zone(self):
    &#34;&#34;&#34;Return: (str) Azure/AWS: the availability zone of the destination VM&#34;&#34;&#34;
    return self._availability_zone</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.create_public_ip"><code class="name">var <span class="ident">create_public_ip</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) Azure: whether public IP will be created for destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L582-L585" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def create_public_ip(self):
    &#34;&#34;&#34;Returns: (bool) Azure: whether public IP will be created for destination VM&#34;&#34;&#34;
    return self._create_public_ip</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.datastore"><code class="name">var <span class="ident">datastore</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware: the datastore name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L485-L488" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def datastore(self):
    &#34;&#34;&#34;Returns: (str) VMware: the datastore name&#34;&#34;&#34;
    return self._datastore</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.destination_host"><code class="name">var <span class="ident">destination_host</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware: the destination ESX host name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L475-L478" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_host(self):
    &#34;&#34;&#34;Returns: (str) VMware: the destination ESX host name&#34;&#34;&#34;
    return self._destination_host</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.destination_hypervisor"><code class="name">var <span class="ident">destination_hypervisor</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the client name of destination hypervisor</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L445-L448" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_hypervisor(self):
    &#34;&#34;&#34;Returns: (str) the client name of destination hypervisor&#34;&#34;&#34;
    return self._destination_hypervisor</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.destination_network"><code class="name">var <span class="ident">destination_network</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware/Hyper-V/AWS: the network name of the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L500-L503" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_network(self):
    &#34;&#34;&#34;Returns: (str) VMware/Hyper-V/AWS: the network name of the destination VM&#34;&#34;&#34;
    return self._destination_network</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.disk_type"><code class="name">var <span class="ident">disk_type</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Azure: the disk type of the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L567-L570" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def disk_type(self):
    &#34;&#34;&#34;Returns: (str) Azure: the disk type of the destination VM&#34;&#34;&#34;
    return self._disk_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.encryption_key"><code class="name">var <span class="ident">encryption_key</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) AWS: the encryption key of the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L612-L615" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def encryption_key(self):
    &#34;&#34;&#34;Returns: (str) AWS: the encryption key of the destination VM&#34;&#34;&#34;
    return self._encryption_key</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.expiration_time"><code class="name">var <span class="ident">expiration_time</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware/Azure: the expiration time of the test boot VM/test failover VM
eg: 4 hours or 3 days</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L505-L510" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def expiration_time(self):
    &#34;&#34;&#34;Returns: (str) VMware/Azure: the expiration time of the test boot VM/test failover VM
        eg: 4 hours or 3 days
    &#34;&#34;&#34;
    return self._expiration_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.failover_ma"><code class="name">var <span class="ident">failover_ma</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware: the preferred Media Agent to be used for test failover job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L512-L515" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def failover_ma(self):
    &#34;&#34;&#34;Returns: (str) VMware: the preferred Media Agent to be used for test failover job&#34;&#34;&#34;
    return self._failover_ma</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.iam_role_id"><code class="name">var <span class="ident">iam_role_id</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) AWS: the AWS IAM Role ID associated with the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L617-L620" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def iam_role_id(self):
    &#34;&#34;&#34;Returns: (str) AWS: the AWS IAM Role ID associated with the destination VM&#34;&#34;&#34;
    return self._iam_role_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.iam_role_name"><code class="name">var <span class="ident">iam_role_name</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) AWS: the AWS IAM Role name associated with the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L622-L625" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def iam_role_name(self):
    &#34;&#34;&#34;Returns: (str) AWS: the AWS IAM Role name associated with the destination VM&#34;&#34;&#34;
    return self._iam_role_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.instance_type"><code class="name">var <span class="ident">instance_type</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) AWS: the AWS instance type which is used for defining hardware config</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L627-L630" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def instance_type(self):
    &#34;&#34;&#34;Returns: (str) AWS: the AWS instance type which is used for defining hardware config&#34;&#34;&#34;
    return self._instance_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.iso_path"><code class="name">var <span class="ident">iso_path</span></code></dt>
<dd>
<div class="desc"><p>Returns: list<str> VMware regular: the path of ISOs used for test boot operations</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L522-L525" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def iso_path(self):
    &#34;&#34;&#34;Returns: list&lt;str&gt; VMware regular: the path of ISOs used for test boot operations&#34;&#34;&#34;
    return self._iso_paths</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.isolated_network"><code class="name">var <span class="ident">isolated_network</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) VMware: whether the target is configured to create isolated network or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L517-L520" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def isolated_network(self):
    &#34;&#34;&#34;Returns: (bool) VMware: whether the target is configured to create isolated network or not&#34;&#34;&#34;
    return self._isolated_network</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.no_of_cpu"><code class="name">var <span class="ident">no_of_cpu</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware regular: the maximum number of CPU cores for live mount</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L532-L535" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def no_of_cpu(self):
    &#34;&#34;&#34;Returns: (str) VMware regular: the maximum number of CPU cores for live mount&#34;&#34;&#34;
    return self._no_of_cpu</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.no_of_vm"><code class="name">var <span class="ident">no_of_vm</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware regular: the maximum number of VMs to be deployed for live mount</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L537-L540" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def no_of_vm(self):
    &#34;&#34;&#34;Returns: (str) VMware regular: the maximum number of VMs to be deployed for live mount&#34;&#34;&#34;
    return self._no_of_vm</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.policy_type"><code class="name">var <span class="ident">policy_type</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the policy type ID
1
- AWS
2
- Microsoft Hyper-V
7
- Azure
13 - VMware</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L427-L435" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def policy_type(self):
    &#34;&#34;&#34;Returns: (str) the policy type ID
        1  - AWS
        2  - Microsoft Hyper-V
        7  - Azure
        13 - VMware
    &#34;&#34;&#34;
    return self._policy_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.recovery_target_id"><code class="name">var <span class="ident">recovery_target_id</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the id of the recovery target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L417-L420" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def recovery_target_id(self):
    &#34;&#34;&#34;Returns: (str) the id of the recovery target&#34;&#34;&#34;
    return self._recovery_target_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.recovery_target_name"><code class="name">var <span class="ident">recovery_target_name</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the display name of the recovery target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L422-L425" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def recovery_target_name(self):
    &#34;&#34;&#34;Returns: (str) the display name of the recovery target&#34;&#34;&#34;
    return self._recovery_target_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.region"><code class="name">var <span class="ident">region</span></code></dt>
<dd>
<div class="desc"><p>Return: (str) Azure: the recovery target region for destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L547-L550" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def region(self):
    &#34;&#34;&#34;Return: (str) Azure: the recovery target region for destination VM&#34;&#34;&#34;
    return self._region</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.resource_group"><code class="name">var <span class="ident">resource_group</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Azure: the resource group name for destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L542-L545" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def resource_group(self):
    &#34;&#34;&#34;Returns: (str) Azure: the resource group name for destination VM&#34;&#34;&#34;
    return self._resource_group</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.resource_pool"><code class="name">var <span class="ident">resource_pool</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware: the resource pool name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L490-L493" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def resource_pool(self):
    &#34;&#34;&#34;Returns: (str) VMware: the resource pool name&#34;&#34;&#34;
    return self._resource_pool</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.restore_as_managed_vm"><code class="name">var <span class="ident">restore_as_managed_vm</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) whether the destination VM will be a managed VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L587-L590" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def restore_as_managed_vm(self):
    &#34;&#34;&#34;Returns: (bool) whether the destination VM will be a managed VM&#34;&#34;&#34;
    return self._restore_as_managed_vm</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.security_group"><code class="name">var <span class="ident">security_group</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Azure/AWS: the destination VM network security group name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L577-L580" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def security_group(self):
    &#34;&#34;&#34;Returns: (str) Azure/AWS: the destination VM network security group name&#34;&#34;&#34;
    return self._security_group</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.security_user_names"><code class="name">var <span class="ident">security_user_names</span></code></dt>
<dd>
<div class="desc"><p>Returns: list<str> the names of the users who are used for ownership of the hypervisor and VMs</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L460-L463" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def security_user_names(self):
    &#34;&#34;&#34;Returns: list&lt;str&gt; the names of the users who are used for ownership of the hypervisor and VMs&#34;&#34;&#34;
    return [user[&#39;userName&#39;] for user in self._users]</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.server_group"><code class="name">var <span class="ident">server_group</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware regular: the name of the server group to be associated with the recovery target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L527-L530" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_group(self):
    &#34;&#34;&#34;Returns: (str) VMware regular: the name of the server group to be associated with the recovery target&#34;&#34;&#34;
    return self._server_group</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.storage_account"><code class="name">var <span class="ident">storage_account</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Azure: the storage account name used to deploy the VM's storage</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L557-L560" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_account(self):
    &#34;&#34;&#34;Returns: (str) Azure: the storage account name used to deploy the VM&#39;s storage&#34;&#34;&#34;
    return self._storage_account</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.test_security_group"><code class="name">var <span class="ident">test_security_group</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) AWS: the clone VM security group for test failover</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L597-L600" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def test_security_group(self):
    &#34;&#34;&#34;Returns: (str) AWS: the clone VM security group for test failover&#34;&#34;&#34;
    return self._test_security_group</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.test_virtual_network"><code class="name">var <span class="ident">test_virtual_network</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Azure: the destination VM virtual network for test failover</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L592-L595" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def test_virtual_network(self):
    &#34;&#34;&#34;Returns: (str) Azure: the destination VM virtual network for test failover&#34;&#34;&#34;
    return self._test_virtual_network</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.test_vm_size"><code class="name">var <span class="ident">test_vm_size</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Azure: the destination VM size for test failover</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L602-L605" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def test_vm_size(self):
    &#34;&#34;&#34;Returns: (str) Azure: the destination VM size for test failover&#34;&#34;&#34;
    return self._test_vm_size</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.virtual_network"><code class="name">var <span class="ident">virtual_network</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Azure: the destination VM virtual network to assign NIC to</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L572-L575" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def virtual_network(self):
    &#34;&#34;&#34;Returns: (str) Azure: the destination VM virtual network to assign NIC to&#34;&#34;&#34;
    return self._virtual_network</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.vm_folder"><code class="name">var <span class="ident">vm_folder</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware/Hyper-V: the destination VM folder</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L495-L498" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_folder(self):
    &#34;&#34;&#34;Returns: (str) VMware/Hyper-V: the destination VM folder&#34;&#34;&#34;
    return self._vm_folder</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.vm_prefix"><code class="name">var <span class="ident">vm_prefix</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the prefix of the vm name to be prefixed to the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L465-L468" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_prefix(self):
    &#34;&#34;&#34;Returns: (str) the prefix of the vm name to be prefixed to the destination VM&#34;&#34;&#34;
    return self._vm_prefix</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.vm_size"><code class="name">var <span class="ident">vm_size</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Azure: the size of the destination VM. This defines the hardware config</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L562-L565" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_size(self):
    &#34;&#34;&#34;Returns: (str) Azure: the size of the destination VM. This defines the hardware config&#34;&#34;&#34;
    return self._vm_size</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.vm_storage_policy"><code class="name">var <span class="ident">vm_storage_policy</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware: the vm storage policy name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L480-L483" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_storage_policy(self):
    &#34;&#34;&#34;Returns: (str) VMware: the vm storage policy name&#34;&#34;&#34;
    return self._vm_storage_policy</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.vm_suffix"><code class="name">var <span class="ident">vm_suffix</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the suffix of the vm name to be suffixed to the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L470-L473" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_suffix(self):
    &#34;&#34;&#34;Returns: (str) the suffix of the vm name to be suffixed to the destination VM&#34;&#34;&#34;
    return self._vm_suffix</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.volume_type"><code class="name">var <span class="ident">volume_type</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) AWS: the destination VM volume type/disk type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L607-L610" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def volume_type(self):
    &#34;&#34;&#34;Returns: (str) AWS: the destination VM volume type/disk type&#34;&#34;&#34;
    return self._volume_type</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.recovery_targets.RecoveryTarget.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the Recovery Target.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L632-L634" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the Recovery Target.&#34;&#34;&#34;
    self._get_recovery_target_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTargets"><code class="flex name class">
<span>class <span class="ident">RecoveryTargets</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the recovery targets</p>
<p>Initialize object of the RecoveryTargets class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L77-L213" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RecoveryTargets:

    &#34;&#34;&#34; Class for representing all the recovery targets&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the RecoveryTargets class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._RECOVERY_TARGETS_API = self._services[&#39;GET_ALL_RECOVERY_TARGETS&#39;]

        self._recovery_targets = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all targets .

            Returns:
                str     -   string of all the targets

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;RecoveryTargets&#39;)

        for index, recovery_target in enumerate(self._recovery_targets):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(
                index + 1,
                recovery_target
            )
            representation_string += sub_str

        return representation_string.strip()


    def _get_recovery_targets(self):
        &#34;&#34;&#34;Gets all the recovery targets.

            Returns:
                dict - consists of all targets in the client
                    {
                         &#34;target1_name&#34;: target1_id,
                         &#34;target2_name&#34;: target2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_TARGETS_API)
        if flag:
            if response.json() and &#39;recoveryTargets&#39; in response.json():

                recovery_target_dict = {}
                for recoveryTarget in response.json()[&#39;recoveryTargets&#39;]:
                    if recoveryTarget[&#39;applicationType&#39;] != &#34;CLEAN_ROOM&#34;:
                        temp_name = recoveryTarget[&#39;name&#39;].lower()
                        recovery_target_dict[temp_name] = str(recoveryTarget[&#39;id&#39;])

                return recovery_target_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def all_targets(self):
        &#34;&#34;&#34;Returns dict of all the targets.

         Returns dict    -   consists of all targets

                {
                    &#34;target1_name&#34;: target1_id,

                    &#34;target2_name&#34;: target2_id
                }

        &#34;&#34;&#34;
        return self._recovery_targets

    def has_recovery_target(self, target_name):
        &#34;&#34;&#34;Checks if a target is present in the commcell.

            Args:
                target_name (str)  --  name of the target

            Returns:
                bool - boolean output whether the target is present in commcell or not

            Raises:
                SDKException:
                    if type of the target name argument is not string

        &#34;&#34;&#34;
        if not isinstance(target_name, str):
            raise SDKException(&#39;Target&#39;, &#39;101&#39;)

        return self._recovery_targets and target_name.lower() in self._recovery_targets

    def get(self, recovery_target_name):
        &#34;&#34;&#34;Returns a target object.

            Args:
                recovery_target_name (str)  --  name of the target

            Returns:
                object - instance of the target class for the given target name

            Raises:
                SDKException:
                    if type of the target name argument is not string

                    if no target exists with the given name

        &#34;&#34;&#34;
        if not isinstance(recovery_target_name, str):
            raise SDKException(&#39;Target&#39;, &#39;101&#39;)
        else:
            recovery_target_name = recovery_target_name.lower()

            if self.has_recovery_target(recovery_target_name):
                return RecoveryTarget(
                    self._commcell_object, recovery_target_name, self.all_targets[recovery_target_name])

            raise SDKException(&#39;RecoveryTarget&#39;, &#39;102&#39;, &#39;No target exists with name: {0}&#39;.format(recovery_target_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the recovery targets&#34;&#34;&#34;
        self._recovery_targets = self._get_recovery_targets()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.recovery_targets.RecoveryTargets.all_targets"><code class="name">var <span class="ident">all_targets</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all the targets.</p>
<p>Returns dict
-
consists of all targets</p>
<pre><code>   {
       "target1_name": target1_id,

       "target2_name": target2_id
   }
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L150-L163" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_targets(self):
    &#34;&#34;&#34;Returns dict of all the targets.

     Returns dict    -   consists of all targets

            {
                &#34;target1_name&#34;: target1_id,

                &#34;target2_name&#34;: target2_id
            }

    &#34;&#34;&#34;
    return self._recovery_targets</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.recovery_targets.RecoveryTargets.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, recovery_target_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a target object.</p>
<h2 id="args">Args</h2>
<p>recovery_target_name (str)
&ndash;
name of the target</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the target class for the given target name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the target name argument is not string</p>
<pre><code>if no target exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L184-L209" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, recovery_target_name):
    &#34;&#34;&#34;Returns a target object.

        Args:
            recovery_target_name (str)  --  name of the target

        Returns:
            object - instance of the target class for the given target name

        Raises:
            SDKException:
                if type of the target name argument is not string

                if no target exists with the given name

    &#34;&#34;&#34;
    if not isinstance(recovery_target_name, str):
        raise SDKException(&#39;Target&#39;, &#39;101&#39;)
    else:
        recovery_target_name = recovery_target_name.lower()

        if self.has_recovery_target(recovery_target_name):
            return RecoveryTarget(
                self._commcell_object, recovery_target_name, self.all_targets[recovery_target_name])

        raise SDKException(&#39;RecoveryTarget&#39;, &#39;102&#39;, &#39;No target exists with name: {0}&#39;.format(recovery_target_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTargets.has_recovery_target"><code class="name flex">
<span>def <span class="ident">has_recovery_target</span></span>(<span>self, target_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a target is present in the commcell.</p>
<h2 id="args">Args</h2>
<p>target_name (str)
&ndash;
name of the target</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the target is present in commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the target name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L165-L182" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_recovery_target(self, target_name):
    &#34;&#34;&#34;Checks if a target is present in the commcell.

        Args:
            target_name (str)  --  name of the target

        Returns:
            bool - boolean output whether the target is present in commcell or not

        Raises:
            SDKException:
                if type of the target name argument is not string

    &#34;&#34;&#34;
    if not isinstance(target_name, str):
        raise SDKException(&#39;Target&#39;, &#39;101&#39;)

    return self._recovery_targets and target_name.lower() in self._recovery_targets</code></pre>
</details>
</dd>
<dt id="cvpysdk.recovery_targets.RecoveryTargets.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the recovery targets</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/recovery_targets.py#L211-L213" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the recovery targets&#34;&#34;&#34;
    self._recovery_targets = self._get_recovery_targets()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#recoverytargets-attributes">RecoveryTargets Attributes</a></li>
<li><a href="#recoverytarget-attributes">RecoveryTarget Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.recovery_targets.RecoveryTarget" href="#cvpysdk.recovery_targets.RecoveryTarget">RecoveryTarget</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.access_node" href="#cvpysdk.recovery_targets.RecoveryTarget.access_node">access_node</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.access_node_client_group" href="#cvpysdk.recovery_targets.RecoveryTarget.access_node_client_group">access_node_client_group</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.application_type" href="#cvpysdk.recovery_targets.RecoveryTarget.application_type">application_type</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.availability_zone" href="#cvpysdk.recovery_targets.RecoveryTarget.availability_zone">availability_zone</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.create_public_ip" href="#cvpysdk.recovery_targets.RecoveryTarget.create_public_ip">create_public_ip</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.datastore" href="#cvpysdk.recovery_targets.RecoveryTarget.datastore">datastore</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.destination_host" href="#cvpysdk.recovery_targets.RecoveryTarget.destination_host">destination_host</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.destination_hypervisor" href="#cvpysdk.recovery_targets.RecoveryTarget.destination_hypervisor">destination_hypervisor</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.destination_network" href="#cvpysdk.recovery_targets.RecoveryTarget.destination_network">destination_network</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.disk_type" href="#cvpysdk.recovery_targets.RecoveryTarget.disk_type">disk_type</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.encryption_key" href="#cvpysdk.recovery_targets.RecoveryTarget.encryption_key">encryption_key</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.expiration_time" href="#cvpysdk.recovery_targets.RecoveryTarget.expiration_time">expiration_time</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.failover_ma" href="#cvpysdk.recovery_targets.RecoveryTarget.failover_ma">failover_ma</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.iam_role_id" href="#cvpysdk.recovery_targets.RecoveryTarget.iam_role_id">iam_role_id</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.iam_role_name" href="#cvpysdk.recovery_targets.RecoveryTarget.iam_role_name">iam_role_name</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.instance_type" href="#cvpysdk.recovery_targets.RecoveryTarget.instance_type">instance_type</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.iso_path" href="#cvpysdk.recovery_targets.RecoveryTarget.iso_path">iso_path</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.isolated_network" href="#cvpysdk.recovery_targets.RecoveryTarget.isolated_network">isolated_network</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.no_of_cpu" href="#cvpysdk.recovery_targets.RecoveryTarget.no_of_cpu">no_of_cpu</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.no_of_vm" href="#cvpysdk.recovery_targets.RecoveryTarget.no_of_vm">no_of_vm</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.policy_type" href="#cvpysdk.recovery_targets.RecoveryTarget.policy_type">policy_type</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.recovery_target_id" href="#cvpysdk.recovery_targets.RecoveryTarget.recovery_target_id">recovery_target_id</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.recovery_target_name" href="#cvpysdk.recovery_targets.RecoveryTarget.recovery_target_name">recovery_target_name</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.refresh" href="#cvpysdk.recovery_targets.RecoveryTarget.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.region" href="#cvpysdk.recovery_targets.RecoveryTarget.region">region</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.resource_group" href="#cvpysdk.recovery_targets.RecoveryTarget.resource_group">resource_group</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.resource_pool" href="#cvpysdk.recovery_targets.RecoveryTarget.resource_pool">resource_pool</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.restore_as_managed_vm" href="#cvpysdk.recovery_targets.RecoveryTarget.restore_as_managed_vm">restore_as_managed_vm</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.security_group" href="#cvpysdk.recovery_targets.RecoveryTarget.security_group">security_group</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.security_user_names" href="#cvpysdk.recovery_targets.RecoveryTarget.security_user_names">security_user_names</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.server_group" href="#cvpysdk.recovery_targets.RecoveryTarget.server_group">server_group</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.storage_account" href="#cvpysdk.recovery_targets.RecoveryTarget.storage_account">storage_account</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.test_security_group" href="#cvpysdk.recovery_targets.RecoveryTarget.test_security_group">test_security_group</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.test_virtual_network" href="#cvpysdk.recovery_targets.RecoveryTarget.test_virtual_network">test_virtual_network</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.test_vm_size" href="#cvpysdk.recovery_targets.RecoveryTarget.test_vm_size">test_vm_size</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.virtual_network" href="#cvpysdk.recovery_targets.RecoveryTarget.virtual_network">virtual_network</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.vm_folder" href="#cvpysdk.recovery_targets.RecoveryTarget.vm_folder">vm_folder</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.vm_prefix" href="#cvpysdk.recovery_targets.RecoveryTarget.vm_prefix">vm_prefix</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.vm_size" href="#cvpysdk.recovery_targets.RecoveryTarget.vm_size">vm_size</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.vm_storage_policy" href="#cvpysdk.recovery_targets.RecoveryTarget.vm_storage_policy">vm_storage_policy</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.vm_suffix" href="#cvpysdk.recovery_targets.RecoveryTarget.vm_suffix">vm_suffix</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTarget.volume_type" href="#cvpysdk.recovery_targets.RecoveryTarget.volume_type">volume_type</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.recovery_targets.RecoveryTargets" href="#cvpysdk.recovery_targets.RecoveryTargets">RecoveryTargets</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.recovery_targets.RecoveryTargets.all_targets" href="#cvpysdk.recovery_targets.RecoveryTargets.all_targets">all_targets</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTargets.get" href="#cvpysdk.recovery_targets.RecoveryTargets.get">get</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTargets.has_recovery_target" href="#cvpysdk.recovery_targets.RecoveryTargets.has_recovery_target">has_recovery_target</a></code></li>
<li><code><a title="cvpysdk.recovery_targets.RecoveryTargets.refresh" href="#cvpysdk.recovery_targets.RecoveryTargets.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>