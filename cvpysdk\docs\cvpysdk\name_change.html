<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.name_change API documentation</title>
<meta name="description" content="Main file for doing Name Change operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.name_change</code></h1>
</header>
<section id="section-intro">
<p>Main file for doing Name Change operations.</p>
<p>OperationType:
Class with the supported hostname change operations</p>
<p>NameChange: Class for doing operations for Name Change operations on clients and commcell.</p>
<h2 id="namechange">Namechange</h2>
<p><strong>init</strong>(class_object)
&ndash;
initialise object of the NameChange
class</p>
<p>hostname()
&ndash;
gets the current hostname of the client or
commserver</p>
<p>hostname(parameters_dict)
&ndash;
sets the hostname from client or commserver
level</p>
<p>display_name()
&ndash;
gets the display name of the client or
commserver</p>
<p>display_name(display_name)
&ndash;
sets the display name of the client or
commserver</p>
<p>client_name()
&ndash;
gets the name of the client</p>
<p>client_name(client_name)
&ndash;
sets the name of the client</p>
<p>domain_name()
&ndash;
gets the commserver hostname</p>
<p>domain_name(domains_dict)
&ndash;
sets the new domain name for the clients</p>
<p>_client_name_change_op()
&ndash;
performs client namechange based on the
setters</p>
<p>_commcell_name_change_op(parameters_dict)
&ndash;
performs commserver namechange based on the
setters</p>
<p>get_clients_for_name_change_post_ccm()
&ndash; gets all the clients available for name change
post commcell migration</p>
<p>name_change_post_ccm(parameters_dict)
&ndash; perfoms name change for migrated clients post
commcell migration</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/name_change.py#L1-L534" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for doing Name Change operations.

OperationType:  Class with the supported hostname change operations

NameChange: Class for doing operations for Name Change operations on clients and commcell.

NameChange:
    __init__(class_object)                          --  initialise object of the NameChange
                                                        class

    hostname()                                      --  gets the current hostname of the client or
                                                        commserver

    hostname(parameters_dict)                       --  sets the hostname from client or commserver
                                                        level

    display_name()                                  --  gets the display name of the client or
                                                        commserver

    display_name(display_name)                      --  sets the display name of the client or
                                                        commserver

    client_name()                                   --  gets the name of the client

    client_name(client_name)                        --  sets the name of the client

    domain_name()                                    --  gets the commserver hostname

    domain_name(domains_dict)                        --  sets the new domain name for the clients

    _client_name_change_op()                        --  performs client namechange based on the
                                                        setters

    _commcell_name_change_op(parameters_dict)       --  performs commserver namechange based on the
                                                        setters

    get_clients_for_name_change_post_ccm()          -- gets all the clients available for name change
                                                        post commcell migration

    name_change_post_ccm(parameters_dict)           -- perfoms name change for migrated clients post
                                                        commcell migration
&#34;&#34;&#34;

import re
from enum import Enum
from .exception import SDKException


class OperationType(Enum):
    &#34;&#34;&#34; Operation Types supported to get schedules of particular optype&#34;&#34;&#34;
    COMMSERVER_HOSTNAME_REMOTE_CLIENTS = 147
    COMMSERVER_HOSTNAME_AFTER_DR = 139
    CLIENT_HOSTNAME = &#34;CLIENT_HOSTNAME&#34;
    COMMSERVER_HOSTNAME = &#34;COMMSERVER_HOSTNAME&#34;


class NameChange(object):
    &#34;&#34;&#34;Class for doing Name Change operations on clients and commcell&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initializes an instance of the NameChange class to perform Name Change operations.

            Args:
                class_object (object)  --  instance of the client/commcell class

        &#34;&#34;&#34;
        from .commcell import Commcell
        from .client import Client

        if isinstance(class_object, Commcell):
            self._commcell_object = class_object
            self._display_name = self._commcell_object.clients.get(self._commcell_object.
                                                                   commserv_hostname).display_name
            self._commcell_name = self._commcell_object.clients.get(self._commcell_object.
                                                                    commserv_hostname).commcell_name
            self._is_client = False

        elif isinstance(class_object, Client):
            self._client_object = class_object
            self._commcell_object = class_object._commcell_object
            self._client_hostname = self._client_object.client_hostname
            self._display_name = self._client_object.display_name
            self._client_name = self._client_object.client_name
            self._commcell_name = self._client_object.commcell_name
            self._new_name = None
            self._is_client = True

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

    @property
    def hostname(self):
        &#34;&#34;&#34;
        Gets the client hostname or commserver hostname

         Returns:
                str - client hostname or commserver hostname
        &#34;&#34;&#34;
        if self._is_client:
            return self._client_hostname
        else:
            return self._commcell_name

    @hostname.setter
    def hostname(self, parameters_dict):
        &#34;&#34;&#34;
        Sets the client hostname or commserver hostname with the parameters provided
        Args:
            parameters_dict (str)      -- dictionary of parameters for namechange
                                    {
                                    &#34;operation&#34;: Operation type to be performed on the client or
                                                commserver (OperationType)
                                    &#34;ClientHostname&#34;:   Client hostname to be updated (str)
                                    &#34;CommserverHostname&#34;:   Commserver hostname to be updated (str)
                                    &#34;oldName&#34;:  old commserver hostname
                                    &#34;newName&#34;:  new commserver hostname
                                    }

        &#34;&#34;&#34;
        if self._is_client:
            if parameters_dict[&#34;operation&#34;] == OperationType.CLIENT_HOSTNAME.value:
                if parameters_dict[&#34;ClientHostname&#34;] is None:
                    raise SDKException(&#39;NameChange&#39;, &#39;101&#39;)
                self._client_hostname = parameters_dict[&#34;ClientHostname&#34;]
                self._client_name_change_op()
            if parameters_dict[&#34;operation&#34;] == OperationType.COMMSERVER_HOSTNAME.value:
                if parameters_dict[&#34;CommserverHostname&#34;] is None:
                    raise SDKException(&#39;NameChange&#39;, &#39;102&#39;)
                self._commcell_name = parameters_dict[&#34;CommserverHostname&#34;]
                self._client_name_change_op()
        else:
            if parameters_dict[&#34;operation&#34;] == OperationType.COMMSERVER_HOSTNAME_REMOTE_CLIENTS.value:
                parameters_dict[&#34;oldName&#34;] = self._commcell_name
                self._commcell_name_change_op(parameters_dict)
            elif parameters_dict[&#34;operation&#34;] == OperationType.COMMSERVER_HOSTNAME_AFTER_DR.value:
                if parameters_dict[&#34;clientIds&#34;] is None:
                    raise SDKException(&#39;NameChange&#39;, &#39;105&#39;)
                parameters_dict[&#34;newName&#34;] = self._commcell_name
                self._commcell_name_change_op(parameters_dict)

    @property
    def domain_name(self):
        &#34;&#34;&#34;
        Gets the commserver hostname

        Returns:
                str - commserver hostname
        &#34;&#34;&#34;
        return self._commcell_name

    @domain_name.setter
    def domain_name(self, domains_dict):
        &#34;&#34;&#34;
        Sets the new domain name for the clients with the parameter provided
        Args:
            domains_dict (dict) -- new client domain name
                                    {
                                    &#34;oldDomain&#34;: old client domain name (str)
                                    &#34;newDomain&#34;: new client domain name (str)
                                    }

        &#34;&#34;&#34;
        if domains_dict[&#34;oldDomain&#34;] is None:
            raise SDKException(&#39;NameChange&#39;, &#39;103&#39;)
        elif domains_dict[&#34;newDomain&#34;] is None:
            raise SDKException(&#39;NameChange&#39;, &#39;104&#39;)
        dict_domains = {
            &#34;oldName&#34;: domains_dict[&#34;oldDomain&#34;],
            &#34;newName&#34;: domains_dict[&#34;newDomain&#34;],
            &#34;operation&#34;: 136
        }
        self._commcell_name_change_op(dict_domains)

    @property
    def display_name(self):
        &#34;&#34;&#34;
        Gets the display name of the client or commserver

        Returns:
                str - client or commserver display name
        &#34;&#34;&#34;
        if self._is_client:
            return self._display_name
        else:
            return self._display_name

    @display_name.setter
    def display_name(self, display_name):
        &#34;&#34;&#34;
        Sets the display name of the client or commserver with the parameter provided
        Args:
            display_name (str) -- new client or commserver display name

        &#34;&#34;&#34;
        if self._is_client:
            self._display_name = display_name
            self._client_name_change_op()
        else:
            dict_cs = {
                &#34;oldName&#34;: self._display_name,
                &#34;newName&#34;: display_name,
                &#34;operation&#34;: 9811,
            }
            self._commcell_name_change_op(dict_cs)

    @property
    def client_name(self):
        &#34;&#34;&#34;
        Gets the client name

        Returns:
                str - client name
        &#34;&#34;&#34;
        if self._is_client:
            return self._client_name
        else:
            False

    @client_name.setter
    def client_name(self, client_name):
        &#34;&#34;&#34;
        Sets the name of the client with the parameter provided
        Args:
            client_name (str) -- new client name

        &#34;&#34;&#34;
        self._new_name = client_name
        self._client_name_change_op()

    def _client_name_change_op(self):
        &#34;&#34;&#34;
        Performs the client namechange operations

            Raises:
            SDKException::
                if the client namechange failed

                if the response is empty
        &#34;&#34;&#34;
        request_json = {
            &#34;App_SetClientPropertiesRequest&#34;:
            {
                &#34;clientProperties&#34;: {
                    &#34;client&#34;: {
                        &#34;displayName&#34;: self._display_name,
                        &#34;clientEntity&#34;: {
                            &#34;hostName&#34;: self._client_hostname,
                            &#34;clientName&#34;: self._client_name,
                            &#34;commCellName&#34;: self._commcell_name
                        }
                    }
                },
                &#34;association&#34;: {
                    &#34;entity&#34;: [
                        {
                            &#34;clientName&#34;: self._client_name,
                            &#34;newName&#34;: self._new_name
                        }
                    ]
                }
            }
        }
        flag, response = self._client_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], request_json
        )

        if flag:
            if response.json():
                if &#39;errorMessage&#39; in response.json():
                    # for errorMessage: &#34;Operation Failed&#34; errorCode: 7
                    # for errorMessage: &#34;Error 0x911: Failed to process request due to invalid /
                    # entity information.Invalid clientId for clientName.\n&#34;
                    # errorCode: 2 and others

                    error_message = &#34;Failed to do namechange on client, &#34; \
                                    &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(
                                        response.json().get(&#39;errorCode&#39;),
                                        response.json().get(&#39;errorMessage&#39;)
                                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)

                elif &#39;errorCode&#39; in response.json().get(&#39;response&#39;)[0]:
                    error_code = str(
                        response.json().get(&#39;response&#39;)[0].get(&#39;errorCode&#39;))
                    if error_code != &#39;0&#39;:
                        error_message = &#34;Failed to do namechange on client&#34;
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def _commcell_name_change_op(self, parameters_dict):
        &#34;&#34;&#34;
        Performs the commcell namechange operations

        Args:
            parameters_dict (dict)          --  dictionary with common namechange parameters like
                                                old commserver hostname, new commserver hostname
                                                or old domain name, new domain name, client IDs.
                                                clientIds can be an empty list too.
                                                  {&#34;newName&#34;,
                                                    &#34;oldName&#34;,
                                                    &#34;operation&#34;,
                                                    &#34;clientIds&#34;}

            Raises:
            SDKException::
                if the client namechange failed

                if the response is empty

        &#34;&#34;&#34;

        request_json = {
            &#34;EVGui_ClientNameControlReq&#34;:
            {
                &#34;isPostMigration&#34;: &#34;&#34;,
                &#34;newName&#34;: parameters_dict.get(&#34;newName&#34;, &#34;&#34;),
                &#34;destinationConfiguration&#34;: 0,
                &#34;sourceConfiguration&#34;: 0,
                &#34;setWithoutConditionFlag&#34;: 0,
                &#34;oldName&#34;: parameters_dict.get(&#34;oldName&#34;, &#34;&#34;),
                &#34;commCellId&#34;: 0,
                &#34;operation&#34;: parameters_dict.get(&#34;operation&#34;, 0),
                &#34;forceChangeName&#34;: 0,
                &#34;clientList&#34;: parameters_dict.get(&#34;clientIds&#34;, [])

            }
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], request_json
        )
        if flag:
            if response.json():
                if &#39;errorMessage&#39; in response.json():
                    # for errorMessage: &#34;Operation Failed&#34; errorCode: 7
                    error_message = &#34;Failed to do namechange on commserver &#34; \
                                    &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(
                        response.json().get(&#39;errorCode&#39;),
                        response.json().get(&#39;errorMessage&#39;)
                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                
                elif not &#39;errorMessage&#39; in response.json():
                    return True

                elif &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                    error_code = int(
                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;))

                    error_message = &#34;Failed to do namechange on commserver &#34; \
                                    &#34;with errorCode [{0}], errorString [{1}]&#34;.format(
                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;),
                        response.json().get(&#39;error&#39;).get(&#39;errorString&#39;)
                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response. text))

    def get_clients_for_name_change_post_ccm(self):
        &#34;&#34;&#34;
            Gets clients available for name change after commcell migration.
            Raises:
            SDKException::
                if the client namechange failed
                if the response is empty
        &#34;&#34;&#34;
        xml = &#34;&#34;&#34;
            &lt;EVGui_GetClientForNameControlReq&gt;
            &lt;/EVGui_GetClientForNameControlReq&gt;
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], xml
        )
        def get_clients(response):
            clients_list = []
            all_clients = response.json()[&#34;clientList&#34;]
            for client in all_clients:
                temp_dict = {}
                name = client.get(&#34;name&#34;, &#34;&#34;)
                domain = client.get(&#34;domain&#34;, &#34;&#34;)
                cs_host_name = client.get(&#34;csHostName&#34;, &#34;&#34;)
                if name + &#34;.&#34; + domain != cs_host_name and name != cs_host_name:
                    clients_list.append({&#34;csHostname&#34;: cs_host_name, &#34;name&#34;: name})
            return clients_list
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                    error_code = int(
                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;))
                    if error_code != 1:
                        # for errorString: &#34;Failed to get clients for name change operation&#34;
                        # errorCode: 0 or others
                        error_message = &#34;Failed to get clients for name change operation&#34; \
                                        &#34;with errorCode [{0}], errorString [{1}]&#34;.format(
                            response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;),
                            response.json().get(&#39;error&#39;).get(&#39;errorString&#39;)
                        )
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                    elif error_code == 1:
                        return get_clients(response)
                elif &#39;errorMessage&#39; in response.json():
                    error_message = &#34;Failed to get clients for name change operation&#34; \
                                    &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(
                        response.json().get(&#39;errorCode&#39;),
                        response.json().get(&#39;errorMessage&#39;)
                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def name_change_post_ccm(self, parameters_dict):
        &#34;&#34;&#34;
        Performs the commcell namechange for clients post commcell migration
        Args:
            parameters_dict (dict)      --  contains old commcell hostname, new commcell hostname,
                                            Ids of clients on which name change is to be performed
                                            {
                                            &#34;sourceCommcellHostname&#34;: &#34;source-1&#34;
                                            &#34;destinationCommcellHostname&#34;: &#34;dest-1&#34;
                                            &#34;clientIds&#34;: [&#34;id1&#34;, &#34;id2&#34;]
                                            }
            Raises:
            SDKException::
                if the client namechange failed
                if the response is empty
        &#34;&#34;&#34;
        name_change_xml = &#34;&#34;&#34;
            &lt;EVGui_ClientNameControlReq 
                commCellId=&#34;0&#34; 
                destinationConfiguration=&#34;2&#34; 
                isPostMigration=&#34;1&#34; 
                newName=&#34;{0}&#34;
                oldName=&#34;{1}&#34;
                operation=&#34;139&#34; 
                setWithoutConditionFlag=&#34;0&#34; 
                sourceConfiguration=&#34;2&#34;&gt; 
                {2}
            &lt;/EVGui_ClientNameControlReq&gt;
        &#34;&#34;&#34;
        client_tag = &#34;&#34;&#34;
            &lt;clientList val= &#34;{0}&#34;/&gt;
        &#34;&#34;&#34;
        clients_string = &#34;&#34;
        for clients_id in parameters_dict.get(&#34;clientIds&#34;, []):
            clients_string += client_tag.format(clients_id)
        name_change_xml = name_change_xml.format(parameters_dict[&#34;destinationCommcellHostname&#34;],
                                                 parameters_dict[&#34;sourceCommcellHostname&#34;],
                                                 clients_string)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], name_change_xml
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                    error_code = int(
                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;))
                    if error_code != 1:
                        error_message = &#34;Failed to perform name change operation&#34; \
                                        &#34;with errorCode [{0}], errorString [{1}]&#34;.format(
                                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;),
                                        response.json().get(&#39;error&#39;).get(&#39;errorString&#39;)
                                        )
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                    elif error_code == 1:
                        return True
                elif &#39;errorMessage&#39; in response.json():
                    error_message = &#34;Failed to get clients for name change operation&#34; \
                                    &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(response.json().get(&#39;errorCode&#39;),
                                                                                      response.json().get(
                                                                                          &#39;errorMessage&#39;)
                                                                                      )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.name_change.NameChange"><code class="flex name class">
<span>class <span class="ident">NameChange</span></span>
<span>(</span><span>class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for doing Name Change operations on clients and commcell</p>
<p>Initializes an instance of the NameChange class to perform Name Change operations.</p>
<h2 id="args">Args</h2>
<p>class_object (object)
&ndash;
instance of the client/commcell class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/name_change.py#L75-L534" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class NameChange(object):
    &#34;&#34;&#34;Class for doing Name Change operations on clients and commcell&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initializes an instance of the NameChange class to perform Name Change operations.

            Args:
                class_object (object)  --  instance of the client/commcell class

        &#34;&#34;&#34;
        from .commcell import Commcell
        from .client import Client

        if isinstance(class_object, Commcell):
            self._commcell_object = class_object
            self._display_name = self._commcell_object.clients.get(self._commcell_object.
                                                                   commserv_hostname).display_name
            self._commcell_name = self._commcell_object.clients.get(self._commcell_object.
                                                                    commserv_hostname).commcell_name
            self._is_client = False

        elif isinstance(class_object, Client):
            self._client_object = class_object
            self._commcell_object = class_object._commcell_object
            self._client_hostname = self._client_object.client_hostname
            self._display_name = self._client_object.display_name
            self._client_name = self._client_object.client_name
            self._commcell_name = self._client_object.commcell_name
            self._new_name = None
            self._is_client = True

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

    @property
    def hostname(self):
        &#34;&#34;&#34;
        Gets the client hostname or commserver hostname

         Returns:
                str - client hostname or commserver hostname
        &#34;&#34;&#34;
        if self._is_client:
            return self._client_hostname
        else:
            return self._commcell_name

    @hostname.setter
    def hostname(self, parameters_dict):
        &#34;&#34;&#34;
        Sets the client hostname or commserver hostname with the parameters provided
        Args:
            parameters_dict (str)      -- dictionary of parameters for namechange
                                    {
                                    &#34;operation&#34;: Operation type to be performed on the client or
                                                commserver (OperationType)
                                    &#34;ClientHostname&#34;:   Client hostname to be updated (str)
                                    &#34;CommserverHostname&#34;:   Commserver hostname to be updated (str)
                                    &#34;oldName&#34;:  old commserver hostname
                                    &#34;newName&#34;:  new commserver hostname
                                    }

        &#34;&#34;&#34;
        if self._is_client:
            if parameters_dict[&#34;operation&#34;] == OperationType.CLIENT_HOSTNAME.value:
                if parameters_dict[&#34;ClientHostname&#34;] is None:
                    raise SDKException(&#39;NameChange&#39;, &#39;101&#39;)
                self._client_hostname = parameters_dict[&#34;ClientHostname&#34;]
                self._client_name_change_op()
            if parameters_dict[&#34;operation&#34;] == OperationType.COMMSERVER_HOSTNAME.value:
                if parameters_dict[&#34;CommserverHostname&#34;] is None:
                    raise SDKException(&#39;NameChange&#39;, &#39;102&#39;)
                self._commcell_name = parameters_dict[&#34;CommserverHostname&#34;]
                self._client_name_change_op()
        else:
            if parameters_dict[&#34;operation&#34;] == OperationType.COMMSERVER_HOSTNAME_REMOTE_CLIENTS.value:
                parameters_dict[&#34;oldName&#34;] = self._commcell_name
                self._commcell_name_change_op(parameters_dict)
            elif parameters_dict[&#34;operation&#34;] == OperationType.COMMSERVER_HOSTNAME_AFTER_DR.value:
                if parameters_dict[&#34;clientIds&#34;] is None:
                    raise SDKException(&#39;NameChange&#39;, &#39;105&#39;)
                parameters_dict[&#34;newName&#34;] = self._commcell_name
                self._commcell_name_change_op(parameters_dict)

    @property
    def domain_name(self):
        &#34;&#34;&#34;
        Gets the commserver hostname

        Returns:
                str - commserver hostname
        &#34;&#34;&#34;
        return self._commcell_name

    @domain_name.setter
    def domain_name(self, domains_dict):
        &#34;&#34;&#34;
        Sets the new domain name for the clients with the parameter provided
        Args:
            domains_dict (dict) -- new client domain name
                                    {
                                    &#34;oldDomain&#34;: old client domain name (str)
                                    &#34;newDomain&#34;: new client domain name (str)
                                    }

        &#34;&#34;&#34;
        if domains_dict[&#34;oldDomain&#34;] is None:
            raise SDKException(&#39;NameChange&#39;, &#39;103&#39;)
        elif domains_dict[&#34;newDomain&#34;] is None:
            raise SDKException(&#39;NameChange&#39;, &#39;104&#39;)
        dict_domains = {
            &#34;oldName&#34;: domains_dict[&#34;oldDomain&#34;],
            &#34;newName&#34;: domains_dict[&#34;newDomain&#34;],
            &#34;operation&#34;: 136
        }
        self._commcell_name_change_op(dict_domains)

    @property
    def display_name(self):
        &#34;&#34;&#34;
        Gets the display name of the client or commserver

        Returns:
                str - client or commserver display name
        &#34;&#34;&#34;
        if self._is_client:
            return self._display_name
        else:
            return self._display_name

    @display_name.setter
    def display_name(self, display_name):
        &#34;&#34;&#34;
        Sets the display name of the client or commserver with the parameter provided
        Args:
            display_name (str) -- new client or commserver display name

        &#34;&#34;&#34;
        if self._is_client:
            self._display_name = display_name
            self._client_name_change_op()
        else:
            dict_cs = {
                &#34;oldName&#34;: self._display_name,
                &#34;newName&#34;: display_name,
                &#34;operation&#34;: 9811,
            }
            self._commcell_name_change_op(dict_cs)

    @property
    def client_name(self):
        &#34;&#34;&#34;
        Gets the client name

        Returns:
                str - client name
        &#34;&#34;&#34;
        if self._is_client:
            return self._client_name
        else:
            False

    @client_name.setter
    def client_name(self, client_name):
        &#34;&#34;&#34;
        Sets the name of the client with the parameter provided
        Args:
            client_name (str) -- new client name

        &#34;&#34;&#34;
        self._new_name = client_name
        self._client_name_change_op()

    def _client_name_change_op(self):
        &#34;&#34;&#34;
        Performs the client namechange operations

            Raises:
            SDKException::
                if the client namechange failed

                if the response is empty
        &#34;&#34;&#34;
        request_json = {
            &#34;App_SetClientPropertiesRequest&#34;:
            {
                &#34;clientProperties&#34;: {
                    &#34;client&#34;: {
                        &#34;displayName&#34;: self._display_name,
                        &#34;clientEntity&#34;: {
                            &#34;hostName&#34;: self._client_hostname,
                            &#34;clientName&#34;: self._client_name,
                            &#34;commCellName&#34;: self._commcell_name
                        }
                    }
                },
                &#34;association&#34;: {
                    &#34;entity&#34;: [
                        {
                            &#34;clientName&#34;: self._client_name,
                            &#34;newName&#34;: self._new_name
                        }
                    ]
                }
            }
        }
        flag, response = self._client_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], request_json
        )

        if flag:
            if response.json():
                if &#39;errorMessage&#39; in response.json():
                    # for errorMessage: &#34;Operation Failed&#34; errorCode: 7
                    # for errorMessage: &#34;Error 0x911: Failed to process request due to invalid /
                    # entity information.Invalid clientId for clientName.\n&#34;
                    # errorCode: 2 and others

                    error_message = &#34;Failed to do namechange on client, &#34; \
                                    &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(
                                        response.json().get(&#39;errorCode&#39;),
                                        response.json().get(&#39;errorMessage&#39;)
                                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)

                elif &#39;errorCode&#39; in response.json().get(&#39;response&#39;)[0]:
                    error_code = str(
                        response.json().get(&#39;response&#39;)[0].get(&#39;errorCode&#39;))
                    if error_code != &#39;0&#39;:
                        error_message = &#34;Failed to do namechange on client&#34;
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def _commcell_name_change_op(self, parameters_dict):
        &#34;&#34;&#34;
        Performs the commcell namechange operations

        Args:
            parameters_dict (dict)          --  dictionary with common namechange parameters like
                                                old commserver hostname, new commserver hostname
                                                or old domain name, new domain name, client IDs.
                                                clientIds can be an empty list too.
                                                  {&#34;newName&#34;,
                                                    &#34;oldName&#34;,
                                                    &#34;operation&#34;,
                                                    &#34;clientIds&#34;}

            Raises:
            SDKException::
                if the client namechange failed

                if the response is empty

        &#34;&#34;&#34;

        request_json = {
            &#34;EVGui_ClientNameControlReq&#34;:
            {
                &#34;isPostMigration&#34;: &#34;&#34;,
                &#34;newName&#34;: parameters_dict.get(&#34;newName&#34;, &#34;&#34;),
                &#34;destinationConfiguration&#34;: 0,
                &#34;sourceConfiguration&#34;: 0,
                &#34;setWithoutConditionFlag&#34;: 0,
                &#34;oldName&#34;: parameters_dict.get(&#34;oldName&#34;, &#34;&#34;),
                &#34;commCellId&#34;: 0,
                &#34;operation&#34;: parameters_dict.get(&#34;operation&#34;, 0),
                &#34;forceChangeName&#34;: 0,
                &#34;clientList&#34;: parameters_dict.get(&#34;clientIds&#34;, [])

            }
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], request_json
        )
        if flag:
            if response.json():
                if &#39;errorMessage&#39; in response.json():
                    # for errorMessage: &#34;Operation Failed&#34; errorCode: 7
                    error_message = &#34;Failed to do namechange on commserver &#34; \
                                    &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(
                        response.json().get(&#39;errorCode&#39;),
                        response.json().get(&#39;errorMessage&#39;)
                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                
                elif not &#39;errorMessage&#39; in response.json():
                    return True

                elif &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                    error_code = int(
                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;))

                    error_message = &#34;Failed to do namechange on commserver &#34; \
                                    &#34;with errorCode [{0}], errorString [{1}]&#34;.format(
                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;),
                        response.json().get(&#39;error&#39;).get(&#39;errorString&#39;)
                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response. text))

    def get_clients_for_name_change_post_ccm(self):
        &#34;&#34;&#34;
            Gets clients available for name change after commcell migration.
            Raises:
            SDKException::
                if the client namechange failed
                if the response is empty
        &#34;&#34;&#34;
        xml = &#34;&#34;&#34;
            &lt;EVGui_GetClientForNameControlReq&gt;
            &lt;/EVGui_GetClientForNameControlReq&gt;
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], xml
        )
        def get_clients(response):
            clients_list = []
            all_clients = response.json()[&#34;clientList&#34;]
            for client in all_clients:
                temp_dict = {}
                name = client.get(&#34;name&#34;, &#34;&#34;)
                domain = client.get(&#34;domain&#34;, &#34;&#34;)
                cs_host_name = client.get(&#34;csHostName&#34;, &#34;&#34;)
                if name + &#34;.&#34; + domain != cs_host_name and name != cs_host_name:
                    clients_list.append({&#34;csHostname&#34;: cs_host_name, &#34;name&#34;: name})
            return clients_list
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                    error_code = int(
                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;))
                    if error_code != 1:
                        # for errorString: &#34;Failed to get clients for name change operation&#34;
                        # errorCode: 0 or others
                        error_message = &#34;Failed to get clients for name change operation&#34; \
                                        &#34;with errorCode [{0}], errorString [{1}]&#34;.format(
                            response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;),
                            response.json().get(&#39;error&#39;).get(&#39;errorString&#39;)
                        )
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                    elif error_code == 1:
                        return get_clients(response)
                elif &#39;errorMessage&#39; in response.json():
                    error_message = &#34;Failed to get clients for name change operation&#34; \
                                    &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(
                        response.json().get(&#39;errorCode&#39;),
                        response.json().get(&#39;errorMessage&#39;)
                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))

    def name_change_post_ccm(self, parameters_dict):
        &#34;&#34;&#34;
        Performs the commcell namechange for clients post commcell migration
        Args:
            parameters_dict (dict)      --  contains old commcell hostname, new commcell hostname,
                                            Ids of clients on which name change is to be performed
                                            {
                                            &#34;sourceCommcellHostname&#34;: &#34;source-1&#34;
                                            &#34;destinationCommcellHostname&#34;: &#34;dest-1&#34;
                                            &#34;clientIds&#34;: [&#34;id1&#34;, &#34;id2&#34;]
                                            }
            Raises:
            SDKException::
                if the client namechange failed
                if the response is empty
        &#34;&#34;&#34;
        name_change_xml = &#34;&#34;&#34;
            &lt;EVGui_ClientNameControlReq 
                commCellId=&#34;0&#34; 
                destinationConfiguration=&#34;2&#34; 
                isPostMigration=&#34;1&#34; 
                newName=&#34;{0}&#34;
                oldName=&#34;{1}&#34;
                operation=&#34;139&#34; 
                setWithoutConditionFlag=&#34;0&#34; 
                sourceConfiguration=&#34;2&#34;&gt; 
                {2}
            &lt;/EVGui_ClientNameControlReq&gt;
        &#34;&#34;&#34;
        client_tag = &#34;&#34;&#34;
            &lt;clientList val= &#34;{0}&#34;/&gt;
        &#34;&#34;&#34;
        clients_string = &#34;&#34;
        for clients_id in parameters_dict.get(&#34;clientIds&#34;, []):
            clients_string += client_tag.format(clients_id)
        name_change_xml = name_change_xml.format(parameters_dict[&#34;destinationCommcellHostname&#34;],
                                                 parameters_dict[&#34;sourceCommcellHostname&#34;],
                                                 clients_string)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], name_change_xml
        )
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                    error_code = int(
                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;))
                    if error_code != 1:
                        error_message = &#34;Failed to perform name change operation&#34; \
                                        &#34;with errorCode [{0}], errorString [{1}]&#34;.format(
                                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;),
                                        response.json().get(&#39;error&#39;).get(&#39;errorString&#39;)
                                        )
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                    elif error_code == 1:
                        return True
                elif &#39;errorMessage&#39; in response.json():
                    error_message = &#34;Failed to get clients for name change operation&#34; \
                                    &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(response.json().get(&#39;errorCode&#39;),
                                                                                      response.json().get(
                                                                                          &#39;errorMessage&#39;)
                                                                                      )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                        response.text))
        else:
            raise SDKException(
                &#39;Response&#39;,
                &#39;101&#39;,
                self._update_response_(
                    response.text))</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.name_change.NameChange.client_name"><code class="name">var <span class="ident">client_name</span></code></dt>
<dd>
<div class="desc"><p>Gets the client name</p>
<h2 id="returns">Returns</h2>
<p>str - client name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/name_change.py#L225-L236" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_name(self):
    &#34;&#34;&#34;
    Gets the client name

    Returns:
            str - client name
    &#34;&#34;&#34;
    if self._is_client:
        return self._client_name
    else:
        False</code></pre>
</details>
</dd>
<dt id="cvpysdk.name_change.NameChange.display_name"><code class="name">var <span class="ident">display_name</span></code></dt>
<dd>
<div class="desc"><p>Gets the display name of the client or commserver</p>
<h2 id="returns">Returns</h2>
<p>str - client or commserver display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/name_change.py#L193-L204" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def display_name(self):
    &#34;&#34;&#34;
    Gets the display name of the client or commserver

    Returns:
            str - client or commserver display name
    &#34;&#34;&#34;
    if self._is_client:
        return self._display_name
    else:
        return self._display_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.name_change.NameChange.domain_name"><code class="name">var <span class="ident">domain_name</span></code></dt>
<dd>
<div class="desc"><p>Gets the commserver hostname</p>
<h2 id="returns">Returns</h2>
<p>str - commserver hostname</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/name_change.py#L160-L168" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def domain_name(self):
    &#34;&#34;&#34;
    Gets the commserver hostname

    Returns:
            str - commserver hostname
    &#34;&#34;&#34;
    return self._commcell_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.name_change.NameChange.hostname"><code class="name">var <span class="ident">hostname</span></code></dt>
<dd>
<div class="desc"><p>Gets the client hostname or commserver hostname</p>
<p>Returns:
str - client hostname or commserver hostname</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/name_change.py#L110-L121" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def hostname(self):
    &#34;&#34;&#34;
    Gets the client hostname or commserver hostname

     Returns:
            str - client hostname or commserver hostname
    &#34;&#34;&#34;
    if self._is_client:
        return self._client_hostname
    else:
        return self._commcell_name</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.name_change.NameChange.get_clients_for_name_change_post_ccm"><code class="name flex">
<span>def <span class="ident">get_clients_for_name_change_post_ccm</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets clients available for name change after commcell migration.
Raises:
SDKException::
if the client namechange failed
if the response is empty</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/name_change.py#L399-L459" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_clients_for_name_change_post_ccm(self):
    &#34;&#34;&#34;
        Gets clients available for name change after commcell migration.
        Raises:
        SDKException::
            if the client namechange failed
            if the response is empty
    &#34;&#34;&#34;
    xml = &#34;&#34;&#34;
        &lt;EVGui_GetClientForNameControlReq&gt;
        &lt;/EVGui_GetClientForNameControlReq&gt;
    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], xml
    )
    def get_clients(response):
        clients_list = []
        all_clients = response.json()[&#34;clientList&#34;]
        for client in all_clients:
            temp_dict = {}
            name = client.get(&#34;name&#34;, &#34;&#34;)
            domain = client.get(&#34;domain&#34;, &#34;&#34;)
            cs_host_name = client.get(&#34;csHostName&#34;, &#34;&#34;)
            if name + &#34;.&#34; + domain != cs_host_name and name != cs_host_name:
                clients_list.append({&#34;csHostname&#34;: cs_host_name, &#34;name&#34;: name})
        return clients_list
    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                error_code = int(
                    response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;))
                if error_code != 1:
                    # for errorString: &#34;Failed to get clients for name change operation&#34;
                    # errorCode: 0 or others
                    error_message = &#34;Failed to get clients for name change operation&#34; \
                                    &#34;with errorCode [{0}], errorString [{1}]&#34;.format(
                        response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;),
                        response.json().get(&#39;error&#39;).get(&#39;errorString&#39;)
                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                elif error_code == 1:
                    return get_clients(response)
            elif &#39;errorMessage&#39; in response.json():
                error_message = &#34;Failed to get clients for name change operation&#34; \
                                &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(
                    response.json().get(&#39;errorCode&#39;),
                    response.json().get(&#39;errorMessage&#39;)
                )
                raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                    response.text))
    else:
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.name_change.NameChange.name_change_post_ccm"><code class="name flex">
<span>def <span class="ident">name_change_post_ccm</span></span>(<span>self, parameters_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs the commcell namechange for clients post commcell migration</p>
<h2 id="args">Args</h2>
<p>parameters_dict (dict)
&ndash;
contains old commcell hostname, new commcell hostname,
Ids of clients on which name change is to be performed
{
"sourceCommcellHostname": "source-1"
"destinationCommcellHostname": "dest-1"
"clientIds": ["id1", "id2"]
}
Raises:
SDKException::
if the client namechange failed
if the response is empty</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/name_change.py#L461-L534" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def name_change_post_ccm(self, parameters_dict):
    &#34;&#34;&#34;
    Performs the commcell namechange for clients post commcell migration
    Args:
        parameters_dict (dict)      --  contains old commcell hostname, new commcell hostname,
                                        Ids of clients on which name change is to be performed
                                        {
                                        &#34;sourceCommcellHostname&#34;: &#34;source-1&#34;
                                        &#34;destinationCommcellHostname&#34;: &#34;dest-1&#34;
                                        &#34;clientIds&#34;: [&#34;id1&#34;, &#34;id2&#34;]
                                        }
        Raises:
        SDKException::
            if the client namechange failed
            if the response is empty
    &#34;&#34;&#34;
    name_change_xml = &#34;&#34;&#34;
        &lt;EVGui_ClientNameControlReq 
            commCellId=&#34;0&#34; 
            destinationConfiguration=&#34;2&#34; 
            isPostMigration=&#34;1&#34; 
            newName=&#34;{0}&#34;
            oldName=&#34;{1}&#34;
            operation=&#34;139&#34; 
            setWithoutConditionFlag=&#34;0&#34; 
            sourceConfiguration=&#34;2&#34;&gt; 
            {2}
        &lt;/EVGui_ClientNameControlReq&gt;
    &#34;&#34;&#34;
    client_tag = &#34;&#34;&#34;
        &lt;clientList val= &#34;{0}&#34;/&gt;
    &#34;&#34;&#34;
    clients_string = &#34;&#34;
    for clients_id in parameters_dict.get(&#34;clientIds&#34;, []):
        clients_string += client_tag.format(clients_id)
    name_change_xml = name_change_xml.format(parameters_dict[&#34;destinationCommcellHostname&#34;],
                                             parameters_dict[&#34;sourceCommcellHostname&#34;],
                                             clients_string)
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;EXECUTE_QCOMMAND&#39;], name_change_xml
    )
    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                error_code = int(
                    response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;))
                if error_code != 1:
                    error_message = &#34;Failed to perform name change operation&#34; \
                                    &#34;with errorCode [{0}], errorString [{1}]&#34;.format(
                                    response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;),
                                    response.json().get(&#39;error&#39;).get(&#39;errorString&#39;)
                                    )
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                elif error_code == 1:
                    return True
            elif &#39;errorMessage&#39; in response.json():
                error_message = &#34;Failed to get clients for name change operation&#34; \
                                &#34;with errorCode [{0}], errorMessage [{1}]&#34;.format(response.json().get(&#39;errorCode&#39;),
                                                                                  response.json().get(
                                                                                      &#39;errorMessage&#39;)
                                                                                  )
                raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;, &#39;101&#39;, self._update_response_(
                    response.text))
    else:
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.name_change.OperationType"><code class="flex name class">
<span>class <span class="ident">OperationType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Operation Types supported to get schedules of particular optype</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/name_change.py#L67-L72" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OperationType(Enum):
    &#34;&#34;&#34; Operation Types supported to get schedules of particular optype&#34;&#34;&#34;
    COMMSERVER_HOSTNAME_REMOTE_CLIENTS = 147
    COMMSERVER_HOSTNAME_AFTER_DR = 139
    CLIENT_HOSTNAME = &#34;CLIENT_HOSTNAME&#34;
    COMMSERVER_HOSTNAME = &#34;COMMSERVER_HOSTNAME&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.name_change.OperationType.CLIENT_HOSTNAME"><code class="name">var <span class="ident">CLIENT_HOSTNAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.name_change.OperationType.COMMSERVER_HOSTNAME"><code class="name">var <span class="ident">COMMSERVER_HOSTNAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.name_change.OperationType.COMMSERVER_HOSTNAME_AFTER_DR"><code class="name">var <span class="ident">COMMSERVER_HOSTNAME_AFTER_DR</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.name_change.OperationType.COMMSERVER_HOSTNAME_REMOTE_CLIENTS"><code class="name">var <span class="ident">COMMSERVER_HOSTNAME_REMOTE_CLIENTS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.name_change.NameChange" href="#cvpysdk.name_change.NameChange">NameChange</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.name_change.NameChange.client_name" href="#cvpysdk.name_change.NameChange.client_name">client_name</a></code></li>
<li><code><a title="cvpysdk.name_change.NameChange.display_name" href="#cvpysdk.name_change.NameChange.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.name_change.NameChange.domain_name" href="#cvpysdk.name_change.NameChange.domain_name">domain_name</a></code></li>
<li><code><a title="cvpysdk.name_change.NameChange.get_clients_for_name_change_post_ccm" href="#cvpysdk.name_change.NameChange.get_clients_for_name_change_post_ccm">get_clients_for_name_change_post_ccm</a></code></li>
<li><code><a title="cvpysdk.name_change.NameChange.hostname" href="#cvpysdk.name_change.NameChange.hostname">hostname</a></code></li>
<li><code><a title="cvpysdk.name_change.NameChange.name_change_post_ccm" href="#cvpysdk.name_change.NameChange.name_change_post_ccm">name_change_post_ccm</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.name_change.OperationType" href="#cvpysdk.name_change.OperationType">OperationType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.name_change.OperationType.CLIENT_HOSTNAME" href="#cvpysdk.name_change.OperationType.CLIENT_HOSTNAME">CLIENT_HOSTNAME</a></code></li>
<li><code><a title="cvpysdk.name_change.OperationType.COMMSERVER_HOSTNAME" href="#cvpysdk.name_change.OperationType.COMMSERVER_HOSTNAME">COMMSERVER_HOSTNAME</a></code></li>
<li><code><a title="cvpysdk.name_change.OperationType.COMMSERVER_HOSTNAME_AFTER_DR" href="#cvpysdk.name_change.OperationType.COMMSERVER_HOSTNAME_AFTER_DR">COMMSERVER_HOSTNAME_AFTER_DR</a></code></li>
<li><code><a title="cvpysdk.name_change.OperationType.COMMSERVER_HOSTNAME_REMOTE_CLIENTS" href="#cvpysdk.name_change.OperationType.COMMSERVER_HOSTNAME_REMOTE_CLIENTS">COMMSERVER_HOSTNAME_REMOTE_CLIENTS</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>