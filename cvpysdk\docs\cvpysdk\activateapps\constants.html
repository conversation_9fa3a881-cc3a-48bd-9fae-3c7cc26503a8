<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activateapps.constants API documentation</title>
<meta name="description" content="Helper file to maintain all the constants for Activate Apps …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activateapps.constants</code></h1>
</header>
<section id="section-intro">
<p>Helper file to maintain all the constants for Activate Apps</p>
<p>ActivateEntityConstants
-
Maintains constants for Regex custom entity</p>
<p>TagConstants
-
Maintains constants for Tags</p>
<p>ClassifierConstants
-
Maintains constants for Classifiers</p>
<p>TrainingStatus
-
Enum class for classifier training status</p>
<p>TargetApps
-
Enum class for Activate App types</p>
<p>PlanConstants
-
Maintains constants for Plan operations</p>
<p>InventoryConstants
-
Maintains constants for Inventory Manager apps</p>
<p>EdiscoveryConstants
-
Maintains constants for Ediscovery clients in activate</p>
<p>RequestConstants
-
Maintains constants for request manager in Activate</p>
<p>ComplianceConstants
-
Maintains constants for Compliance Search in Activate</p>
<p>ClientType
-
Enum class for datasource client type</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L1-L1302" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Helper file to maintain all the constants for Activate Apps

ActivateEntityConstants         -       Maintains constants for Regex custom entity

TagConstants                    -       Maintains constants for Tags

ClassifierConstants             -       Maintains constants for Classifiers

TrainingStatus                  -       Enum class for classifier training status

TargetApps                      -       Enum class for Activate App types

PlanConstants                   -       Maintains constants for Plan operations

InventoryConstants              -       Maintains constants for Inventory Manager apps

EdiscoveryConstants             -       Maintains constants for Ediscovery clients in activate

RequestConstants                -       Maintains constants for request manager in Activate

ComplianceConstants             -       Maintains constants for Compliance Search in Activate

ClientType                      -       Enum class for datasource client type

&#34;&#34;&#34;
import copy
from enum import Enum


class RequestConstants:
    &#34;&#34;&#34;class to maintain constants for request manager&#34;&#34;&#34;
    PROPERTY_REVIEW_CRIERIA = &#39;ReviewCriteria&#39;
    PROPERTY_ENTITIES = &#39;Entities&#39;
    PROPERTY_REQUEST_HANDLER_ID = &#39;RequestHandlerId&#39;
    PROPERTY_REQUEST_HANDLER_NAME = &#39;RequestHandlerName&#39;
    PROPERTY_REVIEW_SET_ID = &#39;ReviewSetId&#39;
    SEARCH_QUERY_SELECTION_SET = {
        &#34;entity_*&#34;,
        &#34;count_entity_*&#34;,
        &#34;Url&#34;,
        &#34;contentid&#34;,
        &#34;FileName&#34;,
        &#34;Size&#34;,
        &#34;data_source&#34;,
        &#34;data_source_name&#34;,
        &#34;entities_extracted&#34;,
        &#34;RedactMode*&#34;,
        &#34;CommentFor*&#34;,
        &#34;ConsentFor*&#34;}

    FACET_REVIEWED = &#39;_ConsentFor_%s_b_Reviewed&#39;
    FACET_NOT_REVIEWED = &#39;_ConsentFor_%s_b_Not reviewed&#39;
    FACET_ACCEPTED = &#39;_ConsentFor_%s_b_Accepted&#39;
    FACET_DECLINED = &#39;_ConsentFor_%s_b_Declined&#39;
    FACET_REDACTED = &#39;_RedactMode_%s_b_Redacted&#39;
    FACET_NOT_REDACTED = &#39;_RedactMode_%s_b_Not redacted&#39;
    FACET_COUNT = &#34;count&#34;
    REQUEST_FEDERATED_FACET_SEARCH_QUERY = {&#34;searchParams&#34;: [{&#34;key&#34;: &#34;q&#34;,
                                                              &#34;value&#34;: &#34;*:*&#34;},
                                                             {&#34;key&#34;: &#34;wt&#34;,
                                                              &#34;value&#34;: &#34;json&#34;},
                                                             {&#34;key&#34;: &#34;rows&#34;,
                                                              &#34;value&#34;: &#34;0&#34;},
                                                             {&#34;key&#34;: &#34;defType&#34;,
                                                              &#34;value&#34;: &#34;edismax&#34;},
                                                             {&#34;key&#34;: &#34;facet&#34;,
                                                              &#34;value&#34;: &#34;true&#34;},
                                                             {&#34;key&#34;: &#34;json.facet&#34;,
                                                              &#34;value&#34;: &#34;{\&#34;_ConsentFor_&lt;rsidparam&gt;_b_Reviewed\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_ConsentFor_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;ConsentFor_&lt;rsidparam&gt;_b:*\&#34;,\&#34;facet\&#34;:{}},\&#34;_ConsentFor_&lt;rsidparam&gt;_b_Not reviewed\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_ConsentFor_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;contentid:* AND -(ConsentFor_&lt;rsidparam&gt;_b:*)\&#34;,\&#34;facet\&#34;:{}},\&#34;_ConsentFor_&lt;rsidparam&gt;_b_Accepted\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_ConsentFor_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;ConsentFor_&lt;rsidparam&gt;_b:true\&#34;,\&#34;facet\&#34;:{}},\&#34;_ConsentFor_&lt;rsidparam&gt;_b_Declined\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_ConsentFor_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;ConsentFor_&lt;rsidparam&gt;_b:false\&#34;,\&#34;facet\&#34;:{}},\&#34;_RedactMode_&lt;rsidparam&gt;_b_Redacted\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_RedactMode_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_RedactMode_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_RedactMode_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;RedactMode_&lt;rsidparam&gt;_b:true\&#34;,\&#34;facet\&#34;:{}},\&#34;_RedactMode_&lt;rsidparam&gt;_b_Not redacted\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_RedactMode_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_RedactMode_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_RedactMode_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;RedactMode_&lt;rsidparam&gt;_b:false\&#34;,\&#34;facet\&#34;:{}},\&#34;FileExtension\&#34;:{\&#34;type\&#34;:\&#34;terms\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_FileExtension\&#34;,\&#34;tag_exclude_FileExtension\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;field\&#34;:\&#34;FileExtension\&#34;,\&#34;limit\&#34;:-1,\&#34;facet\&#34;:{},\&#34;sort\&#34;:{\&#34;count\&#34;:\&#34;desc\&#34;}},\&#34;ReadAccessUserName\&#34;:{\&#34;type\&#34;:\&#34;terms\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_ReadAccessUserName\&#34;,\&#34;tag_exclude_ReadAccessUserName\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;field\&#34;:\&#34;ReadAccessUserName\&#34;,\&#34;limit\&#34;:-1,\&#34;facet\&#34;:{},\&#34;sort\&#34;:{\&#34;count\&#34;:\&#34;desc\&#34;}},\&#34;data_source_name\&#34;:{\&#34;type\&#34;:\&#34;terms\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_data_source_name\&#34;,\&#34;tag_exclude_data_source_name\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;field\&#34;:\&#34;data_source_name\&#34;,\&#34;limit\&#34;:-1,\&#34;facet\&#34;:{},\&#34;sort\&#34;:{\&#34;count\&#34;:\&#34;desc\&#34;}}}&#34;},
                                                             {&#34;key&#34;: &#34;useDCubeReq&#34;,
                                                              &#34;value&#34;: &#34;true&#34;}]}

    FIELD_DOC_COUNT = &#34;TotalDocuments&#34;
    FIELD_REVIEWED = &#34;ReviewedDocuments&#34;
    FIELD_NOT_REVIEWED = &#34;Non-ReviewedDocuments&#34;
    FIELD_ACCEPTED = &#39;AcceptedDocuments&#39;
    FIELD_DECLINED = &#39;DeclinedDocuments&#39;
    FIELD_REDACTED = &#39;RedactedDocuments&#39;
    FIELD_NOT_REDACTED = &#39;Non-RedactedDocuments&#39;

    class RequestStatus(Enum):
        &#34;&#34;&#34;enum to specify different request status&#34;&#34;&#34;
        TaskCreated = 1
        TaskConfigured = 2
        ReviewInProgress = 3
        ReviewCompleted = 4
        ApproveCompleted = 5
        ExportCompleted = 6
        DeleteCompleted = 7
        TaskCompleted = 8
        ApprovalRequested = 9
        ActionInProgress = 10
        CompletedWithErrors = 11
        Failed = 12

    class RequestType(Enum):
        &#34;&#34;&#34;enum to maintain different request type&#34;&#34;&#34;
        EXPORT = &#39;EXPORT&#39;
        DELETE = &#39;DELETE&#39;


class EdiscoveryConstants:
    &#34;&#34;&#34;class to maintain constants for ediscovery clients&#34;&#34;&#34;

    class CrawlType(Enum):
        &#34;&#34;&#34;Crawl type for SDG/FSO jobs&#34;&#34;&#34;
        LIVE = 1
        BACKUP = 2
        CONTENT_INDEXED = 3
        FILE_LEVEL_ANALYTICS = 4
        BACKUP_V2 = 5

    class SourceType(Enum):
        &#34;&#34;&#34;Source type of data for FSO/SDG app&#34;&#34;&#34;
        SOURCE = 1
        BACKUP = 2

    class ReviewActions(Enum):
        &#34;&#34;&#34;Review actions for documents on SDG/FSO app&#34;&#34;&#34;
        DELETE = 1
        MOVE = 4
        RETENTION = 10
        IGNORE = 11
        ARCHIVE = 90
        TAG = 98

    class RiskTypes(Enum):
        &#34;&#34;&#34;Different risk types for documents on SDG/FSO app&#34;&#34;&#34;
        OPEN_ACCESS = 1
        OLD_FILES = 2
        FILE_MOVED = 3
        MULTI_USER_ACCESS = 4
        NO_RETENTION = 5
        IS_PROTECTED = 6

    class ClientType(Enum):
        &#34;&#34;&#34;Different Type of SDG Datasource&#34;&#34;&#34;
        FILE_SYSTEM = 5
        EXCHANGE = 17
        ONEDRIVE = 35

    EXCHANGE_AGENT = &#34;exchange mailbox&#34;
    EXCHANGE_INSTANCE = &#34;defaultinstancename&#34;
    EXCHANGE_BACKUPSET = &#34;user mailbox&#34;
    EXCHANGE_SUBCLIENT = &#34;usermailbox&#34;

    ONEDRIVE_AGENT = &#39;Cloud Apps&#39;
    ONEDRIVE_INSTANCE = &#39;OneDrive&#39;
    ONEDRIVE_BACKUPSET = &#39;defaultbackupset&#39;
    ONEDRIVE_SUBCLIENT = &#39;default&#39;

    FSO_SERVERS = &#34;FsoServers&#34;
    FSO_SERVER_GROUPS = &#34;FsoServerGroups&#34;

    SERVER_LEVEL_SCHEDULE_JSON = {
        &#34;taskInfo&#34;: {
            &#34;task&#34;: {
                &#34;taskType&#34;: 4,
                &#34;taskFlags&#34;: {
                    &#34;isEdiscovery&#34;: True
                },
                &#34;taskName&#34;: &#34;&#34;
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTask&#34;: {
                        &#34;operationType&#34;: 5025,
                        &#34;subTaskType&#34;: 1,
                        &#34;subTaskName&#34;: &#34;&#34;
                    },
                    &#34;pattern&#34;: {},
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {
                            &#34;backupLevel&#34;: 2,
                            &#34;mediaOpt&#34;: {
                                &#34;auxcopyJobOption&#34;: {
                                    &#34;allCopies&#34;: True,
                                    &#34;useMaximumStreams&#34;: True,
                                    &#34;maxNumberOfStreams&#34;: 1
                                }
                            }
                        },
                        &#34;adminOpts&#34;: {
                            &#34;contentIndexingOption&#34;: {
                                &#34;idaType&#34;: 1,
                                &#34;operationType&#34;: 2,
                                &#34;fileAnalytics&#34;: False
                            }
                        }
                    }
                }
            ],
            &#34;associations&#34;: [
                {
                    &#34;clientId&#34;: 0,
                    &#34;_type_&#34;: 3
                }
            ]
        }
    }

    REVIEW_ACTION_FSO_SUPPORTED = [ReviewActions.DELETE.value, ReviewActions.MOVE.value, ReviewActions.ARCHIVE.value]

    REVIEW_ACTION_SDG_SUPPORTED = [
        ReviewActions.DELETE.value,
        ReviewActions.MOVE.value,
        ReviewActions.ARCHIVE.value,
        ReviewActions.RETENTION.value,
        ReviewActions.IGNORE.value]

    CREATE_CLIENT_REQ_JSON = {
        &#34;clientInfo&#34;: {
            &#34;clientType&#34;: 19,
            &#34;edgeDrivePseudoClientProperties&#34;: {
                &#34;systemDriveType&#34;: 7,
                &#34;edgeDriveAssociations&#34;: {},
                &#34;eDiscoveryInfo&#34;: {
                    &#34;eDiscoverySubType&#34;: 2,
                    &#34;inventoryDataSource&#34;: {
                        &#34;seaDataSourceId&#34;: 0
                    }
                }
            },
            &#34;plan&#34;: {
                &#34;planId&#34;: 0
            }
        },
        &#34;entity&#34;: {
            &#34;clientName&#34;: &#34;&#34;,
            &#34;_type_&#34;: 3
        }
    }

    REVIEW_ACTION_DELETE_REQ_JSON = {
        &#34;operation&#34;: ReviewActions.DELETE.value,
        &#34;files&#34;: &#34;&#34;,
        &#34;deleteFromBackup&#34;: False,
        &#34;options&#34;: &#34;&#34;}

    REVIEW_ACTION_MOVE_REQ_JSON = {&#34;operation&#34;: ReviewActions.MOVE.value, &#34;files&#34;: &#34;&#34;, &#34;options&#34;: &#34;&#34;}

    REVIEW_ACTION_SET_RETENTION_REQ_JSON = {
        &#34;operation&#34;: ReviewActions.RETENTION.value,
        &#34;remActionRequest&#34;: {
            &#34;dataSourceId&#34;: 0,
            &#34;handlerName&#34;: &#34;default&#34;,
            &#34;isBulkOperation&#34;: False,
            &#34;searchRequest&#34;: &#34;&#34;},
        &#34;setRetentionReq&#34;: {&#34;numOfMonthsRemain&#34;: 0},
    }

    REVIEW_ACTION_IGNORE_FILES_REQ_JSON = {
        &#34;operation&#34;: ReviewActions.IGNORE.value,
        &#34;remActionRequest&#34;: {
            &#34;dataSourceId&#34;: 0,
            &#34;handlerName&#34;: &#34;default&#34;,
            &#34;isBulkOperation&#34;: False,
            &#34;searchRequest&#34;: &#34;&#34;},
        &#34;ignoreRisksReq&#34;: {&#34;ignoreAllRisks&#34;: False},
    }

    REVIEW_ACTION_TAG_REQ_JSON = {
        &#34;operation&#34;: ReviewActions.TAG.value,
        &#34;remActionRequest&#34;: {
            &#34;dataSourceId&#34;: 0,
            &#34;isBulkOperation&#34;: False},
        &#34;taggingRequest&#34;: {}}

    REVIEW_ACTION_BULK_SEARCH_REQ = &#34;{\&#34;searchParams\&#34;:&#34; \
                                    &#34;[{\&#34;key\&#34;:\&#34;q\&#34;,\&#34;value\&#34;:\&#34;*:*\&#34;},{\&#34;key\&#34;:\&#34;wt\&#34;,\&#34;value\&#34;:\&#34;json\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;rows\&#34;,\&#34;value\&#34;:\&#34;0\&#34;},{\&#34;key\&#34;:\&#34;defType\&#34;,\&#34;value\&#34;:\&#34;edismax\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fq\&#34;,\&#34;value\&#34;:\&#34;IsFile:\\\&#34;1\\\&#34;\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;FileName\&#34;},{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;OwnerName\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;OwnerLocation\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CountryCode\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;FileExtension\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;operatingSystem\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;IsProtected\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;FileName_path\&#34;},{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Url\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ClientId\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;DocumentType\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;contentid\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowListUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowModifyUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowWriteUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowExecuteUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowFullControlUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ExpiryDate\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CreatedTime\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;data_source\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;data_source_name\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;data_source_type\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;entities_extracted\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ConsentFor_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;RedactFor_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;RedactMode_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CommentFor_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AppType\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ApplicationId\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CVTurboGUID\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CommcellNumber\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AchiveFileId\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ArchiveFileOffset\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Size\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Risk_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;LastAccessedBy\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;LastModifiedBy\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ModifiedTimeAsStr\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;entity_doc_tags\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;TagIds\&#34;},{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;FileName\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Url\&#34;},{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Size\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;OwnerName\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;entity_doc_tags\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;start\&#34;,\&#34;value\&#34;:\&#34;0\&#34;}]}&#34;

    REVIEW_ACTION_SEARCH_FL_SET = {
        &#34;FileName&#34;,
        &#34;OwnerName&#34;,
        &#34;OwnerLocation&#34;,
        &#34;CountryCode&#34;,
        &#34;FileExtension&#34;,
        &#34;operatingSystem&#34;,
        &#34;IsProtected&#34;,
        &#34;FileName_path&#34;,
        &#34;Url&#34;,
        &#34;ClientId&#34;,
        &#34;DocumentType&#34;,
        &#34;contentid&#34;,
        &#34;AllowListUsername&#34;,
        &#34;AllowModifyUsername&#34;,
        &#34;AllowWriteUsername&#34;,
        &#34;AllowExecuteUsername&#34;,
        &#34;AllowFullControlUsername&#34;,
        &#34;ExpiryDate&#34;,
        &#34;CreatedTime&#34;,
        &#34;data_source&#34;,
        &#34;data_source_name&#34;,
        &#34;data_source_type&#34;,
        &#34;entities_extracted&#34;,
        &#34;ConsentFor_*&#34;,
        &#34;RedactFor_*&#34;,
        &#34;RedactMode_*&#34;,
        &#34;CommentFor_*&#34;,
        &#34;AppType&#34;,
        &#34;ApplicationId&#34;,
        &#34;CVTurboGUID&#34;,
        &#34;CommcellNumber&#34;,
        &#34;AchiveFileId&#34;,
        &#34;ArchiveFileOffset&#34;,
        &#34;Size&#34;,
        &#34;Risk_*&#34;,
        &#34;LastAccessedBy&#34;,
        &#34;LastModifiedBy&#34;,
        &#34;ModifiedTimeAsStr&#34;,
        &#34;entity_doc_tags&#34;,
        &#34;TagIds&#34;}

    REVIEW_ACTION_IDA_SELECT_SET = {
        5: {&#39;contentid&#39;, &#39;Url&#39;, &#39;ClientId&#39;, &#39;CreatedTime&#39;, &#39;FileName&#39;}}

    FS_SERVER_HANDLER_NAME = &#39;GetFileServers&#39;
    ADD_FS_REQ_JSON = {
        &#34;datasourceId&#34;: 0,
        &#34;indexServerClientId&#34;: 0,
        &#34;followScheduleCrawl&#34;: False,
        &#34;datasources&#34;: [
            {
                &#34;datasourceName&#34;: &#34;&#34;,
                &#34;properties&#34;: [],
                &#34;datasourceType&#34;: 5,
                &#34;accessNodes&#34;: [
                    {
                        &#34;clientId&#34;: 0,
                        &#34;clientName&#34;: &#34;&#34;
                    }
                ]
            }
        ]
    }

    ADD_O365_SDG_BACKED_UP_DS_REQ = {
        &#34;clientId&#34;: 0,
        &#34;followScheduleCrawl&#34;: False,
        &#34;datasources&#34;: [
            {
                &#34;datasourceName&#34;: &#34;&#34;,
                &#34;datasourceType&#34;: 0,
                &#34;properties&#34;: []
            }
        ]
    }

    FS_DEFAULT_EXPORT_FIELDS = {&#39;FileName&#39;, &#39;Url&#39;, &#39;Size&#39;, &#39;OwnerName&#39;, &#39;CreatedTime&#39;, &#39;AccessTime&#39;, &#39;ModifiedTime&#39;}
    EXPORT_DOWNLOAD_REQ = {
        &#34;appTypeId&#34;: 200,
        &#34;responseFileName&#34;: &#34;&#34;,
        &#34;fileParams&#34;: [
            {
                &#34;name&#34;: &#34;&#34;,
                &#34;id&#34;: 2
            },
            {
                &#34;name&#34;: &#34;zip&#34;,
                &#34;id&#34;: 3
            },
            {
                &#34;name&#34;: &#34;Streamed&#34;,
                &#34;id&#34;: 10
            }
        ]
    }

    DATA_SOURCE_TYPES = {
        0: &#39;NONE&#39;,
        1: &#39;jdbc&#39;,
        5: &#39;file&#39;,
        9: &#39;ldap&#39;,
        10: &#39;federated&#39;,
        11: &#39;blank&#39;,
        15: &#39;fla&#39;,
        16: &#39;edge&#39;,
        17: &#39;exchange&#39;,
        18: &#39;reviewset&#39;,
        22: &#39;nfs&#39;,
        24: &#39;systemdefault&#39;,
        26: &#39;onedrive&#39;,
        27: &#39;sharepoint&#39;,
        28: &#39;email&#39;,
        29: &#39;dbanalysis&#39;,
        30: &#39;cloudpaas&#39;,
        31: &#39;googledrive&#39;,
        32: &#39;gmail&#39;,
        34: &#39;onedriveindex&#39;,
        35: &#39;multinodefederated&#39;,
        37: &#39;dynamic365&#39;
    }

    VIEW_CATEGORY_PERMISSION = {
        &#34;permissionId&#34;: 31,
        &#34;permissionName&#34;: &#34;View&#34;,
        &#34;_type_&#34;: 122
    }
    EDIT_CATEGORY_PERMISSION = {
        &#34;permissionId&#34;: 2,
        &#34;permissionName&#34;: &#34;Agent Management&#34;,
        &#34;_type_&#34;: 122
    }

    SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;_type_&#34;: 3,
                    &#34;clientId&#34;: 0
                }
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        {
                            &#34;userId&#34;: 0,
                            &#34;_type_&#34;: 0,
                            &#34;userName&#34;: &#34;&#34;
                        }
                    ],
                    &#34;properties&#34;: {
                        &#34;categoryPermission&#34;: {
                            &#34;categoriesPermissionOperationType&#34;: 1,
                            &#34;categoriesPermissionList&#34;: [
                                VIEW_CATEGORY_PERMISSION
                            ]
                        }
                    }
                }
            ]
        }
    }

    DS_FILE = &#39;file&#39;
    DS_CLOUD_STORAGE = &#39;cloudstorage&#39;
    FIELD_DATA_SOURCE_DISPLAY_NAME = &#39;datasourceDisplayName&#39;
    FIELD_DISPLAY_NAME = &#39;displayName&#39;
    FIELD_DATA_SOURCE_NAME = &#39;datasourceName&#39;
    FIELD_DATA_SOURCE_ID_NON_SEA = &#39;datasourceId&#39;
    FIELD_DOCUMENT_COUNT = &#39;documentCount&#39;
    FIELD_DATA_SOURCE_TYPE = &#39;datasourceType&#39;
    FIELD_DATA_SOURCE_ID = &#39;seaDataSourceId&#39;
    FIELD_PLAN_ID = &#39;planId&#39;
    FIELD_DC_PLAN_ID = &#39;dcplanid&#39;
    FIELD_PSEDUCO_CLIENT_ID = &#39;pseudoclientid&#39;
    FIELD_SUBCLIENT_ID = &#39;subclientid&#39;
    FIELD_CRAWL_TYPE = &#39;crawltype&#39;
    FIELD_DATA_SOURCE_NAME_SEA = &#39;seaDataSourceName&#39;
    FIELD_CONTENT_ID = &#39;contentid&#39;
    FIELD_IS_FILE = &#39;IsFile:1&#39;
    DYNAMIC_FEDERATED_SEARCH_PARAMS = {&#34;searchParams&#34;: []}

    CRITERIA_EXTRACTED_DOCS = &#34;entities_extracted:*&#34;

    TAGGING_ITEMS_REQUEST = {
        &#34;entityType&#34;: &#34;SEA_DATASOURCE_ENTITY&#34;,
        &#34;entityIds&#34;: [],
        &#34;handler&#34;: &#34;default&#34;,
        &#34;searchRequest&#34;: {},
        &#34;opType&#34;: &#34;ADD&#34;,
        &#34;tags&#34;: [],
        &#34;isAsync&#34;: True}
    TAGGING_ITEMS_REVIEW_REQUEST = {&#34;operation&#34;: ReviewActions.TAG.value, &#34;files&#34;: &#34;&#34;,
                                    &#34;options&#34;: &#34;&#34;,
                                    &#34;taggingInformation&#34;: {&#39;opType&#39;: &#39;ADD&#39;, &#34;tagIds&#34;: []}}

    START_CRAWL_SERVER_REQUEST_JSON = {
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [
                {
                    &#34;clientId&#34;: 0,
                    &#34;_type_&#34;: 3
                }
            ],
            &#34;task&#34;: {
                &#34;taskType&#34;: 1,
                &#34;taskName&#34;: &#34;&#34;,
                &#34;taskFlags&#34;: {
                    &#34;isEdiscovery&#34;: True
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTask&#34;: {
                        &#34;subTaskName&#34;: &#34;CVPYSDK_FSO_IMMEDIATE&#34;,
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 5025
                    },
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {
                            &#34;backupLevel&#34;: 2,
                            &#34;mediaOpt&#34;: {
                                &#34;auxcopyJobOption&#34;: {
                                    &#34;maxNumberOfStreams&#34;: 1,
                                    &#34;allCopies&#34;: True,
                                    &#34;useMaximumStreams&#34;: True
                                }
                            }
                        },
                        &#34;adminOpts&#34;: {
                            &#34;contentIndexingOption&#34;: {
                                &#34;fileAnalytics&#34;: False,
                                &#34;idaType&#34;: 1,
                                &#34;operationType&#34;: 2
                            }
                        }
                    }
                }
            ]
        }
    }


class InventoryConstants:
    &#34;&#34;&#34;Class to maintain constants for inventory manager&#34;&#34;&#34;

    CRAWL_JOB_FAILED_STATE = [5, 6, 7, 8, 9, 10, 11, 12]

    CRAWL_JOB_COMPLETE_STATE = 4

    CRAWL_JOB_COMPLETE_ERROR_STATE = 13

    INVENTORY_ADD_REQUEST_JSON = {
        &#34;name&#34;: &#34;&#34;,
        &#34;identityServers&#34;: [],
        &#34;indexServer&#34;: {
            &#34;cloudId&#34;: 0,
            &#34;displayName&#34;: &#34;&#34;
        }
    }

    FIELD_PROPERTY_NAME = &#39;name&#39;
    FIELD_PROPERTY_DNSHOST = &#39;hostName&#39;
    FIELD_PROPERTY_OS = &#39;operatingSystem&#39;
    FIELD_PROPERTY_IP = &#39;ipAddress&#39;
    FIELD_PROPERTY_COUNTRYCODE = &#39;countryCode&#39;

    KWARGS_NAME = &#39;name&#39;
    KWARGS_IP = &#39;ip&#39;
    KWARGS_OS = &#39;os&#39;
    KWARGS_FQDN = &#39;fqdn&#39;
    KWARGS_COUNTRY_CODE = &#39;country_code&#39;

    FIELD_PROPS_MAPPING = {
        FIELD_PROPERTY_NAME: KWARGS_NAME,
        FIELD_PROPERTY_IP: KWARGS_IP,
        FIELD_PROPERTY_OS: KWARGS_OS,
        FIELD_PROPERTY_DNSHOST: KWARGS_FQDN,
        FIELD_PROPERTY_COUNTRYCODE: KWARGS_COUNTRY_CODE
    }

    ASSET_FILE_SERVER_PROPERTY = [
        FIELD_PROPERTY_NAME,
        FIELD_PROPERTY_DNSHOST,
        FIELD_PROPERTY_OS,
        FIELD_PROPERTY_IP,
        FIELD_PROPERTY_COUNTRYCODE
    ]

    IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON = {&#34;identityServers&#34;: [], &#34;startDataCollection&#34;: True}

    VIEW_CATEGORY_PERMISSION = {
        &#34;permissionId&#34;: 31,
        &#34;permissionName&#34;: &#34;View&#34;,
        &#34;_type_&#34;: 122
    }
    EDIT_CATEGORY_PERMISSION = {
        &#34;permissionId&#34;: 2,
        &#34;permissionName&#34;: &#34;Agent Management&#34;,
        &#34;_type_&#34;: 122
    }

    INVENTORY_SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;_type_&#34;: 132,
                    &#34;seaDataSourceId&#34;: 0
                }
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        {
                            &#34;userId&#34;: 0,
                            &#34;_type_&#34;: 0,
                            &#34;userName&#34;: &#34;&#34;
                        }
                    ],
                    &#34;properties&#34;: {
                        &#34;categoryPermission&#34;: {
                            &#34;categoriesPermissionOperationType&#34;: 1,
                            &#34;categoriesPermissionList&#34;: [
                                VIEW_CATEGORY_PERMISSION
                            ]
                        }
                    }
                }
            ]
        }
    }

    class AssetType(Enum):
        &#34;&#34;&#34;Asset type for inventory&#34;&#34;&#34;
        IDENTITY_SERVER = 61
        FILE_SERVER = 132


class PlanConstants:
    &#34;&#34;&#34;Class to maintain constants related to DC plan activate operations&#34;&#34;&#34;

    INDEXING_ONLY_METADATA = 1
    INDEXING_METADATA_AND_CONTENT = 2

    DEFAULT_INCLUDE_DOC_TYPES = &#34;*.doc,*.docx,*.xls,*.xlsx,*.ppt,*.pptx,*.msg,*.txt,*.rtf,*.eml,*.pdf,&#34; \
                                &#34;*.htm,*.html,*.csv,*.log,*.ods,*.odt,*.odg,*.odp,*.dot,*.pages,*.xmind&#34;
    DEFAULT_EXCLUDE_LIST = [
        &#34;C:\\Program Files&#34;,
        &#34;C:\\Program Files (x86)&#34;,
        &#34;C:\\Windows&#34;]

    DEFAULT_MIN_DOC_SIZE = 0
    DEFAULT_MAX_DOC_SIZE = 50

    PLAN_UPDATE_REQUEST_JSON = {
        7: {
            &#34;ciPolicyInfo&#34;: {
                &#34;ciPolicy&#34;: {
                    &#34;policyType&#34;: 5,
                    &#34;flags&#34;: 536870912,
                    &#34;agentType&#34;: {
                        &#34;appTypeId&#34;: 0,
                        &#34;entityInfo&#34;: {},
                        &#34;flags&#34;: {}
                    },
                    &#34;detail&#34;: {
                        &#34;ciPolicy&#34;: {}
                    }
                }
            },
            &#34;eePolicyInfo&#34;: {
                &#34;eePolicy&#34;: {
                    &#34;policyType&#34;: 3,
                    &#34;flags&#34;: 536870920,
                    &#34;agentType&#34;: {
                        &#34;appTypeId&#34;: 0,
                        &#34;entityInfo&#34;: {},
                        &#34;flags&#34;: {}
                    },
                    &#34;detail&#34;: {
                        &#34;eePolicy&#34;: {}
                    }
                }
            },
            &#34;summary&#34;: {
                &#34;plan&#34;: {
                    &#34;planId&#34;: 0
                }
            }
        },
        6: {
            &#34;ciPolicyInfo&#34;: {
                &#34;ciPolicy&#34;: {
                    &#34;policyType&#34;: 5,
                    &#34;detail&#34;: {
                        &#34;ciPolicy&#34;: {
                            &#34;opType&#34;: 2,
                            &#34;enableExactSearch&#34;: False,
                            &#34;ciPolicyType&#34;: 5,
                            &#34;filters&#34;: {
                                &#34;fileFilters&#34;: {
                                    &#34;includeDocTypes&#34;: &#34;&#34;,
                                    &#34;minDocSize&#34;: 0,
                                    &#34;maxDocSize&#34;: 50
                                }
                            }
                        }
                    }
                }
            },
            &#34;eePolicyInfo&#34;: {},
            &#34;exchange&#34;: {},
            &#34;office365Info&#34;: {
                &#34;o365Exchange&#34;: {
                    &#34;mbArchiving&#34;: {
                        &#34;policyType&#34;: 1,
                        &#34;agentType&#34;: {
                            &#34;appTypeId&#34;: 137
                        },
                        &#34;detail&#34;: {
                            &#34;emailPolicy&#34;: {
                                &#34;emailPolicyType&#34;: 1,
                                &#34;archivePolicy&#34;: {
                                    &#34;primaryMailbox&#34;: True,
                                    &#34;contentIndexProps&#34;: {}
                                }
                            }
                        }
                    }
                },
                &#34;o365CloudOffice&#34;: {
                    &#34;caBackup&#34;: {
                        &#34;policyType&#34;: 6,
                        &#34;detail&#34;: {
                            &#34;cloudAppPolicy&#34;: {
                                &#34;cloudAppPolicyType&#34;: 1,
                                &#34;backupPolicy&#34;: {
                                    &#34;onedrivebackupPolicy&#34;: {},
                                    &#34;spbackupPolicy&#34;: {},
                                    &#34;teamsbackupPolicy&#34;: {}
                                }
                            }
                        }

                    }

                }

            },

            &#34;summary&#34;: {
                &#34;plan&#34;: {
                    &#34;planName&#34;: &#34;&#34;,
                    &#34;planId&#34;: 0
                }
            }

        }
    }

    PLAN_SCHEDULE_REQUEST_JSON = {
        7: {
            &#34;summary&#34;: {
                &#34;plan&#34;: {
                    &#34;planId&#34;: 0
                }
            },
            &#34;schedule&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;_type_&#34;: 158,
                        &#34;entityId&#34;: 0
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 4,
                    &#34;taskName&#34;: &#34;&#34;,
                    &#34;taskFlags&#34;: {
                        &#34;isEdiscovery&#34;: True
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskName&#34;: &#34;&#34;,
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 5025
                        },
                        &#34;pattern&#34;: {},
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;backupLevel&#34;: 2,
                                &#34;mediaOpt&#34;: {
                                    &#34;auxcopyJobOption&#34;: {
                                        &#34;maxNumberOfStreams&#34;: 1,
                                        &#34;allCopies&#34;: True,
                                        &#34;useMaximumStreams&#34;: True
                                    }
                                }
                            },
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: {
                                    &#34;fileAnalytics&#34;: False,
                                    &#34;idaType&#34;: 1,
                                    &#34;operationType&#34;: 2
                                }
                            }
                        }
                    }
                ]
            }
        }
    }
    PLAN_SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;entityType&#34;: 158,
                    &#34;_type_&#34;: 150,
                    &#34;entityId&#34;: 0
                }
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        {

                        }
                    ],
                    &#34;properties&#34;: {
                        &#34;role&#34;: {
                            &#34;_type_&#34;: 120,
                            &#34;roleId&#34;: 0,
                            &#34;roleName&#34;: &#34;&#34;
                        }
                    }
                }
            ]
        }
    }

    CREATE_V4_DC_PLAN_REQ = {
        &#34;application&#34;: 2,
        &#34;contentAnalyzer&#34;: [],
        &#34;contentIndexing&#34;: {
            &#34;extractTextFromImage&#34;: False,
            &#34;fileFilters&#34;: {
                &#34;excludePaths&#34;: [
                ],
                &#34;includeDocTypes&#34;: &#34;&#34;,
                &#34;maxDocSize&#34;: 50,
                &#34;minDocSize&#34;: 0
            },
            &#34;searchType&#34;: &#34;METADATA&#34;
        },
        &#34;entityDetection&#34;: {
            &#34;classifiers&#34;: [
            ],
            &#34;entities&#34;: [
            ]
        },
        &#34;indexServer&#34;: {},
        &#34;name&#34;: &#34;&#34;,
        &#34;threatAnalysis&#34;: False
    }

    class RAPlanSearchType(Enum):
        &#34;&#34;&#34;Class to maintain search types in Risk Analysis Plan&#34;&#34;&#34;
        SEARCH_TYPE_ONLY_METADATA = &#34;METADATA&#34;
        SEARCH_TYPE_METADATA_AND_CONTENT = &#34;METADATA_CONTENT&#34;

    class RAPlanAppType(Enum):
        &#34;&#34;&#34;Class to maintain plan application type&#34;&#34;&#34;
        CLASSIFIED = 2
        UNIFIED = 6


class TargetApps(Enum):
    &#34;&#34;&#34;Class to maintain supported apps types in Activate&#34;&#34;&#34;
    SDG = 2
    FSO = 1
    CASE_MGR = 4
    FS = 8
    RA = 128


class TrainingStatus(Enum):
    &#34;&#34;&#34;Class to maintain training status for classifier&#34;&#34;&#34;
    NOT_APPLICABLE = 0
    CREATED = 1
    RUNNING = 2
    FAILED = 3
    COMPLETED = 4
    CANCELLED = 5
    NOT_USABLE = 6


class ClassifierConstants:
    &#34;&#34;&#34;Class to maintain all the Classsifier related constants&#34;&#34;&#34;
    CREATE_REQUEST_JSON = {
        &#34;description&#34;: &#34;&#34;,
        &#34;enabled&#34;: True,
        &#34;entityName&#34;: &#34;&#34;,
        &#34;entityType&#34;: 4,
        &#34;entityKey&#34;: &#34;&#34;,
        &#34;entityXML&#34;: {
            &#34;classifierDetails&#34;: {
                &#34;datasetStorageType&#34;: 1,
                &#34;trainingStatus&#34;: 6,
                &#34;trainDatasetURI&#34;: &#34;http://localhost:22000/solr&#34;,
                &#34;datasetType&#34;: &#34;docs&#34;,
                &#34;CAUsedInTraining&#34;: {
                    &#34;caUrl&#34;: &#34;&#34;,
                    &#34;clientId&#34;: 0,
                    &#34;cloudName&#34;: &#34;&#34;,
                    &#34;cloudId&#34;: 0
                }
            }
        }
    }


class ActivateEntityConstants:
    &#34;&#34;&#34;Class to maintain all the Activate entity related constants&#34;&#34;&#34;

    REQUEST_JSON = {
        &#34;regularExpression&#34;: &#34;&#34;,
        &#34;flags&#34;: 0,
        &#34;enabled&#34;: True,
        &#34;entityName&#34;: &#34;&#34;,
        &#34;entityXML&#34;: {
            &#34;keywords&#34;: &#34;&#34;,
            &#34;isSystemDefinedEntity&#34;: False
        }
    }


class TagConstants:
    &#34;&#34;&#34;class to maintain all the Tags related constants&#34;&#34;&#34;

    TAG_SET_ADD_REQUEST_JSON = {
        &#34;entityType&#34;: 9504,
        &#34;operationType&#34;: 1,
        &#34;fromSite&#34;: 4,
        &#34;container&#34;: {
            &#34;containerType&#34;: 9504,
            &#34;containerName&#34;: &#34;&#34;,
            &#34;comment&#34;: &#34;&#34;
        }
    }

    TAG_SET_MODIFY_REQUEST_JSON = copy.deepcopy(TAG_SET_ADD_REQUEST_JSON)
    TAG_SET_MODIFY_REQUEST_JSON[&#39;operationType&#39;] = 3

    TAG_SET_DELETE_REQUEST_JSON = {
        &#34;entityType&#34;: 9504,
        &#34;containers&#34;: [
            {
                &#34;containerType&#34;: 9504,
                &#34;containerId&#34;: 0
            }
        ]
    }

    TAG_ADD_REQUEST_JSON = {
        &#34;container&#34;: {
            &#34;containerId&#34;: 0
        },
        &#34;tags&#34;: [
            {
                &#34;name&#34;: &#34;&#34;
            }
        ]
    }

    TAG_MODIFY_REQUEST_JSON = copy.deepcopy(TAG_ADD_REQUEST_JSON)
    TAG_MODIFY_REQUEST_JSON[&#39;tags&#39;][0][&#39;tagId&#39;] = 0

    VIEW_PERMISSION = {
        &#34;permissionId&#34;: 31,
        &#34;_type_&#34;: 122,
        &#34;permissionName&#34;: &#34;View&#34;
    }
    ADD_PERMISSION = {
        &#34;permissionId&#34;: 34,
        &#34;_type_&#34;: 122,
        &#34;permissionName&#34;: &#34;Add/Append&#34;
    }

    TAG_SET_SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;tagId&#34;: 0,
                    &#34;_type_&#34;: 9504
                }
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        {

                        }
                    ],
                    &#34;properties&#34;: {
                        &#34;permissions&#34;: [
                            VIEW_PERMISSION
                        ]
                    }
                }

            ]
        }
    }


class ComplianceConstants:
    &#34;&#34;&#34;Class to maintain all the Compliance search related constants&#34;&#34;&#34;

    SOLR_FIELD_FILE_NAME = &#34;fileName&#34;
    SOLR_FIELD_SIZE = &#34;sizeKB&#34;

    VIEW_PERMISSION = {
        &#34;permissionId&#34;: 31,
        &#34;_type_&#34;: 122,
        &#34;permissionName&#34;: &#34;View&#34;
    }

    ADD_PERMISSION = {
        &#34;permissionId&#34;: 34,
        &#34;_type_&#34;: 122,
        &#34;permissionName&#34;: &#34;Add/Append&#34;
    }

    DOWNLOAD_PERMISSION = {
        &#34;_type_&#34;: 122,
        &#34;permissionId&#34;: 36,
        &#34;permissionName&#34;: &#34;Download&#34;
    }

    DELETE_PERMISSION = {
        &#34;permissionId&#34;: 35,
        &#34;permissionName&#34;: &#34;Delete&#34;,
        &#34;_type_&#34;: 122
    }

    PERMISSION_ADD_NAME = &#34;Add&#34;
    PERMISSION_DELETE_NAME = &#34;Delete&#34;
    PERMISSION_DOWNLOAD_NAME = &#34;Download&#34;
    PERMISSION_VIEW_NAME = &#34;View&#34;

    PERMISSIONS = {
        PERMISSION_ADD_NAME: ADD_PERMISSION,
        PERMISSION_DELETE_NAME: DELETE_PERMISSION,
        PERMISSION_DOWNLOAD_NAME: DOWNLOAD_PERMISSION,
        PERMISSION_VIEW_NAME: VIEW_PERMISSION
    }

    EXPORT_SET_SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;entityType&#34;: 9503,
                    &#34;downloadSetId&#34;: None,
                    &#34;_type_&#34;: 9503}
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 2,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: None,
                    &#34;properties&#34;: {
                        &#34;permissions&#34;: [
                            VIEW_PERMISSION,
                            DOWNLOAD_PERMISSION
                        ]
                    }
                }
            ]
        }
    }

    class AppTypes(Enum):
        EXCHANGE = &#34;EXCHANGE&#34;
        EXCHANGE_JOURNAL = &#34;EXCHANGE_JOURNAL&#34;
        SHAREPOINT = &#34;SHAREPOINT&#34;
        ONEDRIVE = &#34;ONEDRIVE&#34;
        TEAMS = &#34;TEAMS&#34;
        FILE_SYSTEM = &#34;FILE_SYSTEM&#34;

    class ExportTypes(Enum):
        CAB = &#34;CAB&#34;
        PST = &#34;PST&#34;

    RESTORE_TYPE = {
        ExportTypes.PST: 1,
        ExportTypes.CAB: 2
    }

    FILE_TYPE = &#34;File&#34;
    EMAIL_TYPE = &#34;Email&#34;
    FILE_TYPES = [AppTypes.FILE_SYSTEM, AppTypes.SHAREPOINT, AppTypes.ONEDRIVE, AppTypes.TEAMS]
    EMAIL_TYPES = [AppTypes.EXCHANGE, AppTypes.EXCHANGE_JOURNAL]

    FILE_FILTERS_KEY = &#34;fileFilter&#34;
    FILE_FILTERS = [
        {
            &#34;filter&#34;: {
                &#34;interFilterOP&#34;: &#34;FTAnd&#34;,
                &#34;filters&#34;: [
                    {
                        &#34;field&#34;: &#34;CISTATE&#34;,
                        &#34;intraFieldOp&#34;: &#34;FTOr&#34;,
                        &#34;fieldValues&#34;: {
                            &#34;values&#34;: [
                                &#34;0&#34;,
                                &#34;1&#34;,
                                &#34;12&#34;,
                                &#34;13&#34;,
                                &#34;14&#34;,
                                &#34;15&#34;,
                                &#34;1014&#34;,
                                &#34;3333&#34;,
                                &#34;3334&#34;,
                                &#34;3335&#34;
                            ]
                        }
                    }
                ]
            }
        }
    ]

    EMAIL_FILTERS_KEY = &#34;emailView&#34;

    ONEDRIVE_FACET = &#34;200118&#34;
    TEAMS_FACET = &#34;200128&#34;
    SHAREPOINT_FACET = &#34;78&#34;
    CUSTOM_FACETS = {
        AppTypes.ONEDRIVE: ONEDRIVE_FACET,
        AppTypes.TEAMS: TEAMS_FACET,
        AppTypes.SHAREPOINT: SHAREPOINT_FACET
    }

    CUSTOM_FACETS_NAME = {
        AppTypes.TEAMS: &#34;TEAMS_NAME&#34;,
        AppTypes.SHAREPOINT: &#34;CUSTODIAN&#34;,
        AppTypes.ONEDRIVE: &#34;CUSTODIAN&#34;
    }

    FACET_KEY = &#34;facetRequest&#34;
    FILE_FACET = [
        {
            &#34;count&#34;: 4,
            &#34;name&#34;: &#34;CUSTODIAN&#34;
        },
        {
            &#34;count&#34;: 4,
            &#34;name&#34;: &#34;APPTYPE&#34;,
            &#34;searchFieldName&#34;: &#34;APPTYPE&#34;,
            &#34;stringParameter&#34;: [
                {
                    &#34;name&#34;: &#34;33&#34;,
                    &#34;custom&#34;: True
                },
                {
                    &#34;name&#34;: &#34;29&#34;,
                    &#34;custom&#34;: True
                },
                {
                    &#34;name&#34;: &#34;63&#34;,
                    &#34;custom&#34;: True
                },
                {
                    &#34;name&#34;: &#34;21&#34;,
                    &#34;custom&#34;: True
                }
            ]
        }
    ]
    CUSTOM_FACET = [
        {
            &#34;count&#34;: 4,
            &#34;name&#34;: &#34;CUSTODIAN&#34;
        },
        {
            &#34;count&#34;: 4,
            &#34;name&#34;: &#34;APPTYPE&#34;,
            &#34;searchFieldName&#34;: &#34;APPTYPE&#34;,
            &#34;stringParameter&#34;: [
                {
                    &#34;name&#34;: None,
                    &#34;custom&#34;: True
                }
            ]
        }
    ]

    RESPONSE_FIELD_LIST = (&#34;DATA_TYPE,CLIENTNAME,CONTENTID,CV_OBJECT_GUID,PARENT_GUID,CV_TURBO_GUID,&#34;
                           &#34;AFILEID,AFILEOFFSET,COMMCELLNO,MODIFIEDTIME,SIZEINKB,BACKUPTIME,CISTATE,DATE_DELETED,&#34;
                           &#34;TEAMS_ITEM_ID,TEAMS_ITEM_NAME,TEAMS_NAME,TEAMS_SMTP,TEAMS_ITEM_TYPE,TEAMS_CHANNEL_TYPE,&#34;
                           &#34;TEAMS_TAB_TYPE,TEAMS_GROUP_VISIBILITY,TEAMS_GUID,TEAMS_CONV_ITEM_TYPE,&#34;
                           &#34;TEAMS_CONV_MESSAGE_TYPE,TEAMS_CONV_SUBJECT,TEAMS_CONV_IMPORTANCE,TEAMS_CONV_SENDER_TYPE,&#34;
                           &#34;TEAMS_CONV_SENDER_NAME,TEAMS_CONV_HAS_REPLIES,CI_URL,TEAMS_DRIVE_FOLDER_TYPE,APPTYPE,APPID&#34;)

    COMPLIANCE_SEARCH_JSON = {
        &#34;mode&#34;: 2,
        &#34;facetRequests&#34;: {},
        &#34;advSearchGrp&#34;: {
            &#34;commonFilter&#34;: [
                {
                    &#34;filter&#34;: {
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;CI_STATUS&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        &#34;1&#34;,
                                        &#34;3&#34;
                                    ]
                                }
                            }
                        ]
                    }
                }
            ],
            &#34;cvSearchKeyword&#34;: {
                &#34;isExactWordsOptionSelected&#34;: False,
                &#34;keyword&#34;: None
            },
            &#34;galaxyFilter&#34;: [
                {
                    &#34;applicationType&#34;: None
                }
            ]
        },
        &#34;userInformation&#34;: {
            &#34;userGuid&#34;: None
        },
        &#34;listOfCIServer&#34;: [
            {
                &#34;cloudID&#34;: None
            }
        ],
        &#34;searchProcessingInfo&#34;: {
            &#34;resultOffset&#34;: 0,
            &#34;pageSize&#34;: 50,
            &#34;queryParams&#34;: [
                {
                    &#34;param&#34;: &#34;ENABLE_NEW_COMPLIANCE_SEARCH&#34;,
                    &#34;value&#34;: &#34;true&#34;
                }
            ]
        }
    }</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activateapps.constants.ActivateEntityConstants"><code class="flex name class">
<span>class <span class="ident">ActivateEntityConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain all the Activate entity related constants</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L965-L977" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ActivateEntityConstants:
    &#34;&#34;&#34;Class to maintain all the Activate entity related constants&#34;&#34;&#34;

    REQUEST_JSON = {
        &#34;regularExpression&#34;: &#34;&#34;,
        &#34;flags&#34;: 0,
        &#34;enabled&#34;: True,
        &#34;entityName&#34;: &#34;&#34;,
        &#34;entityXML&#34;: {
            &#34;keywords&#34;: &#34;&#34;,
            &#34;isSystemDefinedEntity&#34;: False
        }
    }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.ActivateEntityConstants.REQUEST_JSON"><code class="name">var <span class="ident">REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.constants.ClassifierConstants"><code class="flex name class">
<span>class <span class="ident">ClassifierConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain all the Classsifier related constants</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L940-L962" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ClassifierConstants:
    &#34;&#34;&#34;Class to maintain all the Classsifier related constants&#34;&#34;&#34;
    CREATE_REQUEST_JSON = {
        &#34;description&#34;: &#34;&#34;,
        &#34;enabled&#34;: True,
        &#34;entityName&#34;: &#34;&#34;,
        &#34;entityType&#34;: 4,
        &#34;entityKey&#34;: &#34;&#34;,
        &#34;entityXML&#34;: {
            &#34;classifierDetails&#34;: {
                &#34;datasetStorageType&#34;: 1,
                &#34;trainingStatus&#34;: 6,
                &#34;trainDatasetURI&#34;: &#34;http://localhost:22000/solr&#34;,
                &#34;datasetType&#34;: &#34;docs&#34;,
                &#34;CAUsedInTraining&#34;: {
                    &#34;caUrl&#34;: &#34;&#34;,
                    &#34;clientId&#34;: 0,
                    &#34;cloudName&#34;: &#34;&#34;,
                    &#34;cloudId&#34;: 0
                }
            }
        }
    }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.ClassifierConstants.CREATE_REQUEST_JSON"><code class="name">var <span class="ident">CREATE_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants"><code class="flex name class">
<span>class <span class="ident">ComplianceConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain all the Compliance search related constants</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L1062-L1302" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ComplianceConstants:
    &#34;&#34;&#34;Class to maintain all the Compliance search related constants&#34;&#34;&#34;

    SOLR_FIELD_FILE_NAME = &#34;fileName&#34;
    SOLR_FIELD_SIZE = &#34;sizeKB&#34;

    VIEW_PERMISSION = {
        &#34;permissionId&#34;: 31,
        &#34;_type_&#34;: 122,
        &#34;permissionName&#34;: &#34;View&#34;
    }

    ADD_PERMISSION = {
        &#34;permissionId&#34;: 34,
        &#34;_type_&#34;: 122,
        &#34;permissionName&#34;: &#34;Add/Append&#34;
    }

    DOWNLOAD_PERMISSION = {
        &#34;_type_&#34;: 122,
        &#34;permissionId&#34;: 36,
        &#34;permissionName&#34;: &#34;Download&#34;
    }

    DELETE_PERMISSION = {
        &#34;permissionId&#34;: 35,
        &#34;permissionName&#34;: &#34;Delete&#34;,
        &#34;_type_&#34;: 122
    }

    PERMISSION_ADD_NAME = &#34;Add&#34;
    PERMISSION_DELETE_NAME = &#34;Delete&#34;
    PERMISSION_DOWNLOAD_NAME = &#34;Download&#34;
    PERMISSION_VIEW_NAME = &#34;View&#34;

    PERMISSIONS = {
        PERMISSION_ADD_NAME: ADD_PERMISSION,
        PERMISSION_DELETE_NAME: DELETE_PERMISSION,
        PERMISSION_DOWNLOAD_NAME: DOWNLOAD_PERMISSION,
        PERMISSION_VIEW_NAME: VIEW_PERMISSION
    }

    EXPORT_SET_SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;entityType&#34;: 9503,
                    &#34;downloadSetId&#34;: None,
                    &#34;_type_&#34;: 9503}
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 2,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: None,
                    &#34;properties&#34;: {
                        &#34;permissions&#34;: [
                            VIEW_PERMISSION,
                            DOWNLOAD_PERMISSION
                        ]
                    }
                }
            ]
        }
    }

    class AppTypes(Enum):
        EXCHANGE = &#34;EXCHANGE&#34;
        EXCHANGE_JOURNAL = &#34;EXCHANGE_JOURNAL&#34;
        SHAREPOINT = &#34;SHAREPOINT&#34;
        ONEDRIVE = &#34;ONEDRIVE&#34;
        TEAMS = &#34;TEAMS&#34;
        FILE_SYSTEM = &#34;FILE_SYSTEM&#34;

    class ExportTypes(Enum):
        CAB = &#34;CAB&#34;
        PST = &#34;PST&#34;

    RESTORE_TYPE = {
        ExportTypes.PST: 1,
        ExportTypes.CAB: 2
    }

    FILE_TYPE = &#34;File&#34;
    EMAIL_TYPE = &#34;Email&#34;
    FILE_TYPES = [AppTypes.FILE_SYSTEM, AppTypes.SHAREPOINT, AppTypes.ONEDRIVE, AppTypes.TEAMS]
    EMAIL_TYPES = [AppTypes.EXCHANGE, AppTypes.EXCHANGE_JOURNAL]

    FILE_FILTERS_KEY = &#34;fileFilter&#34;
    FILE_FILTERS = [
        {
            &#34;filter&#34;: {
                &#34;interFilterOP&#34;: &#34;FTAnd&#34;,
                &#34;filters&#34;: [
                    {
                        &#34;field&#34;: &#34;CISTATE&#34;,
                        &#34;intraFieldOp&#34;: &#34;FTOr&#34;,
                        &#34;fieldValues&#34;: {
                            &#34;values&#34;: [
                                &#34;0&#34;,
                                &#34;1&#34;,
                                &#34;12&#34;,
                                &#34;13&#34;,
                                &#34;14&#34;,
                                &#34;15&#34;,
                                &#34;1014&#34;,
                                &#34;3333&#34;,
                                &#34;3334&#34;,
                                &#34;3335&#34;
                            ]
                        }
                    }
                ]
            }
        }
    ]

    EMAIL_FILTERS_KEY = &#34;emailView&#34;

    ONEDRIVE_FACET = &#34;200118&#34;
    TEAMS_FACET = &#34;200128&#34;
    SHAREPOINT_FACET = &#34;78&#34;
    CUSTOM_FACETS = {
        AppTypes.ONEDRIVE: ONEDRIVE_FACET,
        AppTypes.TEAMS: TEAMS_FACET,
        AppTypes.SHAREPOINT: SHAREPOINT_FACET
    }

    CUSTOM_FACETS_NAME = {
        AppTypes.TEAMS: &#34;TEAMS_NAME&#34;,
        AppTypes.SHAREPOINT: &#34;CUSTODIAN&#34;,
        AppTypes.ONEDRIVE: &#34;CUSTODIAN&#34;
    }

    FACET_KEY = &#34;facetRequest&#34;
    FILE_FACET = [
        {
            &#34;count&#34;: 4,
            &#34;name&#34;: &#34;CUSTODIAN&#34;
        },
        {
            &#34;count&#34;: 4,
            &#34;name&#34;: &#34;APPTYPE&#34;,
            &#34;searchFieldName&#34;: &#34;APPTYPE&#34;,
            &#34;stringParameter&#34;: [
                {
                    &#34;name&#34;: &#34;33&#34;,
                    &#34;custom&#34;: True
                },
                {
                    &#34;name&#34;: &#34;29&#34;,
                    &#34;custom&#34;: True
                },
                {
                    &#34;name&#34;: &#34;63&#34;,
                    &#34;custom&#34;: True
                },
                {
                    &#34;name&#34;: &#34;21&#34;,
                    &#34;custom&#34;: True
                }
            ]
        }
    ]
    CUSTOM_FACET = [
        {
            &#34;count&#34;: 4,
            &#34;name&#34;: &#34;CUSTODIAN&#34;
        },
        {
            &#34;count&#34;: 4,
            &#34;name&#34;: &#34;APPTYPE&#34;,
            &#34;searchFieldName&#34;: &#34;APPTYPE&#34;,
            &#34;stringParameter&#34;: [
                {
                    &#34;name&#34;: None,
                    &#34;custom&#34;: True
                }
            ]
        }
    ]

    RESPONSE_FIELD_LIST = (&#34;DATA_TYPE,CLIENTNAME,CONTENTID,CV_OBJECT_GUID,PARENT_GUID,CV_TURBO_GUID,&#34;
                           &#34;AFILEID,AFILEOFFSET,COMMCELLNO,MODIFIEDTIME,SIZEINKB,BACKUPTIME,CISTATE,DATE_DELETED,&#34;
                           &#34;TEAMS_ITEM_ID,TEAMS_ITEM_NAME,TEAMS_NAME,TEAMS_SMTP,TEAMS_ITEM_TYPE,TEAMS_CHANNEL_TYPE,&#34;
                           &#34;TEAMS_TAB_TYPE,TEAMS_GROUP_VISIBILITY,TEAMS_GUID,TEAMS_CONV_ITEM_TYPE,&#34;
                           &#34;TEAMS_CONV_MESSAGE_TYPE,TEAMS_CONV_SUBJECT,TEAMS_CONV_IMPORTANCE,TEAMS_CONV_SENDER_TYPE,&#34;
                           &#34;TEAMS_CONV_SENDER_NAME,TEAMS_CONV_HAS_REPLIES,CI_URL,TEAMS_DRIVE_FOLDER_TYPE,APPTYPE,APPID&#34;)

    COMPLIANCE_SEARCH_JSON = {
        &#34;mode&#34;: 2,
        &#34;facetRequests&#34;: {},
        &#34;advSearchGrp&#34;: {
            &#34;commonFilter&#34;: [
                {
                    &#34;filter&#34;: {
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;CI_STATUS&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        &#34;1&#34;,
                                        &#34;3&#34;
                                    ]
                                }
                            }
                        ]
                    }
                }
            ],
            &#34;cvSearchKeyword&#34;: {
                &#34;isExactWordsOptionSelected&#34;: False,
                &#34;keyword&#34;: None
            },
            &#34;galaxyFilter&#34;: [
                {
                    &#34;applicationType&#34;: None
                }
            ]
        },
        &#34;userInformation&#34;: {
            &#34;userGuid&#34;: None
        },
        &#34;listOfCIServer&#34;: [
            {
                &#34;cloudID&#34;: None
            }
        ],
        &#34;searchProcessingInfo&#34;: {
            &#34;resultOffset&#34;: 0,
            &#34;pageSize&#34;: 50,
            &#34;queryParams&#34;: [
                {
                    &#34;param&#34;: &#34;ENABLE_NEW_COMPLIANCE_SEARCH&#34;,
                    &#34;value&#34;: &#34;true&#34;
                }
            ]
        }
    }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.ADD_PERMISSION"><code class="name">var <span class="ident">ADD_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.AppTypes"><code class="name">var <span class="ident">AppTypes</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.COMPLIANCE_SEARCH_JSON"><code class="name">var <span class="ident">COMPLIANCE_SEARCH_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.CUSTOM_FACET"><code class="name">var <span class="ident">CUSTOM_FACET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.CUSTOM_FACETS"><code class="name">var <span class="ident">CUSTOM_FACETS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.CUSTOM_FACETS_NAME"><code class="name">var <span class="ident">CUSTOM_FACETS_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.DELETE_PERMISSION"><code class="name">var <span class="ident">DELETE_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.DOWNLOAD_PERMISSION"><code class="name">var <span class="ident">DOWNLOAD_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.EMAIL_FILTERS_KEY"><code class="name">var <span class="ident">EMAIL_FILTERS_KEY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.EMAIL_TYPE"><code class="name">var <span class="ident">EMAIL_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.EMAIL_TYPES"><code class="name">var <span class="ident">EMAIL_TYPES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.EXPORT_SET_SHARE_REQUEST_JSON"><code class="name">var <span class="ident">EXPORT_SET_SHARE_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.ExportTypes"><code class="name">var <span class="ident">ExportTypes</span></code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.FACET_KEY"><code class="name">var <span class="ident">FACET_KEY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.FILE_FACET"><code class="name">var <span class="ident">FILE_FACET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.FILE_FILTERS"><code class="name">var <span class="ident">FILE_FILTERS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.FILE_FILTERS_KEY"><code class="name">var <span class="ident">FILE_FILTERS_KEY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.FILE_TYPE"><code class="name">var <span class="ident">FILE_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.FILE_TYPES"><code class="name">var <span class="ident">FILE_TYPES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.ONEDRIVE_FACET"><code class="name">var <span class="ident">ONEDRIVE_FACET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSIONS"><code class="name">var <span class="ident">PERMISSIONS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_ADD_NAME"><code class="name">var <span class="ident">PERMISSION_ADD_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_DELETE_NAME"><code class="name">var <span class="ident">PERMISSION_DELETE_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_DOWNLOAD_NAME"><code class="name">var <span class="ident">PERMISSION_DOWNLOAD_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_VIEW_NAME"><code class="name">var <span class="ident">PERMISSION_VIEW_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.RESPONSE_FIELD_LIST"><code class="name">var <span class="ident">RESPONSE_FIELD_LIST</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.RESTORE_TYPE"><code class="name">var <span class="ident">RESTORE_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.SHAREPOINT_FACET"><code class="name">var <span class="ident">SHAREPOINT_FACET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.SOLR_FIELD_FILE_NAME"><code class="name">var <span class="ident">SOLR_FIELD_FILE_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.SOLR_FIELD_SIZE"><code class="name">var <span class="ident">SOLR_FIELD_SIZE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.TEAMS_FACET"><code class="name">var <span class="ident">TEAMS_FACET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.ComplianceConstants.VIEW_PERMISSION"><code class="name">var <span class="ident">VIEW_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants"><code class="flex name class">
<span>class <span class="ident">EdiscoveryConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>class to maintain constants for ediscovery clients</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L120-L577" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class EdiscoveryConstants:
    &#34;&#34;&#34;class to maintain constants for ediscovery clients&#34;&#34;&#34;

    class CrawlType(Enum):
        &#34;&#34;&#34;Crawl type for SDG/FSO jobs&#34;&#34;&#34;
        LIVE = 1
        BACKUP = 2
        CONTENT_INDEXED = 3
        FILE_LEVEL_ANALYTICS = 4
        BACKUP_V2 = 5

    class SourceType(Enum):
        &#34;&#34;&#34;Source type of data for FSO/SDG app&#34;&#34;&#34;
        SOURCE = 1
        BACKUP = 2

    class ReviewActions(Enum):
        &#34;&#34;&#34;Review actions for documents on SDG/FSO app&#34;&#34;&#34;
        DELETE = 1
        MOVE = 4
        RETENTION = 10
        IGNORE = 11
        ARCHIVE = 90
        TAG = 98

    class RiskTypes(Enum):
        &#34;&#34;&#34;Different risk types for documents on SDG/FSO app&#34;&#34;&#34;
        OPEN_ACCESS = 1
        OLD_FILES = 2
        FILE_MOVED = 3
        MULTI_USER_ACCESS = 4
        NO_RETENTION = 5
        IS_PROTECTED = 6

    class ClientType(Enum):
        &#34;&#34;&#34;Different Type of SDG Datasource&#34;&#34;&#34;
        FILE_SYSTEM = 5
        EXCHANGE = 17
        ONEDRIVE = 35

    EXCHANGE_AGENT = &#34;exchange mailbox&#34;
    EXCHANGE_INSTANCE = &#34;defaultinstancename&#34;
    EXCHANGE_BACKUPSET = &#34;user mailbox&#34;
    EXCHANGE_SUBCLIENT = &#34;usermailbox&#34;

    ONEDRIVE_AGENT = &#39;Cloud Apps&#39;
    ONEDRIVE_INSTANCE = &#39;OneDrive&#39;
    ONEDRIVE_BACKUPSET = &#39;defaultbackupset&#39;
    ONEDRIVE_SUBCLIENT = &#39;default&#39;

    FSO_SERVERS = &#34;FsoServers&#34;
    FSO_SERVER_GROUPS = &#34;FsoServerGroups&#34;

    SERVER_LEVEL_SCHEDULE_JSON = {
        &#34;taskInfo&#34;: {
            &#34;task&#34;: {
                &#34;taskType&#34;: 4,
                &#34;taskFlags&#34;: {
                    &#34;isEdiscovery&#34;: True
                },
                &#34;taskName&#34;: &#34;&#34;
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTask&#34;: {
                        &#34;operationType&#34;: 5025,
                        &#34;subTaskType&#34;: 1,
                        &#34;subTaskName&#34;: &#34;&#34;
                    },
                    &#34;pattern&#34;: {},
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {
                            &#34;backupLevel&#34;: 2,
                            &#34;mediaOpt&#34;: {
                                &#34;auxcopyJobOption&#34;: {
                                    &#34;allCopies&#34;: True,
                                    &#34;useMaximumStreams&#34;: True,
                                    &#34;maxNumberOfStreams&#34;: 1
                                }
                            }
                        },
                        &#34;adminOpts&#34;: {
                            &#34;contentIndexingOption&#34;: {
                                &#34;idaType&#34;: 1,
                                &#34;operationType&#34;: 2,
                                &#34;fileAnalytics&#34;: False
                            }
                        }
                    }
                }
            ],
            &#34;associations&#34;: [
                {
                    &#34;clientId&#34;: 0,
                    &#34;_type_&#34;: 3
                }
            ]
        }
    }

    REVIEW_ACTION_FSO_SUPPORTED = [ReviewActions.DELETE.value, ReviewActions.MOVE.value, ReviewActions.ARCHIVE.value]

    REVIEW_ACTION_SDG_SUPPORTED = [
        ReviewActions.DELETE.value,
        ReviewActions.MOVE.value,
        ReviewActions.ARCHIVE.value,
        ReviewActions.RETENTION.value,
        ReviewActions.IGNORE.value]

    CREATE_CLIENT_REQ_JSON = {
        &#34;clientInfo&#34;: {
            &#34;clientType&#34;: 19,
            &#34;edgeDrivePseudoClientProperties&#34;: {
                &#34;systemDriveType&#34;: 7,
                &#34;edgeDriveAssociations&#34;: {},
                &#34;eDiscoveryInfo&#34;: {
                    &#34;eDiscoverySubType&#34;: 2,
                    &#34;inventoryDataSource&#34;: {
                        &#34;seaDataSourceId&#34;: 0
                    }
                }
            },
            &#34;plan&#34;: {
                &#34;planId&#34;: 0
            }
        },
        &#34;entity&#34;: {
            &#34;clientName&#34;: &#34;&#34;,
            &#34;_type_&#34;: 3
        }
    }

    REVIEW_ACTION_DELETE_REQ_JSON = {
        &#34;operation&#34;: ReviewActions.DELETE.value,
        &#34;files&#34;: &#34;&#34;,
        &#34;deleteFromBackup&#34;: False,
        &#34;options&#34;: &#34;&#34;}

    REVIEW_ACTION_MOVE_REQ_JSON = {&#34;operation&#34;: ReviewActions.MOVE.value, &#34;files&#34;: &#34;&#34;, &#34;options&#34;: &#34;&#34;}

    REVIEW_ACTION_SET_RETENTION_REQ_JSON = {
        &#34;operation&#34;: ReviewActions.RETENTION.value,
        &#34;remActionRequest&#34;: {
            &#34;dataSourceId&#34;: 0,
            &#34;handlerName&#34;: &#34;default&#34;,
            &#34;isBulkOperation&#34;: False,
            &#34;searchRequest&#34;: &#34;&#34;},
        &#34;setRetentionReq&#34;: {&#34;numOfMonthsRemain&#34;: 0},
    }

    REVIEW_ACTION_IGNORE_FILES_REQ_JSON = {
        &#34;operation&#34;: ReviewActions.IGNORE.value,
        &#34;remActionRequest&#34;: {
            &#34;dataSourceId&#34;: 0,
            &#34;handlerName&#34;: &#34;default&#34;,
            &#34;isBulkOperation&#34;: False,
            &#34;searchRequest&#34;: &#34;&#34;},
        &#34;ignoreRisksReq&#34;: {&#34;ignoreAllRisks&#34;: False},
    }

    REVIEW_ACTION_TAG_REQ_JSON = {
        &#34;operation&#34;: ReviewActions.TAG.value,
        &#34;remActionRequest&#34;: {
            &#34;dataSourceId&#34;: 0,
            &#34;isBulkOperation&#34;: False},
        &#34;taggingRequest&#34;: {}}

    REVIEW_ACTION_BULK_SEARCH_REQ = &#34;{\&#34;searchParams\&#34;:&#34; \
                                    &#34;[{\&#34;key\&#34;:\&#34;q\&#34;,\&#34;value\&#34;:\&#34;*:*\&#34;},{\&#34;key\&#34;:\&#34;wt\&#34;,\&#34;value\&#34;:\&#34;json\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;rows\&#34;,\&#34;value\&#34;:\&#34;0\&#34;},{\&#34;key\&#34;:\&#34;defType\&#34;,\&#34;value\&#34;:\&#34;edismax\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fq\&#34;,\&#34;value\&#34;:\&#34;IsFile:\\\&#34;1\\\&#34;\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;FileName\&#34;},{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;OwnerName\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;OwnerLocation\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CountryCode\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;FileExtension\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;operatingSystem\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;IsProtected\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;FileName_path\&#34;},{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Url\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ClientId\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;DocumentType\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;contentid\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowListUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowModifyUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowWriteUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowExecuteUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AllowFullControlUsername\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ExpiryDate\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CreatedTime\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;data_source\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;data_source_name\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;data_source_type\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;entities_extracted\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ConsentFor_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;RedactFor_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;RedactMode_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CommentFor_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AppType\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ApplicationId\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CVTurboGUID\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;CommcellNumber\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;AchiveFileId\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ArchiveFileOffset\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Size\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Risk_*\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;LastAccessedBy\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;LastModifiedBy\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;ModifiedTimeAsStr\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;entity_doc_tags\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;TagIds\&#34;},{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;FileName\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Url\&#34;},{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;Size\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;OwnerName\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;fl\&#34;,\&#34;value\&#34;:\&#34;entity_doc_tags\&#34;},&#34; \
                                    &#34;{\&#34;key\&#34;:\&#34;start\&#34;,\&#34;value\&#34;:\&#34;0\&#34;}]}&#34;

    REVIEW_ACTION_SEARCH_FL_SET = {
        &#34;FileName&#34;,
        &#34;OwnerName&#34;,
        &#34;OwnerLocation&#34;,
        &#34;CountryCode&#34;,
        &#34;FileExtension&#34;,
        &#34;operatingSystem&#34;,
        &#34;IsProtected&#34;,
        &#34;FileName_path&#34;,
        &#34;Url&#34;,
        &#34;ClientId&#34;,
        &#34;DocumentType&#34;,
        &#34;contentid&#34;,
        &#34;AllowListUsername&#34;,
        &#34;AllowModifyUsername&#34;,
        &#34;AllowWriteUsername&#34;,
        &#34;AllowExecuteUsername&#34;,
        &#34;AllowFullControlUsername&#34;,
        &#34;ExpiryDate&#34;,
        &#34;CreatedTime&#34;,
        &#34;data_source&#34;,
        &#34;data_source_name&#34;,
        &#34;data_source_type&#34;,
        &#34;entities_extracted&#34;,
        &#34;ConsentFor_*&#34;,
        &#34;RedactFor_*&#34;,
        &#34;RedactMode_*&#34;,
        &#34;CommentFor_*&#34;,
        &#34;AppType&#34;,
        &#34;ApplicationId&#34;,
        &#34;CVTurboGUID&#34;,
        &#34;CommcellNumber&#34;,
        &#34;AchiveFileId&#34;,
        &#34;ArchiveFileOffset&#34;,
        &#34;Size&#34;,
        &#34;Risk_*&#34;,
        &#34;LastAccessedBy&#34;,
        &#34;LastModifiedBy&#34;,
        &#34;ModifiedTimeAsStr&#34;,
        &#34;entity_doc_tags&#34;,
        &#34;TagIds&#34;}

    REVIEW_ACTION_IDA_SELECT_SET = {
        5: {&#39;contentid&#39;, &#39;Url&#39;, &#39;ClientId&#39;, &#39;CreatedTime&#39;, &#39;FileName&#39;}}

    FS_SERVER_HANDLER_NAME = &#39;GetFileServers&#39;
    ADD_FS_REQ_JSON = {
        &#34;datasourceId&#34;: 0,
        &#34;indexServerClientId&#34;: 0,
        &#34;followScheduleCrawl&#34;: False,
        &#34;datasources&#34;: [
            {
                &#34;datasourceName&#34;: &#34;&#34;,
                &#34;properties&#34;: [],
                &#34;datasourceType&#34;: 5,
                &#34;accessNodes&#34;: [
                    {
                        &#34;clientId&#34;: 0,
                        &#34;clientName&#34;: &#34;&#34;
                    }
                ]
            }
        ]
    }

    ADD_O365_SDG_BACKED_UP_DS_REQ = {
        &#34;clientId&#34;: 0,
        &#34;followScheduleCrawl&#34;: False,
        &#34;datasources&#34;: [
            {
                &#34;datasourceName&#34;: &#34;&#34;,
                &#34;datasourceType&#34;: 0,
                &#34;properties&#34;: []
            }
        ]
    }

    FS_DEFAULT_EXPORT_FIELDS = {&#39;FileName&#39;, &#39;Url&#39;, &#39;Size&#39;, &#39;OwnerName&#39;, &#39;CreatedTime&#39;, &#39;AccessTime&#39;, &#39;ModifiedTime&#39;}
    EXPORT_DOWNLOAD_REQ = {
        &#34;appTypeId&#34;: 200,
        &#34;responseFileName&#34;: &#34;&#34;,
        &#34;fileParams&#34;: [
            {
                &#34;name&#34;: &#34;&#34;,
                &#34;id&#34;: 2
            },
            {
                &#34;name&#34;: &#34;zip&#34;,
                &#34;id&#34;: 3
            },
            {
                &#34;name&#34;: &#34;Streamed&#34;,
                &#34;id&#34;: 10
            }
        ]
    }

    DATA_SOURCE_TYPES = {
        0: &#39;NONE&#39;,
        1: &#39;jdbc&#39;,
        5: &#39;file&#39;,
        9: &#39;ldap&#39;,
        10: &#39;federated&#39;,
        11: &#39;blank&#39;,
        15: &#39;fla&#39;,
        16: &#39;edge&#39;,
        17: &#39;exchange&#39;,
        18: &#39;reviewset&#39;,
        22: &#39;nfs&#39;,
        24: &#39;systemdefault&#39;,
        26: &#39;onedrive&#39;,
        27: &#39;sharepoint&#39;,
        28: &#39;email&#39;,
        29: &#39;dbanalysis&#39;,
        30: &#39;cloudpaas&#39;,
        31: &#39;googledrive&#39;,
        32: &#39;gmail&#39;,
        34: &#39;onedriveindex&#39;,
        35: &#39;multinodefederated&#39;,
        37: &#39;dynamic365&#39;
    }

    VIEW_CATEGORY_PERMISSION = {
        &#34;permissionId&#34;: 31,
        &#34;permissionName&#34;: &#34;View&#34;,
        &#34;_type_&#34;: 122
    }
    EDIT_CATEGORY_PERMISSION = {
        &#34;permissionId&#34;: 2,
        &#34;permissionName&#34;: &#34;Agent Management&#34;,
        &#34;_type_&#34;: 122
    }

    SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;_type_&#34;: 3,
                    &#34;clientId&#34;: 0
                }
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        {
                            &#34;userId&#34;: 0,
                            &#34;_type_&#34;: 0,
                            &#34;userName&#34;: &#34;&#34;
                        }
                    ],
                    &#34;properties&#34;: {
                        &#34;categoryPermission&#34;: {
                            &#34;categoriesPermissionOperationType&#34;: 1,
                            &#34;categoriesPermissionList&#34;: [
                                VIEW_CATEGORY_PERMISSION
                            ]
                        }
                    }
                }
            ]
        }
    }

    DS_FILE = &#39;file&#39;
    DS_CLOUD_STORAGE = &#39;cloudstorage&#39;
    FIELD_DATA_SOURCE_DISPLAY_NAME = &#39;datasourceDisplayName&#39;
    FIELD_DISPLAY_NAME = &#39;displayName&#39;
    FIELD_DATA_SOURCE_NAME = &#39;datasourceName&#39;
    FIELD_DATA_SOURCE_ID_NON_SEA = &#39;datasourceId&#39;
    FIELD_DOCUMENT_COUNT = &#39;documentCount&#39;
    FIELD_DATA_SOURCE_TYPE = &#39;datasourceType&#39;
    FIELD_DATA_SOURCE_ID = &#39;seaDataSourceId&#39;
    FIELD_PLAN_ID = &#39;planId&#39;
    FIELD_DC_PLAN_ID = &#39;dcplanid&#39;
    FIELD_PSEDUCO_CLIENT_ID = &#39;pseudoclientid&#39;
    FIELD_SUBCLIENT_ID = &#39;subclientid&#39;
    FIELD_CRAWL_TYPE = &#39;crawltype&#39;
    FIELD_DATA_SOURCE_NAME_SEA = &#39;seaDataSourceName&#39;
    FIELD_CONTENT_ID = &#39;contentid&#39;
    FIELD_IS_FILE = &#39;IsFile:1&#39;
    DYNAMIC_FEDERATED_SEARCH_PARAMS = {&#34;searchParams&#34;: []}

    CRITERIA_EXTRACTED_DOCS = &#34;entities_extracted:*&#34;

    TAGGING_ITEMS_REQUEST = {
        &#34;entityType&#34;: &#34;SEA_DATASOURCE_ENTITY&#34;,
        &#34;entityIds&#34;: [],
        &#34;handler&#34;: &#34;default&#34;,
        &#34;searchRequest&#34;: {},
        &#34;opType&#34;: &#34;ADD&#34;,
        &#34;tags&#34;: [],
        &#34;isAsync&#34;: True}
    TAGGING_ITEMS_REVIEW_REQUEST = {&#34;operation&#34;: ReviewActions.TAG.value, &#34;files&#34;: &#34;&#34;,
                                    &#34;options&#34;: &#34;&#34;,
                                    &#34;taggingInformation&#34;: {&#39;opType&#39;: &#39;ADD&#39;, &#34;tagIds&#34;: []}}

    START_CRAWL_SERVER_REQUEST_JSON = {
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [
                {
                    &#34;clientId&#34;: 0,
                    &#34;_type_&#34;: 3
                }
            ],
            &#34;task&#34;: {
                &#34;taskType&#34;: 1,
                &#34;taskName&#34;: &#34;&#34;,
                &#34;taskFlags&#34;: {
                    &#34;isEdiscovery&#34;: True
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTask&#34;: {
                        &#34;subTaskName&#34;: &#34;CVPYSDK_FSO_IMMEDIATE&#34;,
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 5025
                    },
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {
                            &#34;backupLevel&#34;: 2,
                            &#34;mediaOpt&#34;: {
                                &#34;auxcopyJobOption&#34;: {
                                    &#34;maxNumberOfStreams&#34;: 1,
                                    &#34;allCopies&#34;: True,
                                    &#34;useMaximumStreams&#34;: True
                                }
                            }
                        },
                        &#34;adminOpts&#34;: {
                            &#34;contentIndexingOption&#34;: {
                                &#34;fileAnalytics&#34;: False,
                                &#34;idaType&#34;: 1,
                                &#34;operationType&#34;: 2
                            }
                        }
                    }
                }
            ]
        }
    }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.ADD_FS_REQ_JSON"><code class="name">var <span class="ident">ADD_FS_REQ_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.ADD_O365_SDG_BACKED_UP_DS_REQ"><code class="name">var <span class="ident">ADD_O365_SDG_BACKED_UP_DS_REQ</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.CREATE_CLIENT_REQ_JSON"><code class="name">var <span class="ident">CREATE_CLIENT_REQ_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS"><code class="name">var <span class="ident">CRITERIA_EXTRACTED_DOCS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.ClientType"><code class="name">var <span class="ident">ClientType</span></code></dt>
<dd>
<div class="desc"><p>Different Type of SDG Datasource</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.CrawlType"><code class="name">var <span class="ident">CrawlType</span></code></dt>
<dd>
<div class="desc"><p>Crawl type for SDG/FSO jobs</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.DATA_SOURCE_TYPES"><code class="name">var <span class="ident">DATA_SOURCE_TYPES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.DS_CLOUD_STORAGE"><code class="name">var <span class="ident">DS_CLOUD_STORAGE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.DS_FILE"><code class="name">var <span class="ident">DS_FILE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.DYNAMIC_FEDERATED_SEARCH_PARAMS"><code class="name">var <span class="ident">DYNAMIC_FEDERATED_SEARCH_PARAMS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.EDIT_CATEGORY_PERMISSION"><code class="name">var <span class="ident">EDIT_CATEGORY_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_AGENT"><code class="name">var <span class="ident">EXCHANGE_AGENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_BACKUPSET"><code class="name">var <span class="ident">EXCHANGE_BACKUPSET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_INSTANCE"><code class="name">var <span class="ident">EXCHANGE_INSTANCE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_SUBCLIENT"><code class="name">var <span class="ident">EXCHANGE_SUBCLIENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.EXPORT_DOWNLOAD_REQ"><code class="name">var <span class="ident">EXPORT_DOWNLOAD_REQ</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_CONTENT_ID"><code class="name">var <span class="ident">FIELD_CONTENT_ID</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_CRAWL_TYPE"><code class="name">var <span class="ident">FIELD_CRAWL_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME"><code class="name">var <span class="ident">FIELD_DATA_SOURCE_DISPLAY_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_ID"><code class="name">var <span class="ident">FIELD_DATA_SOURCE_ID</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_ID_NON_SEA"><code class="name">var <span class="ident">FIELD_DATA_SOURCE_ID_NON_SEA</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_NAME"><code class="name">var <span class="ident">FIELD_DATA_SOURCE_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_NAME_SEA"><code class="name">var <span class="ident">FIELD_DATA_SOURCE_NAME_SEA</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE"><code class="name">var <span class="ident">FIELD_DATA_SOURCE_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DC_PLAN_ID"><code class="name">var <span class="ident">FIELD_DC_PLAN_ID</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DISPLAY_NAME"><code class="name">var <span class="ident">FIELD_DISPLAY_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DOCUMENT_COUNT"><code class="name">var <span class="ident">FIELD_DOCUMENT_COUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_IS_FILE"><code class="name">var <span class="ident">FIELD_IS_FILE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_PLAN_ID"><code class="name">var <span class="ident">FIELD_PLAN_ID</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_PSEDUCO_CLIENT_ID"><code class="name">var <span class="ident">FIELD_PSEDUCO_CLIENT_ID</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_SUBCLIENT_ID"><code class="name">var <span class="ident">FIELD_SUBCLIENT_ID</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FSO_SERVERS"><code class="name">var <span class="ident">FSO_SERVERS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FSO_SERVER_GROUPS"><code class="name">var <span class="ident">FSO_SERVER_GROUPS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FS_DEFAULT_EXPORT_FIELDS"><code class="name">var <span class="ident">FS_DEFAULT_EXPORT_FIELDS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.FS_SERVER_HANDLER_NAME"><code class="name">var <span class="ident">FS_SERVER_HANDLER_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_AGENT"><code class="name">var <span class="ident">ONEDRIVE_AGENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_BACKUPSET"><code class="name">var <span class="ident">ONEDRIVE_BACKUPSET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_INSTANCE"><code class="name">var <span class="ident">ONEDRIVE_INSTANCE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_SUBCLIENT"><code class="name">var <span class="ident">ONEDRIVE_SUBCLIENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_BULK_SEARCH_REQ"><code class="name">var <span class="ident">REVIEW_ACTION_BULK_SEARCH_REQ</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_DELETE_REQ_JSON"><code class="name">var <span class="ident">REVIEW_ACTION_DELETE_REQ_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_FSO_SUPPORTED"><code class="name">var <span class="ident">REVIEW_ACTION_FSO_SUPPORTED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET"><code class="name">var <span class="ident">REVIEW_ACTION_IDA_SELECT_SET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_IGNORE_FILES_REQ_JSON"><code class="name">var <span class="ident">REVIEW_ACTION_IGNORE_FILES_REQ_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_MOVE_REQ_JSON"><code class="name">var <span class="ident">REVIEW_ACTION_MOVE_REQ_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_SDG_SUPPORTED"><code class="name">var <span class="ident">REVIEW_ACTION_SDG_SUPPORTED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_SEARCH_FL_SET"><code class="name">var <span class="ident">REVIEW_ACTION_SEARCH_FL_SET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_SET_RETENTION_REQ_JSON"><code class="name">var <span class="ident">REVIEW_ACTION_SET_RETENTION_REQ_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_TAG_REQ_JSON"><code class="name">var <span class="ident">REVIEW_ACTION_TAG_REQ_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.ReviewActions"><code class="name">var <span class="ident">ReviewActions</span></code></dt>
<dd>
<div class="desc"><p>Review actions for documents on SDG/FSO app</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.RiskTypes"><code class="name">var <span class="ident">RiskTypes</span></code></dt>
<dd>
<div class="desc"><p>Different risk types for documents on SDG/FSO app</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.SERVER_LEVEL_SCHEDULE_JSON"><code class="name">var <span class="ident">SERVER_LEVEL_SCHEDULE_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.SHARE_REQUEST_JSON"><code class="name">var <span class="ident">SHARE_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.START_CRAWL_SERVER_REQUEST_JSON"><code class="name">var <span class="ident">START_CRAWL_SERVER_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.SourceType"><code class="name">var <span class="ident">SourceType</span></code></dt>
<dd>
<div class="desc"><p>Source type of data for FSO/SDG app</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.TAGGING_ITEMS_REQUEST"><code class="name">var <span class="ident">TAGGING_ITEMS_REQUEST</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.TAGGING_ITEMS_REVIEW_REQUEST"><code class="name">var <span class="ident">TAGGING_ITEMS_REVIEW_REQUEST</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.EdiscoveryConstants.VIEW_CATEGORY_PERMISSION"><code class="name">var <span class="ident">VIEW_CATEGORY_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants"><code class="flex name class">
<span>class <span class="ident">InventoryConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain constants for inventory manager</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L580-L675" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class InventoryConstants:
    &#34;&#34;&#34;Class to maintain constants for inventory manager&#34;&#34;&#34;

    CRAWL_JOB_FAILED_STATE = [5, 6, 7, 8, 9, 10, 11, 12]

    CRAWL_JOB_COMPLETE_STATE = 4

    CRAWL_JOB_COMPLETE_ERROR_STATE = 13

    INVENTORY_ADD_REQUEST_JSON = {
        &#34;name&#34;: &#34;&#34;,
        &#34;identityServers&#34;: [],
        &#34;indexServer&#34;: {
            &#34;cloudId&#34;: 0,
            &#34;displayName&#34;: &#34;&#34;
        }
    }

    FIELD_PROPERTY_NAME = &#39;name&#39;
    FIELD_PROPERTY_DNSHOST = &#39;hostName&#39;
    FIELD_PROPERTY_OS = &#39;operatingSystem&#39;
    FIELD_PROPERTY_IP = &#39;ipAddress&#39;
    FIELD_PROPERTY_COUNTRYCODE = &#39;countryCode&#39;

    KWARGS_NAME = &#39;name&#39;
    KWARGS_IP = &#39;ip&#39;
    KWARGS_OS = &#39;os&#39;
    KWARGS_FQDN = &#39;fqdn&#39;
    KWARGS_COUNTRY_CODE = &#39;country_code&#39;

    FIELD_PROPS_MAPPING = {
        FIELD_PROPERTY_NAME: KWARGS_NAME,
        FIELD_PROPERTY_IP: KWARGS_IP,
        FIELD_PROPERTY_OS: KWARGS_OS,
        FIELD_PROPERTY_DNSHOST: KWARGS_FQDN,
        FIELD_PROPERTY_COUNTRYCODE: KWARGS_COUNTRY_CODE
    }

    ASSET_FILE_SERVER_PROPERTY = [
        FIELD_PROPERTY_NAME,
        FIELD_PROPERTY_DNSHOST,
        FIELD_PROPERTY_OS,
        FIELD_PROPERTY_IP,
        FIELD_PROPERTY_COUNTRYCODE
    ]

    IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON = {&#34;identityServers&#34;: [], &#34;startDataCollection&#34;: True}

    VIEW_CATEGORY_PERMISSION = {
        &#34;permissionId&#34;: 31,
        &#34;permissionName&#34;: &#34;View&#34;,
        &#34;_type_&#34;: 122
    }
    EDIT_CATEGORY_PERMISSION = {
        &#34;permissionId&#34;: 2,
        &#34;permissionName&#34;: &#34;Agent Management&#34;,
        &#34;_type_&#34;: 122
    }

    INVENTORY_SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;_type_&#34;: 132,
                    &#34;seaDataSourceId&#34;: 0
                }
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        {
                            &#34;userId&#34;: 0,
                            &#34;_type_&#34;: 0,
                            &#34;userName&#34;: &#34;&#34;
                        }
                    ],
                    &#34;properties&#34;: {
                        &#34;categoryPermission&#34;: {
                            &#34;categoriesPermissionOperationType&#34;: 1,
                            &#34;categoriesPermissionList&#34;: [
                                VIEW_CATEGORY_PERMISSION
                            ]
                        }
                    }
                }
            ]
        }
    }

    class AssetType(Enum):
        &#34;&#34;&#34;Asset type for inventory&#34;&#34;&#34;
        IDENTITY_SERVER = 61
        FILE_SERVER = 132</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.ASSET_FILE_SERVER_PROPERTY"><code class="name">var <span class="ident">ASSET_FILE_SERVER_PROPERTY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.AssetType"><code class="name">var <span class="ident">AssetType</span></code></dt>
<dd>
<div class="desc"><p>Asset type for inventory</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.CRAWL_JOB_COMPLETE_ERROR_STATE"><code class="name">var <span class="ident">CRAWL_JOB_COMPLETE_ERROR_STATE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.CRAWL_JOB_COMPLETE_STATE"><code class="name">var <span class="ident">CRAWL_JOB_COMPLETE_STATE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.CRAWL_JOB_FAILED_STATE"><code class="name">var <span class="ident">CRAWL_JOB_FAILED_STATE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.EDIT_CATEGORY_PERMISSION"><code class="name">var <span class="ident">EDIT_CATEGORY_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_COUNTRYCODE"><code class="name">var <span class="ident">FIELD_PROPERTY_COUNTRYCODE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_DNSHOST"><code class="name">var <span class="ident">FIELD_PROPERTY_DNSHOST</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_IP"><code class="name">var <span class="ident">FIELD_PROPERTY_IP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_NAME"><code class="name">var <span class="ident">FIELD_PROPERTY_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_OS"><code class="name">var <span class="ident">FIELD_PROPERTY_OS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPS_MAPPING"><code class="name">var <span class="ident">FIELD_PROPS_MAPPING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON"><code class="name">var <span class="ident">IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.INVENTORY_ADD_REQUEST_JSON"><code class="name">var <span class="ident">INVENTORY_ADD_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.INVENTORY_SHARE_REQUEST_JSON"><code class="name">var <span class="ident">INVENTORY_SHARE_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_COUNTRY_CODE"><code class="name">var <span class="ident">KWARGS_COUNTRY_CODE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_FQDN"><code class="name">var <span class="ident">KWARGS_FQDN</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_IP"><code class="name">var <span class="ident">KWARGS_IP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_NAME"><code class="name">var <span class="ident">KWARGS_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_OS"><code class="name">var <span class="ident">KWARGS_OS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.InventoryConstants.VIEW_CATEGORY_PERMISSION"><code class="name">var <span class="ident">VIEW_CATEGORY_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants"><code class="flex name class">
<span>class <span class="ident">PlanConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain constants related to DC plan activate operations</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L678-L917" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class PlanConstants:
    &#34;&#34;&#34;Class to maintain constants related to DC plan activate operations&#34;&#34;&#34;

    INDEXING_ONLY_METADATA = 1
    INDEXING_METADATA_AND_CONTENT = 2

    DEFAULT_INCLUDE_DOC_TYPES = &#34;*.doc,*.docx,*.xls,*.xlsx,*.ppt,*.pptx,*.msg,*.txt,*.rtf,*.eml,*.pdf,&#34; \
                                &#34;*.htm,*.html,*.csv,*.log,*.ods,*.odt,*.odg,*.odp,*.dot,*.pages,*.xmind&#34;
    DEFAULT_EXCLUDE_LIST = [
        &#34;C:\\Program Files&#34;,
        &#34;C:\\Program Files (x86)&#34;,
        &#34;C:\\Windows&#34;]

    DEFAULT_MIN_DOC_SIZE = 0
    DEFAULT_MAX_DOC_SIZE = 50

    PLAN_UPDATE_REQUEST_JSON = {
        7: {
            &#34;ciPolicyInfo&#34;: {
                &#34;ciPolicy&#34;: {
                    &#34;policyType&#34;: 5,
                    &#34;flags&#34;: 536870912,
                    &#34;agentType&#34;: {
                        &#34;appTypeId&#34;: 0,
                        &#34;entityInfo&#34;: {},
                        &#34;flags&#34;: {}
                    },
                    &#34;detail&#34;: {
                        &#34;ciPolicy&#34;: {}
                    }
                }
            },
            &#34;eePolicyInfo&#34;: {
                &#34;eePolicy&#34;: {
                    &#34;policyType&#34;: 3,
                    &#34;flags&#34;: 536870920,
                    &#34;agentType&#34;: {
                        &#34;appTypeId&#34;: 0,
                        &#34;entityInfo&#34;: {},
                        &#34;flags&#34;: {}
                    },
                    &#34;detail&#34;: {
                        &#34;eePolicy&#34;: {}
                    }
                }
            },
            &#34;summary&#34;: {
                &#34;plan&#34;: {
                    &#34;planId&#34;: 0
                }
            }
        },
        6: {
            &#34;ciPolicyInfo&#34;: {
                &#34;ciPolicy&#34;: {
                    &#34;policyType&#34;: 5,
                    &#34;detail&#34;: {
                        &#34;ciPolicy&#34;: {
                            &#34;opType&#34;: 2,
                            &#34;enableExactSearch&#34;: False,
                            &#34;ciPolicyType&#34;: 5,
                            &#34;filters&#34;: {
                                &#34;fileFilters&#34;: {
                                    &#34;includeDocTypes&#34;: &#34;&#34;,
                                    &#34;minDocSize&#34;: 0,
                                    &#34;maxDocSize&#34;: 50
                                }
                            }
                        }
                    }
                }
            },
            &#34;eePolicyInfo&#34;: {},
            &#34;exchange&#34;: {},
            &#34;office365Info&#34;: {
                &#34;o365Exchange&#34;: {
                    &#34;mbArchiving&#34;: {
                        &#34;policyType&#34;: 1,
                        &#34;agentType&#34;: {
                            &#34;appTypeId&#34;: 137
                        },
                        &#34;detail&#34;: {
                            &#34;emailPolicy&#34;: {
                                &#34;emailPolicyType&#34;: 1,
                                &#34;archivePolicy&#34;: {
                                    &#34;primaryMailbox&#34;: True,
                                    &#34;contentIndexProps&#34;: {}
                                }
                            }
                        }
                    }
                },
                &#34;o365CloudOffice&#34;: {
                    &#34;caBackup&#34;: {
                        &#34;policyType&#34;: 6,
                        &#34;detail&#34;: {
                            &#34;cloudAppPolicy&#34;: {
                                &#34;cloudAppPolicyType&#34;: 1,
                                &#34;backupPolicy&#34;: {
                                    &#34;onedrivebackupPolicy&#34;: {},
                                    &#34;spbackupPolicy&#34;: {},
                                    &#34;teamsbackupPolicy&#34;: {}
                                }
                            }
                        }

                    }

                }

            },

            &#34;summary&#34;: {
                &#34;plan&#34;: {
                    &#34;planName&#34;: &#34;&#34;,
                    &#34;planId&#34;: 0
                }
            }

        }
    }

    PLAN_SCHEDULE_REQUEST_JSON = {
        7: {
            &#34;summary&#34;: {
                &#34;plan&#34;: {
                    &#34;planId&#34;: 0
                }
            },
            &#34;schedule&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;_type_&#34;: 158,
                        &#34;entityId&#34;: 0
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 4,
                    &#34;taskName&#34;: &#34;&#34;,
                    &#34;taskFlags&#34;: {
                        &#34;isEdiscovery&#34;: True
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskName&#34;: &#34;&#34;,
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 5025
                        },
                        &#34;pattern&#34;: {},
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;backupLevel&#34;: 2,
                                &#34;mediaOpt&#34;: {
                                    &#34;auxcopyJobOption&#34;: {
                                        &#34;maxNumberOfStreams&#34;: 1,
                                        &#34;allCopies&#34;: True,
                                        &#34;useMaximumStreams&#34;: True
                                    }
                                }
                            },
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: {
                                    &#34;fileAnalytics&#34;: False,
                                    &#34;idaType&#34;: 1,
                                    &#34;operationType&#34;: 2
                                }
                            }
                        }
                    }
                ]
            }
        }
    }
    PLAN_SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;entityType&#34;: 158,
                    &#34;_type_&#34;: 150,
                    &#34;entityId&#34;: 0
                }
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        {

                        }
                    ],
                    &#34;properties&#34;: {
                        &#34;role&#34;: {
                            &#34;_type_&#34;: 120,
                            &#34;roleId&#34;: 0,
                            &#34;roleName&#34;: &#34;&#34;
                        }
                    }
                }
            ]
        }
    }

    CREATE_V4_DC_PLAN_REQ = {
        &#34;application&#34;: 2,
        &#34;contentAnalyzer&#34;: [],
        &#34;contentIndexing&#34;: {
            &#34;extractTextFromImage&#34;: False,
            &#34;fileFilters&#34;: {
                &#34;excludePaths&#34;: [
                ],
                &#34;includeDocTypes&#34;: &#34;&#34;,
                &#34;maxDocSize&#34;: 50,
                &#34;minDocSize&#34;: 0
            },
            &#34;searchType&#34;: &#34;METADATA&#34;
        },
        &#34;entityDetection&#34;: {
            &#34;classifiers&#34;: [
            ],
            &#34;entities&#34;: [
            ]
        },
        &#34;indexServer&#34;: {},
        &#34;name&#34;: &#34;&#34;,
        &#34;threatAnalysis&#34;: False
    }

    class RAPlanSearchType(Enum):
        &#34;&#34;&#34;Class to maintain search types in Risk Analysis Plan&#34;&#34;&#34;
        SEARCH_TYPE_ONLY_METADATA = &#34;METADATA&#34;
        SEARCH_TYPE_METADATA_AND_CONTENT = &#34;METADATA_CONTENT&#34;

    class RAPlanAppType(Enum):
        &#34;&#34;&#34;Class to maintain plan application type&#34;&#34;&#34;
        CLASSIFIED = 2
        UNIFIED = 6</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.PlanConstants.CREATE_V4_DC_PLAN_REQ"><code class="name">var <span class="ident">CREATE_V4_DC_PLAN_REQ</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.DEFAULT_EXCLUDE_LIST"><code class="name">var <span class="ident">DEFAULT_EXCLUDE_LIST</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.DEFAULT_INCLUDE_DOC_TYPES"><code class="name">var <span class="ident">DEFAULT_INCLUDE_DOC_TYPES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.DEFAULT_MAX_DOC_SIZE"><code class="name">var <span class="ident">DEFAULT_MAX_DOC_SIZE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.DEFAULT_MIN_DOC_SIZE"><code class="name">var <span class="ident">DEFAULT_MIN_DOC_SIZE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.INDEXING_METADATA_AND_CONTENT"><code class="name">var <span class="ident">INDEXING_METADATA_AND_CONTENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.INDEXING_ONLY_METADATA"><code class="name">var <span class="ident">INDEXING_ONLY_METADATA</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.PLAN_SCHEDULE_REQUEST_JSON"><code class="name">var <span class="ident">PLAN_SCHEDULE_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.PLAN_SHARE_REQUEST_JSON"><code class="name">var <span class="ident">PLAN_SHARE_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.PLAN_UPDATE_REQUEST_JSON"><code class="name">var <span class="ident">PLAN_UPDATE_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.RAPlanAppType"><code class="name">var <span class="ident">RAPlanAppType</span></code></dt>
<dd>
<div class="desc"><p>Class to maintain plan application type</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.PlanConstants.RAPlanSearchType"><code class="name">var <span class="ident">RAPlanSearchType</span></code></dt>
<dd>
<div class="desc"><p>Class to maintain search types in Risk Analysis Plan</p></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants"><code class="flex name class">
<span>class <span class="ident">RequestConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>class to maintain constants for request manager</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L48-L117" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RequestConstants:
    &#34;&#34;&#34;class to maintain constants for request manager&#34;&#34;&#34;
    PROPERTY_REVIEW_CRIERIA = &#39;ReviewCriteria&#39;
    PROPERTY_ENTITIES = &#39;Entities&#39;
    PROPERTY_REQUEST_HANDLER_ID = &#39;RequestHandlerId&#39;
    PROPERTY_REQUEST_HANDLER_NAME = &#39;RequestHandlerName&#39;
    PROPERTY_REVIEW_SET_ID = &#39;ReviewSetId&#39;
    SEARCH_QUERY_SELECTION_SET = {
        &#34;entity_*&#34;,
        &#34;count_entity_*&#34;,
        &#34;Url&#34;,
        &#34;contentid&#34;,
        &#34;FileName&#34;,
        &#34;Size&#34;,
        &#34;data_source&#34;,
        &#34;data_source_name&#34;,
        &#34;entities_extracted&#34;,
        &#34;RedactMode*&#34;,
        &#34;CommentFor*&#34;,
        &#34;ConsentFor*&#34;}

    FACET_REVIEWED = &#39;_ConsentFor_%s_b_Reviewed&#39;
    FACET_NOT_REVIEWED = &#39;_ConsentFor_%s_b_Not reviewed&#39;
    FACET_ACCEPTED = &#39;_ConsentFor_%s_b_Accepted&#39;
    FACET_DECLINED = &#39;_ConsentFor_%s_b_Declined&#39;
    FACET_REDACTED = &#39;_RedactMode_%s_b_Redacted&#39;
    FACET_NOT_REDACTED = &#39;_RedactMode_%s_b_Not redacted&#39;
    FACET_COUNT = &#34;count&#34;
    REQUEST_FEDERATED_FACET_SEARCH_QUERY = {&#34;searchParams&#34;: [{&#34;key&#34;: &#34;q&#34;,
                                                              &#34;value&#34;: &#34;*:*&#34;},
                                                             {&#34;key&#34;: &#34;wt&#34;,
                                                              &#34;value&#34;: &#34;json&#34;},
                                                             {&#34;key&#34;: &#34;rows&#34;,
                                                              &#34;value&#34;: &#34;0&#34;},
                                                             {&#34;key&#34;: &#34;defType&#34;,
                                                              &#34;value&#34;: &#34;edismax&#34;},
                                                             {&#34;key&#34;: &#34;facet&#34;,
                                                              &#34;value&#34;: &#34;true&#34;},
                                                             {&#34;key&#34;: &#34;json.facet&#34;,
                                                              &#34;value&#34;: &#34;{\&#34;_ConsentFor_&lt;rsidparam&gt;_b_Reviewed\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_ConsentFor_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;ConsentFor_&lt;rsidparam&gt;_b:*\&#34;,\&#34;facet\&#34;:{}},\&#34;_ConsentFor_&lt;rsidparam&gt;_b_Not reviewed\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_ConsentFor_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;contentid:* AND -(ConsentFor_&lt;rsidparam&gt;_b:*)\&#34;,\&#34;facet\&#34;:{}},\&#34;_ConsentFor_&lt;rsidparam&gt;_b_Accepted\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_ConsentFor_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;ConsentFor_&lt;rsidparam&gt;_b:true\&#34;,\&#34;facet\&#34;:{}},\&#34;_ConsentFor_&lt;rsidparam&gt;_b_Declined\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_ConsentFor_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_ConsentFor_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;ConsentFor_&lt;rsidparam&gt;_b:false\&#34;,\&#34;facet\&#34;:{}},\&#34;_RedactMode_&lt;rsidparam&gt;_b_Redacted\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_RedactMode_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_RedactMode_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_RedactMode_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;RedactMode_&lt;rsidparam&gt;_b:true\&#34;,\&#34;facet\&#34;:{}},\&#34;_RedactMode_&lt;rsidparam&gt;_b_Not redacted\&#34;:{\&#34;type\&#34;:\&#34;query\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_group_RedactMode_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_RedactMode_&lt;rsidparam&gt;_b\&#34;,\&#34;tag_exclude_RedactMode_&lt;rsidparam&gt;_b\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;q\&#34;:\&#34;RedactMode_&lt;rsidparam&gt;_b:false\&#34;,\&#34;facet\&#34;:{}},\&#34;FileExtension\&#34;:{\&#34;type\&#34;:\&#34;terms\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_FileExtension\&#34;,\&#34;tag_exclude_FileExtension\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;field\&#34;:\&#34;FileExtension\&#34;,\&#34;limit\&#34;:-1,\&#34;facet\&#34;:{},\&#34;sort\&#34;:{\&#34;count\&#34;:\&#34;desc\&#34;}},\&#34;ReadAccessUserName\&#34;:{\&#34;type\&#34;:\&#34;terms\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_ReadAccessUserName\&#34;,\&#34;tag_exclude_ReadAccessUserName\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;field\&#34;:\&#34;ReadAccessUserName\&#34;,\&#34;limit\&#34;:-1,\&#34;facet\&#34;:{},\&#34;sort\&#34;:{\&#34;count\&#34;:\&#34;desc\&#34;}},\&#34;data_source_name\&#34;:{\&#34;type\&#34;:\&#34;terms\&#34;,\&#34;domain\&#34;:{\&#34;excludeTags\&#34;:[\&#34;tag_data_source_name\&#34;,\&#34;tag_exclude_data_source_name\&#34;]},\&#34;numBuckets\&#34;:true,\&#34;mincount\&#34;:1,\&#34;field\&#34;:\&#34;data_source_name\&#34;,\&#34;limit\&#34;:-1,\&#34;facet\&#34;:{},\&#34;sort\&#34;:{\&#34;count\&#34;:\&#34;desc\&#34;}}}&#34;},
                                                             {&#34;key&#34;: &#34;useDCubeReq&#34;,
                                                              &#34;value&#34;: &#34;true&#34;}]}

    FIELD_DOC_COUNT = &#34;TotalDocuments&#34;
    FIELD_REVIEWED = &#34;ReviewedDocuments&#34;
    FIELD_NOT_REVIEWED = &#34;Non-ReviewedDocuments&#34;
    FIELD_ACCEPTED = &#39;AcceptedDocuments&#39;
    FIELD_DECLINED = &#39;DeclinedDocuments&#39;
    FIELD_REDACTED = &#39;RedactedDocuments&#39;
    FIELD_NOT_REDACTED = &#39;Non-RedactedDocuments&#39;

    class RequestStatus(Enum):
        &#34;&#34;&#34;enum to specify different request status&#34;&#34;&#34;
        TaskCreated = 1
        TaskConfigured = 2
        ReviewInProgress = 3
        ReviewCompleted = 4
        ApproveCompleted = 5
        ExportCompleted = 6
        DeleteCompleted = 7
        TaskCompleted = 8
        ApprovalRequested = 9
        ActionInProgress = 10
        CompletedWithErrors = 11
        Failed = 12

    class RequestType(Enum):
        &#34;&#34;&#34;enum to maintain different request type&#34;&#34;&#34;
        EXPORT = &#39;EXPORT&#39;
        DELETE = &#39;DELETE&#39;</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FACET_ACCEPTED"><code class="name">var <span class="ident">FACET_ACCEPTED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FACET_COUNT"><code class="name">var <span class="ident">FACET_COUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FACET_DECLINED"><code class="name">var <span class="ident">FACET_DECLINED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FACET_NOT_REDACTED"><code class="name">var <span class="ident">FACET_NOT_REDACTED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FACET_NOT_REVIEWED"><code class="name">var <span class="ident">FACET_NOT_REVIEWED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FACET_REDACTED"><code class="name">var <span class="ident">FACET_REDACTED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FACET_REVIEWED"><code class="name">var <span class="ident">FACET_REVIEWED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FIELD_ACCEPTED"><code class="name">var <span class="ident">FIELD_ACCEPTED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FIELD_DECLINED"><code class="name">var <span class="ident">FIELD_DECLINED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FIELD_DOC_COUNT"><code class="name">var <span class="ident">FIELD_DOC_COUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FIELD_NOT_REDACTED"><code class="name">var <span class="ident">FIELD_NOT_REDACTED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FIELD_NOT_REVIEWED"><code class="name">var <span class="ident">FIELD_NOT_REVIEWED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FIELD_REDACTED"><code class="name">var <span class="ident">FIELD_REDACTED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.FIELD_REVIEWED"><code class="name">var <span class="ident">FIELD_REVIEWED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_ENTITIES"><code class="name">var <span class="ident">PROPERTY_ENTITIES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REQUEST_HANDLER_ID"><code class="name">var <span class="ident">PROPERTY_REQUEST_HANDLER_ID</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REQUEST_HANDLER_NAME"><code class="name">var <span class="ident">PROPERTY_REQUEST_HANDLER_NAME</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REVIEW_CRIERIA"><code class="name">var <span class="ident">PROPERTY_REVIEW_CRIERIA</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REVIEW_SET_ID"><code class="name">var <span class="ident">PROPERTY_REVIEW_SET_ID</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.REQUEST_FEDERATED_FACET_SEARCH_QUERY"><code class="name">var <span class="ident">REQUEST_FEDERATED_FACET_SEARCH_QUERY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.RequestStatus"><code class="name">var <span class="ident">RequestStatus</span></code></dt>
<dd>
<div class="desc"><p>enum to specify different request status</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.RequestType"><code class="name">var <span class="ident">RequestType</span></code></dt>
<dd>
<div class="desc"><p>enum to maintain different request type</p></div>
</dd>
<dt id="cvpysdk.activateapps.constants.RequestConstants.SEARCH_QUERY_SELECTION_SET"><code class="name">var <span class="ident">SEARCH_QUERY_SELECTION_SET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.constants.TagConstants"><code class="flex name class">
<span>class <span class="ident">TagConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>class to maintain all the Tags related constants</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L980-L1059" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TagConstants:
    &#34;&#34;&#34;class to maintain all the Tags related constants&#34;&#34;&#34;

    TAG_SET_ADD_REQUEST_JSON = {
        &#34;entityType&#34;: 9504,
        &#34;operationType&#34;: 1,
        &#34;fromSite&#34;: 4,
        &#34;container&#34;: {
            &#34;containerType&#34;: 9504,
            &#34;containerName&#34;: &#34;&#34;,
            &#34;comment&#34;: &#34;&#34;
        }
    }

    TAG_SET_MODIFY_REQUEST_JSON = copy.deepcopy(TAG_SET_ADD_REQUEST_JSON)
    TAG_SET_MODIFY_REQUEST_JSON[&#39;operationType&#39;] = 3

    TAG_SET_DELETE_REQUEST_JSON = {
        &#34;entityType&#34;: 9504,
        &#34;containers&#34;: [
            {
                &#34;containerType&#34;: 9504,
                &#34;containerId&#34;: 0
            }
        ]
    }

    TAG_ADD_REQUEST_JSON = {
        &#34;container&#34;: {
            &#34;containerId&#34;: 0
        },
        &#34;tags&#34;: [
            {
                &#34;name&#34;: &#34;&#34;
            }
        ]
    }

    TAG_MODIFY_REQUEST_JSON = copy.deepcopy(TAG_ADD_REQUEST_JSON)
    TAG_MODIFY_REQUEST_JSON[&#39;tags&#39;][0][&#39;tagId&#39;] = 0

    VIEW_PERMISSION = {
        &#34;permissionId&#34;: 31,
        &#34;_type_&#34;: 122,
        &#34;permissionName&#34;: &#34;View&#34;
    }
    ADD_PERMISSION = {
        &#34;permissionId&#34;: 34,
        &#34;_type_&#34;: 122,
        &#34;permissionName&#34;: &#34;Add/Append&#34;
    }

    TAG_SET_SHARE_REQUEST_JSON = {
        &#34;entityAssociated&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;tagId&#34;: 0,
                    &#34;_type_&#34;: 9504
                }
            ]
        },
        &#34;securityAssociations&#34;: {
            &#34;associationsOperationType&#34;: 1,
            &#34;associations&#34;: [
                {
                    &#34;userOrGroup&#34;: [
                        {

                        }
                    ],
                    &#34;properties&#34;: {
                        &#34;permissions&#34;: [
                            VIEW_PERMISSION
                        ]
                    }
                }

            ]
        }
    }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.TagConstants.ADD_PERMISSION"><code class="name">var <span class="ident">ADD_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TagConstants.TAG_ADD_REQUEST_JSON"><code class="name">var <span class="ident">TAG_ADD_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TagConstants.TAG_MODIFY_REQUEST_JSON"><code class="name">var <span class="ident">TAG_MODIFY_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TagConstants.TAG_SET_ADD_REQUEST_JSON"><code class="name">var <span class="ident">TAG_SET_ADD_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TagConstants.TAG_SET_DELETE_REQUEST_JSON"><code class="name">var <span class="ident">TAG_SET_DELETE_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TagConstants.TAG_SET_MODIFY_REQUEST_JSON"><code class="name">var <span class="ident">TAG_SET_MODIFY_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TagConstants.TAG_SET_SHARE_REQUEST_JSON"><code class="name">var <span class="ident">TAG_SET_SHARE_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TagConstants.VIEW_PERMISSION"><code class="name">var <span class="ident">VIEW_PERMISSION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.constants.TargetApps"><code class="flex name class">
<span>class <span class="ident">TargetApps</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain supported apps types in Activate</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L920-L926" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TargetApps(Enum):
    &#34;&#34;&#34;Class to maintain supported apps types in Activate&#34;&#34;&#34;
    SDG = 2
    FSO = 1
    CASE_MGR = 4
    FS = 8
    RA = 128</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.TargetApps.CASE_MGR"><code class="name">var <span class="ident">CASE_MGR</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TargetApps.FS"><code class="name">var <span class="ident">FS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TargetApps.FSO"><code class="name">var <span class="ident">FSO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TargetApps.RA"><code class="name">var <span class="ident">RA</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TargetApps.SDG"><code class="name">var <span class="ident">SDG</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.constants.TrainingStatus"><code class="flex name class">
<span>class <span class="ident">TrainingStatus</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain training status for classifier</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/constants.py#L929-L937" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TrainingStatus(Enum):
    &#34;&#34;&#34;Class to maintain training status for classifier&#34;&#34;&#34;
    NOT_APPLICABLE = 0
    CREATED = 1
    RUNNING = 2
    FAILED = 3
    COMPLETED = 4
    CANCELLED = 5
    NOT_USABLE = 6</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.constants.TrainingStatus.CANCELLED"><code class="name">var <span class="ident">CANCELLED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TrainingStatus.COMPLETED"><code class="name">var <span class="ident">COMPLETED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TrainingStatus.CREATED"><code class="name">var <span class="ident">CREATED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TrainingStatus.FAILED"><code class="name">var <span class="ident">FAILED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TrainingStatus.NOT_APPLICABLE"><code class="name">var <span class="ident">NOT_APPLICABLE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TrainingStatus.NOT_USABLE"><code class="name">var <span class="ident">NOT_USABLE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.constants.TrainingStatus.RUNNING"><code class="name">var <span class="ident">RUNNING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.activateapps" href="index.html">cvpysdk.activateapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.ActivateEntityConstants" href="#cvpysdk.activateapps.constants.ActivateEntityConstants">ActivateEntityConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.constants.ActivateEntityConstants.REQUEST_JSON" href="#cvpysdk.activateapps.constants.ActivateEntityConstants.REQUEST_JSON">REQUEST_JSON</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.ClassifierConstants" href="#cvpysdk.activateapps.constants.ClassifierConstants">ClassifierConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.constants.ClassifierConstants.CREATE_REQUEST_JSON" href="#cvpysdk.activateapps.constants.ClassifierConstants.CREATE_REQUEST_JSON">CREATE_REQUEST_JSON</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.ComplianceConstants" href="#cvpysdk.activateapps.constants.ComplianceConstants">ComplianceConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.ADD_PERMISSION" href="#cvpysdk.activateapps.constants.ComplianceConstants.ADD_PERMISSION">ADD_PERMISSION</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.AppTypes" href="#cvpysdk.activateapps.constants.ComplianceConstants.AppTypes">AppTypes</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.COMPLIANCE_SEARCH_JSON" href="#cvpysdk.activateapps.constants.ComplianceConstants.COMPLIANCE_SEARCH_JSON">COMPLIANCE_SEARCH_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.CUSTOM_FACET" href="#cvpysdk.activateapps.constants.ComplianceConstants.CUSTOM_FACET">CUSTOM_FACET</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.CUSTOM_FACETS" href="#cvpysdk.activateapps.constants.ComplianceConstants.CUSTOM_FACETS">CUSTOM_FACETS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.CUSTOM_FACETS_NAME" href="#cvpysdk.activateapps.constants.ComplianceConstants.CUSTOM_FACETS_NAME">CUSTOM_FACETS_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.DELETE_PERMISSION" href="#cvpysdk.activateapps.constants.ComplianceConstants.DELETE_PERMISSION">DELETE_PERMISSION</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.DOWNLOAD_PERMISSION" href="#cvpysdk.activateapps.constants.ComplianceConstants.DOWNLOAD_PERMISSION">DOWNLOAD_PERMISSION</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.EMAIL_FILTERS_KEY" href="#cvpysdk.activateapps.constants.ComplianceConstants.EMAIL_FILTERS_KEY">EMAIL_FILTERS_KEY</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.EMAIL_TYPE" href="#cvpysdk.activateapps.constants.ComplianceConstants.EMAIL_TYPE">EMAIL_TYPE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.EMAIL_TYPES" href="#cvpysdk.activateapps.constants.ComplianceConstants.EMAIL_TYPES">EMAIL_TYPES</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.EXPORT_SET_SHARE_REQUEST_JSON" href="#cvpysdk.activateapps.constants.ComplianceConstants.EXPORT_SET_SHARE_REQUEST_JSON">EXPORT_SET_SHARE_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.ExportTypes" href="#cvpysdk.activateapps.constants.ComplianceConstants.ExportTypes">ExportTypes</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.FACET_KEY" href="#cvpysdk.activateapps.constants.ComplianceConstants.FACET_KEY">FACET_KEY</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.FILE_FACET" href="#cvpysdk.activateapps.constants.ComplianceConstants.FILE_FACET">FILE_FACET</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.FILE_FILTERS" href="#cvpysdk.activateapps.constants.ComplianceConstants.FILE_FILTERS">FILE_FILTERS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.FILE_FILTERS_KEY" href="#cvpysdk.activateapps.constants.ComplianceConstants.FILE_FILTERS_KEY">FILE_FILTERS_KEY</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.FILE_TYPE" href="#cvpysdk.activateapps.constants.ComplianceConstants.FILE_TYPE">FILE_TYPE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.FILE_TYPES" href="#cvpysdk.activateapps.constants.ComplianceConstants.FILE_TYPES">FILE_TYPES</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.ONEDRIVE_FACET" href="#cvpysdk.activateapps.constants.ComplianceConstants.ONEDRIVE_FACET">ONEDRIVE_FACET</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSIONS" href="#cvpysdk.activateapps.constants.ComplianceConstants.PERMISSIONS">PERMISSIONS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_ADD_NAME" href="#cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_ADD_NAME">PERMISSION_ADD_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_DELETE_NAME" href="#cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_DELETE_NAME">PERMISSION_DELETE_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_DOWNLOAD_NAME" href="#cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_DOWNLOAD_NAME">PERMISSION_DOWNLOAD_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_VIEW_NAME" href="#cvpysdk.activateapps.constants.ComplianceConstants.PERMISSION_VIEW_NAME">PERMISSION_VIEW_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.RESPONSE_FIELD_LIST" href="#cvpysdk.activateapps.constants.ComplianceConstants.RESPONSE_FIELD_LIST">RESPONSE_FIELD_LIST</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.RESTORE_TYPE" href="#cvpysdk.activateapps.constants.ComplianceConstants.RESTORE_TYPE">RESTORE_TYPE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.SHAREPOINT_FACET" href="#cvpysdk.activateapps.constants.ComplianceConstants.SHAREPOINT_FACET">SHAREPOINT_FACET</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.SOLR_FIELD_FILE_NAME" href="#cvpysdk.activateapps.constants.ComplianceConstants.SOLR_FIELD_FILE_NAME">SOLR_FIELD_FILE_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.SOLR_FIELD_SIZE" href="#cvpysdk.activateapps.constants.ComplianceConstants.SOLR_FIELD_SIZE">SOLR_FIELD_SIZE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.TEAMS_FACET" href="#cvpysdk.activateapps.constants.ComplianceConstants.TEAMS_FACET">TEAMS_FACET</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.ComplianceConstants.VIEW_PERMISSION" href="#cvpysdk.activateapps.constants.ComplianceConstants.VIEW_PERMISSION">VIEW_PERMISSION</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants" href="#cvpysdk.activateapps.constants.EdiscoveryConstants">EdiscoveryConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.ADD_FS_REQ_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.ADD_FS_REQ_JSON">ADD_FS_REQ_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.ADD_O365_SDG_BACKED_UP_DS_REQ" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.ADD_O365_SDG_BACKED_UP_DS_REQ">ADD_O365_SDG_BACKED_UP_DS_REQ</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.CREATE_CLIENT_REQ_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.CREATE_CLIENT_REQ_JSON">CREATE_CLIENT_REQ_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.CRITERIA_EXTRACTED_DOCS">CRITERIA_EXTRACTED_DOCS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.ClientType" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.ClientType">ClientType</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.CrawlType" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.CrawlType">CrawlType</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.DATA_SOURCE_TYPES" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.DATA_SOURCE_TYPES">DATA_SOURCE_TYPES</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.DS_CLOUD_STORAGE" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.DS_CLOUD_STORAGE">DS_CLOUD_STORAGE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.DS_FILE" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.DS_FILE">DS_FILE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.DYNAMIC_FEDERATED_SEARCH_PARAMS" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.DYNAMIC_FEDERATED_SEARCH_PARAMS">DYNAMIC_FEDERATED_SEARCH_PARAMS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.EDIT_CATEGORY_PERMISSION" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.EDIT_CATEGORY_PERMISSION">EDIT_CATEGORY_PERMISSION</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_AGENT" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_AGENT">EXCHANGE_AGENT</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_BACKUPSET" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_BACKUPSET">EXCHANGE_BACKUPSET</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_INSTANCE" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_INSTANCE">EXCHANGE_INSTANCE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_SUBCLIENT" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.EXCHANGE_SUBCLIENT">EXCHANGE_SUBCLIENT</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.EXPORT_DOWNLOAD_REQ" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.EXPORT_DOWNLOAD_REQ">EXPORT_DOWNLOAD_REQ</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_CONTENT_ID" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_CONTENT_ID">FIELD_CONTENT_ID</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_CRAWL_TYPE" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_CRAWL_TYPE">FIELD_CRAWL_TYPE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_DISPLAY_NAME">FIELD_DATA_SOURCE_DISPLAY_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_ID" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_ID">FIELD_DATA_SOURCE_ID</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_ID_NON_SEA" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_ID_NON_SEA">FIELD_DATA_SOURCE_ID_NON_SEA</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_NAME" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_NAME">FIELD_DATA_SOURCE_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_NAME_SEA" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_NAME_SEA">FIELD_DATA_SOURCE_NAME_SEA</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DATA_SOURCE_TYPE">FIELD_DATA_SOURCE_TYPE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DC_PLAN_ID" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DC_PLAN_ID">FIELD_DC_PLAN_ID</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DISPLAY_NAME" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DISPLAY_NAME">FIELD_DISPLAY_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DOCUMENT_COUNT" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_DOCUMENT_COUNT">FIELD_DOCUMENT_COUNT</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_IS_FILE" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_IS_FILE">FIELD_IS_FILE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_PLAN_ID" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_PLAN_ID">FIELD_PLAN_ID</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_PSEDUCO_CLIENT_ID" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_PSEDUCO_CLIENT_ID">FIELD_PSEDUCO_CLIENT_ID</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_SUBCLIENT_ID" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FIELD_SUBCLIENT_ID">FIELD_SUBCLIENT_ID</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FSO_SERVERS" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FSO_SERVERS">FSO_SERVERS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FSO_SERVER_GROUPS" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FSO_SERVER_GROUPS">FSO_SERVER_GROUPS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FS_DEFAULT_EXPORT_FIELDS" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FS_DEFAULT_EXPORT_FIELDS">FS_DEFAULT_EXPORT_FIELDS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.FS_SERVER_HANDLER_NAME" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.FS_SERVER_HANDLER_NAME">FS_SERVER_HANDLER_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_AGENT" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_AGENT">ONEDRIVE_AGENT</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_BACKUPSET" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_BACKUPSET">ONEDRIVE_BACKUPSET</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_INSTANCE" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_INSTANCE">ONEDRIVE_INSTANCE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_SUBCLIENT" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.ONEDRIVE_SUBCLIENT">ONEDRIVE_SUBCLIENT</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_BULK_SEARCH_REQ" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_BULK_SEARCH_REQ">REVIEW_ACTION_BULK_SEARCH_REQ</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_DELETE_REQ_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_DELETE_REQ_JSON">REVIEW_ACTION_DELETE_REQ_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_FSO_SUPPORTED" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_FSO_SUPPORTED">REVIEW_ACTION_FSO_SUPPORTED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_IDA_SELECT_SET">REVIEW_ACTION_IDA_SELECT_SET</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_IGNORE_FILES_REQ_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_IGNORE_FILES_REQ_JSON">REVIEW_ACTION_IGNORE_FILES_REQ_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_MOVE_REQ_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_MOVE_REQ_JSON">REVIEW_ACTION_MOVE_REQ_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_SDG_SUPPORTED" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_SDG_SUPPORTED">REVIEW_ACTION_SDG_SUPPORTED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_SEARCH_FL_SET" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_SEARCH_FL_SET">REVIEW_ACTION_SEARCH_FL_SET</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_SET_RETENTION_REQ_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_SET_RETENTION_REQ_JSON">REVIEW_ACTION_SET_RETENTION_REQ_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_TAG_REQ_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.REVIEW_ACTION_TAG_REQ_JSON">REVIEW_ACTION_TAG_REQ_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.ReviewActions" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.ReviewActions">ReviewActions</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.RiskTypes" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.RiskTypes">RiskTypes</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.SERVER_LEVEL_SCHEDULE_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.SERVER_LEVEL_SCHEDULE_JSON">SERVER_LEVEL_SCHEDULE_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.SHARE_REQUEST_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.SHARE_REQUEST_JSON">SHARE_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.START_CRAWL_SERVER_REQUEST_JSON" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.START_CRAWL_SERVER_REQUEST_JSON">START_CRAWL_SERVER_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.SourceType" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.SourceType">SourceType</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.TAGGING_ITEMS_REQUEST" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.TAGGING_ITEMS_REQUEST">TAGGING_ITEMS_REQUEST</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.TAGGING_ITEMS_REVIEW_REQUEST" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.TAGGING_ITEMS_REVIEW_REQUEST">TAGGING_ITEMS_REVIEW_REQUEST</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.EdiscoveryConstants.VIEW_CATEGORY_PERMISSION" href="#cvpysdk.activateapps.constants.EdiscoveryConstants.VIEW_CATEGORY_PERMISSION">VIEW_CATEGORY_PERMISSION</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.InventoryConstants" href="#cvpysdk.activateapps.constants.InventoryConstants">InventoryConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.ASSET_FILE_SERVER_PROPERTY" href="#cvpysdk.activateapps.constants.InventoryConstants.ASSET_FILE_SERVER_PROPERTY">ASSET_FILE_SERVER_PROPERTY</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.AssetType" href="#cvpysdk.activateapps.constants.InventoryConstants.AssetType">AssetType</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.CRAWL_JOB_COMPLETE_ERROR_STATE" href="#cvpysdk.activateapps.constants.InventoryConstants.CRAWL_JOB_COMPLETE_ERROR_STATE">CRAWL_JOB_COMPLETE_ERROR_STATE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.CRAWL_JOB_COMPLETE_STATE" href="#cvpysdk.activateapps.constants.InventoryConstants.CRAWL_JOB_COMPLETE_STATE">CRAWL_JOB_COMPLETE_STATE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.CRAWL_JOB_FAILED_STATE" href="#cvpysdk.activateapps.constants.InventoryConstants.CRAWL_JOB_FAILED_STATE">CRAWL_JOB_FAILED_STATE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.EDIT_CATEGORY_PERMISSION" href="#cvpysdk.activateapps.constants.InventoryConstants.EDIT_CATEGORY_PERMISSION">EDIT_CATEGORY_PERMISSION</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_COUNTRYCODE" href="#cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_COUNTRYCODE">FIELD_PROPERTY_COUNTRYCODE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_DNSHOST" href="#cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_DNSHOST">FIELD_PROPERTY_DNSHOST</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_IP" href="#cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_IP">FIELD_PROPERTY_IP</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_NAME" href="#cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_NAME">FIELD_PROPERTY_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_OS" href="#cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPERTY_OS">FIELD_PROPERTY_OS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPS_MAPPING" href="#cvpysdk.activateapps.constants.InventoryConstants.FIELD_PROPS_MAPPING">FIELD_PROPS_MAPPING</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON" href="#cvpysdk.activateapps.constants.InventoryConstants.IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON">IDENTITY_SERVER_ASSET_ADD_TO_INVENTORY_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.INVENTORY_ADD_REQUEST_JSON" href="#cvpysdk.activateapps.constants.InventoryConstants.INVENTORY_ADD_REQUEST_JSON">INVENTORY_ADD_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.INVENTORY_SHARE_REQUEST_JSON" href="#cvpysdk.activateapps.constants.InventoryConstants.INVENTORY_SHARE_REQUEST_JSON">INVENTORY_SHARE_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_COUNTRY_CODE" href="#cvpysdk.activateapps.constants.InventoryConstants.KWARGS_COUNTRY_CODE">KWARGS_COUNTRY_CODE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_FQDN" href="#cvpysdk.activateapps.constants.InventoryConstants.KWARGS_FQDN">KWARGS_FQDN</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_IP" href="#cvpysdk.activateapps.constants.InventoryConstants.KWARGS_IP">KWARGS_IP</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_NAME" href="#cvpysdk.activateapps.constants.InventoryConstants.KWARGS_NAME">KWARGS_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.KWARGS_OS" href="#cvpysdk.activateapps.constants.InventoryConstants.KWARGS_OS">KWARGS_OS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.InventoryConstants.VIEW_CATEGORY_PERMISSION" href="#cvpysdk.activateapps.constants.InventoryConstants.VIEW_CATEGORY_PERMISSION">VIEW_CATEGORY_PERMISSION</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.PlanConstants" href="#cvpysdk.activateapps.constants.PlanConstants">PlanConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.CREATE_V4_DC_PLAN_REQ" href="#cvpysdk.activateapps.constants.PlanConstants.CREATE_V4_DC_PLAN_REQ">CREATE_V4_DC_PLAN_REQ</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.DEFAULT_EXCLUDE_LIST" href="#cvpysdk.activateapps.constants.PlanConstants.DEFAULT_EXCLUDE_LIST">DEFAULT_EXCLUDE_LIST</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.DEFAULT_INCLUDE_DOC_TYPES" href="#cvpysdk.activateapps.constants.PlanConstants.DEFAULT_INCLUDE_DOC_TYPES">DEFAULT_INCLUDE_DOC_TYPES</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.DEFAULT_MAX_DOC_SIZE" href="#cvpysdk.activateapps.constants.PlanConstants.DEFAULT_MAX_DOC_SIZE">DEFAULT_MAX_DOC_SIZE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.DEFAULT_MIN_DOC_SIZE" href="#cvpysdk.activateapps.constants.PlanConstants.DEFAULT_MIN_DOC_SIZE">DEFAULT_MIN_DOC_SIZE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.INDEXING_METADATA_AND_CONTENT" href="#cvpysdk.activateapps.constants.PlanConstants.INDEXING_METADATA_AND_CONTENT">INDEXING_METADATA_AND_CONTENT</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.INDEXING_ONLY_METADATA" href="#cvpysdk.activateapps.constants.PlanConstants.INDEXING_ONLY_METADATA">INDEXING_ONLY_METADATA</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.PLAN_SCHEDULE_REQUEST_JSON" href="#cvpysdk.activateapps.constants.PlanConstants.PLAN_SCHEDULE_REQUEST_JSON">PLAN_SCHEDULE_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.PLAN_SHARE_REQUEST_JSON" href="#cvpysdk.activateapps.constants.PlanConstants.PLAN_SHARE_REQUEST_JSON">PLAN_SHARE_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.PLAN_UPDATE_REQUEST_JSON" href="#cvpysdk.activateapps.constants.PlanConstants.PLAN_UPDATE_REQUEST_JSON">PLAN_UPDATE_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.RAPlanAppType" href="#cvpysdk.activateapps.constants.PlanConstants.RAPlanAppType">RAPlanAppType</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.PlanConstants.RAPlanSearchType" href="#cvpysdk.activateapps.constants.PlanConstants.RAPlanSearchType">RAPlanSearchType</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.RequestConstants" href="#cvpysdk.activateapps.constants.RequestConstants">RequestConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FACET_ACCEPTED" href="#cvpysdk.activateapps.constants.RequestConstants.FACET_ACCEPTED">FACET_ACCEPTED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FACET_COUNT" href="#cvpysdk.activateapps.constants.RequestConstants.FACET_COUNT">FACET_COUNT</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FACET_DECLINED" href="#cvpysdk.activateapps.constants.RequestConstants.FACET_DECLINED">FACET_DECLINED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FACET_NOT_REDACTED" href="#cvpysdk.activateapps.constants.RequestConstants.FACET_NOT_REDACTED">FACET_NOT_REDACTED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FACET_NOT_REVIEWED" href="#cvpysdk.activateapps.constants.RequestConstants.FACET_NOT_REVIEWED">FACET_NOT_REVIEWED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FACET_REDACTED" href="#cvpysdk.activateapps.constants.RequestConstants.FACET_REDACTED">FACET_REDACTED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FACET_REVIEWED" href="#cvpysdk.activateapps.constants.RequestConstants.FACET_REVIEWED">FACET_REVIEWED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FIELD_ACCEPTED" href="#cvpysdk.activateapps.constants.RequestConstants.FIELD_ACCEPTED">FIELD_ACCEPTED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FIELD_DECLINED" href="#cvpysdk.activateapps.constants.RequestConstants.FIELD_DECLINED">FIELD_DECLINED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FIELD_DOC_COUNT" href="#cvpysdk.activateapps.constants.RequestConstants.FIELD_DOC_COUNT">FIELD_DOC_COUNT</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FIELD_NOT_REDACTED" href="#cvpysdk.activateapps.constants.RequestConstants.FIELD_NOT_REDACTED">FIELD_NOT_REDACTED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FIELD_NOT_REVIEWED" href="#cvpysdk.activateapps.constants.RequestConstants.FIELD_NOT_REVIEWED">FIELD_NOT_REVIEWED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FIELD_REDACTED" href="#cvpysdk.activateapps.constants.RequestConstants.FIELD_REDACTED">FIELD_REDACTED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.FIELD_REVIEWED" href="#cvpysdk.activateapps.constants.RequestConstants.FIELD_REVIEWED">FIELD_REVIEWED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_ENTITIES" href="#cvpysdk.activateapps.constants.RequestConstants.PROPERTY_ENTITIES">PROPERTY_ENTITIES</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REQUEST_HANDLER_ID" href="#cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REQUEST_HANDLER_ID">PROPERTY_REQUEST_HANDLER_ID</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REQUEST_HANDLER_NAME" href="#cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REQUEST_HANDLER_NAME">PROPERTY_REQUEST_HANDLER_NAME</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REVIEW_CRIERIA" href="#cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REVIEW_CRIERIA">PROPERTY_REVIEW_CRIERIA</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REVIEW_SET_ID" href="#cvpysdk.activateapps.constants.RequestConstants.PROPERTY_REVIEW_SET_ID">PROPERTY_REVIEW_SET_ID</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.REQUEST_FEDERATED_FACET_SEARCH_QUERY" href="#cvpysdk.activateapps.constants.RequestConstants.REQUEST_FEDERATED_FACET_SEARCH_QUERY">REQUEST_FEDERATED_FACET_SEARCH_QUERY</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.RequestStatus" href="#cvpysdk.activateapps.constants.RequestConstants.RequestStatus">RequestStatus</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.RequestType" href="#cvpysdk.activateapps.constants.RequestConstants.RequestType">RequestType</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.RequestConstants.SEARCH_QUERY_SELECTION_SET" href="#cvpysdk.activateapps.constants.RequestConstants.SEARCH_QUERY_SELECTION_SET">SEARCH_QUERY_SELECTION_SET</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.TagConstants" href="#cvpysdk.activateapps.constants.TagConstants">TagConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.constants.TagConstants.ADD_PERMISSION" href="#cvpysdk.activateapps.constants.TagConstants.ADD_PERMISSION">ADD_PERMISSION</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TagConstants.TAG_ADD_REQUEST_JSON" href="#cvpysdk.activateapps.constants.TagConstants.TAG_ADD_REQUEST_JSON">TAG_ADD_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TagConstants.TAG_MODIFY_REQUEST_JSON" href="#cvpysdk.activateapps.constants.TagConstants.TAG_MODIFY_REQUEST_JSON">TAG_MODIFY_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TagConstants.TAG_SET_ADD_REQUEST_JSON" href="#cvpysdk.activateapps.constants.TagConstants.TAG_SET_ADD_REQUEST_JSON">TAG_SET_ADD_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TagConstants.TAG_SET_DELETE_REQUEST_JSON" href="#cvpysdk.activateapps.constants.TagConstants.TAG_SET_DELETE_REQUEST_JSON">TAG_SET_DELETE_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TagConstants.TAG_SET_MODIFY_REQUEST_JSON" href="#cvpysdk.activateapps.constants.TagConstants.TAG_SET_MODIFY_REQUEST_JSON">TAG_SET_MODIFY_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TagConstants.TAG_SET_SHARE_REQUEST_JSON" href="#cvpysdk.activateapps.constants.TagConstants.TAG_SET_SHARE_REQUEST_JSON">TAG_SET_SHARE_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TagConstants.VIEW_PERMISSION" href="#cvpysdk.activateapps.constants.TagConstants.VIEW_PERMISSION">VIEW_PERMISSION</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.TargetApps" href="#cvpysdk.activateapps.constants.TargetApps">TargetApps</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.constants.TargetApps.CASE_MGR" href="#cvpysdk.activateapps.constants.TargetApps.CASE_MGR">CASE_MGR</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TargetApps.FS" href="#cvpysdk.activateapps.constants.TargetApps.FS">FS</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TargetApps.FSO" href="#cvpysdk.activateapps.constants.TargetApps.FSO">FSO</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TargetApps.RA" href="#cvpysdk.activateapps.constants.TargetApps.RA">RA</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TargetApps.SDG" href="#cvpysdk.activateapps.constants.TargetApps.SDG">SDG</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.constants.TrainingStatus" href="#cvpysdk.activateapps.constants.TrainingStatus">TrainingStatus</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.constants.TrainingStatus.CANCELLED" href="#cvpysdk.activateapps.constants.TrainingStatus.CANCELLED">CANCELLED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TrainingStatus.COMPLETED" href="#cvpysdk.activateapps.constants.TrainingStatus.COMPLETED">COMPLETED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TrainingStatus.CREATED" href="#cvpysdk.activateapps.constants.TrainingStatus.CREATED">CREATED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TrainingStatus.FAILED" href="#cvpysdk.activateapps.constants.TrainingStatus.FAILED">FAILED</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TrainingStatus.NOT_APPLICABLE" href="#cvpysdk.activateapps.constants.TrainingStatus.NOT_APPLICABLE">NOT_APPLICABLE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TrainingStatus.NOT_USABLE" href="#cvpysdk.activateapps.constants.TrainingStatus.NOT_USABLE">NOT_USABLE</a></code></li>
<li><code><a title="cvpysdk.activateapps.constants.TrainingStatus.RUNNING" href="#cvpysdk.activateapps.constants.TrainingStatus.RUNNING">RUNNING</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>