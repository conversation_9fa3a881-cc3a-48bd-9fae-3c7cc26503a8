<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activitycontrol API documentation</title>
<meta name="description" content="Main file for performing activity control operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activitycontrol</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing activity control operations</p>
<p>Activity Control is the only class defined in this file.</p>
<p>ActivityControl: Class for managing Activity Control enable/disable
for various entities within the comcell.</p>
<h2 id="activitycontrol">Activitycontrol</h2>
<p><strong>init</strong>(commcell_object) &ndash; initialise object of Class associated to the commcell</p>
<p><strong>repr</strong>()
&ndash;
String representation of the instance of this class.</p>
<p>set()
&ndash;
method to set activity control.</p>
<p>enable_after_delay()
&ndash; method to disable activity control and set a delay time.</p>
<p>_get_activity_control_status()
&ndash; method to get activity control status</p>
<p>is_enabled()
&ndash;
boolean specifying if a given activity is enabled or not
<strong>reEnableTime</strong>
&ndash;
returns the Enable back time
<strong>reEnableTimeZone</strong>
&ndash;
returns the Enable back time zone</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activitycontrol.py#L1-L271" class="git-link">Browse git</a>
</summary>
<pre><code class="python">#!/usr/bin/env python
# -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing activity control operations

Activity Control is the only class defined in this file.

ActivityControl: Class for managing Activity Control enable/disable
                    for various entities within the comcell.

ActivityControl:
    __init__(commcell_object) -- initialise object of Class associated to the commcell

    __repr__()               --  String representation of the instance of this class.

    set()                       --  method to set activity control.

    enable_after_delay()   -- method to disable activity control and set a delay time.

    _get_activity_control_status()   -- method to get activity control status

    is_enabled()          --  boolean specifying if a given activity is enabled or not
    **reEnableTime**                --  returns the Enable back time
    **reEnableTimeZone**                --  returns the Enable back time zone

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from .exception import SDKException


class ActivityControl(object):
    &#34;&#34;&#34;Class for performing activity control operations.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialise the Activity control class instance.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the ActivityControl class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._activity_type_dict = {
            &#34;ALL ACTIVITY&#34;: 128,
            &#34;DATA MANAGEMENT&#34;: 1,
            &#34;DATA RECOVERY&#34;: 2,
            &#34;DATA AGING&#34;: 16,
            &#34;AUX COPY&#34;: 4,
            &#34;DATA VERIFICATION&#34;: 8192,
            &#34;DDB ACTIVITY&#34;: 512,
            &#34;SCHEDULER&#34;: 256,
            &#34;OFFLINE CONTENT INDEXING&#34;: 1024,
        }
        self._get_activity_control_status()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;ActivityControl class instance&#39;
        return representation_string

    def _request_json_(self, activity_type, enable_time):
        &#34;&#34;&#34;Returns the JSON request to pass to the API
            as per the options selected by the user.

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;

        request_json = {
            &#34;commCellInfo&#34;: {
                &#34;commCellActivityControlInfo&#34;: {
                    &#34;activityControlOptions&#34;: [
                        {
                            &#34;activityType&#34;: self._activity_type_dict[activity_type],
                            &#34;enableAfterADelay&#34;: True,
                            &#34;enableActivityType&#34;: False,
                            &#34;dateTime&#34;: {
                                &#34;time&#34;: enable_time}}]}}}

        return request_json

    def set(self, activity_type, action):
        &#34;&#34;&#34;Sets activity control on Commcell.

            Args:
                activity_type (str)  --  Activity Type to be Enabled or Disabled
                Values:
                    &#34;ALL ACTIVITY&#34;,
                    &#34;DATA MANAGEMENT&#34;,
                    &#34;DATA RECOVERY&#34;,
                    &#34;DATA AGING&#34;,
                    &#34;AUX COPY&#34;,
                    &#34;DATA VERIFICATION&#34;,
                    &#34;DDB ACTIVITY&#34;,
                    &#34;SCHEDULER&#34;,
                    &#34;OFFLINE CONTENT INDEXING&#34;,

                action (str)    --    Enable or Disable
                Values:
                    Enable
                    Disable
            Raises:
                SDKException:
                    if failed to set

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        set_request = self._commcell_object._services[&#39;SET_ACTIVITY_CONTROL&#39;] % (
            str(self._activity_type_dict[activity_type]), str(action))
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, set_request
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])
                if error_code == &#39;0&#39;:
                    self._get_activity_control_status()
                    return
                else:
                    raise SDKException(
                        &#39;CVPySDK&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def enable_after_delay(self, activity_type, enable_time):
        &#34;&#34;&#34;Disables activity if not already disabled
            and enables at the time specified.

            Args:
                activity_type (str)  --  Activity Type to be Enabled or Disabled
                Values:
                    &#34;ALL ACTIVITY&#34;,
                    &#34;DATA MANAGEMENT&#34;,
                    &#34;DATA RECOVERY&#34;,
                    &#34;DATA AGING&#34;,
                    &#34;AUX COPY&#34;,
                    &#34;DATA VERIFICATION&#34;,
                    &#34;DDB ACTIVITY&#34;,
                    &#34;SCHEDULER&#34;,
                    &#34;OFFLINE CONTENT INDEXING&#34;,

                enable_time (str)-- Unix Timestamp in UTC timezone
            Raises:
                SDKException:
                    if failed to enable activity control after a time

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        request_json = self._request_json_(activity_type, enable_time)

        set_request = self._commcell_object._services[&#39;SET_COMMCELL_PROPERTIES&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, set_request, request_json
        )

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][0][&#39;errorCode&#39;]

                if error_code == 0:
                    self._get_activity_control_status()
                    return
                elif &#39;errorMessage&#39; in response.json()[&#39;response&#39;][0]:
                    error_message = response.json(
                    )[&#39;response&#39;][0][&#39;errorMessage&#39;]

                    o_str = &#39;Failed to enable activity control \
                                after a delay\nError: &#34;{0}&#34;&#39;.format(
                                    error_message)
                    raise SDKException(&#39;CVPySDK&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_activity_control_status(self):
        &#34;&#34;&#34;Gets the activity control status

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        get_request = self._commcell_object._services[&#39;GET_ACTIVITY_CONTROL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, get_request
        )

        if flag:
            if response.json() and &#39;acObjects&#39; in response.json():
                self._activity_control_properties_list = response.json()[
                    &#39;acObjects&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def is_enabled(self, activity_type):
        &#34;&#34;&#34;Returns True/False based on the enabled flag and also sets
                     other relevant properties for a given activity type.

            Args:
                activity_type (str)  --  Activity Type to be Enabled or Disabled
                Values:
                    &#34;ALL ACTIVITY&#34;,
                    &#34;DATA MANAGEMENT&#34;,
                    &#34;DATA RECOVERY&#34;,
                    &#34;DATA AGING&#34;,
                    &#34;AUX COPY&#34;,
                    &#34;DATA VERIFICATION&#34;,
                    &#34;DDB ACTIVITY&#34;,
                    &#34;SCHEDULER&#34;,
                    &#34;OFFLINE CONTENT INDEXING&#34;,
        &#34;&#34;&#34;
        self._get_activity_control_status()
        for each_activity in self._activity_control_properties_list:
            if int(each_activity[&#39;activityType&#39;]) == \
                    self._activity_type_dict[activity_type]:
                self._reEnableTime = each_activity[&#39;reEnableTime&#39;]
                self._noSchedEnable = each_activity[&#39;noSchedEnable&#39;]
                self._reenableTimeZone = each_activity[&#39;reenableTimeZone&#39;]
                return each_activity[&#39;enabled&#39;]

        o_str = &#39;Failed to find activity type:&#34;{0}&#34; in the response&#39;.format(
            activity_type)
        raise SDKException(&#39;Client&#39;, &#39;102&#39;, o_str)

    @property
    def reEnableTime(self):
        &#34;&#34;&#34;Treats the reEnableTime as a read-only attribute.&#34;&#34;&#34;
        return self._reEnableTime

    @property
    def reEnableTimeZone(self):
        &#34;&#34;&#34;Treats the reEnableTimeZone as a read-only attribute.&#34;&#34;&#34;
        return self._reenableTimeZone</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activitycontrol.ActivityControl"><code class="flex name class">
<span>class <span class="ident">ActivityControl</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing activity control operations.</p>
<p>Initialise the Activity control class instance.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the ActivityControl class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activitycontrol.py#L50-L271" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ActivityControl(object):
    &#34;&#34;&#34;Class for performing activity control operations.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialise the Activity control class instance.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the ActivityControl class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._activity_type_dict = {
            &#34;ALL ACTIVITY&#34;: 128,
            &#34;DATA MANAGEMENT&#34;: 1,
            &#34;DATA RECOVERY&#34;: 2,
            &#34;DATA AGING&#34;: 16,
            &#34;AUX COPY&#34;: 4,
            &#34;DATA VERIFICATION&#34;: 8192,
            &#34;DDB ACTIVITY&#34;: 512,
            &#34;SCHEDULER&#34;: 256,
            &#34;OFFLINE CONTENT INDEXING&#34;: 1024,
        }
        self._get_activity_control_status()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;ActivityControl class instance&#39;
        return representation_string

    def _request_json_(self, activity_type, enable_time):
        &#34;&#34;&#34;Returns the JSON request to pass to the API
            as per the options selected by the user.

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;

        request_json = {
            &#34;commCellInfo&#34;: {
                &#34;commCellActivityControlInfo&#34;: {
                    &#34;activityControlOptions&#34;: [
                        {
                            &#34;activityType&#34;: self._activity_type_dict[activity_type],
                            &#34;enableAfterADelay&#34;: True,
                            &#34;enableActivityType&#34;: False,
                            &#34;dateTime&#34;: {
                                &#34;time&#34;: enable_time}}]}}}

        return request_json

    def set(self, activity_type, action):
        &#34;&#34;&#34;Sets activity control on Commcell.

            Args:
                activity_type (str)  --  Activity Type to be Enabled or Disabled
                Values:
                    &#34;ALL ACTIVITY&#34;,
                    &#34;DATA MANAGEMENT&#34;,
                    &#34;DATA RECOVERY&#34;,
                    &#34;DATA AGING&#34;,
                    &#34;AUX COPY&#34;,
                    &#34;DATA VERIFICATION&#34;,
                    &#34;DDB ACTIVITY&#34;,
                    &#34;SCHEDULER&#34;,
                    &#34;OFFLINE CONTENT INDEXING&#34;,

                action (str)    --    Enable or Disable
                Values:
                    Enable
                    Disable
            Raises:
                SDKException:
                    if failed to set

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        set_request = self._commcell_object._services[&#39;SET_ACTIVITY_CONTROL&#39;] % (
            str(self._activity_type_dict[activity_type]), str(action))
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, set_request
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])
                if error_code == &#39;0&#39;:
                    self._get_activity_control_status()
                    return
                else:
                    raise SDKException(
                        &#39;CVPySDK&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def enable_after_delay(self, activity_type, enable_time):
        &#34;&#34;&#34;Disables activity if not already disabled
            and enables at the time specified.

            Args:
                activity_type (str)  --  Activity Type to be Enabled or Disabled
                Values:
                    &#34;ALL ACTIVITY&#34;,
                    &#34;DATA MANAGEMENT&#34;,
                    &#34;DATA RECOVERY&#34;,
                    &#34;DATA AGING&#34;,
                    &#34;AUX COPY&#34;,
                    &#34;DATA VERIFICATION&#34;,
                    &#34;DDB ACTIVITY&#34;,
                    &#34;SCHEDULER&#34;,
                    &#34;OFFLINE CONTENT INDEXING&#34;,

                enable_time (str)-- Unix Timestamp in UTC timezone
            Raises:
                SDKException:
                    if failed to enable activity control after a time

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        request_json = self._request_json_(activity_type, enable_time)

        set_request = self._commcell_object._services[&#39;SET_COMMCELL_PROPERTIES&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, set_request, request_json
        )

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                error_code = response.json()[&#39;response&#39;][0][&#39;errorCode&#39;]

                if error_code == 0:
                    self._get_activity_control_status()
                    return
                elif &#39;errorMessage&#39; in response.json()[&#39;response&#39;][0]:
                    error_message = response.json(
                    )[&#39;response&#39;][0][&#39;errorMessage&#39;]

                    o_str = &#39;Failed to enable activity control \
                                after a delay\nError: &#34;{0}&#34;&#39;.format(
                                    error_message)
                    raise SDKException(&#39;CVPySDK&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_activity_control_status(self):
        &#34;&#34;&#34;Gets the activity control status

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        get_request = self._commcell_object._services[&#39;GET_ACTIVITY_CONTROL&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, get_request
        )

        if flag:
            if response.json() and &#39;acObjects&#39; in response.json():
                self._activity_control_properties_list = response.json()[
                    &#39;acObjects&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def is_enabled(self, activity_type):
        &#34;&#34;&#34;Returns True/False based on the enabled flag and also sets
                     other relevant properties for a given activity type.

            Args:
                activity_type (str)  --  Activity Type to be Enabled or Disabled
                Values:
                    &#34;ALL ACTIVITY&#34;,
                    &#34;DATA MANAGEMENT&#34;,
                    &#34;DATA RECOVERY&#34;,
                    &#34;DATA AGING&#34;,
                    &#34;AUX COPY&#34;,
                    &#34;DATA VERIFICATION&#34;,
                    &#34;DDB ACTIVITY&#34;,
                    &#34;SCHEDULER&#34;,
                    &#34;OFFLINE CONTENT INDEXING&#34;,
        &#34;&#34;&#34;
        self._get_activity_control_status()
        for each_activity in self._activity_control_properties_list:
            if int(each_activity[&#39;activityType&#39;]) == \
                    self._activity_type_dict[activity_type]:
                self._reEnableTime = each_activity[&#39;reEnableTime&#39;]
                self._noSchedEnable = each_activity[&#39;noSchedEnable&#39;]
                self._reenableTimeZone = each_activity[&#39;reenableTimeZone&#39;]
                return each_activity[&#39;enabled&#39;]

        o_str = &#39;Failed to find activity type:&#34;{0}&#34; in the response&#39;.format(
            activity_type)
        raise SDKException(&#39;Client&#39;, &#39;102&#39;, o_str)

    @property
    def reEnableTime(self):
        &#34;&#34;&#34;Treats the reEnableTime as a read-only attribute.&#34;&#34;&#34;
        return self._reEnableTime

    @property
    def reEnableTimeZone(self):
        &#34;&#34;&#34;Treats the reEnableTimeZone as a read-only attribute.&#34;&#34;&#34;
        return self._reenableTimeZone</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activitycontrol.ActivityControl.reEnableTime"><code class="name">var <span class="ident">reEnableTime</span></code></dt>
<dd>
<div class="desc"><p>Treats the reEnableTime as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activitycontrol.py#L263-L266" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def reEnableTime(self):
    &#34;&#34;&#34;Treats the reEnableTime as a read-only attribute.&#34;&#34;&#34;
    return self._reEnableTime</code></pre>
</details>
</dd>
<dt id="cvpysdk.activitycontrol.ActivityControl.reEnableTimeZone"><code class="name">var <span class="ident">reEnableTimeZone</span></code></dt>
<dd>
<div class="desc"><p>Treats the reEnableTimeZone as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activitycontrol.py#L268-L271" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def reEnableTimeZone(self):
    &#34;&#34;&#34;Treats the reEnableTimeZone as a read-only attribute.&#34;&#34;&#34;
    return self._reenableTimeZone</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activitycontrol.ActivityControl.enable_after_delay"><code class="name flex">
<span>def <span class="ident">enable_after_delay</span></span>(<span>self, activity_type, enable_time)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables activity if not already disabled
and enables at the time specified.</p>
<h2 id="args">Args</h2>
<p>activity_type (str)
&ndash;
Activity Type to be Enabled or Disabled
Values:
"ALL ACTIVITY",
"DATA MANAGEMENT",
"DATA RECOVERY",
"DATA AGING",
"AUX COPY",
"DATA VERIFICATION",
"DDB ACTIVITY",
"SCHEDULER",
"OFFLINE CONTENT INDEXING",</p>
<p>enable_time (str)&ndash; Unix Timestamp in UTC timezone</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable activity control after a time</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activitycontrol.py#L153-L206" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_after_delay(self, activity_type, enable_time):
    &#34;&#34;&#34;Disables activity if not already disabled
        and enables at the time specified.

        Args:
            activity_type (str)  --  Activity Type to be Enabled or Disabled
            Values:
                &#34;ALL ACTIVITY&#34;,
                &#34;DATA MANAGEMENT&#34;,
                &#34;DATA RECOVERY&#34;,
                &#34;DATA AGING&#34;,
                &#34;AUX COPY&#34;,
                &#34;DATA VERIFICATION&#34;,
                &#34;DDB ACTIVITY&#34;,
                &#34;SCHEDULER&#34;,
                &#34;OFFLINE CONTENT INDEXING&#34;,

            enable_time (str)-- Unix Timestamp in UTC timezone
        Raises:
            SDKException:
                if failed to enable activity control after a time

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    request_json = self._request_json_(activity_type, enable_time)

    set_request = self._commcell_object._services[&#39;SET_COMMCELL_PROPERTIES&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;PUT&#39;, set_request, request_json
    )

    if flag:
        if response.json() and &#39;response&#39; in response.json():
            error_code = response.json()[&#39;response&#39;][0][&#39;errorCode&#39;]

            if error_code == 0:
                self._get_activity_control_status()
                return
            elif &#39;errorMessage&#39; in response.json()[&#39;response&#39;][0]:
                error_message = response.json(
                )[&#39;response&#39;][0][&#39;errorMessage&#39;]

                o_str = &#39;Failed to enable activity control \
                            after a delay\nError: &#34;{0}&#34;&#39;.format(
                                error_message)
                raise SDKException(&#39;CVPySDK&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activitycontrol.ActivityControl.is_enabled"><code class="name flex">
<span>def <span class="ident">is_enabled</span></span>(<span>self, activity_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns True/False based on the enabled flag and also sets
other relevant properties for a given activity type.</p>
<h2 id="args">Args</h2>
<p>activity_type (str)
&ndash;
Activity Type to be Enabled or Disabled
Values:
"ALL ACTIVITY",
"DATA MANAGEMENT",
"DATA RECOVERY",
"DATA AGING",
"AUX COPY",
"DATA VERIFICATION",
"DDB ACTIVITY",
"SCHEDULER",
"OFFLINE CONTENT INDEXING",</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activitycontrol.py#L233-L261" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def is_enabled(self, activity_type):
    &#34;&#34;&#34;Returns True/False based on the enabled flag and also sets
                 other relevant properties for a given activity type.

        Args:
            activity_type (str)  --  Activity Type to be Enabled or Disabled
            Values:
                &#34;ALL ACTIVITY&#34;,
                &#34;DATA MANAGEMENT&#34;,
                &#34;DATA RECOVERY&#34;,
                &#34;DATA AGING&#34;,
                &#34;AUX COPY&#34;,
                &#34;DATA VERIFICATION&#34;,
                &#34;DDB ACTIVITY&#34;,
                &#34;SCHEDULER&#34;,
                &#34;OFFLINE CONTENT INDEXING&#34;,
    &#34;&#34;&#34;
    self._get_activity_control_status()
    for each_activity in self._activity_control_properties_list:
        if int(each_activity[&#39;activityType&#39;]) == \
                self._activity_type_dict[activity_type]:
            self._reEnableTime = each_activity[&#39;reEnableTime&#39;]
            self._noSchedEnable = each_activity[&#39;noSchedEnable&#39;]
            self._reenableTimeZone = each_activity[&#39;reenableTimeZone&#39;]
            return each_activity[&#39;enabled&#39;]

    o_str = &#39;Failed to find activity type:&#34;{0}&#34; in the response&#39;.format(
        activity_type)
    raise SDKException(&#39;Client&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activitycontrol.ActivityControl.set"><code class="name flex">
<span>def <span class="ident">set</span></span>(<span>self, activity_type, action)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets activity control on Commcell.</p>
<h2 id="args">Args</h2>
<p>activity_type (str)
&ndash;
Activity Type to be Enabled or Disabled
Values:
"ALL ACTIVITY",
"DATA MANAGEMENT",
"DATA RECOVERY",
"DATA AGING",
"AUX COPY",
"DATA VERIFICATION",
"DDB ACTIVITY",
"SCHEDULER",
"OFFLINE CONTENT INDEXING",</p>
<p>action (str)
&ndash;
Enable or Disable
Values:
Enable
Disable</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to set</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activitycontrol.py#L103-L151" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set(self, activity_type, action):
    &#34;&#34;&#34;Sets activity control on Commcell.

        Args:
            activity_type (str)  --  Activity Type to be Enabled or Disabled
            Values:
                &#34;ALL ACTIVITY&#34;,
                &#34;DATA MANAGEMENT&#34;,
                &#34;DATA RECOVERY&#34;,
                &#34;DATA AGING&#34;,
                &#34;AUX COPY&#34;,
                &#34;DATA VERIFICATION&#34;,
                &#34;DDB ACTIVITY&#34;,
                &#34;SCHEDULER&#34;,
                &#34;OFFLINE CONTENT INDEXING&#34;,

            action (str)    --    Enable or Disable
            Values:
                Enable
                Disable
        Raises:
            SDKException:
                if failed to set

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    set_request = self._commcell_object._services[&#39;SET_ACTIVITY_CONTROL&#39;] % (
        str(self._activity_type_dict[activity_type]), str(action))
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, set_request
    )

    if flag:
        if response.json():
            error_code = str(response.json()[&#39;errorCode&#39;])
            if error_code == &#39;0&#39;:
                self._get_activity_control_status()
                return
            else:
                raise SDKException(
                    &#39;CVPySDK&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activitycontrol.ActivityControl" href="#cvpysdk.activitycontrol.ActivityControl">ActivityControl</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activitycontrol.ActivityControl.enable_after_delay" href="#cvpysdk.activitycontrol.ActivityControl.enable_after_delay">enable_after_delay</a></code></li>
<li><code><a title="cvpysdk.activitycontrol.ActivityControl.is_enabled" href="#cvpysdk.activitycontrol.ActivityControl.is_enabled">is_enabled</a></code></li>
<li><code><a title="cvpysdk.activitycontrol.ActivityControl.reEnableTime" href="#cvpysdk.activitycontrol.ActivityControl.reEnableTime">reEnableTime</a></code></li>
<li><code><a title="cvpysdk.activitycontrol.ActivityControl.reEnableTimeZone" href="#cvpysdk.activitycontrol.ActivityControl.reEnableTimeZone">reEnableTimeZone</a></code></li>
<li><code><a title="cvpysdk.activitycontrol.ActivityControl.set" href="#cvpysdk.activitycontrol.ActivityControl.set">set</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>