<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.policies.configuration_policies API documentation</title>
<meta name="description" content="Main file for performing Configuration Policies related related operations on the commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.policies.configuration_policies</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing Configuration Policies related related operations on the commcell.</p>
<p>ConfigurationPolicies:
Class for representing all the Configuration Policies associated
with the Commcell</p>
<p>ConfigurationPolicy:
Class for representing a single Configuration Policy. Contains
method definitions for common methods among all Configuration Policies</p>
<p>ArchivePolicy:
Class for representing a single Archive Policy associated with
the Commcell; inherits ConfigurationPolicy</p>
<p>JournalPolicy:
Class for representing a single Journal Policy associated with
the Commcell; inherits ConfigurationPolicy</p>
<p>CleanupPolicy:
Class for representing a single Cleanup Policy associated with
the Commcell; inherits ConfigurationPolicy</p>
<p>RetentionPolicy:
Class for representing a single Retention Policy associated with
the Commcell; inherits ConfigurationPolicy</p>
<p>ContentIndexingPolicy:
Class for representing a single Content Indexing Policy associated with
the Commcell; inherits ConfigurationPolicy</p>
<h2 id="configurationpolicies">Configurationpolicies</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialize the ConfigurationPolicies instance for the Commcell</p>
<p><strong>str</strong>()
&ndash;
returns all the ConfigurationPolicies policies associated
with the Commcell</p>
<p><strong>repr</strong>()
&ndash;
returns a string for the instance of the
ConfigurationPolicies class</p>
<p>_get_policies()
&ndash;
gets all the Configuration policies of the Commcell</p>
<p>_get_ci_policies()
&ndash;
gets all the CI configuration policies of the Commcell</p>
<p>has_policy(policy_name)
&ndash;
checks if a Configuration policy exists with the
given name in a particular instance</p>
<p>get(policy_name)
&ndash;
returns a ConfigurationPolicy object of the
specified Configuration policy name</p>
<p>add(policy_object)
&ndash;
adds a new Configuration policy to the
ConfigurationPolicies instance, and returns an object of corresponding policy_type</p>
<p>delete(policy_name)
&ndash;
removes the specified Configuration policy from the Commcell</p>
<p>get_policy_object()
&ndash;
get the policy object based on policy type</p>
<p>run_content_indexing()
&ndash;
runs a Content indexing job for the CI policy</p>
<h2 id="contentindexingpolicy">Contentindexingpolicy</h2>
<p><strong>init</strong>()
&ndash;
initializes the ContentIndexingPolicy instance for the given policy name</p>
<p>_initialize_policy_json()
&ndash;
creates a JSON payload for the Content Indexing Policy</p>
<p>ContentIndexingPolicy Attributes:</p>
<pre><code>**name**                    --  name of the Content Indexing policy

**include_doc_types**       --  list of all the file types to be included while Content Indexing

**index_server_name**       --  index server name to be used for Content Indexing

**data_access_node**        --  data access node's client name

**min_doc_size**            --  minimum documents size in MB

**max_doc_size**            --  maximum documents size in MB

**exclude_paths**           --  list of all the paths to be excluded from Content Indexing
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1-L1886" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing Configuration Policies related related operations on the commcell.

ConfigurationPolicies:  Class for representing all the Configuration Policies associated
                            with the Commcell

ConfigurationPolicy:    Class for representing a single Configuration Policy. Contains
                            method definitions for common methods among all Configuration Policies

ArchivePolicy:          Class for representing a single Archive Policy associated with
                            the Commcell; inherits ConfigurationPolicy

JournalPolicy:          Class for representing a single Journal Policy associated with
                            the Commcell; inherits ConfigurationPolicy

CleanupPolicy:          Class for representing a single Cleanup Policy associated with
                            the Commcell; inherits ConfigurationPolicy

RetentionPolicy:        Class for representing a single Retention Policy associated with
                            the Commcell; inherits ConfigurationPolicy

ContentIndexingPolicy:  Class for representing a single Content Indexing Policy associated with
                            the Commcell; inherits ConfigurationPolicy


ConfigurationPolicies:

    __init__(commcell_object)   --  initialize the ConfigurationPolicies instance for the Commcell

    __str__()                   --  returns all the ConfigurationPolicies policies associated
    with the Commcell

    __repr__()                  --  returns a string for the instance of the
    ConfigurationPolicies class

    _get_policies()             --  gets all the Configuration policies of the Commcell

    _get_ci_policies()          --  gets all the CI configuration policies of the Commcell

    has_policy(policy_name)     --  checks if a Configuration policy exists with the
    given name in a particular instance

    get(policy_name)            --  returns a ConfigurationPolicy object of the
    specified Configuration policy name

    add(policy_object)          --  adds a new Configuration policy to the
    ConfigurationPolicies instance, and returns an object of corresponding policy_type

    delete(policy_name)         --  removes the specified Configuration policy from the Commcell

    get_policy_object()         --  get the policy object based on policy type

    run_content_indexing()      --  runs a Content indexing job for the CI policy


ContentIndexingPolicy:

    __init__()                  --  initializes the ContentIndexingPolicy instance for the given policy name

    _initialize_policy_json()   --  creates a JSON payload for the Content Indexing Policy

ContentIndexingPolicy Attributes:

    **name**                    --  name of the Content Indexing policy

    **include_doc_types**       --  list of all the file types to be included while Content Indexing

    **index_server_name**       --  index server name to be used for Content Indexing

    **data_access_node**        --  data access node&#39;s client name

    **min_doc_size**            --  minimum documents size in MB

    **max_doc_size**            --  maximum documents size in MB

    **exclude_paths**           --  list of all the paths to be excluded from Content Indexing


&#34;&#34;&#34;

from __future__ import unicode_literals

from ..exception import SDKException
from ..job import Job


class ConfigurationPolicies(object):
    &#34;&#34;&#34;Class for getting all the Configuration policies associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the ConfigurationPolicies class.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of the ConfigurationPolicies class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._POLICY = self._services[&#39;GET_CONFIGURATION_POLICIES&#39;]
        self._POLICY_FS = self._services[&#39;GET_CONFIGURATION_POLICIES_FS&#39;]
        self._CREATE_TASK = self._services[&#39;CREATE_TASK&#39;]
        self._policies = None
        self._ci_policies = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the ConfigurationPolicies class.&#34;&#34;&#34;
        return &#34;ConfigurationPolicies class instance for Commcell&#34;

    def _get_policies(self):
        &#34;&#34;&#34;Gets all the Configuration policies associated to the
            commcell specified by commcell object.

            Returns:
                dict    -   consists of all Configuration policies of the commcell

                    {
                        &#34;configuration_policy1_name&#34;: configuration_policy1_id,

                        &#34;configuration_policy2_name&#34;: configuration_policy2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._POLICY)

        if flag:
            if response.json() and &#39;policies&#39; in response.json():
                policies = response.json()[&#39;policies&#39;]

                if policies == []:
                    return {}

                policies_dict = {}

                for policy in policies:
                    temp_name = policy[&#39;policyEntity&#39;][&#39;policyName&#39;].lower()
                    temp_id = str(policy[&#39;policyEntity&#39;][&#39;policyId&#39;]).lower()
                    temp_policytype = str(policy[&#39;detail&#39;][&#39;emailPolicy&#39;]
                                          [&#39;emailPolicyType&#39;]).lower()
                    policies_dict[temp_name] = [temp_id, temp_policytype]

                return policies_dict
            else:
                return {}
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def has_policy(self, policy_name):
        &#34;&#34;&#34;Checks if a Configuration policy exists in the commcell with
            the input Configuration policy name.

            Args:
                policy_name     (str)   --  name of the Configuration policy

            Returns:
                bool    -   boolean output whether the Configuration policy exists in the commcell
                or not

            Raises:
                SDKException:
                    if type of the configuration policy name argument is not string

        &#34;&#34;&#34;
        if not isinstance(policy_name, str):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)

        return (self._policies and policy_name.lower() in self._policies) or \
               (self._ci_policies and policy_name.lower() in self._ci_policies)

    def _get_ci_policies(self):
        &#34;&#34;&#34;Gets all the Content Indexing policies associated to the commcell specified by commcell object.

            Returns:
                 dict    -   consists of all Configuration policies of the commcell
                            {
                                &#34;ci_policy1_name&#34;: [ci_policy1_id, ci_policy_type],

                                &#34;ci_policy2_name&#34;: [ci_policy2_id, ci_policy_type]
                            }

            Raises:
                SDKException:
                        if response is empty

                        if response is not success

                &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._POLICY_FS)

        if flag:
            policies_dict = {}
            if response.json() and &#39;policies&#39; in response.json():
                policies = response.json()[&#39;policies&#39;]
                for policy in policies:
                    temp_name = policy[&#39;policyEntity&#39;][&#39;policyName&#39;].lower()
                    temp_id = str(policy[&#39;policyEntity&#39;][&#39;policyId&#39;]).lower()
                    temp_policy_type = str(policy[&#39;detail&#39;][&#39;filePolicy&#39;]
                                          [&#39;filePolicyType&#39;]).lower()
                    policies_dict[temp_name] = [temp_id, temp_policy_type]
            return policies_dict
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_policy_id(self, policy_name):

        if not isinstance(policy_name, str):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)
        if policy_name.lower() in self._policies:
            return self._policies[policy_name.lower()][0]
        if policy_name.lower() in self._ci_policies:
            return self._ci_policies[policy_name.lower()][0]

    def get(self, configuration_policy_name, policy_type):
        &#34;&#34;&#34;Returns a ConfigurationPolicy object of the specified Configuration policy name.

            Args:
                configuration_policy_name     (str)   --  name of the configuration policy
                policy_type                    (str)   --  type of the policy

            Returns:
                object - instance of the ConfigurationPolicy class for the given policy name

            Raises:
                SDKException:
                    if type of the Configuration policy name argument is not string

                    if no Configuration policy exists with the given name
        &#34;&#34;&#34;
        if not isinstance(configuration_policy_name, str):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)

        if self.has_policy(configuration_policy_name):
            return ConfigurationPolicy(
                self._commcell_object, configuration_policy_name, self._get_policy_id(
                    configuration_policy_name)
            )

        else:
            raise SDKException(
                &#39;ConfigurationPolicies&#39;, &#39;102&#39;, &#39;No policy exists with name: {0}&#39;.format(
                    configuration_policy_name)
            )

    def get_policy_object(self, policy_type, configuration_policy_name):
        &#34;&#34;&#34;Get a  Policy object based on policy type

            Args:
                policy_type                 (str)   --  type of policy to create the object of

                    Valid values are:

                        - Archive

                        - Cleanup

                        - Retention

                        - Journal

                        - Content Indexing

                configuration_policy_name   (str)   --  name of the configuration Policy

            Returns:
                object  -   instance of the appropriate Policy class


        &#34;&#34;&#34;

        policy_types = {
            &#34;Archive&#34;: ArchivePolicy,
            &#34;Journal&#34;: JournalPolicy,
            &#34;Cleanup&#34;: CleanupPolicy,
            &#34;Retention&#34;: RetentionPolicy,
            &#34;ContentIndexing&#34;: ContentIndexingPolicy
        }

        try:
            return policy_types[policy_type](self._commcell_object, configuration_policy_name)
        except KeyError:
            raise SDKException(
                &#39;ConfigurationPolicies&#39;,
                &#39;102&#39;,
                &#39;Policy Type {} is not supported&#39;.format(policy_type)
            )

    def run_content_indexing(self, ci_policy_name):
        &#34;&#34;&#34;Runs Content indexing job from the CI policy level

            Args:
                ci_policy_name      -       Content indexing policy name

            Returns:
                Job                 -       Job class object for the CI Job

            Raises:
                SDKException:
                    No CI policy exists     -   if given policy name does not exist
                    Failed to run CI job    -   if CI job failed to start
                    Response was not success
                    Response received is empty

        &#34;&#34;&#34;
        if not self.has_policy(ci_policy_name):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, f&#39;No CI policy exists with name: {ci_policy_name}&#39;)
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;policyType&#34;: 0,
                    &#34;taskId&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 5022
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: {
                                    &#34;fileAnalytics&#34;: False,
                                    &#34;subClientBasedAnalytics&#34;: False
                                },
                                &#34;contentIndexingPolicyOption&#34;: {
                                    &#34;policyId&#34;: int(self._get_policy_id(ci_policy_name)),
                                    &#34;policyName&#34;: ci_policy_name,
                                    &#34;policyDetailType&#34;: 5,
                                    &#34;policyType&#34;: 2
                                }
                            }
                        }
                    }
                ]
            }
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_TASK, request_json)
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Content Index job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, o_str)
                raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, &#39;Failed to run the content indexing job&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, configuration_policy_name):
        &#34;&#34;&#34;Deletes a Configuration policy from the commcell.

            Args:
                configuration_policy_name (str)  --  name of the configuration policy to delete

            Raises:
                SDKException:
                    if type of the configuration policy name argument is not string

                    if failed to delete configuration policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(configuration_policy_name, str):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)

        if self.has_policy(configuration_policy_name):
            policy_delete_service = self._services[&#39;DELETE_CONFIGURATION_POLICY&#39;] % (
                str(self._get_policy_id(configuration_policy_name)))

            flag, response = self._cvpysdk_object.make_request(
                &#39;DELETE&#39;, policy_delete_service
            )

            if flag:
                try:
                    if response.json():
                        if response.json()[&#39;errorCode&#39;] != 0:
                            error_message = response.json()[&#39;errorMessage&#39;]
                            o_str = &#39;Failed to delete Configuration policy\nError: &#34;{0}&#34;&#39;

                            raise SDKException(
                                &#39;ConfigurationPolicies&#39;, &#39;102&#39;, o_str.format(error_message))
                except ValueError:
                    if response.text:
                        self.refresh()
                        return response.text.strip()
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;ConfigurationPolicies&#39;, &#39;102&#39;, &#39;No policy exists with name: {0}&#39;.format(
                    configuration_policy_name)
            )

    def add_policy(self, policy_object):
        &#34;&#34;&#34;Adds a new Configuration Policy to the Commcell.

            Args:
                policy_object(object)         --  policy onject based on type
                                                    of policy
            Raises:
                SDKException:
                    if failed to create configuration policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        json = policy_object._initialize_policy_json()
        configuration_policy_name = policy_object.name.lower()

        create_configuration_policy = self._services[&#39;CREATE_CONFIGURATION_POLICIES&#39;]

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, create_configuration_policy, json
        )

        if flag:
            if response.json():
                if &#39;policy&#39; in response.json():
                    # initialize the policies again
                    # so the policies object has all the policies
                    self.refresh()
                    return ConfigurationPolicy(
                        self._commcell_object, configuration_policy_name,
                        self._get_policy_id(configuration_policy_name)
                    )
                elif &#39;error&#39; in response.json():
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create Configuration policy\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the Virtual Machine policies.&#34;&#34;&#34;
        self._policies = self._get_policies()
        self._ci_policies = self._get_ci_policies()


class ConfigurationPolicy(object):

    &#34;&#34;&#34;Class for representing a single Configuration Policy. Contains method definitions for
        common operations among all Configuration Policies&#34;&#34;&#34;

    def __init__(self, commcell_object, configuration_policy_name, configuration_policy_id=None):
        &#34;&#34;&#34;
        Initialize object of the ConfigurationPolicy class.
            Args:
                commcell_object    (object)  --  instance of the Commcell class
                configuration_policy_name     (str)     --
                configuration_policy_id       (int)     --
            Returns:
                object - instance of the ConfigurationPolicies class
        &#34;&#34;&#34;

        self._configuration_policy_name = configuration_policy_name
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        if configuration_policy_id:
            self._configuration_policy_id = str(configuration_policy_id)
        else:
            self._configuration_policy_id = self._get_configuration_policy_id()

        self._CONGIGURATION_POLICY = self._services[&#39;GET_CONFIGURATION_POLICY&#39;] % (
            self._configuration_policy_id
        )

    @property
    def configuration_policy_id(self):
        &#34;&#34;&#34;Treats the configuration policy id as a read-only attribute.&#34;&#34;&#34;
        return self._configuration_policy_id

    @property
    def configuration_policy_name(self):
        &#34;&#34;&#34;Treats the configuration policy name as a read-only attribute.&#34;&#34;&#34;
        return self._configuration_policy_name

    def _get_configuration_policy_id(self):
        &#34;&#34;&#34;Gets the Configuration policy id asscoiated with the Configuration policy&#34;&#34;&#34;

        configuration_policies = ConfigurationPolicies(self._commcell_object)
        return configuration_policies._get_policy_id(self._configuration_policy_name)


class ArchivePolicy():
    &#34;&#34;&#34;Class for performing Archive policy operations for a specific archive policy&#34;&#34;&#34;

    def __init__(self, commcell_object, archive_policy_name):
        &#34;&#34;&#34;Initialise the Archive Policy class instance.&#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._name = archive_policy_name
        self._email_policy_type = 1
        self._archive_mailbox = False
        self._backup_deleted_item_retention = False
        self._backup_stubs = False
        self._disabled_mailbox = True
        self._enable_mailbox_quota = False
        self._include_messages_larger_than = 0
        self._include_messages_older_than = 0
        self._include_messages_with_attachements = False
        self._primary_mailbox = True
        self._include_discovery_holds_folder = False
        self._include_purges_folder = False
        self._include_version_folder = False
        self._save_conversation_meta_data = False
        self._include_categories = False
        self._skip_mailboxes_exceeded_quota = 10240
        self._include_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items&#34;
        self._exclude_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items,Junk Mail,Sync Issues&#34;
        self._exclude_message_class_filter = &#34;Appointments,Contacts,Schedules,Tasks&#34;
        self._content_index_behind_alert = False
        self._content_index_data_over = 0
        self._deferred_days = 0
        self._enable_content_index = False
        self._enable_deferred_days = False
        self._enable_preview_generation = False
        self._jobs_older_than = 0
        self._retention_days_for_ci = -1
        self._start_time = 0
        self._synchronize_on = False
        self._path = &#34;&#34;
        self._username = &#34;&#34;
        self._password = &#34;&#34;
        # self._initialize_archive_policy_properties()

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        &#34;&#34;&#34;Sets the name of the policy&#34;&#34;&#34;
        self._name = name

    @property
    def email_policy_type(self):
        &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
        return self._email_policy_type

    @property
    def archive_mailbox(self):
        &#34;&#34;&#34;Treats the archive_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._archive_mailbox

    @archive_mailbox.setter
    def archive_mailbox(self, archive_mailbox):
        &#34;&#34;&#34;Enable/Disable archive_mailbox option for policy&#34;&#34;&#34;
        self._archive_mailbox = archive_mailbox

    @property
    def backup_deleted_item_retention(self):
        &#34;&#34;&#34;Treats the backup_deleted_item_retention as a read-only attribute.&#34;&#34;&#34;
        return self._backup_deleted_item_retention

    @backup_deleted_item_retention.setter
    def backup_deleted_item_retention(self, backup_deleted_item_retention):
        &#34;&#34;&#34;Enable/Disable backup deleted item retention&#34;&#34;&#34;
        self._backup_deleted_item_retention = backup_deleted_item_retention

    @property
    def backup_stubs(self):
        &#34;&#34;&#34;Treats the backup_stubs as a read-only attribute.&#34;&#34;&#34;
        return self._backup_stubs

    @backup_stubs.setter
    def backup_stubs(self, backup_stubs):
        &#34;&#34;&#34;Sets backup stubs option on policy&#34;&#34;&#34;
        self._backup_stubs = backup_stubs

    @property
    def disabled_mailbox(self):
        &#34;&#34;&#34;Treats the disabled_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._disabled_mailbox

    @disabled_mailbox.setter
    def disabled_mailbox(self, disabled_mailbox):
        &#34;&#34;&#34;Enable/Disable disable mailbox on policy&#34;&#34;&#34;
        self._disabled_mailbox = disabled_mailbox

    @property
    def enable_mailbox_quota(self):
        &#34;&#34;&#34;Treats the enable_mailbox_quota as a read-only attribute.&#34;&#34;&#34;
        return self._enable_mailbox_quota

    @enable_mailbox_quota.setter
    def enable_mailbox_quota(self, enable_mailbox_quota):
        &#34;&#34;&#34;Sets the mailbox quota value&#34;&#34;&#34;
        self._enable_mailbox_quota = enable_mailbox_quota

    @property
    def include_messages_larger_than(self):
        &#34;&#34;&#34;Treats the include_messages_larger_than as a read-only attribute.&#34;&#34;&#34;
        return self._include_messages_larger_than

    @include_messages_larger_than.setter
    def include_messages_larger_than(self, include_messages_larger_than):
        &#34;&#34;&#34;Sets the message rule include message larger than&#34;&#34;&#34;
        self._include_messages_larger_than = include_messages_larger_than

    @property
    def include_messages_older_than(self):
        &#34;&#34;&#34;Treats the include_messages_older_than as a read-only attribute.&#34;&#34;&#34;
        return self._include_messages_older_than

    @include_messages_older_than.setter
    def include_messages_older_than(self, include_messages_older_than):
        &#34;&#34;&#34;Sets the message rule include messages older than&#34;&#34;&#34;
        self._include_messages_older_than = include_messages_older_than

    @property
    def include_messages_with_attachements(self):
        &#34;&#34;&#34;Treats the include_messages_with_attachements as a read-only attribute.&#34;&#34;&#34;
        return self._include_messages_with_attachements

    @include_messages_with_attachements.setter
    def include_messages_with_attachements(self, include_messages_with_attachements):
        &#34;&#34;&#34;Sets the message rule include messages with attachments&#34;&#34;&#34;
        self._include_messages_with_attachements = include_messages_with_attachements

    @property
    def primary_mailbox(self):
        &#34;&#34;&#34;Treats the primary_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._primary_mailbox

    @primary_mailbox.setter
    def primary_mailbox(self, primary_mailbox):
        &#34;&#34;&#34;Enable/Disable primary mailbox on policy &#34;&#34;&#34;
        self._primary_mailbox = primary_mailbox

    @property
    def skip_mailboxes_exceeded_quota(self):
        &#34;&#34;&#34;Treats the skip_mailboxes_exceeded_quota as a read-only attribute.&#34;&#34;&#34;
        return self._skip_mailboxes_exceeded_quota

    @skip_mailboxes_exceeded_quota.setter
    def skip_mailboxes_exceeded_quota(self, skip_mailboxes_exceeded_quota):
        &#34;&#34;&#34;Sets the mailbox exceeded quota value&#34;&#34;&#34;
        self._skip_mailboxes_exceeded_quota = skip_mailboxes_exceeded_quota

    @property
    def include_discovery_holds_folder(self):
        &#34;&#34;&#34;Treats the include_discovery_holds_folder as a read-only attribute.&#34;&#34;&#34;
        return self._include_discovery_holds_folder

    @include_discovery_holds_folder.setter
    def include_discovery_holds_folder(self, include_discovery_holds_folder):
        &#34;&#34;&#34;Enable/Disable disocvery hold folder&#34;&#34;&#34;
        self._include_discovery_holds_folder = include_discovery_holds_folder

    @property
    def include_purges_folder(self):
        &#34;&#34;&#34;Treats the include_purges_folder as a read-only attribute.&#34;&#34;&#34;
        return self._include_purges_folder

    @include_purges_folder.setter
    def include_purges_folder(self, include_purges_folder):
        &#34;&#34;&#34;Enable/Disable Purges folder&#34;&#34;&#34;
        self._include_purges_folder = include_purges_folder

    @property
    def include_version_folder(self):
        &#34;&#34;&#34;Treats the include_version_folder as a read-only attribute.&#34;&#34;&#34;
        return self._include_version_folder

    @include_version_folder.setter
    def include_version_folder(self, include_version_folder):
        &#34;&#34;&#34;Enable/Disable versions folder&#34;&#34;&#34;
        self._include_version_folder = include_version_folder

    @property
    def save_conversation_meta_data(self):
        &#34;&#34;&#34;Treats the save_conversation_meta_data as a read-only attribute.&#34;&#34;&#34;
        return self._save_conversation_meta_data

    @save_conversation_meta_data.setter
    def save_conversation_meta_data(self, save_conversation_meta_data):
        &#34;&#34;&#34;sets the save conversation meta data&#34;&#34;&#34;
        self._save_conversation_meta_data = save_conversation_meta_data

    @property
    def include_categories(self):
        &#34;&#34;&#34;Treats the include_categories as a read-only attribute.&#34;&#34;&#34;
        return self._include_categories

    @include_categories.setter
    def include_categories(self, include_categories):
        &#34;&#34;&#34;sets the include categories option on policy&#34;&#34;&#34;
        self._include_categories = include_categories

    @property
    def include_folder_filter(self):
        &#34;&#34;&#34;Treats the include_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._include_folder_filter

    @include_folder_filter.setter
    def include_folder_filter(self, include_folder_filter):
        &#34;&#34;&#34;sets include folder filter on policy&#34;&#34;&#34;
        self._include_folder_filter = include_folder_filter

    @property
    def exclude_folder_filter(self):
        &#34;&#34;&#34;Treats the exclude_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_folder_filter

    @exclude_folder_filter.setter
    def exclude_folder_filter(self, exclude_folder_filter):
        &#34;&#34;&#34;sets exclude folder filter on policy&#34;&#34;&#34;
        self._exclude_folder_filter = exclude_folder_filter

    @property
    def exclude_message_class_filter(self):
        &#34;&#34;&#34;Treats the exclude_message_class_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_message_class_filter

    @exclude_message_class_filter.setter
    def exclude_message_class_filter(self, exclude_message_class_filter):
        &#34;&#34;&#34;sets message class filters on policy&#34;&#34;&#34;
        self._exclude_message_class_filter = exclude_message_class_filter

    @property
    def content_index_behind_alert(self):
        &#34;&#34;&#34;Treats the content_index_behind_alert as a read-only attribute.&#34;&#34;&#34;
        return self._content_index_behind_alert

    @content_index_behind_alert.setter
    def content_index_behind_alert(self, content_index_behind_alert):
        &#34;&#34;&#34;sets content index alert&#34;&#34;&#34;
        self._content_index_behind_alert = content_index_behind_alert

    @property
    def content_index_data_over(self):
        &#34;&#34;&#34;Treats the content_index_data_over as a read-only attribute.&#34;&#34;&#34;
        return self._content_index_data_over

    @content_index_data_over.setter
    def content_index_data_over(self, content_index_data_over):
        &#34;&#34;&#34;sets content Index data over value&#34;&#34;&#34;
        self._content_index_data_over = content_index_data_over

    @property
    def deferred_days(self):
        &#34;&#34;&#34;Treats the deferred_days as a read-only attribute.&#34;&#34;&#34;
        return self._deferred_days

    @deferred_days.setter
    def deferred_days(self, deferred_days):
        &#34;&#34;&#34;sets deferred days&#34;&#34;&#34;
        self._deferred_days = deferred_days

    @property
    def enable_content_index(self):
        &#34;&#34;&#34;Treats the enable_content_index as a read-only attribute.&#34;&#34;&#34;
        return self._enable_content_index

    @enable_content_index.setter
    def enable_content_index(self, enable_content_index):
        &#34;&#34;&#34;Enable/Disable ContentIndex&#34;&#34;&#34;
        self._enable_content_index = enable_content_index

    @property
    def enable_deferred_days(self):
        &#34;&#34;&#34;Treats the enable_deferred_days as a read-only attribute.&#34;&#34;&#34;
        return self._enable_deferred_days

    @enable_deferred_days.setter
    def enable_deferred_days(self, enable_deferred_days):
        &#34;&#34;&#34;Enable/Disable deferred days&#34;&#34;&#34;
        self._enable_deferred_days = enable_deferred_days

    @property
    def enable_preview_generation(self):
        &#34;&#34;&#34;Treats the enable_preview_generation as a read-only attribute.&#34;&#34;&#34;
        return self._enable_preview_generation

    @enable_preview_generation.setter
    def enable_preview_generation(self, enable_preview_generation):
        &#34;&#34;&#34;Enable/Disable preview generation&#34;&#34;&#34;
        self._enable_preview_generation = enable_preview_generation

    @property
    def jobs_older_than(self):
        &#34;&#34;&#34;Treats the jobs_older_than as a read-only attribute.&#34;&#34;&#34;
        return self._jobs_older_than

    @jobs_older_than.setter
    def jobs_older_than(self, jobs_older_than):
        &#34;&#34;&#34;sets job older than value&#34;&#34;&#34;
        self._jobs_older_than = jobs_older_than

    @property
    def retention_days_for_ci(self):
        &#34;&#34;&#34;Treats the retention_days_for_ci as a read-only attribute.&#34;&#34;&#34;
        return self._retention_days_for_ci

    @retention_days_for_ci.setter
    def retention_days_for_ci(self, retention_days_for_ci):
        &#34;&#34;&#34;sets retention for ContentIndex&#34;&#34;&#34;
        self._retention_days_for_ci = retention_days_for_ci

    @property
    def start_time(self):
        &#34;&#34;&#34;Treats the start_time as a read-only attribute.&#34;&#34;&#34;
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        &#34;&#34;&#34;sets start time&#34;&#34;&#34;
        self._start_time = start_time

    @property
    def synchronize_on(self):
        &#34;&#34;&#34;Treats the synchronize_on as a read-only attribute.&#34;&#34;&#34;
        return self._synchronize_on

    @synchronize_on.setter
    def synchronize_on(self, synchronize_on):
        &#34;&#34;&#34;sets synchronize on for ContentIndex&#34;&#34;&#34;
        self._synchronize_on = synchronize_on

    @property
    def path(self):
        &#34;&#34;&#34;Treats the path as a read-only attribute.&#34;&#34;&#34;
        return self._path

    @path.setter
    def path(self, path):
        &#34;&#34;&#34;sets previewpath for ContentIndex&#34;&#34;&#34;
        self._path = path

    @property
    def username(self):
        &#34;&#34;&#34;Treats the username as a read-only attribute.&#34;&#34;&#34;
        return self._username

    @username.setter
    def username(self, username):
        &#34;&#34;&#34;sets username for ContentIndex&#34;&#34;&#34;
        self._username = username

    @property
    def password(self):
        &#34;&#34;&#34;Treats the password as a read-only attribute.&#34;&#34;&#34;
        return self._password

    @password.setter
    def password(self, password):
        self._password = password

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;

        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 1,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 137
                },
                &#34;detail&#34;: {
                    &#34;emailPolicy&#34;: {
                        &#34;emailPolicyType&#34;: 1,
                        &#34;archivePolicy&#34;: {
                            &#34;includeMsgsLargerThan&#34;: self._include_messages_larger_than,
                            &#34;skipMailBoxesExceededQuota&#34;: self._skip_mailboxes_exceeded_quota,
                            &#34;backupDeletedItemRetention&#34;: self._backup_deleted_item_retention,
                            &#34;primaryMailbox&#34;: self._primary_mailbox,
                            &#34;includeMsgsOlderThan&#34;: self._include_messages_older_than,
                            &#34;archiveMailbox&#34;: self._archive_mailbox,
                            &#34;disabledMailbox&#34;: self._disabled_mailbox,
                            &#34;backupStubs&#34;: self._backup_stubs,
                            &#34;enableMailBoxQuota&#34;: self._enable_mailbox_quota,
                            &#34;includeOnlyMsgsWithAttachemts&#34;: self._include_messages_with_attachements,
                            &#34;includeDiscoveryHoldsFolder&#34;: self._include_discovery_holds_folder,
                            &#34;includePurgesFolder&#34;: self._include_purges_folder,
                            &#34;includeVersionsFolder&#34;: self._include_version_folder,
                            &#34;saveConversationMetaData&#34;: self._save_conversation_meta_data,
                            &#34;includeCategories&#34;: self._include_categories,
                            &#34;includeFolderFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            },
                            &#34;excludeFolderFilter&#34;: {
                                &#34;folderPatternsSelected&#34;: [
                                    &#34;Junk Mail&#34;,
                                    &#34;Sync Issues&#34;
                                ],
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            },
                            &#34;contentIndexProps&#34;: {
                                &#34;enableContentIndex&#34;: self._enable_content_index,
                                &#34;contentIndexBehindAlert&#34;: self._content_index_behind_alert,
                                &#34;synchronizeOn&#34;: self._synchronize_on,
                                &#34;contentIndexDataOver&#34;: self._content_index_data_over,
                                &#34;retentionDaysForCI&#34;: self._retention_days_for_ci,
                                &#34;startTime&#34;: self._start_time,
                                &#34;jobsOlderThan&#34;: self._jobs_older_than,
                                &#34;enablePreviewGeneration&#34;: self._enable_preview_generation,
                                &#34;deferredDays&#34;: self._deferred_days,
                                &#34;enableDeferredDays&#34;: self._enable_deferred_days,
                                &#34;pattern&#34;: [
                                    {}
                                ],
                                &#34;previewPathDir&#34;: {
                                    &#34;path&#34;: self._path,
                                    &#34;userAccount&#34;: {
                                        &#34;userName&#34;: self._username,
                                        &#34;password&#34;: self._password
                                    }
                                }
                            },
                            &#34;excludeMessageClassFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Appointments&#34;,
                                    &#34;Contacts&#34;,
                                    &#34;Schedules&#34;,
                                    &#34;Tasks&#34;
                                ]
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self._name
                }
            }
        }

        return policy_json


class JournalPolicy():

    &#34;&#34;&#34;Class for performing Journal policy operations for a specific journal policy&#34;&#34;&#34;

    def __init__(self, commcell_object, journal_policy_name):
        &#34;&#34;&#34;Initialise the Journal Policy class instance.&#34;&#34;&#34;

        self._commcell_object = commcell_object

        self._name = journal_policy_name
        self._commserver = commcell_object
        self._email_policy_type = 4
        self._complete_job_mapi_error = 0
        self._delete_archived_messages = True
        self._job_hours_run = 0
        self._job_messages_protected = 1
        self._include_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items&#34;
        self._exclude_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items,Junk Mail,Sync Issues&#34;
        self._exclude_message_class_filter = &#34;Appointments,Contacts,Schedules,Tasks&#34;
        self._content_index_behind_alert = False
        self._content_index_data_over = 0
        self._deferred_days = 0
        self._enable_content_index = False
        self._enable_deferred_days = False
        self._enable_preview_generation = False
        self._jobs_older_than = 0
        self._retention_days_for_ci = -1
        self._start_time = 0
        self._synchronize_on = False
        self._path = &#34;&#34;
        self._username = &#34;&#34;
        self._password = &#34;&#34;

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        self._name = name

    @property
    def email_policy_type(self):
        &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
        return self._email_policy_type

    @property
    def complete_job_mapi_error(self):
        &#34;&#34;&#34;Treats the complete_job_mapi_error as a read-only attribute.&#34;&#34;&#34;
        return self._complete_job_mapi_error

    @complete_job_mapi_error.setter
    def complete_job_mapi_error(self, complete_job_mapi_error):
        self._complete_job_mapi_error = complete_job_mapi_error

    @property
    def delete_archived_messages(self):
        &#34;&#34;&#34;Treats the delete_archived_messages as a read-only attribute.&#34;&#34;&#34;
        return self._delete_archived_messages

    @delete_archived_messages.setter
    def delete_archived_messages(self, delete_archived_messages):
        self._delete_archived_messages = delete_archived_messages

    @property
    def job_hours_run(self):
        &#34;&#34;&#34;Treats the job_hours_run as a read-only attribute.&#34;&#34;&#34;
        return self._job_hours_run

    @job_hours_run.setter
    def job_hours_run(self, job_hours_run):
        self._job_hours_run = job_hours_run

    @property
    def job_messages_protected(self):
        &#34;&#34;&#34;Treats the job_messages_protected as a read-only attribute.&#34;&#34;&#34;
        return self._job_messages_protected

    @job_messages_protected.setter
    def job_messages_protected(self, job_messages_protected):
        self._job_messages_protected = job_messages_protected

    @property
    def include_folder_filter(self):
        &#34;&#34;&#34;Treats the include_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._include_folder_filter

    @include_folder_filter.setter
    def include_folder_filter(self, include_folder_filter):
        self._include_folder_filter = include_folder_filter

    @property
    def exclude_folder_filter(self):
        &#34;&#34;&#34;Treats the exclude_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_folder_filter

    @exclude_folder_filter.setter
    def exclude_folder_filter(self, exclude_folder_filter):
        self._exclude_folder_filter = exclude_folder_filter

    @property
    def exclude_message_class_filter(self):
        &#34;&#34;&#34;Treats the exclude_message_class_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_message_class_filter

    @exclude_message_class_filter.setter
    def exclude_message_class_filter(self, exclude_message_class_filter):
        self._exclude_message_class_filter = exclude_message_class_filter

    @property
    def content_index_behind_alert(self):
        &#34;&#34;&#34;Treats the content_index_behind_alert as a read-only attribute.&#34;&#34;&#34;
        return self._content_index_behind_alert

    @content_index_behind_alert.setter
    def content_index_behind_alert(self, content_index_behind_alert):
        self._content_index_behind_alert = content_index_behind_alert

    @property
    def content_index_data_over(self):
        &#34;&#34;&#34;Treats the content_index_data_over as a read-only attribute.&#34;&#34;&#34;
        return self._content_index_data_over

    @content_index_data_over.setter
    def content_index_data_over(self, content_index_data_over):
        self._content_index_data_over = content_index_data_over

    @property
    def deferred_days(self):
        &#34;&#34;&#34;Treats the deferred_days as a read-only attribute.&#34;&#34;&#34;
        return self._deferred_days

    @deferred_days.setter
    def deferred_days(self, deferred_days):
        self._deferred_days = deferred_days

    @property
    def enable_content_index(self):
        &#34;&#34;&#34;Treats the enable_content_index as a read-only attribute.&#34;&#34;&#34;
        return self._enable_content_index

    @enable_content_index.setter
    def enable_content_index(self, enable_content_index):
        self._enable_content_index = enable_content_index

    @property
    def enable_deferred_days(self):
        &#34;&#34;&#34;Treats the enable_deferred_days as a read-only attribute.&#34;&#34;&#34;
        return self._enable_deferred_days

    @enable_deferred_days.setter
    def enable_deferred_days(self, enable_deferred_days):
        self._enable_deferred_days = enable_deferred_days

    @property
    def enable_preview_generation(self):
        &#34;&#34;&#34;Treats the enable_preview_generation as a read-only attribute.&#34;&#34;&#34;
        return self.enable_preview_generation

    @enable_preview_generation.setter
    def enable_preview_generation(self, enable_preview_generation):
        self._enable_preview_generation = enable_preview_generation

    @property
    def jobs_older_than(self):
        &#34;&#34;&#34;Treats the jobs_older_than as a read-only attribute.&#34;&#34;&#34;
        return self._jobs_older_than

    @jobs_older_than.setter
    def jobs_older_than(self, jobs_older_than):
        self._jobs_older_than = jobs_older_than

    @property
    def retention_days_for_ci(self):
        &#34;&#34;&#34;Treats the retention_days_for_ci as a read-only attribute.&#34;&#34;&#34;
        return self._retention_days_for_ci

    @retention_days_for_ci.setter
    def retention_days_for_ci(self, retention_days_for_ci):
        self._retention_days_for_ci = retention_days_for_ci

    @property
    def start_time(self):
        &#34;&#34;&#34;Treats the start_time as a read-only attribute.&#34;&#34;&#34;
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        self._start_time = start_time

    @property
    def synchronize_on(self):
        &#34;&#34;&#34;Treats the synchronize_on as a read-only attribute.&#34;&#34;&#34;
        return self._synchronize_on

    @synchronize_on.setter
    def synchronize_on(self, synchronize_on):
        self._synchronize_on = synchronize_on

    @property
    def path(self):
        &#34;&#34;&#34;Treats the path as a read-only attribute.&#34;&#34;&#34;
        return self._path

    @path.setter
    def path(self, path):
        self._path = path

    @property
    def username(self):
        &#34;&#34;&#34;Treats the username as a read-only attribute.&#34;&#34;&#34;
        return self._username

    @username.setter
    def username(self, username):
        self._username = username

    @property
    def password(self):
        &#34;&#34;&#34;Treats the password as a read-only attribute.&#34;&#34;&#34;
        return self._password

    @password.setter
    def password(self, password):
        self._password = password

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;
        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 1,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 137
                },
                &#34;detail&#34;: {
                    &#34;emailPolicy&#34;: {
                        &#34;emailPolicyType&#34;: 4,
                        &#34;journalPolicy&#34;: {
                            &#34;deleteArchivedMessages&#34;: self._delete_archived_messages,
                            &#34;contentIndexProps&#34;: {
                                &#34;enableContentIndex&#34;: self._enable_content_index,
                                &#34;contentIndexBehindAlert&#34;: self.content_index_behind_alert,
                                &#34;synchronizeOn&#34;: self._synchronize_on,
                                &#34;contentIndexDataOver&#34;: self._content_index_data_over,
                                &#34;retentionDaysForCI&#34;: -self._retention_days_for_ci,
                                &#34;startTime&#34;: self._start_time,
                                &#34;jobsOlderThan&#34;: self._jobs_older_than,
                                &#34;enablePreviewGeneration&#34;: self._enable_preview_generation,
                                &#34;deferredDays&#34;: self._deferred_days,
                                &#34;enableDeferredDays&#34;: self._enable_deferred_days,
                                &#34;pattern&#34;: [
                                    {}
                                ],
                                &#34;previewPathDir&#34;: {
                                    &#34;path&#34;: self._path,
                                    &#34;userAccount&#34;: {
                                        &#34;userName&#34;: self._username,
                                        &#34;password&#34;: self._password

                                    }
                                }
                            },
                            &#34;excludeMessageClassFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Appointments&#34;,
                                    &#34;Contacts&#34;,
                                    &#34;Schedules&#34;,
                                    &#34;Tasks&#34;
                                ]
                            },
                            &#34;includeFolderFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            },
                            &#34;excludeFolderFilter&#34;: {
                                &#34;folderPatternsSelected&#34;: [
                                    &#34;Junk Mail&#34;,
                                    &#34;Sync Issues&#34;
                                ],
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self._name
                }
            }
        }

        return policy_json


class CleanupPolicy():

    &#34;&#34;&#34;Class for performing Cleanup policy operations for a specific cleanup policy&#34;&#34;&#34;

    def __init__(self, commcell_object, cleanup_policy_name):
        &#34;&#34;&#34;Initialise the cleanup Policy class instance.&#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._name = cleanup_policy_name
        self._email_policy_type = 2
        self._add_recall_link = True
        self._archive_if_size = 90
        self._archive_mailbox = False
        self._collect_messages_with_attachments = False
        self._collect_messages_days_after = 0
        self._collect_messages_larger_than = 0
        self._create_stubs = True
        self._disabled_mailbox = True
        self._enable_message_rules = True
        self._leave_message_body = True
        self._mailbox_quota = False
        self._number_of_days_for_source_pruning = 730
        self._primary_mailbox = True
        self._prune_erased_messages_or_stubs = False
        self._prune_messages = False
        self._prune_stubs = False
        self._skip_unread_messages = False
        self._stop_archive_if_size = 75
        self._truncate_body = False
        self._truncate_body_to_bytes = 1024
        self._used_disk_space = False
        self._used_disk_space_value = 50
        self._include_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items&#34;
        self._exclude_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items,Junk Mail,Sync Issues&#34;
        self._exclude_message_class_filter = &#34;Appointments,Contacts,Schedules,Tasks&#34;

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        self._name = name

    @property
    def email_policy_type(self):
        &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
        return self._email_policy_type

    @property
    def add_recall_link(self):
        &#34;&#34;&#34;Treats the add_recall_link as a read-only attribute.&#34;&#34;&#34;
        return self._add_recall_link

    @add_recall_link.setter
    def add_recall_link(self, add_recall_link):
        self._add_recall_link = add_recall_link

    @property
    def archive_if_size(self):
        &#34;&#34;&#34;Treats the archive_if_size as a read-only attribute.&#34;&#34;&#34;
        return self._archive_if_size

    @archive_if_size.setter
    def archive_if_size(self, archive_if_size):
        self._archive_if_size = archive_if_size

    @property
    def archive_mailbox(self):
        &#34;&#34;&#34;Treats the archive_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._archive_mailbox

    @archive_mailbox.setter
    def archive_mailbox(self, archive_mailbox):
        self._archive_mailbox = archive_mailbox

    @property
    def collect_messages_with_attachments(self):
        &#34;&#34;&#34;Treats the collect_messages_with_attachments as a read-only attribute.&#34;&#34;&#34;
        return self._collect_messages_with_attachments

    @collect_messages_with_attachments.setter
    def collect_messages_with_attachments(self, collect_messages_with_attachments):
        self._collect_messages_with_attachments = collect_messages_with_attachments

    @property
    def collect_messages_days_after(self):
        &#34;&#34;&#34;Treats the collect_messages_days_after as a read-only attribute.&#34;&#34;&#34;
        return self._collect_messages_days_after

    @collect_messages_days_after.setter
    def collect_messages_days_after(self, collect_messages_days_after):
        self._collect_messages_days_after = collect_messages_days_after

    @property
    def collect_messages_larger_than(self):
        &#34;&#34;&#34;Treats the collect_messages_larger_than as a read-only attribute.&#34;&#34;&#34;
        return self._collect_messages_larger_than

    @collect_messages_larger_than.setter
    def collect_messages_larger_than(self, collect_messages_larger_than):
        self._collect_messages_larger_than = collect_messages_larger_than

    @property
    def create_stubs(self):
        &#34;&#34;&#34;Treats the create_stubs as a read-only attribute.&#34;&#34;&#34;
        return self._create_stubs

    @create_stubs.setter
    def create_stubs(self, create_stubs):
        self._create_stubs = create_stubs

    @property
    def disabled_mailbox(self):
        &#34;&#34;&#34;Treats the disabled_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._disabled_mailbox

    @disabled_mailbox.setter
    def disabled_mailbox(self, disabled_mailbox):
        self._disabled_mailbox = disabled_mailbox

    @property
    def enable_message_rules(self):
        &#34;&#34;&#34;Treats the enable_message_rules as a read-only attribute.&#34;&#34;&#34;
        return self._enable_message_rules

    @enable_message_rules.setter
    def enable_message_rules(self, enable_message_rules):
        self._enable_message_rules = enable_message_rules

    @property
    def leave_message_body(self):
        &#34;&#34;&#34;Treats the leave_message_body as a read-only attribute.&#34;&#34;&#34;
        return self._leave_message_body

    @leave_message_body.setter
    def leave_message_body(self, leave_message_body):
        self._leave_message_body = leave_message_body

    @property
    def mailbox_quota(self):
        &#34;&#34;&#34;Treats the mailbox_quota as a read-only attribute.&#34;&#34;&#34;
        return self._mailbox_quota

    @mailbox_quota.setter
    def mailbox_quota(self, mailbox_quota):
        self._mailbox_quota = mailbox_quota

    @property
    def number_of_days_for_source_pruning(self):
        &#34;&#34;&#34;Treats the number_of_days_for_source_pruning as a read-only attribute.&#34;&#34;&#34;
        return self._number_of_days_for_source_pruning

    @number_of_days_for_source_pruning.setter
    def number_of_days_for_source_pruning(self, number_of_days_for_source_pruning):
        self._number_of_days_for_source_pruning = number_of_days_for_source_pruning

    @property
    def primary_mailbox(self):
        &#34;&#34;&#34;Treats the primary_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._primary_mailbox

    @primary_mailbox.setter
    def primary_mailbox(self, primary_mailbox):
        self._primary_mailbox = primary_mailbox

    @property
    def prune_erased_messages_or_stubs(self):
        &#34;&#34;&#34;Treats the prune_erased_messages_or_stubs as a read-only attribute.&#34;&#34;&#34;
        return self._prune_erased_messages_or_stubs

    @prune_erased_messages_or_stubs.setter
    def prune_erased_messages_or_stubs(self, prune_erased_messages_or_stubs):
        self._prune_erased_messages_or_stubs = prune_erased_messages_or_stubs

    @property
    def prune_messages(self):
        &#34;&#34;&#34;Treats the prune_messages as a read-only attribute.&#34;&#34;&#34;
        return self._prune_messages

    @prune_messages.setter
    def prune_messages(self, prune_messages):
        self._prune_messages = prune_messages

    @property
    def prune_stubs(self):
        &#34;&#34;&#34;Treats the prune_stubs as a read-only attribute.&#34;&#34;&#34;
        return self._prune_stubs

    @prune_stubs.setter
    def prune_stubs(self, prune_stubs):
        self._prune_stubs = prune_stubs

    @property
    def skip_unread_messages(self):
        &#34;&#34;&#34;Treats the skip_unread_messages as a read-only attribute.&#34;&#34;&#34;
        return self._skip_unread_messages

    @skip_unread_messages.setter
    def skip_unread_messages(self, skip_unread_messages):
        self._skip_unread_messages = skip_unread_messages

    @property
    def stop_archive_if_size(self):
        &#34;&#34;&#34;Treats the stop_archive_if_size as a read-only attribute.&#34;&#34;&#34;
        return self._stop_archive_if_size

    @stop_archive_if_size.setter
    def stop_archive_if_size(self, stop_archive_if_size):
        self._stop_archive_if_size = stop_archive_if_size

    @property
    def truncate_body(self):
        &#34;&#34;&#34;Treats the truncate_body as a read-only attribute.&#34;&#34;&#34;
        return self._truncate_body

    @truncate_body.setter
    def truncate_body(self, truncate_body):
        self._truncate_body = truncate_body

    @property
    def truncate_body_to_bytes(self):
        &#34;&#34;&#34;Treats the truncate_body_to_bytes as a read-only attribute.&#34;&#34;&#34;
        return self._truncate_body_to_bytes

    @truncate_body_to_bytes.setter
    def truncate_body_to_bytes(self, truncate_body_to_bytes):
        self._truncate_body_to_bytes = truncate_body_to_bytes

    @property
    def used_disk_space(self):
        &#34;&#34;&#34;Treats the used_disk_space as a read-only attribute.&#34;&#34;&#34;
        return self._used_disk_space

    @used_disk_space.setter
    def path(self, used_disk_space):
        self._used_disk_space = used_disk_space

    @property
    def used_disk_space_value(self):
        &#34;&#34;&#34;Treats the used_disk_space_value as a read-only attribute.&#34;&#34;&#34;
        return self._used_disk_space_value

    @used_disk_space_value.setter
    def used_disk_space_value(self, used_disk_space_value):
        self._used_disk_space_value = used_disk_space_value

    @property
    def include_folder_filter(self):
        &#34;&#34;&#34;Treats the include_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._include_folder_filter

    @include_folder_filter.setter
    def include_folder_filter(self, include_folder_filter):
        self._include_folder_filter = include_folder_filter

    @property
    def exclude_folder_filter(self):
        &#34;&#34;&#34;Treats the exclude_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_folder_filter

    @exclude_folder_filter.setter
    def exclude_folder_filter(self, exclude_folder_filter):
        self._exclude_folder_filter = exclude_folder_filter

    @property
    def exclude_message_class_filter(self):
        &#34;&#34;&#34;Treats the exclude_message_class_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_message_class_filter

    @exclude_message_class_filter.setter
    def exclude_message_class_filter(self, exclude_message_class_filter):
        self._exclude_message_class_filter = exclude_message_class_filter

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;

        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 1,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 137
                },
                &#34;detail&#34;: {
                    &#34;emailPolicy&#34;: {
                        &#34;emailPolicyType&#34;: 2,
                        &#34;cleanupPolicy&#34;: {
                            &#34;usedDiskSpace&#34;: self._used_disk_space,
                            &#34;createStubs&#34;: self._create_stubs,
                            &#34;usedDiskSpaceValue&#34;: self._used_disk_space_value,
                            &#34;pruneMsgs&#34;: self._prune_messages,
                            &#34;primaryMailbox&#34;: self._primary_mailbox,
                            &#34;disabledMailbox&#34;: self._disabled_mailbox,
                            &#34;pruneErasedMsgsOrStubs&#34;: self._prune_erased_messages_or_stubs,
                            &#34;collectMsgsDaysAfter&#34;: self._collect_messages_days_after,
                            &#34;numOfDaysForSourcePruning&#34;: self._number_of_days_for_source_pruning,
                            &#34;collectMsgsLargerThan&#34;: self._collect_messages_larger_than,
                            &#34;skipUnreadMsgs&#34;: self._skip_unread_messages,
                            &#34;collectMsgWithAttach&#34;: self._collect_messages_with_attachments,
                            &#34;leaveMsgBody&#34;: self._leave_message_body,
                            &#34;mailboxQuota&#34;: self.mailbox_quota,
                            &#34;truncateBody&#34;: self._truncate_body,
                            &#34;pruneStubs&#34;: self._prune_stubs,
                            &#34;enableMessageRules&#34;: self._enable_message_rules,
                            &#34;archiveMailbox&#34;: self._archive_mailbox,
                            &#34;archiveIfSize&#34;: self._archive_if_size,
                            &#34;truncateBodyToBytes&#34;: self._truncate_body_to_bytes,
                            &#34;addRecallLink&#34;: self._add_recall_link,
                            &#34;stopArchiveIfSize&#34;: self._stop_archive_if_size,
                            &#34;excludeMessageClassFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Appointments&#34;,
                                    &#34;Contacts&#34;,
                                    &#34;Schedules&#34;,
                                    &#34;Tasks&#34;
                                ]
                            },
                            &#34;includeFolderFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            },
                            &#34;excludeFolderFilter&#34;: {
                                &#34;folderPatternsSelected&#34;: [
                                    &#34;Junk Mail&#34;,
                                    &#34;Sync Issues&#34;
                                ],
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self._name
                }
            }
        }

        return policy_json


class RetentionPolicy():
    &#34;&#34;&#34;Class for performing Retention policy operations for a specific retention policy&#34;&#34;&#34;

    def __init__(self, commcell_object, retention_policy_name):
        &#34;&#34;&#34;Initialise the Rentention Policy class instance.&#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._name = retention_policy_name
        self._email_policy_type = 3
        self._number_of_days_for_media_pruning = -1
        self._retention_type = 0
        self._exchange_folder_retention = False
        self._exchange_retention_tags = False

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        self._name = name

    @property
    def email_policy_type(self):
        &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
        return self._email_policy_type

    @property
    def days_for_media_pruning(self):
        &#34;&#34;&#34;Treats the number_of_days_for_media_pruning as a read-only attribute.&#34;&#34;&#34;
        return self._number_of_days_for_media_pruning

    @days_for_media_pruning.setter
    def days_for_media_pruning(self, days_for_media_pruning):
        self._number_of_days_for_media_pruning = days_for_media_pruning

    @property
    def retention_type(self):
        &#34;&#34;&#34;Treats the retention_type as a read-only attribute.&#34;&#34;&#34;
        return self._retention_type

    @retention_type.setter
    def retention_type(self, retention_type):
        self._retention_type = retention_type

    @property
    def exchange_folder_retention(self):
        &#34;&#34;&#34;Treats the exchange_folder_retention as a read-only attribute.&#34;&#34;&#34;
        return self._exchange_folder_retention

    @exchange_folder_retention.setter
    def exchange_folder_retention(self, exchange_folder_retention):
        self._exchange_folder_retention = exchange_folder_retention

    @property
    def exchange_retention_tags(self):
        &#34;&#34;&#34;Treats the exchange_retention_tags as a read-only attribute.&#34;&#34;&#34;
        return self._exchange_retention_tags

    @exchange_retention_tags.setter
    def exchange_retention_tags(self, exchange_retention_tags):
        self._exchange_retention_tags = exchange_retention_tags

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;
        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 1,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 137
                },
                &#34;detail&#34;: {
                    &#34;emailPolicy&#34;: {
                        &#34;emailPolicyType&#34;: 3,
                        &#34;retentionPolicy&#34;: {
                            &#34;numOfDaysForMediaPruning&#34;: self.days_for_media_pruning,
                            &#34;type&#34;: self.retention_type,
                            &#34;advanceRetentionOption&#34;: {
                                &#34;bExchangeFoldersRetention&#34;: self.exchange_folder_retention,
                                &#34;bExchangeRetentionTags&#34;: self.exchange_retention_tags
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self.name
                }
            }
        }

        return policy_json


class ContentIndexingPolicy():
    &#34;&#34;&#34;Class for performing Content Indexing policy operations for a specific CI policy&#34;&#34;&#34;

    def __init__(self, commcell_object, ci_policy_name):
        &#34;&#34;&#34;Initialise the Content indexing Policy class instance.&#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._name = ci_policy_name
        self._file_policy_type = 5
        self._includeDocTypes = &#34;*.bmp,*.csv,*.doc,*.docx,*.dot,*.eml,*.htm,*.html,*.jpeg,*.jpg,&#34; \
                                &#34;*.log,*.msg,*.odg,*.odp,*.ods,*.odt,*.pages,*.pdf,*.png,*.ppt,&#34; \
                                &#34;*.pptx,*.rtf,*.txt,*.xls,*.xlsx,*.xmind,*.xml&#34;
        self._index_server_name = None
        self._data_access_node = None
        self._exclude_paths = [&#34;C:\\Program Files&#34;, &#34;C:\\Program Files (x86)&#34;, &#34;C:\\Windows&#34;]
        self._min_doc_size = 0
        self._max_doc_size = 50

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, value):
        &#34;&#34;&#34;Sets the value of name&#34;&#34;&#34;
        self._name = value

    @property
    def include_doc_types(self):
        &#34;&#34;&#34;Treats the include_doc_types as a read-only attribute.&#34;&#34;&#34;
        return self._includeDocTypes

    @include_doc_types.setter
    def include_doc_types(self, value):
        &#34;&#34;&#34;Sets the value of include docs type&#34;&#34;&#34;
        self._includeDocTypes = value

    @property
    def index_server_name(self):
        &#34;&#34;&#34;Treats the index_server_name as a read-only attribute.&#34;&#34;&#34;
        return self._index_server_name

    @index_server_name.setter
    def index_server_name(self, value):
        &#34;&#34;&#34;Sets the value of index server name&#34;&#34;&#34;
        self._index_server_name = value

    @property
    def data_access_node(self):
        &#34;&#34;&#34;Treats the data_access_node as a read-only attribute.&#34;&#34;&#34;
        return self._data_access_node

    @data_access_node.setter
    def data_access_node(self, value):
        &#34;&#34;&#34;Sets the value of data access node&#34;&#34;&#34;
        self._data_access_node = value

    @property
    def min_doc_size(self):
        &#34;&#34;&#34;Treats the min_doc_size as a read-only attribute.&#34;&#34;&#34;
        return self._min_doc_size

    @min_doc_size.setter
    def min_doc_size(self, value):
        &#34;&#34;&#34;Sets the value of minimum doc size&#34;&#34;&#34;
        self._min_doc_size = value

    @property
    def max_doc_size(self):
        &#34;&#34;&#34;Treats the max_doc_size as a read-only attribute.&#34;&#34;&#34;
        return self._max_doc_size

    @max_doc_size.setter
    def max_doc_size(self, value):
        &#34;&#34;&#34;Sets the value of maximum doc size&#34;&#34;&#34;
        self._max_doc_size = value

    @property
    def exclude_paths(self):
        &#34;&#34;&#34;Treats the exclude_paths as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_paths

    @exclude_paths.setter
    def exclude_paths(self, value):
        &#34;&#34;&#34;Sets the value of exclude paths&#34;&#34;&#34;
        self._exclude_paths = value

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;
        if not isinstance(self._index_server_name, str) or not isinstance(self._data_access_node, str) \
            or not isinstance(self._exclude_paths, list) or not isinstance(self._includeDocTypes, str) \
                or not isinstance(self._name, str) or not isinstance(self._min_doc_size, int) \
                or not isinstance(self._max_doc_size, int):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)
        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 2,
                &#34;flags&#34;: 0,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 33
                },
                &#34;detail&#34;: {
                    &#34;filePolicy&#34;: {
                        &#34;filePolicyType&#34;: self._file_policy_type,
                        &#34;contentIndexingPolicy&#34;: {
                            &#34;includeDocTypes&#34;: self._includeDocTypes,
                            &#34;copyPrecedence&#34;: 1,
                            &#34;minDocSize&#34;: self._min_doc_size,
                            &#34;searchEngineId&#34;: int(self._commcell_object.index_servers.
                                                  get(self._index_server_name).index_server_client_id),
                            &#34;contentIndexVersionsAfterNumberOfDays&#34;: -1,
                            &#34;maxDocSize&#34;: self._max_doc_size,
                            &#34;globalFilterFlag&#34;: 0,
                            &#34;excludePaths&#34;: self._exclude_paths,
                            &#34;dataAccessNodes&#34;: {
                                &#34;numberOfStreams&#34;: 0,
                                &#34;dataAccessNodes&#34;: [
                                    {
                                        &#34;clientName&#34;: self._data_access_node,
                                        &#34;clientId&#34;: int(self._commcell_object.clients.
                                                        get(self._data_access_node).client_id),
                                        &#34;_type_&#34;: 3
                                    }
                                ]
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self._name
                }
            }
        }
        return policy_json</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy"><code class="flex name class">
<span>class <span class="ident">ArchivePolicy</span></span>
<span>(</span><span>commcell_object, archive_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing Archive policy operations for a specific archive policy</p>
<p>Initialise the Archive Policy class instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L540-L995" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ArchivePolicy():
    &#34;&#34;&#34;Class for performing Archive policy operations for a specific archive policy&#34;&#34;&#34;

    def __init__(self, commcell_object, archive_policy_name):
        &#34;&#34;&#34;Initialise the Archive Policy class instance.&#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._name = archive_policy_name
        self._email_policy_type = 1
        self._archive_mailbox = False
        self._backup_deleted_item_retention = False
        self._backup_stubs = False
        self._disabled_mailbox = True
        self._enable_mailbox_quota = False
        self._include_messages_larger_than = 0
        self._include_messages_older_than = 0
        self._include_messages_with_attachements = False
        self._primary_mailbox = True
        self._include_discovery_holds_folder = False
        self._include_purges_folder = False
        self._include_version_folder = False
        self._save_conversation_meta_data = False
        self._include_categories = False
        self._skip_mailboxes_exceeded_quota = 10240
        self._include_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items&#34;
        self._exclude_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items,Junk Mail,Sync Issues&#34;
        self._exclude_message_class_filter = &#34;Appointments,Contacts,Schedules,Tasks&#34;
        self._content_index_behind_alert = False
        self._content_index_data_over = 0
        self._deferred_days = 0
        self._enable_content_index = False
        self._enable_deferred_days = False
        self._enable_preview_generation = False
        self._jobs_older_than = 0
        self._retention_days_for_ci = -1
        self._start_time = 0
        self._synchronize_on = False
        self._path = &#34;&#34;
        self._username = &#34;&#34;
        self._password = &#34;&#34;
        # self._initialize_archive_policy_properties()

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        &#34;&#34;&#34;Sets the name of the policy&#34;&#34;&#34;
        self._name = name

    @property
    def email_policy_type(self):
        &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
        return self._email_policy_type

    @property
    def archive_mailbox(self):
        &#34;&#34;&#34;Treats the archive_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._archive_mailbox

    @archive_mailbox.setter
    def archive_mailbox(self, archive_mailbox):
        &#34;&#34;&#34;Enable/Disable archive_mailbox option for policy&#34;&#34;&#34;
        self._archive_mailbox = archive_mailbox

    @property
    def backup_deleted_item_retention(self):
        &#34;&#34;&#34;Treats the backup_deleted_item_retention as a read-only attribute.&#34;&#34;&#34;
        return self._backup_deleted_item_retention

    @backup_deleted_item_retention.setter
    def backup_deleted_item_retention(self, backup_deleted_item_retention):
        &#34;&#34;&#34;Enable/Disable backup deleted item retention&#34;&#34;&#34;
        self._backup_deleted_item_retention = backup_deleted_item_retention

    @property
    def backup_stubs(self):
        &#34;&#34;&#34;Treats the backup_stubs as a read-only attribute.&#34;&#34;&#34;
        return self._backup_stubs

    @backup_stubs.setter
    def backup_stubs(self, backup_stubs):
        &#34;&#34;&#34;Sets backup stubs option on policy&#34;&#34;&#34;
        self._backup_stubs = backup_stubs

    @property
    def disabled_mailbox(self):
        &#34;&#34;&#34;Treats the disabled_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._disabled_mailbox

    @disabled_mailbox.setter
    def disabled_mailbox(self, disabled_mailbox):
        &#34;&#34;&#34;Enable/Disable disable mailbox on policy&#34;&#34;&#34;
        self._disabled_mailbox = disabled_mailbox

    @property
    def enable_mailbox_quota(self):
        &#34;&#34;&#34;Treats the enable_mailbox_quota as a read-only attribute.&#34;&#34;&#34;
        return self._enable_mailbox_quota

    @enable_mailbox_quota.setter
    def enable_mailbox_quota(self, enable_mailbox_quota):
        &#34;&#34;&#34;Sets the mailbox quota value&#34;&#34;&#34;
        self._enable_mailbox_quota = enable_mailbox_quota

    @property
    def include_messages_larger_than(self):
        &#34;&#34;&#34;Treats the include_messages_larger_than as a read-only attribute.&#34;&#34;&#34;
        return self._include_messages_larger_than

    @include_messages_larger_than.setter
    def include_messages_larger_than(self, include_messages_larger_than):
        &#34;&#34;&#34;Sets the message rule include message larger than&#34;&#34;&#34;
        self._include_messages_larger_than = include_messages_larger_than

    @property
    def include_messages_older_than(self):
        &#34;&#34;&#34;Treats the include_messages_older_than as a read-only attribute.&#34;&#34;&#34;
        return self._include_messages_older_than

    @include_messages_older_than.setter
    def include_messages_older_than(self, include_messages_older_than):
        &#34;&#34;&#34;Sets the message rule include messages older than&#34;&#34;&#34;
        self._include_messages_older_than = include_messages_older_than

    @property
    def include_messages_with_attachements(self):
        &#34;&#34;&#34;Treats the include_messages_with_attachements as a read-only attribute.&#34;&#34;&#34;
        return self._include_messages_with_attachements

    @include_messages_with_attachements.setter
    def include_messages_with_attachements(self, include_messages_with_attachements):
        &#34;&#34;&#34;Sets the message rule include messages with attachments&#34;&#34;&#34;
        self._include_messages_with_attachements = include_messages_with_attachements

    @property
    def primary_mailbox(self):
        &#34;&#34;&#34;Treats the primary_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._primary_mailbox

    @primary_mailbox.setter
    def primary_mailbox(self, primary_mailbox):
        &#34;&#34;&#34;Enable/Disable primary mailbox on policy &#34;&#34;&#34;
        self._primary_mailbox = primary_mailbox

    @property
    def skip_mailboxes_exceeded_quota(self):
        &#34;&#34;&#34;Treats the skip_mailboxes_exceeded_quota as a read-only attribute.&#34;&#34;&#34;
        return self._skip_mailboxes_exceeded_quota

    @skip_mailboxes_exceeded_quota.setter
    def skip_mailboxes_exceeded_quota(self, skip_mailboxes_exceeded_quota):
        &#34;&#34;&#34;Sets the mailbox exceeded quota value&#34;&#34;&#34;
        self._skip_mailboxes_exceeded_quota = skip_mailboxes_exceeded_quota

    @property
    def include_discovery_holds_folder(self):
        &#34;&#34;&#34;Treats the include_discovery_holds_folder as a read-only attribute.&#34;&#34;&#34;
        return self._include_discovery_holds_folder

    @include_discovery_holds_folder.setter
    def include_discovery_holds_folder(self, include_discovery_holds_folder):
        &#34;&#34;&#34;Enable/Disable disocvery hold folder&#34;&#34;&#34;
        self._include_discovery_holds_folder = include_discovery_holds_folder

    @property
    def include_purges_folder(self):
        &#34;&#34;&#34;Treats the include_purges_folder as a read-only attribute.&#34;&#34;&#34;
        return self._include_purges_folder

    @include_purges_folder.setter
    def include_purges_folder(self, include_purges_folder):
        &#34;&#34;&#34;Enable/Disable Purges folder&#34;&#34;&#34;
        self._include_purges_folder = include_purges_folder

    @property
    def include_version_folder(self):
        &#34;&#34;&#34;Treats the include_version_folder as a read-only attribute.&#34;&#34;&#34;
        return self._include_version_folder

    @include_version_folder.setter
    def include_version_folder(self, include_version_folder):
        &#34;&#34;&#34;Enable/Disable versions folder&#34;&#34;&#34;
        self._include_version_folder = include_version_folder

    @property
    def save_conversation_meta_data(self):
        &#34;&#34;&#34;Treats the save_conversation_meta_data as a read-only attribute.&#34;&#34;&#34;
        return self._save_conversation_meta_data

    @save_conversation_meta_data.setter
    def save_conversation_meta_data(self, save_conversation_meta_data):
        &#34;&#34;&#34;sets the save conversation meta data&#34;&#34;&#34;
        self._save_conversation_meta_data = save_conversation_meta_data

    @property
    def include_categories(self):
        &#34;&#34;&#34;Treats the include_categories as a read-only attribute.&#34;&#34;&#34;
        return self._include_categories

    @include_categories.setter
    def include_categories(self, include_categories):
        &#34;&#34;&#34;sets the include categories option on policy&#34;&#34;&#34;
        self._include_categories = include_categories

    @property
    def include_folder_filter(self):
        &#34;&#34;&#34;Treats the include_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._include_folder_filter

    @include_folder_filter.setter
    def include_folder_filter(self, include_folder_filter):
        &#34;&#34;&#34;sets include folder filter on policy&#34;&#34;&#34;
        self._include_folder_filter = include_folder_filter

    @property
    def exclude_folder_filter(self):
        &#34;&#34;&#34;Treats the exclude_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_folder_filter

    @exclude_folder_filter.setter
    def exclude_folder_filter(self, exclude_folder_filter):
        &#34;&#34;&#34;sets exclude folder filter on policy&#34;&#34;&#34;
        self._exclude_folder_filter = exclude_folder_filter

    @property
    def exclude_message_class_filter(self):
        &#34;&#34;&#34;Treats the exclude_message_class_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_message_class_filter

    @exclude_message_class_filter.setter
    def exclude_message_class_filter(self, exclude_message_class_filter):
        &#34;&#34;&#34;sets message class filters on policy&#34;&#34;&#34;
        self._exclude_message_class_filter = exclude_message_class_filter

    @property
    def content_index_behind_alert(self):
        &#34;&#34;&#34;Treats the content_index_behind_alert as a read-only attribute.&#34;&#34;&#34;
        return self._content_index_behind_alert

    @content_index_behind_alert.setter
    def content_index_behind_alert(self, content_index_behind_alert):
        &#34;&#34;&#34;sets content index alert&#34;&#34;&#34;
        self._content_index_behind_alert = content_index_behind_alert

    @property
    def content_index_data_over(self):
        &#34;&#34;&#34;Treats the content_index_data_over as a read-only attribute.&#34;&#34;&#34;
        return self._content_index_data_over

    @content_index_data_over.setter
    def content_index_data_over(self, content_index_data_over):
        &#34;&#34;&#34;sets content Index data over value&#34;&#34;&#34;
        self._content_index_data_over = content_index_data_over

    @property
    def deferred_days(self):
        &#34;&#34;&#34;Treats the deferred_days as a read-only attribute.&#34;&#34;&#34;
        return self._deferred_days

    @deferred_days.setter
    def deferred_days(self, deferred_days):
        &#34;&#34;&#34;sets deferred days&#34;&#34;&#34;
        self._deferred_days = deferred_days

    @property
    def enable_content_index(self):
        &#34;&#34;&#34;Treats the enable_content_index as a read-only attribute.&#34;&#34;&#34;
        return self._enable_content_index

    @enable_content_index.setter
    def enable_content_index(self, enable_content_index):
        &#34;&#34;&#34;Enable/Disable ContentIndex&#34;&#34;&#34;
        self._enable_content_index = enable_content_index

    @property
    def enable_deferred_days(self):
        &#34;&#34;&#34;Treats the enable_deferred_days as a read-only attribute.&#34;&#34;&#34;
        return self._enable_deferred_days

    @enable_deferred_days.setter
    def enable_deferred_days(self, enable_deferred_days):
        &#34;&#34;&#34;Enable/Disable deferred days&#34;&#34;&#34;
        self._enable_deferred_days = enable_deferred_days

    @property
    def enable_preview_generation(self):
        &#34;&#34;&#34;Treats the enable_preview_generation as a read-only attribute.&#34;&#34;&#34;
        return self._enable_preview_generation

    @enable_preview_generation.setter
    def enable_preview_generation(self, enable_preview_generation):
        &#34;&#34;&#34;Enable/Disable preview generation&#34;&#34;&#34;
        self._enable_preview_generation = enable_preview_generation

    @property
    def jobs_older_than(self):
        &#34;&#34;&#34;Treats the jobs_older_than as a read-only attribute.&#34;&#34;&#34;
        return self._jobs_older_than

    @jobs_older_than.setter
    def jobs_older_than(self, jobs_older_than):
        &#34;&#34;&#34;sets job older than value&#34;&#34;&#34;
        self._jobs_older_than = jobs_older_than

    @property
    def retention_days_for_ci(self):
        &#34;&#34;&#34;Treats the retention_days_for_ci as a read-only attribute.&#34;&#34;&#34;
        return self._retention_days_for_ci

    @retention_days_for_ci.setter
    def retention_days_for_ci(self, retention_days_for_ci):
        &#34;&#34;&#34;sets retention for ContentIndex&#34;&#34;&#34;
        self._retention_days_for_ci = retention_days_for_ci

    @property
    def start_time(self):
        &#34;&#34;&#34;Treats the start_time as a read-only attribute.&#34;&#34;&#34;
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        &#34;&#34;&#34;sets start time&#34;&#34;&#34;
        self._start_time = start_time

    @property
    def synchronize_on(self):
        &#34;&#34;&#34;Treats the synchronize_on as a read-only attribute.&#34;&#34;&#34;
        return self._synchronize_on

    @synchronize_on.setter
    def synchronize_on(self, synchronize_on):
        &#34;&#34;&#34;sets synchronize on for ContentIndex&#34;&#34;&#34;
        self._synchronize_on = synchronize_on

    @property
    def path(self):
        &#34;&#34;&#34;Treats the path as a read-only attribute.&#34;&#34;&#34;
        return self._path

    @path.setter
    def path(self, path):
        &#34;&#34;&#34;sets previewpath for ContentIndex&#34;&#34;&#34;
        self._path = path

    @property
    def username(self):
        &#34;&#34;&#34;Treats the username as a read-only attribute.&#34;&#34;&#34;
        return self._username

    @username.setter
    def username(self, username):
        &#34;&#34;&#34;sets username for ContentIndex&#34;&#34;&#34;
        self._username = username

    @property
    def password(self):
        &#34;&#34;&#34;Treats the password as a read-only attribute.&#34;&#34;&#34;
        return self._password

    @password.setter
    def password(self, password):
        self._password = password

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;

        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 1,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 137
                },
                &#34;detail&#34;: {
                    &#34;emailPolicy&#34;: {
                        &#34;emailPolicyType&#34;: 1,
                        &#34;archivePolicy&#34;: {
                            &#34;includeMsgsLargerThan&#34;: self._include_messages_larger_than,
                            &#34;skipMailBoxesExceededQuota&#34;: self._skip_mailboxes_exceeded_quota,
                            &#34;backupDeletedItemRetention&#34;: self._backup_deleted_item_retention,
                            &#34;primaryMailbox&#34;: self._primary_mailbox,
                            &#34;includeMsgsOlderThan&#34;: self._include_messages_older_than,
                            &#34;archiveMailbox&#34;: self._archive_mailbox,
                            &#34;disabledMailbox&#34;: self._disabled_mailbox,
                            &#34;backupStubs&#34;: self._backup_stubs,
                            &#34;enableMailBoxQuota&#34;: self._enable_mailbox_quota,
                            &#34;includeOnlyMsgsWithAttachemts&#34;: self._include_messages_with_attachements,
                            &#34;includeDiscoveryHoldsFolder&#34;: self._include_discovery_holds_folder,
                            &#34;includePurgesFolder&#34;: self._include_purges_folder,
                            &#34;includeVersionsFolder&#34;: self._include_version_folder,
                            &#34;saveConversationMetaData&#34;: self._save_conversation_meta_data,
                            &#34;includeCategories&#34;: self._include_categories,
                            &#34;includeFolderFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            },
                            &#34;excludeFolderFilter&#34;: {
                                &#34;folderPatternsSelected&#34;: [
                                    &#34;Junk Mail&#34;,
                                    &#34;Sync Issues&#34;
                                ],
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            },
                            &#34;contentIndexProps&#34;: {
                                &#34;enableContentIndex&#34;: self._enable_content_index,
                                &#34;contentIndexBehindAlert&#34;: self._content_index_behind_alert,
                                &#34;synchronizeOn&#34;: self._synchronize_on,
                                &#34;contentIndexDataOver&#34;: self._content_index_data_over,
                                &#34;retentionDaysForCI&#34;: self._retention_days_for_ci,
                                &#34;startTime&#34;: self._start_time,
                                &#34;jobsOlderThan&#34;: self._jobs_older_than,
                                &#34;enablePreviewGeneration&#34;: self._enable_preview_generation,
                                &#34;deferredDays&#34;: self._deferred_days,
                                &#34;enableDeferredDays&#34;: self._enable_deferred_days,
                                &#34;pattern&#34;: [
                                    {}
                                ],
                                &#34;previewPathDir&#34;: {
                                    &#34;path&#34;: self._path,
                                    &#34;userAccount&#34;: {
                                        &#34;userName&#34;: self._username,
                                        &#34;password&#34;: self._password
                                    }
                                }
                            },
                            &#34;excludeMessageClassFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Appointments&#34;,
                                    &#34;Contacts&#34;,
                                    &#34;Schedules&#34;,
                                    &#34;Tasks&#34;
                                ]
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self._name
                }
            }
        }

        return policy_json</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.archive_mailbox"><code class="name">var <span class="ident">archive_mailbox</span></code></dt>
<dd>
<div class="desc"><p>Treats the archive_mailbox as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L597-L600" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archive_mailbox(self):
    &#34;&#34;&#34;Treats the archive_mailbox as a read-only attribute.&#34;&#34;&#34;
    return self._archive_mailbox</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.backup_deleted_item_retention"><code class="name">var <span class="ident">backup_deleted_item_retention</span></code></dt>
<dd>
<div class="desc"><p>Treats the backup_deleted_item_retention as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L607-L610" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_deleted_item_retention(self):
    &#34;&#34;&#34;Treats the backup_deleted_item_retention as a read-only attribute.&#34;&#34;&#34;
    return self._backup_deleted_item_retention</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.backup_stubs"><code class="name">var <span class="ident">backup_stubs</span></code></dt>
<dd>
<div class="desc"><p>Treats the backup_stubs as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L617-L620" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_stubs(self):
    &#34;&#34;&#34;Treats the backup_stubs as a read-only attribute.&#34;&#34;&#34;
    return self._backup_stubs</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.content_index_behind_alert"><code class="name">var <span class="ident">content_index_behind_alert</span></code></dt>
<dd>
<div class="desc"><p>Treats the content_index_behind_alert as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L777-L780" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content_index_behind_alert(self):
    &#34;&#34;&#34;Treats the content_index_behind_alert as a read-only attribute.&#34;&#34;&#34;
    return self._content_index_behind_alert</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.content_index_data_over"><code class="name">var <span class="ident">content_index_data_over</span></code></dt>
<dd>
<div class="desc"><p>Treats the content_index_data_over as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L787-L790" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content_index_data_over(self):
    &#34;&#34;&#34;Treats the content_index_data_over as a read-only attribute.&#34;&#34;&#34;
    return self._content_index_data_over</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.deferred_days"><code class="name">var <span class="ident">deferred_days</span></code></dt>
<dd>
<div class="desc"><p>Treats the deferred_days as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L797-L800" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def deferred_days(self):
    &#34;&#34;&#34;Treats the deferred_days as a read-only attribute.&#34;&#34;&#34;
    return self._deferred_days</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.disabled_mailbox"><code class="name">var <span class="ident">disabled_mailbox</span></code></dt>
<dd>
<div class="desc"><p>Treats the disabled_mailbox as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L627-L630" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def disabled_mailbox(self):
    &#34;&#34;&#34;Treats the disabled_mailbox as a read-only attribute.&#34;&#34;&#34;
    return self._disabled_mailbox</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.email_policy_type"><code class="name">var <span class="ident">email_policy_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the email_policy_type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L592-L595" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def email_policy_type(self):
    &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
    return self._email_policy_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.enable_content_index"><code class="name">var <span class="ident">enable_content_index</span></code></dt>
<dd>
<div class="desc"><p>Treats the enable_content_index as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L807-L810" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_content_index(self):
    &#34;&#34;&#34;Treats the enable_content_index as a read-only attribute.&#34;&#34;&#34;
    return self._enable_content_index</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.enable_deferred_days"><code class="name">var <span class="ident">enable_deferred_days</span></code></dt>
<dd>
<div class="desc"><p>Treats the enable_deferred_days as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L817-L820" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_deferred_days(self):
    &#34;&#34;&#34;Treats the enable_deferred_days as a read-only attribute.&#34;&#34;&#34;
    return self._enable_deferred_days</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.enable_mailbox_quota"><code class="name">var <span class="ident">enable_mailbox_quota</span></code></dt>
<dd>
<div class="desc"><p>Treats the enable_mailbox_quota as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L637-L640" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_mailbox_quota(self):
    &#34;&#34;&#34;Treats the enable_mailbox_quota as a read-only attribute.&#34;&#34;&#34;
    return self._enable_mailbox_quota</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.enable_preview_generation"><code class="name">var <span class="ident">enable_preview_generation</span></code></dt>
<dd>
<div class="desc"><p>Treats the enable_preview_generation as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L827-L830" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_preview_generation(self):
    &#34;&#34;&#34;Treats the enable_preview_generation as a read-only attribute.&#34;&#34;&#34;
    return self._enable_preview_generation</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.exclude_folder_filter"><code class="name">var <span class="ident">exclude_folder_filter</span></code></dt>
<dd>
<div class="desc"><p>Treats the exclude_folder_filter as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L757-L760" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exclude_folder_filter(self):
    &#34;&#34;&#34;Treats the exclude_folder_filter as a read-only attribute.&#34;&#34;&#34;
    return self._exclude_folder_filter</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.exclude_message_class_filter"><code class="name">var <span class="ident">exclude_message_class_filter</span></code></dt>
<dd>
<div class="desc"><p>Treats the exclude_message_class_filter as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L767-L770" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exclude_message_class_filter(self):
    &#34;&#34;&#34;Treats the exclude_message_class_filter as a read-only attribute.&#34;&#34;&#34;
    return self._exclude_message_class_filter</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.include_categories"><code class="name">var <span class="ident">include_categories</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_categories as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L737-L740" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_categories(self):
    &#34;&#34;&#34;Treats the include_categories as a read-only attribute.&#34;&#34;&#34;
    return self._include_categories</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.include_discovery_holds_folder"><code class="name">var <span class="ident">include_discovery_holds_folder</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_discovery_holds_folder as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L697-L700" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_discovery_holds_folder(self):
    &#34;&#34;&#34;Treats the include_discovery_holds_folder as a read-only attribute.&#34;&#34;&#34;
    return self._include_discovery_holds_folder</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.include_folder_filter"><code class="name">var <span class="ident">include_folder_filter</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_folder_filter as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L747-L750" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_folder_filter(self):
    &#34;&#34;&#34;Treats the include_folder_filter as a read-only attribute.&#34;&#34;&#34;
    return self._include_folder_filter</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.include_messages_larger_than"><code class="name">var <span class="ident">include_messages_larger_than</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_messages_larger_than as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L647-L650" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_messages_larger_than(self):
    &#34;&#34;&#34;Treats the include_messages_larger_than as a read-only attribute.&#34;&#34;&#34;
    return self._include_messages_larger_than</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.include_messages_older_than"><code class="name">var <span class="ident">include_messages_older_than</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_messages_older_than as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L657-L660" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_messages_older_than(self):
    &#34;&#34;&#34;Treats the include_messages_older_than as a read-only attribute.&#34;&#34;&#34;
    return self._include_messages_older_than</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.include_messages_with_attachements"><code class="name">var <span class="ident">include_messages_with_attachements</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_messages_with_attachements as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L667-L670" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_messages_with_attachements(self):
    &#34;&#34;&#34;Treats the include_messages_with_attachements as a read-only attribute.&#34;&#34;&#34;
    return self._include_messages_with_attachements</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.include_purges_folder"><code class="name">var <span class="ident">include_purges_folder</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_purges_folder as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L707-L710" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_purges_folder(self):
    &#34;&#34;&#34;Treats the include_purges_folder as a read-only attribute.&#34;&#34;&#34;
    return self._include_purges_folder</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.include_version_folder"><code class="name">var <span class="ident">include_version_folder</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_version_folder as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L717-L720" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_version_folder(self):
    &#34;&#34;&#34;Treats the include_version_folder as a read-only attribute.&#34;&#34;&#34;
    return self._include_version_folder</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.jobs_older_than"><code class="name">var <span class="ident">jobs_older_than</span></code></dt>
<dd>
<div class="desc"><p>Treats the jobs_older_than as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L837-L840" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def jobs_older_than(self):
    &#34;&#34;&#34;Treats the jobs_older_than as a read-only attribute.&#34;&#34;&#34;
    return self._jobs_older_than</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Treats the name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L582-L585" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
    return self._name</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.password"><code class="name">var <span class="ident">password</span></code></dt>
<dd>
<div class="desc"><p>Treats the password as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L897-L900" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def password(self):
    &#34;&#34;&#34;Treats the password as a read-only attribute.&#34;&#34;&#34;
    return self._password</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.path"><code class="name">var <span class="ident">path</span></code></dt>
<dd>
<div class="desc"><p>Treats the path as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L877-L880" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def path(self):
    &#34;&#34;&#34;Treats the path as a read-only attribute.&#34;&#34;&#34;
    return self._path</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.primary_mailbox"><code class="name">var <span class="ident">primary_mailbox</span></code></dt>
<dd>
<div class="desc"><p>Treats the primary_mailbox as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L677-L680" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def primary_mailbox(self):
    &#34;&#34;&#34;Treats the primary_mailbox as a read-only attribute.&#34;&#34;&#34;
    return self._primary_mailbox</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.retention_days_for_ci"><code class="name">var <span class="ident">retention_days_for_ci</span></code></dt>
<dd>
<div class="desc"><p>Treats the retention_days_for_ci as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L847-L850" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def retention_days_for_ci(self):
    &#34;&#34;&#34;Treats the retention_days_for_ci as a read-only attribute.&#34;&#34;&#34;
    return self._retention_days_for_ci</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.save_conversation_meta_data"><code class="name">var <span class="ident">save_conversation_meta_data</span></code></dt>
<dd>
<div class="desc"><p>Treats the save_conversation_meta_data as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L727-L730" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def save_conversation_meta_data(self):
    &#34;&#34;&#34;Treats the save_conversation_meta_data as a read-only attribute.&#34;&#34;&#34;
    return self._save_conversation_meta_data</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.skip_mailboxes_exceeded_quota"><code class="name">var <span class="ident">skip_mailboxes_exceeded_quota</span></code></dt>
<dd>
<div class="desc"><p>Treats the skip_mailboxes_exceeded_quota as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L687-L690" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def skip_mailboxes_exceeded_quota(self):
    &#34;&#34;&#34;Treats the skip_mailboxes_exceeded_quota as a read-only attribute.&#34;&#34;&#34;
    return self._skip_mailboxes_exceeded_quota</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.start_time"><code class="name">var <span class="ident">start_time</span></code></dt>
<dd>
<div class="desc"><p>Treats the start_time as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L857-L860" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def start_time(self):
    &#34;&#34;&#34;Treats the start_time as a read-only attribute.&#34;&#34;&#34;
    return self._start_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.synchronize_on"><code class="name">var <span class="ident">synchronize_on</span></code></dt>
<dd>
<div class="desc"><p>Treats the synchronize_on as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L867-L870" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def synchronize_on(self):
    &#34;&#34;&#34;Treats the synchronize_on as a read-only attribute.&#34;&#34;&#34;
    return self._synchronize_on</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ArchivePolicy.username"><code class="name">var <span class="ident">username</span></code></dt>
<dd>
<div class="desc"><p>Treats the username as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L887-L890" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def username(self):
    &#34;&#34;&#34;Treats the username as a read-only attribute.&#34;&#34;&#34;
    return self._username</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy"><code class="flex name class">
<span>class <span class="ident">CleanupPolicy</span></span>
<span>(</span><span>commcell_object, cleanup_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing Cleanup policy operations for a specific cleanup policy</p>
<p>Initialise the cleanup Policy class instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1303-L1652" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CleanupPolicy():

    &#34;&#34;&#34;Class for performing Cleanup policy operations for a specific cleanup policy&#34;&#34;&#34;

    def __init__(self, commcell_object, cleanup_policy_name):
        &#34;&#34;&#34;Initialise the cleanup Policy class instance.&#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._name = cleanup_policy_name
        self._email_policy_type = 2
        self._add_recall_link = True
        self._archive_if_size = 90
        self._archive_mailbox = False
        self._collect_messages_with_attachments = False
        self._collect_messages_days_after = 0
        self._collect_messages_larger_than = 0
        self._create_stubs = True
        self._disabled_mailbox = True
        self._enable_message_rules = True
        self._leave_message_body = True
        self._mailbox_quota = False
        self._number_of_days_for_source_pruning = 730
        self._primary_mailbox = True
        self._prune_erased_messages_or_stubs = False
        self._prune_messages = False
        self._prune_stubs = False
        self._skip_unread_messages = False
        self._stop_archive_if_size = 75
        self._truncate_body = False
        self._truncate_body_to_bytes = 1024
        self._used_disk_space = False
        self._used_disk_space_value = 50
        self._include_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items&#34;
        self._exclude_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items,Junk Mail,Sync Issues&#34;
        self._exclude_message_class_filter = &#34;Appointments,Contacts,Schedules,Tasks&#34;

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        self._name = name

    @property
    def email_policy_type(self):
        &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
        return self._email_policy_type

    @property
    def add_recall_link(self):
        &#34;&#34;&#34;Treats the add_recall_link as a read-only attribute.&#34;&#34;&#34;
        return self._add_recall_link

    @add_recall_link.setter
    def add_recall_link(self, add_recall_link):
        self._add_recall_link = add_recall_link

    @property
    def archive_if_size(self):
        &#34;&#34;&#34;Treats the archive_if_size as a read-only attribute.&#34;&#34;&#34;
        return self._archive_if_size

    @archive_if_size.setter
    def archive_if_size(self, archive_if_size):
        self._archive_if_size = archive_if_size

    @property
    def archive_mailbox(self):
        &#34;&#34;&#34;Treats the archive_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._archive_mailbox

    @archive_mailbox.setter
    def archive_mailbox(self, archive_mailbox):
        self._archive_mailbox = archive_mailbox

    @property
    def collect_messages_with_attachments(self):
        &#34;&#34;&#34;Treats the collect_messages_with_attachments as a read-only attribute.&#34;&#34;&#34;
        return self._collect_messages_with_attachments

    @collect_messages_with_attachments.setter
    def collect_messages_with_attachments(self, collect_messages_with_attachments):
        self._collect_messages_with_attachments = collect_messages_with_attachments

    @property
    def collect_messages_days_after(self):
        &#34;&#34;&#34;Treats the collect_messages_days_after as a read-only attribute.&#34;&#34;&#34;
        return self._collect_messages_days_after

    @collect_messages_days_after.setter
    def collect_messages_days_after(self, collect_messages_days_after):
        self._collect_messages_days_after = collect_messages_days_after

    @property
    def collect_messages_larger_than(self):
        &#34;&#34;&#34;Treats the collect_messages_larger_than as a read-only attribute.&#34;&#34;&#34;
        return self._collect_messages_larger_than

    @collect_messages_larger_than.setter
    def collect_messages_larger_than(self, collect_messages_larger_than):
        self._collect_messages_larger_than = collect_messages_larger_than

    @property
    def create_stubs(self):
        &#34;&#34;&#34;Treats the create_stubs as a read-only attribute.&#34;&#34;&#34;
        return self._create_stubs

    @create_stubs.setter
    def create_stubs(self, create_stubs):
        self._create_stubs = create_stubs

    @property
    def disabled_mailbox(self):
        &#34;&#34;&#34;Treats the disabled_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._disabled_mailbox

    @disabled_mailbox.setter
    def disabled_mailbox(self, disabled_mailbox):
        self._disabled_mailbox = disabled_mailbox

    @property
    def enable_message_rules(self):
        &#34;&#34;&#34;Treats the enable_message_rules as a read-only attribute.&#34;&#34;&#34;
        return self._enable_message_rules

    @enable_message_rules.setter
    def enable_message_rules(self, enable_message_rules):
        self._enable_message_rules = enable_message_rules

    @property
    def leave_message_body(self):
        &#34;&#34;&#34;Treats the leave_message_body as a read-only attribute.&#34;&#34;&#34;
        return self._leave_message_body

    @leave_message_body.setter
    def leave_message_body(self, leave_message_body):
        self._leave_message_body = leave_message_body

    @property
    def mailbox_quota(self):
        &#34;&#34;&#34;Treats the mailbox_quota as a read-only attribute.&#34;&#34;&#34;
        return self._mailbox_quota

    @mailbox_quota.setter
    def mailbox_quota(self, mailbox_quota):
        self._mailbox_quota = mailbox_quota

    @property
    def number_of_days_for_source_pruning(self):
        &#34;&#34;&#34;Treats the number_of_days_for_source_pruning as a read-only attribute.&#34;&#34;&#34;
        return self._number_of_days_for_source_pruning

    @number_of_days_for_source_pruning.setter
    def number_of_days_for_source_pruning(self, number_of_days_for_source_pruning):
        self._number_of_days_for_source_pruning = number_of_days_for_source_pruning

    @property
    def primary_mailbox(self):
        &#34;&#34;&#34;Treats the primary_mailbox as a read-only attribute.&#34;&#34;&#34;
        return self._primary_mailbox

    @primary_mailbox.setter
    def primary_mailbox(self, primary_mailbox):
        self._primary_mailbox = primary_mailbox

    @property
    def prune_erased_messages_or_stubs(self):
        &#34;&#34;&#34;Treats the prune_erased_messages_or_stubs as a read-only attribute.&#34;&#34;&#34;
        return self._prune_erased_messages_or_stubs

    @prune_erased_messages_or_stubs.setter
    def prune_erased_messages_or_stubs(self, prune_erased_messages_or_stubs):
        self._prune_erased_messages_or_stubs = prune_erased_messages_or_stubs

    @property
    def prune_messages(self):
        &#34;&#34;&#34;Treats the prune_messages as a read-only attribute.&#34;&#34;&#34;
        return self._prune_messages

    @prune_messages.setter
    def prune_messages(self, prune_messages):
        self._prune_messages = prune_messages

    @property
    def prune_stubs(self):
        &#34;&#34;&#34;Treats the prune_stubs as a read-only attribute.&#34;&#34;&#34;
        return self._prune_stubs

    @prune_stubs.setter
    def prune_stubs(self, prune_stubs):
        self._prune_stubs = prune_stubs

    @property
    def skip_unread_messages(self):
        &#34;&#34;&#34;Treats the skip_unread_messages as a read-only attribute.&#34;&#34;&#34;
        return self._skip_unread_messages

    @skip_unread_messages.setter
    def skip_unread_messages(self, skip_unread_messages):
        self._skip_unread_messages = skip_unread_messages

    @property
    def stop_archive_if_size(self):
        &#34;&#34;&#34;Treats the stop_archive_if_size as a read-only attribute.&#34;&#34;&#34;
        return self._stop_archive_if_size

    @stop_archive_if_size.setter
    def stop_archive_if_size(self, stop_archive_if_size):
        self._stop_archive_if_size = stop_archive_if_size

    @property
    def truncate_body(self):
        &#34;&#34;&#34;Treats the truncate_body as a read-only attribute.&#34;&#34;&#34;
        return self._truncate_body

    @truncate_body.setter
    def truncate_body(self, truncate_body):
        self._truncate_body = truncate_body

    @property
    def truncate_body_to_bytes(self):
        &#34;&#34;&#34;Treats the truncate_body_to_bytes as a read-only attribute.&#34;&#34;&#34;
        return self._truncate_body_to_bytes

    @truncate_body_to_bytes.setter
    def truncate_body_to_bytes(self, truncate_body_to_bytes):
        self._truncate_body_to_bytes = truncate_body_to_bytes

    @property
    def used_disk_space(self):
        &#34;&#34;&#34;Treats the used_disk_space as a read-only attribute.&#34;&#34;&#34;
        return self._used_disk_space

    @used_disk_space.setter
    def path(self, used_disk_space):
        self._used_disk_space = used_disk_space

    @property
    def used_disk_space_value(self):
        &#34;&#34;&#34;Treats the used_disk_space_value as a read-only attribute.&#34;&#34;&#34;
        return self._used_disk_space_value

    @used_disk_space_value.setter
    def used_disk_space_value(self, used_disk_space_value):
        self._used_disk_space_value = used_disk_space_value

    @property
    def include_folder_filter(self):
        &#34;&#34;&#34;Treats the include_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._include_folder_filter

    @include_folder_filter.setter
    def include_folder_filter(self, include_folder_filter):
        self._include_folder_filter = include_folder_filter

    @property
    def exclude_folder_filter(self):
        &#34;&#34;&#34;Treats the exclude_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_folder_filter

    @exclude_folder_filter.setter
    def exclude_folder_filter(self, exclude_folder_filter):
        self._exclude_folder_filter = exclude_folder_filter

    @property
    def exclude_message_class_filter(self):
        &#34;&#34;&#34;Treats the exclude_message_class_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_message_class_filter

    @exclude_message_class_filter.setter
    def exclude_message_class_filter(self, exclude_message_class_filter):
        self._exclude_message_class_filter = exclude_message_class_filter

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;

        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 1,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 137
                },
                &#34;detail&#34;: {
                    &#34;emailPolicy&#34;: {
                        &#34;emailPolicyType&#34;: 2,
                        &#34;cleanupPolicy&#34;: {
                            &#34;usedDiskSpace&#34;: self._used_disk_space,
                            &#34;createStubs&#34;: self._create_stubs,
                            &#34;usedDiskSpaceValue&#34;: self._used_disk_space_value,
                            &#34;pruneMsgs&#34;: self._prune_messages,
                            &#34;primaryMailbox&#34;: self._primary_mailbox,
                            &#34;disabledMailbox&#34;: self._disabled_mailbox,
                            &#34;pruneErasedMsgsOrStubs&#34;: self._prune_erased_messages_or_stubs,
                            &#34;collectMsgsDaysAfter&#34;: self._collect_messages_days_after,
                            &#34;numOfDaysForSourcePruning&#34;: self._number_of_days_for_source_pruning,
                            &#34;collectMsgsLargerThan&#34;: self._collect_messages_larger_than,
                            &#34;skipUnreadMsgs&#34;: self._skip_unread_messages,
                            &#34;collectMsgWithAttach&#34;: self._collect_messages_with_attachments,
                            &#34;leaveMsgBody&#34;: self._leave_message_body,
                            &#34;mailboxQuota&#34;: self.mailbox_quota,
                            &#34;truncateBody&#34;: self._truncate_body,
                            &#34;pruneStubs&#34;: self._prune_stubs,
                            &#34;enableMessageRules&#34;: self._enable_message_rules,
                            &#34;archiveMailbox&#34;: self._archive_mailbox,
                            &#34;archiveIfSize&#34;: self._archive_if_size,
                            &#34;truncateBodyToBytes&#34;: self._truncate_body_to_bytes,
                            &#34;addRecallLink&#34;: self._add_recall_link,
                            &#34;stopArchiveIfSize&#34;: self._stop_archive_if_size,
                            &#34;excludeMessageClassFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Appointments&#34;,
                                    &#34;Contacts&#34;,
                                    &#34;Schedules&#34;,
                                    &#34;Tasks&#34;
                                ]
                            },
                            &#34;includeFolderFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            },
                            &#34;excludeFolderFilter&#34;: {
                                &#34;folderPatternsSelected&#34;: [
                                    &#34;Junk Mail&#34;,
                                    &#34;Sync Issues&#34;
                                ],
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self._name
                }
            }
        }

        return policy_json</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.add_recall_link"><code class="name">var <span class="ident">add_recall_link</span></code></dt>
<dd>
<div class="desc"><p>Treats the add_recall_link as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1353-L1356" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def add_recall_link(self):
    &#34;&#34;&#34;Treats the add_recall_link as a read-only attribute.&#34;&#34;&#34;
    return self._add_recall_link</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.archive_if_size"><code class="name">var <span class="ident">archive_if_size</span></code></dt>
<dd>
<div class="desc"><p>Treats the archive_if_size as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1362-L1365" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archive_if_size(self):
    &#34;&#34;&#34;Treats the archive_if_size as a read-only attribute.&#34;&#34;&#34;
    return self._archive_if_size</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.archive_mailbox"><code class="name">var <span class="ident">archive_mailbox</span></code></dt>
<dd>
<div class="desc"><p>Treats the archive_mailbox as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1371-L1374" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archive_mailbox(self):
    &#34;&#34;&#34;Treats the archive_mailbox as a read-only attribute.&#34;&#34;&#34;
    return self._archive_mailbox</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.collect_messages_days_after"><code class="name">var <span class="ident">collect_messages_days_after</span></code></dt>
<dd>
<div class="desc"><p>Treats the collect_messages_days_after as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1389-L1392" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def collect_messages_days_after(self):
    &#34;&#34;&#34;Treats the collect_messages_days_after as a read-only attribute.&#34;&#34;&#34;
    return self._collect_messages_days_after</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.collect_messages_larger_than"><code class="name">var <span class="ident">collect_messages_larger_than</span></code></dt>
<dd>
<div class="desc"><p>Treats the collect_messages_larger_than as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1398-L1401" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def collect_messages_larger_than(self):
    &#34;&#34;&#34;Treats the collect_messages_larger_than as a read-only attribute.&#34;&#34;&#34;
    return self._collect_messages_larger_than</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.collect_messages_with_attachments"><code class="name">var <span class="ident">collect_messages_with_attachments</span></code></dt>
<dd>
<div class="desc"><p>Treats the collect_messages_with_attachments as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1380-L1383" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def collect_messages_with_attachments(self):
    &#34;&#34;&#34;Treats the collect_messages_with_attachments as a read-only attribute.&#34;&#34;&#34;
    return self._collect_messages_with_attachments</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.create_stubs"><code class="name">var <span class="ident">create_stubs</span></code></dt>
<dd>
<div class="desc"><p>Treats the create_stubs as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1407-L1410" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def create_stubs(self):
    &#34;&#34;&#34;Treats the create_stubs as a read-only attribute.&#34;&#34;&#34;
    return self._create_stubs</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.disabled_mailbox"><code class="name">var <span class="ident">disabled_mailbox</span></code></dt>
<dd>
<div class="desc"><p>Treats the disabled_mailbox as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1416-L1419" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def disabled_mailbox(self):
    &#34;&#34;&#34;Treats the disabled_mailbox as a read-only attribute.&#34;&#34;&#34;
    return self._disabled_mailbox</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.email_policy_type"><code class="name">var <span class="ident">email_policy_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the email_policy_type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1348-L1351" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def email_policy_type(self):
    &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
    return self._email_policy_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.enable_message_rules"><code class="name">var <span class="ident">enable_message_rules</span></code></dt>
<dd>
<div class="desc"><p>Treats the enable_message_rules as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1425-L1428" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_message_rules(self):
    &#34;&#34;&#34;Treats the enable_message_rules as a read-only attribute.&#34;&#34;&#34;
    return self._enable_message_rules</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.exclude_folder_filter"><code class="name">var <span class="ident">exclude_folder_filter</span></code></dt>
<dd>
<div class="desc"><p>Treats the exclude_folder_filter as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1560-L1563" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exclude_folder_filter(self):
    &#34;&#34;&#34;Treats the exclude_folder_filter as a read-only attribute.&#34;&#34;&#34;
    return self._exclude_folder_filter</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.exclude_message_class_filter"><code class="name">var <span class="ident">exclude_message_class_filter</span></code></dt>
<dd>
<div class="desc"><p>Treats the exclude_message_class_filter as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1569-L1572" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exclude_message_class_filter(self):
    &#34;&#34;&#34;Treats the exclude_message_class_filter as a read-only attribute.&#34;&#34;&#34;
    return self._exclude_message_class_filter</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.include_folder_filter"><code class="name">var <span class="ident">include_folder_filter</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_folder_filter as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1551-L1554" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_folder_filter(self):
    &#34;&#34;&#34;Treats the include_folder_filter as a read-only attribute.&#34;&#34;&#34;
    return self._include_folder_filter</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.leave_message_body"><code class="name">var <span class="ident">leave_message_body</span></code></dt>
<dd>
<div class="desc"><p>Treats the leave_message_body as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1434-L1437" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def leave_message_body(self):
    &#34;&#34;&#34;Treats the leave_message_body as a read-only attribute.&#34;&#34;&#34;
    return self._leave_message_body</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.mailbox_quota"><code class="name">var <span class="ident">mailbox_quota</span></code></dt>
<dd>
<div class="desc"><p>Treats the mailbox_quota as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1443-L1446" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def mailbox_quota(self):
    &#34;&#34;&#34;Treats the mailbox_quota as a read-only attribute.&#34;&#34;&#34;
    return self._mailbox_quota</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Treats the name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1339-L1342" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
    return self._name</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.number_of_days_for_source_pruning"><code class="name">var <span class="ident">number_of_days_for_source_pruning</span></code></dt>
<dd>
<div class="desc"><p>Treats the number_of_days_for_source_pruning as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1452-L1455" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def number_of_days_for_source_pruning(self):
    &#34;&#34;&#34;Treats the number_of_days_for_source_pruning as a read-only attribute.&#34;&#34;&#34;
    return self._number_of_days_for_source_pruning</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.path"><code class="name">var <span class="ident">path</span></code></dt>
<dd>
<div class="desc"><p>Treats the used_disk_space as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1533-L1536" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def used_disk_space(self):
    &#34;&#34;&#34;Treats the used_disk_space as a read-only attribute.&#34;&#34;&#34;
    return self._used_disk_space</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.primary_mailbox"><code class="name">var <span class="ident">primary_mailbox</span></code></dt>
<dd>
<div class="desc"><p>Treats the primary_mailbox as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1461-L1464" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def primary_mailbox(self):
    &#34;&#34;&#34;Treats the primary_mailbox as a read-only attribute.&#34;&#34;&#34;
    return self._primary_mailbox</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.prune_erased_messages_or_stubs"><code class="name">var <span class="ident">prune_erased_messages_or_stubs</span></code></dt>
<dd>
<div class="desc"><p>Treats the prune_erased_messages_or_stubs as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1470-L1473" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def prune_erased_messages_or_stubs(self):
    &#34;&#34;&#34;Treats the prune_erased_messages_or_stubs as a read-only attribute.&#34;&#34;&#34;
    return self._prune_erased_messages_or_stubs</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.prune_messages"><code class="name">var <span class="ident">prune_messages</span></code></dt>
<dd>
<div class="desc"><p>Treats the prune_messages as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1479-L1482" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def prune_messages(self):
    &#34;&#34;&#34;Treats the prune_messages as a read-only attribute.&#34;&#34;&#34;
    return self._prune_messages</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.prune_stubs"><code class="name">var <span class="ident">prune_stubs</span></code></dt>
<dd>
<div class="desc"><p>Treats the prune_stubs as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1488-L1491" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def prune_stubs(self):
    &#34;&#34;&#34;Treats the prune_stubs as a read-only attribute.&#34;&#34;&#34;
    return self._prune_stubs</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.skip_unread_messages"><code class="name">var <span class="ident">skip_unread_messages</span></code></dt>
<dd>
<div class="desc"><p>Treats the skip_unread_messages as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1497-L1500" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def skip_unread_messages(self):
    &#34;&#34;&#34;Treats the skip_unread_messages as a read-only attribute.&#34;&#34;&#34;
    return self._skip_unread_messages</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.stop_archive_if_size"><code class="name">var <span class="ident">stop_archive_if_size</span></code></dt>
<dd>
<div class="desc"><p>Treats the stop_archive_if_size as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1506-L1509" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def stop_archive_if_size(self):
    &#34;&#34;&#34;Treats the stop_archive_if_size as a read-only attribute.&#34;&#34;&#34;
    return self._stop_archive_if_size</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.truncate_body"><code class="name">var <span class="ident">truncate_body</span></code></dt>
<dd>
<div class="desc"><p>Treats the truncate_body as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1515-L1518" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def truncate_body(self):
    &#34;&#34;&#34;Treats the truncate_body as a read-only attribute.&#34;&#34;&#34;
    return self._truncate_body</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.truncate_body_to_bytes"><code class="name">var <span class="ident">truncate_body_to_bytes</span></code></dt>
<dd>
<div class="desc"><p>Treats the truncate_body_to_bytes as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1524-L1527" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def truncate_body_to_bytes(self):
    &#34;&#34;&#34;Treats the truncate_body_to_bytes as a read-only attribute.&#34;&#34;&#34;
    return self._truncate_body_to_bytes</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.used_disk_space"><code class="name">var <span class="ident">used_disk_space</span></code></dt>
<dd>
<div class="desc"><p>Treats the used_disk_space as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1533-L1536" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def used_disk_space(self):
    &#34;&#34;&#34;Treats the used_disk_space as a read-only attribute.&#34;&#34;&#34;
    return self._used_disk_space</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.CleanupPolicy.used_disk_space_value"><code class="name">var <span class="ident">used_disk_space_value</span></code></dt>
<dd>
<div class="desc"><p>Treats the used_disk_space_value as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1542-L1545" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def used_disk_space_value(self):
    &#34;&#34;&#34;Treats the used_disk_space_value as a read-only attribute.&#34;&#34;&#34;
    return self._used_disk_space_value</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicies"><code class="flex name class">
<span>class <span class="ident">ConfigurationPolicies</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the Configuration policies associated with the commcell.</p>
<p>Initialize object of the ConfigurationPolicies class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the ConfigurationPolicies class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L104-L489" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ConfigurationPolicies(object):
    &#34;&#34;&#34;Class for getting all the Configuration policies associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the ConfigurationPolicies class.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of the ConfigurationPolicies class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._POLICY = self._services[&#39;GET_CONFIGURATION_POLICIES&#39;]
        self._POLICY_FS = self._services[&#39;GET_CONFIGURATION_POLICIES_FS&#39;]
        self._CREATE_TASK = self._services[&#39;CREATE_TASK&#39;]
        self._policies = None
        self._ci_policies = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the ConfigurationPolicies class.&#34;&#34;&#34;
        return &#34;ConfigurationPolicies class instance for Commcell&#34;

    def _get_policies(self):
        &#34;&#34;&#34;Gets all the Configuration policies associated to the
            commcell specified by commcell object.

            Returns:
                dict    -   consists of all Configuration policies of the commcell

                    {
                        &#34;configuration_policy1_name&#34;: configuration_policy1_id,

                        &#34;configuration_policy2_name&#34;: configuration_policy2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._POLICY)

        if flag:
            if response.json() and &#39;policies&#39; in response.json():
                policies = response.json()[&#39;policies&#39;]

                if policies == []:
                    return {}

                policies_dict = {}

                for policy in policies:
                    temp_name = policy[&#39;policyEntity&#39;][&#39;policyName&#39;].lower()
                    temp_id = str(policy[&#39;policyEntity&#39;][&#39;policyId&#39;]).lower()
                    temp_policytype = str(policy[&#39;detail&#39;][&#39;emailPolicy&#39;]
                                          [&#39;emailPolicyType&#39;]).lower()
                    policies_dict[temp_name] = [temp_id, temp_policytype]

                return policies_dict
            else:
                return {}
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def has_policy(self, policy_name):
        &#34;&#34;&#34;Checks if a Configuration policy exists in the commcell with
            the input Configuration policy name.

            Args:
                policy_name     (str)   --  name of the Configuration policy

            Returns:
                bool    -   boolean output whether the Configuration policy exists in the commcell
                or not

            Raises:
                SDKException:
                    if type of the configuration policy name argument is not string

        &#34;&#34;&#34;
        if not isinstance(policy_name, str):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)

        return (self._policies and policy_name.lower() in self._policies) or \
               (self._ci_policies and policy_name.lower() in self._ci_policies)

    def _get_ci_policies(self):
        &#34;&#34;&#34;Gets all the Content Indexing policies associated to the commcell specified by commcell object.

            Returns:
                 dict    -   consists of all Configuration policies of the commcell
                            {
                                &#34;ci_policy1_name&#34;: [ci_policy1_id, ci_policy_type],

                                &#34;ci_policy2_name&#34;: [ci_policy2_id, ci_policy_type]
                            }

            Raises:
                SDKException:
                        if response is empty

                        if response is not success

                &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._POLICY_FS)

        if flag:
            policies_dict = {}
            if response.json() and &#39;policies&#39; in response.json():
                policies = response.json()[&#39;policies&#39;]
                for policy in policies:
                    temp_name = policy[&#39;policyEntity&#39;][&#39;policyName&#39;].lower()
                    temp_id = str(policy[&#39;policyEntity&#39;][&#39;policyId&#39;]).lower()
                    temp_policy_type = str(policy[&#39;detail&#39;][&#39;filePolicy&#39;]
                                          [&#39;filePolicyType&#39;]).lower()
                    policies_dict[temp_name] = [temp_id, temp_policy_type]
            return policies_dict
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_policy_id(self, policy_name):

        if not isinstance(policy_name, str):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)
        if policy_name.lower() in self._policies:
            return self._policies[policy_name.lower()][0]
        if policy_name.lower() in self._ci_policies:
            return self._ci_policies[policy_name.lower()][0]

    def get(self, configuration_policy_name, policy_type):
        &#34;&#34;&#34;Returns a ConfigurationPolicy object of the specified Configuration policy name.

            Args:
                configuration_policy_name     (str)   --  name of the configuration policy
                policy_type                    (str)   --  type of the policy

            Returns:
                object - instance of the ConfigurationPolicy class for the given policy name

            Raises:
                SDKException:
                    if type of the Configuration policy name argument is not string

                    if no Configuration policy exists with the given name
        &#34;&#34;&#34;
        if not isinstance(configuration_policy_name, str):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)

        if self.has_policy(configuration_policy_name):
            return ConfigurationPolicy(
                self._commcell_object, configuration_policy_name, self._get_policy_id(
                    configuration_policy_name)
            )

        else:
            raise SDKException(
                &#39;ConfigurationPolicies&#39;, &#39;102&#39;, &#39;No policy exists with name: {0}&#39;.format(
                    configuration_policy_name)
            )

    def get_policy_object(self, policy_type, configuration_policy_name):
        &#34;&#34;&#34;Get a  Policy object based on policy type

            Args:
                policy_type                 (str)   --  type of policy to create the object of

                    Valid values are:

                        - Archive

                        - Cleanup

                        - Retention

                        - Journal

                        - Content Indexing

                configuration_policy_name   (str)   --  name of the configuration Policy

            Returns:
                object  -   instance of the appropriate Policy class


        &#34;&#34;&#34;

        policy_types = {
            &#34;Archive&#34;: ArchivePolicy,
            &#34;Journal&#34;: JournalPolicy,
            &#34;Cleanup&#34;: CleanupPolicy,
            &#34;Retention&#34;: RetentionPolicy,
            &#34;ContentIndexing&#34;: ContentIndexingPolicy
        }

        try:
            return policy_types[policy_type](self._commcell_object, configuration_policy_name)
        except KeyError:
            raise SDKException(
                &#39;ConfigurationPolicies&#39;,
                &#39;102&#39;,
                &#39;Policy Type {} is not supported&#39;.format(policy_type)
            )

    def run_content_indexing(self, ci_policy_name):
        &#34;&#34;&#34;Runs Content indexing job from the CI policy level

            Args:
                ci_policy_name      -       Content indexing policy name

            Returns:
                Job                 -       Job class object for the CI Job

            Raises:
                SDKException:
                    No CI policy exists     -   if given policy name does not exist
                    Failed to run CI job    -   if CI job failed to start
                    Response was not success
                    Response received is empty

        &#34;&#34;&#34;
        if not self.has_policy(ci_policy_name):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, f&#39;No CI policy exists with name: {ci_policy_name}&#39;)
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;policyType&#34;: 0,
                    &#34;taskId&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 5022
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: {
                                    &#34;fileAnalytics&#34;: False,
                                    &#34;subClientBasedAnalytics&#34;: False
                                },
                                &#34;contentIndexingPolicyOption&#34;: {
                                    &#34;policyId&#34;: int(self._get_policy_id(ci_policy_name)),
                                    &#34;policyName&#34;: ci_policy_name,
                                    &#34;policyDetailType&#34;: 5,
                                    &#34;policyType&#34;: 2
                                }
                            }
                        }
                    }
                ]
            }
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_TASK, request_json)
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Content Index job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, o_str)
                raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, &#39;Failed to run the content indexing job&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, configuration_policy_name):
        &#34;&#34;&#34;Deletes a Configuration policy from the commcell.

            Args:
                configuration_policy_name (str)  --  name of the configuration policy to delete

            Raises:
                SDKException:
                    if type of the configuration policy name argument is not string

                    if failed to delete configuration policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(configuration_policy_name, str):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)

        if self.has_policy(configuration_policy_name):
            policy_delete_service = self._services[&#39;DELETE_CONFIGURATION_POLICY&#39;] % (
                str(self._get_policy_id(configuration_policy_name)))

            flag, response = self._cvpysdk_object.make_request(
                &#39;DELETE&#39;, policy_delete_service
            )

            if flag:
                try:
                    if response.json():
                        if response.json()[&#39;errorCode&#39;] != 0:
                            error_message = response.json()[&#39;errorMessage&#39;]
                            o_str = &#39;Failed to delete Configuration policy\nError: &#34;{0}&#34;&#39;

                            raise SDKException(
                                &#39;ConfigurationPolicies&#39;, &#39;102&#39;, o_str.format(error_message))
                except ValueError:
                    if response.text:
                        self.refresh()
                        return response.text.strip()
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;ConfigurationPolicies&#39;, &#39;102&#39;, &#39;No policy exists with name: {0}&#39;.format(
                    configuration_policy_name)
            )

    def add_policy(self, policy_object):
        &#34;&#34;&#34;Adds a new Configuration Policy to the Commcell.

            Args:
                policy_object(object)         --  policy onject based on type
                                                    of policy
            Raises:
                SDKException:
                    if failed to create configuration policy

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        json = policy_object._initialize_policy_json()
        configuration_policy_name = policy_object.name.lower()

        create_configuration_policy = self._services[&#39;CREATE_CONFIGURATION_POLICIES&#39;]

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, create_configuration_policy, json
        )

        if flag:
            if response.json():
                if &#39;policy&#39; in response.json():
                    # initialize the policies again
                    # so the policies object has all the policies
                    self.refresh()
                    return ConfigurationPolicy(
                        self._commcell_object, configuration_policy_name,
                        self._get_policy_id(configuration_policy_name)
                    )
                elif &#39;error&#39; in response.json():
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create Configuration policy\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the Virtual Machine policies.&#34;&#34;&#34;
        self._policies = self._get_policies()
        self._ci_policies = self._get_ci_policies()</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicies.add_policy"><code class="name flex">
<span>def <span class="ident">add_policy</span></span>(<span>self, policy_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new Configuration Policy to the Commcell.</p>
<h2 id="args">Args</h2>
<p>policy_object(object)
&ndash;
policy onject based on type
of policy</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to create configuration policy</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L441-L484" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_policy(self, policy_object):
    &#34;&#34;&#34;Adds a new Configuration Policy to the Commcell.

        Args:
            policy_object(object)         --  policy onject based on type
                                                of policy
        Raises:
            SDKException:
                if failed to create configuration policy

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    json = policy_object._initialize_policy_json()
    configuration_policy_name = policy_object.name.lower()

    create_configuration_policy = self._services[&#39;CREATE_CONFIGURATION_POLICIES&#39;]

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, create_configuration_policy, json
    )

    if flag:
        if response.json():
            if &#39;policy&#39; in response.json():
                # initialize the policies again
                # so the policies object has all the policies
                self.refresh()
                return ConfigurationPolicy(
                    self._commcell_object, configuration_policy_name,
                    self._get_policy_id(configuration_policy_name)
                )
            elif &#39;error&#39; in response.json():
                error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                o_str = &#39;Failed to create Configuration policy\nError: &#34;{0}&#34;&#39;

                raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, o_str.format(error_message))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicies.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, configuration_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes a Configuration policy from the commcell.</p>
<h2 id="args">Args</h2>
<p>configuration_policy_name (str)
&ndash;
name of the configuration policy to delete</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the configuration policy name argument is not string</p>
<pre><code>if failed to delete configuration policy

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L390-L439" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, configuration_policy_name):
    &#34;&#34;&#34;Deletes a Configuration policy from the commcell.

        Args:
            configuration_policy_name (str)  --  name of the configuration policy to delete

        Raises:
            SDKException:
                if type of the configuration policy name argument is not string

                if failed to delete configuration policy

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if not isinstance(configuration_policy_name, str):
        raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)

    if self.has_policy(configuration_policy_name):
        policy_delete_service = self._services[&#39;DELETE_CONFIGURATION_POLICY&#39;] % (
            str(self._get_policy_id(configuration_policy_name)))

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, policy_delete_service
        )

        if flag:
            try:
                if response.json():
                    if response.json()[&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to delete Configuration policy\nError: &#34;{0}&#34;&#39;

                        raise SDKException(
                            &#39;ConfigurationPolicies&#39;, &#39;102&#39;, o_str.format(error_message))
            except ValueError:
                if response.text:
                    self.refresh()
                    return response.text.strip()
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    else:
        raise SDKException(
            &#39;ConfigurationPolicies&#39;, &#39;102&#39;, &#39;No policy exists with name: {0}&#39;.format(
                configuration_policy_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicies.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, configuration_policy_name, policy_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a ConfigurationPolicy object of the specified Configuration policy name.</p>
<h2 id="args">Args</h2>
<p>configuration_policy_name
(str)
&ndash;
name of the configuration policy
policy_type
(str)
&ndash;
type of the policy</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the ConfigurationPolicy class for the given policy name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the Configuration policy name argument is not string</p>
<pre><code>if no Configuration policy exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L245-L274" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, configuration_policy_name, policy_type):
    &#34;&#34;&#34;Returns a ConfigurationPolicy object of the specified Configuration policy name.

        Args:
            configuration_policy_name     (str)   --  name of the configuration policy
            policy_type                    (str)   --  type of the policy

        Returns:
            object - instance of the ConfigurationPolicy class for the given policy name

        Raises:
            SDKException:
                if type of the Configuration policy name argument is not string

                if no Configuration policy exists with the given name
    &#34;&#34;&#34;
    if not isinstance(configuration_policy_name, str):
        raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)

    if self.has_policy(configuration_policy_name):
        return ConfigurationPolicy(
            self._commcell_object, configuration_policy_name, self._get_policy_id(
                configuration_policy_name)
        )

    else:
        raise SDKException(
            &#39;ConfigurationPolicies&#39;, &#39;102&#39;, &#39;No policy exists with name: {0}&#39;.format(
                configuration_policy_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicies.get_policy_object"><code class="name flex">
<span>def <span class="ident">get_policy_object</span></span>(<span>self, policy_type, configuration_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Get a
Policy object based on policy type</p>
<h2 id="args">Args</h2>
<p>policy_type
(str)
&ndash;
type of policy to create the object of</p>
<pre><code>Valid values are:

    - Archive

    - Cleanup

    - Retention

    - Journal

    - Content Indexing
</code></pre>
<p>configuration_policy_name
(str)
&ndash;
name of the configuration Policy</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the appropriate Policy class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L276-L317" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_policy_object(self, policy_type, configuration_policy_name):
    &#34;&#34;&#34;Get a  Policy object based on policy type

        Args:
            policy_type                 (str)   --  type of policy to create the object of

                Valid values are:

                    - Archive

                    - Cleanup

                    - Retention

                    - Journal

                    - Content Indexing

            configuration_policy_name   (str)   --  name of the configuration Policy

        Returns:
            object  -   instance of the appropriate Policy class


    &#34;&#34;&#34;

    policy_types = {
        &#34;Archive&#34;: ArchivePolicy,
        &#34;Journal&#34;: JournalPolicy,
        &#34;Cleanup&#34;: CleanupPolicy,
        &#34;Retention&#34;: RetentionPolicy,
        &#34;ContentIndexing&#34;: ContentIndexingPolicy
    }

    try:
        return policy_types[policy_type](self._commcell_object, configuration_policy_name)
    except KeyError:
        raise SDKException(
            &#39;ConfigurationPolicies&#39;,
            &#39;102&#39;,
            &#39;Policy Type {} is not supported&#39;.format(policy_type)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicies.has_policy"><code class="name flex">
<span>def <span class="ident">has_policy</span></span>(<span>self, policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a Configuration policy exists in the commcell with
the input Configuration policy name.</p>
<h2 id="args">Args</h2>
<p>policy_name
(str)
&ndash;
name of the Configuration policy</p>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean output whether the Configuration policy exists in the commcell
or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the configuration policy name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L179-L199" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_policy(self, policy_name):
    &#34;&#34;&#34;Checks if a Configuration policy exists in the commcell with
        the input Configuration policy name.

        Args:
            policy_name     (str)   --  name of the Configuration policy

        Returns:
            bool    -   boolean output whether the Configuration policy exists in the commcell
            or not

        Raises:
            SDKException:
                if type of the configuration policy name argument is not string

    &#34;&#34;&#34;
    if not isinstance(policy_name, str):
        raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)

    return (self._policies and policy_name.lower() in self._policies) or \
           (self._ci_policies and policy_name.lower() in self._ci_policies)</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicies.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the Virtual Machine policies.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L486-L489" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the Virtual Machine policies.&#34;&#34;&#34;
    self._policies = self._get_policies()
    self._ci_policies = self._get_ci_policies()</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicies.run_content_indexing"><code class="name flex">
<span>def <span class="ident">run_content_indexing</span></span>(<span>self, ci_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs Content indexing job from the CI policy level</p>
<h2 id="args">Args</h2>
<p>ci_policy_name
-
Content indexing policy name</p>
<h2 id="returns">Returns</h2>
<p>Job
-
Job class object for the CI Job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
No CI policy exists
-
if given policy name does not exist
Failed to run CI job
-
if CI job failed to start
Response was not success
Response received is empty</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L319-L388" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_content_indexing(self, ci_policy_name):
    &#34;&#34;&#34;Runs Content indexing job from the CI policy level

        Args:
            ci_policy_name      -       Content indexing policy name

        Returns:
            Job                 -       Job class object for the CI Job

        Raises:
            SDKException:
                No CI policy exists     -   if given policy name does not exist
                Failed to run CI job    -   if CI job failed to start
                Response was not success
                Response received is empty

    &#34;&#34;&#34;
    if not self.has_policy(ci_policy_name):
        raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, f&#39;No CI policy exists with name: {ci_policy_name}&#39;)
    request_json = {
        &#34;taskInfo&#34;: {
            &#34;task&#34;: {
                &#34;taskType&#34;: 1,
                &#34;initiatedFrom&#34;: 1,
                &#34;policyType&#34;: 0,
                &#34;taskId&#34;: 0,
                &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 5022
                    },
                    &#34;options&#34;: {
                        &#34;adminOpts&#34;: {
                            &#34;contentIndexingOption&#34;: {
                                &#34;fileAnalytics&#34;: False,
                                &#34;subClientBasedAnalytics&#34;: False
                            },
                            &#34;contentIndexingPolicyOption&#34;: {
                                &#34;policyId&#34;: int(self._get_policy_id(ci_policy_name)),
                                &#34;policyName&#34;: ci_policy_name,
                                &#34;policyDetailType&#34;: 5,
                                &#34;policyType&#34;: 2
                            }
                        }
                    }
                }
            ]
        }
    }
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._CREATE_TASK, request_json)
    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]

                o_str = &#39;Content Index job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, o_str)
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;102&#39;, &#39;Failed to run the content indexing job&#39;)
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    response_string = self._commcell_object._update_response_(response.text)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicy"><code class="flex name class">
<span>class <span class="ident">ConfigurationPolicy</span></span>
<span>(</span><span>commcell_object, configuration_policy_name, configuration_policy_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing a single Configuration Policy. Contains method definitions for
common operations among all Configuration Policies</p>
<p>Initialize object of the ConfigurationPolicy class.
Args:
commcell_object
(object)
&ndash;
instance of the Commcell class
configuration_policy_name
(str)
&ndash;
configuration_policy_id
(int)
&ndash;
Returns:
object - instance of the ConfigurationPolicies class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L492-L537" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ConfigurationPolicy(object):

    &#34;&#34;&#34;Class for representing a single Configuration Policy. Contains method definitions for
        common operations among all Configuration Policies&#34;&#34;&#34;

    def __init__(self, commcell_object, configuration_policy_name, configuration_policy_id=None):
        &#34;&#34;&#34;
        Initialize object of the ConfigurationPolicy class.
            Args:
                commcell_object    (object)  --  instance of the Commcell class
                configuration_policy_name     (str)     --
                configuration_policy_id       (int)     --
            Returns:
                object - instance of the ConfigurationPolicies class
        &#34;&#34;&#34;

        self._configuration_policy_name = configuration_policy_name
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        if configuration_policy_id:
            self._configuration_policy_id = str(configuration_policy_id)
        else:
            self._configuration_policy_id = self._get_configuration_policy_id()

        self._CONGIGURATION_POLICY = self._services[&#39;GET_CONFIGURATION_POLICY&#39;] % (
            self._configuration_policy_id
        )

    @property
    def configuration_policy_id(self):
        &#34;&#34;&#34;Treats the configuration policy id as a read-only attribute.&#34;&#34;&#34;
        return self._configuration_policy_id

    @property
    def configuration_policy_name(self):
        &#34;&#34;&#34;Treats the configuration policy name as a read-only attribute.&#34;&#34;&#34;
        return self._configuration_policy_name

    def _get_configuration_policy_id(self):
        &#34;&#34;&#34;Gets the Configuration policy id asscoiated with the Configuration policy&#34;&#34;&#34;

        configuration_policies = ConfigurationPolicies(self._commcell_object)
        return configuration_policies._get_policy_id(self._configuration_policy_name)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicy.configuration_policy_id"><code class="name">var <span class="ident">configuration_policy_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the configuration policy id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L523-L526" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def configuration_policy_id(self):
    &#34;&#34;&#34;Treats the configuration policy id as a read-only attribute.&#34;&#34;&#34;
    return self._configuration_policy_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ConfigurationPolicy.configuration_policy_name"><code class="name">var <span class="ident">configuration_policy_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the configuration policy name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L528-L531" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def configuration_policy_name(self):
    &#34;&#34;&#34;Treats the configuration policy name as a read-only attribute.&#34;&#34;&#34;
    return self._configuration_policy_name</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ContentIndexingPolicy"><code class="flex name class">
<span>class <span class="ident">ContentIndexingPolicy</span></span>
<span>(</span><span>commcell_object, ci_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing Content Indexing policy operations for a specific CI policy</p>
<p>Initialise the Content indexing Policy class instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1751-L1886" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ContentIndexingPolicy():
    &#34;&#34;&#34;Class for performing Content Indexing policy operations for a specific CI policy&#34;&#34;&#34;

    def __init__(self, commcell_object, ci_policy_name):
        &#34;&#34;&#34;Initialise the Content indexing Policy class instance.&#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._name = ci_policy_name
        self._file_policy_type = 5
        self._includeDocTypes = &#34;*.bmp,*.csv,*.doc,*.docx,*.dot,*.eml,*.htm,*.html,*.jpeg,*.jpg,&#34; \
                                &#34;*.log,*.msg,*.odg,*.odp,*.ods,*.odt,*.pages,*.pdf,*.png,*.ppt,&#34; \
                                &#34;*.pptx,*.rtf,*.txt,*.xls,*.xlsx,*.xmind,*.xml&#34;
        self._index_server_name = None
        self._data_access_node = None
        self._exclude_paths = [&#34;C:\\Program Files&#34;, &#34;C:\\Program Files (x86)&#34;, &#34;C:\\Windows&#34;]
        self._min_doc_size = 0
        self._max_doc_size = 50

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, value):
        &#34;&#34;&#34;Sets the value of name&#34;&#34;&#34;
        self._name = value

    @property
    def include_doc_types(self):
        &#34;&#34;&#34;Treats the include_doc_types as a read-only attribute.&#34;&#34;&#34;
        return self._includeDocTypes

    @include_doc_types.setter
    def include_doc_types(self, value):
        &#34;&#34;&#34;Sets the value of include docs type&#34;&#34;&#34;
        self._includeDocTypes = value

    @property
    def index_server_name(self):
        &#34;&#34;&#34;Treats the index_server_name as a read-only attribute.&#34;&#34;&#34;
        return self._index_server_name

    @index_server_name.setter
    def index_server_name(self, value):
        &#34;&#34;&#34;Sets the value of index server name&#34;&#34;&#34;
        self._index_server_name = value

    @property
    def data_access_node(self):
        &#34;&#34;&#34;Treats the data_access_node as a read-only attribute.&#34;&#34;&#34;
        return self._data_access_node

    @data_access_node.setter
    def data_access_node(self, value):
        &#34;&#34;&#34;Sets the value of data access node&#34;&#34;&#34;
        self._data_access_node = value

    @property
    def min_doc_size(self):
        &#34;&#34;&#34;Treats the min_doc_size as a read-only attribute.&#34;&#34;&#34;
        return self._min_doc_size

    @min_doc_size.setter
    def min_doc_size(self, value):
        &#34;&#34;&#34;Sets the value of minimum doc size&#34;&#34;&#34;
        self._min_doc_size = value

    @property
    def max_doc_size(self):
        &#34;&#34;&#34;Treats the max_doc_size as a read-only attribute.&#34;&#34;&#34;
        return self._max_doc_size

    @max_doc_size.setter
    def max_doc_size(self, value):
        &#34;&#34;&#34;Sets the value of maximum doc size&#34;&#34;&#34;
        self._max_doc_size = value

    @property
    def exclude_paths(self):
        &#34;&#34;&#34;Treats the exclude_paths as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_paths

    @exclude_paths.setter
    def exclude_paths(self, value):
        &#34;&#34;&#34;Sets the value of exclude paths&#34;&#34;&#34;
        self._exclude_paths = value

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;
        if not isinstance(self._index_server_name, str) or not isinstance(self._data_access_node, str) \
            or not isinstance(self._exclude_paths, list) or not isinstance(self._includeDocTypes, str) \
                or not isinstance(self._name, str) or not isinstance(self._min_doc_size, int) \
                or not isinstance(self._max_doc_size, int):
            raise SDKException(&#39;ConfigurationPolicies&#39;, &#39;101&#39;)
        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 2,
                &#34;flags&#34;: 0,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 33
                },
                &#34;detail&#34;: {
                    &#34;filePolicy&#34;: {
                        &#34;filePolicyType&#34;: self._file_policy_type,
                        &#34;contentIndexingPolicy&#34;: {
                            &#34;includeDocTypes&#34;: self._includeDocTypes,
                            &#34;copyPrecedence&#34;: 1,
                            &#34;minDocSize&#34;: self._min_doc_size,
                            &#34;searchEngineId&#34;: int(self._commcell_object.index_servers.
                                                  get(self._index_server_name).index_server_client_id),
                            &#34;contentIndexVersionsAfterNumberOfDays&#34;: -1,
                            &#34;maxDocSize&#34;: self._max_doc_size,
                            &#34;globalFilterFlag&#34;: 0,
                            &#34;excludePaths&#34;: self._exclude_paths,
                            &#34;dataAccessNodes&#34;: {
                                &#34;numberOfStreams&#34;: 0,
                                &#34;dataAccessNodes&#34;: [
                                    {
                                        &#34;clientName&#34;: self._data_access_node,
                                        &#34;clientId&#34;: int(self._commcell_object.clients.
                                                        get(self._data_access_node).client_id),
                                        &#34;_type_&#34;: 3
                                    }
                                ]
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self._name
                }
            }
        }
        return policy_json</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.data_access_node"><code class="name">var <span class="ident">data_access_node</span></code></dt>
<dd>
<div class="desc"><p>Treats the data_access_node as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1798-L1801" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_access_node(self):
    &#34;&#34;&#34;Treats the data_access_node as a read-only attribute.&#34;&#34;&#34;
    return self._data_access_node</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.exclude_paths"><code class="name">var <span class="ident">exclude_paths</span></code></dt>
<dd>
<div class="desc"><p>Treats the exclude_paths as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1828-L1831" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exclude_paths(self):
    &#34;&#34;&#34;Treats the exclude_paths as a read-only attribute.&#34;&#34;&#34;
    return self._exclude_paths</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.include_doc_types"><code class="name">var <span class="ident">include_doc_types</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_doc_types as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1778-L1781" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_doc_types(self):
    &#34;&#34;&#34;Treats the include_doc_types as a read-only attribute.&#34;&#34;&#34;
    return self._includeDocTypes</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.index_server_name"><code class="name">var <span class="ident">index_server_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the index_server_name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1788-L1791" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_server_name(self):
    &#34;&#34;&#34;Treats the index_server_name as a read-only attribute.&#34;&#34;&#34;
    return self._index_server_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.max_doc_size"><code class="name">var <span class="ident">max_doc_size</span></code></dt>
<dd>
<div class="desc"><p>Treats the max_doc_size as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1818-L1821" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def max_doc_size(self):
    &#34;&#34;&#34;Treats the max_doc_size as a read-only attribute.&#34;&#34;&#34;
    return self._max_doc_size</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.min_doc_size"><code class="name">var <span class="ident">min_doc_size</span></code></dt>
<dd>
<div class="desc"><p>Treats the min_doc_size as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1808-L1811" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def min_doc_size(self):
    &#34;&#34;&#34;Treats the min_doc_size as a read-only attribute.&#34;&#34;&#34;
    return self._min_doc_size</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Treats the name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1768-L1771" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
    return self._name</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy"><code class="flex name class">
<span>class <span class="ident">JournalPolicy</span></span>
<span>(</span><span>commcell_object, journal_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing Journal policy operations for a specific journal policy</p>
<p>Initialise the Journal Policy class instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L998-L1300" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class JournalPolicy():

    &#34;&#34;&#34;Class for performing Journal policy operations for a specific journal policy&#34;&#34;&#34;

    def __init__(self, commcell_object, journal_policy_name):
        &#34;&#34;&#34;Initialise the Journal Policy class instance.&#34;&#34;&#34;

        self._commcell_object = commcell_object

        self._name = journal_policy_name
        self._commserver = commcell_object
        self._email_policy_type = 4
        self._complete_job_mapi_error = 0
        self._delete_archived_messages = True
        self._job_hours_run = 0
        self._job_messages_protected = 1
        self._include_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items&#34;
        self._exclude_folder_filter = &#34;Deleted Items,Drafts,Inbox,Sent Items,Junk Mail,Sync Issues&#34;
        self._exclude_message_class_filter = &#34;Appointments,Contacts,Schedules,Tasks&#34;
        self._content_index_behind_alert = False
        self._content_index_data_over = 0
        self._deferred_days = 0
        self._enable_content_index = False
        self._enable_deferred_days = False
        self._enable_preview_generation = False
        self._jobs_older_than = 0
        self._retention_days_for_ci = -1
        self._start_time = 0
        self._synchronize_on = False
        self._path = &#34;&#34;
        self._username = &#34;&#34;
        self._password = &#34;&#34;

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        self._name = name

    @property
    def email_policy_type(self):
        &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
        return self._email_policy_type

    @property
    def complete_job_mapi_error(self):
        &#34;&#34;&#34;Treats the complete_job_mapi_error as a read-only attribute.&#34;&#34;&#34;
        return self._complete_job_mapi_error

    @complete_job_mapi_error.setter
    def complete_job_mapi_error(self, complete_job_mapi_error):
        self._complete_job_mapi_error = complete_job_mapi_error

    @property
    def delete_archived_messages(self):
        &#34;&#34;&#34;Treats the delete_archived_messages as a read-only attribute.&#34;&#34;&#34;
        return self._delete_archived_messages

    @delete_archived_messages.setter
    def delete_archived_messages(self, delete_archived_messages):
        self._delete_archived_messages = delete_archived_messages

    @property
    def job_hours_run(self):
        &#34;&#34;&#34;Treats the job_hours_run as a read-only attribute.&#34;&#34;&#34;
        return self._job_hours_run

    @job_hours_run.setter
    def job_hours_run(self, job_hours_run):
        self._job_hours_run = job_hours_run

    @property
    def job_messages_protected(self):
        &#34;&#34;&#34;Treats the job_messages_protected as a read-only attribute.&#34;&#34;&#34;
        return self._job_messages_protected

    @job_messages_protected.setter
    def job_messages_protected(self, job_messages_protected):
        self._job_messages_protected = job_messages_protected

    @property
    def include_folder_filter(self):
        &#34;&#34;&#34;Treats the include_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._include_folder_filter

    @include_folder_filter.setter
    def include_folder_filter(self, include_folder_filter):
        self._include_folder_filter = include_folder_filter

    @property
    def exclude_folder_filter(self):
        &#34;&#34;&#34;Treats the exclude_folder_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_folder_filter

    @exclude_folder_filter.setter
    def exclude_folder_filter(self, exclude_folder_filter):
        self._exclude_folder_filter = exclude_folder_filter

    @property
    def exclude_message_class_filter(self):
        &#34;&#34;&#34;Treats the exclude_message_class_filter as a read-only attribute.&#34;&#34;&#34;
        return self._exclude_message_class_filter

    @exclude_message_class_filter.setter
    def exclude_message_class_filter(self, exclude_message_class_filter):
        self._exclude_message_class_filter = exclude_message_class_filter

    @property
    def content_index_behind_alert(self):
        &#34;&#34;&#34;Treats the content_index_behind_alert as a read-only attribute.&#34;&#34;&#34;
        return self._content_index_behind_alert

    @content_index_behind_alert.setter
    def content_index_behind_alert(self, content_index_behind_alert):
        self._content_index_behind_alert = content_index_behind_alert

    @property
    def content_index_data_over(self):
        &#34;&#34;&#34;Treats the content_index_data_over as a read-only attribute.&#34;&#34;&#34;
        return self._content_index_data_over

    @content_index_data_over.setter
    def content_index_data_over(self, content_index_data_over):
        self._content_index_data_over = content_index_data_over

    @property
    def deferred_days(self):
        &#34;&#34;&#34;Treats the deferred_days as a read-only attribute.&#34;&#34;&#34;
        return self._deferred_days

    @deferred_days.setter
    def deferred_days(self, deferred_days):
        self._deferred_days = deferred_days

    @property
    def enable_content_index(self):
        &#34;&#34;&#34;Treats the enable_content_index as a read-only attribute.&#34;&#34;&#34;
        return self._enable_content_index

    @enable_content_index.setter
    def enable_content_index(self, enable_content_index):
        self._enable_content_index = enable_content_index

    @property
    def enable_deferred_days(self):
        &#34;&#34;&#34;Treats the enable_deferred_days as a read-only attribute.&#34;&#34;&#34;
        return self._enable_deferred_days

    @enable_deferred_days.setter
    def enable_deferred_days(self, enable_deferred_days):
        self._enable_deferred_days = enable_deferred_days

    @property
    def enable_preview_generation(self):
        &#34;&#34;&#34;Treats the enable_preview_generation as a read-only attribute.&#34;&#34;&#34;
        return self.enable_preview_generation

    @enable_preview_generation.setter
    def enable_preview_generation(self, enable_preview_generation):
        self._enable_preview_generation = enable_preview_generation

    @property
    def jobs_older_than(self):
        &#34;&#34;&#34;Treats the jobs_older_than as a read-only attribute.&#34;&#34;&#34;
        return self._jobs_older_than

    @jobs_older_than.setter
    def jobs_older_than(self, jobs_older_than):
        self._jobs_older_than = jobs_older_than

    @property
    def retention_days_for_ci(self):
        &#34;&#34;&#34;Treats the retention_days_for_ci as a read-only attribute.&#34;&#34;&#34;
        return self._retention_days_for_ci

    @retention_days_for_ci.setter
    def retention_days_for_ci(self, retention_days_for_ci):
        self._retention_days_for_ci = retention_days_for_ci

    @property
    def start_time(self):
        &#34;&#34;&#34;Treats the start_time as a read-only attribute.&#34;&#34;&#34;
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        self._start_time = start_time

    @property
    def synchronize_on(self):
        &#34;&#34;&#34;Treats the synchronize_on as a read-only attribute.&#34;&#34;&#34;
        return self._synchronize_on

    @synchronize_on.setter
    def synchronize_on(self, synchronize_on):
        self._synchronize_on = synchronize_on

    @property
    def path(self):
        &#34;&#34;&#34;Treats the path as a read-only attribute.&#34;&#34;&#34;
        return self._path

    @path.setter
    def path(self, path):
        self._path = path

    @property
    def username(self):
        &#34;&#34;&#34;Treats the username as a read-only attribute.&#34;&#34;&#34;
        return self._username

    @username.setter
    def username(self, username):
        self._username = username

    @property
    def password(self):
        &#34;&#34;&#34;Treats the password as a read-only attribute.&#34;&#34;&#34;
        return self._password

    @password.setter
    def password(self, password):
        self._password = password

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;
        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 1,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 137
                },
                &#34;detail&#34;: {
                    &#34;emailPolicy&#34;: {
                        &#34;emailPolicyType&#34;: 4,
                        &#34;journalPolicy&#34;: {
                            &#34;deleteArchivedMessages&#34;: self._delete_archived_messages,
                            &#34;contentIndexProps&#34;: {
                                &#34;enableContentIndex&#34;: self._enable_content_index,
                                &#34;contentIndexBehindAlert&#34;: self.content_index_behind_alert,
                                &#34;synchronizeOn&#34;: self._synchronize_on,
                                &#34;contentIndexDataOver&#34;: self._content_index_data_over,
                                &#34;retentionDaysForCI&#34;: -self._retention_days_for_ci,
                                &#34;startTime&#34;: self._start_time,
                                &#34;jobsOlderThan&#34;: self._jobs_older_than,
                                &#34;enablePreviewGeneration&#34;: self._enable_preview_generation,
                                &#34;deferredDays&#34;: self._deferred_days,
                                &#34;enableDeferredDays&#34;: self._enable_deferred_days,
                                &#34;pattern&#34;: [
                                    {}
                                ],
                                &#34;previewPathDir&#34;: {
                                    &#34;path&#34;: self._path,
                                    &#34;userAccount&#34;: {
                                        &#34;userName&#34;: self._username,
                                        &#34;password&#34;: self._password

                                    }
                                }
                            },
                            &#34;excludeMessageClassFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Appointments&#34;,
                                    &#34;Contacts&#34;,
                                    &#34;Schedules&#34;,
                                    &#34;Tasks&#34;
                                ]
                            },
                            &#34;includeFolderFilter&#34;: {
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            },
                            &#34;excludeFolderFilter&#34;: {
                                &#34;folderPatternsSelected&#34;: [
                                    &#34;Junk Mail&#34;,
                                    &#34;Sync Issues&#34;
                                ],
                                &#34;folderPatternsAvailable&#34;: [
                                    &#34;Deleted Items&#34;,
                                    &#34;Drafts&#34;,
                                    &#34;Inbox&#34;,
                                    &#34;Sent Items&#34;
                                ]
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self._name
                }
            }
        }

        return policy_json</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.complete_job_mapi_error"><code class="name">var <span class="ident">complete_job_mapi_error</span></code></dt>
<dd>
<div class="desc"><p>Treats the complete_job_mapi_error as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1045-L1048" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def complete_job_mapi_error(self):
    &#34;&#34;&#34;Treats the complete_job_mapi_error as a read-only attribute.&#34;&#34;&#34;
    return self._complete_job_mapi_error</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.content_index_behind_alert"><code class="name">var <span class="ident">content_index_behind_alert</span></code></dt>
<dd>
<div class="desc"><p>Treats the content_index_behind_alert as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1108-L1111" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content_index_behind_alert(self):
    &#34;&#34;&#34;Treats the content_index_behind_alert as a read-only attribute.&#34;&#34;&#34;
    return self._content_index_behind_alert</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.content_index_data_over"><code class="name">var <span class="ident">content_index_data_over</span></code></dt>
<dd>
<div class="desc"><p>Treats the content_index_data_over as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1117-L1120" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content_index_data_over(self):
    &#34;&#34;&#34;Treats the content_index_data_over as a read-only attribute.&#34;&#34;&#34;
    return self._content_index_data_over</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.deferred_days"><code class="name">var <span class="ident">deferred_days</span></code></dt>
<dd>
<div class="desc"><p>Treats the deferred_days as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1126-L1129" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def deferred_days(self):
    &#34;&#34;&#34;Treats the deferred_days as a read-only attribute.&#34;&#34;&#34;
    return self._deferred_days</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.delete_archived_messages"><code class="name">var <span class="ident">delete_archived_messages</span></code></dt>
<dd>
<div class="desc"><p>Treats the delete_archived_messages as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1054-L1057" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def delete_archived_messages(self):
    &#34;&#34;&#34;Treats the delete_archived_messages as a read-only attribute.&#34;&#34;&#34;
    return self._delete_archived_messages</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.email_policy_type"><code class="name">var <span class="ident">email_policy_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the email_policy_type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1040-L1043" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def email_policy_type(self):
    &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
    return self._email_policy_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.enable_content_index"><code class="name">var <span class="ident">enable_content_index</span></code></dt>
<dd>
<div class="desc"><p>Treats the enable_content_index as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1135-L1138" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_content_index(self):
    &#34;&#34;&#34;Treats the enable_content_index as a read-only attribute.&#34;&#34;&#34;
    return self._enable_content_index</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.enable_deferred_days"><code class="name">var <span class="ident">enable_deferred_days</span></code></dt>
<dd>
<div class="desc"><p>Treats the enable_deferred_days as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1144-L1147" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_deferred_days(self):
    &#34;&#34;&#34;Treats the enable_deferred_days as a read-only attribute.&#34;&#34;&#34;
    return self._enable_deferred_days</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.enable_preview_generation"><code class="name">var <span class="ident">enable_preview_generation</span></code></dt>
<dd>
<div class="desc"><p>Treats the enable_preview_generation as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1153-L1156" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_preview_generation(self):
    &#34;&#34;&#34;Treats the enable_preview_generation as a read-only attribute.&#34;&#34;&#34;
    return self.enable_preview_generation</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.exclude_folder_filter"><code class="name">var <span class="ident">exclude_folder_filter</span></code></dt>
<dd>
<div class="desc"><p>Treats the exclude_folder_filter as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1090-L1093" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exclude_folder_filter(self):
    &#34;&#34;&#34;Treats the exclude_folder_filter as a read-only attribute.&#34;&#34;&#34;
    return self._exclude_folder_filter</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.exclude_message_class_filter"><code class="name">var <span class="ident">exclude_message_class_filter</span></code></dt>
<dd>
<div class="desc"><p>Treats the exclude_message_class_filter as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1099-L1102" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exclude_message_class_filter(self):
    &#34;&#34;&#34;Treats the exclude_message_class_filter as a read-only attribute.&#34;&#34;&#34;
    return self._exclude_message_class_filter</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.include_folder_filter"><code class="name">var <span class="ident">include_folder_filter</span></code></dt>
<dd>
<div class="desc"><p>Treats the include_folder_filter as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1081-L1084" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def include_folder_filter(self):
    &#34;&#34;&#34;Treats the include_folder_filter as a read-only attribute.&#34;&#34;&#34;
    return self._include_folder_filter</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.job_hours_run"><code class="name">var <span class="ident">job_hours_run</span></code></dt>
<dd>
<div class="desc"><p>Treats the job_hours_run as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1063-L1066" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def job_hours_run(self):
    &#34;&#34;&#34;Treats the job_hours_run as a read-only attribute.&#34;&#34;&#34;
    return self._job_hours_run</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.job_messages_protected"><code class="name">var <span class="ident">job_messages_protected</span></code></dt>
<dd>
<div class="desc"><p>Treats the job_messages_protected as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1072-L1075" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def job_messages_protected(self):
    &#34;&#34;&#34;Treats the job_messages_protected as a read-only attribute.&#34;&#34;&#34;
    return self._job_messages_protected</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.jobs_older_than"><code class="name">var <span class="ident">jobs_older_than</span></code></dt>
<dd>
<div class="desc"><p>Treats the jobs_older_than as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1162-L1165" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def jobs_older_than(self):
    &#34;&#34;&#34;Treats the jobs_older_than as a read-only attribute.&#34;&#34;&#34;
    return self._jobs_older_than</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Treats the name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1031-L1034" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
    return self._name</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.password"><code class="name">var <span class="ident">password</span></code></dt>
<dd>
<div class="desc"><p>Treats the password as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1216-L1219" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def password(self):
    &#34;&#34;&#34;Treats the password as a read-only attribute.&#34;&#34;&#34;
    return self._password</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.path"><code class="name">var <span class="ident">path</span></code></dt>
<dd>
<div class="desc"><p>Treats the path as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1198-L1201" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def path(self):
    &#34;&#34;&#34;Treats the path as a read-only attribute.&#34;&#34;&#34;
    return self._path</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.retention_days_for_ci"><code class="name">var <span class="ident">retention_days_for_ci</span></code></dt>
<dd>
<div class="desc"><p>Treats the retention_days_for_ci as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1171-L1174" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def retention_days_for_ci(self):
    &#34;&#34;&#34;Treats the retention_days_for_ci as a read-only attribute.&#34;&#34;&#34;
    return self._retention_days_for_ci</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.start_time"><code class="name">var <span class="ident">start_time</span></code></dt>
<dd>
<div class="desc"><p>Treats the start_time as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1180-L1183" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def start_time(self):
    &#34;&#34;&#34;Treats the start_time as a read-only attribute.&#34;&#34;&#34;
    return self._start_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.synchronize_on"><code class="name">var <span class="ident">synchronize_on</span></code></dt>
<dd>
<div class="desc"><p>Treats the synchronize_on as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1189-L1192" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def synchronize_on(self):
    &#34;&#34;&#34;Treats the synchronize_on as a read-only attribute.&#34;&#34;&#34;
    return self._synchronize_on</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.JournalPolicy.username"><code class="name">var <span class="ident">username</span></code></dt>
<dd>
<div class="desc"><p>Treats the username as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1207-L1210" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def username(self):
    &#34;&#34;&#34;Treats the username as a read-only attribute.&#34;&#34;&#34;
    return self._username</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.policies.configuration_policies.RetentionPolicy"><code class="flex name class">
<span>class <span class="ident">RetentionPolicy</span></span>
<span>(</span><span>commcell_object, retention_policy_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing Retention policy operations for a specific retention policy</p>
<p>Initialise the Rentention Policy class instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1655-L1748" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RetentionPolicy():
    &#34;&#34;&#34;Class for performing Retention policy operations for a specific retention policy&#34;&#34;&#34;

    def __init__(self, commcell_object, retention_policy_name):
        &#34;&#34;&#34;Initialise the Rentention Policy class instance.&#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._name = retention_policy_name
        self._email_policy_type = 3
        self._number_of_days_for_media_pruning = -1
        self._retention_type = 0
        self._exchange_folder_retention = False
        self._exchange_retention_tags = False

    @property
    def name(self):
        &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @name.setter
    def name(self, name):
        self._name = name

    @property
    def email_policy_type(self):
        &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
        return self._email_policy_type

    @property
    def days_for_media_pruning(self):
        &#34;&#34;&#34;Treats the number_of_days_for_media_pruning as a read-only attribute.&#34;&#34;&#34;
        return self._number_of_days_for_media_pruning

    @days_for_media_pruning.setter
    def days_for_media_pruning(self, days_for_media_pruning):
        self._number_of_days_for_media_pruning = days_for_media_pruning

    @property
    def retention_type(self):
        &#34;&#34;&#34;Treats the retention_type as a read-only attribute.&#34;&#34;&#34;
        return self._retention_type

    @retention_type.setter
    def retention_type(self, retention_type):
        self._retention_type = retention_type

    @property
    def exchange_folder_retention(self):
        &#34;&#34;&#34;Treats the exchange_folder_retention as a read-only attribute.&#34;&#34;&#34;
        return self._exchange_folder_retention

    @exchange_folder_retention.setter
    def exchange_folder_retention(self, exchange_folder_retention):
        self._exchange_folder_retention = exchange_folder_retention

    @property
    def exchange_retention_tags(self):
        &#34;&#34;&#34;Treats the exchange_retention_tags as a read-only attribute.&#34;&#34;&#34;
        return self._exchange_retention_tags

    @exchange_retention_tags.setter
    def exchange_retention_tags(self, exchange_retention_tags):
        self._exchange_retention_tags = exchange_retention_tags

    def _initialize_policy_json(self):
        &#34;&#34;&#34;
            sets values for creating the add policy json
        &#34;&#34;&#34;
        policy_json = {
            &#34;policy&#34;: {
                &#34;policyType&#34;: 1,
                &#34;agentType&#34;: {
                    &#34;appTypeId&#34;: 137
                },
                &#34;detail&#34;: {
                    &#34;emailPolicy&#34;: {
                        &#34;emailPolicyType&#34;: 3,
                        &#34;retentionPolicy&#34;: {
                            &#34;numOfDaysForMediaPruning&#34;: self.days_for_media_pruning,
                            &#34;type&#34;: self.retention_type,
                            &#34;advanceRetentionOption&#34;: {
                                &#34;bExchangeFoldersRetention&#34;: self.exchange_folder_retention,
                                &#34;bExchangeRetentionTags&#34;: self.exchange_retention_tags
                            }
                        }
                    }
                },
                &#34;policyEntity&#34;: {
                    &#34;policyName&#34;: self.name
                }
            }
        }

        return policy_json</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.policies.configuration_policies.RetentionPolicy.days_for_media_pruning"><code class="name">var <span class="ident">days_for_media_pruning</span></code></dt>
<dd>
<div class="desc"><p>Treats the number_of_days_for_media_pruning as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1683-L1686" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def days_for_media_pruning(self):
    &#34;&#34;&#34;Treats the number_of_days_for_media_pruning as a read-only attribute.&#34;&#34;&#34;
    return self._number_of_days_for_media_pruning</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.RetentionPolicy.email_policy_type"><code class="name">var <span class="ident">email_policy_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the email_policy_type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1678-L1681" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def email_policy_type(self):
    &#34;&#34;&#34;Treats the email_policy_type as a read-only attribute.&#34;&#34;&#34;
    return self._email_policy_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.RetentionPolicy.exchange_folder_retention"><code class="name">var <span class="ident">exchange_folder_retention</span></code></dt>
<dd>
<div class="desc"><p>Treats the exchange_folder_retention as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1701-L1704" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exchange_folder_retention(self):
    &#34;&#34;&#34;Treats the exchange_folder_retention as a read-only attribute.&#34;&#34;&#34;
    return self._exchange_folder_retention</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.RetentionPolicy.exchange_retention_tags"><code class="name">var <span class="ident">exchange_retention_tags</span></code></dt>
<dd>
<div class="desc"><p>Treats the exchange_retention_tags as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1710-L1713" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exchange_retention_tags(self):
    &#34;&#34;&#34;Treats the exchange_retention_tags as a read-only attribute.&#34;&#34;&#34;
    return self._exchange_retention_tags</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.RetentionPolicy.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Treats the name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1669-L1672" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Treats the name as a read-only attribute.&#34;&#34;&#34;
    return self._name</code></pre>
</details>
</dd>
<dt id="cvpysdk.policies.configuration_policies.RetentionPolicy.retention_type"><code class="name">var <span class="ident">retention_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the retention_type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/policies/configuration_policies.py#L1692-L1695" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def retention_type(self):
    &#34;&#34;&#34;Treats the retention_type as a read-only attribute.&#34;&#34;&#34;
    return self._retention_type</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.policies" href="index.html">cvpysdk.policies</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy" href="#cvpysdk.policies.configuration_policies.ArchivePolicy">ArchivePolicy</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.archive_mailbox" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.archive_mailbox">archive_mailbox</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.backup_deleted_item_retention" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.backup_deleted_item_retention">backup_deleted_item_retention</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.backup_stubs" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.backup_stubs">backup_stubs</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.content_index_behind_alert" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.content_index_behind_alert">content_index_behind_alert</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.content_index_data_over" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.content_index_data_over">content_index_data_over</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.deferred_days" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.deferred_days">deferred_days</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.disabled_mailbox" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.disabled_mailbox">disabled_mailbox</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.email_policy_type" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.email_policy_type">email_policy_type</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.enable_content_index" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.enable_content_index">enable_content_index</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.enable_deferred_days" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.enable_deferred_days">enable_deferred_days</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.enable_mailbox_quota" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.enable_mailbox_quota">enable_mailbox_quota</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.enable_preview_generation" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.enable_preview_generation">enable_preview_generation</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.exclude_folder_filter" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.exclude_folder_filter">exclude_folder_filter</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.exclude_message_class_filter" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.exclude_message_class_filter">exclude_message_class_filter</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.include_categories" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.include_categories">include_categories</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.include_discovery_holds_folder" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.include_discovery_holds_folder">include_discovery_holds_folder</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.include_folder_filter" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.include_folder_filter">include_folder_filter</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.include_messages_larger_than" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.include_messages_larger_than">include_messages_larger_than</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.include_messages_older_than" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.include_messages_older_than">include_messages_older_than</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.include_messages_with_attachements" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.include_messages_with_attachements">include_messages_with_attachements</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.include_purges_folder" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.include_purges_folder">include_purges_folder</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.include_version_folder" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.include_version_folder">include_version_folder</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.jobs_older_than" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.jobs_older_than">jobs_older_than</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.name" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.name">name</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.password" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.password">password</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.path" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.path">path</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.primary_mailbox" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.primary_mailbox">primary_mailbox</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.retention_days_for_ci" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.retention_days_for_ci">retention_days_for_ci</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.save_conversation_meta_data" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.save_conversation_meta_data">save_conversation_meta_data</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.skip_mailboxes_exceeded_quota" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.skip_mailboxes_exceeded_quota">skip_mailboxes_exceeded_quota</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.start_time" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.start_time">start_time</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.synchronize_on" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.synchronize_on">synchronize_on</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ArchivePolicy.username" href="#cvpysdk.policies.configuration_policies.ArchivePolicy.username">username</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy" href="#cvpysdk.policies.configuration_policies.CleanupPolicy">CleanupPolicy</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.add_recall_link" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.add_recall_link">add_recall_link</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.archive_if_size" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.archive_if_size">archive_if_size</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.archive_mailbox" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.archive_mailbox">archive_mailbox</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.collect_messages_days_after" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.collect_messages_days_after">collect_messages_days_after</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.collect_messages_larger_than" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.collect_messages_larger_than">collect_messages_larger_than</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.collect_messages_with_attachments" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.collect_messages_with_attachments">collect_messages_with_attachments</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.create_stubs" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.create_stubs">create_stubs</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.disabled_mailbox" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.disabled_mailbox">disabled_mailbox</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.email_policy_type" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.email_policy_type">email_policy_type</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.enable_message_rules" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.enable_message_rules">enable_message_rules</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.exclude_folder_filter" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.exclude_folder_filter">exclude_folder_filter</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.exclude_message_class_filter" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.exclude_message_class_filter">exclude_message_class_filter</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.include_folder_filter" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.include_folder_filter">include_folder_filter</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.leave_message_body" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.leave_message_body">leave_message_body</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.mailbox_quota" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.mailbox_quota">mailbox_quota</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.name" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.name">name</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.number_of_days_for_source_pruning" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.number_of_days_for_source_pruning">number_of_days_for_source_pruning</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.path" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.path">path</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.primary_mailbox" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.primary_mailbox">primary_mailbox</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.prune_erased_messages_or_stubs" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.prune_erased_messages_or_stubs">prune_erased_messages_or_stubs</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.prune_messages" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.prune_messages">prune_messages</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.prune_stubs" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.prune_stubs">prune_stubs</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.skip_unread_messages" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.skip_unread_messages">skip_unread_messages</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.stop_archive_if_size" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.stop_archive_if_size">stop_archive_if_size</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.truncate_body" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.truncate_body">truncate_body</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.truncate_body_to_bytes" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.truncate_body_to_bytes">truncate_body_to_bytes</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.used_disk_space" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.used_disk_space">used_disk_space</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.CleanupPolicy.used_disk_space_value" href="#cvpysdk.policies.configuration_policies.CleanupPolicy.used_disk_space_value">used_disk_space_value</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicies" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicies">ConfigurationPolicies</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicies.add_policy" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicies.add_policy">add_policy</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicies.delete" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicies.delete">delete</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicies.get" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicies.get">get</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicies.get_policy_object" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicies.get_policy_object">get_policy_object</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicies.has_policy" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicies.has_policy">has_policy</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicies.refresh" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicies.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicies.run_content_indexing" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicies.run_content_indexing">run_content_indexing</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicy" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicy">ConfigurationPolicy</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicy.configuration_policy_id" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicy.configuration_policy_id">configuration_policy_id</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ConfigurationPolicy.configuration_policy_name" href="#cvpysdk.policies.configuration_policies.ConfigurationPolicy.configuration_policy_name">configuration_policy_name</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.policies.configuration_policies.ContentIndexingPolicy" href="#cvpysdk.policies.configuration_policies.ContentIndexingPolicy">ContentIndexingPolicy</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.data_access_node" href="#cvpysdk.policies.configuration_policies.ContentIndexingPolicy.data_access_node">data_access_node</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.exclude_paths" href="#cvpysdk.policies.configuration_policies.ContentIndexingPolicy.exclude_paths">exclude_paths</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.include_doc_types" href="#cvpysdk.policies.configuration_policies.ContentIndexingPolicy.include_doc_types">include_doc_types</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.index_server_name" href="#cvpysdk.policies.configuration_policies.ContentIndexingPolicy.index_server_name">index_server_name</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.max_doc_size" href="#cvpysdk.policies.configuration_policies.ContentIndexingPolicy.max_doc_size">max_doc_size</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.min_doc_size" href="#cvpysdk.policies.configuration_policies.ContentIndexingPolicy.min_doc_size">min_doc_size</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.ContentIndexingPolicy.name" href="#cvpysdk.policies.configuration_policies.ContentIndexingPolicy.name">name</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy" href="#cvpysdk.policies.configuration_policies.JournalPolicy">JournalPolicy</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.complete_job_mapi_error" href="#cvpysdk.policies.configuration_policies.JournalPolicy.complete_job_mapi_error">complete_job_mapi_error</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.content_index_behind_alert" href="#cvpysdk.policies.configuration_policies.JournalPolicy.content_index_behind_alert">content_index_behind_alert</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.content_index_data_over" href="#cvpysdk.policies.configuration_policies.JournalPolicy.content_index_data_over">content_index_data_over</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.deferred_days" href="#cvpysdk.policies.configuration_policies.JournalPolicy.deferred_days">deferred_days</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.delete_archived_messages" href="#cvpysdk.policies.configuration_policies.JournalPolicy.delete_archived_messages">delete_archived_messages</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.email_policy_type" href="#cvpysdk.policies.configuration_policies.JournalPolicy.email_policy_type">email_policy_type</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.enable_content_index" href="#cvpysdk.policies.configuration_policies.JournalPolicy.enable_content_index">enable_content_index</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.enable_deferred_days" href="#cvpysdk.policies.configuration_policies.JournalPolicy.enable_deferred_days">enable_deferred_days</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.enable_preview_generation" href="#cvpysdk.policies.configuration_policies.JournalPolicy.enable_preview_generation">enable_preview_generation</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.exclude_folder_filter" href="#cvpysdk.policies.configuration_policies.JournalPolicy.exclude_folder_filter">exclude_folder_filter</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.exclude_message_class_filter" href="#cvpysdk.policies.configuration_policies.JournalPolicy.exclude_message_class_filter">exclude_message_class_filter</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.include_folder_filter" href="#cvpysdk.policies.configuration_policies.JournalPolicy.include_folder_filter">include_folder_filter</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.job_hours_run" href="#cvpysdk.policies.configuration_policies.JournalPolicy.job_hours_run">job_hours_run</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.job_messages_protected" href="#cvpysdk.policies.configuration_policies.JournalPolicy.job_messages_protected">job_messages_protected</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.jobs_older_than" href="#cvpysdk.policies.configuration_policies.JournalPolicy.jobs_older_than">jobs_older_than</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.name" href="#cvpysdk.policies.configuration_policies.JournalPolicy.name">name</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.password" href="#cvpysdk.policies.configuration_policies.JournalPolicy.password">password</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.path" href="#cvpysdk.policies.configuration_policies.JournalPolicy.path">path</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.retention_days_for_ci" href="#cvpysdk.policies.configuration_policies.JournalPolicy.retention_days_for_ci">retention_days_for_ci</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.start_time" href="#cvpysdk.policies.configuration_policies.JournalPolicy.start_time">start_time</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.synchronize_on" href="#cvpysdk.policies.configuration_policies.JournalPolicy.synchronize_on">synchronize_on</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.JournalPolicy.username" href="#cvpysdk.policies.configuration_policies.JournalPolicy.username">username</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.policies.configuration_policies.RetentionPolicy" href="#cvpysdk.policies.configuration_policies.RetentionPolicy">RetentionPolicy</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.policies.configuration_policies.RetentionPolicy.days_for_media_pruning" href="#cvpysdk.policies.configuration_policies.RetentionPolicy.days_for_media_pruning">days_for_media_pruning</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.RetentionPolicy.email_policy_type" href="#cvpysdk.policies.configuration_policies.RetentionPolicy.email_policy_type">email_policy_type</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.RetentionPolicy.exchange_folder_retention" href="#cvpysdk.policies.configuration_policies.RetentionPolicy.exchange_folder_retention">exchange_folder_retention</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.RetentionPolicy.exchange_retention_tags" href="#cvpysdk.policies.configuration_policies.RetentionPolicy.exchange_retention_tags">exchange_retention_tags</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.RetentionPolicy.name" href="#cvpysdk.policies.configuration_policies.RetentionPolicy.name">name</a></code></li>
<li><code><a title="cvpysdk.policies.configuration_policies.RetentionPolicy.retention_type" href="#cvpysdk.policies.configuration_policies.RetentionPolicy.retention_type">retention_type</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>