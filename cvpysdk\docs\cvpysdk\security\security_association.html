<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.security.security_association API documentation</title>
<meta name="description" content="Helper file to manage security associations on this commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.security.security_association</code></h1>
</header>
<section id="section-intro">
<p>Helper file to manage security associations on this commcell</p>
<p>SecurityAssociation is the only class defined in this file</p>
<h2 id="securityassociation">Securityassociation</h2>
<p><strong>init</strong>()
&ndash;
initializes security class object</p>
<p><strong>str</strong>()
&ndash;
returns all the users associated with the commcell</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the User class</p>
<p>_security_association_json()&ndash;
generates security association blob with all
user-entity-role association</p>
<p>fetch_security_association()&ndash;
fetches security associations from entity</p>
<p>_get_security_roles()
&ndash;
gets the list of all the security roles applicable
on this commcell</p>
<p>_add_security_association() &ndash;
adds the security association with client or clientgroup</p>
<p>has_role()
&ndash;
checks if specified role exists on commcell</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/security_association.py#L1-L421" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Helper file to manage security associations on this commcell

SecurityAssociation is the only class defined in this file

SecurityAssociation:
    __init__()                  --  initializes security class object

    __str__()                   --  returns all the users associated with the commcell

    __repr__()                  --  returns the string for the instance of the User class

    _security_association_json()--  generates security association blob with all
                                    user-entity-role association

    fetch_security_association()--  fetches security associations from entity

    _get_security_roles()       --  gets the list of all the security roles applicable
                                        on this commcell

    _add_security_association() --  adds the security association with client or clientgroup

    has_role()                  --  checks if specified role exists on commcell


&#34;&#34;&#34;

from ..exception import SDKException


class SecurityAssociation(object):
    &#34;&#34;&#34;Class for managing the security associations roles on the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, class_object):
        &#34;&#34;&#34;Initializes the security associations object

            Args:
                commcell_object     (object)     --     instance of the Commcell class

                class_object         (object)     --    instance of the class on which we want to
                                                            manage security operations
                                                        default: commcell object will be used
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        if not class_object:
            class_object = self._commcell_object

        from ..commcell import Commcell
        if isinstance(class_object, Commcell):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;commCellId&#34;: class_object.commcell_id,
                    &#34;_type_&#34;: 1
                }]
            }

        from ..client import Client
        if isinstance(class_object, Client):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;clientId&#34;: int(class_object.client_id),
                    &#34;_type_&#34;: 3
                }]
            }

        from ..storage_pool import StoragePool
        if isinstance(class_object, StoragePool):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;storagePolicyId&#34;: int(class_object.storage_pool_id),
                    &#34;_type_&#34;: 17
                }]
            }
            
        from ..plan import Plan
        if isinstance(class_object, Plan):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;planId&#34;: int(class_object.plan_id),
                    &#34;_type_&#34;: 158
                }]
            }

        from ..workflow import WorkFlow
        if isinstance(class_object, WorkFlow):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;workflowId&#34;: int(class_object.workflow_id),
                    &#34;_type_&#34;: 83,
                    &#34;entityType&#34;: 83
                }]
            }

        self._roles = self._get_security_roles()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all available security roles on this commcell.

            Returns:
                str - string of all the available security roles on this commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Roles&#39;)

        for index, role in enumerate(self._roles):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, role)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Security class.&#34;&#34;&#34;
        return &#34;Security class instance for Commcell&#34;

    @staticmethod
    def _security_association_json(entity_dictionary):
        &#34;&#34;&#34;handles three way associations (role-user-entities)

            Args:
                entity_dictionary   --      combination of entity_type, entity names
                                            and role
                e.g.: entity_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    }
                entity_type         --      key for the entity present in dictionary
                                            on which user will have access

                entity_name         --      Value of the key

                role                --      role will remain role in dictionary
                e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
                entity_type:    clientName, mediaAgentName, libraryName, userName,
                                userGroupName, storagePolicyName, clientGroupName,
                                schedulePolicyName, locationName, providerDomainName,
                                alertName, workflowName, policyName, roleName

                entity_name:    client name for entity_type &#39;clientName&#39;
                                Media agent name for entitytype &#39;mediaAgentName&#39;
                                similar for other entity_typees

                request_type        --      decides whether to ADD, DELETE or
                                            OVERWRITE user security association.

        &#34;&#34;&#34;
        complete_association = []
        for entity_value in entity_dictionary.values():
            for each_entity_key in entity_value:
                for element in entity_value[each_entity_key]:
                    if each_entity_key != &#34;role&#34;:
                        if each_entity_key == &#34;_type_&#34;:
                            association_blob = {
                                &#34;entities&#34;: {
                                    &#34;entity&#34;: [{
                                        each_entity_key: element,
                                        &#34;flags&#34;: {
                                            &#34;includeAll&#34;: True
                                        }
                                    }]
                                },
                                &#34;properties&#34;: {
                                    &#34;role&#34;: {
                                        &#34;roleName&#34;: entity_value[&#39;role&#39;][0]
                                    }
                                }
                            }
                        else:
                            association_blob = {
                                &#34;entities&#34;: {
                                    &#34;entity&#34;: [{
                                        each_entity_key: element
                                    }]
                                },
                                &#34;properties&#34;: {
                                    &#34;role&#34;: {
                                        &#34;roleName&#34;: entity_value[&#39;role&#39;][0]
                                    }
                                }
                            }
                        complete_association.append(association_blob)
        return complete_association

    @staticmethod
    def fetch_security_association(security_dict):
        &#34;&#34;&#34;Fetches security associations from entity
        Args:
            security_dict    (dict)   --  security association properties of entity

        Returns:
            formatted security association dictionary with custom permissions marked as invalid
        &#34;&#34;&#34;
        security_list = []
        count = 0
        associations = {}
        entity_permissions = {}
        for every_association in security_dict:
            if &#39;entity&#39; in every_association[&#39;entities&#39;]:
                entities = every_association[&#39;entities&#39;][&#39;entity&#39;]
                for entity in entities:
                    for each_key in entity:
                        if &#39;Name&#39; in each_key:
                            if &#39;externalGroupName&#39; in each_key:
                                associations = entity[each_key]
                            #if &#39;providerDomainName&#39; in each_key:
                                # No need to explicitely  check for provider key
                                if associations:
                                    ext_group = &#34;{0}\\{1}&#34;.format(entity[&#39;providerDomainName&#39;],
                                                                  associations)
                                    associations = {}
                                else:
                                    ext_group = entity[each_key]
                                security_list.append(each_key)
                                security_list.append(ext_group.lower())
                                break
                            elif &#39;displayName&#39; in each_key:
                                security_list.append(&#39;clientName&#39;)
                                security_list.append(entity[&#39;clientName&#39;].lower())
                                break
                            else:
                                security_list.append(each_key)
                                security_list.append(entity[each_key].lower())
                                break
                        elif &#39;flags&#39; in each_key:
                            security_list.append(entity[&#39;_type_&#39;])
                            security_list.append(entity[&#39;flags&#39;])

                    if &#39;role&#39; in every_association[&#39;properties&#39;]:
                        role_list = every_association[&#39;properties&#39;][&#39;role&#39;]
                        for entity in role_list:
                            if &#39;Name&#39; in entity:
                                security_list.append(role_list[entity].lower())
                    if &#39;categoryPermission&#39; in every_association[&#39;properties&#39;]:
                        categories = every_association[&#39;properties&#39;][
                            &#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;]
                        for key in categories:
                            categories = key
                            for permission in categories:
                                if &#39;Name&#39; in permission:
                                    security_list.append(categories[permission] + str(&#39;-invalid&#39;))
                                    #Not supporting custom permissions as of now.
                    entity_permissions.setdefault(count, security_list)
                    security_list = []
                    count += 1
        return entity_permissions


    def _get_security_roles(self):
        &#34;&#34;&#34;Returns the list of available roles on this commcell&#34;&#34;&#34;
        GET_SECURITY_ROLES = self._commcell_object._services[&#39;GET_SECURITY_ROLES&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, GET_SECURITY_ROLES
        )

        if flag:
            if response.json() and &#39;roleProperties&#39; in response.json():
                role_props = response.json()[&#39;roleProperties&#39;]

                roles = {}

                for role in role_props:
                    if &#39;role&#39; in role:
                        role_name = role[&#39;role&#39;][&#39;roleName&#39;].lower()
                        role_id = role[&#39;role&#39;][&#39;roleId&#39;]
                        roles[role_name] = role_id

                return roles
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _add_security_association(self, association_list, user= True, request_type = None, externalGroup = False):
        &#34;&#34;&#34;
        Adds the security association on the specified class object

        Supported Types : Client, Storage Pool class objects.

        Args:
            associations_list   (list)  --  list of users to be associated
                Example:
                    associations_list = [
                        {
                            &#39;user_name&#39;: user1,
                            &#39;role_name&#39;: role1
                        },
                        {
                            &#39;user_name&#39;: user2,
                            &#39;role_name&#39;: role2
                        }
                    ]
 
            user (bool)             --    True or False. set user = False, If associations_list made up of user groups
            request_type (str)      --    eg : &#39;OVERWRITE&#39; or &#39;UPDATE&#39; or &#39;DELETE&#39;, Default will be OVERWRITE operation
            externalGroup (bool)    --    True or False, set externalGroup = True. If Security associations is to be done on External User Groups

        Raises:
            SDKException:
                if association is not of dict type
                if role doesnot exists on Commcell
                if request fails
        &#34;&#34;&#34;

        update_operator_request_type = {
            &#34;OVERWRITE&#34;: 1,
            &#34;UPDATE&#34;: 2,
            &#34;DELETE&#34;: 3
        }

        if request_type:
            request_type = request_type.upper()

        security_association_list = []
        for association in association_list:
            if not isinstance(association, dict):
                raise SDKException(&#39;Security&#39;, &#39;101&#39;)

            if not self.has_role(association[&#39;role_name&#39;]):
                raise SDKException(
                    &#39;Security&#39;, &#39;102&#39;, &#39;Role {0} doesn\&#39;t exist&#39;.format(association[&#39;role_name&#39;])
                )

            user_or_group = {}
            if user:
                user_or_group = {&#39;userName&#39;: association[&#39;user_name&#39;]}
            elif externalGroup:
                user_or_group = {&#39;externalGroupName&#39;: association[&#39;user_name&#39;]}
            else:
                user_or_group = {&#39;userGroupName&#39;: association[&#39;user_name&#39;]}  

            temp = {
                &#34;userOrGroup&#34;: [
                    user_or_group
                ],
                &#34;properties&#34;: {
                    &#34;role&#34;: {
                        &#34;_type_&#34;: 120,
                        &#34;roleId&#34;: self._roles[association[&#39;role_name&#39;].lower()],
                        &#39;roleName&#39;: association[&#39;role_name&#39;]
                    }
                }
            }
            security_association_list.append(temp)

        request_json = {
            &#34;entityAssociated&#34;: self._entity_list,
            &#34;securityAssociations&#34;: {
                &#34;associationsOperationType&#34;: update_operator_request_type.get(request_type, 1),
                &#34;associations&#34;: security_association_list,
                &#34;ownerAssociations&#34;: {
                    &#34;ownersOperationType&#34;: 1
                }
            }
        }

        ADD_SECURITY_ASSOCIATION = self._commcell_object._services[&#39;SECURITY_ASSOCIATION&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, ADD_SECURITY_ASSOCIATION, request_json
        )

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response_json = response.json()[&#39;response&#39;][0]

                error_code = response_json[&#39;errorCode&#39;]

                if error_code != 0:
                    error_message = response_json[&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Security&#39;,
                        &#39;102&#39;,
                        &#39;Failed to add associations. \nError: {0}&#39;.format(error_message)
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def has_role(self, role_name):
        &#34;&#34;&#34;Checks if role with specified name exists

            Args:
                role_name     (str)     --     name of the role to be verified

            Returns:
                (bool)     -  True if role with specified name exists
        &#34;&#34;&#34;
        if not isinstance(role_name, str):
            raise SDKException(&#39;Security&#39;, &#39;101&#39;)

        return self._roles and role_name.lower() in self._roles</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.security.security_association.SecurityAssociation"><code class="flex name class">
<span>class <span class="ident">SecurityAssociation</span></span>
<span>(</span><span>commcell_object, class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for managing the security associations roles on the commcell</p>
<p>Initializes the security associations object</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>class_object
(object)
&ndash;
instance of the class on which we want to
manage security operations
default: commcell object will be used</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/security_association.py#L48-L420" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SecurityAssociation(object):
    &#34;&#34;&#34;Class for managing the security associations roles on the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, class_object):
        &#34;&#34;&#34;Initializes the security associations object

            Args:
                commcell_object     (object)     --     instance of the Commcell class

                class_object         (object)     --    instance of the class on which we want to
                                                            manage security operations
                                                        default: commcell object will be used
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        if not class_object:
            class_object = self._commcell_object

        from ..commcell import Commcell
        if isinstance(class_object, Commcell):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;commCellId&#34;: class_object.commcell_id,
                    &#34;_type_&#34;: 1
                }]
            }

        from ..client import Client
        if isinstance(class_object, Client):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;clientId&#34;: int(class_object.client_id),
                    &#34;_type_&#34;: 3
                }]
            }

        from ..storage_pool import StoragePool
        if isinstance(class_object, StoragePool):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;storagePolicyId&#34;: int(class_object.storage_pool_id),
                    &#34;_type_&#34;: 17
                }]
            }
            
        from ..plan import Plan
        if isinstance(class_object, Plan):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;planId&#34;: int(class_object.plan_id),
                    &#34;_type_&#34;: 158
                }]
            }

        from ..workflow import WorkFlow
        if isinstance(class_object, WorkFlow):
            self._entity_list = {
                &#34;entity&#34;: [{
                    &#34;workflowId&#34;: int(class_object.workflow_id),
                    &#34;_type_&#34;: 83,
                    &#34;entityType&#34;: 83
                }]
            }

        self._roles = self._get_security_roles()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all available security roles on this commcell.

            Returns:
                str - string of all the available security roles on this commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Roles&#39;)

        for index, role in enumerate(self._roles):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, role)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Security class.&#34;&#34;&#34;
        return &#34;Security class instance for Commcell&#34;

    @staticmethod
    def _security_association_json(entity_dictionary):
        &#34;&#34;&#34;handles three way associations (role-user-entities)

            Args:
                entity_dictionary   --      combination of entity_type, entity names
                                            and role
                e.g.: entity_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    }
                entity_type         --      key for the entity present in dictionary
                                            on which user will have access

                entity_name         --      Value of the key

                role                --      role will remain role in dictionary
                e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
                entity_type:    clientName, mediaAgentName, libraryName, userName,
                                userGroupName, storagePolicyName, clientGroupName,
                                schedulePolicyName, locationName, providerDomainName,
                                alertName, workflowName, policyName, roleName

                entity_name:    client name for entity_type &#39;clientName&#39;
                                Media agent name for entitytype &#39;mediaAgentName&#39;
                                similar for other entity_typees

                request_type        --      decides whether to ADD, DELETE or
                                            OVERWRITE user security association.

        &#34;&#34;&#34;
        complete_association = []
        for entity_value in entity_dictionary.values():
            for each_entity_key in entity_value:
                for element in entity_value[each_entity_key]:
                    if each_entity_key != &#34;role&#34;:
                        if each_entity_key == &#34;_type_&#34;:
                            association_blob = {
                                &#34;entities&#34;: {
                                    &#34;entity&#34;: [{
                                        each_entity_key: element,
                                        &#34;flags&#34;: {
                                            &#34;includeAll&#34;: True
                                        }
                                    }]
                                },
                                &#34;properties&#34;: {
                                    &#34;role&#34;: {
                                        &#34;roleName&#34;: entity_value[&#39;role&#39;][0]
                                    }
                                }
                            }
                        else:
                            association_blob = {
                                &#34;entities&#34;: {
                                    &#34;entity&#34;: [{
                                        each_entity_key: element
                                    }]
                                },
                                &#34;properties&#34;: {
                                    &#34;role&#34;: {
                                        &#34;roleName&#34;: entity_value[&#39;role&#39;][0]
                                    }
                                }
                            }
                        complete_association.append(association_blob)
        return complete_association

    @staticmethod
    def fetch_security_association(security_dict):
        &#34;&#34;&#34;Fetches security associations from entity
        Args:
            security_dict    (dict)   --  security association properties of entity

        Returns:
            formatted security association dictionary with custom permissions marked as invalid
        &#34;&#34;&#34;
        security_list = []
        count = 0
        associations = {}
        entity_permissions = {}
        for every_association in security_dict:
            if &#39;entity&#39; in every_association[&#39;entities&#39;]:
                entities = every_association[&#39;entities&#39;][&#39;entity&#39;]
                for entity in entities:
                    for each_key in entity:
                        if &#39;Name&#39; in each_key:
                            if &#39;externalGroupName&#39; in each_key:
                                associations = entity[each_key]
                            #if &#39;providerDomainName&#39; in each_key:
                                # No need to explicitely  check for provider key
                                if associations:
                                    ext_group = &#34;{0}\\{1}&#34;.format(entity[&#39;providerDomainName&#39;],
                                                                  associations)
                                    associations = {}
                                else:
                                    ext_group = entity[each_key]
                                security_list.append(each_key)
                                security_list.append(ext_group.lower())
                                break
                            elif &#39;displayName&#39; in each_key:
                                security_list.append(&#39;clientName&#39;)
                                security_list.append(entity[&#39;clientName&#39;].lower())
                                break
                            else:
                                security_list.append(each_key)
                                security_list.append(entity[each_key].lower())
                                break
                        elif &#39;flags&#39; in each_key:
                            security_list.append(entity[&#39;_type_&#39;])
                            security_list.append(entity[&#39;flags&#39;])

                    if &#39;role&#39; in every_association[&#39;properties&#39;]:
                        role_list = every_association[&#39;properties&#39;][&#39;role&#39;]
                        for entity in role_list:
                            if &#39;Name&#39; in entity:
                                security_list.append(role_list[entity].lower())
                    if &#39;categoryPermission&#39; in every_association[&#39;properties&#39;]:
                        categories = every_association[&#39;properties&#39;][
                            &#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;]
                        for key in categories:
                            categories = key
                            for permission in categories:
                                if &#39;Name&#39; in permission:
                                    security_list.append(categories[permission] + str(&#39;-invalid&#39;))
                                    #Not supporting custom permissions as of now.
                    entity_permissions.setdefault(count, security_list)
                    security_list = []
                    count += 1
        return entity_permissions


    def _get_security_roles(self):
        &#34;&#34;&#34;Returns the list of available roles on this commcell&#34;&#34;&#34;
        GET_SECURITY_ROLES = self._commcell_object._services[&#39;GET_SECURITY_ROLES&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, GET_SECURITY_ROLES
        )

        if flag:
            if response.json() and &#39;roleProperties&#39; in response.json():
                role_props = response.json()[&#39;roleProperties&#39;]

                roles = {}

                for role in role_props:
                    if &#39;role&#39; in role:
                        role_name = role[&#39;role&#39;][&#39;roleName&#39;].lower()
                        role_id = role[&#39;role&#39;][&#39;roleId&#39;]
                        roles[role_name] = role_id

                return roles
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _add_security_association(self, association_list, user= True, request_type = None, externalGroup = False):
        &#34;&#34;&#34;
        Adds the security association on the specified class object

        Supported Types : Client, Storage Pool class objects.

        Args:
            associations_list   (list)  --  list of users to be associated
                Example:
                    associations_list = [
                        {
                            &#39;user_name&#39;: user1,
                            &#39;role_name&#39;: role1
                        },
                        {
                            &#39;user_name&#39;: user2,
                            &#39;role_name&#39;: role2
                        }
                    ]
 
            user (bool)             --    True or False. set user = False, If associations_list made up of user groups
            request_type (str)      --    eg : &#39;OVERWRITE&#39; or &#39;UPDATE&#39; or &#39;DELETE&#39;, Default will be OVERWRITE operation
            externalGroup (bool)    --    True or False, set externalGroup = True. If Security associations is to be done on External User Groups

        Raises:
            SDKException:
                if association is not of dict type
                if role doesnot exists on Commcell
                if request fails
        &#34;&#34;&#34;

        update_operator_request_type = {
            &#34;OVERWRITE&#34;: 1,
            &#34;UPDATE&#34;: 2,
            &#34;DELETE&#34;: 3
        }

        if request_type:
            request_type = request_type.upper()

        security_association_list = []
        for association in association_list:
            if not isinstance(association, dict):
                raise SDKException(&#39;Security&#39;, &#39;101&#39;)

            if not self.has_role(association[&#39;role_name&#39;]):
                raise SDKException(
                    &#39;Security&#39;, &#39;102&#39;, &#39;Role {0} doesn\&#39;t exist&#39;.format(association[&#39;role_name&#39;])
                )

            user_or_group = {}
            if user:
                user_or_group = {&#39;userName&#39;: association[&#39;user_name&#39;]}
            elif externalGroup:
                user_or_group = {&#39;externalGroupName&#39;: association[&#39;user_name&#39;]}
            else:
                user_or_group = {&#39;userGroupName&#39;: association[&#39;user_name&#39;]}  

            temp = {
                &#34;userOrGroup&#34;: [
                    user_or_group
                ],
                &#34;properties&#34;: {
                    &#34;role&#34;: {
                        &#34;_type_&#34;: 120,
                        &#34;roleId&#34;: self._roles[association[&#39;role_name&#39;].lower()],
                        &#39;roleName&#39;: association[&#39;role_name&#39;]
                    }
                }
            }
            security_association_list.append(temp)

        request_json = {
            &#34;entityAssociated&#34;: self._entity_list,
            &#34;securityAssociations&#34;: {
                &#34;associationsOperationType&#34;: update_operator_request_type.get(request_type, 1),
                &#34;associations&#34;: security_association_list,
                &#34;ownerAssociations&#34;: {
                    &#34;ownersOperationType&#34;: 1
                }
            }
        }

        ADD_SECURITY_ASSOCIATION = self._commcell_object._services[&#39;SECURITY_ASSOCIATION&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, ADD_SECURITY_ASSOCIATION, request_json
        )

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response_json = response.json()[&#39;response&#39;][0]

                error_code = response_json[&#39;errorCode&#39;]

                if error_code != 0:
                    error_message = response_json[&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Security&#39;,
                        &#39;102&#39;,
                        &#39;Failed to add associations. \nError: {0}&#39;.format(error_message)
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def has_role(self, role_name):
        &#34;&#34;&#34;Checks if role with specified name exists

            Args:
                role_name     (str)     --     name of the role to be verified

            Returns:
                (bool)     -  True if role with specified name exists
        &#34;&#34;&#34;
        if not isinstance(role_name, str):
            raise SDKException(&#39;Security&#39;, &#39;101&#39;)

        return self._roles and role_name.lower() in self._roles</code></pre>
</details>
<h3>Static methods</h3>
<dl>
<dt id="cvpysdk.security.security_association.SecurityAssociation.fetch_security_association"><code class="name flex">
<span>def <span class="ident">fetch_security_association</span></span>(<span>security_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Fetches security associations from entity</p>
<h2 id="args">Args</h2>
<p>security_dict
(dict)
&ndash;
security association properties of entity</p>
<h2 id="returns">Returns</h2>
<p>formatted security association dictionary with custom permissions marked as invalid</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/security_association.py#L209-L270" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@staticmethod
def fetch_security_association(security_dict):
    &#34;&#34;&#34;Fetches security associations from entity
    Args:
        security_dict    (dict)   --  security association properties of entity

    Returns:
        formatted security association dictionary with custom permissions marked as invalid
    &#34;&#34;&#34;
    security_list = []
    count = 0
    associations = {}
    entity_permissions = {}
    for every_association in security_dict:
        if &#39;entity&#39; in every_association[&#39;entities&#39;]:
            entities = every_association[&#39;entities&#39;][&#39;entity&#39;]
            for entity in entities:
                for each_key in entity:
                    if &#39;Name&#39; in each_key:
                        if &#39;externalGroupName&#39; in each_key:
                            associations = entity[each_key]
                        #if &#39;providerDomainName&#39; in each_key:
                            # No need to explicitely  check for provider key
                            if associations:
                                ext_group = &#34;{0}\\{1}&#34;.format(entity[&#39;providerDomainName&#39;],
                                                              associations)
                                associations = {}
                            else:
                                ext_group = entity[each_key]
                            security_list.append(each_key)
                            security_list.append(ext_group.lower())
                            break
                        elif &#39;displayName&#39; in each_key:
                            security_list.append(&#39;clientName&#39;)
                            security_list.append(entity[&#39;clientName&#39;].lower())
                            break
                        else:
                            security_list.append(each_key)
                            security_list.append(entity[each_key].lower())
                            break
                    elif &#39;flags&#39; in each_key:
                        security_list.append(entity[&#39;_type_&#39;])
                        security_list.append(entity[&#39;flags&#39;])

                if &#39;role&#39; in every_association[&#39;properties&#39;]:
                    role_list = every_association[&#39;properties&#39;][&#39;role&#39;]
                    for entity in role_list:
                        if &#39;Name&#39; in entity:
                            security_list.append(role_list[entity].lower())
                if &#39;categoryPermission&#39; in every_association[&#39;properties&#39;]:
                    categories = every_association[&#39;properties&#39;][
                        &#39;categoryPermission&#39;][&#39;categoriesPermissionList&#39;]
                    for key in categories:
                        categories = key
                        for permission in categories:
                            if &#39;Name&#39; in permission:
                                security_list.append(categories[permission] + str(&#39;-invalid&#39;))
                                #Not supporting custom permissions as of now.
                entity_permissions.setdefault(count, security_list)
                security_list = []
                count += 1
    return entity_permissions</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.security.security_association.SecurityAssociation.has_role"><code class="name flex">
<span>def <span class="ident">has_role</span></span>(<span>self, role_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if role with specified name exists</p>
<h2 id="args">Args</h2>
<p>role_name
(str)
&ndash;
name of the role to be verified</p>
<h2 id="returns">Returns</h2>
<p>(bool)
-
True if role with specified name exists</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/security_association.py#L408-L420" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_role(self, role_name):
    &#34;&#34;&#34;Checks if role with specified name exists

        Args:
            role_name     (str)     --     name of the role to be verified

        Returns:
            (bool)     -  True if role with specified name exists
    &#34;&#34;&#34;
    if not isinstance(role_name, str):
        raise SDKException(&#39;Security&#39;, &#39;101&#39;)

    return self._roles and role_name.lower() in self._roles</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.security" href="index.html">cvpysdk.security</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.security.security_association.SecurityAssociation" href="#cvpysdk.security.security_association.SecurityAssociation">SecurityAssociation</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.security.security_association.SecurityAssociation.fetch_security_association" href="#cvpysdk.security.security_association.SecurityAssociation.fetch_security_association">fetch_security_association</a></code></li>
<li><code><a title="cvpysdk.security.security_association.SecurityAssociation.has_role" href="#cvpysdk.security.security_association.SecurityAssociation.has_role">has_role</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>