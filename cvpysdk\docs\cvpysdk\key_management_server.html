<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.key_management_server API documentation</title>
<meta name="description" content="Main file for performing Key Management Server operations on commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.key_management_server</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing Key Management Server operations on commcell</p>
<p>This file has all the classes related to Key Management Server operations.</p>
<p>KeyManagementServerConstants &ndash;
Abstract class to define the key management server related
constancts</p>
<p>KeyManagementServers
&ndash;
Class for representing all the KMS in the commcell.</p>
<p>KeyManagementServer
&ndash;
Class for representing a single KMS in the commcell.</p>
<h1 id="keymanagementserverconstants-attributes">KeyManagementServerConstants Attributes</h1>
<pre><code>**_KMS_TYPE**           --    dictionary of key management server types
**_KMS_AUTHENTICATION_TYPE** -- dictionary of key management server authentication
</code></pre>
<h1 id="keymanagementservers-attributes">KeyManagementServers Attributes</h1>
<pre><code>**_kms_dict**           --    a name-indexed dictionary of KeyManagementServer objects
</code></pre>
<h1 id="keymanagementservers">KeyManagementServers:</h1>
<pre><code>__init__()              --      initializes KeyManagementServers class object

_get_kms_dict()         --      fetches the dictionary of all Key Management Servers

get()                   --      gets a specific Key Management Server object

get_all_kms()           --      gets the dictionary of all Key Management Servers

refresh()               --      refreshes the dictionary of Key Management Servers

delete()                --      deletes a Key Management Server

has_kms()               --      checks if the Key Management Server exists or not

add_aws_kms()           --      configures AWS Key Management Server with key based authentication

_add_aws_kms_with_cred_file() --  configures AWS KMS with credential file based authentication

_add_aws_kms_with_iam() --      configures AWS KMS with IAM based authentication

_add_azure_key_vault_certificate_auth() -- Configure Azure Key Management Server with AD-app certificate based authentication

_add_azure_key_vault_iam_auth() -- Configure Azure Key Management Server with IAM managed identity based authentication

_add_kmip_certificate()         --  Configure KMIP supported Key Management Server with certificate based authentication

_kms_api_call() --              call KMS API
</code></pre>
<h1 id="keymanagementserver">KeyManagementServer:</h1>
<pre><code>__init__()              --      initializes KeyManagementServer class object

_get_name_from_type()   --      returns the type name for type id
</code></pre>
<h1 id="keymanagementserver-attributes">KeyManagementServer Attributes</h1>
<pre><code>**name**                --    name of the Key Management Server
**id**                  --    id of the Key Management Server
**type_id**             --    type id of the Key Management Server
**type_name**           --    type name of the Key Management Server
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L1-L1118" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing Key Management Server operations on commcell

This file has all the classes related to Key Management Server operations.

KeyManagementServerConstants --  Abstract class to define the key management server related  constancts

KeyManagementServers        --   Class for representing all the KMS in the commcell.

KeyManagementServer         --   Class for representing a single KMS in the commcell.


KeyManagementServerConstants Attributes
=======================================
    **_KMS_TYPE**           --    dictionary of key management server types
    **_KMS_AUTHENTICATION_TYPE** -- dictionary of key management server authentication
    

KeyManagementServers Attributes
==========================

    **_kms_dict**           --    a name-indexed dictionary of KeyManagementServer objects
    

KeyManagementServers:
=================

    __init__()              --      initializes KeyManagementServers class object

    _get_kms_dict()         --      fetches the dictionary of all Key Management Servers

    get()                   --      gets a specific Key Management Server object

    get_all_kms()           --      gets the dictionary of all Key Management Servers

    refresh()               --      refreshes the dictionary of Key Management Servers

    delete()                --      deletes a Key Management Server

    has_kms()               --      checks if the Key Management Server exists or not

    add_aws_kms()           --      configures AWS Key Management Server with key based authentication
    
    _add_aws_kms_with_cred_file() --  configures AWS KMS with credential file based authentication
    
    _add_aws_kms_with_iam() --      configures AWS KMS with IAM based authentication
    
    _add_azure_key_vault_certificate_auth() -- Configure Azure Key Management Server with AD-app certificate based authentication

    _add_azure_key_vault_iam_auth() -- Configure Azure Key Management Server with IAM managed identity based authentication
    
    _add_kmip_certificate()         --  Configure KMIP supported Key Management Server with certificate based authentication
    
    _kms_api_call() --              call KMS API
    

KeyManagementServer:
=================

    __init__()              --      initializes KeyManagementServer class object

    _get_name_from_type()   --      returns the type name for type id
    
KeyManagementServer Attributes
==========================

    **name**                --    name of the Key Management Server
    **id**                  --    id of the Key Management Server
    **type_id**             --    type id of the Key Management Server
    **type_name**           --    type name of the Key Management Server

&#34;&#34;&#34;

from .exception import SDKException
from abc import ABC


class KeyManagementServerConstants(ABC):

    def __init__(self):
        self._KMS_TYPE = {
            1: &#34;KEY_PROVIDER_COMMVAULT&#34;,
            2: &#34;KEY_PROVIDER_KMIP&#34;,
            3: &#34;KEY_PROVIDER_AWS_KMS&#34;,
            4: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
            5: &#34;KEY_PROVIDER_SAFENET&#34;,
            6: &#34;KEY_PROVIDER_PASSPHRASE&#34;,
        }

        self._KMS_AUTHENTICATION_TYPE = {
            &#34;AWS_KEYS&#34;: 0,
            &#34;AWS_IAM&#34;: 1,
            &#34;AWS_CREDENTIALS_FILE&#34;: 0,
            &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;: 1,
            &#34;AZURE_KEY_VAULT_IAM&#34;: 3,
            &#34;AZURE_KEY_VAULT_KEY&#34;: 2,
            &#34;KMIP_CERTIFICATE&#34;: 99
        }

class KeyManagementServers(KeyManagementServerConstants):
    &#34;&#34;&#34;Class for representing all the KMS in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes KeyManagementServers class object

            Args:
                commcell    (object)    --  instance of commcell

        &#34;&#34;&#34;
        KeyManagementServerConstants.__init__(self)
        self._commcell = commcell

        self._cvpysdk_object = commcell._cvpysdk_object
        self._services = commcell._services

        self._KMS_ADD_GET = self._services[&#39;KEY_MANAGEMENT_SERVER_ADD_GET&#39;]
        self._KMS_DELETE = self._services[&#39;KEY_MANAGEMENT_SERVER_DELETE&#39;]
        self._kms_dict = None
        self.refresh()

    def _get_kms_dict(self):
        &#34;&#34;&#34;Fetches the name-indexed dictionary of all Key Management Servers

            Returns:
                the name-indexed dictionary of Key Management Server info
                {
                    name1: {
                       name: name1, 
                       id: id1,
                       type_id: type_id1,
                    },
                    ...
                }

            Raises SDKException:
                    If failed to fetch the list
        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._KMS_ADD_GET)

        if not flag:
            raise SDKException(&#34;Response&#34;, 101)

        if not response.json() or &#39;keyProviders&#39; not in response.json():
            return {}

        key_providers = response.json()[&#34;keyProviders&#34;]
        kms_dict = {}
        for key_provider in key_providers:
            type = key_provider.get(&#34;keyProviderType&#34;)
            
            provider = key_provider.get(&#34;provider&#34;)
            name = provider.get(&#34;keyProviderName&#34;, &#34;&#34;).lower()
            id = provider.get(&#34;keyProviderId&#34;)
            
            kms_dict[name] = {
                &#34;name&#34;: name,
                &#34;id&#34;: id,
                &#34;type_id&#34;: type,
            }

        return kms_dict
    
    def _validate_input(input_value, input_type, exception_id=101):
        &#34;&#34;&#34;Raises SDKException if input_value doesn&#39;t match input_type
        
            Args:
                input_value     (any)   --  The value to check

                input_type      (type)  --  The type to check against.
                                            For int type, the input can be int-convertible

                exception_id    (int)   --  The exception id to throw
                                            defaults to 101

            Raises SDKException:
                If type mismatch was found
        &#34;&#34;&#34; 
        # if int, then try to convert and then check
        if input_type == int:
            try:
                input_value = int(input_value)
            except ValueError as e:
                pass
        
        if not isinstance(input_value, input_type):
            message = f&#34;Received: {type(input_value)}. Expected: {input_type}&#34;
            raise SDKException(&#34;KeyManagementServer&#34;, exception_id, message)
    
    def get(self, kms_name):
        &#34;&#34;&#34;Gets a specific Key Management Server object
        
            Args:
                kms_name    (str)       -- The Key Management Server to get

            Returns:
                kms         (object)    --  The KeyManagementServer object
            
            Raises SDKException:
                If kms_name is not str

                If Key Management Server not found
        &#34;&#34;&#34;      
        if not self.has_kms(kms_name):
            raise SDKException(&#34;KeyManagementServer&#34;, 102)
        
        kms_info = self._kms_dict[kms_name.lower()]
        kms_obj = KeyManagementServer(self._commcell, kms_info[&#39;name&#39;], kms_info[&#39;id&#39;], kms_info[&#39;type_id&#39;])
        return kms_obj


    def get_all_kms(self):
        &#34;&#34;&#34;Gets the name-indexed dictionary of all Key Management Servers

            Returns:
                the name-indexed dictionary of Key Management Server info
                {
                    name1: {
                       name: name1, 
                       id: id1,
                       type_id: type_id1,
                    },
                    ...
                }
                
        &#34;&#34;&#34;
        return self._kms_dict

    def refresh(self):
        &#34;&#34;&#34;Refreshes the dictionary of Key Management Servers&#34;&#34;&#34;
        self._kms_dict = self._get_kms_dict()

    def delete(self, kms_name):
        &#34;&#34;&#34;Deletes a Key Management Server

            Args:
                kms_name (string) -- name of the Key Management Server

            Raises SDKException:
                    If API response code is not successfull

                    If response JSON is empty

                    If errorCode is not part of the response JSON
        &#34;&#34;&#34;
        if not self.has_kms(kms_name):
            raise SDKException(&#39;KeyManagementServer&#39;, 102)

        kms_id = self._kms_dict[kms_name.lower()][&#39;id&#39;]

        kms_service = self._KMS_DELETE % (kms_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, kms_service)

        if not flag:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#34;Response&#34;, 101, response_string)

        if not response.json():
            raise SDKException(&#34;Response&#34;, 102)

        if &#34;errorCode&#34; not in response.json():
            raise SDKException(
                &#34;Response&#34;, 101, f&#34;Something went wrong while deleting {kms_name}&#34;)

        error_code = response.json()[&#34;errorCode&#34;]
        if error_code != 0:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#34;Response&#34;, 101, response_string)

        
    
    def has_kms(self, kms_name):
        &#34;&#34;&#34;Check if the Key Management Server exist or not

            Args:
                kms_name    (str)   -- name of the Key Management Server

            Returns:
                result      (bool)  -- whether Key Management Server exists or not
            
            Raises SDKException:
                If kms_name is not string
        &#34;&#34;&#34;
        KeyManagementServers._validate_input(kms_name, str)
        
        return kms_name.lower() in self._kms_dict
 

    def _add_aws_kms_with_cred_file(self, kms_details):
            &#34;&#34;&#34;Configure AWS Key Management Server with credential file based authentication

                :arg
                    kms_details ( dictionary ) - Dictionary with AWS KMS details
                :return:
                    Object of KeyManagementServer class for the newly created KMS.
            &#34;&#34;&#34;

            if &#34;ACCESS_NODE_NAME&#34; in kms_details:
                payload = {
                    &#34;keyProvider&#34;: {

                        &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#34;KMS_NAME&#34;]
                        },
                        &#34;encryptionType&#34;: 3,
                        &#34;keyProviderType&#34;: 3,

                        &#34;properties&#34;: {
                            &#34;accessNodes&#34;: [
                                {
                                    &#34;accessNode&#34;: {
                                        &#34;clientName&#34;: kms_details[&#34;ACCESS_NODE_NAME&#34;]
                                    },
                                    &#34;awsCredential&#34;: {
                                        &#34;profile&#34;: kms_details[&#34;AWS_CREDENTIALS_FILE_PROFILE_NAME&#34;],
                                        &#34;amazonAuthenticationType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#34;KEY_PROVIDER_AUTH_TYPE&#34;]]
                                    }
                                }
                            ],
                            &#34;bringYourOwnKey&#34;: 0,
                            &#34;regionName&#34;: kms_details[&#34;AWS_REGION_NAME&#34;]
                        }

                    }
                }

                self._kms_api_call(payload)


    def _add_aws_kms_with_iam(self, kms_details):
        &#34;&#34;&#34;Configure AWS Key Management Server with IMA based authentication

            :arg
                kms_details ( dictionary ) - Dictionary with AWS KMS details
            :return:
                Object of KeyManagementServer class for the newly created KMS.
        &#34;&#34;&#34;

        if &#34;ACCESS_NODE_NAME&#34; in kms_details:

            payload= {
                        &#34;keyProvider&#34;: {
                            &#34;provider&#34;: {
                                    &#34;keyProviderName&#34;: kms_details[&#34;KMS_NAME&#34;]
                                    },
                                &#34;encryptionType&#34;: 3,
                                &#34;keyProviderType&#34;: 3,
                                &#34;properties&#34;: {
                                        &#34;accessNodes&#34;: [
                                                {
                                                        &#34;accessNode&#34;: {
                                                                &#34;clientName&#34;: kms_details[&#34;ACCESS_NODE_NAME&#34;]
                                                        },
                                                        &#34;awsCredential&#34;: {
                                                            &#34;amazonAuthenticationType&#34;:1
                                                        }
                                                }
                                            ],
                                            &#34;bringYourOwnKey&#34;: 0,
                                            &#34;regionName&#34;: kms_details[&#34;AWS_REGION_NAME&#34;]
                                    }

                            }
                    }

            self._kms_api_call(payload)

    def _add_azure_key_vault_key_auth(self, kms_details):
        &#34;&#34;&#34;Configure Azure Key Management Server with AD-app key based authentication

            :arg
                kms_details ( dictionary ) - Dictionary with AWS KMS details
            :return:
                Object of KeyManagementServer class for the newly created KMS.
        &#34;&#34;&#34;
        payload = None
        is_bring_your_own_key = 0
        keys = []
        
        if &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34; not in kms_details:
            kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;] = 3072
        
        if &#34;BringYourOwnKey&#34; in kms_details:
            if kms_details[&#34;BringYourOwnKey&#34;]:
                if &#34;KEYS&#34; not in kms_details:
                    raise SDKException(&#39;KeyManagementServer&#39;, 107)
                if type(kms_details[&#39;KEYS&#39;]) != list :
                    raise SDKException(&#39;Storage&#39;, 101)
                is_bring_your_own_key = 1
                for k in kms_details[&#39;KEYS&#39;]:
                    keys.append({&#34;keyId&#34;: k})
                    
        if &#34;ACCESS_NODE_NAME&#34; in kms_details:
            payload= {
                        &#34;keyProvider&#34;: {
                        &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                        &#34;encryptionType&#34;: 1001,
                        &#34;keyProviderType&#34;: 4,
                        &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                        },
                        &#34;properties&#34;: {
                            &#34;accessNodes&#34;: [
                            {
                                &#34;accessNode&#34;: {
                                &#34;clientName&#34;: kms_details[&#39;ACCESS_NODE_NAME&#39;]
                                },
                            &#34;keyVaultCredential&#34;: {
                            &#34;authType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#34;KEY_PROVIDER_AUTH_TYPE&#34;]],
                            &#34;applicationId&#34;: kms_details[&#39;AZURE_APP_ID&#39;],
                            &#34;tenantId&#34;: kms_details[&#39;AZURE_TENANT_ID&#39;],
                            &#34;environment&#34;: &#34;AzureCloud&#34;,
                            &#34;endpoints&#34;: {
                                &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                            },
                        &#34;certPassword&#34;: kms_details[&#39;AZURE_APP_SECRET&#39;],
                        &#34;overrideCredentials&#34;: True,
                        &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;]
                        }
                        }
                        ],
                        &#34;bringYourOwnKey&#34;: is_bring_your_own_key,
                         &#34;keys&#34;: keys
                    }
                }
            }

            
        else:
            payload = {
                &#34;keyProvider&#34;: {
                    &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                    &#34;encryptionType&#34;: 1001,
                    &#34;keyProviderType&#34;: 4,
                    &#34;provider&#34;: {
                        &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                    },
                &#34;properties&#34;: {
                    &#34;keyVaultCredential&#34;: {
                    &#34;authType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#34;KEY_PROVIDER_AUTH_TYPE&#34;]],
                    &#34;applicationId&#34;: kms_details[&#39;AZURE_APP_ID&#39;],
                    &#34;tenantId&#34;: kms_details[&#39;AZURE_TENANT_ID&#39;],
                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;],
                    &#34;environment&#34;: &#34;AzureCloud&#34;,
                    &#34;endpoints&#34;: {
                    &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                    &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                    }
                    },
                &#34;sslPassPhrase&#34;: kms_details[&#39;AZURE_APP_SECRET&#39;],
                &#34;bringYourOwnKey&#34;: is_bring_your_own_key,
                 &#34;keys&#34;: keys
                }
                }
            }     

        self._kms_api_call(payload)
        
    
    def _add_azure_key_vault_certificate_auth(self, kms_details):
        &#34;&#34;&#34;Configure Azure Key Management Server with AD-app certificate based authentication

            :arg
                kms_details ( dictionary ) - Dictionary with AWS KMS details
            :return:
                Object of KeyManagementServer class for the newly created KMS.
        &#34;&#34;&#34;
        payload = None
        is_bring_your_own_key = 0
        keys = []

        if &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34; not in kms_details:
            kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;] = 3072

        if &#34;BringYourOwnKey&#34; in kms_details:
            if kms_details[&#34;BringYourOwnKey&#34;]:
                if &#34;KEYS&#34; not in kms_details:
                    raise SDKException(&#39;KeyManagementServer&#39;, 107)
                if type(kms_details[&#39;KEYS&#39;]) != list :
                    raise SDKException(&#39;Storage&#39;, 101)
                is_bring_your_own_key = 1
                for k in kms_details[&#39;KEYS&#39;]:
                    keys.append({&#34;keyId&#34;: k})

        if &#34;ACCESS_NODE_NAME&#34; in kms_details:
            payload = {
                        &#34;keyProvider&#34;: {
                            &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                            },
                            &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                            &#34;encryptionType&#34;: 1001,
                            &#34;keyProviderType&#34;: 4,
                            &#34;properties&#34;: {
                                &#34;accessNodes&#34;: [
                                {
                                    &#34;keyVaultCredential&#34;: {
                                    &#34;certificate&#34;: kms_details[&#39;AZURE_CERTIFICATE_PATH&#39;],
                                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;],
                                    &#34;environment&#34;: &#34;AzureCloud&#34;,
                                    &#34;certificateThumbprint&#34;: kms_details[&#39;AZURE_CERTIFICATE_THUMBPRINT&#39;],
                                    &#34;tenantId&#34;: kms_details[&#39;AZURE_TENANT_ID&#39;],
                                    &#34;authType&#34;: 1,
                                    &#34;applicationId&#34;: kms_details[&#39;AZURE_APP_ID&#39;],
                                    &#34;endpoints&#34;: {
                                        &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                        &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                                    },
                                    &#34;certPassword&#34;: kms_details[&#39;AZURE_CERTIFICATE_PASSWORD&#39;]
                                    },
                                    &#34;accessNode&#34;: {
                                        &#34;clientName&#34;: kms_details[&#39;ACCESS_NODE_NAME&#39;]
                                    }
                                }
                                ],
                            &#34;keyVaultCredential&#34;: {
                                &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;]
                            },
                            &#34;bringYourOwnKey&#34;: is_bring_your_own_key,
                            &#34;keys&#34;: keys
                            }
                        }
                    }
        else:
            payload = {
                        &#34;keyProvider&#34;: {
                            &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                            },
                            &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                            &#34;encryptionType&#34;: 1001,
                            &#34;keyProviderType&#34;: 4,
                            &#34;properties&#34;: {
                                    &#34;keyVaultCredential&#34;: {
                                    &#34;certificate&#34;: kms_details[&#39;AZURE_CERTIFICATE_PATH&#39;],
                                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;],
                                    &#34;environment&#34;: &#34;AzureCloud&#34;,
                                    &#34;certificateThumbprint&#34;: kms_details[&#39;AZURE_CERTIFICATE_THUMBPRINT&#39;],
                                    &#34;tenantId&#34;: kms_details[&#39;AZURE_TENANT_ID&#39;],
                                    &#34;authType&#34;: 1,
                                    &#34;applicationId&#34;:kms_details[&#39;AZURE_APP_ID&#39;],
                                    &#34;endpoints&#34;: {
                                        &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                        &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                                    }
                                    },
                            &#34;bringYourOwnKey&#34;: 0,
                            &#34;sslPassPhrase&#34;: kms_details[&#39;AZURE_CERTIFICATE_PASSWORD&#39;]
                            }
                        }
                      }

        self._kms_api_call(payload)



    def add(self, kms_details):
        &#34;&#34;&#34;
        Method to add Key Management Server

        Args:
                kms_details    (dictionary)   -- dictionary with KMS details

        input dictionary for creating AWS KMS without access node ( key based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;KMS_NAME&#34;: &#34;KMS1&#34; ,
                &#34;AWS_ACCESS_KEY&#34;:&#34;&#34;,
                &#34;AWS_SECRET_KEY&#34;: &#34;&#34;,
                &#34;AWS_REGION_NAME&#34;: &#34;Asia Pacific (Mumbai)&#34;,  -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_KEYS&#34;
            }

        input dictionary for creating AWS KMS with access node ( key based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;AWS_REGION_NAME&#34;: &#34;US East (Ohio)&#34;,    -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;&#34;,
                &#34;AWS_ACCESS_KEY&#34;: &#34;&#34;,
                &#34;AWS_SECRET_KEY&#34;: &#34;&#34;     -- Base64 encoded
            }
            
        input dictionary for creating AWS KMS with access node ( key based authentication ) and by enabling Bring Your Own Key.
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;AWS_REGION_NAME&#34;: &#34;US East (Ohio)&#34;,    -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_KEYS&#34;,
                &#34;AWS_ACCESS_KEY&#34;: &#34;&#34;,
                &#34;AWS_SECRET_KEY&#34;: &#34;&#34;,     -- Base64 encoded
                &#34;BringYourOwnKey&#34;: True,
                &#34;KEYS&#34;: []
            }

        input dictionary for creating AWS KMS with access node ( credential template file based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;AWS_REGION_NAME&#34;: &#34;US East (Ohio)&#34;,    -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;AWS_KMS_NAME&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_CREDENTIALS_FILE&#34;,
                &#34;AWS_CREDENTIALS_FILE_PROFILE_NAME&#34;: &#34;&#34;
            }

        input dictionary for creating AWS KMS with access Node ( IAM based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_IAM&#34;
            }
            
        

        input dictionary for creating Azure KMS with access Node ( certificate based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;,
                &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;:2048,     -- Optional Value. Default is 3072
                &#34;AZURE_KEY_VAULT_NAME&#34;:&#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;:&#34;&#34;,
                &#34;AZURE_APP_ID&#34;:&#34;&#34;,
                &#34;AZURE_CERTIFICATE_PATH&#34;:&#34;&#34;,
                &#34;AZURE_CERTIFICATE_THUMBPRINT&#34;:&#34;&#34;,
                &#34;AZURE_CERTIFICATE_PASSWORD&#34;: &#34;&#34;,    -- Base64 encoded
            }

        input dictionary for creating Azure KMS with access Node ( IAM managed identity based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_IAM&#34;,
                &#34;AZURE_KEY_VAULT_NAME&#34;:&#34;&#34;,
            }

        input dictionary for creating Azure KMS without access Node ( certificate based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;,
                &#34;AZURE_KEY_VAULT_NAME&#34;:&#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_ID&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_PATH&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_THUMBPRINT&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_PASSWORD&#34;: &#34;&#34;,    -- Base64 encoded
            }
            
        input dictionary for creating KMIP KMS with access Node ( certificate based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_KMIP&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;KMIP_CERTIFICATE&#34;,
                &#34;KMIP_CERTIFICATE_PATH&#34;: &#34;&#34;,
                &#34;KMIP_CERTIFICATE_KEY_PATH&#34;: &#34;&#34;,
                &#34;KMIP_CA_CERTIFICATE_PATH&#34;: &#34;&#34;,
                &#34;KMIP_CERTIFICATE_PASS&#34;: &#34;&#34;, -- Base64 encoded
                &#34;KMIP_HOST&#34;: &#34;&#34;,
                &#34;KMIP_PORT&#34;: &#34;&#34;,
                &#34;KMIP_ENC_KEY_LENGTH&#34;:256           -- Optional Value. Default is 256
            }

        input dictionary for Azure KMS with access Node ( certificate based authentication ) with Bring Your Own Key enabled
            self.kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;MyKMS&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;,
                &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;: 2072,
                &#34;AZURE_KEY_VAULT_NAME&#34;: &#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_ID&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_PATH&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_THUMBPRINT&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_PASSWORD&#34;: &#34;&#34;,    -- Base64 encoded
                &#34;BringYourOwnKey&#34;: True,
                &#34;KEYS&#34;: [&#34;KeyID1/KeyVersion1&#34;, &#34;KeyID2/KeyVersion2&#34;, &#34;KeyID3/KeyVersion3&#34;]
            }
            
        input dictionary for Azure KMS with access Node ( AD APp based authentication ) with Bring Your Own Key enabled
            self.kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;MyKMS&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_KEY&#34;,
                &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;: 2072,
                &#34;AZURE_KEY_VAULT_NAME&#34;: &#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_SECRET&#34;: &#34;&#34;, -- Base64 encoded
                &#34;BringYourOwnKey&#34;: True,
                &#34;KEYS&#34;: [&#34;KeyID1/KeyVersion1&#34;, &#34;KeyID2/KeyVersion2&#34;, &#34;KeyID3/KeyVersion3&#34;]
            }
        
        input dictionary for Azure KMS without access Node ( AD APp based authentication ) with Bring Your Own Key enabled
            self.kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;KMS_NAME&#34;: &#34;MyKMS&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_KEY&#34;,
                &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;: 2072,
                &#34;AZURE_KEY_VAULT_NAME&#34;: &#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_SECRET&#34;: &#34;&#34;, -- Base64 encoded
                &#34;BringYourOwnKey&#34;: True,
                &#34;KEYS&#34;: [&#34;KeyID1/KeyVersion1&#34;, &#34;KeyID2/KeyVersion2&#34;, &#34;KeyID3/KeyVersion3&#34;]
            }
        &#34;&#34;&#34;
        
        KeyManagementServers._validate_input(kms_details, dict)

        if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] not in self._KMS_TYPE.values():
            raise SDKException(&#34;KeyManagementServer&#34;, 103)

        if kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] not in self._KMS_AUTHENTICATION_TYPE:
            raise SDKException(&#34;KeyManagementServer&#34;, 105)

        if &#34;KMS_NAME&#34; not in kms_details:
            raise SDKException(&#34;KeyManagementServer&#34;, 106)



        if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] == &#34;KEY_PROVIDER_AWS_KMS&#34;:
            if &#34;AWS_REGION_NAME&#34; not in kms_details:
                kms_details[&#34;AWS_REGION_NAME&#34;] = &#34;Asia Pacific (Mumbai)&#34;

            if kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_KEYS&#34;:
                self.add_aws_kms(kms_name=kms_details[&#39;KMS_NAME&#39;], aws_access_key=kms_details[&#39;AWS_ACCESS_KEY&#39;], aws_secret_key=kms_details[&#39;AWS_SECRET_KEY&#39;],aws_region_name=kms_details[&#34;AWS_REGION_NAME&#34;], kms_details = kms_details)

            elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_CREDENTIALS_FILE&#34;:
                self._add_aws_kms_with_cred_file(kms_details)

            elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_IAM&#34;:
                self._add_aws_kms_with_iam(kms_details)

        if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] == &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;:
            if kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;:
                self._add_azure_key_vault_certificate_auth(kms_details)

            elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AZURE_KEY_VAULT_IAM&#34;:
                self._add_azure_key_vault_iam_auth(kms_details)
            
            elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AZURE_KEY_VAULT_KEY&#34;:
                self._add_azure_key_vault_key_auth(kms_details)

        if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] == &#34;KEY_PROVIDER_KMIP&#34;:
            self._add_kmip_certificate(kms_details)
            
        return self.get(kms_details[&#39;KMS_NAME&#39;])


    def _add_kmip_certificate(self, kms_details):
        &#34;&#34;&#34;
        Configure KMIP Key Management Server with certificate based authentication

        Args:
            kms_name    (dictionary): dictionary with KMIP KMS details
        &#34;&#34;&#34;
        
        if &#34;KMIP_ENC_KEY_LENGTH&#34; not in kms_details:
            kms_details[&#34;KMIP_ENC_KEY_LENGTH&#34;] = 256
            payload = None

        if &#34;ACCESS_NODE_NAME&#34; in kms_details:

                payload = {
                        &#34;keyProvider&#34;: {
                        &#34;encryptionKeyLength&#34;: kms_details[&#34;KMIP_ENC_KEY_LENGTH&#34;],
                        &#34;encryptionType&#34;: 3,
                        &#34;keyProviderType&#34;: 2,
                        &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#34;KMS_NAME&#34;]
                        },
                        &#34;properties&#34;: {
                            &#34;bringYourOwnKey&#34;: &#34;0&#34;,
                            &#34;host&#34;: kms_details[&#34;KMIP_HOST&#34;],
                            &#34;port&#34;: int(kms_details[&#34;KMIP_PORT&#34;]),
                            &#34;accessNodes&#34;: [
                                {
                                &#34;accessNode&#34;: {
                                    &#34;clientName&#34;: kms_details[&#34;ACCESS_NODE_NAME&#34;]
                                },
                                &#34;kmipCredential&#34;: {
                                    &#34;caCertFilePath&#34;: kms_details[&#34;KMIP_CA_CERTIFICATE_PATH&#34;],
                                    &#34;certFilePath&#34;: kms_details[&#34;KMIP_CERTIFICATE_PATH&#34;],
                                    &#34;certPassword&#34;: kms_details[&#34;KMIP_CERTIFICATE_PASS&#34;],
                                    &#34;keyFilePath&#34;: kms_details[&#34;KMIP_CERTIFICATE_KEY_PATH&#34;]
                                }
                                }
                            ]
                        }
                        }
                        }

        else:
                payload = {
                    &#34;keyProvider&#34;: {
                        &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                        },
                        &#34;encryptionKeyLength&#34;: kms_details[&#39;KMIP_ENC_KEY_LENGTH&#39;],
                        &#34;encryptionType&#34;: 3,
                        &#34;keyProviderType&#34;: 2,
                        &#34;properties&#34;: {
                            &#34;caCertFilePath&#34;: kms_details[&#39;KMIP_CA_CERTIFICATE_PATH&#39;],
                            &#34;certFilePath&#34;: kms_details[&#39;KMIP_CERTIFICATE_PATH&#39;],
                            &#34;certPassword&#34;: kms_details[&#39;KMIP_CERTIFICATE_PASS&#39;],
                            &#34;keyFilePath&#34;: kms_details[&#39;KMIP_CERTIFICATE_KEY_PATH&#39;],
                            &#34;bringYourOwnKey&#34;: 0,
                            &#34;host&#34;: kms_details[&#39;KMIP_HOST&#39;],
                            &#34;port&#34;: int(kms_details[&#39;KMIP_PORT&#39;])
                        }
                    }
                }
                
        self._kms_api_call(payload)


    def _add_azure_key_vault_iam_auth(self, kms_details):
        &#34;&#34;&#34;Configure Azure Key Management Server with IAM based authentication

            :arg
                kms_details ( dictionary ) - Dictionary with AWS KMS details
            :return:
                Object of KeyManagementServer class for the newly created KMS.
        &#34;&#34;&#34;

        if &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34; not in kms_details:
            kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;] = 3072

        if &#34;ACCESS_NODE_NAME&#34; in kms_details:
            payload = {
                        &#34;keyProvider&#34;: {
                            &#34;provider&#34;: {
                                &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                            },
                            &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                            &#34;encryptionType&#34;: 1001,
                            &#34;keyProviderType&#34;: 4,
                            &#34;properties&#34;: {
                                &#34;accessNodes&#34;: [
                                {
                                    &#34;keyVaultCredential&#34;: {
                                    &#34;environment&#34;: &#34;AzureCloud&#34;,
                                    &#34;authType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;]],
                                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;],
                                    &#34;endpoints&#34;: {
                                        &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                        &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                                        }
                                    },
                                    &#34;accessNode&#34;: {
                                    &#34;clientName&#34;: kms_details[&#39;ACCESS_NODE_NAME&#39;]
                                }
                                }
                                ],
                                &#34;keyVaultCredential&#34;: {
                                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;]
                                },
                                &#34;bringYourOwnKey&#34;: 0
                                }
                            }
                        }

            self._kms_api_call(payload)


    def add_aws_kms(self, kms_name, aws_access_key, aws_secret_key, aws_region_name=None, kms_details = None):
        &#34;&#34;&#34;Configure AWS Key Management Server

            Args:
                kms_name        (string) -- name of the Key Management Server

                aws_access_key  (string) -- AWS access key

                aws_secret_key  (string) -- AWS secret key, base64 encoded

                aws_region_name (string) -- AWS region
                                            defaults to &#34;Asia Pacific (Mumbai)&#34;

                kms_details ( dictionary ) - Dictionary with AWS KMS details

            Raises SDKException:
                If inputs are wrong data type

                If API response is not successful

                If the API response JSON is empty

                If error code on API response JSON is not 0
        &#34;&#34;&#34;

        KeyManagementServers._validate_input(kms_name, str)

        payload = None
        is_bring_your_own_key = 0
        keys = []

        if &#34;BringYourOwnKey&#34; in kms_details:
            if kms_details.get(&#34;BringYourOwnKey&#34;):
                if &#34;KEYS&#34; not in kms_details:
                    raise SDKException(&#39;KeyManagementServer&#39;, 107)
                if type(kms_details[&#39;KEYS&#39;]) != list :
                    raise SDKException(&#39;Storage&#39;, 101)
                is_bring_your_own_key = 1
                for k in kms_details[&#39;KEYS&#39;]:
                    keys.append({&#34;keyId&#34;: k})

        if kms_details == None or &#34;ACCESS_NODE_NAME&#34; not in kms_details:

            if aws_region_name is None:
                aws_region_name = &#34;Asia Pacific (Mumbai)&#34;

            KeyManagementServers._validate_input(aws_access_key, str)
            KeyManagementServers._validate_input(aws_secret_key, str)
            KeyManagementServers._validate_input(aws_region_name, str)

            payload = {
                &#34;keyProvider&#34;: {
                    &#34;encryptionType&#34;: 3,
                    &#34;keyProviderType&#34;: 3,
                    &#34;provider&#34;: {
                        &#34;keyProviderName&#34;: kms_name
                    },
                    &#34;properties&#34;: {
                        &#34;regionName&#34;: aws_region_name,
                        &#34;userAccount&#34;: {
                            &#34;userName&#34;: aws_access_key,
                            &#34;password&#34;: aws_secret_key
                        }
                    }
                }
            }

        elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_KEYS&#34; and kms_details[&#39;ACCESS_NODE_NAME&#39;] != None:

            if &#34;AWS_REGION_NAME&#34; not in kms_details:
                kms_details[&#39;AWS_REGION_NAME&#39;] = &#34;Asia Pacific (Mumbai)&#34;

            KeyManagementServers._validate_input(aws_access_key, str)
            KeyManagementServers._validate_input(aws_secret_key, str)
            KeyManagementServers._validate_input(aws_region_name, str)

            payload = {
                            &#34;keyProvider&#34;: {
                                    &#34;properties&#34;: {
                                            &#34;accessNodes&#34;: [
                                                {
                                                        &#34;accessNode&#34;: {
                                                                &#34;clientName&#34;: kms_details[&#39;ACCESS_NODE_NAME&#39;]
                                                        },
                                                        &#34;awsCredential&#34;: {
                                                                &#34;userAccount&#34;: {
                                                                        &#34;password&#34;: aws_secret_key,
                                                                        &#34;userName&#34;: aws_access_key
                                                                },
                                                            &#34;amazonAuthenticationType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;]]
                                                        }
                                                }
                                            ],
                                            &#34;bringYourOwnKey&#34;: str(is_bring_your_own_key),
                            &#34;keys&#34;: keys,
                                            &#34;regionName&#34;: aws_region_name if aws_region_name!=None else kms_details[&#39;AWS_REGION_NAME&#39;]
                                    },
                                    &#34;provider&#34;: {
                                            &#34;keyProviderName&#34;: kms_name
                                    },
                                    &#34;encryptionType&#34;: 3,
                                    &#34;keyProviderType&#34;: &#34;3&#34;
                            }
                }

        self._kms_api_call(payload)
        

    def _kms_api_call(self, payload):
        &#34;&#34;&#34; Calling KMS API

        :param
        kms_details ( JSON ) - prefilled JSON payload for KMS API

        :exception
        Raises SDKException:
                    If API response code is not successful

                    If response JSON is empty

                    If errorCode is not part of the response JSON

        &#34;&#34;&#34;
    
        KeyManagementServers._validate_input(payload, dict)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._KMS_ADD_GET, payload)

        if not flag:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#34;Response&#34;, 101, response_string)

        if not response.json():
            raise SDKException(&#34;Response&#34;, 102)

        error_code = response.json().get(&#34;errorCode&#34;, -1)

        if error_code != 0:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#34;Response&#34;, 101, response_string)
        
        self.refresh()
        
    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all KMS of the commcell.

            Returns:
                str - string of all the KMS associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;KMS&#39;)

        for index, client in enumerate(self._kms_dict):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, client)
            representation_string += sub_str

        return representation_string.strip()
    
    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the KeyManagementServers class.
        
        Returns:
                str - string representation of this class
        &#34;&#34;&#34;
        return &#34;KeyManagementServers class instance for Commcell&#34;

class KeyManagementServer(object):
    &#34;&#34;&#34;Class for representing a single KMS in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell, name, id, type_id):
        &#34;&#34;&#34;Initializes the KeyManagementServer object

        Args:
                commcell    (object)    --  instance of commcell
                name        (str)       --  The name of Key Management Server
                id          (int)       --  The id of Key Management Server
                type_id     (int)       --  The type id of Key Management Server
        
        Raises SDKException:
            If input type is invalid for any param

        &#34;&#34;&#34;
        KeyManagementServerConstants.__init__(self)
        self._commcell = commcell
        self._cvpysdk_object = commcell._cvpysdk_object
        self._services = commcell._services

        KeyManagementServers._validate_input(name, str)
        self.name = name

        KeyManagementServers._validate_input(id, int)
        self.id = int(id)

        KeyManagementServers._validate_input(type_id, int, 103)
        self.type_id = int(type_id)

        self.type_name = self._get_name_from_type(type_id)
    
    def _get_name_from_type(self, type_id):
        &#34;&#34;&#34;Returns the type name for type id
        
            Args:
                type_id     (int)   --  The type id of the Key Management Server
            
            Returns:
                type_name   (str)   --  The type name of the Key Management Server
            
            Raises SDKException:
                If type_id is not int() convertible

                If Unknown type_id received

        &#34;&#34;&#34;
        KeyManagementServers._validate_input(type_id, int, 103)
        type_id = int(type_id)

        if type_id not in self._KMS_TYPE:
            raise SDKException(&#34;KeyManagementServer&#34;, 104)
        
        return self._KMS_TYPE[type_id]
    
    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;KeyServerManagement class instance for: &#34;{0}&#34;&#39;
        return representation_string.format(self.name)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.key_management_server.KeyManagementServer"><code class="flex name class">
<span>class <span class="ident">KeyManagementServer</span></span>
<span>(</span><span>commcell, name, id, type_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing a single KMS in the commcell.</p>
<p>Initializes the KeyManagementServer object</p>
<h2 id="args">Args</h2>
<p>commcell
(object)
&ndash;
instance of commcell
name
(str)
&ndash;
The name of Key Management Server
id
(int)
&ndash;
The id of Key Management Server
type_id
(int)
&ndash;
The type id of Key Management Server
Raises SDKException:
If input type is invalid for any param</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L1060-L1118" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class KeyManagementServer(object):
    &#34;&#34;&#34;Class for representing a single KMS in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell, name, id, type_id):
        &#34;&#34;&#34;Initializes the KeyManagementServer object

        Args:
                commcell    (object)    --  instance of commcell
                name        (str)       --  The name of Key Management Server
                id          (int)       --  The id of Key Management Server
                type_id     (int)       --  The type id of Key Management Server
        
        Raises SDKException:
            If input type is invalid for any param

        &#34;&#34;&#34;
        KeyManagementServerConstants.__init__(self)
        self._commcell = commcell
        self._cvpysdk_object = commcell._cvpysdk_object
        self._services = commcell._services

        KeyManagementServers._validate_input(name, str)
        self.name = name

        KeyManagementServers._validate_input(id, int)
        self.id = int(id)

        KeyManagementServers._validate_input(type_id, int, 103)
        self.type_id = int(type_id)

        self.type_name = self._get_name_from_type(type_id)
    
    def _get_name_from_type(self, type_id):
        &#34;&#34;&#34;Returns the type name for type id
        
            Args:
                type_id     (int)   --  The type id of the Key Management Server
            
            Returns:
                type_name   (str)   --  The type name of the Key Management Server
            
            Raises SDKException:
                If type_id is not int() convertible

                If Unknown type_id received

        &#34;&#34;&#34;
        KeyManagementServers._validate_input(type_id, int, 103)
        type_id = int(type_id)

        if type_id not in self._KMS_TYPE:
            raise SDKException(&#34;KeyManagementServer&#34;, 104)
        
        return self._KMS_TYPE[type_id]
    
    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;KeyServerManagement class instance for: &#34;{0}&#34;&#39;
        return representation_string.format(self.name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.key_management_server.KeyManagementServerConstants"><code class="flex name class">
<span>class <span class="ident">KeyManagementServerConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>Helper class that provides a standard way to create an ABC using
inheritance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L94-L114" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class KeyManagementServerConstants(ABC):

    def __init__(self):
        self._KMS_TYPE = {
            1: &#34;KEY_PROVIDER_COMMVAULT&#34;,
            2: &#34;KEY_PROVIDER_KMIP&#34;,
            3: &#34;KEY_PROVIDER_AWS_KMS&#34;,
            4: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
            5: &#34;KEY_PROVIDER_SAFENET&#34;,
            6: &#34;KEY_PROVIDER_PASSPHRASE&#34;,
        }

        self._KMS_AUTHENTICATION_TYPE = {
            &#34;AWS_KEYS&#34;: 0,
            &#34;AWS_IAM&#34;: 1,
            &#34;AWS_CREDENTIALS_FILE&#34;: 0,
            &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;: 1,
            &#34;AZURE_KEY_VAULT_IAM&#34;: 3,
            &#34;AZURE_KEY_VAULT_KEY&#34;: 2,
            &#34;KMIP_CERTIFICATE&#34;: 99
        }</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>abc.ABC</li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.key_management_server.KeyManagementServers" href="#cvpysdk.key_management_server.KeyManagementServers">KeyManagementServers</a></li>
</ul>
</dd>
<dt id="cvpysdk.key_management_server.KeyManagementServers"><code class="flex name class">
<span>class <span class="ident">KeyManagementServers</span></span>
<span>(</span><span>commcell)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the KMS in the commcell.</p>
<p>Initializes KeyManagementServers class object</p>
<h2 id="args">Args</h2>
<p>commcell
(object)
&ndash;
instance of commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L116-L1058" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class KeyManagementServers(KeyManagementServerConstants):
    &#34;&#34;&#34;Class for representing all the KMS in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes KeyManagementServers class object

            Args:
                commcell    (object)    --  instance of commcell

        &#34;&#34;&#34;
        KeyManagementServerConstants.__init__(self)
        self._commcell = commcell

        self._cvpysdk_object = commcell._cvpysdk_object
        self._services = commcell._services

        self._KMS_ADD_GET = self._services[&#39;KEY_MANAGEMENT_SERVER_ADD_GET&#39;]
        self._KMS_DELETE = self._services[&#39;KEY_MANAGEMENT_SERVER_DELETE&#39;]
        self._kms_dict = None
        self.refresh()

    def _get_kms_dict(self):
        &#34;&#34;&#34;Fetches the name-indexed dictionary of all Key Management Servers

            Returns:
                the name-indexed dictionary of Key Management Server info
                {
                    name1: {
                       name: name1, 
                       id: id1,
                       type_id: type_id1,
                    },
                    ...
                }

            Raises SDKException:
                    If failed to fetch the list
        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._KMS_ADD_GET)

        if not flag:
            raise SDKException(&#34;Response&#34;, 101)

        if not response.json() or &#39;keyProviders&#39; not in response.json():
            return {}

        key_providers = response.json()[&#34;keyProviders&#34;]
        kms_dict = {}
        for key_provider in key_providers:
            type = key_provider.get(&#34;keyProviderType&#34;)
            
            provider = key_provider.get(&#34;provider&#34;)
            name = provider.get(&#34;keyProviderName&#34;, &#34;&#34;).lower()
            id = provider.get(&#34;keyProviderId&#34;)
            
            kms_dict[name] = {
                &#34;name&#34;: name,
                &#34;id&#34;: id,
                &#34;type_id&#34;: type,
            }

        return kms_dict
    
    def _validate_input(input_value, input_type, exception_id=101):
        &#34;&#34;&#34;Raises SDKException if input_value doesn&#39;t match input_type
        
            Args:
                input_value     (any)   --  The value to check

                input_type      (type)  --  The type to check against.
                                            For int type, the input can be int-convertible

                exception_id    (int)   --  The exception id to throw
                                            defaults to 101

            Raises SDKException:
                If type mismatch was found
        &#34;&#34;&#34; 
        # if int, then try to convert and then check
        if input_type == int:
            try:
                input_value = int(input_value)
            except ValueError as e:
                pass
        
        if not isinstance(input_value, input_type):
            message = f&#34;Received: {type(input_value)}. Expected: {input_type}&#34;
            raise SDKException(&#34;KeyManagementServer&#34;, exception_id, message)
    
    def get(self, kms_name):
        &#34;&#34;&#34;Gets a specific Key Management Server object
        
            Args:
                kms_name    (str)       -- The Key Management Server to get

            Returns:
                kms         (object)    --  The KeyManagementServer object
            
            Raises SDKException:
                If kms_name is not str

                If Key Management Server not found
        &#34;&#34;&#34;      
        if not self.has_kms(kms_name):
            raise SDKException(&#34;KeyManagementServer&#34;, 102)
        
        kms_info = self._kms_dict[kms_name.lower()]
        kms_obj = KeyManagementServer(self._commcell, kms_info[&#39;name&#39;], kms_info[&#39;id&#39;], kms_info[&#39;type_id&#39;])
        return kms_obj


    def get_all_kms(self):
        &#34;&#34;&#34;Gets the name-indexed dictionary of all Key Management Servers

            Returns:
                the name-indexed dictionary of Key Management Server info
                {
                    name1: {
                       name: name1, 
                       id: id1,
                       type_id: type_id1,
                    },
                    ...
                }
                
        &#34;&#34;&#34;
        return self._kms_dict

    def refresh(self):
        &#34;&#34;&#34;Refreshes the dictionary of Key Management Servers&#34;&#34;&#34;
        self._kms_dict = self._get_kms_dict()

    def delete(self, kms_name):
        &#34;&#34;&#34;Deletes a Key Management Server

            Args:
                kms_name (string) -- name of the Key Management Server

            Raises SDKException:
                    If API response code is not successfull

                    If response JSON is empty

                    If errorCode is not part of the response JSON
        &#34;&#34;&#34;
        if not self.has_kms(kms_name):
            raise SDKException(&#39;KeyManagementServer&#39;, 102)

        kms_id = self._kms_dict[kms_name.lower()][&#39;id&#39;]

        kms_service = self._KMS_DELETE % (kms_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, kms_service)

        if not flag:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#34;Response&#34;, 101, response_string)

        if not response.json():
            raise SDKException(&#34;Response&#34;, 102)

        if &#34;errorCode&#34; not in response.json():
            raise SDKException(
                &#34;Response&#34;, 101, f&#34;Something went wrong while deleting {kms_name}&#34;)

        error_code = response.json()[&#34;errorCode&#34;]
        if error_code != 0:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#34;Response&#34;, 101, response_string)

        
    
    def has_kms(self, kms_name):
        &#34;&#34;&#34;Check if the Key Management Server exist or not

            Args:
                kms_name    (str)   -- name of the Key Management Server

            Returns:
                result      (bool)  -- whether Key Management Server exists or not
            
            Raises SDKException:
                If kms_name is not string
        &#34;&#34;&#34;
        KeyManagementServers._validate_input(kms_name, str)
        
        return kms_name.lower() in self._kms_dict
 

    def _add_aws_kms_with_cred_file(self, kms_details):
            &#34;&#34;&#34;Configure AWS Key Management Server with credential file based authentication

                :arg
                    kms_details ( dictionary ) - Dictionary with AWS KMS details
                :return:
                    Object of KeyManagementServer class for the newly created KMS.
            &#34;&#34;&#34;

            if &#34;ACCESS_NODE_NAME&#34; in kms_details:
                payload = {
                    &#34;keyProvider&#34;: {

                        &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#34;KMS_NAME&#34;]
                        },
                        &#34;encryptionType&#34;: 3,
                        &#34;keyProviderType&#34;: 3,

                        &#34;properties&#34;: {
                            &#34;accessNodes&#34;: [
                                {
                                    &#34;accessNode&#34;: {
                                        &#34;clientName&#34;: kms_details[&#34;ACCESS_NODE_NAME&#34;]
                                    },
                                    &#34;awsCredential&#34;: {
                                        &#34;profile&#34;: kms_details[&#34;AWS_CREDENTIALS_FILE_PROFILE_NAME&#34;],
                                        &#34;amazonAuthenticationType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#34;KEY_PROVIDER_AUTH_TYPE&#34;]]
                                    }
                                }
                            ],
                            &#34;bringYourOwnKey&#34;: 0,
                            &#34;regionName&#34;: kms_details[&#34;AWS_REGION_NAME&#34;]
                        }

                    }
                }

                self._kms_api_call(payload)


    def _add_aws_kms_with_iam(self, kms_details):
        &#34;&#34;&#34;Configure AWS Key Management Server with IMA based authentication

            :arg
                kms_details ( dictionary ) - Dictionary with AWS KMS details
            :return:
                Object of KeyManagementServer class for the newly created KMS.
        &#34;&#34;&#34;

        if &#34;ACCESS_NODE_NAME&#34; in kms_details:

            payload= {
                        &#34;keyProvider&#34;: {
                            &#34;provider&#34;: {
                                    &#34;keyProviderName&#34;: kms_details[&#34;KMS_NAME&#34;]
                                    },
                                &#34;encryptionType&#34;: 3,
                                &#34;keyProviderType&#34;: 3,
                                &#34;properties&#34;: {
                                        &#34;accessNodes&#34;: [
                                                {
                                                        &#34;accessNode&#34;: {
                                                                &#34;clientName&#34;: kms_details[&#34;ACCESS_NODE_NAME&#34;]
                                                        },
                                                        &#34;awsCredential&#34;: {
                                                            &#34;amazonAuthenticationType&#34;:1
                                                        }
                                                }
                                            ],
                                            &#34;bringYourOwnKey&#34;: 0,
                                            &#34;regionName&#34;: kms_details[&#34;AWS_REGION_NAME&#34;]
                                    }

                            }
                    }

            self._kms_api_call(payload)

    def _add_azure_key_vault_key_auth(self, kms_details):
        &#34;&#34;&#34;Configure Azure Key Management Server with AD-app key based authentication

            :arg
                kms_details ( dictionary ) - Dictionary with AWS KMS details
            :return:
                Object of KeyManagementServer class for the newly created KMS.
        &#34;&#34;&#34;
        payload = None
        is_bring_your_own_key = 0
        keys = []
        
        if &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34; not in kms_details:
            kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;] = 3072
        
        if &#34;BringYourOwnKey&#34; in kms_details:
            if kms_details[&#34;BringYourOwnKey&#34;]:
                if &#34;KEYS&#34; not in kms_details:
                    raise SDKException(&#39;KeyManagementServer&#39;, 107)
                if type(kms_details[&#39;KEYS&#39;]) != list :
                    raise SDKException(&#39;Storage&#39;, 101)
                is_bring_your_own_key = 1
                for k in kms_details[&#39;KEYS&#39;]:
                    keys.append({&#34;keyId&#34;: k})
                    
        if &#34;ACCESS_NODE_NAME&#34; in kms_details:
            payload= {
                        &#34;keyProvider&#34;: {
                        &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                        &#34;encryptionType&#34;: 1001,
                        &#34;keyProviderType&#34;: 4,
                        &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                        },
                        &#34;properties&#34;: {
                            &#34;accessNodes&#34;: [
                            {
                                &#34;accessNode&#34;: {
                                &#34;clientName&#34;: kms_details[&#39;ACCESS_NODE_NAME&#39;]
                                },
                            &#34;keyVaultCredential&#34;: {
                            &#34;authType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#34;KEY_PROVIDER_AUTH_TYPE&#34;]],
                            &#34;applicationId&#34;: kms_details[&#39;AZURE_APP_ID&#39;],
                            &#34;tenantId&#34;: kms_details[&#39;AZURE_TENANT_ID&#39;],
                            &#34;environment&#34;: &#34;AzureCloud&#34;,
                            &#34;endpoints&#34;: {
                                &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                            },
                        &#34;certPassword&#34;: kms_details[&#39;AZURE_APP_SECRET&#39;],
                        &#34;overrideCredentials&#34;: True,
                        &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;]
                        }
                        }
                        ],
                        &#34;bringYourOwnKey&#34;: is_bring_your_own_key,
                         &#34;keys&#34;: keys
                    }
                }
            }

            
        else:
            payload = {
                &#34;keyProvider&#34;: {
                    &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                    &#34;encryptionType&#34;: 1001,
                    &#34;keyProviderType&#34;: 4,
                    &#34;provider&#34;: {
                        &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                    },
                &#34;properties&#34;: {
                    &#34;keyVaultCredential&#34;: {
                    &#34;authType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#34;KEY_PROVIDER_AUTH_TYPE&#34;]],
                    &#34;applicationId&#34;: kms_details[&#39;AZURE_APP_ID&#39;],
                    &#34;tenantId&#34;: kms_details[&#39;AZURE_TENANT_ID&#39;],
                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;],
                    &#34;environment&#34;: &#34;AzureCloud&#34;,
                    &#34;endpoints&#34;: {
                    &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                    &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                    }
                    },
                &#34;sslPassPhrase&#34;: kms_details[&#39;AZURE_APP_SECRET&#39;],
                &#34;bringYourOwnKey&#34;: is_bring_your_own_key,
                 &#34;keys&#34;: keys
                }
                }
            }     

        self._kms_api_call(payload)
        
    
    def _add_azure_key_vault_certificate_auth(self, kms_details):
        &#34;&#34;&#34;Configure Azure Key Management Server with AD-app certificate based authentication

            :arg
                kms_details ( dictionary ) - Dictionary with AWS KMS details
            :return:
                Object of KeyManagementServer class for the newly created KMS.
        &#34;&#34;&#34;
        payload = None
        is_bring_your_own_key = 0
        keys = []

        if &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34; not in kms_details:
            kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;] = 3072

        if &#34;BringYourOwnKey&#34; in kms_details:
            if kms_details[&#34;BringYourOwnKey&#34;]:
                if &#34;KEYS&#34; not in kms_details:
                    raise SDKException(&#39;KeyManagementServer&#39;, 107)
                if type(kms_details[&#39;KEYS&#39;]) != list :
                    raise SDKException(&#39;Storage&#39;, 101)
                is_bring_your_own_key = 1
                for k in kms_details[&#39;KEYS&#39;]:
                    keys.append({&#34;keyId&#34;: k})

        if &#34;ACCESS_NODE_NAME&#34; in kms_details:
            payload = {
                        &#34;keyProvider&#34;: {
                            &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                            },
                            &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                            &#34;encryptionType&#34;: 1001,
                            &#34;keyProviderType&#34;: 4,
                            &#34;properties&#34;: {
                                &#34;accessNodes&#34;: [
                                {
                                    &#34;keyVaultCredential&#34;: {
                                    &#34;certificate&#34;: kms_details[&#39;AZURE_CERTIFICATE_PATH&#39;],
                                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;],
                                    &#34;environment&#34;: &#34;AzureCloud&#34;,
                                    &#34;certificateThumbprint&#34;: kms_details[&#39;AZURE_CERTIFICATE_THUMBPRINT&#39;],
                                    &#34;tenantId&#34;: kms_details[&#39;AZURE_TENANT_ID&#39;],
                                    &#34;authType&#34;: 1,
                                    &#34;applicationId&#34;: kms_details[&#39;AZURE_APP_ID&#39;],
                                    &#34;endpoints&#34;: {
                                        &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                        &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                                    },
                                    &#34;certPassword&#34;: kms_details[&#39;AZURE_CERTIFICATE_PASSWORD&#39;]
                                    },
                                    &#34;accessNode&#34;: {
                                        &#34;clientName&#34;: kms_details[&#39;ACCESS_NODE_NAME&#39;]
                                    }
                                }
                                ],
                            &#34;keyVaultCredential&#34;: {
                                &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;]
                            },
                            &#34;bringYourOwnKey&#34;: is_bring_your_own_key,
                            &#34;keys&#34;: keys
                            }
                        }
                    }
        else:
            payload = {
                        &#34;keyProvider&#34;: {
                            &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                            },
                            &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                            &#34;encryptionType&#34;: 1001,
                            &#34;keyProviderType&#34;: 4,
                            &#34;properties&#34;: {
                                    &#34;keyVaultCredential&#34;: {
                                    &#34;certificate&#34;: kms_details[&#39;AZURE_CERTIFICATE_PATH&#39;],
                                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;],
                                    &#34;environment&#34;: &#34;AzureCloud&#34;,
                                    &#34;certificateThumbprint&#34;: kms_details[&#39;AZURE_CERTIFICATE_THUMBPRINT&#39;],
                                    &#34;tenantId&#34;: kms_details[&#39;AZURE_TENANT_ID&#39;],
                                    &#34;authType&#34;: 1,
                                    &#34;applicationId&#34;:kms_details[&#39;AZURE_APP_ID&#39;],
                                    &#34;endpoints&#34;: {
                                        &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                        &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                                    }
                                    },
                            &#34;bringYourOwnKey&#34;: 0,
                            &#34;sslPassPhrase&#34;: kms_details[&#39;AZURE_CERTIFICATE_PASSWORD&#39;]
                            }
                        }
                      }

        self._kms_api_call(payload)



    def add(self, kms_details):
        &#34;&#34;&#34;
        Method to add Key Management Server

        Args:
                kms_details    (dictionary)   -- dictionary with KMS details

        input dictionary for creating AWS KMS without access node ( key based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;KMS_NAME&#34;: &#34;KMS1&#34; ,
                &#34;AWS_ACCESS_KEY&#34;:&#34;&#34;,
                &#34;AWS_SECRET_KEY&#34;: &#34;&#34;,
                &#34;AWS_REGION_NAME&#34;: &#34;Asia Pacific (Mumbai)&#34;,  -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_KEYS&#34;
            }

        input dictionary for creating AWS KMS with access node ( key based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;AWS_REGION_NAME&#34;: &#34;US East (Ohio)&#34;,    -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;&#34;,
                &#34;AWS_ACCESS_KEY&#34;: &#34;&#34;,
                &#34;AWS_SECRET_KEY&#34;: &#34;&#34;     -- Base64 encoded
            }
            
        input dictionary for creating AWS KMS with access node ( key based authentication ) and by enabling Bring Your Own Key.
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;AWS_REGION_NAME&#34;: &#34;US East (Ohio)&#34;,    -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_KEYS&#34;,
                &#34;AWS_ACCESS_KEY&#34;: &#34;&#34;,
                &#34;AWS_SECRET_KEY&#34;: &#34;&#34;,     -- Base64 encoded
                &#34;BringYourOwnKey&#34;: True,
                &#34;KEYS&#34;: []
            }

        input dictionary for creating AWS KMS with access node ( credential template file based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;AWS_REGION_NAME&#34;: &#34;US East (Ohio)&#34;,    -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;AWS_KMS_NAME&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_CREDENTIALS_FILE&#34;,
                &#34;AWS_CREDENTIALS_FILE_PROFILE_NAME&#34;: &#34;&#34;
            }

        input dictionary for creating AWS KMS with access Node ( IAM based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_IAM&#34;
            }
            
        

        input dictionary for creating Azure KMS with access Node ( certificate based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;,
                &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;:2048,     -- Optional Value. Default is 3072
                &#34;AZURE_KEY_VAULT_NAME&#34;:&#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;:&#34;&#34;,
                &#34;AZURE_APP_ID&#34;:&#34;&#34;,
                &#34;AZURE_CERTIFICATE_PATH&#34;:&#34;&#34;,
                &#34;AZURE_CERTIFICATE_THUMBPRINT&#34;:&#34;&#34;,
                &#34;AZURE_CERTIFICATE_PASSWORD&#34;: &#34;&#34;,    -- Base64 encoded
            }

        input dictionary for creating Azure KMS with access Node ( IAM managed identity based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_IAM&#34;,
                &#34;AZURE_KEY_VAULT_NAME&#34;:&#34;&#34;,
            }

        input dictionary for creating Azure KMS without access Node ( certificate based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;,
                &#34;AZURE_KEY_VAULT_NAME&#34;:&#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_ID&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_PATH&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_THUMBPRINT&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_PASSWORD&#34;: &#34;&#34;,    -- Base64 encoded
            }
            
        input dictionary for creating KMIP KMS with access Node ( certificate based authentication )
            kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_KMIP&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;KMIP_CERTIFICATE&#34;,
                &#34;KMIP_CERTIFICATE_PATH&#34;: &#34;&#34;,
                &#34;KMIP_CERTIFICATE_KEY_PATH&#34;: &#34;&#34;,
                &#34;KMIP_CA_CERTIFICATE_PATH&#34;: &#34;&#34;,
                &#34;KMIP_CERTIFICATE_PASS&#34;: &#34;&#34;, -- Base64 encoded
                &#34;KMIP_HOST&#34;: &#34;&#34;,
                &#34;KMIP_PORT&#34;: &#34;&#34;,
                &#34;KMIP_ENC_KEY_LENGTH&#34;:256           -- Optional Value. Default is 256
            }

        input dictionary for Azure KMS with access Node ( certificate based authentication ) with Bring Your Own Key enabled
            self.kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;MyKMS&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;,
                &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;: 2072,
                &#34;AZURE_KEY_VAULT_NAME&#34;: &#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_ID&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_PATH&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_THUMBPRINT&#34;: &#34;&#34;,
                &#34;AZURE_CERTIFICATE_PASSWORD&#34;: &#34;&#34;,    -- Base64 encoded
                &#34;BringYourOwnKey&#34;: True,
                &#34;KEYS&#34;: [&#34;KeyID1/KeyVersion1&#34;, &#34;KeyID2/KeyVersion2&#34;, &#34;KeyID3/KeyVersion3&#34;]
            }
            
        input dictionary for Azure KMS with access Node ( AD APp based authentication ) with Bring Your Own Key enabled
            self.kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
                &#34;KMS_NAME&#34;: &#34;MyKMS&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_KEY&#34;,
                &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;: 2072,
                &#34;AZURE_KEY_VAULT_NAME&#34;: &#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_SECRET&#34;: &#34;&#34;, -- Base64 encoded
                &#34;BringYourOwnKey&#34;: True,
                &#34;KEYS&#34;: [&#34;KeyID1/KeyVersion1&#34;, &#34;KeyID2/KeyVersion2&#34;, &#34;KeyID3/KeyVersion3&#34;]
            }
        
        input dictionary for Azure KMS without access Node ( AD APp based authentication ) with Bring Your Own Key enabled
            self.kms_details = {
                &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
                &#34;KMS_NAME&#34;: &#34;MyKMS&#34;,
                &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_KEY&#34;,
                &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;: 2072,
                &#34;AZURE_KEY_VAULT_NAME&#34;: &#34;&#34;,
                &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_ID&#34;: &#34;&#34;,
                &#34;AZURE_APP_SECRET&#34;: &#34;&#34;, -- Base64 encoded
                &#34;BringYourOwnKey&#34;: True,
                &#34;KEYS&#34;: [&#34;KeyID1/KeyVersion1&#34;, &#34;KeyID2/KeyVersion2&#34;, &#34;KeyID3/KeyVersion3&#34;]
            }
        &#34;&#34;&#34;
        
        KeyManagementServers._validate_input(kms_details, dict)

        if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] not in self._KMS_TYPE.values():
            raise SDKException(&#34;KeyManagementServer&#34;, 103)

        if kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] not in self._KMS_AUTHENTICATION_TYPE:
            raise SDKException(&#34;KeyManagementServer&#34;, 105)

        if &#34;KMS_NAME&#34; not in kms_details:
            raise SDKException(&#34;KeyManagementServer&#34;, 106)



        if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] == &#34;KEY_PROVIDER_AWS_KMS&#34;:
            if &#34;AWS_REGION_NAME&#34; not in kms_details:
                kms_details[&#34;AWS_REGION_NAME&#34;] = &#34;Asia Pacific (Mumbai)&#34;

            if kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_KEYS&#34;:
                self.add_aws_kms(kms_name=kms_details[&#39;KMS_NAME&#39;], aws_access_key=kms_details[&#39;AWS_ACCESS_KEY&#39;], aws_secret_key=kms_details[&#39;AWS_SECRET_KEY&#39;],aws_region_name=kms_details[&#34;AWS_REGION_NAME&#34;], kms_details = kms_details)

            elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_CREDENTIALS_FILE&#34;:
                self._add_aws_kms_with_cred_file(kms_details)

            elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_IAM&#34;:
                self._add_aws_kms_with_iam(kms_details)

        if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] == &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;:
            if kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;:
                self._add_azure_key_vault_certificate_auth(kms_details)

            elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AZURE_KEY_VAULT_IAM&#34;:
                self._add_azure_key_vault_iam_auth(kms_details)
            
            elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AZURE_KEY_VAULT_KEY&#34;:
                self._add_azure_key_vault_key_auth(kms_details)

        if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] == &#34;KEY_PROVIDER_KMIP&#34;:
            self._add_kmip_certificate(kms_details)
            
        return self.get(kms_details[&#39;KMS_NAME&#39;])


    def _add_kmip_certificate(self, kms_details):
        &#34;&#34;&#34;
        Configure KMIP Key Management Server with certificate based authentication

        Args:
            kms_name    (dictionary): dictionary with KMIP KMS details
        &#34;&#34;&#34;
        
        if &#34;KMIP_ENC_KEY_LENGTH&#34; not in kms_details:
            kms_details[&#34;KMIP_ENC_KEY_LENGTH&#34;] = 256
            payload = None

        if &#34;ACCESS_NODE_NAME&#34; in kms_details:

                payload = {
                        &#34;keyProvider&#34;: {
                        &#34;encryptionKeyLength&#34;: kms_details[&#34;KMIP_ENC_KEY_LENGTH&#34;],
                        &#34;encryptionType&#34;: 3,
                        &#34;keyProviderType&#34;: 2,
                        &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#34;KMS_NAME&#34;]
                        },
                        &#34;properties&#34;: {
                            &#34;bringYourOwnKey&#34;: &#34;0&#34;,
                            &#34;host&#34;: kms_details[&#34;KMIP_HOST&#34;],
                            &#34;port&#34;: int(kms_details[&#34;KMIP_PORT&#34;]),
                            &#34;accessNodes&#34;: [
                                {
                                &#34;accessNode&#34;: {
                                    &#34;clientName&#34;: kms_details[&#34;ACCESS_NODE_NAME&#34;]
                                },
                                &#34;kmipCredential&#34;: {
                                    &#34;caCertFilePath&#34;: kms_details[&#34;KMIP_CA_CERTIFICATE_PATH&#34;],
                                    &#34;certFilePath&#34;: kms_details[&#34;KMIP_CERTIFICATE_PATH&#34;],
                                    &#34;certPassword&#34;: kms_details[&#34;KMIP_CERTIFICATE_PASS&#34;],
                                    &#34;keyFilePath&#34;: kms_details[&#34;KMIP_CERTIFICATE_KEY_PATH&#34;]
                                }
                                }
                            ]
                        }
                        }
                        }

        else:
                payload = {
                    &#34;keyProvider&#34;: {
                        &#34;provider&#34;: {
                            &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                        },
                        &#34;encryptionKeyLength&#34;: kms_details[&#39;KMIP_ENC_KEY_LENGTH&#39;],
                        &#34;encryptionType&#34;: 3,
                        &#34;keyProviderType&#34;: 2,
                        &#34;properties&#34;: {
                            &#34;caCertFilePath&#34;: kms_details[&#39;KMIP_CA_CERTIFICATE_PATH&#39;],
                            &#34;certFilePath&#34;: kms_details[&#39;KMIP_CERTIFICATE_PATH&#39;],
                            &#34;certPassword&#34;: kms_details[&#39;KMIP_CERTIFICATE_PASS&#39;],
                            &#34;keyFilePath&#34;: kms_details[&#39;KMIP_CERTIFICATE_KEY_PATH&#39;],
                            &#34;bringYourOwnKey&#34;: 0,
                            &#34;host&#34;: kms_details[&#39;KMIP_HOST&#39;],
                            &#34;port&#34;: int(kms_details[&#39;KMIP_PORT&#39;])
                        }
                    }
                }
                
        self._kms_api_call(payload)


    def _add_azure_key_vault_iam_auth(self, kms_details):
        &#34;&#34;&#34;Configure Azure Key Management Server with IAM based authentication

            :arg
                kms_details ( dictionary ) - Dictionary with AWS KMS details
            :return:
                Object of KeyManagementServer class for the newly created KMS.
        &#34;&#34;&#34;

        if &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34; not in kms_details:
            kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;] = 3072

        if &#34;ACCESS_NODE_NAME&#34; in kms_details:
            payload = {
                        &#34;keyProvider&#34;: {
                            &#34;provider&#34;: {
                                &#34;keyProviderName&#34;: kms_details[&#39;KMS_NAME&#39;]
                            },
                            &#34;encryptionKeyLength&#34;: kms_details[&#39;AZURE_KEY_VAULT_KEY_LENGTH&#39;],
                            &#34;encryptionType&#34;: 1001,
                            &#34;keyProviderType&#34;: 4,
                            &#34;properties&#34;: {
                                &#34;accessNodes&#34;: [
                                {
                                    &#34;keyVaultCredential&#34;: {
                                    &#34;environment&#34;: &#34;AzureCloud&#34;,
                                    &#34;authType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;]],
                                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;],
                                    &#34;endpoints&#34;: {
                                        &#34;activeDirectoryEndpoint&#34;: &#34;https://login.microsoftonline.com/&#34;,
                                        &#34;keyVaultEndpoint&#34;: &#34;vault.azure.net&#34;
                                        }
                                    },
                                    &#34;accessNode&#34;: {
                                    &#34;clientName&#34;: kms_details[&#39;ACCESS_NODE_NAME&#39;]
                                }
                                }
                                ],
                                &#34;keyVaultCredential&#34;: {
                                    &#34;resourceName&#34;: kms_details[&#39;AZURE_KEY_VAULT_NAME&#39;]
                                },
                                &#34;bringYourOwnKey&#34;: 0
                                }
                            }
                        }

            self._kms_api_call(payload)


    def add_aws_kms(self, kms_name, aws_access_key, aws_secret_key, aws_region_name=None, kms_details = None):
        &#34;&#34;&#34;Configure AWS Key Management Server

            Args:
                kms_name        (string) -- name of the Key Management Server

                aws_access_key  (string) -- AWS access key

                aws_secret_key  (string) -- AWS secret key, base64 encoded

                aws_region_name (string) -- AWS region
                                            defaults to &#34;Asia Pacific (Mumbai)&#34;

                kms_details ( dictionary ) - Dictionary with AWS KMS details

            Raises SDKException:
                If inputs are wrong data type

                If API response is not successful

                If the API response JSON is empty

                If error code on API response JSON is not 0
        &#34;&#34;&#34;

        KeyManagementServers._validate_input(kms_name, str)

        payload = None
        is_bring_your_own_key = 0
        keys = []

        if &#34;BringYourOwnKey&#34; in kms_details:
            if kms_details.get(&#34;BringYourOwnKey&#34;):
                if &#34;KEYS&#34; not in kms_details:
                    raise SDKException(&#39;KeyManagementServer&#39;, 107)
                if type(kms_details[&#39;KEYS&#39;]) != list :
                    raise SDKException(&#39;Storage&#39;, 101)
                is_bring_your_own_key = 1
                for k in kms_details[&#39;KEYS&#39;]:
                    keys.append({&#34;keyId&#34;: k})

        if kms_details == None or &#34;ACCESS_NODE_NAME&#34; not in kms_details:

            if aws_region_name is None:
                aws_region_name = &#34;Asia Pacific (Mumbai)&#34;

            KeyManagementServers._validate_input(aws_access_key, str)
            KeyManagementServers._validate_input(aws_secret_key, str)
            KeyManagementServers._validate_input(aws_region_name, str)

            payload = {
                &#34;keyProvider&#34;: {
                    &#34;encryptionType&#34;: 3,
                    &#34;keyProviderType&#34;: 3,
                    &#34;provider&#34;: {
                        &#34;keyProviderName&#34;: kms_name
                    },
                    &#34;properties&#34;: {
                        &#34;regionName&#34;: aws_region_name,
                        &#34;userAccount&#34;: {
                            &#34;userName&#34;: aws_access_key,
                            &#34;password&#34;: aws_secret_key
                        }
                    }
                }
            }

        elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_KEYS&#34; and kms_details[&#39;ACCESS_NODE_NAME&#39;] != None:

            if &#34;AWS_REGION_NAME&#34; not in kms_details:
                kms_details[&#39;AWS_REGION_NAME&#39;] = &#34;Asia Pacific (Mumbai)&#34;

            KeyManagementServers._validate_input(aws_access_key, str)
            KeyManagementServers._validate_input(aws_secret_key, str)
            KeyManagementServers._validate_input(aws_region_name, str)

            payload = {
                            &#34;keyProvider&#34;: {
                                    &#34;properties&#34;: {
                                            &#34;accessNodes&#34;: [
                                                {
                                                        &#34;accessNode&#34;: {
                                                                &#34;clientName&#34;: kms_details[&#39;ACCESS_NODE_NAME&#39;]
                                                        },
                                                        &#34;awsCredential&#34;: {
                                                                &#34;userAccount&#34;: {
                                                                        &#34;password&#34;: aws_secret_key,
                                                                        &#34;userName&#34;: aws_access_key
                                                                },
                                                            &#34;amazonAuthenticationType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;]]
                                                        }
                                                }
                                            ],
                                            &#34;bringYourOwnKey&#34;: str(is_bring_your_own_key),
                            &#34;keys&#34;: keys,
                                            &#34;regionName&#34;: aws_region_name if aws_region_name!=None else kms_details[&#39;AWS_REGION_NAME&#39;]
                                    },
                                    &#34;provider&#34;: {
                                            &#34;keyProviderName&#34;: kms_name
                                    },
                                    &#34;encryptionType&#34;: 3,
                                    &#34;keyProviderType&#34;: &#34;3&#34;
                            }
                }

        self._kms_api_call(payload)
        

    def _kms_api_call(self, payload):
        &#34;&#34;&#34; Calling KMS API

        :param
        kms_details ( JSON ) - prefilled JSON payload for KMS API

        :exception
        Raises SDKException:
                    If API response code is not successful

                    If response JSON is empty

                    If errorCode is not part of the response JSON

        &#34;&#34;&#34;
    
        KeyManagementServers._validate_input(payload, dict)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._KMS_ADD_GET, payload)

        if not flag:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#34;Response&#34;, 101, response_string)

        if not response.json():
            raise SDKException(&#34;Response&#34;, 102)

        error_code = response.json().get(&#34;errorCode&#34;, -1)

        if error_code != 0:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#34;Response&#34;, 101, response_string)
        
        self.refresh()
        
    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all KMS of the commcell.

            Returns:
                str - string of all the KMS associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;KMS&#39;)

        for index, client in enumerate(self._kms_dict):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, client)
            representation_string += sub_str

        return representation_string.strip()
    
    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the KeyManagementServers class.
        
        Returns:
                str - string representation of this class
        &#34;&#34;&#34;
        return &#34;KeyManagementServers class instance for Commcell&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.key_management_server.KeyManagementServerConstants" href="#cvpysdk.key_management_server.KeyManagementServerConstants">KeyManagementServerConstants</a></li>
<li>abc.ABC</li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.key_management_server.KeyManagementServers.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, kms_details)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to add Key Management Server</p>
<h2 id="args">Args</h2>
<p>kms_details
(dictionary)
&ndash; dictionary with KMS details
input dictionary for creating AWS KMS without access node ( key based authentication )
kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AWS_KMS",
"KMS_NAME": "KMS1" ,
"AWS_ACCESS_KEY":"",
"AWS_SECRET_KEY": "",
"AWS_REGION_NAME": "Asia Pacific (Mumbai)",
&ndash; Optional Value. Default is "Asia Pacific (Mumbai)"
"KEY_PROVIDER_AUTH_TYPE": "AWS_KEYS"
}</p>
<p>input dictionary for creating AWS KMS with access node ( key based authentication )
kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AWS_KMS",
"AWS_REGION_NAME": "US East (Ohio)",
&ndash; Optional Value. Default is "Asia Pacific (Mumbai)"
"ACCESS_NODE_NAME": "",
"KMS_NAME": "",
"KEY_PROVIDER_AUTH_TYPE": "",
"AWS_ACCESS_KEY": "",
"AWS_SECRET_KEY": ""
&ndash; Base64 encoded
}</p>
<p>input dictionary for creating AWS KMS with access node ( key based authentication ) and by enabling Bring Your Own Key.
kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AWS_KMS",
"AWS_REGION_NAME": "US East (Ohio)",
&ndash; Optional Value. Default is "Asia Pacific (Mumbai)"
"ACCESS_NODE_NAME": "",
"KMS_NAME": "",
"KEY_PROVIDER_AUTH_TYPE": "AWS_KEYS",
"AWS_ACCESS_KEY": "",
"AWS_SECRET_KEY": "",
&ndash; Base64 encoded
"BringYourOwnKey": True,
"KEYS": []
}</p>
<p>input dictionary for creating AWS KMS with access node ( credential template file based authentication )
kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AWS_KMS",
"AWS_REGION_NAME": "US East (Ohio)",
&ndash; Optional Value. Default is "Asia Pacific (Mumbai)"
"ACCESS_NODE_NAME": "",
"KMS_NAME": "AWS_KMS_NAME",
"KEY_PROVIDER_AUTH_TYPE": "AWS_CREDENTIALS_FILE",
"AWS_CREDENTIALS_FILE_PROFILE_NAME": ""
}</p>
<p>input dictionary for creating AWS KMS with access Node ( IAM based authentication )
kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AWS_KMS",
"ACCESS_NODE_NAME": "",
"KMS_NAME": "",
"KEY_PROVIDER_AUTH_TYPE": "AWS_IAM"
}</p>
<p>input dictionary for creating Azure KMS with access Node ( certificate based authentication )
kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AZURE_KEY_VAULT",
"ACCESS_NODE_NAME": "",
"KMS_NAME": "",
"KEY_PROVIDER_AUTH_TYPE": "AZURE_KEY_VAULT_CERTIFICATE",
"AZURE_KEY_VAULT_KEY_LENGTH":2048,
&ndash; Optional Value. Default is 3072
"AZURE_KEY_VAULT_NAME":"",
"AZURE_TENANT_ID":"",
"AZURE_APP_ID":"",
"AZURE_CERTIFICATE_PATH":"",
"AZURE_CERTIFICATE_THUMBPRINT":"",
"AZURE_CERTIFICATE_PASSWORD": "",
&ndash; Base64 encoded
}</p>
<p>input dictionary for creating Azure KMS with access Node ( IAM managed identity based authentication )
kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AZURE_KEY_VAULT",
"ACCESS_NODE_NAME": "",
"KMS_NAME": "",
"KEY_PROVIDER_AUTH_TYPE": "AZURE_KEY_VAULT_IAM",
"AZURE_KEY_VAULT_NAME":"",
}</p>
<p>input dictionary for creating Azure KMS without access Node ( certificate based authentication )
kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AZURE_KEY_VAULT",
"KMS_NAME": "",
"KEY_PROVIDER_AUTH_TYPE": "AZURE_KEY_VAULT_CERTIFICATE",
"AZURE_KEY_VAULT_NAME":"",
"AZURE_TENANT_ID": "",
"AZURE_APP_ID": "",
"AZURE_CERTIFICATE_PATH": "",
"AZURE_CERTIFICATE_THUMBPRINT": "",
"AZURE_CERTIFICATE_PASSWORD": "",
&ndash; Base64 encoded
}</p>
<p>input dictionary for creating KMIP KMS with access Node ( certificate based authentication )
kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_KMIP",
"ACCESS_NODE_NAME": "",
"KMS_NAME": "",
"KEY_PROVIDER_AUTH_TYPE": "KMIP_CERTIFICATE",
"KMIP_CERTIFICATE_PATH": "",
"KMIP_CERTIFICATE_KEY_PATH": "",
"KMIP_CA_CERTIFICATE_PATH": "",
"KMIP_CERTIFICATE_PASS": "", &ndash; Base64 encoded
"KMIP_HOST": "",
"KMIP_PORT": "",
"KMIP_ENC_KEY_LENGTH":256
&ndash; Optional Value. Default is 256
}</p>
<p>input dictionary for Azure KMS with access Node ( certificate based authentication ) with Bring Your Own Key enabled
self.kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AZURE_KEY_VAULT",
"ACCESS_NODE_NAME": "",
"KMS_NAME": "MyKMS",
"KEY_PROVIDER_AUTH_TYPE": "AZURE_KEY_VAULT_CERTIFICATE",
"AZURE_KEY_VAULT_KEY_LENGTH": 2072,
"AZURE_KEY_VAULT_NAME": "",
"AZURE_TENANT_ID": "",
"AZURE_APP_ID": "",
"AZURE_CERTIFICATE_PATH": "",
"AZURE_CERTIFICATE_THUMBPRINT": "",
"AZURE_CERTIFICATE_PASSWORD": "",
&ndash; Base64 encoded
"BringYourOwnKey": True,
"KEYS": ["KeyID1/KeyVersion1", "KeyID2/KeyVersion2", "KeyID3/KeyVersion3"]
}</p>
<p>input dictionary for Azure KMS with access Node ( AD APp based authentication ) with Bring Your Own Key enabled
self.kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AZURE_KEY_VAULT",
"ACCESS_NODE_NAME": "",
"KMS_NAME": "MyKMS",
"KEY_PROVIDER_AUTH_TYPE": "AZURE_KEY_VAULT_KEY",
"AZURE_KEY_VAULT_KEY_LENGTH": 2072,
"AZURE_KEY_VAULT_NAME": "",
"AZURE_TENANT_ID": "",
"AZURE_APP_ID": "",
"AZURE_APP_SECRET": "", &ndash; Base64 encoded
"BringYourOwnKey": True,
"KEYS": ["KeyID1/KeyVersion1", "KeyID2/KeyVersion2", "KeyID3/KeyVersion3"]
}</p>
<p>input dictionary for Azure KMS without access Node ( AD APp based authentication ) with Bring Your Own Key enabled
self.kms_details = {
"KEY_PROVIDER_TYPE": "KEY_PROVIDER_AZURE_KEY_VAULT",
"KMS_NAME": "MyKMS",
"KEY_PROVIDER_AUTH_TYPE": "AZURE_KEY_VAULT_KEY",
"AZURE_KEY_VAULT_KEY_LENGTH": 2072,
"AZURE_KEY_VAULT_NAME": "",
"AZURE_TENANT_ID": "",
"AZURE_APP_ID": "",
"AZURE_APP_SECRET": "", &ndash; Base64 encoded
"BringYourOwnKey": True,
"KEYS": ["KeyID1/KeyVersion1", "KeyID2/KeyVersion2", "KeyID3/KeyVersion3"]
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L576-L775" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, kms_details):
    &#34;&#34;&#34;
    Method to add Key Management Server

    Args:
            kms_details    (dictionary)   -- dictionary with KMS details

    input dictionary for creating AWS KMS without access node ( key based authentication )
        kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
            &#34;KMS_NAME&#34;: &#34;KMS1&#34; ,
            &#34;AWS_ACCESS_KEY&#34;:&#34;&#34;,
            &#34;AWS_SECRET_KEY&#34;: &#34;&#34;,
            &#34;AWS_REGION_NAME&#34;: &#34;Asia Pacific (Mumbai)&#34;,  -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_KEYS&#34;
        }

    input dictionary for creating AWS KMS with access node ( key based authentication )
        kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
            &#34;AWS_REGION_NAME&#34;: &#34;US East (Ohio)&#34;,    -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
            &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
            &#34;KMS_NAME&#34;: &#34;&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;&#34;,
            &#34;AWS_ACCESS_KEY&#34;: &#34;&#34;,
            &#34;AWS_SECRET_KEY&#34;: &#34;&#34;     -- Base64 encoded
        }
        
    input dictionary for creating AWS KMS with access node ( key based authentication ) and by enabling Bring Your Own Key.
        kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
            &#34;AWS_REGION_NAME&#34;: &#34;US East (Ohio)&#34;,    -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
            &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
            &#34;KMS_NAME&#34;: &#34;&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_KEYS&#34;,
            &#34;AWS_ACCESS_KEY&#34;: &#34;&#34;,
            &#34;AWS_SECRET_KEY&#34;: &#34;&#34;,     -- Base64 encoded
            &#34;BringYourOwnKey&#34;: True,
            &#34;KEYS&#34;: []
        }

    input dictionary for creating AWS KMS with access node ( credential template file based authentication )
        kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
            &#34;AWS_REGION_NAME&#34;: &#34;US East (Ohio)&#34;,    -- Optional Value. Default is &#34;Asia Pacific (Mumbai)&#34;
            &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
            &#34;KMS_NAME&#34;: &#34;AWS_KMS_NAME&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_CREDENTIALS_FILE&#34;,
            &#34;AWS_CREDENTIALS_FILE_PROFILE_NAME&#34;: &#34;&#34;
        }

    input dictionary for creating AWS KMS with access Node ( IAM based authentication )
        kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AWS_KMS&#34;,
            &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
            &#34;KMS_NAME&#34;: &#34;&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AWS_IAM&#34;
        }
        
    

    input dictionary for creating Azure KMS with access Node ( certificate based authentication )
        kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
            &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
            &#34;KMS_NAME&#34;: &#34;&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;,
            &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;:2048,     -- Optional Value. Default is 3072
            &#34;AZURE_KEY_VAULT_NAME&#34;:&#34;&#34;,
            &#34;AZURE_TENANT_ID&#34;:&#34;&#34;,
            &#34;AZURE_APP_ID&#34;:&#34;&#34;,
            &#34;AZURE_CERTIFICATE_PATH&#34;:&#34;&#34;,
            &#34;AZURE_CERTIFICATE_THUMBPRINT&#34;:&#34;&#34;,
            &#34;AZURE_CERTIFICATE_PASSWORD&#34;: &#34;&#34;,    -- Base64 encoded
        }

    input dictionary for creating Azure KMS with access Node ( IAM managed identity based authentication )
        kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
            &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
            &#34;KMS_NAME&#34;: &#34;&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_IAM&#34;,
            &#34;AZURE_KEY_VAULT_NAME&#34;:&#34;&#34;,
        }

    input dictionary for creating Azure KMS without access Node ( certificate based authentication )
        kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
            &#34;KMS_NAME&#34;: &#34;&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;,
            &#34;AZURE_KEY_VAULT_NAME&#34;:&#34;&#34;,
            &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
            &#34;AZURE_APP_ID&#34;: &#34;&#34;,
            &#34;AZURE_CERTIFICATE_PATH&#34;: &#34;&#34;,
            &#34;AZURE_CERTIFICATE_THUMBPRINT&#34;: &#34;&#34;,
            &#34;AZURE_CERTIFICATE_PASSWORD&#34;: &#34;&#34;,    -- Base64 encoded
        }
        
    input dictionary for creating KMIP KMS with access Node ( certificate based authentication )
        kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_KMIP&#34;,
            &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
            &#34;KMS_NAME&#34;: &#34;&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;KMIP_CERTIFICATE&#34;,
            &#34;KMIP_CERTIFICATE_PATH&#34;: &#34;&#34;,
            &#34;KMIP_CERTIFICATE_KEY_PATH&#34;: &#34;&#34;,
            &#34;KMIP_CA_CERTIFICATE_PATH&#34;: &#34;&#34;,
            &#34;KMIP_CERTIFICATE_PASS&#34;: &#34;&#34;, -- Base64 encoded
            &#34;KMIP_HOST&#34;: &#34;&#34;,
            &#34;KMIP_PORT&#34;: &#34;&#34;,
            &#34;KMIP_ENC_KEY_LENGTH&#34;:256           -- Optional Value. Default is 256
        }

    input dictionary for Azure KMS with access Node ( certificate based authentication ) with Bring Your Own Key enabled
        self.kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
            &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
            &#34;KMS_NAME&#34;: &#34;MyKMS&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;,
            &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;: 2072,
            &#34;AZURE_KEY_VAULT_NAME&#34;: &#34;&#34;,
            &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
            &#34;AZURE_APP_ID&#34;: &#34;&#34;,
            &#34;AZURE_CERTIFICATE_PATH&#34;: &#34;&#34;,
            &#34;AZURE_CERTIFICATE_THUMBPRINT&#34;: &#34;&#34;,
            &#34;AZURE_CERTIFICATE_PASSWORD&#34;: &#34;&#34;,    -- Base64 encoded
            &#34;BringYourOwnKey&#34;: True,
            &#34;KEYS&#34;: [&#34;KeyID1/KeyVersion1&#34;, &#34;KeyID2/KeyVersion2&#34;, &#34;KeyID3/KeyVersion3&#34;]
        }
        
    input dictionary for Azure KMS with access Node ( AD APp based authentication ) with Bring Your Own Key enabled
        self.kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
            &#34;ACCESS_NODE_NAME&#34;: &#34;&#34;,
            &#34;KMS_NAME&#34;: &#34;MyKMS&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_KEY&#34;,
            &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;: 2072,
            &#34;AZURE_KEY_VAULT_NAME&#34;: &#34;&#34;,
            &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
            &#34;AZURE_APP_ID&#34;: &#34;&#34;,
            &#34;AZURE_APP_SECRET&#34;: &#34;&#34;, -- Base64 encoded
            &#34;BringYourOwnKey&#34;: True,
            &#34;KEYS&#34;: [&#34;KeyID1/KeyVersion1&#34;, &#34;KeyID2/KeyVersion2&#34;, &#34;KeyID3/KeyVersion3&#34;]
        }
    
    input dictionary for Azure KMS without access Node ( AD APp based authentication ) with Bring Your Own Key enabled
        self.kms_details = {
            &#34;KEY_PROVIDER_TYPE&#34;: &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;,
            &#34;KMS_NAME&#34;: &#34;MyKMS&#34;,
            &#34;KEY_PROVIDER_AUTH_TYPE&#34;: &#34;AZURE_KEY_VAULT_KEY&#34;,
            &#34;AZURE_KEY_VAULT_KEY_LENGTH&#34;: 2072,
            &#34;AZURE_KEY_VAULT_NAME&#34;: &#34;&#34;,
            &#34;AZURE_TENANT_ID&#34;: &#34;&#34;,
            &#34;AZURE_APP_ID&#34;: &#34;&#34;,
            &#34;AZURE_APP_SECRET&#34;: &#34;&#34;, -- Base64 encoded
            &#34;BringYourOwnKey&#34;: True,
            &#34;KEYS&#34;: [&#34;KeyID1/KeyVersion1&#34;, &#34;KeyID2/KeyVersion2&#34;, &#34;KeyID3/KeyVersion3&#34;]
        }
    &#34;&#34;&#34;
    
    KeyManagementServers._validate_input(kms_details, dict)

    if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] not in self._KMS_TYPE.values():
        raise SDKException(&#34;KeyManagementServer&#34;, 103)

    if kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] not in self._KMS_AUTHENTICATION_TYPE:
        raise SDKException(&#34;KeyManagementServer&#34;, 105)

    if &#34;KMS_NAME&#34; not in kms_details:
        raise SDKException(&#34;KeyManagementServer&#34;, 106)



    if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] == &#34;KEY_PROVIDER_AWS_KMS&#34;:
        if &#34;AWS_REGION_NAME&#34; not in kms_details:
            kms_details[&#34;AWS_REGION_NAME&#34;] = &#34;Asia Pacific (Mumbai)&#34;

        if kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_KEYS&#34;:
            self.add_aws_kms(kms_name=kms_details[&#39;KMS_NAME&#39;], aws_access_key=kms_details[&#39;AWS_ACCESS_KEY&#39;], aws_secret_key=kms_details[&#39;AWS_SECRET_KEY&#39;],aws_region_name=kms_details[&#34;AWS_REGION_NAME&#34;], kms_details = kms_details)

        elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_CREDENTIALS_FILE&#34;:
            self._add_aws_kms_with_cred_file(kms_details)

        elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_IAM&#34;:
            self._add_aws_kms_with_iam(kms_details)

    if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] == &#34;KEY_PROVIDER_AZURE_KEY_VAULT&#34;:
        if kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AZURE_KEY_VAULT_CERTIFICATE&#34;:
            self._add_azure_key_vault_certificate_auth(kms_details)

        elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AZURE_KEY_VAULT_IAM&#34;:
            self._add_azure_key_vault_iam_auth(kms_details)
        
        elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AZURE_KEY_VAULT_KEY&#34;:
            self._add_azure_key_vault_key_auth(kms_details)

    if kms_details[&#39;KEY_PROVIDER_TYPE&#39;] == &#34;KEY_PROVIDER_KMIP&#34;:
        self._add_kmip_certificate(kms_details)
        
    return self.get(kms_details[&#39;KMS_NAME&#39;])</code></pre>
</details>
</dd>
<dt id="cvpysdk.key_management_server.KeyManagementServers.add_aws_kms"><code class="name flex">
<span>def <span class="ident">add_aws_kms</span></span>(<span>self, kms_name, aws_access_key, aws_secret_key, aws_region_name=None, kms_details=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Configure AWS Key Management Server</p>
<h2 id="args">Args</h2>
<p>kms_name
(string) &ndash; name of the Key Management Server</p>
<p>aws_access_key
(string) &ndash; AWS access key</p>
<p>aws_secret_key
(string) &ndash; AWS secret key, base64 encoded</p>
<p>aws_region_name (string) &ndash; AWS region
defaults to "Asia Pacific (Mumbai)"</p>
<p>kms_details ( dictionary ) - Dictionary with AWS KMS details
Raises SDKException:
If inputs are wrong data type</p>
<pre><code>If API response is not successful

If the API response JSON is empty

If error code on API response JSON is not 0
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L894-L999" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_aws_kms(self, kms_name, aws_access_key, aws_secret_key, aws_region_name=None, kms_details = None):
    &#34;&#34;&#34;Configure AWS Key Management Server

        Args:
            kms_name        (string) -- name of the Key Management Server

            aws_access_key  (string) -- AWS access key

            aws_secret_key  (string) -- AWS secret key, base64 encoded

            aws_region_name (string) -- AWS region
                                        defaults to &#34;Asia Pacific (Mumbai)&#34;

            kms_details ( dictionary ) - Dictionary with AWS KMS details

        Raises SDKException:
            If inputs are wrong data type

            If API response is not successful

            If the API response JSON is empty

            If error code on API response JSON is not 0
    &#34;&#34;&#34;

    KeyManagementServers._validate_input(kms_name, str)

    payload = None
    is_bring_your_own_key = 0
    keys = []

    if &#34;BringYourOwnKey&#34; in kms_details:
        if kms_details.get(&#34;BringYourOwnKey&#34;):
            if &#34;KEYS&#34; not in kms_details:
                raise SDKException(&#39;KeyManagementServer&#39;, 107)
            if type(kms_details[&#39;KEYS&#39;]) != list :
                raise SDKException(&#39;Storage&#39;, 101)
            is_bring_your_own_key = 1
            for k in kms_details[&#39;KEYS&#39;]:
                keys.append({&#34;keyId&#34;: k})

    if kms_details == None or &#34;ACCESS_NODE_NAME&#34; not in kms_details:

        if aws_region_name is None:
            aws_region_name = &#34;Asia Pacific (Mumbai)&#34;

        KeyManagementServers._validate_input(aws_access_key, str)
        KeyManagementServers._validate_input(aws_secret_key, str)
        KeyManagementServers._validate_input(aws_region_name, str)

        payload = {
            &#34;keyProvider&#34;: {
                &#34;encryptionType&#34;: 3,
                &#34;keyProviderType&#34;: 3,
                &#34;provider&#34;: {
                    &#34;keyProviderName&#34;: kms_name
                },
                &#34;properties&#34;: {
                    &#34;regionName&#34;: aws_region_name,
                    &#34;userAccount&#34;: {
                        &#34;userName&#34;: aws_access_key,
                        &#34;password&#34;: aws_secret_key
                    }
                }
            }
        }

    elif kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;] == &#34;AWS_KEYS&#34; and kms_details[&#39;ACCESS_NODE_NAME&#39;] != None:

        if &#34;AWS_REGION_NAME&#34; not in kms_details:
            kms_details[&#39;AWS_REGION_NAME&#39;] = &#34;Asia Pacific (Mumbai)&#34;

        KeyManagementServers._validate_input(aws_access_key, str)
        KeyManagementServers._validate_input(aws_secret_key, str)
        KeyManagementServers._validate_input(aws_region_name, str)

        payload = {
                        &#34;keyProvider&#34;: {
                                &#34;properties&#34;: {
                                        &#34;accessNodes&#34;: [
                                            {
                                                    &#34;accessNode&#34;: {
                                                            &#34;clientName&#34;: kms_details[&#39;ACCESS_NODE_NAME&#39;]
                                                    },
                                                    &#34;awsCredential&#34;: {
                                                            &#34;userAccount&#34;: {
                                                                    &#34;password&#34;: aws_secret_key,
                                                                    &#34;userName&#34;: aws_access_key
                                                            },
                                                        &#34;amazonAuthenticationType&#34;: self._KMS_AUTHENTICATION_TYPE[kms_details[&#39;KEY_PROVIDER_AUTH_TYPE&#39;]]
                                                    }
                                            }
                                        ],
                                        &#34;bringYourOwnKey&#34;: str(is_bring_your_own_key),
                        &#34;keys&#34;: keys,
                                        &#34;regionName&#34;: aws_region_name if aws_region_name!=None else kms_details[&#39;AWS_REGION_NAME&#39;]
                                },
                                &#34;provider&#34;: {
                                        &#34;keyProviderName&#34;: kms_name
                                },
                                &#34;encryptionType&#34;: 3,
                                &#34;keyProviderType&#34;: &#34;3&#34;
                        }
            }

    self._kms_api_call(payload)</code></pre>
</details>
</dd>
<dt id="cvpysdk.key_management_server.KeyManagementServers.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, kms_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes a Key Management Server</p>
<h2 id="args">Args</h2>
<p>kms_name (string) &ndash; name of the Key Management Server
Raises SDKException:
If API response code is not successfull</p>
<pre><code>    If response JSON is empty

    If errorCode is not part of the response JSON
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L250-L286" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, kms_name):
    &#34;&#34;&#34;Deletes a Key Management Server

        Args:
            kms_name (string) -- name of the Key Management Server

        Raises SDKException:
                If API response code is not successfull

                If response JSON is empty

                If errorCode is not part of the response JSON
    &#34;&#34;&#34;
    if not self.has_kms(kms_name):
        raise SDKException(&#39;KeyManagementServer&#39;, 102)

    kms_id = self._kms_dict[kms_name.lower()][&#39;id&#39;]

    kms_service = self._KMS_DELETE % (kms_id)
    flag, response = self._cvpysdk_object.make_request(
        &#39;DELETE&#39;, kms_service)

    if not flag:
        response_string = self._commcell._update_response_(response.text)
        raise SDKException(&#34;Response&#34;, 101, response_string)

    if not response.json():
        raise SDKException(&#34;Response&#34;, 102)

    if &#34;errorCode&#34; not in response.json():
        raise SDKException(
            &#34;Response&#34;, 101, f&#34;Something went wrong while deleting {kms_name}&#34;)

    error_code = response.json()[&#34;errorCode&#34;]
    if error_code != 0:
        response_string = self._commcell._update_response_(response.text)
        raise SDKException(&#34;Response&#34;, 101, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.key_management_server.KeyManagementServers.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, kms_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets a specific Key Management Server object</p>
<h2 id="args">Args</h2>
<p>kms_name
(str)
&ndash; The Key Management Server to get</p>
<h2 id="returns">Returns</h2>
<p>kms
(object)
&ndash;
The KeyManagementServer object
Raises SDKException:
If kms_name is not str</p>
<pre><code>If Key Management Server not found
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L207-L226" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, kms_name):
    &#34;&#34;&#34;Gets a specific Key Management Server object
    
        Args:
            kms_name    (str)       -- The Key Management Server to get

        Returns:
            kms         (object)    --  The KeyManagementServer object
        
        Raises SDKException:
            If kms_name is not str

            If Key Management Server not found
    &#34;&#34;&#34;      
    if not self.has_kms(kms_name):
        raise SDKException(&#34;KeyManagementServer&#34;, 102)
    
    kms_info = self._kms_dict[kms_name.lower()]
    kms_obj = KeyManagementServer(self._commcell, kms_info[&#39;name&#39;], kms_info[&#39;id&#39;], kms_info[&#39;type_id&#39;])
    return kms_obj</code></pre>
</details>
</dd>
<dt id="cvpysdk.key_management_server.KeyManagementServers.get_all_kms"><code class="name flex">
<span>def <span class="ident">get_all_kms</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the name-indexed dictionary of all Key Management Servers</p>
<h2 id="returns">Returns</h2>
<dl>
<dt>the name-indexed dictionary of Key Management Server info</dt>
<dt>{</dt>
<dt><code>
name1</code></dt>
<dd>{
name: name1,
id: id1,
type_id: type_id1,
},
&hellip;</dd>
</dl>
<p>}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L229-L244" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_all_kms(self):
    &#34;&#34;&#34;Gets the name-indexed dictionary of all Key Management Servers

        Returns:
            the name-indexed dictionary of Key Management Server info
            {
                name1: {
                   name: name1, 
                   id: id1,
                   type_id: type_id1,
                },
                ...
            }
            
    &#34;&#34;&#34;
    return self._kms_dict</code></pre>
</details>
</dd>
<dt id="cvpysdk.key_management_server.KeyManagementServers.has_kms"><code class="name flex">
<span>def <span class="ident">has_kms</span></span>(<span>self, kms_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Check if the Key Management Server exist or not</p>
<h2 id="args">Args</h2>
<p>kms_name
(str)
&ndash; name of the Key Management Server</p>
<h2 id="returns">Returns</h2>
<p>result
(bool)
&ndash; whether Key Management Server exists or not
Raises SDKException:
If kms_name is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L290-L304" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_kms(self, kms_name):
    &#34;&#34;&#34;Check if the Key Management Server exist or not

        Args:
            kms_name    (str)   -- name of the Key Management Server

        Returns:
            result      (bool)  -- whether Key Management Server exists or not
        
        Raises SDKException:
            If kms_name is not string
    &#34;&#34;&#34;
    KeyManagementServers._validate_input(kms_name, str)
    
    return kms_name.lower() in self._kms_dict</code></pre>
</details>
</dd>
<dt id="cvpysdk.key_management_server.KeyManagementServers.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refreshes the dictionary of Key Management Servers</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/key_management_server.py#L246-L248" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refreshes the dictionary of Key Management Servers&#34;&#34;&#34;
    self._kms_dict = self._get_kms_dict()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#keymanagementserverconstants-attributes">KeyManagementServerConstants Attributes</a></li>
<li><a href="#keymanagementservers-attributes">KeyManagementServers Attributes</a></li>
<li><a href="#keymanagementservers">KeyManagementServers:</a></li>
<li><a href="#keymanagementserver">KeyManagementServer:</a></li>
<li><a href="#keymanagementserver-attributes">KeyManagementServer Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.key_management_server.KeyManagementServer" href="#cvpysdk.key_management_server.KeyManagementServer">KeyManagementServer</a></code></h4>
</li>
<li>
<h4><code><a title="cvpysdk.key_management_server.KeyManagementServerConstants" href="#cvpysdk.key_management_server.KeyManagementServerConstants">KeyManagementServerConstants</a></code></h4>
</li>
<li>
<h4><code><a title="cvpysdk.key_management_server.KeyManagementServers" href="#cvpysdk.key_management_server.KeyManagementServers">KeyManagementServers</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.key_management_server.KeyManagementServers.add" href="#cvpysdk.key_management_server.KeyManagementServers.add">add</a></code></li>
<li><code><a title="cvpysdk.key_management_server.KeyManagementServers.add_aws_kms" href="#cvpysdk.key_management_server.KeyManagementServers.add_aws_kms">add_aws_kms</a></code></li>
<li><code><a title="cvpysdk.key_management_server.KeyManagementServers.delete" href="#cvpysdk.key_management_server.KeyManagementServers.delete">delete</a></code></li>
<li><code><a title="cvpysdk.key_management_server.KeyManagementServers.get" href="#cvpysdk.key_management_server.KeyManagementServers.get">get</a></code></li>
<li><code><a title="cvpysdk.key_management_server.KeyManagementServers.get_all_kms" href="#cvpysdk.key_management_server.KeyManagementServers.get_all_kms">get_all_kms</a></code></li>
<li><code><a title="cvpysdk.key_management_server.KeyManagementServers.has_kms" href="#cvpysdk.key_management_server.KeyManagementServers.has_kms">has_kms</a></code></li>
<li><code><a title="cvpysdk.key_management_server.KeyManagementServers.refresh" href="#cvpysdk.key_management_server.KeyManagementServers.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>