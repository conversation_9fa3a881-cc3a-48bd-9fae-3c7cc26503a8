<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.cloudapps.cloud_database_subclient API documentation</title>
<meta name="description" content="File for operating on a Cloud Database Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.cloudapps.cloud_database_subclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Cloud Database Subclient.</p>
<p>CloudDatabaseSubclient is the only class defined in this file.</p>
<p>CloudDatabaseSubclient:
Derived class from CloudAppsSubclient Base class, representing a
Cloud Database subclient(Amazon RDS/Redshift/DocumentDB and DynamoDB), and
to perform operations on that subclient</p>
<h2 id="clouddatabasesubclient">Clouddatabasesubclient</h2>
<p>_get_subclient_properties()
&ndash;
gets the properties of Cloud Database Subclient</p>
<p>_get_subclient_properties_json()
&ndash;
gets the properties JSON of Cloud Database Subclient</p>
<p>content()
&ndash;
gets the content of the subclient</p>
<p>_set_content()
&ndash;
sets the content of the subclient</p>
<p>browse()
&ndash;
Browse and returns the content of this subclient's instance backups</p>
<p>restore()
&ndash;
Restores a cloud database from the specified source and restore options</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/cloud_database_subclient.py#L1-L199" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Cloud Database Subclient.

CloudDatabaseSubclient is the only class defined in this file.

CloudDatabaseSubclient:  Derived class from CloudAppsSubclient Base class, representing a
                        Cloud Database subclient(Amazon RDS/Redshift/DocumentDB and DynamoDB), and
                        to perform operations on that subclient

CloudDatabaseSubclient:

    _get_subclient_properties()         --  gets the properties of Cloud Database Subclient

    _get_subclient_properties_json()    --  gets the properties JSON of Cloud Database Subclient

    content()                           --  gets the content of the subclient

    _set_content()                      --  sets the content of the subclient

    browse()                            --  Browse and returns the content of this subclient&#39;s instance backups

    restore()                           --  Restores a cloud database from the specified source and restore options

&#34;&#34;&#34;
from ..casubclient import CloudAppsSubclient
from ...exception import SDKException


class CloudDatabaseSubclient(CloudAppsSubclient):
    &#34;&#34;&#34; Derived class from Subclient Base class, representing a Cloud Database subclient,
            and to perform operations on that subclient. &#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34; Gets the subclient related properties of Cloud Database subclient. &#34;&#34;&#34;

        super(CloudDatabaseSubclient, self)._get_subclient_properties()

        if &#39;cloudDbContent&#39; in self._subclient_properties:
            self._cloud_db_content = self._subclient_properties[&#34;cloudDbContent&#34;]
        else:
            self._cloud_db_content = {}

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34; Gets the properties JSON of Cloud Database Subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;cloudAppsSubClientProp&#34;: {
                        &#34;instanceType&#34;: self._backupset_object._instance_object.ca_instance_type
                    },
                    &#34;planEntity&#34;: {
                        &#34;planName&#34;: self.storage_policy
                    },
                    &#34;cloudDbContent&#34;: self._cloud_db_content
                }
        }
        return subclient_json

    def _set_content(self, content=None):
        &#34;&#34;&#34; Sets the subclient content dictionary

            Args:
                content         (list)      --  list of subclient content

        &#34;&#34;&#34;
        if content is not None:
            self._cloud_db_content = {
                &#34;children&#34;: content
            }

        self._set_subclient_properties(&#34;_cloud_db_content&#34;, self._cloud_db_content)

    @property
    def content(self):
        &#34;&#34;&#34; Gets the appropriate content from the Subclient relevant to the user.

           Returns:
               dict - dict of cloud database content associated with the subclient

       &#34;&#34;&#34;
        return self._cloud_db_content

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34; Creates the dict of content JSON to pass to the API to add/update content of a
            Cloud Database Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                dict - dict of the appropriate JSON for an agent to send to the POST Subclient API

            Raises :
                SDKException : if the subclient content is not a list value and if it is empty

        &#34;&#34;&#34;
        if isinstance(subclient_content, list) and subclient_content != []:
            self._set_content(content=subclient_content)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient content should be a list value and not empty&#39;
            )

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;
            Browses the content of this cloud database subclient&#39;s instance

            args: Dictionary of browse options

                Example:

                        {
                            &#39;start_time&#39;: 0,
                            &#39;end_time&#39;: 1570808875,
                            &#39;include_aged_data&#39;: 0,
                            &#39;copy_precedence&#39;: 0,
                        }

            kwargs: keyword argument of browse options

                Example:

                        {
                            start_time: 0,
                            end_time: 1570808875,
                            include_aged_data: 0,
                            copy_precedence: 0,
                        }

            Returns:
                dict - Browse response json that contains list of snapshot information

        &#34;&#34;&#34;
        return self._instance_object.browse(*args, **kwargs)

    def restore(
            self,
            destination,
            source,
            restore_options):
        &#34;&#34;&#34;
            Restores the content of this subclient&#39;s instance content

            Args:
                destination : Destination cluster name we want to restore to.

                source   : Source snapshot we want to restore from.

                restore_options  : Restore options needed to submit a restore request.

                Example:    Restore of amazon redshift instance cluster from snapshot
                        {
                            destination : &#39;cluster&#39;,
                            source : &#39;snapshot&#39;,
                            options :   {
                                            &#39;allowVersionUpgrade&#39; : true,
                                            &#39;publicallyAccessible&#39; : true,
                                            &#39;restoreTags&#39; : false,
                                            &#39;enableDeletionProtection&#39;: false,
                                            &#39;availabilityZone&#39;: &#39;us-east-2a&#39;,
                                            &#39;targetParameterGroup&#39;: &#39;param&#39;,
                                            &#39;targetSubnetGroup&#39;: &#39;subnet&#39;,
                                            &#39;nodeType&#39;: &#39;dc-large-8&#39;,
                                            &#39;targetPort&#39;: 2990,
                                            &#39;numberOfNodes&#39;: 1
                                        }
                        }

            Returns:

                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        return self._instance_object.restore(destination, source, restore_options)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient"><code class="flex name class">
<span>class <span class="ident">CloudDatabaseSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Subclient Base class, representing a Cloud Database subclient,
and to perform operations on that subclient. </p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/cloud_database_subclient.py#L46-L199" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CloudDatabaseSubclient(CloudAppsSubclient):
    &#34;&#34;&#34; Derived class from Subclient Base class, representing a Cloud Database subclient,
            and to perform operations on that subclient. &#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34; Gets the subclient related properties of Cloud Database subclient. &#34;&#34;&#34;

        super(CloudDatabaseSubclient, self)._get_subclient_properties()

        if &#39;cloudDbContent&#39; in self._subclient_properties:
            self._cloud_db_content = self._subclient_properties[&#34;cloudDbContent&#34;]
        else:
            self._cloud_db_content = {}

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34; Gets the properties JSON of Cloud Database Subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;cloudAppsSubClientProp&#34;: {
                        &#34;instanceType&#34;: self._backupset_object._instance_object.ca_instance_type
                    },
                    &#34;planEntity&#34;: {
                        &#34;planName&#34;: self.storage_policy
                    },
                    &#34;cloudDbContent&#34;: self._cloud_db_content
                }
        }
        return subclient_json

    def _set_content(self, content=None):
        &#34;&#34;&#34; Sets the subclient content dictionary

            Args:
                content         (list)      --  list of subclient content

        &#34;&#34;&#34;
        if content is not None:
            self._cloud_db_content = {
                &#34;children&#34;: content
            }

        self._set_subclient_properties(&#34;_cloud_db_content&#34;, self._cloud_db_content)

    @property
    def content(self):
        &#34;&#34;&#34; Gets the appropriate content from the Subclient relevant to the user.

           Returns:
               dict - dict of cloud database content associated with the subclient

       &#34;&#34;&#34;
        return self._cloud_db_content

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34; Creates the dict of content JSON to pass to the API to add/update content of a
            Cloud Database Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                dict - dict of the appropriate JSON for an agent to send to the POST Subclient API

            Raises :
                SDKException : if the subclient content is not a list value and if it is empty

        &#34;&#34;&#34;
        if isinstance(subclient_content, list) and subclient_content != []:
            self._set_content(content=subclient_content)
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Subclient content should be a list value and not empty&#39;
            )

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;
            Browses the content of this cloud database subclient&#39;s instance

            args: Dictionary of browse options

                Example:

                        {
                            &#39;start_time&#39;: 0,
                            &#39;end_time&#39;: 1570808875,
                            &#39;include_aged_data&#39;: 0,
                            &#39;copy_precedence&#39;: 0,
                        }

            kwargs: keyword argument of browse options

                Example:

                        {
                            start_time: 0,
                            end_time: 1570808875,
                            include_aged_data: 0,
                            copy_precedence: 0,
                        }

            Returns:
                dict - Browse response json that contains list of snapshot information

        &#34;&#34;&#34;
        return self._instance_object.browse(*args, **kwargs)

    def restore(
            self,
            destination,
            source,
            restore_options):
        &#34;&#34;&#34;
            Restores the content of this subclient&#39;s instance content

            Args:
                destination : Destination cluster name we want to restore to.

                source   : Source snapshot we want to restore from.

                restore_options  : Restore options needed to submit a restore request.

                Example:    Restore of amazon redshift instance cluster from snapshot
                        {
                            destination : &#39;cluster&#39;,
                            source : &#39;snapshot&#39;,
                            options :   {
                                            &#39;allowVersionUpgrade&#39; : true,
                                            &#39;publicallyAccessible&#39; : true,
                                            &#39;restoreTags&#39; : false,
                                            &#39;enableDeletionProtection&#39;: false,
                                            &#39;availabilityZone&#39;: &#39;us-east-2a&#39;,
                                            &#39;targetParameterGroup&#39;: &#39;param&#39;,
                                            &#39;targetSubnetGroup&#39;: &#39;subnet&#39;,
                                            &#39;nodeType&#39;: &#39;dc-large-8&#39;,
                                            &#39;targetPort&#39;: 2990,
                                            &#39;numberOfNodes&#39;: 1
                                        }
                        }

            Returns:

                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        return self._instance_object.restore(destination, source, restore_options)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="../casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate content from the Subclient relevant to the user.</p>
<h2 id="returns">Returns</h2>
<p>dict - dict of cloud database content associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/cloud_database_subclient.py#L98-L106" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34; Gets the appropriate content from the Subclient relevant to the user.

       Returns:
           dict - dict of cloud database content associated with the subclient

   &#34;&#34;&#34;
    return self._cloud_db_content</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the content of this cloud database subclient's instance</p>
<p>args: Dictionary of browse options</p>
<pre><code>Example:

        {
            'start_time': 0,
            'end_time': 1570808875,
            'include_aged_data': 0,
            'copy_precedence': 0,
        }
</code></pre>
<p>kwargs: keyword argument of browse options</p>
<pre><code>Example:

        {
            start_time: 0,
            end_time: 1570808875,
            include_aged_data: 0,
            copy_precedence: 0,
        }
</code></pre>
<h2 id="returns">Returns</h2>
<p>dict - Browse response json that contains list of snapshot information</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/cloud_database_subclient.py#L130-L160" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, *args, **kwargs):
    &#34;&#34;&#34;
        Browses the content of this cloud database subclient&#39;s instance

        args: Dictionary of browse options

            Example:

                    {
                        &#39;start_time&#39;: 0,
                        &#39;end_time&#39;: 1570808875,
                        &#39;include_aged_data&#39;: 0,
                        &#39;copy_precedence&#39;: 0,
                    }

        kwargs: keyword argument of browse options

            Example:

                    {
                        start_time: 0,
                        end_time: 1570808875,
                        include_aged_data: 0,
                        copy_precedence: 0,
                    }

        Returns:
            dict - Browse response json that contains list of snapshot information

    &#34;&#34;&#34;
    return self._instance_object.browse(*args, **kwargs)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient.restore"><code class="name flex">
<span>def <span class="ident">restore</span></span>(<span>self, destination, source, restore_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the content of this subclient's instance content</p>
<h2 id="args">Args</h2>
<p>destination : Destination cluster name we want to restore to.</p>
<p>source
: Source snapshot we want to restore from.</p>
<p>restore_options
: Restore options needed to submit a restore request.</p>
<dl>
<dt><strong><code>Example</code></strong></dt>
<dd>Restore of amazon redshift instance cluster from snapshot
{
destination : 'cluster',
source : 'snapshot',
options :
{
'allowVersionUpgrade' : true,
'publicallyAccessible' : true,
'restoreTags' : false,
'enableDeletionProtection': false,
'availabilityZone': 'us-east-2a',
'targetParameterGroup': 'param',
'targetSubnetGroup': 'subnet',
'nodeType': 'dc-large-8',
'targetPort': 2990,
'numberOfNodes': 1
}
}</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/cloud_database_subclient.py#L162-L199" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore(
        self,
        destination,
        source,
        restore_options):
    &#34;&#34;&#34;
        Restores the content of this subclient&#39;s instance content

        Args:
            destination : Destination cluster name we want to restore to.

            source   : Source snapshot we want to restore from.

            restore_options  : Restore options needed to submit a restore request.

            Example:    Restore of amazon redshift instance cluster from snapshot
                    {
                        destination : &#39;cluster&#39;,
                        source : &#39;snapshot&#39;,
                        options :   {
                                        &#39;allowVersionUpgrade&#39; : true,
                                        &#39;publicallyAccessible&#39; : true,
                                        &#39;restoreTags&#39; : false,
                                        &#39;enableDeletionProtection&#39;: false,
                                        &#39;availabilityZone&#39;: &#39;us-east-2a&#39;,
                                        &#39;targetParameterGroup&#39;: &#39;param&#39;,
                                        &#39;targetSubnetGroup&#39;: &#39;subnet&#39;,
                                        &#39;nodeType&#39;: &#39;dc-large-8&#39;,
                                        &#39;targetPort&#39;: 2990,
                                        &#39;numberOfNodes&#39;: 1
                                    }
                    }

        Returns:

            object - instance of the Job class for this restore job
    &#34;&#34;&#34;
    return self._instance_object.restore(destination, source, restore_options)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="../casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.allow_multiple_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.backup" href="../../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.data_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.deduplication_options" href="../../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.description" href="../../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.display_name" href="../../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup_at_time" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup_days" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.encryption_flag" href="../../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.exclude_from_sla" href="../../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find" href="../../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find_latest_job" href="../../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.get_ma_associated_storagepolicy" href="../../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_blocklevel_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_intelli_snap_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_on_demand_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_trueup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.last_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.list_media" href="../../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.name" href="../../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.network_agent" href="../../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.next_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.plan" href="../../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.properties" href="../../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.read_buffer_size" href="../../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.refresh" href="../../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.restore_in_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.restore_out_of_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.run_content_indexing" href="../../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_backup_nodes" href="../../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.snapshot_engine_name" href="../../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.software_compression" href="../../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma_id" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_policy" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_guid" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_id" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_name" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.unset_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.update_properties" href="../../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.cloudapps" href="index.html">cvpysdk.subclients.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient" href="#cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient">CloudDatabaseSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient.browse" href="#cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient.content" href="#cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient.restore" href="#cvpysdk.subclients.cloudapps.cloud_database_subclient.CloudDatabaseSubclient.restore">restore</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>