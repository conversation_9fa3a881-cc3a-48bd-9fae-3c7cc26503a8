<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.security.user API documentation</title>
<meta name="description" content="Main file for managing users on this commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.security.user</code></h1>
</header>
<section id="section-intro">
<p>Main file for managing users on this commcell</p>
<p>Users and User are only the two classes defined in this commcell</p>
<h2 id="users">Users</h2>
<p><strong>init</strong>()
&ndash;
initializes the users class object</p>
<p><strong>str</strong>()
&ndash;
returns all the users associated with the
commcell</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the
Users class</p>
<p>_get_users()
&ndash;
gets all the users on this commcell</p>
<p>_get_fl_parameters()
&ndash;
Returns the fl parameters to be passed in the mongodb caching api call</p>
<p>_get_sort_parameters()
&ndash;
Returns the sort parameters to be passed in the mongodb caching api call</p>
<p>_get_fq_parameters()
&ndash;
Returns the fq parameters based on the fq list passed</p>
<p>get_users_cache()
&ndash;
Gets all the users present in CommcellEntityCache DB.</p>
<p>all_users_cache()
&ndash;
Returns dict of all the users and their info present in CommcellEntityCache
in mongoDB</p>
<p>_process_add_or_delete_response()
&ndash;
process the add or delete users response</p>
<p>add()
&ndash;
adds local/external user to commcell</p>
<p>has_user()
&ndash;
checks if user with specified user exists
on this commcell</p>
<p>get()
&ndash;
returns the user class object for the
specified user name</p>
<p>delete()
&ndash;
deletes the user on this commcell</p>
<p>refresh()
&ndash;
refreshes the list of users on this
commcell</p>
<p>all_users()
&ndash;
Returns all the users present in the commcell</p>
<p>_get_users_on_service_commcell()
&ndash; gets the users from service commcell</p>
<p>all_users_prop()
&ndash;
Returns complete GET API response</p>
<p>User
<strong>init</strong>()
&ndash;
initiaizes the user class object</p>
<pre><code>__repr__()                          --  returns the string for the instance of the
                                        User class

_get_user_id()                      --  returns the user id associated with this
                                        user

_get_user_properties()              --  gets all the properties associated with
                                        this user

_update_user_props()                --  updates the properties associated with
                                        this user

_update_usergroup_request()         --  makes the request to update usergroups
                                        associated with this user

user_name()                         --  returns the name of this user

user_id()                           --  returns the id of this user

description()                       --  returns the description of this user

email()                             --  returns the email of this user

upn()                               --  Returns user principal name of the user

number_of_laptops()                 --  Returns number of devices for the user

associated_usergroups()             --  returns the usergroups associated with
                                        this user

associated_external_usergroups()    -- returns the external usergroups associated with this user

add_usergroups()                    --  associates the usergroups with this user

remove_usergroups()                 --  disassociated the usergroups with this user

overwrite_usergroups()              --  reassociates the usergroups with this user

refresh()                           --  refreshes the properties of this user

update_security_associations()      --  updates 3-way security associations on user

request_OTP()                       --  fetches OTP for user

user_security_associations()        --  returns sorted roles and custom roles present on the
                                        different entities.

status()                            --  returns the status of user

update_user_password()              --  Updates new passwords of user

user_guid()                         --  returns user GUID

age_password_days()                 --  returns age password days for user

user_company_name()                 -- returns company name if user is a company user else returns empty str

get_account_lock_info()             -- returns account lock information

unlock()                            --  Unlocks user account

reset_tenant_password()             --  resets password of a tenant admin using token received in email
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1-L1643" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for managing users on this commcell

Users and User are only the two classes defined in this commcell

Users:
    __init__()                          --  initializes the users class object

    __str__()                           --  returns all the users associated with the
                                            commcell

    __repr__()                          --  returns the string for the instance of the
                                            Users class

    _get_users()                        --  gets all the users on this commcell

    _get_fl_parameters()                --  Returns the fl parameters to be passed in the mongodb caching api call

    _get_sort_parameters()              --  Returns the sort parameters to be passed in the mongodb caching api call

    _get_fq_parameters()                --  Returns the fq parameters based on the fq list passed

    get_users_cache()                   --  Gets all the users present in CommcellEntityCache DB.

    all_users_cache()                   --  Returns dict of all the users and their info present in CommcellEntityCache
                                            in mongoDB

    _process_add_or_delete_response()   --  process the add or delete users response

    add()                               --  adds local/external user to commcell

    has_user()                          --  checks if user with specified user exists
                                            on this commcell

    get()                               --  returns the user class object for the
                                            specified user name

    delete()                            --  deletes the user on this commcell

    refresh()                           --  refreshes the list of users on this
                                            commcell

    all_users()                         --  Returns all the users present in the commcell

    _get_users_on_service_commcell()    -- gets the users from service commcell

    all_users_prop()                    --  Returns complete GET API response

User
    __init__()                          --  initiaizes the user class object

    __repr__()                          --  returns the string for the instance of the
                                            User class

    _get_user_id()                      --  returns the user id associated with this
                                            user

    _get_user_properties()              --  gets all the properties associated with
                                            this user

    _update_user_props()                --  updates the properties associated with
                                            this user

    _update_usergroup_request()         --  makes the request to update usergroups
                                            associated with this user

    user_name()                         --  returns the name of this user

    user_id()                           --  returns the id of this user

    description()                       --  returns the description of this user

    email()                             --  returns the email of this user

    upn()                               --  Returns user principal name of the user

    number_of_laptops()                 --  Returns number of devices for the user

    associated_usergroups()             --  returns the usergroups associated with
                                            this user

    associated_external_usergroups()    -- returns the external usergroups associated with this user

    add_usergroups()                    --  associates the usergroups with this user

    remove_usergroups()                 --  disassociated the usergroups with this user

    overwrite_usergroups()              --  reassociates the usergroups with this user

    refresh()                           --  refreshes the properties of this user

    update_security_associations()      --  updates 3-way security associations on user

    request_OTP()                       --  fetches OTP for user

    user_security_associations()        --  returns sorted roles and custom roles present on the
                                            different entities.

    status()                            --  returns the status of user

    update_user_password()              --  Updates new passwords of user

    user_guid()                         --  returns user GUID

    age_password_days()                 --  returns age password days for user

    user_company_name()                 -- returns company name if user is a company user else returns empty str

    get_account_lock_info()             -- returns account lock information

    unlock()                            --  Unlocks user account

    reset_tenant_password()             --  resets password of a tenant admin using token received in email
&#34;&#34;&#34;

from base64 import b64encode
from .security_association import SecurityAssociation
from ..exception import SDKException


class Users(object):
    &#34;&#34;&#34;Class for maintaining all the configured users on this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes the users class object for this commcell

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Clients class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._users = None
        self._users_cache = None
        self._users_on_service = None
        self._all_users_prop = None
        self.filter_query_count = 0
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all users of the commcell.

            Returns:
                str - string of all the users configured on the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Users&#39;)

        for index, user in enumerate(self._users):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, user)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Users class.&#34;&#34;&#34;
        return &#34;Users class instance for Commcell&#34;

    def _get_users(self, full_response: bool = False):
        &#34;&#34;&#34;Returns the list of users configured on this commcell

            Args:
                full_response(bool) --  flag to return complete response

            Returns:
                dict of all the users on this commcell
                    {
                        &#39;user_name_1&#39;: user_id_1
                    }

        &#34;&#34;&#34;
        get_all_user_service = self._commcell_object._services[&#39;USERS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, get_all_user_service
        )

        if flag:
            if response.json() and &#39;users&#39; in response.json():
                if full_response:
                    return response.json()
                users_dict = {}

                for user in response.json()[&#39;users&#39;]:
                    temp_name = user[&#39;userEntity&#39;][&#39;userName&#39;].lower()
                    temp_id = user[&#39;userEntity&#39;][&#39;userId&#39;]
                    users_dict[temp_name] = temp_id

                return users_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_fl_parameters(self, fl: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fl parameters to be passed in the mongodb caching api call

        Args:
            fl    (list)  --   list of columns to be passed in API request

        Returns:
            fl_parameters(str) -- fl parameter string
        &#34;&#34;&#34;
        self.valid_columns = {
            &#39;userName&#39;: &#39;users.userEntity.userName&#39;,
            &#39;userId&#39;: &#39;users.userEntity.userId&#39;,
            &#39;email&#39;: &#39;users.email&#39;,
            &#39;fullName&#39;: &#39;users.fullName&#39;,
            &#39;description&#39;: &#39;users.description&#39;,
            &#39;UPN&#39;: &#39;users.UPN&#39;,
            &#39;enableUser&#39;: &#39;users.enableUser&#39;,
            &#39;isAccountLocked&#39;: &#39;users.isAccountLocked&#39;,
            &#39;numDevices&#39;: &#39;users.numDevices&#39;,
            &#39;company&#39;: &#39;users.userEntity.entityInfo.companyName&#39;,
            &#39;lastLogIntime&#39;: &#39;users.lastLogIntime&#39;,
            &#39;commcell&#39;: &#39;users.userEntity.entityInfo.multiCommcellName&#39;
        }
        default_columns = &#39;users.userEntity&#39;

        if fl:
            if all(col in self.valid_columns for col in fl):
                fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(self.valid_columns[column] for column in fl)}&#34;
            else:
                raise SDKException(&#39;User&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        else:
            fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(column for column in self.valid_columns.values())}&#34;

        return fl_parameters

    def _get_sort_parameters(self, sort: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the sort parameters to be passed in the mongodb caching api call

        Args:
            sort  (list)  --   contains the name of the column on which sorting will be performed and type of sort
                                valid sor type -- 1 for ascending and -1 for descending
                                e.g. sort = [&#39;connectName&#39;,&#39;1&#39;]

        Returns:
            sort_parameters(str) -- sort parameter string
        &#34;&#34;&#34;
        sort_type = str(sort[1])
        col = sort[0]
        if col in self.valid_columns.keys() and sort_type in [&#39;1&#39;, &#39;-1&#39;]:
            sort_parameter = &#39;&amp;sort=&#39; + self.valid_columns[col] + &#39;:&#39; + sort_type
        else:
            raise SDKException(&#39;User&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        return sort_parameter

    def _get_fq_parameters(self, fq: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fq parameters based on the fq list passed
        Args:
             fq     (list) --   contains the columnName, condition and value
                    e.g. fq = [[&#39;UserName&#39;,&#39;contains&#39;, &#39;test&#39;],[&#39;email&#39;,&#39;contains&#39;, &#39;test&#39;]]

        Returns:
            fq_parameters(str) -- fq parameter string
        &#34;&#34;&#34;
        conditions = {&#34;contains&#34;, &#34;notContain&#34;, &#34;eq&#34;, &#34;neq&#34;, &#34;gt&#34;, &#34;lt&#34;}
        params = []

        for column, condition, *value in fq or []:
            if column not in self.valid_columns:
                raise SDKException(&#39;User&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)

            if condition in conditions:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:{condition.lower()}:{value[0]}&#34;)
            elif condition == &#34;isEmpty&#34; and not value:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:in:null,&#34;)
            elif condition.lower() == &#34;between&#34; and value and &#34;-&#34; in value[0]:
                start, end = value[0].split(&#34;-&#34;, 1)
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:gteq:{start}&#34;)
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:lteq:{end}&#34;)
            else:
                raise SDKException(&#39;User&#39;, &#39;102&#39;, &#39;Invalid condition passed&#39;)

        return &#34;&#34;.join(params)

    def get_users_cache(self, hard: bool = False, **kwargs) -&gt; dict:
        &#34;&#34;&#34;
        Gets all the users present in CommcellEntityCache DB.

        Args:
            hard  (bool)        --   Flag to perform hard refresh on users cache.
            **kwargs (dict):
                fl (list)       --   List of columns to return in response (default: None).
                sort (list)     --   Contains the name of the column on which sorting will be performed and type of sort.
                                        Valid sort type: 1 for ascending and -1 for descending
                                        e.g. sort = [&#39;columnName&#39;, &#39;1&#39;] (default: None).
                limit (list)    --   Contains the start and limit parameter value.
                                        Default [&#39;0&#39;, &#39;100&#39;].
                search (str)    --   Contains the string to search in the commcell entity cache (default: None).
                fq (list)       --   Contains the columnName, condition and value.
                                        e.g. fq = [[&#39;UserName&#39;, &#39;contains&#39;, &#39;test&#39;],
                                         [&#39;email&#39;, &#39;contains&#39;, &#39;test&#39;]] (default: None).

        Returns:
            dict: Dictionary of all the properties present in response.
        &#34;&#34;&#34;
        # computing params
        fl_parameters = self._get_fl_parameters(kwargs.get(&#39;fl&#39;, None))
        fq_parameters = self._get_fq_parameters(kwargs.get(&#39;fq&#39;, None))
        limit = kwargs.get(&#39;limit&#39;, None)
        limit_parameters = f&#39;start={limit[0]}&amp;limit={limit[1]}&#39; if limit else &#39;&#39;
        hard_refresh = &#39;&amp;hardRefresh=true&#39; if hard else &#39;&#39;
        sort_parameters = self._get_sort_parameters(kwargs.get(&#39;sort&#39;, None)) if kwargs.get(&#39;sort&#39;, None) else &#39;&#39;

        # Search operation can only be performed on limited columns, so filtering out the columns on which search works
        searchable_columns = [&#34;userName&#34;,&#34;email&#34;,&#34;fullName&#34;,&#34;company&#34;,&#34;description&#34;]
        search_parameter = (f&#39;&amp;search={&#34;,&#34;.join(self.valid_columns[col] for col in searchable_columns)}:contains:&#39;
                            f&#39;{kwargs.get(&#34;search&#34;, None)}&#39;) if kwargs.get(&#39;search&#39;, None) else &#39;&#39;

        params = [
            limit_parameters,
            sort_parameters,
            fl_parameters,
            hard_refresh,
            search_parameter,
            fq_parameters
        ]

        request_url = f&#34;{self._commcell_object._services[&#39;USERS&#39;]}?&#34; + &#34;&#34;.join(params)
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, request_url)

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        users_cache = {}
        if response.json() and &#39;users&#39; in response.json():
            self.filter_query_count = response.json().get(&#39;filterQueryCount&#39;,0)
            for user in response.json()[&#39;users&#39;]:
                name = user.get(&#39;userEntity&#39;, {}).get(&#39;userName&#39;)
                users_config = {
                    &#39;userName&#39;: name,
                    &#39;userId&#39;: user.get(&#39;userEntity&#39;, {}).get(&#39;userId&#39;),
                    &#39;email&#39;: user.get(&#39;email&#39;),
                    &#39;fullName&#39;: user.get(&#39;fullName&#39;),
                    &#39;description&#39;: user.get(&#39;description&#39;,&#39;&#39;),
                    &#39;UPN&#39;: user.get(&#39;UPN&#39;),
                    &#39;enableUser&#39;: user.get(&#39;enableUser&#39;),
                    &#39;isAccountLocked&#39;: user.get(&#39;isAccountLocked&#39;),
                    &#39;numDevices&#39;: user.get(&#39;numDevices&#39;),
                    &#39;company&#39;: user.get(&#39;userEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;),
                    &#39;lastLogIntime&#39;: user.get(&#39;lastLogIntime&#39;)
                }
                if self._commcell_object.is_global_scope():
                    users_config[&#39;commcell&#39;] = user.get(&#39;userEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;multiCommcellName&#39;)

                    # Handle duplicate names for different commcells
                    unique_name = name
                    i = 1
                    while unique_name in users_cache:
                        existing_user = users_cache[unique_name]
                        if existing_user.get(&#39;commcell&#39;) != users_config.get(&#39;commcell&#39;):
                            unique_name = f&#34;{name}__{i}&#34;
                            i += 1
                        else:
                            break
                    users_cache[unique_name] = users_config
                else:
                    users_cache[name] = users_config

            return users_cache
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def all_users_cache(self) -&gt; dict:
        &#34;&#34;&#34;Returns dict of all the users and their info present in CommcellEntityCache in mongoDB

            dict - consists of all users of the in the CommcellEntityCache
                    {
                         &#34;user1_name&#34;: {
                                &#39;id&#39;: user1_id ,
                                &#39;email&#39;: user1_email,
                                &#39;fullName&#39;: user1_fullName,
                                &#39;description&#39;: user1_description,
                                &#39;UPN&#39;: user1_UPN,
                                &#39;enabled&#39;: user1_enabled_user_flag,
                                &#39;locked&#39;: user1_is_user_locked_flag,
                                &#39;numberOfLaptops&#39;: user1_number_of_devices,
                                &#39;company&#39;: user1_company
                                },
                         &#34;user2_name&#34;: {
                                &#39;id&#39;: user2_id ,
                                &#39;email&#39;: user2_email,
                                &#39;fullName&#39;: user2_fullName,
                                &#39;description&#39;: user2_description,
                                &#39;UPN&#39;: user2_UPN,
                                &#39;enabled&#39;: user2_enabled_user_flag,
                                &#39;locked&#39;: user2_is_user_locked_flag,
                                &#39;numberOfLaptops&#39;: user2_number_of_devices,
                                &#39;company&#39;: user2_company
                                }
                    }
        &#34;&#34;&#34;
        if not self._users_cache:
            self._users_cache = self.get_users_cache()
        return self._users_cache

    def _process_add_or_delete_response(self, flag, response):
        &#34;&#34;&#34;Processes the flag and response received from the server during add delete request

            Args:
                request_object  (object)  --  request objects specifying the details
                                              to request

            Raises:
                SDKException:
                    if response is empty

                    if reponse is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                error_code = -1
                error_message = &#39;&#39;
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    if &#39;errorString&#39; in response_json:
                        error_message = response_json[&#39;errorString&#39;]
                elif &#39;errorCode&#39; in response.json():
                    error_code = response.json()[&#39;errorCode&#39;]
                    if &#39;errorMessage&#39; in response:
                        error_message = response[&#39;errorMessage&#39;]

                return error_code, error_message

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _add_user(self, create_user_request):
        &#34;&#34;&#34;Makes the add user request on the server

            Args:
                create_user_request     (dict)  --  request json to create an user

            Raises:
                SDKException:
                    if failed to add user
        &#34;&#34;&#34;
        add_user = self._commcell_object._services[&#39;USERS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, add_user, create_user_request
        )
        error_code, error_message = self._process_add_or_delete_response(flag, response)

        if not error_message:
            error_message = &#39;Failed to add user. Please check logs for further details.&#39;

        if error_code != 0:
            raise SDKException(&#39;User&#39;, &#39;102&#39;, error_message)

        self._users = self._get_users()

        return response.json()

    def add(self,
            user_name,
            email,
            full_name=None,
            domain=None,
            password=None,
            system_generated_password=False,
            local_usergroups=None,
            entity_dictionary=None):
        &#34;&#34;&#34;Adds a local/external user to this commcell

            Args:
                user_name                     (str)     --  name of the user to be
                                                            created

                full_name                     (str)     --  full name of the user to be
                                                            created

                email                         (str)     --  email of the user to be
                                                            created

                domain                        (str)     --  Needed in case you are adding
                                                            external user

                password                      (str)     --  password of the user to be
                                                            created
                    default: None

                local_usergroups              (list)     --  user can be member of
                                                            these user groups
                                                            Ex:1. [&#34;master&#34;],
                                                               2. [&#34;group1&#34;, &#34;group2&#34;]

                system_generated_password     (bool)    --  if set to true system
                                                            defined password will be used
                                                            default: False

                entity_dictionary   --      combination of entity_type, entity names
                                            and role

                e.g.: security_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    }
                entity_type         --      key for the entity present in dictionary
                                            on which user will have access

                entity_name         --      Value of the key

                 role               --      key for role name you specify

                e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
                entity_type:    clientName, mediaAgentName, libraryName, userName,
                                userGroupName, storagePolicyName, clientGroupName,
                                schedulePolicyName, locationName, providerDomainName,
                                alertName, workflowName, policyName, roleName

                entity_name:    client name for entity_type &#39;clientName&#39;
                                Media agent name for entitytype &#39;mediaAgentName&#39;
                                similar for other entity_typees

            Raises:
                SDKException:
                    if data type of input is invalid

                    if user with specified name already exists

                    if password or system_generated_password are not set

                    if failed to add user to commcell
        &#34;&#34;&#34;
        if domain:
            username = &#34;{0}\\{1}&#34;.format(domain, user_name)
            password = &#34;&#34;
            system_generated_password = False
        else:
            username = user_name
            if not password:
                system_generated_password = True

        if not (isinstance(username, str) and
                isinstance(email, str)):
            raise SDKException(&#39;User&#39;, &#39;101&#39;)

        if self.has_user(username):
            raise SDKException(&#39;User&#39;, &#39;103&#39;, &#39;User: {0}&#39;.format(username))

        if password is not None:
            password = b64encode(password.encode()).decode()
        else:
            password = &#39;&#39;

        if local_usergroups:
            groups_json = [{&#34;userGroupName&#34;: lname} for lname in local_usergroups]
        else:
            groups_json = [{}]

        security_json = {}
        if entity_dictionary:
            security_request = SecurityAssociation._security_association_json(
                entity_dictionary=entity_dictionary)
            security_json = {
                &#34;associationsOperationType&#34;: &#34;ADD&#34;,
                &#34;associations&#34;: security_request
                }

        create_user_request = {
            &#34;users&#34;: [{
                &#34;password&#34;: password,
                &#34;email&#34;: email,
                &#34;fullName&#34;: full_name,
                &#34;systemGeneratePassword&#34;: system_generated_password,
                &#34;userEntity&#34;: {
                    &#34;userName&#34;: username
                },
                &#34;securityAssociations&#34;: security_json,
                &#34;associatedUserGroups&#34;: groups_json
            }]
        }
        response_json = self._add_user(create_user_request)


        created_user_username = response_json.get(&#34;response&#34;, [{}])[0].get(&#34;entity&#34;, {}).get(&#34;userName&#34;)

        return self.get(created_user_username)

    def has_user(self, user_name):
        &#34;&#34;&#34;Checks if any user with specified name exists on this commcell

            Args:
                user_name         (str)     --     name of the user which has to be
                                                   checked if exists

            Raises:
                SDKException:
                    if data type of input is invalid
        &#34;&#34;&#34;
        if not isinstance(user_name, str):
            raise SDKException(&#39;User&#39;, &#39;101&#39;)

        return self._users and user_name.lower() in self._users

    def get(self, user_name):
        &#34;&#34;&#34;Returns the user object for the specified user name

            Args:
                user_name  (str)  --  name of the user for which the object has to be
                                      created

            Raises:
                SDKException:
                    if user doesn&#39;t exist with specified name
        &#34;&#34;&#34;
        if not self.has_user(user_name):
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                    user_name)
            )

        return User(self._commcell_object, user_name, self._users[user_name.lower()])

    def delete(self, user_name, new_user=None, new_usergroup=None):
        &#34;&#34;&#34;Deletes the specified user from the existing commcell users

            Args:
                user_name       (str)   --  name of the user which has to be deleted

                new_user        (str)   --  name of the target user, whom the ownership
                                            of entities should be transferred

                new_usergroup   (str)   --  name of the user group, whom the ownership
                                            of entities should be transferred

                Note: either user or usergroup  should be provided for ownership
                transfer not both.

            Raises:
                SDKException:
                    if user doesn&#39;t exist

                    if new user and new usergroup any of these is passed and these doesn&#39;t
                    exist on commcell

                    if both user and usergroup is passed for ownership transfer

                    if both user and usergroup is not passed for ownership transfer

                    if response is not success

        &#34;&#34;&#34;
        if not self.has_user(user_name):
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                    user_name)
            )
        if new_user and new_usergroup:
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;{0} and {1} both can not be set as owner!! &#34;
                &#34;please send either new_user or new_usergroup&#34;.format(new_user, new_usergroup)
            )
        else:
            if new_user:
                if not self.has_user(new_user):
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                            new_user)
                    )
                new_user_id = self._users[new_user.lower()]
                new_group_id = 0
            else:
                if new_usergroup:
                    if not self._commcell_object.user_groups.has_user_group(new_usergroup):
                        raise SDKException(
                            &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t exists &#34;
                            &#34;on this commcell.&#34;.format(new_usergroup)
                        )
                else:
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;,
                        &#34;Ownership transfer is mondatory!! Please provide new owner information&#34;
                    )
                new_group_id = self._commcell_object.user_groups.get(new_usergroup).user_group_id
                new_user_id = 0

        delete_user = self._commcell_object._services[&#39;DELETE_USER&#39;] %(
            self._users[user_name.lower()], new_user_id, new_group_id)
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;DELETE&#39;, delete_user
        )
        error_code, error_message = self._process_add_or_delete_response(flag, response)
        if not error_message:
            error_message = &#39;Failed to delete user. Please check logs for further details.&#39;
        if error_code != 0:
            raise SDKException(&#39;User&#39;, &#39;102&#39;, error_message)
        self._users = self._get_users()

    def _get_users_on_service_commcell(self):
        &#34;&#34;&#34;gets the userspace from service commcell

        Returns:
            list  - consisting of all users assciated with service commcell

                    [&#39;user1&#39;, &#39;user2&#39;]
        Raises:
            SDKException:
                if response is empty

                if response is not success
        &#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;GET_USERSPACE_SERVICE&#39;]
        )

        if flag:
            if response.json() and &#39;users&#39; in response.json():
                users_space_dict = {}
                for user in response.json()[&#39;users&#39;]:
                    users_space_dict[user[&#39;userEntity&#39;][&#39;userName&#39;]] = user
                return users_space_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def service_commcell_users_space(self):
        &#34;&#34;&#34;Returns the user space from service commcell

        list - consists of users space from service commcell
            [&#39;user1&#39;,&#39;user2&#39;]
        &#34;&#34;&#34;
        if self._users_on_service is None:
            self._users_on_service = self._get_users_on_service_commcell()
        return self._users_on_service

    def refresh(self, **kwargs):
        &#34;&#34;&#34;
        Refresh the list of users on this commcell.

            Args:
                **kwargs (dict):
                    mongodb (bool)  -- Flag to fetch users cache from MongoDB (default: False).
                    hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
        &#34;&#34;&#34;
        mongodb = kwargs.get(&#39;mongodb&#39;, False)
        hard = kwargs.get(&#39;hard&#39;, False)

        self._users = self._get_users()
        self._users_on_service = None
        if mongodb:
            self._users_cache = self.get_users_cache(hard=hard)

    @property
    def all_users(self):
        &#34;&#34;&#34;Returns the dict of all the users on the commcell

        dict of all the users on commcell
                   {
                      &#39;user_name_1&#39;: user_id_1
                   }
        &#34;&#34;&#34;
        return self._users

    @property
    def all_users_prop(self)-&gt;list[dict]:
        &#34;&#34;&#34;
        Returns complete GET API response
        &#34;&#34;&#34;
        self._all_users_prop = self._get_users(full_response=True).get(&#39;users&#39;, [])
        return self._all_users_prop


class User(object):
    &#34;&#34;&#34;Class for representing a particular user configured on this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, user_name, user_id=None):
        &#34;&#34;&#34;Initialize the User class object for specified user

            Args:
                commcell_object (object)  --  instance of the Commcell class

                user_name         (str)     --  name of the user

                user_id           (str)     --  id of the user
                    default: None

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._user_name = user_name.lower()

        if user_id is None:
            self._user_id = self._get_user_id(self._user_name)
        else:
            self._user_id = user_id

        self._user = self._commcell_object._services[&#39;USER&#39;] % (self._user_id)
        self._user_status = None
        self._email = None
        self._description = None
        self._associated_external_usergroups = None
        self._associated_usergroups = None
        self._properties = None
        self._tfa_status = None
        self._upn = None
        self._num_devices = None
        self._get_user_properties()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;User class instance for User: &#34;{0}&#34;&#39;
        return representation_string.format(self.user_name)

    def _get_user_id(self, user_name):
        &#34;&#34;&#34;Gets the user id associated with this user

            Args:
                user_name         (str)     --     name of the user whose

            Returns:
                int     -     id associated to the specified user
        &#34;&#34;&#34;
        users = Users(self._commcell_object)
        return users.get(user_name).user_id

    def _get_user_properties(self):
        &#34;&#34;&#34;Gets the properties of this user&#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._user
        )

        if flag:
            if response.json() and &#39;users&#39; in response.json():
                self._properties = response.json()[&#39;users&#39;][0]
                self._security_properties = self._properties.get(&#39;securityAssociations&#39;, {}).get(
                    &#39;associations&#39;, {})
                self._security_associations = SecurityAssociation.fetch_security_association(
                    security_dict=self._security_properties)
                if &#39;enableUser&#39; in self._properties:
                    self._user_status = self._properties[&#39;enableUser&#39;]

                if &#39;email&#39; in self._properties:
                    self._email = self._properties[&#39;email&#39;]

                if &#39;description&#39; in self._properties:
                    self._description = self._properties[&#39;description&#39;]

                if &#39;associatedUserGroups&#39; in self._properties:
                    self._associated_usergroups = self._properties[&#39;associatedUserGroups&#39;]

                if &#39;associatedExternalUserGroups&#39; in self._properties:
                    self._associated_external_usergroups = self._properties[&#39;associatedExternalUserGroups&#39;]

                if &#39;UPN&#39; in self._properties:
                    self._upn = self._properties.get(&#39;UPN&#39;)

                if &#39;numDevices&#39; in self._properties:
                    self._num_devices = self._properties.get(&#39;numDevices&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update_user_props(self, properties_dict, **kwargs):
        &#34;&#34;&#34;Updates the properties of this user

            Args:
                properties_dict (dict)  --  user property dict which is to be updated
                    e.g.: {
                            &#34;description&#34;: &#34;My description&#34;
                        }
                ** kwargs(dict)         --  Key value pairs for supported arguments
                Supported arguments values:
                    new_username (str)  -- New login name for the user
            Returns:
                User Properties update dict
            Raises:
                SDKException:
                    If invalid type arguments are passed
                    Response was not success.
                    Response was empty.
        &#34;&#34;&#34;
        request_json = {
            &#34;users&#34;: [{
                &#34;userEntity&#34;: {
                    &#34;userName&#34;: self.user_name
                }
            }]
        }
        new_username = kwargs.get(&#34;new_username&#34;, None)
        if new_username is not None:
            if not isinstance(new_username, str):
                raise SDKException(&#34;USER&#34;, &#34;101&#34;)
            request_json[&#34;users&#34;][0][&#34;userEntity&#34;][&#34;userName&#34;] = new_username
        request_json[&#39;users&#39;][0].update(properties_dict)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._user, request_json
        )

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json[&#39;errorString&#39;]
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def _update_usergroup_request(self, request_type, usergroups_list=None):
        &#34;&#34;&#34;Updates the usergroups this user is associated to

            Args:
                usergroups_list     (list)     --     list of usergroups to be updated

                request_type         (str)     --     type of request to be done

            Raises:
                SDKException:

                    if failed to update usergroups

                    if usergroup is not list

                    if usergroup doesn&#39;t exixt on this commcell

        &#34;&#34;&#34;
        update_usergroup_request = {
            &#34;NONE&#34;: 0,
            &#34;OVERWRITE&#34;: 1,
            &#34;UPDATE&#34;: 2,
            &#34;DELETE&#34;: 3,
        }

        if not isinstance(usergroups_list, list):
            raise SDKException(&#39;USER&#39;, &#39;101&#39;)

        for usergroup in usergroups_list:
            if not self._commcell_object.user_groups.has_user_group(usergroup):
                raise SDKException(
                    &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t &#34;
                    &#34;exists on this commcell&#34;.format(usergroup)
                )

        associated_usergroups = []
        if usergroups_list:
            for usergroup in usergroups_list:
                temp = {
                    &#34;userGroupName&#34;: usergroup
                }
                associated_usergroups.append(temp)

        update_usergroup_dict = {
            &#34;associatedUserGroupsOperationType&#34;: update_usergroup_request[
                request_type.upper()],
            &#34;associatedUserGroups&#34;: associated_usergroups
        }

        self._update_user_props(update_usergroup_dict)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the User display name&#34;&#34;&#34;
        return self._properties[&#39;userEntity&#39;][&#39;userName&#39;]

    @property
    def full_name(self):
        &#34;&#34;&#34;Returns the full name of this commcell user&#34;&#34;&#34;
        return self._properties.get(&#39;fullName&#39;,&#39;&#39;)

    @property
    def user_name(self):
        &#34;&#34;&#34;Returns the user name of this commcell user&#34;&#34;&#34;
        return self._user_name

    @property
    def user_id(self):
        &#34;&#34;&#34;Returns the user id of this commcell user&#34;&#34;&#34;
        return self._user_id

    @property
    def description(self):
        &#34;&#34;&#34;Returns the description associated with this commcell user&#34;&#34;&#34;
        return self._description

    @property
    def email(self):
        &#34;&#34;&#34;Returns the email associated with this commcell user&#34;&#34;&#34;
        return self._email

    @property
    def upn(self) -&gt; str:
        &#34;&#34;&#34;
        Returns user principal name of the user

        Returns:
            str -- upn of the user
        &#34;&#34;&#34;
        return self._upn

    @property
    def number_of_laptops(self) -&gt; int:
        &#34;&#34;&#34;
        Returns number of devices for the user

        Returns:
            int --  number of devices
        &#34;&#34;&#34;
        return self._num_devices

    @user_name.setter
    def user_name(self, value):
        &#34;&#34;&#34;Sets the new username for this commcell user&#34;&#34;&#34;
        self._update_user_props(&#34;&#34;, new_username=value)

    @email.setter
    def email(self, value):
        &#34;&#34;&#34;&#34;&#34;Sets the description for this commcell user&#34;&#34;&#34;
        props_dict = {
            &#34;email&#34;: value
        }
        self._update_user_props(props_dict)

    @description.setter
    def description(self, value):
        &#34;&#34;&#34;Sets the description for this commcell user&#34;&#34;&#34;
        props_dict = {
            &#34;description&#34;: value
        }
        self._update_user_props(props_dict)

    @property
    def associated_usergroups(self):
        &#34;&#34;&#34;Returns the list of associated usergroups&#34;&#34;&#34;
        usergroups = []
        if self._associated_usergroups is not None:
            for usergroup in self._associated_usergroups:
                usergroups.append(usergroup[&#39;userGroupName&#39;])
        return usergroups

    @property
    def associated_external_usergroups(self):
        &#34;&#34;&#34;Returns the list of associated external usergroups&#34;&#34;&#34;
        usergroups = []
        if self._associated_external_usergroups is not None:
            for usergroup in self._associated_external_usergroups:
                usergroups.append(usergroup[&#39;externalGroupName&#39;])
        return usergroups

    @property
    def user_security_associations(self):
        &#34;&#34;&#34;Returns security associations from properties of the User.&#34;&#34;&#34;
        return self._security_associations

    @property
    def status(self):
        &#34;&#34;&#34;Returns the status of this commcell user&#34;&#34;&#34;
        return self._user_status

    @status.setter
    def status(self, value):
        &#34;&#34;&#34;Sets the status for this commcell user&#34;&#34;&#34;
        request_json = {
            &#34;users&#34;:[{
                &#34;enableUser&#34;: value
            }]
        }
        usergroup_request = self._commcell_object._services[&#39;USER&#39;]%(self._user_id)
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, usergroup_request, request_json
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json[&#39;errorString&#39;]
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def user_guid(self):
        &#34;&#34;&#34;
        returns user guid
        &#34;&#34;&#34;
        return self._properties.get(&#39;userEntity&#39;, {}).get(&#39;userGUID&#39;)

    @property
    def age_password_days(self):
        &#34;&#34;&#34;
        returns age password days
        &#34;&#34;&#34;
        return self._properties.get(&#39;agePasswordDays&#39;)

    @property
    def user_company_name(self):
        &#34;&#34;&#34;
        returns user associated company name
        &#34;&#34;&#34;
        return self._properties.get(&#39;companyName&#39;, &#39;&#39;).lower()

    @age_password_days.setter
    def age_password_days(self, days):
        &#34;&#34;&#34;
        sets the age password days

        Args:
            days    (int) -- number of days password needs to be required
        &#34;&#34;&#34;
        if isinstance(days, int):
            props_dict = {
                &#34;agePasswordDays&#34;: days
            }
            self._update_user_props(props_dict)
        else:
            raise SDKException(&#39;User&#39;, &#39;101&#39;)

    def update_user_password(self, new_password, logged_in_user_password):
        &#34;&#34;&#34;updates new passwords of user

            Args:
                new_password            (str)   --  new password for user

                logged_in_user_password (str)   --  password of logged-in user(User who is changing
                                                    the password) for validation.
        &#34;&#34;&#34;
        password = b64encode(new_password.encode()).decode()
        validation_password = b64encode(logged_in_user_password.encode()).decode()
        props_dict = {
            &#34;password&#34;: password,
            &#34;validationParameters&#34;:{
                &#34;password&#34;: validation_password,
                &#34;passwordOperationType&#34;: 2
            }
        }
        self._update_user_props(props_dict)

    def add_usergroups(self, usergroups_list):
        &#34;&#34;&#34;UPDATE the specified usergroups to this commcell user

            Args:
                usergroups_list     (list)  --     list of usergroups to be added
        &#34;&#34;&#34;
        self._update_usergroup_request(&#39;UPDATE&#39;, usergroups_list)

    def remove_usergroups(self, usergroups_list):
        &#34;&#34;&#34;DELETE the specified usergroups to this commcell user

            Args:
                usergroups_list     (list)  --     list of usergroups to be deleted
        &#34;&#34;&#34;
        self._update_usergroup_request(&#39;DELETE&#39;, usergroups_list)

    def overwrite_usergroups(self, usergroups_list):
        &#34;&#34;&#34;OVERWRITE the specified usergroups to this commcell user

            Args:
                usergroups_list     (list)  --     list of usergroups to be overwritten

        &#34;&#34;&#34;
        self._update_usergroup_request(&#39;OVERWRITE&#39;, usergroups_list)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the User.&#34;&#34;&#34;
        self._get_user_properties()

    def update_security_associations(self, entity_dictionary, request_type):
        &#34;&#34;&#34;handles three way associations (role-user-entities)

            Args:
                entity_dictionary   --      combination of entity_type, entity names
                                            and role
                e.g.: security_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    }

                entity_type         --      key for the entity present in dictionary
                                            on which user will have access

                entity_name         --      Value of the key

                role                --      key for role name you specify

                e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}

                Entity Types are:   clientName, mediaAgentName, libraryName, userName,
                                    userGroupName, storagePolicyName, clientGroupName,
                                    schedulePolicyName, locationName, providerDomainName,
                                    alertName, workflowName, policyName, roleName

                entity_name:        client name for entity_type &#39;clientName&#39;
                                    Media agent name for entitytype &#39;mediaAgentName&#39;
                                    similar for other entity_types

                request_type        --      decides whether to ADD, DELETE or
                                            OVERWRITE user security association.

            Raises:
                SDKException:

                    if response is not success
        &#34;&#34;&#34;
        update_user_request = {
            &#34;NONE&#34;: 0,
            &#34;OVERWRITE&#34;: 1,
            &#34;UPDATE&#34;: 2,
            &#34;DELETE&#34;: 3,
        }

        sec_request = {}
        if entity_dictionary:
            sec_request = SecurityAssociation._security_association_json(
                entity_dictionary=entity_dictionary)

        request_json = {
            &#34;securityAssociations&#34;:{
                &#34;associationsOperationType&#34;:update_user_request[request_type.upper()],
                &#34;associations&#34;:sec_request
                }
        }
        self._update_user_props(request_json)

    def request_otp(self):
        &#34;&#34;&#34;fetches OTP for user
        Returns:
            OTP generated for user
        Raises:
                Exception:
                    if response is not successful
        &#34;&#34;&#34;

        if self._commcell_object.users.has_user(self.user_name):
            get_otp = self._commcell_object._services[&#39;OTP&#39;] % (self.user_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, get_otp
        )
        if flag:
            if response.json():
                if &#39;value&#39; in response.json():
                    return response.json()[&#39;value&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_tfa_status(self):
        &#34;&#34;&#34;
        Gets the status of two factor authentication for this user
        &#34;&#34;&#34;
        url = self._commcell_object._services[&#39;TFA_STATUS_OF_USER&#39;] % self._user_name
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, url=url
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json().get(&#39;errorCode&#39;) != 0:
                    raise SDKException(&#39;User&#39;,
                                       &#39;102&#39;,
                                       &#34;Failed to get two factor authentication &#34;
                                       &#34;status. error={0}&#34;.format(response.json().get(&#39;errorMessage&#39;)))
            if response.json() and &#39;twoFactorInfo&#39; in response.json():
                info = response.json().get(&#39;twoFactorInfo&#39;)
                self._tfa_status = info.get(&#39;isTwoFactorAuthenticationEnabled&#39;, False)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def is_tfa_enabled(self):
        &#34;&#34;&#34;
        Returns the status of two factor authentication for this user

        bool    --  tfa status
        &#34;&#34;&#34;
        self._get_tfa_status()
        return self._tfa_status

    @property
    def get_account_lock_info(self):
        &#34;&#34;&#34;
        Returns user account lock status
        dict     --  account lock info
        example:
            {
                &#34;isAccountLocked&#34; : True,
                &#34;lockStartTime&#34; : **********,
                &#34;lockEndTime&#34; : **********
            }
        &#34;&#34;&#34;
        lock_info = dict()
        lock_info[&#39;isAccountLocked&#39;] = self._properties.get(&#39;isAccountLocked&#39;, False)
        lock_info[&#39;lockStartTime&#39;] = self._properties.get(&#39;lockStartTime&#39;, 0)
        lock_info[&#39;lockEndTime&#39;] = self._properties.get(&#39;lockEndTime&#39;, 0)
        return lock_info

    def unlock(self):
        &#34;&#34;&#34;
        Unlocks user account.
        Returns:
            status      (str)   --      unlock operation status
                Example:-
                &#34;Unlock successful for user account&#34;
                &#34;Logged in user cannot unlock their own account&#34;
                &#34;Unlock failed for user account&#34;
                &#34;User account is not locked&#34;
                &#34;Logged in user does not have rights to unlock this user account&#34;
            statusCode
        Raises:
            SDKException:
                if response is empty
                if response is not success
        &#34;&#34;&#34;
        payload = {&#34;lockedAccounts&#34;: [{&#34;user&#34;: {&#34;userName&#34;: self._user_name, &#34;userId&#34;: self._user_id}}]}
        service = self._commcell_object._services[&#39;UNLOCK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, service, payload
        )
        if flag:
            if response and response.json() and &#39;lockedAccounts&#39; in response.json():
                return response.json().get(&#39;lockedAccounts&#39;)[0].get(&#39;status&#39;), response.json().get(&#39;lockedAccounts&#39;)[0].get(&#39;statusCode&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def reset_tenant_password(self, token: str, password: str):
        &#34;&#34;&#34;
        Method to reset the password of a tenant admin using a token received in an email.

        Args:
            token (str): The token received in the reset password email.
            password (str): The new password to set for the tenant admin.

        Returns:
            bool: True if the password reset is successful.

        Raises:
            SDKException: If there&#39;s an error with the response or the password reset fails.
                - &#39;User&#39;, &#39;102&#39; if there&#39;s an error code or error string in the response.
                - &#39;Response&#39;, &#39;102&#39; if the response is empty or has an invalid JSON format.
                - &#39;Response&#39;, &#39;101&#39; if the HTTP request fails.
        &#34;&#34;&#34;
        headers = self._commcell_object._headers.copy()
        del headers[&#39;Authtoken&#39;]
        headers[&#39;Reset-Password-token&#39;] = token
        payload = (f&#39;&lt;App_UpdateUserPropertiesRequest&gt;&lt;processinginstructioninfo&gt;&lt;formatFlags skipIdToNameConversion=&#39;
                   f&#39;&#34;1&#34;/&gt;&lt;/processinginstructioninfo&gt;&lt;users removeOtherActiveSessions=&#34;1&#34; password = &#34;{password}&#34;&gt;&#39;
                   f&#39;&lt;userEntity userId=&#34;{str(self.user_id)}&#34; /&gt;&lt;validationParameters passwordOperationType=&#34;1&#34; &#39;
                   f&#39;password=&#34;{password}&#34;/&gt;&lt;/users&gt;&lt;/App_UpdateUserPropertiesRequest&gt;&#39;)
        service = self._commcell_object._services[&#39;RESET_TENANT_PASSWORD&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, service, payload, headers=headers
        )
        if flag:
            if response and response.json():
                response_data = response.json()
                error_code = response_data[&#39;response&#39;][0].get(&#39;errorCode&#39;, -1)
                error_string = response_data[&#39;response&#39;][0].get(&#39;errorString&#39;, &#39;&#39;)

                # Check if there&#39;s an error
                if error_code != 0 or error_string:
                    raise SDKException(&#39;User&#39;, &#39;102&#39;, f&#39;Error Code:&#39;
                                                      f&#39;{error_code}, Error String: {error_string}&#39;)
                return True
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Empty response or invalid JSON format.&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def create_access_token(self,
                            token_name,
                            token_type=None,
                            renewable_until_time=None,
                            token_expiry_time=None,
                            api_endpoints=None):
        &#34;&#34;&#34;
        Creates v4 Access token for the given User
        Args:
            token_name   (str)   -- User friendly name for the Access token

            token_type   (int)   -- Scope for the Access token
                Expected values: 0: All Scope(Default)
                                 1: Microsoft SCIM
                                 2: All Scope
                                 3: Custom
                                 4: 1-Touch

            renewable_until_time (int) -- Unix time stamp for renewable until time applicable
            for Scopes [&#34;All Scope&#34;, &#34;custom&#34;]. It will be ignored for other scopes.

            token_expiry_time (int) -- Unix time stamp for Token expiry time
            applicable for Scopes [&#34;Microsoft SCIM&#34;, &#34;1-Touch&#34;]. It will be ignored for other scopes.

            api_endpoints (list) -- List of Commvault REST API to be considered in the custom scope
                Example-&gt; [&#34;/client&#34;, &#34;/v4/servergroup&#34;]

        Returns:
            dict - Containing Access token details
            Example response:
            For Microsoft SCIM and 1-Touch:
                {&#39;accessTokenId&#39;: &lt;token_ID&gt;, &#39;tokenName&#39;: &#39;sample_token_name&#39;, &#39;accessToken&#39;: &#39;&lt;Access token&gt;&#39;,
                 &#39;userId&#39;: &lt;logged-in-user-ID&gt;, &#39;tokenExpiryTimestamp&#39;: Expiry time stamp}
            For All scope and custom scope:
                {&#39;accessTokenId&#39;: &lt;token_ID&gt;, &#39;renewableUntilTimestamp&#39;: &lt;renewable until time stamp&gt;,
                &#39;tokenName&#39;: &#39;sample_token_name&#39;, &#39;accessToken&#39;: &#39;&lt;Access token&gt;&#39;, &#39;userId&#39;: 1,
                &#39;refreshTokenExpiryTimestamp&#39;: &lt;refresh token expiry time&gt;,
                &#39;tokenExpiryTimestamp&#39;: &lt;token expiry time&gt;, &#39;refreshToken&#39;: &#39;&lt;refresh token&gt;&#39;}
        &#34;&#34;&#34;
        payload = {
                &#34;tokenName&#34;: token_name
        }
        if token_type:
            payload[&#34;tokenType&#34;] = token_type

        if renewable_until_time:
            payload[&#34;renewableUntilTimestamp&#34;] = renewable_until_time

        if token_expiry_time:
            payload[&#34;tokenExpiryTimestamp&#34;] = token_expiry_time

        if api_endpoints:
            payload[&#34;apiEndpoints&#34;] = api_endpoints

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CREATE_ACCESS_TOKEN&#39;], payload
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                if error_code != 0:
                    error_string = response.json()[&#39;error&#39;].get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;104&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()[&#34;tokenInfo&#34;]

    def edit_access_token(self,
                          access_token_id,
                          token_name=None,
                          token_type=None,
                          renewable_until_time=None,
                          token_expiry_time=None,
                          api_endpoints=None):
        &#34;&#34;&#34;
        update v4 Access token for the given token ID
        Args:
            access_token_id (int) -- Access token ID received in the create request

            token_name   (str)   -- User friendly name for the Access token

            token_type   (int)   -- Scope for the Access token
                Expected values: 0: All Scope(Default)
                                 1: Microsoft SCIM
                                 2: All Scope
                                 3: Custom
                                 4: 1-Touch

            renewable_until_time (int) -- Unix time stamp for renewable until time applicable
            for Scopes [&#34;All Scope&#34;, &#34;custom&#34;]. It will be ignored for other scopes.

            token_expiry_time (int) -- Unix time stamp for Token expiry time
            applicable for Scopes [&#34;Microsoft SCIM&#34;, &#34;1-Touch&#34;]. It will be ignored for other scopes.

            api_endpoints (list) -- List of Commvault REST API to be considered in the custom scope
                Example-&gt; [&#34;/client&#34;, &#34;/v4/servergroup&#34;]

        Returns:
            dict - Containing updated Access token details
        &#34;&#34;&#34;

        if not any([token_name, renewable_until_time, token_expiry_time, api_endpoints]):
            if token_type is None:
                raise SDKException(&#39;User&#39;, &#39;105&#39;, &#34;At least one input is required for update token operation&#34;)

        payload = {}
        if token_name:
            payload[&#34;tokenName&#34;] = token_name

        if token_type:
            payload[&#34;tokenType&#34;] = token_type

        if renewable_until_time:
            payload[&#34;renewableUntilTimestamp&#34;] = renewable_until_time

        if token_expiry_time:
            payload[&#34;tokenExpiryTimestamp&#34;] = token_expiry_time

        if api_endpoints:
            payload[&#34;apiEndpoints&#34;] = api_endpoints

        update_token_api_url = self._commcell_object._services[&#39;UPDATE_ACCESS_TOKEN&#39;] % access_token_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;, update_token_api_url, payload)
        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                if error_code != 0:
                    error_string = response.json()[&#39;error&#39;].get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;106&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()[&#34;tokenInfo&#34;]

    def delete_access_token(self, access_token_id):
        &#34;&#34;&#34;
        delete v4 Access token for the given token ID
        Args:
            access_token_id (int) -- Access token ID received in the create request

        Returns:
            dict - Containing error message and error code.
        &#34;&#34;&#34;
        revoke_token_api_url = self._commcell_object._services[&#39;REVOKE_ACCESS_TOKEN&#39;] % access_token_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;DELETE&#39;, revoke_token_api_url)
        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]
                if error_code != 0:
                    error_string = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;107&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()

    def get_access_tokens(self):
        &#34;&#34;&#34;
        get v4 Access token for the current user
        Args:
        Returns:
            dict - Containing List of all Access tokens available for the current user.
        &#34;&#34;&#34;
        get_tokens_api_url = self._commcell_object._services[&#39;GET_ACCESS_TOKENS&#39;] % self.user_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, get_tokens_api_url)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code and error_code != 0:
                    error_string = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;108&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()

    def renew_access_token(self, access_token, refresh_token):
        &#34;&#34;&#34;
        Renew Access token
        Args:
            access_token    (str)  -- Access token received in create request
            refresh_token   (str)  -- refresh token received in create request
        Returns:
            dict - Containing details of renewed Access token.
        &#34;&#34;&#34;
        renew_token_api_url = self._commcell_object._services[&#39;RENEW_TOKEN&#39;]
        payload = {
            &#34;accessToken&#34; : access_token,
            &#34;refreshToken&#34; : refresh_token
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, renew_token_api_url, payload)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code and error_code != 0:
                    error_string = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;109&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.security.user.User"><code class="flex name class">
<span>class <span class="ident">User</span></span>
<span>(</span><span>commcell_object, user_name, user_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing a particular user configured on this commcell</p>
<p>Initialize the User class object for specified user</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<p>user_name
(str)
&ndash;
name of the user</p>
<p>user_id
(str)
&ndash;
id of the user
default: None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L809-L1641" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class User(object):
    &#34;&#34;&#34;Class for representing a particular user configured on this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, user_name, user_id=None):
        &#34;&#34;&#34;Initialize the User class object for specified user

            Args:
                commcell_object (object)  --  instance of the Commcell class

                user_name         (str)     --  name of the user

                user_id           (str)     --  id of the user
                    default: None

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._user_name = user_name.lower()

        if user_id is None:
            self._user_id = self._get_user_id(self._user_name)
        else:
            self._user_id = user_id

        self._user = self._commcell_object._services[&#39;USER&#39;] % (self._user_id)
        self._user_status = None
        self._email = None
        self._description = None
        self._associated_external_usergroups = None
        self._associated_usergroups = None
        self._properties = None
        self._tfa_status = None
        self._upn = None
        self._num_devices = None
        self._get_user_properties()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;User class instance for User: &#34;{0}&#34;&#39;
        return representation_string.format(self.user_name)

    def _get_user_id(self, user_name):
        &#34;&#34;&#34;Gets the user id associated with this user

            Args:
                user_name         (str)     --     name of the user whose

            Returns:
                int     -     id associated to the specified user
        &#34;&#34;&#34;
        users = Users(self._commcell_object)
        return users.get(user_name).user_id

    def _get_user_properties(self):
        &#34;&#34;&#34;Gets the properties of this user&#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._user
        )

        if flag:
            if response.json() and &#39;users&#39; in response.json():
                self._properties = response.json()[&#39;users&#39;][0]
                self._security_properties = self._properties.get(&#39;securityAssociations&#39;, {}).get(
                    &#39;associations&#39;, {})
                self._security_associations = SecurityAssociation.fetch_security_association(
                    security_dict=self._security_properties)
                if &#39;enableUser&#39; in self._properties:
                    self._user_status = self._properties[&#39;enableUser&#39;]

                if &#39;email&#39; in self._properties:
                    self._email = self._properties[&#39;email&#39;]

                if &#39;description&#39; in self._properties:
                    self._description = self._properties[&#39;description&#39;]

                if &#39;associatedUserGroups&#39; in self._properties:
                    self._associated_usergroups = self._properties[&#39;associatedUserGroups&#39;]

                if &#39;associatedExternalUserGroups&#39; in self._properties:
                    self._associated_external_usergroups = self._properties[&#39;associatedExternalUserGroups&#39;]

                if &#39;UPN&#39; in self._properties:
                    self._upn = self._properties.get(&#39;UPN&#39;)

                if &#39;numDevices&#39; in self._properties:
                    self._num_devices = self._properties.get(&#39;numDevices&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update_user_props(self, properties_dict, **kwargs):
        &#34;&#34;&#34;Updates the properties of this user

            Args:
                properties_dict (dict)  --  user property dict which is to be updated
                    e.g.: {
                            &#34;description&#34;: &#34;My description&#34;
                        }
                ** kwargs(dict)         --  Key value pairs for supported arguments
                Supported arguments values:
                    new_username (str)  -- New login name for the user
            Returns:
                User Properties update dict
            Raises:
                SDKException:
                    If invalid type arguments are passed
                    Response was not success.
                    Response was empty.
        &#34;&#34;&#34;
        request_json = {
            &#34;users&#34;: [{
                &#34;userEntity&#34;: {
                    &#34;userName&#34;: self.user_name
                }
            }]
        }
        new_username = kwargs.get(&#34;new_username&#34;, None)
        if new_username is not None:
            if not isinstance(new_username, str):
                raise SDKException(&#34;USER&#34;, &#34;101&#34;)
            request_json[&#34;users&#34;][0][&#34;userEntity&#34;][&#34;userName&#34;] = new_username
        request_json[&#39;users&#39;][0].update(properties_dict)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._user, request_json
        )

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json[&#39;errorString&#39;]
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()

    def _update_usergroup_request(self, request_type, usergroups_list=None):
        &#34;&#34;&#34;Updates the usergroups this user is associated to

            Args:
                usergroups_list     (list)     --     list of usergroups to be updated

                request_type         (str)     --     type of request to be done

            Raises:
                SDKException:

                    if failed to update usergroups

                    if usergroup is not list

                    if usergroup doesn&#39;t exixt on this commcell

        &#34;&#34;&#34;
        update_usergroup_request = {
            &#34;NONE&#34;: 0,
            &#34;OVERWRITE&#34;: 1,
            &#34;UPDATE&#34;: 2,
            &#34;DELETE&#34;: 3,
        }

        if not isinstance(usergroups_list, list):
            raise SDKException(&#39;USER&#39;, &#39;101&#39;)

        for usergroup in usergroups_list:
            if not self._commcell_object.user_groups.has_user_group(usergroup):
                raise SDKException(
                    &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t &#34;
                    &#34;exists on this commcell&#34;.format(usergroup)
                )

        associated_usergroups = []
        if usergroups_list:
            for usergroup in usergroups_list:
                temp = {
                    &#34;userGroupName&#34;: usergroup
                }
                associated_usergroups.append(temp)

        update_usergroup_dict = {
            &#34;associatedUserGroupsOperationType&#34;: update_usergroup_request[
                request_type.upper()],
            &#34;associatedUserGroups&#34;: associated_usergroups
        }

        self._update_user_props(update_usergroup_dict)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the User display name&#34;&#34;&#34;
        return self._properties[&#39;userEntity&#39;][&#39;userName&#39;]

    @property
    def full_name(self):
        &#34;&#34;&#34;Returns the full name of this commcell user&#34;&#34;&#34;
        return self._properties.get(&#39;fullName&#39;,&#39;&#39;)

    @property
    def user_name(self):
        &#34;&#34;&#34;Returns the user name of this commcell user&#34;&#34;&#34;
        return self._user_name

    @property
    def user_id(self):
        &#34;&#34;&#34;Returns the user id of this commcell user&#34;&#34;&#34;
        return self._user_id

    @property
    def description(self):
        &#34;&#34;&#34;Returns the description associated with this commcell user&#34;&#34;&#34;
        return self._description

    @property
    def email(self):
        &#34;&#34;&#34;Returns the email associated with this commcell user&#34;&#34;&#34;
        return self._email

    @property
    def upn(self) -&gt; str:
        &#34;&#34;&#34;
        Returns user principal name of the user

        Returns:
            str -- upn of the user
        &#34;&#34;&#34;
        return self._upn

    @property
    def number_of_laptops(self) -&gt; int:
        &#34;&#34;&#34;
        Returns number of devices for the user

        Returns:
            int --  number of devices
        &#34;&#34;&#34;
        return self._num_devices

    @user_name.setter
    def user_name(self, value):
        &#34;&#34;&#34;Sets the new username for this commcell user&#34;&#34;&#34;
        self._update_user_props(&#34;&#34;, new_username=value)

    @email.setter
    def email(self, value):
        &#34;&#34;&#34;&#34;&#34;Sets the description for this commcell user&#34;&#34;&#34;
        props_dict = {
            &#34;email&#34;: value
        }
        self._update_user_props(props_dict)

    @description.setter
    def description(self, value):
        &#34;&#34;&#34;Sets the description for this commcell user&#34;&#34;&#34;
        props_dict = {
            &#34;description&#34;: value
        }
        self._update_user_props(props_dict)

    @property
    def associated_usergroups(self):
        &#34;&#34;&#34;Returns the list of associated usergroups&#34;&#34;&#34;
        usergroups = []
        if self._associated_usergroups is not None:
            for usergroup in self._associated_usergroups:
                usergroups.append(usergroup[&#39;userGroupName&#39;])
        return usergroups

    @property
    def associated_external_usergroups(self):
        &#34;&#34;&#34;Returns the list of associated external usergroups&#34;&#34;&#34;
        usergroups = []
        if self._associated_external_usergroups is not None:
            for usergroup in self._associated_external_usergroups:
                usergroups.append(usergroup[&#39;externalGroupName&#39;])
        return usergroups

    @property
    def user_security_associations(self):
        &#34;&#34;&#34;Returns security associations from properties of the User.&#34;&#34;&#34;
        return self._security_associations

    @property
    def status(self):
        &#34;&#34;&#34;Returns the status of this commcell user&#34;&#34;&#34;
        return self._user_status

    @status.setter
    def status(self, value):
        &#34;&#34;&#34;Sets the status for this commcell user&#34;&#34;&#34;
        request_json = {
            &#34;users&#34;:[{
                &#34;enableUser&#34;: value
            }]
        }
        usergroup_request = self._commcell_object._services[&#39;USER&#39;]%(self._user_id)
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, usergroup_request, request_json
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json[&#39;errorString&#39;]
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def user_guid(self):
        &#34;&#34;&#34;
        returns user guid
        &#34;&#34;&#34;
        return self._properties.get(&#39;userEntity&#39;, {}).get(&#39;userGUID&#39;)

    @property
    def age_password_days(self):
        &#34;&#34;&#34;
        returns age password days
        &#34;&#34;&#34;
        return self._properties.get(&#39;agePasswordDays&#39;)

    @property
    def user_company_name(self):
        &#34;&#34;&#34;
        returns user associated company name
        &#34;&#34;&#34;
        return self._properties.get(&#39;companyName&#39;, &#39;&#39;).lower()

    @age_password_days.setter
    def age_password_days(self, days):
        &#34;&#34;&#34;
        sets the age password days

        Args:
            days    (int) -- number of days password needs to be required
        &#34;&#34;&#34;
        if isinstance(days, int):
            props_dict = {
                &#34;agePasswordDays&#34;: days
            }
            self._update_user_props(props_dict)
        else:
            raise SDKException(&#39;User&#39;, &#39;101&#39;)

    def update_user_password(self, new_password, logged_in_user_password):
        &#34;&#34;&#34;updates new passwords of user

            Args:
                new_password            (str)   --  new password for user

                logged_in_user_password (str)   --  password of logged-in user(User who is changing
                                                    the password) for validation.
        &#34;&#34;&#34;
        password = b64encode(new_password.encode()).decode()
        validation_password = b64encode(logged_in_user_password.encode()).decode()
        props_dict = {
            &#34;password&#34;: password,
            &#34;validationParameters&#34;:{
                &#34;password&#34;: validation_password,
                &#34;passwordOperationType&#34;: 2
            }
        }
        self._update_user_props(props_dict)

    def add_usergroups(self, usergroups_list):
        &#34;&#34;&#34;UPDATE the specified usergroups to this commcell user

            Args:
                usergroups_list     (list)  --     list of usergroups to be added
        &#34;&#34;&#34;
        self._update_usergroup_request(&#39;UPDATE&#39;, usergroups_list)

    def remove_usergroups(self, usergroups_list):
        &#34;&#34;&#34;DELETE the specified usergroups to this commcell user

            Args:
                usergroups_list     (list)  --     list of usergroups to be deleted
        &#34;&#34;&#34;
        self._update_usergroup_request(&#39;DELETE&#39;, usergroups_list)

    def overwrite_usergroups(self, usergroups_list):
        &#34;&#34;&#34;OVERWRITE the specified usergroups to this commcell user

            Args:
                usergroups_list     (list)  --     list of usergroups to be overwritten

        &#34;&#34;&#34;
        self._update_usergroup_request(&#39;OVERWRITE&#39;, usergroups_list)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the User.&#34;&#34;&#34;
        self._get_user_properties()

    def update_security_associations(self, entity_dictionary, request_type):
        &#34;&#34;&#34;handles three way associations (role-user-entities)

            Args:
                entity_dictionary   --      combination of entity_type, entity names
                                            and role
                e.g.: security_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    }

                entity_type         --      key for the entity present in dictionary
                                            on which user will have access

                entity_name         --      Value of the key

                role                --      key for role name you specify

                e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}

                Entity Types are:   clientName, mediaAgentName, libraryName, userName,
                                    userGroupName, storagePolicyName, clientGroupName,
                                    schedulePolicyName, locationName, providerDomainName,
                                    alertName, workflowName, policyName, roleName

                entity_name:        client name for entity_type &#39;clientName&#39;
                                    Media agent name for entitytype &#39;mediaAgentName&#39;
                                    similar for other entity_types

                request_type        --      decides whether to ADD, DELETE or
                                            OVERWRITE user security association.

            Raises:
                SDKException:

                    if response is not success
        &#34;&#34;&#34;
        update_user_request = {
            &#34;NONE&#34;: 0,
            &#34;OVERWRITE&#34;: 1,
            &#34;UPDATE&#34;: 2,
            &#34;DELETE&#34;: 3,
        }

        sec_request = {}
        if entity_dictionary:
            sec_request = SecurityAssociation._security_association_json(
                entity_dictionary=entity_dictionary)

        request_json = {
            &#34;securityAssociations&#34;:{
                &#34;associationsOperationType&#34;:update_user_request[request_type.upper()],
                &#34;associations&#34;:sec_request
                }
        }
        self._update_user_props(request_json)

    def request_otp(self):
        &#34;&#34;&#34;fetches OTP for user
        Returns:
            OTP generated for user
        Raises:
                Exception:
                    if response is not successful
        &#34;&#34;&#34;

        if self._commcell_object.users.has_user(self.user_name):
            get_otp = self._commcell_object._services[&#39;OTP&#39;] % (self.user_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, get_otp
        )
        if flag:
            if response.json():
                if &#39;value&#39; in response.json():
                    return response.json()[&#39;value&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_tfa_status(self):
        &#34;&#34;&#34;
        Gets the status of two factor authentication for this user
        &#34;&#34;&#34;
        url = self._commcell_object._services[&#39;TFA_STATUS_OF_USER&#39;] % self._user_name
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, url=url
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json().get(&#39;errorCode&#39;) != 0:
                    raise SDKException(&#39;User&#39;,
                                       &#39;102&#39;,
                                       &#34;Failed to get two factor authentication &#34;
                                       &#34;status. error={0}&#34;.format(response.json().get(&#39;errorMessage&#39;)))
            if response.json() and &#39;twoFactorInfo&#39; in response.json():
                info = response.json().get(&#39;twoFactorInfo&#39;)
                self._tfa_status = info.get(&#39;isTwoFactorAuthenticationEnabled&#39;, False)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def is_tfa_enabled(self):
        &#34;&#34;&#34;
        Returns the status of two factor authentication for this user

        bool    --  tfa status
        &#34;&#34;&#34;
        self._get_tfa_status()
        return self._tfa_status

    @property
    def get_account_lock_info(self):
        &#34;&#34;&#34;
        Returns user account lock status
        dict     --  account lock info
        example:
            {
                &#34;isAccountLocked&#34; : True,
                &#34;lockStartTime&#34; : **********,
                &#34;lockEndTime&#34; : **********
            }
        &#34;&#34;&#34;
        lock_info = dict()
        lock_info[&#39;isAccountLocked&#39;] = self._properties.get(&#39;isAccountLocked&#39;, False)
        lock_info[&#39;lockStartTime&#39;] = self._properties.get(&#39;lockStartTime&#39;, 0)
        lock_info[&#39;lockEndTime&#39;] = self._properties.get(&#39;lockEndTime&#39;, 0)
        return lock_info

    def unlock(self):
        &#34;&#34;&#34;
        Unlocks user account.
        Returns:
            status      (str)   --      unlock operation status
                Example:-
                &#34;Unlock successful for user account&#34;
                &#34;Logged in user cannot unlock their own account&#34;
                &#34;Unlock failed for user account&#34;
                &#34;User account is not locked&#34;
                &#34;Logged in user does not have rights to unlock this user account&#34;
            statusCode
        Raises:
            SDKException:
                if response is empty
                if response is not success
        &#34;&#34;&#34;
        payload = {&#34;lockedAccounts&#34;: [{&#34;user&#34;: {&#34;userName&#34;: self._user_name, &#34;userId&#34;: self._user_id}}]}
        service = self._commcell_object._services[&#39;UNLOCK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, service, payload
        )
        if flag:
            if response and response.json() and &#39;lockedAccounts&#39; in response.json():
                return response.json().get(&#39;lockedAccounts&#39;)[0].get(&#39;status&#39;), response.json().get(&#39;lockedAccounts&#39;)[0].get(&#39;statusCode&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def reset_tenant_password(self, token: str, password: str):
        &#34;&#34;&#34;
        Method to reset the password of a tenant admin using a token received in an email.

        Args:
            token (str): The token received in the reset password email.
            password (str): The new password to set for the tenant admin.

        Returns:
            bool: True if the password reset is successful.

        Raises:
            SDKException: If there&#39;s an error with the response or the password reset fails.
                - &#39;User&#39;, &#39;102&#39; if there&#39;s an error code or error string in the response.
                - &#39;Response&#39;, &#39;102&#39; if the response is empty or has an invalid JSON format.
                - &#39;Response&#39;, &#39;101&#39; if the HTTP request fails.
        &#34;&#34;&#34;
        headers = self._commcell_object._headers.copy()
        del headers[&#39;Authtoken&#39;]
        headers[&#39;Reset-Password-token&#39;] = token
        payload = (f&#39;&lt;App_UpdateUserPropertiesRequest&gt;&lt;processinginstructioninfo&gt;&lt;formatFlags skipIdToNameConversion=&#39;
                   f&#39;&#34;1&#34;/&gt;&lt;/processinginstructioninfo&gt;&lt;users removeOtherActiveSessions=&#34;1&#34; password = &#34;{password}&#34;&gt;&#39;
                   f&#39;&lt;userEntity userId=&#34;{str(self.user_id)}&#34; /&gt;&lt;validationParameters passwordOperationType=&#34;1&#34; &#39;
                   f&#39;password=&#34;{password}&#34;/&gt;&lt;/users&gt;&lt;/App_UpdateUserPropertiesRequest&gt;&#39;)
        service = self._commcell_object._services[&#39;RESET_TENANT_PASSWORD&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, service, payload, headers=headers
        )
        if flag:
            if response and response.json():
                response_data = response.json()
                error_code = response_data[&#39;response&#39;][0].get(&#39;errorCode&#39;, -1)
                error_string = response_data[&#39;response&#39;][0].get(&#39;errorString&#39;, &#39;&#39;)

                # Check if there&#39;s an error
                if error_code != 0 or error_string:
                    raise SDKException(&#39;User&#39;, &#39;102&#39;, f&#39;Error Code:&#39;
                                                      f&#39;{error_code}, Error String: {error_string}&#39;)
                return True
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Empty response or invalid JSON format.&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def create_access_token(self,
                            token_name,
                            token_type=None,
                            renewable_until_time=None,
                            token_expiry_time=None,
                            api_endpoints=None):
        &#34;&#34;&#34;
        Creates v4 Access token for the given User
        Args:
            token_name   (str)   -- User friendly name for the Access token

            token_type   (int)   -- Scope for the Access token
                Expected values: 0: All Scope(Default)
                                 1: Microsoft SCIM
                                 2: All Scope
                                 3: Custom
                                 4: 1-Touch

            renewable_until_time (int) -- Unix time stamp for renewable until time applicable
            for Scopes [&#34;All Scope&#34;, &#34;custom&#34;]. It will be ignored for other scopes.

            token_expiry_time (int) -- Unix time stamp for Token expiry time
            applicable for Scopes [&#34;Microsoft SCIM&#34;, &#34;1-Touch&#34;]. It will be ignored for other scopes.

            api_endpoints (list) -- List of Commvault REST API to be considered in the custom scope
                Example-&gt; [&#34;/client&#34;, &#34;/v4/servergroup&#34;]

        Returns:
            dict - Containing Access token details
            Example response:
            For Microsoft SCIM and 1-Touch:
                {&#39;accessTokenId&#39;: &lt;token_ID&gt;, &#39;tokenName&#39;: &#39;sample_token_name&#39;, &#39;accessToken&#39;: &#39;&lt;Access token&gt;&#39;,
                 &#39;userId&#39;: &lt;logged-in-user-ID&gt;, &#39;tokenExpiryTimestamp&#39;: Expiry time stamp}
            For All scope and custom scope:
                {&#39;accessTokenId&#39;: &lt;token_ID&gt;, &#39;renewableUntilTimestamp&#39;: &lt;renewable until time stamp&gt;,
                &#39;tokenName&#39;: &#39;sample_token_name&#39;, &#39;accessToken&#39;: &#39;&lt;Access token&gt;&#39;, &#39;userId&#39;: 1,
                &#39;refreshTokenExpiryTimestamp&#39;: &lt;refresh token expiry time&gt;,
                &#39;tokenExpiryTimestamp&#39;: &lt;token expiry time&gt;, &#39;refreshToken&#39;: &#39;&lt;refresh token&gt;&#39;}
        &#34;&#34;&#34;
        payload = {
                &#34;tokenName&#34;: token_name
        }
        if token_type:
            payload[&#34;tokenType&#34;] = token_type

        if renewable_until_time:
            payload[&#34;renewableUntilTimestamp&#34;] = renewable_until_time

        if token_expiry_time:
            payload[&#34;tokenExpiryTimestamp&#34;] = token_expiry_time

        if api_endpoints:
            payload[&#34;apiEndpoints&#34;] = api_endpoints

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CREATE_ACCESS_TOKEN&#39;], payload
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                if error_code != 0:
                    error_string = response.json()[&#39;error&#39;].get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;104&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()[&#34;tokenInfo&#34;]

    def edit_access_token(self,
                          access_token_id,
                          token_name=None,
                          token_type=None,
                          renewable_until_time=None,
                          token_expiry_time=None,
                          api_endpoints=None):
        &#34;&#34;&#34;
        update v4 Access token for the given token ID
        Args:
            access_token_id (int) -- Access token ID received in the create request

            token_name   (str)   -- User friendly name for the Access token

            token_type   (int)   -- Scope for the Access token
                Expected values: 0: All Scope(Default)
                                 1: Microsoft SCIM
                                 2: All Scope
                                 3: Custom
                                 4: 1-Touch

            renewable_until_time (int) -- Unix time stamp for renewable until time applicable
            for Scopes [&#34;All Scope&#34;, &#34;custom&#34;]. It will be ignored for other scopes.

            token_expiry_time (int) -- Unix time stamp for Token expiry time
            applicable for Scopes [&#34;Microsoft SCIM&#34;, &#34;1-Touch&#34;]. It will be ignored for other scopes.

            api_endpoints (list) -- List of Commvault REST API to be considered in the custom scope
                Example-&gt; [&#34;/client&#34;, &#34;/v4/servergroup&#34;]

        Returns:
            dict - Containing updated Access token details
        &#34;&#34;&#34;

        if not any([token_name, renewable_until_time, token_expiry_time, api_endpoints]):
            if token_type is None:
                raise SDKException(&#39;User&#39;, &#39;105&#39;, &#34;At least one input is required for update token operation&#34;)

        payload = {}
        if token_name:
            payload[&#34;tokenName&#34;] = token_name

        if token_type:
            payload[&#34;tokenType&#34;] = token_type

        if renewable_until_time:
            payload[&#34;renewableUntilTimestamp&#34;] = renewable_until_time

        if token_expiry_time:
            payload[&#34;tokenExpiryTimestamp&#34;] = token_expiry_time

        if api_endpoints:
            payload[&#34;apiEndpoints&#34;] = api_endpoints

        update_token_api_url = self._commcell_object._services[&#39;UPDATE_ACCESS_TOKEN&#39;] % access_token_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;, update_token_api_url, payload)
        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                if error_code != 0:
                    error_string = response.json()[&#39;error&#39;].get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;106&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()[&#34;tokenInfo&#34;]

    def delete_access_token(self, access_token_id):
        &#34;&#34;&#34;
        delete v4 Access token for the given token ID
        Args:
            access_token_id (int) -- Access token ID received in the create request

        Returns:
            dict - Containing error message and error code.
        &#34;&#34;&#34;
        revoke_token_api_url = self._commcell_object._services[&#39;REVOKE_ACCESS_TOKEN&#39;] % access_token_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;DELETE&#39;, revoke_token_api_url)
        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]
                if error_code != 0:
                    error_string = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;107&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()

    def get_access_tokens(self):
        &#34;&#34;&#34;
        get v4 Access token for the current user
        Args:
        Returns:
            dict - Containing List of all Access tokens available for the current user.
        &#34;&#34;&#34;
        get_tokens_api_url = self._commcell_object._services[&#39;GET_ACCESS_TOKENS&#39;] % self.user_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, get_tokens_api_url)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code and error_code != 0:
                    error_string = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;108&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()

    def renew_access_token(self, access_token, refresh_token):
        &#34;&#34;&#34;
        Renew Access token
        Args:
            access_token    (str)  -- Access token received in create request
            refresh_token   (str)  -- refresh token received in create request
        Returns:
            dict - Containing details of renewed Access token.
        &#34;&#34;&#34;
        renew_token_api_url = self._commcell_object._services[&#39;RENEW_TOKEN&#39;]
        payload = {
            &#34;accessToken&#34; : access_token,
            &#34;refreshToken&#34; : refresh_token
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, renew_token_api_url, payload)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;)
                if error_code and error_code != 0:
                    error_string = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;User&#39;, &#39;109&#39;, error_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return response.json()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.security.user.User.age_password_days"><code class="name">var <span class="ident">age_password_days</span></code></dt>
<dd>
<div class="desc"><p>returns age password days</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1135-L1140" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def age_password_days(self):
    &#34;&#34;&#34;
    returns age password days
    &#34;&#34;&#34;
    return self._properties.get(&#39;agePasswordDays&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.associated_external_usergroups"><code class="name">var <span class="ident">associated_external_usergroups</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of associated external usergroups</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1083-L1090" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associated_external_usergroups(self):
    &#34;&#34;&#34;Returns the list of associated external usergroups&#34;&#34;&#34;
    usergroups = []
    if self._associated_external_usergroups is not None:
        for usergroup in self._associated_external_usergroups:
            usergroups.append(usergroup[&#39;externalGroupName&#39;])
    return usergroups</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.associated_usergroups"><code class="name">var <span class="ident">associated_usergroups</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of associated usergroups</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1074-L1081" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associated_usergroups(self):
    &#34;&#34;&#34;Returns the list of associated usergroups&#34;&#34;&#34;
    usergroups = []
    if self._associated_usergroups is not None:
        for usergroup in self._associated_usergroups:
            usergroups.append(usergroup[&#39;userGroupName&#39;])
    return usergroups</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.description"><code class="name">var <span class="ident">description</span></code></dt>
<dd>
<div class="desc"><p>Returns the description associated with this commcell user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1023-L1026" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def description(self):
    &#34;&#34;&#34;Returns the description associated with this commcell user&#34;&#34;&#34;
    return self._description</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.email"><code class="name">var <span class="ident">email</span></code></dt>
<dd>
<div class="desc"><p>Returns the email associated with this commcell user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1028-L1031" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def email(self):
    &#34;&#34;&#34;Returns the email associated with this commcell user&#34;&#34;&#34;
    return self._email</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.full_name"><code class="name">var <span class="ident">full_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the full name of this commcell user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1008-L1011" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def full_name(self):
    &#34;&#34;&#34;Returns the full name of this commcell user&#34;&#34;&#34;
    return self._properties.get(&#39;fullName&#39;,&#39;&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.get_account_lock_info"><code class="name">var <span class="ident">get_account_lock_info</span></code></dt>
<dd>
<div class="desc"><p>Returns user account lock status
dict
&ndash;
account lock info
example:
{
"isAccountLocked" : True,
"lockStartTime" : **********,
"lockEndTime" : **********
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1341-L1357" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def get_account_lock_info(self):
    &#34;&#34;&#34;
    Returns user account lock status
    dict     --  account lock info
    example:
        {
            &#34;isAccountLocked&#34; : True,
            &#34;lockStartTime&#34; : **********,
            &#34;lockEndTime&#34; : **********
        }
    &#34;&#34;&#34;
    lock_info = dict()
    lock_info[&#39;isAccountLocked&#39;] = self._properties.get(&#39;isAccountLocked&#39;, False)
    lock_info[&#39;lockStartTime&#39;] = self._properties.get(&#39;lockStartTime&#39;, 0)
    lock_info[&#39;lockEndTime&#39;] = self._properties.get(&#39;lockEndTime&#39;, 0)
    return lock_info</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.is_tfa_enabled"><code class="name">var <span class="ident">is_tfa_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns the status of two factor authentication for this user</p>
<p>bool
&ndash;
tfa status</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1331-L1339" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_tfa_enabled(self):
    &#34;&#34;&#34;
    Returns the status of two factor authentication for this user

    bool    --  tfa status
    &#34;&#34;&#34;
    self._get_tfa_status()
    return self._tfa_status</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Returns the User display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1003-L1006" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Returns the User display name&#34;&#34;&#34;
    return self._properties[&#39;userEntity&#39;][&#39;userName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.number_of_laptops"><code class="name">var <span class="ident">number_of_laptops</span> : int</code></dt>
<dd>
<div class="desc"><p>Returns number of devices for the user</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
number of devices</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1043-L1051" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def number_of_laptops(self) -&gt; int:
    &#34;&#34;&#34;
    Returns number of devices for the user

    Returns:
        int --  number of devices
    &#34;&#34;&#34;
    return self._num_devices</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.status"><code class="name">var <span class="ident">status</span></code></dt>
<dd>
<div class="desc"><p>Returns the status of this commcell user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1097-L1100" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def status(self):
    &#34;&#34;&#34;Returns the status of this commcell user&#34;&#34;&#34;
    return self._user_status</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.upn"><code class="name">var <span class="ident">upn</span> : str</code></dt>
<dd>
<div class="desc"><p>Returns user principal name of the user</p>
<h2 id="returns">Returns</h2>
<p>str &ndash; upn of the user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1033-L1041" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def upn(self) -&gt; str:
    &#34;&#34;&#34;
    Returns user principal name of the user

    Returns:
        str -- upn of the user
    &#34;&#34;&#34;
    return self._upn</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.user_company_name"><code class="name">var <span class="ident">user_company_name</span></code></dt>
<dd>
<div class="desc"><p>returns user associated company name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1142-L1147" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_company_name(self):
    &#34;&#34;&#34;
    returns user associated company name
    &#34;&#34;&#34;
    return self._properties.get(&#39;companyName&#39;, &#39;&#39;).lower()</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.user_guid"><code class="name">var <span class="ident">user_guid</span></code></dt>
<dd>
<div class="desc"><p>returns user guid</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1128-L1133" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_guid(self):
    &#34;&#34;&#34;
    returns user guid
    &#34;&#34;&#34;
    return self._properties.get(&#39;userEntity&#39;, {}).get(&#39;userGUID&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.user_id"><code class="name">var <span class="ident">user_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the user id of this commcell user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1018-L1021" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_id(self):
    &#34;&#34;&#34;Returns the user id of this commcell user&#34;&#34;&#34;
    return self._user_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.user_name"><code class="name">var <span class="ident">user_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the user name of this commcell user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1013-L1016" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_name(self):
    &#34;&#34;&#34;Returns the user name of this commcell user&#34;&#34;&#34;
    return self._user_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.user_security_associations"><code class="name">var <span class="ident">user_security_associations</span></code></dt>
<dd>
<div class="desc"><p>Returns security associations from properties of the User.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1092-L1095" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_security_associations(self):
    &#34;&#34;&#34;Returns security associations from properties of the User.&#34;&#34;&#34;
    return self._security_associations</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.security.user.User.add_usergroups"><code class="name flex">
<span>def <span class="ident">add_usergroups</span></span>(<span>self, usergroups_list)</span>
</code></dt>
<dd>
<div class="desc"><p>UPDATE the specified usergroups to this commcell user</p>
<h2 id="args">Args</h2>
<p>usergroups_list
(list)
&ndash;
list of usergroups to be added</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1185-L1191" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_usergroups(self, usergroups_list):
    &#34;&#34;&#34;UPDATE the specified usergroups to this commcell user

        Args:
            usergroups_list     (list)  --     list of usergroups to be added
    &#34;&#34;&#34;
    self._update_usergroup_request(&#39;UPDATE&#39;, usergroups_list)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.create_access_token"><code class="name flex">
<span>def <span class="ident">create_access_token</span></span>(<span>self, token_name, token_type=None, renewable_until_time=None, token_expiry_time=None, api_endpoints=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates v4 Access token for the given User</p>
<h2 id="args">Args</h2>
<p>token_name
(str)
&ndash; User friendly name for the Access token</p>
<p>token_type
(int)
&ndash; Scope for the Access token
Expected values: 0: All Scope(Default)
1: Microsoft SCIM
2: All Scope
3: Custom
4: 1-Touch</p>
<p>renewable_until_time (int) &ndash; Unix time stamp for renewable until time applicable
for Scopes ["All Scope", "custom"]. It will be ignored for other scopes.</p>
<p>token_expiry_time (int) &ndash; Unix time stamp for Token expiry time
applicable for Scopes ["Microsoft SCIM", "1-Touch"]. It will be ignored for other scopes.</p>
<p>api_endpoints (list) &ndash; List of Commvault REST API to be considered in the custom scope
Example-&gt; ["/client", "/v4/servergroup"]</p>
<h2 id="returns">Returns</h2>
<p>dict - Containing Access token details
Example response:
For Microsoft SCIM and 1-Touch:
{'accessTokenId': <token_ID>, 'tokenName': 'sample_token_name', 'accessToken': '<Access token>',
'userId': <logged-in-user-ID>, 'tokenExpiryTimestamp': Expiry time stamp}
For All scope and custom scope:
{'accessTokenId': <token_ID>, 'renewableUntilTimestamp': <renewable until time stamp>,
'tokenName': 'sample_token_name', 'accessToken': '<Access token>', 'userId': 1,
'refreshTokenExpiryTimestamp': <refresh token expiry time>,
'tokenExpiryTimestamp': <token expiry time>, 'refreshToken': '<refresh token>'}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1435-L1502" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_access_token(self,
                        token_name,
                        token_type=None,
                        renewable_until_time=None,
                        token_expiry_time=None,
                        api_endpoints=None):
    &#34;&#34;&#34;
    Creates v4 Access token for the given User
    Args:
        token_name   (str)   -- User friendly name for the Access token

        token_type   (int)   -- Scope for the Access token
            Expected values: 0: All Scope(Default)
                             1: Microsoft SCIM
                             2: All Scope
                             3: Custom
                             4: 1-Touch

        renewable_until_time (int) -- Unix time stamp for renewable until time applicable
        for Scopes [&#34;All Scope&#34;, &#34;custom&#34;]. It will be ignored for other scopes.

        token_expiry_time (int) -- Unix time stamp for Token expiry time
        applicable for Scopes [&#34;Microsoft SCIM&#34;, &#34;1-Touch&#34;]. It will be ignored for other scopes.

        api_endpoints (list) -- List of Commvault REST API to be considered in the custom scope
            Example-&gt; [&#34;/client&#34;, &#34;/v4/servergroup&#34;]

    Returns:
        dict - Containing Access token details
        Example response:
        For Microsoft SCIM and 1-Touch:
            {&#39;accessTokenId&#39;: &lt;token_ID&gt;, &#39;tokenName&#39;: &#39;sample_token_name&#39;, &#39;accessToken&#39;: &#39;&lt;Access token&gt;&#39;,
             &#39;userId&#39;: &lt;logged-in-user-ID&gt;, &#39;tokenExpiryTimestamp&#39;: Expiry time stamp}
        For All scope and custom scope:
            {&#39;accessTokenId&#39;: &lt;token_ID&gt;, &#39;renewableUntilTimestamp&#39;: &lt;renewable until time stamp&gt;,
            &#39;tokenName&#39;: &#39;sample_token_name&#39;, &#39;accessToken&#39;: &#39;&lt;Access token&gt;&#39;, &#39;userId&#39;: 1,
            &#39;refreshTokenExpiryTimestamp&#39;: &lt;refresh token expiry time&gt;,
            &#39;tokenExpiryTimestamp&#39;: &lt;token expiry time&gt;, &#39;refreshToken&#39;: &#39;&lt;refresh token&gt;&#39;}
    &#34;&#34;&#34;
    payload = {
            &#34;tokenName&#34;: token_name
    }
    if token_type:
        payload[&#34;tokenType&#34;] = token_type

    if renewable_until_time:
        payload[&#34;renewableUntilTimestamp&#34;] = renewable_until_time

    if token_expiry_time:
        payload[&#34;tokenExpiryTimestamp&#34;] = token_expiry_time

    if api_endpoints:
        payload[&#34;apiEndpoints&#34;] = api_endpoints

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._commcell_object._services[&#39;CREATE_ACCESS_TOKEN&#39;], payload
    )
    if flag:
        if response.json():
            error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
            if error_code != 0:
                error_string = response.json()[&#39;error&#39;].get(&#39;errorMessage&#39;)
                raise SDKException(&#39;User&#39;, &#39;104&#39;, error_string)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    return response.json()[&#34;tokenInfo&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.delete_access_token"><code class="name flex">
<span>def <span class="ident">delete_access_token</span></span>(<span>self, access_token_id)</span>
</code></dt>
<dd>
<div class="desc"><p>delete v4 Access token for the given token ID</p>
<h2 id="args">Args</h2>
<p>access_token_id (int) &ndash; Access token ID received in the create request</p>
<h2 id="returns">Returns</h2>
<p>dict - Containing error message and error code.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1572-L1593" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_access_token(self, access_token_id):
    &#34;&#34;&#34;
    delete v4 Access token for the given token ID
    Args:
        access_token_id (int) -- Access token ID received in the create request

    Returns:
        dict - Containing error message and error code.
    &#34;&#34;&#34;
    revoke_token_api_url = self._commcell_object._services[&#39;REVOKE_ACCESS_TOKEN&#39;] % access_token_id
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;DELETE&#39;, revoke_token_api_url)
    if flag:
        if response.json():
            error_code = response.json()[&#39;errorCode&#39;]
            if error_code != 0:
                error_string = response.json().get(&#39;errorMessage&#39;)
                raise SDKException(&#39;User&#39;, &#39;107&#39;, error_string)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    return response.json()</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.edit_access_token"><code class="name flex">
<span>def <span class="ident">edit_access_token</span></span>(<span>self, access_token_id, token_name=None, token_type=None, renewable_until_time=None, token_expiry_time=None, api_endpoints=None)</span>
</code></dt>
<dd>
<div class="desc"><p>update v4 Access token for the given token ID</p>
<h2 id="args">Args</h2>
<p>access_token_id (int) &ndash; Access token ID received in the create request</p>
<p>token_name
(str)
&ndash; User friendly name for the Access token</p>
<p>token_type
(int)
&ndash; Scope for the Access token
Expected values: 0: All Scope(Default)
1: Microsoft SCIM
2: All Scope
3: Custom
4: 1-Touch</p>
<p>renewable_until_time (int) &ndash; Unix time stamp for renewable until time applicable
for Scopes ["All Scope", "custom"]. It will be ignored for other scopes.</p>
<p>token_expiry_time (int) &ndash; Unix time stamp for Token expiry time
applicable for Scopes ["Microsoft SCIM", "1-Touch"]. It will be ignored for other scopes.</p>
<p>api_endpoints (list) &ndash; List of Commvault REST API to be considered in the custom scope
Example-&gt; ["/client", "/v4/servergroup"]</p>
<h2 id="returns">Returns</h2>
<p>dict - Containing updated Access token details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1504-L1570" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def edit_access_token(self,
                      access_token_id,
                      token_name=None,
                      token_type=None,
                      renewable_until_time=None,
                      token_expiry_time=None,
                      api_endpoints=None):
    &#34;&#34;&#34;
    update v4 Access token for the given token ID
    Args:
        access_token_id (int) -- Access token ID received in the create request

        token_name   (str)   -- User friendly name for the Access token

        token_type   (int)   -- Scope for the Access token
            Expected values: 0: All Scope(Default)
                             1: Microsoft SCIM
                             2: All Scope
                             3: Custom
                             4: 1-Touch

        renewable_until_time (int) -- Unix time stamp for renewable until time applicable
        for Scopes [&#34;All Scope&#34;, &#34;custom&#34;]. It will be ignored for other scopes.

        token_expiry_time (int) -- Unix time stamp for Token expiry time
        applicable for Scopes [&#34;Microsoft SCIM&#34;, &#34;1-Touch&#34;]. It will be ignored for other scopes.

        api_endpoints (list) -- List of Commvault REST API to be considered in the custom scope
            Example-&gt; [&#34;/client&#34;, &#34;/v4/servergroup&#34;]

    Returns:
        dict - Containing updated Access token details
    &#34;&#34;&#34;

    if not any([token_name, renewable_until_time, token_expiry_time, api_endpoints]):
        if token_type is None:
            raise SDKException(&#39;User&#39;, &#39;105&#39;, &#34;At least one input is required for update token operation&#34;)

    payload = {}
    if token_name:
        payload[&#34;tokenName&#34;] = token_name

    if token_type:
        payload[&#34;tokenType&#34;] = token_type

    if renewable_until_time:
        payload[&#34;renewableUntilTimestamp&#34;] = renewable_until_time

    if token_expiry_time:
        payload[&#34;tokenExpiryTimestamp&#34;] = token_expiry_time

    if api_endpoints:
        payload[&#34;apiEndpoints&#34;] = api_endpoints

    update_token_api_url = self._commcell_object._services[&#39;UPDATE_ACCESS_TOKEN&#39;] % access_token_id
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;, update_token_api_url, payload)
    if flag:
        if response.json():
            error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
            if error_code != 0:
                error_string = response.json()[&#39;error&#39;].get(&#39;errorMessage&#39;)
                raise SDKException(&#39;User&#39;, &#39;106&#39;, error_string)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    return response.json()[&#34;tokenInfo&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.get_access_tokens"><code class="name flex">
<span>def <span class="ident">get_access_tokens</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>get v4 Access token for the current user
Args:</p>
<h2 id="returns">Returns</h2>
<p>dict - Containing List of all Access tokens available for the current user.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1595-L1614" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_access_tokens(self):
    &#34;&#34;&#34;
    get v4 Access token for the current user
    Args:
    Returns:
        dict - Containing List of all Access tokens available for the current user.
    &#34;&#34;&#34;
    get_tokens_api_url = self._commcell_object._services[&#39;GET_ACCESS_TOKENS&#39;] % self.user_id
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, get_tokens_api_url)
    if flag:
        if response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code and error_code != 0:
                error_string = response.json().get(&#39;errorMessage&#39;)
                raise SDKException(&#39;User&#39;, &#39;108&#39;, error_string)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    return response.json()</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.overwrite_usergroups"><code class="name flex">
<span>def <span class="ident">overwrite_usergroups</span></span>(<span>self, usergroups_list)</span>
</code></dt>
<dd>
<div class="desc"><p>OVERWRITE the specified usergroups to this commcell user</p>
<h2 id="args">Args</h2>
<p>usergroups_list
(list)
&ndash;
list of usergroups to be overwritten</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1201-L1208" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def overwrite_usergroups(self, usergroups_list):
    &#34;&#34;&#34;OVERWRITE the specified usergroups to this commcell user

        Args:
            usergroups_list     (list)  --     list of usergroups to be overwritten

    &#34;&#34;&#34;
    self._update_usergroup_request(&#39;OVERWRITE&#39;, usergroups_list)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the User.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1210-L1212" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the User.&#34;&#34;&#34;
    self._get_user_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.remove_usergroups"><code class="name flex">
<span>def <span class="ident">remove_usergroups</span></span>(<span>self, usergroups_list)</span>
</code></dt>
<dd>
<div class="desc"><p>DELETE the specified usergroups to this commcell user</p>
<h2 id="args">Args</h2>
<p>usergroups_list
(list)
&ndash;
list of usergroups to be deleted</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1193-L1199" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def remove_usergroups(self, usergroups_list):
    &#34;&#34;&#34;DELETE the specified usergroups to this commcell user

        Args:
            usergroups_list     (list)  --     list of usergroups to be deleted
    &#34;&#34;&#34;
    self._update_usergroup_request(&#39;DELETE&#39;, usergroups_list)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.renew_access_token"><code class="name flex">
<span>def <span class="ident">renew_access_token</span></span>(<span>self, access_token, refresh_token)</span>
</code></dt>
<dd>
<div class="desc"><p>Renew Access token</p>
<h2 id="args">Args</h2>
<p>access_token
(str)
&ndash; Access token received in create request
refresh_token
(str)
&ndash; refresh token received in create request</p>
<h2 id="returns">Returns</h2>
<p>dict - Containing details of renewed Access token.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1616-L1641" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def renew_access_token(self, access_token, refresh_token):
    &#34;&#34;&#34;
    Renew Access token
    Args:
        access_token    (str)  -- Access token received in create request
        refresh_token   (str)  -- refresh token received in create request
    Returns:
        dict - Containing details of renewed Access token.
    &#34;&#34;&#34;
    renew_token_api_url = self._commcell_object._services[&#39;RENEW_TOKEN&#39;]
    payload = {
        &#34;accessToken&#34; : access_token,
        &#34;refreshToken&#34; : refresh_token
    }
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, renew_token_api_url, payload)
    if flag:
        if response.json():
            error_code = response.json().get(&#39;errorCode&#39;)
            if error_code and error_code != 0:
                error_string = response.json().get(&#39;errorMessage&#39;)
                raise SDKException(&#39;User&#39;, &#39;109&#39;, error_string)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    return response.json()</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.request_otp"><code class="name flex">
<span>def <span class="ident">request_otp</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>fetches OTP for user</p>
<h2 id="returns">Returns</h2>
<p>OTP generated for user</p>
<h2 id="raises">Raises</h2>
<p>Exception:
if response is not successful</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1281-L1304" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def request_otp(self):
    &#34;&#34;&#34;fetches OTP for user
    Returns:
        OTP generated for user
    Raises:
            Exception:
                if response is not successful
    &#34;&#34;&#34;

    if self._commcell_object.users.has_user(self.user_name):
        get_otp = self._commcell_object._services[&#39;OTP&#39;] % (self.user_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;GET&#39;, get_otp
    )
    if flag:
        if response.json():
            if &#39;value&#39; in response.json():
                return response.json()[&#39;value&#39;]
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.reset_tenant_password"><code class="name flex">
<span>def <span class="ident">reset_tenant_password</span></span>(<span>self, token: str, password: str)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to reset the password of a tenant admin using a token received in an email.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>token</code></strong> :&ensp;<code>str</code></dt>
<dd>The token received in the reset password email.</dd>
<dt><strong><code>password</code></strong> :&ensp;<code>str</code></dt>
<dd>The new password to set for the tenant admin.</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>bool</code></dt>
<dd>True if the password reset is successful.</dd>
</dl>
<h2 id="raises">Raises</h2>
<dl>
<dt><code>SDKException</code></dt>
<dd>If there's an error with the response or the password reset fails.
- 'User', '102' if there's an error code or error string in the response.
- 'Response', '102' if the response is empty or has an invalid JSON format.
- 'Response', '101' if the HTTP request fails.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1390-L1433" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def reset_tenant_password(self, token: str, password: str):
    &#34;&#34;&#34;
    Method to reset the password of a tenant admin using a token received in an email.

    Args:
        token (str): The token received in the reset password email.
        password (str): The new password to set for the tenant admin.

    Returns:
        bool: True if the password reset is successful.

    Raises:
        SDKException: If there&#39;s an error with the response or the password reset fails.
            - &#39;User&#39;, &#39;102&#39; if there&#39;s an error code or error string in the response.
            - &#39;Response&#39;, &#39;102&#39; if the response is empty or has an invalid JSON format.
            - &#39;Response&#39;, &#39;101&#39; if the HTTP request fails.
    &#34;&#34;&#34;
    headers = self._commcell_object._headers.copy()
    del headers[&#39;Authtoken&#39;]
    headers[&#39;Reset-Password-token&#39;] = token
    payload = (f&#39;&lt;App_UpdateUserPropertiesRequest&gt;&lt;processinginstructioninfo&gt;&lt;formatFlags skipIdToNameConversion=&#39;
               f&#39;&#34;1&#34;/&gt;&lt;/processinginstructioninfo&gt;&lt;users removeOtherActiveSessions=&#34;1&#34; password = &#34;{password}&#34;&gt;&#39;
               f&#39;&lt;userEntity userId=&#34;{str(self.user_id)}&#34; /&gt;&lt;validationParameters passwordOperationType=&#34;1&#34; &#39;
               f&#39;password=&#34;{password}&#34;/&gt;&lt;/users&gt;&lt;/App_UpdateUserPropertiesRequest&gt;&#39;)
    service = self._commcell_object._services[&#39;RESET_TENANT_PASSWORD&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, service, payload, headers=headers
    )
    if flag:
        if response and response.json():
            response_data = response.json()
            error_code = response_data[&#39;response&#39;][0].get(&#39;errorCode&#39;, -1)
            error_string = response_data[&#39;response&#39;][0].get(&#39;errorString&#39;, &#39;&#39;)

            # Check if there&#39;s an error
            if error_code != 0 or error_string:
                raise SDKException(&#39;User&#39;, &#39;102&#39;, f&#39;Error Code:&#39;
                                                  f&#39;{error_code}, Error String: {error_string}&#39;)
            return True
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Empty response or invalid JSON format.&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.unlock"><code class="name flex">
<span>def <span class="ident">unlock</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Unlocks user account.</p>
<h2 id="returns">Returns</h2>
<p>status
(str)
&ndash;
unlock operation status
Example:-
"Unlock successful for user account"
"Logged in user cannot unlock their own account"
"Unlock failed for user account"
"User account is not locked"
"Logged in user does not have rights to unlock this user account"
statusCode</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1359-L1388" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def unlock(self):
    &#34;&#34;&#34;
    Unlocks user account.
    Returns:
        status      (str)   --      unlock operation status
            Example:-
            &#34;Unlock successful for user account&#34;
            &#34;Logged in user cannot unlock their own account&#34;
            &#34;Unlock failed for user account&#34;
            &#34;User account is not locked&#34;
            &#34;Logged in user does not have rights to unlock this user account&#34;
        statusCode
    Raises:
        SDKException:
            if response is empty
            if response is not success
    &#34;&#34;&#34;
    payload = {&#34;lockedAccounts&#34;: [{&#34;user&#34;: {&#34;userName&#34;: self._user_name, &#34;userId&#34;: self._user_id}}]}
    service = self._commcell_object._services[&#39;UNLOCK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, service, payload
    )
    if flag:
        if response and response.json() and &#39;lockedAccounts&#39; in response.json():
            return response.json().get(&#39;lockedAccounts&#39;)[0].get(&#39;status&#39;), response.json().get(&#39;lockedAccounts&#39;)[0].get(&#39;statusCode&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.update_security_associations"><code class="name flex">
<span>def <span class="ident">update_security_associations</span></span>(<span>self, entity_dictionary, request_type)</span>
</code></dt>
<dd>
<div class="desc"><p>handles three way associations (role-user-entities)</p>
<h2 id="args">Args</h2>
<p>entity_dictionary
&ndash;
combination of entity_type, entity names
and role
e.g.: security_dict={
'assoc1':
{
'entity_type':['entity_name'],
'entity_type':['entity_name', 'entity_name'],
'role': ['role1']
},
'assoc2':
{
'mediaAgentName': ['networktestcs', 'standbycs'],
'clientName': ['Linux1'],
'role': ['New1']
}
}</p>
<p>entity_type
&ndash;
key for the entity present in dictionary
on which user will have access</p>
<p>entity_name
&ndash;
Value of the key</p>
<p>role
&ndash;
key for role name you specify</p>
<p>e.g.: {"clientName":"Linux1"}</p>
<p>Entity Types are:
clientName, mediaAgentName, libraryName, userName,
userGroupName, storagePolicyName, clientGroupName,
schedulePolicyName, locationName, providerDomainName,
alertName, workflowName, policyName, roleName</p>
<dl>
<dt><strong><code>entity_name</code></strong></dt>
<dd>
<p>client name for entity_type 'clientName'
Media agent name for entitytype 'mediaAgentName'
similar for other entity_types</p>
</dd>
</dl>
<p>request_type
&ndash;
decides whether to ADD, DELETE or
OVERWRITE user security association.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1214-L1279" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_security_associations(self, entity_dictionary, request_type):
    &#34;&#34;&#34;handles three way associations (role-user-entities)

        Args:
            entity_dictionary   --      combination of entity_type, entity names
                                        and role
            e.g.: security_dict={
                            &#39;assoc1&#39;:
                                {
                                    &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                    &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                    &#39;role&#39;: [&#39;role1&#39;]
                                },
                            &#39;assoc2&#39;:
                                {
                                    &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                    &#39;clientName&#39;: [&#39;Linux1&#39;],
                                    &#39;role&#39;: [&#39;New1&#39;]
                                    }
                                }

            entity_type         --      key for the entity present in dictionary
                                        on which user will have access

            entity_name         --      Value of the key

            role                --      key for role name you specify

            e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}

            Entity Types are:   clientName, mediaAgentName, libraryName, userName,
                                userGroupName, storagePolicyName, clientGroupName,
                                schedulePolicyName, locationName, providerDomainName,
                                alertName, workflowName, policyName, roleName

            entity_name:        client name for entity_type &#39;clientName&#39;
                                Media agent name for entitytype &#39;mediaAgentName&#39;
                                similar for other entity_types

            request_type        --      decides whether to ADD, DELETE or
                                        OVERWRITE user security association.

        Raises:
            SDKException:

                if response is not success
    &#34;&#34;&#34;
    update_user_request = {
        &#34;NONE&#34;: 0,
        &#34;OVERWRITE&#34;: 1,
        &#34;UPDATE&#34;: 2,
        &#34;DELETE&#34;: 3,
    }

    sec_request = {}
    if entity_dictionary:
        sec_request = SecurityAssociation._security_association_json(
            entity_dictionary=entity_dictionary)

    request_json = {
        &#34;securityAssociations&#34;:{
            &#34;associationsOperationType&#34;:update_user_request[request_type.upper()],
            &#34;associations&#34;:sec_request
            }
    }
    self._update_user_props(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.User.update_user_password"><code class="name flex">
<span>def <span class="ident">update_user_password</span></span>(<span>self, new_password, logged_in_user_password)</span>
</code></dt>
<dd>
<div class="desc"><p>updates new passwords of user</p>
<h2 id="args">Args</h2>
<p>new_password
(str)
&ndash;
new password for user</p>
<p>logged_in_user_password (str)
&ndash;
password of logged-in user(User who is changing
the password) for validation.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L1165-L1183" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_user_password(self, new_password, logged_in_user_password):
    &#34;&#34;&#34;updates new passwords of user

        Args:
            new_password            (str)   --  new password for user

            logged_in_user_password (str)   --  password of logged-in user(User who is changing
                                                the password) for validation.
    &#34;&#34;&#34;
    password = b64encode(new_password.encode()).decode()
    validation_password = b64encode(logged_in_user_password.encode()).decode()
    props_dict = {
        &#34;password&#34;: password,
        &#34;validationParameters&#34;:{
            &#34;password&#34;: validation_password,
            &#34;passwordOperationType&#34;: 2
        }
    }
    self._update_user_props(props_dict)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.security.user.Users"><code class="flex name class">
<span>class <span class="ident">Users</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for maintaining all the configured users on this commcell</p>
<p>Initializes the users class object for this commcell</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Clients class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L138-L806" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Users(object):
    &#34;&#34;&#34;Class for maintaining all the configured users on this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes the users class object for this commcell

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Clients class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._users = None
        self._users_cache = None
        self._users_on_service = None
        self._all_users_prop = None
        self.filter_query_count = 0
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all users of the commcell.

            Returns:
                str - string of all the users configured on the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Users&#39;)

        for index, user in enumerate(self._users):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, user)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Users class.&#34;&#34;&#34;
        return &#34;Users class instance for Commcell&#34;

    def _get_users(self, full_response: bool = False):
        &#34;&#34;&#34;Returns the list of users configured on this commcell

            Args:
                full_response(bool) --  flag to return complete response

            Returns:
                dict of all the users on this commcell
                    {
                        &#39;user_name_1&#39;: user_id_1
                    }

        &#34;&#34;&#34;
        get_all_user_service = self._commcell_object._services[&#39;USERS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, get_all_user_service
        )

        if flag:
            if response.json() and &#39;users&#39; in response.json():
                if full_response:
                    return response.json()
                users_dict = {}

                for user in response.json()[&#39;users&#39;]:
                    temp_name = user[&#39;userEntity&#39;][&#39;userName&#39;].lower()
                    temp_id = user[&#39;userEntity&#39;][&#39;userId&#39;]
                    users_dict[temp_name] = temp_id

                return users_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_fl_parameters(self, fl: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fl parameters to be passed in the mongodb caching api call

        Args:
            fl    (list)  --   list of columns to be passed in API request

        Returns:
            fl_parameters(str) -- fl parameter string
        &#34;&#34;&#34;
        self.valid_columns = {
            &#39;userName&#39;: &#39;users.userEntity.userName&#39;,
            &#39;userId&#39;: &#39;users.userEntity.userId&#39;,
            &#39;email&#39;: &#39;users.email&#39;,
            &#39;fullName&#39;: &#39;users.fullName&#39;,
            &#39;description&#39;: &#39;users.description&#39;,
            &#39;UPN&#39;: &#39;users.UPN&#39;,
            &#39;enableUser&#39;: &#39;users.enableUser&#39;,
            &#39;isAccountLocked&#39;: &#39;users.isAccountLocked&#39;,
            &#39;numDevices&#39;: &#39;users.numDevices&#39;,
            &#39;company&#39;: &#39;users.userEntity.entityInfo.companyName&#39;,
            &#39;lastLogIntime&#39;: &#39;users.lastLogIntime&#39;,
            &#39;commcell&#39;: &#39;users.userEntity.entityInfo.multiCommcellName&#39;
        }
        default_columns = &#39;users.userEntity&#39;

        if fl:
            if all(col in self.valid_columns for col in fl):
                fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(self.valid_columns[column] for column in fl)}&#34;
            else:
                raise SDKException(&#39;User&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        else:
            fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(column for column in self.valid_columns.values())}&#34;

        return fl_parameters

    def _get_sort_parameters(self, sort: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the sort parameters to be passed in the mongodb caching api call

        Args:
            sort  (list)  --   contains the name of the column on which sorting will be performed and type of sort
                                valid sor type -- 1 for ascending and -1 for descending
                                e.g. sort = [&#39;connectName&#39;,&#39;1&#39;]

        Returns:
            sort_parameters(str) -- sort parameter string
        &#34;&#34;&#34;
        sort_type = str(sort[1])
        col = sort[0]
        if col in self.valid_columns.keys() and sort_type in [&#39;1&#39;, &#39;-1&#39;]:
            sort_parameter = &#39;&amp;sort=&#39; + self.valid_columns[col] + &#39;:&#39; + sort_type
        else:
            raise SDKException(&#39;User&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        return sort_parameter

    def _get_fq_parameters(self, fq: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fq parameters based on the fq list passed
        Args:
             fq     (list) --   contains the columnName, condition and value
                    e.g. fq = [[&#39;UserName&#39;,&#39;contains&#39;, &#39;test&#39;],[&#39;email&#39;,&#39;contains&#39;, &#39;test&#39;]]

        Returns:
            fq_parameters(str) -- fq parameter string
        &#34;&#34;&#34;
        conditions = {&#34;contains&#34;, &#34;notContain&#34;, &#34;eq&#34;, &#34;neq&#34;, &#34;gt&#34;, &#34;lt&#34;}
        params = []

        for column, condition, *value in fq or []:
            if column not in self.valid_columns:
                raise SDKException(&#39;User&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)

            if condition in conditions:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:{condition.lower()}:{value[0]}&#34;)
            elif condition == &#34;isEmpty&#34; and not value:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:in:null,&#34;)
            elif condition.lower() == &#34;between&#34; and value and &#34;-&#34; in value[0]:
                start, end = value[0].split(&#34;-&#34;, 1)
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:gteq:{start}&#34;)
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:lteq:{end}&#34;)
            else:
                raise SDKException(&#39;User&#39;, &#39;102&#39;, &#39;Invalid condition passed&#39;)

        return &#34;&#34;.join(params)

    def get_users_cache(self, hard: bool = False, **kwargs) -&gt; dict:
        &#34;&#34;&#34;
        Gets all the users present in CommcellEntityCache DB.

        Args:
            hard  (bool)        --   Flag to perform hard refresh on users cache.
            **kwargs (dict):
                fl (list)       --   List of columns to return in response (default: None).
                sort (list)     --   Contains the name of the column on which sorting will be performed and type of sort.
                                        Valid sort type: 1 for ascending and -1 for descending
                                        e.g. sort = [&#39;columnName&#39;, &#39;1&#39;] (default: None).
                limit (list)    --   Contains the start and limit parameter value.
                                        Default [&#39;0&#39;, &#39;100&#39;].
                search (str)    --   Contains the string to search in the commcell entity cache (default: None).
                fq (list)       --   Contains the columnName, condition and value.
                                        e.g. fq = [[&#39;UserName&#39;, &#39;contains&#39;, &#39;test&#39;],
                                         [&#39;email&#39;, &#39;contains&#39;, &#39;test&#39;]] (default: None).

        Returns:
            dict: Dictionary of all the properties present in response.
        &#34;&#34;&#34;
        # computing params
        fl_parameters = self._get_fl_parameters(kwargs.get(&#39;fl&#39;, None))
        fq_parameters = self._get_fq_parameters(kwargs.get(&#39;fq&#39;, None))
        limit = kwargs.get(&#39;limit&#39;, None)
        limit_parameters = f&#39;start={limit[0]}&amp;limit={limit[1]}&#39; if limit else &#39;&#39;
        hard_refresh = &#39;&amp;hardRefresh=true&#39; if hard else &#39;&#39;
        sort_parameters = self._get_sort_parameters(kwargs.get(&#39;sort&#39;, None)) if kwargs.get(&#39;sort&#39;, None) else &#39;&#39;

        # Search operation can only be performed on limited columns, so filtering out the columns on which search works
        searchable_columns = [&#34;userName&#34;,&#34;email&#34;,&#34;fullName&#34;,&#34;company&#34;,&#34;description&#34;]
        search_parameter = (f&#39;&amp;search={&#34;,&#34;.join(self.valid_columns[col] for col in searchable_columns)}:contains:&#39;
                            f&#39;{kwargs.get(&#34;search&#34;, None)}&#39;) if kwargs.get(&#39;search&#39;, None) else &#39;&#39;

        params = [
            limit_parameters,
            sort_parameters,
            fl_parameters,
            hard_refresh,
            search_parameter,
            fq_parameters
        ]

        request_url = f&#34;{self._commcell_object._services[&#39;USERS&#39;]}?&#34; + &#34;&#34;.join(params)
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, request_url)

        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        users_cache = {}
        if response.json() and &#39;users&#39; in response.json():
            self.filter_query_count = response.json().get(&#39;filterQueryCount&#39;,0)
            for user in response.json()[&#39;users&#39;]:
                name = user.get(&#39;userEntity&#39;, {}).get(&#39;userName&#39;)
                users_config = {
                    &#39;userName&#39;: name,
                    &#39;userId&#39;: user.get(&#39;userEntity&#39;, {}).get(&#39;userId&#39;),
                    &#39;email&#39;: user.get(&#39;email&#39;),
                    &#39;fullName&#39;: user.get(&#39;fullName&#39;),
                    &#39;description&#39;: user.get(&#39;description&#39;,&#39;&#39;),
                    &#39;UPN&#39;: user.get(&#39;UPN&#39;),
                    &#39;enableUser&#39;: user.get(&#39;enableUser&#39;),
                    &#39;isAccountLocked&#39;: user.get(&#39;isAccountLocked&#39;),
                    &#39;numDevices&#39;: user.get(&#39;numDevices&#39;),
                    &#39;company&#39;: user.get(&#39;userEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;),
                    &#39;lastLogIntime&#39;: user.get(&#39;lastLogIntime&#39;)
                }
                if self._commcell_object.is_global_scope():
                    users_config[&#39;commcell&#39;] = user.get(&#39;userEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;multiCommcellName&#39;)

                    # Handle duplicate names for different commcells
                    unique_name = name
                    i = 1
                    while unique_name in users_cache:
                        existing_user = users_cache[unique_name]
                        if existing_user.get(&#39;commcell&#39;) != users_config.get(&#39;commcell&#39;):
                            unique_name = f&#34;{name}__{i}&#34;
                            i += 1
                        else:
                            break
                    users_cache[unique_name] = users_config
                else:
                    users_cache[name] = users_config

            return users_cache
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def all_users_cache(self) -&gt; dict:
        &#34;&#34;&#34;Returns dict of all the users and their info present in CommcellEntityCache in mongoDB

            dict - consists of all users of the in the CommcellEntityCache
                    {
                         &#34;user1_name&#34;: {
                                &#39;id&#39;: user1_id ,
                                &#39;email&#39;: user1_email,
                                &#39;fullName&#39;: user1_fullName,
                                &#39;description&#39;: user1_description,
                                &#39;UPN&#39;: user1_UPN,
                                &#39;enabled&#39;: user1_enabled_user_flag,
                                &#39;locked&#39;: user1_is_user_locked_flag,
                                &#39;numberOfLaptops&#39;: user1_number_of_devices,
                                &#39;company&#39;: user1_company
                                },
                         &#34;user2_name&#34;: {
                                &#39;id&#39;: user2_id ,
                                &#39;email&#39;: user2_email,
                                &#39;fullName&#39;: user2_fullName,
                                &#39;description&#39;: user2_description,
                                &#39;UPN&#39;: user2_UPN,
                                &#39;enabled&#39;: user2_enabled_user_flag,
                                &#39;locked&#39;: user2_is_user_locked_flag,
                                &#39;numberOfLaptops&#39;: user2_number_of_devices,
                                &#39;company&#39;: user2_company
                                }
                    }
        &#34;&#34;&#34;
        if not self._users_cache:
            self._users_cache = self.get_users_cache()
        return self._users_cache

    def _process_add_or_delete_response(self, flag, response):
        &#34;&#34;&#34;Processes the flag and response received from the server during add delete request

            Args:
                request_object  (object)  --  request objects specifying the details
                                              to request

            Raises:
                SDKException:
                    if response is empty

                    if reponse is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                error_code = -1
                error_message = &#39;&#39;
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    if &#39;errorString&#39; in response_json:
                        error_message = response_json[&#39;errorString&#39;]
                elif &#39;errorCode&#39; in response.json():
                    error_code = response.json()[&#39;errorCode&#39;]
                    if &#39;errorMessage&#39; in response:
                        error_message = response[&#39;errorMessage&#39;]

                return error_code, error_message

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _add_user(self, create_user_request):
        &#34;&#34;&#34;Makes the add user request on the server

            Args:
                create_user_request     (dict)  --  request json to create an user

            Raises:
                SDKException:
                    if failed to add user
        &#34;&#34;&#34;
        add_user = self._commcell_object._services[&#39;USERS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, add_user, create_user_request
        )
        error_code, error_message = self._process_add_or_delete_response(flag, response)

        if not error_message:
            error_message = &#39;Failed to add user. Please check logs for further details.&#39;

        if error_code != 0:
            raise SDKException(&#39;User&#39;, &#39;102&#39;, error_message)

        self._users = self._get_users()

        return response.json()

    def add(self,
            user_name,
            email,
            full_name=None,
            domain=None,
            password=None,
            system_generated_password=False,
            local_usergroups=None,
            entity_dictionary=None):
        &#34;&#34;&#34;Adds a local/external user to this commcell

            Args:
                user_name                     (str)     --  name of the user to be
                                                            created

                full_name                     (str)     --  full name of the user to be
                                                            created

                email                         (str)     --  email of the user to be
                                                            created

                domain                        (str)     --  Needed in case you are adding
                                                            external user

                password                      (str)     --  password of the user to be
                                                            created
                    default: None

                local_usergroups              (list)     --  user can be member of
                                                            these user groups
                                                            Ex:1. [&#34;master&#34;],
                                                               2. [&#34;group1&#34;, &#34;group2&#34;]

                system_generated_password     (bool)    --  if set to true system
                                                            defined password will be used
                                                            default: False

                entity_dictionary   --      combination of entity_type, entity names
                                            and role

                e.g.: security_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    }
                entity_type         --      key for the entity present in dictionary
                                            on which user will have access

                entity_name         --      Value of the key

                 role               --      key for role name you specify

                e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
                entity_type:    clientName, mediaAgentName, libraryName, userName,
                                userGroupName, storagePolicyName, clientGroupName,
                                schedulePolicyName, locationName, providerDomainName,
                                alertName, workflowName, policyName, roleName

                entity_name:    client name for entity_type &#39;clientName&#39;
                                Media agent name for entitytype &#39;mediaAgentName&#39;
                                similar for other entity_typees

            Raises:
                SDKException:
                    if data type of input is invalid

                    if user with specified name already exists

                    if password or system_generated_password are not set

                    if failed to add user to commcell
        &#34;&#34;&#34;
        if domain:
            username = &#34;{0}\\{1}&#34;.format(domain, user_name)
            password = &#34;&#34;
            system_generated_password = False
        else:
            username = user_name
            if not password:
                system_generated_password = True

        if not (isinstance(username, str) and
                isinstance(email, str)):
            raise SDKException(&#39;User&#39;, &#39;101&#39;)

        if self.has_user(username):
            raise SDKException(&#39;User&#39;, &#39;103&#39;, &#39;User: {0}&#39;.format(username))

        if password is not None:
            password = b64encode(password.encode()).decode()
        else:
            password = &#39;&#39;

        if local_usergroups:
            groups_json = [{&#34;userGroupName&#34;: lname} for lname in local_usergroups]
        else:
            groups_json = [{}]

        security_json = {}
        if entity_dictionary:
            security_request = SecurityAssociation._security_association_json(
                entity_dictionary=entity_dictionary)
            security_json = {
                &#34;associationsOperationType&#34;: &#34;ADD&#34;,
                &#34;associations&#34;: security_request
                }

        create_user_request = {
            &#34;users&#34;: [{
                &#34;password&#34;: password,
                &#34;email&#34;: email,
                &#34;fullName&#34;: full_name,
                &#34;systemGeneratePassword&#34;: system_generated_password,
                &#34;userEntity&#34;: {
                    &#34;userName&#34;: username
                },
                &#34;securityAssociations&#34;: security_json,
                &#34;associatedUserGroups&#34;: groups_json
            }]
        }
        response_json = self._add_user(create_user_request)


        created_user_username = response_json.get(&#34;response&#34;, [{}])[0].get(&#34;entity&#34;, {}).get(&#34;userName&#34;)

        return self.get(created_user_username)

    def has_user(self, user_name):
        &#34;&#34;&#34;Checks if any user with specified name exists on this commcell

            Args:
                user_name         (str)     --     name of the user which has to be
                                                   checked if exists

            Raises:
                SDKException:
                    if data type of input is invalid
        &#34;&#34;&#34;
        if not isinstance(user_name, str):
            raise SDKException(&#39;User&#39;, &#39;101&#39;)

        return self._users and user_name.lower() in self._users

    def get(self, user_name):
        &#34;&#34;&#34;Returns the user object for the specified user name

            Args:
                user_name  (str)  --  name of the user for which the object has to be
                                      created

            Raises:
                SDKException:
                    if user doesn&#39;t exist with specified name
        &#34;&#34;&#34;
        if not self.has_user(user_name):
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                    user_name)
            )

        return User(self._commcell_object, user_name, self._users[user_name.lower()])

    def delete(self, user_name, new_user=None, new_usergroup=None):
        &#34;&#34;&#34;Deletes the specified user from the existing commcell users

            Args:
                user_name       (str)   --  name of the user which has to be deleted

                new_user        (str)   --  name of the target user, whom the ownership
                                            of entities should be transferred

                new_usergroup   (str)   --  name of the user group, whom the ownership
                                            of entities should be transferred

                Note: either user or usergroup  should be provided for ownership
                transfer not both.

            Raises:
                SDKException:
                    if user doesn&#39;t exist

                    if new user and new usergroup any of these is passed and these doesn&#39;t
                    exist on commcell

                    if both user and usergroup is passed for ownership transfer

                    if both user and usergroup is not passed for ownership transfer

                    if response is not success

        &#34;&#34;&#34;
        if not self.has_user(user_name):
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                    user_name)
            )
        if new_user and new_usergroup:
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;{0} and {1} both can not be set as owner!! &#34;
                &#34;please send either new_user or new_usergroup&#34;.format(new_user, new_usergroup)
            )
        else:
            if new_user:
                if not self.has_user(new_user):
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                            new_user)
                    )
                new_user_id = self._users[new_user.lower()]
                new_group_id = 0
            else:
                if new_usergroup:
                    if not self._commcell_object.user_groups.has_user_group(new_usergroup):
                        raise SDKException(
                            &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t exists &#34;
                            &#34;on this commcell.&#34;.format(new_usergroup)
                        )
                else:
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;,
                        &#34;Ownership transfer is mondatory!! Please provide new owner information&#34;
                    )
                new_group_id = self._commcell_object.user_groups.get(new_usergroup).user_group_id
                new_user_id = 0

        delete_user = self._commcell_object._services[&#39;DELETE_USER&#39;] %(
            self._users[user_name.lower()], new_user_id, new_group_id)
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;DELETE&#39;, delete_user
        )
        error_code, error_message = self._process_add_or_delete_response(flag, response)
        if not error_message:
            error_message = &#39;Failed to delete user. Please check logs for further details.&#39;
        if error_code != 0:
            raise SDKException(&#39;User&#39;, &#39;102&#39;, error_message)
        self._users = self._get_users()

    def _get_users_on_service_commcell(self):
        &#34;&#34;&#34;gets the userspace from service commcell

        Returns:
            list  - consisting of all users assciated with service commcell

                    [&#39;user1&#39;, &#39;user2&#39;]
        Raises:
            SDKException:
                if response is empty

                if response is not success
        &#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;GET_USERSPACE_SERVICE&#39;]
        )

        if flag:
            if response.json() and &#39;users&#39; in response.json():
                users_space_dict = {}
                for user in response.json()[&#39;users&#39;]:
                    users_space_dict[user[&#39;userEntity&#39;][&#39;userName&#39;]] = user
                return users_space_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def service_commcell_users_space(self):
        &#34;&#34;&#34;Returns the user space from service commcell

        list - consists of users space from service commcell
            [&#39;user1&#39;,&#39;user2&#39;]
        &#34;&#34;&#34;
        if self._users_on_service is None:
            self._users_on_service = self._get_users_on_service_commcell()
        return self._users_on_service

    def refresh(self, **kwargs):
        &#34;&#34;&#34;
        Refresh the list of users on this commcell.

            Args:
                **kwargs (dict):
                    mongodb (bool)  -- Flag to fetch users cache from MongoDB (default: False).
                    hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
        &#34;&#34;&#34;
        mongodb = kwargs.get(&#39;mongodb&#39;, False)
        hard = kwargs.get(&#39;hard&#39;, False)

        self._users = self._get_users()
        self._users_on_service = None
        if mongodb:
            self._users_cache = self.get_users_cache(hard=hard)

    @property
    def all_users(self):
        &#34;&#34;&#34;Returns the dict of all the users on the commcell

        dict of all the users on commcell
                   {
                      &#39;user_name_1&#39;: user_id_1
                   }
        &#34;&#34;&#34;
        return self._users

    @property
    def all_users_prop(self)-&gt;list[dict]:
        &#34;&#34;&#34;
        Returns complete GET API response
        &#34;&#34;&#34;
        self._all_users_prop = self._get_users(full_response=True).get(&#39;users&#39;, [])
        return self._all_users_prop</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.security.user.Users.all_users"><code class="name">var <span class="ident">all_users</span></code></dt>
<dd>
<div class="desc"><p>Returns the dict of all the users on the commcell</p>
<p>dict of all the users on commcell
{
'user_name_1': user_id_1
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L789-L798" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_users(self):
    &#34;&#34;&#34;Returns the dict of all the users on the commcell

    dict of all the users on commcell
               {
                  &#39;user_name_1&#39;: user_id_1
               }
    &#34;&#34;&#34;
    return self._users</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.Users.all_users_cache"><code class="name">var <span class="ident">all_users_cache</span> : dict</code></dt>
<dd>
<div class="desc"><p>Returns dict of all the users and their info present in CommcellEntityCache in mongoDB</p>
<p>dict - consists of all users of the in the CommcellEntityCache
{
"user1_name": {
'id': user1_id ,
'email': user1_email,
'fullName': user1_fullName,
'description': user1_description,
'UPN': user1_UPN,
'enabled': user1_enabled_user_flag,
'locked': user1_is_user_locked_flag,
'numberOfLaptops': user1_number_of_devices,
'company': user1_company
},
"user2_name": {
'id': user2_id ,
'email': user2_email,
'fullName': user2_fullName,
'description': user2_description,
'UPN': user2_UPN,
'enabled': user2_enabled_user_flag,
'locked': user2_is_user_locked_flag,
'numberOfLaptops': user2_number_of_devices,
'company': user2_company
}
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L388-L420" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_users_cache(self) -&gt; dict:
    &#34;&#34;&#34;Returns dict of all the users and their info present in CommcellEntityCache in mongoDB

        dict - consists of all users of the in the CommcellEntityCache
                {
                     &#34;user1_name&#34;: {
                            &#39;id&#39;: user1_id ,
                            &#39;email&#39;: user1_email,
                            &#39;fullName&#39;: user1_fullName,
                            &#39;description&#39;: user1_description,
                            &#39;UPN&#39;: user1_UPN,
                            &#39;enabled&#39;: user1_enabled_user_flag,
                            &#39;locked&#39;: user1_is_user_locked_flag,
                            &#39;numberOfLaptops&#39;: user1_number_of_devices,
                            &#39;company&#39;: user1_company
                            },
                     &#34;user2_name&#34;: {
                            &#39;id&#39;: user2_id ,
                            &#39;email&#39;: user2_email,
                            &#39;fullName&#39;: user2_fullName,
                            &#39;description&#39;: user2_description,
                            &#39;UPN&#39;: user2_UPN,
                            &#39;enabled&#39;: user2_enabled_user_flag,
                            &#39;locked&#39;: user2_is_user_locked_flag,
                            &#39;numberOfLaptops&#39;: user2_number_of_devices,
                            &#39;company&#39;: user2_company
                            }
                }
    &#34;&#34;&#34;
    if not self._users_cache:
        self._users_cache = self.get_users_cache()
    return self._users_cache</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.Users.all_users_prop"><code class="name">var <span class="ident">all_users_prop</span> : list[dict]</code></dt>
<dd>
<div class="desc"><p>Returns complete GET API response</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L800-L806" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_users_prop(self)-&gt;list[dict]:
    &#34;&#34;&#34;
    Returns complete GET API response
    &#34;&#34;&#34;
    self._all_users_prop = self._get_users(full_response=True).get(&#39;users&#39;, [])
    return self._all_users_prop</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.Users.service_commcell_users_space"><code class="name">var <span class="ident">service_commcell_users_space</span></code></dt>
<dd>
<div class="desc"><p>Returns the user space from service commcell</p>
<p>list - consists of users space from service commcell
['user1','user2']</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L761-L770" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def service_commcell_users_space(self):
    &#34;&#34;&#34;Returns the user space from service commcell

    list - consists of users space from service commcell
        [&#39;user1&#39;,&#39;user2&#39;]
    &#34;&#34;&#34;
    if self._users_on_service is None:
        self._users_on_service = self._get_users_on_service_commcell()
    return self._users_on_service</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.security.user.Users.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, user_name, email, full_name=None, domain=None, password=None, system_generated_password=False, local_usergroups=None, entity_dictionary=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a local/external user to this commcell</p>
<h2 id="args">Args</h2>
<p>user_name
(str)
&ndash;
name of the user to be
created</p>
<p>full_name
(str)
&ndash;
full name of the user to be
created</p>
<p>email
(str)
&ndash;
email of the user to be
created</p>
<p>domain
(str)
&ndash;
Needed in case you are adding
external user</p>
<p>password
(str)
&ndash;
password of the user to be
created
default: None</p>
<p>local_usergroups
(list)
&ndash;
user can be member of
these user groups
Ex:1. ["master"],
2. ["group1", "group2"]</p>
<p>system_generated_password
(bool)
&ndash;
if set to true system
defined password will be used
default: False</p>
<p>entity_dictionary
&ndash;
combination of entity_type, entity names
and role</p>
<p>e.g.: security_dict={
'assoc1':
{
'entity_type':['entity_name'],
'entity_type':['entity_name', 'entity_name'],
'role': ['role1']
},
'assoc2':
{
'mediaAgentName': ['networktestcs', 'standbycs'],
'clientName': ['Linux1'],
'role': ['New1']
}
}
entity_type
&ndash;
key for the entity present in dictionary
on which user will have access</p>
<p>entity_name
&ndash;
Value of the key</p>
<p>role
&ndash;
key for role name you specify</p>
<dl>
<dt _clientName_:_Linux1_="&quot;clientName&quot;:&quot;Linux1&quot;">e.g.:</dt>
<dt><strong><code>entity_type</code></strong></dt>
<dd>clientName, mediaAgentName, libraryName, userName,
userGroupName, storagePolicyName, clientGroupName,
schedulePolicyName, locationName, providerDomainName,
alertName, workflowName, policyName, roleName</dd>
<dt><strong><code>entity_name</code></strong></dt>
<dd>client name for entity_type 'clientName'
Media agent name for entitytype 'mediaAgentName'
similar for other entity_typees</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if data type of input is invalid</p>
<pre><code>if user with specified name already exists

if password or system_generated_password are not set

if failed to add user to commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L484-L618" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self,
        user_name,
        email,
        full_name=None,
        domain=None,
        password=None,
        system_generated_password=False,
        local_usergroups=None,
        entity_dictionary=None):
    &#34;&#34;&#34;Adds a local/external user to this commcell

        Args:
            user_name                     (str)     --  name of the user to be
                                                        created

            full_name                     (str)     --  full name of the user to be
                                                        created

            email                         (str)     --  email of the user to be
                                                        created

            domain                        (str)     --  Needed in case you are adding
                                                        external user

            password                      (str)     --  password of the user to be
                                                        created
                default: None

            local_usergroups              (list)     --  user can be member of
                                                        these user groups
                                                        Ex:1. [&#34;master&#34;],
                                                           2. [&#34;group1&#34;, &#34;group2&#34;]

            system_generated_password     (bool)    --  if set to true system
                                                        defined password will be used
                                                        default: False

            entity_dictionary   --      combination of entity_type, entity names
                                        and role

            e.g.: security_dict={
                            &#39;assoc1&#39;:
                                {
                                    &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                    &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                    &#39;role&#39;: [&#39;role1&#39;]
                                },
                            &#39;assoc2&#39;:
                                {
                                    &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                    &#39;clientName&#39;: [&#39;Linux1&#39;],
                                    &#39;role&#39;: [&#39;New1&#39;]
                                    }
                                }
            entity_type         --      key for the entity present in dictionary
                                        on which user will have access

            entity_name         --      Value of the key

             role               --      key for role name you specify

            e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
            entity_type:    clientName, mediaAgentName, libraryName, userName,
                            userGroupName, storagePolicyName, clientGroupName,
                            schedulePolicyName, locationName, providerDomainName,
                            alertName, workflowName, policyName, roleName

            entity_name:    client name for entity_type &#39;clientName&#39;
                            Media agent name for entitytype &#39;mediaAgentName&#39;
                            similar for other entity_typees

        Raises:
            SDKException:
                if data type of input is invalid

                if user with specified name already exists

                if password or system_generated_password are not set

                if failed to add user to commcell
    &#34;&#34;&#34;
    if domain:
        username = &#34;{0}\\{1}&#34;.format(domain, user_name)
        password = &#34;&#34;
        system_generated_password = False
    else:
        username = user_name
        if not password:
            system_generated_password = True

    if not (isinstance(username, str) and
            isinstance(email, str)):
        raise SDKException(&#39;User&#39;, &#39;101&#39;)

    if self.has_user(username):
        raise SDKException(&#39;User&#39;, &#39;103&#39;, &#39;User: {0}&#39;.format(username))

    if password is not None:
        password = b64encode(password.encode()).decode()
    else:
        password = &#39;&#39;

    if local_usergroups:
        groups_json = [{&#34;userGroupName&#34;: lname} for lname in local_usergroups]
    else:
        groups_json = [{}]

    security_json = {}
    if entity_dictionary:
        security_request = SecurityAssociation._security_association_json(
            entity_dictionary=entity_dictionary)
        security_json = {
            &#34;associationsOperationType&#34;: &#34;ADD&#34;,
            &#34;associations&#34;: security_request
            }

    create_user_request = {
        &#34;users&#34;: [{
            &#34;password&#34;: password,
            &#34;email&#34;: email,
            &#34;fullName&#34;: full_name,
            &#34;systemGeneratePassword&#34;: system_generated_password,
            &#34;userEntity&#34;: {
                &#34;userName&#34;: username
            },
            &#34;securityAssociations&#34;: security_json,
            &#34;associatedUserGroups&#34;: groups_json
        }]
    }
    response_json = self._add_user(create_user_request)


    created_user_username = response_json.get(&#34;response&#34;, [{}])[0].get(&#34;entity&#34;, {}).get(&#34;userName&#34;)

    return self.get(created_user_username)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.Users.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, user_name, new_user=None, new_usergroup=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the specified user from the existing commcell users</p>
<h2 id="args">Args</h2>
<p>user_name
(str)
&ndash;
name of the user which has to be deleted</p>
<p>new_user
(str)
&ndash;
name of the target user, whom the ownership
of entities should be transferred</p>
<p>new_usergroup
(str)
&ndash;
name of the user group, whom the ownership
of entities should be transferred</p>
<dl>
<dt><strong><code>Note</code></strong></dt>
<dd>either user or usergroup
should be provided for ownership</dd>
</dl>
<p>transfer not both.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if user doesn't exist</p>
<pre><code>if new user and new usergroup any of these is passed and these doesn't
exist on commcell

if both user and usergroup is passed for ownership transfer

if both user and usergroup is not passed for ownership transfer

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L655-L728" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, user_name, new_user=None, new_usergroup=None):
    &#34;&#34;&#34;Deletes the specified user from the existing commcell users

        Args:
            user_name       (str)   --  name of the user which has to be deleted

            new_user        (str)   --  name of the target user, whom the ownership
                                        of entities should be transferred

            new_usergroup   (str)   --  name of the user group, whom the ownership
                                        of entities should be transferred

            Note: either user or usergroup  should be provided for ownership
            transfer not both.

        Raises:
            SDKException:
                if user doesn&#39;t exist

                if new user and new usergroup any of these is passed and these doesn&#39;t
                exist on commcell

                if both user and usergroup is passed for ownership transfer

                if both user and usergroup is not passed for ownership transfer

                if response is not success

    &#34;&#34;&#34;
    if not self.has_user(user_name):
        raise SDKException(
            &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                user_name)
        )
    if new_user and new_usergroup:
        raise SDKException(
            &#39;User&#39;, &#39;102&#39;, &#34;{0} and {1} both can not be set as owner!! &#34;
            &#34;please send either new_user or new_usergroup&#34;.format(new_user, new_usergroup)
        )
    else:
        if new_user:
            if not self.has_user(new_user):
                raise SDKException(
                    &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                        new_user)
                )
            new_user_id = self._users[new_user.lower()]
            new_group_id = 0
        else:
            if new_usergroup:
                if not self._commcell_object.user_groups.has_user_group(new_usergroup):
                    raise SDKException(
                        &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t exists &#34;
                        &#34;on this commcell.&#34;.format(new_usergroup)
                    )
            else:
                raise SDKException(
                    &#39;User&#39;, &#39;102&#39;,
                    &#34;Ownership transfer is mondatory!! Please provide new owner information&#34;
                )
            new_group_id = self._commcell_object.user_groups.get(new_usergroup).user_group_id
            new_user_id = 0

    delete_user = self._commcell_object._services[&#39;DELETE_USER&#39;] %(
        self._users[user_name.lower()], new_user_id, new_group_id)
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;DELETE&#39;, delete_user
    )
    error_code, error_message = self._process_add_or_delete_response(flag, response)
    if not error_message:
        error_message = &#39;Failed to delete user. Please check logs for further details.&#39;
    if error_code != 0:
        raise SDKException(&#39;User&#39;, &#39;102&#39;, error_message)
    self._users = self._get_users()</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.Users.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, user_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the user object for the specified user name</p>
<h2 id="args">Args</h2>
<p>user_name
(str)
&ndash;
name of the user for which the object has to be
created</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if user doesn't exist with specified name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L636-L653" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, user_name):
    &#34;&#34;&#34;Returns the user object for the specified user name

        Args:
            user_name  (str)  --  name of the user for which the object has to be
                                  created

        Raises:
            SDKException:
                if user doesn&#39;t exist with specified name
    &#34;&#34;&#34;
    if not self.has_user(user_name):
        raise SDKException(
            &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                user_name)
        )

    return User(self._commcell_object, user_name, self._users[user_name.lower()])</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.Users.get_users_cache"><code class="name flex">
<span>def <span class="ident">get_users_cache</span></span>(<span>self, hard: bool = False, **kwargs) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Gets all the users present in CommcellEntityCache DB.</p>
<h2 id="args">Args</h2>
<p>hard
(bool)
&ndash;
Flag to perform hard refresh on users cache.
**kwargs (dict):
fl (list)
&ndash;
List of columns to return in response (default: None).
sort (list)
&ndash;
Contains the name of the column on which sorting will be performed and type of sort.
Valid sort type: 1 for ascending and -1 for descending
e.g. sort = ['columnName', '1'] (default: None).
limit (list)
&ndash;
Contains the start and limit parameter value.
Default ['0', '100'].
search (str)
&ndash;
Contains the string to search in the commcell entity cache (default: None).
fq (list)
&ndash;
Contains the columnName, condition and value.
e.g. fq = [['UserName', 'contains', 'test'],
['email', 'contains', 'test']] (default: None).</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>dict</code></dt>
<dd>Dictionary of all the properties present in response.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L299-L386" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_users_cache(self, hard: bool = False, **kwargs) -&gt; dict:
    &#34;&#34;&#34;
    Gets all the users present in CommcellEntityCache DB.

    Args:
        hard  (bool)        --   Flag to perform hard refresh on users cache.
        **kwargs (dict):
            fl (list)       --   List of columns to return in response (default: None).
            sort (list)     --   Contains the name of the column on which sorting will be performed and type of sort.
                                    Valid sort type: 1 for ascending and -1 for descending
                                    e.g. sort = [&#39;columnName&#39;, &#39;1&#39;] (default: None).
            limit (list)    --   Contains the start and limit parameter value.
                                    Default [&#39;0&#39;, &#39;100&#39;].
            search (str)    --   Contains the string to search in the commcell entity cache (default: None).
            fq (list)       --   Contains the columnName, condition and value.
                                    e.g. fq = [[&#39;UserName&#39;, &#39;contains&#39;, &#39;test&#39;],
                                     [&#39;email&#39;, &#39;contains&#39;, &#39;test&#39;]] (default: None).

    Returns:
        dict: Dictionary of all the properties present in response.
    &#34;&#34;&#34;
    # computing params
    fl_parameters = self._get_fl_parameters(kwargs.get(&#39;fl&#39;, None))
    fq_parameters = self._get_fq_parameters(kwargs.get(&#39;fq&#39;, None))
    limit = kwargs.get(&#39;limit&#39;, None)
    limit_parameters = f&#39;start={limit[0]}&amp;limit={limit[1]}&#39; if limit else &#39;&#39;
    hard_refresh = &#39;&amp;hardRefresh=true&#39; if hard else &#39;&#39;
    sort_parameters = self._get_sort_parameters(kwargs.get(&#39;sort&#39;, None)) if kwargs.get(&#39;sort&#39;, None) else &#39;&#39;

    # Search operation can only be performed on limited columns, so filtering out the columns on which search works
    searchable_columns = [&#34;userName&#34;,&#34;email&#34;,&#34;fullName&#34;,&#34;company&#34;,&#34;description&#34;]
    search_parameter = (f&#39;&amp;search={&#34;,&#34;.join(self.valid_columns[col] for col in searchable_columns)}:contains:&#39;
                        f&#39;{kwargs.get(&#34;search&#34;, None)}&#39;) if kwargs.get(&#39;search&#39;, None) else &#39;&#39;

    params = [
        limit_parameters,
        sort_parameters,
        fl_parameters,
        hard_refresh,
        search_parameter,
        fq_parameters
    ]

    request_url = f&#34;{self._commcell_object._services[&#39;USERS&#39;]}?&#34; + &#34;&#34;.join(params)
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, request_url)

    if not flag:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    users_cache = {}
    if response.json() and &#39;users&#39; in response.json():
        self.filter_query_count = response.json().get(&#39;filterQueryCount&#39;,0)
        for user in response.json()[&#39;users&#39;]:
            name = user.get(&#39;userEntity&#39;, {}).get(&#39;userName&#39;)
            users_config = {
                &#39;userName&#39;: name,
                &#39;userId&#39;: user.get(&#39;userEntity&#39;, {}).get(&#39;userId&#39;),
                &#39;email&#39;: user.get(&#39;email&#39;),
                &#39;fullName&#39;: user.get(&#39;fullName&#39;),
                &#39;description&#39;: user.get(&#39;description&#39;,&#39;&#39;),
                &#39;UPN&#39;: user.get(&#39;UPN&#39;),
                &#39;enableUser&#39;: user.get(&#39;enableUser&#39;),
                &#39;isAccountLocked&#39;: user.get(&#39;isAccountLocked&#39;),
                &#39;numDevices&#39;: user.get(&#39;numDevices&#39;),
                &#39;company&#39;: user.get(&#39;userEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;),
                &#39;lastLogIntime&#39;: user.get(&#39;lastLogIntime&#39;)
            }
            if self._commcell_object.is_global_scope():
                users_config[&#39;commcell&#39;] = user.get(&#39;userEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;multiCommcellName&#39;)

                # Handle duplicate names for different commcells
                unique_name = name
                i = 1
                while unique_name in users_cache:
                    existing_user = users_cache[unique_name]
                    if existing_user.get(&#39;commcell&#39;) != users_config.get(&#39;commcell&#39;):
                        unique_name = f&#34;{name}__{i}&#34;
                        i += 1
                    else:
                        break
                users_cache[unique_name] = users_config
            else:
                users_cache[name] = users_config

        return users_cache
    else:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.Users.has_user"><code class="name flex">
<span>def <span class="ident">has_user</span></span>(<span>self, user_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if any user with specified name exists on this commcell</p>
<h2 id="args">Args</h2>
<p>user_name
(str)
&ndash;
name of the user which has to be
checked if exists</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if data type of input is invalid</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L620-L634" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_user(self, user_name):
    &#34;&#34;&#34;Checks if any user with specified name exists on this commcell

        Args:
            user_name         (str)     --     name of the user which has to be
                                               checked if exists

        Raises:
            SDKException:
                if data type of input is invalid
    &#34;&#34;&#34;
    if not isinstance(user_name, str):
        raise SDKException(&#39;User&#39;, &#39;101&#39;)

    return self._users and user_name.lower() in self._users</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.user.Users.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the list of users on this commcell.</p>
<pre><code>Args:
    **kwargs (dict):
        mongodb (bool)  -- Flag to fetch users cache from MongoDB (default: False).
        hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/user.py#L772-L787" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self, **kwargs):
    &#34;&#34;&#34;
    Refresh the list of users on this commcell.

        Args:
            **kwargs (dict):
                mongodb (bool)  -- Flag to fetch users cache from MongoDB (default: False).
                hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
    &#34;&#34;&#34;
    mongodb = kwargs.get(&#39;mongodb&#39;, False)
    hard = kwargs.get(&#39;hard&#39;, False)

    self._users = self._get_users()
    self._users_on_service = None
    if mongodb:
        self._users_cache = self.get_users_cache(hard=hard)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.security" href="index.html">cvpysdk.security</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.security.user.User" href="#cvpysdk.security.user.User">User</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.security.user.User.add_usergroups" href="#cvpysdk.security.user.User.add_usergroups">add_usergroups</a></code></li>
<li><code><a title="cvpysdk.security.user.User.age_password_days" href="#cvpysdk.security.user.User.age_password_days">age_password_days</a></code></li>
<li><code><a title="cvpysdk.security.user.User.associated_external_usergroups" href="#cvpysdk.security.user.User.associated_external_usergroups">associated_external_usergroups</a></code></li>
<li><code><a title="cvpysdk.security.user.User.associated_usergroups" href="#cvpysdk.security.user.User.associated_usergroups">associated_usergroups</a></code></li>
<li><code><a title="cvpysdk.security.user.User.create_access_token" href="#cvpysdk.security.user.User.create_access_token">create_access_token</a></code></li>
<li><code><a title="cvpysdk.security.user.User.delete_access_token" href="#cvpysdk.security.user.User.delete_access_token">delete_access_token</a></code></li>
<li><code><a title="cvpysdk.security.user.User.description" href="#cvpysdk.security.user.User.description">description</a></code></li>
<li><code><a title="cvpysdk.security.user.User.edit_access_token" href="#cvpysdk.security.user.User.edit_access_token">edit_access_token</a></code></li>
<li><code><a title="cvpysdk.security.user.User.email" href="#cvpysdk.security.user.User.email">email</a></code></li>
<li><code><a title="cvpysdk.security.user.User.full_name" href="#cvpysdk.security.user.User.full_name">full_name</a></code></li>
<li><code><a title="cvpysdk.security.user.User.get_access_tokens" href="#cvpysdk.security.user.User.get_access_tokens">get_access_tokens</a></code></li>
<li><code><a title="cvpysdk.security.user.User.get_account_lock_info" href="#cvpysdk.security.user.User.get_account_lock_info">get_account_lock_info</a></code></li>
<li><code><a title="cvpysdk.security.user.User.is_tfa_enabled" href="#cvpysdk.security.user.User.is_tfa_enabled">is_tfa_enabled</a></code></li>
<li><code><a title="cvpysdk.security.user.User.name" href="#cvpysdk.security.user.User.name">name</a></code></li>
<li><code><a title="cvpysdk.security.user.User.number_of_laptops" href="#cvpysdk.security.user.User.number_of_laptops">number_of_laptops</a></code></li>
<li><code><a title="cvpysdk.security.user.User.overwrite_usergroups" href="#cvpysdk.security.user.User.overwrite_usergroups">overwrite_usergroups</a></code></li>
<li><code><a title="cvpysdk.security.user.User.refresh" href="#cvpysdk.security.user.User.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.security.user.User.remove_usergroups" href="#cvpysdk.security.user.User.remove_usergroups">remove_usergroups</a></code></li>
<li><code><a title="cvpysdk.security.user.User.renew_access_token" href="#cvpysdk.security.user.User.renew_access_token">renew_access_token</a></code></li>
<li><code><a title="cvpysdk.security.user.User.request_otp" href="#cvpysdk.security.user.User.request_otp">request_otp</a></code></li>
<li><code><a title="cvpysdk.security.user.User.reset_tenant_password" href="#cvpysdk.security.user.User.reset_tenant_password">reset_tenant_password</a></code></li>
<li><code><a title="cvpysdk.security.user.User.status" href="#cvpysdk.security.user.User.status">status</a></code></li>
<li><code><a title="cvpysdk.security.user.User.unlock" href="#cvpysdk.security.user.User.unlock">unlock</a></code></li>
<li><code><a title="cvpysdk.security.user.User.update_security_associations" href="#cvpysdk.security.user.User.update_security_associations">update_security_associations</a></code></li>
<li><code><a title="cvpysdk.security.user.User.update_user_password" href="#cvpysdk.security.user.User.update_user_password">update_user_password</a></code></li>
<li><code><a title="cvpysdk.security.user.User.upn" href="#cvpysdk.security.user.User.upn">upn</a></code></li>
<li><code><a title="cvpysdk.security.user.User.user_company_name" href="#cvpysdk.security.user.User.user_company_name">user_company_name</a></code></li>
<li><code><a title="cvpysdk.security.user.User.user_guid" href="#cvpysdk.security.user.User.user_guid">user_guid</a></code></li>
<li><code><a title="cvpysdk.security.user.User.user_id" href="#cvpysdk.security.user.User.user_id">user_id</a></code></li>
<li><code><a title="cvpysdk.security.user.User.user_name" href="#cvpysdk.security.user.User.user_name">user_name</a></code></li>
<li><code><a title="cvpysdk.security.user.User.user_security_associations" href="#cvpysdk.security.user.User.user_security_associations">user_security_associations</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.security.user.Users" href="#cvpysdk.security.user.Users">Users</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.security.user.Users.add" href="#cvpysdk.security.user.Users.add">add</a></code></li>
<li><code><a title="cvpysdk.security.user.Users.all_users" href="#cvpysdk.security.user.Users.all_users">all_users</a></code></li>
<li><code><a title="cvpysdk.security.user.Users.all_users_cache" href="#cvpysdk.security.user.Users.all_users_cache">all_users_cache</a></code></li>
<li><code><a title="cvpysdk.security.user.Users.all_users_prop" href="#cvpysdk.security.user.Users.all_users_prop">all_users_prop</a></code></li>
<li><code><a title="cvpysdk.security.user.Users.delete" href="#cvpysdk.security.user.Users.delete">delete</a></code></li>
<li><code><a title="cvpysdk.security.user.Users.get" href="#cvpysdk.security.user.Users.get">get</a></code></li>
<li><code><a title="cvpysdk.security.user.Users.get_users_cache" href="#cvpysdk.security.user.Users.get_users_cache">get_users_cache</a></code></li>
<li><code><a title="cvpysdk.security.user.Users.has_user" href="#cvpysdk.security.user.Users.has_user">has_user</a></code></li>
<li><code><a title="cvpysdk.security.user.Users.refresh" href="#cvpysdk.security.user.Users.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.security.user.Users.service_commcell_users_space" href="#cvpysdk.security.user.Users.service_commcell_users_space">service_commcell_users_space</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>