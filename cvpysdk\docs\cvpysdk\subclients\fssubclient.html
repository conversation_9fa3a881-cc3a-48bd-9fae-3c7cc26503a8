<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.fssubclient API documentation</title>
<meta name="description" content="File for operating on a File System Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.fssubclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a File System Subclient</p>
<p>FileSystemSubclient is the only class defined in this file.</p>
<p>FileSystemSubclient: Derived class from Subclient Base class, representing a file system subclient,
and to perform operations on that subclient</p>
<h2 id="filesystemsubclient">Filesystemsubclient</h2>
<p>_get_subclient_properties()
&ndash;
initializes the subclient related properties of the
File System subclient</p>
<p>_get_subclient_properties_json()
&ndash;
gets all the subclient related properties of the
File System subclient</p>
<p>_common_backup_options()
&ndash;
Generates the advanced job options dict</p>
<p>_advanced_backup_options()
&ndash;
sets the advanced backup options</p>
<p>enable_content_indexing
&ndash;
Enables Content indexing and add the policy associations</p>
<p>disable_content_indexing
&ndash;
Disables Content indexing and disassociate the CI policy</p>
<p>find_all_versions()
&ndash;
returns the dict containing list of all the backed up
versions of specified file</p>
<p>backup()
&ndash;
run a backup job for the subclient</p>
<p>run_backup_copy()
&ndash;
Runs the backup copy job from Subclient</p>
<p>restore_in_place()
&ndash;
Restores the files/folders
specified in the input paths list to the same location.</p>
<p>restore_out_of_place()
&ndash;
Restores the files/folders specified in the input paths list
to the input client, at the specified destionation location</p>
<p>preview_backedup_file()
&ndash;
Get the preview content of the file</p>
<p>add_comparison()
&ndash;
Adds a comparison job for the subclient</p>
<h1 id="filesystemsubclient-instance-attributes">FileSystemSubclient Instance Attributes:</h1>
<pre><code>**_fs_subclient_prop**                --  Returns the JSON for the fsSubclientProp tag in the Subclient 
                                          Properties JSON

**content**                           --  update the content of the subclient

**filter_content**                    --  update the filter of the subclient

**exception_content**                 --  update the exception of the subclient

**scan_type**                         --  update the scan type of the subclient

**trueup_option**                     --  enable/disable trueup option of the subclient

**backup_retention**                  --  enable/disable backup retention for the subclient

**backup_retention_days**             --  set number of days for backup retention

**archiver_retention**                --  enable/disable archiver_retention of the subclient.

**archiver_retention_days**           --  set number of days for archiver retention

**file_version**                      --  set version mode and no of version or days

**disk_cleanup**                      --  enable/disable disk cleanup tab

**disk_cleanup_rules**                --  update rules for disk_cleanup

**backup_only_archiving_candidate**   --  enable or disable backup only candidate on the subclient

**trueup_days**                       --  update trueup after **n** days value of the subclient

**generate_signature_on_ibmi**        --  enable or disable signature generation on ibmi

**backup_using_multiple_drives**      --  enable or disable VTL multiple drives for ibmi subclient.

**pending_record_changes**            --  Updates the pending record changes value on ibmi subclient.

**other_pending_changes**             --  Updates the other pending changes value on ibmi subclient.

**object_level_backup**               --  enable or disable object level backup for ibmi subclient

**global_filter_status**              --  returns the status whther to include global filters

**enable_synclib**                    --  enable or disable SAVACT option for ibmi subclients.

**software_compression**              --  The software compression setting's value for the subclient.

**use_vss**                           --  The Use VSS setting's value for the subclient.

**block_level_backup_option**         --  Enable/Disable Blocklevel Option on subclient

**create_file_level_index_option**    --  Enable/Disable Metadata collection Option on subclient

**system_state_option**               --  Enable/Disable System state option for the subclient

**_dc_options_dict**                  --   Data Classification plan  Options

**enable_dc_content_indexing**        -- Enable Dataclassification Indexing option.

**onetouch_option**                   --  Enable/Disable One-Touch option for the subclient

**onetouch_server**                   --  Provides the 1-touch server name

**onetouch_server_directory**         --  Provides the 1-touch server directory

**catalog_acl**                       --  To enable/disable ACL on the subclient

**index_server**                      --  Sets/gets the index server client for the subclient

**index_pruning_type**                --  Sets the index pruning type

**index_pruning_days_retention**      --  Sets the number of days to be maintained in
                                          subclient index

**index_pruning_cycles_retention**    --  Sets the number of cycles to be maintained in
                                          subclient index

**ibmi_dr_config**                    --  Sets the subclient into one touch mode and adds ibmi DR parameters

**backup_savf_file_data**             --  Sets the savf file data property for ibmi backup.

**backup_spool_file_data**            --  Gets the value of spool file data on ibmi option for IBMi subclient

**backup_queue_data**                 --  Gets the value of queue data on ibmi option for IBMi subclient.

**backup_private_authorities**        --  Gets the value of private authorities on ibmi option for IBMi subclient.

**target_release**                    --  Gets the value of target and release on ibmi option for IBMi subclient.

**save_access_path**                  --  Gets the value of save access path on ibmi option for IBMi subclient.

**update_history**                    --  Updates the update history property value on ibmi subclient.

**ibmi_compression**                  --  Gets the value of IBMi compression property on
                                            ibmi option for IBMi subclient.

**save_while_active_option**          --  Set the save while active options for an IBMi subclient.

**pre_post_commands**                                 --  Sets the pre/post commands for the subclient.

**backup_nodes**                      --  Sets backup nodes for FS Agent under Network Share clients.

**impersonate_user**                  --  Impersonation information for the subclient.

**plan**                              -- Set plan without/with overriding Plan's default content.
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1-L2801" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a File System Subclient

FileSystemSubclient is the only class defined in this file.

FileSystemSubclient: Derived class from Subclient Base class, representing a file system subclient,
                        and to perform operations on that subclient

FileSystemSubclient:

    _get_subclient_properties()         --  initializes the subclient related properties of the
    File System subclient

    _get_subclient_properties_json()    --  gets all the subclient related properties of the
    File System subclient

    _common_backup_options()            --  Generates the advanced job options dict

    _advanced_backup_options()          --  sets the advanced backup options

    enable_content_indexing             --  Enables Content indexing and add the policy associations

    disable_content_indexing            --  Disables Content indexing and disassociate the CI policy

    find_all_versions()                 --  returns the dict containing list of all the backed up
                                            versions of specified file

    backup()                            --  run a backup job for the subclient

    run_backup_copy()                   --  Runs the backup copy job from Subclient

    restore_in_place()                  --  Restores the files/folders
                                            specified in the input paths list to the same location.

    restore_out_of_place()              --  Restores the files/folders specified in the input paths list
                                            to the input client, at the specified destionation location

    preview_backedup_file()             --  Get the preview content of the file

    add_comparison()                    --  Adds a comparison job for the subclient

FileSystemSubclient Instance Attributes:
=======================================

    **_fs_subclient_prop**                --  Returns the JSON for the fsSubclientProp tag in the Subclient 
                                              Properties JSON
                                             
    **content**                           --  update the content of the subclient

    **filter_content**                    --  update the filter of the subclient

    **exception_content**                 --  update the exception of the subclient

    **scan_type**                         --  update the scan type of the subclient

    **trueup_option**                     --  enable/disable trueup option of the subclient

    **backup_retention**                  --  enable/disable backup retention for the subclient

    **backup_retention_days**             --  set number of days for backup retention

    **archiver_retention**                --  enable/disable archiver_retention of the subclient.

    **archiver_retention_days**           --  set number of days for archiver retention

    **file_version**                      --  set version mode and no of version or days

    **disk_cleanup**                      --  enable/disable disk cleanup tab

    **disk_cleanup_rules**                --  update rules for disk_cleanup

    **backup_only_archiving_candidate**   --  enable or disable backup only candidate on the subclient

    **trueup_days**                       --  update trueup after **n** days value of the subclient

    **generate_signature_on_ibmi**        --  enable or disable signature generation on ibmi

    **backup_using_multiple_drives**      --  enable or disable VTL multiple drives for ibmi subclient.

    **pending_record_changes**            --  Updates the pending record changes value on ibmi subclient.

    **other_pending_changes**             --  Updates the other pending changes value on ibmi subclient.

    **object_level_backup**               --  enable or disable object level backup for ibmi subclient

    **global_filter_status**              --  returns the status whther to include global filters

    **enable_synclib**                    --  enable or disable SAVACT option for ibmi subclients.

    **software_compression**              --  The software compression setting&#39;s value for the subclient.

    **use_vss**                           --  The Use VSS setting&#39;s value for the subclient.
  
    **block_level_backup_option**         --  Enable/Disable Blocklevel Option on subclient

    **create_file_level_index_option**    --  Enable/Disable Metadata collection Option on subclient

    **system_state_option**               --  Enable/Disable System state option for the subclient

    **_dc_options_dict**                  --   Data Classification plan  Options

    **enable_dc_content_indexing**        -- Enable Dataclassification Indexing option.

    **onetouch_option**                   --  Enable/Disable One-Touch option for the subclient

    **onetouch_server**                   --  Provides the 1-touch server name

    **onetouch_server_directory**         --  Provides the 1-touch server directory
    
    **catalog_acl**                       --  To enable/disable ACL on the subclient

    **index_server**                      --  Sets/gets the index server client for the subclient

    **index_pruning_type**                --  Sets the index pruning type

    **index_pruning_days_retention**      --  Sets the number of days to be maintained in
                                              subclient index

    **index_pruning_cycles_retention**    --  Sets the number of cycles to be maintained in
                                              subclient index

    **ibmi_dr_config**                    --  Sets the subclient into one touch mode and adds ibmi DR parameters

    **backup_savf_file_data**             --  Sets the savf file data property for ibmi backup.

    **backup_spool_file_data**            --  Gets the value of spool file data on ibmi option for IBMi subclient

    **backup_queue_data**                 --  Gets the value of queue data on ibmi option for IBMi subclient.

    **backup_private_authorities**        --  Gets the value of private authorities on ibmi option for IBMi subclient.

    **target_release**                    --  Gets the value of target and release on ibmi option for IBMi subclient.

    **save_access_path**                  --  Gets the value of save access path on ibmi option for IBMi subclient.

    **update_history**                    --  Updates the update history property value on ibmi subclient.

    **ibmi_compression**                  --  Gets the value of IBMi compression property on
                                                ibmi option for IBMi subclient.

    **save_while_active_option**          --  Set the save while active options for an IBMi subclient.

    **pre_post_commands**                                 --  Sets the pre/post commands for the subclient.

    **backup_nodes**                      --  Sets backup nodes for FS Agent under Network Share clients.

    **impersonate_user**                  --  Impersonation information for the subclient.
    
    **plan**                              -- Set plan without/with overriding Plan&#39;s default content.

&#34;&#34;&#34;

from __future__ import unicode_literals
from base64 import b64encode

from ..client import Client
from ..subclient import Subclient
from ..exception import SDKException
from ..job import Job


def _nested_dict(source, update_dict):
    &#34;&#34;&#34;
    This function recursively update the source dictionary with new values.

    Args:
         source   (dict)  --  Original dictionary

         update_dict   (dict)  --  The changes which are need to make

    Return:
        dict  --  modified source dictionary with updated values

    &#34;&#34;&#34;
    for key, value in update_dict.items():
        if isinstance(value, dict) and value:
            source[key] = _nested_dict(source.get(key, {}), value)
        else:
            source[key] = value
    return source


class FileSystemSubclient(Subclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a file system subclient,
        and to perform operations on that subclient.
    &#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of File System subclient.

        &#34;&#34;&#34;
        super(FileSystemSubclient, self)._get_subclient_properties()
        self._impersonateUser={}
        if &#39;impersonateUser&#39; in self._subclient_properties:
            self._impersonateUser = self._subclient_properties[&#39;impersonateUser&#39;]

        if &#39;fsSubClientProp&#39; in self._subclient_properties:
            self._fsSubClientProp = self._subclient_properties[&#39;fsSubClientProp&#39;]

        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

        self._global_filter_status_dict = {
            &#39;OFF&#39;: 0,
            &#39;ON&#39;: 1,
            &#39;USE CELL LEVEL POLICY&#39;: 2
        }

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;impersonateUser&#34;: self._impersonateUser,
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;fsSubClientProp&#34;: self._fsSubClientProp,

                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;fsContentOperationType&#34;: &#34;OVERWRITE&#34;,
                    &#34;fsExcludeFilterOperationType&#34;: &#34;OVERWRITE&#34; if not hasattr(self, &#39;_fsExcludeFilterOperationType&#39;) else self._fsExcludeFilterOperationType,
                    &#34;fsIncludeFilterOperationType&#34;: &#34;OVERWRITE&#34; if not hasattr(self, &#39;_fsIncludeFilterOperationType&#39;) else self._fsIncludeFilterOperationType
                }
        }

        if &#39;isDDBSubclient&#39; in self._fs_subclient_prop:
            if self._fs_subclient_prop[&#39;isDDBSubclient&#39;]:
                del subclient_json[&#34;subClientProperties&#34;][&#34;content&#34;]
        return subclient_json

    @property
    def _fs_subclient_prop(self):
        &#34;&#34;&#34;Returns the JSON for the fsSubclientProp tag in the Subclient Properties JSON&#34;&#34;&#34;
        return self._fsSubClientProp

    @_fs_subclient_prop.setter
    def _fs_subclient_prop(self, value):
        &#34;&#34;&#34;Update the values of fsSubclientProp JSON.

            Args:
                value   (dict)  --  dictionary consisting of the JSON attribute as the key
                and the new data as its value

            Raises:
                SDKException:
                    if value is not of type dict

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        _nested_dict(self._fsSubClientProp, value)

        if &#39;enableOnePass&#39; in self._fsSubClientProp:
            del self._fsSubClientProp[&#39;enableOnePass&#39;]

        if &#39;isTurboSubclient&#39; in self._commonProperties:
            del self._commonProperties[&#39;isTurboSubclient&#39;]

    def _set_content(self,
                     content=None,
                     filter_content=None,
                     exception_content=None):
        &#34;&#34;&#34;Sets the subclient content / filter / exception content

            Args:
                content             (list)      --  list of subclient content

                filter_content      (list)      --  list of filter content

                exception_content   (list)      --  list of exception content
        &#34;&#34;&#34;
        if content is None:
            content = self.content

        if filter_content is None:
            filter_content = self.filter_content

        if exception_content is None:
            exception_content = self.exception_content

        update_content = []
        for path in content:
            file_system_dict = {
                &#34;path&#34;: path
            }
            update_content.append(file_system_dict)

        for path in filter_content:
            filter_dict = {
                &#34;excludePath&#34;: path
            }
            update_content.append(filter_dict)

        for path in exception_content:
            exception_dict = {
                &#34;includePath&#34;: path
            }
            update_content.append(exception_dict)

        self._set_subclient_properties(&#34;_content&#34;, update_content)
        self._fsExcludeFilterOperationType = &#34;OVERWRITE&#34;  # RESET THE OPERATION TYPE TO ITS DEFAULT
        self._fsIncludeFilterOperationType = &#34;OVERWRITE&#34;  # RESET THE OPERATION TYPE TO ITS DEFAULT


    def _common_backup_options(self, options):
        &#34;&#34;&#34;
         Generates the advanced job options dict

            Args:
                options     (dict)  --  advanced job options that are to be included
                                            in the request

            Returns:
                (dict)  -   generated advanced options dict
        &#34;&#34;&#34;
        final_dict = super(FileSystemSubclient, self)._common_backup_options(options)

        common_options = {
            &#34;jobDescription&#34;: options.get(&#39;job_description&#39;, &#34;&#34;),
            &#34;jobRetryOpts&#34;: {
                &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: options.get(
                    &#39;kill_running_job_when_total_running_time_expires&#39;, False),
                &#34;numberOfRetries&#34;: options.get(&#39;number_of_retries&#39;, 0),
                &#34;enableNumberOfRetries&#34;: options.get(&#39;enable_number_of_retries&#39;, False),
                &#34;runningTime&#34;: {
                    &#34;enableTotalRunningTime&#34;: options.get(&#39;enable_total_running_time&#39;, False),
                    &#34;totalRunningTime&#34;: options.get(&#39;total_running_time&#39;, 3600)
                }
            },
            &#34;startUpOpts&#34;: {
                &#34;startInSuspendedState&#34;: options.get(&#39;start_in_suspended_state&#39;, False),
                &#34;useDefaultPriority&#34;: options.get(&#39;use_default_priority&#39;, True),
                &#34;priority&#34;: options.get(&#39;priority&#39;, 166)
            }
        }

        return common_options

    def _advanced_backup_options(self, options):
        &#34;&#34;&#34;Generates the advanced backup options dict

            Args:
                options     (dict)  --  advanced backup options that are to be included
                                            in the request

            Returns:
                (dict)  -   generated advanced options dict
        &#34;&#34;&#34;
        final_dict = super(FileSystemSubclient, self)._advanced_backup_options(options)

        if &#39;on_demand_input&#39; in options and options[&#39;on_demand_input&#39;] is not None:
            final_dict[&#39;onDemandInputFile&#39;] = options[&#39;on_demand_input&#39;]

        if &#39;directive_file&#39; in options and options[&#39;directive_file&#39;] is not None:
            final_dict[&#39;onDemandInputFile&#39;] = options[&#39;directive_file&#39;]

        if &#39;adhoc_backup&#39; in options and options[&#39;adhoc_backup&#39;] is not None:
            final_dict[&#39;adHocBackup&#39;] = options[&#39;adhoc_backup&#39;]

        if &#39;inline_bkp_cpy&#39; in options or &#39;skip_catalog&#39; in options:
            final_dict[&#39;dataOpt&#39;] = {
                &#39;createBackupCopyImmediately&#39;: options.get(&#39;inline_bkp_cpy&#39;, False),
                &#39;skipCatalogPhaseForSnapBackup&#39;: options.get(&#39;skip_catalog&#39;, False)}

        if &#39;adhoc_backup_contents&#39; in options and options[&#39;adhoc_backup_contents&#39;] is not None:
            if not isinstance(options[&#39;adhoc_backup_contents&#39;], list):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            final_dict[&#39;adHocBkpContents&#39;] = {
                &#39;selectedAdHocPaths&#39;: options[&#39;adhoc_backup_contents&#39;]
            }

        if &#39;use_multi_stream&#39; in options and options[&#39;use_multi_stream&#39;]:

            multi_stream_opts = {
                &#39;useMultiStream&#39;: options.get(&#39;use_multi_stream&#39;, False),
                &#39;useMaximumStreams&#39;: options.get(&#39;use_maximum_streams&#39;, True),
                &#39;maxNumberOfStreams&#39;: options.get(&#39;max_number_of_streams&#39;, 1)
            }

            if &#39;dataOpt&#39; in final_dict and isinstance(final_dict[&#39;dataOpt&#39;], dict):
                final_dict[&#39;dataOpt&#39;].update(multi_stream_opts)
            else:
                final_dict[&#39;dataOpt&#39;] = multi_stream_opts

        if &#39;start_new_media&#39; in options and options[&#39;start_new_media&#39;]:

            media_opts = {
                &#39;startNewMedia&#39;: options.get(&#39;start_new_media&#39;, False)
            }

            if &#39;mediaOpt&#39; in final_dict and isinstance(final_dict[&#39;mediaOpt&#39;], dict):
                final_dict[&#39;mediaOpt&#39;].update(media_opts)
            else:
                final_dict[&#39;mediaOpt&#39;] = media_opts
        
        if options.get(&#39;media_agent_name&#39;):
            media_agent_name = options[&#39;media_agent_name&#39;]
            if not isinstance(media_agent_name, str):
                message = f&#34;media_agent_name: Expected str, received {type(media_agent_name)}&#34;
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, message)
            final_dict[&#39;dataPathOpt&#39;] = {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentName&#34;: media_agent_name
                }
            }

        if &#39;mark_media_full_on_success&#39; in options and options[&#39;mark_media_full_on_success&#39;]:

            media_opts = {
                &#39;markMediaFullOnSuccess&#39;: options.get(&#39;mark_media_full_on_success&#39;, False)
            }

            if &#39;mediaOpt&#39; in final_dict and isinstance(final_dict[&#39;mediaOpt&#39;], dict):
                final_dict[&#39;mediaOpt&#39;].update(media_opts)
            else:
                final_dict[&#39;mediaOpt&#39;] = media_opts

        return final_dict

    @property
    def _vlr_restore_options_dict(self):
        &#34;&#34;&#34; Constructs volume level Restore Dictionary&#34;&#34;&#34;

        physical_volume = &#39;PHYSICAL_VOLUME&#39;
        vlr_options_dict = {
            &#34;volumeRstOption&#34;: {
                &#34;volumeLeveRestore&#34;: True,
                &#34;volumeLevelRestoreType&#34;: physical_volume
            },
            &#34;virtualServerRstOption&#34;: {
                &#34;isDiskBrowse&#34;: False,
                &#34;isVolumeBrowse&#34;: True,
                &#34;isBlockLevelReplication&#34;: False
            }
        }
        return vlr_options_dict

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;
        content = []

        for path in self._content:
            if &#39;path&#39; in path:
                content.append(path[&#34;path&#34;])

        return content

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update content of a
            File System Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        if isinstance(subclient_content, list) and subclient_content != []:
            self._set_content(content=subclient_content)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Subclient content should be a list value and not empty&#39;)

    @property
    def filter_content(self):
        &#34;&#34;&#34;Treats the subclient filter content as a property of the Subclient class.&#34;&#34;&#34;
        _filter_content = []

        for path in self._content:
            if &#39;excludePath&#39; in path:
                _filter_content.append(path[&#34;excludePath&#34;])

        return _filter_content

    @filter_content.setter
    def filter_content(self, value):
        &#34;&#34;&#34;Sets the filter content of the subclient as the value provided as input.
            An empty list will clear all filters.

            example: [&#39;*book*&#39;, &#39;file**&#39;]

            Raises:
                SDKException:
                    if failed to update filter content of subclient

                    if the type of value input is not list

        &#34;&#34;&#34;
        if isinstance(value, list):
            if value == []:
                value = self.filter_content
                self._fsExcludeFilterOperationType = &#34;DELETE&#34;
            self._set_content(filter_content=value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Subclient filter content should be a list value&#39;)

    @property
    def exception_content(self):
        &#34;&#34;&#34;Treats the subclient exception content as a property of the Subclient class.&#34;&#34;&#34;
        _exception_content = []

        for path in self._content:
            if &#39;includePath&#39; in path:
                _exception_content.append(path[&#34;includePath&#34;])

        return _exception_content

    @exception_content.setter
    def exception_content(self, value):
        &#34;&#34;&#34;Sets the exception content of the subclient as the value provided as input.

            example: [&#39;*book*&#39;, &#39;file**&#39;]

            Raises:
                SDKException:
                    if failed to update exception content of subclient

                    if the type of value input is not list

                    if value list is empty
        &#34;&#34;&#34;
        if isinstance(value, list):
            if value == []:
                value = self.exception_content
                self._fsIncludeFilterOperationType = &#34;DELETE&#34;
            self._set_content(exception_content=value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Subclient exception content should be a list value and not empty&#39;)

    @property
    def scan_type(self):
        &#34;&#34;&#34;Gets the appropriate scan type for this Subclient

            Returns:
                int
                    1   -   Recursive Scan
                    2   -   Optimized Scan
                    3   -   Change Journal Scan

        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;scanOption&#39;]

    @scan_type.setter
    def scan_type(self, scan_type_value):
        &#34;&#34;&#34;Creates the JSON with the specified scan type to pass to the API
            to update the scan type of this File System Subclient.

            Args:
                scan_type_value     (int)   --  scan type value as indicated below

                    1   -   Recursive Scan
                    2   -   Optimized Scan
                    3   -   Change Journal Scan

            Raises:
                SDKException:
                    if failed to update scan type of subclient

                    if scan_type_value is invalid

        &#34;&#34;&#34;
        if isinstance(scan_type_value, int) and scan_type_value &gt;= 1 and scan_type_value &lt;= 3:
            self._set_subclient_properties(&#34;_fsSubClientProp[&#39;scanOption&#39;]&#34;, scan_type_value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Invalid scan type&#39;)

    @property
    def trueup_option(self):
        &#34;&#34;&#34;Gets the value of TrueUp Option

            Returns:
                True    -   if trueup is enabled on the subclient

                False   -   if trueup is not enabled on the subclient

        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;isTrueUpOptionEnabledForFS&#39;]

    @trueup_option.setter
    def trueup_option(self, trueup_option_value):
        &#34;&#34;&#34;Creates the JSON with the specified scan type to pass to the API
            to update the scan type of this File System Subclient.

            Args:
                trueup_option_value (bool)  --  Specifies to enable or disable trueup
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;isTrueUpOptionEnabledForFS&#39;]&#34;,
            trueup_option_value
        )

    def run_backup_copy(self):
        &#34;&#34;&#34;
        Runs the backup copy from Commcell for the given subclient

        Args:
                None

        Returns:
                object - instance of the Job class for this backup copy job
        Raises:
            SDKException:

                    if backup copy job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;clientName&#34;: self._client_object._client_name,
                        &#34;subclientName&#34;: self._subclient_name,
                        &#34;backupsetName&#34;: self._backupset_object._backupset_name,
                        &#34;storagePolicyName&#34;: self.storage_policy,
                        &#34;_type_&#34;: 17,
                        &#34;appName&#34;: self._agent_object._agent_name
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskId&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4028
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;snapToTapeOption&#34;: {
                                    &#34;allowMaximum&#34;: True,
                                    &#34;noofJobsToRun&#34;: 1
                                }
                            }
                        }
                    }
                ]
            }
        }

        backup_copy = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_copy, request_json)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Backup copy job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, &#39;Failed to run the backup copy job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def backup_retention(self):
        &#34;&#34;&#34;return if backup retention is enabled or not

        Returns:
                True    -   if backup_retention is enabled for the subclient

                False   -   if backup_rentention is not enabled for the subclient

        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;backupRetention&#39;]

    @backup_retention.setter
    def backup_retention(self, value):
        &#34;&#34;&#34;Creates the JSON with the specified Boolean variable to pass to the API
            to update the backup_retention of this File System Subclient

        Args:
             value   (bool)  --  To enable or disable backup_retention.

        &#34;&#34;&#34;

        if isinstance(value, bool):

            if value:
                new_value = {
                    &#39;extendStoragePolicyRetention&#39;: True,
                    &#39;backupRetention&#39;: True}
            else:
                new_value = {&#39;backupRetention&#39;: False}
            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be boolean&#39;)

    @property
    def block_level_backup_option(self):
        &#34;&#34;&#34;Gets the block level option

            Returns:
                true - if blocklevel is enabled on the subclient
                false - if blocklevel is not enabled on the subclient
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;blockLevelBackup&#39;]

    @block_level_backup_option.setter
    def block_level_backup_option(self, block_level_backup_value):
        &#34;&#34;&#34;Creates the JSON with the specified blocklevel flag
            to pass to the API to update the blocklevel of this
            File System Subclient.

            Args:
                block_level_backup_value (bool)  --  Specifies to enable or disable blocklevel option
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;blockLevelBackup&#39;]&#34;,
            block_level_backup_value)


    @property
    def _dc_options_dict(self):
        &#34;&#34;&#34; Constructs Data classification Property&#34;&#34;&#34;
        dc_options = {&#39;dcPlanEntity&#39;: {&#39;planType&#39;: 7, &#39;planSubtype&#39;: 117506053, &#39;planName&#39;: &#39;&#39;}}
        return dc_options

    def enable_dc_content_indexing(self, dcplan_name):
        &#34;&#34;&#34;Creates the JSON with the specified dataclassification plan to pass to API to
            update  file system Subclient

            Args:
                dcplan_name (String)  --  DC plan name

        &#34;&#34;&#34;
        temp_dc = self._dc_options_dict
        temp_dc[&#39;dcPlanEntity&#39;][&#39;planName&#39;] = dcplan_name
        self.update_properties(temp_dc)

    @property
    def create_file_level_index_option(self):
        &#34;&#34;&#34;Gets the value of Metadata collection Option

            Returns:
                true - if metadata collection is enabled on the subclient
                false - if metadata collection is not enabled on the subclient
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;createFileLevelIndexDuringBackup&#39;]

    @create_file_level_index_option.setter
    def create_file_level_index_option(self, create_file_level_index_value):
        &#34;&#34;&#34;Creates the JSON with the specified scan type
            to pass to the API to update the Metadata collection of this
            File System Subclient.

            Args:
                create_file_level_index_value (bool)  --  Specifies to enable or disable metadata collection
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;createFileLevelIndexDuringBackup&#39;]&#34;,
            create_file_level_index_value)

    @property
    def backup_retention_days(self):
        &#34;&#34;&#34;return number of days for backup retention

        Returns:
                        (int)

        &#34;&#34;&#34;

        # For Indexing V2 clients
        if &#39;afterDeletionKeepItemsForNDays&#39; in self._fsSubClientProp:
            return self._fsSubClientProp[&#39;afterDeletionKeepItemsForNDays&#39;]

        # For Indexing V1 clients
        else:
            return self._fsSubClientProp.get(&#39;daysToKeepItems&#39;, 0)

    @backup_retention_days.setter
    def backup_retention_days(self, value):
        &#34;&#34;&#34;Creates the JSON with the specified backup_retention days to pass to the API
            to update the retention for deleted item of this File System Subclient

        Args:
                value   (int)  --  To set extended retention days for deleted items

                The value will be converted in years , months and days form on GUI.

                To set infinite ,value should be -1

        Raises:
                SDKException:
                    if failed to update days for deleted item retention for the subclient

                    if value is invalid

        &#34;&#34;&#34;

        if isinstance(value, int):
            if &#39;afterDeletionKeepItemsForNDays&#39; in self._fsSubClientProp:
                if value != -1:
                    new_value = {
                        &#39;afterDeletionKeepItemsForNDays&#39;: value,
                        &#39;backupRetentionMode&#39;: 1
                    }
                else:
                    new_value = {&#39;afterDeletionKeepItemsForNDays&#39;: value}
            else:
                new_value = {
                    &#39;daysToKeepItems&#39;: value
                }

            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)

        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be boolean&#39;)

    @property
    def system_state_option(self):
        &#34;&#34;&#34;Checks whether the system state option is enabled

        Returns:
            True    -   if system state property is enabled for the subclient

            False   -   if system state property is not enabled for the subclient
        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;backupSystemState&#39;]

    @system_state_option.setter
    def system_state_option(self, backup_system_state):
        &#34;&#34;&#34;
        Enables the system state property for the subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;backupSystemState&#39;]&#34;,
            backup_system_state)

    @property
    def onetouch_option(self):
        &#34;&#34;&#34;Checks whether the onetouch option is enabled

        Returns:
            True    -   if system state property is enabled for the subclient

            False   -   if system state property is not enabled for the subclient
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;oneTouchSubclient&#39;)

    @onetouch_option.setter
    def onetouch_option(self, backup_onetouch):
        &#34;&#34;&#34;
        Enables the system state property for the subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(&#34;_fsSubClientProp[&#39;oneTouchSubclient&#39;]&#34;, backup_onetouch)

    @property
    def onetouch_server(self):
        &#34;&#34;&#34;
        Returns: Onetouch Server Name
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;oneTouchServer&#39;, {}).get(&#39;clientName&#39;)

    @onetouch_server.setter
    def onetouch_server(self, onetouch_server):
        &#34;&#34;&#34;
        Sets the onetouch server property
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;oneTouchServer&#39;][&#39;clientName&#39;]&#34;,
            onetouch_server)

    @property
    def onetouch_server_directory(self):
        &#34;&#34;&#34;
        Returns the onetouch server directory
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;oneTouchServerDirectory&#39;)

    @onetouch_server_directory.setter
    def onetouch_server_directory(self, onetouch_server_directory):
        &#34;&#34;&#34;
        Sets the onetouch server directory
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;oneTouchServerDirectory&#39;]&#34;,
            onetouch_server_directory)

    @property
    def trueup_days(self):
        &#34;&#34;&#34;Gets the trueup after n days value for this Subclient

            Returns: int
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;runTrueUpJobAfterDaysForFS&#39;]

    @trueup_days.setter
    def trueup_days(self, trueup_days_value):
        &#34;&#34;&#34;Creates the JSON with the specified trueup days to pass to the API
            to update the trueup after **n** days value of this File System Subclient.

            Args:
                trueup_days_value   (int)   --  run trueup after days

            Raises:
                SDKException:
                    if failed to update trueup after n days of subclient

                    if trueup_days_value is invalid

        &#34;&#34;&#34;

        if isinstance(trueup_days_value, int):
            self._set_subclient_properties(
                &#34;_fsSubClientProp[&#39;runTrueUpJobAfterDaysForFS&#39;]&#34;,
                trueup_days_value
            )
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Invalid trueup days&#39;)

    @property
    def archiver_retention(self):
        &#34;&#34;&#34;return the value of archiver retention or modified time retention

          Returns:
                True    -   if archiver or modified time retention is enabled for the subclient

                False   -   if archiver or modified time retention is not enabled for the subclient


        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;archiverRetention&#39;]

    @archiver_retention.setter
    def archiver_retention(self, value):
        &#34;&#34;&#34;
        Creates the JSON with the specified Boolean variable to pass to the API
            to update the archiver or modified time based retention of this File System Subclient

        If archiver retention is enabled-
                With backup retention, the object based retention is selected and
                modified time based retention is selected.

                Without backup retention, job based retention is selected
        Args:
            value  (bool)  --  To enable or disable job based retention or modified time retention



        &#34;&#34;&#34;
        if isinstance(value, bool):

            if value:
                new_value = {
                    &#39;extendStoragePolicyRetention&#39;: True,
                    &#39;archiverRetention&#39;: True}
            else:
                new_value = {&#39;archiverRetention&#39;: False}
            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be boolean&#39;)

    @property
    def archiver_retention_days(self):
        &#34;&#34;&#34;return number of days for archiver or modified time  retention

           Return:
                                (int)
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;extendRetentionForNDays&#39;]

    @archiver_retention_days.setter
    def archiver_retention_days(self, value):
        &#34;&#34;&#34;
        Creates the JSON with the specified archiver retention or modified time based retentiondays
         to pass to the API to update the respected value of this File System Subclient

        Args:
                value  (int)  --   To update archiving retention or modified time based retention

                               The value will be converted in years , months and days from on GUI.

                               To set infinite value should be -1

        Raises:
                SDKException:
                    if failed to update archiver retention days of subclient

                    if value is invalid


        &#34;&#34;&#34;
        if isinstance(value, int):
            if value != -1:
                new_value = {
                    &#39;extendRetentionForNDays&#39;: value,
                    &#39;archiverRetentionMode&#39;: 1}
            else:
                new_value = {&#39;extendRetentionForNDays&#39;: value}
            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)

        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be integer&#39;)

    @property
    def disk_cleanup(self):
        &#34;&#34;&#34;
        return value of disk cleanup of the subclient

         Returns:
                True    -   if disk Cleanup is enabled for the subclient

                False   -   if disk Cleanup is not enabled for the subclient


        &#34;&#34;&#34;
        diskcleanup = None
        if &#39;enableArchivingWithRules&#39; in self._fsSubClientProp[&#39;diskCleanupRules&#39;]:
            return self._fsSubClientProp[&#39;diskCleanupRules&#39;][&#39;enableArchivingWithRules&#39;]

        return diskcleanup

    @disk_cleanup.setter
    def disk_cleanup(self, value):
        &#34;&#34;&#34;
        Creates the JSON with the specified Boolean to pass to the API
            to update the disk cleanup option of this File System Subclient

        Args:
            value   (bool)  --  To enable or disbale disk cleanup

        Raises:
                SDKException:
                    if failed to update the propety of subclient

                    if value is invalid

        &#34;&#34;&#34;

        if isinstance(value, bool):

            self._set_subclient_properties(
                &#34;_fsSubClientProp[&#39;diskCleanupRules&#39;][&#39;enableArchivingWithRules&#39;]&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be boolean&#39;)

    @property
    def disk_cleanup_rules(self):
        &#34;&#34;&#34;
        return disk cleanup rules for this FileSystem Subclient

        Return:
            (dict)  --  disk clean up rules
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;diskCleanupRules&#39;]

    @disk_cleanup_rules.setter
    def disk_cleanup_rules(self, rules):
        &#34;&#34;&#34;
        Creates the JSON with the specified dictionary value to pass to the API
            to update the disk cleanup rules of this File System Subclient

        Args:
                        rules   (dict)  --  To update the rules Only need to send the value which need to be
                        updated

                        {
                &#39;useNativeSnapshotToPreserveFileAccessTime&#39;: False,
                &#39;fileModifiedTimeOlderThan&#39;: 0,
                &#39;fileSizeGreaterThan&#39;: 1024,
                &#39;stubPruningOptions&#39;: 0, 0 to disable and 1,2 ,3 for different option

                &#39;afterArchivingRule&#39;: 1, - 1 for stub the file and 2 for delete the file

                &#39;stubRetentionDaysOld&#39;: 365,
                &#39;fileCreatedTimeOlderThan&#39;: 0,
                &#39;maximumFileSize&#39;: 0,
                &#39;fileAccessTimeOlderThan&#39;: 89,
                &#39;startCleaningIfLessThan&#39;: 50,
                &#39;enableRedundancyForDataBackedup&#39;: True,
                 &#39;stopCleaningIfupto&#39;: 80,

                 &#39;diskCleanupFileTypes&#39;: {&#39;fileTypes&#39;: [&#34;%Text%&#34;, &#39;%Image%&#39;]}

                 or

                 &#39;diskCleanupFilesTypes&#39;:{} for no extension
                }
        Raises:
                SDKException:
                    if failed to update the property of the subclient

                    if value is invalid


        &#34;&#34;&#34;
        if isinstance(rules, dict):
            value = {&#39;diskCleanupRules&#39;: rules}
            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#34;The parameter should be dictionary&#34;)

    @property
    def backup_only_archiving_candidate(self):
        &#34;&#34;&#34;
            To get the value of backup only archiving candidate

        Returns:
                True    -   if backup only archiving candidate is enabled for the subclient

                False   -   if backup only archiving candidate is not enabled for the subclient
        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;backupFilesQualifiedForArchive&#39;]

    @backup_only_archiving_candidate.setter
    def backup_only_archiving_candidate(self, value):
        &#34;&#34;&#34;
        Creates the JSON with the specified boolean value to pass to the API
            to update the backup only archiving candidate of this File System Subclient

        Args:
            value   (bool)  --  Enable or disable the option

        Raises:
                SDKException:
                    if failed to update the propety of subclient

                    if value is invalid

        &#34;&#34;&#34;
        if isinstance(value, bool):
            self._set_subclient_properties(
                &#34;_fsSubClientProp[&#39;backupFilesQualifiedForArchive&#39;]&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;The parameter must be boolean type&#39;)

    @property
    def file_version(self):
        &#34;&#34;&#34;

        Returns:
                        (dict)  --  file version mode
        &#34;&#34;&#34;
        version = {}

        # For Indexing V1 client, this property is not supported. Taking default versions by number
        if &#39;olderFileVersionsMode&#39; not in self._fsSubClientProp:
            return {
                &#39;Mode&#39;: 2,
                &#39;DaysOrNumber&#39;: self._fsSubClientProp.get(&#39;keepAtLeastPreviousVersions&#39;, 0)
            }

        version[&#39;Mode&#39;] = self._fsSubClientProp[&#39;olderFileVersionsMode&#39;]
        modes = {
            1: self._fsSubClientProp[&#39;keepOlderVersionsForNDays&#39;],
            2: self._fsSubClientProp[&#39;keepVersions&#39;]
        }
        version[&#39;DaysOrNumber&#39;] = modes.get(version[&#39;Mode&#39;])
        return version

    @file_version.setter
    def file_version(self, value):
        &#34;&#34;&#34;
            Creates the JSON with the specified dictionary to pass to the API
            to update the version mode and the value of this File System Subclient

        Args:
             value   (dict)  --  format -{&#39;Mode&#39;:value,&#39;DaysOrNumber&#39;:value}

                    Mode value 1- version based on modified time

                                2- No of version

                Example-
                    To set version based on modified time to 2 years

                    value={&#39;Mode&#39;:1,&#39;DaysOrNumber&#39;,730}

                    To set Number of version to 10

                    value={&#39;Mode&#39;:2,&#39;DaysOrNumber&#39;:10}
        Raises:
               SDKException:
                    if failed to update the propety of subclient

                    if value is invalid

        &#34;&#34;&#34;

        if isinstance(value, dict):
            # For Indexing V2 client
            if &#39;olderFileVersionsMode&#39; in self._fsSubClientProp:
                if value[&#39;Mode&#39;] == 1 or value[&#39;Mode&#39;] == 2:
                    new_value = {&#39;olderFileVersionsMode&#39;: value[&#39;Mode&#39;]}
                else:
                    raise SDKException(
                        &#39;Subclient&#39;, &#39;102&#39;, &#34;File version mode can only be 1 or 2&#34;)
                modes = {
                    1: &#39;keepOlderVersionsForNDays&#39;,
                    2: &#39;keepVersions&#39;
                }

                new_value[modes[value[&#39;Mode&#39;]]] = value[&#39;DaysOrNumber&#39;]

            # For Indexing V1 client
            else:
                new_value = {
                    &#39;keepAtLeastPreviousVersions&#39;: value[&#39;DaysOrNumber&#39;]
                }

            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#34;Parameter need to be dictionary&#34;)

    @property
    def generate_signature_on_ibmi(self):
        &#34;&#34;&#34;Gets the value of generate signature on ibmi option for IBMi subclient.

            Returns:
                False   -   if signature generation on IBMi is enabled on the subclient

                True    -   if signature generation on IBMi is not enabled on the subclient
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;genSignatureOnIBMi&#39;))

    @generate_signature_on_ibmi.setter
    def generate_signature_on_ibmi(self, generate_signature_value):
        &#34;&#34;&#34;Updates the generate signature property value on ibmi subclient.

            Args:
                generate_signature_value (int)  --  Enable or disable signature generation on IBMi
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;genSignatureOnIBMi&#39;]&#34;,
            generate_signature_value
        )

    @property
    def backup_using_multiple_drives(self):
        &#34;&#34;&#34;Gets the value of VTL multiple drives on ibmi option for IBMi subclient.

            Returns:
                False   -   if multiple drives is not enabled.

                True    -   if multiple drives is enabled.
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;backupUsingMultipleDrives&#39;,False))

    @backup_using_multiple_drives.setter
    def backup_using_multiple_drives(self, set_vtl_multiple_drives):
        &#34;&#34;&#34;Updates the VTL multiple drives property value on ibmi subclient.

            Args:
                set_vtl_multiple_drives (bool)  --  Enable or disable VTL multiple drives on IBMi
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(set_vtl_multiple_drives, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupUsingMultipleDrives&#39;] = set_vtl_multiple_drives
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)
    
    @property
    def pending_record_changes(self):
        &#34;&#34;&#34;Gets the value of pending record changes option for  IBMi subclient.

            Returns:
                False   -   if multiple drives is not enabled.

                True    -   if multiple drives is enabled.
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;pendingRecordChange&#39;))

    @pending_record_changes.setter
    def pending_record_changes(self, value):
        &#34;&#34;&#34;Updates the pending record changes value on ibmi subclient.

            Args:
                value   (str)  --  To set pending records changes value for backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;pendingRecordChange&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def other_pending_changes(self):
        &#34;&#34;&#34;Gets the value of other pending changes for IBMi subclient.

            Returns:
                False   -   if multiple drives is not enabled.

                True    -   if multiple drives is enabled.
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;otherPendingChange&#39;))

    @other_pending_changes.setter
    def other_pending_changes(self, value):
        &#34;&#34;&#34;Updates the other pending changes value on ibmi subclient.

            Args:
                value   (str)  --  To set other pending changes value for backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;otherPendingChange&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def object_level_backup(self):
        &#34;&#34;&#34;Gets the value of object level backup option for IBMi subclient.

            Returns:
                True    -   if object level backup is enabled on the subclient

                False   -   if object level backup is not enabled on the subclient
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;backupAsObjects&#39;)

    @object_level_backup.setter
    def object_level_backup(self, object_level_value):
        &#34;&#34;&#34;Updates the object level backup property for an IBMi subclient.

            Args:
                object_level_value (bool)  --  Specifies to enable or disable object level backup on IBMi
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;backupAsObjects&#39;]&#34;,
            object_level_value
        )

    @property
    def global_filter_status(self):
        &#34;&#34;&#34;Returns the status whether the global filters are included in configuration&#34;&#34;&#34;
        for key, value in self._global_filter_status_dict.items():
            if self._fsSubClientProp.get(&#39;useGlobalFilters&#39;) == value:
                return key

    @global_filter_status.setter
    def global_filter_status(self, value):
        &#34;&#34;&#34;Sets the configuration flag whether to include global filters

            Accepted Values:
                1. `OFF`

                2. `ON`

                3. `USE CELL LEVEL POLICY`
        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;useGlobalFilters&#39;]&#34;, self._global_filter_status_dict.get(value, 2)
        )

    @property
    def enable_synclib(self):
        &#34;&#34;&#34;
        Return the save while active options for an IBMi subclient.

        Returns:
             (dict) --  Dictionary of synclib options
        &#34;&#34;&#34;
        return {
            &#39;saveWhileActiveOpt&#39;: self._fsSubClientProp[&#39;saveWhileActiveOpt&#39;],
            &#39;syncQueue&#39;: self._fsSubClientProp[&#39;syncQueue&#39;],
            &#39;syncAllLibForBackup&#39;: self._fsSubClientProp[&#39;syncAllLibForBackup&#39;],
            &#39;txtlibSyncCheckPoint&#39;: self._fsSubClientProp[&#39;txtlibSyncCheckPoint&#39;],
            &#39;activeWaitTime&#39;: self._fsSubClientProp[&#39;activeWaitTime&#39;]
        }

    @enable_synclib.setter
    def enable_synclib(self, synclib_config):
        &#34;&#34;&#34;
        Updates the save while active backup property for an IBMi subclient.

            Args:
                synclib_config      (dict)  -- Dictionary of synclib config options

                    options                 --

                        synclib_value       (str)   --  Value of save while active option.

                        sync_queue          (str)   --  Path for the sync queue

                        sync_all_lib        (bool)  --  Whether to synchronize all libraries.

                        check_point         (str)   --  Command to run on checkpoint

                        active_wait_time    (int)   --  Amount of time to wait for check point.

        Returns:
            None

        Raises:
            SDKException:
                if failed to update the property of the subclient

                if value is invalid
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(synclib_config, dict):
            update_properties[&#39;fsSubClientProp&#39;] = synclib_config
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;The parameter should be a dictionary.&#39;)
        self.update_properties(update_properties)

    @property
    def software_compression(self):
        &#34;&#34;&#34;Returns the software compression status for this subclient.

            Returns:    int
                    1   -   On Client
                    2   -   On Media Agent
                    3   -   Use Storage Policy Settings
                    4   -   Off

        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;commonProperties&#39;][&#39;storageDevice&#39;][&#39;softwareCompression&#39;]

    @software_compression.setter
    def software_compression(self, sw_compression_value):
        &#34;&#34;&#34;Updates the software compression property for a subclient.

            Args:
                sw_compression_value  (int)   --  Specifies the software compression method indicated by values below.
                    1   -   On Client
                    2   -   On Media Agent
                    3   -   Use Storage Policy Settings
                    4   -   Off

            Raises:
                SDKException:
                    if failed to update software compression method of subclient

                    if software_compression_value is invalid
        &#34;&#34;&#34;
        if isinstance(sw_compression_value, int) and sw_compression_value in range(1, 5):
            attr_name = &#34;_commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]&#34;
            self._set_subclient_properties(attr_name, sw_compression_value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Invalid value for software compression type.&#39;)

    @property
    def use_vss(self):
        &#34;&#34;&#34;Returns the value of the Use VSS options for Windows FS subclients.

            Returns:    dict

                Dictionary contains the keys &#39;useVSS&#39;, &#39;vssOptions&#39; and &#39;useVssForAllFilesOptions&#39;.

                useVSS:
                    True    -   ENABLED
                    False   -   DISABLED

                vssOptions:
                    1   -   For all files
                    2   -   For locked files only

                useVssForAllFilesOptions:
                    1   -   Fail the job
                    2   -   Continue and reset access time
                    3   -   Continue and do not reset access time

        &#34;&#34;&#34;
        return {&#34;useVSS&#34;: self._fsSubClientProp[&#39;useVSS&#39;],
                &#34;vssOptions&#34;: self._fsSubClientProp[&#39;vssOptions&#39;],
                &#34;useVssForAllFilesOptions&#34;: self._fsSubClientProp[&#39;useVssForAllFilesOptions&#39;]}

    @use_vss.setter
    def use_vss(self, value):
        &#34;&#34;&#34;Updates the value of the Use VSS options for Windows FS subclients.

            Args:
                value  (dict)   --  Specifies the value of the Use VSS options for Windows FS subclients.

                    useVSS:
                        True    -   ENABLED
                        False   -   DISABLED

                    vssOptions:
                        1   -   For all files
                        2   -   For locked files only

                    useVssForAllFilesOptions:
                        1   -   Fail the job
                        2   -   Continue and reset access time
                        3   -   Continue and do not reset access time

        &#34;&#34;&#34;
        fs_subclient_prop = self._fs_subclient_prop

        if isinstance(value[&#39;useVSS&#39;], bool):
            fs_subclient_prop[&#39;useVSS&#39;] = value[&#39;useVSS&#39;]
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(value[&#39;useVssForAllFilesOptions&#39;], int) and value[&#39;useVssForAllFilesOptions&#39;] in range(1, 4):
            fs_subclient_prop[&#39;useVssForAllFilesOptions&#39;] = value[&#39;useVssForAllFilesOptions&#39;]
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(value[&#39;vssOptions&#39;], int) and value[&#39;vssOptions&#39;] in range(1, 3):
            fs_subclient_prop[&#39;vssOptions&#39;] = value[&#39;vssOptions&#39;]
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._set_subclient_properties(&#39;_fs_subclient_prop&#39;, fs_subclient_prop)

    def find_all_versions(self, *args, **kwargs):
        &#34;&#34;&#34;Searches the content of a Subclient.

            Args:
                Dictionary of browse options:
                    Example:
                        find_all_versions({
                            &#39;path&#39;: &#39;c:\\hello&#39;,
                            &#39;show_deleted&#39;: True,
                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,
                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

                    (OR)

                Keyword argument of browse options:
                    Example:
                        find_all_versions(
                            path=&#39;c:\\hello.txt&#39;,
                            show_deleted=True,
                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

                Refer self._default_browse_options for all the supported options

        Returns:
            dict    -   dictionary of the specified file with list of all the file versions and
                            additional metadata retrieved from browse
        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;operation&#39;] = &#39;all_versions&#39;

        return self._backupset_object._do_browse(options)

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               collect_metadata=False,
               on_demand_input=None,
               advanced_options=None,
               schedule_pattern=None,
               common_backup_options=None):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level        (str)   --  level of backup the user wish to run
                        Full / Incremental / Differential / Synthetic_full
                    default: Incremental

                incremental_backup  (bool)  --  run incremental backup
                        only applicable in case of Synthetic_full backup
                    default: False

                incremental_level   (str)   --  run incremental backup before/after synthetic full
                        BEFORE_SYNTH / AFTER_SYNTH

                        only applicable in case of Synthetic_full backup
                    default: BEFORE_SYNTH

                on_demand_input     (str)   --  input directive file location for on
                                                    demand subclient

                        only applicable in case of on demand subclient
                    default: None

                advanced_options    (dict)  --  advanced backup options to be included while
                                                    making the request
                        default: None

                        options:
                            directive_file          :   path to the directive file
                            adhoc_backup            :   if set triggers the adhoc backup job
                            adhoc_backup_contents   :   sets the contents for adhoc backup
                            inline_backup_copy      :   to run backup copy immediately(inline)
                            skip_catalog            :   skip catalog for intellisnap operation
                            start_new_media         :   enables the option to start new media for the job
                            media_agent_name        :   to run backup via this media agent
                            impersonate_gui         :   sets the initiatedFrom property to GUI if True
                            mark_media_full_on_success: boolean (True/False) that marks vols full on successful backup

                common_backup_options      (dict)  --  advanced job options to be included while
                                                        making request

                        default: None

                        options:
                            job_description              :  job description to be set.

                            enable_number_of_retries     :  enables/disables the property, number of retrys.
                                values:
                                    True/False

                            number_of_retries            : total number of retries to be set.

                            enable_total_running_time    :  enables/disables the property, toal running time.
                                values:
                                    True/False

                            total_running_time           :  total run time to be set in (secs)

                            kill_running_job_when_total_running_time_expires    :   enables/disables the property.
                                values:
                                    True/False

                            start_in_suspended_state     :  enables/disables the property.
                                values:
                                    True/False

                            use_default_priority         :  enables/disables the property.
                                values:
                                    True/False

                            priority                     :  three digit number to be set.
                                default: 166

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

            Returns:
                object - instance of the Job class for this backup job if its an immediate Job
                         instance of the Schedule class for the backup job if its a scheduled Job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if on_demand_input is not None:
            if not isinstance(on_demand_input, str):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            if not self.is_on_demand_subclient:
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;On Demand backup is not supported for this subclient&#39;)

            if not advanced_options:
                advanced_options = {}

            advanced_options[&#39;on_demand_input&#39;] = on_demand_input

        if advanced_options or schedule_pattern or common_backup_options:
            request_json = self._backup_json(
                backup_level,
                incremental_backup,
                incremental_level,
                advanced_options,
                schedule_pattern,
                common_backup_options
            )

            backup_service = self._services[&#39;CREATE_TASK&#39;]

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )

        else:
            return super(FileSystemSubclient, self).backup(
                backup_level=backup_level,
                incremental_backup=incremental_backup,
                incremental_level=incremental_level,
                collect_metadata=collect_metadata
            )

        return self._process_backup_response(flag, response)

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options
                    options:
                        all_versions        : if set to True restores all the versions of the
                                                specified file
                        versions            : list of version numbers to be backed up
                        validate_only       : To validate data backed up for restore


                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._backupset_object._instance_object._restore_association = self._subClientEntity

        if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
            fs_options[&#39;destination_appTypeId&#39;] = int(self._client_object.agents.all_agents.get(&#39;file system&#39;, self._client_object.agents.all_agents.get(&#39;windows file system&#39;, self._client_object.agents.all_agents.get(&#39;linux file system&#39;, self._client_object.agents.all_agents.get(&#39;big data apps&#39;, self._client_object.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
            if not fs_options[&#39;destination_appTypeId&#39;]:
                del fs_options[&#39;destination_appTypeId&#39;]

        return super(FileSystemSubclient, self).restore_in_place(
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            advanced_options=advanced_options
        )

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options

                    options:

                        preserve_level          : preserve level option to set in restore

                        proxy_client            : proxy that needed to be used for restore

                        impersonate_user        : Impersonate user options for restore

                        impersonate_password    : Impersonate password option for restore
                        in base64 encoded form

                        all_versions            : if set to True restores all the versions of the
                        specified file

                        versions                : list of version numbers to be backed up

                        media_agent             : Media Agent need to be used for Browse and restore

                        is_vlr_restore          : sets if the restore job is to be triggered as vlr

                        validate_only           : To validate data backed up for restore

                        instant_clone_options   : Options for FS clone found on Command Center, the value must be
                        a dictionary containing the following key value pairs.

                            reservation_time        (int)   --  The amount of time, specified in seconds, that the mounted
                            snapshot needs to be reserved for before it is cleaned up.
                            This is an OPTIONAL key.

                                Default :   3600

                            clone_mount_path        (str)   --  The path to which the snapshot needs to be mounted.
                            This is NOT an optional key.

                            post_clone_script       (str)   --  The script that will run post clone.
                            This is an OPTIONAL key.

                            clone_cleanup_script    (str)   --  The script that will run after clean up.
                            This is an OPTIONAL key.

                        no_of_streams   (int)       -- Number of streams to be used for restore

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._backupset_object._instance_object._restore_association = self._subClientEntity

        if not isinstance(client, (str, Client)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(client, str):
            client = Client(self._commcell_object, client)

        if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
            fs_options[&#39;destination_appTypeId&#39;] = int(client.agents.all_agents.get(&#39;file system&#39;, client.agents.all_agents.get(&#39;windows file system&#39;, client.agents.all_agents.get(&#39;linux file system&#39;, client.agents.all_agents.get(&#39;big data apps&#39;, client.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
            if not fs_options[&#39;destination_appTypeId&#39;]:
                del fs_options[&#39;destination_appTypeId&#39;]

            # check to find whether file level Restore/ Volume level restore for blocklevel.

        if fs_options is not None and fs_options.get(&#39;is_vlr_restore&#39;, False):
            if not (isinstance(paths, list) and
                    isinstance(overwrite, bool) and
                    isinstance(restore_data_and_acl, bool)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            paths = self._filter_paths(paths)

            if paths == []:
                raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

            request_json = self._restore_json(
                client=client,
                paths=paths,
                overwrite=overwrite,
                restore_data_and_acl=restore_data_and_acl,
                copy_precedence=copy_precedence,
                from_time=from_time,
                to_time=to_time,
                destPath=destination_path,
                restore_option=fs_options
            )

            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;].update(
                self._vlr_restore_options_dict)
            destination_options = request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;].get(&#39;destination&#39;, {})
            destination_options[&#39;destPath&#39;] = destination_options.get(&#39;destPath&#39;, [&#39;&#39;])
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;destPath&#39;][0] = \
                destination_path
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;inPlace&#39;] = False

            return self._process_restore_response(request_json)

        else:
            return super(FileSystemSubclient, self).restore_out_of_place(
                client=client,
                destination_path=destination_path,
                paths=paths,
                overwrite=overwrite,
                restore_data_and_acl=restore_data_and_acl,
                copy_precedence=copy_precedence,
                from_time=from_time,
                to_time=to_time,
                fs_options=fs_options,
                schedule_pattern=schedule_pattern,
                advanced_options=advanced_options
            )

    def enable_content_indexing(self, policy_id):
        &#34;&#34;&#34;Enables Content indexing and add the policy associations&#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#39;fsSubClientProp&#39;][&#39;enableContentIndexing&#39;] = True
        update_properties[&#39;fsSubClientProp&#39;][&#39;contentIndexingPolicy&#39;] = int(policy_id)
        self.update_properties(update_properties)

    def disable_content_indexing(self):
        &#34;&#34;&#34;Disables Content indexing and disassociate the CI policy&#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#39;fsSubClientProp&#39;][&#39;enableContentIndexing&#39;] = False
        self.update_properties(update_properties)

    @property
    def catalog_acl(self):
        &#34;&#34;&#34;Gets the catalog acl option

        Returns:
            true  - if catalog acl is enbaled on the subclient

            false - if catalog acl disabled on the subclient
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;catalogACL&#39;]

    @catalog_acl.setter
    def catalog_acl(self, value):
        &#34;&#34;&#34;
        To enable or disable catalog_acl
        Args:

            value   (bool)  -- To enable or disbale catalog acl
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;catalogACL&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def index_server(self):
        &#34;&#34;&#34;Returns the index server client set for the subclient. None if no Index Server is set&#34;&#34;&#34;

        if &#39;indexSettings&#39; not in self._commonProperties:
            return None

        index_settings = self._commonProperties[&#39;indexSettings&#39;]
        index_server = None

        if (&#39;currentIndexServer&#39; in index_settings and
                &#39;clientName&#39; in index_settings[&#39;currentIndexServer&#39;]):
            index_server = index_settings[&#39;currentIndexServer&#39;][&#39;clientName&#39;]

        if index_server is None:
            return None

        return Client(self._commcell_object, client_name=index_server)

    @index_server.setter
    def index_server(self, value):
        &#34;&#34;&#34;Sets the index server client for the backupset

            Args:
                value   (object)    --  The index server client object to set

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(value, Client):
            raise SDKException(&#39;Subclient&#39;, &#39;121&#39;)

        index_server_name = value.client_name

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]&#34;,
            index_server_name)

    @property
    def index_pruning_type(self):
        &#34;&#34;&#34;Treats the subclient pruning type as a read-only attribute.&#34;&#34;&#34;

        index_settings = self._commonProperties[&#39;indexSettings&#39;]
        if &#39;indexPruningType&#39; in index_settings:
            pruning_type = index_settings[&#39;indexPruningType&#39;]
            return pruning_type

    @property
    def index_pruning_days_retention(self):
        &#34;&#34;&#34;Returns number of days to be maintained in index by index pruning for the subclient&#34;&#34;&#34;

        return self._commonProperties[&#34;indexSettings&#34;][&#34;indexRetDays&#34;]

    @property
    def index_pruning_cycles_retention(self):
        &#34;&#34;&#34;Returns number of cycles to be maintained in index by index pruning for the subclient&#34;&#34;&#34;

        return self._commonProperties[&#34;indexSettings&#34;][&#34;indexRetCycle&#34;]

    @index_pruning_type.setter
    def index_pruning_type(self, value):
        &#34;&#34;&#34;Updates the pruning type for the subclient when subclient level indexing is enabled.
        Can be days based pruning or cycles based pruning.
        Days based pruning will set index retention on the basis of days,
        cycles based pruning will set index retention on basis of cycles.

        Args:
            value    (str)  --  &#34;days_based&#34; or &#34;cycles_based&#34;

        &#34;&#34;&#34;

        if value.lower() == &#34;cycles_based&#34;:
            final_value = 1

        elif value.lower() == &#34;days_based&#34;:
            final_value = 2

        elif value.lower() == &#34;infinite&#34;:
            final_value = 0

        else:
            raise SDKException(&#39;Subclient&#39;, &#39;119&#39;)

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;indexSettings&#39;][&#39;indexPruningType&#39;]&#34;, final_value)

    @index_pruning_days_retention.setter
    def index_pruning_days_retention(self, value):
        &#34;&#34;&#34;Sets index pruning days value at subclient level for days-based index pruning&#34;&#34;&#34;

        if isinstance(value, int) and value &gt;= 2:
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;indexSettings&#39;][&#39;indexRetDays&#39;]&#34;, value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;120&#39;)

    @index_pruning_cycles_retention.setter
    def index_pruning_cycles_retention(self, value):
        &#34;&#34;&#34;Sets index pruning cycles value at subclient level for cycles-based index pruning&#34;&#34;&#34;

        if isinstance(value, int) and value &gt; 0:
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;indexSettings&#39;][&#39;indexRetCycle&#39;]&#34;, value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;120&#39;)

    @property
    def ibmi_dr_config(self):
        &#34;&#34;&#34;
        Return the ibmi dr configuration

        Returns:
            (dict)  --  Dictionary of DR parameters
        &#34;&#34;&#34;
        return {
            &#39;backupMaxTime&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;backupMaxTime&#39;, 0),
            &#39;printSysInfo&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;printSysInfo&#39;, False),
            &#39;userProgram&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;userProgram&#39;, &#39;&#39;),
            &#39;saveSecData&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;saveSecData&#39;, False),
            &#39;saveConfObject&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;saveConfObject&#39;, False)
        }

    @ibmi_dr_config.setter
    def ibmi_dr_config(self, dr_config):
        &#34;&#34;&#34;
            Sets the subclient into one touch mode and adds ibmi DR parameters

            Args:
                dr_config   (dict)  --  Dictionary of IBMi DR parameters
                
                Example:
                    ibmi_dr_config = {
                        &#39;backupMaxTime&#39; : 120,
                        &#39;printSysInfo&#39; : True,
                        &#39;userProgram&#39; : &#39;QSYS/CVPGM&#39;,
                        &#39;saveSecData&#39; : True,
                        &#39;saveConfObject&#39; : False,
                        &#39;library&#39; : [{path:&#39;/QSYS.LIB/TEST1.LIB&#39;}]
                    }
            Returns:
                None

            Raises:
                SDKException:
                    if parameters are not valid
        &#34;&#34;&#34;
        self.onetouch_option = True
        update_properties = self.properties
        if isinstance(dr_config, dict):
            update_properties[&#39;fsSubClientProp&#39;][&#39;ibmiSubclientprop&#39;]= dr_config
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;The parameter should be a dictionary.&#39;)
        self.update_properties(update_properties)

    @property
    def backup_savf_file_data(self):
        &#34;&#34;&#34;
         Return the ibmi savf file data configuration

        Returns:
            (bool)  --  Is savf file data going to be backed up
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;backupSaveFileData&#39;, False)

    @backup_savf_file_data.setter
    def backup_savf_file_data(self, value):
        &#34;&#34;&#34;
        Sets the backup save file data property on an ibmi subclient
        Args:
                value   (boolean)  --  Toggle the backup property

            Returns:
                None

            Raises:
                None
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupSaveFileData&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def backup_spool_file_data(self):
        &#34;&#34;&#34;Gets the value of spool file data on ibmi option for IBMi subclient.

            Returns:
                False   -   if spool file data on IBMi is disabled on the subclient

                True    -   if spool file data on IBMi is enabled on the subclient
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;backupSpooledFileData&#39;))

    @backup_spool_file_data.setter
    def backup_spool_file_data(self, value):
        &#34;&#34;&#34;Updates the generate signature property value on ibmi subclient.

            Args:
                value   (bool)  --  To enable or disable spool file data backup.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupSpooledFileData&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def backup_queue_data(self):
        &#34;&#34;&#34;Gets the value of queue data data on ibmi option for IBMi subclient.

            Returns:
                False   -   if queue data on IBMi is disabled on the subclient

                True    -   if queue data on IBMi is enabled on the subclient
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;backupQueueData&#39;))

    @backup_queue_data.setter
    def backup_queue_data(self, value):
        &#34;&#34;&#34;Updates the queue data property value on ibmi subclient.

            Args:
                value   (bool)  --  To enable or disable queue data backup.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupQueueData&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def backup_private_authorities(self):
        &#34;&#34;&#34;Gets the value of private authorities on ibmi option for IBMi subclient.

            Returns:
                False   -   if PVTAUT on IBMi is disabled on the subclient

                True    -   if PVTAUT on IBMi is enabled on the subclient
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;backupPrivateAuthority&#39;))

    @backup_private_authorities.setter
    def backup_private_authorities(self, value):
        &#34;&#34;&#34;Updates the private authorities property value on ibmi subclient.

            Args:
                value   (bool)  --  To enable or disable private authorities backup.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupPrivateAuthority&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def target_release(self):
        &#34;&#34;&#34;Gets the value of target and release on ibmi option for IBMi subclient.

            Returns:
                (str)   -   Return the target and release string value
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;targetReleaseForBackupData&#39;))

    @target_release.setter
    def target_release(self, value):
        &#34;&#34;&#34;Updates the private authorities property value on ibmi subclient.

            Args:
                value   (str)  --  To set target and release for  backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;targetReleaseForBackupData&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def save_access_path(self):
        &#34;&#34;&#34;Gets the value of save access path on ibmi option for IBMi subclient.

            Returns:
                (str)   -   Return the save access path string value
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;saveAccessPath&#39;))

    @save_access_path.setter
    def save_access_path(self, value):
        &#34;&#34;&#34;Updates the save access path property value on ibmi subclient.

            Args:
                value   (str)  --  To set access path value for  backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;saveAccessPath&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def update_history(self):
        &#34;&#34;&#34;Gets the value of update history property on ibmi option for IBMi subclient.

            Returns:
                (str)   -   Return the string value of update history property
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;updateHistory&#39;))

    @update_history.setter
    def update_history(self, value):
        &#34;&#34;&#34;Updates the update history property value on ibmi subclient.

            Args:
                value   (str)  --  To set update history value for  backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;updateHistory&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def ibmi_compression(self):
        &#34;&#34;&#34;Gets the value of IBMi compression property on ibmi option for IBMi subclient.

            Returns:
                (str)   -   Return the string value of IBMi compression property
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;ibmiCompression&#39;))

    @ibmi_compression.setter
    def ibmi_compression(self, value):
        &#34;&#34;&#34;Updates the IBMi compression property value on ibmi subclient.

            Args:
                value   (str)  --  To set IBMi compression value for  backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;ibmiCompression&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def save_while_active_option(self):
        &#34;&#34;&#34;
        Return the save while active options for an IBMi subclient.

        Returns:
             (dict) --  Dictionary of save while active options
        &#34;&#34;&#34;
        return {
            &#39;saveWhileActiveOpt&#39;: self._fsSubClientProp[&#39;saveWhileActiveOpt&#39;],
            &#39;activeWaitTime&#39;: self._fsSubClientProp[&#39;activeWaitTime&#39;]
        }

    @save_while_active_option.setter
    def save_while_active_option(self, swa_config):
        &#34;&#34;&#34;
        Updates the save while active backup property for an IBMi subclient.

            Args:
                swa_config      (dict)  -- Dictionary of synclib config options

                    options                 --

                        saveWhileActiveOpt      (str)   --  Value of save while active option.

                        activeWaitTime          (int)   --  Amount of time to wait for check point.

        Returns:
            None

        Raises:
            SDKException:
                if failed to update the property of the subclient

                if value is invalid
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(swa_config, dict):
            update_properties[&#39;fsSubClientProp&#39;] = swa_config
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;SWA should be a dictionary.&#39;)
        self.update_properties(update_properties)

    @property
    def pre_post_commands(self):
        &#34;&#34;&#34;
         Return the prep_post commands set for a subclient

        Returns:
            (dict)  --  All the pre/post commands
        &#34;&#34;&#34;
        pre_scan_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;preScanCommand&#34;]
        post_scan_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;postScanCommand&#34;]
        pre_backup_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;preBackupCommand&#34;]
        post_backup_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;postBackupCommand&#34;]

        pre_post_commands = {&#39;pre_scan_command&#39;: pre_scan_command, &#39;post_scan_command&#39;: post_scan_command, &#39;pre_backup_command&#39;: pre_backup_command, &#39;post_backup_command&#39;: post_backup_command}

        return pre_post_commands

    @pre_post_commands.setter
    def pre_post_commands(self, value):
        &#34;&#34;&#34;
        Sets the pre post commands on a subclient
        Args:

            value   (dict)      --  Specifies the pre.post commands to be set

                pre_scan_command    (str)       --     The pre scan command to be set

                post_scan_command   (str)       --     The post scan command to be set

                pre_backup_command  (str)       --     The pre backup command to be set

                post_backup_command (str)       --     The post backup command to be set

            Returns:
                None

            Raises:
                None
        &#34;&#34;&#34;
        if isinstance(value, dict):

            pre_post_process = self._commonProperties[&#34;prepostProcess&#34;]

            pre_post_process[&#34;preScanCommand&#34;] = value.get(&#34;pre_scan_command&#34;, &#34;&#34;)
            pre_post_process[&#34;postScanCommand&#34;] = value.get(&#34;post_scan_command&#34;, &#34;&#34;)
            pre_post_process[&#34;preBackupCommand&#34;] = value.get(&#34;pre_backup_command&#34;, &#34;&#34;)
            pre_post_process[&#34;postBackupCommand&#34;] = value.get(&#34;post_backup_command&#34;, &#34;&#34;)

            self._set_subclient_properties(&#39;_commonProperties[&#34;prepostProcess&#34;]&#39;, pre_post_process)

        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    @property
    def backup_nodes(self):
        &#34;&#34;&#34;
        Gets the backup nodes for FS Agent under Network Share Clients.
        &#34;&#34;&#34;
        return self._fsSubClientProp[&#34;backupConfiguration&#34;][&#34;backupDataAccessNodes&#34;]

    @backup_nodes.setter
    def backup_nodes(self, value):
        &#34;&#34;&#34;
        Sets the backup nodes for FS Agent under Network Share Clients.

            Args:
                value  (list)   --  Specifies the nodes, a list of strings, values are data access node host names.
        &#34;&#34;&#34;

        update_properties = self.properties

        access_nodes = []
        for access_node in value:
            access_nodes.append({&#34;clientName&#34;: access_node})

        update_properties[&#34;fsSubClientProp&#34;][&#34;backupConfiguration&#34;] = {&#34;backupDataAccessNodes&#34;: access_nodes}
        self.update_properties(update_properties)

    @property
    def network_share_auto_mount(self):
        &#34;&#34;&#34;
        Returns the value of enableNetworkShareAutoMount, if true, the content will be auto-mounted during backup and
        auto-mounted during in-place restores.
        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;enableNetworkShareAutoMount&#39;]

    @network_share_auto_mount.setter
    def network_share_auto_mount(self, value):
        &#34;&#34;&#34;
        Sets the value for enableNetworkShareAutoMount, needs to set to true if content is specified
        in the file_server:/path format.

            Args:
                value   (bool)  --  Enables or disables the property by setting True or False respectively.

        &#34;&#34;&#34;
        update_properties = self.properties

        if isinstance(value, bool):
            update_properties[&#34;fsSubClientProp&#34;][&#39;enableNetworkShareAutoMount&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self.update_properties(update_properties)

    @property
    def impersonate_user(self):
        &#34;&#34;&#34;
        Returns the username ONLY and applicable to Windows FS subclients only.
        &#34;&#34;&#34;
        return self._subclient_properties[&#39;impersonateUser&#39;]

    @impersonate_user.setter
    def impersonate_user(self, value):
        &#34;&#34;&#34;
        Sets the user impersonation information for a subclient, applicable to Windows only.

        Args:

            value   (dict)  --  Valid keys are &#39;username&#39; and &#39;password&#39;

                username    (str)   --  The username

                password    (str)   --  The password
        &#34;&#34;&#34;

        update_properties = self.properties

        if isinstance(value, dict):
            update_properties[&#34;impersonateUser&#34;][&#34;userName&#34;] = value[&#34;username&#34;]
            update_properties[&#34;impersonateUser&#34;][&#34;password&#34;] = b64encode(value[&#34;password&#34;].encode()).decode()

        self.update_properties(update_properties)

    @property
    def plan(self):
        &#34;&#34;&#34;Returns the name of the plan associated with the subclient.
           Returns None if no plan is associated
        &#34;&#34;&#34;
        return super().plan

    @plan.setter
    def plan(self, value):
        &#34;&#34;&#34;Associates a plan to the subclient.

            Args:
                value   (object)    --  the Plan object which is to be associated
                                        with the subclient

                value   (str)       --  name of the plan to be associated
                
                value   (list)      --  associate subclient with Plan and override the content of Plan on subclient
                                        Example-1 ( with Plan name ): subclient_object.plan = [&#34;plan_name&#34;,[&#34;content_path1&#34;,&#34;content_path2&#34;]]
                                        Example-2 ( with Plan Object ): subclient_object.plan = [plan_object,[&#34;content_path1&#34;,&#34;content_path2&#34;]]

                value   (None)      --  set value to None to remove plan associations
                

            Raises:
                SDKException:
                    if the type of input is incorrect

                    if the plan association is unsuccessful
        &#34;&#34;&#34;
        
        from ..plan import Plan
        if isinstance(value, Plan):
            if self._commcell_object.plans.has_plan(value.plan_name):
                self.update_properties({
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value.plan_name
                    },
                    &#34;useContentFromPlan&#34;: True
                })
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Plan does not exist&#39;)
                
        elif isinstance(value, str):
            if self._commcell_object.plans.has_plan(value):
                self.update_properties({
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value
                    },
                    &#34;useContentFromPlan&#34;: True
                })
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Plan does not exist&#39;)
                
        elif isinstance(value, list):
            if not isinstance(value[0], Plan) and not isinstance(value[0], str):
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Expecting Plan object or str&#39;)
            if not isinstance(value[1], list):
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Expecting List of contents&#39;)
           
            self.update_properties({
                     &#34;content&#34;: [{&#34;path&#34;:p} for p in value[1]],
                    &#34;useContentFromPlan&#34;: False,
                    &#34;fsContentOperationType&#34;: 1,
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value[0].plan_name if isinstance(value[0], Plan) else value[0]
                    }
                })
                
        elif value is None:
            self.update_properties({
                &#39;removePlanAssociation&#39;: True
            })
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    def preview_backedup_file(self, file_path):
        &#34;&#34;&#34;Gets the preview content for the subclient.
            Params:
                file_path (str) --  file path to get the preview content

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self._get_preview(file_path)

    def add_comparison(self, name, source_backup_time, compare_with_time):
        &#34;&#34;&#34;Adds a comparison job for the subclient

            Args:
                name                (str)       --      The name of the comparison

                source_backup_time  (int)       --      The epoch time of the source job&#39;s backup time. Ex: 1743167209

                compare_with_time   (int)       --      The epoch time of the other job to compare.

            Returns:
                object - Instance of the Job class for the comparison job.

        &#34;&#34;&#34;

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;clientName&#34;: self._client_object._client_name,
                        &#34;appName&#34;: self._agent_object._agent_name,
                        &#34;backupsetName&#34;: self._backupset_object._backupset_name,
                        &#34;subclientName&#34;: self._subclient_name,
                        &#34;_type_&#34;: 7
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4025
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {},
                            &#34;adComparisonOption&#34;: {
                                &#34;leftSetTime&#34;: int(source_backup_time),
                                &#34;rightSetTime&#34;: int(compare_with_time),
                                &#34;adCompareType&#34;: 0,
                                &#34;nodeClientId&#34;: int(self._client_object.client_id),
                                &#34;clientId&#34;: int(self._client_object.client_id),
                                &#34;comparisonName&#34;: name,
                                &#34;appTypeId&#34;: 33,
                                &#34;subClientId&#34;: int(self.subclient_id),
                                &#34;adComparisonJobType&#34;: 0,
                                &#34;status&#34;: 0
                            }
                        }
                    }
                ]
            }
        }

        comparison_task = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, comparison_task, request_json)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Failed to start the comparison job.\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, &#39;Failed to start the comparison job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient"><code class="flex name class">
<span>class <span class="ident">FileSystemSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Subclient Base class, representing a file system subclient,
and to perform operations on that subclient.</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L200-L2801" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class FileSystemSubclient(Subclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a file system subclient,
        and to perform operations on that subclient.
    &#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of File System subclient.

        &#34;&#34;&#34;
        super(FileSystemSubclient, self)._get_subclient_properties()
        self._impersonateUser={}
        if &#39;impersonateUser&#39; in self._subclient_properties:
            self._impersonateUser = self._subclient_properties[&#39;impersonateUser&#39;]

        if &#39;fsSubClientProp&#39; in self._subclient_properties:
            self._fsSubClientProp = self._subclient_properties[&#39;fsSubClientProp&#39;]

        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

        self._global_filter_status_dict = {
            &#39;OFF&#39;: 0,
            &#39;ON&#39;: 1,
            &#39;USE CELL LEVEL POLICY&#39;: 2
        }

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;impersonateUser&#34;: self._impersonateUser,
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;fsSubClientProp&#34;: self._fsSubClientProp,

                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;fsContentOperationType&#34;: &#34;OVERWRITE&#34;,
                    &#34;fsExcludeFilterOperationType&#34;: &#34;OVERWRITE&#34; if not hasattr(self, &#39;_fsExcludeFilterOperationType&#39;) else self._fsExcludeFilterOperationType,
                    &#34;fsIncludeFilterOperationType&#34;: &#34;OVERWRITE&#34; if not hasattr(self, &#39;_fsIncludeFilterOperationType&#39;) else self._fsIncludeFilterOperationType
                }
        }

        if &#39;isDDBSubclient&#39; in self._fs_subclient_prop:
            if self._fs_subclient_prop[&#39;isDDBSubclient&#39;]:
                del subclient_json[&#34;subClientProperties&#34;][&#34;content&#34;]
        return subclient_json

    @property
    def _fs_subclient_prop(self):
        &#34;&#34;&#34;Returns the JSON for the fsSubclientProp tag in the Subclient Properties JSON&#34;&#34;&#34;
        return self._fsSubClientProp

    @_fs_subclient_prop.setter
    def _fs_subclient_prop(self, value):
        &#34;&#34;&#34;Update the values of fsSubclientProp JSON.

            Args:
                value   (dict)  --  dictionary consisting of the JSON attribute as the key
                and the new data as its value

            Raises:
                SDKException:
                    if value is not of type dict

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        _nested_dict(self._fsSubClientProp, value)

        if &#39;enableOnePass&#39; in self._fsSubClientProp:
            del self._fsSubClientProp[&#39;enableOnePass&#39;]

        if &#39;isTurboSubclient&#39; in self._commonProperties:
            del self._commonProperties[&#39;isTurboSubclient&#39;]

    def _set_content(self,
                     content=None,
                     filter_content=None,
                     exception_content=None):
        &#34;&#34;&#34;Sets the subclient content / filter / exception content

            Args:
                content             (list)      --  list of subclient content

                filter_content      (list)      --  list of filter content

                exception_content   (list)      --  list of exception content
        &#34;&#34;&#34;
        if content is None:
            content = self.content

        if filter_content is None:
            filter_content = self.filter_content

        if exception_content is None:
            exception_content = self.exception_content

        update_content = []
        for path in content:
            file_system_dict = {
                &#34;path&#34;: path
            }
            update_content.append(file_system_dict)

        for path in filter_content:
            filter_dict = {
                &#34;excludePath&#34;: path
            }
            update_content.append(filter_dict)

        for path in exception_content:
            exception_dict = {
                &#34;includePath&#34;: path
            }
            update_content.append(exception_dict)

        self._set_subclient_properties(&#34;_content&#34;, update_content)
        self._fsExcludeFilterOperationType = &#34;OVERWRITE&#34;  # RESET THE OPERATION TYPE TO ITS DEFAULT
        self._fsIncludeFilterOperationType = &#34;OVERWRITE&#34;  # RESET THE OPERATION TYPE TO ITS DEFAULT


    def _common_backup_options(self, options):
        &#34;&#34;&#34;
         Generates the advanced job options dict

            Args:
                options     (dict)  --  advanced job options that are to be included
                                            in the request

            Returns:
                (dict)  -   generated advanced options dict
        &#34;&#34;&#34;
        final_dict = super(FileSystemSubclient, self)._common_backup_options(options)

        common_options = {
            &#34;jobDescription&#34;: options.get(&#39;job_description&#39;, &#34;&#34;),
            &#34;jobRetryOpts&#34;: {
                &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: options.get(
                    &#39;kill_running_job_when_total_running_time_expires&#39;, False),
                &#34;numberOfRetries&#34;: options.get(&#39;number_of_retries&#39;, 0),
                &#34;enableNumberOfRetries&#34;: options.get(&#39;enable_number_of_retries&#39;, False),
                &#34;runningTime&#34;: {
                    &#34;enableTotalRunningTime&#34;: options.get(&#39;enable_total_running_time&#39;, False),
                    &#34;totalRunningTime&#34;: options.get(&#39;total_running_time&#39;, 3600)
                }
            },
            &#34;startUpOpts&#34;: {
                &#34;startInSuspendedState&#34;: options.get(&#39;start_in_suspended_state&#39;, False),
                &#34;useDefaultPriority&#34;: options.get(&#39;use_default_priority&#39;, True),
                &#34;priority&#34;: options.get(&#39;priority&#39;, 166)
            }
        }

        return common_options

    def _advanced_backup_options(self, options):
        &#34;&#34;&#34;Generates the advanced backup options dict

            Args:
                options     (dict)  --  advanced backup options that are to be included
                                            in the request

            Returns:
                (dict)  -   generated advanced options dict
        &#34;&#34;&#34;
        final_dict = super(FileSystemSubclient, self)._advanced_backup_options(options)

        if &#39;on_demand_input&#39; in options and options[&#39;on_demand_input&#39;] is not None:
            final_dict[&#39;onDemandInputFile&#39;] = options[&#39;on_demand_input&#39;]

        if &#39;directive_file&#39; in options and options[&#39;directive_file&#39;] is not None:
            final_dict[&#39;onDemandInputFile&#39;] = options[&#39;directive_file&#39;]

        if &#39;adhoc_backup&#39; in options and options[&#39;adhoc_backup&#39;] is not None:
            final_dict[&#39;adHocBackup&#39;] = options[&#39;adhoc_backup&#39;]

        if &#39;inline_bkp_cpy&#39; in options or &#39;skip_catalog&#39; in options:
            final_dict[&#39;dataOpt&#39;] = {
                &#39;createBackupCopyImmediately&#39;: options.get(&#39;inline_bkp_cpy&#39;, False),
                &#39;skipCatalogPhaseForSnapBackup&#39;: options.get(&#39;skip_catalog&#39;, False)}

        if &#39;adhoc_backup_contents&#39; in options and options[&#39;adhoc_backup_contents&#39;] is not None:
            if not isinstance(options[&#39;adhoc_backup_contents&#39;], list):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            final_dict[&#39;adHocBkpContents&#39;] = {
                &#39;selectedAdHocPaths&#39;: options[&#39;adhoc_backup_contents&#39;]
            }

        if &#39;use_multi_stream&#39; in options and options[&#39;use_multi_stream&#39;]:

            multi_stream_opts = {
                &#39;useMultiStream&#39;: options.get(&#39;use_multi_stream&#39;, False),
                &#39;useMaximumStreams&#39;: options.get(&#39;use_maximum_streams&#39;, True),
                &#39;maxNumberOfStreams&#39;: options.get(&#39;max_number_of_streams&#39;, 1)
            }

            if &#39;dataOpt&#39; in final_dict and isinstance(final_dict[&#39;dataOpt&#39;], dict):
                final_dict[&#39;dataOpt&#39;].update(multi_stream_opts)
            else:
                final_dict[&#39;dataOpt&#39;] = multi_stream_opts

        if &#39;start_new_media&#39; in options and options[&#39;start_new_media&#39;]:

            media_opts = {
                &#39;startNewMedia&#39;: options.get(&#39;start_new_media&#39;, False)
            }

            if &#39;mediaOpt&#39; in final_dict and isinstance(final_dict[&#39;mediaOpt&#39;], dict):
                final_dict[&#39;mediaOpt&#39;].update(media_opts)
            else:
                final_dict[&#39;mediaOpt&#39;] = media_opts
        
        if options.get(&#39;media_agent_name&#39;):
            media_agent_name = options[&#39;media_agent_name&#39;]
            if not isinstance(media_agent_name, str):
                message = f&#34;media_agent_name: Expected str, received {type(media_agent_name)}&#34;
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, message)
            final_dict[&#39;dataPathOpt&#39;] = {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentName&#34;: media_agent_name
                }
            }

        if &#39;mark_media_full_on_success&#39; in options and options[&#39;mark_media_full_on_success&#39;]:

            media_opts = {
                &#39;markMediaFullOnSuccess&#39;: options.get(&#39;mark_media_full_on_success&#39;, False)
            }

            if &#39;mediaOpt&#39; in final_dict and isinstance(final_dict[&#39;mediaOpt&#39;], dict):
                final_dict[&#39;mediaOpt&#39;].update(media_opts)
            else:
                final_dict[&#39;mediaOpt&#39;] = media_opts

        return final_dict

    @property
    def _vlr_restore_options_dict(self):
        &#34;&#34;&#34; Constructs volume level Restore Dictionary&#34;&#34;&#34;

        physical_volume = &#39;PHYSICAL_VOLUME&#39;
        vlr_options_dict = {
            &#34;volumeRstOption&#34;: {
                &#34;volumeLeveRestore&#34;: True,
                &#34;volumeLevelRestoreType&#34;: physical_volume
            },
            &#34;virtualServerRstOption&#34;: {
                &#34;isDiskBrowse&#34;: False,
                &#34;isVolumeBrowse&#34;: True,
                &#34;isBlockLevelReplication&#34;: False
            }
        }
        return vlr_options_dict

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;
        content = []

        for path in self._content:
            if &#39;path&#39; in path:
                content.append(path[&#34;path&#34;])

        return content

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update content of a
            File System Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        if isinstance(subclient_content, list) and subclient_content != []:
            self._set_content(content=subclient_content)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Subclient content should be a list value and not empty&#39;)

    @property
    def filter_content(self):
        &#34;&#34;&#34;Treats the subclient filter content as a property of the Subclient class.&#34;&#34;&#34;
        _filter_content = []

        for path in self._content:
            if &#39;excludePath&#39; in path:
                _filter_content.append(path[&#34;excludePath&#34;])

        return _filter_content

    @filter_content.setter
    def filter_content(self, value):
        &#34;&#34;&#34;Sets the filter content of the subclient as the value provided as input.
            An empty list will clear all filters.

            example: [&#39;*book*&#39;, &#39;file**&#39;]

            Raises:
                SDKException:
                    if failed to update filter content of subclient

                    if the type of value input is not list

        &#34;&#34;&#34;
        if isinstance(value, list):
            if value == []:
                value = self.filter_content
                self._fsExcludeFilterOperationType = &#34;DELETE&#34;
            self._set_content(filter_content=value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Subclient filter content should be a list value&#39;)

    @property
    def exception_content(self):
        &#34;&#34;&#34;Treats the subclient exception content as a property of the Subclient class.&#34;&#34;&#34;
        _exception_content = []

        for path in self._content:
            if &#39;includePath&#39; in path:
                _exception_content.append(path[&#34;includePath&#34;])

        return _exception_content

    @exception_content.setter
    def exception_content(self, value):
        &#34;&#34;&#34;Sets the exception content of the subclient as the value provided as input.

            example: [&#39;*book*&#39;, &#39;file**&#39;]

            Raises:
                SDKException:
                    if failed to update exception content of subclient

                    if the type of value input is not list

                    if value list is empty
        &#34;&#34;&#34;
        if isinstance(value, list):
            if value == []:
                value = self.exception_content
                self._fsIncludeFilterOperationType = &#34;DELETE&#34;
            self._set_content(exception_content=value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;Subclient exception content should be a list value and not empty&#39;)

    @property
    def scan_type(self):
        &#34;&#34;&#34;Gets the appropriate scan type for this Subclient

            Returns:
                int
                    1   -   Recursive Scan
                    2   -   Optimized Scan
                    3   -   Change Journal Scan

        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;scanOption&#39;]

    @scan_type.setter
    def scan_type(self, scan_type_value):
        &#34;&#34;&#34;Creates the JSON with the specified scan type to pass to the API
            to update the scan type of this File System Subclient.

            Args:
                scan_type_value     (int)   --  scan type value as indicated below

                    1   -   Recursive Scan
                    2   -   Optimized Scan
                    3   -   Change Journal Scan

            Raises:
                SDKException:
                    if failed to update scan type of subclient

                    if scan_type_value is invalid

        &#34;&#34;&#34;
        if isinstance(scan_type_value, int) and scan_type_value &gt;= 1 and scan_type_value &lt;= 3:
            self._set_subclient_properties(&#34;_fsSubClientProp[&#39;scanOption&#39;]&#34;, scan_type_value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Invalid scan type&#39;)

    @property
    def trueup_option(self):
        &#34;&#34;&#34;Gets the value of TrueUp Option

            Returns:
                True    -   if trueup is enabled on the subclient

                False   -   if trueup is not enabled on the subclient

        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;isTrueUpOptionEnabledForFS&#39;]

    @trueup_option.setter
    def trueup_option(self, trueup_option_value):
        &#34;&#34;&#34;Creates the JSON with the specified scan type to pass to the API
            to update the scan type of this File System Subclient.

            Args:
                trueup_option_value (bool)  --  Specifies to enable or disable trueup
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;isTrueUpOptionEnabledForFS&#39;]&#34;,
            trueup_option_value
        )

    def run_backup_copy(self):
        &#34;&#34;&#34;
        Runs the backup copy from Commcell for the given subclient

        Args:
                None

        Returns:
                object - instance of the Job class for this backup copy job
        Raises:
            SDKException:

                    if backup copy job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;clientName&#34;: self._client_object._client_name,
                        &#34;subclientName&#34;: self._subclient_name,
                        &#34;backupsetName&#34;: self._backupset_object._backupset_name,
                        &#34;storagePolicyName&#34;: self.storage_policy,
                        &#34;_type_&#34;: 17,
                        &#34;appName&#34;: self._agent_object._agent_name
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskId&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4028
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;snapToTapeOption&#34;: {
                                    &#34;allowMaximum&#34;: True,
                                    &#34;noofJobsToRun&#34;: 1
                                }
                            }
                        }
                    }
                ]
            }
        }

        backup_copy = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_copy, request_json)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Backup copy job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, &#39;Failed to run the backup copy job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def backup_retention(self):
        &#34;&#34;&#34;return if backup retention is enabled or not

        Returns:
                True    -   if backup_retention is enabled for the subclient

                False   -   if backup_rentention is not enabled for the subclient

        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;backupRetention&#39;]

    @backup_retention.setter
    def backup_retention(self, value):
        &#34;&#34;&#34;Creates the JSON with the specified Boolean variable to pass to the API
            to update the backup_retention of this File System Subclient

        Args:
             value   (bool)  --  To enable or disable backup_retention.

        &#34;&#34;&#34;

        if isinstance(value, bool):

            if value:
                new_value = {
                    &#39;extendStoragePolicyRetention&#39;: True,
                    &#39;backupRetention&#39;: True}
            else:
                new_value = {&#39;backupRetention&#39;: False}
            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be boolean&#39;)

    @property
    def block_level_backup_option(self):
        &#34;&#34;&#34;Gets the block level option

            Returns:
                true - if blocklevel is enabled on the subclient
                false - if blocklevel is not enabled on the subclient
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;blockLevelBackup&#39;]

    @block_level_backup_option.setter
    def block_level_backup_option(self, block_level_backup_value):
        &#34;&#34;&#34;Creates the JSON with the specified blocklevel flag
            to pass to the API to update the blocklevel of this
            File System Subclient.

            Args:
                block_level_backup_value (bool)  --  Specifies to enable or disable blocklevel option
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;blockLevelBackup&#39;]&#34;,
            block_level_backup_value)


    @property
    def _dc_options_dict(self):
        &#34;&#34;&#34; Constructs Data classification Property&#34;&#34;&#34;
        dc_options = {&#39;dcPlanEntity&#39;: {&#39;planType&#39;: 7, &#39;planSubtype&#39;: 117506053, &#39;planName&#39;: &#39;&#39;}}
        return dc_options

    def enable_dc_content_indexing(self, dcplan_name):
        &#34;&#34;&#34;Creates the JSON with the specified dataclassification plan to pass to API to
            update  file system Subclient

            Args:
                dcplan_name (String)  --  DC plan name

        &#34;&#34;&#34;
        temp_dc = self._dc_options_dict
        temp_dc[&#39;dcPlanEntity&#39;][&#39;planName&#39;] = dcplan_name
        self.update_properties(temp_dc)

    @property
    def create_file_level_index_option(self):
        &#34;&#34;&#34;Gets the value of Metadata collection Option

            Returns:
                true - if metadata collection is enabled on the subclient
                false - if metadata collection is not enabled on the subclient
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;createFileLevelIndexDuringBackup&#39;]

    @create_file_level_index_option.setter
    def create_file_level_index_option(self, create_file_level_index_value):
        &#34;&#34;&#34;Creates the JSON with the specified scan type
            to pass to the API to update the Metadata collection of this
            File System Subclient.

            Args:
                create_file_level_index_value (bool)  --  Specifies to enable or disable metadata collection
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;createFileLevelIndexDuringBackup&#39;]&#34;,
            create_file_level_index_value)

    @property
    def backup_retention_days(self):
        &#34;&#34;&#34;return number of days for backup retention

        Returns:
                        (int)

        &#34;&#34;&#34;

        # For Indexing V2 clients
        if &#39;afterDeletionKeepItemsForNDays&#39; in self._fsSubClientProp:
            return self._fsSubClientProp[&#39;afterDeletionKeepItemsForNDays&#39;]

        # For Indexing V1 clients
        else:
            return self._fsSubClientProp.get(&#39;daysToKeepItems&#39;, 0)

    @backup_retention_days.setter
    def backup_retention_days(self, value):
        &#34;&#34;&#34;Creates the JSON with the specified backup_retention days to pass to the API
            to update the retention for deleted item of this File System Subclient

        Args:
                value   (int)  --  To set extended retention days for deleted items

                The value will be converted in years , months and days form on GUI.

                To set infinite ,value should be -1

        Raises:
                SDKException:
                    if failed to update days for deleted item retention for the subclient

                    if value is invalid

        &#34;&#34;&#34;

        if isinstance(value, int):
            if &#39;afterDeletionKeepItemsForNDays&#39; in self._fsSubClientProp:
                if value != -1:
                    new_value = {
                        &#39;afterDeletionKeepItemsForNDays&#39;: value,
                        &#39;backupRetentionMode&#39;: 1
                    }
                else:
                    new_value = {&#39;afterDeletionKeepItemsForNDays&#39;: value}
            else:
                new_value = {
                    &#39;daysToKeepItems&#39;: value
                }

            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)

        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be boolean&#39;)

    @property
    def system_state_option(self):
        &#34;&#34;&#34;Checks whether the system state option is enabled

        Returns:
            True    -   if system state property is enabled for the subclient

            False   -   if system state property is not enabled for the subclient
        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;backupSystemState&#39;]

    @system_state_option.setter
    def system_state_option(self, backup_system_state):
        &#34;&#34;&#34;
        Enables the system state property for the subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;backupSystemState&#39;]&#34;,
            backup_system_state)

    @property
    def onetouch_option(self):
        &#34;&#34;&#34;Checks whether the onetouch option is enabled

        Returns:
            True    -   if system state property is enabled for the subclient

            False   -   if system state property is not enabled for the subclient
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;oneTouchSubclient&#39;)

    @onetouch_option.setter
    def onetouch_option(self, backup_onetouch):
        &#34;&#34;&#34;
        Enables the system state property for the subclient
        &#34;&#34;&#34;
        self._set_subclient_properties(&#34;_fsSubClientProp[&#39;oneTouchSubclient&#39;]&#34;, backup_onetouch)

    @property
    def onetouch_server(self):
        &#34;&#34;&#34;
        Returns: Onetouch Server Name
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;oneTouchServer&#39;, {}).get(&#39;clientName&#39;)

    @onetouch_server.setter
    def onetouch_server(self, onetouch_server):
        &#34;&#34;&#34;
        Sets the onetouch server property
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;oneTouchServer&#39;][&#39;clientName&#39;]&#34;,
            onetouch_server)

    @property
    def onetouch_server_directory(self):
        &#34;&#34;&#34;
        Returns the onetouch server directory
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;oneTouchServerDirectory&#39;)

    @onetouch_server_directory.setter
    def onetouch_server_directory(self, onetouch_server_directory):
        &#34;&#34;&#34;
        Sets the onetouch server directory
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;oneTouchServerDirectory&#39;]&#34;,
            onetouch_server_directory)

    @property
    def trueup_days(self):
        &#34;&#34;&#34;Gets the trueup after n days value for this Subclient

            Returns: int
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;runTrueUpJobAfterDaysForFS&#39;]

    @trueup_days.setter
    def trueup_days(self, trueup_days_value):
        &#34;&#34;&#34;Creates the JSON with the specified trueup days to pass to the API
            to update the trueup after **n** days value of this File System Subclient.

            Args:
                trueup_days_value   (int)   --  run trueup after days

            Raises:
                SDKException:
                    if failed to update trueup after n days of subclient

                    if trueup_days_value is invalid

        &#34;&#34;&#34;

        if isinstance(trueup_days_value, int):
            self._set_subclient_properties(
                &#34;_fsSubClientProp[&#39;runTrueUpJobAfterDaysForFS&#39;]&#34;,
                trueup_days_value
            )
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Invalid trueup days&#39;)

    @property
    def archiver_retention(self):
        &#34;&#34;&#34;return the value of archiver retention or modified time retention

          Returns:
                True    -   if archiver or modified time retention is enabled for the subclient

                False   -   if archiver or modified time retention is not enabled for the subclient


        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;archiverRetention&#39;]

    @archiver_retention.setter
    def archiver_retention(self, value):
        &#34;&#34;&#34;
        Creates the JSON with the specified Boolean variable to pass to the API
            to update the archiver or modified time based retention of this File System Subclient

        If archiver retention is enabled-
                With backup retention, the object based retention is selected and
                modified time based retention is selected.

                Without backup retention, job based retention is selected
        Args:
            value  (bool)  --  To enable or disable job based retention or modified time retention



        &#34;&#34;&#34;
        if isinstance(value, bool):

            if value:
                new_value = {
                    &#39;extendStoragePolicyRetention&#39;: True,
                    &#39;archiverRetention&#39;: True}
            else:
                new_value = {&#39;archiverRetention&#39;: False}
            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be boolean&#39;)

    @property
    def archiver_retention_days(self):
        &#34;&#34;&#34;return number of days for archiver or modified time  retention

           Return:
                                (int)
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;extendRetentionForNDays&#39;]

    @archiver_retention_days.setter
    def archiver_retention_days(self, value):
        &#34;&#34;&#34;
        Creates the JSON with the specified archiver retention or modified time based retentiondays
         to pass to the API to update the respected value of this File System Subclient

        Args:
                value  (int)  --   To update archiving retention or modified time based retention

                               The value will be converted in years , months and days from on GUI.

                               To set infinite value should be -1

        Raises:
                SDKException:
                    if failed to update archiver retention days of subclient

                    if value is invalid


        &#34;&#34;&#34;
        if isinstance(value, int):
            if value != -1:
                new_value = {
                    &#39;extendRetentionForNDays&#39;: value,
                    &#39;archiverRetentionMode&#39;: 1}
            else:
                new_value = {&#39;extendRetentionForNDays&#39;: value}
            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)

        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be integer&#39;)

    @property
    def disk_cleanup(self):
        &#34;&#34;&#34;
        return value of disk cleanup of the subclient

         Returns:
                True    -   if disk Cleanup is enabled for the subclient

                False   -   if disk Cleanup is not enabled for the subclient


        &#34;&#34;&#34;
        diskcleanup = None
        if &#39;enableArchivingWithRules&#39; in self._fsSubClientProp[&#39;diskCleanupRules&#39;]:
            return self._fsSubClientProp[&#39;diskCleanupRules&#39;][&#39;enableArchivingWithRules&#39;]

        return diskcleanup

    @disk_cleanup.setter
    def disk_cleanup(self, value):
        &#34;&#34;&#34;
        Creates the JSON with the specified Boolean to pass to the API
            to update the disk cleanup option of this File System Subclient

        Args:
            value   (bool)  --  To enable or disbale disk cleanup

        Raises:
                SDKException:
                    if failed to update the propety of subclient

                    if value is invalid

        &#34;&#34;&#34;

        if isinstance(value, bool):

            self._set_subclient_properties(
                &#34;_fsSubClientProp[&#39;diskCleanupRules&#39;][&#39;enableArchivingWithRules&#39;]&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;argument should only be boolean&#39;)

    @property
    def disk_cleanup_rules(self):
        &#34;&#34;&#34;
        return disk cleanup rules for this FileSystem Subclient

        Return:
            (dict)  --  disk clean up rules
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;diskCleanupRules&#39;]

    @disk_cleanup_rules.setter
    def disk_cleanup_rules(self, rules):
        &#34;&#34;&#34;
        Creates the JSON with the specified dictionary value to pass to the API
            to update the disk cleanup rules of this File System Subclient

        Args:
                        rules   (dict)  --  To update the rules Only need to send the value which need to be
                        updated

                        {
                &#39;useNativeSnapshotToPreserveFileAccessTime&#39;: False,
                &#39;fileModifiedTimeOlderThan&#39;: 0,
                &#39;fileSizeGreaterThan&#39;: 1024,
                &#39;stubPruningOptions&#39;: 0, 0 to disable and 1,2 ,3 for different option

                &#39;afterArchivingRule&#39;: 1, - 1 for stub the file and 2 for delete the file

                &#39;stubRetentionDaysOld&#39;: 365,
                &#39;fileCreatedTimeOlderThan&#39;: 0,
                &#39;maximumFileSize&#39;: 0,
                &#39;fileAccessTimeOlderThan&#39;: 89,
                &#39;startCleaningIfLessThan&#39;: 50,
                &#39;enableRedundancyForDataBackedup&#39;: True,
                 &#39;stopCleaningIfupto&#39;: 80,

                 &#39;diskCleanupFileTypes&#39;: {&#39;fileTypes&#39;: [&#34;%Text%&#34;, &#39;%Image%&#39;]}

                 or

                 &#39;diskCleanupFilesTypes&#39;:{} for no extension
                }
        Raises:
                SDKException:
                    if failed to update the property of the subclient

                    if value is invalid


        &#34;&#34;&#34;
        if isinstance(rules, dict):
            value = {&#39;diskCleanupRules&#39;: rules}
            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#34;The parameter should be dictionary&#34;)

    @property
    def backup_only_archiving_candidate(self):
        &#34;&#34;&#34;
            To get the value of backup only archiving candidate

        Returns:
                True    -   if backup only archiving candidate is enabled for the subclient

                False   -   if backup only archiving candidate is not enabled for the subclient
        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;backupFilesQualifiedForArchive&#39;]

    @backup_only_archiving_candidate.setter
    def backup_only_archiving_candidate(self, value):
        &#34;&#34;&#34;
        Creates the JSON with the specified boolean value to pass to the API
            to update the backup only archiving candidate of this File System Subclient

        Args:
            value   (bool)  --  Enable or disable the option

        Raises:
                SDKException:
                    if failed to update the propety of subclient

                    if value is invalid

        &#34;&#34;&#34;
        if isinstance(value, bool):
            self._set_subclient_properties(
                &#34;_fsSubClientProp[&#39;backupFilesQualifiedForArchive&#39;]&#34;, value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;The parameter must be boolean type&#39;)

    @property
    def file_version(self):
        &#34;&#34;&#34;

        Returns:
                        (dict)  --  file version mode
        &#34;&#34;&#34;
        version = {}

        # For Indexing V1 client, this property is not supported. Taking default versions by number
        if &#39;olderFileVersionsMode&#39; not in self._fsSubClientProp:
            return {
                &#39;Mode&#39;: 2,
                &#39;DaysOrNumber&#39;: self._fsSubClientProp.get(&#39;keepAtLeastPreviousVersions&#39;, 0)
            }

        version[&#39;Mode&#39;] = self._fsSubClientProp[&#39;olderFileVersionsMode&#39;]
        modes = {
            1: self._fsSubClientProp[&#39;keepOlderVersionsForNDays&#39;],
            2: self._fsSubClientProp[&#39;keepVersions&#39;]
        }
        version[&#39;DaysOrNumber&#39;] = modes.get(version[&#39;Mode&#39;])
        return version

    @file_version.setter
    def file_version(self, value):
        &#34;&#34;&#34;
            Creates the JSON with the specified dictionary to pass to the API
            to update the version mode and the value of this File System Subclient

        Args:
             value   (dict)  --  format -{&#39;Mode&#39;:value,&#39;DaysOrNumber&#39;:value}

                    Mode value 1- version based on modified time

                                2- No of version

                Example-
                    To set version based on modified time to 2 years

                    value={&#39;Mode&#39;:1,&#39;DaysOrNumber&#39;,730}

                    To set Number of version to 10

                    value={&#39;Mode&#39;:2,&#39;DaysOrNumber&#39;:10}
        Raises:
               SDKException:
                    if failed to update the propety of subclient

                    if value is invalid

        &#34;&#34;&#34;

        if isinstance(value, dict):
            # For Indexing V2 client
            if &#39;olderFileVersionsMode&#39; in self._fsSubClientProp:
                if value[&#39;Mode&#39;] == 1 or value[&#39;Mode&#39;] == 2:
                    new_value = {&#39;olderFileVersionsMode&#39;: value[&#39;Mode&#39;]}
                else:
                    raise SDKException(
                        &#39;Subclient&#39;, &#39;102&#39;, &#34;File version mode can only be 1 or 2&#34;)
                modes = {
                    1: &#39;keepOlderVersionsForNDays&#39;,
                    2: &#39;keepVersions&#39;
                }

                new_value[modes[value[&#39;Mode&#39;]]] = value[&#39;DaysOrNumber&#39;]

            # For Indexing V1 client
            else:
                new_value = {
                    &#39;keepAtLeastPreviousVersions&#39;: value[&#39;DaysOrNumber&#39;]
                }

            self._set_subclient_properties(&#34;_fs_subclient_prop&#34;, new_value)
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#34;Parameter need to be dictionary&#34;)

    @property
    def generate_signature_on_ibmi(self):
        &#34;&#34;&#34;Gets the value of generate signature on ibmi option for IBMi subclient.

            Returns:
                False   -   if signature generation on IBMi is enabled on the subclient

                True    -   if signature generation on IBMi is not enabled on the subclient
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;genSignatureOnIBMi&#39;))

    @generate_signature_on_ibmi.setter
    def generate_signature_on_ibmi(self, generate_signature_value):
        &#34;&#34;&#34;Updates the generate signature property value on ibmi subclient.

            Args:
                generate_signature_value (int)  --  Enable or disable signature generation on IBMi
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;genSignatureOnIBMi&#39;]&#34;,
            generate_signature_value
        )

    @property
    def backup_using_multiple_drives(self):
        &#34;&#34;&#34;Gets the value of VTL multiple drives on ibmi option for IBMi subclient.

            Returns:
                False   -   if multiple drives is not enabled.

                True    -   if multiple drives is enabled.
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;backupUsingMultipleDrives&#39;,False))

    @backup_using_multiple_drives.setter
    def backup_using_multiple_drives(self, set_vtl_multiple_drives):
        &#34;&#34;&#34;Updates the VTL multiple drives property value on ibmi subclient.

            Args:
                set_vtl_multiple_drives (bool)  --  Enable or disable VTL multiple drives on IBMi
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(set_vtl_multiple_drives, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupUsingMultipleDrives&#39;] = set_vtl_multiple_drives
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)
    
    @property
    def pending_record_changes(self):
        &#34;&#34;&#34;Gets the value of pending record changes option for  IBMi subclient.

            Returns:
                False   -   if multiple drives is not enabled.

                True    -   if multiple drives is enabled.
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;pendingRecordChange&#39;))

    @pending_record_changes.setter
    def pending_record_changes(self, value):
        &#34;&#34;&#34;Updates the pending record changes value on ibmi subclient.

            Args:
                value   (str)  --  To set pending records changes value for backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;pendingRecordChange&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def other_pending_changes(self):
        &#34;&#34;&#34;Gets the value of other pending changes for IBMi subclient.

            Returns:
                False   -   if multiple drives is not enabled.

                True    -   if multiple drives is enabled.
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;otherPendingChange&#39;))

    @other_pending_changes.setter
    def other_pending_changes(self, value):
        &#34;&#34;&#34;Updates the other pending changes value on ibmi subclient.

            Args:
                value   (str)  --  To set other pending changes value for backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;otherPendingChange&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def object_level_backup(self):
        &#34;&#34;&#34;Gets the value of object level backup option for IBMi subclient.

            Returns:
                True    -   if object level backup is enabled on the subclient

                False   -   if object level backup is not enabled on the subclient
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;backupAsObjects&#39;)

    @object_level_backup.setter
    def object_level_backup(self, object_level_value):
        &#34;&#34;&#34;Updates the object level backup property for an IBMi subclient.

            Args:
                object_level_value (bool)  --  Specifies to enable or disable object level backup on IBMi
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;backupAsObjects&#39;]&#34;,
            object_level_value
        )

    @property
    def global_filter_status(self):
        &#34;&#34;&#34;Returns the status whether the global filters are included in configuration&#34;&#34;&#34;
        for key, value in self._global_filter_status_dict.items():
            if self._fsSubClientProp.get(&#39;useGlobalFilters&#39;) == value:
                return key

    @global_filter_status.setter
    def global_filter_status(self, value):
        &#34;&#34;&#34;Sets the configuration flag whether to include global filters

            Accepted Values:
                1. `OFF`

                2. `ON`

                3. `USE CELL LEVEL POLICY`
        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return self._set_subclient_properties(
            &#34;_fsSubClientProp[&#39;useGlobalFilters&#39;]&#34;, self._global_filter_status_dict.get(value, 2)
        )

    @property
    def enable_synclib(self):
        &#34;&#34;&#34;
        Return the save while active options for an IBMi subclient.

        Returns:
             (dict) --  Dictionary of synclib options
        &#34;&#34;&#34;
        return {
            &#39;saveWhileActiveOpt&#39;: self._fsSubClientProp[&#39;saveWhileActiveOpt&#39;],
            &#39;syncQueue&#39;: self._fsSubClientProp[&#39;syncQueue&#39;],
            &#39;syncAllLibForBackup&#39;: self._fsSubClientProp[&#39;syncAllLibForBackup&#39;],
            &#39;txtlibSyncCheckPoint&#39;: self._fsSubClientProp[&#39;txtlibSyncCheckPoint&#39;],
            &#39;activeWaitTime&#39;: self._fsSubClientProp[&#39;activeWaitTime&#39;]
        }

    @enable_synclib.setter
    def enable_synclib(self, synclib_config):
        &#34;&#34;&#34;
        Updates the save while active backup property for an IBMi subclient.

            Args:
                synclib_config      (dict)  -- Dictionary of synclib config options

                    options                 --

                        synclib_value       (str)   --  Value of save while active option.

                        sync_queue          (str)   --  Path for the sync queue

                        sync_all_lib        (bool)  --  Whether to synchronize all libraries.

                        check_point         (str)   --  Command to run on checkpoint

                        active_wait_time    (int)   --  Amount of time to wait for check point.

        Returns:
            None

        Raises:
            SDKException:
                if failed to update the property of the subclient

                if value is invalid
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(synclib_config, dict):
            update_properties[&#39;fsSubClientProp&#39;] = synclib_config
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;The parameter should be a dictionary.&#39;)
        self.update_properties(update_properties)

    @property
    def software_compression(self):
        &#34;&#34;&#34;Returns the software compression status for this subclient.

            Returns:    int
                    1   -   On Client
                    2   -   On Media Agent
                    3   -   Use Storage Policy Settings
                    4   -   Off

        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;commonProperties&#39;][&#39;storageDevice&#39;][&#39;softwareCompression&#39;]

    @software_compression.setter
    def software_compression(self, sw_compression_value):
        &#34;&#34;&#34;Updates the software compression property for a subclient.

            Args:
                sw_compression_value  (int)   --  Specifies the software compression method indicated by values below.
                    1   -   On Client
                    2   -   On Media Agent
                    3   -   Use Storage Policy Settings
                    4   -   Off

            Raises:
                SDKException:
                    if failed to update software compression method of subclient

                    if software_compression_value is invalid
        &#34;&#34;&#34;
        if isinstance(sw_compression_value, int) and sw_compression_value in range(1, 5):
            attr_name = &#34;_commonProperties[&#39;storageDevice&#39;][&#39;softwareCompression&#39;]&#34;
            self._set_subclient_properties(attr_name, sw_compression_value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Invalid value for software compression type.&#39;)

    @property
    def use_vss(self):
        &#34;&#34;&#34;Returns the value of the Use VSS options for Windows FS subclients.

            Returns:    dict

                Dictionary contains the keys &#39;useVSS&#39;, &#39;vssOptions&#39; and &#39;useVssForAllFilesOptions&#39;.

                useVSS:
                    True    -   ENABLED
                    False   -   DISABLED

                vssOptions:
                    1   -   For all files
                    2   -   For locked files only

                useVssForAllFilesOptions:
                    1   -   Fail the job
                    2   -   Continue and reset access time
                    3   -   Continue and do not reset access time

        &#34;&#34;&#34;
        return {&#34;useVSS&#34;: self._fsSubClientProp[&#39;useVSS&#39;],
                &#34;vssOptions&#34;: self._fsSubClientProp[&#39;vssOptions&#39;],
                &#34;useVssForAllFilesOptions&#34;: self._fsSubClientProp[&#39;useVssForAllFilesOptions&#39;]}

    @use_vss.setter
    def use_vss(self, value):
        &#34;&#34;&#34;Updates the value of the Use VSS options for Windows FS subclients.

            Args:
                value  (dict)   --  Specifies the value of the Use VSS options for Windows FS subclients.

                    useVSS:
                        True    -   ENABLED
                        False   -   DISABLED

                    vssOptions:
                        1   -   For all files
                        2   -   For locked files only

                    useVssForAllFilesOptions:
                        1   -   Fail the job
                        2   -   Continue and reset access time
                        3   -   Continue and do not reset access time

        &#34;&#34;&#34;
        fs_subclient_prop = self._fs_subclient_prop

        if isinstance(value[&#39;useVSS&#39;], bool):
            fs_subclient_prop[&#39;useVSS&#39;] = value[&#39;useVSS&#39;]
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(value[&#39;useVssForAllFilesOptions&#39;], int) and value[&#39;useVssForAllFilesOptions&#39;] in range(1, 4):
            fs_subclient_prop[&#39;useVssForAllFilesOptions&#39;] = value[&#39;useVssForAllFilesOptions&#39;]
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(value[&#39;vssOptions&#39;], int) and value[&#39;vssOptions&#39;] in range(1, 3):
            fs_subclient_prop[&#39;vssOptions&#39;] = value[&#39;vssOptions&#39;]
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._set_subclient_properties(&#39;_fs_subclient_prop&#39;, fs_subclient_prop)

    def find_all_versions(self, *args, **kwargs):
        &#34;&#34;&#34;Searches the content of a Subclient.

            Args:
                Dictionary of browse options:
                    Example:
                        find_all_versions({
                            &#39;path&#39;: &#39;c:\\hello&#39;,
                            &#39;show_deleted&#39;: True,
                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,
                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

                    (OR)

                Keyword argument of browse options:
                    Example:
                        find_all_versions(
                            path=&#39;c:\\hello.txt&#39;,
                            show_deleted=True,
                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

                Refer self._default_browse_options for all the supported options

        Returns:
            dict    -   dictionary of the specified file with list of all the file versions and
                            additional metadata retrieved from browse
        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;operation&#39;] = &#39;all_versions&#39;

        return self._backupset_object._do_browse(options)

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               collect_metadata=False,
               on_demand_input=None,
               advanced_options=None,
               schedule_pattern=None,
               common_backup_options=None):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level        (str)   --  level of backup the user wish to run
                        Full / Incremental / Differential / Synthetic_full
                    default: Incremental

                incremental_backup  (bool)  --  run incremental backup
                        only applicable in case of Synthetic_full backup
                    default: False

                incremental_level   (str)   --  run incremental backup before/after synthetic full
                        BEFORE_SYNTH / AFTER_SYNTH

                        only applicable in case of Synthetic_full backup
                    default: BEFORE_SYNTH

                on_demand_input     (str)   --  input directive file location for on
                                                    demand subclient

                        only applicable in case of on demand subclient
                    default: None

                advanced_options    (dict)  --  advanced backup options to be included while
                                                    making the request
                        default: None

                        options:
                            directive_file          :   path to the directive file
                            adhoc_backup            :   if set triggers the adhoc backup job
                            adhoc_backup_contents   :   sets the contents for adhoc backup
                            inline_backup_copy      :   to run backup copy immediately(inline)
                            skip_catalog            :   skip catalog for intellisnap operation
                            start_new_media         :   enables the option to start new media for the job
                            media_agent_name        :   to run backup via this media agent
                            impersonate_gui         :   sets the initiatedFrom property to GUI if True
                            mark_media_full_on_success: boolean (True/False) that marks vols full on successful backup

                common_backup_options      (dict)  --  advanced job options to be included while
                                                        making request

                        default: None

                        options:
                            job_description              :  job description to be set.

                            enable_number_of_retries     :  enables/disables the property, number of retrys.
                                values:
                                    True/False

                            number_of_retries            : total number of retries to be set.

                            enable_total_running_time    :  enables/disables the property, toal running time.
                                values:
                                    True/False

                            total_running_time           :  total run time to be set in (secs)

                            kill_running_job_when_total_running_time_expires    :   enables/disables the property.
                                values:
                                    True/False

                            start_in_suspended_state     :  enables/disables the property.
                                values:
                                    True/False

                            use_default_priority         :  enables/disables the property.
                                values:
                                    True/False

                            priority                     :  three digit number to be set.
                                default: 166

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

            Returns:
                object - instance of the Job class for this backup job if its an immediate Job
                         instance of the Schedule class for the backup job if its a scheduled Job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if on_demand_input is not None:
            if not isinstance(on_demand_input, str):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            if not self.is_on_demand_subclient:
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;On Demand backup is not supported for this subclient&#39;)

            if not advanced_options:
                advanced_options = {}

            advanced_options[&#39;on_demand_input&#39;] = on_demand_input

        if advanced_options or schedule_pattern or common_backup_options:
            request_json = self._backup_json(
                backup_level,
                incremental_backup,
                incremental_level,
                advanced_options,
                schedule_pattern,
                common_backup_options
            )

            backup_service = self._services[&#39;CREATE_TASK&#39;]

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )

        else:
            return super(FileSystemSubclient, self).backup(
                backup_level=backup_level,
                incremental_backup=incremental_backup,
                incremental_level=incremental_level,
                collect_metadata=collect_metadata
            )

        return self._process_backup_response(flag, response)

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            proxy_client=None,
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options
                    options:
                        all_versions        : if set to True restores all the versions of the
                                                specified file
                        versions            : list of version numbers to be backed up
                        validate_only       : To validate data backed up for restore


                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                proxy_client    (str)          -- Proxy client used during FS under NAS operations

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job if its an immediate Job
                         instance of the Schedule class for this restore job if its a scheduled Job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._backupset_object._instance_object._restore_association = self._subClientEntity

        if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
            fs_options[&#39;destination_appTypeId&#39;] = int(self._client_object.agents.all_agents.get(&#39;file system&#39;, self._client_object.agents.all_agents.get(&#39;windows file system&#39;, self._client_object.agents.all_agents.get(&#39;linux file system&#39;, self._client_object.agents.all_agents.get(&#39;big data apps&#39;, self._client_object.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
            if not fs_options[&#39;destination_appTypeId&#39;]:
                del fs_options[&#39;destination_appTypeId&#39;]

        return super(FileSystemSubclient, self).restore_in_place(
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            schedule_pattern=schedule_pattern,
            proxy_client=proxy_client,
            advanced_options=advanced_options
        )

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            schedule_pattern=None,
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options

                    options:

                        preserve_level          : preserve level option to set in restore

                        proxy_client            : proxy that needed to be used for restore

                        impersonate_user        : Impersonate user options for restore

                        impersonate_password    : Impersonate password option for restore
                        in base64 encoded form

                        all_versions            : if set to True restores all the versions of the
                        specified file

                        versions                : list of version numbers to be backed up

                        media_agent             : Media Agent need to be used for Browse and restore

                        is_vlr_restore          : sets if the restore job is to be triggered as vlr

                        validate_only           : To validate data backed up for restore

                        instant_clone_options   : Options for FS clone found on Command Center, the value must be
                        a dictionary containing the following key value pairs.

                            reservation_time        (int)   --  The amount of time, specified in seconds, that the mounted
                            snapshot needs to be reserved for before it is cleaned up.
                            This is an OPTIONAL key.

                                Default :   3600

                            clone_mount_path        (str)   --  The path to which the snapshot needs to be mounted.
                            This is NOT an optional key.

                            post_clone_script       (str)   --  The script that will run post clone.
                            This is an OPTIONAL key.

                            clone_cleanup_script    (str)   --  The script that will run after clean up.
                            This is an OPTIONAL key.

                        no_of_streams   (int)       -- Number of streams to be used for restore

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._backupset_object._instance_object._restore_association = self._subClientEntity

        if not isinstance(client, (str, Client)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(client, str):
            client = Client(self._commcell_object, client)

        if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
            fs_options[&#39;destination_appTypeId&#39;] = int(client.agents.all_agents.get(&#39;file system&#39;, client.agents.all_agents.get(&#39;windows file system&#39;, client.agents.all_agents.get(&#39;linux file system&#39;, client.agents.all_agents.get(&#39;big data apps&#39;, client.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
            if not fs_options[&#39;destination_appTypeId&#39;]:
                del fs_options[&#39;destination_appTypeId&#39;]

            # check to find whether file level Restore/ Volume level restore for blocklevel.

        if fs_options is not None and fs_options.get(&#39;is_vlr_restore&#39;, False):
            if not (isinstance(paths, list) and
                    isinstance(overwrite, bool) and
                    isinstance(restore_data_and_acl, bool)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            paths = self._filter_paths(paths)

            if paths == []:
                raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

            request_json = self._restore_json(
                client=client,
                paths=paths,
                overwrite=overwrite,
                restore_data_and_acl=restore_data_and_acl,
                copy_precedence=copy_precedence,
                from_time=from_time,
                to_time=to_time,
                destPath=destination_path,
                restore_option=fs_options
            )

            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;].update(
                self._vlr_restore_options_dict)
            destination_options = request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;].get(&#39;destination&#39;, {})
            destination_options[&#39;destPath&#39;] = destination_options.get(&#39;destPath&#39;, [&#39;&#39;])
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;destPath&#39;][0] = \
                destination_path
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;inPlace&#39;] = False

            return self._process_restore_response(request_json)

        else:
            return super(FileSystemSubclient, self).restore_out_of_place(
                client=client,
                destination_path=destination_path,
                paths=paths,
                overwrite=overwrite,
                restore_data_and_acl=restore_data_and_acl,
                copy_precedence=copy_precedence,
                from_time=from_time,
                to_time=to_time,
                fs_options=fs_options,
                schedule_pattern=schedule_pattern,
                advanced_options=advanced_options
            )

    def enable_content_indexing(self, policy_id):
        &#34;&#34;&#34;Enables Content indexing and add the policy associations&#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#39;fsSubClientProp&#39;][&#39;enableContentIndexing&#39;] = True
        update_properties[&#39;fsSubClientProp&#39;][&#39;contentIndexingPolicy&#39;] = int(policy_id)
        self.update_properties(update_properties)

    def disable_content_indexing(self):
        &#34;&#34;&#34;Disables Content indexing and disassociate the CI policy&#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#39;fsSubClientProp&#39;][&#39;enableContentIndexing&#39;] = False
        self.update_properties(update_properties)

    @property
    def catalog_acl(self):
        &#34;&#34;&#34;Gets the catalog acl option

        Returns:
            true  - if catalog acl is enbaled on the subclient

            false - if catalog acl disabled on the subclient
        &#34;&#34;&#34;

        return self._fsSubClientProp[&#39;catalogACL&#39;]

    @catalog_acl.setter
    def catalog_acl(self, value):
        &#34;&#34;&#34;
        To enable or disable catalog_acl
        Args:

            value   (bool)  -- To enable or disbale catalog acl
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;catalogACL&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def index_server(self):
        &#34;&#34;&#34;Returns the index server client set for the subclient. None if no Index Server is set&#34;&#34;&#34;

        if &#39;indexSettings&#39; not in self._commonProperties:
            return None

        index_settings = self._commonProperties[&#39;indexSettings&#39;]
        index_server = None

        if (&#39;currentIndexServer&#39; in index_settings and
                &#39;clientName&#39; in index_settings[&#39;currentIndexServer&#39;]):
            index_server = index_settings[&#39;currentIndexServer&#39;][&#39;clientName&#39;]

        if index_server is None:
            return None

        return Client(self._commcell_object, client_name=index_server)

    @index_server.setter
    def index_server(self, value):
        &#34;&#34;&#34;Sets the index server client for the backupset

            Args:
                value   (object)    --  The index server client object to set

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(value, Client):
            raise SDKException(&#39;Subclient&#39;, &#39;121&#39;)

        index_server_name = value.client_name

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]&#34;,
            index_server_name)

    @property
    def index_pruning_type(self):
        &#34;&#34;&#34;Treats the subclient pruning type as a read-only attribute.&#34;&#34;&#34;

        index_settings = self._commonProperties[&#39;indexSettings&#39;]
        if &#39;indexPruningType&#39; in index_settings:
            pruning_type = index_settings[&#39;indexPruningType&#39;]
            return pruning_type

    @property
    def index_pruning_days_retention(self):
        &#34;&#34;&#34;Returns number of days to be maintained in index by index pruning for the subclient&#34;&#34;&#34;

        return self._commonProperties[&#34;indexSettings&#34;][&#34;indexRetDays&#34;]

    @property
    def index_pruning_cycles_retention(self):
        &#34;&#34;&#34;Returns number of cycles to be maintained in index by index pruning for the subclient&#34;&#34;&#34;

        return self._commonProperties[&#34;indexSettings&#34;][&#34;indexRetCycle&#34;]

    @index_pruning_type.setter
    def index_pruning_type(self, value):
        &#34;&#34;&#34;Updates the pruning type for the subclient when subclient level indexing is enabled.
        Can be days based pruning or cycles based pruning.
        Days based pruning will set index retention on the basis of days,
        cycles based pruning will set index retention on basis of cycles.

        Args:
            value    (str)  --  &#34;days_based&#34; or &#34;cycles_based&#34;

        &#34;&#34;&#34;

        if value.lower() == &#34;cycles_based&#34;:
            final_value = 1

        elif value.lower() == &#34;days_based&#34;:
            final_value = 2

        elif value.lower() == &#34;infinite&#34;:
            final_value = 0

        else:
            raise SDKException(&#39;Subclient&#39;, &#39;119&#39;)

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;indexSettings&#39;][&#39;indexPruningType&#39;]&#34;, final_value)

    @index_pruning_days_retention.setter
    def index_pruning_days_retention(self, value):
        &#34;&#34;&#34;Sets index pruning days value at subclient level for days-based index pruning&#34;&#34;&#34;

        if isinstance(value, int) and value &gt;= 2:
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;indexSettings&#39;][&#39;indexRetDays&#39;]&#34;, value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;120&#39;)

    @index_pruning_cycles_retention.setter
    def index_pruning_cycles_retention(self, value):
        &#34;&#34;&#34;Sets index pruning cycles value at subclient level for cycles-based index pruning&#34;&#34;&#34;

        if isinstance(value, int) and value &gt; 0:
            self._set_subclient_properties(
                &#34;_commonProperties[&#39;indexSettings&#39;][&#39;indexRetCycle&#39;]&#34;, value)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;120&#39;)

    @property
    def ibmi_dr_config(self):
        &#34;&#34;&#34;
        Return the ibmi dr configuration

        Returns:
            (dict)  --  Dictionary of DR parameters
        &#34;&#34;&#34;
        return {
            &#39;backupMaxTime&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;backupMaxTime&#39;, 0),
            &#39;printSysInfo&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;printSysInfo&#39;, False),
            &#39;userProgram&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;userProgram&#39;, &#39;&#39;),
            &#39;saveSecData&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;saveSecData&#39;, False),
            &#39;saveConfObject&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;saveConfObject&#39;, False)
        }

    @ibmi_dr_config.setter
    def ibmi_dr_config(self, dr_config):
        &#34;&#34;&#34;
            Sets the subclient into one touch mode and adds ibmi DR parameters

            Args:
                dr_config   (dict)  --  Dictionary of IBMi DR parameters
                
                Example:
                    ibmi_dr_config = {
                        &#39;backupMaxTime&#39; : 120,
                        &#39;printSysInfo&#39; : True,
                        &#39;userProgram&#39; : &#39;QSYS/CVPGM&#39;,
                        &#39;saveSecData&#39; : True,
                        &#39;saveConfObject&#39; : False,
                        &#39;library&#39; : [{path:&#39;/QSYS.LIB/TEST1.LIB&#39;}]
                    }
            Returns:
                None

            Raises:
                SDKException:
                    if parameters are not valid
        &#34;&#34;&#34;
        self.onetouch_option = True
        update_properties = self.properties
        if isinstance(dr_config, dict):
            update_properties[&#39;fsSubClientProp&#39;][&#39;ibmiSubclientprop&#39;]= dr_config
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;The parameter should be a dictionary.&#39;)
        self.update_properties(update_properties)

    @property
    def backup_savf_file_data(self):
        &#34;&#34;&#34;
         Return the ibmi savf file data configuration

        Returns:
            (bool)  --  Is savf file data going to be backed up
        &#34;&#34;&#34;
        return self._fsSubClientProp.get(&#39;backupSaveFileData&#39;, False)

    @backup_savf_file_data.setter
    def backup_savf_file_data(self, value):
        &#34;&#34;&#34;
        Sets the backup save file data property on an ibmi subclient
        Args:
                value   (boolean)  --  Toggle the backup property

            Returns:
                None

            Raises:
                None
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupSaveFileData&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def backup_spool_file_data(self):
        &#34;&#34;&#34;Gets the value of spool file data on ibmi option for IBMi subclient.

            Returns:
                False   -   if spool file data on IBMi is disabled on the subclient

                True    -   if spool file data on IBMi is enabled on the subclient
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;backupSpooledFileData&#39;))

    @backup_spool_file_data.setter
    def backup_spool_file_data(self, value):
        &#34;&#34;&#34;Updates the generate signature property value on ibmi subclient.

            Args:
                value   (bool)  --  To enable or disable spool file data backup.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupSpooledFileData&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def backup_queue_data(self):
        &#34;&#34;&#34;Gets the value of queue data data on ibmi option for IBMi subclient.

            Returns:
                False   -   if queue data on IBMi is disabled on the subclient

                True    -   if queue data on IBMi is enabled on the subclient
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;backupQueueData&#39;))

    @backup_queue_data.setter
    def backup_queue_data(self, value):
        &#34;&#34;&#34;Updates the queue data property value on ibmi subclient.

            Args:
                value   (bool)  --  To enable or disable queue data backup.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupQueueData&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def backup_private_authorities(self):
        &#34;&#34;&#34;Gets the value of private authorities on ibmi option for IBMi subclient.

            Returns:
                False   -   if PVTAUT on IBMi is disabled on the subclient

                True    -   if PVTAUT on IBMi is enabled on the subclient
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;backupPrivateAuthority&#39;))

    @backup_private_authorities.setter
    def backup_private_authorities(self, value):
        &#34;&#34;&#34;Updates the private authorities property value on ibmi subclient.

            Args:
                value   (bool)  --  To enable or disable private authorities backup.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, bool):
            update_properties[&#39;fsSubClientProp&#39;][&#39;backupPrivateAuthority&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def target_release(self):
        &#34;&#34;&#34;Gets the value of target and release on ibmi option for IBMi subclient.

            Returns:
                (str)   -   Return the target and release string value
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;targetReleaseForBackupData&#39;))

    @target_release.setter
    def target_release(self, value):
        &#34;&#34;&#34;Updates the private authorities property value on ibmi subclient.

            Args:
                value   (str)  --  To set target and release for  backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;targetReleaseForBackupData&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def save_access_path(self):
        &#34;&#34;&#34;Gets the value of save access path on ibmi option for IBMi subclient.

            Returns:
                (str)   -   Return the save access path string value
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;saveAccessPath&#39;))

    @save_access_path.setter
    def save_access_path(self, value):
        &#34;&#34;&#34;Updates the save access path property value on ibmi subclient.

            Args:
                value   (str)  --  To set access path value for  backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;saveAccessPath&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def update_history(self):
        &#34;&#34;&#34;Gets the value of update history property on ibmi option for IBMi subclient.

            Returns:
                (str)   -   Return the string value of update history property
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;updateHistory&#39;))

    @update_history.setter
    def update_history(self, value):
        &#34;&#34;&#34;Updates the update history property value on ibmi subclient.

            Args:
                value   (str)  --  To set update history value for  backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;updateHistory&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def ibmi_compression(self):
        &#34;&#34;&#34;Gets the value of IBMi compression property on ibmi option for IBMi subclient.

            Returns:
                (str)   -   Return the string value of IBMi compression property
        &#34;&#34;&#34;
        return bool(self._fsSubClientProp.get(&#39;ibmiCompression&#39;))

    @ibmi_compression.setter
    def ibmi_compression(self, value):
        &#34;&#34;&#34;Updates the IBMi compression property value on ibmi subclient.

            Args:
                value   (str)  --  To set IBMi compression value for  backup data.
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(value, str):
            update_properties[&#39;fsSubClientProp&#39;][&#39;ibmiCompression&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self.update_properties(update_properties)

    @property
    def save_while_active_option(self):
        &#34;&#34;&#34;
        Return the save while active options for an IBMi subclient.

        Returns:
             (dict) --  Dictionary of save while active options
        &#34;&#34;&#34;
        return {
            &#39;saveWhileActiveOpt&#39;: self._fsSubClientProp[&#39;saveWhileActiveOpt&#39;],
            &#39;activeWaitTime&#39;: self._fsSubClientProp[&#39;activeWaitTime&#39;]
        }

    @save_while_active_option.setter
    def save_while_active_option(self, swa_config):
        &#34;&#34;&#34;
        Updates the save while active backup property for an IBMi subclient.

            Args:
                swa_config      (dict)  -- Dictionary of synclib config options

                    options                 --

                        saveWhileActiveOpt      (str)   --  Value of save while active option.

                        activeWaitTime          (int)   --  Amount of time to wait for check point.

        Returns:
            None

        Raises:
            SDKException:
                if failed to update the property of the subclient

                if value is invalid
        &#34;&#34;&#34;
        update_properties = self.properties
        if isinstance(swa_config, dict):
            update_properties[&#39;fsSubClientProp&#39;] = swa_config
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;SWA should be a dictionary.&#39;)
        self.update_properties(update_properties)

    @property
    def pre_post_commands(self):
        &#34;&#34;&#34;
         Return the prep_post commands set for a subclient

        Returns:
            (dict)  --  All the pre/post commands
        &#34;&#34;&#34;
        pre_scan_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;preScanCommand&#34;]
        post_scan_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;postScanCommand&#34;]
        pre_backup_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;preBackupCommand&#34;]
        post_backup_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;postBackupCommand&#34;]

        pre_post_commands = {&#39;pre_scan_command&#39;: pre_scan_command, &#39;post_scan_command&#39;: post_scan_command, &#39;pre_backup_command&#39;: pre_backup_command, &#39;post_backup_command&#39;: post_backup_command}

        return pre_post_commands

    @pre_post_commands.setter
    def pre_post_commands(self, value):
        &#34;&#34;&#34;
        Sets the pre post commands on a subclient
        Args:

            value   (dict)      --  Specifies the pre.post commands to be set

                pre_scan_command    (str)       --     The pre scan command to be set

                post_scan_command   (str)       --     The post scan command to be set

                pre_backup_command  (str)       --     The pre backup command to be set

                post_backup_command (str)       --     The post backup command to be set

            Returns:
                None

            Raises:
                None
        &#34;&#34;&#34;
        if isinstance(value, dict):

            pre_post_process = self._commonProperties[&#34;prepostProcess&#34;]

            pre_post_process[&#34;preScanCommand&#34;] = value.get(&#34;pre_scan_command&#34;, &#34;&#34;)
            pre_post_process[&#34;postScanCommand&#34;] = value.get(&#34;post_scan_command&#34;, &#34;&#34;)
            pre_post_process[&#34;preBackupCommand&#34;] = value.get(&#34;pre_backup_command&#34;, &#34;&#34;)
            pre_post_process[&#34;postBackupCommand&#34;] = value.get(&#34;post_backup_command&#34;, &#34;&#34;)

            self._set_subclient_properties(&#39;_commonProperties[&#34;prepostProcess&#34;]&#39;, pre_post_process)

        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    @property
    def backup_nodes(self):
        &#34;&#34;&#34;
        Gets the backup nodes for FS Agent under Network Share Clients.
        &#34;&#34;&#34;
        return self._fsSubClientProp[&#34;backupConfiguration&#34;][&#34;backupDataAccessNodes&#34;]

    @backup_nodes.setter
    def backup_nodes(self, value):
        &#34;&#34;&#34;
        Sets the backup nodes for FS Agent under Network Share Clients.

            Args:
                value  (list)   --  Specifies the nodes, a list of strings, values are data access node host names.
        &#34;&#34;&#34;

        update_properties = self.properties

        access_nodes = []
        for access_node in value:
            access_nodes.append({&#34;clientName&#34;: access_node})

        update_properties[&#34;fsSubClientProp&#34;][&#34;backupConfiguration&#34;] = {&#34;backupDataAccessNodes&#34;: access_nodes}
        self.update_properties(update_properties)

    @property
    def network_share_auto_mount(self):
        &#34;&#34;&#34;
        Returns the value of enableNetworkShareAutoMount, if true, the content will be auto-mounted during backup and
        auto-mounted during in-place restores.
        &#34;&#34;&#34;
        return self._fsSubClientProp[&#39;enableNetworkShareAutoMount&#39;]

    @network_share_auto_mount.setter
    def network_share_auto_mount(self, value):
        &#34;&#34;&#34;
        Sets the value for enableNetworkShareAutoMount, needs to set to true if content is specified
        in the file_server:/path format.

            Args:
                value   (bool)  --  Enables or disables the property by setting True or False respectively.

        &#34;&#34;&#34;
        update_properties = self.properties

        if isinstance(value, bool):
            update_properties[&#34;fsSubClientProp&#34;][&#39;enableNetworkShareAutoMount&#39;] = value
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self.update_properties(update_properties)

    @property
    def impersonate_user(self):
        &#34;&#34;&#34;
        Returns the username ONLY and applicable to Windows FS subclients only.
        &#34;&#34;&#34;
        return self._subclient_properties[&#39;impersonateUser&#39;]

    @impersonate_user.setter
    def impersonate_user(self, value):
        &#34;&#34;&#34;
        Sets the user impersonation information for a subclient, applicable to Windows only.

        Args:

            value   (dict)  --  Valid keys are &#39;username&#39; and &#39;password&#39;

                username    (str)   --  The username

                password    (str)   --  The password
        &#34;&#34;&#34;

        update_properties = self.properties

        if isinstance(value, dict):
            update_properties[&#34;impersonateUser&#34;][&#34;userName&#34;] = value[&#34;username&#34;]
            update_properties[&#34;impersonateUser&#34;][&#34;password&#34;] = b64encode(value[&#34;password&#34;].encode()).decode()

        self.update_properties(update_properties)

    @property
    def plan(self):
        &#34;&#34;&#34;Returns the name of the plan associated with the subclient.
           Returns None if no plan is associated
        &#34;&#34;&#34;
        return super().plan

    @plan.setter
    def plan(self, value):
        &#34;&#34;&#34;Associates a plan to the subclient.

            Args:
                value   (object)    --  the Plan object which is to be associated
                                        with the subclient

                value   (str)       --  name of the plan to be associated
                
                value   (list)      --  associate subclient with Plan and override the content of Plan on subclient
                                        Example-1 ( with Plan name ): subclient_object.plan = [&#34;plan_name&#34;,[&#34;content_path1&#34;,&#34;content_path2&#34;]]
                                        Example-2 ( with Plan Object ): subclient_object.plan = [plan_object,[&#34;content_path1&#34;,&#34;content_path2&#34;]]

                value   (None)      --  set value to None to remove plan associations
                

            Raises:
                SDKException:
                    if the type of input is incorrect

                    if the plan association is unsuccessful
        &#34;&#34;&#34;
        
        from ..plan import Plan
        if isinstance(value, Plan):
            if self._commcell_object.plans.has_plan(value.plan_name):
                self.update_properties({
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value.plan_name
                    },
                    &#34;useContentFromPlan&#34;: True
                })
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Plan does not exist&#39;)
                
        elif isinstance(value, str):
            if self._commcell_object.plans.has_plan(value):
                self.update_properties({
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value
                    },
                    &#34;useContentFromPlan&#34;: True
                })
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Plan does not exist&#39;)
                
        elif isinstance(value, list):
            if not isinstance(value[0], Plan) and not isinstance(value[0], str):
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Expecting Plan object or str&#39;)
            if not isinstance(value[1], list):
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Expecting List of contents&#39;)
           
            self.update_properties({
                     &#34;content&#34;: [{&#34;path&#34;:p} for p in value[1]],
                    &#34;useContentFromPlan&#34;: False,
                    &#34;fsContentOperationType&#34;: 1,
                    &#39;planEntity&#39;: {
                        &#39;planName&#39;: value[0].plan_name if isinstance(value[0], Plan) else value[0]
                    }
                })
                
        elif value is None:
            self.update_properties({
                &#39;removePlanAssociation&#39;: True
            })
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    def preview_backedup_file(self, file_path):
        &#34;&#34;&#34;Gets the preview content for the subclient.
            Params:
                file_path (str) --  file path to get the preview content

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self._get_preview(file_path)

    def add_comparison(self, name, source_backup_time, compare_with_time):
        &#34;&#34;&#34;Adds a comparison job for the subclient

            Args:
                name                (str)       --      The name of the comparison

                source_backup_time  (int)       --      The epoch time of the source job&#39;s backup time. Ex: 1743167209

                compare_with_time   (int)       --      The epoch time of the other job to compare.

            Returns:
                object - Instance of the Job class for the comparison job.

        &#34;&#34;&#34;

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;clientName&#34;: self._client_object._client_name,
                        &#34;appName&#34;: self._agent_object._agent_name,
                        &#34;backupsetName&#34;: self._backupset_object._backupset_name,
                        &#34;subclientName&#34;: self._subclient_name,
                        &#34;_type_&#34;: 7
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4025
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {},
                            &#34;adComparisonOption&#34;: {
                                &#34;leftSetTime&#34;: int(source_backup_time),
                                &#34;rightSetTime&#34;: int(compare_with_time),
                                &#34;adCompareType&#34;: 0,
                                &#34;nodeClientId&#34;: int(self._client_object.client_id),
                                &#34;clientId&#34;: int(self._client_object.client_id),
                                &#34;comparisonName&#34;: name,
                                &#34;appTypeId&#34;: 33,
                                &#34;subClientId&#34;: int(self.subclient_id),
                                &#34;adComparisonJobType&#34;: 0,
                                &#34;status&#34;: 0
                            }
                        }
                    }
                ]
            }
        }

        comparison_task = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, comparison_task, request_json)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Failed to start the comparison job.\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, &#39;Failed to start the comparison job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.bigdataappssubclient.BigDataAppsSubclient" href="bigdataappssubclient.html#cvpysdk.subclients.bigdataappssubclient.BigDataAppsSubclient">BigDataAppsSubclient</a></li>
<li><a title="cvpysdk.subclients.nassubclient.NASSubclient" href="nassubclient.html#cvpysdk.subclients.nassubclient.NASSubclient">NASSubclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention"><code class="name">var <span class="ident">archiver_retention</span></code></dt>
<dd>
<div class="desc"><p>return the value of archiver retention or modified time retention</p>
<h2 id="returns">Returns</h2>
<p>True
-
if archiver or modified time retention is enabled for the subclient</p>
<p>False
-
if archiver or modified time retention is not enabled for the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L981-L993" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archiver_retention(self):
    &#34;&#34;&#34;return the value of archiver retention or modified time retention

      Returns:
            True    -   if archiver or modified time retention is enabled for the subclient

            False   -   if archiver or modified time retention is not enabled for the subclient


    &#34;&#34;&#34;

    return self._fsSubClientProp[&#39;archiverRetention&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention_days"><code class="name">var <span class="ident">archiver_retention_days</span></code></dt>
<dd>
<div class="desc"><p>return number of days for archiver or modified time
retention</p>
<h2 id="return">Return</h2>
<p>(int)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1027-L1035" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archiver_retention_days(self):
    &#34;&#34;&#34;return number of days for archiver or modified time  retention

       Return:
                            (int)
    &#34;&#34;&#34;

    return self._fsSubClientProp[&#39;extendRetentionForNDays&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_nodes"><code class="name">var <span class="ident">backup_nodes</span></code></dt>
<dd>
<div class="desc"><p>Gets the backup nodes for FS Agent under Network Share Clients.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2552-L2557" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_nodes(self):
    &#34;&#34;&#34;
    Gets the backup nodes for FS Agent under Network Share Clients.
    &#34;&#34;&#34;
    return self._fsSubClientProp[&#34;backupConfiguration&#34;][&#34;backupDataAccessNodes&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_only_archiving_candidate"><code class="name">var <span class="ident">backup_only_archiving_candidate</span></code></dt>
<dd>
<div class="desc"><p>To get the value of backup only archiving candidate</p>
<h2 id="returns">Returns</h2>
<p>True
-
if backup only archiving candidate is enabled for the subclient</p>
<p>False
-
if backup only archiving candidate is not enabled for the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1178-L1188" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_only_archiving_candidate(self):
    &#34;&#34;&#34;
        To get the value of backup only archiving candidate

    Returns:
            True    -   if backup only archiving candidate is enabled for the subclient

            False   -   if backup only archiving candidate is not enabled for the subclient
    &#34;&#34;&#34;
    return self._fsSubClientProp[&#39;backupFilesQualifiedForArchive&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_private_authorities"><code class="name">var <span class="ident">backup_private_authorities</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of private authorities on ibmi option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>False
-
if PVTAUT on IBMi is disabled on the subclient</p>
<p>True
-
if PVTAUT on IBMi is enabled on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2339-L2348" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_private_authorities(self):
    &#34;&#34;&#34;Gets the value of private authorities on ibmi option for IBMi subclient.

        Returns:
            False   -   if PVTAUT on IBMi is disabled on the subclient

            True    -   if PVTAUT on IBMi is enabled on the subclient
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;backupPrivateAuthority&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_queue_data"><code class="name">var <span class="ident">backup_queue_data</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of queue data data on ibmi option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>False
-
if queue data on IBMi is disabled on the subclient</p>
<p>True
-
if queue data on IBMi is enabled on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2314-L2323" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_queue_data(self):
    &#34;&#34;&#34;Gets the value of queue data data on ibmi option for IBMi subclient.

        Returns:
            False   -   if queue data on IBMi is disabled on the subclient

            True    -   if queue data on IBMi is enabled on the subclient
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;backupQueueData&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention"><code class="name">var <span class="ident">backup_retention</span></code></dt>
<dd>
<div class="desc"><p>return if backup retention is enabled or not</p>
<h2 id="returns">Returns</h2>
<p>True
-
if backup_retention is enabled for the subclient</p>
<p>False
-
if backup_rentention is not enabled for the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L712-L723" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_retention(self):
    &#34;&#34;&#34;return if backup retention is enabled or not

    Returns:
            True    -   if backup_retention is enabled for the subclient

            False   -   if backup_rentention is not enabled for the subclient

    &#34;&#34;&#34;

    return self._fsSubClientProp[&#39;backupRetention&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention_days"><code class="name">var <span class="ident">backup_retention_days</span></code></dt>
<dd>
<div class="desc"><p>return number of days for backup retention</p>
<h2 id="returns">Returns</h2>
<p>(int)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L819-L834" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_retention_days(self):
    &#34;&#34;&#34;return number of days for backup retention

    Returns:
                    (int)

    &#34;&#34;&#34;

    # For Indexing V2 clients
    if &#39;afterDeletionKeepItemsForNDays&#39; in self._fsSubClientProp:
        return self._fsSubClientProp[&#39;afterDeletionKeepItemsForNDays&#39;]

    # For Indexing V1 clients
    else:
        return self._fsSubClientProp.get(&#39;daysToKeepItems&#39;, 0)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_savf_file_data"><code class="name">var <span class="ident">backup_savf_file_data</span></code></dt>
<dd>
<div class="desc"><p>Return the ibmi savf file data configuration</p>
<h2 id="returns">Returns</h2>
<p>(bool)
&ndash;
Is savf file data going to be backed up</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2259-L2267" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_savf_file_data(self):
    &#34;&#34;&#34;
     Return the ibmi savf file data configuration

    Returns:
        (bool)  --  Is savf file data going to be backed up
    &#34;&#34;&#34;
    return self._fsSubClientProp.get(&#39;backupSaveFileData&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_spool_file_data"><code class="name">var <span class="ident">backup_spool_file_data</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of spool file data on ibmi option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>False
-
if spool file data on IBMi is disabled on the subclient</p>
<p>True
-
if spool file data on IBMi is enabled on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2289-L2298" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_spool_file_data(self):
    &#34;&#34;&#34;Gets the value of spool file data on ibmi option for IBMi subclient.

        Returns:
            False   -   if spool file data on IBMi is disabled on the subclient

            True    -   if spool file data on IBMi is enabled on the subclient
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;backupSpooledFileData&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_using_multiple_drives"><code class="name">var <span class="ident">backup_using_multiple_drives</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of VTL multiple drives on ibmi option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>False
-
if multiple drives is not enabled.</p>
<p>True
-
if multiple drives is enabled.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1319-L1328" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_using_multiple_drives(self):
    &#34;&#34;&#34;Gets the value of VTL multiple drives on ibmi option for IBMi subclient.

        Returns:
            False   -   if multiple drives is not enabled.

            True    -   if multiple drives is enabled.
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;backupUsingMultipleDrives&#39;,False))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.block_level_backup_option"><code class="name">var <span class="ident">block_level_backup_option</span></code></dt>
<dd>
<div class="desc"><p>Gets the block level option</p>
<h2 id="returns">Returns</h2>
<p>true - if blocklevel is enabled on the subclient
false - if blocklevel is not enabled on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L750-L759" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def block_level_backup_option(self):
    &#34;&#34;&#34;Gets the block level option

        Returns:
            true - if blocklevel is enabled on the subclient
            false - if blocklevel is not enabled on the subclient
    &#34;&#34;&#34;

    return self._fsSubClientProp[&#39;blockLevelBackup&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.catalog_acl"><code class="name">var <span class="ident">catalog_acl</span></code></dt>
<dd>
<div class="desc"><p>Gets the catalog acl option</p>
<h2 id="returns">Returns</h2>
<p>true
- if catalog acl is enbaled on the subclient</p>
<p>false - if catalog acl disabled on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2073-L2083" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def catalog_acl(self):
    &#34;&#34;&#34;Gets the catalog acl option

    Returns:
        true  - if catalog acl is enbaled on the subclient

        false - if catalog acl disabled on the subclient
    &#34;&#34;&#34;

    return self._fsSubClientProp[&#39;catalogACL&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate content from the Subclient relevant to the user.</p>
<h2 id="returns">Returns</h2>
<p>list - list of content associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L463-L476" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

        Returns:
            list - list of content associated with the subclient
    &#34;&#34;&#34;
    content = []

    for path in self._content:
        if &#39;path&#39; in path:
            content.append(path[&#34;path&#34;])

    return content</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.create_file_level_index_option"><code class="name">var <span class="ident">create_file_level_index_option</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of Metadata collection Option</p>
<h2 id="returns">Returns</h2>
<p>true - if metadata collection is enabled on the subclient
false - if metadata collection is not enabled on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L794-L803" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def create_file_level_index_option(self):
    &#34;&#34;&#34;Gets the value of Metadata collection Option

        Returns:
            true - if metadata collection is enabled on the subclient
            false - if metadata collection is not enabled on the subclient
    &#34;&#34;&#34;

    return self._fsSubClientProp[&#39;createFileLevelIndexDuringBackup&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup"><code class="name">var <span class="ident">disk_cleanup</span></code></dt>
<dd>
<div class="desc"><p>return value of disk cleanup of the subclient</p>
<p>Returns:
True
-
if disk Cleanup is enabled for the subclient</p>
<pre><code>    False   -   if disk Cleanup is not enabled for the subclient
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1073-L1089" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def disk_cleanup(self):
    &#34;&#34;&#34;
    return value of disk cleanup of the subclient

     Returns:
            True    -   if disk Cleanup is enabled for the subclient

            False   -   if disk Cleanup is not enabled for the subclient


    &#34;&#34;&#34;
    diskcleanup = None
    if &#39;enableArchivingWithRules&#39; in self._fsSubClientProp[&#39;diskCleanupRules&#39;]:
        return self._fsSubClientProp[&#39;diskCleanupRules&#39;][&#39;enableArchivingWithRules&#39;]

    return diskcleanup</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup_rules"><code class="name">var <span class="ident">disk_cleanup_rules</span></code></dt>
<dd>
<div class="desc"><p>return disk cleanup rules for this FileSystem Subclient</p>
<h2 id="return">Return</h2>
<p>(dict)
&ndash;
disk clean up rules</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1118-L1127" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def disk_cleanup_rules(self):
    &#34;&#34;&#34;
    return disk cleanup rules for this FileSystem Subclient

    Return:
        (dict)  --  disk clean up rules
    &#34;&#34;&#34;

    return self._fsSubClientProp[&#39;diskCleanupRules&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_synclib"><code class="name">var <span class="ident">enable_synclib</span></code></dt>
<dd>
<div class="desc"><p>Return the save while active options for an IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>(dict) &ndash;
Dictionary of synclib options</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1442-L1456" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_synclib(self):
    &#34;&#34;&#34;
    Return the save while active options for an IBMi subclient.

    Returns:
         (dict) --  Dictionary of synclib options
    &#34;&#34;&#34;
    return {
        &#39;saveWhileActiveOpt&#39;: self._fsSubClientProp[&#39;saveWhileActiveOpt&#39;],
        &#39;syncQueue&#39;: self._fsSubClientProp[&#39;syncQueue&#39;],
        &#39;syncAllLibForBackup&#39;: self._fsSubClientProp[&#39;syncAllLibForBackup&#39;],
        &#39;txtlibSyncCheckPoint&#39;: self._fsSubClientProp[&#39;txtlibSyncCheckPoint&#39;],
        &#39;activeWaitTime&#39;: self._fsSubClientProp[&#39;activeWaitTime&#39;]
    }</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.exception_content"><code class="name">var <span class="ident">exception_content</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient exception content as a property of the Subclient class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L533-L542" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def exception_content(self):
    &#34;&#34;&#34;Treats the subclient exception content as a property of the Subclient class.&#34;&#34;&#34;
    _exception_content = []

    for path in self._content:
        if &#39;includePath&#39; in path:
            _exception_content.append(path[&#34;includePath&#34;])

    return _exception_content</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.file_version"><code class="name">var <span class="ident">file_version</span></code></dt>
<dd>
<div class="desc"><h2 id="returns">Returns</h2>
<p>(dict)
&ndash;
file version mode</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1215-L1237" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def file_version(self):
    &#34;&#34;&#34;

    Returns:
                    (dict)  --  file version mode
    &#34;&#34;&#34;
    version = {}

    # For Indexing V1 client, this property is not supported. Taking default versions by number
    if &#39;olderFileVersionsMode&#39; not in self._fsSubClientProp:
        return {
            &#39;Mode&#39;: 2,
            &#39;DaysOrNumber&#39;: self._fsSubClientProp.get(&#39;keepAtLeastPreviousVersions&#39;, 0)
        }

    version[&#39;Mode&#39;] = self._fsSubClientProp[&#39;olderFileVersionsMode&#39;]
    modes = {
        1: self._fsSubClientProp[&#39;keepOlderVersionsForNDays&#39;],
        2: self._fsSubClientProp[&#39;keepVersions&#39;]
    }
    version[&#39;DaysOrNumber&#39;] = modes.get(version[&#39;Mode&#39;])
    return version</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.filter_content"><code class="name">var <span class="ident">filter_content</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient filter content as a property of the Subclient class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L497-L506" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def filter_content(self):
    &#34;&#34;&#34;Treats the subclient filter content as a property of the Subclient class.&#34;&#34;&#34;
    _filter_content = []

    for path in self._content:
        if &#39;excludePath&#39; in path:
            _filter_content.append(path[&#34;excludePath&#34;])

    return _filter_content</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.generate_signature_on_ibmi"><code class="name">var <span class="ident">generate_signature_on_ibmi</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of generate signature on ibmi option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>False
-
if signature generation on IBMi is enabled on the subclient</p>
<p>True
-
if signature generation on IBMi is not enabled on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1296-L1305" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def generate_signature_on_ibmi(self):
    &#34;&#34;&#34;Gets the value of generate signature on ibmi option for IBMi subclient.

        Returns:
            False   -   if signature generation on IBMi is enabled on the subclient

            True    -   if signature generation on IBMi is not enabled on the subclient
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;genSignatureOnIBMi&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.global_filter_status"><code class="name">var <span class="ident">global_filter_status</span></code></dt>
<dd>
<div class="desc"><p>Returns the status whether the global filters are included in configuration</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1417-L1422" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def global_filter_status(self):
    &#34;&#34;&#34;Returns the status whether the global filters are included in configuration&#34;&#34;&#34;
    for key, value in self._global_filter_status_dict.items():
        if self._fsSubClientProp.get(&#39;useGlobalFilters&#39;) == value:
            return key</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_compression"><code class="name">var <span class="ident">ibmi_compression</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of IBMi compression property on ibmi option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>(str)
-
Return the string value of IBMi compression property</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2433-L2440" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ibmi_compression(self):
    &#34;&#34;&#34;Gets the value of IBMi compression property on ibmi option for IBMi subclient.

        Returns:
            (str)   -   Return the string value of IBMi compression property
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;ibmiCompression&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_dr_config"><code class="name">var <span class="ident">ibmi_dr_config</span></code></dt>
<dd>
<div class="desc"><p>Return the ibmi dr configuration</p>
<h2 id="returns">Returns</h2>
<p>(dict)
&ndash;
Dictionary of DR parameters</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2211-L2225" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ibmi_dr_config(self):
    &#34;&#34;&#34;
    Return the ibmi dr configuration

    Returns:
        (dict)  --  Dictionary of DR parameters
    &#34;&#34;&#34;
    return {
        &#39;backupMaxTime&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;backupMaxTime&#39;, 0),
        &#39;printSysInfo&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;printSysInfo&#39;, False),
        &#39;userProgram&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;userProgram&#39;, &#39;&#39;),
        &#39;saveSecData&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;saveSecData&#39;, False),
        &#39;saveConfObject&#39;: self._fsSubClientProp.get(&#39;ibmiSubclientprop&#39;, {}).get(&#39;saveConfObject&#39;, False)
    }</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.impersonate_user"><code class="name">var <span class="ident">impersonate_user</span></code></dt>
<dd>
<div class="desc"><p>Returns the username ONLY and applicable to Windows FS subclients only.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2604-L2609" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def impersonate_user(self):
    &#34;&#34;&#34;
    Returns the username ONLY and applicable to Windows FS subclients only.
    &#34;&#34;&#34;
    return self._subclient_properties[&#39;impersonateUser&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_cycles_retention"><code class="name">var <span class="ident">index_pruning_cycles_retention</span></code></dt>
<dd>
<div class="desc"><p>Returns number of cycles to be maintained in index by index pruning for the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2158-L2162" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_pruning_cycles_retention(self):
    &#34;&#34;&#34;Returns number of cycles to be maintained in index by index pruning for the subclient&#34;&#34;&#34;

    return self._commonProperties[&#34;indexSettings&#34;][&#34;indexRetCycle&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_days_retention"><code class="name">var <span class="ident">index_pruning_days_retention</span></code></dt>
<dd>
<div class="desc"><p>Returns number of days to be maintained in index by index pruning for the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2152-L2156" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_pruning_days_retention(self):
    &#34;&#34;&#34;Returns number of days to be maintained in index by index pruning for the subclient&#34;&#34;&#34;

    return self._commonProperties[&#34;indexSettings&#34;][&#34;indexRetDays&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_type"><code class="name">var <span class="ident">index_pruning_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient pruning type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2143-L2150" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_pruning_type(self):
    &#34;&#34;&#34;Treats the subclient pruning type as a read-only attribute.&#34;&#34;&#34;

    index_settings = self._commonProperties[&#39;indexSettings&#39;]
    if &#39;indexPruningType&#39; in index_settings:
        pruning_type = index_settings[&#39;indexPruningType&#39;]
        return pruning_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_server"><code class="name">var <span class="ident">index_server</span></code></dt>
<dd>
<div class="desc"><p>Returns the index server client set for the subclient. None if no Index Server is set</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2100-L2117" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_server(self):
    &#34;&#34;&#34;Returns the index server client set for the subclient. None if no Index Server is set&#34;&#34;&#34;

    if &#39;indexSettings&#39; not in self._commonProperties:
        return None

    index_settings = self._commonProperties[&#39;indexSettings&#39;]
    index_server = None

    if (&#39;currentIndexServer&#39; in index_settings and
            &#39;clientName&#39; in index_settings[&#39;currentIndexServer&#39;]):
        index_server = index_settings[&#39;currentIndexServer&#39;][&#39;clientName&#39;]

    if index_server is None:
        return None

    return Client(self._commcell_object, client_name=index_server)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.network_share_auto_mount"><code class="name">var <span class="ident">network_share_auto_mount</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of enableNetworkShareAutoMount, if true, the content will be auto-mounted during backup and
auto-mounted during in-place restores.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2577-L2583" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def network_share_auto_mount(self):
    &#34;&#34;&#34;
    Returns the value of enableNetworkShareAutoMount, if true, the content will be auto-mounted during backup and
    auto-mounted during in-place restores.
    &#34;&#34;&#34;
    return self._fsSubClientProp[&#39;enableNetworkShareAutoMount&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.object_level_backup"><code class="name">var <span class="ident">object_level_backup</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of object level backup option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>True
-
if object level backup is enabled on the subclient</p>
<p>False
-
if object level backup is not enabled on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1394-L1403" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def object_level_backup(self):
    &#34;&#34;&#34;Gets the value of object level backup option for IBMi subclient.

        Returns:
            True    -   if object level backup is enabled on the subclient

            False   -   if object level backup is not enabled on the subclient
    &#34;&#34;&#34;
    return self._fsSubClientProp.get(&#39;backupAsObjects&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_option"><code class="name">var <span class="ident">onetouch_option</span></code></dt>
<dd>
<div class="desc"><p>Checks whether the onetouch option is enabled</p>
<h2 id="returns">Returns</h2>
<p>True
-
if system state property is enabled for the subclient</p>
<p>False
-
if system state property is not enabled for the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L898-L907" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def onetouch_option(self):
    &#34;&#34;&#34;Checks whether the onetouch option is enabled

    Returns:
        True    -   if system state property is enabled for the subclient

        False   -   if system state property is not enabled for the subclient
    &#34;&#34;&#34;
    return self._fsSubClientProp.get(&#39;oneTouchSubclient&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server"><code class="name">var <span class="ident">onetouch_server</span></code></dt>
<dd>
<div class="desc"><p>Returns: Onetouch Server Name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L916-L921" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def onetouch_server(self):
    &#34;&#34;&#34;
    Returns: Onetouch Server Name
    &#34;&#34;&#34;
    return self._fsSubClientProp.get(&#39;oneTouchServer&#39;, {}).get(&#39;clientName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server_directory"><code class="name">var <span class="ident">onetouch_server_directory</span></code></dt>
<dd>
<div class="desc"><p>Returns the onetouch server directory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L932-L937" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def onetouch_server_directory(self):
    &#34;&#34;&#34;
    Returns the onetouch server directory
    &#34;&#34;&#34;
    return self._fsSubClientProp.get(&#39;oneTouchServerDirectory&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.other_pending_changes"><code class="name">var <span class="ident">other_pending_changes</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of other pending changes for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>False
-
if multiple drives is not enabled.</p>
<p>True
-
if multiple drives is enabled.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1369-L1378" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def other_pending_changes(self):
    &#34;&#34;&#34;Gets the value of other pending changes for IBMi subclient.

        Returns:
            False   -   if multiple drives is not enabled.

            True    -   if multiple drives is enabled.
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;otherPendingChange&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.pending_record_changes"><code class="name">var <span class="ident">pending_record_changes</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of pending record changes option for
IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>False
-
if multiple drives is not enabled.</p>
<p>True
-
if multiple drives is enabled.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1344-L1353" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pending_record_changes(self):
    &#34;&#34;&#34;Gets the value of pending record changes option for  IBMi subclient.

        Returns:
            False   -   if multiple drives is not enabled.

            True    -   if multiple drives is enabled.
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;pendingRecordChange&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.pre_post_commands"><code class="name">var <span class="ident">pre_post_commands</span></code></dt>
<dd>
<div class="desc"><p>Return the prep_post commands set for a subclient</p>
<h2 id="returns">Returns</h2>
<p>(dict)
&ndash;
All the pre/post commands</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2499-L2514" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pre_post_commands(self):
    &#34;&#34;&#34;
     Return the prep_post commands set for a subclient

    Returns:
        (dict)  --  All the pre/post commands
    &#34;&#34;&#34;
    pre_scan_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;preScanCommand&#34;]
    post_scan_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;postScanCommand&#34;]
    pre_backup_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;preBackupCommand&#34;]
    post_backup_command = self._commonProperties[&#34;prepostProcess&#34;][&#34;postBackupCommand&#34;]

    pre_post_commands = {&#39;pre_scan_command&#39;: pre_scan_command, &#39;post_scan_command&#39;: post_scan_command, &#39;pre_backup_command&#39;: pre_backup_command, &#39;post_backup_command&#39;: post_backup_command}

    return pre_post_commands</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.save_access_path"><code class="name">var <span class="ident">save_access_path</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of save access path on ibmi option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>(str)
-
Return the save access path string value</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2387-L2394" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def save_access_path(self):
    &#34;&#34;&#34;Gets the value of save access path on ibmi option for IBMi subclient.

        Returns:
            (str)   -   Return the save access path string value
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;saveAccessPath&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.save_while_active_option"><code class="name">var <span class="ident">save_while_active_option</span></code></dt>
<dd>
<div class="desc"><p>Return the save while active options for an IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>(dict) &ndash;
Dictionary of save while active options</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2456-L2467" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def save_while_active_option(self):
    &#34;&#34;&#34;
    Return the save while active options for an IBMi subclient.

    Returns:
         (dict) --  Dictionary of save while active options
    &#34;&#34;&#34;
    return {
        &#39;saveWhileActiveOpt&#39;: self._fsSubClientProp[&#39;saveWhileActiveOpt&#39;],
        &#39;activeWaitTime&#39;: self._fsSubClientProp[&#39;activeWaitTime&#39;]
    }</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.scan_type"><code class="name">var <span class="ident">scan_type</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate scan type for this Subclient</p>
<h2 id="returns">Returns</h2>
<p>int
1
-
Recursive Scan
2
-
Optimized Scan
3
-
Change Journal Scan</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L569-L580" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def scan_type(self):
    &#34;&#34;&#34;Gets the appropriate scan type for this Subclient

        Returns:
            int
                1   -   Recursive Scan
                2   -   Optimized Scan
                3   -   Change Journal Scan

    &#34;&#34;&#34;
    return self._fsSubClientProp[&#39;scanOption&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.software_compression"><code class="name">var <span class="ident">software_compression</span></code></dt>
<dd>
<div class="desc"><p>Returns the software compression status for this subclient.</p>
<p>Returns:
int
1
-
On Client
2
-
On Media Agent
3
-
Use Storage Policy Settings
4
-
Off</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1494-L1505" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def software_compression(self):
    &#34;&#34;&#34;Returns the software compression status for this subclient.

        Returns:    int
                1   -   On Client
                2   -   On Media Agent
                3   -   Use Storage Policy Settings
                4   -   Off

    &#34;&#34;&#34;
    return self._fsSubClientProp[&#39;commonProperties&#39;][&#39;storageDevice&#39;][&#39;softwareCompression&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.system_state_option"><code class="name">var <span class="ident">system_state_option</span></code></dt>
<dd>
<div class="desc"><p>Checks whether the system state option is enabled</p>
<h2 id="returns">Returns</h2>
<p>True
-
if system state property is enabled for the subclient</p>
<p>False
-
if system state property is not enabled for the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L878-L887" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def system_state_option(self):
    &#34;&#34;&#34;Checks whether the system state option is enabled

    Returns:
        True    -   if system state property is enabled for the subclient

        False   -   if system state property is not enabled for the subclient
    &#34;&#34;&#34;
    return self._fsSubClientProp[&#39;backupSystemState&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.target_release"><code class="name">var <span class="ident">target_release</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of target and release on ibmi option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>(str)
-
Return the target and release string value</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2364-L2371" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def target_release(self):
    &#34;&#34;&#34;Gets the value of target and release on ibmi option for IBMi subclient.

        Returns:
            (str)   -   Return the target and release string value
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;targetReleaseForBackupData&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_days"><code class="name">var <span class="ident">trueup_days</span></code></dt>
<dd>
<div class="desc"><p>Gets the trueup after n days value for this Subclient</p>
<p>Returns: int</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L948-L955" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def trueup_days(self):
    &#34;&#34;&#34;Gets the trueup after n days value for this Subclient

        Returns: int
    &#34;&#34;&#34;

    return self._fsSubClientProp[&#39;runTrueUpJobAfterDaysForFS&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_option"><code class="name">var <span class="ident">trueup_option</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of TrueUp Option</p>
<h2 id="returns">Returns</h2>
<p>True
-
if trueup is enabled on the subclient</p>
<p>False
-
if trueup is not enabled on the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L606-L617" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def trueup_option(self):
    &#34;&#34;&#34;Gets the value of TrueUp Option

        Returns:
            True    -   if trueup is enabled on the subclient

            False   -   if trueup is not enabled on the subclient

    &#34;&#34;&#34;

    return self._fsSubClientProp[&#39;isTrueUpOptionEnabledForFS&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.update_history"><code class="name">var <span class="ident">update_history</span></code></dt>
<dd>
<div class="desc"><p>Gets the value of update history property on ibmi option for IBMi subclient.</p>
<h2 id="returns">Returns</h2>
<p>(str)
-
Return the string value of update history property</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2410-L2417" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def update_history(self):
    &#34;&#34;&#34;Gets the value of update history property on ibmi option for IBMi subclient.

        Returns:
            (str)   -   Return the string value of update history property
    &#34;&#34;&#34;
    return bool(self._fsSubClientProp.get(&#39;updateHistory&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.use_vss"><code class="name">var <span class="ident">use_vss</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of the Use VSS options for Windows FS subclients.</p>
<p>Returns:
dict</p>
<pre><code>Dictionary contains the keys 'useVSS', 'vssOptions' and 'useVssForAllFilesOptions'.

useVSS:
    True    -   ENABLED
    False   -   DISABLED

vssOptions:
    1   -   For all files
    2   -   For locked files only

useVssForAllFilesOptions:
    1   -   Fail the job
    2   -   Continue and reset access time
    3   -   Continue and do not reset access time
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1530-L1554" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def use_vss(self):
    &#34;&#34;&#34;Returns the value of the Use VSS options for Windows FS subclients.

        Returns:    dict

            Dictionary contains the keys &#39;useVSS&#39;, &#39;vssOptions&#39; and &#39;useVssForAllFilesOptions&#39;.

            useVSS:
                True    -   ENABLED
                False   -   DISABLED

            vssOptions:
                1   -   For all files
                2   -   For locked files only

            useVssForAllFilesOptions:
                1   -   Fail the job
                2   -   Continue and reset access time
                3   -   Continue and do not reset access time

    &#34;&#34;&#34;
    return {&#34;useVSS&#34;: self._fsSubClientProp[&#39;useVSS&#39;],
            &#34;vssOptions&#34;: self._fsSubClientProp[&#39;vssOptions&#39;],
            &#34;useVssForAllFilesOptions&#34;: self._fsSubClientProp[&#39;useVssForAllFilesOptions&#39;]}</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.add_comparison"><code class="name flex">
<span>def <span class="ident">add_comparison</span></span>(<span>self, name, source_backup_time, compare_with_time)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a comparison job for the subclient</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
The name of the comparison</p>
<p>source_backup_time
(int)
&ndash;
The epoch time of the source job's backup time. Ex: 1743167209</p>
<p>compare_with_time
(int)
&ndash;
The epoch time of the other job to compare.</p>
<h2 id="returns">Returns</h2>
<p>object - Instance of the Job class for the comparison job.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2727-L2801" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_comparison(self, name, source_backup_time, compare_with_time):
    &#34;&#34;&#34;Adds a comparison job for the subclient

        Args:
            name                (str)       --      The name of the comparison

            source_backup_time  (int)       --      The epoch time of the source job&#39;s backup time. Ex: 1743167209

            compare_with_time   (int)       --      The epoch time of the other job to compare.

        Returns:
            object - Instance of the Job class for the comparison job.

    &#34;&#34;&#34;

    request_json = {
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [
                {
                    &#34;clientName&#34;: self._client_object._client_name,
                    &#34;appName&#34;: self._agent_object._agent_name,
                    &#34;backupsetName&#34;: self._backupset_object._backupset_name,
                    &#34;subclientName&#34;: self._subclient_name,
                    &#34;_type_&#34;: 7
                }
            ],
            &#34;task&#34;: {
                &#34;taskType&#34;: 1
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 4025
                    },
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {},
                        &#34;adComparisonOption&#34;: {
                            &#34;leftSetTime&#34;: int(source_backup_time),
                            &#34;rightSetTime&#34;: int(compare_with_time),
                            &#34;adCompareType&#34;: 0,
                            &#34;nodeClientId&#34;: int(self._client_object.client_id),
                            &#34;clientId&#34;: int(self._client_object.client_id),
                            &#34;comparisonName&#34;: name,
                            &#34;appTypeId&#34;: 33,
                            &#34;subClientId&#34;: int(self.subclient_id),
                            &#34;adComparisonJobType&#34;: 0,
                            &#34;status&#34;: 0
                        }
                    }
                }
            ]
        }
    }

    comparison_task = self._commcell_object._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, comparison_task, request_json)

    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]

                o_str = &#39;Failed to start the comparison job.\nError: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, o_str)
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, &#39;Failed to start the comparison job&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, backup_level='Incremental', incremental_backup=False, incremental_level='BEFORE_SYNTH', collect_metadata=False, on_demand_input=None, advanced_options=None, schedule_pattern=None, common_backup_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a backup job for the subclient of the level specified.</p>
<h2 id="args">Args</h2>
<p>backup_level
(str)
&ndash;
level of backup the user wish to run
Full / Incremental / Differential / Synthetic_full
default: Incremental</p>
<p>incremental_backup
(bool)
&ndash;
run incremental backup
only applicable in case of Synthetic_full backup
default: False</p>
<p>incremental_level
(str)
&ndash;
run incremental backup before/after synthetic full
BEFORE_SYNTH / AFTER_SYNTH</p>
<pre><code>    only applicable in case of Synthetic_full backup
default: BEFORE_SYNTH
</code></pre>
<p>on_demand_input
(str)
&ndash;
input directive file location for on
demand subclient</p>
<pre><code>    only applicable in case of on demand subclient
default: None
</code></pre>
<p>advanced_options
(dict)
&ndash;
advanced backup options to be included while
making the request
default: None</p>
<pre><code>    options:
        directive_file          :   path to the directive file
        adhoc_backup            :   if set triggers the adhoc backup job
        adhoc_backup_contents   :   sets the contents for adhoc backup
        inline_backup_copy      :   to run backup copy immediately(inline)
        skip_catalog            :   skip catalog for intellisnap operation
        start_new_media         :   enables the option to start new media for the job
        media_agent_name        :   to run backup via this media agent
        impersonate_gui         :   sets the initiatedFrom property to GUI if True
        mark_media_full_on_success: boolean (True/False) that marks vols full on successful backup
</code></pre>
<p>common_backup_options
(dict)
&ndash;
advanced job options to be included while
making request</p>
<pre><code>    default: None

    options:
        job_description              :  job description to be set.

        enable_number_of_retries     :  enables/disables the property, number of retrys.
            values:
                True/False

        number_of_retries            : total number of retries to be set.

        enable_total_running_time    :  enables/disables the property, toal running time.
            values:
                True/False

        total_running_time           :  total run time to be set in (secs)

        kill_running_job_when_total_running_time_expires    :   enables/disables the property.
            values:
                True/False

        start_in_suspended_state     :  enables/disables the property.
            values:
                True/False

        use_default_priority         :  enables/disables the property.
            values:
                True/False

        priority                     :  three digit number to be set.
            default: 166
</code></pre>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job if its an immediate Job
instance of the Schedule class for the backup job if its a scheduled Job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level specified is not correct</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1634-L1773" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self,
           backup_level=&#34;Incremental&#34;,
           incremental_backup=False,
           incremental_level=&#39;BEFORE_SYNTH&#39;,
           collect_metadata=False,
           on_demand_input=None,
           advanced_options=None,
           schedule_pattern=None,
           common_backup_options=None):
    &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

        Args:
            backup_level        (str)   --  level of backup the user wish to run
                    Full / Incremental / Differential / Synthetic_full
                default: Incremental

            incremental_backup  (bool)  --  run incremental backup
                    only applicable in case of Synthetic_full backup
                default: False

            incremental_level   (str)   --  run incremental backup before/after synthetic full
                    BEFORE_SYNTH / AFTER_SYNTH

                    only applicable in case of Synthetic_full backup
                default: BEFORE_SYNTH

            on_demand_input     (str)   --  input directive file location for on
                                                demand subclient

                    only applicable in case of on demand subclient
                default: None

            advanced_options    (dict)  --  advanced backup options to be included while
                                                making the request
                    default: None

                    options:
                        directive_file          :   path to the directive file
                        adhoc_backup            :   if set triggers the adhoc backup job
                        adhoc_backup_contents   :   sets the contents for adhoc backup
                        inline_backup_copy      :   to run backup copy immediately(inline)
                        skip_catalog            :   skip catalog for intellisnap operation
                        start_new_media         :   enables the option to start new media for the job
                        media_agent_name        :   to run backup via this media agent
                        impersonate_gui         :   sets the initiatedFrom property to GUI if True
                        mark_media_full_on_success: boolean (True/False) that marks vols full on successful backup

            common_backup_options      (dict)  --  advanced job options to be included while
                                                    making request

                    default: None

                    options:
                        job_description              :  job description to be set.

                        enable_number_of_retries     :  enables/disables the property, number of retrys.
                            values:
                                True/False

                        number_of_retries            : total number of retries to be set.

                        enable_total_running_time    :  enables/disables the property, toal running time.
                            values:
                                True/False

                        total_running_time           :  total run time to be set in (secs)

                        kill_running_job_when_total_running_time_expires    :   enables/disables the property.
                            values:
                                True/False

                        start_in_suspended_state     :  enables/disables the property.
                            values:
                                True/False

                        use_default_priority         :  enables/disables the property.
                            values:
                                True/False

                        priority                     :  three digit number to be set.
                            default: 166

            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

        Returns:
            object - instance of the Job class for this backup job if its an immediate Job
                     instance of the Schedule class for the backup job if its a scheduled Job

        Raises:
            SDKException:
                if backup level specified is not correct

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if on_demand_input is not None:
        if not isinstance(on_demand_input, str):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not self.is_on_demand_subclient:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                &#39;On Demand backup is not supported for this subclient&#39;)

        if not advanced_options:
            advanced_options = {}

        advanced_options[&#39;on_demand_input&#39;] = on_demand_input

    if advanced_options or schedule_pattern or common_backup_options:
        request_json = self._backup_json(
            backup_level,
            incremental_backup,
            incremental_level,
            advanced_options,
            schedule_pattern,
            common_backup_options
        )

        backup_service = self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )

    else:
        return super(FileSystemSubclient, self).backup(
            backup_level=backup_level,
            incremental_backup=incremental_backup,
            incremental_level=incremental_level,
            collect_metadata=collect_metadata
        )

    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.disable_content_indexing"><code class="name flex">
<span>def <span class="ident">disable_content_indexing</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Content indexing and disassociate the CI policy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2067-L2071" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_content_indexing(self):
    &#34;&#34;&#34;Disables Content indexing and disassociate the CI policy&#34;&#34;&#34;
    update_properties = self.properties
    update_properties[&#39;fsSubClientProp&#39;][&#39;enableContentIndexing&#39;] = False
    self.update_properties(update_properties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_content_indexing"><code class="name flex">
<span>def <span class="ident">enable_content_indexing</span></span>(<span>self, policy_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables Content indexing and add the policy associations</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2060-L2065" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_content_indexing(self, policy_id):
    &#34;&#34;&#34;Enables Content indexing and add the policy associations&#34;&#34;&#34;
    update_properties = self.properties
    update_properties[&#39;fsSubClientProp&#39;][&#39;enableContentIndexing&#39;] = True
    update_properties[&#39;fsSubClientProp&#39;][&#39;contentIndexingPolicy&#39;] = int(policy_id)
    self.update_properties(update_properties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_dc_content_indexing"><code class="name flex">
<span>def <span class="ident">enable_dc_content_indexing</span></span>(<span>self, dcplan_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates the JSON with the specified dataclassification plan to pass to API to
update
file system Subclient</p>
<h2 id="args">Args</h2>
<p>dcplan_name (String)
&ndash;
DC plan name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L782-L792" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_dc_content_indexing(self, dcplan_name):
    &#34;&#34;&#34;Creates the JSON with the specified dataclassification plan to pass to API to
        update  file system Subclient

        Args:
            dcplan_name (String)  --  DC plan name

    &#34;&#34;&#34;
    temp_dc = self._dc_options_dict
    temp_dc[&#39;dcPlanEntity&#39;][&#39;planName&#39;] = dcplan_name
    self.update_properties(temp_dc)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.find_all_versions"><code class="name flex">
<span>def <span class="ident">find_all_versions</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches the content of a Subclient.</p>
<pre><code>Args:
    Dictionary of browse options:
        Example:
            find_all_versions({
                'path': 'c:\hello',
                'show_deleted': True,
                'from_time': '2014-04-20 12:00:00',
                'to_time': '2016-04-31 12:00:00'
            })

        (OR)

    Keyword argument of browse options:
        Example:
            find_all_versions(
                path='c:\hello.txt',
                show_deleted=True,
                to_time='2016-04-31 12:00:00'
            )

    Refer self._default_browse_options for all the supported options
</code></pre>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary of the specified file with list of all the file versions and
additional metadata retrieved from browse</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1596-L1632" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def find_all_versions(self, *args, **kwargs):
    &#34;&#34;&#34;Searches the content of a Subclient.

        Args:
            Dictionary of browse options:
                Example:
                    find_all_versions({
                        &#39;path&#39;: &#39;c:\\hello&#39;,
                        &#39;show_deleted&#39;: True,
                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,
                        &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                    })

                (OR)

            Keyword argument of browse options:
                Example:
                    find_all_versions(
                        path=&#39;c:\\hello.txt&#39;,
                        show_deleted=True,
                        to_time=&#39;2016-04-31 12:00:00&#39;
                    )

            Refer self._default_browse_options for all the supported options

    Returns:
        dict    -   dictionary of the specified file with list of all the file versions and
                        additional metadata retrieved from browse
    &#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs

    options[&#39;operation&#39;] = &#39;all_versions&#39;

    return self._backupset_object._do_browse(options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.preview_backedup_file"><code class="name flex">
<span>def <span class="ident">preview_backedup_file</span></span>(<span>self, file_path)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the preview content for the subclient.</p>
<h2 id="params">Params</h2>
<p>file_path (str) &ndash;
file path to get the preview content</p>
<h2 id="returns">Returns</h2>
<p>html
(str)
&ndash;
html content of the preview</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if file is not found</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L2709-L2725" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def preview_backedup_file(self, file_path):
    &#34;&#34;&#34;Gets the preview content for the subclient.
        Params:
            file_path (str) --  file path to get the preview content

        Returns:
            html   (str)   --  html content of the preview

        Raises:
            SDKException:
                if file is not found

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    return self._get_preview(file_path)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, fs_options=None, schedule_pattern=None, proxy_client=None, advanced_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of full paths of files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>fs_options
(dict)
&ndash; dictionary that includes all advanced options
options:
all_versions
: if set to True restores all the versions of the
specified file
versions
: list of version numbers to be backed up
validate_only
: To validate data backed up for restore</p>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<p>proxy_client
(str)
&ndash; Proxy client used during FS under NAS operations</p>
<p>advanced_options
(dict)
&ndash; Advanced restore options</p>
<pre><code>Options:

    job_description (str)   --  Restore job description

    timezone        (str)   --  Timezone to be used for restore

        **Note** make use of TIMEZONES dict in constants.py to pass timezone
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job if its an immediate Job
instance of the Schedule class for this restore job if its a scheduled Job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1775-L1874" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        fs_options=None,
        schedule_pattern=None,
        proxy_client=None,
        advanced_options=None
):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

        Args:
            paths                   (list)  --  list of full paths of files/folders to restore

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl    (bool)  --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time           (str)       --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            fs_options      (dict)          -- dictionary that includes all advanced options
                options:
                    all_versions        : if set to True restores all the versions of the
                                            specified file
                    versions            : list of version numbers to be backed up
                    validate_only       : To validate data backed up for restore


            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

            proxy_client    (str)          -- Proxy client used during FS under NAS operations

            advanced_options    (dict)  -- Advanced restore options

                Options:

                    job_description (str)   --  Restore job description

                    timezone        (str)   --  Timezone to be used for restore

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

        Returns:
            object - instance of the Job class for this restore job if its an immediate Job
                     instance of the Schedule class for this restore job if its a scheduled Job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    self._backupset_object._instance_object._restore_association = self._subClientEntity

    if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
        fs_options[&#39;destination_appTypeId&#39;] = int(self._client_object.agents.all_agents.get(&#39;file system&#39;, self._client_object.agents.all_agents.get(&#39;windows file system&#39;, self._client_object.agents.all_agents.get(&#39;linux file system&#39;, self._client_object.agents.all_agents.get(&#39;big data apps&#39;, self._client_object.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
        if not fs_options[&#39;destination_appTypeId&#39;]:
            del fs_options[&#39;destination_appTypeId&#39;]

    return super(FileSystemSubclient, self).restore_in_place(
        paths=paths,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        fs_options=fs_options,
        schedule_pattern=schedule_pattern,
        proxy_client=proxy_client,
        advanced_options=advanced_options
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, client, destination_path, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, fs_options=None, schedule_pattern=None, advanced_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destionation location.</p>
<h2 id="args">Args</h2>
<p>client
(str/object) &ndash;
either the name of the client or
the instance of the Client</p>
<p>destination_path
(str)
&ndash;
full path of the restore location on client</p>
<p>paths
(list)
&ndash;
list of full paths of
files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>fs_options
(dict)
&ndash; dictionary that includes all advanced options</p>
<pre><code>options:

    preserve_level          : preserve level option to set in restore

    proxy_client            : proxy that needed to be used for restore

    impersonate_user        : Impersonate user options for restore

    impersonate_password    : Impersonate password option for restore
    in base64 encoded form

    all_versions            : if set to True restores all the versions of the
    specified file

    versions                : list of version numbers to be backed up

    media_agent             : Media Agent need to be used for Browse and restore

    is_vlr_restore          : sets if the restore job is to be triggered as vlr

    validate_only           : To validate data backed up for restore

    instant_clone_options   : Options for FS clone found on Command Center, the value must be
    a dictionary containing the following key value pairs.

        reservation_time        (int)   --  The amount of time, specified in seconds, that the mounted
        snapshot needs to be reserved for before it is cleaned up.
        This is an OPTIONAL key.

            Default :   3600

        clone_mount_path        (str)   --  The path to which the snapshot needs to be mounted.
        This is NOT an optional key.

        post_clone_script       (str)   --  The script that will run post clone.
        This is an OPTIONAL key.

        clone_cleanup_script    (str)   --  The script that will run after clean up.
        This is an OPTIONAL key.

    no_of_streams   (int)       -- Number of streams to be used for restore
</code></pre>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<p>advanced_options
(dict)
&ndash; Advanced restore options</p>
<pre><code>Options:

    job_description (str)   --  Restore job description

    timezone        (str)   --  Timezone to be used for restore

        **Note** make use of TIMEZONES dict in constants.py to pass timezone
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client instance</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L1876-L2058" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        client,
        destination_path,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        fs_options=None,
        schedule_pattern=None,
        advanced_options=None
):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destionation location.

        Args:
            client                (str/object) --  either the name of the client or
                                                       the instance of the Client

            destination_path      (str)        --  full path of the restore location on client

            paths                 (list)       --  list of full paths of
                                                       files/folders to restore

            overwrite             (bool)       --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl  (bool)       --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time           (str)       --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            fs_options      (dict)          -- dictionary that includes all advanced options

                options:

                    preserve_level          : preserve level option to set in restore

                    proxy_client            : proxy that needed to be used for restore

                    impersonate_user        : Impersonate user options for restore

                    impersonate_password    : Impersonate password option for restore
                    in base64 encoded form

                    all_versions            : if set to True restores all the versions of the
                    specified file

                    versions                : list of version numbers to be backed up

                    media_agent             : Media Agent need to be used for Browse and restore

                    is_vlr_restore          : sets if the restore job is to be triggered as vlr

                    validate_only           : To validate data backed up for restore

                    instant_clone_options   : Options for FS clone found on Command Center, the value must be
                    a dictionary containing the following key value pairs.

                        reservation_time        (int)   --  The amount of time, specified in seconds, that the mounted
                        snapshot needs to be reserved for before it is cleaned up.
                        This is an OPTIONAL key.

                            Default :   3600

                        clone_mount_path        (str)   --  The path to which the snapshot needs to be mounted.
                        This is NOT an optional key.

                        post_clone_script       (str)   --  The script that will run post clone.
                        This is an OPTIONAL key.

                        clone_cleanup_script    (str)   --  The script that will run after clean up.
                        This is an OPTIONAL key.

                    no_of_streams   (int)       -- Number of streams to be used for restore

            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

            advanced_options    (dict)  -- Advanced restore options

                Options:

                    job_description (str)   --  Restore job description

                    timezone        (str)   --  Timezone to be used for restore

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or Client instance

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    self._backupset_object._instance_object._restore_association = self._subClientEntity

    if not isinstance(client, (str, Client)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if isinstance(client, str):
        client = Client(self._commcell_object, client)

    if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
        fs_options[&#39;destination_appTypeId&#39;] = int(client.agents.all_agents.get(&#39;file system&#39;, client.agents.all_agents.get(&#39;windows file system&#39;, client.agents.all_agents.get(&#39;linux file system&#39;, client.agents.all_agents.get(&#39;big data apps&#39;, client.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
        if not fs_options[&#39;destination_appTypeId&#39;]:
            del fs_options[&#39;destination_appTypeId&#39;]

        # check to find whether file level Restore/ Volume level restore for blocklevel.

    if fs_options is not None and fs_options.get(&#39;is_vlr_restore&#39;, False):
        if not (isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        paths = self._filter_paths(paths)

        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        request_json = self._restore_json(
            client=client,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            destPath=destination_path,
            restore_option=fs_options
        )

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;].update(
            self._vlr_restore_options_dict)
        destination_options = request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;].get(&#39;destination&#39;, {})
        destination_options[&#39;destPath&#39;] = destination_options.get(&#39;destPath&#39;, [&#39;&#39;])
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;destPath&#39;][0] = \
            destination_path
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;inPlace&#39;] = False

        return self._process_restore_response(request_json)

    else:
        return super(FileSystemSubclient, self).restore_out_of_place(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            schedule_pattern=schedule_pattern,
            advanced_options=advanced_options
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.fssubclient.FileSystemSubclient.run_backup_copy"><code class="name flex">
<span>def <span class="ident">run_backup_copy</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs the backup copy from Commcell for the given subclient</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup copy job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if backup copy job failed

    if response is empty

    if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/fssubclient.py#L633-L710" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_backup_copy(self):
    &#34;&#34;&#34;
    Runs the backup copy from Commcell for the given subclient

    Args:
            None

    Returns:
            object - instance of the Job class for this backup copy job
    Raises:
        SDKException:

                if backup copy job failed

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    request_json = {
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [
                {
                    &#34;clientName&#34;: self._client_object._client_name,
                    &#34;subclientName&#34;: self._subclient_name,
                    &#34;backupsetName&#34;: self._backupset_object._backupset_name,
                    &#34;storagePolicyName&#34;: self.storage_policy,
                    &#34;_type_&#34;: 17,
                    &#34;appName&#34;: self._agent_object._agent_name
                }
            ],
            &#34;task&#34;: {
                &#34;taskType&#34;: 1,
                &#34;initiatedFrom&#34;: 1,
                &#34;taskId&#34;: 0,
                &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 4028
                    },
                    &#34;options&#34;: {
                        &#34;adminOpts&#34;: {
                            &#34;snapToTapeOption&#34;: {
                                &#34;allowMaximum&#34;: True,
                                &#34;noofJobsToRun&#34;: 1
                            }
                        }
                    }
                }
            ]
        }
    }

    backup_copy = self._commcell_object._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, backup_copy, request_json)

    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]

                o_str = &#39;Backup copy job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, o_str)
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;118&#39;, &#39;Failed to run the backup copy job&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclient.Subclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#filesystemsubclient-instance-attributes">FileSystemSubclient Instance Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient">FileSystemSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.add_comparison" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.add_comparison">add_comparison</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention">archiver_retention</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention_days" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.archiver_retention_days">archiver_retention_days</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_nodes" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_nodes">backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_only_archiving_candidate" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_only_archiving_candidate">backup_only_archiving_candidate</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_private_authorities" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_private_authorities">backup_private_authorities</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_queue_data" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_queue_data">backup_queue_data</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention">backup_retention</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention_days" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_retention_days">backup_retention_days</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_savf_file_data" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_savf_file_data">backup_savf_file_data</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_spool_file_data" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_spool_file_data">backup_spool_file_data</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_using_multiple_drives" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.backup_using_multiple_drives">backup_using_multiple_drives</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.block_level_backup_option" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.block_level_backup_option">block_level_backup_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.catalog_acl" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.catalog_acl">catalog_acl</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.content" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.create_file_level_index_option" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.create_file_level_index_option">create_file_level_index_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.disable_content_indexing" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.disable_content_indexing">disable_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup">disk_cleanup</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup_rules" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.disk_cleanup_rules">disk_cleanup_rules</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_content_indexing" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_content_indexing">enable_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_dc_content_indexing" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_dc_content_indexing">enable_dc_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_synclib" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.enable_synclib">enable_synclib</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.exception_content" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.exception_content">exception_content</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.file_version" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.file_version">file_version</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.filter_content" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.filter_content">filter_content</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.find_all_versions" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.find_all_versions">find_all_versions</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.generate_signature_on_ibmi" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.generate_signature_on_ibmi">generate_signature_on_ibmi</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.global_filter_status" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.global_filter_status">global_filter_status</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_compression" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_compression">ibmi_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_dr_config" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.ibmi_dr_config">ibmi_dr_config</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.impersonate_user" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.impersonate_user">impersonate_user</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_cycles_retention" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_cycles_retention">index_pruning_cycles_retention</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_days_retention" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_days_retention">index_pruning_days_retention</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_type" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.index_pruning_type">index_pruning_type</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.index_server" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.index_server">index_server</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.network_share_auto_mount" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.network_share_auto_mount">network_share_auto_mount</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.object_level_backup" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.object_level_backup">object_level_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_option" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_option">onetouch_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server">onetouch_server</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server_directory" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.onetouch_server_directory">onetouch_server_directory</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.other_pending_changes" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.other_pending_changes">other_pending_changes</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.pending_record_changes" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.pending_record_changes">pending_record_changes</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.pre_post_commands" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.pre_post_commands">pre_post_commands</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.preview_backedup_file" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.preview_backedup_file">preview_backedup_file</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_in_place" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_out_of_place" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.run_backup_copy" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.run_backup_copy">run_backup_copy</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.save_access_path" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.save_access_path">save_access_path</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.save_while_active_option" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.save_while_active_option">save_while_active_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.scan_type" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.scan_type">scan_type</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.software_compression" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.system_state_option" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.system_state_option">system_state_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.target_release" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.target_release">target_release</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_days" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_days">trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_option" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.trueup_option">trueup_option</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.update_history" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.update_history">update_history</a></code></li>
<li><code><a title="cvpysdk.subclients.fssubclient.FileSystemSubclient.use_vss" href="#cvpysdk.subclients.fssubclient.FileSystemSubclient.use_vss">use_vss</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>