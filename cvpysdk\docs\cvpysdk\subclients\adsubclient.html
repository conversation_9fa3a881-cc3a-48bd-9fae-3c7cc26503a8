<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.adsubclient API documentation</title>
<meta name="description" content="File for Ad agent sublcient replated operation …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.adsubclient</code></h1>
</header>
<section id="section-intro">
<p>File for Ad agent sublcient replated operation</p>
<h2 id="class">Class</h2>
<p>ADSubclient :</p>
<pre><code>_get_subclient_properties()    --    Method to get subclinet properites

_get_subclient_properties_json --    Method to generate subclinet properties in json format

content                        --    Properties of AD objects in subclient

compare_id                     --    Method returns AD compare id

trigger_compare_job            --    Method to trigger AD compare job

checkcompare_result_generated  --    Method to check AD compare report generated

generate_compare_report        --    Method to generate AD compare report

restore_job                    --    Method to do AD point in time restore
</code></pre>
<h2 id="adsubclient">Adsubclient</h2>
<p>content() &ndash; method to get AD agent subclient content</p>
<p>Function:</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/adsubclient.py#L1-L371" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------
&#34;&#34;&#34;
File for Ad agent sublcient replated operation

Class:
    ADSubclient :

        _get_subclient_properties()    --    Method to get subclinet properites

        _get_subclient_properties_json --    Method to generate subclinet properties in json format

        content                        --    Properties of AD objects in subclient

        compare_id                     --    Method returns AD compare id

        trigger_compare_job            --    Method to trigger AD compare job

        checkcompare_result_generated  --    Method to check AD compare report generated

        generate_compare_report        --    Method to generate AD compare report

        restore_job                    --    Method to do AD point in time restore
ADSubclient:
    content() -- method to get AD agent subclient content

Function:
&#34;&#34;&#34;

from __future__ import unicode_literals

from ..exception import SDKException
from ..subclient import Subclient
import time

class ADSubclient(Subclient):
    &#34;&#34;&#34;Class for AD agent related subclient &#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of File System subclient.
        &#34;&#34;&#34;
        super()._get_subclient_properties()
        if &#39;impersonateUser&#39; in self._subclient_properties:
            self._impersonateUser = self._subclient_properties[&#39;impersonateUser&#39;]
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.
           Returns:
                dict - all subclient properties put inside a dict
        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;impersonateUser&#34;: self._impersonateUser,
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return subclient_json

    @property
    def content(self):
        &#34;&#34;&#34; Get AD agent subclient content&#34;&#34;&#34;
        contents = []
        if &#34;content&#34; in self._subclient_properties:
            subclient_content = self._subclient_properties[&#39;content&#39;]
        else:
            subclient_content = []
        if len(subclient_content) &gt; 0:
            for entry in subclient_content:
                if len(entry[&#39;path&#39;].split(&#39;,&#39;)) &gt; 1:
                    contents.append(&#34;,&#34;.join(entry[&#39;path&#39;].split(&#39;,&#39;)[1:]))
                else:
                    raise SDKException(&#39;Subclient&#39;, &#34;101&#34;,
                                       &#34;subclient content is not valid&#34;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                               &#34;subclient content return empty result&#34;)

        return contents

    def cv_contents(self, contents, entrypoint=None):
        &#34;&#34;&#34;Commvault subclient content convert to AD format
            Args:
                content    (list)    subclient content
                entrypoint    (string)    ad object entry point
            Return:
                (list)    ad format content
            Exception:
                None
        &#34;&#34;&#34;
        content_ad = []
        for content in contents:
            contententry = &#34;,&#34;.join(list(reversed(content.split(&#34;,&#34;))))
            if entrypoint is not None:
                shortdn = contententry.split(&#34;,{0}&#34;.format(entrypoint))[0]
            else:
                shortdn = None
            content_ad.append((contententry, shortdn))
        return content_ad

    def compare_id(self,left_set_time,right_set_time,
                   source_item,comparison_name):
        &#34;&#34;&#34;
        Generate Commvault AD Compare Comparison id
        Args:
             left_set_time  (int)      End Time of the first backup for comparison
             right_set_time (int)      End Time of the second backup for comparison
             source_item    (string)   sourceItem for comparison
             comparison_name(string)   name of the comparison
        Return:
            (int) Comparison ID
        Exception:
                Response was not a success
        &#34;&#34;&#34;
        client_id=int(self._client_object.client_id)
        subclient_id=int(self.subclient_id)

        payload = {
            &#34;adCompareOptions&#34;: {&#34;adCompareType&#34;: 0, &#34;comparisonName&#34;: comparison_name,
                                 &#34;subClientId&#34;: (subclient_id),
                                 &#34;appTypeId&#34;: 41, &#34;clientId&#34;: (client_id),
                                 &#34;nodeClientId&#34;: (client_id),
                                 &#34;leftSetTime&#34;: (left_set_time),
                                 &#34;rightSetTime&#34;: (right_set_time), &#34;status&#34;: 0,
                                 &#34;adComparisonJobType&#34;: 0,
                                 &#34;selectedItems&#34;: {&#34;sourceItem&#34;: [source_item]},
                                 &#34;includeUnchangedItems&#34;: False,
                                 &#34;excludeFrequentlyChanged&#34;: True}}
        generate_comparison_id = self._services[&#39;ADCOMPAREID&#39;] % (subclient_id)

        flag, response = self._cvpysdk_object.make_request(
           method= &#39;POST&#39;,url= generate_comparison_id,payload= payload
        )

        if flag:
            response_dict=response.json()
            comp_id = response_dict[&#39;adCompareDetails&#39;][0][&#39;comparisonId&#39;]


        else:

            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

        return comp_id

    def trigger_compare_job(self,left_set_time,
                            right_set_time,
                            display_name,client_name,
                            comp_id,source_item,comparison_name):
        &#34;&#34;&#34;
        Triggers Commvault AD compare job
        Args:
              left_set_time (int) End Time of the first backup for comparison
              right_set_time(int) End Time of the second backup for comparison
              display_name(str) displayName of the client
              client_name(str) name of the client
              comp_id(int) the comparison id for AD compare
              source_item(str)  sourceItem for comparison
              comparison_name(str) name of the comparison
        Return:
            None
        Exception:
                Response was not a success
        &#34;&#34;&#34;
        client_id = int(self._client_object.client_id)
        backupset_id=int(self._backupset_object._get_backupset_id())

        subclient_id = int(self.subclient_id)

        trigger_compare_job=self._services[&#39;CREATE_TASK&#39;]
        request_time=int(time.time())
        payload = {&#34;taskInfo&#34;: {&#34;associations&#34;: [
            {&#34;subclientId&#34;: (subclient_id), &#34;displayName&#34;:
                display_name, &#34;clientId&#34;: (client_id),
             &#34;applicationId&#34;: 41, &#34;clientName&#34;:
                 client_name, &#34;backupsetId&#34;: (backupset_id),
             &#34;_type_&#34;: &#34;SUBCLIENT_ENTITY&#34;}],
            &#34;task&#34;: {&#34;taskType&#34;: 1}, &#34;subTasks&#34;: [
            {&#34;subTask&#34;: {&#34;subTaskType&#34;: 1, &#34;operationType&#34;: 4025},
             &#34;options&#34;: {&#34;backupOpts&#34;: {},
                         &#34;commonOpts&#34;: {&#34;notifyUserOnJobCompletion&#34;: False},
                         &#34;adComparisonOption&#34;: {&#34;adCompareType&#34;: 0,
                                                &#34;comparisonName&#34;: comparison_name,
                                                &#34;subClientId&#34;: (subclient_id),
                                                &#34;appTypeId&#34;: 41,
                                                &#34;clientId&#34;: (client_id),
                                                &#34;nodeClientId&#34;: (client_id),
                                                &#34;leftSetTime&#34;: left_set_time,
                                                &#34;rightSetTime&#34;: right_set_time,
                                                &#34;status&#34;: 0,
                                                &#34;adComparisonJobType&#34;: 0,
                                                &#34;selectedItems&#34;: {&#34;sourceItem&#34;: [source_item]},
                                                &#34;includeUnchangedItems&#34;: False,
                                                &#34;excludeFrequentlyChanged&#34;: True,
                                                &#34;comparisonId&#34;: (comp_id),
                                                &#34;requestTime&#34;:request_time ,
                            &#34;selectionHash&#34;: &#34;D51A0DDBBFF582EEFA23A750DB90F1A46F6E90CE&#34;}}}]}}


        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;,
                                                                   url=trigger_compare_job,
                                                                     payload=payload)

        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))

    def checkcompare_result_generated(self,comp_id):
        &#34;&#34;&#34;
        Function to check AD comparison result generated and returns comparison cache path
        Args:
             comp_id(int): comparison id of AD compare operation
        Return:
            (string) comparisonCachePath
        Exception:
                Response was not a success
        &#34;&#34;&#34;
        compare_result = self._services[&#39;ADCOMPARESTATUSCHECK&#39;] % (comp_id)

        while True:

            flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;,
                                                                       url=compare_result)

            if flag:

                response_dict = response.json()
                error_message = response_dict[&#39;adCompareDetails&#39;][0][&#39;errorMessage&#39;]
                status = response_dict[&#39;adCompareDetails&#39;][0][&#39;status&#39;]
                comparison_cache_path = response_dict[&#39;adCompareDetails&#39;][0][&#39;sqlLiteDBCachePath&#39;]

                if (error_message == &#34;&#34; and status == 0 and comparison_cache_path != &#34;&#34;):
                    return comparison_cache_path
                if error_message != &#34;&#34;:

                    raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                                       self._commcell_objectj._update_response_(response.text))

                time.sleep(30)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                                   self._commcell_object._update_response_(response.text))

    def generate_compare_report(self,comp_id,comparison_cache_path,op_type=2):
        &#34;&#34;&#34;
        Return AD Compare report
            Args:
                comp_id (int) the comparison id generate
                comparison_cache_path (str) local directory where we write the comparison file
            Return:
                (json) AD Compare Report
            Exception:
                    Response was not a success
        &#34;&#34;&#34;
        client_id = int(self._client_object.client_id)
        subclient_id = int(self.subclient_id)
        ad_comp_url = self._services[&#39;ADCOMPAREVIEWRESULTS&#39;]
        payload = {&#34;isEnterprise&#34;: True, &#34;discoverySentTypes&#34;: [24],
                     &#34;subclientDetails&#34;: {&#34;subclientId&#34;: (subclient_id), &#34;appTypeId&#34;: 41,
                                          &#34;clientId&#34;: (client_id)},
                     &#34;adComparisonBrowseReq&#34;: {&#34;pageNumber&#34;: 0, &#34;comparisonId&#34;: comp_id,
                                               &#34;opType&#34;: op_type, &#34;browseItemId&#34;: 0,
                                               &#34;pageSize&#34;: 15, &#34;comparisonCachePath&#34;:
                                                   comparison_cache_path,
                                               &#34;searches&#34;:[{&#34;masks&#34;:[&#34;2&#34;,&#34;1&#34;,&#34;0&#34;],&#34;searchType&#34;:2}]}}


        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=ad_comp_url,
                                                                       payload=payload)

        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))

        return response.json()

    def restore_job(self,display_name,client_name,subclient_name,to_time,restore_path):
        &#34;&#34;&#34;
        Triggers AD restore job and waits for its completion
        Args:
              display_name(str) displayName of the client
              client_name(str) name of the client
              subclient_name(str) name of the subclient
              to_time(int) time from where to restore
              restore_path(str)  restore path
        Return:
             None
        Exception:
             Response was not a success
        &#34;&#34;&#34;
        client_id = int(self._client_object.client_id)
        subclient_id = int(self.subclient_id)
        backupset_id = int(self._backupset_object._get_backupset_id())
        client_guid=self._client_object.client_guid
        commcell_id=int(self._commcell_object.commcell_id)
        instance_id=int(self._instance_object.instance_id)

        payload={&#34;taskInfo&#34;:{&#34;associations&#34;:[{&#34;subclientId&#34;:subclient_id,
                                              &#34;displayName&#34;:display_name,
                                              &#34;clientId&#34;:client_id,
                                              &#34;entityInfo&#34;:{&#34;companyId&#34;:0,&#34;companyName&#34;:&#34;&#34;},
                                              &#34;instanceName&#34;:&#34;DefaultInstanceName&#34;,
                                              &#34;applicationId&#34;:41,
                                              &#34;clientName&#34;:client_name,
                                              &#34;backupsetId&#34;:backupset_id,
                                              &#34;instanceId&#34;:instance_id,
                                              &#34;clientGUID&#34;:client_guid,
                                              &#34;subclientName&#34;:subclient_name,
                                              &#34;backupsetName&#34;:&#34;defaultBackupSet&#34;,&#34;_type_&#34;:7}],
                                              &#34;task&#34;:{&#34;taskType&#34;:1,&#34;initiatedFrom&#34;:1},
                                              &#34;subTasks&#34;:[{&#34;subTask&#34;:
                                              {&#34;subTaskType&#34;:3,&#34;operationType&#34;:1001},
                                              &#34;options&#34;:{&#34;commonOpts&#34;:{
                                              &#34;notifyUserOnJobCompletion&#34;:False,
                                              &#34;jobMetadata&#34;:[{&#34;selectedItems&#34;:
                                              [{&#34;itemName&#34;:&#34;&#34;,&#34;itemType&#34;:&#34;&#34;}],
                                              &#34;jobOptionItems&#34;:[]}]},
                                              &#34;restoreOptions&#34;:{&#34;browseOption&#34;:
                                              {&#34;commCellId&#34;:commcell_id,
                                              &#34;noImage&#34;:False,&#34;useExactIndex&#34;:False,
                                              &#34;listMedia&#34;:False,&#34;backupset&#34;:
                                              {&#34;backupsetId&#34;:backupset_id,
                                              &#34;clientId&#34;:client_id},
                                              &#34;timeRange&#34;:{&#34;toTime&#34;:to_time},
                                              &#34;mediaOption&#34;:{&#34;copyPrecedence&#34;:
                                              {&#34;copyPrecedenceApplicable&#34;:False}}},
                                              &#34;destination&#34;:{&#34;inPlace&#34;:True,&#34;destClient&#34;:
                                              {&#34;clientId&#34;:client_id,&#34;clientName&#34;:client_name}},
                                              &#34;fileOption&#34;:{&#34;sourceItem&#34;:[restore_path]},
                                              &#34;activeDirectoryRstOption&#34;:{},
                                              &#34;commonOptions&#34;:{&#34;detectRegularExpression&#34;:True,
                                              &#34;preserveLevel&#34;:1,&#34;restoreACLs&#34;:True}}}}]}}

        trigger_response=self._services[&#39;CREATE_TASK&#39;]
        flag, response =self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=trigger_response,
                                                                   payload=payload)
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))
        if not response:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))
        response = response.json()
        if &#34;errorCode&#34; in response:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))
        # get restore job and wait for its completion
        job_id = response[&#34;jobIds&#34;][0]
        job = self._commcell_object.job_controller.get(job_id)
        job.wait_for_completion()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.adsubclient.ADSubclient"><code class="flex name class">
<span>class <span class="ident">ADSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for AD agent related subclient </p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/adsubclient.py#L51-L371" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ADSubclient(Subclient):
    &#34;&#34;&#34;Class for AD agent related subclient &#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of File System subclient.
        &#34;&#34;&#34;
        super()._get_subclient_properties()
        if &#39;impersonateUser&#39; in self._subclient_properties:
            self._impersonateUser = self._subclient_properties[&#39;impersonateUser&#39;]
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.
           Returns:
                dict - all subclient properties put inside a dict
        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;impersonateUser&#34;: self._impersonateUser,
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return subclient_json

    @property
    def content(self):
        &#34;&#34;&#34; Get AD agent subclient content&#34;&#34;&#34;
        contents = []
        if &#34;content&#34; in self._subclient_properties:
            subclient_content = self._subclient_properties[&#39;content&#39;]
        else:
            subclient_content = []
        if len(subclient_content) &gt; 0:
            for entry in subclient_content:
                if len(entry[&#39;path&#39;].split(&#39;,&#39;)) &gt; 1:
                    contents.append(&#34;,&#34;.join(entry[&#39;path&#39;].split(&#39;,&#39;)[1:]))
                else:
                    raise SDKException(&#39;Subclient&#39;, &#34;101&#34;,
                                       &#34;subclient content is not valid&#34;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                               &#34;subclient content return empty result&#34;)

        return contents

    def cv_contents(self, contents, entrypoint=None):
        &#34;&#34;&#34;Commvault subclient content convert to AD format
            Args:
                content    (list)    subclient content
                entrypoint    (string)    ad object entry point
            Return:
                (list)    ad format content
            Exception:
                None
        &#34;&#34;&#34;
        content_ad = []
        for content in contents:
            contententry = &#34;,&#34;.join(list(reversed(content.split(&#34;,&#34;))))
            if entrypoint is not None:
                shortdn = contententry.split(&#34;,{0}&#34;.format(entrypoint))[0]
            else:
                shortdn = None
            content_ad.append((contententry, shortdn))
        return content_ad

    def compare_id(self,left_set_time,right_set_time,
                   source_item,comparison_name):
        &#34;&#34;&#34;
        Generate Commvault AD Compare Comparison id
        Args:
             left_set_time  (int)      End Time of the first backup for comparison
             right_set_time (int)      End Time of the second backup for comparison
             source_item    (string)   sourceItem for comparison
             comparison_name(string)   name of the comparison
        Return:
            (int) Comparison ID
        Exception:
                Response was not a success
        &#34;&#34;&#34;
        client_id=int(self._client_object.client_id)
        subclient_id=int(self.subclient_id)

        payload = {
            &#34;adCompareOptions&#34;: {&#34;adCompareType&#34;: 0, &#34;comparisonName&#34;: comparison_name,
                                 &#34;subClientId&#34;: (subclient_id),
                                 &#34;appTypeId&#34;: 41, &#34;clientId&#34;: (client_id),
                                 &#34;nodeClientId&#34;: (client_id),
                                 &#34;leftSetTime&#34;: (left_set_time),
                                 &#34;rightSetTime&#34;: (right_set_time), &#34;status&#34;: 0,
                                 &#34;adComparisonJobType&#34;: 0,
                                 &#34;selectedItems&#34;: {&#34;sourceItem&#34;: [source_item]},
                                 &#34;includeUnchangedItems&#34;: False,
                                 &#34;excludeFrequentlyChanged&#34;: True}}
        generate_comparison_id = self._services[&#39;ADCOMPAREID&#39;] % (subclient_id)

        flag, response = self._cvpysdk_object.make_request(
           method= &#39;POST&#39;,url= generate_comparison_id,payload= payload
        )

        if flag:
            response_dict=response.json()
            comp_id = response_dict[&#39;adCompareDetails&#39;][0][&#39;comparisonId&#39;]


        else:

            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

        return comp_id

    def trigger_compare_job(self,left_set_time,
                            right_set_time,
                            display_name,client_name,
                            comp_id,source_item,comparison_name):
        &#34;&#34;&#34;
        Triggers Commvault AD compare job
        Args:
              left_set_time (int) End Time of the first backup for comparison
              right_set_time(int) End Time of the second backup for comparison
              display_name(str) displayName of the client
              client_name(str) name of the client
              comp_id(int) the comparison id for AD compare
              source_item(str)  sourceItem for comparison
              comparison_name(str) name of the comparison
        Return:
            None
        Exception:
                Response was not a success
        &#34;&#34;&#34;
        client_id = int(self._client_object.client_id)
        backupset_id=int(self._backupset_object._get_backupset_id())

        subclient_id = int(self.subclient_id)

        trigger_compare_job=self._services[&#39;CREATE_TASK&#39;]
        request_time=int(time.time())
        payload = {&#34;taskInfo&#34;: {&#34;associations&#34;: [
            {&#34;subclientId&#34;: (subclient_id), &#34;displayName&#34;:
                display_name, &#34;clientId&#34;: (client_id),
             &#34;applicationId&#34;: 41, &#34;clientName&#34;:
                 client_name, &#34;backupsetId&#34;: (backupset_id),
             &#34;_type_&#34;: &#34;SUBCLIENT_ENTITY&#34;}],
            &#34;task&#34;: {&#34;taskType&#34;: 1}, &#34;subTasks&#34;: [
            {&#34;subTask&#34;: {&#34;subTaskType&#34;: 1, &#34;operationType&#34;: 4025},
             &#34;options&#34;: {&#34;backupOpts&#34;: {},
                         &#34;commonOpts&#34;: {&#34;notifyUserOnJobCompletion&#34;: False},
                         &#34;adComparisonOption&#34;: {&#34;adCompareType&#34;: 0,
                                                &#34;comparisonName&#34;: comparison_name,
                                                &#34;subClientId&#34;: (subclient_id),
                                                &#34;appTypeId&#34;: 41,
                                                &#34;clientId&#34;: (client_id),
                                                &#34;nodeClientId&#34;: (client_id),
                                                &#34;leftSetTime&#34;: left_set_time,
                                                &#34;rightSetTime&#34;: right_set_time,
                                                &#34;status&#34;: 0,
                                                &#34;adComparisonJobType&#34;: 0,
                                                &#34;selectedItems&#34;: {&#34;sourceItem&#34;: [source_item]},
                                                &#34;includeUnchangedItems&#34;: False,
                                                &#34;excludeFrequentlyChanged&#34;: True,
                                                &#34;comparisonId&#34;: (comp_id),
                                                &#34;requestTime&#34;:request_time ,
                            &#34;selectionHash&#34;: &#34;D51A0DDBBFF582EEFA23A750DB90F1A46F6E90CE&#34;}}}]}}


        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;,
                                                                   url=trigger_compare_job,
                                                                     payload=payload)

        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))

    def checkcompare_result_generated(self,comp_id):
        &#34;&#34;&#34;
        Function to check AD comparison result generated and returns comparison cache path
        Args:
             comp_id(int): comparison id of AD compare operation
        Return:
            (string) comparisonCachePath
        Exception:
                Response was not a success
        &#34;&#34;&#34;
        compare_result = self._services[&#39;ADCOMPARESTATUSCHECK&#39;] % (comp_id)

        while True:

            flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;,
                                                                       url=compare_result)

            if flag:

                response_dict = response.json()
                error_message = response_dict[&#39;adCompareDetails&#39;][0][&#39;errorMessage&#39;]
                status = response_dict[&#39;adCompareDetails&#39;][0][&#39;status&#39;]
                comparison_cache_path = response_dict[&#39;adCompareDetails&#39;][0][&#39;sqlLiteDBCachePath&#39;]

                if (error_message == &#34;&#34; and status == 0 and comparison_cache_path != &#34;&#34;):
                    return comparison_cache_path
                if error_message != &#34;&#34;:

                    raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                                       self._commcell_objectj._update_response_(response.text))

                time.sleep(30)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                                   self._commcell_object._update_response_(response.text))

    def generate_compare_report(self,comp_id,comparison_cache_path,op_type=2):
        &#34;&#34;&#34;
        Return AD Compare report
            Args:
                comp_id (int) the comparison id generate
                comparison_cache_path (str) local directory where we write the comparison file
            Return:
                (json) AD Compare Report
            Exception:
                    Response was not a success
        &#34;&#34;&#34;
        client_id = int(self._client_object.client_id)
        subclient_id = int(self.subclient_id)
        ad_comp_url = self._services[&#39;ADCOMPAREVIEWRESULTS&#39;]
        payload = {&#34;isEnterprise&#34;: True, &#34;discoverySentTypes&#34;: [24],
                     &#34;subclientDetails&#34;: {&#34;subclientId&#34;: (subclient_id), &#34;appTypeId&#34;: 41,
                                          &#34;clientId&#34;: (client_id)},
                     &#34;adComparisonBrowseReq&#34;: {&#34;pageNumber&#34;: 0, &#34;comparisonId&#34;: comp_id,
                                               &#34;opType&#34;: op_type, &#34;browseItemId&#34;: 0,
                                               &#34;pageSize&#34;: 15, &#34;comparisonCachePath&#34;:
                                                   comparison_cache_path,
                                               &#34;searches&#34;:[{&#34;masks&#34;:[&#34;2&#34;,&#34;1&#34;,&#34;0&#34;],&#34;searchType&#34;:2}]}}


        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=ad_comp_url,
                                                                       payload=payload)

        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))

        return response.json()

    def restore_job(self,display_name,client_name,subclient_name,to_time,restore_path):
        &#34;&#34;&#34;
        Triggers AD restore job and waits for its completion
        Args:
              display_name(str) displayName of the client
              client_name(str) name of the client
              subclient_name(str) name of the subclient
              to_time(int) time from where to restore
              restore_path(str)  restore path
        Return:
             None
        Exception:
             Response was not a success
        &#34;&#34;&#34;
        client_id = int(self._client_object.client_id)
        subclient_id = int(self.subclient_id)
        backupset_id = int(self._backupset_object._get_backupset_id())
        client_guid=self._client_object.client_guid
        commcell_id=int(self._commcell_object.commcell_id)
        instance_id=int(self._instance_object.instance_id)

        payload={&#34;taskInfo&#34;:{&#34;associations&#34;:[{&#34;subclientId&#34;:subclient_id,
                                              &#34;displayName&#34;:display_name,
                                              &#34;clientId&#34;:client_id,
                                              &#34;entityInfo&#34;:{&#34;companyId&#34;:0,&#34;companyName&#34;:&#34;&#34;},
                                              &#34;instanceName&#34;:&#34;DefaultInstanceName&#34;,
                                              &#34;applicationId&#34;:41,
                                              &#34;clientName&#34;:client_name,
                                              &#34;backupsetId&#34;:backupset_id,
                                              &#34;instanceId&#34;:instance_id,
                                              &#34;clientGUID&#34;:client_guid,
                                              &#34;subclientName&#34;:subclient_name,
                                              &#34;backupsetName&#34;:&#34;defaultBackupSet&#34;,&#34;_type_&#34;:7}],
                                              &#34;task&#34;:{&#34;taskType&#34;:1,&#34;initiatedFrom&#34;:1},
                                              &#34;subTasks&#34;:[{&#34;subTask&#34;:
                                              {&#34;subTaskType&#34;:3,&#34;operationType&#34;:1001},
                                              &#34;options&#34;:{&#34;commonOpts&#34;:{
                                              &#34;notifyUserOnJobCompletion&#34;:False,
                                              &#34;jobMetadata&#34;:[{&#34;selectedItems&#34;:
                                              [{&#34;itemName&#34;:&#34;&#34;,&#34;itemType&#34;:&#34;&#34;}],
                                              &#34;jobOptionItems&#34;:[]}]},
                                              &#34;restoreOptions&#34;:{&#34;browseOption&#34;:
                                              {&#34;commCellId&#34;:commcell_id,
                                              &#34;noImage&#34;:False,&#34;useExactIndex&#34;:False,
                                              &#34;listMedia&#34;:False,&#34;backupset&#34;:
                                              {&#34;backupsetId&#34;:backupset_id,
                                              &#34;clientId&#34;:client_id},
                                              &#34;timeRange&#34;:{&#34;toTime&#34;:to_time},
                                              &#34;mediaOption&#34;:{&#34;copyPrecedence&#34;:
                                              {&#34;copyPrecedenceApplicable&#34;:False}}},
                                              &#34;destination&#34;:{&#34;inPlace&#34;:True,&#34;destClient&#34;:
                                              {&#34;clientId&#34;:client_id,&#34;clientName&#34;:client_name}},
                                              &#34;fileOption&#34;:{&#34;sourceItem&#34;:[restore_path]},
                                              &#34;activeDirectoryRstOption&#34;:{},
                                              &#34;commonOptions&#34;:{&#34;detectRegularExpression&#34;:True,
                                              &#34;preserveLevel&#34;:1,&#34;restoreACLs&#34;:True}}}}]}}

        trigger_response=self._services[&#39;CREATE_TASK&#39;]
        flag, response =self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=trigger_response,
                                                                   payload=payload)
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))
        if not response:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))
        response = response.json()
        if &#34;errorCode&#34; in response:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))
        # get restore job and wait for its completion
        job_id = response[&#34;jobIds&#34;][0]
        job = self._commcell_object.job_controller.get(job_id)
        job.wait_for_completion()</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.adsubclient.ADSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Get AD agent subclient content</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/adsubclient.py#L81-L100" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34; Get AD agent subclient content&#34;&#34;&#34;
    contents = []
    if &#34;content&#34; in self._subclient_properties:
        subclient_content = self._subclient_properties[&#39;content&#39;]
    else:
        subclient_content = []
    if len(subclient_content) &gt; 0:
        for entry in subclient_content:
            if len(entry[&#39;path&#39;].split(&#39;,&#39;)) &gt; 1:
                contents.append(&#34;,&#34;.join(entry[&#39;path&#39;].split(&#39;,&#39;)[1:]))
            else:
                raise SDKException(&#39;Subclient&#39;, &#34;101&#34;,
                                   &#34;subclient content is not valid&#34;)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;,
                           &#34;subclient content return empty result&#34;)

    return contents</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.adsubclient.ADSubclient.checkcompare_result_generated"><code class="name flex">
<span>def <span class="ident">checkcompare_result_generated</span></span>(<span>self, comp_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Function to check AD comparison result generated and returns comparison cache path</p>
<h2 id="args">Args</h2>
<p>comp_id(int): comparison id of AD compare operation</p>
<h2 id="return">Return</h2>
<p>(string) comparisonCachePath</p>
<h2 id="exception">Exception</h2>
<p>Response was not a success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/adsubclient.py#L229-L263" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def checkcompare_result_generated(self,comp_id):
    &#34;&#34;&#34;
    Function to check AD comparison result generated and returns comparison cache path
    Args:
         comp_id(int): comparison id of AD compare operation
    Return:
        (string) comparisonCachePath
    Exception:
            Response was not a success
    &#34;&#34;&#34;
    compare_result = self._services[&#39;ADCOMPARESTATUSCHECK&#39;] % (comp_id)

    while True:

        flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;,
                                                                   url=compare_result)

        if flag:

            response_dict = response.json()
            error_message = response_dict[&#39;adCompareDetails&#39;][0][&#39;errorMessage&#39;]
            status = response_dict[&#39;adCompareDetails&#39;][0][&#39;status&#39;]
            comparison_cache_path = response_dict[&#39;adCompareDetails&#39;][0][&#39;sqlLiteDBCachePath&#39;]

            if (error_message == &#34;&#34; and status == 0 and comparison_cache_path != &#34;&#34;):
                return comparison_cache_path
            if error_message != &#34;&#34;:

                raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                                   self._commcell_objectj._update_response_(response.text))

            time.sleep(30)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.adsubclient.ADSubclient.compare_id"><code class="name flex">
<span>def <span class="ident">compare_id</span></span>(<span>self, left_set_time, right_set_time, source_item, comparison_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Generate Commvault AD Compare Comparison id</p>
<h2 id="args">Args</h2>
<p>left_set_time
(int)
End Time of the first backup for comparison
right_set_time (int)
End Time of the second backup for comparison
source_item
(string)
sourceItem for comparison
comparison_name(string)
name of the comparison</p>
<h2 id="return">Return</h2>
<p>(int) Comparison ID</p>
<h2 id="exception">Exception</h2>
<p>Response was not a success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/adsubclient.py#L122-L165" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def compare_id(self,left_set_time,right_set_time,
               source_item,comparison_name):
    &#34;&#34;&#34;
    Generate Commvault AD Compare Comparison id
    Args:
         left_set_time  (int)      End Time of the first backup for comparison
         right_set_time (int)      End Time of the second backup for comparison
         source_item    (string)   sourceItem for comparison
         comparison_name(string)   name of the comparison
    Return:
        (int) Comparison ID
    Exception:
            Response was not a success
    &#34;&#34;&#34;
    client_id=int(self._client_object.client_id)
    subclient_id=int(self.subclient_id)

    payload = {
        &#34;adCompareOptions&#34;: {&#34;adCompareType&#34;: 0, &#34;comparisonName&#34;: comparison_name,
                             &#34;subClientId&#34;: (subclient_id),
                             &#34;appTypeId&#34;: 41, &#34;clientId&#34;: (client_id),
                             &#34;nodeClientId&#34;: (client_id),
                             &#34;leftSetTime&#34;: (left_set_time),
                             &#34;rightSetTime&#34;: (right_set_time), &#34;status&#34;: 0,
                             &#34;adComparisonJobType&#34;: 0,
                             &#34;selectedItems&#34;: {&#34;sourceItem&#34;: [source_item]},
                             &#34;includeUnchangedItems&#34;: False,
                             &#34;excludeFrequentlyChanged&#34;: True}}
    generate_comparison_id = self._services[&#39;ADCOMPAREID&#39;] % (subclient_id)

    flag, response = self._cvpysdk_object.make_request(
       method= &#39;POST&#39;,url= generate_comparison_id,payload= payload
    )

    if flag:
        response_dict=response.json()
        comp_id = response_dict[&#39;adCompareDetails&#39;][0][&#39;comparisonId&#39;]


    else:

        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    return comp_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.adsubclient.ADSubclient.cv_contents"><code class="name flex">
<span>def <span class="ident">cv_contents</span></span>(<span>self, contents, entrypoint=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Commvault subclient content convert to AD format</p>
<h2 id="args">Args</h2>
<p>content
(list)
subclient content
entrypoint
(string)
ad object entry point</p>
<h2 id="return">Return</h2>
<p>(list)
ad format content</p>
<h2 id="exception">Exception</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/adsubclient.py#L102-L120" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def cv_contents(self, contents, entrypoint=None):
    &#34;&#34;&#34;Commvault subclient content convert to AD format
        Args:
            content    (list)    subclient content
            entrypoint    (string)    ad object entry point
        Return:
            (list)    ad format content
        Exception:
            None
    &#34;&#34;&#34;
    content_ad = []
    for content in contents:
        contententry = &#34;,&#34;.join(list(reversed(content.split(&#34;,&#34;))))
        if entrypoint is not None:
            shortdn = contententry.split(&#34;,{0}&#34;.format(entrypoint))[0]
        else:
            shortdn = None
        content_ad.append((contententry, shortdn))
    return content_ad</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.adsubclient.ADSubclient.generate_compare_report"><code class="name flex">
<span>def <span class="ident">generate_compare_report</span></span>(<span>self, comp_id, comparison_cache_path, op_type=2)</span>
</code></dt>
<dd>
<div class="desc"><p>Return AD Compare report
Args:
comp_id (int) the comparison id generate
comparison_cache_path (str) local directory where we write the comparison file
Return:
(json) AD Compare Report
Exception:
Response was not a success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/adsubclient.py#L265-L296" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def generate_compare_report(self,comp_id,comparison_cache_path,op_type=2):
    &#34;&#34;&#34;
    Return AD Compare report
        Args:
            comp_id (int) the comparison id generate
            comparison_cache_path (str) local directory where we write the comparison file
        Return:
            (json) AD Compare Report
        Exception:
                Response was not a success
    &#34;&#34;&#34;
    client_id = int(self._client_object.client_id)
    subclient_id = int(self.subclient_id)
    ad_comp_url = self._services[&#39;ADCOMPAREVIEWRESULTS&#39;]
    payload = {&#34;isEnterprise&#34;: True, &#34;discoverySentTypes&#34;: [24],
                 &#34;subclientDetails&#34;: {&#34;subclientId&#34;: (subclient_id), &#34;appTypeId&#34;: 41,
                                      &#34;clientId&#34;: (client_id)},
                 &#34;adComparisonBrowseReq&#34;: {&#34;pageNumber&#34;: 0, &#34;comparisonId&#34;: comp_id,
                                           &#34;opType&#34;: op_type, &#34;browseItemId&#34;: 0,
                                           &#34;pageSize&#34;: 15, &#34;comparisonCachePath&#34;:
                                               comparison_cache_path,
                                           &#34;searches&#34;:[{&#34;masks&#34;:[&#34;2&#34;,&#34;1&#34;,&#34;0&#34;],&#34;searchType&#34;:2}]}}


    flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=ad_comp_url,
                                                                   payload=payload)

    if not flag:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._commcell_object._update_response_(response.text))

    return response.json()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.adsubclient.ADSubclient.restore_job"><code class="name flex">
<span>def <span class="ident">restore_job</span></span>(<span>self, display_name, client_name, subclient_name, to_time, restore_path)</span>
</code></dt>
<dd>
<div class="desc"><p>Triggers AD restore job and waits for its completion</p>
<h2 id="args">Args</h2>
<p>display_name(str) displayName of the client
client_name(str) name of the client
subclient_name(str) name of the subclient
to_time(int) time from where to restore
restore_path(str)
restore path</p>
<h2 id="return">Return</h2>
<p>None</p>
<h2 id="exception">Exception</h2>
<p>Response was not a success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/adsubclient.py#L298-L371" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_job(self,display_name,client_name,subclient_name,to_time,restore_path):
    &#34;&#34;&#34;
    Triggers AD restore job and waits for its completion
    Args:
          display_name(str) displayName of the client
          client_name(str) name of the client
          subclient_name(str) name of the subclient
          to_time(int) time from where to restore
          restore_path(str)  restore path
    Return:
         None
    Exception:
         Response was not a success
    &#34;&#34;&#34;
    client_id = int(self._client_object.client_id)
    subclient_id = int(self.subclient_id)
    backupset_id = int(self._backupset_object._get_backupset_id())
    client_guid=self._client_object.client_guid
    commcell_id=int(self._commcell_object.commcell_id)
    instance_id=int(self._instance_object.instance_id)

    payload={&#34;taskInfo&#34;:{&#34;associations&#34;:[{&#34;subclientId&#34;:subclient_id,
                                          &#34;displayName&#34;:display_name,
                                          &#34;clientId&#34;:client_id,
                                          &#34;entityInfo&#34;:{&#34;companyId&#34;:0,&#34;companyName&#34;:&#34;&#34;},
                                          &#34;instanceName&#34;:&#34;DefaultInstanceName&#34;,
                                          &#34;applicationId&#34;:41,
                                          &#34;clientName&#34;:client_name,
                                          &#34;backupsetId&#34;:backupset_id,
                                          &#34;instanceId&#34;:instance_id,
                                          &#34;clientGUID&#34;:client_guid,
                                          &#34;subclientName&#34;:subclient_name,
                                          &#34;backupsetName&#34;:&#34;defaultBackupSet&#34;,&#34;_type_&#34;:7}],
                                          &#34;task&#34;:{&#34;taskType&#34;:1,&#34;initiatedFrom&#34;:1},
                                          &#34;subTasks&#34;:[{&#34;subTask&#34;:
                                          {&#34;subTaskType&#34;:3,&#34;operationType&#34;:1001},
                                          &#34;options&#34;:{&#34;commonOpts&#34;:{
                                          &#34;notifyUserOnJobCompletion&#34;:False,
                                          &#34;jobMetadata&#34;:[{&#34;selectedItems&#34;:
                                          [{&#34;itemName&#34;:&#34;&#34;,&#34;itemType&#34;:&#34;&#34;}],
                                          &#34;jobOptionItems&#34;:[]}]},
                                          &#34;restoreOptions&#34;:{&#34;browseOption&#34;:
                                          {&#34;commCellId&#34;:commcell_id,
                                          &#34;noImage&#34;:False,&#34;useExactIndex&#34;:False,
                                          &#34;listMedia&#34;:False,&#34;backupset&#34;:
                                          {&#34;backupsetId&#34;:backupset_id,
                                          &#34;clientId&#34;:client_id},
                                          &#34;timeRange&#34;:{&#34;toTime&#34;:to_time},
                                          &#34;mediaOption&#34;:{&#34;copyPrecedence&#34;:
                                          {&#34;copyPrecedenceApplicable&#34;:False}}},
                                          &#34;destination&#34;:{&#34;inPlace&#34;:True,&#34;destClient&#34;:
                                          {&#34;clientId&#34;:client_id,&#34;clientName&#34;:client_name}},
                                          &#34;fileOption&#34;:{&#34;sourceItem&#34;:[restore_path]},
                                          &#34;activeDirectoryRstOption&#34;:{},
                                          &#34;commonOptions&#34;:{&#34;detectRegularExpression&#34;:True,
                                          &#34;preserveLevel&#34;:1,&#34;restoreACLs&#34;:True}}}}]}}

    trigger_response=self._services[&#39;CREATE_TASK&#39;]
    flag, response =self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=trigger_response,
                                                               payload=payload)
    if not flag:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._commcell_object._update_response_(response.text))
    if not response:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._commcell_object._update_response_(response.text))
    response = response.json()
    if &#34;errorCode&#34; in response:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._commcell_object._update_response_(response.text))
    # get restore job and wait for its completion
    job_id = response[&#34;jobIds&#34;][0]
    job = self._commcell_object.job_controller.get(job_id)
    job.wait_for_completion()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.adsubclient.ADSubclient.trigger_compare_job"><code class="name flex">
<span>def <span class="ident">trigger_compare_job</span></span>(<span>self, left_set_time, right_set_time, display_name, client_name, comp_id, source_item, comparison_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Triggers Commvault AD compare job</p>
<h2 id="args">Args</h2>
<p>left_set_time (int) End Time of the first backup for comparison
right_set_time(int) End Time of the second backup for comparison
display_name(str) displayName of the client
client_name(str) name of the client
comp_id(int) the comparison id for AD compare
source_item(str)
sourceItem for comparison
comparison_name(str) name of the comparison</p>
<h2 id="return">Return</h2>
<p>None</p>
<h2 id="exception">Exception</h2>
<p>Response was not a success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/adsubclient.py#L167-L227" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def trigger_compare_job(self,left_set_time,
                        right_set_time,
                        display_name,client_name,
                        comp_id,source_item,comparison_name):
    &#34;&#34;&#34;
    Triggers Commvault AD compare job
    Args:
          left_set_time (int) End Time of the first backup for comparison
          right_set_time(int) End Time of the second backup for comparison
          display_name(str) displayName of the client
          client_name(str) name of the client
          comp_id(int) the comparison id for AD compare
          source_item(str)  sourceItem for comparison
          comparison_name(str) name of the comparison
    Return:
        None
    Exception:
            Response was not a success
    &#34;&#34;&#34;
    client_id = int(self._client_object.client_id)
    backupset_id=int(self._backupset_object._get_backupset_id())

    subclient_id = int(self.subclient_id)

    trigger_compare_job=self._services[&#39;CREATE_TASK&#39;]
    request_time=int(time.time())
    payload = {&#34;taskInfo&#34;: {&#34;associations&#34;: [
        {&#34;subclientId&#34;: (subclient_id), &#34;displayName&#34;:
            display_name, &#34;clientId&#34;: (client_id),
         &#34;applicationId&#34;: 41, &#34;clientName&#34;:
             client_name, &#34;backupsetId&#34;: (backupset_id),
         &#34;_type_&#34;: &#34;SUBCLIENT_ENTITY&#34;}],
        &#34;task&#34;: {&#34;taskType&#34;: 1}, &#34;subTasks&#34;: [
        {&#34;subTask&#34;: {&#34;subTaskType&#34;: 1, &#34;operationType&#34;: 4025},
         &#34;options&#34;: {&#34;backupOpts&#34;: {},
                     &#34;commonOpts&#34;: {&#34;notifyUserOnJobCompletion&#34;: False},
                     &#34;adComparisonOption&#34;: {&#34;adCompareType&#34;: 0,
                                            &#34;comparisonName&#34;: comparison_name,
                                            &#34;subClientId&#34;: (subclient_id),
                                            &#34;appTypeId&#34;: 41,
                                            &#34;clientId&#34;: (client_id),
                                            &#34;nodeClientId&#34;: (client_id),
                                            &#34;leftSetTime&#34;: left_set_time,
                                            &#34;rightSetTime&#34;: right_set_time,
                                            &#34;status&#34;: 0,
                                            &#34;adComparisonJobType&#34;: 0,
                                            &#34;selectedItems&#34;: {&#34;sourceItem&#34;: [source_item]},
                                            &#34;includeUnchangedItems&#34;: False,
                                            &#34;excludeFrequentlyChanged&#34;: True,
                                            &#34;comparisonId&#34;: (comp_id),
                                            &#34;requestTime&#34;:request_time ,
                        &#34;selectionHash&#34;: &#34;D51A0DDBBFF582EEFA23A750DB90F1A46F6E90CE&#34;}}}]}}


    flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;,
                                                               url=trigger_compare_job,
                                                                 payload=payload)

    if not flag:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclient.Subclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.backup" href="../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_in_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.adsubclient.ADSubclient" href="#cvpysdk.subclients.adsubclient.ADSubclient">ADSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.adsubclient.ADSubclient.checkcompare_result_generated" href="#cvpysdk.subclients.adsubclient.ADSubclient.checkcompare_result_generated">checkcompare_result_generated</a></code></li>
<li><code><a title="cvpysdk.subclients.adsubclient.ADSubclient.compare_id" href="#cvpysdk.subclients.adsubclient.ADSubclient.compare_id">compare_id</a></code></li>
<li><code><a title="cvpysdk.subclients.adsubclient.ADSubclient.content" href="#cvpysdk.subclients.adsubclient.ADSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.adsubclient.ADSubclient.cv_contents" href="#cvpysdk.subclients.adsubclient.ADSubclient.cv_contents">cv_contents</a></code></li>
<li><code><a title="cvpysdk.subclients.adsubclient.ADSubclient.generate_compare_report" href="#cvpysdk.subclients.adsubclient.ADSubclient.generate_compare_report">generate_compare_report</a></code></li>
<li><code><a title="cvpysdk.subclients.adsubclient.ADSubclient.restore_job" href="#cvpysdk.subclients.adsubclient.ADSubclient.restore_job">restore_job</a></code></li>
<li><code><a title="cvpysdk.subclients.adsubclient.ADSubclient.trigger_compare_job" href="#cvpysdk.subclients.adsubclient.ADSubclient.trigger_compare_job">trigger_compare_job</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>