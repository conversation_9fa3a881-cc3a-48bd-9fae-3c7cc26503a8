<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.sharepointsubclient API documentation</title>
<meta name="description" content="File for operating on a Sharepoint Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.sharepointsubclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Sharepoint Subclient</p>
<p>SharepointSuperSubclient: Derived class from Subclient Base class, containing common methods for both Sharepoint v1 and v2 subclients.</p>
<h2 id="sharepointsupersubclient">Sharepointsupersubclient</h2>
<p>backup()
&ndash;
Runs a backup job for the subclient of the level specified.</p>
<p>_get_subclient_properties()
&ndash;
gets the subclient related properties of the Sharepoint subclient.</p>
<p>_json_out_of_place_destination_option() &ndash; setter for the SharePoint Online out of place restore
option in restore json</p>
<p>SharepointSubclient: Derived class from SharepointSuperSubclient Base class, representing a sharepoint subclient,
and to perform operations on that subclient</p>
<h2 id="sharepointsubclient">Sharepointsubclient</h2>
<p>_get_subclient_properties_json()
&ndash;
gets all the subclient related properties of the Sharepoint subclient.</p>
<p>_process_restore_response
&ndash;
processes response received for the restore request.</p>
<p>_restore_request_json
&ndash;
returns the JSON request to pass to the API as per the options.</p>
<p>sharepoint_subclient_prop()
&ndash;
initializes additional properties of this subclient.</p>
<p>content()
&ndash;
sets the content of the subclient.</p>
<p>restore()
&ndash;
restores the databases specified in the input paths list.</p>
<p>add_azure_app()
&ndash;
adds a single azure app to the sharepoint client</p>
<p>delete_azure_app()
&ndash;
deletes one/multiple azure app(s) from the sharepoint client</p>
<p>run_manual_discovery()
&ndash;
runs the manual disocvery for specified backupset</p>
<p>browse_for_content()
&ndash;
returns the user association content</p>
<p>associate_site_collections_and_webs()&ndash; associates the specified site collections/webs</p>
<p>delete_data()
&ndash;
delete backed up data from sharepoint clients</p>
<p>restore_in_place()
&ndash;
runs an in-place restore job on the specified Sharepoint pseudo client</p>
<p>restore_in_place_syntex()
&ndash;
runs an in-place restore job on the specified Sharepoint Syntex pseudo client</p>
<p>refresh_license_collection()
&ndash;
runs a license collection process</p>
<p>preview_backedup_file()
&ndash;
gets the preview content for the file</p>
<p>SharepointV1Subclient: Derived class from SharepointSuperSubclient Base class, representing a sharepoint v1 subclient,
and to perform operations on that subclient</p>
<h2 id="sharepointv1subclient">Sharepointv1Subclient</h2>
<p>discover_sharepoint_sites()
&ndash;
Checks whether SP content i.e, sites/webs are available</p>
<p>_get_subclient_properties_json()
&ndash;
gets all the subclient related properties of the Sharepoint subclient.</p>
<p>content()
&ndash;
sets the content of the subclient.</p>
<p>restore_in_place()
&ndash;
runs a in-place restore job on the specified Sharepoint pseudo client</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1-L1616" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# ————————————————————————–
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ————————————————————————–

&#34;&#34;&#34;File for operating on a Sharepoint Subclient

SharepointSuperSubclient: Derived class from Subclient Base class, containing common methods for both Sharepoint v1 and v2 subclients.

SharepointSuperSubclient:

    backup()                            --  Runs a backup job for the subclient of the level specified.
    
    _get_subclient_properties()         --  gets the subclient related properties of the Sharepoint subclient.
    
    _json_out_of_place_destination_option() -- setter for the SharePoint Online out of place restore
        option in restore json
    

SharepointSubclient: Derived class from SharepointSuperSubclient Base class, representing a sharepoint subclient,
and to perform operations on that subclient

SharepointSubclient:


    _get_subclient_properties_json()    --  gets all the subclient related properties of the Sharepoint subclient.

    _process_restore_response           --  processes response received for the restore request.

    _restore_request_json               --  returns the JSON request to pass to the API as per the options.

    sharepoint_subclient_prop()         --  initializes additional properties of this subclient.

    content()                           --  sets the content of the subclient.

    restore()                           --  restores the databases specified in the input paths list.

    add_azure_app()                     --  adds a single azure app to the sharepoint client

    delete_azure_app()                  --  deletes one/multiple azure app(s) from the sharepoint client

    run_manual_discovery()              --  runs the manual disocvery for specified backupset

    browse_for_content()                --  returns the user association content

    associate_site_collections_and_webs()-- associates the specified site collections/webs

    delete_data()                       --  delete backed up data from sharepoint clients

    restore_in_place()                  --  runs an in-place restore job on the specified Sharepoint pseudo client

    restore_in_place_syntex()           --  runs an in-place restore job on the specified Sharepoint Syntex pseudo client

    refresh_license_collection()        --  runs a license collection process

    preview_backedup_file()               --  gets the preview content for the file

SharepointV1Subclient: Derived class from SharepointSuperSubclient Base class, representing a sharepoint v1 subclient,
and to perform operations on that subclient

SharepointV1Subclient:

    discover_sharepoint_sites()         --  Checks whether SP content i.e, sites/webs are available

    _get_subclient_properties_json()    --  gets all the subclient related properties of the Sharepoint subclient.

    content()                           --  sets the content of the subclient.

    restore_in_place()                  --  runs a in-place restore job on the specified Sharepoint pseudo client


&#34;&#34;&#34;

from __future__ import unicode_literals

import datetime
from base64 import b64encode
from ..subclient import Subclient
from ..exception import SDKException
from ..constants import SQLDefines
from ..constants import SharepointDefines


class SharepointSuperSubclient(Subclient):

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               collect_metadata=False,
               advanced_options=None,
               common_backup_options=None):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.
            Args:
                backup_level            (str)   --  level of backup the user wish to run
                                                    Full / Incremental
                incremental_backup      (bool)  --  run incremental backup
                                                    only applicable in case of Synthetic_full backup
                incremental_level       (str)   --  run incremental backup before/after synthetic full
                                                    BEFORE_SYNTH / AFTER_SYNTH
                                                    only applicable in case of Synthetic_full backup
                collect_metadata        (bool)  --  Collect Meta data for the backup
                advanced_options       (dict)  --  advanced backup options to be included while
                                                    making the request
            Returns:
                object - instance of the Job class for this backup job if its an immediate Job
                         instance of the Schedule class for the backup job if its a scheduled Job
            Raises:
                SDKException:
                    if backup level specified is not correct
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        backup_level = backup_level.lower()
        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)
        if advanced_options or common_backup_options:
            request_json = self._backup_json(
                backup_level=backup_level,
                incremental_backup=incremental_backup,
                incremental_level=incremental_level,
                advanced_options=advanced_options,
                common_backup_options=common_backup_options
            )
            backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )
            return self._process_backup_response(flag, response)
        else:
            return super(SharepointSuperSubclient, self).backup(backup_level=backup_level,
                                                                incremental_backup=incremental_backup,
                                                                incremental_level=incremental_level,
                                                                collect_metadata=collect_metadata)

    def _json_out_of_place_destination_option(self, value):
        &#34;&#34;&#34;setter for the SharePoint Online out of place restore
        option in restore json
            Args:
                value (dict)    --  restore option need to be included
            Returns:
                (dict)          --  generated exchange restore options JSON
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self._out_of_place_destination_json = {
            &#34;inPlace&#34;: False,
            &#34;destPath&#34;: [value.get(&#34;destination_path&#34;)],
            &#34;destClient&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;clientName&#34;: self._client_object.client_name
            },
        }

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient related properties of the Sharepoint subclient.
        &#34;&#34;&#34;
        super(SharepointSuperSubclient, self)._get_subclient_properties()
        self._sharepoint_subclient_prop = self._subclient_properties.get(&#39;sharepointsubclientprop&#39;, {})
        self._content = self._subclient_properties.get(&#39;content&#39;, {})


class SharepointSubclient(SharepointSuperSubclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a Sharepoint subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;sharepointsubclientprop&#34;: self._sharepoint_subclient_prop,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return subclient_json

    @property
    def sharepoint_subclient_prop(self):
        &#34;&#34;&#34; getter for sql server subclient properties &#34;&#34;&#34;
        return self._sharepoint_subclient_prop

    @sharepoint_subclient_prop.setter
    def sharepoint_subclient_prop(self, value):
        &#34;&#34;&#34;

            Args:
                value (list)  --  list of the category and properties to update on the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        category, prop = value

        self._set_subclient_properties(category, prop)

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;

        subclient_content = self._content

        content_list = []

        for content in subclient_content:
            if &#39;spContentPath&#39; in content:
                content_list.append(content[&#34;spContentPath&#34;])

        return content_list

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add a new Sharepoint server Subclient
            with the content passed in subclient content.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

        &#34;&#34;&#34;
        content = []

        for webapp in subclient_content:
            sp_server_dict = {
                &#34;spContentPath&#34;: webapp
            }
            content.append(sp_server_dict)

        self._set_subclient_properties(&#34;_content&#34;, content)

    def restore(
            self,
            content_to_restore,
            database_client,
            spsetup_list,
            overwrite=True
    ):
        &#34;&#34;&#34;Restores the Sharepoint content specified in the input paths list.

            Args:
                content_to_restore (list):  Content to restore.

                database_client (str): Name of Sharepoint SQL server back-end client.

                spsetup_list (dict): Dictionary of the Sharepoint setup configuration.

                overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

                to_time (str):  Restore to time.  Defaults to None.


            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if content_to_restore is not a list

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not self._backupset_object.is_sharepoint_online_instance:
            if not isinstance(content_to_restore, list):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            self._backupset_object._instance_object._restore_association = self._subClientEntity

            request_json = self._sharepoint_restore_options_json(
                content_to_restore,
                database_client,
                spsetup_list,
                overwrite=overwrite
            )

            return self._process_restore_response(request_json)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint Online Instance&#39;)

    def _sharepoint_restore_options_json(
            self,
            content,
            database_client,
            spsetup_list,
            destination_client=None,
            overwrite=None
    ):
        &#34;&#34;&#34;Constructs JSON for Sharepoint restore options based on restore request
            and returns the constructed json.

            Args:
                content (list):  List of Sharepoint content to restore.

                database_client (str): Name of Sharepoint SQL server back-end client.

                spsetup_list (dict): Dictionary of the Sharepoint setup configuration.

                destination_client (str): Restore destination Sharepoint client.

                overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

            Returns:
                dict: dictionary consisting of the Sharepoint Server options.
        &#34;&#34;&#34;
        if not self._backupset_object.is_sharepoint_online_instance:
            client_name = self._client_object._client_name

            request_json = self._restore_json(
                client=client_name
            )

            if destination_client is None:
                destination_client = client_name

            common_options = {
                &#34;allVersion&#34;: True,
                &#34;erExSpdbPathRestore&#34;: True
            }
            destination = {
                &#34;inPlace&#34;: True,  # TODO check if in-place/oop.. will do this when we implement oop testcase
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: destination_client
                },
                &#34;destinationInstance&#34;: {
                    &#34;clientName&#34;: destination_client,
                    &#34;instanceName&#34;: &#34;defaultInstance&#34;,
                    &#34;appName&#34;: self._agent_object.agent_name
                }
            }
            sql_restore_options = {
                &#34;sqlRecoverType&#34;: SQLDefines.STATE_RECOVER,
                &#34;dropConnectionsToDatabase&#34;: True,
                &#34;overWrite&#34;: overwrite,
                &#34;sqlRestoreType&#34;: SQLDefines.DATABASE_RESTORE
            }
            sharepoint_restore_option = {
                &#34;configContentDatabase&#34;: True,
                &#34;isSharePointRBS&#34;: False,
                &#34;restoreSqlDBTO&#34;: False,
                &#34;is90OrUpgradedClient&#34;: False,
                &#34;restoreSqlDBtoLocation&#34;: &#34;&#34;,
                &#34;fetchSqlDatabases&#34;: True,
                &#34;spRestoreToDisk&#34;: {
                    &#34;restoreToDisk&#34;: False
                }
            }
            sharepoint_db_restore_option = {
                &#34;restoreType&#34;: &#34;SameConfiguration&#34;,
                &#34;restoreDatabaseOption&#34;: &#34;RESTORE_ALL&#34;,
                &#34;rbsOptions&#34;: {
                    &#34;sqlSource&#34;: {
                        &#34;clientName&#34;: database_client
                    },
                    &#34;sqlDestination&#34;: {
                        &#34;destClient&#34;: {
                            &#34;clientName&#34;: database_client
                        }
                    }
                }
            }

            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;] \
                [&#39;browseOption&#39;][&#39;backupset&#39;][&#34;backupsetName&#34;] = self._backupset_object._backupset_name
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sqlServerRstOption&#34;] = sql_restore_options
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sharePointDBRestoreOption&#34;] = sharepoint_db_restore_option
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;commonOptions&#34;].update(common_options)
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;destination&#34;].update(destination)
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sharePointRstOption&#34;].update(sharepoint_restore_option)

            content_json = {}
            source_items = []
            database_list = []
            for item in content:
                item = item.split(&#34;\\&#34;)[2]
                for sp_dict in spsetup_list:
                    if sp_dict[&#34;application_pool&#34;].lower() == item.lower():
                        database_name = sp_dict[&#34;content_database&#34;]
                        database_server = sp_dict[&#34;database_server&#34;]
                        web_application = sp_dict[&#34;web_application&#34;]
                        username = sp_dict[&#34;credentials&#34;][&#34;username&#34;]
                        password = b64encode(sp_dict[&#34;credentials&#34;][&#34;password&#34;].encode()).decode()

                        source_items.append(
                            SharepointDefines.CONTENT_WEBAPP.format(item)
                        )
                        source_items.append(
                            SharepointDefines.CONTENT_DB.format(item, database_name)
                        )

                        database_list.append(database_name)

                        content_json = {
                            &#34;SharePointMetaData&#34;: [{
                                &#34;newDirectoryName&#34;: &#34;&#34;,
                                &#34;newDatabaseServerName&#34;: database_server,
                                &#34;sourceItem&#34;:
                                    SharepointDefines.CONTENT_DB.format(item, database_name),
                                &#34;newDatabaseName&#34;: database_name,
                                &#34;sharePointMetaDataType&#34;: &#34;SPContentDatabase&#34;
                            }, {
                                &#34;sourceItem&#34;:
                                    SharepointDefines.CONTENT_WEBAPP.format(item),
                                &#34;newWebApplicationURL&#34;: web_application,
                                &#34;newWebApplicationName&#34;: item,
                                &#34;sharePointMetaDataType&#34;: &#34;SPWebApplication&#34;,
                                &#34;credentials&#34;: {
                                    &#34;userName&#34;: username,
                                    &#34;password&#34;: password
                                }
                            }]
                        }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;fileOption&#34;][&#34;sourceItem&#34;] = source_items
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sqlServerRstOption&#34;][&#34;database&#34;] = database_list
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sqlServerRstOption&#34;][&#34;restoreSource&#34;] = database_list
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sharePointDBRestoreOption&#34;].update(content_json)

            return request_json
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint Online Instance&#39;)

    def _json_disk_restore_sharepoint_restore_option(self, value):
        &#34;&#34;&#34;Setter for  the SharePoint Online Disk restore option
        in restore json

            Args:
                value   (dict)  --  restore option need to be included
                                    Example:
                                        {
                                            &#34;disk_restore_type&#34;: 1,
                                            &#34;destination_path&#34;: &#34;C:\\TestRestore&#34;
                                        }

                                        disk_restore_type - 1 (Restore as native files)
                                        disk_restore_type - 2 (Restore as original files)


            Returns:
                (dict)          --  generated sharepoint restore options JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._sharepoint_disk_option_restore_json = {
            &#34;restoreToDiskType&#34;: value.get(&#34;disk_restore_type&#34;, 1),
            &#34;restoreToDiskPath&#34;: value.get(&#34;destination_path&#34;, &#34;&#34;),
            &#34;restoreToDisk&#34;: True
        }

    def _prepare_disk_restore_json(self, _disk_restore_option):
        &#34;&#34;&#34;
        Prepare disk restore Json with all getters

        Args:
            _disk_restore_option - dictionary with all disk restore options

            value:

                paths (list)                    --  list of paths of lists/libraries to restore

                destination_client (str)        --  client where the lists/libraries needs to be restored

                destination_path (str)          --  path where the lists/libraries needs to be restored

                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True


        returns:
            request_json        -complete json for performing disk Restore options
        &#34;&#34;&#34;

        if _disk_restore_option is None:
            _disk_restore_option = {}

        paths = self._filter_paths(_disk_restore_option.get(&#39;paths&#39;, []))
        self._json_disk_restore_sharepoint_restore_option(_disk_restore_option)
        _disk_restore_option[&#39;paths&#39;] = paths

        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_disk_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][&#34;sharePointRstOption&#34;][
            &#34;spRestoreToDisk&#34;] = self._sharepoint_disk_option_restore_json

        return request_json

    def _prepare_out_of_place_restore_json(self, _restore_option, common_options: dict):
        &#34;&#34;&#34;
        Prepare out of place retsore Json with all getters

        Args:
            _restore_option - dictionary with all out of place restore options

            value:

                paths (list)            --  list of paths of SharePoint list/libraries to restore

                destination_path (str)  --  path where the SharePoint Site where list/libraries needs to be restored

                overwrite (bool)        --  unconditional overwrite files during restore
                    default: True

            common_options      (dict)  -- common options for restore job payload
            
        returns:
            request_json        -  complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _restore_option is None:
            _restore_option = {}

        paths = self._filter_paths(_restore_option[&#39;paths&#39;])
        self._json_out_of_place_destination_option(_restore_option)
        _restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;destination&#39;] = self._out_of_place_destination_json
        
        if common_options:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;commonOptions&#39;].update(common_options)
        return request_json

    def _set_properties_to_update_site_association(self, operation):
        &#34;&#34;&#34;Updates the association properties of site

            Args:

               operation (int)                  --  type of operation to be performed
                                                     Example: 1 - Associate
                                                              2 - Enable
                                                              3 - Disable
                                                              4 - Remove

            Raises:

            SDKException:

                if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            properties_dict = {}
            if operation == 1:
                properties_dict[&#34;commonFlags&#34;] = 0
                properties_dict[&#34;isAutoDiscoveredUser&#34;] = False
                properties_dict[&#34;accountStatus&#34;] = 0
            elif operation == 2:
                properties_dict[&#34;commonFlags&#34;] = 4
                properties_dict[&#34;accountStatus&#34;] = 0
            elif operation == 3:
                properties_dict[&#34;commonFlags&#34;] = 4
                properties_dict[&#34;accountStatus&#34;] = 2
            elif operation == 4:
                properties_dict[&#34;isAutoDiscoveredUser&#34;] = True
            return properties_dict
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def add_azure_app(self, azure_app_id, azure_app_key_id, azure_directory_id, cert_string=None, cert_password=None):
        &#34;&#34;&#34;
        Adds an azure app to the sharepoint client

        args:
            azure_app_id        (str)       --      Application id of the azure app
            azure_app_key_id    (str)       --      Client Secret of the azure app
            azure_directory_id  (str)       --      Azure directory/tenant ID
            cert_string         (str)       --      Certificate String
            cert_password       (str)       --      Certificate Password
        &#34;&#34;&#34;
        properties_dict = self._backupset_object.properties
        azure_app_list = properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;].get(&#34;azureAppList&#34;, {}).get(&#34;azureApps&#34;, [])
        azure_app_key_id = b64encode(azure_app_key_id.encode()).decode()

        azure_app_dict = {
            &#34;azureAppId&#34;: azure_app_id,
            &#34;azureAppKeyValue&#34;: azure_app_key_id,
            &#34;azureDirectoryId&#34;: azure_directory_id
        }

        if cert_string:
            # cert_string needs to be encoded twice
            cert_string = b64encode(cert_string).decode()
            cert_string = b64encode(cert_string.encode()).decode()

            cert_password = b64encode(cert_password.encode()).decode()

            cert_dict = {
                &#34;certificate&#34;: {
                    &#34;certBase64String&#34;: cert_string,
                    &#34;certPassword&#34;: cert_password
                }
            }
            azure_app_dict.update(cert_dict)

        azure_app_list.append(azure_app_dict)

        properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;] =  {
            &#34;azureApps&#34;: azure_app_list
        }

        self._backupset_object.update_properties(properties_dict)

    def delete_azure_app(self, app_ids):
        &#34;&#34;&#34;
        Deletes azure app from the sharepoint client

        args:
            app_ids         (str / list[str])   --      Azure App ID or list of Azure App IDs to delete.
        &#34;&#34;&#34;
        if not isinstance(app_ids, list):
            app_ids = [app_ids]

        properties_dict = self._backupset_object.properties
        azure_app_list = properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;][&#34;azureApps&#34;]
        new_app_list = []

        for azure_app_dict in azure_app_list:
            if not azure_app_dict[&#34;azureAppId&#34;] in app_ids:
                new_app_list.append(azure_app_dict)

        properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;][&#34;azureApps&#34;] = new_app_list

        self._backupset_object.update_properties(properties_dict)

    def run_manual_discovery(self):
        &#34;&#34;&#34;Runs the manual discovery of backupset

            Raises:

                SDKException:

                    if failed to initialize job

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._MANUAL_DISCOVERY = self._services[&#39;CLOUD_DISCOVERY&#39;] % (
                self._instance_object.instance_id, self._client_object.client_id, self._agent_object.agent_id)
            flag, response = self._cvpysdk_object.make_request(
                &#39;GET&#39;, self._MANUAL_DISCOVERY
            )
            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response = response.json().get(&#39;response&#39;, [])
                        if response:
                            error_code = response[0].get(&#39;errorCode&#39;, -1)
                            if error_code != 0:
                                error_string = response.json().get(&#39;response&#39;, {})
                                o_str = &#39;Failed to run manual discovery\nError: &#34;{0}&#34;&#39;.format(error_string)
                                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json().get(&#39;errorMessage&#39;, &#34;&#34;)
                        o_str = &#39;Failed to run manual discovery\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def configure_group_for_backup(self, discovery_type, association_group_name, plan_id):
        &#34;&#34;&#34;Configures group for backup

            Args:

                discovery_type  (int)       --  type of discovery for content
                                                All Web Sites - 9
                                                All Groups And Teams Sites - 10
                                                All Project Online Sites - 11

                association_group_name(str) --  type of association
                                                Example: All Web Sites, All Groups And Teams Sites,
                                                All Project Online Sites

                plan_id (int)               --  id of office 365 plan

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

         &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._SET_USER_POLICY_ASSOCIATION = self._services[&#39;SET_USER_POLICY_ASSOCIATION&#39;]
            request_json = {
                &#34;cloudAppAssociation&#34;: {
                    &#34;accountStatus&#34;: 2,
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: discovery_type,
                        &#34;groups&#34;: [
                            {
                                &#34;name&#34;: association_group_name
                            }
                        ]
                    },
                    &#34;plan&#34;: {
                        &#34;planId&#34;: plan_id
                    }
                }
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._SET_USER_POLICY_ASSOCIATION, request_json
            )
            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response = response.json().get(&#39;response&#39;, [])
                        if response:
                            error_code = response[0].get(&#39;errorCode&#39;, -1)
                            if error_code != 0:
                                error_string = response.json().get(&#39;response&#39;, {})
                                o_str = &#39;Failed to set \nError: &#34;{0}&#34;&#39;.format(error_string)
                                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json().get(&#39;errorMessage&#39;, &#34;&#34;)
                        o_str = &#39;Failed to set category based content for association\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def update_auto_association_group_properties(self, discovery_type, association_group_name,
                                                 account_status=None, plan_id=None):
        &#34;&#34;&#34;Associates the content for backup based on provided group

            Args:

                discovery_type  (int)       --  type of discovery for content
                                                All Web Sites - 9
                                                All Groups And Teams Sites - 10
                                                All Project Online Sites - 11

                association_group_name(str) --  type of association
                                                Example: All Web Sites, All Groups And Teams Sites,
                                                All Project Online Sites

                account_status  (int)       --  type of operation to be performed
                                                enable - 0
                                                remove - 1
                                                disable - 2

                plan_id (int)               --  id of office 365 plan

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._ASSOCIATE_CONTENT = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
            request_json = {
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: discovery_type,
                        &#34;groups&#34;: [
                            {
                                &#34;name&#34;: association_group_name
                            }
                        ]
                    }
                }
            }
            if account_status is not None:
                request_json[&#39;cloudAppAssociation&#39;][&#39;accountStatus&#39;] = account_status
            if plan_id:
                request_json[&#39;cloudAppAssociation&#39;][&#39;plan&#39;] = {
                    &#34;planId&#34;: plan_id
                }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._ASSOCIATE_CONTENT, request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                            o_str = &#39;Failed to enable group\nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def browse_for_content(self, discovery_type):
        &#34;&#34;&#34;Returns the SP content i.e. sites/web information that is discovered in auto discovery phase

                Args:

                    discovery_type  (int)   --  type of discovery for content
                                                For all Associated Web/Sites = 6
                                                For all Non-Associated Web/Sites = 7

                Returns:

                    site_dict     (dict)    --  dictionary of sites properties

                    no_of_records   (int)   --  no of records

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._USER_POLICY_ASSOCIATION = self._services[&#39;USER_POLICY_ASSOCIATION&#39;]
            request_json = {
                &#34;discoverByType&#34;: discovery_type,
                &#34;bIncludeDeleted&#34;: False,
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    }
                },
                &#34;searchInfo&#34;: {
                    &#34;isSearch&#34;: 0,
                    &#34;searchKey&#34;: &#34;&#34;
                }
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._USER_POLICY_ASSOCIATION, request_json
            )
            if flag:
                if response and response.json():
                    no_of_records = None
                    if &#39;associations&#39; in response.json():
                        no_of_records = response.json().get(&#39;associations&#39;, [])[0].get(&#39;pagingInfo&#39;, {}). \
                            get(&#39;totalRecords&#39;, -1)
                    elif &#39;pagingInfo&#39; in response.json():
                        no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                        if no_of_records &lt;= 0:
                            return {}, no_of_records
                    associations = response.json().get(&#39;associations&#39;, [])
                    site_dict = {}
                    if discovery_type == 8:
                        if associations:
                            for group in associations:
                                group_name = group.get(&#34;groups&#34;, {}).get(&#34;name&#34;, &#34;&#34;)
                                site_dict[group_name] = {
                                    &#39;accountStatus&#39;: group.get(&#34;accountStatus&#34;),
                                    &#39;discoverByType&#39;: group.get(&#34;discoverByType&#34;),
                                    &#39;planName&#39;: group.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;)
                                }
                    else:
                        if associations:
                            for site in associations:
                                site_url = site.get(&#34;userAccountInfo&#34;, {}).get(&#34;smtpAddress&#34;, &#34;&#34;)
                                user_account_info = site.get(&#34;userAccountInfo&#34;, {})
                                site_dict[site_url] = {
                                    &#39;userAccountInfo&#39;: user_account_info,
                                    &#39;accountStatus&#39;: site.get(&#34;accountStatus&#34;),
                                    &#39;discoverByType&#39;: site.get(&#34;discoverByType&#34;),
                                    &#39;planName&#39;: site.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;)
                                }
                    return site_dict, no_of_records
                return {}, 0
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def update_sites_association_properties(self, site_user_accounts_list, operation, plan_id=None):
        &#34;&#34;&#34;Updates the association properties of site

                Args:

                    site_user_accounts_list (list)   --  list of user accounts of all sites
                                                           It has all information of sites/webs

                    operation (int)                  --  type of operation to be performed
                                                         Example: 1 - Associate
                                                                  2 - Enable
                                                                  3 - Disable
                                                                  4 - Remove

                    plan_id (int)                    --  id of office 365 plan

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            properties_dict = self._set_properties_to_update_site_association(operation)
            self._ASSOCIATE_CONTENT = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
            for user_account in site_user_accounts_list:
                item_type = user_account[&#39;itemType&#39;]
                if item_type == 2 and operation == 4:
                    user_account[&#39;commonFlags&#39;] = 6
                elif item_type == 1 and operation == 4:
                    user_account[&#39;commonFlags&#39;] = 10
                else:
                    user_account[&#39;commonFlags&#39;] = properties_dict[&#39;commonFlags&#39;]
                if properties_dict.get(&#39;isAutoDiscoveredUser&#39;, None) is not None:
                    user_account[&#39;isAutoDiscoveredUser&#39;] = properties_dict[&#39;isAutoDiscoveredUser&#39;]
            request_json = {
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 6,
                        &#34;userAccounts&#34;: site_user_accounts_list
                    }
                }
            }
            if properties_dict.get(&#39;accountStatus&#39;, None) is not None:
                request_json[&#39;cloudAppAssociation&#39;][&#39;accountStatus&#39;] = properties_dict[&#39;accountStatus&#39;]
            if plan_id:
                request_json[&#39;cloudAppAssociation&#39;][&#39;plan&#39;] = {
                    &#34;planId&#34;: plan_id
                }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._ASSOCIATE_CONTENT, request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json().get(&#39;response&#39;, {}).get(&#39;errorString&#39;, str())
                            o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def delete_data(self, guids=None, search_string=None, folder_delete=False, search_and_select_all=False):
        &#34;&#34;&#34;
        Trigger a bulk delete job or normal delete for files/folders according to input given for SharePoint V2

        Args:
            guids (list)                    --  List of file/folder object GUIDs or web GUIDs of items we are deleting
            search_string (string)          --  Search string (needed for search and delete all)
            folder_delete (bool)            --  Bool value to confirm if a folder is being deleted or not
            search_and_select_all (bool)    --  Normal delete operation or search and delete all operation
        &#34;&#34;&#34;
        ci_state_values = [&#34;1&#34;]
        bulk_mode = False
        if folder_delete or search_and_select_all:
            bulk_mode = True
        if search_and_select_all:
            file_filter = [{&#34;interGroupOP&#34;: 2,
                            &#34;filter&#34;: {&#34;interFilterOP&#34;: 2, &#34;filters&#34;: [
                                {&#34;field&#34;: &#34;HIDDEN&#34;, &#34;intraFieldOp&#34;: 4, &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}}, {
                                    &#34;field&#34;: &#34;SPWebGUID&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {&#34;values&#34;: guids}}]}},
                           {&#34;interGroupOP&#34;: 2, &#34;filter&#34;: {&#34;interFilterOP&#34;: 0,
                                                          &#34;filters&#34;: [{
                                                              &#34;field&#34;: &#34;FILE_NAME&#34;,
                                                              &#34;intraFieldOp&#34;: 0,
                                                              &#34;fieldValues&#34;: {
                                                                  &#34;values&#34;: [search_string, search_string + &#34;*&#34;]}},
                                                              {
                                                                  &#34;field&#34;: &#34;SPTitle&#34;,
                                                                  &#34;intraFieldOp&#34;: 0,
                                                                  &#34;fieldValues&#34;: {&#34;values&#34;: [search_string,
                                                                                             search_string + &#34;*&#34;]}}]}}]
        else:
            ci_state_values.extend([&#34;3333&#34;, &#34;3334&#34;, &#34;3335&#34;])
            file_filter = [{&#34;interGroupOP&#34;: 2, &#34;filter&#34;: {&#34;interFilterOP&#34;: 2, &#34;filters&#34;: [
                {
                    &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                    &#34;intraFieldOp&#34;: 0,
                    &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}},
                {
                    &#34;field&#34;: &#34;CV_OBJECT_GUID&#34;,
                    &#34;intraFieldOp&#34;: 0,
                    &#34;fieldValues&#34;: {&#34;values&#34;: guids}}]}}]

        request_json = {&#34;opType&#34;: 1, &#34;bulkMode&#34;: bulk_mode, &#34;deleteOption&#34;: {&#34;folderDelete&#34;: bulk_mode},
                        &#34;searchReq&#34;: {
                            &#34;mode&#34;: 4, &#34;advSearchGrp&#34;: {&#34;commonFilter&#34;: [{&#34;filter&#34;: {&#34;interFilterOP&#34;: 2,
                                                                                     &#34;filters&#34;: [
                                                                                         {&#34;field&#34;: &#34;CISTATE&#34;,
                                                                                          &#34;intraFieldOp&#34;: 0,
                                                                                          &#34;fieldValues&#34;: {
                                                                                              &#34;values&#34;: ci_state_values}}]}}],
                                                        &#34;fileFilter&#34;: file_filter,
                                                        &#34;emailFilter&#34;: [],
                                                        &#34;galaxyFilter&#34;: [{&#34;appIdList&#34;: [int(self.subclient_id)]}],
                                                        &#34;graphFilter&#34;: [
                                                            {
                                                                &#34;toField&#34;: &#34;CV_OBJECT_GUID&#34;, &#34;fromField&#34;: &#34;PARENT_GUID&#34;,
                                                                &#34;returnRoot&#34;: True,
                                                                &#34;traversalFilter&#34;: [{&#34;filters&#34;: [
                                                                    {&#34;field&#34;: &#34;IS_VISIBLE&#34;, &#34;intraFieldOp&#34;: 2,
                                                                     &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}},
                                                                    {&#34;field&#34;: &#34;HIDDEN&#34;, &#34;intraFieldOp&#34;: 4,
                                                                     &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}}]}]
                                                            }]},
                            &#34;searchProcessingInfo&#34;: {
                                &#34;resultOffset&#34;: 0, &#34;pageSize&#34;: 0,
                                &#34;queryParams&#34;: [{&#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;, &#34;value&#34;: &#34;true&#34;}],
                                &#34;sortParams&#34;: []
                            }
                        }
                        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;DELETE_DOCUMENTS&#39;], request_json
        )
        return self._process_index_delete_response(flag, response)

    def restore_in_place(self, **kwargs):
        &#34;&#34;&#34;Runs a in-place restore job on the specified Sharepoint pseudo client
           This is used by Sharepoint V2 pseudo client

             Kwargs:

                 paths     (list)   --  list of sites or webs to be restored
                 Example: [
                    &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Shared Documents&#34;,
                    &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Test Automation List&#34;
                    ]

             Returns:

                Job object

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._instance_object._restore_association = self._subClientEntity
            parameter_dict = self._restore_json(**kwargs)
            return self._process_restore_response(parameter_dict)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def restore_in_place_syntex(self, **kwargs):
        &#34;&#34;&#34;Runs an in-place restore job on the specified Syntex Sharepoint pseudo client

             Kwargs:

                 paths     (list)   --  list of sites or webs to be restored
                 Example: [
                    &#34;MB\\&lt;tenant-url&gt;/sites/TestSite\Contents\Shared Documents&#34;,
                    &#34;MB\\&lt;tenant-url&gt;/sites/TestSite\Contents\Test Automation List&#34;
                    ]

                 fast_restore_point   (booL)  -- Whether to use fast restore point or not
                                                 default: False
             Returns:

                Job object

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        paths = kwargs.get(&#39;paths&#39;, [])
        fast_restore_point = kwargs.get(&#39;fast_restore_point&#39;, False)

        if self._backupset_object.is_sharepoint_online_instance:
            site_dict, _ = self.browse_for_content(discovery_type=7)
            site_details = {}
            for path in paths:
                site_details[path] = site_dict[path].get(&#39;userAccountInfo&#39;, {})
            self._instance_object._restore_association = self._subClientEntity
            parameter_dict = self._restore_json(**kwargs)

            syntex_restore_items = []

            for key, value in site_details.items():
                sharepoint_item = value[&#34;EVGui_SharePointItem&#34;][0]
                syntex_restore_items.append({
                    &#34;displayName&#34;: value[&#34;displayName&#34;],
                    &#34;email&#34;: value[&#34;smtpAddress&#34;],
                    &#34;guid&#34;: value[&#34;user&#34;][&#34;userGUID&#34;],
                    &#34;rawId&#34;: sharepoint_item[&#39;siteId&#39;]+&#34;;&#34;+sharepoint_item[&#39;objectId&#39;]+&#34;;&#34;+sharepoint_item[&#39;contentPath&#39;],
                    &#34;restoreType&#34;: 1
                })

            # Get the current time in UTC
            current_time = datetime.datetime.now(datetime.timezone.utc)
            current_timestamp = int(current_time.timestamp())
            current_iso_format = current_time.strftime(&#39;%Y-%m-%dT%H:%M:%S.%f&#39;)[:-3] + &#39;Z&#39;
            parameter_dict[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = {}
            parameter_dict[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
                &#34;msSyntexRestoreOptions&#34;] = {
                &#34;msSyntexRestoreItems&#34;: {
                    &#34;listMsSyntexRestoreItems&#34;: syntex_restore_items
                },
                &#34;restoreDate&#34;: {
                    &#34;time&#34;: current_timestamp,
                    &#34;timeValue&#34;: current_iso_format
                },
                &#34;restorePointId&#34;: &#34;&#34;,
                &#34;restoreType&#34;: 1,
                &#34;useFastRestorePoint&#34;: fast_restore_point
            }

            return self._process_restore_response(parameter_dict)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def out_of_place_restore(
            self,
            paths,
            destination_path,
            overwrite=True,
            common_options: dict = None):
        &#34;&#34;&#34;Restores the SharePoint list/libraries specified in the input paths list to the different site

            Args:
                paths                   (list)  --  list of paths of SharePoint list/libraries to restore

                destination_path        (str)   --  path where the SharePoint Site where list/libraries needs to be restored

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                common_options          (dict)  -- add common options for restoring all versions
            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            restore_option = {}
            if not paths:
                raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
            restore_option[&#39;overwrite&#39;] = overwrite
            restore_option[&#39;paths&#39;] = paths
            restore_option[&#39;destination_path&#39;] = destination_path
            restore_option[&#39;in_place&#39;] = False
            request_json = self._prepare_out_of_place_restore_json(restore_option, common_options)
            return self._process_restore_response(request_json)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def disk_restore(
            self,
            paths,
            destination_client,
            destination_path,
            disk_restore_type,
            overwrite=True,
            in_place=False):
        &#34;&#34;&#34;Restores the sharepoint libraries/list specified in the input paths list to the same location.

           value:
                paths                   (list)  --  list of paths of lists/libraries to restore

                destination_client              --  client where the lists/libraries needs to be restored

                destination_path                --  path where the lists/libraries needs to be restored

                disk_restore_type               --  type of disk restore

                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True

                in_place               (bool)   --  in place restore set to false by default
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            restore_option = {}
            if not paths:
                raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
            restore_option[&#39;unconditional_overwrite&#39;] = overwrite
            restore_option[&#39;paths&#39;] = paths
            restore_option[&#39;client&#39;] = destination_client
            restore_option[&#39;destination_path&#39;] = destination_path
            restore_option[&#39;disk_restore_type&#39;] = disk_restore_type
            restore_option[&#39;in_place&#39;] = in_place
            request_json = self._prepare_disk_restore_json(restore_option)
            return self._process_restore_response(request_json)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def process_index_retention_rules(self, index_server_client_id):
        &#34;&#34;&#34;Makes API call to process index retention rules

         Args:
                index_server_client_id (int)  --  client id of index server

        Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            request_json = {
                &#34;appType&#34;: int(self._agent_object.agent_id),
                &#34;subclientId&#34;: int(self.subclient_id),
                &#34;indexServerClientId&#34;: index_server_client_id
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                            o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def refresh_license_collection(self):

        &#34;&#34;&#34;
        Method is used to update the License collection status

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        payload = {
            &#34;subClient&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;backupsetId&#34;: int(self._subClientEntity.get(&#39;backupsetId&#39;))
            }
        }

        api_url = self._services[&#39;LICENSE_COLLECTION&#39;]
        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;,
                                                           url=api_url, payload=payload)
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))
        if not response:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                               self._commcell_object._update_response_(response.text))

    def preview_backedup_file(self, file_path):
        &#34;&#34;&#34;Gets the preview content for the subclient.

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self._get_preview(file_path)


class SharepointV1Subclient(SharepointSuperSubclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a Sharepoint v1 subclient,
            and to perform operations on that subclient.&#34;&#34;&#34;

    def discover_sharepoint_sites(self, paths):
        &#34;&#34;&#34;Checks whether SP content i.e, sites/webs are available

                    Args:
                            paths (list)          --      list of paths of SharePoint sites to be checked

                &#34;&#34;&#34;
        request_json = {
            &#34;opType&#34;: 0,
            &#34;session&#34;: {
                &#34;sessionId&#34;: &#34;&#34;
            },
            &#34;paths&#34;: [{&#34;path&#34;: path} for path in paths],
            &#34;entity&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;applicationId&#34;: int(self._agent_object.agent_id),
                &#34;instanceId&#34;: int(self._instance_object.instance_id),
                &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
            },
            &#34;advOptions&#34;: {
                &#34;advConfig&#34;: {
                    &#34;applicationMining&#34;: {
                        &#34;isApplicationMiningReq&#34;: True,
                        &#34;appType&#34;: int(self._agent_object._agent_id),
                        &#34;agentVersion&#34;: 2013,
                        &#34;browseReq&#34;: {
                            &#34;spBrowseReq&#34;: {
                                &#34;spBrowseType&#34;: 2,
                                &#34;spBrowseLevel&#34;: 1
                            }
                        }
                    }
                }
            }
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._BROWSE, request_json)
        if flag:
            if response and response.json():
                response = response.json()
                if len(response[&#39;browseResponses&#39;][0][&#39;browseResult&#39;][&#39;advConfig&#39;][&#39;applicationMining&#39;][&#39;browseResp&#39;][
                           &#39;spBrowseResp&#39;][&#39;dataPathContents&#39;]) &gt; 0:
                    return \
                        response[&#39;browseResponses&#39;][0][&#39;browseResult&#39;][&#39;advConfig&#39;][&#39;applicationMining&#39;][&#39;browseResp&#39;][
                            &#39;spBrowseResp&#39;][&#39;dataPathContents&#39;]
                else:
                    raise SDKException(&#39;Sites not found&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;

        return self._content

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add a new Sharepoint server Subclient
            with the content passed in subclient content.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

        &#34;&#34;&#34;

        self._set_subclient_properties(&#34;_content&#34;, subclient_content)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;sharepointsubclientprop&#34;: self._sharepoint_subclient_prop,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1,
                    &#34;planEntity&#34;: self._planEntity,
                },
            &#34;association&#34;: {
                &#34;entity&#34;: [
                    {
                        &#34;subclientId&#34;: int(self.subclient_id),
                        &#34;applicationId&#34;: self._subClientEntity[&#34;applicationId&#34;],
                        &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
                        &#34;instanceId&#34;: int(self._instance_object.instance_id),
                        &#34;clientId&#34;: int(self._client_object.client_id),
                        &#34;subclientName&#34;: self.subclient_name
                    }
                ]
            }
        }
        return subclient_json

    def restore_in_place(self, **kwargs):
        &#34;&#34;&#34;Runs a in-place restore job on the specified Sharepoint pseudo client
           This is used by Sharepoint V2 pseudo client

             Kwargs:

                 paths     (list)   --  list of sites or webs to be restored
                 Example: [
                    &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Shared Documents&#34;,
                    &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Test Automation List&#34;
                    ]

             Returns:

                Job object

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity
        parameter_dict = self._restore_json(**kwargs, v1=True)
        return self._process_restore_response(parameter_dict)

    def out_of_place_restore(
            self,
            paths,
            destination_path,
            overwrite=True):
        &#34;&#34;&#34;Restores the SharePoint list/libraries specified in the input paths list to the different site

            Args:
                paths                   (list)  --  list of paths of SharePoint list/libraries to restore

                destination_path        (str)   --  path where the SharePoint Site where list/libraries needs to be restored

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        restore_option = {}
        if not paths:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        restore_option[&#39;destination_path&#39;] = destination_path
        restore_option[&#39;in_place&#39;] = False
        request_json = self._prepare_out_of_place_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def _prepare_out_of_place_restore_json(self, _restore_option):
        &#34;&#34;&#34;
        Prepare out of place retsore Json with all getters

        Args:
            _restore_option - dictionary with all out of place restore options

            value:

                paths (list)            --  list of paths of SharePoint list/libraries to restore

                destination_path (str)  --  path where the SharePoint Site where list/libraries needs to be restored

                overwrite (bool)        --  unconditional overwrite files during restore
                    default: True


        returns:
            request_json        -  complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _restore_option is None:
            _restore_option = {}

        paths = self._filter_paths(_restore_option[&#39;paths&#39;])
        self._json_out_of_place_destination_option(_restore_option)
        _restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_restore_option, v1=True)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;destination&#39;] = self._out_of_place_destination_json
        return request_json</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient"><code class="flex name class">
<span>class <span class="ident">SharepointSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Subclient Base class, representing a Sharepoint subclient,
and to perform operations on that subclient.</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L175-L1395" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SharepointSubclient(SharepointSuperSubclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a Sharepoint subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;sharepointsubclientprop&#34;: self._sharepoint_subclient_prop,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return subclient_json

    @property
    def sharepoint_subclient_prop(self):
        &#34;&#34;&#34; getter for sql server subclient properties &#34;&#34;&#34;
        return self._sharepoint_subclient_prop

    @sharepoint_subclient_prop.setter
    def sharepoint_subclient_prop(self, value):
        &#34;&#34;&#34;

            Args:
                value (list)  --  list of the category and properties to update on the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        category, prop = value

        self._set_subclient_properties(category, prop)

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;

        subclient_content = self._content

        content_list = []

        for content in subclient_content:
            if &#39;spContentPath&#39; in content:
                content_list.append(content[&#34;spContentPath&#34;])

        return content_list

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add a new Sharepoint server Subclient
            with the content passed in subclient content.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

        &#34;&#34;&#34;
        content = []

        for webapp in subclient_content:
            sp_server_dict = {
                &#34;spContentPath&#34;: webapp
            }
            content.append(sp_server_dict)

        self._set_subclient_properties(&#34;_content&#34;, content)

    def restore(
            self,
            content_to_restore,
            database_client,
            spsetup_list,
            overwrite=True
    ):
        &#34;&#34;&#34;Restores the Sharepoint content specified in the input paths list.

            Args:
                content_to_restore (list):  Content to restore.

                database_client (str): Name of Sharepoint SQL server back-end client.

                spsetup_list (dict): Dictionary of the Sharepoint setup configuration.

                overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

                to_time (str):  Restore to time.  Defaults to None.


            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if content_to_restore is not a list

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not self._backupset_object.is_sharepoint_online_instance:
            if not isinstance(content_to_restore, list):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

            self._backupset_object._instance_object._restore_association = self._subClientEntity

            request_json = self._sharepoint_restore_options_json(
                content_to_restore,
                database_client,
                spsetup_list,
                overwrite=overwrite
            )

            return self._process_restore_response(request_json)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint Online Instance&#39;)

    def _sharepoint_restore_options_json(
            self,
            content,
            database_client,
            spsetup_list,
            destination_client=None,
            overwrite=None
    ):
        &#34;&#34;&#34;Constructs JSON for Sharepoint restore options based on restore request
            and returns the constructed json.

            Args:
                content (list):  List of Sharepoint content to restore.

                database_client (str): Name of Sharepoint SQL server back-end client.

                spsetup_list (dict): Dictionary of the Sharepoint setup configuration.

                destination_client (str): Restore destination Sharepoint client.

                overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

            Returns:
                dict: dictionary consisting of the Sharepoint Server options.
        &#34;&#34;&#34;
        if not self._backupset_object.is_sharepoint_online_instance:
            client_name = self._client_object._client_name

            request_json = self._restore_json(
                client=client_name
            )

            if destination_client is None:
                destination_client = client_name

            common_options = {
                &#34;allVersion&#34;: True,
                &#34;erExSpdbPathRestore&#34;: True
            }
            destination = {
                &#34;inPlace&#34;: True,  # TODO check if in-place/oop.. will do this when we implement oop testcase
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: destination_client
                },
                &#34;destinationInstance&#34;: {
                    &#34;clientName&#34;: destination_client,
                    &#34;instanceName&#34;: &#34;defaultInstance&#34;,
                    &#34;appName&#34;: self._agent_object.agent_name
                }
            }
            sql_restore_options = {
                &#34;sqlRecoverType&#34;: SQLDefines.STATE_RECOVER,
                &#34;dropConnectionsToDatabase&#34;: True,
                &#34;overWrite&#34;: overwrite,
                &#34;sqlRestoreType&#34;: SQLDefines.DATABASE_RESTORE
            }
            sharepoint_restore_option = {
                &#34;configContentDatabase&#34;: True,
                &#34;isSharePointRBS&#34;: False,
                &#34;restoreSqlDBTO&#34;: False,
                &#34;is90OrUpgradedClient&#34;: False,
                &#34;restoreSqlDBtoLocation&#34;: &#34;&#34;,
                &#34;fetchSqlDatabases&#34;: True,
                &#34;spRestoreToDisk&#34;: {
                    &#34;restoreToDisk&#34;: False
                }
            }
            sharepoint_db_restore_option = {
                &#34;restoreType&#34;: &#34;SameConfiguration&#34;,
                &#34;restoreDatabaseOption&#34;: &#34;RESTORE_ALL&#34;,
                &#34;rbsOptions&#34;: {
                    &#34;sqlSource&#34;: {
                        &#34;clientName&#34;: database_client
                    },
                    &#34;sqlDestination&#34;: {
                        &#34;destClient&#34;: {
                            &#34;clientName&#34;: database_client
                        }
                    }
                }
            }

            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;] \
                [&#39;browseOption&#39;][&#39;backupset&#39;][&#34;backupsetName&#34;] = self._backupset_object._backupset_name
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sqlServerRstOption&#34;] = sql_restore_options
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sharePointDBRestoreOption&#34;] = sharepoint_db_restore_option
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;commonOptions&#34;].update(common_options)
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;destination&#34;].update(destination)
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sharePointRstOption&#34;].update(sharepoint_restore_option)

            content_json = {}
            source_items = []
            database_list = []
            for item in content:
                item = item.split(&#34;\\&#34;)[2]
                for sp_dict in spsetup_list:
                    if sp_dict[&#34;application_pool&#34;].lower() == item.lower():
                        database_name = sp_dict[&#34;content_database&#34;]
                        database_server = sp_dict[&#34;database_server&#34;]
                        web_application = sp_dict[&#34;web_application&#34;]
                        username = sp_dict[&#34;credentials&#34;][&#34;username&#34;]
                        password = b64encode(sp_dict[&#34;credentials&#34;][&#34;password&#34;].encode()).decode()

                        source_items.append(
                            SharepointDefines.CONTENT_WEBAPP.format(item)
                        )
                        source_items.append(
                            SharepointDefines.CONTENT_DB.format(item, database_name)
                        )

                        database_list.append(database_name)

                        content_json = {
                            &#34;SharePointMetaData&#34;: [{
                                &#34;newDirectoryName&#34;: &#34;&#34;,
                                &#34;newDatabaseServerName&#34;: database_server,
                                &#34;sourceItem&#34;:
                                    SharepointDefines.CONTENT_DB.format(item, database_name),
                                &#34;newDatabaseName&#34;: database_name,
                                &#34;sharePointMetaDataType&#34;: &#34;SPContentDatabase&#34;
                            }, {
                                &#34;sourceItem&#34;:
                                    SharepointDefines.CONTENT_WEBAPP.format(item),
                                &#34;newWebApplicationURL&#34;: web_application,
                                &#34;newWebApplicationName&#34;: item,
                                &#34;sharePointMetaDataType&#34;: &#34;SPWebApplication&#34;,
                                &#34;credentials&#34;: {
                                    &#34;userName&#34;: username,
                                    &#34;password&#34;: password
                                }
                            }]
                        }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;fileOption&#34;][&#34;sourceItem&#34;] = source_items
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sqlServerRstOption&#34;][&#34;database&#34;] = database_list
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sqlServerRstOption&#34;][&#34;restoreSource&#34;] = database_list
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#34;restoreOptions&#34;] \
                [&#34;sharePointDBRestoreOption&#34;].update(content_json)

            return request_json
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint Online Instance&#39;)

    def _json_disk_restore_sharepoint_restore_option(self, value):
        &#34;&#34;&#34;Setter for  the SharePoint Online Disk restore option
        in restore json

            Args:
                value   (dict)  --  restore option need to be included
                                    Example:
                                        {
                                            &#34;disk_restore_type&#34;: 1,
                                            &#34;destination_path&#34;: &#34;C:\\TestRestore&#34;
                                        }

                                        disk_restore_type - 1 (Restore as native files)
                                        disk_restore_type - 2 (Restore as original files)


            Returns:
                (dict)          --  generated sharepoint restore options JSON

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._sharepoint_disk_option_restore_json = {
            &#34;restoreToDiskType&#34;: value.get(&#34;disk_restore_type&#34;, 1),
            &#34;restoreToDiskPath&#34;: value.get(&#34;destination_path&#34;, &#34;&#34;),
            &#34;restoreToDisk&#34;: True
        }

    def _prepare_disk_restore_json(self, _disk_restore_option):
        &#34;&#34;&#34;
        Prepare disk restore Json with all getters

        Args:
            _disk_restore_option - dictionary with all disk restore options

            value:

                paths (list)                    --  list of paths of lists/libraries to restore

                destination_client (str)        --  client where the lists/libraries needs to be restored

                destination_path (str)          --  path where the lists/libraries needs to be restored

                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True


        returns:
            request_json        -complete json for performing disk Restore options
        &#34;&#34;&#34;

        if _disk_restore_option is None:
            _disk_restore_option = {}

        paths = self._filter_paths(_disk_restore_option.get(&#39;paths&#39;, []))
        self._json_disk_restore_sharepoint_restore_option(_disk_restore_option)
        _disk_restore_option[&#39;paths&#39;] = paths

        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_disk_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][&#34;sharePointRstOption&#34;][
            &#34;spRestoreToDisk&#34;] = self._sharepoint_disk_option_restore_json

        return request_json

    def _prepare_out_of_place_restore_json(self, _restore_option, common_options: dict):
        &#34;&#34;&#34;
        Prepare out of place retsore Json with all getters

        Args:
            _restore_option - dictionary with all out of place restore options

            value:

                paths (list)            --  list of paths of SharePoint list/libraries to restore

                destination_path (str)  --  path where the SharePoint Site where list/libraries needs to be restored

                overwrite (bool)        --  unconditional overwrite files during restore
                    default: True

            common_options      (dict)  -- common options for restore job payload
            
        returns:
            request_json        -  complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _restore_option is None:
            _restore_option = {}

        paths = self._filter_paths(_restore_option[&#39;paths&#39;])
        self._json_out_of_place_destination_option(_restore_option)
        _restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_restore_option)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;destination&#39;] = self._out_of_place_destination_json
        
        if common_options:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;commonOptions&#39;].update(common_options)
        return request_json

    def _set_properties_to_update_site_association(self, operation):
        &#34;&#34;&#34;Updates the association properties of site

            Args:

               operation (int)                  --  type of operation to be performed
                                                     Example: 1 - Associate
                                                              2 - Enable
                                                              3 - Disable
                                                              4 - Remove

            Raises:

            SDKException:

                if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            properties_dict = {}
            if operation == 1:
                properties_dict[&#34;commonFlags&#34;] = 0
                properties_dict[&#34;isAutoDiscoveredUser&#34;] = False
                properties_dict[&#34;accountStatus&#34;] = 0
            elif operation == 2:
                properties_dict[&#34;commonFlags&#34;] = 4
                properties_dict[&#34;accountStatus&#34;] = 0
            elif operation == 3:
                properties_dict[&#34;commonFlags&#34;] = 4
                properties_dict[&#34;accountStatus&#34;] = 2
            elif operation == 4:
                properties_dict[&#34;isAutoDiscoveredUser&#34;] = True
            return properties_dict
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def add_azure_app(self, azure_app_id, azure_app_key_id, azure_directory_id, cert_string=None, cert_password=None):
        &#34;&#34;&#34;
        Adds an azure app to the sharepoint client

        args:
            azure_app_id        (str)       --      Application id of the azure app
            azure_app_key_id    (str)       --      Client Secret of the azure app
            azure_directory_id  (str)       --      Azure directory/tenant ID
            cert_string         (str)       --      Certificate String
            cert_password       (str)       --      Certificate Password
        &#34;&#34;&#34;
        properties_dict = self._backupset_object.properties
        azure_app_list = properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;].get(&#34;azureAppList&#34;, {}).get(&#34;azureApps&#34;, [])
        azure_app_key_id = b64encode(azure_app_key_id.encode()).decode()

        azure_app_dict = {
            &#34;azureAppId&#34;: azure_app_id,
            &#34;azureAppKeyValue&#34;: azure_app_key_id,
            &#34;azureDirectoryId&#34;: azure_directory_id
        }

        if cert_string:
            # cert_string needs to be encoded twice
            cert_string = b64encode(cert_string).decode()
            cert_string = b64encode(cert_string.encode()).decode()

            cert_password = b64encode(cert_password.encode()).decode()

            cert_dict = {
                &#34;certificate&#34;: {
                    &#34;certBase64String&#34;: cert_string,
                    &#34;certPassword&#34;: cert_password
                }
            }
            azure_app_dict.update(cert_dict)

        azure_app_list.append(azure_app_dict)

        properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;] =  {
            &#34;azureApps&#34;: azure_app_list
        }

        self._backupset_object.update_properties(properties_dict)

    def delete_azure_app(self, app_ids):
        &#34;&#34;&#34;
        Deletes azure app from the sharepoint client

        args:
            app_ids         (str / list[str])   --      Azure App ID or list of Azure App IDs to delete.
        &#34;&#34;&#34;
        if not isinstance(app_ids, list):
            app_ids = [app_ids]

        properties_dict = self._backupset_object.properties
        azure_app_list = properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;][&#34;azureApps&#34;]
        new_app_list = []

        for azure_app_dict in azure_app_list:
            if not azure_app_dict[&#34;azureAppId&#34;] in app_ids:
                new_app_list.append(azure_app_dict)

        properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;][&#34;azureApps&#34;] = new_app_list

        self._backupset_object.update_properties(properties_dict)

    def run_manual_discovery(self):
        &#34;&#34;&#34;Runs the manual discovery of backupset

            Raises:

                SDKException:

                    if failed to initialize job

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._MANUAL_DISCOVERY = self._services[&#39;CLOUD_DISCOVERY&#39;] % (
                self._instance_object.instance_id, self._client_object.client_id, self._agent_object.agent_id)
            flag, response = self._cvpysdk_object.make_request(
                &#39;GET&#39;, self._MANUAL_DISCOVERY
            )
            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response = response.json().get(&#39;response&#39;, [])
                        if response:
                            error_code = response[0].get(&#39;errorCode&#39;, -1)
                            if error_code != 0:
                                error_string = response.json().get(&#39;response&#39;, {})
                                o_str = &#39;Failed to run manual discovery\nError: &#34;{0}&#34;&#39;.format(error_string)
                                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json().get(&#39;errorMessage&#39;, &#34;&#34;)
                        o_str = &#39;Failed to run manual discovery\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def configure_group_for_backup(self, discovery_type, association_group_name, plan_id):
        &#34;&#34;&#34;Configures group for backup

            Args:

                discovery_type  (int)       --  type of discovery for content
                                                All Web Sites - 9
                                                All Groups And Teams Sites - 10
                                                All Project Online Sites - 11

                association_group_name(str) --  type of association
                                                Example: All Web Sites, All Groups And Teams Sites,
                                                All Project Online Sites

                plan_id (int)               --  id of office 365 plan

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

         &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._SET_USER_POLICY_ASSOCIATION = self._services[&#39;SET_USER_POLICY_ASSOCIATION&#39;]
            request_json = {
                &#34;cloudAppAssociation&#34;: {
                    &#34;accountStatus&#34;: 2,
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: discovery_type,
                        &#34;groups&#34;: [
                            {
                                &#34;name&#34;: association_group_name
                            }
                        ]
                    },
                    &#34;plan&#34;: {
                        &#34;planId&#34;: plan_id
                    }
                }
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._SET_USER_POLICY_ASSOCIATION, request_json
            )
            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response = response.json().get(&#39;response&#39;, [])
                        if response:
                            error_code = response[0].get(&#39;errorCode&#39;, -1)
                            if error_code != 0:
                                error_string = response.json().get(&#39;response&#39;, {})
                                o_str = &#39;Failed to set \nError: &#34;{0}&#34;&#39;.format(error_string)
                                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json().get(&#39;errorMessage&#39;, &#34;&#34;)
                        o_str = &#39;Failed to set category based content for association\nError: &#34;{0}&#34;&#39;.format(
                            error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def update_auto_association_group_properties(self, discovery_type, association_group_name,
                                                 account_status=None, plan_id=None):
        &#34;&#34;&#34;Associates the content for backup based on provided group

            Args:

                discovery_type  (int)       --  type of discovery for content
                                                All Web Sites - 9
                                                All Groups And Teams Sites - 10
                                                All Project Online Sites - 11

                association_group_name(str) --  type of association
                                                Example: All Web Sites, All Groups And Teams Sites,
                                                All Project Online Sites

                account_status  (int)       --  type of operation to be performed
                                                enable - 0
                                                remove - 1
                                                disable - 2

                plan_id (int)               --  id of office 365 plan

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._ASSOCIATE_CONTENT = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
            request_json = {
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: discovery_type,
                        &#34;groups&#34;: [
                            {
                                &#34;name&#34;: association_group_name
                            }
                        ]
                    }
                }
            }
            if account_status is not None:
                request_json[&#39;cloudAppAssociation&#39;][&#39;accountStatus&#39;] = account_status
            if plan_id:
                request_json[&#39;cloudAppAssociation&#39;][&#39;plan&#39;] = {
                    &#34;planId&#34;: plan_id
                }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._ASSOCIATE_CONTENT, request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                            o_str = &#39;Failed to enable group\nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def browse_for_content(self, discovery_type):
        &#34;&#34;&#34;Returns the SP content i.e. sites/web information that is discovered in auto discovery phase

                Args:

                    discovery_type  (int)   --  type of discovery for content
                                                For all Associated Web/Sites = 6
                                                For all Non-Associated Web/Sites = 7

                Returns:

                    site_dict     (dict)    --  dictionary of sites properties

                    no_of_records   (int)   --  no of records

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._USER_POLICY_ASSOCIATION = self._services[&#39;USER_POLICY_ASSOCIATION&#39;]
            request_json = {
                &#34;discoverByType&#34;: discovery_type,
                &#34;bIncludeDeleted&#34;: False,
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    }
                },
                &#34;searchInfo&#34;: {
                    &#34;isSearch&#34;: 0,
                    &#34;searchKey&#34;: &#34;&#34;
                }
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._USER_POLICY_ASSOCIATION, request_json
            )
            if flag:
                if response and response.json():
                    no_of_records = None
                    if &#39;associations&#39; in response.json():
                        no_of_records = response.json().get(&#39;associations&#39;, [])[0].get(&#39;pagingInfo&#39;, {}). \
                            get(&#39;totalRecords&#39;, -1)
                    elif &#39;pagingInfo&#39; in response.json():
                        no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                        if no_of_records &lt;= 0:
                            return {}, no_of_records
                    associations = response.json().get(&#39;associations&#39;, [])
                    site_dict = {}
                    if discovery_type == 8:
                        if associations:
                            for group in associations:
                                group_name = group.get(&#34;groups&#34;, {}).get(&#34;name&#34;, &#34;&#34;)
                                site_dict[group_name] = {
                                    &#39;accountStatus&#39;: group.get(&#34;accountStatus&#34;),
                                    &#39;discoverByType&#39;: group.get(&#34;discoverByType&#34;),
                                    &#39;planName&#39;: group.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;)
                                }
                    else:
                        if associations:
                            for site in associations:
                                site_url = site.get(&#34;userAccountInfo&#34;, {}).get(&#34;smtpAddress&#34;, &#34;&#34;)
                                user_account_info = site.get(&#34;userAccountInfo&#34;, {})
                                site_dict[site_url] = {
                                    &#39;userAccountInfo&#39;: user_account_info,
                                    &#39;accountStatus&#39;: site.get(&#34;accountStatus&#34;),
                                    &#39;discoverByType&#39;: site.get(&#34;discoverByType&#34;),
                                    &#39;planName&#39;: site.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;)
                                }
                    return site_dict, no_of_records
                return {}, 0
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def update_sites_association_properties(self, site_user_accounts_list, operation, plan_id=None):
        &#34;&#34;&#34;Updates the association properties of site

                Args:

                    site_user_accounts_list (list)   --  list of user accounts of all sites
                                                           It has all information of sites/webs

                    operation (int)                  --  type of operation to be performed
                                                         Example: 1 - Associate
                                                                  2 - Enable
                                                                  3 - Disable
                                                                  4 - Remove

                    plan_id (int)                    --  id of office 365 plan

                Raises:

                    SDKException:

                        if response is empty

                        if response is not success

                        if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            properties_dict = self._set_properties_to_update_site_association(operation)
            self._ASSOCIATE_CONTENT = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
            for user_account in site_user_accounts_list:
                item_type = user_account[&#39;itemType&#39;]
                if item_type == 2 and operation == 4:
                    user_account[&#39;commonFlags&#39;] = 6
                elif item_type == 1 and operation == 4:
                    user_account[&#39;commonFlags&#39;] = 10
                else:
                    user_account[&#39;commonFlags&#39;] = properties_dict[&#39;commonFlags&#39;]
                if properties_dict.get(&#39;isAutoDiscoveredUser&#39;, None) is not None:
                    user_account[&#39;isAutoDiscoveredUser&#39;] = properties_dict[&#39;isAutoDiscoveredUser&#39;]
            request_json = {
                &#34;cloudAppAssociation&#34;: {
                    &#34;subclientEntity&#34;: {
                        &#34;subclientId&#34;: int(self.subclient_id)
                    },
                    &#34;cloudAppDiscoverinfo&#34;: {
                        &#34;discoverByType&#34;: 6,
                        &#34;userAccounts&#34;: site_user_accounts_list
                    }
                }
            }
            if properties_dict.get(&#39;accountStatus&#39;, None) is not None:
                request_json[&#39;cloudAppAssociation&#39;][&#39;accountStatus&#39;] = properties_dict[&#39;accountStatus&#39;]
            if plan_id:
                request_json[&#39;cloudAppAssociation&#39;][&#39;plan&#39;] = {
                    &#34;planId&#34;: plan_id
                }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._ASSOCIATE_CONTENT, request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json().get(&#39;response&#39;, {}).get(&#39;errorString&#39;, str())
                            o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def delete_data(self, guids=None, search_string=None, folder_delete=False, search_and_select_all=False):
        &#34;&#34;&#34;
        Trigger a bulk delete job or normal delete for files/folders according to input given for SharePoint V2

        Args:
            guids (list)                    --  List of file/folder object GUIDs or web GUIDs of items we are deleting
            search_string (string)          --  Search string (needed for search and delete all)
            folder_delete (bool)            --  Bool value to confirm if a folder is being deleted or not
            search_and_select_all (bool)    --  Normal delete operation or search and delete all operation
        &#34;&#34;&#34;
        ci_state_values = [&#34;1&#34;]
        bulk_mode = False
        if folder_delete or search_and_select_all:
            bulk_mode = True
        if search_and_select_all:
            file_filter = [{&#34;interGroupOP&#34;: 2,
                            &#34;filter&#34;: {&#34;interFilterOP&#34;: 2, &#34;filters&#34;: [
                                {&#34;field&#34;: &#34;HIDDEN&#34;, &#34;intraFieldOp&#34;: 4, &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}}, {
                                    &#34;field&#34;: &#34;SPWebGUID&#34;,
                                    &#34;intraFieldOp&#34;: 0,
                                    &#34;fieldValues&#34;: {&#34;values&#34;: guids}}]}},
                           {&#34;interGroupOP&#34;: 2, &#34;filter&#34;: {&#34;interFilterOP&#34;: 0,
                                                          &#34;filters&#34;: [{
                                                              &#34;field&#34;: &#34;FILE_NAME&#34;,
                                                              &#34;intraFieldOp&#34;: 0,
                                                              &#34;fieldValues&#34;: {
                                                                  &#34;values&#34;: [search_string, search_string + &#34;*&#34;]}},
                                                              {
                                                                  &#34;field&#34;: &#34;SPTitle&#34;,
                                                                  &#34;intraFieldOp&#34;: 0,
                                                                  &#34;fieldValues&#34;: {&#34;values&#34;: [search_string,
                                                                                             search_string + &#34;*&#34;]}}]}}]
        else:
            ci_state_values.extend([&#34;3333&#34;, &#34;3334&#34;, &#34;3335&#34;])
            file_filter = [{&#34;interGroupOP&#34;: 2, &#34;filter&#34;: {&#34;interFilterOP&#34;: 2, &#34;filters&#34;: [
                {
                    &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                    &#34;intraFieldOp&#34;: 0,
                    &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}},
                {
                    &#34;field&#34;: &#34;CV_OBJECT_GUID&#34;,
                    &#34;intraFieldOp&#34;: 0,
                    &#34;fieldValues&#34;: {&#34;values&#34;: guids}}]}}]

        request_json = {&#34;opType&#34;: 1, &#34;bulkMode&#34;: bulk_mode, &#34;deleteOption&#34;: {&#34;folderDelete&#34;: bulk_mode},
                        &#34;searchReq&#34;: {
                            &#34;mode&#34;: 4, &#34;advSearchGrp&#34;: {&#34;commonFilter&#34;: [{&#34;filter&#34;: {&#34;interFilterOP&#34;: 2,
                                                                                     &#34;filters&#34;: [
                                                                                         {&#34;field&#34;: &#34;CISTATE&#34;,
                                                                                          &#34;intraFieldOp&#34;: 0,
                                                                                          &#34;fieldValues&#34;: {
                                                                                              &#34;values&#34;: ci_state_values}}]}}],
                                                        &#34;fileFilter&#34;: file_filter,
                                                        &#34;emailFilter&#34;: [],
                                                        &#34;galaxyFilter&#34;: [{&#34;appIdList&#34;: [int(self.subclient_id)]}],
                                                        &#34;graphFilter&#34;: [
                                                            {
                                                                &#34;toField&#34;: &#34;CV_OBJECT_GUID&#34;, &#34;fromField&#34;: &#34;PARENT_GUID&#34;,
                                                                &#34;returnRoot&#34;: True,
                                                                &#34;traversalFilter&#34;: [{&#34;filters&#34;: [
                                                                    {&#34;field&#34;: &#34;IS_VISIBLE&#34;, &#34;intraFieldOp&#34;: 2,
                                                                     &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}},
                                                                    {&#34;field&#34;: &#34;HIDDEN&#34;, &#34;intraFieldOp&#34;: 4,
                                                                     &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}}]}]
                                                            }]},
                            &#34;searchProcessingInfo&#34;: {
                                &#34;resultOffset&#34;: 0, &#34;pageSize&#34;: 0,
                                &#34;queryParams&#34;: [{&#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;, &#34;value&#34;: &#34;true&#34;}],
                                &#34;sortParams&#34;: []
                            }
                        }
                        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;DELETE_DOCUMENTS&#39;], request_json
        )
        return self._process_index_delete_response(flag, response)

    def restore_in_place(self, **kwargs):
        &#34;&#34;&#34;Runs a in-place restore job on the specified Sharepoint pseudo client
           This is used by Sharepoint V2 pseudo client

             Kwargs:

                 paths     (list)   --  list of sites or webs to be restored
                 Example: [
                    &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Shared Documents&#34;,
                    &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Test Automation List&#34;
                    ]

             Returns:

                Job object

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            self._instance_object._restore_association = self._subClientEntity
            parameter_dict = self._restore_json(**kwargs)
            return self._process_restore_response(parameter_dict)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def restore_in_place_syntex(self, **kwargs):
        &#34;&#34;&#34;Runs an in-place restore job on the specified Syntex Sharepoint pseudo client

             Kwargs:

                 paths     (list)   --  list of sites or webs to be restored
                 Example: [
                    &#34;MB\\&lt;tenant-url&gt;/sites/TestSite\Contents\Shared Documents&#34;,
                    &#34;MB\\&lt;tenant-url&gt;/sites/TestSite\Contents\Test Automation List&#34;
                    ]

                 fast_restore_point   (booL)  -- Whether to use fast restore point or not
                                                 default: False
             Returns:

                Job object

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        paths = kwargs.get(&#39;paths&#39;, [])
        fast_restore_point = kwargs.get(&#39;fast_restore_point&#39;, False)

        if self._backupset_object.is_sharepoint_online_instance:
            site_dict, _ = self.browse_for_content(discovery_type=7)
            site_details = {}
            for path in paths:
                site_details[path] = site_dict[path].get(&#39;userAccountInfo&#39;, {})
            self._instance_object._restore_association = self._subClientEntity
            parameter_dict = self._restore_json(**kwargs)

            syntex_restore_items = []

            for key, value in site_details.items():
                sharepoint_item = value[&#34;EVGui_SharePointItem&#34;][0]
                syntex_restore_items.append({
                    &#34;displayName&#34;: value[&#34;displayName&#34;],
                    &#34;email&#34;: value[&#34;smtpAddress&#34;],
                    &#34;guid&#34;: value[&#34;user&#34;][&#34;userGUID&#34;],
                    &#34;rawId&#34;: sharepoint_item[&#39;siteId&#39;]+&#34;;&#34;+sharepoint_item[&#39;objectId&#39;]+&#34;;&#34;+sharepoint_item[&#39;contentPath&#39;],
                    &#34;restoreType&#34;: 1
                })

            # Get the current time in UTC
            current_time = datetime.datetime.now(datetime.timezone.utc)
            current_timestamp = int(current_time.timestamp())
            current_iso_format = current_time.strftime(&#39;%Y-%m-%dT%H:%M:%S.%f&#39;)[:-3] + &#39;Z&#39;
            parameter_dict[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = {}
            parameter_dict[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
                &#34;msSyntexRestoreOptions&#34;] = {
                &#34;msSyntexRestoreItems&#34;: {
                    &#34;listMsSyntexRestoreItems&#34;: syntex_restore_items
                },
                &#34;restoreDate&#34;: {
                    &#34;time&#34;: current_timestamp,
                    &#34;timeValue&#34;: current_iso_format
                },
                &#34;restorePointId&#34;: &#34;&#34;,
                &#34;restoreType&#34;: 1,
                &#34;useFastRestorePoint&#34;: fast_restore_point
            }

            return self._process_restore_response(parameter_dict)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def out_of_place_restore(
            self,
            paths,
            destination_path,
            overwrite=True,
            common_options: dict = None):
        &#34;&#34;&#34;Restores the SharePoint list/libraries specified in the input paths list to the different site

            Args:
                paths                   (list)  --  list of paths of SharePoint list/libraries to restore

                destination_path        (str)   --  path where the SharePoint Site where list/libraries needs to be restored

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                common_options          (dict)  -- add common options for restoring all versions
            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            restore_option = {}
            if not paths:
                raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
            restore_option[&#39;overwrite&#39;] = overwrite
            restore_option[&#39;paths&#39;] = paths
            restore_option[&#39;destination_path&#39;] = destination_path
            restore_option[&#39;in_place&#39;] = False
            request_json = self._prepare_out_of_place_restore_json(restore_option, common_options)
            return self._process_restore_response(request_json)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def disk_restore(
            self,
            paths,
            destination_client,
            destination_path,
            disk_restore_type,
            overwrite=True,
            in_place=False):
        &#34;&#34;&#34;Restores the sharepoint libraries/list specified in the input paths list to the same location.

           value:
                paths                   (list)  --  list of paths of lists/libraries to restore

                destination_client              --  client where the lists/libraries needs to be restored

                destination_path                --  path where the lists/libraries needs to be restored

                disk_restore_type               --  type of disk restore

                unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                    default: True

                in_place               (bool)   --  in place restore set to false by default
                    default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            restore_option = {}
            if not paths:
                raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
            restore_option[&#39;unconditional_overwrite&#39;] = overwrite
            restore_option[&#39;paths&#39;] = paths
            restore_option[&#39;client&#39;] = destination_client
            restore_option[&#39;destination_path&#39;] = destination_path
            restore_option[&#39;disk_restore_type&#39;] = disk_restore_type
            restore_option[&#39;in_place&#39;] = in_place
            request_json = self._prepare_disk_restore_json(restore_option)
            return self._process_restore_response(request_json)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def process_index_retention_rules(self, index_server_client_id):
        &#34;&#34;&#34;Makes API call to process index retention rules

         Args:
                index_server_client_id (int)  --  client id of index server

        Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        if self._backupset_object.is_sharepoint_online_instance:
            request_json = {
                &#34;appType&#34;: int(self._agent_object.agent_id),
                &#34;subclientId&#34;: int(self.subclient_id),
                &#34;indexServerClientId&#34;: index_server_client_id
            }
            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
            )
            if flag:
                if response.json():
                    if &#34;resp&#34; in response.json():
                        error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                        if error_code != 0:
                            error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                            o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                    elif &#39;errorMessage&#39; in response.json():
                        error_string = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)

    def refresh_license_collection(self):

        &#34;&#34;&#34;
        Method is used to update the License collection status

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        payload = {
            &#34;subClient&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;backupsetId&#34;: int(self._subClientEntity.get(&#39;backupsetId&#39;))
            }
        }

        api_url = self._services[&#39;LICENSE_COLLECTION&#39;]
        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;,
                                                           url=api_url, payload=payload)
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                               self._commcell_object._update_response_(response.text))
        if not response:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                               self._commcell_object._update_response_(response.text))

    def preview_backedup_file(self, file_path):
        &#34;&#34;&#34;Gets the preview content for the subclient.

            Returns:
                html   (str)   --  html content of the preview

            Raises:
                SDKException:
                    if file is not found

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self._get_preview(file_path)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient" href="#cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient">SharepointSuperSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate content from the Subclient relevant to the user.</p>
<h2 id="returns">Returns</h2>
<p>list - list of content associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L218-L234" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

        Returns:
            list - list of content associated with the subclient
    &#34;&#34;&#34;

    subclient_content = self._content

    content_list = []

    for content in subclient_content:
        if &#39;spContentPath&#39; in content:
            content_list.append(content[&#34;spContentPath&#34;])

    return content_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.sharepoint_subclient_prop"><code class="name">var <span class="ident">sharepoint_subclient_prop</span></code></dt>
<dd>
<div class="desc"><p>getter for sql server subclient properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L199-L202" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sharepoint_subclient_prop(self):
    &#34;&#34;&#34; getter for sql server subclient properties &#34;&#34;&#34;
    return self._sharepoint_subclient_prop</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.add_azure_app"><code class="name flex">
<span>def <span class="ident">add_azure_app</span></span>(<span>self, azure_app_id, azure_app_key_id, azure_directory_id, cert_string=None, cert_password=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds an azure app to the sharepoint client</p>
<p>args:
azure_app_id
(str)
&ndash;
Application id of the azure app
azure_app_key_id
(str)
&ndash;
Client Secret of the azure app
azure_directory_id
(str)
&ndash;
Azure directory/tenant ID
cert_string
(str)
&ndash;
Certificate String
cert_password
(str)
&ndash;
Certificate Password</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L601-L643" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_azure_app(self, azure_app_id, azure_app_key_id, azure_directory_id, cert_string=None, cert_password=None):
    &#34;&#34;&#34;
    Adds an azure app to the sharepoint client

    args:
        azure_app_id        (str)       --      Application id of the azure app
        azure_app_key_id    (str)       --      Client Secret of the azure app
        azure_directory_id  (str)       --      Azure directory/tenant ID
        cert_string         (str)       --      Certificate String
        cert_password       (str)       --      Certificate Password
    &#34;&#34;&#34;
    properties_dict = self._backupset_object.properties
    azure_app_list = properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;].get(&#34;azureAppList&#34;, {}).get(&#34;azureApps&#34;, [])
    azure_app_key_id = b64encode(azure_app_key_id.encode()).decode()

    azure_app_dict = {
        &#34;azureAppId&#34;: azure_app_id,
        &#34;azureAppKeyValue&#34;: azure_app_key_id,
        &#34;azureDirectoryId&#34;: azure_directory_id
    }

    if cert_string:
        # cert_string needs to be encoded twice
        cert_string = b64encode(cert_string).decode()
        cert_string = b64encode(cert_string.encode()).decode()

        cert_password = b64encode(cert_password.encode()).decode()

        cert_dict = {
            &#34;certificate&#34;: {
                &#34;certBase64String&#34;: cert_string,
                &#34;certPassword&#34;: cert_password
            }
        }
        azure_app_dict.update(cert_dict)

    azure_app_list.append(azure_app_dict)

    properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;] =  {
        &#34;azureApps&#34;: azure_app_list
    }

    self._backupset_object.update_properties(properties_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.browse_for_content"><code class="name flex">
<span>def <span class="ident">browse_for_content</span></span>(<span>self, discovery_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the SP content i.e. sites/web information that is discovered in auto discovery phase</p>
<h2 id="args">Args</h2>
<p>discovery_type
(int)
&ndash;
type of discovery for content
For all Associated Web/Sites = 6
For all Non-Associated Web/Sites = 7</p>
<h2 id="returns">Returns</h2>
<p>site_dict
(dict)
&ndash;
dictionary of sites properties</p>
<p>no_of_records
(int)
&ndash;
no of records</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success

if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L859-L939" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse_for_content(self, discovery_type):
    &#34;&#34;&#34;Returns the SP content i.e. sites/web information that is discovered in auto discovery phase

            Args:

                discovery_type  (int)   --  type of discovery for content
                                            For all Associated Web/Sites = 6
                                            For all Non-Associated Web/Sites = 7

            Returns:

                site_dict     (dict)    --  dictionary of sites properties

                no_of_records   (int)   --  no of records

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    if self._backupset_object.is_sharepoint_online_instance:
        self._USER_POLICY_ASSOCIATION = self._services[&#39;USER_POLICY_ASSOCIATION&#39;]
        request_json = {
            &#34;discoverByType&#34;: discovery_type,
            &#34;bIncludeDeleted&#34;: False,
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                }
            },
            &#34;searchInfo&#34;: {
                &#34;isSearch&#34;: 0,
                &#34;searchKey&#34;: &#34;&#34;
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._USER_POLICY_ASSOCIATION, request_json
        )
        if flag:
            if response and response.json():
                no_of_records = None
                if &#39;associations&#39; in response.json():
                    no_of_records = response.json().get(&#39;associations&#39;, [])[0].get(&#39;pagingInfo&#39;, {}). \
                        get(&#39;totalRecords&#39;, -1)
                elif &#39;pagingInfo&#39; in response.json():
                    no_of_records = response.json().get(&#39;pagingInfo&#39;, {}).get(&#39;totalRecords&#39;, -1)
                    if no_of_records &lt;= 0:
                        return {}, no_of_records
                associations = response.json().get(&#39;associations&#39;, [])
                site_dict = {}
                if discovery_type == 8:
                    if associations:
                        for group in associations:
                            group_name = group.get(&#34;groups&#34;, {}).get(&#34;name&#34;, &#34;&#34;)
                            site_dict[group_name] = {
                                &#39;accountStatus&#39;: group.get(&#34;accountStatus&#34;),
                                &#39;discoverByType&#39;: group.get(&#34;discoverByType&#34;),
                                &#39;planName&#39;: group.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;)
                            }
                else:
                    if associations:
                        for site in associations:
                            site_url = site.get(&#34;userAccountInfo&#34;, {}).get(&#34;smtpAddress&#34;, &#34;&#34;)
                            user_account_info = site.get(&#34;userAccountInfo&#34;, {})
                            site_dict[site_url] = {
                                &#39;userAccountInfo&#39;: user_account_info,
                                &#39;accountStatus&#39;: site.get(&#34;accountStatus&#34;),
                                &#39;discoverByType&#39;: site.get(&#34;discoverByType&#34;),
                                &#39;planName&#39;: site.get(&#34;plan&#34;, {}).get(&#34;planName&#34;, &#34;&#34;)
                            }
                return site_dict, no_of_records
            return {}, 0
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.configure_group_for_backup"><code class="name flex">
<span>def <span class="ident">configure_group_for_backup</span></span>(<span>self, discovery_type, association_group_name, plan_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Configures group for backup</p>
<h2 id="args">Args</h2>
<p>discovery_type
(int)
&ndash;
type of discovery for content
All Web Sites - 9
All Groups And Teams Sites - 10
All Project Online Sites - 11</p>
<p>association_group_name(str) &ndash;
type of association
Example: All Web Sites, All Groups And Teams Sites,
All Project Online Sites</p>
<p>plan_id (int)
&ndash;
id of office 365 plan</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success

if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L712-L781" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_group_for_backup(self, discovery_type, association_group_name, plan_id):
    &#34;&#34;&#34;Configures group for backup

        Args:

            discovery_type  (int)       --  type of discovery for content
                                            All Web Sites - 9
                                            All Groups And Teams Sites - 10
                                            All Project Online Sites - 11

            association_group_name(str) --  type of association
                                            Example: All Web Sites, All Groups And Teams Sites,
                                            All Project Online Sites

            plan_id (int)               --  id of office 365 plan

        Raises:

            SDKException:

                if response is empty

                if response is not success

                if the method is called by SharePoint On-Premise Instance

     &#34;&#34;&#34;
    if self._backupset_object.is_sharepoint_online_instance:
        self._SET_USER_POLICY_ASSOCIATION = self._services[&#39;SET_USER_POLICY_ASSOCIATION&#39;]
        request_json = {
            &#34;cloudAppAssociation&#34;: {
                &#34;accountStatus&#34;: 2,
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: discovery_type,
                    &#34;groups&#34;: [
                        {
                            &#34;name&#34;: association_group_name
                        }
                    ]
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: plan_id
                }
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._SET_USER_POLICY_ASSOCIATION, request_json
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response = response.json().get(&#39;response&#39;, [])
                    if response:
                        error_code = response[0].get(&#39;errorCode&#39;, -1)
                        if error_code != 0:
                            error_string = response.json().get(&#39;response&#39;, {})
                            o_str = &#39;Failed to set \nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json().get(&#39;errorMessage&#39;, &#34;&#34;)
                    o_str = &#39;Failed to set category based content for association\nError: &#34;{0}&#34;&#39;.format(
                        error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.delete_azure_app"><code class="name flex">
<span>def <span class="ident">delete_azure_app</span></span>(<span>self, app_ids)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes azure app from the sharepoint client</p>
<p>args:
app_ids
(str / list[str])
&ndash;
Azure App ID or list of Azure App IDs to delete.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L645-L665" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_azure_app(self, app_ids):
    &#34;&#34;&#34;
    Deletes azure app from the sharepoint client

    args:
        app_ids         (str / list[str])   --      Azure App ID or list of Azure App IDs to delete.
    &#34;&#34;&#34;
    if not isinstance(app_ids, list):
        app_ids = [app_ids]

    properties_dict = self._backupset_object.properties
    azure_app_list = properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;][&#34;azureApps&#34;]
    new_app_list = []

    for azure_app_dict in azure_app_list:
        if not azure_app_dict[&#34;azureAppId&#34;] in app_ids:
            new_app_list.append(azure_app_dict)

    properties_dict[&#34;sharepointBackupSet&#34;][&#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;][&#34;azureApps&#34;] = new_app_list

    self._backupset_object.update_properties(properties_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.delete_data"><code class="name flex">
<span>def <span class="ident">delete_data</span></span>(<span>self, guids=None, search_string=None, folder_delete=False, search_and_select_all=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Trigger a bulk delete job or normal delete for files/folders according to input given for SharePoint V2</p>
<h2 id="args">Args</h2>
<p>guids (list)
&ndash;
List of file/folder object GUIDs or web GUIDs of items we are deleting
search_string (string)
&ndash;
Search string (needed for search and delete all)
folder_delete (bool)
&ndash;
Bool value to confirm if a folder is being deleted or not
search_and_select_all (bool)
&ndash;
Normal delete operation or search and delete all operation</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1018-L1093" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_data(self, guids=None, search_string=None, folder_delete=False, search_and_select_all=False):
    &#34;&#34;&#34;
    Trigger a bulk delete job or normal delete for files/folders according to input given for SharePoint V2

    Args:
        guids (list)                    --  List of file/folder object GUIDs or web GUIDs of items we are deleting
        search_string (string)          --  Search string (needed for search and delete all)
        folder_delete (bool)            --  Bool value to confirm if a folder is being deleted or not
        search_and_select_all (bool)    --  Normal delete operation or search and delete all operation
    &#34;&#34;&#34;
    ci_state_values = [&#34;1&#34;]
    bulk_mode = False
    if folder_delete or search_and_select_all:
        bulk_mode = True
    if search_and_select_all:
        file_filter = [{&#34;interGroupOP&#34;: 2,
                        &#34;filter&#34;: {&#34;interFilterOP&#34;: 2, &#34;filters&#34;: [
                            {&#34;field&#34;: &#34;HIDDEN&#34;, &#34;intraFieldOp&#34;: 4, &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}}, {
                                &#34;field&#34;: &#34;SPWebGUID&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {&#34;values&#34;: guids}}]}},
                       {&#34;interGroupOP&#34;: 2, &#34;filter&#34;: {&#34;interFilterOP&#34;: 0,
                                                      &#34;filters&#34;: [{
                                                          &#34;field&#34;: &#34;FILE_NAME&#34;,
                                                          &#34;intraFieldOp&#34;: 0,
                                                          &#34;fieldValues&#34;: {
                                                              &#34;values&#34;: [search_string, search_string + &#34;*&#34;]}},
                                                          {
                                                              &#34;field&#34;: &#34;SPTitle&#34;,
                                                              &#34;intraFieldOp&#34;: 0,
                                                              &#34;fieldValues&#34;: {&#34;values&#34;: [search_string,
                                                                                         search_string + &#34;*&#34;]}}]}}]
    else:
        ci_state_values.extend([&#34;3333&#34;, &#34;3334&#34;, &#34;3335&#34;])
        file_filter = [{&#34;interGroupOP&#34;: 2, &#34;filter&#34;: {&#34;interFilterOP&#34;: 2, &#34;filters&#34;: [
            {
                &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}},
            {
                &#34;field&#34;: &#34;CV_OBJECT_GUID&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {&#34;values&#34;: guids}}]}}]

    request_json = {&#34;opType&#34;: 1, &#34;bulkMode&#34;: bulk_mode, &#34;deleteOption&#34;: {&#34;folderDelete&#34;: bulk_mode},
                    &#34;searchReq&#34;: {
                        &#34;mode&#34;: 4, &#34;advSearchGrp&#34;: {&#34;commonFilter&#34;: [{&#34;filter&#34;: {&#34;interFilterOP&#34;: 2,
                                                                                 &#34;filters&#34;: [
                                                                                     {&#34;field&#34;: &#34;CISTATE&#34;,
                                                                                      &#34;intraFieldOp&#34;: 0,
                                                                                      &#34;fieldValues&#34;: {
                                                                                          &#34;values&#34;: ci_state_values}}]}}],
                                                    &#34;fileFilter&#34;: file_filter,
                                                    &#34;emailFilter&#34;: [],
                                                    &#34;galaxyFilter&#34;: [{&#34;appIdList&#34;: [int(self.subclient_id)]}],
                                                    &#34;graphFilter&#34;: [
                                                        {
                                                            &#34;toField&#34;: &#34;CV_OBJECT_GUID&#34;, &#34;fromField&#34;: &#34;PARENT_GUID&#34;,
                                                            &#34;returnRoot&#34;: True,
                                                            &#34;traversalFilter&#34;: [{&#34;filters&#34;: [
                                                                {&#34;field&#34;: &#34;IS_VISIBLE&#34;, &#34;intraFieldOp&#34;: 2,
                                                                 &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}},
                                                                {&#34;field&#34;: &#34;HIDDEN&#34;, &#34;intraFieldOp&#34;: 4,
                                                                 &#34;fieldValues&#34;: {&#34;values&#34;: [&#34;true&#34;]}}]}]
                                                        }]},
                        &#34;searchProcessingInfo&#34;: {
                            &#34;resultOffset&#34;: 0, &#34;pageSize&#34;: 0,
                            &#34;queryParams&#34;: [{&#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;, &#34;value&#34;: &#34;true&#34;}],
                            &#34;sortParams&#34;: []
                        }
                    }
                    }
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;DELETE_DOCUMENTS&#39;], request_json
    )
    return self._process_index_delete_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.disk_restore"><code class="name flex">
<span>def <span class="ident">disk_restore</span></span>(<span>self, paths, destination_client, destination_path, disk_restore_type, overwrite=True, in_place=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the sharepoint libraries/list specified in the input paths list to the same location.</p>
<p>value:
paths
(list)
&ndash;
list of paths of lists/libraries to restore</p>
<pre><code> destination_client              --  client where the lists/libraries needs to be restored

 destination_path                --  path where the lists/libraries needs to be restored

 disk_restore_type               --  type of disk restore

 unconditional_overwrite (bool)  --  unconditional overwrite files during restore
     default: True

 in_place               (bool)   --  in place restore set to false by default
     default: False
</code></pre>
<p>Returns:
object - instance of the Job class for this restore job</p>
<p>Raises:
SDKException:</p>
<pre><code>     if paths is not a list

     if failed to initialize job

     if response is empty

     if response is not success

     if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1253-L1308" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disk_restore(
        self,
        paths,
        destination_client,
        destination_path,
        disk_restore_type,
        overwrite=True,
        in_place=False):
    &#34;&#34;&#34;Restores the sharepoint libraries/list specified in the input paths list to the same location.

       value:
            paths                   (list)  --  list of paths of lists/libraries to restore

            destination_client              --  client where the lists/libraries needs to be restored

            destination_path                --  path where the lists/libraries needs to be restored

            disk_restore_type               --  type of disk restore

            unconditional_overwrite (bool)  --  unconditional overwrite files during restore
                default: True

            in_place               (bool)   --  in place restore set to false by default
                default: False

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

                if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    if self._backupset_object.is_sharepoint_online_instance:
        restore_option = {}
        if not paths:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;unconditional_overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        restore_option[&#39;client&#39;] = destination_client
        restore_option[&#39;destination_path&#39;] = destination_path
        restore_option[&#39;disk_restore_type&#39;] = disk_restore_type
        restore_option[&#39;in_place&#39;] = in_place
        request_json = self._prepare_disk_restore_json(restore_option)
        return self._process_restore_response(request_json)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.out_of_place_restore"><code class="name flex">
<span>def <span class="ident">out_of_place_restore</span></span>(<span>self, paths, destination_path, overwrite=True, common_options: dict = None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the SharePoint list/libraries specified in the input paths list to the different site</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of paths of SharePoint list/libraries to restore</p>
<p>destination_path
(str)
&ndash;
path where the SharePoint Site where list/libraries needs to be restored</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>common_options
(dict)
&ndash; add common options for restoring all versions</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success

if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1207-L1251" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def out_of_place_restore(
        self,
        paths,
        destination_path,
        overwrite=True,
        common_options: dict = None):
    &#34;&#34;&#34;Restores the SharePoint list/libraries specified in the input paths list to the different site

        Args:
            paths                   (list)  --  list of paths of SharePoint list/libraries to restore

            destination_path        (str)   --  path where the SharePoint Site where list/libraries needs to be restored

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True

            common_options          (dict)  -- add common options for restoring all versions
        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

                if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    if self._backupset_object.is_sharepoint_online_instance:
        restore_option = {}
        if not paths:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        restore_option[&#39;destination_path&#39;] = destination_path
        restore_option[&#39;in_place&#39;] = False
        request_json = self._prepare_out_of_place_restore_json(restore_option, common_options)
        return self._process_restore_response(request_json)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.preview_backedup_file"><code class="name flex">
<span>def <span class="ident">preview_backedup_file</span></span>(<span>self, file_path)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the preview content for the subclient.</p>
<h2 id="returns">Returns</h2>
<p>html
(str)
&ndash;
html content of the preview</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if file is not found</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1381-L1395" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def preview_backedup_file(self, file_path):
    &#34;&#34;&#34;Gets the preview content for the subclient.

        Returns:
            html   (str)   --  html content of the preview

        Raises:
            SDKException:
                if file is not found

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    return self._get_preview(file_path)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.process_index_retention_rules"><code class="name flex">
<span>def <span class="ident">process_index_retention_rules</span></span>(<span>self, index_server_client_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Makes API call to process index retention rules</p>
<p>Args:
index_server_client_id (int)
&ndash;
client id of index server</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success

if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1310-L1351" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def process_index_retention_rules(self, index_server_client_id):
    &#34;&#34;&#34;Makes API call to process index retention rules

     Args:
            index_server_client_id (int)  --  client id of index server

    Raises:

            SDKException:

                if response is empty

                if response is not success

                if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    if self._backupset_object.is_sharepoint_online_instance:
        request_json = {
            &#34;appType&#34;: int(self._agent_object.agent_id),
            &#34;subclientId&#34;: int(self.subclient_id),
            &#34;indexServerClientId&#34;: index_server_client_id
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
        )
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to process index retention rules\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.refresh_license_collection"><code class="name flex">
<span>def <span class="ident">refresh_license_collection</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Method is used to update the License collection status</p>
<pre><code>Raises:
    SDKException:
        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1353-L1379" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh_license_collection(self):

    &#34;&#34;&#34;
    Method is used to update the License collection status

        Raises:
            SDKException:
                if response is empty

                if response is not success
    &#34;&#34;&#34;
    payload = {
        &#34;subClient&#34;: {
            &#34;clientId&#34;: int(self._client_object.client_id),
            &#34;backupsetId&#34;: int(self._subClientEntity.get(&#39;backupsetId&#39;))
        }
    }

    api_url = self._services[&#39;LICENSE_COLLECTION&#39;]
    flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;,
                                                       url=api_url, payload=payload)
    if not flag:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;,
                           self._commcell_object._update_response_(response.text))
    if not response:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                           self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.restore"><code class="name flex">
<span>def <span class="ident">restore</span></span>(<span>self, content_to_restore, database_client, spsetup_list, overwrite=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the Sharepoint content specified in the input paths list.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>content_to_restore</code></strong> :&ensp;<code>list</code></dt>
<dd>Content to restore.</dd>
<dt><strong><code>database_client</code></strong> :&ensp;<code>str</code></dt>
<dd>Name of Sharepoint SQL server back-end client.</dd>
<dt><strong><code>spsetup_list</code></strong> :&ensp;<code>dict</code></dt>
<dd>Dictionary of the Sharepoint setup configuration.</dd>
<dt><strong><code>overwrite</code></strong> :&ensp;<code>bool</code></dt>
<dd>Unconditional overwrite files during restore.
Defaults to True.</dd>
<dt><strong><code>to_time</code></strong> :&ensp;<code>str</code></dt>
<dd>Restore to time.
Defaults to None.</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if content_to_restore is not a list</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L255-L302" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore(
        self,
        content_to_restore,
        database_client,
        spsetup_list,
        overwrite=True
):
    &#34;&#34;&#34;Restores the Sharepoint content specified in the input paths list.

        Args:
            content_to_restore (list):  Content to restore.

            database_client (str): Name of Sharepoint SQL server back-end client.

            spsetup_list (dict): Dictionary of the Sharepoint setup configuration.

            overwrite (bool):  Unconditional overwrite files during restore.  Defaults to True.

            to_time (str):  Restore to time.  Defaults to None.


        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if content_to_restore is not a list

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if not self._backupset_object.is_sharepoint_online_instance:
        if not isinstance(content_to_restore, list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._backupset_object._instance_object._restore_association = self._subClientEntity

        request_json = self._sharepoint_restore_options_json(
            content_to_restore,
            database_client,
            spsetup_list,
            overwrite=overwrite
        )

        return self._process_restore_response(request_json)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint Online Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a in-place restore job on the specified Sharepoint pseudo client
This is used by Sharepoint V2 pseudo client</p>
<p>Kwargs:</p>
<pre><code>  paths     (list)   --  list of sites or webs to be restored
  Example: [
     "MB\&lt;https://&lt;tenant_name&gt;&gt;.sharepoint.com/sites/TestSite\Contents\Shared Documents",
     "MB\&lt;https://&lt;tenant_name&gt;&gt;.sharepoint.com/sites/TestSite\Contents\Test Automation List"
     ]
</code></pre>
<p>Returns:</p>
<pre><code> Job object
</code></pre>
<p>Raises:</p>
<pre><code> SDKException:

     if paths is not a list

     if failed to initialize job

     if response is empty

     if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1095-L1129" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(self, **kwargs):
    &#34;&#34;&#34;Runs a in-place restore job on the specified Sharepoint pseudo client
       This is used by Sharepoint V2 pseudo client

         Kwargs:

             paths     (list)   --  list of sites or webs to be restored
             Example: [
                &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Shared Documents&#34;,
                &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Test Automation List&#34;
                ]

         Returns:

            Job object

        Raises:

            SDKException:

                if paths is not a list

                if failed to initialize job

                if response is empty

                if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    if self._backupset_object.is_sharepoint_online_instance:
        self._instance_object._restore_association = self._subClientEntity
        parameter_dict = self._restore_json(**kwargs)
        return self._process_restore_response(parameter_dict)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.restore_in_place_syntex"><code class="name flex">
<span>def <span class="ident">restore_in_place_syntex</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an in-place restore job on the specified Syntex Sharepoint pseudo client</p>
<p>Kwargs:</p>
<pre><code> paths     (list)   --  list of sites or webs to be restored
 Example: [
    "MB\&lt;tenant-url&gt;/sites/TestSite\Contents\Shared Documents",
    "MB\&lt;tenant-url&gt;/sites/TestSite\Contents\Test Automation List"
    ]

 fast_restore_point   (booL)  -- Whether to use fast restore point or not
                                 default: False
</code></pre>
<p>Returns:</p>
<pre><code>Job object
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if paths is not a list

if failed to initialize job

if response is empty

if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1131-L1205" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place_syntex(self, **kwargs):
    &#34;&#34;&#34;Runs an in-place restore job on the specified Syntex Sharepoint pseudo client

         Kwargs:

             paths     (list)   --  list of sites or webs to be restored
             Example: [
                &#34;MB\\&lt;tenant-url&gt;/sites/TestSite\Contents\Shared Documents&#34;,
                &#34;MB\\&lt;tenant-url&gt;/sites/TestSite\Contents\Test Automation List&#34;
                ]

             fast_restore_point   (booL)  -- Whether to use fast restore point or not
                                             default: False
         Returns:

            Job object

        Raises:

            SDKException:

                if paths is not a list

                if failed to initialize job

                if response is empty

                if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    paths = kwargs.get(&#39;paths&#39;, [])
    fast_restore_point = kwargs.get(&#39;fast_restore_point&#39;, False)

    if self._backupset_object.is_sharepoint_online_instance:
        site_dict, _ = self.browse_for_content(discovery_type=7)
        site_details = {}
        for path in paths:
            site_details[path] = site_dict[path].get(&#39;userAccountInfo&#39;, {})
        self._instance_object._restore_association = self._subClientEntity
        parameter_dict = self._restore_json(**kwargs)

        syntex_restore_items = []

        for key, value in site_details.items():
            sharepoint_item = value[&#34;EVGui_SharePointItem&#34;][0]
            syntex_restore_items.append({
                &#34;displayName&#34;: value[&#34;displayName&#34;],
                &#34;email&#34;: value[&#34;smtpAddress&#34;],
                &#34;guid&#34;: value[&#34;user&#34;][&#34;userGUID&#34;],
                &#34;rawId&#34;: sharepoint_item[&#39;siteId&#39;]+&#34;;&#34;+sharepoint_item[&#39;objectId&#39;]+&#34;;&#34;+sharepoint_item[&#39;contentPath&#39;],
                &#34;restoreType&#34;: 1
            })

        # Get the current time in UTC
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_timestamp = int(current_time.timestamp())
        current_iso_format = current_time.strftime(&#39;%Y-%m-%dT%H:%M:%S.%f&#39;)[:-3] + &#39;Z&#39;
        parameter_dict[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = {}
        parameter_dict[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
            &#34;msSyntexRestoreOptions&#34;] = {
            &#34;msSyntexRestoreItems&#34;: {
                &#34;listMsSyntexRestoreItems&#34;: syntex_restore_items
            },
            &#34;restoreDate&#34;: {
                &#34;time&#34;: current_timestamp,
                &#34;timeValue&#34;: current_iso_format
            },
            &#34;restorePointId&#34;: &#34;&#34;,
            &#34;restoreType&#34;: 1,
            &#34;useFastRestorePoint&#34;: fast_restore_point
        }

        return self._process_restore_response(parameter_dict)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.run_manual_discovery"><code class="name flex">
<span>def <span class="ident">run_manual_discovery</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs the manual discovery of backupset</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success

if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L667-L710" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_manual_discovery(self):
    &#34;&#34;&#34;Runs the manual discovery of backupset

        Raises:

            SDKException:

                if failed to initialize job

                if response is empty

                if response is not success

                if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    if self._backupset_object.is_sharepoint_online_instance:
        self._MANUAL_DISCOVERY = self._services[&#39;CLOUD_DISCOVERY&#39;] % (
            self._instance_object.instance_id, self._client_object.client_id, self._agent_object.agent_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._MANUAL_DISCOVERY
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response = response.json().get(&#39;response&#39;, [])
                    if response:
                        error_code = response[0].get(&#39;errorCode&#39;, -1)
                        if error_code != 0:
                            error_string = response.json().get(&#39;response&#39;, {})
                            o_str = &#39;Failed to run manual discovery\nError: &#34;{0}&#34;&#39;.format(error_string)
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json().get(&#39;errorMessage&#39;, &#34;&#34;)
                    o_str = &#39;Failed to run manual discovery\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.update_auto_association_group_properties"><code class="name flex">
<span>def <span class="ident">update_auto_association_group_properties</span></span>(<span>self, discovery_type, association_group_name, account_status=None, plan_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Associates the content for backup based on provided group</p>
<h2 id="args">Args</h2>
<p>discovery_type
(int)
&ndash;
type of discovery for content
All Web Sites - 9
All Groups And Teams Sites - 10
All Project Online Sites - 11</p>
<p>association_group_name(str) &ndash;
type of association
Example: All Web Sites, All Groups And Teams Sites,
All Project Online Sites</p>
<p>account_status
(int)
&ndash;
type of operation to be performed
enable - 0
remove - 1
disable - 2</p>
<p>plan_id (int)
&ndash;
id of office 365 plan</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success

if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L783-L857" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_auto_association_group_properties(self, discovery_type, association_group_name,
                                             account_status=None, plan_id=None):
    &#34;&#34;&#34;Associates the content for backup based on provided group

        Args:

            discovery_type  (int)       --  type of discovery for content
                                            All Web Sites - 9
                                            All Groups And Teams Sites - 10
                                            All Project Online Sites - 11

            association_group_name(str) --  type of association
                                            Example: All Web Sites, All Groups And Teams Sites,
                                            All Project Online Sites

            account_status  (int)       --  type of operation to be performed
                                            enable - 0
                                            remove - 1
                                            disable - 2

            plan_id (int)               --  id of office 365 plan

        Raises:

            SDKException:

                if response is empty

                if response is not success

                if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    if self._backupset_object.is_sharepoint_online_instance:
        self._ASSOCIATE_CONTENT = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
        request_json = {
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: discovery_type,
                    &#34;groups&#34;: [
                        {
                            &#34;name&#34;: association_group_name
                        }
                    ]
                }
            }
        }
        if account_status is not None:
            request_json[&#39;cloudAppAssociation&#39;][&#39;accountStatus&#39;] = account_status
        if plan_id:
            request_json[&#39;cloudAppAssociation&#39;][&#39;plan&#39;] = {
                &#34;planId&#34;: plan_id
            }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._ASSOCIATE_CONTENT, request_json
        )
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to enable group\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.update_sites_association_properties"><code class="name flex">
<span>def <span class="ident">update_sites_association_properties</span></span>(<span>self, site_user_accounts_list, operation, plan_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the association properties of site</p>
<h2 id="args">Args</h2>
<p>site_user_accounts_list (list)
&ndash;
list of user accounts of all sites
It has all information of sites/webs</p>
<p>operation (int)
&ndash;
type of operation to be performed
Example: 1 - Associate
2 - Enable
3 - Disable
4 - Remove</p>
<p>plan_id (int)
&ndash;
id of office 365 plan</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success

if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L941-L1016" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_sites_association_properties(self, site_user_accounts_list, operation, plan_id=None):
    &#34;&#34;&#34;Updates the association properties of site

            Args:

                site_user_accounts_list (list)   --  list of user accounts of all sites
                                                       It has all information of sites/webs

                operation (int)                  --  type of operation to be performed
                                                     Example: 1 - Associate
                                                              2 - Enable
                                                              3 - Disable
                                                              4 - Remove

                plan_id (int)                    --  id of office 365 plan

            Raises:

                SDKException:

                    if response is empty

                    if response is not success

                    if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    if self._backupset_object.is_sharepoint_online_instance:
        properties_dict = self._set_properties_to_update_site_association(operation)
        self._ASSOCIATE_CONTENT = self._services[&#39;UPDATE_USER_POLICY_ASSOCIATION&#39;]
        for user_account in site_user_accounts_list:
            item_type = user_account[&#39;itemType&#39;]
            if item_type == 2 and operation == 4:
                user_account[&#39;commonFlags&#39;] = 6
            elif item_type == 1 and operation == 4:
                user_account[&#39;commonFlags&#39;] = 10
            else:
                user_account[&#39;commonFlags&#39;] = properties_dict[&#39;commonFlags&#39;]
            if properties_dict.get(&#39;isAutoDiscoveredUser&#39;, None) is not None:
                user_account[&#39;isAutoDiscoveredUser&#39;] = properties_dict[&#39;isAutoDiscoveredUser&#39;]
        request_json = {
            &#34;cloudAppAssociation&#34;: {
                &#34;subclientEntity&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id)
                },
                &#34;cloudAppDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: 6,
                    &#34;userAccounts&#34;: site_user_accounts_list
                }
            }
        }
        if properties_dict.get(&#39;accountStatus&#39;, None) is not None:
            request_json[&#39;cloudAppAssociation&#39;][&#39;accountStatus&#39;] = properties_dict[&#39;accountStatus&#39;]
        if plan_id:
            request_json[&#39;cloudAppAssociation&#39;][&#39;plan&#39;] = {
                &#34;planId&#34;: plan_id
            }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._ASSOCIATE_CONTENT, request_json
        )
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json().get(&#39;response&#39;, {}).get(&#39;errorString&#39;, str())
                        o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to associate content\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Method not supported for SharePoint On-Premise Instance&#39;)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient" href="#cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient">SharepointSuperSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.backup" href="#cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient"><code class="flex name class">
<span>class <span class="ident">SharepointSuperSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Base class consisting of all the common properties and operations for a Subclient</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L96-L172" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SharepointSuperSubclient(Subclient):

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               collect_metadata=False,
               advanced_options=None,
               common_backup_options=None):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.
            Args:
                backup_level            (str)   --  level of backup the user wish to run
                                                    Full / Incremental
                incremental_backup      (bool)  --  run incremental backup
                                                    only applicable in case of Synthetic_full backup
                incremental_level       (str)   --  run incremental backup before/after synthetic full
                                                    BEFORE_SYNTH / AFTER_SYNTH
                                                    only applicable in case of Synthetic_full backup
                collect_metadata        (bool)  --  Collect Meta data for the backup
                advanced_options       (dict)  --  advanced backup options to be included while
                                                    making the request
            Returns:
                object - instance of the Job class for this backup job if its an immediate Job
                         instance of the Schedule class for the backup job if its a scheduled Job
            Raises:
                SDKException:
                    if backup level specified is not correct
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        backup_level = backup_level.lower()
        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)
        if advanced_options or common_backup_options:
            request_json = self._backup_json(
                backup_level=backup_level,
                incremental_backup=incremental_backup,
                incremental_level=incremental_level,
                advanced_options=advanced_options,
                common_backup_options=common_backup_options
            )
            backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )
            return self._process_backup_response(flag, response)
        else:
            return super(SharepointSuperSubclient, self).backup(backup_level=backup_level,
                                                                incremental_backup=incremental_backup,
                                                                incremental_level=incremental_level,
                                                                collect_metadata=collect_metadata)

    def _json_out_of_place_destination_option(self, value):
        &#34;&#34;&#34;setter for the SharePoint Online out of place restore
        option in restore json
            Args:
                value (dict)    --  restore option need to be included
            Returns:
                (dict)          --  generated exchange restore options JSON
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        self._out_of_place_destination_json = {
            &#34;inPlace&#34;: False,
            &#34;destPath&#34;: [value.get(&#34;destination_path&#34;)],
            &#34;destClient&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;clientName&#34;: self._client_object.client_name
            },
        }

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient related properties of the Sharepoint subclient.
        &#34;&#34;&#34;
        super(SharepointSuperSubclient, self)._get_subclient_properties()
        self._sharepoint_subclient_prop = self._subclient_properties.get(&#39;sharepointsubclientprop&#39;, {})
        self._content = self._subclient_properties.get(&#39;content&#39;, {})</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient">SharepointSubclient</a></li>
<li><a title="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient" href="#cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient">SharepointV1Subclient</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, backup_level='Incremental', incremental_backup=False, incremental_level='BEFORE_SYNTH', collect_metadata=False, advanced_options=None, common_backup_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a backup job for the subclient of the level specified.</p>
<h2 id="args">Args</h2>
<p>backup_level
(str)
&ndash;
level of backup the user wish to run
Full / Incremental
incremental_backup
(bool)
&ndash;
run incremental backup
only applicable in case of Synthetic_full backup
incremental_level
(str)
&ndash;
run incremental backup before/after synthetic full
BEFORE_SYNTH / AFTER_SYNTH
only applicable in case of Synthetic_full backup
collect_metadata
(bool)
&ndash;
Collect Meta data for the backup
advanced_options
(dict)
&ndash;
advanced backup options to be included while
making the request</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job if its an immediate Job
instance of the Schedule class for the backup job if its a scheduled Job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level specified is not correct
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L98-L146" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self,
           backup_level=&#34;Incremental&#34;,
           incremental_backup=False,
           incremental_level=&#39;BEFORE_SYNTH&#39;,
           collect_metadata=False,
           advanced_options=None,
           common_backup_options=None):
    &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.
        Args:
            backup_level            (str)   --  level of backup the user wish to run
                                                Full / Incremental
            incremental_backup      (bool)  --  run incremental backup
                                                only applicable in case of Synthetic_full backup
            incremental_level       (str)   --  run incremental backup before/after synthetic full
                                                BEFORE_SYNTH / AFTER_SYNTH
                                                only applicable in case of Synthetic_full backup
            collect_metadata        (bool)  --  Collect Meta data for the backup
            advanced_options       (dict)  --  advanced backup options to be included while
                                                making the request
        Returns:
            object - instance of the Job class for this backup job if its an immediate Job
                     instance of the Schedule class for the backup job if its a scheduled Job
        Raises:
            SDKException:
                if backup level specified is not correct
                if response is empty
                if response is not success
    &#34;&#34;&#34;
    backup_level = backup_level.lower()
    if backup_level not in [&#39;full&#39;, &#39;incremental&#39;]:
        raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)
    if advanced_options or common_backup_options:
        request_json = self._backup_json(
            backup_level=backup_level,
            incremental_backup=incremental_backup,
            incremental_level=incremental_level,
            advanced_options=advanced_options,
            common_backup_options=common_backup_options
        )
        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )
        return self._process_backup_response(flag, response)
    else:
        return super(SharepointSuperSubclient, self).backup(backup_level=backup_level,
                                                            incremental_backup=incremental_backup,
                                                            incremental_level=incremental_level,
                                                            collect_metadata=collect_metadata)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclient.Subclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_in_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient"><code class="flex name class">
<span>class <span class="ident">SharepointV1Subclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Subclient Base class, representing a Sharepoint v1 subclient,
and to perform operations on that subclient.</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1398-L1616" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SharepointV1Subclient(SharepointSuperSubclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a Sharepoint v1 subclient,
            and to perform operations on that subclient.&#34;&#34;&#34;

    def discover_sharepoint_sites(self, paths):
        &#34;&#34;&#34;Checks whether SP content i.e, sites/webs are available

                    Args:
                            paths (list)          --      list of paths of SharePoint sites to be checked

                &#34;&#34;&#34;
        request_json = {
            &#34;opType&#34;: 0,
            &#34;session&#34;: {
                &#34;sessionId&#34;: &#34;&#34;
            },
            &#34;paths&#34;: [{&#34;path&#34;: path} for path in paths],
            &#34;entity&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;applicationId&#34;: int(self._agent_object.agent_id),
                &#34;instanceId&#34;: int(self._instance_object.instance_id),
                &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
            },
            &#34;advOptions&#34;: {
                &#34;advConfig&#34;: {
                    &#34;applicationMining&#34;: {
                        &#34;isApplicationMiningReq&#34;: True,
                        &#34;appType&#34;: int(self._agent_object._agent_id),
                        &#34;agentVersion&#34;: 2013,
                        &#34;browseReq&#34;: {
                            &#34;spBrowseReq&#34;: {
                                &#34;spBrowseType&#34;: 2,
                                &#34;spBrowseLevel&#34;: 1
                            }
                        }
                    }
                }
            }
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._BROWSE, request_json)
        if flag:
            if response and response.json():
                response = response.json()
                if len(response[&#39;browseResponses&#39;][0][&#39;browseResult&#39;][&#39;advConfig&#39;][&#39;applicationMining&#39;][&#39;browseResp&#39;][
                           &#39;spBrowseResp&#39;][&#39;dataPathContents&#39;]) &gt; 0:
                    return \
                        response[&#39;browseResponses&#39;][0][&#39;browseResult&#39;][&#39;advConfig&#39;][&#39;applicationMining&#39;][&#39;browseResp&#39;][
                            &#39;spBrowseResp&#39;][&#39;dataPathContents&#39;]
                else:
                    raise SDKException(&#39;Sites not found&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;

        return self._content

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add a new Sharepoint server Subclient
            with the content passed in subclient content.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

        &#34;&#34;&#34;

        self._set_subclient_properties(&#34;_content&#34;, subclient_content)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;sharepointsubclientprop&#34;: self._sharepoint_subclient_prop,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1,
                    &#34;planEntity&#34;: self._planEntity,
                },
            &#34;association&#34;: {
                &#34;entity&#34;: [
                    {
                        &#34;subclientId&#34;: int(self.subclient_id),
                        &#34;applicationId&#34;: self._subClientEntity[&#34;applicationId&#34;],
                        &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
                        &#34;instanceId&#34;: int(self._instance_object.instance_id),
                        &#34;clientId&#34;: int(self._client_object.client_id),
                        &#34;subclientName&#34;: self.subclient_name
                    }
                ]
            }
        }
        return subclient_json

    def restore_in_place(self, **kwargs):
        &#34;&#34;&#34;Runs a in-place restore job on the specified Sharepoint pseudo client
           This is used by Sharepoint V2 pseudo client

             Kwargs:

                 paths     (list)   --  list of sites or webs to be restored
                 Example: [
                    &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Shared Documents&#34;,
                    &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Test Automation List&#34;
                    ]

             Returns:

                Job object

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if the method is called by SharePoint On-Premise Instance

        &#34;&#34;&#34;
        self._instance_object._restore_association = self._subClientEntity
        parameter_dict = self._restore_json(**kwargs, v1=True)
        return self._process_restore_response(parameter_dict)

    def out_of_place_restore(
            self,
            paths,
            destination_path,
            overwrite=True):
        &#34;&#34;&#34;Restores the SharePoint list/libraries specified in the input paths list to the different site

            Args:
                paths                   (list)  --  list of paths of SharePoint list/libraries to restore

                destination_path        (str)   --  path where the SharePoint Site where list/libraries needs to be restored

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success


        &#34;&#34;&#34;
        restore_option = {}
        if not paths:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#39;overwrite&#39;] = overwrite
        restore_option[&#39;paths&#39;] = paths
        restore_option[&#39;destination_path&#39;] = destination_path
        restore_option[&#39;in_place&#39;] = False
        request_json = self._prepare_out_of_place_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def _prepare_out_of_place_restore_json(self, _restore_option):
        &#34;&#34;&#34;
        Prepare out of place retsore Json with all getters

        Args:
            _restore_option - dictionary with all out of place restore options

            value:

                paths (list)            --  list of paths of SharePoint list/libraries to restore

                destination_path (str)  --  path where the SharePoint Site where list/libraries needs to be restored

                overwrite (bool)        --  unconditional overwrite files during restore
                    default: True


        returns:
            request_json        -  complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _restore_option is None:
            _restore_option = {}

        paths = self._filter_paths(_restore_option[&#39;paths&#39;])
        self._json_out_of_place_destination_option(_restore_option)
        _restore_option[&#39;paths&#39;] = paths

        # set the setters
        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_restore_option, v1=True)

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;destination&#39;] = self._out_of_place_destination_json
        return request_json</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient" href="#cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient">SharepointSuperSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate content from the Subclient relevant to the user.</p>
<h2 id="returns">Returns</h2>
<p>list - list of content associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1453-L1461" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

        Returns:
            list - list of content associated with the subclient
    &#34;&#34;&#34;

    return self._content</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.discover_sharepoint_sites"><code class="name flex">
<span>def <span class="ident">discover_sharepoint_sites</span></span>(<span>self, paths)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks whether SP content i.e, sites/webs are available</p>
<h2 id="args">Args</h2>
<p>paths (list)
&ndash;
list of paths of SharePoint sites to be checked</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1402-L1451" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def discover_sharepoint_sites(self, paths):
    &#34;&#34;&#34;Checks whether SP content i.e, sites/webs are available

                Args:
                        paths (list)          --      list of paths of SharePoint sites to be checked

            &#34;&#34;&#34;
    request_json = {
        &#34;opType&#34;: 0,
        &#34;session&#34;: {
            &#34;sessionId&#34;: &#34;&#34;
        },
        &#34;paths&#34;: [{&#34;path&#34;: path} for path in paths],
        &#34;entity&#34;: {
            &#34;clientId&#34;: int(self._client_object.client_id),
            &#34;applicationId&#34;: int(self._agent_object.agent_id),
            &#34;instanceId&#34;: int(self._instance_object.instance_id),
            &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
        },
        &#34;advOptions&#34;: {
            &#34;advConfig&#34;: {
                &#34;applicationMining&#34;: {
                    &#34;isApplicationMiningReq&#34;: True,
                    &#34;appType&#34;: int(self._agent_object._agent_id),
                    &#34;agentVersion&#34;: 2013,
                    &#34;browseReq&#34;: {
                        &#34;spBrowseReq&#34;: {
                            &#34;spBrowseType&#34;: 2,
                            &#34;spBrowseLevel&#34;: 1
                        }
                    }
                }
            }
        }
    }
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._BROWSE, request_json)
    if flag:
        if response and response.json():
            response = response.json()
            if len(response[&#39;browseResponses&#39;][0][&#39;browseResult&#39;][&#39;advConfig&#39;][&#39;applicationMining&#39;][&#39;browseResp&#39;][
                       &#39;spBrowseResp&#39;][&#39;dataPathContents&#39;]) &gt; 0:
                return \
                    response[&#39;browseResponses&#39;][0][&#39;browseResult&#39;][&#39;advConfig&#39;][&#39;applicationMining&#39;][&#39;browseResp&#39;][
                        &#39;spBrowseResp&#39;][&#39;dataPathContents&#39;]
            else:
                raise SDKException(&#39;Sites not found&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.out_of_place_restore"><code class="name flex">
<span>def <span class="ident">out_of_place_restore</span></span>(<span>self, paths, destination_path, overwrite=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the SharePoint list/libraries specified in the input paths list to the different site</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of paths of SharePoint list/libraries to restore</p>
<p>destination_path
(str)
&ndash;
path where the SharePoint Site where list/libraries needs to be restored</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1540-L1578" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def out_of_place_restore(
        self,
        paths,
        destination_path,
        overwrite=True):
    &#34;&#34;&#34;Restores the SharePoint list/libraries specified in the input paths list to the different site

        Args:
            paths                   (list)  --  list of paths of SharePoint list/libraries to restore

            destination_path        (str)   --  path where the SharePoint Site where list/libraries needs to be restored

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success


    &#34;&#34;&#34;
    restore_option = {}
    if not paths:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
    restore_option[&#39;overwrite&#39;] = overwrite
    restore_option[&#39;paths&#39;] = paths
    restore_option[&#39;destination_path&#39;] = destination_path
    restore_option[&#39;in_place&#39;] = False
    request_json = self._prepare_out_of_place_restore_json(restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a in-place restore job on the specified Sharepoint pseudo client
This is used by Sharepoint V2 pseudo client</p>
<p>Kwargs:</p>
<pre><code>  paths     (list)   --  list of sites or webs to be restored
  Example: [
     "MB\&lt;https://&lt;tenant_name&gt;&gt;.sharepoint.com/sites/TestSite\Contents\Shared Documents",
     "MB\&lt;https://&lt;tenant_name&gt;&gt;.sharepoint.com/sites/TestSite\Contents\Test Automation List"
     ]
</code></pre>
<p>Returns:</p>
<pre><code> Job object
</code></pre>
<p>Raises:</p>
<pre><code> SDKException:

     if paths is not a list

     if failed to initialize job

     if response is empty

     if the method is called by SharePoint On-Premise Instance
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sharepointsubclient.py#L1507-L1538" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(self, **kwargs):
    &#34;&#34;&#34;Runs a in-place restore job on the specified Sharepoint pseudo client
       This is used by Sharepoint V2 pseudo client

         Kwargs:

             paths     (list)   --  list of sites or webs to be restored
             Example: [
                &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Shared Documents&#34;,
                &#34;MB\\https://&lt;tenant_name&gt;.sharepoint.com/sites/TestSite\\Contents\\Test Automation List&#34;
                ]

         Returns:

            Job object

        Raises:

            SDKException:

                if paths is not a list

                if failed to initialize job

                if response is empty

                if the method is called by SharePoint On-Premise Instance

    &#34;&#34;&#34;
    self._instance_object._restore_association = self._subClientEntity
    parameter_dict = self._restore_json(**kwargs, v1=True)
    return self._process_restore_response(parameter_dict)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient" href="#cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient">SharepointSuperSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.backup" href="#cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient">SharepointSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.add_azure_app" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.add_azure_app">add_azure_app</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.browse_for_content" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.browse_for_content">browse_for_content</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.configure_group_for_backup" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.configure_group_for_backup">configure_group_for_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.content" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.delete_azure_app" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.delete_azure_app">delete_azure_app</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.delete_data" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.delete_data">delete_data</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.disk_restore" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.disk_restore">disk_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.out_of_place_restore" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.out_of_place_restore">out_of_place_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.preview_backedup_file" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.preview_backedup_file">preview_backedup_file</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.process_index_retention_rules" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.process_index_retention_rules">process_index_retention_rules</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.refresh_license_collection" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.refresh_license_collection">refresh_license_collection</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.restore" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.restore">restore</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.restore_in_place" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.restore_in_place_syntex" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.restore_in_place_syntex">restore_in_place_syntex</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.run_manual_discovery" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.run_manual_discovery">run_manual_discovery</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.sharepoint_subclient_prop" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.sharepoint_subclient_prop">sharepoint_subclient_prop</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.update_auto_association_group_properties" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.update_auto_association_group_properties">update_auto_association_group_properties</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSubclient.update_sites_association_properties" href="#cvpysdk.subclients.sharepointsubclient.SharepointSubclient.update_sites_association_properties">update_sites_association_properties</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient" href="#cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient">SharepointSuperSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.backup" href="#cvpysdk.subclients.sharepointsubclient.SharepointSuperSubclient.backup">backup</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient" href="#cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient">SharepointV1Subclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.content" href="#cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.discover_sharepoint_sites" href="#cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.discover_sharepoint_sites">discover_sharepoint_sites</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.out_of_place_restore" href="#cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.out_of_place_restore">out_of_place_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.restore_in_place" href="#cvpysdk.subclients.sharepointsubclient.SharepointV1Subclient.restore_in_place">restore_in_place</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>