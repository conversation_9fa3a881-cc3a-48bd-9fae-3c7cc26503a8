[build-system]
requires = ["flit_core >=3.2,<4"]
build-backend = "flit_core.buildapi"

[project]
name = "cvpysdk"
description = "Commvault SDK for Python"
authors = [{name = "Commvault Systems Inc.", email = "<EMAIL>"}]
readme = "README.rst"
license = {file = "LICENSE.txt"}
classifiers = [
  "Development Status :: 5 - Production/Stable",
  "License :: OSI Approved :: Apache Software License"
]
dynamic = ["version"]
dependencies = [
  "requests",
  "xmltodict"
]
requires-python = ">=3.6"
keywords = ["commvault", "python", "sdk", "cv", "simpana", "commcell", "cvlt", "webconsole"]

[project.urls]
"Home" = "https://github.com/Commvault/cvpysdk"
"Bug Tracker" = "https://github.com/Commvault/cvpysdk/issues"
"Documentation" = "https://commvault.github.io/cvpysdk/"
"Source" = "https://github.com/Commvault/cvpysdk"