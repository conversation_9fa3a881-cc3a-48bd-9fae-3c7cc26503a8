<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.deployment.uninstall API documentation</title>
<meta name="description" content="&#34; Main file for performing the Uniinstall operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.deployment.uninstall</code></h1>
</header>
<section id="section-intro">
<p>" Main file for performing the Uniinstall operations</p>
<h1 id="download">Download</h1>
<pre><code>__init__(commcell_object)        --  initialize commcell_object of Uninstall class
associated with the commcell

uninstall_software                 --  Uninstalls all the packages of the selected client.
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/uninstall.py#L1-L163" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;&#34; Main file for performing the Uniinstall operations

Download
========

    __init__(commcell_object)        --  initialize commcell_object of Uninstall class
    associated with the commcell

    uninstall_software                 --  Uninstalls all the packages of the selected client.

&#34;&#34;&#34;

from ..job import Job
from ..exception import SDKException

UNINSTALL_SELECTED_PACKAGES = 6
class Uninstall(object):
    &#34;&#34;&#34;&#34;class for Uninstalling software packages&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the Uninstall class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Uninstall class

        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._cvpysdk_object = commcell_object._cvpysdk_object

    def uninstall_software(self, client_name, force_uninstall=True,client_composition=[]):
        &#34;&#34;&#34;
        Performs readiness check on the client

            Args:
                force_uninstall (bool): Uninstalls packages forcibly. Defaults to True.

                client_name     (str): The client_name whose packages are to be uninstalled.

                client_composition (list): The client_coposition will contain the list of components need to be uninstalled. 

            Usage:
                uninstall.uninstall_software(client_name=&#34;freezaclient&#34;,force_uninstall=False,client_composition=[{
                                         &#34;activateClient&#34;: True,
                                         &#34;packageDeliveryOption&#34;: 0,
                                         &#34;components&#34;: {
                                             &#34;componentInfo&#34;: [
                                                 {
                                                     &#34;osType&#34;: &#34;Windows&#34;,
                                                     &#34;ComponentName&#34;: &#34;High Availability Computing&#34;
                                                 },
                                                 {
                                                     &#34;osType&#34;: &#34;Windows&#34;,
                                                     &#34;ComponentName&#34;: &#34;Index Store&#34;
                                                 },
                                                 {
                                                     &#34;osType&#34;: &#34;Windows&#34;,
                                                     &#34;ComponentName&#34;: &#34;Index Gateway&#34;
                                                 }
                                             ]
                                         }
                                     }
                                 ])

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(client_name, str):
            raise SDKException(&#39;Uninstall&#39;, &#39;101&#39;)
        optype = 7 # default value to remove all the packages
        if client_composition:
            optype = UNINSTALL_SELECTED_PACKAGES
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;clientName&#34;: client_name
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;policyType&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4027
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;clientInstallOption&#34;: {
                                    &#34;forceUninstall&#34;: force_uninstall,
                                    &#34;installOSType&#34;: 0,
                                    &#34;discoveryType&#34;: 0,
                                    &#34;installerOption&#34;: {
                                        &#34;requestType&#34;: 0,
                                        &#34;Operationtype&#34;: optype,
                                        &#34;CommServeHostName&#34;: self._commcell_object.commserv_name,
                                        &#34;RemoteClient&#34;: False,
                                        &#34;User&#34;: {
                                            &#34;userName&#34;: &#34;admin&#34;
                                        },
                                        &#34;clientComposition&#34;:client_composition
                                    },
                                    &#34;clientDetails&#34;: [
                                        {
                                            &#34;clientEntity&#34;: {
                                                &#34;clientName&#34;: client_name
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;CREATE_TASK&#39;], request_json)

        if flag:
            if response.json():
                if &#39;jobIds&#39; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                else:
                    o_str = &#39;Failed to submit uninstall job.&#39;
                    raise SDKException(&#39;Uninstall&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.deployment.uninstall.Uninstall"><code class="flex name class">
<span>class <span class="ident">Uninstall</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>"class for Uninstalling software packages</p>
<p>Initialize object of the Uninstall class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Uninstall class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/uninstall.py#L35-L163" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Uninstall(object):
    &#34;&#34;&#34;&#34;class for Uninstalling software packages&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the Uninstall class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Uninstall class

        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._services = commcell_object._services
        self._cvpysdk_object = commcell_object._cvpysdk_object

    def uninstall_software(self, client_name, force_uninstall=True,client_composition=[]):
        &#34;&#34;&#34;
        Performs readiness check on the client

            Args:
                force_uninstall (bool): Uninstalls packages forcibly. Defaults to True.

                client_name     (str): The client_name whose packages are to be uninstalled.

                client_composition (list): The client_coposition will contain the list of components need to be uninstalled. 

            Usage:
                uninstall.uninstall_software(client_name=&#34;freezaclient&#34;,force_uninstall=False,client_composition=[{
                                         &#34;activateClient&#34;: True,
                                         &#34;packageDeliveryOption&#34;: 0,
                                         &#34;components&#34;: {
                                             &#34;componentInfo&#34;: [
                                                 {
                                                     &#34;osType&#34;: &#34;Windows&#34;,
                                                     &#34;ComponentName&#34;: &#34;High Availability Computing&#34;
                                                 },
                                                 {
                                                     &#34;osType&#34;: &#34;Windows&#34;,
                                                     &#34;ComponentName&#34;: &#34;Index Store&#34;
                                                 },
                                                 {
                                                     &#34;osType&#34;: &#34;Windows&#34;,
                                                     &#34;ComponentName&#34;: &#34;Index Gateway&#34;
                                                 }
                                             ]
                                         }
                                     }
                                 ])

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(client_name, str):
            raise SDKException(&#39;Uninstall&#39;, &#39;101&#39;)
        optype = 7 # default value to remove all the packages
        if client_composition:
            optype = UNINSTALL_SELECTED_PACKAGES
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;clientName&#34;: client_name
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;policyType&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4027
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;clientInstallOption&#34;: {
                                    &#34;forceUninstall&#34;: force_uninstall,
                                    &#34;installOSType&#34;: 0,
                                    &#34;discoveryType&#34;: 0,
                                    &#34;installerOption&#34;: {
                                        &#34;requestType&#34;: 0,
                                        &#34;Operationtype&#34;: optype,
                                        &#34;CommServeHostName&#34;: self._commcell_object.commserv_name,
                                        &#34;RemoteClient&#34;: False,
                                        &#34;User&#34;: {
                                            &#34;userName&#34;: &#34;admin&#34;
                                        },
                                        &#34;clientComposition&#34;:client_composition
                                    },
                                    &#34;clientDetails&#34;: [
                                        {
                                            &#34;clientEntity&#34;: {
                                                &#34;clientName&#34;: client_name
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;CREATE_TASK&#39;], request_json)

        if flag:
            if response.json():
                if &#39;jobIds&#39; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                else:
                    o_str = &#39;Failed to submit uninstall job.&#39;
                    raise SDKException(&#39;Uninstall&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.deployment.uninstall.Uninstall.uninstall_software"><code class="name flex">
<span>def <span class="ident">uninstall_software</span></span>(<span>self, client_name, force_uninstall=True, client_composition=[])</span>
</code></dt>
<dd>
<div class="desc"><p>Performs readiness check on the client</p>
<pre><code>Args:
    force_uninstall (bool): Uninstalls packages forcibly. Defaults to True.

    client_name     (str): The client_name whose packages are to be uninstalled.

    client_composition (list): The client_coposition will contain the list of components need to be uninstalled.

Usage:
    uninstall.uninstall_software(client_name="freezaclient",force_uninstall=False,client_composition=[{
                             "activateClient": True,
                             "packageDeliveryOption": 0,
                             "components": {
                                 "componentInfo": [
                                     {
                                         "osType": "Windows",
                                         "ComponentName": "High Availability Computing"
                                     },
                                     {
                                         "osType": "Windows",
                                         "ComponentName": "Index Store"
                                     },
                                     {
                                         "osType": "Windows",
                                         "ComponentName": "Index Gateway"
                                     }
                                 ]
                             }
                         }
                     ])

Raises:
    SDKException:
        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/uninstall.py#L53-L163" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def uninstall_software(self, client_name, force_uninstall=True,client_composition=[]):
    &#34;&#34;&#34;
    Performs readiness check on the client

        Args:
            force_uninstall (bool): Uninstalls packages forcibly. Defaults to True.

            client_name     (str): The client_name whose packages are to be uninstalled.

            client_composition (list): The client_coposition will contain the list of components need to be uninstalled. 

        Usage:
            uninstall.uninstall_software(client_name=&#34;freezaclient&#34;,force_uninstall=False,client_composition=[{
                                     &#34;activateClient&#34;: True,
                                     &#34;packageDeliveryOption&#34;: 0,
                                     &#34;components&#34;: {
                                         &#34;componentInfo&#34;: [
                                             {
                                                 &#34;osType&#34;: &#34;Windows&#34;,
                                                 &#34;ComponentName&#34;: &#34;High Availability Computing&#34;
                                             },
                                             {
                                                 &#34;osType&#34;: &#34;Windows&#34;,
                                                 &#34;ComponentName&#34;: &#34;Index Store&#34;
                                             },
                                             {
                                                 &#34;osType&#34;: &#34;Windows&#34;,
                                                 &#34;ComponentName&#34;: &#34;Index Gateway&#34;
                                             }
                                         ]
                                     }
                                 }
                             ])

        Raises:
            SDKException:
                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if not isinstance(client_name, str):
        raise SDKException(&#39;Uninstall&#39;, &#39;101&#39;)
    optype = 7 # default value to remove all the packages
    if client_composition:
        optype = UNINSTALL_SELECTED_PACKAGES
    request_json = {
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [
                {
                    &#34;clientName&#34;: client_name
                }
            ],
            &#34;task&#34;: {
                &#34;taskType&#34;: 1,
                &#34;initiatedFrom&#34;: 2,
                &#34;policyType&#34;: 0,
                &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 4027
                    },
                    &#34;options&#34;: {
                        &#34;adminOpts&#34;: {
                            &#34;clientInstallOption&#34;: {
                                &#34;forceUninstall&#34;: force_uninstall,
                                &#34;installOSType&#34;: 0,
                                &#34;discoveryType&#34;: 0,
                                &#34;installerOption&#34;: {
                                    &#34;requestType&#34;: 0,
                                    &#34;Operationtype&#34;: optype,
                                    &#34;CommServeHostName&#34;: self._commcell_object.commserv_name,
                                    &#34;RemoteClient&#34;: False,
                                    &#34;User&#34;: {
                                        &#34;userName&#34;: &#34;admin&#34;
                                    },
                                    &#34;clientComposition&#34;:client_composition
                                },
                                &#34;clientDetails&#34;: [
                                    {
                                        &#34;clientEntity&#34;: {
                                            &#34;clientName&#34;: client_name
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            ]
        }
    }

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;CREATE_TASK&#39;], request_json)

    if flag:
        if response.json():
            if &#39;jobIds&#39; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            else:
                o_str = &#39;Failed to submit uninstall job.&#39;
                raise SDKException(&#39;Uninstall&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#download">Download</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.deployment" href="index.html">cvpysdk.deployment</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.deployment.uninstall.Uninstall" href="#cvpysdk.deployment.uninstall.Uninstall">Uninstall</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.uninstall.Uninstall.uninstall_software" href="#cvpysdk.deployment.uninstall.Uninstall.uninstall_software">uninstall_software</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>