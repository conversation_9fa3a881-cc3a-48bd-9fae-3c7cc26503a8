<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.license API documentation</title>
<meta name="description" content="File for License operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.license</code></h1>
</header>
<section id="section-intro">
<p>File for License operations.</p>
<p>LicenseDetails
: Class for representing license details information</p>
<h2 id="licensedetails">Licensedetails</h2>
<p><strong>init</strong>(Commcell_object)
&ndash;
initialise with object of CommCell</p>
<p>_get_detailed_licenses()
&ndash;
Gets all types of license details associated to the commcell object</p>
<p>_get_capacity_details()
&ndash; GET request to get capacity licenses property</p>
<p>_get_complete_oi_licenses()
&ndash;
GET request to get OI licenses property</p>
<p>_get_virtualization_licenses()
&ndash;
GET request to get virtualization licenses property</p>
<p>_get_user_licenses()
&ndash;
GET request to get user licenses property</p>
<p>_get_activate_licenses()
&ndash;
GET request to get activate licenses property</p>
<p>_get_metallic_licenses()
&ndash;
GET request to get metallic licenses property</p>
<p>_get_other_licenses()
&ndash;
GET request to get other licenses property</p>
<p>_get_license_details
&ndash;
GET request to get detailed license information</p>
<p>refresh()
&ndash;
Updates License object with the latest configuration</p>
<h2 id="licensedetails-attributes">Licensedetails Attributes</h2>
<pre><code>commcell_id   --    Returns the CommCell Id in decimal value

commcell_id_hex   --    Returns the hexadecimal value of commcell id

cs_hostname   --    Returns the csHostName Or Address of CommCell

license_ipaddress   --    Returns the license Ip Address

oem_name   --    Returns the oem_name

license_mode   --    Returns the license mode of license

registration_code   --    Returns the registration code of CommCell

serial_number   --    Returns the serial number of CommCell

expiry_date   --    Returns the expiry date of License

capacity_licenses   --    Returns dictionary with the capacity licenses

complete_oi_licenses   --    Returns dictionary with the complete oi licenses

virtualization_licenses   --    Returns dictionary with the virtualization licenses

user_licenses   --    Returns dictionary with the user licenses

activate_licenses   --    Returns dictionary with the activate licenses

metallic_licenses   --    Returns dictionary with the metallic licenses

other_licenses   --    Returns dictionary with the other licenses
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L1-L412" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for License operations.

LicenseDetails        : Class for representing license details information

LicenseDetails:
    __init__(Commcell_object)    --  initialise with object of CommCell
    
    _get_detailed_licenses()      --  Gets all types of license details associated to the commcell object
    
    _get_capacity_details()       -- GET request to get capacity licenses property
    
    _get_complete_oi_licenses()    --   GET request to get OI licenses property
    
    _get_virtualization_licenses()    --   GET request to get virtualization licenses property
    
    _get_user_licenses()    --   GET request to get user licenses property
    
    _get_activate_licenses()    --   GET request to get activate licenses property
    
    _get_metallic_licenses()    --   GET request to get metallic licenses property
    
    _get_other_licenses()    --   GET request to get other licenses property
    
    _get_license_details    --   GET request to get detailed license information
    
    
    refresh()    --    Updates License object with the latest configuration
    
    

LicenseDetails Attributes
-------------------------
    commcell_id   --    Returns the CommCell Id in decimal value
    
    commcell_id_hex   --    Returns the hexadecimal value of commcell id
    
    cs_hostname   --    Returns the csHostName Or Address of CommCell
    
    license_ipaddress   --    Returns the license Ip Address 
    
    oem_name   --    Returns the oem_name
    
    license_mode   --    Returns the license mode of license
    
    registration_code   --    Returns the registration code of CommCell
    
    serial_number   --    Returns the serial number of CommCell
    
    expiry_date   --    Returns the expiry date of License
    
    capacity_licenses   --    Returns dictionary with the capacity licenses
    
    complete_oi_licenses   --    Returns dictionary with the complete oi licenses
    
    virtualization_licenses   --    Returns dictionary with the virtualization licenses
    
    user_licenses   --    Returns dictionary with the user licenses
    
    activate_licenses   --    Returns dictionary with the activate licenses
    
    metallic_licenses   --    Returns dictionary with the metallic licenses
    
    other_licenses   --    Returns dictionary with the other licenses

&#34;&#34;&#34;
from __future__ import absolute_import
from __future__ import unicode_literals
from .exception import SDKException


class LicenseDetails(object):
    &#34;&#34;&#34;Class for accessing license details information&#34;&#34;&#34;    

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the LicenseDetails class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the LicenseDetails class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._LICENSE = self._commcell_object._services[&#39;LICENSE&#39;]
        self._APPLY_LICENSE = self._commcell_object._services[&#39;APPLY_LICENSE&#39;]
        self._capacity_licenses = None
        self._complete_oi_licenses = None
        self._virtualization_licenses = None
        self._user_licenses = None
        self._activate_licenses = None
        self._metallic_licenses = None
        self._other_licenses = None
        self._get_license_details()

    def _get_detailed_licenses(self):
        &#34;&#34;&#34;
        Gets all types of license details associated to the commcell object
        &#34;&#34;&#34;
        self._get_capacity_details()
        self._get_complete_oi_licenses()
        self._get_virtualization_licenses()
        self._get_user_licenses()
        self._get_activate_licenses()
        self._get_metallic_licenses()
        self._get_other_licenses()

    def _get_capacity_details(self):
        &#34;&#34;&#34;
        Request to get capacity licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;

        self._CAPACITY_LICENSE = self._commcell_object._services[&#39;CAPACITY_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._CAPACITY_LICENSE)

        if flag:
            if response.json():
                self._capacity_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_complete_oi_licenses(self):
        &#34;&#34;&#34;
       GET request to get OI licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;

        self._OI_LICENSE = self._commcell_object._services[&#39;OI_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._OI_LICENSE)

        if flag:
            if response.json():
                self._complete_oi_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_virtualization_licenses(self):
        &#34;&#34;&#34;
       GET request to get virtualization licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._VIRTUALIZATION_LICENSE = self._commcell_object._services[&#39;VIRTUALIZATION_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._VIRTUALIZATION_LICENSE)

        if flag:
            if response.json():
                self._virtualization_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_user_licenses(self):
        &#34;&#34;&#34;
       GET request to get user licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._USER_LICENSE = self._commcell_object._services[&#39;USER_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._USER_LICENSE)

        if flag:
            if response.json():
                self._user_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_activate_licenses(self):
        &#34;&#34;&#34;
       GET request to get activate licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._ACTIVATE_LICENSE = self._commcell_object._services[&#39;ACTIVATE_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._ACTIVATE_LICENSE)

        if flag:
            if response.json():
                self._activate_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_metallic_licenses(self):
        &#34;&#34;&#34;
       GET request to get metallic licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._METALLIC_LICENSE = self._commcell_object._services[&#39;METALLIC_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._METALLIC_LICENSE)

        if flag:
            if response.json():
                self._metallic_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_other_licenses(self):
        &#34;&#34;&#34;
       GET request to get other licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._OTHER_LICENSE = self._commcell_object._services[&#39;OTHER_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._OTHER_LICENSE)

        if flag:
            if response.json():
                self._other_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_license_details(self):
        &#34;&#34;&#34;
       GET request to get detailed license information

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._LICENSE
        )
        if flag:
            if response.json():
                self._commcell_id = response.json()[&#39;commcellId&#39;]
                self._cs_hostname = response.json()[&#39;csHostNameOrAddress&#39;]
                self._license_ipaddress = response.json()[&#39;licenseIpAddress&#39;]
                self._oemname = response.json()[&#39;oemName&#39;]
                self._regcode = response.json()[&#39;regCode&#39;]
                self._serialno = response.json()[&#39;serialNo&#39;]
                self._license_mode = response.json()[&#39;licenseMode&#39;]
                self._expiry_date = response.json()[&#39;expiryDate&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)
        
    def refresh(self):
        &#34;&#34;&#34;Updates metrics object with the latest configuration&#34;&#34;&#34;
        self._get_license_details()
        self._get_detailed_licenses()

    @property
    def commcell_id(self):
        &#34;&#34;&#34;Returns the CommCell Id in decimal value&#34;&#34;&#34;
        return self._commcell_id

    @property
    def commcell_id_hex(self):
        &#34;&#34;&#34;Returns the hexadecimal value of commcell id&#34;&#34;&#34;
        ccid = self._commcell_id
        if ccid == -1:
            return &#39;FFFFF&#39;
        return hex(ccid).split(&#39;x&#39;)[1].upper()

    @property
    def cs_hostname(self):
        &#34;&#34;&#34; Returns the csHostName Or Address of CommCell&#34;&#34;&#34;
        return self._cs_hostname

    @property
    def license_ipaddress(self):
        &#34;&#34;&#34; Returns the license Ip Address &#34;&#34;&#34;
        return self._license_ipaddress

    @property
    def oem_name(self):
        &#34;&#34;&#34;Returns the oem_name&#34;&#34;&#34;
        return self._oemname

    @property
    def license_mode(self):
        &#34;&#34;&#34; Returns the license mode of license&#34;&#34;&#34;
        return self._license_mode

    @property
    def registration_code(self):
        &#34;&#34;&#34;Returns the registration code of CommCell&#34;&#34;&#34;
        return self._regcode

    @property
    def serial_number(self):
        &#34;&#34;&#34;Returns the serial number of CommCell&#34;&#34;&#34;
        return self._serialno

    @property
    def expiry_date(self):
        &#34;&#34;&#34; Returns the expiry date of License&#34;&#34;&#34;
        return self._expiry_date

    @property
    def capacity_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the capacity licenses&#34;&#34;&#34;
        return self._capacity_licenses

    @property
    def complete_oi_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the complete oi licenses&#34;&#34;&#34;
        return self._complete_oi_licenses

    @property
    def virtualization_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the virtualization licenses&#34;&#34;&#34;
        return self._virtualization_licenses

    @property
    def user_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the user licenses&#34;&#34;&#34;
        return self._user_licenses

    @property
    def activate_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the activate licenses&#34;&#34;&#34;
        return self._activate_licenses

    @property
    def metallic_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the metallic licenses&#34;&#34;&#34;
        return self._metallic_licenses

    @property
    def other_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the other licenses&#34;&#34;&#34;
        return self._other_licenses</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.license.LicenseDetails"><code class="flex name class">
<span>class <span class="ident">LicenseDetails</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for accessing license details information</p>
<p>Initialize object of the LicenseDetails class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the LicenseDetails class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L89-L412" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class LicenseDetails(object):
    &#34;&#34;&#34;Class for accessing license details information&#34;&#34;&#34;    

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the LicenseDetails class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the LicenseDetails class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._LICENSE = self._commcell_object._services[&#39;LICENSE&#39;]
        self._APPLY_LICENSE = self._commcell_object._services[&#39;APPLY_LICENSE&#39;]
        self._capacity_licenses = None
        self._complete_oi_licenses = None
        self._virtualization_licenses = None
        self._user_licenses = None
        self._activate_licenses = None
        self._metallic_licenses = None
        self._other_licenses = None
        self._get_license_details()

    def _get_detailed_licenses(self):
        &#34;&#34;&#34;
        Gets all types of license details associated to the commcell object
        &#34;&#34;&#34;
        self._get_capacity_details()
        self._get_complete_oi_licenses()
        self._get_virtualization_licenses()
        self._get_user_licenses()
        self._get_activate_licenses()
        self._get_metallic_licenses()
        self._get_other_licenses()

    def _get_capacity_details(self):
        &#34;&#34;&#34;
        Request to get capacity licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;

        self._CAPACITY_LICENSE = self._commcell_object._services[&#39;CAPACITY_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._CAPACITY_LICENSE)

        if flag:
            if response.json():
                self._capacity_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_complete_oi_licenses(self):
        &#34;&#34;&#34;
       GET request to get OI licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;

        self._OI_LICENSE = self._commcell_object._services[&#39;OI_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._OI_LICENSE)

        if flag:
            if response.json():
                self._complete_oi_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_virtualization_licenses(self):
        &#34;&#34;&#34;
       GET request to get virtualization licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._VIRTUALIZATION_LICENSE = self._commcell_object._services[&#39;VIRTUALIZATION_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._VIRTUALIZATION_LICENSE)

        if flag:
            if response.json():
                self._virtualization_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_user_licenses(self):
        &#34;&#34;&#34;
       GET request to get user licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._USER_LICENSE = self._commcell_object._services[&#39;USER_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._USER_LICENSE)

        if flag:
            if response.json():
                self._user_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_activate_licenses(self):
        &#34;&#34;&#34;
       GET request to get activate licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._ACTIVATE_LICENSE = self._commcell_object._services[&#39;ACTIVATE_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._ACTIVATE_LICENSE)

        if flag:
            if response.json():
                self._activate_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_metallic_licenses(self):
        &#34;&#34;&#34;
       GET request to get metallic licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._METALLIC_LICENSE = self._commcell_object._services[&#39;METALLIC_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._METALLIC_LICENSE)

        if flag:
            if response.json():
                self._metallic_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_other_licenses(self):
        &#34;&#34;&#34;
       GET request to get other licenses property

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;
        self._OTHER_LICENSE = self._commcell_object._services[&#39;OTHER_LICENSE&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._OTHER_LICENSE)

        if flag:
            if response.json():
                self._other_licenses = response.json().get(&#39;records&#39;,None)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def _get_license_details(self):
        &#34;&#34;&#34;
       GET request to get detailed license information

        Raises:
            SDKException:

                if response is not success

                if response is empty

        &#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._LICENSE
        )
        if flag:
            if response.json():
                self._commcell_id = response.json()[&#39;commcellId&#39;]
                self._cs_hostname = response.json()[&#39;csHostNameOrAddress&#39;]
                self._license_ipaddress = response.json()[&#39;licenseIpAddress&#39;]
                self._oemname = response.json()[&#39;oemName&#39;]
                self._regcode = response.json()[&#39;regCode&#39;]
                self._serialno = response.json()[&#39;serialNo&#39;]
                self._license_mode = response.json()[&#39;licenseMode&#39;]
                self._expiry_date = response.json()[&#39;expiryDate&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)
        
    def refresh(self):
        &#34;&#34;&#34;Updates metrics object with the latest configuration&#34;&#34;&#34;
        self._get_license_details()
        self._get_detailed_licenses()

    @property
    def commcell_id(self):
        &#34;&#34;&#34;Returns the CommCell Id in decimal value&#34;&#34;&#34;
        return self._commcell_id

    @property
    def commcell_id_hex(self):
        &#34;&#34;&#34;Returns the hexadecimal value of commcell id&#34;&#34;&#34;
        ccid = self._commcell_id
        if ccid == -1:
            return &#39;FFFFF&#39;
        return hex(ccid).split(&#39;x&#39;)[1].upper()

    @property
    def cs_hostname(self):
        &#34;&#34;&#34; Returns the csHostName Or Address of CommCell&#34;&#34;&#34;
        return self._cs_hostname

    @property
    def license_ipaddress(self):
        &#34;&#34;&#34; Returns the license Ip Address &#34;&#34;&#34;
        return self._license_ipaddress

    @property
    def oem_name(self):
        &#34;&#34;&#34;Returns the oem_name&#34;&#34;&#34;
        return self._oemname

    @property
    def license_mode(self):
        &#34;&#34;&#34; Returns the license mode of license&#34;&#34;&#34;
        return self._license_mode

    @property
    def registration_code(self):
        &#34;&#34;&#34;Returns the registration code of CommCell&#34;&#34;&#34;
        return self._regcode

    @property
    def serial_number(self):
        &#34;&#34;&#34;Returns the serial number of CommCell&#34;&#34;&#34;
        return self._serialno

    @property
    def expiry_date(self):
        &#34;&#34;&#34; Returns the expiry date of License&#34;&#34;&#34;
        return self._expiry_date

    @property
    def capacity_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the capacity licenses&#34;&#34;&#34;
        return self._capacity_licenses

    @property
    def complete_oi_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the complete oi licenses&#34;&#34;&#34;
        return self._complete_oi_licenses

    @property
    def virtualization_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the virtualization licenses&#34;&#34;&#34;
        return self._virtualization_licenses

    @property
    def user_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the user licenses&#34;&#34;&#34;
        return self._user_licenses

    @property
    def activate_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the activate licenses&#34;&#34;&#34;
        return self._activate_licenses

    @property
    def metallic_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the metallic licenses&#34;&#34;&#34;
        return self._metallic_licenses

    @property
    def other_licenses(self):
        &#34;&#34;&#34;Returns dictionary with the other licenses&#34;&#34;&#34;
        return self._other_licenses</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.license.LicenseDetails.activate_licenses"><code class="name">var <span class="ident">activate_licenses</span></code></dt>
<dd>
<div class="desc"><p>Returns dictionary with the activate licenses</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L399-L402" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def activate_licenses(self):
    &#34;&#34;&#34;Returns dictionary with the activate licenses&#34;&#34;&#34;
    return self._activate_licenses</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.capacity_licenses"><code class="name">var <span class="ident">capacity_licenses</span></code></dt>
<dd>
<div class="desc"><p>Returns dictionary with the capacity licenses</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L379-L382" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def capacity_licenses(self):
    &#34;&#34;&#34;Returns dictionary with the capacity licenses&#34;&#34;&#34;
    return self._capacity_licenses</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.commcell_id"><code class="name">var <span class="ident">commcell_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the CommCell Id in decimal value</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L331-L334" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def commcell_id(self):
    &#34;&#34;&#34;Returns the CommCell Id in decimal value&#34;&#34;&#34;
    return self._commcell_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.commcell_id_hex"><code class="name">var <span class="ident">commcell_id_hex</span></code></dt>
<dd>
<div class="desc"><p>Returns the hexadecimal value of commcell id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L336-L342" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def commcell_id_hex(self):
    &#34;&#34;&#34;Returns the hexadecimal value of commcell id&#34;&#34;&#34;
    ccid = self._commcell_id
    if ccid == -1:
        return &#39;FFFFF&#39;
    return hex(ccid).split(&#39;x&#39;)[1].upper()</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.complete_oi_licenses"><code class="name">var <span class="ident">complete_oi_licenses</span></code></dt>
<dd>
<div class="desc"><p>Returns dictionary with the complete oi licenses</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L384-L387" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def complete_oi_licenses(self):
    &#34;&#34;&#34;Returns dictionary with the complete oi licenses&#34;&#34;&#34;
    return self._complete_oi_licenses</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.cs_hostname"><code class="name">var <span class="ident">cs_hostname</span></code></dt>
<dd>
<div class="desc"><p>Returns the csHostName Or Address of CommCell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L344-L347" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cs_hostname(self):
    &#34;&#34;&#34; Returns the csHostName Or Address of CommCell&#34;&#34;&#34;
    return self._cs_hostname</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.expiry_date"><code class="name">var <span class="ident">expiry_date</span></code></dt>
<dd>
<div class="desc"><p>Returns the expiry date of License</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L374-L377" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def expiry_date(self):
    &#34;&#34;&#34; Returns the expiry date of License&#34;&#34;&#34;
    return self._expiry_date</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.license_ipaddress"><code class="name">var <span class="ident">license_ipaddress</span></code></dt>
<dd>
<div class="desc"><p>Returns the license Ip Address</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L349-L352" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def license_ipaddress(self):
    &#34;&#34;&#34; Returns the license Ip Address &#34;&#34;&#34;
    return self._license_ipaddress</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.license_mode"><code class="name">var <span class="ident">license_mode</span></code></dt>
<dd>
<div class="desc"><p>Returns the license mode of license</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L359-L362" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def license_mode(self):
    &#34;&#34;&#34; Returns the license mode of license&#34;&#34;&#34;
    return self._license_mode</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.metallic_licenses"><code class="name">var <span class="ident">metallic_licenses</span></code></dt>
<dd>
<div class="desc"><p>Returns dictionary with the metallic licenses</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L404-L407" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def metallic_licenses(self):
    &#34;&#34;&#34;Returns dictionary with the metallic licenses&#34;&#34;&#34;
    return self._metallic_licenses</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.oem_name"><code class="name">var <span class="ident">oem_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the oem_name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L354-L357" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def oem_name(self):
    &#34;&#34;&#34;Returns the oem_name&#34;&#34;&#34;
    return self._oemname</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.other_licenses"><code class="name">var <span class="ident">other_licenses</span></code></dt>
<dd>
<div class="desc"><p>Returns dictionary with the other licenses</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L409-L412" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def other_licenses(self):
    &#34;&#34;&#34;Returns dictionary with the other licenses&#34;&#34;&#34;
    return self._other_licenses</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.registration_code"><code class="name">var <span class="ident">registration_code</span></code></dt>
<dd>
<div class="desc"><p>Returns the registration code of CommCell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L364-L367" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def registration_code(self):
    &#34;&#34;&#34;Returns the registration code of CommCell&#34;&#34;&#34;
    return self._regcode</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.serial_number"><code class="name">var <span class="ident">serial_number</span></code></dt>
<dd>
<div class="desc"><p>Returns the serial number of CommCell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L369-L372" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def serial_number(self):
    &#34;&#34;&#34;Returns the serial number of CommCell&#34;&#34;&#34;
    return self._serialno</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.user_licenses"><code class="name">var <span class="ident">user_licenses</span></code></dt>
<dd>
<div class="desc"><p>Returns dictionary with the user licenses</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L394-L397" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_licenses(self):
    &#34;&#34;&#34;Returns dictionary with the user licenses&#34;&#34;&#34;
    return self._user_licenses</code></pre>
</details>
</dd>
<dt id="cvpysdk.license.LicenseDetails.virtualization_licenses"><code class="name">var <span class="ident">virtualization_licenses</span></code></dt>
<dd>
<div class="desc"><p>Returns dictionary with the virtualization licenses</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L389-L392" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def virtualization_licenses(self):
    &#34;&#34;&#34;Returns dictionary with the virtualization licenses&#34;&#34;&#34;
    return self._virtualization_licenses</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.license.LicenseDetails.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates metrics object with the latest configuration</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/license.py#L326-L329" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Updates metrics object with the latest configuration&#34;&#34;&#34;
    self._get_license_details()
    self._get_detailed_licenses()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#licensedetails-attributes">LicenseDetails Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.license.LicenseDetails" href="#cvpysdk.license.LicenseDetails">LicenseDetails</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.license.LicenseDetails.activate_licenses" href="#cvpysdk.license.LicenseDetails.activate_licenses">activate_licenses</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.capacity_licenses" href="#cvpysdk.license.LicenseDetails.capacity_licenses">capacity_licenses</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.commcell_id" href="#cvpysdk.license.LicenseDetails.commcell_id">commcell_id</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.commcell_id_hex" href="#cvpysdk.license.LicenseDetails.commcell_id_hex">commcell_id_hex</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.complete_oi_licenses" href="#cvpysdk.license.LicenseDetails.complete_oi_licenses">complete_oi_licenses</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.cs_hostname" href="#cvpysdk.license.LicenseDetails.cs_hostname">cs_hostname</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.expiry_date" href="#cvpysdk.license.LicenseDetails.expiry_date">expiry_date</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.license_ipaddress" href="#cvpysdk.license.LicenseDetails.license_ipaddress">license_ipaddress</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.license_mode" href="#cvpysdk.license.LicenseDetails.license_mode">license_mode</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.metallic_licenses" href="#cvpysdk.license.LicenseDetails.metallic_licenses">metallic_licenses</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.oem_name" href="#cvpysdk.license.LicenseDetails.oem_name">oem_name</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.other_licenses" href="#cvpysdk.license.LicenseDetails.other_licenses">other_licenses</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.refresh" href="#cvpysdk.license.LicenseDetails.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.registration_code" href="#cvpysdk.license.LicenseDetails.registration_code">registration_code</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.serial_number" href="#cvpysdk.license.LicenseDetails.serial_number">serial_number</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.user_licenses" href="#cvpysdk.license.LicenseDetails.user_licenses">user_licenses</a></code></li>
<li><code><a title="cvpysdk.license.LicenseDetails.virtualization_licenses" href="#cvpysdk.license.LicenseDetails.virtualization_licenses">virtualization_licenses</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>