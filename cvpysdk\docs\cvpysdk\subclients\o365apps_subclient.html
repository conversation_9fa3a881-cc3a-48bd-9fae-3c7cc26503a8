<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.o365apps_subclient API documentation</title>
<meta name="description" content="Main file for common operations for the Office 365 Apps Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.o365apps_subclient</code></h1>
</header>
<section id="section-intro">
<p>Main file for common operations for the Office 365 Apps Subclient</p>
<h2 id="o365appssubclient">O365Appssubclient</h2>
<p>Derived class from CloudAppsSubclient Base class, for common sub-client functionalities
pertaining to the Office 365 Apps</p>
<h1 id="o365appssubclient-attributes">O365AppsSubclient Attributes:</h1>
<pre><code>_prepare_web_search_browse_json()       --          Prepare the JSON for the web search based browse
_process_web_search_response()          --          Process the response received from the do web search browse
do_web_search()                         --          Perform a search of the backed up contents
process_index_retention()               --          Run the retention thread for Office 365 Apps on the INdex Server
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/o365apps_subclient.py#L1-L211" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Comm-vault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for common operations for the Office 365 Apps Subclient

O365AppsSubclient:
    Derived class from CloudAppsSubclient Base class, for common sub-client functionalities
    pertaining to the Office 365 Apps

O365AppsSubclient Attributes:
==============================

    _prepare_web_search_browse_json()       --          Prepare the JSON for the web search based browse
    _process_web_search_response()          --          Process the response received from the do web search browse
    do_web_search()                         --          Perform a search of the backed up contents
    process_index_retention()               --          Run the retention thread for Office 365 Apps on the INdex Server
&#34;&#34;&#34;

import time

from .casubclient import CloudAppsSubclient
from cvpysdk.exception import SDKException


class O365AppsSubclient(CloudAppsSubclient):
    &#34;&#34;&#34;
        Parent class representing the Office 365 Apps based sub-clients.
        Supported agents:
            Dynamics 365 CRM, SharePoint online, OneDrive for Business and MS Teams
    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Sub client object for the given O365Apps Subclient.

            Args:
                backupset_object    (object)    --  instance of the backup-set class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(O365AppsSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

        self._instance_object = backupset_object._instance_object
        self._client_object = self._instance_object._agent_object._client_object
        self._associated_tables: dict = dict()
        self._associated_environments: dict = dict()
        self._discovered_environments: dict = dict()
        self._discovered_tables: dict = dict()
        self._instance_type: int = 35
        self._app_id: int = 134
        # App ID for cloud apps
        self._O365Apps_SET_USER_POLICY_ASSOCIATION = self._commcell_object._services[&#39;SET_USER_POLICY_ASSOCIATION&#39;]
        self._O365APPS_BROWSE = self._commcell_object._services[&#39;DO_WEB_SEARCH&#39;]

    def _process_web_search_response(self, flag, response) -&gt; dict:
        &#34;&#34;&#34;
            Method to process the response from the web search operation

            Arguments:
                flag        (bool)  --  boolean, whether the response was success or not

                response    (dict)  --  JSON response received for the request from the Server
            Returns:
                dict - Dictionary of all the paths with additional metadata retrieved from browse
        &#34;&#34;&#34;
        if flag:
            response_json = response.json()

            _search_result = response_json.get(&#34;searchResult&#34;)
            return _search_result.get(&#34;resultItem&#34;)

        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _prepare_web_search_browse_json(self, browse_options: dict) -&gt; dict:
        &#34;&#34;&#34;
            Prepare the request JSON for the webSearch browse

            Arguments:
                browse_options      dict:   Dictionary of browse options

        &#34;&#34;&#34;
        request_json = {
            &#34;mode&#34;: 4,

            &#34;advSearchGrp&#34;: {
                &#34;commonFilter&#34;: [
                    {
                        &#34;filter&#34;: {
                            &#34;interFilterOP&#34;: 2,
                            &#34;filters&#34;: browse_options.get(&#34;common_filters&#34;, list())
                        }
                    }
                ],
                &#34;fileFilter&#34;: [
                    {
                        &#34;filter&#34;: {
                            &#34;interFilterOP&#34;: 2,
                            &#34;filters&#34;: browse_options.get(&#34;file_filter&#34;, list())
                        }
                    }
                ],
                &#34;galaxyFilter&#34;: [
                    {
                        &#34;appIdList&#34;: [
                            int(self.subclient_id)
                        ]
                    }
                ]
            },
            &#34;searchProcessingInfo&#34;: {
                &#34;resultOffset&#34;: browse_options.get(&#34;offset&#34;, 0),
                &#34;pageSize&#34;: browse_options.get(&#34;page_size&#34;, 10000),
                &#34;queryParams&#34;: browse_options.get(&#34;query_params&#34;, list()),
                &#34;sortParams&#34;: browse_options.get(&#34;sort_param&#34;, list())
            }
        }

        if browse_options.get(&#39;to_time&#39;, 0) != 0:
            _point_in_time_browse_args = {
                &#34;field&#34;: &#34;BACKUPTIME&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;0&#34;,
                        str(browse_options.get(&#34;to_time&#34;))
                    ]
                }
            }
            request_json[&#39;advSearchGrp&#39;][&#39;fileFilter&#39;][0][&#39;filter&#39;][&#39;filters&#39;].append(_point_in_time_browse_args)

        return request_json

    def do_web_search(self, **kwargs) -&gt; dict:
        &#34;&#34;&#34;
            Method to perform a web search using the /Search endpoint.
            Default browse endpoint for new O365 agents.

            Arguments:
                kwargs:     Dictionary of arguments to be used for the browse
        &#34;&#34;&#34;
        _browse_options = kwargs
        _retry = kwargs.get(&#34;retry&#34;, 10)

        _browse_req = self._prepare_web_search_browse_json(browse_options=_browse_options)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._O365APPS_BROWSE, _browse_req)

        attempt = 1
        while attempt &lt;= _retry:
            if response.json() == {}:
                time.sleep(120)
                flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._WEB_SEARCH, _browse_req)
            else:
                break
            attempt += 1
        return self._process_web_search_response(flag, response)

    def process_index_retention(self, index_server_client_id):
        &#34;&#34;&#34;
            Run the retention thread for Dynamics 365/ Office 365 Apps sub-client

         Args:
                index_server_client_id (int)  --  client id of index server

        Raises:

                SDKException:

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        request_json = {
            &#34;appType&#34;: int(self._instance_object.idx_app_type),  # 200127
            &#34;indexServerClientId&#34;: index_server_client_id
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
        )
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to process index retention request\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to process index retention request\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient"><code class="flex name class">
<span>class <span class="ident">O365AppsSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Parent class representing the Office 365 Apps based sub-clients.
Supported agents:
Dynamics 365 CRM, SharePoint online, OneDrive for Business and MS Teams</p>
<p>Initialize the Sub client object for the given O365Apps Subclient.</p>
<h2 id="args">Args</h2>
<p>backupset_object
(object)
&ndash;
instance of the backup-set class</p>
<p>subclient_name
(str)
&ndash;
subclient name</p>
<p>subclient_id
(int)
&ndash;
subclient id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/o365apps_subclient.py#L40-L211" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class O365AppsSubclient(CloudAppsSubclient):
    &#34;&#34;&#34;
        Parent class representing the Office 365 Apps based sub-clients.
        Supported agents:
            Dynamics 365 CRM, SharePoint online, OneDrive for Business and MS Teams
    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Sub client object for the given O365Apps Subclient.

            Args:
                backupset_object    (object)    --  instance of the backup-set class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(O365AppsSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

        self._instance_object = backupset_object._instance_object
        self._client_object = self._instance_object._agent_object._client_object
        self._associated_tables: dict = dict()
        self._associated_environments: dict = dict()
        self._discovered_environments: dict = dict()
        self._discovered_tables: dict = dict()
        self._instance_type: int = 35
        self._app_id: int = 134
        # App ID for cloud apps
        self._O365Apps_SET_USER_POLICY_ASSOCIATION = self._commcell_object._services[&#39;SET_USER_POLICY_ASSOCIATION&#39;]
        self._O365APPS_BROWSE = self._commcell_object._services[&#39;DO_WEB_SEARCH&#39;]

    def _process_web_search_response(self, flag, response) -&gt; dict:
        &#34;&#34;&#34;
            Method to process the response from the web search operation

            Arguments:
                flag        (bool)  --  boolean, whether the response was success or not

                response    (dict)  --  JSON response received for the request from the Server
            Returns:
                dict - Dictionary of all the paths with additional metadata retrieved from browse
        &#34;&#34;&#34;
        if flag:
            response_json = response.json()

            _search_result = response_json.get(&#34;searchResult&#34;)
            return _search_result.get(&#34;resultItem&#34;)

        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _prepare_web_search_browse_json(self, browse_options: dict) -&gt; dict:
        &#34;&#34;&#34;
            Prepare the request JSON for the webSearch browse

            Arguments:
                browse_options      dict:   Dictionary of browse options

        &#34;&#34;&#34;
        request_json = {
            &#34;mode&#34;: 4,

            &#34;advSearchGrp&#34;: {
                &#34;commonFilter&#34;: [
                    {
                        &#34;filter&#34;: {
                            &#34;interFilterOP&#34;: 2,
                            &#34;filters&#34;: browse_options.get(&#34;common_filters&#34;, list())
                        }
                    }
                ],
                &#34;fileFilter&#34;: [
                    {
                        &#34;filter&#34;: {
                            &#34;interFilterOP&#34;: 2,
                            &#34;filters&#34;: browse_options.get(&#34;file_filter&#34;, list())
                        }
                    }
                ],
                &#34;galaxyFilter&#34;: [
                    {
                        &#34;appIdList&#34;: [
                            int(self.subclient_id)
                        ]
                    }
                ]
            },
            &#34;searchProcessingInfo&#34;: {
                &#34;resultOffset&#34;: browse_options.get(&#34;offset&#34;, 0),
                &#34;pageSize&#34;: browse_options.get(&#34;page_size&#34;, 10000),
                &#34;queryParams&#34;: browse_options.get(&#34;query_params&#34;, list()),
                &#34;sortParams&#34;: browse_options.get(&#34;sort_param&#34;, list())
            }
        }

        if browse_options.get(&#39;to_time&#39;, 0) != 0:
            _point_in_time_browse_args = {
                &#34;field&#34;: &#34;BACKUPTIME&#34;,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        &#34;0&#34;,
                        str(browse_options.get(&#34;to_time&#34;))
                    ]
                }
            }
            request_json[&#39;advSearchGrp&#39;][&#39;fileFilter&#39;][0][&#39;filter&#39;][&#39;filters&#39;].append(_point_in_time_browse_args)

        return request_json

    def do_web_search(self, **kwargs) -&gt; dict:
        &#34;&#34;&#34;
            Method to perform a web search using the /Search endpoint.
            Default browse endpoint for new O365 agents.

            Arguments:
                kwargs:     Dictionary of arguments to be used for the browse
        &#34;&#34;&#34;
        _browse_options = kwargs
        _retry = kwargs.get(&#34;retry&#34;, 10)

        _browse_req = self._prepare_web_search_browse_json(browse_options=_browse_options)
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._O365APPS_BROWSE, _browse_req)

        attempt = 1
        while attempt &lt;= _retry:
            if response.json() == {}:
                time.sleep(120)
                flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._WEB_SEARCH, _browse_req)
            else:
                break
            attempt += 1
        return self._process_web_search_response(flag, response)

    def process_index_retention(self, index_server_client_id):
        &#34;&#34;&#34;
            Run the retention thread for Dynamics 365/ Office 365 Apps sub-client

         Args:
                index_server_client_id (int)  --  client id of index server

        Raises:

                SDKException:

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        request_json = {
            &#34;appType&#34;: int(self._instance_object.idx_app_type),  # 200127
            &#34;indexServerClientId&#34;: index_server_client_id
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
        )
        if flag:
            if response.json():
                if &#34;resp&#34; in response.json():
                    error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                        o_str = &#39;Failed to process index retention request\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to process index retention request\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient" href="cloudapps/dynamics365_subclient.html#cvpysdk.subclients.cloudapps.dynamics365_subclient.MSDynamics365Subclient">MSDynamics365Subclient</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.do_web_search"><code class="name flex">
<span>def <span class="ident">do_web_search</span></span>(<span>self, **kwargs) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Method to perform a web search using the /Search endpoint.
Default browse endpoint for new O365 agents.</p>
<h2 id="arguments">Arguments</h2>
<p>kwargs:
Dictionary of arguments to be used for the browse</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/o365apps_subclient.py#L151-L173" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def do_web_search(self, **kwargs) -&gt; dict:
    &#34;&#34;&#34;
        Method to perform a web search using the /Search endpoint.
        Default browse endpoint for new O365 agents.

        Arguments:
            kwargs:     Dictionary of arguments to be used for the browse
    &#34;&#34;&#34;
    _browse_options = kwargs
    _retry = kwargs.get(&#34;retry&#34;, 10)

    _browse_req = self._prepare_web_search_browse_json(browse_options=_browse_options)
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._O365APPS_BROWSE, _browse_req)

    attempt = 1
    while attempt &lt;= _retry:
        if response.json() == {}:
            time.sleep(120)
            flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._WEB_SEARCH, _browse_req)
        else:
            break
        attempt += 1
    return self._process_web_search_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.process_index_retention"><code class="name flex">
<span>def <span class="ident">process_index_retention</span></span>(<span>self, index_server_client_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Run the retention thread for Dynamics 365/ Office 365 Apps sub-client</p>
<p>Args:
index_server_client_id (int)
&ndash;
client id of index server</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/o365apps_subclient.py#L175-L211" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def process_index_retention(self, index_server_client_id):
    &#34;&#34;&#34;
        Run the retention thread for Dynamics 365/ Office 365 Apps sub-client

     Args:
            index_server_client_id (int)  --  client id of index server

    Raises:

            SDKException:

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    request_json = {
        &#34;appType&#34;: int(self._instance_object.idx_app_type),  # 200127
        &#34;indexServerClientId&#34;: index_server_client_id
    }
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;OFFICE365_PROCESS_INDEX_RETENTION_RULES&#39;], request_json
    )
    if flag:
        if response.json():
            if &#34;resp&#34; in response.json():
                error_code = response.json()[&#39;resp&#39;][&#39;errorCode&#39;]
                if error_code != 0:
                    error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                    o_str = &#39;Failed to process index retention request\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            elif &#39;errorMessage&#39; in response.json():
                error_string = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Failed to process index retention request\nError: &#34;{0}&#34;&#39;.format(error_string)
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient" href="casubclient.html#cvpysdk.subclients.casubclient.CloudAppsSubclient">CloudAppsSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.backup" href="../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.restore_in_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.casubclient.CloudAppsSubclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#o365appssubclient-attributes">O365AppsSubclient Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient" href="#cvpysdk.subclients.o365apps_subclient.O365AppsSubclient">O365AppsSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.do_web_search" href="#cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.do_web_search">do_web_search</a></code></li>
<li><code><a title="cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.process_index_retention" href="#cvpysdk.subclients.o365apps_subclient.O365AppsSubclient.process_index_retention">process_index_retention</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>