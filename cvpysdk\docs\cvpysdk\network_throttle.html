<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.network_throttle API documentation</title>
<meta name="description" content="Main file for performing network throttle related operations on a client/client group …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.network_throttle</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing network throttle related operations on a client/client group</p>
<h2 id="networkthrottle">Networkthrottle</h2>
<p><strong>init</strong>(class_object)
&ndash;
initialize object of the NetworkThrottle class</p>
<p>_get_throttle_properties()
&ndash;
returns all the existing network throttle properties
on a client/client group</p>
<p>enable_network_throttle()
&ndash;
enables network throttling option on the
client/client group</p>
<p>share_bandwidth()
&ndash;
enables share bandwidth option on the
client/client group</p>
<p>remote_clients()
&ndash;
adds the remote clients for throttling on a
client/client group</p>
<p>remote_client_groups()
&ndash;
adds the remote client group for throttling on a
client/client group</p>
<p>throttle_schedules()
&ndash;
adds the throttling schedules with different options
provided</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network_throttle.py#L1-L393" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing network throttle related operations on a client/client group

NetworkThrottle:

    __init__(class_object)             --    initialize object of the NetworkThrottle class

    _get_throttle_properties()         --    returns all the existing network throttle properties
                                             on a client/client group

    enable_network_throttle()          --    enables network throttling option on the
                                             client/client group

    share_bandwidth()                  --    enables share bandwidth option on the
                                             client/client group

    remote_clients()                   --    adds the remote clients for throttling on a
                                             client/client group

    remote_client_groups()             --    adds the remote client group for throttling on a
                                             client/client group

    throttle_schedules()               --    adds the throttling schedules with different options
                                             provided

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from .exception import SDKException


class NetworkThrottle(object):
    &#34;&#34;&#34;Class for performing network throttle related operations on a client or client group&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initialize the NetworkThrottle class object

            Args:
                class_object (object)  --  instance of the client/client group class

        &#34;&#34;&#34;
        from .client import Client
        from .clientgroup import ClientGroup

        self._class_object = class_object
        self._commcell_object = self._class_object._commcell_object
        self.flag = &#34;&#34;
        self.is_client = None
        self.is_client_group = None

        if isinstance(class_object, Client):
            self._client_object = class_object
            self.is_client = True

        elif isinstance(class_object, ClientGroup):
            self._clientgroup_object = class_object
            self.is_client_group = True

        self._enable_network_throttling = None
        self._share_bandwidth = None
        self._throttle_schedules = []
        self._remote_client_groups = []
        self._remote_clients = []
        self._get_throttle_properties()

    def _get_throttle_properties(self):
        &#34;&#34;&#34;Get all the existing network throttle properties on a client/client group
        and retain each of them


        &#34;&#34;&#34;
        if self.is_client:
            throttle_prop = self._client_object._properties[&#39;clientProps&#39;]

        elif self.is_client_group:
            throttle_prop = self._clientgroup_object._properties

        if &#39;networkThrottle&#39; in throttle_prop:
            self._enable_network_throttling = (throttle_prop[&#39;networkThrottle&#39;][&#39;enableThrottle&#39;])

            self._share_bandwidth = (throttle_prop.get(
                &#39;networkThrottle&#39;).get(&#39;throttle&#39;, {}).get(&#39;shareBandwidth&#39;, True))

            self._throttle_schedules = (throttle_prop.get(
                &#39;networkThrottle&#39;).get(&#39;throttle&#39;, {}).get(&#39;throttle&#39;, []))

            self._remote_client_groups = (throttle_prop.get(&#39;networkThrottle&#39;).get(
                &#39;clientGroupList&#39;, []))

            self._remote_clients = (throttle_prop.get(&#39;networkThrottle&#39;).get(&#39;clientList&#39;, []))

    @property
    def enable_network_throttle(self):
        &#34;&#34;&#34;Gets the value for enable network throttling

        &#34;&#34;&#34;
        return self._enable_network_throttling

    @enable_network_throttle.setter
    def enable_network_throttle(self, val):
        &#34;&#34;&#34;sets the value for enable network throttling

            Args:
                val (boolean) --  value for enable network throttling

        &#34;&#34;&#34;

        self._enable_network_throttling = val
        self._config_network_throttle()

    @property
    def share_bandwidth(self):
        &#34;&#34;&#34;Gets the value for share bandwidth

        &#34;&#34;&#34;
        return self._share_bandwidth

    @share_bandwidth.setter
    def share_bandwidth(self, val):
        &#34;&#34;&#34;Sets the value for share bandwidth

            Args:
                val (boolean) --  value for share bandwidth

        &#34;&#34;&#34;

        self._share_bandwidth = val
        self.enable_network_throttle = True

    @property
    def remote_clients(self):
        &#34;&#34;&#34;Gets the associated client towards which throttling is configured

        &#34;&#34;&#34;
        clients = []

        for client in self._remote_clients:
            clients.append(client[&#39;clientName&#39;])
        return clients

    @remote_clients.setter
    def remote_clients(self, clients):
        &#34;&#34;&#34;Sets the remote clients towards which throttling will be configured

            Args:
                clients (list) --   list of clients

        &#34;&#34;&#34;

        for client in clients:
            client_dict = {
                &#34;clientName&#34;: client
            }
            self._remote_clients.append(client_dict)

        self.enable_network_throttle = True

    @property
    def remote_client_groups(self):
        &#34;&#34;&#34;Gets the associated client groups towards which throttling is configured

        &#34;&#34;&#34;
        client_groups = []

        for client_group in self._remote_client_groups:
            client_groups.append(client_group[&#39;clientGroupName&#39;])
        return client_groups

    @remote_client_groups.setter
    def remote_client_groups(self, client_groups):
        &#34;&#34;&#34;Sets the remote client groups towards which throttling will be configured

            Args:
                client_groups (list) -- list of client groups

        &#34;&#34;&#34;

        for client_group in client_groups:
            client_group_dict = {
                &#34;clientGroupName&#34;: client_group
            }
            self._remote_client_groups.append(client_group_dict)

        self.enable_network_throttle = True

    @property
    def throttle_schedules(self):
        &#34;&#34;&#34;Gets the throttle rules set on a client or client group

        &#34;&#34;&#34;
        return self._throttle_schedules

    @throttle_schedules.setter
    def throttle_schedules(self, throttle_rules):
        &#34;&#34;&#34;Sets different throttle schedules on a client/client group

            Args:
                throttle_rules (list of dict) --  list of throttle rules

            Supported keys:

                &#34;sendRate&#34;
                &#34;sendEnabled&#34;
                &#34;receiveEnabled&#34;
                &#34;recvRate&#34;
                &#34;days&#34;
                &#34;isAbsolute&#34;
                &#34;startTime&#34;
                &#34;endTime&#34;
                &#34;sendRatePercent&#34;
                &#34;recvRatePercent&#34;


            Example:
             [
                {
                    &#34;sendRate&#34;: 1024,
                    &#34;sendEnabled&#34;: true,
                    &#34;receiveEnabled&#34;: true,
                    &#34;recvRate&#34;: 1024,
                    &#34;days&#34;: &#39;1010101&#39;,
                    &#34;isAbsolute&#34;: true,
                    &#34;startTime&#34;: 0,
                    &#34;endTime&#34;: 0,
                    &#34;sendRatePercent&#34;: 40,
                    &#34;recvRatePercent&#34;: 40
                },

                {
                    &#34;sendRate&#34;: 1024,
                    &#34;sendEnabled&#34;: True,
                    &#34;receiveEnabled&#34;: True,
                    &#34;recvRate&#34;: 1024,
                    &#34;days&#34;: &#39;1111111&#39;,
                    &#34;isAbsolute&#34;: False

                }
            ]

        &#34;&#34;&#34;

        for throttle_rule in throttle_rules:
            days = int(throttle_rule.get(&#39;days&#39;, &#39;1111111&#39;), 2)
            throttle_rule_dict = {
                &#34;sendRate&#34;: throttle_rule.get(&#39;sendRate&#39;, 1024),
                &#34;sendEnabled&#34;: throttle_rule.get(&#39;sendEnabled&#39;, False),
                &#34;receiveEnabled&#34;: throttle_rule.get(&#39;receiveEnabled&#39;, False),
                &#34;recvRate&#34;: throttle_rule.get(&#39;recvRate&#39;, 1024),
                &#34;days&#34;: days,
                &#34;isAbsolute&#34;: throttle_rule.get(&#39;isAbsolute&#39;, True),
                &#34;startTime&#34;: 0,
                &#34;endTime&#34;: 0,
                &#34;sendRatePercent&#34;: throttle_rule.get(&#39;sendRatePercent&#39;, 40),
                &#34;recvRatePercent&#34;: throttle_rule.get(&#39;recvRatePercent&#39;, 40)
            }

            self._throttle_schedules.append(throttle_rule_dict)

        self.enable_network_throttle = True

    def _config_network_throttle(self):
        &#34;&#34;&#34;Sets network throttle properties on the client/client group


                    Raises:
                        SDKException:
                            if  request was not successful

                            if  invalid input was provided in the request

                            if empty response was received

        &#34;&#34;&#34;

        update_props_call = None
        request_url = None

        if self.is_client:
            update_props_call = self._client_object.refresh
            request_url = self._client_object._CLIENT
            if not self._enable_network_throttling:
                update_networkconfig_dict = {
                    &#34;networkThrottle&#34;:
                        {
                            &#34;enableThrottle&#34;: self._enable_network_throttling
                        }
                }

            else:
                update_networkconfig_dict = {
                    &#34;networkThrottle&#34;: {
                        &#34;enableThrottle&#34;: self._enable_network_throttling,
                        &#34;throttle&#34;: {
                            &#34;shareBandwidth&#34;: self._share_bandwidth,
                            &#34;throttle&#34;: self._throttle_schedules
                        },
                        &#34;clientGroupList&#34;:
                            self._remote_client_groups,

                        &#34;clientList&#34;: self._remote_clients
                    }
                }

            request_json = self._client_object._update_client_props_json(update_networkconfig_dict)

        elif self.is_client_group:
            update_props_call = self._clientgroup_object.refresh
            request_url = self._clientgroup_object._CLIENTGROUP

            if not self._enable_network_throttling:
                request_json = {
                    &#34;clientGroupOperationType&#34;: 2,
                    &#34;clientGroupDetail&#34;: {
                        &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self._clientgroup_object._clientgroup_name
                        },
                        &#34;networkThrottle&#34;: {
                            &#34;enableThrottle&#34;: self._enable_network_throttling,

                        }
                    }
                }

            else:
                request_json = {
                    &#34;clientGroupOperationType&#34;: 2,
                    &#34;clientGroupDetail&#34;: {
                        &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self._clientgroup_object._clientgroup_name
                            },
                        &#34;networkThrottle&#34;: {
                            &#34;enableThrottle&#34;: self._enable_network_throttling,
                            &#34;throttle&#34;: {
                                &#34;shareBandwidth&#34;: self._share_bandwidth,
                                &#34;throttle&#34;: self._throttle_schedules
                            },
                            &#34;clientGroupList&#34;: self._remote_client_groups,

                            &#34;clientList&#34;: self._remote_clients
                        }

                    }}

        flag, response = (self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request_url, request_json))

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                self.error_code = response.json()[&#39;response&#39;][0][&#39;errorCode&#39;]

            elif response.json():
                self.error_code = str(response.json()[&#39;errorCode&#39;])

                if self.error_code == 0 or self.error_code == &#39;0&#39;:
                    update_props_call()

                elif &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    update_props_call()
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)

                elif self.error_code != &#39;0&#39; and self.is_client_group:
                    update_props_call()
                    raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;,
                                       &#39;Client group properties were not updated&#39;)

            else:
                update_props_call()
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            update_props_call()
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.network_throttle.NetworkThrottle"><code class="flex name class">
<span>class <span class="ident">NetworkThrottle</span></span>
<span>(</span><span>class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing network throttle related operations on a client or client group</p>
<p>Initialize the NetworkThrottle class object</p>
<h2 id="args">Args</h2>
<p>class_object (object)
&ndash;
instance of the client/client group class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network_throttle.py#L51-L393" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class NetworkThrottle(object):
    &#34;&#34;&#34;Class for performing network throttle related operations on a client or client group&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initialize the NetworkThrottle class object

            Args:
                class_object (object)  --  instance of the client/client group class

        &#34;&#34;&#34;
        from .client import Client
        from .clientgroup import ClientGroup

        self._class_object = class_object
        self._commcell_object = self._class_object._commcell_object
        self.flag = &#34;&#34;
        self.is_client = None
        self.is_client_group = None

        if isinstance(class_object, Client):
            self._client_object = class_object
            self.is_client = True

        elif isinstance(class_object, ClientGroup):
            self._clientgroup_object = class_object
            self.is_client_group = True

        self._enable_network_throttling = None
        self._share_bandwidth = None
        self._throttle_schedules = []
        self._remote_client_groups = []
        self._remote_clients = []
        self._get_throttle_properties()

    def _get_throttle_properties(self):
        &#34;&#34;&#34;Get all the existing network throttle properties on a client/client group
        and retain each of them


        &#34;&#34;&#34;
        if self.is_client:
            throttle_prop = self._client_object._properties[&#39;clientProps&#39;]

        elif self.is_client_group:
            throttle_prop = self._clientgroup_object._properties

        if &#39;networkThrottle&#39; in throttle_prop:
            self._enable_network_throttling = (throttle_prop[&#39;networkThrottle&#39;][&#39;enableThrottle&#39;])

            self._share_bandwidth = (throttle_prop.get(
                &#39;networkThrottle&#39;).get(&#39;throttle&#39;, {}).get(&#39;shareBandwidth&#39;, True))

            self._throttle_schedules = (throttle_prop.get(
                &#39;networkThrottle&#39;).get(&#39;throttle&#39;, {}).get(&#39;throttle&#39;, []))

            self._remote_client_groups = (throttle_prop.get(&#39;networkThrottle&#39;).get(
                &#39;clientGroupList&#39;, []))

            self._remote_clients = (throttle_prop.get(&#39;networkThrottle&#39;).get(&#39;clientList&#39;, []))

    @property
    def enable_network_throttle(self):
        &#34;&#34;&#34;Gets the value for enable network throttling

        &#34;&#34;&#34;
        return self._enable_network_throttling

    @enable_network_throttle.setter
    def enable_network_throttle(self, val):
        &#34;&#34;&#34;sets the value for enable network throttling

            Args:
                val (boolean) --  value for enable network throttling

        &#34;&#34;&#34;

        self._enable_network_throttling = val
        self._config_network_throttle()

    @property
    def share_bandwidth(self):
        &#34;&#34;&#34;Gets the value for share bandwidth

        &#34;&#34;&#34;
        return self._share_bandwidth

    @share_bandwidth.setter
    def share_bandwidth(self, val):
        &#34;&#34;&#34;Sets the value for share bandwidth

            Args:
                val (boolean) --  value for share bandwidth

        &#34;&#34;&#34;

        self._share_bandwidth = val
        self.enable_network_throttle = True

    @property
    def remote_clients(self):
        &#34;&#34;&#34;Gets the associated client towards which throttling is configured

        &#34;&#34;&#34;
        clients = []

        for client in self._remote_clients:
            clients.append(client[&#39;clientName&#39;])
        return clients

    @remote_clients.setter
    def remote_clients(self, clients):
        &#34;&#34;&#34;Sets the remote clients towards which throttling will be configured

            Args:
                clients (list) --   list of clients

        &#34;&#34;&#34;

        for client in clients:
            client_dict = {
                &#34;clientName&#34;: client
            }
            self._remote_clients.append(client_dict)

        self.enable_network_throttle = True

    @property
    def remote_client_groups(self):
        &#34;&#34;&#34;Gets the associated client groups towards which throttling is configured

        &#34;&#34;&#34;
        client_groups = []

        for client_group in self._remote_client_groups:
            client_groups.append(client_group[&#39;clientGroupName&#39;])
        return client_groups

    @remote_client_groups.setter
    def remote_client_groups(self, client_groups):
        &#34;&#34;&#34;Sets the remote client groups towards which throttling will be configured

            Args:
                client_groups (list) -- list of client groups

        &#34;&#34;&#34;

        for client_group in client_groups:
            client_group_dict = {
                &#34;clientGroupName&#34;: client_group
            }
            self._remote_client_groups.append(client_group_dict)

        self.enable_network_throttle = True

    @property
    def throttle_schedules(self):
        &#34;&#34;&#34;Gets the throttle rules set on a client or client group

        &#34;&#34;&#34;
        return self._throttle_schedules

    @throttle_schedules.setter
    def throttle_schedules(self, throttle_rules):
        &#34;&#34;&#34;Sets different throttle schedules on a client/client group

            Args:
                throttle_rules (list of dict) --  list of throttle rules

            Supported keys:

                &#34;sendRate&#34;
                &#34;sendEnabled&#34;
                &#34;receiveEnabled&#34;
                &#34;recvRate&#34;
                &#34;days&#34;
                &#34;isAbsolute&#34;
                &#34;startTime&#34;
                &#34;endTime&#34;
                &#34;sendRatePercent&#34;
                &#34;recvRatePercent&#34;


            Example:
             [
                {
                    &#34;sendRate&#34;: 1024,
                    &#34;sendEnabled&#34;: true,
                    &#34;receiveEnabled&#34;: true,
                    &#34;recvRate&#34;: 1024,
                    &#34;days&#34;: &#39;1010101&#39;,
                    &#34;isAbsolute&#34;: true,
                    &#34;startTime&#34;: 0,
                    &#34;endTime&#34;: 0,
                    &#34;sendRatePercent&#34;: 40,
                    &#34;recvRatePercent&#34;: 40
                },

                {
                    &#34;sendRate&#34;: 1024,
                    &#34;sendEnabled&#34;: True,
                    &#34;receiveEnabled&#34;: True,
                    &#34;recvRate&#34;: 1024,
                    &#34;days&#34;: &#39;1111111&#39;,
                    &#34;isAbsolute&#34;: False

                }
            ]

        &#34;&#34;&#34;

        for throttle_rule in throttle_rules:
            days = int(throttle_rule.get(&#39;days&#39;, &#39;1111111&#39;), 2)
            throttle_rule_dict = {
                &#34;sendRate&#34;: throttle_rule.get(&#39;sendRate&#39;, 1024),
                &#34;sendEnabled&#34;: throttle_rule.get(&#39;sendEnabled&#39;, False),
                &#34;receiveEnabled&#34;: throttle_rule.get(&#39;receiveEnabled&#39;, False),
                &#34;recvRate&#34;: throttle_rule.get(&#39;recvRate&#39;, 1024),
                &#34;days&#34;: days,
                &#34;isAbsolute&#34;: throttle_rule.get(&#39;isAbsolute&#39;, True),
                &#34;startTime&#34;: 0,
                &#34;endTime&#34;: 0,
                &#34;sendRatePercent&#34;: throttle_rule.get(&#39;sendRatePercent&#39;, 40),
                &#34;recvRatePercent&#34;: throttle_rule.get(&#39;recvRatePercent&#39;, 40)
            }

            self._throttle_schedules.append(throttle_rule_dict)

        self.enable_network_throttle = True

    def _config_network_throttle(self):
        &#34;&#34;&#34;Sets network throttle properties on the client/client group


                    Raises:
                        SDKException:
                            if  request was not successful

                            if  invalid input was provided in the request

                            if empty response was received

        &#34;&#34;&#34;

        update_props_call = None
        request_url = None

        if self.is_client:
            update_props_call = self._client_object.refresh
            request_url = self._client_object._CLIENT
            if not self._enable_network_throttling:
                update_networkconfig_dict = {
                    &#34;networkThrottle&#34;:
                        {
                            &#34;enableThrottle&#34;: self._enable_network_throttling
                        }
                }

            else:
                update_networkconfig_dict = {
                    &#34;networkThrottle&#34;: {
                        &#34;enableThrottle&#34;: self._enable_network_throttling,
                        &#34;throttle&#34;: {
                            &#34;shareBandwidth&#34;: self._share_bandwidth,
                            &#34;throttle&#34;: self._throttle_schedules
                        },
                        &#34;clientGroupList&#34;:
                            self._remote_client_groups,

                        &#34;clientList&#34;: self._remote_clients
                    }
                }

            request_json = self._client_object._update_client_props_json(update_networkconfig_dict)

        elif self.is_client_group:
            update_props_call = self._clientgroup_object.refresh
            request_url = self._clientgroup_object._CLIENTGROUP

            if not self._enable_network_throttling:
                request_json = {
                    &#34;clientGroupOperationType&#34;: 2,
                    &#34;clientGroupDetail&#34;: {
                        &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self._clientgroup_object._clientgroup_name
                        },
                        &#34;networkThrottle&#34;: {
                            &#34;enableThrottle&#34;: self._enable_network_throttling,

                        }
                    }
                }

            else:
                request_json = {
                    &#34;clientGroupOperationType&#34;: 2,
                    &#34;clientGroupDetail&#34;: {
                        &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self._clientgroup_object._clientgroup_name
                            },
                        &#34;networkThrottle&#34;: {
                            &#34;enableThrottle&#34;: self._enable_network_throttling,
                            &#34;throttle&#34;: {
                                &#34;shareBandwidth&#34;: self._share_bandwidth,
                                &#34;throttle&#34;: self._throttle_schedules
                            },
                            &#34;clientGroupList&#34;: self._remote_client_groups,

                            &#34;clientList&#34;: self._remote_clients
                        }

                    }}

        flag, response = (self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, request_url, request_json))

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                self.error_code = response.json()[&#39;response&#39;][0][&#39;errorCode&#39;]

            elif response.json():
                self.error_code = str(response.json()[&#39;errorCode&#39;])

                if self.error_code == 0 or self.error_code == &#39;0&#39;:
                    update_props_call()

                elif &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    update_props_call()
                    raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)

                elif self.error_code != &#39;0&#39; and self.is_client_group:
                    update_props_call()
                    raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;,
                                       &#39;Client group properties were not updated&#39;)

            else:
                update_props_call()
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            update_props_call()
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.network_throttle.NetworkThrottle.enable_network_throttle"><code class="name">var <span class="ident">enable_network_throttle</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for enable network throttling</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network_throttle.py#L111-L116" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_network_throttle(self):
    &#34;&#34;&#34;Gets the value for enable network throttling

    &#34;&#34;&#34;
    return self._enable_network_throttling</code></pre>
</details>
</dd>
<dt id="cvpysdk.network_throttle.NetworkThrottle.remote_client_groups"><code class="name">var <span class="ident">remote_client_groups</span></code></dt>
<dd>
<div class="desc"><p>Gets the associated client groups towards which throttling is configured</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network_throttle.py#L177-L186" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def remote_client_groups(self):
    &#34;&#34;&#34;Gets the associated client groups towards which throttling is configured

    &#34;&#34;&#34;
    client_groups = []

    for client_group in self._remote_client_groups:
        client_groups.append(client_group[&#39;clientGroupName&#39;])
    return client_groups</code></pre>
</details>
</dd>
<dt id="cvpysdk.network_throttle.NetworkThrottle.remote_clients"><code class="name">var <span class="ident">remote_clients</span></code></dt>
<dd>
<div class="desc"><p>Gets the associated client towards which throttling is configured</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network_throttle.py#L149-L158" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def remote_clients(self):
    &#34;&#34;&#34;Gets the associated client towards which throttling is configured

    &#34;&#34;&#34;
    clients = []

    for client in self._remote_clients:
        clients.append(client[&#39;clientName&#39;])
    return clients</code></pre>
</details>
</dd>
<dt id="cvpysdk.network_throttle.NetworkThrottle.share_bandwidth"><code class="name">var <span class="ident">share_bandwidth</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for share bandwidth</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network_throttle.py#L130-L135" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def share_bandwidth(self):
    &#34;&#34;&#34;Gets the value for share bandwidth

    &#34;&#34;&#34;
    return self._share_bandwidth</code></pre>
</details>
</dd>
<dt id="cvpysdk.network_throttle.NetworkThrottle.throttle_schedules"><code class="name">var <span class="ident">throttle_schedules</span></code></dt>
<dd>
<div class="desc"><p>Gets the throttle rules set on a client or client group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network_throttle.py#L205-L210" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def throttle_schedules(self):
    &#34;&#34;&#34;Gets the throttle rules set on a client or client group

    &#34;&#34;&#34;
    return self._throttle_schedules</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.network_throttle.NetworkThrottle" href="#cvpysdk.network_throttle.NetworkThrottle">NetworkThrottle</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.network_throttle.NetworkThrottle.enable_network_throttle" href="#cvpysdk.network_throttle.NetworkThrottle.enable_network_throttle">enable_network_throttle</a></code></li>
<li><code><a title="cvpysdk.network_throttle.NetworkThrottle.remote_client_groups" href="#cvpysdk.network_throttle.NetworkThrottle.remote_client_groups">remote_client_groups</a></code></li>
<li><code><a title="cvpysdk.network_throttle.NetworkThrottle.remote_clients" href="#cvpysdk.network_throttle.NetworkThrottle.remote_clients">remote_clients</a></code></li>
<li><code><a title="cvpysdk.network_throttle.NetworkThrottle.share_bandwidth" href="#cvpysdk.network_throttle.NetworkThrottle.share_bandwidth">share_bandwidth</a></code></li>
<li><code><a title="cvpysdk.network_throttle.NetworkThrottle.throttle_schedules" href="#cvpysdk.network_throttle.NetworkThrottle.throttle_schedules">throttle_schedules</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>