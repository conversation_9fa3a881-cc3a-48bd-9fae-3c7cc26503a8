<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.globalfilter API documentation</title>
<meta name="description" content="Main file for managing global filters for this commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.globalfilter</code></h1>
</header>
<section id="section-intro">
<p>Main file for managing global filters for this commcell</p>
<p>GlobalFilters and GlobalFilter are the only classes defined in this file</p>
<p>GlobalFilters: Class for managing global filters for this commcell</p>
<p>GlobalFilter: Class to represent one agent specific global filter</p>
<h2 id="globalfilters">Globalfilters</h2>
<p><strong>init</strong>()
&ndash;
initializes global filter class object</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the GlobalFilter
class</p>
<p>get()
&ndash;
returns the GlobalFilter object for specified filter name</p>
<h2 id="globalfilter">Globalfilter</h2>
<p><strong>init</strong>()
&ndash;
initializes global filter object</p>
<p><strong>repr</strong>()
&ndash;
returns string representing this class</p>
<p>_get_global_filters()
&ndash;
gets the global filters associated with commcell
for specified filter</p>
<p>_initialize_global_filters()
&ndash;
initializes GlobalFilter class objects</p>
<p>_update()
&ndash;
updates the global filters list on commcell</p>
<p>content()
&ndash;
returns the list of filters associated with this agent</p>
<p>add()
&ndash;
adds the specified filter to global list</p>
<p>overwrite()
&ndash;
overwrites existing global list with specified</p>
<p>delete_all()
&ndash;
removes all the filters from global filters list</p>
<p>refresh()
&ndash;
refresh the properties of the global filter</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/globalfilter.py#L1-L283" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for managing global filters for this commcell

GlobalFilters and GlobalFilter are the only classes defined in this file

GlobalFilters: Class for managing global filters for this commcell

GlobalFilter: Class to represent one agent specific global filter

GlobalFilters:
    __init__()                      --  initializes global filter class object

    __repr__()                      --  returns the string for the instance of the GlobalFilter
                                            class

    get()                           --  returns the GlobalFilter object for specified filter name


GlobalFilter:
    __init__()                      --  initializes global filter object

    __repr__()                      --  returns string representing this class

    _get_global_filters()           --  gets the global filters associated with commcell
                                            for specified filter

    _initialize_global_filters()    --  initializes GlobalFilter class objects

    _update()                       --  updates the global filters list on commcell

    content()                       --  returns the list of filters associated with this agent

    add()                           --  adds the specified filter to global list

    overwrite()                     --  overwrites existing global list with specified

    delete_all()                    --  removes all the filters from global filters list

    refresh()                       --  refresh the properties of the global filter

&#34;&#34;&#34;

from .exception import SDKException


class GlobalFilters(object):
    &#34;&#34;&#34;Class for managing global filters for this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes global filter object

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the GlobalFilter class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._global_filter_dict = {
            &#34;WINDOWS&#34;: &#39;windowsGlobalFilters&#39;,
            &#34;UNIX&#34;: &#39;unixGlobalFilters&#39;,
            &#34;NAS&#34;:  &#39;nasGlobalFilters&#39;
        }

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the GlobalFilter class.&#34;&#34;&#34;
        o_str = &#34;GlobalFilter class instance for CommServ &#39;{0}&#39;&#34;.format(
            self._commcell_object.commserv_name
        )
        return o_str

    def get(self, filter_name):
        &#34;&#34;&#34;Returns the global filter agent object for specified filter name

            Args:
                filter_name     (str)   -- Global filter name for which the object is to be created
                    Accepted values: WINDOWS/ UNIX/ NAS

            Returns:
                object - GlobalFilter object for specified global filter

            Raises:
                SDKException:
                    if data type of input is invalid

                    if specified global filter doesn&#39;t exist
        &#34;&#34;&#34;
        if not isinstance(filter_name, str):
            raise SDKException(&#39;GlobalFilter&#39;, &#39;101&#39;)

        if filter_name.upper() not in self._global_filter_dict:
            raise SDKException(
                &#39;GlobalFilter&#39;, &#39;102&#39;, &#39;Invalid Global Filter name {0}&#39;.format(filter_name)
            )

        return GlobalFilter(
            self._commcell_object,
            filter_name.upper(),
            self._global_filter_dict[filter_name.upper()]
        )


class GlobalFilter(object):
    &#34;&#34;&#34;Class to represent any one particular agent global filter&#34;&#34;&#34;

    def __init__(self, commcell_object, filter_name, filter_key):
        &#34;&#34;&#34;Initializes global filter object

            Args:
                commcell_object     (object)    -- commcell object

                agent_key           (str)       --  agent key that shall be used in requests
        &#34;&#34;&#34;
        self._filter_name = filter_name
        self._filter_key = filter_key
        self._commcell_object = commcell_object
        self._GLOBAL_FILTER = self._commcell_object._services[&#39;GLOBAL_FILTER&#39;]
        self._content = []

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return &#34;Global Filter object for: {0}&#34;.format(self._filter_name)

    def _get_global_filters(self):
        &#34;&#34;&#34;Returns the global filters associated with this commcell&#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._GLOBAL_FILTER
        )

        if flag:
            if response.json():
                return response.json()
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _initialize_global_filters(self):
        &#34;&#34;&#34;Initializes global filters&#34;&#34;&#34;
        global_filters = self._get_global_filters()

        self._content = []

        if self._filter_key in global_filters:
            self._content = global_filters[self._filter_key]

    def _update(self, op_type, filters_list):
        &#34;&#34;&#34;Updates the global filters list on tise commcell

            Args:
                op_type         (dict)  --  operation type to be performed
                        Accepted values: ADD/ OVERWRITE/ DELETE

                filters_list    (list)  --  list of filters to be associated

            Raises:
                SDKException:
                    if failed to update global filter content

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        op_dict = {
            &#34;ADD&#34;: 1,
            &#34;OVERWRITE&#34;: 1,
            &#34;DELETE&#34;: 3
        }

        request_json = {
            self._filter_key: {
                &#34;opType&#34;: op_dict[op_type],
                &#34;filters&#34;: filters_list
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._GLOBAL_FILTER, request_json
        )

        self.refresh()

        if flag:
            if response.json() and &#39;error&#39; in response.json():
                if &#39;errorCode&#39; in response.json()[&#39;error&#39;]:
                    error_code = int(response.json()[&#39;error&#39;][&#39;errorCode&#39;])

                    if error_code != 0:
                        raise SDKException(
                            &#39;GlobalFilter&#39;, &#39;102&#39;, &#39;Failed to update global filters&#39;
                        )
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def content(self):
        &#34;&#34;&#34;Treats filter content as read-only property&#34;&#34;&#34;
        return self._content

    def add(self, filters_list):
        &#34;&#34;&#34;Adds the filters list to the specified agent global filters list

            Args:
                filters_list    (list)  --  list of filters to be added to this agent

            Raises:
                SDKException:
                    if data type of input is invalid

                    if failed to update global filter content

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(filters_list, list):
            raise SDKException(&#39;GlobalFilter&#39;, &#39;101&#39;)

        self._update(&#34;ADD&#34;, filters_list + self.content)

    def overwrite(self, filters_list):
        &#34;&#34;&#34;Overwrites the existing filters list with given filter list

            Args:
                filters_list    (list)  --  list of filters to be replaced with existing

            Raises:
                SDKException:
                    if data type of input is invalid

                    if failed to update global filter content

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(filters_list, list):
            raise SDKException(&#39;GlobalFilter&#39;, &#39;101&#39;)

        self._update(&#34;OVERWRITE&#34;, filters_list)

    def delete_all(self):
        &#34;&#34;&#34;Deletes all the filters from given agent filters list

            Raises:
                SDKException:
                    if failed to update global filter content

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        self._update(&#34;OVERWRITE&#34;, [&#34;&#34;])

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the GlobalFilter.&#34;&#34;&#34;
        self._initialize_global_filters()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.globalfilter.GlobalFilter"><code class="flex name class">
<span>class <span class="ident">GlobalFilter</span></span>
<span>(</span><span>commcell_object, filter_name, filter_key)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent any one particular agent global filter</p>
<p>Initializes global filter object</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash; commcell object</p>
<p>agent_key
(str)
&ndash;
agent key that shall be used in requests</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/globalfilter.py#L121-L283" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class GlobalFilter(object):
    &#34;&#34;&#34;Class to represent any one particular agent global filter&#34;&#34;&#34;

    def __init__(self, commcell_object, filter_name, filter_key):
        &#34;&#34;&#34;Initializes global filter object

            Args:
                commcell_object     (object)    -- commcell object

                agent_key           (str)       --  agent key that shall be used in requests
        &#34;&#34;&#34;
        self._filter_name = filter_name
        self._filter_key = filter_key
        self._commcell_object = commcell_object
        self._GLOBAL_FILTER = self._commcell_object._services[&#39;GLOBAL_FILTER&#39;]
        self._content = []

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return &#34;Global Filter object for: {0}&#34;.format(self._filter_name)

    def _get_global_filters(self):
        &#34;&#34;&#34;Returns the global filters associated with this commcell&#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._GLOBAL_FILTER
        )

        if flag:
            if response.json():
                return response.json()
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _initialize_global_filters(self):
        &#34;&#34;&#34;Initializes global filters&#34;&#34;&#34;
        global_filters = self._get_global_filters()

        self._content = []

        if self._filter_key in global_filters:
            self._content = global_filters[self._filter_key]

    def _update(self, op_type, filters_list):
        &#34;&#34;&#34;Updates the global filters list on tise commcell

            Args:
                op_type         (dict)  --  operation type to be performed
                        Accepted values: ADD/ OVERWRITE/ DELETE

                filters_list    (list)  --  list of filters to be associated

            Raises:
                SDKException:
                    if failed to update global filter content

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        op_dict = {
            &#34;ADD&#34;: 1,
            &#34;OVERWRITE&#34;: 1,
            &#34;DELETE&#34;: 3
        }

        request_json = {
            self._filter_key: {
                &#34;opType&#34;: op_dict[op_type],
                &#34;filters&#34;: filters_list
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._GLOBAL_FILTER, request_json
        )

        self.refresh()

        if flag:
            if response.json() and &#39;error&#39; in response.json():
                if &#39;errorCode&#39; in response.json()[&#39;error&#39;]:
                    error_code = int(response.json()[&#39;error&#39;][&#39;errorCode&#39;])

                    if error_code != 0:
                        raise SDKException(
                            &#39;GlobalFilter&#39;, &#39;102&#39;, &#39;Failed to update global filters&#39;
                        )
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def content(self):
        &#34;&#34;&#34;Treats filter content as read-only property&#34;&#34;&#34;
        return self._content

    def add(self, filters_list):
        &#34;&#34;&#34;Adds the filters list to the specified agent global filters list

            Args:
                filters_list    (list)  --  list of filters to be added to this agent

            Raises:
                SDKException:
                    if data type of input is invalid

                    if failed to update global filter content

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(filters_list, list):
            raise SDKException(&#39;GlobalFilter&#39;, &#39;101&#39;)

        self._update(&#34;ADD&#34;, filters_list + self.content)

    def overwrite(self, filters_list):
        &#34;&#34;&#34;Overwrites the existing filters list with given filter list

            Args:
                filters_list    (list)  --  list of filters to be replaced with existing

            Raises:
                SDKException:
                    if data type of input is invalid

                    if failed to update global filter content

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(filters_list, list):
            raise SDKException(&#39;GlobalFilter&#39;, &#39;101&#39;)

        self._update(&#34;OVERWRITE&#34;, filters_list)

    def delete_all(self):
        &#34;&#34;&#34;Deletes all the filters from given agent filters list

            Raises:
                SDKException:
                    if failed to update global filter content

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        self._update(&#34;OVERWRITE&#34;, [&#34;&#34;])

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the GlobalFilter.&#34;&#34;&#34;
        self._initialize_global_filters()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.globalfilter.GlobalFilter.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Treats filter content as read-only property</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/globalfilter.py#L221-L224" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Treats filter content as read-only property&#34;&#34;&#34;
    return self._content</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.globalfilter.GlobalFilter.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, filters_list)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the filters list to the specified agent global filters list</p>
<h2 id="args">Args</h2>
<p>filters_list
(list)
&ndash;
list of filters to be added to this agent</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if data type of input is invalid</p>
<pre><code>if failed to update global filter content

if response received is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/globalfilter.py#L226-L245" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, filters_list):
    &#34;&#34;&#34;Adds the filters list to the specified agent global filters list

        Args:
            filters_list    (list)  --  list of filters to be added to this agent

        Raises:
            SDKException:
                if data type of input is invalid

                if failed to update global filter content

                if response received is empty

                if response is not success
    &#34;&#34;&#34;
    if not isinstance(filters_list, list):
        raise SDKException(&#39;GlobalFilter&#39;, &#39;101&#39;)

    self._update(&#34;ADD&#34;, filters_list + self.content)</code></pre>
</details>
</dd>
<dt id="cvpysdk.globalfilter.GlobalFilter.delete_all"><code class="name flex">
<span>def <span class="ident">delete_all</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes all the filters from given agent filters list</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to update global filter content</p>
<pre><code>if response received is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/globalfilter.py#L268-L279" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_all(self):
    &#34;&#34;&#34;Deletes all the filters from given agent filters list

        Raises:
            SDKException:
                if failed to update global filter content

                if response received is empty

                if response is not success
    &#34;&#34;&#34;
    self._update(&#34;OVERWRITE&#34;, [&#34;&#34;])</code></pre>
</details>
</dd>
<dt id="cvpysdk.globalfilter.GlobalFilter.overwrite"><code class="name flex">
<span>def <span class="ident">overwrite</span></span>(<span>self, filters_list)</span>
</code></dt>
<dd>
<div class="desc"><p>Overwrites the existing filters list with given filter list</p>
<h2 id="args">Args</h2>
<p>filters_list
(list)
&ndash;
list of filters to be replaced with existing</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if data type of input is invalid</p>
<pre><code>if failed to update global filter content

if response received is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/globalfilter.py#L247-L266" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def overwrite(self, filters_list):
    &#34;&#34;&#34;Overwrites the existing filters list with given filter list

        Args:
            filters_list    (list)  --  list of filters to be replaced with existing

        Raises:
            SDKException:
                if data type of input is invalid

                if failed to update global filter content

                if response received is empty

                if response is not success
    &#34;&#34;&#34;
    if not isinstance(filters_list, list):
        raise SDKException(&#39;GlobalFilter&#39;, &#39;101&#39;)

    self._update(&#34;OVERWRITE&#34;, filters_list)</code></pre>
</details>
</dd>
<dt id="cvpysdk.globalfilter.GlobalFilter.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the GlobalFilter.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/globalfilter.py#L281-L283" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the GlobalFilter.&#34;&#34;&#34;
    self._initialize_global_filters()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.globalfilter.GlobalFilters"><code class="flex name class">
<span>class <span class="ident">GlobalFilters</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for managing global filters for this commcell</p>
<p>Initializes global filter object</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the GlobalFilter class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/globalfilter.py#L63-L118" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class GlobalFilters(object):
    &#34;&#34;&#34;Class for managing global filters for this commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes global filter object

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the GlobalFilter class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._global_filter_dict = {
            &#34;WINDOWS&#34;: &#39;windowsGlobalFilters&#39;,
            &#34;UNIX&#34;: &#39;unixGlobalFilters&#39;,
            &#34;NAS&#34;:  &#39;nasGlobalFilters&#39;
        }

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the GlobalFilter class.&#34;&#34;&#34;
        o_str = &#34;GlobalFilter class instance for CommServ &#39;{0}&#39;&#34;.format(
            self._commcell_object.commserv_name
        )
        return o_str

    def get(self, filter_name):
        &#34;&#34;&#34;Returns the global filter agent object for specified filter name

            Args:
                filter_name     (str)   -- Global filter name for which the object is to be created
                    Accepted values: WINDOWS/ UNIX/ NAS

            Returns:
                object - GlobalFilter object for specified global filter

            Raises:
                SDKException:
                    if data type of input is invalid

                    if specified global filter doesn&#39;t exist
        &#34;&#34;&#34;
        if not isinstance(filter_name, str):
            raise SDKException(&#39;GlobalFilter&#39;, &#39;101&#39;)

        if filter_name.upper() not in self._global_filter_dict:
            raise SDKException(
                &#39;GlobalFilter&#39;, &#39;102&#39;, &#39;Invalid Global Filter name {0}&#39;.format(filter_name)
            )

        return GlobalFilter(
            self._commcell_object,
            filter_name.upper(),
            self._global_filter_dict[filter_name.upper()]
        )</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.globalfilter.GlobalFilters.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, filter_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the global filter agent object for specified filter name</p>
<h2 id="args">Args</h2>
<p>filter_name
(str)
&ndash; Global filter name for which the object is to be created
Accepted values: WINDOWS/ UNIX/ NAS</p>
<h2 id="returns">Returns</h2>
<p>object - GlobalFilter object for specified global filter</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if data type of input is invalid</p>
<pre><code>if specified global filter doesn't exist
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/globalfilter.py#L90-L118" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, filter_name):
    &#34;&#34;&#34;Returns the global filter agent object for specified filter name

        Args:
            filter_name     (str)   -- Global filter name for which the object is to be created
                Accepted values: WINDOWS/ UNIX/ NAS

        Returns:
            object - GlobalFilter object for specified global filter

        Raises:
            SDKException:
                if data type of input is invalid

                if specified global filter doesn&#39;t exist
    &#34;&#34;&#34;
    if not isinstance(filter_name, str):
        raise SDKException(&#39;GlobalFilter&#39;, &#39;101&#39;)

    if filter_name.upper() not in self._global_filter_dict:
        raise SDKException(
            &#39;GlobalFilter&#39;, &#39;102&#39;, &#39;Invalid Global Filter name {0}&#39;.format(filter_name)
        )

    return GlobalFilter(
        self._commcell_object,
        filter_name.upper(),
        self._global_filter_dict[filter_name.upper()]
    )</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.globalfilter.GlobalFilter" href="#cvpysdk.globalfilter.GlobalFilter">GlobalFilter</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.globalfilter.GlobalFilter.add" href="#cvpysdk.globalfilter.GlobalFilter.add">add</a></code></li>
<li><code><a title="cvpysdk.globalfilter.GlobalFilter.content" href="#cvpysdk.globalfilter.GlobalFilter.content">content</a></code></li>
<li><code><a title="cvpysdk.globalfilter.GlobalFilter.delete_all" href="#cvpysdk.globalfilter.GlobalFilter.delete_all">delete_all</a></code></li>
<li><code><a title="cvpysdk.globalfilter.GlobalFilter.overwrite" href="#cvpysdk.globalfilter.GlobalFilter.overwrite">overwrite</a></code></li>
<li><code><a title="cvpysdk.globalfilter.GlobalFilter.refresh" href="#cvpysdk.globalfilter.GlobalFilter.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.globalfilter.GlobalFilters" href="#cvpysdk.globalfilter.GlobalFilters">GlobalFilters</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.globalfilter.GlobalFilters.get" href="#cvpysdk.globalfilter.GlobalFilters.get">get</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>