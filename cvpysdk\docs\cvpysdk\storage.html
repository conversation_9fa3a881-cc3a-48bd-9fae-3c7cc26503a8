<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.storage API documentation</title>
<meta name="description" content="Main file for performing storage related operations on the commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.storage</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing storage related operations on the commcell.</p>
<p>This file has all the classes related to Storage operations.</p>
<p>MediaAgents:
Class for representing all the media agents attached to the commcell.</p>
<p>MediaAgent:
Class for representing a single media agent attached to the commcell.</p>
<p>DiskLibraries:
Class for representing all the disk libraries attached to the commcell.</p>
<p>DiskLibrary:
Class for representing a single disk library associated with the commcell.</p>
<h2 id="mediaagents">Mediaagents</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialize the MediaAgents class instance for the commcell</p>
<p><strong>str</strong>()
&ndash;
returns all the media agents associated with the commcell</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the MediaAgents class</p>
<p>_get_media_agents()
&ndash;
gets all the media agents of the commcell</p>
<p>all_media_agents()
&ndash;
returns all the media agents on the commcell</p>
<p>has_media_agent()
&ndash;
checks if a media agent exists with the given name or not</p>
<p>get(media_agent_name)
&ndash;
returns the instance of MediaAgent class
of the media agent specified</p>
<p>delete(media_agent)
&ndash;
Deletes the media agent from the commcell.</p>
<p>refresh()
&ndash;
refresh the media agents associated with the commcell</p>
<h2 id="mediaagent">Mediaagent</h2>
<p><strong>init</strong>(commcell_object,
media_agent_name,
media_agent_id)
&ndash;
initialize the instance of MediaAgent class for a
specific media agent of the commcell</p>
<p><strong>repr</strong>()
&ndash;
returns a string representation of the
MediaAgent instance</p>
<p>_get_media_agent_id()
&ndash;
gets the id of the MediaAgent instance from
commcell</p>
<p>_get_media_agent_properties()
&ndash;
returns media agent properties</p>
<p>_initialize_media_agent_properties()
&ndash;
initializes media agent properties</p>
<p>enable_power_management()
&ndash;
Enable VM Management (power management)</p>
<p>_perform_power_operation()
&ndash;
Performs power operation (power-on/power-off)</p>
<p>power_on()
&ndash;
Power-on MediaAgent if VM management is enabled</p>
<p>power_off()
&ndash;
Power-off MediaAgent if VM management is enabled</p>
<p>wait_for_power_status()
&ndash; Waits till the expected power status is not achieved</p>
<p>media_agent_name()
&ndash;
returns media agent name</p>
<p>media_agent_id()
&ndash;
returns media agent id</p>
<p>is_online()
&ndash;
returns True is media agent is online</p>
<p>platform()
&ndash;
returns os info of the media agent</p>
<p>refresh()
&ndash;
refresh the properties of the media agent</p>
<p>change_index_cache()
&ndash;
runs catalog migration</p>
<p>index_cache_path()
&ndash;
returns index cache path of the media agent</p>
<p>index_cache_enabled()
&ndash;
returns index cache enabled status</p>
<p>set_state()
&ndash; enables/disables media agent</p>
<p>mark_for_maintenance() &ndash; marks/unmarks media agent offline for maintenance</p>
<p>set_ransomware_protection()
&ndash; set / unset ransomware protection on Windows MA</p>
<p>set_concurrent_lan()
&ndash;
set / unset concurrent LAN backup in Media agent properties.</p>
<p>is_power_management_enabled() &ndash; returns of power management is enabled or not</p>
<h2 id="libraries">Libraries</h2>
<p><strong>init</strong>()
&ndash;
initialize the instance of Libraries class</p>
<p>_get_libraries
&ndash;
Gets all the libraries associated to the commcell specified by commcell object</p>
<p>has_library
&ndash;
Checks if a library exists in the commcell with the input library name</p>
<p>refresh
&ndash;
Refresh the libraries associated with the Commcell</p>
<h2 id="disklibraries">Disklibraries</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialize the DiskLibraries class instance for the commcell</p>
<p><strong>str</strong>()
&ndash;
returns all the disk libraries associated with the commcell</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the DiskLibraries class</p>
<p>all_disk_libraries()
&ndash;
returns the dict of all the disk libraries on commcell</p>
<p>add()
&ndash;
adds a new disk library to the commcell</p>
<p>delete()
&ndash;
Deletes a disk library from commcell</p>
<p>get(library_name)
&ndash;
returns the instance of the DiskLibrary class
for the library specified</p>
<h2 id="disklibrary">Disklibrary</h2>
<p><strong>init</strong>(commcell_object,
library_name,
library_id)
&ndash;
initialize the instance of DiskLibrary class for a specific
disk library of the commcell</p>
<p><strong>repr</strong>()
&ndash;
returns a string representation of the DiskLibrary instance</p>
<p>_get_library_id()
&ndash;
gets the id of the DiskLibrary instance from commcell</p>
<p>move_mountpath()
&ndash;
To perform move mountpath operation</p>
<p>validate_mountpath()
&ndash;
To perform storage validation on mountpath</p>
<p>add_cloud_mount_path()
&ndash;
Adds a mount path to the cloud library</p>
<p>add_storage_accelerator_credential() &ndash; Add storage accelerator credential to the cloud mount path</p>
<p>add_mount_path()
&ndash;
adds the mount path on the local/ remote machine</p>
<p>set_mountpath_reserve_space()
&ndash;
to set reserve space on the mountpath</p>
<p>set_max_data_to_write_on_mount_path()
&ndash; to set max data to write on the mountpath</p>
<p>change_device_access_type()
&ndash; to change device access type</p>
<p>modify_cloud_access_type()
&ndash; To change device access type for cloud mount path</p>
<p>update_device_controller()
&ndash; To update device controller properties.</p>
<p>verify_media()
&ndash;
To perform verify media operation on media</p>
<p>set_mountpath_preferred_on_mediaagent() &ndash;
Sets select preferred mountPath according to mediaagent setting on the
library</p>
<p>_get_library_properties()
&ndash;
gets the disk library properties</p>
<p>_get_advanced_library_properties() &ndash;
gets the advanced disk library
properties</p>
<p>refresh()
&ndash;
Refresh the properties of this disk library.</p>
<p>DiskLibrary instance Attributes</p>
<pre><code>**media_agents_associated**     --  returns the media agents associated with the disk library
**library_properties**          --  Returns the dictionary consisting of the full properties of the library
**advanced_library_properties** -- Returns the dictionary consisting of advanced library properites
**free_space**                  --  returns the free space on the library
**mountpath_usage**             --  returns mountpath usage on library
</code></pre>
<h2 id="tapelibraries">Tapelibraries</h2>
<p><strong>init</strong>()
&ndash;
initialize the TapeLibrary class instance for the commcell</p>
<p>get()
&ndash;
Returns the TapeLibrary object of the specified library</p>
<p>delete()
&ndash;
Deletes the specified library</p>
<p>lock_mm_configuration()
&ndash;
Locks the MM config for tape library detection</p>
<p>unlock_mm_configuration()
&ndash;
Unlocks the MM config for tape library detection</p>
<p>__lock_unlock_mm_configuration()
&ndash;
Locks or unlocks the MM config for tape library detection</p>
<p>detect_tape_library()
&ndash;
Detect the tape library of the specified MediaAgent(s)</p>
<p>configure_tape_library()
&ndash;
Configure the specified tape library</p>
<h2 id="tapelibrary">Tapelibrary</h2>
<p><strong>init</strong>()
&ndash;
Initialize the TapeLibrary class instance</p>
<p><strong>repr</strong>
&ndash;
returns the string for the instance of the TapeLibrary class</p>
<p>_get_library_id()
&ndash;
Returns the library ID</p>
<p>_get_library_properties()
&ndash;
gets the disk library properties</p>
<p>get_drive_list()
&ndash;
Returns the tape drive list of this tape library</p>
<p>refresh()
&ndash;
Refresh the properties of this tape library.</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1-L2949" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing storage related operations on the commcell.

This file has all the classes related to Storage operations.


MediaAgents:      Class for representing all the media agents attached to the commcell.

MediaAgent:       Class for representing a single media agent attached to the commcell.

DiskLibraries:    Class for representing all the disk libraries attached to the commcell.

DiskLibrary:      Class for representing a single disk library associated with the commcell.


MediaAgents:
    __init__(commcell_object)   --  initialize the MediaAgents class instance for the commcell

    __str__()                   --  returns all the media agents associated with the commcell

    __repr__()                  --  returns the string for the instance of the MediaAgents class

    _get_media_agents()         --  gets all the media agents of the commcell

    all_media_agents()          --  returns all the media agents on the commcell

    has_media_agent()           --  checks if a media agent exists with the given name or not

    get(media_agent_name)       --  returns the instance of MediaAgent class
    of the media agent specified

    delete(media_agent)     --  Deletes the media agent from the commcell.

    refresh()                   --  refresh the media agents associated with the commcell


MediaAgent:
    __init__(commcell_object,
             media_agent_name,
             media_agent_id)                --  initialize the instance of MediaAgent class for a
    specific media agent of the commcell

    __repr__()                              --  returns a string representation of the
    MediaAgent instance

    _get_media_agent_id()                   --  gets the id of the MediaAgent instance from
    commcell

    _get_media_agent_properties()           --  returns media agent properties

    _initialize_media_agent_properties()    --  initializes media agent properties

    enable_power_management()               --  Enable VM Management (power management)

    _perform_power_operation()              --  Performs power operation (power-on/power-off)

    power_on()                              --  Power-on MediaAgent if VM management is enabled

    power_off()                             --  Power-off MediaAgent if VM management is enabled

    wait_for_power_status()                 -- Waits till the expected power status is not achieved

    media_agent_name()                      --  returns media agent name

    media_agent_id()                        --  returns media agent id

    is_online()                             --  returns True is media agent is online

    platform()                              --  returns os info of the media agent

    refresh()                               --  refresh the properties of the media agent

    change_index_cache()                    --  runs catalog migration

    index_cache_path()                      --  returns index cache path of the media agent

    index_cache_enabled()                   --  returns index cache enabled status

    set_state()                    -- enables/disables media agent

    mark_for_maintenance() -- marks/unmarks media agent offline for maintenance

    set_ransomware_protection()  -- set / unset ransomware protection on Windows MA

    set_concurrent_lan()        --  set / unset concurrent LAN backup in Media agent properties.

    is_power_management_enabled() -- returns of power management is enabled or not

Libraries:

    __init__()               --  initialize the instance of Libraries class

    _get_libraries           --  Gets all the libraries associated to the commcell specified by commcell object

    has_library              --  Checks if a library exists in the commcell with the input library name

    refresh                  --  Refresh the libraries associated with the Commcell


DiskLibraries:
    __init__(commcell_object)   --  initialize the DiskLibraries class instance for the commcell

    __str__()                   --  returns all the disk libraries associated with the commcell

    __repr__()                  --  returns the string for the instance of the DiskLibraries class

    all_disk_libraries()        --  returns the dict of all the disk libraries on commcell

    add()                       --  adds a new disk library to the commcell

    delete()                    --  Deletes a disk library from commcell

    get(library_name)           --  returns the instance of the DiskLibrary class
    for the library specified


DiskLibrary:
    __init__(commcell_object,
             library_name,
             library_id)        --  initialize the instance of DiskLibrary class for a specific
    disk library of the commcell

    __repr__()                  --  returns a string representation of the DiskLibrary instance

    _get_library_id()           --  gets the id of the DiskLibrary instance from commcell

    move_mountpath()            --  To perform move mountpath operation

    validate_mountpath()        --  To perform storage validation on mountpath

    add_cloud_mount_path()      --  Adds a mount path to the cloud library

    add_storage_accelerator_credential() -- Add storage accelerator credential to the cloud mount path

    add_mount_path()            --  adds the mount path on the local/ remote machine

    set_mountpath_reserve_space()      --  to set reserve space on the mountpath

    set_max_data_to_write_on_mount_path()  -- to set max data to write on the mountpath

    change_device_access_type()  -- to change device access type

    modify_cloud_access_type()   -- To change device access type for cloud mount path

    update_device_controller()   -- To update device controller properties.

    verify_media()              --  To perform verify media operation on media

    set_mountpath_preferred_on_mediaagent() --  Sets select preferred mountPath according to mediaagent setting on the
                                                library

    _get_library_properties()   --  gets the disk library properties

    _get_advanced_library_properties() --  gets the advanced disk library  properties
    
    refresh()                   --  Refresh the properties of this disk library.

DiskLibrary instance Attributes

    **media_agents_associated**     --  returns the media agents associated with the disk library
    **library_properties**          --  Returns the dictionary consisting of the full properties of the library
    **advanced_library_properties** -- Returns the dictionary consisting of advanced library properites
    **free_space**                  --  returns the free space on the library
    **mountpath_usage**             --  returns mountpath usage on library

TapeLibraries:

    __init__()    --  initialize the TapeLibrary class instance for the commcell

    get()                        --  Returns the TapeLibrary object of the specified library

    delete()                     --  Deletes the specified library

    lock_mm_configuration()      --  Locks the MM config for tape library detection

    unlock_mm_configuration()    --  Unlocks the MM config for tape library detection

    __lock_unlock_mm_configuration()    --  Locks or unlocks the MM config for tape library detection

    detect_tape_library()        --  Detect the tape library of the specified MediaAgent(s)

    configure_tape_library()     --  Configure the specified tape library


TapeLibrary:

     __init__()         --  Initialize the TapeLibrary class instance

     __repr__           --  returns the string for the instance of the TapeLibrary class

     _get_library_id()  --  Returns the library ID

     _get_library_properties()   --  gets the disk library properties

     get_drive_list()   --  Returns the tape drive list of this tape library

     refresh()          --  Refresh the properties of this tape library.


&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals
import uuid, time, json

from base64 import b64encode

from .exception import SDKException



class MediaAgents(object):
    &#34;&#34;&#34;Class for getting all the media agents associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the MediaAgents class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the MediaAgents class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._MEDIA_AGENTS = self._commcell_object._services[&#39;GET_MEDIA_AGENTS&#39;]
        self._media_agents = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all media agents of the commcell.

            Returns:
                str - string of all the media agents associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Media Agent&#39;)

        for index, media_agent in enumerate(self._media_agents):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, media_agent)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the MediaAgents class.&#34;&#34;&#34;
        return &#34;MediaAgents class instance for Commcell&#34;

    def _get_media_agents(self):
        &#34;&#34;&#34;Gets all the media agents associated to the commcell specified by commcell object.

            Returns:
                dict - consists of all media agents of the commcell
                    {
                         &#34;media_agent1_name&#34;: {

                                 &#39;id&#39;: media_agent1_id,

                                 &#39;os_info&#39;: media_agent1_os,

                                 &#39;is_online&#39;: media_agent1_status
                         },
                         &#34;media_agent2_name&#34;: {

                                 &#39;id&#39;: media_agent2_id,

                                 &#39;os_info&#39;: media_agent2_os,

                                 &#39;is_online&#39;: media_agent2_status
                         }
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._MEDIA_AGENTS
        )

        if flag:
            if isinstance(response.json(), dict):
                media_agents = response.json().get(&#39;mediaAgentList&#39;, [])
                media_agents_dict = {}

                for media_agent in media_agents:
                    temp_name = media_agent[&#39;mediaAgent&#39;][&#39;mediaAgentName&#39;].lower()
                    temp_id = str(media_agent[&#39;mediaAgent&#39;][&#39;mediaAgentId&#39;]).lower()
                    temp_os = media_agent[&#39;osInfo&#39;][&#39;OsDisplayInfo&#39;][&#39;OSName&#39;]
                    temp_status = bool(media_agent[&#39;status&#39;])
                    media_agents_dict[temp_name] = {
                        &#39;id&#39;: temp_id,
                        &#39;os_info&#39;: temp_os,
                        &#39;is_online&#39;: temp_status
                    }

                return media_agents_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_media_agents(self):
        &#34;&#34;&#34;Returns dict of all the media agents on this commcell

            dict - consists of all media agents of the commcell
                    {
                         &#34;media_agent1_name&#34;: {

                                 &#39;id&#39;: media_agent1_id,

                                 &#39;os_info&#39;: media_agent1_os,

                                 &#39;is_online&#39;: media_agent1_status
                         },
                         &#34;media_agent2_name&#34;: {

                                 &#39;id&#39;: media_agent2_id,

                                 &#39;os_info&#39;: media_agent2_os,

                                 &#39;is_online&#39;: media_agent2_status
                         }
                    }
        &#34;&#34;&#34;
        return self._media_agents

    def has_media_agent(self, media_agent_name):
        &#34;&#34;&#34;Checks if a media agent exists in the commcell with the input media agent name.

            Args:
                media_agent_name (str)  --  name of the media agent

            Returns:
                bool - boolean output whether the media agent exists in the commcell or not

            Raises:
                SDKException:
                    if type of the media agent name argument is not string
        &#34;&#34;&#34;
        if not isinstance(media_agent_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        return self._media_agents and media_agent_name.lower() in self._media_agents

    def get(self, media_agent_name):
        &#34;&#34;&#34;Returns a MediaAgent object of the specified media agent name.

            Args:
                media_agent_name (str)  --  name of the media agent

            Returns:
                object - instance of the MediaAgent class for the given media agent name

            Raises:
                SDKException:
                    if type of the media agent name argument is not string

                    if no media agent exists with the given name
        &#34;&#34;&#34;
        if not isinstance(media_agent_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            media_agent_name = media_agent_name.lower()

            if self.has_media_agent(media_agent_name):
                return MediaAgent(self._commcell_object,
                                  media_agent_name,
                                  self._media_agents[media_agent_name][&#39;id&#39;])

            raise SDKException(
                &#39;Storage&#39;, &#39;102&#39;, &#39;No media agent exists with name: {0}&#39;.format(media_agent_name)
            )

    def delete(self, media_agent, force=False):
        &#34;&#34;&#34;Deletes the media agent from the commcell.

            Args:
                media_agent (str)  --  name of the Mediaagent to remove from the commcell

                force       (bool)     --  True if you want to delete media agent forcefully.

            Raises:
                SDKException:
                    if type of the media agent name argument is not string

                    if failed to delete Media agent

                    if response is empty

                    if response is not success

                    if no media agent exists with the given name

        &#34;&#34;&#34;
        if not isinstance(media_agent, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            media_agent = media_agent.lower()

            if self.has_media_agent(media_agent):
                mediagent_id = self.all_media_agents[media_agent][&#39;id&#39;]
                mediagent_delete_service = self._commcell_object._services[&#39;MEDIA_AGENT&#39;] % (mediagent_id)
                if force:
                    mediagent_delete_service += &#34;?forceDelete=1&#34;

                flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;DELETE&#39;, mediagent_delete_service)

                error_code = 0
                if flag:
                    if &#39;errorCode&#39; in response.json():
                        o_str = &#39;Failed to delete mediaagent&#39;
                        error_code = response.json()[&#39;errorCode&#39;]
                        if error_code == 0:
                            # initialize the mediaagents again
                            # so the mediaagents object has all the mediaagents
                            self.refresh()
                        else:
                            error_message = response.json()[&#39;errorMessage&#39;]
                            if error_message:
                                o_str += &#39;\nError: &#34;{0}&#34;&#39;.format(error_message)
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, o_str)
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
            else:
                raise SDKException(
                    &#39;Storage&#39;, &#39;102&#39;, &#39;No Mediaagent exists with name: {0}&#39;.format(media_agent)
                )

    def refresh(self):
        &#34;&#34;&#34;Refresh the media agents associated with the Commcell.&#34;&#34;&#34;
        self._media_agents = self._get_media_agents()


class MediaAgent(object):
    &#34;&#34;&#34;Class for a specific media agent.&#34;&#34;&#34;

    def __init__(self, commcell_object, media_agent_name, media_agent_id=None):
        &#34;&#34;&#34;Initialise the MediaAgent object.

            Args:
                commcell_object   (object)  --  instance of the Commcell class

                media_agent_name  (str)     --  name of the media agent

                media_agent_id    (str)     --  id of the media agent
                    default: None

            Returns:
                object - instance of the MediaAgent class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._media_agent_name = media_agent_name.lower()
        self._media_agent_info = None
        if media_agent_id:
            self._media_agent_id = str(media_agent_id)
        else:
            self._media_agent_id = self._get_media_agent_id()

        self._MEDIA_AGENT = self._commcell_object._services[&#39;MEDIA_AGENT&#39;] % (
            self._media_agent_name
        )

        self._CLOUD_MEDIA_AGENT = self._commcell_object._services[&#39;CLOUD_MEDIA_AGENT&#39;] % (
            self._media_agent_id
        )

        self._CREATE_TASK = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        self._MEDIA_AGENTS = self._commcell_object._services[&#39;GET_MEDIA_AGENTS&#39;] + &#34;/{0}&#34;.format(
            self.media_agent_id
        )

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;MediaAgent class instance for MA: &#34;{0}&#34;, of Commcell: &#34;{1}&#34;&#39;

        return representation_string.format(
            self.media_agent_name, self._commcell_object.commserv_name
        )

    def _get_media_agent_id(self):
        &#34;&#34;&#34;Gets the media agent id associated with this media agent.

            Returns:
                str - id associated with this media agent
        &#34;&#34;&#34;
        media_agents = MediaAgents(self._commcell_object)
        return media_agents.get(self.media_agent_name).media_agent_id

    def _get_media_agent_properties(self):
        &#34;&#34;&#34;Returns the media agent properties of this media agent.

            Returns:
                dict - dictionary consisting of the properties of this client

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._MEDIA_AGENTS
        )

        if flag:
            if response.json() and &#39;mediaAgentList&#39; in response.json():
                self._media_agent_info = response.json()[&#39;mediaAgentList&#39;][0]
                return response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _initialize_media_agent_properties(self):
        &#34;&#34;&#34;Initializes the properties for this Media Agent&#34;&#34;&#34;
        self._status = None
        self._platform = None
        self._index_cache_enabled = None
        self._index_cache = None
        self._is_power_mgmt_allowed = None
        self._is_power_mgmt_supported = None
        self._is_power_management_enabled = None
        self._power_management_controller_name = None
        self._power_status = None

        properties = self._get_media_agent_properties()

        if properties[&#39;mediaAgentList&#39;]:
            mediaagent_list = properties[&#39;mediaAgentList&#39;][0]
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        status = mediaagent_list.get(&#39;status&#39;)
        if status == 1:
            self._is_online = True
        else:
            self._is_online = False

        if mediaagent_list[&#39;osInfo&#39;][&#39;OsDisplayInfo&#39;][&#39;OSName&#39;]:
            platform = mediaagent_list[&#39;osInfo&#39;][&#39;OsDisplayInfo&#39;][&#39;OSName&#39;]
            if &#39;windows&#39; in platform.lower():
                self._platform = &#39;WINDOWS&#39;
            elif &#39;unix&#39; in platform.lower() or &#39;linux&#39; in platform.lower():
                self._platform = &#39;UNIX&#39;
            else:
                self._platform = platform

        if mediaagent_list[&#39;mediaAgentProps&#39;][&#39;mediaAgentIdxCacheProps&#39;][&#39;cacheEnabled&#39;]:
            self._index_cache_enabled = mediaagent_list[&#39;mediaAgentProps&#39;][
                &#39;mediaAgentIdxCacheProps&#39;][&#39;cacheEnabled&#39;]

        if mediaagent_list[&#39;mediaAgentProps&#39;][&#39;indexLogsCacheInfo&#39;][&#39;logsCachePath&#39;][&#39;path&#39;]:
            self._index_cache = mediaagent_list[&#39;mediaAgentProps&#39;][&#39;indexLogsCacheInfo&#39;
                                                                   ][&#39;logsCachePath&#39;][&#39;path&#39;]

        if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerMgmtSupported&#39;]:
            self._is_power_mgmt_supported = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerMgmtSupported&#39;]

        if self._is_power_mgmt_supported:

            if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerManagementEnabled&#39;]:
                self._is_power_management_enabled = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerManagementEnabled&#39;]

            if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerMgmtAllowed&#39;]:
                self._is_power_mgmt_allowed = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerMgmtAllowed&#39;]

            if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;powerStatus&#39;]:
                self._power_status = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;powerStatus&#39;]

            if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;selectedCloudController&#39;][&#39;clientName&#39;]:
                self._power_management_controller_name = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;selectedCloudController&#39;][&#39;clientName&#39;]

    def enable_power_management(self, pseudo_client_name):
        &#34;&#34;&#34;
            Enables power management using the provided cloud controller (pseudo client)

                Args :
                        pseudo_client_name : VSA pseudo client to be used as cloud controller
                Raises:
                        SDKException:
                                    If response is not success

                                    If Power management is not supported
        &#34;&#34;&#34;
        if self._is_power_mgmt_allowed:
            client_obj = self._commcell_object._clients.get(pseudo_client_name)
            pseudo_client_name_client_id = client_obj._get_client_id()

            &#34;&#34;&#34;
            payLoad = &#39;&lt;EVGui_SetCloudVMManagementInfoReq hostId=&#34;&#39; + self.media_agent_id + &#39;&#34; useMediaAgent=&#34;1&#34;&gt; &lt;powerManagementInfo isPowerManagementEnabled=&#34;1&#34; &gt; &lt;selectedCloudController clientId=&#34;&#39; + PseudoClientName_client_id + &#39;&#34; clientName=&#34;&#39; + \
                      PseudoClientName + &#39;&#34;/&gt;&lt;/powerManagementInfo&gt;&lt;/EVGui_SetCloudVMManagementInfoReq&gt;&#39;
            &#34;&#34;&#34;
            payLoad = &#39;&lt;EVGui_SetCloudVMManagementInfoReq hostId=&#34;{0}&#34; useMediaAgent=&#34;1&#34;&gt; &lt;powerManagementInfo isPowerManagementEnabled=&#34;1&#34; &gt; &lt;selectedCloudController clientId=&#34;{1}&#34; clientName=&#34;{2}&#34;/&gt;&lt;/powerManagementInfo&gt;&lt;/EVGui_SetCloudVMManagementInfoReq&gt;&#39;.format(
                self.media_agent_id, pseudo_client_name_client_id, pseudo_client_name)

            response = self._commcell_object._qoperation_execute(payLoad)

            if response[&#39;errorCode&#39;] != 0:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, str(response))
        else:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Power management is not supported&#34;)

    def _perform_power_operation(self, operation):
        &#34;&#34;&#34;
            Performs power operation

                Args :
                        self : Object
                        operation : Operation to perform

                Raises:
                        SDKException:
                                        If operation is not 1 or 0

                                        If ower management is NOT enabled or NOT supported on MediaAgent

                                        If API response is empty

                                        If API response is not success
        &#34;&#34;&#34;
        if not operation in (&#34;1&#34;, &#34;0&#34;):
            raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                               &#34;Invalid power operation type&#34;)

        if self._is_power_management_enabled:
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;GET&#39;, self._CLOUD_MEDIA_AGENT + &#34;/&#34; + operation
            )
            if not flag:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                                   str(response))
            if response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, str(response))
        else:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                               &#39;Power management is NOT enabled or NOT supported&#39;)

    def power_on(self, wait_till_online=True):
        &#34;&#34;&#34;
            Power-on the MediaAgent

                Args :
                        self : Object
                        wait_till_online :
                                            True : Waits until the MediaAgent is online
                                            False : Just submits the power-on request
        &#34;&#34;&#34;

        if self.current_power_status not in [&#34;Starting&#34;, &#34;Started&#34;, &#34;Online&#34;]:
            self._perform_power_operation(&#34;1&#34;)

        if wait_till_online == True and self.current_power_status != &#34;Online&#34;:
            self.wait_for_power_status(&#34;Online&#34;)

    def power_off(self, wait_till_stopped=True):
        &#34;&#34;&#34;
            Power-off MediaAgent

                Args :
                        self : Object
                        wait_till_stopped :
                                            True : Waits until the MediaAgent is stopped
                                            False : Just submits the power-off request
        &#34;&#34;&#34;

        if self.current_power_status not in [&#34;Stopping&#34;, &#34;Stopped&#34;]:
            self._perform_power_operation(&#34;0&#34;)

        if wait_till_stopped == True and self.current_power_status != &#34;Stopped&#34;:
            self.wait_for_power_status(&#34;Stopped&#34;)

    def wait_for_power_status(self, expected_power_status, time_out_sec=600):
        &#34;&#34;&#34;
            Waits until the expected power status not achieved

                Args :
                                        self : Object
                                        expected_power_status : The expected power status as following.
                                                                    Starting
                                                                    Started
                                                                    Online
                                                                    Stopping
                                                                    Stopped
                                        time_out_sec : Maximum time to wait for the expected power status

                                        Raises:
                                                SDKException:
                                                                If time_out_sec is not an integer and time_out_sec not None

                                                                If expected power status is not achieved within time_out_sec time
        &#34;&#34;&#34;
        if time_out_sec != None:
            if not isinstance(time_out_sec, int):
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                                   &#39;Expected an integer value for [time_out_sec]&#39;)

        start_time = time.time()
        while self.current_power_status != expected_power_status:
            time.sleep(10)
            if time_out_sec != None:
                if time.time() - start_time &gt; time_out_sec:
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                                       &#39;The expected power status is not achieved within expected time&#39;)

    def change_index_cache(self, old_index_cache_path, new_index_cache_path):
        &#34;&#34;&#34;
        Begins a catalog migration job via the CreateTask end point.

            Args :
                old_index_cache_path - source index cache path

                new_index_cache_path - destination index cache path

            Returns :
                Returns job object of catalog migration job

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        conf_guid = str(uuid.uuid4())

        xml_options_string = &#39;&#39;&#39;&lt;Indexing_IdxDirectoryConfiguration configurationGuid=&#34;{0}&#34;
        icdPath=&#34;{1}&#34; maClientFocusName=&#34;{2}&#34; maGuid=&#34;&#34; oldIcdPath=&#34;{3}&#34;
        opType=&#34;0&#34; /&gt;&#39;&#39;&#39; .format(
            conf_guid, new_index_cache_path, self.media_agent_name, old_index_cache_path)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;taskOperation&#34;: 1,
                &#34;task&#34;: {
                    &#34;isEZOperation&#34;: False,
                    &#34;description&#34;: &#34;&#34;,
                    &#34;ownerId&#34;: 1,
                    &#34;runUserId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;&#34;,
                    &#34;alertName&#34;: &#34;&#34;,
                    &#34;sequenceNumber&#34;: 0,
                    &#34;isEditing&#34;: False,
                    &#34;GUID&#34;: &#34;&#34;,
                    &#34;isFromCommNetBrowserRootNode&#34;: False,
                    &#34;initiatedFrom&#34;: 3,
                    &#34;policyType&#34;: 0,
                    &#34;associatedObjects&#34;: 0,
                    &#34;taskName&#34;: &#34;&#34;,
                    &#34;taskFlags&#34;: {
                        &#34;notRunnable&#34;: False,
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskOrder&#34;: 0,
                            &#34;subTaskType&#34;: 1,
                            &#34;flags&#34;: 0,
                            &#34;operationType&#34;: 5018,
                            &#34;subTaskId&#34;: 1
                        },
                        &#34;options&#34;: {
                            &#34;originalJobId&#34;: 0,
                            &#34;adminOpts&#34;: {
                                &#34;catalogMigrationOptions&#34;: {
                                    &#34;xmlOptions&#34;: xml_options_string,
                                    &#34;mediaAgent&#34;: {
                                        &#34;mediaAgentId&#34;: int(self._media_agent_id),
                                        &#34;_type_&#34;: 11
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_TASK, request_json
        )

        if flag:
            if response.json() and &#39;jobIds&#39; in response.json() and response.json()[&#39;jobIds&#39;][0]:

                response_json = response.json()
                catalogmigration_jobid = response_json[&#34;jobIds&#34;][0]
                catalogmigration_job_obj = self._commcell_object.job_controller.get(
                    catalogmigration_jobid)
                return catalogmigration_job_obj

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def set_state(self, enable=True):
        &#34;&#34;&#34;
        disable the media agent by change in media agent properties.
            Args:
            enable      -   (bool)
                            True        - Enable the media agent
                            False       - Disable the media agent

            Raises:
            &#34;exception&#34;                  -   if there is an empty response
                                         -   if there is an error in request execution
                                         -   if response status is failure

        &#34;&#34;&#34;

        if type(enable) != bool:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_id = int(self.media_agent_id)
        request_json = {
            &#34;mediaAgentInfo&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: media_id
                },
                &#34;mediaAgentProps&#34;: {
                    &#34;enableMA&#34;: enable
                }
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
        )

        # check for response
        # possible key errors if key not present in response, defaults set
        if flag:
            if response and response.json():
                response = response.json()
                if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                    error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)


    def mark_for_maintenance(self, mark=False):
        &#34;&#34;&#34;
        mark the media agent offline for maintenance
            Args:
                mark  - (bool)
                                        True    - mark the media agent for maintenance
                                        False   - UNMARK the media agent for maintenance

            Raises:
            &#34;exception&#34;                  -   if there is an empty response
                                         -   if there is an error in request execution
                                         -   if response status is failure

        &#34;&#34;&#34;

        if type(mark) != bool:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_id = int(self.media_agent_id)
        request_json = {
            &#34;mediaAgentInfo&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: media_id
                },
                &#34;mediaAgentProps&#34;: {
                    &#34;markMAOfflineForMaintenance&#34;: mark
                }
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
        )

        if flag:
            if response and response.json():
                response = response.json()
                if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                    error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def set_ransomware_protection(self, status):
        &#34;&#34;&#34;Enables / Disables the ransomware protection on Windows MediaAgent.

        Args:
            status    (bool)        --  True or False value to turn it on/off
                                        True - ransomware protection on MediaAgent - ON
                                        False - ransomware protection on MediaAgent - OFF

        Returns:
            None                   --   if operation performed successfully.

        Raises:
            Exception(Exception_Code, Exception_Message):
                - if there is failure in executing the operation
        &#34;&#34;&#34;
        # this works only on WINDOWS MA
        if self._platform != &#39;WINDOWS&#39;:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if type(status) != bool:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_id = int(self.media_agent_id)

        request_json = {
            &#34;mediaAgentInfo&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: media_id
                },
                &#34;mediaAgentProps&#34;: {
                    &#34;isRansomwareProtected&#34;: status
                }
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
        )

        if flag:
            if response and response.json():
                response = response.json()
                if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                    error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def set_concurrent_lan(self, enable=True):
        &#34;&#34;&#34;
        disable / enable concurrent LAN backup in Media agent properties.
            Args:
            enable      -   (bool)
                            True        - Enable concurent LAN Backup
                            False       - Disable concurent LAN Backup

        Returns:
            None                   --   if operation performed successfully.

        Raises:
            SDKException:
                - if there is failure in executing the operation

        &#34;&#34;&#34;

        if type(enable) != bool:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_id = int(self.media_agent_id)
        request_json = {
            &#34;mediaAgentInfo&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: media_id
                },
                &#34;mediaAgentProps&#34;: {
                    &#34;optimizeForConcurrentLANBackups&#34;: enable
                }
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
        )

        if flag:
            if response and response.json():
                response = response.json()
                if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                    error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the media agent display name&#34;&#34;&#34;
        return self._media_agent_info[&#39;mediaAgent&#39;][&#39;displayName&#39;]

    @property
    def media_agent_name(self):
        &#34;&#34;&#34;Treats the media agent name as a read-only attribute.&#34;&#34;&#34;
        return self._media_agent_name

    @property
    def media_agent_id(self):
        &#34;&#34;&#34;Treats the media agent id as a read-only attribute.&#34;&#34;&#34;
        return self._media_agent_id

    @property
    def is_online(self):
        &#34;&#34;&#34;Treats the status as read-only attribute&#34;&#34;&#34;
        return self._is_online

    @property
    def platform(self):
        &#34;&#34;&#34;Treats the platform as read-only attribute&#34;&#34;&#34;
        return self._platform

    @property
    def index_cache_path(self):
        &#34;&#34;&#34;Treats the index cache path as a read-only attribute&#34;&#34;&#34;
        return self._index_cache

    @property
    def index_cache_enabled(self):
        &#34;&#34;&#34;Treats the cache enabled value as a read-only attribute&#34;&#34;&#34;
        return self._index_cache_enabled

    @property
    def is_power_management_enabled(self):
        &#34;&#34;&#34; Returns power management enable status&#34;&#34;&#34;
        return self._is_power_management_enabled

    @property
    def current_power_status(self):
        &#34;&#34;&#34;
                Returns the power state of the MA.

                    Args :
                            self : Object
                    Returns :
                            str - Current power status of the MediaAgent as following
                                    Starting : Power-on process in going on
                                    Started : MA is powered-on successfully but still not synced with CS
                                    Online : Powered-on and synced with CS. MA is ready to use.
                                    Stopping : Power-off operation is going on.
                                    Stopped : MA is powered-off
                                    Unknown : MA power status is still not synced with cloud provider. MA discovery is going on or power state sync with happening with cloud provider or something is NOT right.
        &#34;&#34;&#34;
        self.refresh()
        power_status = {0: &#39;Unknown&#39;, 1: &#39;Starting&#39;, 2: &#39;Started&#39;, 3: &#39;Online&#39;, 4: &#39;Stopping&#39;, 5: &#39;Stopped&#39;}
        return power_status.get(self._power_status)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the MediaAgent.&#34;&#34;&#34;
        self._initialize_media_agent_properties()


class Libraries(object):
    &#34;&#34;&#34; Class for libraries&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the DiskLibraries class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the DiskLibraries class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._LIBRARY = self._commcell_object._services[&#39;LIBRARY&#39;]

        self._libraries = None
        self.refresh()

    def _get_libraries(self):
        &#34;&#34;&#34;Gets all the disk libraries associated to the commcell specified by commcell object.

            Returns:
                dict - consists of all disk libraries of the commcell
                    {
                         &#34;disk_library1_name&#34;: disk_library1_id,
                         &#34;disk_library2_name&#34;: disk_library2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._LIBRARY)

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                libraries = response.json()[&#39;response&#39;]
                libraries_dict = {}

                for library in libraries:
                    temp_name = library[&#39;entityInfo&#39;][&#39;name&#39;].lower()
                    temp_id = str(library[&#39;entityInfo&#39;][&#39;id&#39;]).lower()
                    libraries_dict[temp_name] = temp_id

                return libraries_dict
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def has_library(self, library_name):
        &#34;&#34;&#34;Checks if a library exists in the commcell with the input library name.

            Args:
                library_name (str)  --  name of the library

            Returns:
                bool - boolean output whether the library exists in the commcell or not

            Raises:
                SDKException:
                    if type of the library name argument is not string
        &#34;&#34;&#34;
        if not isinstance(library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        return self._libraries and library_name.lower() in self._libraries

    def refresh(self):
        &#34;&#34;&#34;Refresh the disk libraries associated with the Commcell.&#34;&#34;&#34;
        self._libraries = self._get_libraries()


class DiskLibraries(Libraries):
    &#34;&#34;&#34;Class for getting all the disk libraries associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        super().__init__(commcell_object)

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all disk libraries of the commcell.

            Returns:
                str - string of all the disk libraries associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Disk Library&#39;)

        for index, library in enumerate(self._libraries):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, library)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the DiskLibraries class.&#34;&#34;&#34;
        return &#34;DiskLibraries class instance for Commcell&#34;

    @property
    def all_disk_libraries(self):
        &#34;&#34;&#34;Returns dict of all the disk libraries on this commcell

            dict - consists of all disk libraries of the commcell
                    {
                         &#34;disk_library1_name&#34;: disk_library1_id,
                         &#34;disk_library2_name&#34;: disk_library2_id
                    }

        &#34;&#34;&#34;
        return self._libraries

    def add(self, library_name, media_agent, mount_path, username=&#34;&#34;, password=&#34;&#34;, servertype=0,
            saved_credential_name=&#34;&#34;, **kwargs):
        &#34;&#34;&#34;Adds a new Disk Library to the Commcell.

            Args:
                library_name (str)        --  name of the new library to add

                media_agent  (str/object) --  name or instance of media agent to add the library to

                mount_path   (str)        --  full path of the folder to mount the library at

                username     (str)        --  username to access the mount path
                    default: &#34;&#34;

                password     (str)        --  password to access the mount path
                    default: &#34;&#34;

                servertype   (int)        -- provide cloud library server type
                    default 0, value 59 for HPstore

                saved_credential_name   (str)   --  name of the saved credential
                    default: &#34;&#34;

                kwargs      (dict)  --  optional arguments

                Available kwargs Options:

                    proxy_password (str) -- plain text password of proxy server
                        default: &#34;&#34;

            Returns:
                object - instance of the DiskLibrary class, if created successfully

            Raises:
                SDKException:
                    if type of the library name argument is not string

                    if type of the mount path argument is not string

                    if type of the username argument is not string

                    if type of the password argument is not string

                    if type of the media agent argument is not either string or MediaAgent instance

                    if failed to create disk library

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not (isinstance(library_name, str) and
                isinstance(mount_path, str) and
                isinstance(username, str) and
                isinstance(password, str)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if isinstance(media_agent, MediaAgent):
            media_agent = media_agent
        elif isinstance(media_agent, str):
            media_agent = MediaAgent(self._commcell_object, media_agent)
        else:
            raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        proxy_password = kwargs.get(&#39;proxy_password&#39;, &#39;&#39;)

        request_json = {
            &#34;isConfigRequired&#34;: 1,
            &#34;library&#34;: {
                &#34;mediaAgentId&#34;: int(media_agent.media_agent_id),
                &#34;libraryName&#34;: library_name,
                &#34;mountPath&#34;: mount_path,
                &#34;loginName&#34;: username,
                &#34;password&#34;: b64encode(password.encode()).decode(),
                &#34;opType&#34;: 1,
                &#34;savedCredential&#34;:{
                    &#34;credentialName&#34;: saved_credential_name
                }
            }
        }

        if servertype &gt; 0:
            request_json[&#34;library&#34;][&#34;serverType&#34;] = servertype
            request_json[&#34;library&#34;][&#34;isCloud&#34;] = 1

            if saved_credential_name:
                request_json[&#34;library&#34;][&#34;password&#34;] = b64encode(&#34;XXXXX&#34;.encode()).decode()

            if proxy_password != &#34;&#34;:
                request_json[&#34;library&#34;][&#34;proxyPassword&#34;] = b64encode(proxy_password.encode()).decode()

            if servertype == 59:
                request_json[&#34;library&#34;][&#34;HybridCloudOption&#34;] = {
                    &#34;enableHybridCloud&#34;: &#34;2&#34;, &#34;diskLibrary&#34;: {&#34;_type_&#34;: &#34;9&#34;}}
                request_json[&#34;library&#34;][&#34;savedCredential&#34;] = {&#34;_type_&#34;: &#34;9&#34;}

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._LIBRARY, request_json
        )

        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    library = response.json()[&#39;library&#39;]

                    # initialize the libraries again
                    # so the libraries object has all the libraries
                    self.refresh()

                    return DiskLibrary(
                        self._commcell_object,
                        library[&#39;libraryName&#39;],
                        library_details=library)
                elif &#39;errorCode&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create disk library\nError: &#34;{0}&#34;&#39;.format(error_message)

                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, library_name):
        &#34;&#34;&#34;deletes the specified library.

            Args:
                library_name (str)  --  name of the disk library to delete

            Raises:
                SDKException:
                    if type of the library name argument is not string
                    if no library exists with the given name
                    if response is incorrect
        &#34;&#34;&#34;
        if not isinstance(library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if not self.has_library(library_name):
            raise SDKException(&#39;Storage&#39;,
                               &#39;102&#39;,
                               &#39;No library exists with name: {0}&#39;.
                               format(library_name))

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isDeconfigLibrary&#34;: 1,
                    &#34;library&#34;:
                        {
                            &#34;opType&#34;: 2,
                            &#34;libraryName&#34;: library_name
                        }
                }
        }
        exec_command = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, exec_command, request_json)

        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    _response = response.json()[&#39;library&#39;]

                    if &#39;errorCode&#39; in _response:
                        if _response[&#39;errorCode&#39;] == 0:
                            self.refresh()
                        else:
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
                else:
                    if &#39;errorMessage&#39; in response.json():
                        o_str = &#39;Error: &#39; + response.json()[&#39;errorMessage&#39;]
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)

                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to delete library {0} with error: \n [{1}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(library_name, _stderr))

    def get(self, library_name, library_details=None):
        &#34;&#34;&#34;Returns a DiskLibrary object of the specified disk library name.

            Args:
                library_name (str)  --  name of the disk library

                library_details (dict) -- dict containing mountpath and mediaagent details

            Returns:
                object - instance of the DiskLibrary class for the given library name

            Raises:
                SDKException:
                    if type of the library name argument is not string

                    if no disk library exists with the given name
        &#34;&#34;&#34;
        if not isinstance(library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            library_name = library_name.lower()

            if self.has_library(library_name):
                return DiskLibrary(self._commcell_object,
                                   library_name,
                                   self._libraries[library_name], library_details)

            raise SDKException(
                &#39;Storage&#39;, &#39;102&#39;, &#39;No disk library exists with name: {0}&#39;.format(library_name)
            )



class DiskLibrary(object):
    &#34;&#34;&#34;Class for a specific disk library.&#34;&#34;&#34;

    def __init__(self, commcell_object, library_name, library_id=None, library_details=None):
        &#34;&#34;&#34;Initialise the DiskLibrary object.

            Args:
                commcell_object  (object)  --  instance of the Commcell class

                library_name     (str)     --  name of the disk library

                library_id       (str)     --  id of the disk library
                    default: None

                library_details (dict) -- dict containing mountpath and mediaagent details
                    default: None

            Returns:
                object - instance of the DiskLibrary class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._library_name = library_name.lower()

        if library_id:
            self._library_id = str(library_id)
        else:
            self._library_id = self._get_library_id()
        self._library_properties_service = self._commcell_object._services[
            &#39;GET_LIBRARY_PROPERTIES&#39;] % (self._library_id)
        self._library_properties = self._get_library_properties()
        self._advanced_library_properties = self._get_advanced_library_properties()
        if library_details is not None:
            self.mountpath = library_details.get(&#39;mountPath&#39;, None)
            self.mediaagent = library_details.get(&#39;mediaAgentName&#39;, None)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;DiskLibrary class instance for library: &#34;{0}&#34; of Commcell: &#34;{1}&#34;&#39;
        return representation_string.format(
            self.library_name, self._commcell_object.commserv_name
        )

    def move_mountpath(self, mountpath_id, source_device_path,
                       source_mediaagent_id, target_device_path, target_mediaagent_id,
                       target_device_id=0):

        &#34;&#34;&#34; To perform move mountpath operation
        Args:
            mountpath_id  (int)   --  Mountpath Id that need to be moved.

            source_device_path (str)   -- Present Mountpath location

            source_mediaagent_id    (int)   -- MediaAgent Id on which present mountpath exists

            target_device_path    (str)   -- New Mountpath location

            target_mediaagent_id    (int)   -- MediaAgent Id on which new mountpath exists

            target_device_id        (int)   --  Device Id of target path if already exists

        Returns:
            instance of the Job class for this move mountpath job

        Raises
            Exception:
                - if argument datatype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
        &#34;&#34;&#34;

        if not (isinstance(mountpath_id, int) and
                isinstance(source_mediaagent_id, int) and
                isinstance(target_mediaagent_id, int) and
                (isinstance(target_device_path, str) or target_device_id &gt; 0) and
                isinstance(source_device_path, str)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        MOVE_MOUNTPATH_DETAILS = self._commcell_object._services[&#39;GET_MOVE_MOUNTPATH_DETAILS&#39;] % (mountpath_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, MOVE_MOUNTPATH_DETAILS)

        source_device_id = None

        if flag:
            if response.json():
                if &#39;sourceDeviceInfo&#39; in response.json():
                    source_device_id = response.json().get(&#39;sourceDeviceInfo&#39;).get(&#39;deviceId&#39;, None)
                if not source_device_id:
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;Failed to get details of the mountpath for move&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        request_json = {
            &#39;MPMoveOption&#39;: {
                &#39;mountPathMoveList&#39;: [{
                    &#39;sourceDeviceId&#39;: source_device_id,
                    &#39;sourcemediaAgentId&#39;: source_mediaagent_id,
                    &#39;targetMediaAgentId&#39;: target_mediaagent_id,
                }]
            }
        }
        if target_device_id &gt; 0:
            request_json[&#39;MPMoveOption&#39;][&#39;mountPathMoveList&#39;][0][&#39;targetDeviceId&#39;] = target_device_id
        else:
            request_json[&#39;MPMoveOption&#39;][&#39;mountPathMoveList&#39;][0][&#39;targetDevicePath&#39;] = target_device_path

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;MOVE_MOUNTPATH&#39;], request_json)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    if len(response.json()[&#39;jobIds&#39;]) == 1:
                        from cvpysdk.job import Job
                        return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                    else:
                        from cvpysdk.job import Job
                        mp_move_job_list = []
                        for job_id in response.json()[&#39;jobIds&#39;]:
                            mp_move_job_list.append(Job(self._commcell_object, job_id))
                        return mp_move_job_list

                if &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Commcell&#39;, &#39;105&#39;, o_str)

                else:
                    raise SDKException(&#39;Commcell&#39;, &#39;105&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def validate_mountpath(self, mountpath_drive_id, media_agent):

        &#34;&#34;&#34; To perform storage validation on mountpath
        Args:
            mountpath_drive_id  (int)   --  Drive Id of mountpath that need to be validate.

            media_agent (str)   -- MediaAgent on which Mountpath exists

        Returns:
            instance of the Job class for this storage validation job

        Raises
            Exception:
                - if argument datatype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
        &#34;&#34;&#34;

        if not (isinstance(mountpath_drive_id, int) and
                isinstance(media_agent, str)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)


        request_xml = &#34;&#34;&#34;&lt;TMMsg_CreateTaskReq&gt;
                        &lt;taskInfo taskOperation=&#34;1&#34;&gt;
                            &lt;task associatedObjects=&#34;0&#34; description=&#34;Storage Validation - Automation&#34; initiatedFrom=&#34;1&#34; 
                            isEZOperation=&#34;0&#34; isEditing=&#34;0&#34; isFromCommNetBrowserRootNode=&#34;0&#34; ownerId=&#34;1&#34; ownerName=&#34;&#34; 
                            policyType=&#34;0&#34; runUserId=&#34;1&#34; sequenceNumber=&#34;0&#34; taskType=&#34;1&#34;&gt;
                            &lt;taskFlags notRunnable=&#34;0&#34; /&gt;
                            &lt;/task&gt;
                            &lt;subTasks subTaskOperation=&#34;1&#34;&gt;
                                &lt;subTask flags=&#34;0&#34; operationType=&#34;4013&#34; subTaskId=&#34;1&#34; subTaskOrder=&#34;0&#34; subTaskType=&#34;1&#34;/&gt;
                                &lt;options originalJobId=&#34;0&#34;&gt;
                                    &lt;adminOpts&gt;
                                        &lt;libraryOption  operation=&#34;13&#34; validationFlags=&#34;0&#34; validattionReservedFlags=&#34;0&#34;&gt;
                                            &lt;library libraryId=&#34;{0}&#34; /&gt;
                                            &lt;mediaAgent mediaAgentName=&#34;{1}&#34; /&gt;
                                            &lt;driveIds driveId=&#34;{2}&#34; /&gt;
                                            &lt;validateDrive chunkSize=&#34;16384&#34; chunksTillEnd=&#34;0&#34; fileMarkerToStart=&#34;2&#34;
                                             numberOfChunks=&#34;2&#34; threadCount=&#34;2&#34; volumeBlockSize=&#34;64&#34; /&gt;
                                        &lt;/libraryOption&gt; &lt;/adminOpts&gt; &lt;/options&gt; &lt;/subTasks&gt;  &lt;/taskInfo&gt;
                            &lt;/TMMsg_CreateTaskReq&gt;&#34;&#34;&#34;.format(self.library_id, media_agent, mountpath_drive_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CREATE_TASK&#39;], request_xml
        )

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    from cvpysdk.job import Job
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                if &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Commcell&#39;, &#39;105&#39;, o_str)

                else:
                    raise SDKException(&#39;Commcell&#39;, &#39;105&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def add_cloud_mount_path(self, mount_path, media_agent, username, password, server_type, saved_credential_name=&#34;&#34;):
        &#34;&#34;&#34; Adds a mount path to the cloud library

        Args:
            mount_path  (str)   -- cloud container or bucket.

            media_agent (str)   -- MediaAgent on which mountpath exists

            username    (str)   -- Username to access the mount path in the format &lt;Service Host&gt;//&lt;Account Name&gt;
            Eg: s3.us-west-1.amazonaws.com//MyAccessKeyID. For more information refer http://documentation.commvault.com/commvault/v11/article?p=97863.htm.

            password    (str)   -- Password to access the mount path

            server_type  (int)   -- provide cloud library server type
                                    Eg: 3-Microsoft Azure Storage . For more information refer http://documentation.commvault.com/commvault/v11/article?p=97863.htm.

            saved_credential_name   (str)   --  name of the saved credential
                default: &#34;&#34;

        Returns:
            None

        Raises
            Exception:
                - if mountpath or mediaagent or username or password or servertype arguments dataype is invalid

                - if servertype input data is incorrect

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
            &#34;&#34;&#34;

        if not (isinstance(mount_path, str) or isinstance(media_agent, str)
                or isinstance(username, str) or isinstance(password, str)
                or isinstance(server_type, int)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
            &#34;isConfigRequired&#34;: 1,
            &#34;library&#34;: {
                &#34;opType&#34;: 4,
                &#34;isCloud&#34;: 1,
                &#34;mediaAgentName&#34;: media_agent,
                &#34;libraryName&#34;: self._library_name,
                &#34;mountPath&#34;: mount_path,
                &#34;loginName&#34;: username,
                &#34;password&#34;: b64encode(password.encode()).decode(),
                &#34;serverType&#34;: server_type,
                &#34;savedCredential&#34;: {
                    &#34;credentialName&#34;: saved_credential_name
                }
            }
        }

        exec_command = self._commcell_object._services[&#39;LIBRARY&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, exec_command, request_json
        )

        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    _response = response.json()[&#39;library&#39;]

                    if &#39;errorCode&#39; in _response:
                        if _response[&#39;errorCode&#39;] != 0:
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to add mount path [{0}] for library [{1}] with error: \n [{2}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(mount_path,
                                                                 self._library_name,
                                                                 _stderr))

    def add_storage_accelerator_credential(self, mount_path, saved_credential=&#34;&#34;, reset=False):
        &#34;&#34;&#34; Add storage accelerator credential to the cloud mount path

        Args:
            mount_path  (str)   -- Mount path to which secondary credentials needs to be added

            saved_credential (str)   -- saved credential name
                default: &#34;&#34;

            reset    (bool)   -- reset storage accelerator credential
                default: False

        Raises
            Exception:
                - if mountpath datatype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
            &#34;&#34;&#34;

        if not isinstance(mount_path, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
                        &#34;library&#34;: {
                            &#34;mediaAgentName&#34;: self.media_agent,
                            &#34;libraryName&#34;: self._library_name,
                            &#34;mountPath&#34;: mount_path,
                            &#34;opType&#34;: 8
                        },
                        &#34;libNewProp&#34;: {
                            &#34;secondaryCredential&#34;: {
                                &#34;credentialName&#34;: saved_credential
                            },
                            &#34;resetSecondaryCredentials&#34;: reset
                        }
                    }

        exec_command = self._commcell_object._services[&#39;LIBRARY&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, exec_command, request_json
        )

        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    _response = response.json()[&#39;library&#39;]

                    if &#39;errorCode&#39; in _response:
                        if _response[&#39;errorCode&#39;] != 0:
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to add storage accelerator credential with error: \n [{0}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(_stderr))

    def _get_library_properties(self):
        &#34;&#34;&#34;Gets the disk library properties.

            Returns:
                dict - dictionary consisting of the properties of this library

            Raises:
                SDKException:
                    if response is empty

                    if failed to get disk library properties

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._library_properties_service
        )

        if flag:
            if response.json():
                if &#39;libraryInfo&#39; in response.json():
                    return response.json()[&#39;libraryInfo&#39;]
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;Failed to get disk Library properties&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_advanced_library_properties(self):
        &#34;&#34;&#34;Gets the advanced disk library  properties.

            Returns:
                dict - dictionary consisting of the advanced properties of disk library

            Raises:
                SDKException:
                    if response is empty

                    if failed to get disk library properties

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, f&#34;{self._library_properties_service}?propertylevel=20&#34;
        )

        if flag:
            if response.json():
                if &#39;libraryInfo&#39; in response.json():
                    return response.json()[&#39;libraryInfo&#39;]
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;Failed to get disk Library properties&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def _get_library_id(self):
        &#34;&#34;&#34;Gets the library id associated with this disk library.

            Returns:
                str - id associated with this disk library
        &#34;&#34;&#34;
        libraries = DiskLibraries(self._commcell_object)
        return libraries.get(self.library_name).library_id

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of this disk library.&#34;&#34;&#34;
        self._library_properties = self._get_library_properties()
        self._advanced_library_properties = self._get_advanced_library_properties()

    def add_mount_path(self, mount_path, media_agent, username=&#39;&#39;, password=&#39;&#39;):
        &#34;&#34;&#34; Adds a mount path [local/remote] to the disk library

        Args:
            mount_path  (str)   -- Mount path which needs to be added to disklibrary.
                                  This could be a local or remote mount path on mediaagent

            media_agent (str)   -- MediaAgent on which mountpath exists

            username    (str)   -- Username to access the mount path

            password    (str)   -- Password to access the mount path

        Returns:
            None

        Raises
            Exception:
                - if mountpath and mediaagent datatype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
            &#34;&#34;&#34;

        if not isinstance(mount_path, str) or not isinstance(media_agent, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isConfigRequired&#34;: 1,
                    &#34;library&#34;: {
                        &#34;opType&#34;: 4,
                        &#34;mediaAgentName&#34;: media_agent,
                        &#34;libraryName&#34;: self._library_name,
                        &#34;mountPath&#34;: mount_path,
                        &#34;loginName&#34;: username,
                        &#34;password&#34;: b64encode(password.encode()).decode(),
                    }
                }
        }

        exec_command = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;,
                                                                            exec_command,
                                                                            request_json)
        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    _response = response.json()[&#39;library&#39;]

                    if &#39;errorCode&#39; in _response:
                        if _response[&#39;errorCode&#39;] != 0:
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to add mount path [{0}] for library [{1}] with error: \n [{2}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(mount_path,
                                                                 self._library_name,
                                                                 _stderr))

    def set_mountpath_reserve_space(self, mount_path, size):
        &#34;&#34;&#34;
            To set reserve space on the mountpath
            Args:
                mount_path (str)    --  Mountpath

                size (int)          --  reserve space to be set in MB
        &#34;&#34;&#34;

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isConfigRequired&#34;: 1,
                    &#34;library&#34;: {
                        &#34;opType&#34;: 8,
                        &#34;mediaAgentName&#34;: self.media_agent,
                        &#34;libraryName&#34;: self._library_name,
                        &#34;mountPath&#34;: mount_path
                    },
                    &#34;libNewProp&#34;:{
                      &#34;reserveSpaceInMB&#34;: size
                    }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    def set_max_data_to_write_on_mount_path(self, mount_path, size):
        &#34;&#34;&#34;
            To set max data to write on the mountpath
            Args:
                mount_path (str)    --  Folder path for this mount path.

                size (int)          --  max data to be consumed in MB
        &#34;&#34;&#34;

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isConfigRequired&#34;: 1,
                    &#34;library&#34;: {
                        &#34;opType&#34;: 8,
                        &#34;mediaAgentName&#34;: self.media_agent,
                        &#34;libraryName&#34;: self._library_name,
                        &#34;mountPath&#34;: mount_path
                    },
                    &#34;libNewProp&#34;:{
                      &#34;maxDataToWriteMB&#34;: size
                    }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    def change_device_access_type(self, mountpath_id, device_id, device_controller_id, media_agent_id,
                                  device_access_type):
        &#34;&#34;&#34;
        To change device access type
            Args:
                mountpath_id (int)  -- Mount Path Id

                device_id (int)     -- Device Id

                device_controller_id (int) -- Device Controller Id

                media_agent_id (int)    --   Media Agent Id

                device_access_type (int)    --  Device access type
                                        Regular:
                                                Access type     Value
                                                Read              4
                                                Read and Write    6
                                                Preferred         8

                                        IP:
                                                Access type     Value
                                                Read             20
                                                Read/ Write      22

                                        Fibre Channel (FC)
                                                Access type     Value
                                                Read             36
                                                Read and Write   38

                                        iSCSi
                                                Access type     Value
                                                Read             132
                                                Read and Write   134
        &#34;&#34;&#34;

        if not all([isinstance(mountpath_id, int), isinstance(device_id, int), isinstance(device_controller_id, int),
                    isinstance(media_agent_id, int), isinstance(device_access_type, int)]):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
            &#34;EVGui_MMDevicePathInfoReq&#34;:
                {
                    &#34;mountpathId&#34;: mountpath_id,
                    &#34;infoList&#34;: {
                        &#34;accessType&#34;: device_access_type,
                        &#34;deviceId&#34;: device_id,
                        &#34;deviceControllerId&#34;: device_controller_id,
                        &#34;path&#34;: self.mount_path,
                        &#34;enabled&#34;: 1,
                        &#34;numWriters&#34;: -1,
                        &#34;opType&#34;: 2,
                        &#34;autoPickTransportType&#34;: 0,
                        &#34;protocolType&#34;: 679,
                        &#34;mediaAgent&#34;: {
                            &#34;id&#34;: media_agent_id
                        }
                    }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    def modify_cloud_access_type(self, mountpath_id, device_controller_id,
                                  device_access_type, enabled=True):
        &#34;&#34;&#34;
        To change device access type for cloud mount path
            Args:
                mountpath_id (int)  -- Mount Path Id

                device_controller_id (int) -- Device Controller Id

                device_access_type (int)    --  Device access type
                                        Possible values:
                                                Access type     Value
                                                Read              4
                                                Read and Write    6

                                        **by default preferred access (preferred = 8) will be set
        &#34;&#34;&#34;

        if not all([isinstance(mountpath_id, int), isinstance(device_controller_id, int),
                    isinstance(device_access_type, int)]):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        access = &#34;&#34;
        if device_access_type == 4:
            access = &#34;READ&#34;
        else:
            access = &#34;READ_AND_WRITE&#34;

        payload = {
            &#34;access&#34;: access,
            &#34;enable&#34;: enabled
        }

        EDIT_CLOUD_CONTROLLER = self._commcell_object._services[&#39;EDIT_CLOUD_CONTROLLER&#39;] % (mountpath_id, device_controller_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;, EDIT_CLOUD_CONTROLLER, payload)

        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json():
                    error_code = int(response.json().get(&#39;errorCode&#39;))
                    if error_code != 0:
                        error_message = response.json().get(&#39;errorMessage&#39;)
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to modify cloud access type with error: \n [{0}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(_stderr))


    def update_device_controller(self, mountpath_id, device_id, device_controller_id, media_agent_id,
                                 device_access_type, **kwargs):
        &#34;&#34;&#34;
        To update device controller properties.
            Args:
                mountpath_id (int)  -- Mount Path Id

                device_id (int)     -- Device Id

                device_controller_id (int) -- Device Controller Id

                media_agent_id (int)    --   Media Agent Id

                device_access_type (int)    --  Device access type
                                        Regular:
                                                Access type     Value
                                                Read              4
                                                Read and Write    6
                                                Preferred         8

                                        IP:
                                                Access type     Value
                                                Read             20
                                                Read/ Write      22

                                        Fibre Channel (FC)
                                                Access type     Value
                                                Read             36
                                                Read and Write   38

                                        iSCSi
                                                Access type     Value
                                                Read             132
                                                Read and Write   134

                **kwargs  (dict)  --  Optional arguments

                        Available kwargs Options:

                        username     (str)     -- username for the device
                                                  ** in case of cloud library username needs to be in the following format
                                                  ** &lt;vendorURL&gt;//__CVCRED__

                        password     (str)     -- password for the device
                                                  ** if credential name is used then use a dummy password

                        credential_name (str)  -- credential name as in the credential manager

                        path                   -- accessing path for media agent local / UNC

        &#34;&#34;&#34;

        if not all([isinstance(mountpath_id, int), isinstance(device_id, int), isinstance(device_controller_id, int),
                    isinstance(media_agent_id, int), isinstance(device_access_type, int)]):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        username = kwargs.get(&#34;username&#34;, &#34;&#34;)
        password = kwargs.get(&#34;password&#34;, &#34;&#34;)
        credential_name = kwargs.get(&#34;credential_name&#34;, &#34;&#34;)
        path = kwargs.get(&#34;path&#34;, self.mount_path)
        enabled = 1
        if not kwargs.get(&#39;enabled&#39;, True):
            enabled = 0
        request_json = {
            &#34;EVGui_MMDevicePathInfoReq&#34;:
                {
                    &#34;mountpathId&#34;: mountpath_id,
                    &#34;infoList&#34;: {
                        &#34;password&#34;: password,
                        &#34;accessType&#34;: device_access_type,
                        &#34;deviceId&#34;: device_id,
                        &#34;deviceControllerId&#34;: device_controller_id,
                        &#34;path&#34;: path,
                        &#34;enabled&#34;: enabled,
                        &#34;numWriters&#34;: -1,
                        &#34;opType&#34;: 2,
                        &#34;autoPickTransportType&#34;: 0,
                        &#34;protocolType&#34;: 679,
                        &#34;mediaAgent&#34;: {
                            &#34;id&#34;: media_agent_id
                        },
                        &#34;savedCredential&#34;: {
                            &#34;credentialName&#34;: credential_name
                        },
                        &#34;userName&#34;: username
                    }
                }
        }

        self._commcell_object.qoperation_execute(request_json)


    def verify_media(self, media_name, location_id):
        &#34;&#34;&#34;
            To perform verify media operation on media
            Args:
                media_name  --  Barcode of the media

                location_id --  Slot Id of the media on the library
        &#34;&#34;&#34;

        if not (isinstance(media_name, str) and
                isinstance(location_id,int)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_xml = f&#34;&#34;&#34;&lt;TMMsg_CreateTaskReq&gt;
                            &lt;taskInfo&gt;
                                &lt;task taskType=&#34;1&#34; /&gt;
                                &lt;subTasks subTaskOperation=&#34;1&#34;&gt;
                                    &lt;subTask operationType=&#34;4005&#34; subTaskType=&#34;1&#34;/&gt;
                                    &lt;options&gt;
                                        &lt;adminOpts&gt;
                                            &lt;libraryOption operation=&#34;6&#34;&gt;
                                                &lt;library _type_=&#34;9&#34; libraryName=&#34;{self.library_name}&#34;/&gt;
                                                &lt;media _type_=&#34;46&#34; mediaName=&#34;{media_name}&#34;/&gt;
                                                &lt;verifyMedia&gt;
                                                    &lt;location _type_=&#34;53&#34; locationId=&#34;{location_id}&#34;/&gt;
                                                &lt;/verifyMedia&gt;
                                            &lt;/libraryOption&gt;
                                        &lt;/adminOpts&gt;
                                    &lt;/options&gt;
                                &lt;/subTasks&gt;
                            &lt;/taskInfo&gt;
                        &lt;/TMMsg_CreateTaskReq&gt;&#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CREATE_TASK&#39;], request_xml
        )

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    from cvpysdk.job import Job
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                if &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;,o_str)

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    @property
    def free_space(self):
        &#34;&#34;&#34;Returns free space&#34;&#34;&#34;
        return self._library_properties.get(&#39;magLibSummary&#39;, {}).get(&#39;totalFreeSpace&#39;).strip()

    @property
    def mountpath_usage(self):
        &#34;&#34;&#34;Returns mount path usage&#34;&#34;&#34;
        return self._library_properties.get(&#39;magLibSummary&#39;, {}).get(&#39;mountPathUsage&#39;).strip()

    @mountpath_usage.setter
    def mountpath_usage(self, value):
        &#34;&#34;&#34;
            Sets mount path usage on the library
            Args:
                value  (str)   -- option needed to set for mountpath usage
                                    value: &#39;SPILL_AND_FILL&#39; or &#39;FILL_AND_SPILL&#39;
        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if value == &#39;SPILL_AND_FILL&#39;:
            value = 1
        elif value == &#39;FILL_AND_SPILL&#39;:
            value = 2
        else:
            raise SDKException(&#39;Storage&#39;, &#39;110&#39;)

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;library&#34;: {
                            &#34;opType&#34;: 32,
                            &#34;libraryName&#34;: self.library_name
                        },
                    &#34;libNewProp&#34;: {
                            &#34;mountPathUsage&#34;: value
                        }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    def set_mountpath_preferred_on_mediaagent(self, value):
        &#34;&#34;&#34;Sets select preferred mountPath according to mediaagent setting on the library.
            Args:
                value    (bool) --  preferMountPathAccordingToMA value to be set on library (True/False)

            Raises:
                SDKException:
                    if failed to update

                    if the type of value input is not correct

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isConfigRequired&#34;: 1,
                    &#34;library&#34;: {
                            &#34;opType&#34;: 32,
                            &#34;libraryName&#34;: self.library_name
                        },
                    &#34;libNewProp&#34;: {
                            &#34;preferMountPathAccordingToMA&#34;: int(value)
                        }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    @property
    def media_agents_associated(self):
        &#34;&#34;&#34; Returns the media agents associated with the disk library &#34;&#34;&#34;
        mount_paths = self._library_properties.get(&#39;MountPathList&#39;)
        media_agents = [mount_path.get(&#39;mountPathName&#39;).split(&#39;[&#39;)[1].split(&#39;]&#39;)[0] for mount_path in mount_paths if &#34;mountPathName&#34; in mount_path]
        return list(set(media_agents))

    @property
    def name(self):
        &#34;&#34;&#34;Returns library display name.&#34;&#34;&#34;
        return self._library_properties[&#39;MountPathList&#39;][0][&#39;mountPathSummary&#39;][&#39;libraryName&#39;]

    @property
    def library_name(self):
        &#34;&#34;&#34;Treats the library name as a read-only attribute.&#34;&#34;&#34;
        return self._library_name

    @property
    def library_id(self):
        &#34;&#34;&#34;Treats the library id as a read-only attribute.&#34;&#34;&#34;
        return self._library_id

    @property
    def library_properties(self):
        &#34;&#34;&#34;Returns the dictionary consisting of the full properties of the library&#34;&#34;&#34;
        self.refresh()
        return self._library_properties

    @property
    def advanced_library_properties(self):
        &#34;&#34;&#34;Returns the dictionary consisting of the advanced properties of the library&#34;&#34;&#34;
        self.refresh()
        return self._advanced_library_properties

    @property
    def mount_path(self):
        &#34;&#34;&#34;Treats the library id as a read-only attribute.&#34;&#34;&#34;
        return self.mountpath

    @mount_path.setter
    def mount_path(self, mount_path):
        &#34;&#34;&#34; setter for mountpath&#34;&#34;&#34;
        self.mountpath = mount_path

    @property
    def media_agent(self):
        &#34;&#34;&#34;Treats the library id as a read-only attribute.&#34;&#34;&#34;
        return self.mediaagent

    @media_agent.setter
    def media_agent(self, media_agent):
        &#34;&#34;&#34;setter for media agent&#34;&#34;&#34;
        self.mediaagent = media_agent

    def share_mount_path(self, new_media_agent, new_mount_path, **kwargs):
        &#34;&#34;&#34;
        Method to share a mountpath

        Args:

            new_media_agent (str)   -- Media agent which is accessing the shared mount path

            new_mount_path  (int)   -- Mount path to be shared

                kwargs  (dict)  --  Optional arguments

                    Available kwargs Options:

                        media_agent     (str)   -- Media agent associated with library

                        library_name    (str)   -- Name of the library which has the mount path

                        mount_path      (str)   -- Mount path to be shared

                        access_type     (int)   -- The access type of the shared mount path

                                                    Read Device Access = 4

                                                    Read/ Write Device Access = 6

                                                    Read Device Access with Preferred = 12

                                                    Read/Write Device Access with Preferred = 14

                                                    Data Server - IP Read = 20

                                                    Data Server - IP Read/ Write = 22

                                                    Data Server - FC Read = 36

                                                    Data Server - FC Read/ Write = 38

                                                    Data Server - iSCSI Read = 132

                                                    Data Server - iSCSI Read/ Write = 134

                                                    Note: For the Data Server device access type,
                                                          enter the local path provided in the library/mountPath
                                                          parameter in the libNewProp/mountPath parameter also.


                        username        (str)   -- Username to access the mount path, if UNC

                        password        (str)   -- Password to access the mount path, if UNC

                        credential_name  (str)  -- credential name for the credential manager
                                                   ** For cloud if you use credential_name update the username parameter
                                                   ** in the format of &#34;&lt;vendorURL&gt;//__CVCRED__&#34;
                                                   ** For example, &#34;s3.amazonaws.com//__CVCRED__&#34;
                                                   ** Update a dummy value for password parameter

        Returns:
            None

        Raises
            Exception:
                - if any of the parameter&#39;s dataype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
        &#34;&#34;&#34;

        media_agent = kwargs.get(&#39;media_agent&#39;, self.mediaagent)
        library_name = kwargs.get(&#39;library_name&#39;, self.library_name)
        mount_path = kwargs.get(&#39;mount_path&#39;, self.mountpath)
        access_type = kwargs.get(&#39;access_type&#39;, 22)
        username = kwargs.get(&#39;username&#39;, &#39;&#39;)
        password = kwargs.get(&#39;password&#39;, &#39;&#39;)
        credential_name = kwargs.get(&#39;credential_name&#39;, &#39;&#39;)

        self._EXECUTE = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]
        self.library = {
            &#34;opType&#34;: 64,
            &#34;mediaAgentName&#34;: media_agent,
            &#34;libraryName&#34;: library_name,
            &#34;mountPath&#34;: &#34;%s&#34; %
                         mount_path}
        self.lib_new_prop = {
            &#34;deviceAccessType&#34;: access_type,
            &#34;password&#34;: password,
            &#34;loginName&#34;: username,
            &#34;mediaAgentName&#34;: new_media_agent,
            &#34;mountPath&#34;: &#34;{}&#34;.format(new_mount_path),
            &#34;proxyPassword&#34;: &#34;&#34;,
            &#34;savedCredential&#34;: {
                &#34;credentialName&#34;: credential_name
            }
        }
        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;library&#34;: self.library,
                    &#34;libNewProp&#34;: self.lib_new_prop
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._EXECUTE, request_json
        )
        if flag:
            response_string = self._commcell_object._update_response_(response.text)
            if response.json():
                if &#34;library&#34; in response.json():
                    response = response.json()[&#34;library&#34;]
                    return response
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)
            else:

                raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


class RPStores(object):
    def __init__(self, commcell):
        &#34;&#34;&#34;Initialize object of the MediaAgents class.

            Args:
                commcell(object)  --  instance of the Commcell class

            Returns:
                object - instance of the MediaAgents class
        &#34;&#34;&#34;
        self._commcell = commcell
        self._rp_stores = None
        self.refresh()

    def _get_rp_stores(self):
        flag, response = self._commcell._cvpysdk_object.make_request(&#39;GET&#39;, self._commcell._services[&#39;ALL_RPStores&#39;])

        try:
            if response.json().get(&#39;libraryList&#39;):
                return {library[&#34;library&#34;][&#34;libraryName&#34;].lower(): library[&#34;MountPathList&#34;][0][&#34;rpStoreLibraryInfo&#34;]
                        [&#34;rpStoreId&#34;] for library in response.json()[&#34;libraryList&#34;]}
            return {}
        except (KeyError, ValueError):
            generic_msg = &#34;Unable to fetch RPStore&#34;
            err_msg = response.json().get(&#34;errorMessage&#34;, generic_msg) if response.status_code == 200 else generic_msg
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;{0}&#39;.format(err_msg))

    def add(self, name, path, storage, media_agent_name):
        &#34;&#34;&#34;

        Args:
            name    (str):     Name of the RPStore

            path    (str):     Path of the RPStore

            storage (int):     Storage Capacity of the RPStore in GB

            media_agent_name(str)   :   Name of the media agent

        Returns:
            An instance of RPStore

        &#34;&#34;&#34;
        try:
            assert self.has_rp_store(name) is False
        except AssertionError:
            raise SDKException(&#34;Storage&#34;, 102, &#34;An RPStore already exists with the same name&#34;)

        media_agents = MediaAgents(self._commcell)
        try:
            ma_id = media_agents.all_media_agents[media_agent_name][&#34;id&#34;]
        except KeyError:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;No media agent exists with name: {0}&#39;.format(media_agent_name))

        payload = {
            &#34;rpLibrary&#34;: {&#34;maxSpacePerRPStoreGB&#34;: storage},
            &#34;storageLibrary&#34;: {
                &#34;mediaAgentId&#34;: int(ma_id),
                &#34;libraryName&#34;: name,
                &#34;mountPath&#34;: path
            },
            &#34;opType&#34;: 1
        }
        flag, response = self._commcell._cvpysdk_object.make_request(
            &#34;POST&#34;, self._commcell._services[&#34;RPSTORE&#34;], payload)

        try:
            return RPStore(self._commcell, name, response.json()[&#34;storageLibrary&#34;][&#34;libraryId&#34;])
        except KeyError:
            generic_msg = &#34;Unable to add RPStore&#34;
            err_msg = response.json().get(&#34;errorMessage&#34;, generic_msg) if flag else generic_msg
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;{0}&#39;.format(err_msg))

    def has_rp_store(self, rpstore_name):
        &#34;&#34;&#34;Validates if the given RPStore is present

        Args:
            rpstore_name       (str):   Name of the RPStore

        Returns:
            bool : True if present else False
        &#34;&#34;&#34;
        if not isinstance(rpstore_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        return rpstore_name.lower() in self._rp_stores

    def get(self, rpstore_name):
        &#34;&#34;&#34;Fetches the given RPStore

        Args:
            rpstore_name    (str):  Name of the RPStore

        Returns:
            An instance of the RPStore

        &#34;&#34;&#34;
        if not isinstance(rpstore_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        try:
            return RPStore(self._commcell, rpstore_name, self._rp_stores[rpstore_name.lower()])
        except KeyError:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;No RPStore exists with name: {0}&#39;.format(rpstore_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the media agents associated with the Commcell.&#34;&#34;&#34;
        self._rp_stores = self._get_rp_stores()


class RPStore(object):
    def __init__(self, commcell, rpstore_name, rpstore_id):
        self._commcell = commcell
        self._rpstore_name = rpstore_name.lower()
        self._rpstore_id = rpstore_id

    @property
    def rpstore_name(self):
        return self._rpstore_name

    @property
    def rpstore_id(self):
        return self._rpstore_id



class TapeLibraries(Libraries):

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the DiskLibraries class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the DiskLibraries class
        &#34;&#34;&#34;
        super().__init__(commcell_object)
        self._commcell_object = commcell_object
        self._DETECT_TAPE_LIBRARY = self._commcell_object._services[&#39;DETECT_TAPE_LIBRARY&#39;]
        self._CONFIGURE_TAPE_LIBRARY = self._commcell_object._services[&#39;CONFIGURE_TAPE_LIBRARY&#39;]
        self._LOCK_MM_CONFIGURATION = self._commcell_object._services[&#39;LOCK_MM_CONFIGURATION&#39;]


    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all tape libraries of the commcell.

            Returns:
                str - string of all the tape libraries associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Tape Library&#39;)

        for index, library in enumerate(self._libraries):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, library)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the TapeLibraries class.&#34;&#34;&#34;
        return &#34;TapeLibraries class instance for Commcell&#34;



    def get(self, tape_library_name):
        &#34;&#34;&#34;
        Returns the object of TapeLibrary class of the specified library name

                    Args:
                        library_name (str)  --  name of the library

                    Returns:
                        object - object of TapeLibrary class of the specified library name

                    Raises:
                        SDKException:
                            if type of the library name argument is not string
        &#34;&#34;&#34;

        if not isinstance(tape_library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            if self.has_library(tape_library_name):
                tape_library_name = tape_library_name.lower()
                return TapeLibrary(self._commcell_object, tape_library_name, self._libraries[tape_library_name])

    def delete(self, tape_library_name):
        &#34;&#34;&#34;
        Deletes the specified library

                    Args:
                        tape_library_name (str)  --  name of the library

                    Returns:
                        bool - returns true if the library deleted successfully

                    Raises:
                        SDKException:
                            if type of the library name argument is not string
                            if library does not exists
                            if its failed to delete the library
        &#34;&#34;&#34;

        if not isinstance(tape_library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if not self.has_library(tape_library_name):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Invalid library name&#34;)

        pay_load={
                &#34;isDeconfigLibrary&#34;: 1,
                &#34;library&#34;: {
                    &#34;opType&#34;: 2,
                    &#34;libraryName&#34;: tape_library_name
                }
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._LIBRARY, pay_load)

        if not flag:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to DELETE the library&#34;)

        self.refresh()


    def __lock_unlock_mm_configuration(self, operation):
        &#34;&#34;&#34;
                Locks or unlocks the MM config for tape library detection

                            Args:
                                operation (int)  --  operation type
                                                            1 : Lock
                                                            0 : Unlock
                                                            2: Force lock

                            Raises:
                                SDKException:
                                    If API call is not successful
                                    If API response is invalid
                                    If errorCode is not part of response JSON
                                    If lock/unlock operation fails
        &#34;&#34;&#34;

        if not isinstance(operation, int):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Invalid Operation data type. Expected is integer&#34;)

        if not operation in [0,1,2]:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Invalid Operation type. Expected among [0,1,2] but received &#34;+str(operation))

        pay_load ={
        &#34;configLockUnlock&#34;: {
        &#34;lockType&#34;: operation
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._LOCK_MM_CONFIGURATION, pay_load)

        if flag :
            if response and response.json():
                if &#39;errorCode&#39; in response.json():
                    if response.json()[&#39;errorCode&#39;] != 0:
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to lock the MM Config. errorMessage : &#34;+response.json().get(&#39;errorMessage&#39;))
                else:
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                                       &#34;lock_unlock_mm_configuration :: Error code is not part of response JSON&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;Invalid response&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#34;API call is not successful&#34;)

    def lock_mm_configuration(self, forceLock = False):
        &#34;&#34;&#34;
            Locks the MM config for tape library detection

                Args:
                    forceLock (bool)  --  True for force lock
        &#34;&#34;&#34;
        if forceLock:
            self.__lock_unlock_mm_configuration(2)
            return
        self.__lock_unlock_mm_configuration(1)

    def unlock_mm_configuration(self):
        &#34;&#34;&#34;
            Unlocks the MM config for tape library detection
        &#34;&#34;&#34;
        self.__lock_unlock_mm_configuration(0)


    def detect_tape_library(self, mediaagents):
        &#34;&#34;&#34;
        Detect the tape libraries(s) of the provided MediaAgent(s)

                    Args:
                        mediaagents (list)  --  The list of the mediaagent(s)

                    Returns:
                        JSON - JSON of the tape library detections response

                    Raises:
                        SDKException:
                            if its fails to detect
        &#34;&#34;&#34;

        pay_load ={
        &#34;autoDetect&#34;: True,
        &#34;mediaAgentIdList&#34;: mediaagents
        }

        try:
            self.lock_mm_configuration()
            flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._DETECT_TAPE_LIBRARY, pay_load )
        finally:
            self.unlock_mm_configuration()

        if flag and response.json():
            return response.json()
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to detect library&#34;)

    def configure_tape_library(self,tape_library_name, mediaagents):
        &#34;&#34;&#34;
        Configure the tape library

                    Args:
                        tape_library_name (str)  --  name of the library

                        mediaagents(list) -- list of MediaAgents to use for configuration

                    Returns:
                        object - object of the TapeLibrary class for the specified tape library

                    Raises:
                        SDKException:
                            if fails to configure the tape library
        &#34;&#34;&#34;

        libraries=self.detect_tape_library(mediaagents)
        flag=False
        for lib in libraries[&#39;libraries&#39;]:

            if lib[&#39;libraryName&#39;] == tape_library_name:
                drive_list=lib[&#39;drives&#39;]

                pay_load= {
                    &#34;driveList&#34;: drive_list,
                    &#34;hdr&#34;: {
                        &#34;tag&#34;: 0
                    }
                }
                flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._CONFIGURE_TAPE_LIBRARY,
                                                                                    pay_load)
                break

        if not flag:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to configure the library&#34;)

        self.refresh()

        tape_library_name = tape_library_name.lower()
        for lib_name, lib_id in self._libraries.items():
            if lib_name.startswith(tape_library_name + &#34; &#34;):
                return self.get(lib_name)



class TapeLibrary(object):

    def __init__(self, commcell_object, tape_library_name, tape_library_id=None):
        &#34;&#34;&#34;Initialize object of the TapeLibrary class.

            Args:
                commcell_object (object)  --  instance of the Commcell class
                tape_library_name (string) -- name of the tape library
                tape_library_id (int) -- tape library ID

            Returns:
                object - instance of the TapeLibrary class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._name = tape_library_name
        if tape_library_id:
            self._library_id = str(tape_library_id)
        else:
            self._library_id = self._get_library_id()

        self._library_properties_service = self._commcell_object._services[
                                               &#39;GET_LIBRARY_PROPERTIES&#39;] % (self._library_id)

        self.library_properties = self._get_library_properties()

        self._name = self.library_properties[&#39;library&#39;][&#39;libraryName&#39;]


    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of the specified tape library.

            Returns:
                str - string of all the tape library associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#34;TapeLibrary instance of library : {0}&#34;

        return representation_string.format(self._name)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the TapeLibrary instance of this class.&#34;&#34;&#34;
        representation_string = &#39;TapeLibrary class instance for library: &#34;{0}&#34; of Commcell: &#34;{1}&#34;&#39;
        return representation_string.format(
            self._name, self._commcell_object.commserv_name
        )


    def _get_library_id(self):
        &#34;&#34;&#34;Gets the library id associated with this tape library.

            Returns:
                str - id associated with this tape library
        &#34;&#34;&#34;
        libraries = TapeLibraries(self._commcell_object)
        return libraries.get(self.library_name).library_id


    def get_drive_list(self):
        &#34;&#34;&#34;
            Returns the tape drive list of this tape library

            Returns:
                list - List of the drives of this tape library
        &#34;&#34;&#34;

        self.refresh()

        drive_list=[]

        if &#39;DriveList&#39; in self.library_properties:
            for drive in self.library_properties[&#34;DriveList&#34;]:
                drive_list.append(drive[&#34;driveName&#34;])

        return drive_list


    def _get_library_properties(self):
        &#34;&#34;&#34;Gets the tape library properties.

            Returns:
                dict - dictionary consisting of the properties of this library

            Raises:
                SDKException:
                    if response is empty

                    if failed to get tape library properties

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._library_properties_service
        )

        if flag:
            if response.json():
                if &#39;libraryInfo&#39; in response.json():
                    return response.json()[&#39;libraryInfo&#39;]
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;Failed to get tape Library properties&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of this tape library.&#34;&#34;&#34;
        self.library_properties = self._get_library_properties()


    @property
    def library_name(self):
        &#34;&#34;&#34;Treats the library name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @property
    def library_id(self):
        &#34;&#34;&#34;Treats the library ID as a read-only attribute.&#34;&#34;&#34;
        return self._library_id</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.storage.DiskLibraries"><code class="flex name class">
<span>class <span class="ident">DiskLibraries</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the disk libraries associated with the commcell.</p>
<p>Initialize object of the DiskLibraries class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the DiskLibraries class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1159-L1407" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DiskLibraries(Libraries):
    &#34;&#34;&#34;Class for getting all the disk libraries associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        super().__init__(commcell_object)

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all disk libraries of the commcell.

            Returns:
                str - string of all the disk libraries associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Disk Library&#39;)

        for index, library in enumerate(self._libraries):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, library)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the DiskLibraries class.&#34;&#34;&#34;
        return &#34;DiskLibraries class instance for Commcell&#34;

    @property
    def all_disk_libraries(self):
        &#34;&#34;&#34;Returns dict of all the disk libraries on this commcell

            dict - consists of all disk libraries of the commcell
                    {
                         &#34;disk_library1_name&#34;: disk_library1_id,
                         &#34;disk_library2_name&#34;: disk_library2_id
                    }

        &#34;&#34;&#34;
        return self._libraries

    def add(self, library_name, media_agent, mount_path, username=&#34;&#34;, password=&#34;&#34;, servertype=0,
            saved_credential_name=&#34;&#34;, **kwargs):
        &#34;&#34;&#34;Adds a new Disk Library to the Commcell.

            Args:
                library_name (str)        --  name of the new library to add

                media_agent  (str/object) --  name or instance of media agent to add the library to

                mount_path   (str)        --  full path of the folder to mount the library at

                username     (str)        --  username to access the mount path
                    default: &#34;&#34;

                password     (str)        --  password to access the mount path
                    default: &#34;&#34;

                servertype   (int)        -- provide cloud library server type
                    default 0, value 59 for HPstore

                saved_credential_name   (str)   --  name of the saved credential
                    default: &#34;&#34;

                kwargs      (dict)  --  optional arguments

                Available kwargs Options:

                    proxy_password (str) -- plain text password of proxy server
                        default: &#34;&#34;

            Returns:
                object - instance of the DiskLibrary class, if created successfully

            Raises:
                SDKException:
                    if type of the library name argument is not string

                    if type of the mount path argument is not string

                    if type of the username argument is not string

                    if type of the password argument is not string

                    if type of the media agent argument is not either string or MediaAgent instance

                    if failed to create disk library

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not (isinstance(library_name, str) and
                isinstance(mount_path, str) and
                isinstance(username, str) and
                isinstance(password, str)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if isinstance(media_agent, MediaAgent):
            media_agent = media_agent
        elif isinstance(media_agent, str):
            media_agent = MediaAgent(self._commcell_object, media_agent)
        else:
            raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        proxy_password = kwargs.get(&#39;proxy_password&#39;, &#39;&#39;)

        request_json = {
            &#34;isConfigRequired&#34;: 1,
            &#34;library&#34;: {
                &#34;mediaAgentId&#34;: int(media_agent.media_agent_id),
                &#34;libraryName&#34;: library_name,
                &#34;mountPath&#34;: mount_path,
                &#34;loginName&#34;: username,
                &#34;password&#34;: b64encode(password.encode()).decode(),
                &#34;opType&#34;: 1,
                &#34;savedCredential&#34;:{
                    &#34;credentialName&#34;: saved_credential_name
                }
            }
        }

        if servertype &gt; 0:
            request_json[&#34;library&#34;][&#34;serverType&#34;] = servertype
            request_json[&#34;library&#34;][&#34;isCloud&#34;] = 1

            if saved_credential_name:
                request_json[&#34;library&#34;][&#34;password&#34;] = b64encode(&#34;XXXXX&#34;.encode()).decode()

            if proxy_password != &#34;&#34;:
                request_json[&#34;library&#34;][&#34;proxyPassword&#34;] = b64encode(proxy_password.encode()).decode()

            if servertype == 59:
                request_json[&#34;library&#34;][&#34;HybridCloudOption&#34;] = {
                    &#34;enableHybridCloud&#34;: &#34;2&#34;, &#34;diskLibrary&#34;: {&#34;_type_&#34;: &#34;9&#34;}}
                request_json[&#34;library&#34;][&#34;savedCredential&#34;] = {&#34;_type_&#34;: &#34;9&#34;}

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._LIBRARY, request_json
        )

        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    library = response.json()[&#39;library&#39;]

                    # initialize the libraries again
                    # so the libraries object has all the libraries
                    self.refresh()

                    return DiskLibrary(
                        self._commcell_object,
                        library[&#39;libraryName&#39;],
                        library_details=library)
                elif &#39;errorCode&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create disk library\nError: &#34;{0}&#34;&#39;.format(error_message)

                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, library_name):
        &#34;&#34;&#34;deletes the specified library.

            Args:
                library_name (str)  --  name of the disk library to delete

            Raises:
                SDKException:
                    if type of the library name argument is not string
                    if no library exists with the given name
                    if response is incorrect
        &#34;&#34;&#34;
        if not isinstance(library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if not self.has_library(library_name):
            raise SDKException(&#39;Storage&#39;,
                               &#39;102&#39;,
                               &#39;No library exists with name: {0}&#39;.
                               format(library_name))

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isDeconfigLibrary&#34;: 1,
                    &#34;library&#34;:
                        {
                            &#34;opType&#34;: 2,
                            &#34;libraryName&#34;: library_name
                        }
                }
        }
        exec_command = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, exec_command, request_json)

        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    _response = response.json()[&#39;library&#39;]

                    if &#39;errorCode&#39; in _response:
                        if _response[&#39;errorCode&#39;] == 0:
                            self.refresh()
                        else:
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
                else:
                    if &#39;errorMessage&#39; in response.json():
                        o_str = &#39;Error: &#39; + response.json()[&#39;errorMessage&#39;]
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)

                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to delete library {0} with error: \n [{1}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(library_name, _stderr))

    def get(self, library_name, library_details=None):
        &#34;&#34;&#34;Returns a DiskLibrary object of the specified disk library name.

            Args:
                library_name (str)  --  name of the disk library

                library_details (dict) -- dict containing mountpath and mediaagent details

            Returns:
                object - instance of the DiskLibrary class for the given library name

            Raises:
                SDKException:
                    if type of the library name argument is not string

                    if no disk library exists with the given name
        &#34;&#34;&#34;
        if not isinstance(library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            library_name = library_name.lower()

            if self.has_library(library_name):
                return DiskLibrary(self._commcell_object,
                                   library_name,
                                   self._libraries[library_name], library_details)

            raise SDKException(
                &#39;Storage&#39;, &#39;102&#39;, &#39;No disk library exists with name: {0}&#39;.format(library_name)
            )</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.storage.Libraries" href="#cvpysdk.storage.Libraries">Libraries</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.storage.DiskLibraries.all_disk_libraries"><code class="name">var <span class="ident">all_disk_libraries</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all the disk libraries on this commcell</p>
<p>dict - consists of all disk libraries of the commcell
{
"disk_library1_name": disk_library1_id,
"disk_library2_name": disk_library2_id
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1183-L1194" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_disk_libraries(self):
    &#34;&#34;&#34;Returns dict of all the disk libraries on this commcell

        dict - consists of all disk libraries of the commcell
                {
                     &#34;disk_library1_name&#34;: disk_library1_id,
                     &#34;disk_library2_name&#34;: disk_library2_id
                }

    &#34;&#34;&#34;
    return self._libraries</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage.DiskLibraries.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, library_name, media_agent, mount_path, username='', password='', servertype=0, saved_credential_name='', **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new Disk Library to the Commcell.</p>
<h2 id="args">Args</h2>
<p>library_name (str)
&ndash;
name of the new library to add</p>
<p>media_agent
(str/object) &ndash;
name or instance of media agent to add the library to</p>
<p>mount_path
(str)
&ndash;
full path of the folder to mount the library at</p>
<p>username
(str)
&ndash;
username to access the mount path
default: ""</p>
<p>password
(str)
&ndash;
password to access the mount path
default: ""</p>
<p>servertype
(int)
&ndash; provide cloud library server type
default 0, value 59 for HPstore</p>
<p>saved_credential_name
(str)
&ndash;
name of the saved credential
default: ""</p>
<p>kwargs
(dict)
&ndash;
optional arguments</p>
<p>Available kwargs Options:</p>
<pre><code>proxy_password (str) -- plain text password of proxy server
    default: ""
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the DiskLibrary class, if created successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the library name argument is not string</p>
<pre><code>if type of the mount path argument is not string

if type of the username argument is not string

if type of the password argument is not string

if type of the media agent argument is not either string or MediaAgent instance

if failed to create disk library

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1196-L1318" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, library_name, media_agent, mount_path, username=&#34;&#34;, password=&#34;&#34;, servertype=0,
        saved_credential_name=&#34;&#34;, **kwargs):
    &#34;&#34;&#34;Adds a new Disk Library to the Commcell.

        Args:
            library_name (str)        --  name of the new library to add

            media_agent  (str/object) --  name or instance of media agent to add the library to

            mount_path   (str)        --  full path of the folder to mount the library at

            username     (str)        --  username to access the mount path
                default: &#34;&#34;

            password     (str)        --  password to access the mount path
                default: &#34;&#34;

            servertype   (int)        -- provide cloud library server type
                default 0, value 59 for HPstore

            saved_credential_name   (str)   --  name of the saved credential
                default: &#34;&#34;

            kwargs      (dict)  --  optional arguments

            Available kwargs Options:

                proxy_password (str) -- plain text password of proxy server
                    default: &#34;&#34;

        Returns:
            object - instance of the DiskLibrary class, if created successfully

        Raises:
            SDKException:
                if type of the library name argument is not string

                if type of the mount path argument is not string

                if type of the username argument is not string

                if type of the password argument is not string

                if type of the media agent argument is not either string or MediaAgent instance

                if failed to create disk library

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if not (isinstance(library_name, str) and
            isinstance(mount_path, str) and
            isinstance(username, str) and
            isinstance(password, str)):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    if isinstance(media_agent, MediaAgent):
        media_agent = media_agent
    elif isinstance(media_agent, str):
        media_agent = MediaAgent(self._commcell_object, media_agent)
    else:
        raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

    proxy_password = kwargs.get(&#39;proxy_password&#39;, &#39;&#39;)

    request_json = {
        &#34;isConfigRequired&#34;: 1,
        &#34;library&#34;: {
            &#34;mediaAgentId&#34;: int(media_agent.media_agent_id),
            &#34;libraryName&#34;: library_name,
            &#34;mountPath&#34;: mount_path,
            &#34;loginName&#34;: username,
            &#34;password&#34;: b64encode(password.encode()).decode(),
            &#34;opType&#34;: 1,
            &#34;savedCredential&#34;:{
                &#34;credentialName&#34;: saved_credential_name
            }
        }
    }

    if servertype &gt; 0:
        request_json[&#34;library&#34;][&#34;serverType&#34;] = servertype
        request_json[&#34;library&#34;][&#34;isCloud&#34;] = 1

        if saved_credential_name:
            request_json[&#34;library&#34;][&#34;password&#34;] = b64encode(&#34;XXXXX&#34;.encode()).decode()

        if proxy_password != &#34;&#34;:
            request_json[&#34;library&#34;][&#34;proxyPassword&#34;] = b64encode(proxy_password.encode()).decode()

        if servertype == 59:
            request_json[&#34;library&#34;][&#34;HybridCloudOption&#34;] = {
                &#34;enableHybridCloud&#34;: &#34;2&#34;, &#34;diskLibrary&#34;: {&#34;_type_&#34;: &#34;9&#34;}}
            request_json[&#34;library&#34;][&#34;savedCredential&#34;] = {&#34;_type_&#34;: &#34;9&#34;}

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._LIBRARY, request_json
    )

    if flag:
        if response.json():
            if &#39;library&#39; in response.json():
                library = response.json()[&#39;library&#39;]

                # initialize the libraries again
                # so the libraries object has all the libraries
                self.refresh()

                return DiskLibrary(
                    self._commcell_object,
                    library[&#39;libraryName&#39;],
                    library_details=library)
            elif &#39;errorCode&#39; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Failed to create disk library\nError: &#34;{0}&#34;&#39;.format(error_message)

                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibraries.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, library_name)</span>
</code></dt>
<dd>
<div class="desc"><p>deletes the specified library.</p>
<h2 id="args">Args</h2>
<p>library_name (str)
&ndash;
name of the disk library to delete</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the library name argument is not string
if no library exists with the given name
if response is incorrect</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1320-L1376" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, library_name):
    &#34;&#34;&#34;deletes the specified library.

        Args:
            library_name (str)  --  name of the disk library to delete

        Raises:
            SDKException:
                if type of the library name argument is not string
                if no library exists with the given name
                if response is incorrect
    &#34;&#34;&#34;
    if not isinstance(library_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    if not self.has_library(library_name):
        raise SDKException(&#39;Storage&#39;,
                           &#39;102&#39;,
                           &#39;No library exists with name: {0}&#39;.
                           format(library_name))

    request_json = {
        &#34;EVGui_ConfigureStorageLibraryReq&#34;:
            {
                &#34;isDeconfigLibrary&#34;: 1,
                &#34;library&#34;:
                    {
                        &#34;opType&#34;: 2,
                        &#34;libraryName&#34;: library_name
                    }
            }
    }
    exec_command = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, exec_command, request_json)

    if flag:
        if response.json():
            if &#39;library&#39; in response.json():
                _response = response.json()[&#39;library&#39;]

                if &#39;errorCode&#39; in _response:
                    if _response[&#39;errorCode&#39;] == 0:
                        self.refresh()
                    else:
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
            else:
                if &#39;errorMessage&#39; in response.json():
                    o_str = &#39;Error: &#39; + response.json()[&#39;errorMessage&#39;]
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)

                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        _stdout = &#39;Failed to delete library {0} with error: \n [{1}]&#39;
        _stderr = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(library_name, _stderr))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibraries.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, library_name, library_details=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a DiskLibrary object of the specified disk library name.</p>
<h2 id="args">Args</h2>
<p>library_name (str)
&ndash;
name of the disk library</p>
<p>library_details (dict) &ndash; dict containing mountpath and mediaagent details</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the DiskLibrary class for the given library name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the library name argument is not string</p>
<pre><code>if no disk library exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1378-L1407" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, library_name, library_details=None):
    &#34;&#34;&#34;Returns a DiskLibrary object of the specified disk library name.

        Args:
            library_name (str)  --  name of the disk library

            library_details (dict) -- dict containing mountpath and mediaagent details

        Returns:
            object - instance of the DiskLibrary class for the given library name

        Raises:
            SDKException:
                if type of the library name argument is not string

                if no disk library exists with the given name
    &#34;&#34;&#34;
    if not isinstance(library_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
    else:
        library_name = library_name.lower()

        if self.has_library(library_name):
            return DiskLibrary(self._commcell_object,
                               library_name,
                               self._libraries[library_name], library_details)

        raise SDKException(
            &#39;Storage&#39;, &#39;102&#39;, &#39;No disk library exists with name: {0}&#39;.format(library_name)
        )</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.storage.Libraries" href="#cvpysdk.storage.Libraries">Libraries</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.storage.Libraries.has_library" href="#cvpysdk.storage.Libraries.has_library">has_library</a></code></li>
<li><code><a title="cvpysdk.storage.Libraries.refresh" href="#cvpysdk.storage.Libraries.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.storage.DiskLibrary"><code class="flex name class">
<span>class <span class="ident">DiskLibrary</span></span>
<span>(</span><span>commcell_object, library_name, library_id=None, library_details=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for a specific disk library.</p>
<p>Initialise the DiskLibrary object.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>library_name
(str)
&ndash;
name of the disk library</p>
<p>library_id
(str)
&ndash;
id of the disk library
default: None</p>
<p>library_details (dict) &ndash; dict containing mountpath and mediaagent details
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the DiskLibrary class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1411-L2465" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DiskLibrary(object):
    &#34;&#34;&#34;Class for a specific disk library.&#34;&#34;&#34;

    def __init__(self, commcell_object, library_name, library_id=None, library_details=None):
        &#34;&#34;&#34;Initialise the DiskLibrary object.

            Args:
                commcell_object  (object)  --  instance of the Commcell class

                library_name     (str)     --  name of the disk library

                library_id       (str)     --  id of the disk library
                    default: None

                library_details (dict) -- dict containing mountpath and mediaagent details
                    default: None

            Returns:
                object - instance of the DiskLibrary class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._library_name = library_name.lower()

        if library_id:
            self._library_id = str(library_id)
        else:
            self._library_id = self._get_library_id()
        self._library_properties_service = self._commcell_object._services[
            &#39;GET_LIBRARY_PROPERTIES&#39;] % (self._library_id)
        self._library_properties = self._get_library_properties()
        self._advanced_library_properties = self._get_advanced_library_properties()
        if library_details is not None:
            self.mountpath = library_details.get(&#39;mountPath&#39;, None)
            self.mediaagent = library_details.get(&#39;mediaAgentName&#39;, None)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;DiskLibrary class instance for library: &#34;{0}&#34; of Commcell: &#34;{1}&#34;&#39;
        return representation_string.format(
            self.library_name, self._commcell_object.commserv_name
        )

    def move_mountpath(self, mountpath_id, source_device_path,
                       source_mediaagent_id, target_device_path, target_mediaagent_id,
                       target_device_id=0):

        &#34;&#34;&#34; To perform move mountpath operation
        Args:
            mountpath_id  (int)   --  Mountpath Id that need to be moved.

            source_device_path (str)   -- Present Mountpath location

            source_mediaagent_id    (int)   -- MediaAgent Id on which present mountpath exists

            target_device_path    (str)   -- New Mountpath location

            target_mediaagent_id    (int)   -- MediaAgent Id on which new mountpath exists

            target_device_id        (int)   --  Device Id of target path if already exists

        Returns:
            instance of the Job class for this move mountpath job

        Raises
            Exception:
                - if argument datatype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
        &#34;&#34;&#34;

        if not (isinstance(mountpath_id, int) and
                isinstance(source_mediaagent_id, int) and
                isinstance(target_mediaagent_id, int) and
                (isinstance(target_device_path, str) or target_device_id &gt; 0) and
                isinstance(source_device_path, str)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        MOVE_MOUNTPATH_DETAILS = self._commcell_object._services[&#39;GET_MOVE_MOUNTPATH_DETAILS&#39;] % (mountpath_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, MOVE_MOUNTPATH_DETAILS)

        source_device_id = None

        if flag:
            if response.json():
                if &#39;sourceDeviceInfo&#39; in response.json():
                    source_device_id = response.json().get(&#39;sourceDeviceInfo&#39;).get(&#39;deviceId&#39;, None)
                if not source_device_id:
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;Failed to get details of the mountpath for move&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        request_json = {
            &#39;MPMoveOption&#39;: {
                &#39;mountPathMoveList&#39;: [{
                    &#39;sourceDeviceId&#39;: source_device_id,
                    &#39;sourcemediaAgentId&#39;: source_mediaagent_id,
                    &#39;targetMediaAgentId&#39;: target_mediaagent_id,
                }]
            }
        }
        if target_device_id &gt; 0:
            request_json[&#39;MPMoveOption&#39;][&#39;mountPathMoveList&#39;][0][&#39;targetDeviceId&#39;] = target_device_id
        else:
            request_json[&#39;MPMoveOption&#39;][&#39;mountPathMoveList&#39;][0][&#39;targetDevicePath&#39;] = target_device_path

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;MOVE_MOUNTPATH&#39;], request_json)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    if len(response.json()[&#39;jobIds&#39;]) == 1:
                        from cvpysdk.job import Job
                        return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                    else:
                        from cvpysdk.job import Job
                        mp_move_job_list = []
                        for job_id in response.json()[&#39;jobIds&#39;]:
                            mp_move_job_list.append(Job(self._commcell_object, job_id))
                        return mp_move_job_list

                if &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Commcell&#39;, &#39;105&#39;, o_str)

                else:
                    raise SDKException(&#39;Commcell&#39;, &#39;105&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def validate_mountpath(self, mountpath_drive_id, media_agent):

        &#34;&#34;&#34; To perform storage validation on mountpath
        Args:
            mountpath_drive_id  (int)   --  Drive Id of mountpath that need to be validate.

            media_agent (str)   -- MediaAgent on which Mountpath exists

        Returns:
            instance of the Job class for this storage validation job

        Raises
            Exception:
                - if argument datatype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
        &#34;&#34;&#34;

        if not (isinstance(mountpath_drive_id, int) and
                isinstance(media_agent, str)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)


        request_xml = &#34;&#34;&#34;&lt;TMMsg_CreateTaskReq&gt;
                        &lt;taskInfo taskOperation=&#34;1&#34;&gt;
                            &lt;task associatedObjects=&#34;0&#34; description=&#34;Storage Validation - Automation&#34; initiatedFrom=&#34;1&#34; 
                            isEZOperation=&#34;0&#34; isEditing=&#34;0&#34; isFromCommNetBrowserRootNode=&#34;0&#34; ownerId=&#34;1&#34; ownerName=&#34;&#34; 
                            policyType=&#34;0&#34; runUserId=&#34;1&#34; sequenceNumber=&#34;0&#34; taskType=&#34;1&#34;&gt;
                            &lt;taskFlags notRunnable=&#34;0&#34; /&gt;
                            &lt;/task&gt;
                            &lt;subTasks subTaskOperation=&#34;1&#34;&gt;
                                &lt;subTask flags=&#34;0&#34; operationType=&#34;4013&#34; subTaskId=&#34;1&#34; subTaskOrder=&#34;0&#34; subTaskType=&#34;1&#34;/&gt;
                                &lt;options originalJobId=&#34;0&#34;&gt;
                                    &lt;adminOpts&gt;
                                        &lt;libraryOption  operation=&#34;13&#34; validationFlags=&#34;0&#34; validattionReservedFlags=&#34;0&#34;&gt;
                                            &lt;library libraryId=&#34;{0}&#34; /&gt;
                                            &lt;mediaAgent mediaAgentName=&#34;{1}&#34; /&gt;
                                            &lt;driveIds driveId=&#34;{2}&#34; /&gt;
                                            &lt;validateDrive chunkSize=&#34;16384&#34; chunksTillEnd=&#34;0&#34; fileMarkerToStart=&#34;2&#34;
                                             numberOfChunks=&#34;2&#34; threadCount=&#34;2&#34; volumeBlockSize=&#34;64&#34; /&gt;
                                        &lt;/libraryOption&gt; &lt;/adminOpts&gt; &lt;/options&gt; &lt;/subTasks&gt;  &lt;/taskInfo&gt;
                            &lt;/TMMsg_CreateTaskReq&gt;&#34;&#34;&#34;.format(self.library_id, media_agent, mountpath_drive_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CREATE_TASK&#39;], request_xml
        )

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    from cvpysdk.job import Job
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                if &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Commcell&#39;, &#39;105&#39;, o_str)

                else:
                    raise SDKException(&#39;Commcell&#39;, &#39;105&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def add_cloud_mount_path(self, mount_path, media_agent, username, password, server_type, saved_credential_name=&#34;&#34;):
        &#34;&#34;&#34; Adds a mount path to the cloud library

        Args:
            mount_path  (str)   -- cloud container or bucket.

            media_agent (str)   -- MediaAgent on which mountpath exists

            username    (str)   -- Username to access the mount path in the format &lt;Service Host&gt;//&lt;Account Name&gt;
            Eg: s3.us-west-1.amazonaws.com//MyAccessKeyID. For more information refer http://documentation.commvault.com/commvault/v11/article?p=97863.htm.

            password    (str)   -- Password to access the mount path

            server_type  (int)   -- provide cloud library server type
                                    Eg: 3-Microsoft Azure Storage . For more information refer http://documentation.commvault.com/commvault/v11/article?p=97863.htm.

            saved_credential_name   (str)   --  name of the saved credential
                default: &#34;&#34;

        Returns:
            None

        Raises
            Exception:
                - if mountpath or mediaagent or username or password or servertype arguments dataype is invalid

                - if servertype input data is incorrect

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
            &#34;&#34;&#34;

        if not (isinstance(mount_path, str) or isinstance(media_agent, str)
                or isinstance(username, str) or isinstance(password, str)
                or isinstance(server_type, int)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
            &#34;isConfigRequired&#34;: 1,
            &#34;library&#34;: {
                &#34;opType&#34;: 4,
                &#34;isCloud&#34;: 1,
                &#34;mediaAgentName&#34;: media_agent,
                &#34;libraryName&#34;: self._library_name,
                &#34;mountPath&#34;: mount_path,
                &#34;loginName&#34;: username,
                &#34;password&#34;: b64encode(password.encode()).decode(),
                &#34;serverType&#34;: server_type,
                &#34;savedCredential&#34;: {
                    &#34;credentialName&#34;: saved_credential_name
                }
            }
        }

        exec_command = self._commcell_object._services[&#39;LIBRARY&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, exec_command, request_json
        )

        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    _response = response.json()[&#39;library&#39;]

                    if &#39;errorCode&#39; in _response:
                        if _response[&#39;errorCode&#39;] != 0:
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to add mount path [{0}] for library [{1}] with error: \n [{2}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(mount_path,
                                                                 self._library_name,
                                                                 _stderr))

    def add_storage_accelerator_credential(self, mount_path, saved_credential=&#34;&#34;, reset=False):
        &#34;&#34;&#34; Add storage accelerator credential to the cloud mount path

        Args:
            mount_path  (str)   -- Mount path to which secondary credentials needs to be added

            saved_credential (str)   -- saved credential name
                default: &#34;&#34;

            reset    (bool)   -- reset storage accelerator credential
                default: False

        Raises
            Exception:
                - if mountpath datatype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
            &#34;&#34;&#34;

        if not isinstance(mount_path, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
                        &#34;library&#34;: {
                            &#34;mediaAgentName&#34;: self.media_agent,
                            &#34;libraryName&#34;: self._library_name,
                            &#34;mountPath&#34;: mount_path,
                            &#34;opType&#34;: 8
                        },
                        &#34;libNewProp&#34;: {
                            &#34;secondaryCredential&#34;: {
                                &#34;credentialName&#34;: saved_credential
                            },
                            &#34;resetSecondaryCredentials&#34;: reset
                        }
                    }

        exec_command = self._commcell_object._services[&#39;LIBRARY&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, exec_command, request_json
        )

        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    _response = response.json()[&#39;library&#39;]

                    if &#39;errorCode&#39; in _response:
                        if _response[&#39;errorCode&#39;] != 0:
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to add storage accelerator credential with error: \n [{0}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(_stderr))

    def _get_library_properties(self):
        &#34;&#34;&#34;Gets the disk library properties.

            Returns:
                dict - dictionary consisting of the properties of this library

            Raises:
                SDKException:
                    if response is empty

                    if failed to get disk library properties

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._library_properties_service
        )

        if flag:
            if response.json():
                if &#39;libraryInfo&#39; in response.json():
                    return response.json()[&#39;libraryInfo&#39;]
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;Failed to get disk Library properties&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_advanced_library_properties(self):
        &#34;&#34;&#34;Gets the advanced disk library  properties.

            Returns:
                dict - dictionary consisting of the advanced properties of disk library

            Raises:
                SDKException:
                    if response is empty

                    if failed to get disk library properties

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, f&#34;{self._library_properties_service}?propertylevel=20&#34;
        )

        if flag:
            if response.json():
                if &#39;libraryInfo&#39; in response.json():
                    return response.json()[&#39;libraryInfo&#39;]
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;Failed to get disk Library properties&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def _get_library_id(self):
        &#34;&#34;&#34;Gets the library id associated with this disk library.

            Returns:
                str - id associated with this disk library
        &#34;&#34;&#34;
        libraries = DiskLibraries(self._commcell_object)
        return libraries.get(self.library_name).library_id

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of this disk library.&#34;&#34;&#34;
        self._library_properties = self._get_library_properties()
        self._advanced_library_properties = self._get_advanced_library_properties()

    def add_mount_path(self, mount_path, media_agent, username=&#39;&#39;, password=&#39;&#39;):
        &#34;&#34;&#34; Adds a mount path [local/remote] to the disk library

        Args:
            mount_path  (str)   -- Mount path which needs to be added to disklibrary.
                                  This could be a local or remote mount path on mediaagent

            media_agent (str)   -- MediaAgent on which mountpath exists

            username    (str)   -- Username to access the mount path

            password    (str)   -- Password to access the mount path

        Returns:
            None

        Raises
            Exception:
                - if mountpath and mediaagent datatype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
            &#34;&#34;&#34;

        if not isinstance(mount_path, str) or not isinstance(media_agent, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isConfigRequired&#34;: 1,
                    &#34;library&#34;: {
                        &#34;opType&#34;: 4,
                        &#34;mediaAgentName&#34;: media_agent,
                        &#34;libraryName&#34;: self._library_name,
                        &#34;mountPath&#34;: mount_path,
                        &#34;loginName&#34;: username,
                        &#34;password&#34;: b64encode(password.encode()).decode(),
                    }
                }
        }

        exec_command = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;,
                                                                            exec_command,
                                                                            request_json)
        if flag:
            if response.json():
                if &#39;library&#39; in response.json():
                    _response = response.json()[&#39;library&#39;]

                    if &#39;errorCode&#39; in _response:
                        if _response[&#39;errorCode&#39;] != 0:
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to add mount path [{0}] for library [{1}] with error: \n [{2}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(mount_path,
                                                                 self._library_name,
                                                                 _stderr))

    def set_mountpath_reserve_space(self, mount_path, size):
        &#34;&#34;&#34;
            To set reserve space on the mountpath
            Args:
                mount_path (str)    --  Mountpath

                size (int)          --  reserve space to be set in MB
        &#34;&#34;&#34;

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isConfigRequired&#34;: 1,
                    &#34;library&#34;: {
                        &#34;opType&#34;: 8,
                        &#34;mediaAgentName&#34;: self.media_agent,
                        &#34;libraryName&#34;: self._library_name,
                        &#34;mountPath&#34;: mount_path
                    },
                    &#34;libNewProp&#34;:{
                      &#34;reserveSpaceInMB&#34;: size
                    }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    def set_max_data_to_write_on_mount_path(self, mount_path, size):
        &#34;&#34;&#34;
            To set max data to write on the mountpath
            Args:
                mount_path (str)    --  Folder path for this mount path.

                size (int)          --  max data to be consumed in MB
        &#34;&#34;&#34;

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isConfigRequired&#34;: 1,
                    &#34;library&#34;: {
                        &#34;opType&#34;: 8,
                        &#34;mediaAgentName&#34;: self.media_agent,
                        &#34;libraryName&#34;: self._library_name,
                        &#34;mountPath&#34;: mount_path
                    },
                    &#34;libNewProp&#34;:{
                      &#34;maxDataToWriteMB&#34;: size
                    }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    def change_device_access_type(self, mountpath_id, device_id, device_controller_id, media_agent_id,
                                  device_access_type):
        &#34;&#34;&#34;
        To change device access type
            Args:
                mountpath_id (int)  -- Mount Path Id

                device_id (int)     -- Device Id

                device_controller_id (int) -- Device Controller Id

                media_agent_id (int)    --   Media Agent Id

                device_access_type (int)    --  Device access type
                                        Regular:
                                                Access type     Value
                                                Read              4
                                                Read and Write    6
                                                Preferred         8

                                        IP:
                                                Access type     Value
                                                Read             20
                                                Read/ Write      22

                                        Fibre Channel (FC)
                                                Access type     Value
                                                Read             36
                                                Read and Write   38

                                        iSCSi
                                                Access type     Value
                                                Read             132
                                                Read and Write   134
        &#34;&#34;&#34;

        if not all([isinstance(mountpath_id, int), isinstance(device_id, int), isinstance(device_controller_id, int),
                    isinstance(media_agent_id, int), isinstance(device_access_type, int)]):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
            &#34;EVGui_MMDevicePathInfoReq&#34;:
                {
                    &#34;mountpathId&#34;: mountpath_id,
                    &#34;infoList&#34;: {
                        &#34;accessType&#34;: device_access_type,
                        &#34;deviceId&#34;: device_id,
                        &#34;deviceControllerId&#34;: device_controller_id,
                        &#34;path&#34;: self.mount_path,
                        &#34;enabled&#34;: 1,
                        &#34;numWriters&#34;: -1,
                        &#34;opType&#34;: 2,
                        &#34;autoPickTransportType&#34;: 0,
                        &#34;protocolType&#34;: 679,
                        &#34;mediaAgent&#34;: {
                            &#34;id&#34;: media_agent_id
                        }
                    }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    def modify_cloud_access_type(self, mountpath_id, device_controller_id,
                                  device_access_type, enabled=True):
        &#34;&#34;&#34;
        To change device access type for cloud mount path
            Args:
                mountpath_id (int)  -- Mount Path Id

                device_controller_id (int) -- Device Controller Id

                device_access_type (int)    --  Device access type
                                        Possible values:
                                                Access type     Value
                                                Read              4
                                                Read and Write    6

                                        **by default preferred access (preferred = 8) will be set
        &#34;&#34;&#34;

        if not all([isinstance(mountpath_id, int), isinstance(device_controller_id, int),
                    isinstance(device_access_type, int)]):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        access = &#34;&#34;
        if device_access_type == 4:
            access = &#34;READ&#34;
        else:
            access = &#34;READ_AND_WRITE&#34;

        payload = {
            &#34;access&#34;: access,
            &#34;enable&#34;: enabled
        }

        EDIT_CLOUD_CONTROLLER = self._commcell_object._services[&#39;EDIT_CLOUD_CONTROLLER&#39;] % (mountpath_id, device_controller_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;, EDIT_CLOUD_CONTROLLER, payload)

        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json():
                    error_code = int(response.json().get(&#39;errorCode&#39;))
                    if error_code != 0:
                        error_message = response.json().get(&#39;errorMessage&#39;)
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            _stdout = &#39;Failed to modify cloud access type with error: \n [{0}]&#39;
            _stderr = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(_stderr))


    def update_device_controller(self, mountpath_id, device_id, device_controller_id, media_agent_id,
                                 device_access_type, **kwargs):
        &#34;&#34;&#34;
        To update device controller properties.
            Args:
                mountpath_id (int)  -- Mount Path Id

                device_id (int)     -- Device Id

                device_controller_id (int) -- Device Controller Id

                media_agent_id (int)    --   Media Agent Id

                device_access_type (int)    --  Device access type
                                        Regular:
                                                Access type     Value
                                                Read              4
                                                Read and Write    6
                                                Preferred         8

                                        IP:
                                                Access type     Value
                                                Read             20
                                                Read/ Write      22

                                        Fibre Channel (FC)
                                                Access type     Value
                                                Read             36
                                                Read and Write   38

                                        iSCSi
                                                Access type     Value
                                                Read             132
                                                Read and Write   134

                **kwargs  (dict)  --  Optional arguments

                        Available kwargs Options:

                        username     (str)     -- username for the device
                                                  ** in case of cloud library username needs to be in the following format
                                                  ** &lt;vendorURL&gt;//__CVCRED__

                        password     (str)     -- password for the device
                                                  ** if credential name is used then use a dummy password

                        credential_name (str)  -- credential name as in the credential manager

                        path                   -- accessing path for media agent local / UNC

        &#34;&#34;&#34;

        if not all([isinstance(mountpath_id, int), isinstance(device_id, int), isinstance(device_controller_id, int),
                    isinstance(media_agent_id, int), isinstance(device_access_type, int)]):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        username = kwargs.get(&#34;username&#34;, &#34;&#34;)
        password = kwargs.get(&#34;password&#34;, &#34;&#34;)
        credential_name = kwargs.get(&#34;credential_name&#34;, &#34;&#34;)
        path = kwargs.get(&#34;path&#34;, self.mount_path)
        enabled = 1
        if not kwargs.get(&#39;enabled&#39;, True):
            enabled = 0
        request_json = {
            &#34;EVGui_MMDevicePathInfoReq&#34;:
                {
                    &#34;mountpathId&#34;: mountpath_id,
                    &#34;infoList&#34;: {
                        &#34;password&#34;: password,
                        &#34;accessType&#34;: device_access_type,
                        &#34;deviceId&#34;: device_id,
                        &#34;deviceControllerId&#34;: device_controller_id,
                        &#34;path&#34;: path,
                        &#34;enabled&#34;: enabled,
                        &#34;numWriters&#34;: -1,
                        &#34;opType&#34;: 2,
                        &#34;autoPickTransportType&#34;: 0,
                        &#34;protocolType&#34;: 679,
                        &#34;mediaAgent&#34;: {
                            &#34;id&#34;: media_agent_id
                        },
                        &#34;savedCredential&#34;: {
                            &#34;credentialName&#34;: credential_name
                        },
                        &#34;userName&#34;: username
                    }
                }
        }

        self._commcell_object.qoperation_execute(request_json)


    def verify_media(self, media_name, location_id):
        &#34;&#34;&#34;
            To perform verify media operation on media
            Args:
                media_name  --  Barcode of the media

                location_id --  Slot Id of the media on the library
        &#34;&#34;&#34;

        if not (isinstance(media_name, str) and
                isinstance(location_id,int)):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_xml = f&#34;&#34;&#34;&lt;TMMsg_CreateTaskReq&gt;
                            &lt;taskInfo&gt;
                                &lt;task taskType=&#34;1&#34; /&gt;
                                &lt;subTasks subTaskOperation=&#34;1&#34;&gt;
                                    &lt;subTask operationType=&#34;4005&#34; subTaskType=&#34;1&#34;/&gt;
                                    &lt;options&gt;
                                        &lt;adminOpts&gt;
                                            &lt;libraryOption operation=&#34;6&#34;&gt;
                                                &lt;library _type_=&#34;9&#34; libraryName=&#34;{self.library_name}&#34;/&gt;
                                                &lt;media _type_=&#34;46&#34; mediaName=&#34;{media_name}&#34;/&gt;
                                                &lt;verifyMedia&gt;
                                                    &lt;location _type_=&#34;53&#34; locationId=&#34;{location_id}&#34;/&gt;
                                                &lt;/verifyMedia&gt;
                                            &lt;/libraryOption&gt;
                                        &lt;/adminOpts&gt;
                                    &lt;/options&gt;
                                &lt;/subTasks&gt;
                            &lt;/taskInfo&gt;
                        &lt;/TMMsg_CreateTaskReq&gt;&#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;CREATE_TASK&#39;], request_xml
        )

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    from cvpysdk.job import Job
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                if &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;,o_str)

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    @property
    def free_space(self):
        &#34;&#34;&#34;Returns free space&#34;&#34;&#34;
        return self._library_properties.get(&#39;magLibSummary&#39;, {}).get(&#39;totalFreeSpace&#39;).strip()

    @property
    def mountpath_usage(self):
        &#34;&#34;&#34;Returns mount path usage&#34;&#34;&#34;
        return self._library_properties.get(&#39;magLibSummary&#39;, {}).get(&#39;mountPathUsage&#39;).strip()

    @mountpath_usage.setter
    def mountpath_usage(self, value):
        &#34;&#34;&#34;
            Sets mount path usage on the library
            Args:
                value  (str)   -- option needed to set for mountpath usage
                                    value: &#39;SPILL_AND_FILL&#39; or &#39;FILL_AND_SPILL&#39;
        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if value == &#39;SPILL_AND_FILL&#39;:
            value = 1
        elif value == &#39;FILL_AND_SPILL&#39;:
            value = 2
        else:
            raise SDKException(&#39;Storage&#39;, &#39;110&#39;)

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;library&#34;: {
                            &#34;opType&#34;: 32,
                            &#34;libraryName&#34;: self.library_name
                        },
                    &#34;libNewProp&#34;: {
                            &#34;mountPathUsage&#34;: value
                        }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    def set_mountpath_preferred_on_mediaagent(self, value):
        &#34;&#34;&#34;Sets select preferred mountPath according to mediaagent setting on the library.
            Args:
                value    (bool) --  preferMountPathAccordingToMA value to be set on library (True/False)

            Raises:
                SDKException:
                    if failed to update

                    if the type of value input is not correct

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;isConfigRequired&#34;: 1,
                    &#34;library&#34;: {
                            &#34;opType&#34;: 32,
                            &#34;libraryName&#34;: self.library_name
                        },
                    &#34;libNewProp&#34;: {
                            &#34;preferMountPathAccordingToMA&#34;: int(value)
                        }
                }
        }
        self._commcell_object.qoperation_execute(request_json)

    @property
    def media_agents_associated(self):
        &#34;&#34;&#34; Returns the media agents associated with the disk library &#34;&#34;&#34;
        mount_paths = self._library_properties.get(&#39;MountPathList&#39;)
        media_agents = [mount_path.get(&#39;mountPathName&#39;).split(&#39;[&#39;)[1].split(&#39;]&#39;)[0] for mount_path in mount_paths if &#34;mountPathName&#34; in mount_path]
        return list(set(media_agents))

    @property
    def name(self):
        &#34;&#34;&#34;Returns library display name.&#34;&#34;&#34;
        return self._library_properties[&#39;MountPathList&#39;][0][&#39;mountPathSummary&#39;][&#39;libraryName&#39;]

    @property
    def library_name(self):
        &#34;&#34;&#34;Treats the library name as a read-only attribute.&#34;&#34;&#34;
        return self._library_name

    @property
    def library_id(self):
        &#34;&#34;&#34;Treats the library id as a read-only attribute.&#34;&#34;&#34;
        return self._library_id

    @property
    def library_properties(self):
        &#34;&#34;&#34;Returns the dictionary consisting of the full properties of the library&#34;&#34;&#34;
        self.refresh()
        return self._library_properties

    @property
    def advanced_library_properties(self):
        &#34;&#34;&#34;Returns the dictionary consisting of the advanced properties of the library&#34;&#34;&#34;
        self.refresh()
        return self._advanced_library_properties

    @property
    def mount_path(self):
        &#34;&#34;&#34;Treats the library id as a read-only attribute.&#34;&#34;&#34;
        return self.mountpath

    @mount_path.setter
    def mount_path(self, mount_path):
        &#34;&#34;&#34; setter for mountpath&#34;&#34;&#34;
        self.mountpath = mount_path

    @property
    def media_agent(self):
        &#34;&#34;&#34;Treats the library id as a read-only attribute.&#34;&#34;&#34;
        return self.mediaagent

    @media_agent.setter
    def media_agent(self, media_agent):
        &#34;&#34;&#34;setter for media agent&#34;&#34;&#34;
        self.mediaagent = media_agent

    def share_mount_path(self, new_media_agent, new_mount_path, **kwargs):
        &#34;&#34;&#34;
        Method to share a mountpath

        Args:

            new_media_agent (str)   -- Media agent which is accessing the shared mount path

            new_mount_path  (int)   -- Mount path to be shared

                kwargs  (dict)  --  Optional arguments

                    Available kwargs Options:

                        media_agent     (str)   -- Media agent associated with library

                        library_name    (str)   -- Name of the library which has the mount path

                        mount_path      (str)   -- Mount path to be shared

                        access_type     (int)   -- The access type of the shared mount path

                                                    Read Device Access = 4

                                                    Read/ Write Device Access = 6

                                                    Read Device Access with Preferred = 12

                                                    Read/Write Device Access with Preferred = 14

                                                    Data Server - IP Read = 20

                                                    Data Server - IP Read/ Write = 22

                                                    Data Server - FC Read = 36

                                                    Data Server - FC Read/ Write = 38

                                                    Data Server - iSCSI Read = 132

                                                    Data Server - iSCSI Read/ Write = 134

                                                    Note: For the Data Server device access type,
                                                          enter the local path provided in the library/mountPath
                                                          parameter in the libNewProp/mountPath parameter also.


                        username        (str)   -- Username to access the mount path, if UNC

                        password        (str)   -- Password to access the mount path, if UNC

                        credential_name  (str)  -- credential name for the credential manager
                                                   ** For cloud if you use credential_name update the username parameter
                                                   ** in the format of &#34;&lt;vendorURL&gt;//__CVCRED__&#34;
                                                   ** For example, &#34;s3.amazonaws.com//__CVCRED__&#34;
                                                   ** Update a dummy value for password parameter

        Returns:
            None

        Raises
            Exception:
                - if any of the parameter&#39;s dataype is invalid

                - if API response error code is not 0

                - if response is empty

                - if response code is not as expected
        &#34;&#34;&#34;

        media_agent = kwargs.get(&#39;media_agent&#39;, self.mediaagent)
        library_name = kwargs.get(&#39;library_name&#39;, self.library_name)
        mount_path = kwargs.get(&#39;mount_path&#39;, self.mountpath)
        access_type = kwargs.get(&#39;access_type&#39;, 22)
        username = kwargs.get(&#39;username&#39;, &#39;&#39;)
        password = kwargs.get(&#39;password&#39;, &#39;&#39;)
        credential_name = kwargs.get(&#39;credential_name&#39;, &#39;&#39;)

        self._EXECUTE = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]
        self.library = {
            &#34;opType&#34;: 64,
            &#34;mediaAgentName&#34;: media_agent,
            &#34;libraryName&#34;: library_name,
            &#34;mountPath&#34;: &#34;%s&#34; %
                         mount_path}
        self.lib_new_prop = {
            &#34;deviceAccessType&#34;: access_type,
            &#34;password&#34;: password,
            &#34;loginName&#34;: username,
            &#34;mediaAgentName&#34;: new_media_agent,
            &#34;mountPath&#34;: &#34;{}&#34;.format(new_mount_path),
            &#34;proxyPassword&#34;: &#34;&#34;,
            &#34;savedCredential&#34;: {
                &#34;credentialName&#34;: credential_name
            }
        }
        request_json = {
            &#34;EVGui_ConfigureStorageLibraryReq&#34;:
                {
                    &#34;library&#34;: self.library,
                    &#34;libNewProp&#34;: self.lib_new_prop
                }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._EXECUTE, request_json
        )
        if flag:
            response_string = self._commcell_object._update_response_(response.text)
            if response.json():
                if &#34;library&#34; in response.json():
                    response = response.json()[&#34;library&#34;]
                    return response
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)
            else:

                raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.storage.DiskLibrary.advanced_library_properties"><code class="name">var <span class="ident">advanced_library_properties</span></code></dt>
<dd>
<div class="desc"><p>Returns the dictionary consisting of the advanced properties of the library</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2318-L2322" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def advanced_library_properties(self):
    &#34;&#34;&#34;Returns the dictionary consisting of the advanced properties of the library&#34;&#34;&#34;
    self.refresh()
    return self._advanced_library_properties</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.free_space"><code class="name">var <span class="ident">free_space</span></code></dt>
<dd>
<div class="desc"><p>Returns free space</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2218-L2221" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def free_space(self):
    &#34;&#34;&#34;Returns free space&#34;&#34;&#34;
    return self._library_properties.get(&#39;magLibSummary&#39;, {}).get(&#39;totalFreeSpace&#39;).strip()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.library_id"><code class="name">var <span class="ident">library_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the library id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2307-L2310" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def library_id(self):
    &#34;&#34;&#34;Treats the library id as a read-only attribute.&#34;&#34;&#34;
    return self._library_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.library_name"><code class="name">var <span class="ident">library_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the library name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2302-L2305" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def library_name(self):
    &#34;&#34;&#34;Treats the library name as a read-only attribute.&#34;&#34;&#34;
    return self._library_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.library_properties"><code class="name">var <span class="ident">library_properties</span></code></dt>
<dd>
<div class="desc"><p>Returns the dictionary consisting of the full properties of the library</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2312-L2316" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def library_properties(self):
    &#34;&#34;&#34;Returns the dictionary consisting of the full properties of the library&#34;&#34;&#34;
    self.refresh()
    return self._library_properties</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.media_agent"><code class="name">var <span class="ident">media_agent</span></code></dt>
<dd>
<div class="desc"><p>Treats the library id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2334-L2337" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def media_agent(self):
    &#34;&#34;&#34;Treats the library id as a read-only attribute.&#34;&#34;&#34;
    return self.mediaagent</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.media_agents_associated"><code class="name">var <span class="ident">media_agents_associated</span></code></dt>
<dd>
<div class="desc"><p>Returns the media agents associated with the disk library</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2290-L2295" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def media_agents_associated(self):
    &#34;&#34;&#34; Returns the media agents associated with the disk library &#34;&#34;&#34;
    mount_paths = self._library_properties.get(&#39;MountPathList&#39;)
    media_agents = [mount_path.get(&#39;mountPathName&#39;).split(&#39;[&#39;)[1].split(&#39;]&#39;)[0] for mount_path in mount_paths if &#34;mountPathName&#34; in mount_path]
    return list(set(media_agents))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.mount_path"><code class="name">var <span class="ident">mount_path</span></code></dt>
<dd>
<div class="desc"><p>Treats the library id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2324-L2327" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def mount_path(self):
    &#34;&#34;&#34;Treats the library id as a read-only attribute.&#34;&#34;&#34;
    return self.mountpath</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.mountpath_usage"><code class="name">var <span class="ident">mountpath_usage</span></code></dt>
<dd>
<div class="desc"><p>Returns mount path usage</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2223-L2226" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def mountpath_usage(self):
    &#34;&#34;&#34;Returns mount path usage&#34;&#34;&#34;
    return self._library_properties.get(&#39;magLibSummary&#39;, {}).get(&#39;mountPathUsage&#39;).strip()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Returns library display name.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2297-L2300" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Returns library display name.&#34;&#34;&#34;
    return self._library_properties[&#39;MountPathList&#39;][0][&#39;mountPathSummary&#39;][&#39;libraryName&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage.DiskLibrary.add_cloud_mount_path"><code class="name flex">
<span>def <span class="ident">add_cloud_mount_path</span></span>(<span>self, mount_path, media_agent, username, password, server_type, saved_credential_name='')</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a mount path to the cloud library</p>
<h2 id="args">Args</h2>
<p>mount_path
(str)
&ndash; cloud container or bucket.</p>
<p>media_agent (str)
&ndash; MediaAgent on which mountpath exists</p>
<dl>
<dt>username
(str)
&ndash; Username to access the mount path in the format <Service Host>//<Account Name></dt>
<dt><strong><code>Eg</code></strong></dt>
<dd>s3.us-west-1.amazonaws.com//MyAccessKeyID. For more information refer <a href="http://documentation.commvault.com/commvault/v11/article?p=97863.htm.">http://documentation.commvault.com/commvault/v11/article?p=97863.htm.</a></dd>
</dl>
<p>password
(str)
&ndash; Password to access the mount path</p>
<p>server_type
(int)
&ndash; provide cloud library server type
Eg: 3-Microsoft Azure Storage . For more information refer <a href="http://documentation.commvault.com/commvault/v11/article?p=97863.htm.">http://documentation.commvault.com/commvault/v11/article?p=97863.htm.</a></p>
<p>saved_credential_name
(str)
&ndash;
name of the saved credential
default: ""</p>
<h2 id="returns">Returns</h2>
<p>None
Raises
Exception:
- if mountpath or mediaagent or username or password or servertype arguments dataype is invalid</p>
<pre><code>    - if servertype input data is incorrect

    - if API response error code is not 0

    - if response is empty

    - if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1622-L1701" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_cloud_mount_path(self, mount_path, media_agent, username, password, server_type, saved_credential_name=&#34;&#34;):
    &#34;&#34;&#34; Adds a mount path to the cloud library

    Args:
        mount_path  (str)   -- cloud container or bucket.

        media_agent (str)   -- MediaAgent on which mountpath exists

        username    (str)   -- Username to access the mount path in the format &lt;Service Host&gt;//&lt;Account Name&gt;
        Eg: s3.us-west-1.amazonaws.com//MyAccessKeyID. For more information refer http://documentation.commvault.com/commvault/v11/article?p=97863.htm.

        password    (str)   -- Password to access the mount path

        server_type  (int)   -- provide cloud library server type
                                Eg: 3-Microsoft Azure Storage . For more information refer http://documentation.commvault.com/commvault/v11/article?p=97863.htm.

        saved_credential_name   (str)   --  name of the saved credential
            default: &#34;&#34;

    Returns:
        None

    Raises
        Exception:
            - if mountpath or mediaagent or username or password or servertype arguments dataype is invalid

            - if servertype input data is incorrect

            - if API response error code is not 0

            - if response is empty

            - if response code is not as expected
        &#34;&#34;&#34;

    if not (isinstance(mount_path, str) or isinstance(media_agent, str)
            or isinstance(username, str) or isinstance(password, str)
            or isinstance(server_type, int)):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    request_json = {
        &#34;isConfigRequired&#34;: 1,
        &#34;library&#34;: {
            &#34;opType&#34;: 4,
            &#34;isCloud&#34;: 1,
            &#34;mediaAgentName&#34;: media_agent,
            &#34;libraryName&#34;: self._library_name,
            &#34;mountPath&#34;: mount_path,
            &#34;loginName&#34;: username,
            &#34;password&#34;: b64encode(password.encode()).decode(),
            &#34;serverType&#34;: server_type,
            &#34;savedCredential&#34;: {
                &#34;credentialName&#34;: saved_credential_name
            }
        }
    }

    exec_command = self._commcell_object._services[&#39;LIBRARY&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, exec_command, request_json
    )

    if flag:
        if response.json():
            if &#39;library&#39; in response.json():
                _response = response.json()[&#39;library&#39;]

                if &#39;errorCode&#39; in _response:
                    if _response[&#39;errorCode&#39;] != 0:
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        _stdout = &#39;Failed to add mount path [{0}] for library [{1}] with error: \n [{2}]&#39;
        _stderr = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(mount_path,
                                                             self._library_name,
                                                             _stderr))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.add_mount_path"><code class="name flex">
<span>def <span class="ident">add_mount_path</span></span>(<span>self, mount_path, media_agent, username='', password='')</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a mount path [local/remote] to the disk library</p>
<h2 id="args">Args</h2>
<p>mount_path
(str)
&ndash; Mount path which needs to be added to disklibrary.
This could be a local or remote mount path on mediaagent</p>
<p>media_agent (str)
&ndash; MediaAgent on which mountpath exists</p>
<p>username
(str)
&ndash; Username to access the mount path</p>
<p>password
(str)
&ndash; Password to access the mount path</p>
<h2 id="returns">Returns</h2>
<p>None
Raises
Exception:
- if mountpath and mediaagent datatype is invalid</p>
<pre><code>    - if API response error code is not 0

    - if response is empty

    - if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1835-L1902" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_mount_path(self, mount_path, media_agent, username=&#39;&#39;, password=&#39;&#39;):
    &#34;&#34;&#34; Adds a mount path [local/remote] to the disk library

    Args:
        mount_path  (str)   -- Mount path which needs to be added to disklibrary.
                              This could be a local or remote mount path on mediaagent

        media_agent (str)   -- MediaAgent on which mountpath exists

        username    (str)   -- Username to access the mount path

        password    (str)   -- Password to access the mount path

    Returns:
        None

    Raises
        Exception:
            - if mountpath and mediaagent datatype is invalid

            - if API response error code is not 0

            - if response is empty

            - if response code is not as expected
        &#34;&#34;&#34;

    if not isinstance(mount_path, str) or not isinstance(media_agent, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    request_json = {
        &#34;EVGui_ConfigureStorageLibraryReq&#34;:
            {
                &#34;isConfigRequired&#34;: 1,
                &#34;library&#34;: {
                    &#34;opType&#34;: 4,
                    &#34;mediaAgentName&#34;: media_agent,
                    &#34;libraryName&#34;: self._library_name,
                    &#34;mountPath&#34;: mount_path,
                    &#34;loginName&#34;: username,
                    &#34;password&#34;: b64encode(password.encode()).decode(),
                }
            }
    }

    exec_command = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]

    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;,
                                                                        exec_command,
                                                                        request_json)
    if flag:
        if response.json():
            if &#39;library&#39; in response.json():
                _response = response.json()[&#39;library&#39;]

                if &#39;errorCode&#39; in _response:
                    if _response[&#39;errorCode&#39;] != 0:
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        _stdout = &#39;Failed to add mount path [{0}] for library [{1}] with error: \n [{2}]&#39;
        _stderr = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(mount_path,
                                                             self._library_name,
                                                             _stderr))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.add_storage_accelerator_credential"><code class="name flex">
<span>def <span class="ident">add_storage_accelerator_credential</span></span>(<span>self, mount_path, saved_credential='', reset=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Add storage accelerator credential to the cloud mount path</p>
<h2 id="args">Args</h2>
<p>mount_path
(str)
&ndash; Mount path to which secondary credentials needs to be added</p>
<p>saved_credential (str)
&ndash; saved credential name
default: ""</p>
<p>reset
(bool)
&ndash; reset storage accelerator credential
default: False
Raises
Exception:
- if mountpath datatype is invalid</p>
<pre><code>    - if API response error code is not 0

    - if response is empty

    - if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1703-L1764" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_storage_accelerator_credential(self, mount_path, saved_credential=&#34;&#34;, reset=False):
    &#34;&#34;&#34; Add storage accelerator credential to the cloud mount path

    Args:
        mount_path  (str)   -- Mount path to which secondary credentials needs to be added

        saved_credential (str)   -- saved credential name
            default: &#34;&#34;

        reset    (bool)   -- reset storage accelerator credential
            default: False

    Raises
        Exception:
            - if mountpath datatype is invalid

            - if API response error code is not 0

            - if response is empty

            - if response code is not as expected
        &#34;&#34;&#34;

    if not isinstance(mount_path, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    request_json = {
                    &#34;library&#34;: {
                        &#34;mediaAgentName&#34;: self.media_agent,
                        &#34;libraryName&#34;: self._library_name,
                        &#34;mountPath&#34;: mount_path,
                        &#34;opType&#34;: 8
                    },
                    &#34;libNewProp&#34;: {
                        &#34;secondaryCredential&#34;: {
                            &#34;credentialName&#34;: saved_credential
                        },
                        &#34;resetSecondaryCredentials&#34;: reset
                    }
                }

    exec_command = self._commcell_object._services[&#39;LIBRARY&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, exec_command, request_json
    )

    if flag:
        if response.json():
            if &#39;library&#39; in response.json():
                _response = response.json()[&#39;library&#39;]

                if &#39;errorCode&#39; in _response:
                    if _response[&#39;errorCode&#39;] != 0:
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, _response[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        _stdout = &#39;Failed to add storage accelerator credential with error: \n [{0}]&#39;
        _stderr = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(_stderr))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.change_device_access_type"><code class="name flex">
<span>def <span class="ident">change_device_access_type</span></span>(<span>self, mountpath_id, device_id, device_controller_id, media_agent_id, device_access_type)</span>
</code></dt>
<dd>
<div class="desc"><p>To change device access type
Args:
mountpath_id (int)
&ndash; Mount Path Id</p>
<pre><code>    device_id (int)     -- Device Id

    device_controller_id (int) -- Device Controller Id

    media_agent_id (int)    --   Media Agent Id

    device_access_type (int)    --  Device access type
                            Regular:
                                    Access type     Value
                                    Read              4
                                    Read and Write    6
                                    Preferred         8

                            IP:
                                    Access type     Value
                                    Read             20
                                    Read/ Write      22

                            Fibre Channel (FC)
                                    Access type     Value
                                    Read             36
                                    Read and Write   38

                            iSCSi
                                    Access type     Value
                                    Read             132
                                    Read and Write   134
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1956-L2016" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def change_device_access_type(self, mountpath_id, device_id, device_controller_id, media_agent_id,
                              device_access_type):
    &#34;&#34;&#34;
    To change device access type
        Args:
            mountpath_id (int)  -- Mount Path Id

            device_id (int)     -- Device Id

            device_controller_id (int) -- Device Controller Id

            media_agent_id (int)    --   Media Agent Id

            device_access_type (int)    --  Device access type
                                    Regular:
                                            Access type     Value
                                            Read              4
                                            Read and Write    6
                                            Preferred         8

                                    IP:
                                            Access type     Value
                                            Read             20
                                            Read/ Write      22

                                    Fibre Channel (FC)
                                            Access type     Value
                                            Read             36
                                            Read and Write   38

                                    iSCSi
                                            Access type     Value
                                            Read             132
                                            Read and Write   134
    &#34;&#34;&#34;

    if not all([isinstance(mountpath_id, int), isinstance(device_id, int), isinstance(device_controller_id, int),
                isinstance(media_agent_id, int), isinstance(device_access_type, int)]):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    request_json = {
        &#34;EVGui_MMDevicePathInfoReq&#34;:
            {
                &#34;mountpathId&#34;: mountpath_id,
                &#34;infoList&#34;: {
                    &#34;accessType&#34;: device_access_type,
                    &#34;deviceId&#34;: device_id,
                    &#34;deviceControllerId&#34;: device_controller_id,
                    &#34;path&#34;: self.mount_path,
                    &#34;enabled&#34;: 1,
                    &#34;numWriters&#34;: -1,
                    &#34;opType&#34;: 2,
                    &#34;autoPickTransportType&#34;: 0,
                    &#34;protocolType&#34;: 679,
                    &#34;mediaAgent&#34;: {
                        &#34;id&#34;: media_agent_id
                    }
                }
            }
    }
    self._commcell_object.qoperation_execute(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.modify_cloud_access_type"><code class="name flex">
<span>def <span class="ident">modify_cloud_access_type</span></span>(<span>self, mountpath_id, device_controller_id, device_access_type, enabled=True)</span>
</code></dt>
<dd>
<div class="desc"><p>To change device access type for cloud mount path
Args:
mountpath_id (int)
&ndash; Mount Path Id</p>
<pre><code>    device_controller_id (int) -- Device Controller Id

    device_access_type (int)    --  Device access type
                            Possible values:
                                    Access type     Value
                                    Read              4
                                    Read and Write    6

                            **by default preferred access (preferred = 8) will be set
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2018-L2067" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_cloud_access_type(self, mountpath_id, device_controller_id,
                              device_access_type, enabled=True):
    &#34;&#34;&#34;
    To change device access type for cloud mount path
        Args:
            mountpath_id (int)  -- Mount Path Id

            device_controller_id (int) -- Device Controller Id

            device_access_type (int)    --  Device access type
                                    Possible values:
                                            Access type     Value
                                            Read              4
                                            Read and Write    6

                                    **by default preferred access (preferred = 8) will be set
    &#34;&#34;&#34;

    if not all([isinstance(mountpath_id, int), isinstance(device_controller_id, int),
                isinstance(device_access_type, int)]):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    access = &#34;&#34;
    if device_access_type == 4:
        access = &#34;READ&#34;
    else:
        access = &#34;READ_AND_WRITE&#34;

    payload = {
        &#34;access&#34;: access,
        &#34;enable&#34;: enabled
    }

    EDIT_CLOUD_CONTROLLER = self._commcell_object._services[&#39;EDIT_CLOUD_CONTROLLER&#39;] % (mountpath_id, device_controller_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;, EDIT_CLOUD_CONTROLLER, payload)

    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json():
                error_code = int(response.json().get(&#39;errorCode&#39;))
                if error_code != 0:
                    error_message = response.json().get(&#39;errorMessage&#39;)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        _stdout = &#39;Failed to modify cloud access type with error: \n [{0}]&#39;
        _stderr = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, _stdout.format(_stderr))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.move_mountpath"><code class="name flex">
<span>def <span class="ident">move_mountpath</span></span>(<span>self, mountpath_id, source_device_path, source_mediaagent_id, target_device_path, target_mediaagent_id, target_device_id=0)</span>
</code></dt>
<dd>
<div class="desc"><p>To perform move mountpath operation</p>
<h2 id="args">Args</h2>
<p>mountpath_id
(int)
&ndash;
Mountpath Id that need to be moved.</p>
<p>source_device_path (str)
&ndash; Present Mountpath location</p>
<p>source_mediaagent_id
(int)
&ndash; MediaAgent Id on which present mountpath exists</p>
<p>target_device_path
(str)
&ndash; New Mountpath location</p>
<p>target_mediaagent_id
(int)
&ndash; MediaAgent Id on which new mountpath exists</p>
<p>target_device_id
(int)
&ndash;
Device Id of target path if already exists</p>
<h2 id="returns">Returns</h2>
<p>instance of the Job class for this move mountpath job
Raises
Exception:
- if argument datatype is invalid</p>
<pre><code>    - if API response error code is not 0

    - if response is empty

    - if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1453-L1550" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def move_mountpath(self, mountpath_id, source_device_path,
                   source_mediaagent_id, target_device_path, target_mediaagent_id,
                   target_device_id=0):

    &#34;&#34;&#34; To perform move mountpath operation
    Args:
        mountpath_id  (int)   --  Mountpath Id that need to be moved.

        source_device_path (str)   -- Present Mountpath location

        source_mediaagent_id    (int)   -- MediaAgent Id on which present mountpath exists

        target_device_path    (str)   -- New Mountpath location

        target_mediaagent_id    (int)   -- MediaAgent Id on which new mountpath exists

        target_device_id        (int)   --  Device Id of target path if already exists

    Returns:
        instance of the Job class for this move mountpath job

    Raises
        Exception:
            - if argument datatype is invalid

            - if API response error code is not 0

            - if response is empty

            - if response code is not as expected
    &#34;&#34;&#34;

    if not (isinstance(mountpath_id, int) and
            isinstance(source_mediaagent_id, int) and
            isinstance(target_mediaagent_id, int) and
            (isinstance(target_device_path, str) or target_device_id &gt; 0) and
            isinstance(source_device_path, str)):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    MOVE_MOUNTPATH_DETAILS = self._commcell_object._services[&#39;GET_MOVE_MOUNTPATH_DETAILS&#39;] % (mountpath_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, MOVE_MOUNTPATH_DETAILS)

    source_device_id = None

    if flag:
        if response.json():
            if &#39;sourceDeviceInfo&#39; in response.json():
                source_device_id = response.json().get(&#39;sourceDeviceInfo&#39;).get(&#39;deviceId&#39;, None)
            if not source_device_id:
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;Failed to get details of the mountpath for move&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    request_json = {
        &#39;MPMoveOption&#39;: {
            &#39;mountPathMoveList&#39;: [{
                &#39;sourceDeviceId&#39;: source_device_id,
                &#39;sourcemediaAgentId&#39;: source_mediaagent_id,
                &#39;targetMediaAgentId&#39;: target_mediaagent_id,
            }]
        }
    }
    if target_device_id &gt; 0:
        request_json[&#39;MPMoveOption&#39;][&#39;mountPathMoveList&#39;][0][&#39;targetDeviceId&#39;] = target_device_id
    else:
        request_json[&#39;MPMoveOption&#39;][&#39;mountPathMoveList&#39;][0][&#39;targetDevicePath&#39;] = target_device_path

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._commcell_object._services[&#39;MOVE_MOUNTPATH&#39;], request_json)

    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                if len(response.json()[&#39;jobIds&#39;]) == 1:
                    from cvpysdk.job import Job
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
                else:
                    from cvpysdk.job import Job
                    mp_move_job_list = []
                    for job_id in response.json()[&#39;jobIds&#39;]:
                        mp_move_job_list.append(Job(self._commcell_object, job_id))
                    return mp_move_job_list

            if &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;Commcell&#39;, &#39;105&#39;, o_str)

            else:
                raise SDKException(&#39;Commcell&#39;, &#39;105&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of this disk library.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1830-L1833" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of this disk library.&#34;&#34;&#34;
    self._library_properties = self._get_library_properties()
    self._advanced_library_properties = self._get_advanced_library_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.set_max_data_to_write_on_mount_path"><code class="name flex">
<span>def <span class="ident">set_max_data_to_write_on_mount_path</span></span>(<span>self, mount_path, size)</span>
</code></dt>
<dd>
<div class="desc"><p>To set max data to write on the mountpath</p>
<h2 id="args">Args</h2>
<p>mount_path (str)
&ndash;
Folder path for this mount path.</p>
<p>size (int)
&ndash;
max data to be consumed in MB</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1930-L1954" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_max_data_to_write_on_mount_path(self, mount_path, size):
    &#34;&#34;&#34;
        To set max data to write on the mountpath
        Args:
            mount_path (str)    --  Folder path for this mount path.

            size (int)          --  max data to be consumed in MB
    &#34;&#34;&#34;

    request_json = {
        &#34;EVGui_ConfigureStorageLibraryReq&#34;:
            {
                &#34;isConfigRequired&#34;: 1,
                &#34;library&#34;: {
                    &#34;opType&#34;: 8,
                    &#34;mediaAgentName&#34;: self.media_agent,
                    &#34;libraryName&#34;: self._library_name,
                    &#34;mountPath&#34;: mount_path
                },
                &#34;libNewProp&#34;:{
                  &#34;maxDataToWriteMB&#34;: size
                }
            }
    }
    self._commcell_object.qoperation_execute(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.set_mountpath_preferred_on_mediaagent"><code class="name flex">
<span>def <span class="ident">set_mountpath_preferred_on_mediaagent</span></span>(<span>self, value)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets select preferred mountPath according to mediaagent setting on the library.</p>
<h2 id="args">Args</h2>
<p>value
(bool) &ndash;
preferMountPathAccordingToMA value to be set on library (True/False)</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to update</p>
<pre><code>if the type of value input is not correct
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2260-L2288" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_mountpath_preferred_on_mediaagent(self, value):
    &#34;&#34;&#34;Sets select preferred mountPath according to mediaagent setting on the library.
        Args:
            value    (bool) --  preferMountPathAccordingToMA value to be set on library (True/False)

        Raises:
            SDKException:
                if failed to update

                if the type of value input is not correct

    &#34;&#34;&#34;
    if not isinstance(value, bool):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    request_json = {
        &#34;EVGui_ConfigureStorageLibraryReq&#34;:
            {
                &#34;isConfigRequired&#34;: 1,
                &#34;library&#34;: {
                        &#34;opType&#34;: 32,
                        &#34;libraryName&#34;: self.library_name
                    },
                &#34;libNewProp&#34;: {
                        &#34;preferMountPathAccordingToMA&#34;: int(value)
                    }
            }
    }
    self._commcell_object.qoperation_execute(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.set_mountpath_reserve_space"><code class="name flex">
<span>def <span class="ident">set_mountpath_reserve_space</span></span>(<span>self, mount_path, size)</span>
</code></dt>
<dd>
<div class="desc"><p>To set reserve space on the mountpath</p>
<h2 id="args">Args</h2>
<p>mount_path (str)
&ndash;
Mountpath</p>
<p>size (int)
&ndash;
reserve space to be set in MB</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1904-L1928" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_mountpath_reserve_space(self, mount_path, size):
    &#34;&#34;&#34;
        To set reserve space on the mountpath
        Args:
            mount_path (str)    --  Mountpath

            size (int)          --  reserve space to be set in MB
    &#34;&#34;&#34;

    request_json = {
        &#34;EVGui_ConfigureStorageLibraryReq&#34;:
            {
                &#34;isConfigRequired&#34;: 1,
                &#34;library&#34;: {
                    &#34;opType&#34;: 8,
                    &#34;mediaAgentName&#34;: self.media_agent,
                    &#34;libraryName&#34;: self._library_name,
                    &#34;mountPath&#34;: mount_path
                },
                &#34;libNewProp&#34;:{
                  &#34;reserveSpaceInMB&#34;: size
                }
            }
    }
    self._commcell_object.qoperation_execute(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.share_mount_path"><code class="name flex">
<span>def <span class="ident">share_mount_path</span></span>(<span>self, new_media_agent, new_mount_path, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to share a mountpath</p>
<h2 id="args">Args</h2>
<p>new_media_agent (str)
&ndash; Media agent which is accessing the shared mount path</p>
<p>new_mount_path
(int)
&ndash; Mount path to be shared</p>
<pre><code>kwargs  (dict)  --  Optional arguments

    Available kwargs Options:

        media_agent     (str)   -- Media agent associated with library

        library_name    (str)   -- Name of the library which has the mount path

        mount_path      (str)   -- Mount path to be shared

        access_type     (int)   -- The access type of the shared mount path

                                    Read Device Access = 4

                                    Read/ Write Device Access = 6

                                    Read Device Access with Preferred = 12

                                    Read/Write Device Access with Preferred = 14

                                    Data Server - IP Read = 20

                                    Data Server - IP Read/ Write = 22

                                    Data Server - FC Read = 36

                                    Data Server - FC Read/ Write = 38

                                    Data Server - iSCSI Read = 132

                                    Data Server - iSCSI Read/ Write = 134

                                    Note: For the Data Server device access type,
                                          enter the local path provided in the library/mountPath
                                          parameter in the libNewProp/mountPath parameter also.


        username        (str)   -- Username to access the mount path, if UNC

        password        (str)   -- Password to access the mount path, if UNC

        credential_name  (str)  -- credential name for the credential manager
                                   ** For cloud if you use credential_name update the username parameter
                                   ** in the format of "&lt;vendorURL&gt;//__CVCRED__"
                                   ** For example, "s3.amazonaws.com//__CVCRED__"
                                   ** Update a dummy value for password parameter
</code></pre>
<h2 id="returns">Returns</h2>
<p>None
Raises
Exception:
- if any of the parameter's dataype is invalid</p>
<pre><code>    - if API response error code is not 0

    - if response is empty

    - if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2344-L2465" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def share_mount_path(self, new_media_agent, new_mount_path, **kwargs):
    &#34;&#34;&#34;
    Method to share a mountpath

    Args:

        new_media_agent (str)   -- Media agent which is accessing the shared mount path

        new_mount_path  (int)   -- Mount path to be shared

            kwargs  (dict)  --  Optional arguments

                Available kwargs Options:

                    media_agent     (str)   -- Media agent associated with library

                    library_name    (str)   -- Name of the library which has the mount path

                    mount_path      (str)   -- Mount path to be shared

                    access_type     (int)   -- The access type of the shared mount path

                                                Read Device Access = 4

                                                Read/ Write Device Access = 6

                                                Read Device Access with Preferred = 12

                                                Read/Write Device Access with Preferred = 14

                                                Data Server - IP Read = 20

                                                Data Server - IP Read/ Write = 22

                                                Data Server - FC Read = 36

                                                Data Server - FC Read/ Write = 38

                                                Data Server - iSCSI Read = 132

                                                Data Server - iSCSI Read/ Write = 134

                                                Note: For the Data Server device access type,
                                                      enter the local path provided in the library/mountPath
                                                      parameter in the libNewProp/mountPath parameter also.


                    username        (str)   -- Username to access the mount path, if UNC

                    password        (str)   -- Password to access the mount path, if UNC

                    credential_name  (str)  -- credential name for the credential manager
                                               ** For cloud if you use credential_name update the username parameter
                                               ** in the format of &#34;&lt;vendorURL&gt;//__CVCRED__&#34;
                                               ** For example, &#34;s3.amazonaws.com//__CVCRED__&#34;
                                               ** Update a dummy value for password parameter

    Returns:
        None

    Raises
        Exception:
            - if any of the parameter&#39;s dataype is invalid

            - if API response error code is not 0

            - if response is empty

            - if response code is not as expected
    &#34;&#34;&#34;

    media_agent = kwargs.get(&#39;media_agent&#39;, self.mediaagent)
    library_name = kwargs.get(&#39;library_name&#39;, self.library_name)
    mount_path = kwargs.get(&#39;mount_path&#39;, self.mountpath)
    access_type = kwargs.get(&#39;access_type&#39;, 22)
    username = kwargs.get(&#39;username&#39;, &#39;&#39;)
    password = kwargs.get(&#39;password&#39;, &#39;&#39;)
    credential_name = kwargs.get(&#39;credential_name&#39;, &#39;&#39;)

    self._EXECUTE = self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;]
    self.library = {
        &#34;opType&#34;: 64,
        &#34;mediaAgentName&#34;: media_agent,
        &#34;libraryName&#34;: library_name,
        &#34;mountPath&#34;: &#34;%s&#34; %
                     mount_path}
    self.lib_new_prop = {
        &#34;deviceAccessType&#34;: access_type,
        &#34;password&#34;: password,
        &#34;loginName&#34;: username,
        &#34;mediaAgentName&#34;: new_media_agent,
        &#34;mountPath&#34;: &#34;{}&#34;.format(new_mount_path),
        &#34;proxyPassword&#34;: &#34;&#34;,
        &#34;savedCredential&#34;: {
            &#34;credentialName&#34;: credential_name
        }
    }
    request_json = {
        &#34;EVGui_ConfigureStorageLibraryReq&#34;:
            {
                &#34;library&#34;: self.library,
                &#34;libNewProp&#34;: self.lib_new_prop
            }
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._EXECUTE, request_json
    )
    if flag:
        response_string = self._commcell_object._update_response_(response.text)
        if response.json():
            if &#34;library&#34; in response.json():
                response = response.json()[&#34;library&#34;]
                return response
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)
        else:

            raise SDKException(&#39;Response&#39;, &#39;102&#39;, response_string)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.update_device_controller"><code class="name flex">
<span>def <span class="ident">update_device_controller</span></span>(<span>self, mountpath_id, device_id, device_controller_id, media_agent_id, device_access_type, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>To update device controller properties.
Args:
mountpath_id (int)
&ndash; Mount Path Id</p>
<pre><code>    device_id (int)     -- Device Id

    device_controller_id (int) -- Device Controller Id

    media_agent_id (int)    --   Media Agent Id

    device_access_type (int)    --  Device access type
                            Regular:
                                    Access type     Value
                                    Read              4
                                    Read and Write    6
                                    Preferred         8

                            IP:
                                    Access type     Value
                                    Read             20
                                    Read/ Write      22

                            Fibre Channel (FC)
                                    Access type     Value
                                    Read             36
                                    Read and Write   38

                            iSCSi
                                    Access type     Value
                                    Read             132
                                    Read and Write   134

    **kwargs  (dict)  --  Optional arguments

            Available kwargs Options:

            username     (str)     -- username for the device
                                      ** in case of cloud library username needs to be in the following format
                                      ** &lt;vendorURL&gt;//__CVCRED__

            password     (str)     -- password for the device
                                      ** if credential name is used then use a dummy password

            credential_name (str)  -- credential name as in the credential manager

            path                   -- accessing path for media agent local / UNC
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2070-L2159" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_device_controller(self, mountpath_id, device_id, device_controller_id, media_agent_id,
                             device_access_type, **kwargs):
    &#34;&#34;&#34;
    To update device controller properties.
        Args:
            mountpath_id (int)  -- Mount Path Id

            device_id (int)     -- Device Id

            device_controller_id (int) -- Device Controller Id

            media_agent_id (int)    --   Media Agent Id

            device_access_type (int)    --  Device access type
                                    Regular:
                                            Access type     Value
                                            Read              4
                                            Read and Write    6
                                            Preferred         8

                                    IP:
                                            Access type     Value
                                            Read             20
                                            Read/ Write      22

                                    Fibre Channel (FC)
                                            Access type     Value
                                            Read             36
                                            Read and Write   38

                                    iSCSi
                                            Access type     Value
                                            Read             132
                                            Read and Write   134

            **kwargs  (dict)  --  Optional arguments

                    Available kwargs Options:

                    username     (str)     -- username for the device
                                              ** in case of cloud library username needs to be in the following format
                                              ** &lt;vendorURL&gt;//__CVCRED__

                    password     (str)     -- password for the device
                                              ** if credential name is used then use a dummy password

                    credential_name (str)  -- credential name as in the credential manager

                    path                   -- accessing path for media agent local / UNC

    &#34;&#34;&#34;

    if not all([isinstance(mountpath_id, int), isinstance(device_id, int), isinstance(device_controller_id, int),
                isinstance(media_agent_id, int), isinstance(device_access_type, int)]):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    username = kwargs.get(&#34;username&#34;, &#34;&#34;)
    password = kwargs.get(&#34;password&#34;, &#34;&#34;)
    credential_name = kwargs.get(&#34;credential_name&#34;, &#34;&#34;)
    path = kwargs.get(&#34;path&#34;, self.mount_path)
    enabled = 1
    if not kwargs.get(&#39;enabled&#39;, True):
        enabled = 0
    request_json = {
        &#34;EVGui_MMDevicePathInfoReq&#34;:
            {
                &#34;mountpathId&#34;: mountpath_id,
                &#34;infoList&#34;: {
                    &#34;password&#34;: password,
                    &#34;accessType&#34;: device_access_type,
                    &#34;deviceId&#34;: device_id,
                    &#34;deviceControllerId&#34;: device_controller_id,
                    &#34;path&#34;: path,
                    &#34;enabled&#34;: enabled,
                    &#34;numWriters&#34;: -1,
                    &#34;opType&#34;: 2,
                    &#34;autoPickTransportType&#34;: 0,
                    &#34;protocolType&#34;: 679,
                    &#34;mediaAgent&#34;: {
                        &#34;id&#34;: media_agent_id
                    },
                    &#34;savedCredential&#34;: {
                        &#34;credentialName&#34;: credential_name
                    },
                    &#34;userName&#34;: username
                }
            }
    }

    self._commcell_object.qoperation_execute(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.validate_mountpath"><code class="name flex">
<span>def <span class="ident">validate_mountpath</span></span>(<span>self, mountpath_drive_id, media_agent)</span>
</code></dt>
<dd>
<div class="desc"><p>To perform storage validation on mountpath</p>
<h2 id="args">Args</h2>
<p>mountpath_drive_id
(int)
&ndash;
Drive Id of mountpath that need to be validate.</p>
<p>media_agent (str)
&ndash; MediaAgent on which Mountpath exists</p>
<h2 id="returns">Returns</h2>
<p>instance of the Job class for this storage validation job
Raises
Exception:
- if argument datatype is invalid</p>
<pre><code>    - if API response error code is not 0

    - if response is empty

    - if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1552-L1620" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def validate_mountpath(self, mountpath_drive_id, media_agent):

    &#34;&#34;&#34; To perform storage validation on mountpath
    Args:
        mountpath_drive_id  (int)   --  Drive Id of mountpath that need to be validate.

        media_agent (str)   -- MediaAgent on which Mountpath exists

    Returns:
        instance of the Job class for this storage validation job

    Raises
        Exception:
            - if argument datatype is invalid

            - if API response error code is not 0

            - if response is empty

            - if response code is not as expected
    &#34;&#34;&#34;

    if not (isinstance(mountpath_drive_id, int) and
            isinstance(media_agent, str)):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)


    request_xml = &#34;&#34;&#34;&lt;TMMsg_CreateTaskReq&gt;
                    &lt;taskInfo taskOperation=&#34;1&#34;&gt;
                        &lt;task associatedObjects=&#34;0&#34; description=&#34;Storage Validation - Automation&#34; initiatedFrom=&#34;1&#34; 
                        isEZOperation=&#34;0&#34; isEditing=&#34;0&#34; isFromCommNetBrowserRootNode=&#34;0&#34; ownerId=&#34;1&#34; ownerName=&#34;&#34; 
                        policyType=&#34;0&#34; runUserId=&#34;1&#34; sequenceNumber=&#34;0&#34; taskType=&#34;1&#34;&gt;
                        &lt;taskFlags notRunnable=&#34;0&#34; /&gt;
                        &lt;/task&gt;
                        &lt;subTasks subTaskOperation=&#34;1&#34;&gt;
                            &lt;subTask flags=&#34;0&#34; operationType=&#34;4013&#34; subTaskId=&#34;1&#34; subTaskOrder=&#34;0&#34; subTaskType=&#34;1&#34;/&gt;
                            &lt;options originalJobId=&#34;0&#34;&gt;
                                &lt;adminOpts&gt;
                                    &lt;libraryOption  operation=&#34;13&#34; validationFlags=&#34;0&#34; validattionReservedFlags=&#34;0&#34;&gt;
                                        &lt;library libraryId=&#34;{0}&#34; /&gt;
                                        &lt;mediaAgent mediaAgentName=&#34;{1}&#34; /&gt;
                                        &lt;driveIds driveId=&#34;{2}&#34; /&gt;
                                        &lt;validateDrive chunkSize=&#34;16384&#34; chunksTillEnd=&#34;0&#34; fileMarkerToStart=&#34;2&#34;
                                         numberOfChunks=&#34;2&#34; threadCount=&#34;2&#34; volumeBlockSize=&#34;64&#34; /&gt;
                                    &lt;/libraryOption&gt; &lt;/adminOpts&gt; &lt;/options&gt; &lt;/subTasks&gt;  &lt;/taskInfo&gt;
                        &lt;/TMMsg_CreateTaskReq&gt;&#34;&#34;&#34;.format(self.library_id, media_agent, mountpath_drive_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._commcell_object._services[&#39;CREATE_TASK&#39;], request_xml
    )

    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                from cvpysdk.job import Job
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

            if &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;Commcell&#39;, &#39;105&#39;, o_str)

            else:
                raise SDKException(&#39;Commcell&#39;, &#39;105&#39;)

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.DiskLibrary.verify_media"><code class="name flex">
<span>def <span class="ident">verify_media</span></span>(<span>self, media_name, location_id)</span>
</code></dt>
<dd>
<div class="desc"><p>To perform verify media operation on media</p>
<h2 id="args">Args</h2>
<p>media_name
&ndash;
Barcode of the media</p>
<p>location_id &ndash;
Slot Id of the media on the library</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2162-L2216" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def verify_media(self, media_name, location_id):
    &#34;&#34;&#34;
        To perform verify media operation on media
        Args:
            media_name  --  Barcode of the media

            location_id --  Slot Id of the media on the library
    &#34;&#34;&#34;

    if not (isinstance(media_name, str) and
            isinstance(location_id,int)):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    request_xml = f&#34;&#34;&#34;&lt;TMMsg_CreateTaskReq&gt;
                        &lt;taskInfo&gt;
                            &lt;task taskType=&#34;1&#34; /&gt;
                            &lt;subTasks subTaskOperation=&#34;1&#34;&gt;
                                &lt;subTask operationType=&#34;4005&#34; subTaskType=&#34;1&#34;/&gt;
                                &lt;options&gt;
                                    &lt;adminOpts&gt;
                                        &lt;libraryOption operation=&#34;6&#34;&gt;
                                            &lt;library _type_=&#34;9&#34; libraryName=&#34;{self.library_name}&#34;/&gt;
                                            &lt;media _type_=&#34;46&#34; mediaName=&#34;{media_name}&#34;/&gt;
                                            &lt;verifyMedia&gt;
                                                &lt;location _type_=&#34;53&#34; locationId=&#34;{location_id}&#34;/&gt;
                                            &lt;/verifyMedia&gt;
                                        &lt;/libraryOption&gt;
                                    &lt;/adminOpts&gt;
                                &lt;/options&gt;
                            &lt;/subTasks&gt;
                        &lt;/taskInfo&gt;
                    &lt;/TMMsg_CreateTaskReq&gt;&#34;&#34;&#34;

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._commcell_object._services[&#39;CREATE_TASK&#39;], request_xml
    )

    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                from cvpysdk.job import Job
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

            if &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;,o_str)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage.Libraries"><code class="flex name class">
<span>class <span class="ident">Libraries</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for libraries</p>
<p>Initialize object of the DiskLibraries class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the DiskLibraries class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1082-L1156" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Libraries(object):
    &#34;&#34;&#34; Class for libraries&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the DiskLibraries class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the DiskLibraries class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._LIBRARY = self._commcell_object._services[&#39;LIBRARY&#39;]

        self._libraries = None
        self.refresh()

    def _get_libraries(self):
        &#34;&#34;&#34;Gets all the disk libraries associated to the commcell specified by commcell object.

            Returns:
                dict - consists of all disk libraries of the commcell
                    {
                         &#34;disk_library1_name&#34;: disk_library1_id,
                         &#34;disk_library2_name&#34;: disk_library2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._LIBRARY)

        if flag:
            if response.json() and &#39;response&#39; in response.json():
                libraries = response.json()[&#39;response&#39;]
                libraries_dict = {}

                for library in libraries:
                    temp_name = library[&#39;entityInfo&#39;][&#39;name&#39;].lower()
                    temp_id = str(library[&#39;entityInfo&#39;][&#39;id&#39;]).lower()
                    libraries_dict[temp_name] = temp_id

                return libraries_dict
            else:
                return {}
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def has_library(self, library_name):
        &#34;&#34;&#34;Checks if a library exists in the commcell with the input library name.

            Args:
                library_name (str)  --  name of the library

            Returns:
                bool - boolean output whether the library exists in the commcell or not

            Raises:
                SDKException:
                    if type of the library name argument is not string
        &#34;&#34;&#34;
        if not isinstance(library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        return self._libraries and library_name.lower() in self._libraries

    def refresh(self):
        &#34;&#34;&#34;Refresh the disk libraries associated with the Commcell.&#34;&#34;&#34;
        self._libraries = self._get_libraries()</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.storage.DiskLibraries" href="#cvpysdk.storage.DiskLibraries">DiskLibraries</a></li>
<li><a title="cvpysdk.storage.TapeLibraries" href="#cvpysdk.storage.TapeLibraries">TapeLibraries</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage.Libraries.has_library"><code class="name flex">
<span>def <span class="ident">has_library</span></span>(<span>self, library_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a library exists in the commcell with the input library name.</p>
<h2 id="args">Args</h2>
<p>library_name (str)
&ndash;
name of the library</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the library exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the library name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1136-L1152" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_library(self, library_name):
    &#34;&#34;&#34;Checks if a library exists in the commcell with the input library name.

        Args:
            library_name (str)  --  name of the library

        Returns:
            bool - boolean output whether the library exists in the commcell or not

        Raises:
            SDKException:
                if type of the library name argument is not string
    &#34;&#34;&#34;
    if not isinstance(library_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    return self._libraries and library_name.lower() in self._libraries</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.Libraries.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the disk libraries associated with the Commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1154-L1156" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the disk libraries associated with the Commcell.&#34;&#34;&#34;
    self._libraries = self._get_libraries()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage.MediaAgent"><code class="flex name class">
<span>class <span class="ident">MediaAgent</span></span>
<span>(</span><span>commcell_object, media_agent_name, media_agent_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for a specific media agent.</p>
<p>Initialise the MediaAgent object.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>media_agent_name
(str)
&ndash;
name of the media agent</p>
<p>media_agent_id
(str)
&ndash;
id of the media agent
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the MediaAgent class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L456-L1079" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class MediaAgent(object):
    &#34;&#34;&#34;Class for a specific media agent.&#34;&#34;&#34;

    def __init__(self, commcell_object, media_agent_name, media_agent_id=None):
        &#34;&#34;&#34;Initialise the MediaAgent object.

            Args:
                commcell_object   (object)  --  instance of the Commcell class

                media_agent_name  (str)     --  name of the media agent

                media_agent_id    (str)     --  id of the media agent
                    default: None

            Returns:
                object - instance of the MediaAgent class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._media_agent_name = media_agent_name.lower()
        self._media_agent_info = None
        if media_agent_id:
            self._media_agent_id = str(media_agent_id)
        else:
            self._media_agent_id = self._get_media_agent_id()

        self._MEDIA_AGENT = self._commcell_object._services[&#39;MEDIA_AGENT&#39;] % (
            self._media_agent_name
        )

        self._CLOUD_MEDIA_AGENT = self._commcell_object._services[&#39;CLOUD_MEDIA_AGENT&#39;] % (
            self._media_agent_id
        )

        self._CREATE_TASK = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        self._MEDIA_AGENTS = self._commcell_object._services[&#39;GET_MEDIA_AGENTS&#39;] + &#34;/{0}&#34;.format(
            self.media_agent_id
        )

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;MediaAgent class instance for MA: &#34;{0}&#34;, of Commcell: &#34;{1}&#34;&#39;

        return representation_string.format(
            self.media_agent_name, self._commcell_object.commserv_name
        )

    def _get_media_agent_id(self):
        &#34;&#34;&#34;Gets the media agent id associated with this media agent.

            Returns:
                str - id associated with this media agent
        &#34;&#34;&#34;
        media_agents = MediaAgents(self._commcell_object)
        return media_agents.get(self.media_agent_name).media_agent_id

    def _get_media_agent_properties(self):
        &#34;&#34;&#34;Returns the media agent properties of this media agent.

            Returns:
                dict - dictionary consisting of the properties of this client

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._MEDIA_AGENTS
        )

        if flag:
            if response.json() and &#39;mediaAgentList&#39; in response.json():
                self._media_agent_info = response.json()[&#39;mediaAgentList&#39;][0]
                return response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _initialize_media_agent_properties(self):
        &#34;&#34;&#34;Initializes the properties for this Media Agent&#34;&#34;&#34;
        self._status = None
        self._platform = None
        self._index_cache_enabled = None
        self._index_cache = None
        self._is_power_mgmt_allowed = None
        self._is_power_mgmt_supported = None
        self._is_power_management_enabled = None
        self._power_management_controller_name = None
        self._power_status = None

        properties = self._get_media_agent_properties()

        if properties[&#39;mediaAgentList&#39;]:
            mediaagent_list = properties[&#39;mediaAgentList&#39;][0]
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        status = mediaagent_list.get(&#39;status&#39;)
        if status == 1:
            self._is_online = True
        else:
            self._is_online = False

        if mediaagent_list[&#39;osInfo&#39;][&#39;OsDisplayInfo&#39;][&#39;OSName&#39;]:
            platform = mediaagent_list[&#39;osInfo&#39;][&#39;OsDisplayInfo&#39;][&#39;OSName&#39;]
            if &#39;windows&#39; in platform.lower():
                self._platform = &#39;WINDOWS&#39;
            elif &#39;unix&#39; in platform.lower() or &#39;linux&#39; in platform.lower():
                self._platform = &#39;UNIX&#39;
            else:
                self._platform = platform

        if mediaagent_list[&#39;mediaAgentProps&#39;][&#39;mediaAgentIdxCacheProps&#39;][&#39;cacheEnabled&#39;]:
            self._index_cache_enabled = mediaagent_list[&#39;mediaAgentProps&#39;][
                &#39;mediaAgentIdxCacheProps&#39;][&#39;cacheEnabled&#39;]

        if mediaagent_list[&#39;mediaAgentProps&#39;][&#39;indexLogsCacheInfo&#39;][&#39;logsCachePath&#39;][&#39;path&#39;]:
            self._index_cache = mediaagent_list[&#39;mediaAgentProps&#39;][&#39;indexLogsCacheInfo&#39;
                                                                   ][&#39;logsCachePath&#39;][&#39;path&#39;]

        if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerMgmtSupported&#39;]:
            self._is_power_mgmt_supported = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerMgmtSupported&#39;]

        if self._is_power_mgmt_supported:

            if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerManagementEnabled&#39;]:
                self._is_power_management_enabled = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerManagementEnabled&#39;]

            if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerMgmtAllowed&#39;]:
                self._is_power_mgmt_allowed = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;isPowerMgmtAllowed&#39;]

            if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;powerStatus&#39;]:
                self._power_status = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;powerStatus&#39;]

            if mediaagent_list[&#39;powerManagementInfo&#39;][&#39;selectedCloudController&#39;][&#39;clientName&#39;]:
                self._power_management_controller_name = mediaagent_list[&#39;powerManagementInfo&#39;][&#39;selectedCloudController&#39;][&#39;clientName&#39;]

    def enable_power_management(self, pseudo_client_name):
        &#34;&#34;&#34;
            Enables power management using the provided cloud controller (pseudo client)

                Args :
                        pseudo_client_name : VSA pseudo client to be used as cloud controller
                Raises:
                        SDKException:
                                    If response is not success

                                    If Power management is not supported
        &#34;&#34;&#34;
        if self._is_power_mgmt_allowed:
            client_obj = self._commcell_object._clients.get(pseudo_client_name)
            pseudo_client_name_client_id = client_obj._get_client_id()

            &#34;&#34;&#34;
            payLoad = &#39;&lt;EVGui_SetCloudVMManagementInfoReq hostId=&#34;&#39; + self.media_agent_id + &#39;&#34; useMediaAgent=&#34;1&#34;&gt; &lt;powerManagementInfo isPowerManagementEnabled=&#34;1&#34; &gt; &lt;selectedCloudController clientId=&#34;&#39; + PseudoClientName_client_id + &#39;&#34; clientName=&#34;&#39; + \
                      PseudoClientName + &#39;&#34;/&gt;&lt;/powerManagementInfo&gt;&lt;/EVGui_SetCloudVMManagementInfoReq&gt;&#39;
            &#34;&#34;&#34;
            payLoad = &#39;&lt;EVGui_SetCloudVMManagementInfoReq hostId=&#34;{0}&#34; useMediaAgent=&#34;1&#34;&gt; &lt;powerManagementInfo isPowerManagementEnabled=&#34;1&#34; &gt; &lt;selectedCloudController clientId=&#34;{1}&#34; clientName=&#34;{2}&#34;/&gt;&lt;/powerManagementInfo&gt;&lt;/EVGui_SetCloudVMManagementInfoReq&gt;&#39;.format(
                self.media_agent_id, pseudo_client_name_client_id, pseudo_client_name)

            response = self._commcell_object._qoperation_execute(payLoad)

            if response[&#39;errorCode&#39;] != 0:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, str(response))
        else:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Power management is not supported&#34;)

    def _perform_power_operation(self, operation):
        &#34;&#34;&#34;
            Performs power operation

                Args :
                        self : Object
                        operation : Operation to perform

                Raises:
                        SDKException:
                                        If operation is not 1 or 0

                                        If ower management is NOT enabled or NOT supported on MediaAgent

                                        If API response is empty

                                        If API response is not success
        &#34;&#34;&#34;
        if not operation in (&#34;1&#34;, &#34;0&#34;):
            raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                               &#34;Invalid power operation type&#34;)

        if self._is_power_management_enabled:
            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;GET&#39;, self._CLOUD_MEDIA_AGENT + &#34;/&#34; + operation
            )
            if not flag:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;,
                                   str(response))
            if response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, str(response))
        else:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                               &#39;Power management is NOT enabled or NOT supported&#39;)

    def power_on(self, wait_till_online=True):
        &#34;&#34;&#34;
            Power-on the MediaAgent

                Args :
                        self : Object
                        wait_till_online :
                                            True : Waits until the MediaAgent is online
                                            False : Just submits the power-on request
        &#34;&#34;&#34;

        if self.current_power_status not in [&#34;Starting&#34;, &#34;Started&#34;, &#34;Online&#34;]:
            self._perform_power_operation(&#34;1&#34;)

        if wait_till_online == True and self.current_power_status != &#34;Online&#34;:
            self.wait_for_power_status(&#34;Online&#34;)

    def power_off(self, wait_till_stopped=True):
        &#34;&#34;&#34;
            Power-off MediaAgent

                Args :
                        self : Object
                        wait_till_stopped :
                                            True : Waits until the MediaAgent is stopped
                                            False : Just submits the power-off request
        &#34;&#34;&#34;

        if self.current_power_status not in [&#34;Stopping&#34;, &#34;Stopped&#34;]:
            self._perform_power_operation(&#34;0&#34;)

        if wait_till_stopped == True and self.current_power_status != &#34;Stopped&#34;:
            self.wait_for_power_status(&#34;Stopped&#34;)

    def wait_for_power_status(self, expected_power_status, time_out_sec=600):
        &#34;&#34;&#34;
            Waits until the expected power status not achieved

                Args :
                                        self : Object
                                        expected_power_status : The expected power status as following.
                                                                    Starting
                                                                    Started
                                                                    Online
                                                                    Stopping
                                                                    Stopped
                                        time_out_sec : Maximum time to wait for the expected power status

                                        Raises:
                                                SDKException:
                                                                If time_out_sec is not an integer and time_out_sec not None

                                                                If expected power status is not achieved within time_out_sec time
        &#34;&#34;&#34;
        if time_out_sec != None:
            if not isinstance(time_out_sec, int):
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                                   &#39;Expected an integer value for [time_out_sec]&#39;)

        start_time = time.time()
        while self.current_power_status != expected_power_status:
            time.sleep(10)
            if time_out_sec != None:
                if time.time() - start_time &gt; time_out_sec:
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                                       &#39;The expected power status is not achieved within expected time&#39;)

    def change_index_cache(self, old_index_cache_path, new_index_cache_path):
        &#34;&#34;&#34;
        Begins a catalog migration job via the CreateTask end point.

            Args :
                old_index_cache_path - source index cache path

                new_index_cache_path - destination index cache path

            Returns :
                Returns job object of catalog migration job

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        conf_guid = str(uuid.uuid4())

        xml_options_string = &#39;&#39;&#39;&lt;Indexing_IdxDirectoryConfiguration configurationGuid=&#34;{0}&#34;
        icdPath=&#34;{1}&#34; maClientFocusName=&#34;{2}&#34; maGuid=&#34;&#34; oldIcdPath=&#34;{3}&#34;
        opType=&#34;0&#34; /&gt;&#39;&#39;&#39; .format(
            conf_guid, new_index_cache_path, self.media_agent_name, old_index_cache_path)

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;taskOperation&#34;: 1,
                &#34;task&#34;: {
                    &#34;isEZOperation&#34;: False,
                    &#34;description&#34;: &#34;&#34;,
                    &#34;ownerId&#34;: 1,
                    &#34;runUserId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;&#34;,
                    &#34;alertName&#34;: &#34;&#34;,
                    &#34;sequenceNumber&#34;: 0,
                    &#34;isEditing&#34;: False,
                    &#34;GUID&#34;: &#34;&#34;,
                    &#34;isFromCommNetBrowserRootNode&#34;: False,
                    &#34;initiatedFrom&#34;: 3,
                    &#34;policyType&#34;: 0,
                    &#34;associatedObjects&#34;: 0,
                    &#34;taskName&#34;: &#34;&#34;,
                    &#34;taskFlags&#34;: {
                        &#34;notRunnable&#34;: False,
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskOrder&#34;: 0,
                            &#34;subTaskType&#34;: 1,
                            &#34;flags&#34;: 0,
                            &#34;operationType&#34;: 5018,
                            &#34;subTaskId&#34;: 1
                        },
                        &#34;options&#34;: {
                            &#34;originalJobId&#34;: 0,
                            &#34;adminOpts&#34;: {
                                &#34;catalogMigrationOptions&#34;: {
                                    &#34;xmlOptions&#34;: xml_options_string,
                                    &#34;mediaAgent&#34;: {
                                        &#34;mediaAgentId&#34;: int(self._media_agent_id),
                                        &#34;_type_&#34;: 11
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_TASK, request_json
        )

        if flag:
            if response.json() and &#39;jobIds&#39; in response.json() and response.json()[&#39;jobIds&#39;][0]:

                response_json = response.json()
                catalogmigration_jobid = response_json[&#34;jobIds&#34;][0]
                catalogmigration_job_obj = self._commcell_object.job_controller.get(
                    catalogmigration_jobid)
                return catalogmigration_job_obj

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def set_state(self, enable=True):
        &#34;&#34;&#34;
        disable the media agent by change in media agent properties.
            Args:
            enable      -   (bool)
                            True        - Enable the media agent
                            False       - Disable the media agent

            Raises:
            &#34;exception&#34;                  -   if there is an empty response
                                         -   if there is an error in request execution
                                         -   if response status is failure

        &#34;&#34;&#34;

        if type(enable) != bool:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_id = int(self.media_agent_id)
        request_json = {
            &#34;mediaAgentInfo&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: media_id
                },
                &#34;mediaAgentProps&#34;: {
                    &#34;enableMA&#34;: enable
                }
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
        )

        # check for response
        # possible key errors if key not present in response, defaults set
        if flag:
            if response and response.json():
                response = response.json()
                if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                    error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)


    def mark_for_maintenance(self, mark=False):
        &#34;&#34;&#34;
        mark the media agent offline for maintenance
            Args:
                mark  - (bool)
                                        True    - mark the media agent for maintenance
                                        False   - UNMARK the media agent for maintenance

            Raises:
            &#34;exception&#34;                  -   if there is an empty response
                                         -   if there is an error in request execution
                                         -   if response status is failure

        &#34;&#34;&#34;

        if type(mark) != bool:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_id = int(self.media_agent_id)
        request_json = {
            &#34;mediaAgentInfo&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: media_id
                },
                &#34;mediaAgentProps&#34;: {
                    &#34;markMAOfflineForMaintenance&#34;: mark
                }
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
        )

        if flag:
            if response and response.json():
                response = response.json()
                if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                    error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def set_ransomware_protection(self, status):
        &#34;&#34;&#34;Enables / Disables the ransomware protection on Windows MediaAgent.

        Args:
            status    (bool)        --  True or False value to turn it on/off
                                        True - ransomware protection on MediaAgent - ON
                                        False - ransomware protection on MediaAgent - OFF

        Returns:
            None                   --   if operation performed successfully.

        Raises:
            Exception(Exception_Code, Exception_Message):
                - if there is failure in executing the operation
        &#34;&#34;&#34;
        # this works only on WINDOWS MA
        if self._platform != &#39;WINDOWS&#39;:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if type(status) != bool:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_id = int(self.media_agent_id)

        request_json = {
            &#34;mediaAgentInfo&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: media_id
                },
                &#34;mediaAgentProps&#34;: {
                    &#34;isRansomwareProtected&#34;: status
                }
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
        )

        if flag:
            if response and response.json():
                response = response.json()
                if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                    error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def set_concurrent_lan(self, enable=True):
        &#34;&#34;&#34;
        disable / enable concurrent LAN backup in Media agent properties.
            Args:
            enable      -   (bool)
                            True        - Enable concurent LAN Backup
                            False       - Disable concurent LAN Backup

        Returns:
            None                   --   if operation performed successfully.

        Raises:
            SDKException:
                - if there is failure in executing the operation

        &#34;&#34;&#34;

        if type(enable) != bool:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_id = int(self.media_agent_id)
        request_json = {
            &#34;mediaAgentInfo&#34;: {
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: media_id
                },
                &#34;mediaAgentProps&#34;: {
                    &#34;optimizeForConcurrentLANBackups&#34;: enable
                }
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
        )

        if flag:
            if response and response.json():
                response = response.json()
                if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                    error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the media agent display name&#34;&#34;&#34;
        return self._media_agent_info[&#39;mediaAgent&#39;][&#39;displayName&#39;]

    @property
    def media_agent_name(self):
        &#34;&#34;&#34;Treats the media agent name as a read-only attribute.&#34;&#34;&#34;
        return self._media_agent_name

    @property
    def media_agent_id(self):
        &#34;&#34;&#34;Treats the media agent id as a read-only attribute.&#34;&#34;&#34;
        return self._media_agent_id

    @property
    def is_online(self):
        &#34;&#34;&#34;Treats the status as read-only attribute&#34;&#34;&#34;
        return self._is_online

    @property
    def platform(self):
        &#34;&#34;&#34;Treats the platform as read-only attribute&#34;&#34;&#34;
        return self._platform

    @property
    def index_cache_path(self):
        &#34;&#34;&#34;Treats the index cache path as a read-only attribute&#34;&#34;&#34;
        return self._index_cache

    @property
    def index_cache_enabled(self):
        &#34;&#34;&#34;Treats the cache enabled value as a read-only attribute&#34;&#34;&#34;
        return self._index_cache_enabled

    @property
    def is_power_management_enabled(self):
        &#34;&#34;&#34; Returns power management enable status&#34;&#34;&#34;
        return self._is_power_management_enabled

    @property
    def current_power_status(self):
        &#34;&#34;&#34;
                Returns the power state of the MA.

                    Args :
                            self : Object
                    Returns :
                            str - Current power status of the MediaAgent as following
                                    Starting : Power-on process in going on
                                    Started : MA is powered-on successfully but still not synced with CS
                                    Online : Powered-on and synced with CS. MA is ready to use.
                                    Stopping : Power-off operation is going on.
                                    Stopped : MA is powered-off
                                    Unknown : MA power status is still not synced with cloud provider. MA discovery is going on or power state sync with happening with cloud provider or something is NOT right.
        &#34;&#34;&#34;
        self.refresh()
        power_status = {0: &#39;Unknown&#39;, 1: &#39;Starting&#39;, 2: &#39;Started&#39;, 3: &#39;Online&#39;, 4: &#39;Stopping&#39;, 5: &#39;Stopped&#39;}
        return power_status.get(self._power_status)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the MediaAgent.&#34;&#34;&#34;
        self._initialize_media_agent_properties()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.storage.MediaAgent.current_power_status"><code class="name">var <span class="ident">current_power_status</span></code></dt>
<dd>
<div class="desc"><p>Returns the power state of the MA.</p>
<pre><code>Args :
        self : Object
Returns :
        str - Current power status of the MediaAgent as following
                Starting : Power-on process in going on
                Started : MA is powered-on successfully but still not synced with CS
                Online : Powered-on and synced with CS. MA is ready to use.
                Stopping : Power-off operation is going on.
                Stopped : MA is powered-off
                Unknown : MA power status is still not synced with cloud provider. MA discovery is going on or power state sync with happening with cloud provider or something is NOT right.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1057-L1075" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def current_power_status(self):
    &#34;&#34;&#34;
            Returns the power state of the MA.

                Args :
                        self : Object
                Returns :
                        str - Current power status of the MediaAgent as following
                                Starting : Power-on process in going on
                                Started : MA is powered-on successfully but still not synced with CS
                                Online : Powered-on and synced with CS. MA is ready to use.
                                Stopping : Power-off operation is going on.
                                Stopped : MA is powered-off
                                Unknown : MA power status is still not synced with cloud provider. MA discovery is going on or power state sync with happening with cloud provider or something is NOT right.
    &#34;&#34;&#34;
    self.refresh()
    power_status = {0: &#39;Unknown&#39;, 1: &#39;Starting&#39;, 2: &#39;Started&#39;, 3: &#39;Online&#39;, 4: &#39;Stopping&#39;, 5: &#39;Stopped&#39;}
    return power_status.get(self._power_status)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.index_cache_enabled"><code class="name">var <span class="ident">index_cache_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the cache enabled value as a read-only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1047-L1050" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_cache_enabled(self):
    &#34;&#34;&#34;Treats the cache enabled value as a read-only attribute&#34;&#34;&#34;
    return self._index_cache_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.index_cache_path"><code class="name">var <span class="ident">index_cache_path</span></code></dt>
<dd>
<div class="desc"><p>Treats the index cache path as a read-only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1042-L1045" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_cache_path(self):
    &#34;&#34;&#34;Treats the index cache path as a read-only attribute&#34;&#34;&#34;
    return self._index_cache</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.is_online"><code class="name">var <span class="ident">is_online</span></code></dt>
<dd>
<div class="desc"><p>Treats the status as read-only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1032-L1035" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_online(self):
    &#34;&#34;&#34;Treats the status as read-only attribute&#34;&#34;&#34;
    return self._is_online</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.is_power_management_enabled"><code class="name">var <span class="ident">is_power_management_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns power management enable status</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1052-L1055" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_power_management_enabled(self):
    &#34;&#34;&#34; Returns power management enable status&#34;&#34;&#34;
    return self._is_power_management_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.media_agent_id"><code class="name">var <span class="ident">media_agent_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the media agent id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1027-L1030" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def media_agent_id(self):
    &#34;&#34;&#34;Treats the media agent id as a read-only attribute.&#34;&#34;&#34;
    return self._media_agent_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.media_agent_name"><code class="name">var <span class="ident">media_agent_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the media agent name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1022-L1025" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def media_agent_name(self):
    &#34;&#34;&#34;Treats the media agent name as a read-only attribute.&#34;&#34;&#34;
    return self._media_agent_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Returns the media agent display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1017-L1020" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Returns the media agent display name&#34;&#34;&#34;
    return self._media_agent_info[&#39;mediaAgent&#39;][&#39;displayName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.platform"><code class="name">var <span class="ident">platform</span></code></dt>
<dd>
<div class="desc"><p>Treats the platform as read-only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1037-L1040" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def platform(self):
    &#34;&#34;&#34;Treats the platform as read-only attribute&#34;&#34;&#34;
    return self._platform</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage.MediaAgent.change_index_cache"><code class="name flex">
<span>def <span class="ident">change_index_cache</span></span>(<span>self, old_index_cache_path, new_index_cache_path)</span>
</code></dt>
<dd>
<div class="desc"><p>Begins a catalog migration job via the CreateTask end point.</p>
<pre><code>Args :
    old_index_cache_path - source index cache path

    new_index_cache_path - destination index cache path

Returns :
    Returns job object of catalog migration job

Raises:
    SDKException:
        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L730-L825" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def change_index_cache(self, old_index_cache_path, new_index_cache_path):
    &#34;&#34;&#34;
    Begins a catalog migration job via the CreateTask end point.

        Args :
            old_index_cache_path - source index cache path

            new_index_cache_path - destination index cache path

        Returns :
            Returns job object of catalog migration job

        Raises:
            SDKException:
                if response is empty

                if response is not success

    &#34;&#34;&#34;

    conf_guid = str(uuid.uuid4())

    xml_options_string = &#39;&#39;&#39;&lt;Indexing_IdxDirectoryConfiguration configurationGuid=&#34;{0}&#34;
    icdPath=&#34;{1}&#34; maClientFocusName=&#34;{2}&#34; maGuid=&#34;&#34; oldIcdPath=&#34;{3}&#34;
    opType=&#34;0&#34; /&gt;&#39;&#39;&#39; .format(
        conf_guid, new_index_cache_path, self.media_agent_name, old_index_cache_path)

    request_json = {
        &#34;taskInfo&#34;: {
            &#34;taskOperation&#34;: 1,
            &#34;task&#34;: {
                &#34;isEZOperation&#34;: False,
                &#34;description&#34;: &#34;&#34;,
                &#34;ownerId&#34;: 1,
                &#34;runUserId&#34;: 1,
                &#34;taskType&#34;: 1,
                &#34;ownerName&#34;: &#34;&#34;,
                &#34;alertName&#34;: &#34;&#34;,
                &#34;sequenceNumber&#34;: 0,
                &#34;isEditing&#34;: False,
                &#34;GUID&#34;: &#34;&#34;,
                &#34;isFromCommNetBrowserRootNode&#34;: False,
                &#34;initiatedFrom&#34;: 3,
                &#34;policyType&#34;: 0,
                &#34;associatedObjects&#34;: 0,
                &#34;taskName&#34;: &#34;&#34;,
                &#34;taskFlags&#34;: {
                    &#34;notRunnable&#34;: False,
                    &#34;disabled&#34;: False
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: {
                        &#34;subTaskOrder&#34;: 0,
                        &#34;subTaskType&#34;: 1,
                        &#34;flags&#34;: 0,
                        &#34;operationType&#34;: 5018,
                        &#34;subTaskId&#34;: 1
                    },
                    &#34;options&#34;: {
                        &#34;originalJobId&#34;: 0,
                        &#34;adminOpts&#34;: {
                            &#34;catalogMigrationOptions&#34;: {
                                &#34;xmlOptions&#34;: xml_options_string,
                                &#34;mediaAgent&#34;: {
                                    &#34;mediaAgentId&#34;: int(self._media_agent_id),
                                    &#34;_type_&#34;: 11
                                }
                            }
                        }
                    }
                }
            ]
        }
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._CREATE_TASK, request_json
    )

    if flag:
        if response.json() and &#39;jobIds&#39; in response.json() and response.json()[&#39;jobIds&#39;][0]:

            response_json = response.json()
            catalogmigration_jobid = response_json[&#34;jobIds&#34;][0]
            catalogmigration_job_obj = self._commcell_object.job_controller.get(
                catalogmigration_jobid)
            return catalogmigration_job_obj

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.enable_power_management"><code class="name flex">
<span>def <span class="ident">enable_power_management</span></span>(<span>self, pseudo_client_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables power management using the provided cloud controller (pseudo client)</p>
<pre><code>Args :
        pseudo_client_name : VSA pseudo client to be used as cloud controller
Raises:
        SDKException:
                    If response is not success

                    If Power management is not supported
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L598-L626" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_power_management(self, pseudo_client_name):
    &#34;&#34;&#34;
        Enables power management using the provided cloud controller (pseudo client)

            Args :
                    pseudo_client_name : VSA pseudo client to be used as cloud controller
            Raises:
                    SDKException:
                                If response is not success

                                If Power management is not supported
    &#34;&#34;&#34;
    if self._is_power_mgmt_allowed:
        client_obj = self._commcell_object._clients.get(pseudo_client_name)
        pseudo_client_name_client_id = client_obj._get_client_id()

        &#34;&#34;&#34;
        payLoad = &#39;&lt;EVGui_SetCloudVMManagementInfoReq hostId=&#34;&#39; + self.media_agent_id + &#39;&#34; useMediaAgent=&#34;1&#34;&gt; &lt;powerManagementInfo isPowerManagementEnabled=&#34;1&#34; &gt; &lt;selectedCloudController clientId=&#34;&#39; + PseudoClientName_client_id + &#39;&#34; clientName=&#34;&#39; + \
                  PseudoClientName + &#39;&#34;/&gt;&lt;/powerManagementInfo&gt;&lt;/EVGui_SetCloudVMManagementInfoReq&gt;&#39;
        &#34;&#34;&#34;
        payLoad = &#39;&lt;EVGui_SetCloudVMManagementInfoReq hostId=&#34;{0}&#34; useMediaAgent=&#34;1&#34;&gt; &lt;powerManagementInfo isPowerManagementEnabled=&#34;1&#34; &gt; &lt;selectedCloudController clientId=&#34;{1}&#34; clientName=&#34;{2}&#34;/&gt;&lt;/powerManagementInfo&gt;&lt;/EVGui_SetCloudVMManagementInfoReq&gt;&#39;.format(
            self.media_agent_id, pseudo_client_name_client_id, pseudo_client_name)

        response = self._commcell_object._qoperation_execute(payLoad)

        if response[&#39;errorCode&#39;] != 0:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, str(response))
    else:
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Power management is not supported&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.mark_for_maintenance"><code class="name flex">
<span>def <span class="ident">mark_for_maintenance</span></span>(<span>self, mark=False)</span>
</code></dt>
<dd>
<div class="desc"><p>mark the media agent offline for maintenance
Args:
mark
- (bool)
True
- mark the media agent for maintenance
False
- UNMARK the media agent for maintenance</p>
<pre><code>Raises:
"exception"                  -   if there is an empty response
                             -   if there is an error in request execution
                             -   if response status is failure
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L875-L918" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def mark_for_maintenance(self, mark=False):
    &#34;&#34;&#34;
    mark the media agent offline for maintenance
        Args:
            mark  - (bool)
                                    True    - mark the media agent for maintenance
                                    False   - UNMARK the media agent for maintenance

        Raises:
        &#34;exception&#34;                  -   if there is an empty response
                                     -   if there is an error in request execution
                                     -   if response status is failure

    &#34;&#34;&#34;

    if type(mark) != bool:
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    media_id = int(self.media_agent_id)
    request_json = {
        &#34;mediaAgentInfo&#34;: {
            &#34;mediaAgent&#34;: {
                &#34;mediaAgentId&#34;: media_id
            },
            &#34;mediaAgentProps&#34;: {
                &#34;markMAOfflineForMaintenance&#34;: mark
            }
        }
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
    )

    if flag:
        if response and response.json():
            response = response.json()
            if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.power_off"><code class="name flex">
<span>def <span class="ident">power_off</span></span>(<span>self, wait_till_stopped=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Power-off MediaAgent</p>
<pre><code>Args :
        self : Object
        wait_till_stopped :
                            True : Waits until the MediaAgent is stopped
                            False : Just submits the power-off request
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L680-L695" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def power_off(self, wait_till_stopped=True):
    &#34;&#34;&#34;
        Power-off MediaAgent

            Args :
                    self : Object
                    wait_till_stopped :
                                        True : Waits until the MediaAgent is stopped
                                        False : Just submits the power-off request
    &#34;&#34;&#34;

    if self.current_power_status not in [&#34;Stopping&#34;, &#34;Stopped&#34;]:
        self._perform_power_operation(&#34;0&#34;)

    if wait_till_stopped == True and self.current_power_status != &#34;Stopped&#34;:
        self.wait_for_power_status(&#34;Stopped&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.power_on"><code class="name flex">
<span>def <span class="ident">power_on</span></span>(<span>self, wait_till_online=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Power-on the MediaAgent</p>
<pre><code>Args :
        self : Object
        wait_till_online :
                            True : Waits until the MediaAgent is online
                            False : Just submits the power-on request
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L663-L678" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def power_on(self, wait_till_online=True):
    &#34;&#34;&#34;
        Power-on the MediaAgent

            Args :
                    self : Object
                    wait_till_online :
                                        True : Waits until the MediaAgent is online
                                        False : Just submits the power-on request
    &#34;&#34;&#34;

    if self.current_power_status not in [&#34;Starting&#34;, &#34;Started&#34;, &#34;Online&#34;]:
        self._perform_power_operation(&#34;1&#34;)

    if wait_till_online == True and self.current_power_status != &#34;Online&#34;:
        self.wait_for_power_status(&#34;Online&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the MediaAgent.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L1077-L1079" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the MediaAgent.&#34;&#34;&#34;
    self._initialize_media_agent_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.set_concurrent_lan"><code class="name flex">
<span>def <span class="ident">set_concurrent_lan</span></span>(<span>self, enable=True)</span>
</code></dt>
<dd>
<div class="desc"><p>disable / enable concurrent LAN backup in Media agent properties.
Args:
enable
-
(bool)
True
- Enable concurent LAN Backup
False
- Disable concurent LAN Backup</p>
<h2 id="returns">Returns</h2>
<p>None
&ndash;
if operation performed successfully.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
- if there is failure in executing the operation</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L970-L1015" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_concurrent_lan(self, enable=True):
    &#34;&#34;&#34;
    disable / enable concurrent LAN backup in Media agent properties.
        Args:
        enable      -   (bool)
                        True        - Enable concurent LAN Backup
                        False       - Disable concurent LAN Backup

    Returns:
        None                   --   if operation performed successfully.

    Raises:
        SDKException:
            - if there is failure in executing the operation

    &#34;&#34;&#34;

    if type(enable) != bool:
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    media_id = int(self.media_agent_id)
    request_json = {
        &#34;mediaAgentInfo&#34;: {
            &#34;mediaAgent&#34;: {
                &#34;mediaAgentId&#34;: media_id
            },
            &#34;mediaAgentProps&#34;: {
                &#34;optimizeForConcurrentLANBackups&#34;: enable
            }
        }
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
    )

    if flag:
        if response and response.json():
            response = response.json()
            if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.set_ransomware_protection"><code class="name flex">
<span>def <span class="ident">set_ransomware_protection</span></span>(<span>self, status)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables / Disables the ransomware protection on Windows MediaAgent.</p>
<h2 id="args">Args</h2>
<p>status
(bool)
&ndash;
True or False value to turn it on/off
True - ransomware protection on MediaAgent - ON
False - ransomware protection on MediaAgent - OFF</p>
<h2 id="returns">Returns</h2>
<p>None
&ndash;
if operation performed successfully.</p>
<h2 id="raises">Raises</h2>
<p>Exception(Exception_Code, Exception_Message):
- if there is failure in executing the operation</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L920-L968" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_ransomware_protection(self, status):
    &#34;&#34;&#34;Enables / Disables the ransomware protection on Windows MediaAgent.

    Args:
        status    (bool)        --  True or False value to turn it on/off
                                    True - ransomware protection on MediaAgent - ON
                                    False - ransomware protection on MediaAgent - OFF

    Returns:
        None                   --   if operation performed successfully.

    Raises:
        Exception(Exception_Code, Exception_Message):
            - if there is failure in executing the operation
    &#34;&#34;&#34;
    # this works only on WINDOWS MA
    if self._platform != &#39;WINDOWS&#39;:
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    if type(status) != bool:
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    media_id = int(self.media_agent_id)

    request_json = {
        &#34;mediaAgentInfo&#34;: {
            &#34;mediaAgent&#34;: {
                &#34;mediaAgentId&#34;: media_id
            },
            &#34;mediaAgentProps&#34;: {
                &#34;isRansomwareProtected&#34;: status
            }
        }
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
    )

    if flag:
        if response and response.json():
            response = response.json()
            if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.set_state"><code class="name flex">
<span>def <span class="ident">set_state</span></span>(<span>self, enable=True)</span>
</code></dt>
<dd>
<div class="desc"><p>disable the media agent by change in media agent properties.
Args:
enable
-
(bool)
True
- Enable the media agent
False
- Disable the media agent</p>
<pre><code>Raises:
"exception"                  -   if there is an empty response
                             -   if there is an error in request execution
                             -   if response status is failure
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L827-L872" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_state(self, enable=True):
    &#34;&#34;&#34;
    disable the media agent by change in media agent properties.
        Args:
        enable      -   (bool)
                        True        - Enable the media agent
                        False       - Disable the media agent

        Raises:
        &#34;exception&#34;                  -   if there is an empty response
                                     -   if there is an error in request execution
                                     -   if response status is failure

    &#34;&#34;&#34;

    if type(enable) != bool:
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    media_id = int(self.media_agent_id)
    request_json = {
        &#34;mediaAgentInfo&#34;: {
            &#34;mediaAgent&#34;: {
                &#34;mediaAgentId&#34;: media_id
            },
            &#34;mediaAgentProps&#34;: {
                &#34;enableMA&#34;: enable
            }
        }
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._MEDIA_AGENTS, request_json
    )

    # check for response
    # possible key errors if key not present in response, defaults set
    if flag:
        if response and response.json():
            response = response.json()
            if response.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, -1) != 0:
                error_message = response.get(&#39;error&#39;, {}).get(&#39;errorString&#39;, &#39;&#39;)
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgent.wait_for_power_status"><code class="name flex">
<span>def <span class="ident">wait_for_power_status</span></span>(<span>self, expected_power_status, time_out_sec=600)</span>
</code></dt>
<dd>
<div class="desc"><p>Waits until the expected power status not achieved</p>
<pre><code>Args :
                        self : Object
                        expected_power_status : The expected power status as following.
                                                    Starting
                                                    Started
                                                    Online
                                                    Stopping
                                                    Stopped
                        time_out_sec : Maximum time to wait for the expected power status

                        Raises:
                                SDKException:
                                                If time_out_sec is not an integer and time_out_sec not None

                                                If expected power status is not achieved within time_out_sec time
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L697-L728" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def wait_for_power_status(self, expected_power_status, time_out_sec=600):
    &#34;&#34;&#34;
        Waits until the expected power status not achieved

            Args :
                                    self : Object
                                    expected_power_status : The expected power status as following.
                                                                Starting
                                                                Started
                                                                Online
                                                                Stopping
                                                                Stopped
                                    time_out_sec : Maximum time to wait for the expected power status

                                    Raises:
                                            SDKException:
                                                            If time_out_sec is not an integer and time_out_sec not None

                                                            If expected power status is not achieved within time_out_sec time
    &#34;&#34;&#34;
    if time_out_sec != None:
        if not isinstance(time_out_sec, int):
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                               &#39;Expected an integer value for [time_out_sec]&#39;)

    start_time = time.time()
    while self.current_power_status != expected_power_status:
        time.sleep(10)
        if time_out_sec != None:
            if time.time() - start_time &gt; time_out_sec:
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                                   &#39;The expected power status is not achieved within expected time&#39;)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage.MediaAgents"><code class="flex name class">
<span>class <span class="ident">MediaAgents</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the media agents associated with the commcell.</p>
<p>Initialize object of the MediaAgents class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the MediaAgents class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L229-L453" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class MediaAgents(object):
    &#34;&#34;&#34;Class for getting all the media agents associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the MediaAgents class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the MediaAgents class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._MEDIA_AGENTS = self._commcell_object._services[&#39;GET_MEDIA_AGENTS&#39;]
        self._media_agents = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all media agents of the commcell.

            Returns:
                str - string of all the media agents associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Media Agent&#39;)

        for index, media_agent in enumerate(self._media_agents):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, media_agent)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the MediaAgents class.&#34;&#34;&#34;
        return &#34;MediaAgents class instance for Commcell&#34;

    def _get_media_agents(self):
        &#34;&#34;&#34;Gets all the media agents associated to the commcell specified by commcell object.

            Returns:
                dict - consists of all media agents of the commcell
                    {
                         &#34;media_agent1_name&#34;: {

                                 &#39;id&#39;: media_agent1_id,

                                 &#39;os_info&#39;: media_agent1_os,

                                 &#39;is_online&#39;: media_agent1_status
                         },
                         &#34;media_agent2_name&#34;: {

                                 &#39;id&#39;: media_agent2_id,

                                 &#39;os_info&#39;: media_agent2_os,

                                 &#39;is_online&#39;: media_agent2_status
                         }
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._MEDIA_AGENTS
        )

        if flag:
            if isinstance(response.json(), dict):
                media_agents = response.json().get(&#39;mediaAgentList&#39;, [])
                media_agents_dict = {}

                for media_agent in media_agents:
                    temp_name = media_agent[&#39;mediaAgent&#39;][&#39;mediaAgentName&#39;].lower()
                    temp_id = str(media_agent[&#39;mediaAgent&#39;][&#39;mediaAgentId&#39;]).lower()
                    temp_os = media_agent[&#39;osInfo&#39;][&#39;OsDisplayInfo&#39;][&#39;OSName&#39;]
                    temp_status = bool(media_agent[&#39;status&#39;])
                    media_agents_dict[temp_name] = {
                        &#39;id&#39;: temp_id,
                        &#39;os_info&#39;: temp_os,
                        &#39;is_online&#39;: temp_status
                    }

                return media_agents_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_media_agents(self):
        &#34;&#34;&#34;Returns dict of all the media agents on this commcell

            dict - consists of all media agents of the commcell
                    {
                         &#34;media_agent1_name&#34;: {

                                 &#39;id&#39;: media_agent1_id,

                                 &#39;os_info&#39;: media_agent1_os,

                                 &#39;is_online&#39;: media_agent1_status
                         },
                         &#34;media_agent2_name&#34;: {

                                 &#39;id&#39;: media_agent2_id,

                                 &#39;os_info&#39;: media_agent2_os,

                                 &#39;is_online&#39;: media_agent2_status
                         }
                    }
        &#34;&#34;&#34;
        return self._media_agents

    def has_media_agent(self, media_agent_name):
        &#34;&#34;&#34;Checks if a media agent exists in the commcell with the input media agent name.

            Args:
                media_agent_name (str)  --  name of the media agent

            Returns:
                bool - boolean output whether the media agent exists in the commcell or not

            Raises:
                SDKException:
                    if type of the media agent name argument is not string
        &#34;&#34;&#34;
        if not isinstance(media_agent_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        return self._media_agents and media_agent_name.lower() in self._media_agents

    def get(self, media_agent_name):
        &#34;&#34;&#34;Returns a MediaAgent object of the specified media agent name.

            Args:
                media_agent_name (str)  --  name of the media agent

            Returns:
                object - instance of the MediaAgent class for the given media agent name

            Raises:
                SDKException:
                    if type of the media agent name argument is not string

                    if no media agent exists with the given name
        &#34;&#34;&#34;
        if not isinstance(media_agent_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            media_agent_name = media_agent_name.lower()

            if self.has_media_agent(media_agent_name):
                return MediaAgent(self._commcell_object,
                                  media_agent_name,
                                  self._media_agents[media_agent_name][&#39;id&#39;])

            raise SDKException(
                &#39;Storage&#39;, &#39;102&#39;, &#39;No media agent exists with name: {0}&#39;.format(media_agent_name)
            )

    def delete(self, media_agent, force=False):
        &#34;&#34;&#34;Deletes the media agent from the commcell.

            Args:
                media_agent (str)  --  name of the Mediaagent to remove from the commcell

                force       (bool)     --  True if you want to delete media agent forcefully.

            Raises:
                SDKException:
                    if type of the media agent name argument is not string

                    if failed to delete Media agent

                    if response is empty

                    if response is not success

                    if no media agent exists with the given name

        &#34;&#34;&#34;
        if not isinstance(media_agent, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            media_agent = media_agent.lower()

            if self.has_media_agent(media_agent):
                mediagent_id = self.all_media_agents[media_agent][&#39;id&#39;]
                mediagent_delete_service = self._commcell_object._services[&#39;MEDIA_AGENT&#39;] % (mediagent_id)
                if force:
                    mediagent_delete_service += &#34;?forceDelete=1&#34;

                flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;DELETE&#39;, mediagent_delete_service)

                error_code = 0
                if flag:
                    if &#39;errorCode&#39; in response.json():
                        o_str = &#39;Failed to delete mediaagent&#39;
                        error_code = response.json()[&#39;errorCode&#39;]
                        if error_code == 0:
                            # initialize the mediaagents again
                            # so the mediaagents object has all the mediaagents
                            self.refresh()
                        else:
                            error_message = response.json()[&#39;errorMessage&#39;]
                            if error_message:
                                o_str += &#39;\nError: &#34;{0}&#34;&#39;.format(error_message)
                            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, o_str)
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
            else:
                raise SDKException(
                    &#39;Storage&#39;, &#39;102&#39;, &#39;No Mediaagent exists with name: {0}&#39;.format(media_agent)
                )

    def refresh(self):
        &#34;&#34;&#34;Refresh the media agents associated with the Commcell.&#34;&#34;&#34;
        self._media_agents = self._get_media_agents()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.storage.MediaAgents.all_media_agents"><code class="name">var <span class="ident">all_media_agents</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all the media agents on this commcell</p>
<p>dict - consists of all media agents of the commcell
{
"media_agent1_name": {</p>
<pre><code>                 'id': media_agent1_id,

                 'os_info': media_agent1_os,

                 'is_online': media_agent1_status
         },
         "media_agent2_name": {

                 'id': media_agent2_id,

                 'os_info': media_agent2_os,

                 'is_online': media_agent2_status
         }
    }
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L321-L345" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_media_agents(self):
    &#34;&#34;&#34;Returns dict of all the media agents on this commcell

        dict - consists of all media agents of the commcell
                {
                     &#34;media_agent1_name&#34;: {

                             &#39;id&#39;: media_agent1_id,

                             &#39;os_info&#39;: media_agent1_os,

                             &#39;is_online&#39;: media_agent1_status
                     },
                     &#34;media_agent2_name&#34;: {

                             &#39;id&#39;: media_agent2_id,

                             &#39;os_info&#39;: media_agent2_os,

                             &#39;is_online&#39;: media_agent2_status
                     }
                }
    &#34;&#34;&#34;
    return self._media_agents</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage.MediaAgents.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, media_agent, force=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the media agent from the commcell.</p>
<h2 id="args">Args</h2>
<p>media_agent (str)
&ndash;
name of the Mediaagent to remove from the commcell</p>
<p>force
(bool)
&ndash;
True if you want to delete media agent forcefully.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the media agent name argument is not string</p>
<pre><code>if failed to delete Media agent

if response is empty

if response is not success

if no media agent exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L394-L449" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, media_agent, force=False):
    &#34;&#34;&#34;Deletes the media agent from the commcell.

        Args:
            media_agent (str)  --  name of the Mediaagent to remove from the commcell

            force       (bool)     --  True if you want to delete media agent forcefully.

        Raises:
            SDKException:
                if type of the media agent name argument is not string

                if failed to delete Media agent

                if response is empty

                if response is not success

                if no media agent exists with the given name

    &#34;&#34;&#34;
    if not isinstance(media_agent, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
    else:
        media_agent = media_agent.lower()

        if self.has_media_agent(media_agent):
            mediagent_id = self.all_media_agents[media_agent][&#39;id&#39;]
            mediagent_delete_service = self._commcell_object._services[&#39;MEDIA_AGENT&#39;] % (mediagent_id)
            if force:
                mediagent_delete_service += &#34;?forceDelete=1&#34;

            flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;DELETE&#39;, mediagent_delete_service)

            error_code = 0
            if flag:
                if &#39;errorCode&#39; in response.json():
                    o_str = &#39;Failed to delete mediaagent&#39;
                    error_code = response.json()[&#39;errorCode&#39;]
                    if error_code == 0:
                        # initialize the mediaagents again
                        # so the mediaagents object has all the mediaagents
                        self.refresh()
                    else:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        if error_message:
                            o_str += &#39;\nError: &#34;{0}&#34;&#39;.format(error_message)
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
        else:
            raise SDKException(
                &#39;Storage&#39;, &#39;102&#39;, &#39;No Mediaagent exists with name: {0}&#39;.format(media_agent)
            )</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgents.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, media_agent_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a MediaAgent object of the specified media agent name.</p>
<h2 id="args">Args</h2>
<p>media_agent_name (str)
&ndash;
name of the media agent</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the MediaAgent class for the given media agent name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the media agent name argument is not string</p>
<pre><code>if no media agent exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L365-L392" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, media_agent_name):
    &#34;&#34;&#34;Returns a MediaAgent object of the specified media agent name.

        Args:
            media_agent_name (str)  --  name of the media agent

        Returns:
            object - instance of the MediaAgent class for the given media agent name

        Raises:
            SDKException:
                if type of the media agent name argument is not string

                if no media agent exists with the given name
    &#34;&#34;&#34;
    if not isinstance(media_agent_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
    else:
        media_agent_name = media_agent_name.lower()

        if self.has_media_agent(media_agent_name):
            return MediaAgent(self._commcell_object,
                              media_agent_name,
                              self._media_agents[media_agent_name][&#39;id&#39;])

        raise SDKException(
            &#39;Storage&#39;, &#39;102&#39;, &#39;No media agent exists with name: {0}&#39;.format(media_agent_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgents.has_media_agent"><code class="name flex">
<span>def <span class="ident">has_media_agent</span></span>(<span>self, media_agent_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a media agent exists in the commcell with the input media agent name.</p>
<h2 id="args">Args</h2>
<p>media_agent_name (str)
&ndash;
name of the media agent</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the media agent exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the media agent name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L347-L363" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_media_agent(self, media_agent_name):
    &#34;&#34;&#34;Checks if a media agent exists in the commcell with the input media agent name.

        Args:
            media_agent_name (str)  --  name of the media agent

        Returns:
            bool - boolean output whether the media agent exists in the commcell or not

        Raises:
            SDKException:
                if type of the media agent name argument is not string
    &#34;&#34;&#34;
    if not isinstance(media_agent_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    return self._media_agents and media_agent_name.lower() in self._media_agents</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.MediaAgents.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the media agents associated with the Commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L451-L453" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the media agents associated with the Commcell.&#34;&#34;&#34;
    self._media_agents = self._get_media_agents()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage.RPStore"><code class="flex name class">
<span>class <span class="ident">RPStore</span></span>
<span>(</span><span>commcell, rpstore_name, rpstore_id)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2578-L2590" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RPStore(object):
    def __init__(self, commcell, rpstore_name, rpstore_id):
        self._commcell = commcell
        self._rpstore_name = rpstore_name.lower()
        self._rpstore_id = rpstore_id

    @property
    def rpstore_name(self):
        return self._rpstore_name

    @property
    def rpstore_id(self):
        return self._rpstore_id</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.storage.RPStore.rpstore_id"><code class="name">var <span class="ident">rpstore_id</span></code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2588-L2590" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def rpstore_id(self):
    return self._rpstore_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.RPStore.rpstore_name"><code class="name">var <span class="ident">rpstore_name</span></code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2584-L2586" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def rpstore_name(self):
    return self._rpstore_name</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage.RPStores"><code class="flex name class">
<span>class <span class="ident">RPStores</span></span>
<span>(</span><span>commcell)</span>
</code></dt>
<dd>
<div class="desc"><p>Initialize object of the MediaAgents class.</p>
<h2 id="args">Args</h2>
<p>commcell(object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the MediaAgents class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2468-L2575" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class RPStores(object):
    def __init__(self, commcell):
        &#34;&#34;&#34;Initialize object of the MediaAgents class.

            Args:
                commcell(object)  --  instance of the Commcell class

            Returns:
                object - instance of the MediaAgents class
        &#34;&#34;&#34;
        self._commcell = commcell
        self._rp_stores = None
        self.refresh()

    def _get_rp_stores(self):
        flag, response = self._commcell._cvpysdk_object.make_request(&#39;GET&#39;, self._commcell._services[&#39;ALL_RPStores&#39;])

        try:
            if response.json().get(&#39;libraryList&#39;):
                return {library[&#34;library&#34;][&#34;libraryName&#34;].lower(): library[&#34;MountPathList&#34;][0][&#34;rpStoreLibraryInfo&#34;]
                        [&#34;rpStoreId&#34;] for library in response.json()[&#34;libraryList&#34;]}
            return {}
        except (KeyError, ValueError):
            generic_msg = &#34;Unable to fetch RPStore&#34;
            err_msg = response.json().get(&#34;errorMessage&#34;, generic_msg) if response.status_code == 200 else generic_msg
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;{0}&#39;.format(err_msg))

    def add(self, name, path, storage, media_agent_name):
        &#34;&#34;&#34;

        Args:
            name    (str):     Name of the RPStore

            path    (str):     Path of the RPStore

            storage (int):     Storage Capacity of the RPStore in GB

            media_agent_name(str)   :   Name of the media agent

        Returns:
            An instance of RPStore

        &#34;&#34;&#34;
        try:
            assert self.has_rp_store(name) is False
        except AssertionError:
            raise SDKException(&#34;Storage&#34;, 102, &#34;An RPStore already exists with the same name&#34;)

        media_agents = MediaAgents(self._commcell)
        try:
            ma_id = media_agents.all_media_agents[media_agent_name][&#34;id&#34;]
        except KeyError:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;No media agent exists with name: {0}&#39;.format(media_agent_name))

        payload = {
            &#34;rpLibrary&#34;: {&#34;maxSpacePerRPStoreGB&#34;: storage},
            &#34;storageLibrary&#34;: {
                &#34;mediaAgentId&#34;: int(ma_id),
                &#34;libraryName&#34;: name,
                &#34;mountPath&#34;: path
            },
            &#34;opType&#34;: 1
        }
        flag, response = self._commcell._cvpysdk_object.make_request(
            &#34;POST&#34;, self._commcell._services[&#34;RPSTORE&#34;], payload)

        try:
            return RPStore(self._commcell, name, response.json()[&#34;storageLibrary&#34;][&#34;libraryId&#34;])
        except KeyError:
            generic_msg = &#34;Unable to add RPStore&#34;
            err_msg = response.json().get(&#34;errorMessage&#34;, generic_msg) if flag else generic_msg
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;{0}&#39;.format(err_msg))

    def has_rp_store(self, rpstore_name):
        &#34;&#34;&#34;Validates if the given RPStore is present

        Args:
            rpstore_name       (str):   Name of the RPStore

        Returns:
            bool : True if present else False
        &#34;&#34;&#34;
        if not isinstance(rpstore_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        return rpstore_name.lower() in self._rp_stores

    def get(self, rpstore_name):
        &#34;&#34;&#34;Fetches the given RPStore

        Args:
            rpstore_name    (str):  Name of the RPStore

        Returns:
            An instance of the RPStore

        &#34;&#34;&#34;
        if not isinstance(rpstore_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        try:
            return RPStore(self._commcell, rpstore_name, self._rp_stores[rpstore_name.lower()])
        except KeyError:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;No RPStore exists with name: {0}&#39;.format(rpstore_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the media agents associated with the Commcell.&#34;&#34;&#34;
        self._rp_stores = self._get_rp_stores()</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage.RPStores.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, name, path, storage, media_agent_name)</span>
</code></dt>
<dd>
<div class="desc"><h2 id="args">Args</h2>
<p>name
(str):
Name of the RPStore</p>
<p>path
(str):
Path of the RPStore</p>
<dl>
<dt><strong><code>storage</code></strong> :&ensp;<code>int</code></dt>
<dd>
<p>Storage Capacity of the RPStore in GB</p>
</dd>
</dl>
<p>media_agent_name(str)
:
Name of the media agent</p>
<h2 id="returns">Returns</h2>
<p>An instance of RPStore</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2495-L2539" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, name, path, storage, media_agent_name):
    &#34;&#34;&#34;

    Args:
        name    (str):     Name of the RPStore

        path    (str):     Path of the RPStore

        storage (int):     Storage Capacity of the RPStore in GB

        media_agent_name(str)   :   Name of the media agent

    Returns:
        An instance of RPStore

    &#34;&#34;&#34;
    try:
        assert self.has_rp_store(name) is False
    except AssertionError:
        raise SDKException(&#34;Storage&#34;, 102, &#34;An RPStore already exists with the same name&#34;)

    media_agents = MediaAgents(self._commcell)
    try:
        ma_id = media_agents.all_media_agents[media_agent_name][&#34;id&#34;]
    except KeyError:
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;No media agent exists with name: {0}&#39;.format(media_agent_name))

    payload = {
        &#34;rpLibrary&#34;: {&#34;maxSpacePerRPStoreGB&#34;: storage},
        &#34;storageLibrary&#34;: {
            &#34;mediaAgentId&#34;: int(ma_id),
            &#34;libraryName&#34;: name,
            &#34;mountPath&#34;: path
        },
        &#34;opType&#34;: 1
    }
    flag, response = self._commcell._cvpysdk_object.make_request(
        &#34;POST&#34;, self._commcell._services[&#34;RPSTORE&#34;], payload)

    try:
        return RPStore(self._commcell, name, response.json()[&#34;storageLibrary&#34;][&#34;libraryId&#34;])
    except KeyError:
        generic_msg = &#34;Unable to add RPStore&#34;
        err_msg = response.json().get(&#34;errorMessage&#34;, generic_msg) if flag else generic_msg
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;{0}&#39;.format(err_msg))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.RPStores.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, rpstore_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Fetches the given RPStore</p>
<h2 id="args">Args</h2>
<p>rpstore_name
(str):
Name of the RPStore</p>
<h2 id="returns">Returns</h2>
<p>An instance of the RPStore</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2555-L2571" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, rpstore_name):
    &#34;&#34;&#34;Fetches the given RPStore

    Args:
        rpstore_name    (str):  Name of the RPStore

    Returns:
        An instance of the RPStore

    &#34;&#34;&#34;
    if not isinstance(rpstore_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    try:
        return RPStore(self._commcell, rpstore_name, self._rp_stores[rpstore_name.lower()])
    except KeyError:
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;No RPStore exists with name: {0}&#39;.format(rpstore_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.RPStores.has_rp_store"><code class="name flex">
<span>def <span class="ident">has_rp_store</span></span>(<span>self, rpstore_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Validates if the given RPStore is present</p>
<h2 id="args">Args</h2>
<p>rpstore_name
(str):
Name of the RPStore</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>bool </code></dt>
<dd>True if present else False</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2541-L2553" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_rp_store(self, rpstore_name):
    &#34;&#34;&#34;Validates if the given RPStore is present

    Args:
        rpstore_name       (str):   Name of the RPStore

    Returns:
        bool : True if present else False
    &#34;&#34;&#34;
    if not isinstance(rpstore_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    return rpstore_name.lower() in self._rp_stores</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.RPStores.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the media agents associated with the Commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2573-L2575" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the media agents associated with the Commcell.&#34;&#34;&#34;
    self._rp_stores = self._get_rp_stores()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage.TapeLibraries"><code class="flex name class">
<span>class <span class="ident">TapeLibraries</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for libraries</p>
<p>Initialize object of the DiskLibraries class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the DiskLibraries class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2594-L2828" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TapeLibraries(Libraries):

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the DiskLibraries class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the DiskLibraries class
        &#34;&#34;&#34;
        super().__init__(commcell_object)
        self._commcell_object = commcell_object
        self._DETECT_TAPE_LIBRARY = self._commcell_object._services[&#39;DETECT_TAPE_LIBRARY&#39;]
        self._CONFIGURE_TAPE_LIBRARY = self._commcell_object._services[&#39;CONFIGURE_TAPE_LIBRARY&#39;]
        self._LOCK_MM_CONFIGURATION = self._commcell_object._services[&#39;LOCK_MM_CONFIGURATION&#39;]


    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all tape libraries of the commcell.

            Returns:
                str - string of all the tape libraries associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Tape Library&#39;)

        for index, library in enumerate(self._libraries):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, library)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the TapeLibraries class.&#34;&#34;&#34;
        return &#34;TapeLibraries class instance for Commcell&#34;



    def get(self, tape_library_name):
        &#34;&#34;&#34;
        Returns the object of TapeLibrary class of the specified library name

                    Args:
                        library_name (str)  --  name of the library

                    Returns:
                        object - object of TapeLibrary class of the specified library name

                    Raises:
                        SDKException:
                            if type of the library name argument is not string
        &#34;&#34;&#34;

        if not isinstance(tape_library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            if self.has_library(tape_library_name):
                tape_library_name = tape_library_name.lower()
                return TapeLibrary(self._commcell_object, tape_library_name, self._libraries[tape_library_name])

    def delete(self, tape_library_name):
        &#34;&#34;&#34;
        Deletes the specified library

                    Args:
                        tape_library_name (str)  --  name of the library

                    Returns:
                        bool - returns true if the library deleted successfully

                    Raises:
                        SDKException:
                            if type of the library name argument is not string
                            if library does not exists
                            if its failed to delete the library
        &#34;&#34;&#34;

        if not isinstance(tape_library_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if not self.has_library(tape_library_name):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Invalid library name&#34;)

        pay_load={
                &#34;isDeconfigLibrary&#34;: 1,
                &#34;library&#34;: {
                    &#34;opType&#34;: 2,
                    &#34;libraryName&#34;: tape_library_name
                }
        }
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._LIBRARY, pay_load)

        if not flag:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to DELETE the library&#34;)

        self.refresh()


    def __lock_unlock_mm_configuration(self, operation):
        &#34;&#34;&#34;
                Locks or unlocks the MM config for tape library detection

                            Args:
                                operation (int)  --  operation type
                                                            1 : Lock
                                                            0 : Unlock
                                                            2: Force lock

                            Raises:
                                SDKException:
                                    If API call is not successful
                                    If API response is invalid
                                    If errorCode is not part of response JSON
                                    If lock/unlock operation fails
        &#34;&#34;&#34;

        if not isinstance(operation, int):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Invalid Operation data type. Expected is integer&#34;)

        if not operation in [0,1,2]:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Invalid Operation type. Expected among [0,1,2] but received &#34;+str(operation))

        pay_load ={
        &#34;configLockUnlock&#34;: {
        &#34;lockType&#34;: operation
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._LOCK_MM_CONFIGURATION, pay_load)

        if flag :
            if response and response.json():
                if &#39;errorCode&#39; in response.json():
                    if response.json()[&#39;errorCode&#39;] != 0:
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to lock the MM Config. errorMessage : &#34;+response.json().get(&#39;errorMessage&#39;))
                else:
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;,
                                       &#34;lock_unlock_mm_configuration :: Error code is not part of response JSON&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#34;Invalid response&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#34;API call is not successful&#34;)

    def lock_mm_configuration(self, forceLock = False):
        &#34;&#34;&#34;
            Locks the MM config for tape library detection

                Args:
                    forceLock (bool)  --  True for force lock
        &#34;&#34;&#34;
        if forceLock:
            self.__lock_unlock_mm_configuration(2)
            return
        self.__lock_unlock_mm_configuration(1)

    def unlock_mm_configuration(self):
        &#34;&#34;&#34;
            Unlocks the MM config for tape library detection
        &#34;&#34;&#34;
        self.__lock_unlock_mm_configuration(0)


    def detect_tape_library(self, mediaagents):
        &#34;&#34;&#34;
        Detect the tape libraries(s) of the provided MediaAgent(s)

                    Args:
                        mediaagents (list)  --  The list of the mediaagent(s)

                    Returns:
                        JSON - JSON of the tape library detections response

                    Raises:
                        SDKException:
                            if its fails to detect
        &#34;&#34;&#34;

        pay_load ={
        &#34;autoDetect&#34;: True,
        &#34;mediaAgentIdList&#34;: mediaagents
        }

        try:
            self.lock_mm_configuration()
            flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._DETECT_TAPE_LIBRARY, pay_load )
        finally:
            self.unlock_mm_configuration()

        if flag and response.json():
            return response.json()
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to detect library&#34;)

    def configure_tape_library(self,tape_library_name, mediaagents):
        &#34;&#34;&#34;
        Configure the tape library

                    Args:
                        tape_library_name (str)  --  name of the library

                        mediaagents(list) -- list of MediaAgents to use for configuration

                    Returns:
                        object - object of the TapeLibrary class for the specified tape library

                    Raises:
                        SDKException:
                            if fails to configure the tape library
        &#34;&#34;&#34;

        libraries=self.detect_tape_library(mediaagents)
        flag=False
        for lib in libraries[&#39;libraries&#39;]:

            if lib[&#39;libraryName&#39;] == tape_library_name:
                drive_list=lib[&#39;drives&#39;]

                pay_load= {
                    &#34;driveList&#34;: drive_list,
                    &#34;hdr&#34;: {
                        &#34;tag&#34;: 0
                    }
                }
                flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._CONFIGURE_TAPE_LIBRARY,
                                                                                    pay_load)
                break

        if not flag:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to configure the library&#34;)

        self.refresh()

        tape_library_name = tape_library_name.lower()
        for lib_name, lib_id in self._libraries.items():
            if lib_name.startswith(tape_library_name + &#34; &#34;):
                return self.get(lib_name)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.storage.Libraries" href="#cvpysdk.storage.Libraries">Libraries</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage.TapeLibraries.configure_tape_library"><code class="name flex">
<span>def <span class="ident">configure_tape_library</span></span>(<span>self, tape_library_name, mediaagents)</span>
</code></dt>
<dd>
<div class="desc"><p>Configure the tape library</p>
<pre><code>        Args:
            tape_library_name (str)  --  name of the library

            mediaagents(list) -- list of MediaAgents to use for configuration

        Returns:
            object - object of the TapeLibrary class for the specified tape library

        Raises:
            SDKException:
                if fails to configure the tape library
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2786-L2828" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_tape_library(self,tape_library_name, mediaagents):
    &#34;&#34;&#34;
    Configure the tape library

                Args:
                    tape_library_name (str)  --  name of the library

                    mediaagents(list) -- list of MediaAgents to use for configuration

                Returns:
                    object - object of the TapeLibrary class for the specified tape library

                Raises:
                    SDKException:
                        if fails to configure the tape library
    &#34;&#34;&#34;

    libraries=self.detect_tape_library(mediaagents)
    flag=False
    for lib in libraries[&#39;libraries&#39;]:

        if lib[&#39;libraryName&#39;] == tape_library_name:
            drive_list=lib[&#39;drives&#39;]

            pay_load= {
                &#34;driveList&#34;: drive_list,
                &#34;hdr&#34;: {
                    &#34;tag&#34;: 0
                }
            }
            flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._CONFIGURE_TAPE_LIBRARY,
                                                                                pay_load)
            break

    if not flag:
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to configure the library&#34;)

    self.refresh()

    tape_library_name = tape_library_name.lower()
    for lib_name, lib_id in self._libraries.items():
        if lib_name.startswith(tape_library_name + &#34; &#34;):
            return self.get(lib_name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.TapeLibraries.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, tape_library_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the specified library</p>
<pre><code>        Args:
            tape_library_name (str)  --  name of the library

        Returns:
            bool - returns true if the library deleted successfully

        Raises:
            SDKException:
                if type of the library name argument is not string
                if library does not exists
                if its failed to delete the library
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2654-L2689" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, tape_library_name):
    &#34;&#34;&#34;
    Deletes the specified library

                Args:
                    tape_library_name (str)  --  name of the library

                Returns:
                    bool - returns true if the library deleted successfully

                Raises:
                    SDKException:
                        if type of the library name argument is not string
                        if library does not exists
                        if its failed to delete the library
    &#34;&#34;&#34;

    if not isinstance(tape_library_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    if not self.has_library(tape_library_name):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Invalid library name&#34;)

    pay_load={
            &#34;isDeconfigLibrary&#34;: 1,
            &#34;library&#34;: {
                &#34;opType&#34;: 2,
                &#34;libraryName&#34;: tape_library_name
            }
    }
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._LIBRARY, pay_load)

    if not flag:
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to DELETE the library&#34;)

    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.TapeLibraries.detect_tape_library"><code class="name flex">
<span>def <span class="ident">detect_tape_library</span></span>(<span>self, mediaagents)</span>
</code></dt>
<dd>
<div class="desc"><p>Detect the tape libraries(s) of the provided MediaAgent(s)</p>
<pre><code>        Args:
            mediaagents (list)  --  The list of the mediaagent(s)

        Returns:
            JSON - JSON of the tape library detections response

        Raises:
            SDKException:
                if its fails to detect
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2756-L2784" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def detect_tape_library(self, mediaagents):
    &#34;&#34;&#34;
    Detect the tape libraries(s) of the provided MediaAgent(s)

                Args:
                    mediaagents (list)  --  The list of the mediaagent(s)

                Returns:
                    JSON - JSON of the tape library detections response

                Raises:
                    SDKException:
                        if its fails to detect
    &#34;&#34;&#34;

    pay_load ={
    &#34;autoDetect&#34;: True,
    &#34;mediaAgentIdList&#34;: mediaagents
    }

    try:
        self.lock_mm_configuration()
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._DETECT_TAPE_LIBRARY, pay_load )
    finally:
        self.unlock_mm_configuration()

    if flag and response.json():
        return response.json()
    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Failed to detect library&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.TapeLibraries.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, tape_library_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the object of TapeLibrary class of the specified library name</p>
<pre><code>        Args:
            library_name (str)  --  name of the library

        Returns:
            object - object of TapeLibrary class of the specified library name

        Raises:
            SDKException:
                if type of the library name argument is not string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2632-L2652" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, tape_library_name):
    &#34;&#34;&#34;
    Returns the object of TapeLibrary class of the specified library name

                Args:
                    library_name (str)  --  name of the library

                Returns:
                    object - object of TapeLibrary class of the specified library name

                Raises:
                    SDKException:
                        if type of the library name argument is not string
    &#34;&#34;&#34;

    if not isinstance(tape_library_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
    else:
        if self.has_library(tape_library_name):
            tape_library_name = tape_library_name.lower()
            return TapeLibrary(self._commcell_object, tape_library_name, self._libraries[tape_library_name])</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.TapeLibraries.lock_mm_configuration"><code class="name flex">
<span>def <span class="ident">lock_mm_configuration</span></span>(<span>self, forceLock=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Locks the MM config for tape library detection</p>
<pre><code>Args:
    forceLock (bool)  --  True for force lock
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2737-L2747" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def lock_mm_configuration(self, forceLock = False):
    &#34;&#34;&#34;
        Locks the MM config for tape library detection

            Args:
                forceLock (bool)  --  True for force lock
    &#34;&#34;&#34;
    if forceLock:
        self.__lock_unlock_mm_configuration(2)
        return
    self.__lock_unlock_mm_configuration(1)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.TapeLibraries.unlock_mm_configuration"><code class="name flex">
<span>def <span class="ident">unlock_mm_configuration</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Unlocks the MM config for tape library detection</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2749-L2753" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def unlock_mm_configuration(self):
    &#34;&#34;&#34;
        Unlocks the MM config for tape library detection
    &#34;&#34;&#34;
    self.__lock_unlock_mm_configuration(0)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.storage.Libraries" href="#cvpysdk.storage.Libraries">Libraries</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.storage.Libraries.has_library" href="#cvpysdk.storage.Libraries.has_library">has_library</a></code></li>
<li><code><a title="cvpysdk.storage.Libraries.refresh" href="#cvpysdk.storage.Libraries.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.storage.TapeLibrary"><code class="flex name class">
<span>class <span class="ident">TapeLibrary</span></span>
<span>(</span><span>commcell_object, tape_library_name, tape_library_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Initialize object of the TapeLibrary class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class
tape_library_name (string) &ndash; name of the tape library
tape_library_id (int) &ndash; tape library ID</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the TapeLibrary class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2832-L2949" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TapeLibrary(object):

    def __init__(self, commcell_object, tape_library_name, tape_library_id=None):
        &#34;&#34;&#34;Initialize object of the TapeLibrary class.

            Args:
                commcell_object (object)  --  instance of the Commcell class
                tape_library_name (string) -- name of the tape library
                tape_library_id (int) -- tape library ID

            Returns:
                object - instance of the TapeLibrary class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._name = tape_library_name
        if tape_library_id:
            self._library_id = str(tape_library_id)
        else:
            self._library_id = self._get_library_id()

        self._library_properties_service = self._commcell_object._services[
                                               &#39;GET_LIBRARY_PROPERTIES&#39;] % (self._library_id)

        self.library_properties = self._get_library_properties()

        self._name = self.library_properties[&#39;library&#39;][&#39;libraryName&#39;]


    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of the specified tape library.

            Returns:
                str - string of all the tape library associated with the commcell
        &#34;&#34;&#34;
        representation_string = &#34;TapeLibrary instance of library : {0}&#34;

        return representation_string.format(self._name)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the TapeLibrary instance of this class.&#34;&#34;&#34;
        representation_string = &#39;TapeLibrary class instance for library: &#34;{0}&#34; of Commcell: &#34;{1}&#34;&#39;
        return representation_string.format(
            self._name, self._commcell_object.commserv_name
        )


    def _get_library_id(self):
        &#34;&#34;&#34;Gets the library id associated with this tape library.

            Returns:
                str - id associated with this tape library
        &#34;&#34;&#34;
        libraries = TapeLibraries(self._commcell_object)
        return libraries.get(self.library_name).library_id


    def get_drive_list(self):
        &#34;&#34;&#34;
            Returns the tape drive list of this tape library

            Returns:
                list - List of the drives of this tape library
        &#34;&#34;&#34;

        self.refresh()

        drive_list=[]

        if &#39;DriveList&#39; in self.library_properties:
            for drive in self.library_properties[&#34;DriveList&#34;]:
                drive_list.append(drive[&#34;driveName&#34;])

        return drive_list


    def _get_library_properties(self):
        &#34;&#34;&#34;Gets the tape library properties.

            Returns:
                dict - dictionary consisting of the properties of this library

            Raises:
                SDKException:
                    if response is empty

                    if failed to get tape library properties

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._library_properties_service
        )

        if flag:
            if response.json():
                if &#39;libraryInfo&#39; in response.json():
                    return response.json()[&#39;libraryInfo&#39;]
                raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#39;Failed to get tape Library properties&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of this tape library.&#34;&#34;&#34;
        self.library_properties = self._get_library_properties()


    @property
    def library_name(self):
        &#34;&#34;&#34;Treats the library name as a read-only attribute.&#34;&#34;&#34;
        return self._name

    @property
    def library_id(self):
        &#34;&#34;&#34;Treats the library ID as a read-only attribute.&#34;&#34;&#34;
        return self._library_id</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.storage.TapeLibrary.library_id"><code class="name">var <span class="ident">library_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the library ID as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2946-L2949" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def library_id(self):
    &#34;&#34;&#34;Treats the library ID as a read-only attribute.&#34;&#34;&#34;
    return self._library_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.TapeLibrary.library_name"><code class="name">var <span class="ident">library_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the library name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2941-L2944" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def library_name(self):
    &#34;&#34;&#34;Treats the library name as a read-only attribute.&#34;&#34;&#34;
    return self._name</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage.TapeLibrary.get_drive_list"><code class="name flex">
<span>def <span class="ident">get_drive_list</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the tape drive list of this tape library</p>
<h2 id="returns">Returns</h2>
<p>list - List of the drives of this tape library</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2889-L2905" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_drive_list(self):
    &#34;&#34;&#34;
        Returns the tape drive list of this tape library

        Returns:
            list - List of the drives of this tape library
    &#34;&#34;&#34;

    self.refresh()

    drive_list=[]

    if &#39;DriveList&#39; in self.library_properties:
        for drive in self.library_properties[&#34;DriveList&#34;]:
            drive_list.append(drive[&#34;driveName&#34;])

    return drive_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage.TapeLibrary.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of this tape library.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage.py#L2936-L2938" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of this tape library.&#34;&#34;&#34;
    self.library_properties = self._get_library_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.storage.DiskLibraries" href="#cvpysdk.storage.DiskLibraries">DiskLibraries</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage.DiskLibraries.add" href="#cvpysdk.storage.DiskLibraries.add">add</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibraries.all_disk_libraries" href="#cvpysdk.storage.DiskLibraries.all_disk_libraries">all_disk_libraries</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibraries.delete" href="#cvpysdk.storage.DiskLibraries.delete">delete</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibraries.get" href="#cvpysdk.storage.DiskLibraries.get">get</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage.DiskLibrary" href="#cvpysdk.storage.DiskLibrary">DiskLibrary</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage.DiskLibrary.add_cloud_mount_path" href="#cvpysdk.storage.DiskLibrary.add_cloud_mount_path">add_cloud_mount_path</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.add_mount_path" href="#cvpysdk.storage.DiskLibrary.add_mount_path">add_mount_path</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.add_storage_accelerator_credential" href="#cvpysdk.storage.DiskLibrary.add_storage_accelerator_credential">add_storage_accelerator_credential</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.advanced_library_properties" href="#cvpysdk.storage.DiskLibrary.advanced_library_properties">advanced_library_properties</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.change_device_access_type" href="#cvpysdk.storage.DiskLibrary.change_device_access_type">change_device_access_type</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.free_space" href="#cvpysdk.storage.DiskLibrary.free_space">free_space</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.library_id" href="#cvpysdk.storage.DiskLibrary.library_id">library_id</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.library_name" href="#cvpysdk.storage.DiskLibrary.library_name">library_name</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.library_properties" href="#cvpysdk.storage.DiskLibrary.library_properties">library_properties</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.media_agent" href="#cvpysdk.storage.DiskLibrary.media_agent">media_agent</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.media_agents_associated" href="#cvpysdk.storage.DiskLibrary.media_agents_associated">media_agents_associated</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.modify_cloud_access_type" href="#cvpysdk.storage.DiskLibrary.modify_cloud_access_type">modify_cloud_access_type</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.mount_path" href="#cvpysdk.storage.DiskLibrary.mount_path">mount_path</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.mountpath_usage" href="#cvpysdk.storage.DiskLibrary.mountpath_usage">mountpath_usage</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.move_mountpath" href="#cvpysdk.storage.DiskLibrary.move_mountpath">move_mountpath</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.name" href="#cvpysdk.storage.DiskLibrary.name">name</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.refresh" href="#cvpysdk.storage.DiskLibrary.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.set_max_data_to_write_on_mount_path" href="#cvpysdk.storage.DiskLibrary.set_max_data_to_write_on_mount_path">set_max_data_to_write_on_mount_path</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.set_mountpath_preferred_on_mediaagent" href="#cvpysdk.storage.DiskLibrary.set_mountpath_preferred_on_mediaagent">set_mountpath_preferred_on_mediaagent</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.set_mountpath_reserve_space" href="#cvpysdk.storage.DiskLibrary.set_mountpath_reserve_space">set_mountpath_reserve_space</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.share_mount_path" href="#cvpysdk.storage.DiskLibrary.share_mount_path">share_mount_path</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.update_device_controller" href="#cvpysdk.storage.DiskLibrary.update_device_controller">update_device_controller</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.validate_mountpath" href="#cvpysdk.storage.DiskLibrary.validate_mountpath">validate_mountpath</a></code></li>
<li><code><a title="cvpysdk.storage.DiskLibrary.verify_media" href="#cvpysdk.storage.DiskLibrary.verify_media">verify_media</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage.Libraries" href="#cvpysdk.storage.Libraries">Libraries</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage.Libraries.has_library" href="#cvpysdk.storage.Libraries.has_library">has_library</a></code></li>
<li><code><a title="cvpysdk.storage.Libraries.refresh" href="#cvpysdk.storage.Libraries.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage.MediaAgent" href="#cvpysdk.storage.MediaAgent">MediaAgent</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage.MediaAgent.change_index_cache" href="#cvpysdk.storage.MediaAgent.change_index_cache">change_index_cache</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.current_power_status" href="#cvpysdk.storage.MediaAgent.current_power_status">current_power_status</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.enable_power_management" href="#cvpysdk.storage.MediaAgent.enable_power_management">enable_power_management</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.index_cache_enabled" href="#cvpysdk.storage.MediaAgent.index_cache_enabled">index_cache_enabled</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.index_cache_path" href="#cvpysdk.storage.MediaAgent.index_cache_path">index_cache_path</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.is_online" href="#cvpysdk.storage.MediaAgent.is_online">is_online</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.is_power_management_enabled" href="#cvpysdk.storage.MediaAgent.is_power_management_enabled">is_power_management_enabled</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.mark_for_maintenance" href="#cvpysdk.storage.MediaAgent.mark_for_maintenance">mark_for_maintenance</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.media_agent_id" href="#cvpysdk.storage.MediaAgent.media_agent_id">media_agent_id</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.media_agent_name" href="#cvpysdk.storage.MediaAgent.media_agent_name">media_agent_name</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.name" href="#cvpysdk.storage.MediaAgent.name">name</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.platform" href="#cvpysdk.storage.MediaAgent.platform">platform</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.power_off" href="#cvpysdk.storage.MediaAgent.power_off">power_off</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.power_on" href="#cvpysdk.storage.MediaAgent.power_on">power_on</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.refresh" href="#cvpysdk.storage.MediaAgent.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.set_concurrent_lan" href="#cvpysdk.storage.MediaAgent.set_concurrent_lan">set_concurrent_lan</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.set_ransomware_protection" href="#cvpysdk.storage.MediaAgent.set_ransomware_protection">set_ransomware_protection</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.set_state" href="#cvpysdk.storage.MediaAgent.set_state">set_state</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgent.wait_for_power_status" href="#cvpysdk.storage.MediaAgent.wait_for_power_status">wait_for_power_status</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage.MediaAgents" href="#cvpysdk.storage.MediaAgents">MediaAgents</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage.MediaAgents.all_media_agents" href="#cvpysdk.storage.MediaAgents.all_media_agents">all_media_agents</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgents.delete" href="#cvpysdk.storage.MediaAgents.delete">delete</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgents.get" href="#cvpysdk.storage.MediaAgents.get">get</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgents.has_media_agent" href="#cvpysdk.storage.MediaAgents.has_media_agent">has_media_agent</a></code></li>
<li><code><a title="cvpysdk.storage.MediaAgents.refresh" href="#cvpysdk.storage.MediaAgents.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage.RPStore" href="#cvpysdk.storage.RPStore">RPStore</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage.RPStore.rpstore_id" href="#cvpysdk.storage.RPStore.rpstore_id">rpstore_id</a></code></li>
<li><code><a title="cvpysdk.storage.RPStore.rpstore_name" href="#cvpysdk.storage.RPStore.rpstore_name">rpstore_name</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage.RPStores" href="#cvpysdk.storage.RPStores">RPStores</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage.RPStores.add" href="#cvpysdk.storage.RPStores.add">add</a></code></li>
<li><code><a title="cvpysdk.storage.RPStores.get" href="#cvpysdk.storage.RPStores.get">get</a></code></li>
<li><code><a title="cvpysdk.storage.RPStores.has_rp_store" href="#cvpysdk.storage.RPStores.has_rp_store">has_rp_store</a></code></li>
<li><code><a title="cvpysdk.storage.RPStores.refresh" href="#cvpysdk.storage.RPStores.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage.TapeLibraries" href="#cvpysdk.storage.TapeLibraries">TapeLibraries</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage.TapeLibraries.configure_tape_library" href="#cvpysdk.storage.TapeLibraries.configure_tape_library">configure_tape_library</a></code></li>
<li><code><a title="cvpysdk.storage.TapeLibraries.delete" href="#cvpysdk.storage.TapeLibraries.delete">delete</a></code></li>
<li><code><a title="cvpysdk.storage.TapeLibraries.detect_tape_library" href="#cvpysdk.storage.TapeLibraries.detect_tape_library">detect_tape_library</a></code></li>
<li><code><a title="cvpysdk.storage.TapeLibraries.get" href="#cvpysdk.storage.TapeLibraries.get">get</a></code></li>
<li><code><a title="cvpysdk.storage.TapeLibraries.lock_mm_configuration" href="#cvpysdk.storage.TapeLibraries.lock_mm_configuration">lock_mm_configuration</a></code></li>
<li><code><a title="cvpysdk.storage.TapeLibraries.unlock_mm_configuration" href="#cvpysdk.storage.TapeLibraries.unlock_mm_configuration">unlock_mm_configuration</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage.TapeLibrary" href="#cvpysdk.storage.TapeLibrary">TapeLibrary</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage.TapeLibrary.get_drive_list" href="#cvpysdk.storage.TapeLibrary.get_drive_list">get_drive_list</a></code></li>
<li><code><a title="cvpysdk.storage.TapeLibrary.library_id" href="#cvpysdk.storage.TapeLibrary.library_id">library_id</a></code></li>
<li><code><a title="cvpysdk.storage.TapeLibrary.library_name" href="#cvpysdk.storage.TapeLibrary.library_name">library_name</a></code></li>
<li><code><a title="cvpysdk.storage.TapeLibrary.refresh" href="#cvpysdk.storage.TapeLibrary.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>