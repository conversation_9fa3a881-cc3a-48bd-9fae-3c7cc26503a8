import requests
import urllib3

# This operation logs off users from Web Server and the command line interface.

# Suppress warnings for insecure requests in a lab environment
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# The full URL for the client API endpoint
#url = "http://192.168.100.55/commandcenter/api/Client" # this is the working endpoint
# url = "http://192.168.100.55:81/SearchSvc/CVWebService.svc/Sessions"
url = "http://192.168.100.55/webconsole/api/Sessions"
# url = "http://192.168.100.55/webconsole/api/User/"

'''
https://documentation.commvault.com/v11/essential/available_web_services_for_rest_api.html
'''

# The authentication token obtained from a previous login request
auth_token = "QSDK 3a63874c8abd5e922c879a82f0d9180670e17fe14d6b75b342bb5e96e28889efc37e8e574387403617b4ea1cad8d5475c40ddffc10f242d6d9b605658786af3b4781e89a5a0bea354e4a3fd764e94cf4d75446c0c6272d6b670b925ed0ed3e7b1de7f683aa444756b0dab71345371dd8afe39bbe7cbe0bc98bcbe420fa43d593af72598ccf93a9f4bb5c830a6c8e105f5ac1a71cd4e95d9e6dd5b4de88772d7d7c098892d7dccb4bdd9fe2ee7b8057f087fdee29fef6e53b062cb7607f209b51dc561321aaaab3d02ff650af437ddb95d8d0fc2beeae9bc44"
# Headers for the request, including the token in the Cookie2 header
headers = {
    "Accept": "application/json",
    "Cookie2": auth_token
}

# The JSON payload with the list of sessions to be deleted
data = {
    "sessions": [
        "6CC95260-85A1-4C35-A64F-710194983EDE"
    ]
}


# Make the DELETE request

try:
    # Make the DELETE request with the specified URL, headers, and JSON data
    response = requests.delete(url, headers=headers, json=data, verify=False)

    # Raise an HTTPError for bad responses (4xx or 5xx)
    response.raise_for_status()

    # Print the response content
    print("Status Code:", response.status_code)
    print("Response Body:", response.text)  # Or response.json() if the server returns JSON

except requests.exceptions.RequestException as e:
    print(f"An error occurred: {e}")
