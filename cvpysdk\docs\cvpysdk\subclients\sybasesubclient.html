<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.sybasesubclient API documentation</title>
<meta name="description" content="Main File for performing Sybase Subclient Operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.sybasesubclient</code></h1>
</header>
<section id="section-intro">
<p>Main File for performing Sybase Subclient Operations</p>
<p>SybaseSubclient is the only class defined in this file.</p>
<p>SybaseSubclient :
Derived class from DatabaseSubclient Base class,
representing an Sybase subclient,
and to perform operations on that subclient</p>
<h2 id="sybasesubclient">Sybasesubclient</h2>
<p><strong>init</strong>()
&ndash;
initialise object of sybase
subclient object associated
with the specified instance</p>
<p>_get_subclient_properties
&ndash;
get the all subclient related
properties of this subclient</p>
<p>_sybase_backup_request_json
&ndash;
Returns the JSON request to pass to
the API as per the options selected by the user</p>
<p>is_snapenabled()
&ndash;
Check if intellisnap has been enabled
in the subclient and sets it accordingly</p>
<p>snap_engine()
&ndash;
updates snap_engine for sybase subclient</p>
<p>snap_proxy()
&ndash;
updates proxy name from sybase snap operation</p>
<p>use_dump_based_backup_copy()
&ndash;
updates the use of dump based method for backup copy</p>
<p>dump_based_backup_copy_option()
&ndash;
updates subtype of dump based operation</p>
<p>configured_instance()
&ndash;
updates configured instance name
for dump based backup copy type 1</p>
<p>auxiliary_sybase_server()
&ndash;
updates custom instance properties
for dump based backup copy type 2</p>
<p>content()
&ndash;
update the content of
the sybase
subclient</p>
<p>backup()
&ndash;
Run a backup job for the subclient</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L1-L530" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
Main File for performing Sybase Subclient Operations

SybaseSubclient is the only class defined in this file.

SybaseSubclient :       Derived class from DatabaseSubclient Base class,
                        representing an Sybase subclient,
                        and to perform operations on that subclient

SybaseSubclient:

    __init__()                          --  initialise object of sybase
                                            subclient object associated
                                            with the specified instance

    _get_subclient_properties           --  get the all subclient related
                                            properties of this subclient

    _sybase_backup_request_json         --  Returns the JSON request to pass to
                                            the API as per the options selected by the user

    is_snapenabled()                    --  Check if intellisnap has been enabled
                                            in the subclient and sets it accordingly

    snap_engine()                       --  updates snap_engine for sybase subclient

    snap_proxy()                        --  updates proxy name from sybase snap operation

    use_dump_based_backup_copy()        --  updates the use of dump based method for backup copy

    dump_based_backup_copy_option()     --  updates subtype of dump based operation

    configured_instance()               --  updates configured instance name
                                            for dump based backup copy type 1

    auxiliary_sybase_server()           --  updates custom instance properties
                                            for dump based backup copy type 2

    content()                           --  update the content of
                                            the sybase  subclient

    backup()                            --  Run a backup job for the subclient


&#34;&#34;&#34;
from __future__ import unicode_literals
from ..subclient import Subclient
from ..exception import SDKException


class SybaseSubclient(Subclient):
    &#34;&#34;&#34;
    Base class consisting of all the common properties and operations for a Sybase Subclient
    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Initialize Sybase Subclient Object

        Args:
            backupset_object  (object)  --  instance of the Backupset class

            subclient_name    (str)     --  name of the subclient

            subclient_id      (str)     --  id of the subclient
                                            default : None


        Returns :
            (object) - instance of the Sybase Subclient class

        &#34;&#34;&#34;
        self._sybase_properties = {}
        self._snap_copy_info = None
        super(SybaseSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

    def _get_subclient_properties(self):
        &#34;&#34;&#34;
        Gets the subclient related properties of Sybase subclient
        &#34;&#34;&#34;

        super(SybaseSubclient, self)._get_subclient_properties()
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

        self._snap_copy_info = self._commonProperties.get(&#39;snapCopyInfo&#39;)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;
        Get the all subclient related properties of this subclient.

           Returns:
                (dict) - all subclient properties put inside a dict

        &#34;&#34;&#34;
        return {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1,
                    &#34;snapCopyInfo&#34;: self._snap_copy_info
                }
        }

    def _sybase_backup_request_json(self,
                                    backup_level,
                                    do_not_truncate_log=False,
                                    sybase_skip_full_after_logbkp=False,
                                    create_backup_copy_immediately=False,
                                    backup_copy_type=2,
                                    directive_file=None):
        &#34;&#34;&#34;
        Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
               backup_level                     (list)  --  level of backup the user wish to run
                                                            Full / Incremental / Differential

               do_not_truncate_log              (bool)  --  Sybase truncate log option
                                                            for incremental backup
                                                            default : False

               sybase_skip_full_after_logbkp    (bool)  --  Sybase backup option for incremental
                                                            default : False

               create_backup_copy_immediately   (bool)  --  Sybase snap job needs
                                                            this backup copy operation
                                                            default : False

               backup_copy_type                 (int)   --  backup copy job to be launched
                                                            based on below two options
                                                            default : 2, possible values :
                                                            1 (USING_STORAGE_POLICY_RULE),
                                                            2( USING_LATEST_CYCLE)

               directive_file                   (str)   --  inputfile for ondemand backup
                                                            containing database list
                                                            default : None

            Returns:

                (dict) - JSON request to pass to the API

        &#34;&#34;&#34;
        request_json = self._backup_json(backup_level, False, &#34;BEFORE_SYNTH&#34;)
        sybase_options = {
            &#34;doNotTruncateLog&#34;: do_not_truncate_log,
            &#34;sybaseSkipFullafterLogBkp&#34;: sybase_skip_full_after_logbkp
        }

        if create_backup_copy_immediately:
            sub_opt = {&#34;dataOpt&#34;:
                       {
                           &#34;createBackupCopyImmediately&#34;: create_backup_copy_immediately,
                           &#34;backupCopyType&#34;: backup_copy_type
                       }
                      }
            sybase_options.update(sub_opt)
        if self._commonProperties.get(&#34;onDemandSubClient&#34;, False):
            on_demand_input = {&#34;onDemandInputFile&#34;:directive_file}
            sybase_options.update(on_demand_input)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;].update(
            sybase_options
        )
        return request_json


    @property
    def is_snapenabled(self):
        &#34;&#34;&#34;
        Getter to check whether the subclient has snap enabled

        Returns:
            (bool)  -    boolean value based on snap
                         status at subclient level

                True    -  returns Truee if snap is enabled on the subclient
                False   -  returns False if snap is not
                            enabled at subclient level

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#34;isSnapBackupEnabled&#34;, False)

    @is_snapenabled.setter
    def is_snapenabled(self, value):
        &#34;&#34;&#34;
        To set is snap enabled to true or false

        Args:
            value           (bool) --   to enable snap at subclient level or not

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;isSnapBackupEnabled&#39;]&#34;, value)

    @property
    def snap_engine(self):
        &#34;&#34;&#34;
        Getter to fetch snap_engine

        Returns:
            (str)     -  name of snap engine at subclient level

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#39;snapToTapeSelectedEngine&#39;, {}).get(&#39;snapShotEngineName&#39;)

    @snap_engine.setter
    def snap_engine(self, engine_name):
        &#34;&#34;&#34;
        To set snap engine name

        Args:
            engine_name           (str) --      name of snap engine
                                                for intellisnap

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;snapToTapeSelectedEngine&#39;][&#39;snapShotEngineName&#39;]&#34;, engine_name)

    @property
    def snap_proxy(self):
        &#34;&#34;&#34;
        Getter to snap_proxy if set any

        Returns:
            (str)     --    name of proxy client used
                            for intellisnap operation

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#39;snapToTapeProxyToUse&#39;, {}).get(&#39;clientName&#39;)

    @snap_proxy.setter
    def snap_proxy(self, proxy_name):
        &#34;&#34;&#34;
        Setter for snap proxy name

        Args:
            proxy_name           (str) --   snap proxy name

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;snapToTapeProxyToUse&#39;][&#39;clientName&#39;]&#34;, proxy_name)

    @property
    def use_dump_based_backup_copy(self):
        &#34;&#34;&#34;
        Getter to status of dumpbased backup copy

        Returns:
            (bool)     -    checks if dump based backup
                            copy is enabled or not

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#39;useDumpBasedBackupCopy&#39;)

    @use_dump_based_backup_copy.setter
    def use_dump_based_backup_copy(self, dump_based):
        &#34;&#34;&#34;
        To enable dump based backup copy

        Args:
            dump_based      (bool)     --      set true  to enable dump
                                               based backup copy option

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;useDumpBasedBackupCopy&#39;]&#34;, dump_based)

    @property
    def dump_based_backup_copy_option(self):
        &#34;&#34;&#34;
        Getter to fetch dumpbased backup copy option :
        1(configured instance), 2(custom new instance)

        Returns:
            (int)     -  returns 1 or 2 based type of sybase instance configured

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#34;dumpBasedBackupCopyOption&#34;)

    @dump_based_backup_copy_option.setter
    def dump_based_backup_copy_option(self, dump_based_backup_copy_option):
        &#34;&#34;&#34;
        Enable dump based backup copy

        Args:
            dump_based_backup_copy_option      (int) --  set 1(configured instance),
                                                             2(custom new instance)
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;dumpBasedBackupCopyOption&#39;]&#34;, dump_based_backup_copy_option)

    @property
    def configured_instance(self):
        &#34;&#34;&#34;
        Getter to fetch configured instance
        name if dump based backup copy option is 2
        Returns:
            (str)       -       string of configured instance
                                if dump based backup option is 1

        Raises:
            SDK Exception
                if dump based backup copy not enabled

                if dump based copy option is not 1

        &#34;&#34;&#34;
        if self._snap_copy_info.get(&#39;useDumpBasedBackupCopy&#39;):
            if self._snap_copy_info.get(&#39;dumpBasedBackupCopyOption&#39;) == 1:
                return self._snap_copy_info[&#39;configuredSybaseInstance&#39;][&#39;instanceName&#39;]
            else:
                raise SDKException(
                    &#39;Subclient&#39;, &#39;102&#39;, &#34;Invalid dump based copy option&#34;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#34;Dump based parameter is not available&#34;)

    @configured_instance.setter
    def configured_instance(self, instance_name):
        &#34;&#34;&#34;
        Setter for configured instance
        name for dump based backup copy option

        Args:
            instance_name       (str) --    string of instance name
                                            to be used for dump based backup copy

        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;configuredSybaseInstance&#39;][&#39;instanceName&#39;]&#34;, instance_name)

    @property
    def auxiliary_sybase_server(self):
        &#34;&#34;&#34;
        Getter to fetch custom instance properties if dump based copy option is 2

        Returns:
            (dict)       -  dict of four properties
                            for custom instance
        Raises:
            SDK Exception
                if dump based backup copy not enabled

                if dump based copy option is not 2

        &#34;&#34;&#34;

        if self.use_dump_based_backup_copy:
            if self.dump_based_backup_copy_option == 2:
                auxiliary_sybase_server = {
                    &#39;sybaseHome&#39;: self._snap_copy_info.get(&#39;sybaseHome&#39;),
                    &#39;sybaseASE&#39;: self._snap_copy_info.get(&#39;sybaseASE&#39;),
                    &#39;sybaseOCS&#39;: self._snap_copy_info.get(&#39;sybaseOCS&#39;),
                    &#39;sybaseUser&#39;: self._snap_copy_info.get(&#39;sybaseUser&#39;, {}).get(&#39;userName&#39;)
                }
                return auxiliary_sybase_server
            else:
                raise SDKException(
                    &#39;Subclient&#39;, &#39;102&#39;, &#34;Invalid dump based copy option set&#34;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#34;dump based parameter is not available&#34;)

    @auxiliary_sybase_server.setter
    def auxiliary_sybase_server(self, instance_properties):
        &#34;&#34;&#34;
        Setter  custom instance properties if dump based copy option is 2

        Args:
            instance_properties     (dict)       --  dict of four properties
                                                     for custom instance

            Sample dict:
            instance_properties = {
                        &#39;sybaseHome&#39;:sybase_home,
                        &#39;sybaseASE&#39;:sybase_ase,
                        &#39;sybaseOCS&#39;:sybase_ocs,
                        &#39;sybaseUser&#39;:sybase_user
                }
        Raises:
            SDK Exception

                if None value in instance_properties

        &#34;&#34;&#34;
        if None in instance_properties.values():
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#34;One of the sybase custom instance parameter is None. Exiting&#34;)
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;sybaseHome&#39;]&#34;, instance_properties[&#39;sybaseHome&#39;])
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;sybaseASE&#39;]&#34;, instance_properties[&#39;sybaseASE&#39;])
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;sybaseOCS&#39;]&#34;, instance_properties[&#39;sybaseOCS&#39;])
        self._set_subclient_properties(&#34;_snap_copy_info[&#39;sybaseUser&#39;][&#39;userName&#39;]&#34;,
                                       instance_properties[&#39;sybaseUser&#39;])

    @property
    def content(self):
        &#34;&#34;&#34;Treats the subclient content as a property of the Subclient class.&#34;&#34;&#34;
        subclient_content = self._content
        sybase_dblist = []
        for item in subclient_content:
            sybase_server_dict = item
            dbname = sybase_server_dict[&#39;sybaseContent&#39;][&#39;databaseName&#39;]
            sybase_dblist.append(dbname)

        return sybase_dblist

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;
        Creates the list of content JSON to pass to the API to add a new Sybase Subclient
        with the content passed in subclient content.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                (list) -    list of the appropriate JSON
                            for an agent to send to the POST Subclient API

        &#34;&#34;&#34;
        content_new = []
        for dbname in subclient_content:
            sybase_server_dict = {&#34;sybaseContent&#34;: {&#34;databaseName&#34;: dbname}}
            content_new.append(sybase_server_dict)
        self._set_subclient_properties(&#34;_content&#34;, content_new)

    def backup(self,
               backup_level=r&#39;full&#39;,
               do_not_truncate_log=False,
               sybase_skip_full_after_logbkp=False,
               create_backup_copy_immediately=False,
               backup_copy_type=2,
               directive_file=None):
        &#34;&#34;&#34;
        Performs backup on sybase subclient

        Args:
            backup_level                            (str)   --  Level of backup.
                                                                full|incremental|differential
                                                                default: full

            do_not_truncate_log                     (bool)  --  Sybase truncate log option
                                                                for incremental backup
                                                                default : False

            sybase_skip_full_after_logbkp           (bool)  --  Sybase backup option for incremental
                                                                default : False

            create_backup_copy_immediately          (bool)  --  Sybase snap job needs
                                                                this backup copy operation
                                                                default : False

            backup_copy_type                        (int)   --  backup copy job to be launched
                                                                based on below two options
                                                                default : 2, possible values :
                                                                1 (USING_STORAGE_POLICY_RULE),
                                                                2( USING_LATEST_CYCLE)

            directive_file                          (str)   --  input file for ondemand backup
                                                                containing database list
                                                                default : None

        Returns:
            (object) - instance of Job class

        Raises:
            SDKException:
                if backup level is incorrect

                if response is empty

                if response does not succeed

        &#34;&#34;&#34;
        if backup_level.lower() not in [&#39;full&#39;, &#39;incremental&#39;, &#39;differential&#39;]:
            raise SDKException(r&#39;Subclient&#39;, r&#39;103&#39;)

        if backup_level.lower() == &#39;incremental&#39;:
            do_not_truncate_log = do_not_truncate_log
            sybase_skip_full_after_logbkp = sybase_skip_full_after_logbkp
        else:
            do_not_truncate_log = False
            sybase_skip_full_after_logbkp = False

        if create_backup_copy_immediately:
            if backup_level.lower() == &#39;incremental&#39;:
                raise SDKException(
                    &#39;Subclient&#39;, &#39;102&#39;, &#39;Backup Copy job is not valid for Transaction Log Backup &#39;)

        request_json = self._sybase_backup_request_json(backup_level.lower(),
                                                        do_not_truncate_log,
                                                        sybase_skip_full_after_logbkp,
                                                        create_backup_copy_immediately,
                                                        backup_copy_type,
                                                        directive_file)

        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )
        return self._process_backup_response(flag, response)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient"><code class="flex name class">
<span>class <span class="ident">SybaseSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Base class consisting of all the common properties and operations for a Sybase Subclient</p>
<p>Initialize Sybase Subclient Object</p>
<h2 id="args">Args</h2>
<p>backupset_object
(object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default : None
Returns :
(object) - instance of the Sybase Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L69-L530" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SybaseSubclient(Subclient):
    &#34;&#34;&#34;
    Base class consisting of all the common properties and operations for a Sybase Subclient
    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Initialize Sybase Subclient Object

        Args:
            backupset_object  (object)  --  instance of the Backupset class

            subclient_name    (str)     --  name of the subclient

            subclient_id      (str)     --  id of the subclient
                                            default : None


        Returns :
            (object) - instance of the Sybase Subclient class

        &#34;&#34;&#34;
        self._sybase_properties = {}
        self._snap_copy_info = None
        super(SybaseSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

    def _get_subclient_properties(self):
        &#34;&#34;&#34;
        Gets the subclient related properties of Sybase subclient
        &#34;&#34;&#34;

        super(SybaseSubclient, self)._get_subclient_properties()
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]

        self._snap_copy_info = self._commonProperties.get(&#39;snapCopyInfo&#39;)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;
        Get the all subclient related properties of this subclient.

           Returns:
                (dict) - all subclient properties put inside a dict

        &#34;&#34;&#34;
        return {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1,
                    &#34;snapCopyInfo&#34;: self._snap_copy_info
                }
        }

    def _sybase_backup_request_json(self,
                                    backup_level,
                                    do_not_truncate_log=False,
                                    sybase_skip_full_after_logbkp=False,
                                    create_backup_copy_immediately=False,
                                    backup_copy_type=2,
                                    directive_file=None):
        &#34;&#34;&#34;
        Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
               backup_level                     (list)  --  level of backup the user wish to run
                                                            Full / Incremental / Differential

               do_not_truncate_log              (bool)  --  Sybase truncate log option
                                                            for incremental backup
                                                            default : False

               sybase_skip_full_after_logbkp    (bool)  --  Sybase backup option for incremental
                                                            default : False

               create_backup_copy_immediately   (bool)  --  Sybase snap job needs
                                                            this backup copy operation
                                                            default : False

               backup_copy_type                 (int)   --  backup copy job to be launched
                                                            based on below two options
                                                            default : 2, possible values :
                                                            1 (USING_STORAGE_POLICY_RULE),
                                                            2( USING_LATEST_CYCLE)

               directive_file                   (str)   --  inputfile for ondemand backup
                                                            containing database list
                                                            default : None

            Returns:

                (dict) - JSON request to pass to the API

        &#34;&#34;&#34;
        request_json = self._backup_json(backup_level, False, &#34;BEFORE_SYNTH&#34;)
        sybase_options = {
            &#34;doNotTruncateLog&#34;: do_not_truncate_log,
            &#34;sybaseSkipFullafterLogBkp&#34;: sybase_skip_full_after_logbkp
        }

        if create_backup_copy_immediately:
            sub_opt = {&#34;dataOpt&#34;:
                       {
                           &#34;createBackupCopyImmediately&#34;: create_backup_copy_immediately,
                           &#34;backupCopyType&#34;: backup_copy_type
                       }
                      }
            sybase_options.update(sub_opt)
        if self._commonProperties.get(&#34;onDemandSubClient&#34;, False):
            on_demand_input = {&#34;onDemandInputFile&#34;:directive_file}
            sybase_options.update(on_demand_input)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;].update(
            sybase_options
        )
        return request_json


    @property
    def is_snapenabled(self):
        &#34;&#34;&#34;
        Getter to check whether the subclient has snap enabled

        Returns:
            (bool)  -    boolean value based on snap
                         status at subclient level

                True    -  returns Truee if snap is enabled on the subclient
                False   -  returns False if snap is not
                            enabled at subclient level

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#34;isSnapBackupEnabled&#34;, False)

    @is_snapenabled.setter
    def is_snapenabled(self, value):
        &#34;&#34;&#34;
        To set is snap enabled to true or false

        Args:
            value           (bool) --   to enable snap at subclient level or not

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;isSnapBackupEnabled&#39;]&#34;, value)

    @property
    def snap_engine(self):
        &#34;&#34;&#34;
        Getter to fetch snap_engine

        Returns:
            (str)     -  name of snap engine at subclient level

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#39;snapToTapeSelectedEngine&#39;, {}).get(&#39;snapShotEngineName&#39;)

    @snap_engine.setter
    def snap_engine(self, engine_name):
        &#34;&#34;&#34;
        To set snap engine name

        Args:
            engine_name           (str) --      name of snap engine
                                                for intellisnap

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;snapToTapeSelectedEngine&#39;][&#39;snapShotEngineName&#39;]&#34;, engine_name)

    @property
    def snap_proxy(self):
        &#34;&#34;&#34;
        Getter to snap_proxy if set any

        Returns:
            (str)     --    name of proxy client used
                            for intellisnap operation

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#39;snapToTapeProxyToUse&#39;, {}).get(&#39;clientName&#39;)

    @snap_proxy.setter
    def snap_proxy(self, proxy_name):
        &#34;&#34;&#34;
        Setter for snap proxy name

        Args:
            proxy_name           (str) --   snap proxy name

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;snapToTapeProxyToUse&#39;][&#39;clientName&#39;]&#34;, proxy_name)

    @property
    def use_dump_based_backup_copy(self):
        &#34;&#34;&#34;
        Getter to status of dumpbased backup copy

        Returns:
            (bool)     -    checks if dump based backup
                            copy is enabled or not

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#39;useDumpBasedBackupCopy&#39;)

    @use_dump_based_backup_copy.setter
    def use_dump_based_backup_copy(self, dump_based):
        &#34;&#34;&#34;
        To enable dump based backup copy

        Args:
            dump_based      (bool)     --      set true  to enable dump
                                               based backup copy option

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;useDumpBasedBackupCopy&#39;]&#34;, dump_based)

    @property
    def dump_based_backup_copy_option(self):
        &#34;&#34;&#34;
        Getter to fetch dumpbased backup copy option :
        1(configured instance), 2(custom new instance)

        Returns:
            (int)     -  returns 1 or 2 based type of sybase instance configured

        &#34;&#34;&#34;
        return self._snap_copy_info.get(&#34;dumpBasedBackupCopyOption&#34;)

    @dump_based_backup_copy_option.setter
    def dump_based_backup_copy_option(self, dump_based_backup_copy_option):
        &#34;&#34;&#34;
        Enable dump based backup copy

        Args:
            dump_based_backup_copy_option      (int) --  set 1(configured instance),
                                                             2(custom new instance)
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;dumpBasedBackupCopyOption&#39;]&#34;, dump_based_backup_copy_option)

    @property
    def configured_instance(self):
        &#34;&#34;&#34;
        Getter to fetch configured instance
        name if dump based backup copy option is 2
        Returns:
            (str)       -       string of configured instance
                                if dump based backup option is 1

        Raises:
            SDK Exception
                if dump based backup copy not enabled

                if dump based copy option is not 1

        &#34;&#34;&#34;
        if self._snap_copy_info.get(&#39;useDumpBasedBackupCopy&#39;):
            if self._snap_copy_info.get(&#39;dumpBasedBackupCopyOption&#39;) == 1:
                return self._snap_copy_info[&#39;configuredSybaseInstance&#39;][&#39;instanceName&#39;]
            else:
                raise SDKException(
                    &#39;Subclient&#39;, &#39;102&#39;, &#34;Invalid dump based copy option&#34;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#34;Dump based parameter is not available&#34;)

    @configured_instance.setter
    def configured_instance(self, instance_name):
        &#34;&#34;&#34;
        Setter for configured instance
        name for dump based backup copy option

        Args:
            instance_name       (str) --    string of instance name
                                            to be used for dump based backup copy

        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;configuredSybaseInstance&#39;][&#39;instanceName&#39;]&#34;, instance_name)

    @property
    def auxiliary_sybase_server(self):
        &#34;&#34;&#34;
        Getter to fetch custom instance properties if dump based copy option is 2

        Returns:
            (dict)       -  dict of four properties
                            for custom instance
        Raises:
            SDK Exception
                if dump based backup copy not enabled

                if dump based copy option is not 2

        &#34;&#34;&#34;

        if self.use_dump_based_backup_copy:
            if self.dump_based_backup_copy_option == 2:
                auxiliary_sybase_server = {
                    &#39;sybaseHome&#39;: self._snap_copy_info.get(&#39;sybaseHome&#39;),
                    &#39;sybaseASE&#39;: self._snap_copy_info.get(&#39;sybaseASE&#39;),
                    &#39;sybaseOCS&#39;: self._snap_copy_info.get(&#39;sybaseOCS&#39;),
                    &#39;sybaseUser&#39;: self._snap_copy_info.get(&#39;sybaseUser&#39;, {}).get(&#39;userName&#39;)
                }
                return auxiliary_sybase_server
            else:
                raise SDKException(
                    &#39;Subclient&#39;, &#39;102&#39;, &#34;Invalid dump based copy option set&#34;)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#34;dump based parameter is not available&#34;)

    @auxiliary_sybase_server.setter
    def auxiliary_sybase_server(self, instance_properties):
        &#34;&#34;&#34;
        Setter  custom instance properties if dump based copy option is 2

        Args:
            instance_properties     (dict)       --  dict of four properties
                                                     for custom instance

            Sample dict:
            instance_properties = {
                        &#39;sybaseHome&#39;:sybase_home,
                        &#39;sybaseASE&#39;:sybase_ase,
                        &#39;sybaseOCS&#39;:sybase_ocs,
                        &#39;sybaseUser&#39;:sybase_user
                }
        Raises:
            SDK Exception

                if None value in instance_properties

        &#34;&#34;&#34;
        if None in instance_properties.values():
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#34;One of the sybase custom instance parameter is None. Exiting&#34;)
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;sybaseHome&#39;]&#34;, instance_properties[&#39;sybaseHome&#39;])
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;sybaseASE&#39;]&#34;, instance_properties[&#39;sybaseASE&#39;])
        self._set_subclient_properties(
            &#34;_snap_copy_info[&#39;sybaseOCS&#39;]&#34;, instance_properties[&#39;sybaseOCS&#39;])
        self._set_subclient_properties(&#34;_snap_copy_info[&#39;sybaseUser&#39;][&#39;userName&#39;]&#34;,
                                       instance_properties[&#39;sybaseUser&#39;])

    @property
    def content(self):
        &#34;&#34;&#34;Treats the subclient content as a property of the Subclient class.&#34;&#34;&#34;
        subclient_content = self._content
        sybase_dblist = []
        for item in subclient_content:
            sybase_server_dict = item
            dbname = sybase_server_dict[&#39;sybaseContent&#39;][&#39;databaseName&#39;]
            sybase_dblist.append(dbname)

        return sybase_dblist

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;
        Creates the list of content JSON to pass to the API to add a new Sybase Subclient
        with the content passed in subclient content.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                (list) -    list of the appropriate JSON
                            for an agent to send to the POST Subclient API

        &#34;&#34;&#34;
        content_new = []
        for dbname in subclient_content:
            sybase_server_dict = {&#34;sybaseContent&#34;: {&#34;databaseName&#34;: dbname}}
            content_new.append(sybase_server_dict)
        self._set_subclient_properties(&#34;_content&#34;, content_new)

    def backup(self,
               backup_level=r&#39;full&#39;,
               do_not_truncate_log=False,
               sybase_skip_full_after_logbkp=False,
               create_backup_copy_immediately=False,
               backup_copy_type=2,
               directive_file=None):
        &#34;&#34;&#34;
        Performs backup on sybase subclient

        Args:
            backup_level                            (str)   --  Level of backup.
                                                                full|incremental|differential
                                                                default: full

            do_not_truncate_log                     (bool)  --  Sybase truncate log option
                                                                for incremental backup
                                                                default : False

            sybase_skip_full_after_logbkp           (bool)  --  Sybase backup option for incremental
                                                                default : False

            create_backup_copy_immediately          (bool)  --  Sybase snap job needs
                                                                this backup copy operation
                                                                default : False

            backup_copy_type                        (int)   --  backup copy job to be launched
                                                                based on below two options
                                                                default : 2, possible values :
                                                                1 (USING_STORAGE_POLICY_RULE),
                                                                2( USING_LATEST_CYCLE)

            directive_file                          (str)   --  input file for ondemand backup
                                                                containing database list
                                                                default : None

        Returns:
            (object) - instance of Job class

        Raises:
            SDKException:
                if backup level is incorrect

                if response is empty

                if response does not succeed

        &#34;&#34;&#34;
        if backup_level.lower() not in [&#39;full&#39;, &#39;incremental&#39;, &#39;differential&#39;]:
            raise SDKException(r&#39;Subclient&#39;, r&#39;103&#39;)

        if backup_level.lower() == &#39;incremental&#39;:
            do_not_truncate_log = do_not_truncate_log
            sybase_skip_full_after_logbkp = sybase_skip_full_after_logbkp
        else:
            do_not_truncate_log = False
            sybase_skip_full_after_logbkp = False

        if create_backup_copy_immediately:
            if backup_level.lower() == &#39;incremental&#39;:
                raise SDKException(
                    &#39;Subclient&#39;, &#39;102&#39;, &#39;Backup Copy job is not valid for Transaction Log Backup &#39;)

        request_json = self._sybase_backup_request_json(backup_level.lower(),
                                                        do_not_truncate_log,
                                                        sybase_skip_full_after_logbkp,
                                                        create_backup_copy_immediately,
                                                        backup_copy_type,
                                                        directive_file)

        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )
        return self._process_backup_response(flag, response)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient.auxiliary_sybase_server"><code class="name">var <span class="ident">auxiliary_sybase_server</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch custom instance properties if dump based copy option is 2</p>
<h2 id="returns">Returns</h2>
<p>(dict)
-
dict of four properties
for custom instance</p>
<h2 id="raises">Raises</h2>
<p>SDK Exception
if dump based backup copy not enabled</p>
<pre><code>if dump based copy option is not 2
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L357-L387" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def auxiliary_sybase_server(self):
    &#34;&#34;&#34;
    Getter to fetch custom instance properties if dump based copy option is 2

    Returns:
        (dict)       -  dict of four properties
                        for custom instance
    Raises:
        SDK Exception
            if dump based backup copy not enabled

            if dump based copy option is not 2

    &#34;&#34;&#34;

    if self.use_dump_based_backup_copy:
        if self.dump_based_backup_copy_option == 2:
            auxiliary_sybase_server = {
                &#39;sybaseHome&#39;: self._snap_copy_info.get(&#39;sybaseHome&#39;),
                &#39;sybaseASE&#39;: self._snap_copy_info.get(&#39;sybaseASE&#39;),
                &#39;sybaseOCS&#39;: self._snap_copy_info.get(&#39;sybaseOCS&#39;),
                &#39;sybaseUser&#39;: self._snap_copy_info.get(&#39;sybaseUser&#39;, {}).get(&#39;userName&#39;)
            }
            return auxiliary_sybase_server
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#34;Invalid dump based copy option set&#34;)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                           &#34;dump based parameter is not available&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient.configured_instance"><code class="name">var <span class="ident">configured_instance</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch configured instance
name if dump based backup copy option is 2</p>
<h2 id="returns">Returns</h2>
<p>(str)
-
string of configured instance
if dump based backup option is 1</p>
<h2 id="raises">Raises</h2>
<p>SDK Exception
if dump based backup copy not enabled</p>
<pre><code>if dump based copy option is not 1
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L316-L340" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def configured_instance(self):
    &#34;&#34;&#34;
    Getter to fetch configured instance
    name if dump based backup copy option is 2
    Returns:
        (str)       -       string of configured instance
                            if dump based backup option is 1

    Raises:
        SDK Exception
            if dump based backup copy not enabled

            if dump based copy option is not 1

    &#34;&#34;&#34;
    if self._snap_copy_info.get(&#39;useDumpBasedBackupCopy&#39;):
        if self._snap_copy_info.get(&#39;dumpBasedBackupCopyOption&#39;) == 1:
            return self._snap_copy_info[&#39;configuredSybaseInstance&#39;][&#39;instanceName&#39;]
        else:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#34;Invalid dump based copy option&#34;)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                           &#34;Dump based parameter is not available&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient content as a property of the Subclient class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L423-L433" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Treats the subclient content as a property of the Subclient class.&#34;&#34;&#34;
    subclient_content = self._content
    sybase_dblist = []
    for item in subclient_content:
        sybase_server_dict = item
        dbname = sybase_server_dict[&#39;sybaseContent&#39;][&#39;databaseName&#39;]
        sybase_dblist.append(dbname)

    return sybase_dblist</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient.dump_based_backup_copy_option"><code class="name">var <span class="ident">dump_based_backup_copy_option</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch dumpbased backup copy option :
1(configured instance), 2(custom new instance)</p>
<h2 id="returns">Returns</h2>
<p>(int)
-
returns 1 or 2 based type of sybase instance configured</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L292-L302" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def dump_based_backup_copy_option(self):
    &#34;&#34;&#34;
    Getter to fetch dumpbased backup copy option :
    1(configured instance), 2(custom new instance)

    Returns:
        (int)     -  returns 1 or 2 based type of sybase instance configured

    &#34;&#34;&#34;
    return self._snap_copy_info.get(&#34;dumpBasedBackupCopyOption&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient.is_snapenabled"><code class="name">var <span class="ident">is_snapenabled</span></code></dt>
<dd>
<div class="desc"><p>Getter to check whether the subclient has snap enabled</p>
<h2 id="returns">Returns</h2>
<p>(bool)
-
boolean value based on snap
status at subclient level</p>
<pre><code>True    -  returns Truee if snap is enabled on the subclient
False   -  returns False if snap is not
            enabled at subclient level
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L191-L205" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_snapenabled(self):
    &#34;&#34;&#34;
    Getter to check whether the subclient has snap enabled

    Returns:
        (bool)  -    boolean value based on snap
                     status at subclient level

            True    -  returns Truee if snap is enabled on the subclient
            False   -  returns False if snap is not
                        enabled at subclient level

    &#34;&#34;&#34;
    return self._snap_copy_info.get(&#34;isSnapBackupEnabled&#34;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient.snap_engine"><code class="name">var <span class="ident">snap_engine</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch snap_engine</p>
<h2 id="returns">Returns</h2>
<p>(str)
-
name of snap engine at subclient level</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L219-L228" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def snap_engine(self):
    &#34;&#34;&#34;
    Getter to fetch snap_engine

    Returns:
        (str)     -  name of snap engine at subclient level

    &#34;&#34;&#34;
    return self._snap_copy_info.get(&#39;snapToTapeSelectedEngine&#39;, {}).get(&#39;snapShotEngineName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient.snap_proxy"><code class="name">var <span class="ident">snap_proxy</span></code></dt>
<dd>
<div class="desc"><p>Getter to snap_proxy if set any</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
name of proxy client used
for intellisnap operation</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L243-L253" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def snap_proxy(self):
    &#34;&#34;&#34;
    Getter to snap_proxy if set any

    Returns:
        (str)     --    name of proxy client used
                        for intellisnap operation

    &#34;&#34;&#34;
    return self._snap_copy_info.get(&#39;snapToTapeProxyToUse&#39;, {}).get(&#39;clientName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient.use_dump_based_backup_copy"><code class="name">var <span class="ident">use_dump_based_backup_copy</span></code></dt>
<dd>
<div class="desc"><p>Getter to status of dumpbased backup copy</p>
<h2 id="returns">Returns</h2>
<p>(bool)
-
checks if dump based backup
copy is enabled or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L267-L277" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def use_dump_based_backup_copy(self):
    &#34;&#34;&#34;
    Getter to status of dumpbased backup copy

    Returns:
        (bool)     -    checks if dump based backup
                        copy is enabled or not

    &#34;&#34;&#34;
    return self._snap_copy_info.get(&#39;useDumpBasedBackupCopy&#39;)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.sybasesubclient.SybaseSubclient.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, backup_level='full', do_not_truncate_log=False, sybase_skip_full_after_logbkp=False, create_backup_copy_immediately=False, backup_copy_type=2, directive_file=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs backup on sybase subclient</p>
<h2 id="args">Args</h2>
<p>backup_level
(str)
&ndash;
Level of backup.
full|incremental|differential
default: full</p>
<p>do_not_truncate_log
(bool)
&ndash;
Sybase truncate log option
for incremental backup
default : False</p>
<p>sybase_skip_full_after_logbkp
(bool)
&ndash;
Sybase backup option for incremental
default : False</p>
<p>create_backup_copy_immediately
(bool)
&ndash;
Sybase snap job needs
this backup copy operation
default : False</p>
<p>backup_copy_type
(int)
&ndash;
backup copy job to be launched
based on below two options
default : 2, possible values :
1 (USING_STORAGE_POLICY_RULE),
2( USING_LATEST_CYCLE)</p>
<p>directive_file
(str)
&ndash;
input file for ondemand backup
containing database list
default : None</p>
<h2 id="returns">Returns</h2>
<p>(object) - instance of Job class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level is incorrect</p>
<pre><code>if response is empty

if response does not succeed
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sybasesubclient.py#L455-L530" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self,
           backup_level=r&#39;full&#39;,
           do_not_truncate_log=False,
           sybase_skip_full_after_logbkp=False,
           create_backup_copy_immediately=False,
           backup_copy_type=2,
           directive_file=None):
    &#34;&#34;&#34;
    Performs backup on sybase subclient

    Args:
        backup_level                            (str)   --  Level of backup.
                                                            full|incremental|differential
                                                            default: full

        do_not_truncate_log                     (bool)  --  Sybase truncate log option
                                                            for incremental backup
                                                            default : False

        sybase_skip_full_after_logbkp           (bool)  --  Sybase backup option for incremental
                                                            default : False

        create_backup_copy_immediately          (bool)  --  Sybase snap job needs
                                                            this backup copy operation
                                                            default : False

        backup_copy_type                        (int)   --  backup copy job to be launched
                                                            based on below two options
                                                            default : 2, possible values :
                                                            1 (USING_STORAGE_POLICY_RULE),
                                                            2( USING_LATEST_CYCLE)

        directive_file                          (str)   --  input file for ondemand backup
                                                            containing database list
                                                            default : None

    Returns:
        (object) - instance of Job class

    Raises:
        SDKException:
            if backup level is incorrect

            if response is empty

            if response does not succeed

    &#34;&#34;&#34;
    if backup_level.lower() not in [&#39;full&#39;, &#39;incremental&#39;, &#39;differential&#39;]:
        raise SDKException(r&#39;Subclient&#39;, r&#39;103&#39;)

    if backup_level.lower() == &#39;incremental&#39;:
        do_not_truncate_log = do_not_truncate_log
        sybase_skip_full_after_logbkp = sybase_skip_full_after_logbkp
    else:
        do_not_truncate_log = False
        sybase_skip_full_after_logbkp = False

    if create_backup_copy_immediately:
        if backup_level.lower() == &#39;incremental&#39;:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Backup Copy job is not valid for Transaction Log Backup &#39;)

    request_json = self._sybase_backup_request_json(backup_level.lower(),
                                                    do_not_truncate_log,
                                                    sybase_skip_full_after_logbkp,
                                                    create_backup_copy_immediately,
                                                    backup_copy_type,
                                                    directive_file)

    backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, backup_service, request_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclient.Subclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_in_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient">SybaseSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient.auxiliary_sybase_server" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient.auxiliary_sybase_server">auxiliary_sybase_server</a></code></li>
<li><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient.backup" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient.configured_instance" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient.configured_instance">configured_instance</a></code></li>
<li><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient.content" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient.dump_based_backup_copy_option" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient.dump_based_backup_copy_option">dump_based_backup_copy_option</a></code></li>
<li><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient.is_snapenabled" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient.is_snapenabled">is_snapenabled</a></code></li>
<li><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient.snap_engine" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient.snap_engine">snap_engine</a></code></li>
<li><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient.snap_proxy" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient.snap_proxy">snap_proxy</a></code></li>
<li><code><a title="cvpysdk.subclients.sybasesubclient.SybaseSubclient.use_dump_based_backup_copy" href="#cvpysdk.subclients.sybasesubclient.SybaseSubclient.use_dump_based_backup_copy">use_dump_based_backup_copy</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>