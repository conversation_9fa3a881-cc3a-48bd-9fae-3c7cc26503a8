<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.index_server API documentation</title>
<meta name="description" content="File for performing index server related operations on the commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.index_server</code></h1>
</header>
<section id="section-intro">
<p>File for performing index server related operations on the commcell</p>
<p>IndexServers, IndexServer and _Roles are 3 classes defined in this file</p>
<p>IndexServers:
Class for representing all the index servers associated with the commcell</p>
<p>IndexServer:
Class for a instance of a single index server of the commcell</p>
<p>_Roles:
Class for storing all the cloud role details</p>
<p>"IndexServerOSType" is the enum class used to represent os type of IS</p>
<h1 id="indexservers">IndexServers</h1>
<pre><code>__init__()                          --  initialize object of IndexServers class associated with
                                        the commcell

__str()                             --  returns all the index servers of the commcell

__repr__()                          --  returns the string to represent the instance

__len__()                           --  returns the number of index servers associated

_get_index_servers()                --  gets all the index server associated with the commcell

_response_not_success()             --  raise exception when response is not 200

_get_all_roles()                    --  creates an instance of _Roles class

has()                               --  returns whether the index server is present or not

get()                               --  returns a IndexServer object for given cloud name

create()                            --  creates a index server within the commcell

delete()                            --  deletes a index server associated with commcell

update_roles_data()                 --  fetches the cloud roles data from commcell

get_properties()                    --  returns a dict of data of index server for the given
                                        cloud name

refresh()                           --  refresh the index servers associated with commcell

prune_orphan_datasources()          --  Deletes all the orphan datasources
</code></pre>
<h2 id="indexservers-attributes">Indexservers Attributes</h2>
<pre><code>**all_index_servers**               --  returns the dictionary consisting of all the index
                                        servers associated with the commcell and there details

**roles_data**                      --  returns the list of cloud roles details
</code></pre>
<h1 id="indexserver">IndexServer</h1>
<pre><code>__init()__                          --  initializes the object with the specified commcell
                                        object, index server name and the cloud id

__repr__()                          --  returns the index server's name, the instance is
associated with

_get_cloud_id()                     --  gets the cloud id

_get_properties()                   --  gets all the properties of the index server

refresh()                           --  refresh all the properties of client

update_roles_data()                 --  fetches the cloud roles data from commcell

modify()                            --  to modify the index server node details

change_plan()                       --  changes the plan of a given index server

update_role()                       --  to update the roles assigned to cloud

delete_docs_from_core()             --  Deletes the docs from the given core name on index server depending
                                        on the select dict passed

hard_commit                         --  do hard commit on specified index server solr core

get_health_indicators()             --  get health indicators for index server node by client name

get_all_cores                       --  gets all the cores in index server

_create_solr_query()                --  Create solr search query based on inputs provided

execute_solr_query()                --  Creates solr url based on input and executes it on solr on given core

get_index_node()                    --  returns an Index server node object for given node name

get_os_info()                       --  returns the OS type for the Index server

get_plan_info()                     --  Returns the plan information of the index server

__form_field_query()                --  returns the query with the key and value passed
</code></pre>
<h2 id="indexserver-attributes">Indexserver Attributes</h2>
<pre><code>**properties**                      --  returns the properties of this index server

**roles_data**                      --  returns all the available cloud roles data

**host_name**                       --  returns the host name for the index server

**internal_cloud_name**             --  returns the internal cloud name

**client_name**                     --  returns the client name for index server

**server_url**                      --  returns the content indexing server url

**type**                            --  returns the type of the index server

**base_port**                       --  returns the base port of this index server

**client_id**                       --  returns the client id for this index server

**roles**                           --  returns the array of roles installed
                                        with the index server within the commcell

**cloud_id**                        --  returns the cloud id of the index server

**server_type**                     --  returns the server type of the index server

**engine_name**                     --  returns the engine name that is index server name

**index_server_client_id**          --  returns the index server client id

**role_display_name**               --  display name of roles

**is_cloud**                        --  returns boolean True if the Index server is cloud else returns False

**node_count**                      --  returns the number of Index server nodes

**os_info**                         --  returns the OS type for the Index server

**plan_name**                       --  Returns the plan name associated with index server

**fs_collection**                   --  Returns the multinode collection name of File System Index
</code></pre>
<h1 id="indexnode">IndexNode</h1>
<pre><code>__init__()                          --  initializes the class with commcell object
                                        Index server cloud id and Node client name

refresh()                           --  refreshes the attributes

modify()                            --  to modify the index server node details
</code></pre>
<h2 id="indexnode-attributes">Indexnode Attributes</h2>
<pre><code>**node_name**                       --  returns Index server node client name

**node_id**                         --  returns Index server node client id

**solr_port**                       --  returns port number Solr is running on the                                            Index server node

**solr_url**                        --  returns Solr URL for Index server node

**roles**                           --  returns the array of roles installed
                                        with the index server within the commcell

**index_location**                  --  returns Index directory for the Index server Node

**jvm_memory**                      --  returns Solr JVM memory for the Index server Node
</code></pre>
<h1 id="_roles">_Roles</h1>
<pre><code>__init__()                          --  initializes the class with commcell object

refresh()                           --  refreshes the attributes

_get_all_roles()                    --  fetches the cloud roles data from commcell

get_role_id()                       --  returns role id for given role name

update_roles_data()                 --  fetches the cloud roles data from commcell
</code></pre>
<h2 id="_roles-attributes">_Roles Attributes</h2>
<pre><code>**roles_data**                      --  returns the list of details of all cloud roles
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1-L1497" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for performing index server related operations on the commcell

IndexServers, IndexServer and _Roles are 3 classes defined in this file

IndexServers:   Class for representing all the index servers associated with the commcell

IndexServer:    Class for a instance of a single index server of the commcell

_Roles:         Class for storing all the cloud role details

&#34;IndexServerOSType&#34; is the enum class used to represent os type of IS

IndexServers
============

    __init__()                          --  initialize object of IndexServers class associated with
                                            the commcell

    __str()                             --  returns all the index servers of the commcell

    __repr__()                          --  returns the string to represent the instance

    __len__()                           --  returns the number of index servers associated

    _get_index_servers()                --  gets all the index server associated with the commcell

    _response_not_success()             --  raise exception when response is not 200

    _get_all_roles()                    --  creates an instance of _Roles class

    has()                               --  returns whether the index server is present or not

    get()                               --  returns a IndexServer object for given cloud name

    create()                            --  creates a index server within the commcell

    delete()                            --  deletes a index server associated with commcell

    update_roles_data()                 --  fetches the cloud roles data from commcell

    get_properties()                    --  returns a dict of data of index server for the given
                                            cloud name

    refresh()                           --  refresh the index servers associated with commcell

    prune_orphan_datasources()          --  Deletes all the orphan datasources

IndexServers Attributes
-----------------------

    **all_index_servers**               --  returns the dictionary consisting of all the index
                                            servers associated with the commcell and there details

    **roles_data**                      --  returns the list of cloud roles details


IndexServer
===========

    __init()__                          --  initializes the object with the specified commcell
                                            object, index server name and the cloud id

    __repr__()                          --  returns the index server&#39;s name, the instance is
    associated with

    _get_cloud_id()                     --  gets the cloud id

    _get_properties()                   --  gets all the properties of the index server

    refresh()                           --  refresh all the properties of client

    update_roles_data()                 --  fetches the cloud roles data from commcell

    modify()                            --  to modify the index server node details

    change_plan()                       --  changes the plan of a given index server

    update_role()                       --  to update the roles assigned to cloud

    delete_docs_from_core()             --  Deletes the docs from the given core name on index server depending
                                            on the select dict passed

    hard_commit                         --  do hard commit on specified index server solr core

    get_health_indicators()             --  get health indicators for index server node by client name

    get_all_cores                       --  gets all the cores in index server

    _create_solr_query()                --  Create solr search query based on inputs provided

    execute_solr_query()                --  Creates solr url based on input and executes it on solr on given core

    get_index_node()                    --  returns an Index server node object for given node name

    get_os_info()                       --  returns the OS type for the Index server

    get_plan_info()                     --  Returns the plan information of the index server

    __form_field_query()                --  returns the query with the key and value passed

IndexServer Attributes
----------------------

    **properties**                      --  returns the properties of this index server

    **roles_data**                      --  returns all the available cloud roles data

    **host_name**                       --  returns the host name for the index server

    **internal_cloud_name**             --  returns the internal cloud name

    **client_name**                     --  returns the client name for index server

    **server_url**                      --  returns the content indexing server url

    **type**                            --  returns the type of the index server

    **base_port**                       --  returns the base port of this index server

    **client_id**                       --  returns the client id for this index server

    **roles**                           --  returns the array of roles installed
                                            with the index server within the commcell

    **cloud_id**                        --  returns the cloud id of the index server

    **server_type**                     --  returns the server type of the index server

    **engine_name**                     --  returns the engine name that is index server name

    **index_server_client_id**          --  returns the index server client id

    **role_display_name**               --  display name of roles

    **is_cloud**                        --  returns boolean True if the Index server is cloud else returns False

    **node_count**                      --  returns the number of Index server nodes

    **os_info**                         --  returns the OS type for the Index server

    **plan_name**                       --  Returns the plan name associated with index server

    **fs_collection**                   --  Returns the multinode collection name of File System Index


IndexNode
=========

    __init__()                          --  initializes the class with commcell object
                                            Index server cloud id and Node client name

    refresh()                           --  refreshes the attributes

    modify()                            --  to modify the index server node details

IndexNode Attributes
--------------------

    **node_name**                       --  returns Index server node client name

    **node_id**                         --  returns Index server node client id

    **solr_port**                       --  returns port number Solr is running on the\
                                            Index server node

    **solr_url**                        --  returns Solr URL for Index server node

    **roles**                           --  returns the array of roles installed
                                            with the index server within the commcell

    **index_location**                  --  returns Index directory for the Index server Node

    **jvm_memory**                      --  returns Solr JVM memory for the Index server Node

_Roles
======

    __init__()                          --  initializes the class with commcell object

    refresh()                           --  refreshes the attributes

    _get_all_roles()                    --  fetches the cloud roles data from commcell

    get_role_id()                       --  returns role id for given role name

    update_roles_data()                 --  fetches the cloud roles data from commcell

_Roles Attributes
-----------------

    **roles_data**                      --  returns the list of details of all cloud roles
    &#34;&#34;&#34;
import json

import http.client as httplib
from copy import deepcopy
import enum
from .exception import SDKException
from .datacube.constants import IndexServerConstants


class IndexServers(object):
    &#34;&#34;&#34;Class for representing all the index servers associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the IndexServers class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the IndexServers class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._all_index_servers = None
        self._roles_obj = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all index servers of the commcell.

                Returns:
                    str - string of all the index servers with different roles associated
                    with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;IS Name&#39;)
        index = 1
        for index_server in self._all_index_servers:
            representation_string += &#39;{:^5}\t{:^20}\n&#39;.format(
                index, index_server[&#39;engineName&#39;])
            index += 1
        return representation_string

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the IndexServers class.&#34;&#34;&#34;
        return &#34;IndexServers class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the index servers associated with the commcell&#34;&#34;&#34;
        return len(self._all_index_servers)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

                Raises:
                    SDKException:
                        Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def _get_index_servers(self):
        &#34;&#34;&#34;Method to retrieve all the index server available on commcell.

            Raises:
                SDKException:
                    Failed to get the list of analytics engines

                    Response was not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[&#39;GET_ALL_INDEX_SERVERS&#39;])
        if flag:
            if response.json() and &#39;listOfCIServer&#39; in response.json():
                for item in response.json()[&#39;listOfCIServer&#39;]:
                    if item[&#39;cloudID&#39;] in self._all_index_servers:
                        # Add only unique roles to list
                        if &#39;version&#39; in item and item[&#39;version&#39;] not in self._all_index_servers[item[&#39;cloudID&#39;]][&#39;version&#39;]:
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;version&#39;].append(item[&#39;version&#39;])
                        # check whether we have populated node details earlier. if not, add it to
                        # exisitng respective fields
                        if item[&#39;clientName&#39;] not in self._all_index_servers[item[&#39;cloudID&#39;]][&#39;clientName&#39;]:

                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;clientId&#39;].append(item[&#39;clientId&#39;])
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;clientName&#39;].append(item[&#39;clientName&#39;])
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;hostName&#39;].append(item[&#39;hostName&#39;])
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;cIServerURL&#39;].append(item[&#39;cIServerURL&#39;])
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;basePort&#39;].append(item[&#39;basePort&#39;])

                    else:
                        item[&#39;version&#39;] = [item.get(&#39;version&#39;, &#39;&#39;)]
                        item[&#39;clientId&#39;] = [item[&#39;clientId&#39;]]
                        item[&#39;clientName&#39;] = [item[&#39;clientName&#39;]]
                        item[&#39;hostName&#39;] = [item[&#39;hostName&#39;]]
                        item[&#39;cIServerURL&#39;] = [item[&#39;cIServerURL&#39;]]
                        item[&#39;basePort&#39;] = [item[&#39;basePort&#39;]]
                        self._all_index_servers[item[&#39;cloudID&#39;]] = item
            else:
                self._all_index_servers = {}
        else:
            self._response_not_success(response)

    def _get_all_roles(self):
        &#34;&#34;&#34;Creates an instance of _Roles class and adds it to the IndexServer class&#34;&#34;&#34;
        self._roles_obj = _Roles(self._commcell_object)

    @property
    def all_index_servers(self):
        &#34;&#34;&#34;Returns the details of all the index server for associated commcell.

                Returns:
                    dict - dictionary consisting details of all the index servers
                    associated with commcell
                    Sample - {
                                &lt;cloud_id_1&gt;   :
                                    {
                                        &#34;engineName&#34; : &lt;property_value&gt;,
                                        &#34;internalCloudName&#34; : &lt;property_value&gt;,
                                        ...
                                    },
                                &lt;cloud_id_2&gt;   :
                                    {
                                        &#34;engineName&#34; : &lt;property_value&gt;,
                                        &#34;cloudID&#34; : &lt;property_value&gt;,
                                        ...
                                    }
                            }
        &#34;&#34;&#34;
        return self._all_index_servers

    @property
    def roles_data(self):
        &#34;&#34;&#34;Returns the details of all the cloud roles data

                Returns:
                    list - list of dictionary containing details of the cloud roles
        &#34;&#34;&#34;
        return self._roles_obj.roles_data

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of IndexServers class&#34;&#34;&#34;
        self._all_index_servers = {}
        self._get_index_servers()
        if not self._roles_obj:
            self._get_all_roles()

    def update_roles_data(self):
        &#34;&#34;&#34;Synchronises all the cloud roles details with the commcell&#34;&#34;&#34;
        self._roles_obj.update_roles_data()

    def get_properties(self, cloud_name):
        &#34;&#34;&#34;Returns all details of a index server with the cloud name

                Args:
                    cloud_name     (str)       --  cloud name of index server

                Returns:
                    dict        -   dict consisting details of the index server
        &#34;&#34;&#34;
        for index_server in self._all_index_servers:
            if self._all_index_servers[index_server][&#39;engineName&#39;] == cloud_name:
                return self._all_index_servers[index_server]
        raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)

    def has(self, cloud_name):
        &#34;&#34;&#34;Returns True if the index server with given name is present in commcell.

                Args:
                    cloud_name     (str)       --  the engine name of index server

                Returns:
                    boolean     -   True if index server with given name as is_name
                    is associated with the commcell else returns False

                Raises:
                    SDKExecption:
                        Data type of the input(s) is not valid
        &#34;&#34;&#34;
        if isinstance(cloud_name, str):
            for index_server in self._all_index_servers:
                if self._all_index_servers[index_server][&#34;engineName&#34;].lower() == cloud_name.lower():
                    return True
            return False
        raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)

    def get(self, cloud_data):
        &#34;&#34;&#34;Returns IndexServer object if a index server is found.

                Args:
                    cloud_data        (int/str)       --    cloud name or
                                                            cloud ID of index server

                Returns:
                    object            (IndexServer)   --  Instance on index server with
                    the engine name or cloud id as item

                Raises:
                    SDKException:
                        Index Server not found.

                        Data type of the input(s) is not valid.
        &#34;&#34;&#34;
        if isinstance(cloud_data, int):
            if cloud_data in self._all_index_servers:
                return IndexServer(
                    self._commcell_object,
                    self._all_index_servers[cloud_data][&#39;engineName&#39;],
                    cloud_data)
            SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
        elif isinstance(cloud_data, str):
            name = cloud_data.lower()
            for itter in self._all_index_servers:
                if self._all_index_servers[itter][&#39;engineName&#39;].lower(
                ) == name:
                    return IndexServer(
                        self._commcell_object,
                        self._all_index_servers[itter][&#39;engineName&#39;],
                        self._all_index_servers[itter][&#39;cloudID&#39;])
            raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
        raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)

    def create(
            self,
            index_server_name,
            index_server_node_names,
            index_directory,
            index_server_roles,
            index_pool_name=None,
            is_cloud=False,
            cloud_param=None):
        &#34;&#34;&#34;Creates an index server within the commcell

                Args:
                    index_server_node_names         (list)  --  client names for index server node
                    index_server_name               (str)   --  name for the index server
                    index_directory                 (list)  --  list of index locations for the index server
                                                                nodes respectively
                                                    For example:
                                                            [&lt;path_1&gt;] - same index location for all the nodes
                                                            [&lt;path_1&gt;, &lt;path_2&gt;, &lt;path_3&gt;] - different index
                                                    location for index server with 3 nodes
                    index_server_roles              (list)  --  list of role names to be assigned
                    index_pool_name                 (str)   --  name for the index pool to used by cloud index server
                    cloud_param                     (list)  --  list of custom parameters to be parsed
                                                    into the json for index server meta info
                                                    [
                                                        {
                                                            &#34;name&#34;: &lt;name&gt;,
                                                            &#34;value&#34;: &lt;value&gt;
                                                        }
                                                    ]
                    is_cloud            (bool)  --  if true then creates a cloud mode index server

                Raises:
                    SDKException:
                        Data type of the input(s) is not valid.

                        Response was not success.

                        Response was empty.
        &#34;&#34;&#34;
        if not (isinstance(index_server_roles, list) and isinstance(index_server_node_names, list)
                and isinstance(index_server_name, str)):
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        if isinstance(index_directory, str):
            index_directory = index_directory.split(&#34;,&#34;)
        node_count = len(index_server_node_names)
        index_directories_count = len(index_directory)
        if index_directories_count != 1 and index_directories_count != node_count:
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        cloud_meta_infos = {
            &#39;REPLICATION&#39;: &#39;1&#39;,
            &#39;LANGUAGE&#39;: &#39;0&#39;
        }
        node_meta_infos = {
            &#39;PORTNO&#39;: IndexServerConstants.DEFAULT_SOLR_PORT,
            &#39;JVMMAXMEMORY&#39;: IndexServerConstants.DEFAULT_JVM_MAX_MEMORY
        }
        role_meta_infos = {}
        req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
        req_json[&#39;cloudInfoEntity&#39;] = {
            &#39;cloudName&#39;: index_server_name,
            &#39;cloudDisplayName&#39;: index_server_name
        }
        if is_cloud:
            index_pool_obj = self._commcell_object.index_pools[index_pool_name]
            req_json[&#39;type&#39;] = 5
            req_json[&#39;solrCloudInfo&#39;][&#39;cloudPoolInfo&#39;] = {
                &#39;cloudId&#39;: int(index_pool_obj[&#39;pool_id&#39;])
            }
            cloud_meta_infos[&#39;INDEXLOCATION&#39;] = index_directory[0]
        for node_name_index in range(len(index_server_node_names)):
            node_name = index_server_node_names[node_name_index]
            location_index = node_name_index - (node_name_index//index_directories_count)
            node_obj = self._commcell_object.clients[node_name]
            node_data = {
                &#34;opType&#34;: IndexServerConstants.OPERATION_ADD,
                &#34;nodeClientEntity&#34;: {
                    &#34;hostName&#34;: node_obj[&#39;hostname&#39;],
                    &#34;clientId&#34;: int(node_obj[&#39;id&#39;]),
                    &#34;clientName&#34;: node_name
                },
                &#39;nodeMetaInfos&#39;: [{
                    &#34;name&#34;: &#34;INDEXLOCATION&#34;,
                    &#34;value&#34;: index_directory[location_index]
                }]
            }
            for node_info in node_meta_infos:
                node_data[&#39;nodeMetaInfos&#39;].append({
                    &#39;name&#39;: node_info,
                    &#39;value&#39;: str(node_meta_infos[node_info])
                })
            req_json[&#39;cloudNodes&#39;].append(node_data)
        for role in index_server_roles:
            role_id = self._roles_obj.get_role_id(role)
            if not role_id:
                raise SDKException(&#39;IndexServers&#39;, &#39;103&#39;)
            role_data = {
                &#34;roleId&#34;: role_id,
                &#34;roleName&#34;: role,
                &#34;operationType&#34;: IndexServerConstants.OPERATION_ADD,
                &#39;roleMetaInfos&#39;: []
            }
            for role_info in role_meta_infos:
                role_data[&#39;roleMetaInfos&#39;].append({
                    &#39;name&#39;: role_info,
                    &#39;value&#39;: role_meta_infos[role_info]
                })
            req_json[&#39;solrCloudInfo&#39;][&#39;roles&#39;].append(role_data)
        if cloud_param:
            for param in cloud_param:
                if param[&#39;name&#39;] in cloud_meta_infos:
                    del cloud_meta_infos[param[&#39;name&#39;]]
                req_json[&#39;cloudMetaInfos&#39;].append(param)
        for cloud_info in cloud_meta_infos:
            req_json[&#39;cloudMetaInfos&#39;].append({
                &#39;name&#39;: cloud_info,
                &#39;value&#39;: cloud_meta_infos[cloud_info]
            })
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_CREATE&#39;], req_json)
        if flag:
            if response.json():
                error_code = response.json()[&#39;genericResp&#39;][&#39;errorCode&#39;]
                error_string = response.json()[&#39;genericResp&#39;][&#39;errorMessage&#39;]
                if error_code == 0:
                    self.refresh()
                    self._commcell_object.clients.refresh()
                    self._commcell_object.datacube.refresh_engine()
                else:
                    o_str = &#39;Failed to create Index Server. Error: &#34;{0}&#34;&#39;.format(
                        error_string)
                    raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)

    def delete(self, cloud_name):
        &#34;&#34;&#34;Deletes / removes an index server from the commcell

                Args:
                    cloud_name      (str)   --  cloud name of index server
                    to be removed from the commcell

                Raises:
                    SDKException:
                        Data type of the input(s) is not valid.

                        Response was not success.

                        Response was empty.
        &#34;&#34;&#34;
        if not isinstance(cloud_name, str):
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        cloud_id = self.get(cloud_name).cloud_id
        req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
        req_json[&#34;opType&#34;] = IndexServerConstants.OPERATION_DELETE
        req_json[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = cloud_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_DELETE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json() \
                    and &#39;errorCode&#39; not in response.json()[&#39;genericResp&#39;]:
                self.refresh()
                self._commcell_object.clients.refresh()
                self._commcell_object.datacube.refresh_engine()
                return
            if response.json() and &#39;genericResp&#39; in response.json():
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, response.json()[&#39;genericResp&#39;].get(
                        &#39;errorMessage&#39;, &#39;&#39;))
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        self._response_not_success(response)

    def prune_orphan_datasources(self):
        &#34;&#34;&#34;Deletes all the orphan datasources
            Raises:
                SDKException:
                    if failed to prune the orphan datasources

                    If response is empty

                    if response is not success
        &#34;&#34;&#34;
        prune_datasource = self._services[&#39;PRUNE_DATASOURCE&#39;]
        request_json = IndexServerConstants.PRUNE_REQUEST_JSON
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, prune_datasource, request_json)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;Failed to prune orphan datasources&#39;)
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))


class IndexServerOSType(enum.Enum):
    &#34;&#34;&#34;Enum class for Index Server OS Type&#34;&#34;&#34;
    WINDOWS = &#34;Windows&#34;
    UNIX = &#34;Unix&#34;
    MIXED = &#34;Mixed&#34;


class IndexServer(object):
    &#34;&#34;&#34;Class for performing index server operations for a specific index server&#34;&#34;&#34;

    def __init__(self, commcell_obj, name, cloud_id=None):
        &#34;&#34;&#34;Initialize the IndexServer class instance.

            Args:
                commcell_obj    (object)        --  instance of the Commcell class

                name            (str)           --  name of the index server

                cloud_id        (int)           --  cloud id of the index server
                    default: None

            Returns:
                object - instance of the IndexServer class
        &#34;&#34;&#34;
        self._engine_name = name
        self._commcell_obj = commcell_obj
        self._cvpysdk_object = self._commcell_obj._cvpysdk_object
        self._services = self._commcell_obj._services
        if cloud_id:
            self._cloud_id = cloud_id
        else:
            self._cloud_id = self._get_cloud_id()
        self._properties = None
        self._roles_obj = None
        self.plan_info = None
        self.os_type = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return &#39;IndexServer class instance for index server: &#34;{0}&#34;&#39;.format(
            self._engine_name)

    def _get_cloud_id(self):
        &#34;&#34;&#34;Get the cloud id for the index server

                Returns:
                    int - cloud id for the index server
        &#34;&#34;&#34;
        return self._commcell_obj.index_servers.get(self._engine_name).cloud_id

    def _get_properties(self):
        &#34;&#34;&#34;Get the properties of the index server&#34;&#34;&#34;
        self._properties = self._commcell_obj.index_servers.get_properties(
            self._engine_name)

    def refresh(self):
        &#34;&#34;&#34;Refresh the index server properties&#34;&#34;&#34;
        self._commcell_obj.index_servers.refresh()
        self._get_properties()
        if self.os_type is None:
            self.os_type = self.get_os_info()
        if not self._roles_obj:
            self._roles_obj = _Roles(self._commcell_obj)
        if self.plan_info is None:
            self.plan_info = self.get_plan_info()

    def update_roles_data(self):
        &#34;&#34;&#34;Synchronize the cloud roles data with the commcell&#34;&#34;&#34;
        self._roles_obj.update_roles_data()

    def modify(self, index_location, node_name, node_params):
        &#34;&#34;&#34;Modifies the properties of an index server

            Args:
                index_location      (str)       --  index server data directory
                node_name           (str)       --  index server node name
                node_params         (dict)      --  parameters to be passed
                                                    [
                                                        {
                                                            &#34;name&#34; : &lt;property_name&gt;,
                                                            &#34;value&#34; : &lt;property_value&gt;
                                                        }
                                                    ]
            Raises:
                SDKException:
                    Response was not success.
                    Response was empty.
        &#34;&#34;&#34;
        json_req = deepcopy(IndexServerConstants.REQUEST_JSON)
        json_req[&#39;opType&#39;] = IndexServerConstants.OPERATION_EDIT
        json_req[&#39;cloudNodes&#39;] = [{
            &#34;opType&#34;: IndexServerConstants.OPERATION_EDIT,
            &#34;nodeClientEntity&#34;: {
                &#34;clientId&#34;: int(self._commcell_obj.clients.get(node_name).client_id)
            },
            &#34;nodeMetaInfos&#34;: [
                {
                    &#34;name&#34;: &#34;INDEXLOCATION&#34;,
                    &#34;value&#34;: index_location
                }
            ]
        }]
        json_req[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = self.cloud_id
        for param in node_params:
            json_req[&#39;cloudNodes&#39;][0][&#39;nodeMetaInfos&#39;].append(param)
        flag, response = self._cvpysdk_object.make_request(
            &#34;POST&#34;, self._services[&#39;CLOUD_MODIFY&#39;], json_req)
        if flag:
            if response.json():
                if &#39;cloudId&#39; in response.json():
                    self.refresh()
                    return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def change_plan(self, plan_name):
        &#34;&#34;&#34;Modifies the plan used by an index server

            Args:
                plan_name      (str)       --  Name of the plan to be used for the index server
            Raises:
                SDKException:
                    Response was not success.
                    Response was empty.
                    if plan with given name doesn&#39;t exist
        &#34;&#34;&#34;
        if not self._commcell_obj.plans.has_plan(plan_name):
            raise SDKException(
                &#39;Plan&#39;, &#39;102&#39;, f&#34;Plan with name [{plan_name}] doesn&#39;t exist&#34;)
        request_json = {
            &#34;opType&#34;: IndexServerConstants.OPERATION_EDIT,
            &#34;type&#34;: 1,
            &#34;planInfo&#34;: {
                &#34;planId&#34;: int(self._commcell_obj.plans.get(plan_name).plan_id)
            },
            &#34;cloudInfoEntity&#34;: {
                &#34;cloudId&#34;: self.cloud_id
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#34;POST&#34;, self._services[&#39;CLOUD_MODIFY&#39;], request_json)
        if flag:
            if response.json():
                if &#39;cloudId&#39; in response.json():
                    self.refresh()
                    return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def update_role(self, props=None):
        &#34;&#34;&#34;Updates a role of an Index Server

            Args:
                props               (list)  --  array of dictionaries
                consisting details of the roles such as role name
                and operation type.
                                            [{
                                                &#34;roleName&#34;: &lt;name&gt;          (str)
                                                &#34;operationType&#34;: 1 or 2     (int)
                                                    1 for adding a role
                                                    2 for removing a role
                                            }]

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        json_req = {&#34;cloudId&#34;: self.cloud_id, &#34;roles&#34;: []}
        if props:
            for prop in props:
                role_id = self._roles_obj.get_role_id(prop[&#39;roleName&#39;])
                if not role_id:
                    raise SDKException(&#39;IndexServers&#39;, &#39;103&#39;)
                prop[&#39;roleId&#39;] = role_id
                json_req[&#39;roles&#39;].append(prop)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_ROLE_UPDATE&#39;], json_req
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def delete_docs_from_core(self, core_name, select_dict = None):
        &#34;&#34;&#34;Deletes the docs from the given core name on index server depending on the select dict passed

                Args:

                        core_name               (str)  --  name of the solr core
                        select_dict             (dict) --  dict with query to delete specific documents
                                                    default query - &#34;*:*&#34; (Deletes all the docs)

                    Returns:
                        None

                    Raises:
                        SDKException:

                            if input data is not valid

                            if index server is cloud, not implemented error

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        if not isinstance(core_name, str):
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        if self.is_cloud:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Not implemented for solr cloud&#34;)
        json_req = {&#34;delete&#34;: {&#34;query&#34;: self._create_solr_query(select_dict).replace(&#34;q=&#34;, &#34;&#34;).replace(&#34;&amp;wt=json&#34;, &#34;&#34;)}}
        baseurl = f&#34;{self.server_url[0]}/solr/{core_name}/update?commitWithin=1000&amp;overwrite=true&amp;wt=json&#34;
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, baseurl, json_req)
        if flag and response.json():
            if &#39;error&#39; in response.json():
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, f&#39; Failed with error message - &#39;
                                                          f&#39;{response.json().get(&#34;error&#34;).get(&#34;msg&#34;)}&#39;)
            if &#39;responseHeader&#39; in response.json():
                commitstatus = str(response.json().get(&#34;responseHeader&#34;).get(&#34;status&#34;))
                if int(commitstatus) != 0:
                    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;,
                                       f&#34;Deleting docs from the core returned bad status - {commitstatus}&#34;)
                return
        raise SDKException(&#39;IndexServers&#39;, &#39;111&#39;)

    def hard_commit(self, core_name):
        &#34;&#34;&#34;do hard commit for the given core name on index server

                    Args:

                        core_name               (str)  --  name of the solr core

                    Returns:
                        None

                    Raises:
                        SDKException:

                            if input data is not valid

                            if index server is cloud, not implemented error

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        if not isinstance(core_name, str):
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        if self.is_cloud:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Not implemented for solr cloud&#34;)
        baseurl = f&#34;{self.server_url[0]}/solr/{core_name}/update?commit=true&#34;
        flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, baseurl)
        if flag and response.json():
            if &#39;error&#39; in response.json():
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Hard commit returned error&#34;)
            if &#39;responseHeader&#39; in response.json():
                commitstatus = str(response.json()[&#39;responseHeader&#39;][&#39;status&#39;])
                if int(commitstatus) != 0:
                    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Hard commit returned bad status&#34;)
                return
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Something went wrong with hard commit&#34;)

    
    def get_health_indicators(self, client_name=None):
        &#34;&#34;&#34;Get health indicators for index server node by client name

                Args:
                    client_name     (str)       --  name of the client node

                Returns:
                    (response(str)) -- str json object

                Raises:

                    SDKException:
                        if input data is not valid
                        if client name is not passed for index server cloud
                        if response is not success
                        if response is empty

        &#34;&#34;&#34;
        server_url = self.server_url[0]
        response = None
        if self.is_cloud or len(self.client_name) &gt; 1:
            if client_name is None:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;Client name param missing for solr cloud&#39;)
            if client_name not in self.client_name:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;client name not found in this index server cloud&#39;)
            server_url = self.server_url[self.client_name.index(client_name)]
        baseurl = f&#34;{server_url}/solr/rest/admin/healthsummary&#34;
        headers = {
            &#39;Accept&#39;: &#39;application/xml&#39;
        }
        flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, headers=headers, url=baseurl)
        if flag:
            return response
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Could not get health summary for [{}]&#34;.format(client_name))

    def get_all_cores(self, client_name=None):
        &#34;&#34;&#34;gets all cores &amp; core details from index server

                Args:
                    client_name     (str)       --  name of the client node
                        ***Applicable only for solr cloud mode or multi node Index Server***

                Returns:
                    (list,dict)     -- list containing core names
                                    -- dict containing details about cores

                Raises:

                    SDKException:

                        if input data is not valid

                        if client name is not passed for index server cloud

                        if response is not success

                        if response is empty

        &#34;&#34;&#34;
        server_url = self.server_url[0]
        if self.is_cloud or len(self.client_name) &gt; 1:
            if client_name is None:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;Client name param missing for solr cloud&#39;)
            if client_name not in self.client_name:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;client name not found in this index server cloud&#39;)
            server_url = self.server_url[self.client_name.index(client_name)]
        core_names = []
        baseurl = f&#34;{server_url}/solr/admin/cores&#34;
        flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, baseurl)
        if flag and response.json():
            if &#39;error&#39; in response.json():
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Unable to get core names from index server&#34;)
            if &#39;status&#39; in response.json():
                for core in response.json()[&#39;status&#39;]:
                    core_names.append(core)
                return core_names, response.json()[&#39;status&#39;]
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Something went wrong while getting core names&#34;)

    def _create_solr_query(self, select_dict=None, attr_list=None, op_params=None):
        &#34;&#34;&#34;Method to create the solr query based on the params
            Args:
                select_dict     (dictionary)     --  Dictionary containing search criteria and value
                                                     Acts as &#39;q&#39; field in solr query

                attr_list       (set)            --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in solr query

                op_params       (dictionary)     --  Other params and values for solr query
                                                        (Ex: start, rows)

            Returns:
                The solr url based on params

            Raises:
                SDKException:

                        if failed to form solr query
        &#34;&#34;&#34;
        try:
            search_query = f&#39;q=&#39;
            simple_search = 0
            if select_dict:
                for key, value in select_dict.items():
                    if isinstance(key, tuple):
                        if isinstance(value, list):
                            search_query += f&#39;({key[0]}:{str(value[0])}&#39;
                            for val in value[1:]:
                                search_query += f&#39; OR {key[0]}:{str(val)}&#39;
                        else:
                            search_query += f&#39;({key[0]}:{value}&#39;
                        for key_val in key[1:]:
                            if isinstance(value, list):
                                search_query += f&#39; OR {key_val}:{str(value[0])}&#39;
                                for val in value[1:]:
                                    search_query += f&#39; OR {key_val}:{str(val)}&#39;
                            else:
                                search_query += f&#39; OR {key_val}:{value}&#39;
                        search_query += &#39;) AND &#39;
                    elif isinstance(value, list):
                        search_query += f&#39;({key}:{str(value[0])}&#39;
                        for val in value[1:]:
                            search_query += f&#39; OR {key}:{str(val)}&#39;
                        search_query += &#34;) AND &#34;
                    elif key == &#34;keyword&#34;:
                        search_query += &#34;(&#34; + value + &#34;)&#34;
                        simple_search = 1
                        break
                    else:
                        search_query = search_query + f&#39;{key}:{str(value)} AND &#39;
                if simple_search == 0:
                    search_query = search_query[:-5]
            else:
                search_query += &#34;*:*&#34;

            field_query = &#34;&#34;
            if attr_list:
                field_query = &#34;&amp;fl=&#34;
                for item in attr_list:
                    field_query += f&#39;{str(item)},&#39;
                field_query = field_query[:-1]
            if attr_list and &#39;content&#39; in attr_list:
                field_query = f&#34;{field_query}&amp;exclude=false&#34;

            ex_query = &#34;&#34;
            if not op_params:
                op_params = {&#39;wt&#39;: &#34;json&#34;}
            else:
                op_params[&#39;wt&#39;] = &#34;json&#34;
            for key, values in op_params.items():
                if isinstance(values, list):
                    for value in values:
                        ex_query += self.__form_field_query(key, value)
                else:
                    ex_query += self.__form_field_query(key, values)
            final_url = f&#39;{search_query}{field_query}{ex_query}&#39;
            return final_url
        except Exception as excp:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, f&#34;Something went wrong while creating solr query - {excp}&#34;)

    def execute_solr_query(
            self,
            core_name,
            solr_client=None,
            select_dict=None,
            attr_list=None,
            op_params=None):
        &#34;&#34;&#34;Creates solr url based on input and executes it on solr on given core/collection
            Args:

                core_name               (str)           --  Core name/collection name where we want to query

                solr_client             (str)           --  Index Server client name to execute solr query
                                                                Default : None (picks first client on index server)

                select_dict             (dictionary)    --  Dictionary containing search criteria and
                                                            value. Acts as &#39;q&#39; field in solr query

                        Example :

                            1. General Criteria to filter results              -   {&#34;jid&#34;: 1024, &#34;datatype&#34;: 2,clid: 2}
                            2. Keyword Searches on solr                        -   {&#39;keyword&#39;: &#39;SearchKeyword&#39;}
                            3. For multiple value searches on single field     -   {&#39;cvowner&#39;: [&#39;xxx&#39;,&#39;yyy&#39;]}
                            4. For single value searches on multiple fields    -   {(&#39;cvowner&#39;,&#39;cvreaddisp&#39;) : &#39;xxx&#39;}

                attr_list               (set)           --  Column names to be returned in results.
                                                                Acts as &#39;fl&#39; in solr query

                        Example (For Exchange Mailbox IDA, below fields are there in solr) :
                                    {
                                     &#39;msgclass&#39;,
                                     &#39;ccsmtp&#39;,
                                     &#39;fmsmtp&#39;,
                                     &#39;folder&#39;
                                   }

                op_params               (dictionary)    --  Other params and values for solr query. Do not
                                                            mention &#39;wt&#39; param as it is always json

                                                            Example : {&#34;rows&#34;: 0}

            Returns:
                content of the response

            Raises:
                SDKException:

                        if unable to send request

                        if response is not success
        &#34;&#34;&#34;
        solr_url = f&#34;solr/{core_name}/select?{self._create_solr_query(select_dict, attr_list, op_params)}&#34;
        if solr_client is None:
            solr_url = f&#34;{self.server_url[0]}/{solr_url}&#34;
        else:
            if solr_client not in self.client_name:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;client name not found in this index server&#39;)
            server_url = self.server_url[self.client_name.index(solr_client)]
            solr_url = f&#34;{server_url}/{solr_url}&#34;
        flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, solr_url)
        if flag and response.json():
            return response.json()
        elif response.status_code == httplib.FORBIDDEN:
            cmd = f&#34;(Invoke-WebRequest -UseBasicParsing -uri \&#34;{solr_url}\&#34;).content&#34;
            client_obj = None
            if solr_client:
                client_obj = self._commcell_obj.clients.get(solr_client)
            else:
                # if no client is passed, then take first client in index server cloud
                client_obj = self._commcell_obj.clients.get(self.client_name[0])
            exit_code, output, error_message = client_obj.execute_script(script_type=&#34;PowerShell&#34;,
                                                                         script=cmd)
            if exit_code != 0:
                raise SDKException(
                    &#39;IndexServers&#39;,
                    &#39;104&#39;,
                    f&#34;Something went wrong while querying solr - {exit_code}&#34;)
            elif error_message:
                raise SDKException(
                    &#39;IndexServers&#39;,
                    &#39;104&#39;,
                    f&#34;Something went wrong while querying solr - {error_message}&#34;)
            try:
                return json.loads(output.strip())
            except Exception:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, f&#34;Something went wrong while querying solr - {output}&#34;)
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Something went wrong while querying solr&#34;)

    def get_index_node(self, node_name):
        &#34;&#34;&#34;Returns an Index server node object for given node name
            Args:
                node_name           (str)   --  Index server node name

            Returns:
                IndexNode class object

            Raises:
                SDKException:

                        if node not found for the given node name

        &#34;&#34;&#34;
        node_name = node_name.lower()
        if node_name in self.client_name:
            return IndexNode(self._commcell_obj, self.engine_name, node_name)
        raise SDKException(&#34;IndexServers&#34;, &#39;104&#39;, &#39;Index server node not found&#39;)

    def get_plan_info(self):
        &#34;&#34;&#34;Gets the plan information of the index server
            Returns:
                dict - containing the plan information
        &#34;&#34;&#34;
        client = self._commcell_obj.clients.get(self.engine_name)
        instance_props = client.properties.get(&#34;pseudoClientInfo&#34;, {}).get(&#34;distributedClusterInstanceProperties&#34;, {})
        plan_details = instance_props.get(&#34;clusterConfig&#34;,{}).get(&#34;cloudInfo&#34;, {}).get(&#34;planInfo&#34;, {})
        return plan_details

    def get_os_info(self):
        &#34;&#34;&#34;Returns the OS type for the Index server&#34;&#34;&#34;

        nodes_name = self.client_name
        nodes = [self._commcell_obj.clients.get(node) for node in nodes_name]
        nodes_os_info = [node.os_info for node in nodes]
        if IndexServerOSType.WINDOWS.value.lower() in nodes_os_info[0].lower():
            for node in nodes_os_info[1:]:
                if IndexServerOSType.UNIX.value.lower() in node.lower():
                    return IndexServerOSType.MIXED.value
            return IndexServerOSType.WINDOWS.value
        else:
            for node in nodes_os_info[1:]:
                if IndexServerOSType.WINDOWS.value.lower() in node.lower():
                    return IndexServerOSType.MIXED.value
            return IndexServerOSType.UNIX.value

    def __form_field_query(self, key, value):
        &#34;&#34;&#34;
        Returns the query with the key and value passed
        Args:
                key(str)    -- key for forming the query
                value(str)  -- value for forming the query
            Returns:
                query to be executed against solr
        &#34;&#34;&#34;
        query = None
        if value is None:
            query = f&#39;&amp;{key}&#39;
        else:
            query = f&#39;&amp;{key}={str(value)}&#39;
        return query

    @property
    def plan_name(self):
        &#34;&#34;&#34;Returns the plan name associated with index server
            Returns:
                str - name of the plan
        &#34;&#34;&#34;
        return self.plan_info.get(&#34;planName&#34;)

    @property
    def os_info(self):
        &#34;&#34;&#34;Returns the OS type for the Index server&#34;&#34;&#34;
        return self.os_type

    @property
    def is_cloud(self):
        &#34;&#34;&#34;Returns true if the Index server is cloud and false if not&#34;&#34;&#34;
        return self.server_type == 5

    @property
    def nodes_count(self):
        &#34;&#34;&#34;Returns the count of Index server nodes&#34;&#34;&#34;
        return len(self.client_id)

    @property
    def roles_data(self):
        &#34;&#34;&#34;Returns the cloud roles data&#34;&#34;&#34;
        return self._roles_obj.roles_data

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the index server properties&#34;&#34;&#34;
        return self._properties

    @property
    def host_name(self):
        &#34;&#34;&#34;Returns a list of host names of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.HOST_NAME]

    @property
    def cloud_name(self):
        &#34;&#34;&#34;Returns the internal cloud name of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CLOUD_NAME]

    @property
    def client_name(self):
        &#34;&#34;&#34;Returns a list of client names of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CLIENT_NAME]

    @property
    def server_url(self):
        &#34;&#34;&#34;Returns a list of Solr url of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CI_SERVER_URL]

    @property
    def type(self):
        &#34;&#34;&#34;Returns the type of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.TYPE]

    @property
    def base_port(self):
        &#34;&#34;&#34;Returns a list of base ports of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.BASE_PORT]

    @property
    def client_id(self):
        &#34;&#34;&#34;Returns a list client ids of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CLIENT_ID]

    @property
    def roles(self):
        &#34;&#34;&#34;Returns a list of roles of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.ROLES]

    @property
    def role_display_name(self):
        &#34;&#34;&#34;Returns the roles display name of index server&#34;&#34;&#34;
        role_disp_name = []
        for role_version in self.roles:
            for role in self.roles_data:
                if role_version == role[&#39;roleVersion&#39;]:
                    role_disp_name.append(role[&#39;roleName&#39;])
                    break
        return role_disp_name

    @property
    def cloud_id(self):
        &#34;&#34;&#34;Returns the cloud id of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CLOUD_ID]

    @property
    def server_type(self):
        &#34;&#34;&#34;Returns the server type of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.SERVER_TYPE]

    @property
    def engine_name(self):
        &#34;&#34;&#34;Returns the engine name of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.ENGINE_NAME]

    @property
    def index_server_client_id(self):
        &#34;&#34;&#34;Returns the index server client id of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.INDEX_SERVER_CLIENT_ID]

    @property
    def fs_collection(self):
        &#34;&#34;&#34;Returns the multinode collection name of File System Index

            Returns:

                str --  File System index multinode collection name

        &#34;&#34;&#34;
        return f&#39;fsindex_{&#34;&#34;.join(letter for letter in self.cloud_name if letter.isalnum())}_multinode&#39;


class IndexNode(object):
    &#34;&#34;&#34;Class for Index server node object&#34;&#34;&#34;

    def __init__(self, commcell_obj, index_server_name, node_name):
        &#34;&#34;&#34;Initialize the IndexNode class

            Args:
                commcell_obj        (object)    --  commcell object
                index_server_name   (int)       --  Index server name
                node_name           (str)       --  Index server node client name

        &#34;&#34;&#34;
        self.commcell = commcell_obj
        self.index_server_name = index_server_name
        self.data_index = 0
        self.index_server = None
        self.index_node_name = node_name.lower()
        self.index_node_client = None
        self.index_client_properties = None
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;Refresh the index node properties&#34;&#34;&#34;
        self.commcell.index_servers.refresh()
        self.index_server = self.commcell.index_servers.get(self.index_server_name)
        self.data_index = self.index_server.client_name.index(self.index_node_name)
        self.commcell.clients.refresh()
        self.index_node_client = self.commcell.clients.get(self.index_node_name)
        # TODO: Rewrite Index server API logic to access client properties
        self.index_client_properties = (self.index_node_client.properties.get(&#39;pseudoClientInfo&#39;, {}).
                                        get(&#39;indexServerProperties&#39;, {}))

    @property
    def node_name(self):
        &#34;&#34;&#34;Returns Index server node client name&#34;&#34;&#34;
        return self.index_server.client_name[self.data_index]

    @property
    def node_id(self):
        &#34;&#34;&#34;Returns Index server node client id&#34;&#34;&#34;
        return self.index_server.client_id[self.data_index]

    @property
    def solr_port(self):
        &#34;&#34;&#34;Returns port number Solr is running on the Index server node&#34;&#34;&#34;
        return self.index_server.base_port[self.data_index]

    @property
    def solr_url(self):
        &#34;&#34;&#34;Returns Solr URL for Index server node&#34;&#34;&#34;
        return self.index_server.server_url[self.data_index]

    @property
    def roles(self):
        &#34;&#34;&#34;Returns the array of roles installed with the index server within the commcell&#34;&#34;&#34;
        return self.index_server.role_display_name

    @property
    def index_location(self):
        &#34;&#34;&#34;Returns Index directory for the Index server Node&#34;&#34;&#34;
        node_meta_infos = self.index_client_properties[&#39;nodeMetaInfos&#39;]
        for info in node_meta_infos:
            if info[&#39;name&#39;] == &#39;INDEXLOCATION&#39;:
                return info[&#39;value&#39;]
        return None

    @property
    def jvm_memory(self):
        &#34;&#34;&#34;Returns Solr JVM memory for the Index server Node&#34;&#34;&#34;
        node_meta_infos = self.index_client_properties[&#39;nodeMetaInfos&#39;]
        for info in node_meta_infos:
            if info[&#39;name&#39;] == &#39;JVMMAXMEMORY&#39;:
                return info[&#39;value&#39;]
        return None

    @solr_port.setter
    def solr_port(self, port_no):
        &#34;&#34;&#34;Setter to set the Solr port number for the node

            Args:
                port_no     (str)   --  Solr port number to be set for node

        &#34;&#34;&#34;
        solr_port_param = deepcopy(IndexServerConstants.SOLR_PORT_META_INFO)
        solr_port_param[&#39;value&#39;] = str(port_no)
        cloud_param = [solr_port_param]
        self.index_server.modify(self.index_location, self.index_node_name, cloud_param)
        self.refresh()

    @jvm_memory.setter
    def jvm_memory(self, memory):
        &#34;&#34;&#34;Setter to set the Solr JVM memory for the node

                    Args:
                        memory      (str)   --  Solr JVM memory to be set for the node

        &#34;&#34;&#34;
        solr_jvm_param = deepcopy(IndexServerConstants.SOLR_JVM_META_INFO)
        solr_jvm_param[&#39;value&#39;] = str(memory)
        solr_port_param = deepcopy(IndexServerConstants.SOLR_PORT_META_INFO)
        solr_port_param[&#39;value&#39;] = str(self.solr_port)
        cloud_param = [solr_jvm_param, solr_port_param]
        self.index_server.modify(self.index_location, self.index_node_name, cloud_param)
        self.refresh()


class _Roles(object):
    &#34;&#34;&#34;Class for cloud roles data operations&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes _Roles class with commcell object

            Args:
                commcell_object (object)    --  instance of Commcell class

            Returns:
                object  -   instance of _Roles class
        &#34;&#34;&#34;
        self.commcell_object = commcell_object
        self._roles_data = None
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;Refreshes the class data&#34;&#34;&#34;
        self._get_all_roles()

    def _get_all_roles(self):
        &#34;&#34;&#34;Method to get all cloud roles details available on the commcell.

            Raises:
                SDKException:
                    Response was empty.

                    Response was not success.
        &#34;&#34;&#34;
        flag, response = self.commcell_object._cvpysdk_object.make_request(
            &#34;GET&#34;, self.commcell_object._services[&#39;GET_ALL_ROLES&#39;]
        )
        if flag:
            if response.json():
                if &#39;rolesInfo&#39; in response.json():
                    self._roles_data = response.json()[&#39;rolesInfo&#39;]
                    return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def get_role_id(self, role_name):
        &#34;&#34;&#34;Method to get a cloud role id with given name

            Args:
                role_name       (str)   --  cloud role name of which role id has to be returned

            Returns:
                role_id         (int)   --  if role name is found in roles data then returns the id
                                            else returns None

        &#34;&#34;&#34;
        for role_data in self._roles_data:
            if role_data[&#39;roleName&#39;] == role_name:
                return role_data[&#39;roleId&#39;]
        return None

    def update_roles_data(self):
        &#34;&#34;&#34;Synchronize the cloud role data with the commcell database&#34;&#34;&#34;
        self._get_all_roles()

    @property
    def roles_data(self):
        &#34;&#34;&#34;Returns the list of dictionary of details of each cloud role&#34;&#34;&#34;
        return self._roles_data</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.index_server.IndexNode"><code class="flex name class">
<span>class <span class="ident">IndexNode</span></span>
<span>(</span><span>commcell_obj, index_server_name, node_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for Index server node object</p>
<p>Initialize the IndexNode class</p>
<h2 id="args">Args</h2>
<p>commcell_obj
(object)
&ndash;
commcell object
index_server_name
(int)
&ndash;
Index server name
node_name
(str)
&ndash;
Index server node client name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1328-L1431" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class IndexNode(object):
    &#34;&#34;&#34;Class for Index server node object&#34;&#34;&#34;

    def __init__(self, commcell_obj, index_server_name, node_name):
        &#34;&#34;&#34;Initialize the IndexNode class

            Args:
                commcell_obj        (object)    --  commcell object
                index_server_name   (int)       --  Index server name
                node_name           (str)       --  Index server node client name

        &#34;&#34;&#34;
        self.commcell = commcell_obj
        self.index_server_name = index_server_name
        self.data_index = 0
        self.index_server = None
        self.index_node_name = node_name.lower()
        self.index_node_client = None
        self.index_client_properties = None
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;Refresh the index node properties&#34;&#34;&#34;
        self.commcell.index_servers.refresh()
        self.index_server = self.commcell.index_servers.get(self.index_server_name)
        self.data_index = self.index_server.client_name.index(self.index_node_name)
        self.commcell.clients.refresh()
        self.index_node_client = self.commcell.clients.get(self.index_node_name)
        # TODO: Rewrite Index server API logic to access client properties
        self.index_client_properties = (self.index_node_client.properties.get(&#39;pseudoClientInfo&#39;, {}).
                                        get(&#39;indexServerProperties&#39;, {}))

    @property
    def node_name(self):
        &#34;&#34;&#34;Returns Index server node client name&#34;&#34;&#34;
        return self.index_server.client_name[self.data_index]

    @property
    def node_id(self):
        &#34;&#34;&#34;Returns Index server node client id&#34;&#34;&#34;
        return self.index_server.client_id[self.data_index]

    @property
    def solr_port(self):
        &#34;&#34;&#34;Returns port number Solr is running on the Index server node&#34;&#34;&#34;
        return self.index_server.base_port[self.data_index]

    @property
    def solr_url(self):
        &#34;&#34;&#34;Returns Solr URL for Index server node&#34;&#34;&#34;
        return self.index_server.server_url[self.data_index]

    @property
    def roles(self):
        &#34;&#34;&#34;Returns the array of roles installed with the index server within the commcell&#34;&#34;&#34;
        return self.index_server.role_display_name

    @property
    def index_location(self):
        &#34;&#34;&#34;Returns Index directory for the Index server Node&#34;&#34;&#34;
        node_meta_infos = self.index_client_properties[&#39;nodeMetaInfos&#39;]
        for info in node_meta_infos:
            if info[&#39;name&#39;] == &#39;INDEXLOCATION&#39;:
                return info[&#39;value&#39;]
        return None

    @property
    def jvm_memory(self):
        &#34;&#34;&#34;Returns Solr JVM memory for the Index server Node&#34;&#34;&#34;
        node_meta_infos = self.index_client_properties[&#39;nodeMetaInfos&#39;]
        for info in node_meta_infos:
            if info[&#39;name&#39;] == &#39;JVMMAXMEMORY&#39;:
                return info[&#39;value&#39;]
        return None

    @solr_port.setter
    def solr_port(self, port_no):
        &#34;&#34;&#34;Setter to set the Solr port number for the node

            Args:
                port_no     (str)   --  Solr port number to be set for node

        &#34;&#34;&#34;
        solr_port_param = deepcopy(IndexServerConstants.SOLR_PORT_META_INFO)
        solr_port_param[&#39;value&#39;] = str(port_no)
        cloud_param = [solr_port_param]
        self.index_server.modify(self.index_location, self.index_node_name, cloud_param)
        self.refresh()

    @jvm_memory.setter
    def jvm_memory(self, memory):
        &#34;&#34;&#34;Setter to set the Solr JVM memory for the node

                    Args:
                        memory      (str)   --  Solr JVM memory to be set for the node

        &#34;&#34;&#34;
        solr_jvm_param = deepcopy(IndexServerConstants.SOLR_JVM_META_INFO)
        solr_jvm_param[&#39;value&#39;] = str(memory)
        solr_port_param = deepcopy(IndexServerConstants.SOLR_PORT_META_INFO)
        solr_port_param[&#39;value&#39;] = str(self.solr_port)
        cloud_param = [solr_jvm_param, solr_port_param]
        self.index_server.modify(self.index_location, self.index_node_name, cloud_param)
        self.refresh()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.index_server.IndexNode.index_location"><code class="name">var <span class="ident">index_location</span></code></dt>
<dd>
<div class="desc"><p>Returns Index directory for the Index server Node</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1385-L1392" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_location(self):
    &#34;&#34;&#34;Returns Index directory for the Index server Node&#34;&#34;&#34;
    node_meta_infos = self.index_client_properties[&#39;nodeMetaInfos&#39;]
    for info in node_meta_infos:
        if info[&#39;name&#39;] == &#39;INDEXLOCATION&#39;:
            return info[&#39;value&#39;]
    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexNode.jvm_memory"><code class="name">var <span class="ident">jvm_memory</span></code></dt>
<dd>
<div class="desc"><p>Returns Solr JVM memory for the Index server Node</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1394-L1401" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def jvm_memory(self):
    &#34;&#34;&#34;Returns Solr JVM memory for the Index server Node&#34;&#34;&#34;
    node_meta_infos = self.index_client_properties[&#39;nodeMetaInfos&#39;]
    for info in node_meta_infos:
        if info[&#39;name&#39;] == &#39;JVMMAXMEMORY&#39;:
            return info[&#39;value&#39;]
    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexNode.node_id"><code class="name">var <span class="ident">node_id</span></code></dt>
<dd>
<div class="desc"><p>Returns Index server node client id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1365-L1368" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def node_id(self):
    &#34;&#34;&#34;Returns Index server node client id&#34;&#34;&#34;
    return self.index_server.client_id[self.data_index]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexNode.node_name"><code class="name">var <span class="ident">node_name</span></code></dt>
<dd>
<div class="desc"><p>Returns Index server node client name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1360-L1363" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def node_name(self):
    &#34;&#34;&#34;Returns Index server node client name&#34;&#34;&#34;
    return self.index_server.client_name[self.data_index]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexNode.roles"><code class="name">var <span class="ident">roles</span></code></dt>
<dd>
<div class="desc"><p>Returns the array of roles installed with the index server within the commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1380-L1383" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def roles(self):
    &#34;&#34;&#34;Returns the array of roles installed with the index server within the commcell&#34;&#34;&#34;
    return self.index_server.role_display_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexNode.solr_port"><code class="name">var <span class="ident">solr_port</span></code></dt>
<dd>
<div class="desc"><p>Returns port number Solr is running on the Index server node</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1370-L1373" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def solr_port(self):
    &#34;&#34;&#34;Returns port number Solr is running on the Index server node&#34;&#34;&#34;
    return self.index_server.base_port[self.data_index]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexNode.solr_url"><code class="name">var <span class="ident">solr_url</span></code></dt>
<dd>
<div class="desc"><p>Returns Solr URL for Index server node</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1375-L1378" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def solr_url(self):
    &#34;&#34;&#34;Returns Solr URL for Index server node&#34;&#34;&#34;
    return self.index_server.server_url[self.data_index]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.index_server.IndexNode.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the index node properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1349-L1358" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the index node properties&#34;&#34;&#34;
    self.commcell.index_servers.refresh()
    self.index_server = self.commcell.index_servers.get(self.index_server_name)
    self.data_index = self.index_server.client_name.index(self.index_node_name)
    self.commcell.clients.refresh()
    self.index_node_client = self.commcell.clients.get(self.index_node_name)
    # TODO: Rewrite Index server API logic to access client properties
    self.index_client_properties = (self.index_node_client.properties.get(&#39;pseudoClientInfo&#39;, {}).
                                    get(&#39;indexServerProperties&#39;, {}))</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.index_server.IndexServer"><code class="flex name class">
<span>class <span class="ident">IndexServer</span></span>
<span>(</span><span>commcell_obj, name, cloud_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing index server operations for a specific index server</p>
<p>Initialize the IndexServer class instance.</p>
<h2 id="args">Args</h2>
<p>commcell_obj
(object)
&ndash;
instance of the Commcell class</p>
<p>name
(str)
&ndash;
name of the index server</p>
<p>cloud_id
(int)
&ndash;
cloud id of the index server
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the IndexServer class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L640-L1325" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class IndexServer(object):
    &#34;&#34;&#34;Class for performing index server operations for a specific index server&#34;&#34;&#34;

    def __init__(self, commcell_obj, name, cloud_id=None):
        &#34;&#34;&#34;Initialize the IndexServer class instance.

            Args:
                commcell_obj    (object)        --  instance of the Commcell class

                name            (str)           --  name of the index server

                cloud_id        (int)           --  cloud id of the index server
                    default: None

            Returns:
                object - instance of the IndexServer class
        &#34;&#34;&#34;
        self._engine_name = name
        self._commcell_obj = commcell_obj
        self._cvpysdk_object = self._commcell_obj._cvpysdk_object
        self._services = self._commcell_obj._services
        if cloud_id:
            self._cloud_id = cloud_id
        else:
            self._cloud_id = self._get_cloud_id()
        self._properties = None
        self._roles_obj = None
        self.plan_info = None
        self.os_type = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return &#39;IndexServer class instance for index server: &#34;{0}&#34;&#39;.format(
            self._engine_name)

    def _get_cloud_id(self):
        &#34;&#34;&#34;Get the cloud id for the index server

                Returns:
                    int - cloud id for the index server
        &#34;&#34;&#34;
        return self._commcell_obj.index_servers.get(self._engine_name).cloud_id

    def _get_properties(self):
        &#34;&#34;&#34;Get the properties of the index server&#34;&#34;&#34;
        self._properties = self._commcell_obj.index_servers.get_properties(
            self._engine_name)

    def refresh(self):
        &#34;&#34;&#34;Refresh the index server properties&#34;&#34;&#34;
        self._commcell_obj.index_servers.refresh()
        self._get_properties()
        if self.os_type is None:
            self.os_type = self.get_os_info()
        if not self._roles_obj:
            self._roles_obj = _Roles(self._commcell_obj)
        if self.plan_info is None:
            self.plan_info = self.get_plan_info()

    def update_roles_data(self):
        &#34;&#34;&#34;Synchronize the cloud roles data with the commcell&#34;&#34;&#34;
        self._roles_obj.update_roles_data()

    def modify(self, index_location, node_name, node_params):
        &#34;&#34;&#34;Modifies the properties of an index server

            Args:
                index_location      (str)       --  index server data directory
                node_name           (str)       --  index server node name
                node_params         (dict)      --  parameters to be passed
                                                    [
                                                        {
                                                            &#34;name&#34; : &lt;property_name&gt;,
                                                            &#34;value&#34; : &lt;property_value&gt;
                                                        }
                                                    ]
            Raises:
                SDKException:
                    Response was not success.
                    Response was empty.
        &#34;&#34;&#34;
        json_req = deepcopy(IndexServerConstants.REQUEST_JSON)
        json_req[&#39;opType&#39;] = IndexServerConstants.OPERATION_EDIT
        json_req[&#39;cloudNodes&#39;] = [{
            &#34;opType&#34;: IndexServerConstants.OPERATION_EDIT,
            &#34;nodeClientEntity&#34;: {
                &#34;clientId&#34;: int(self._commcell_obj.clients.get(node_name).client_id)
            },
            &#34;nodeMetaInfos&#34;: [
                {
                    &#34;name&#34;: &#34;INDEXLOCATION&#34;,
                    &#34;value&#34;: index_location
                }
            ]
        }]
        json_req[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = self.cloud_id
        for param in node_params:
            json_req[&#39;cloudNodes&#39;][0][&#39;nodeMetaInfos&#39;].append(param)
        flag, response = self._cvpysdk_object.make_request(
            &#34;POST&#34;, self._services[&#39;CLOUD_MODIFY&#39;], json_req)
        if flag:
            if response.json():
                if &#39;cloudId&#39; in response.json():
                    self.refresh()
                    return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def change_plan(self, plan_name):
        &#34;&#34;&#34;Modifies the plan used by an index server

            Args:
                plan_name      (str)       --  Name of the plan to be used for the index server
            Raises:
                SDKException:
                    Response was not success.
                    Response was empty.
                    if plan with given name doesn&#39;t exist
        &#34;&#34;&#34;
        if not self._commcell_obj.plans.has_plan(plan_name):
            raise SDKException(
                &#39;Plan&#39;, &#39;102&#39;, f&#34;Plan with name [{plan_name}] doesn&#39;t exist&#34;)
        request_json = {
            &#34;opType&#34;: IndexServerConstants.OPERATION_EDIT,
            &#34;type&#34;: 1,
            &#34;planInfo&#34;: {
                &#34;planId&#34;: int(self._commcell_obj.plans.get(plan_name).plan_id)
            },
            &#34;cloudInfoEntity&#34;: {
                &#34;cloudId&#34;: self.cloud_id
            }
        }
        flag, response = self._cvpysdk_object.make_request(
            &#34;POST&#34;, self._services[&#39;CLOUD_MODIFY&#39;], request_json)
        if flag:
            if response.json():
                if &#39;cloudId&#39; in response.json():
                    self.refresh()
                    return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def update_role(self, props=None):
        &#34;&#34;&#34;Updates a role of an Index Server

            Args:
                props               (list)  --  array of dictionaries
                consisting details of the roles such as role name
                and operation type.
                                            [{
                                                &#34;roleName&#34;: &lt;name&gt;          (str)
                                                &#34;operationType&#34;: 1 or 2     (int)
                                                    1 for adding a role
                                                    2 for removing a role
                                            }]

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        json_req = {&#34;cloudId&#34;: self.cloud_id, &#34;roles&#34;: []}
        if props:
            for prop in props:
                role_id = self._roles_obj.get_role_id(prop[&#39;roleName&#39;])
                if not role_id:
                    raise SDKException(&#39;IndexServers&#39;, &#39;103&#39;)
                prop[&#39;roleId&#39;] = role_id
                json_req[&#39;roles&#39;].append(prop)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_ROLE_UPDATE&#39;], json_req
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def delete_docs_from_core(self, core_name, select_dict = None):
        &#34;&#34;&#34;Deletes the docs from the given core name on index server depending on the select dict passed

                Args:

                        core_name               (str)  --  name of the solr core
                        select_dict             (dict) --  dict with query to delete specific documents
                                                    default query - &#34;*:*&#34; (Deletes all the docs)

                    Returns:
                        None

                    Raises:
                        SDKException:

                            if input data is not valid

                            if index server is cloud, not implemented error

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        if not isinstance(core_name, str):
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        if self.is_cloud:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Not implemented for solr cloud&#34;)
        json_req = {&#34;delete&#34;: {&#34;query&#34;: self._create_solr_query(select_dict).replace(&#34;q=&#34;, &#34;&#34;).replace(&#34;&amp;wt=json&#34;, &#34;&#34;)}}
        baseurl = f&#34;{self.server_url[0]}/solr/{core_name}/update?commitWithin=1000&amp;overwrite=true&amp;wt=json&#34;
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, baseurl, json_req)
        if flag and response.json():
            if &#39;error&#39; in response.json():
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, f&#39; Failed with error message - &#39;
                                                          f&#39;{response.json().get(&#34;error&#34;).get(&#34;msg&#34;)}&#39;)
            if &#39;responseHeader&#39; in response.json():
                commitstatus = str(response.json().get(&#34;responseHeader&#34;).get(&#34;status&#34;))
                if int(commitstatus) != 0:
                    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;,
                                       f&#34;Deleting docs from the core returned bad status - {commitstatus}&#34;)
                return
        raise SDKException(&#39;IndexServers&#39;, &#39;111&#39;)

    def hard_commit(self, core_name):
        &#34;&#34;&#34;do hard commit for the given core name on index server

                    Args:

                        core_name               (str)  --  name of the solr core

                    Returns:
                        None

                    Raises:
                        SDKException:

                            if input data is not valid

                            if index server is cloud, not implemented error

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        if not isinstance(core_name, str):
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        if self.is_cloud:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Not implemented for solr cloud&#34;)
        baseurl = f&#34;{self.server_url[0]}/solr/{core_name}/update?commit=true&#34;
        flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, baseurl)
        if flag and response.json():
            if &#39;error&#39; in response.json():
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Hard commit returned error&#34;)
            if &#39;responseHeader&#39; in response.json():
                commitstatus = str(response.json()[&#39;responseHeader&#39;][&#39;status&#39;])
                if int(commitstatus) != 0:
                    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Hard commit returned bad status&#34;)
                return
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Something went wrong with hard commit&#34;)

    
    def get_health_indicators(self, client_name=None):
        &#34;&#34;&#34;Get health indicators for index server node by client name

                Args:
                    client_name     (str)       --  name of the client node

                Returns:
                    (response(str)) -- str json object

                Raises:

                    SDKException:
                        if input data is not valid
                        if client name is not passed for index server cloud
                        if response is not success
                        if response is empty

        &#34;&#34;&#34;
        server_url = self.server_url[0]
        response = None
        if self.is_cloud or len(self.client_name) &gt; 1:
            if client_name is None:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;Client name param missing for solr cloud&#39;)
            if client_name not in self.client_name:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;client name not found in this index server cloud&#39;)
            server_url = self.server_url[self.client_name.index(client_name)]
        baseurl = f&#34;{server_url}/solr/rest/admin/healthsummary&#34;
        headers = {
            &#39;Accept&#39;: &#39;application/xml&#39;
        }
        flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, headers=headers, url=baseurl)
        if flag:
            return response
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Could not get health summary for [{}]&#34;.format(client_name))

    def get_all_cores(self, client_name=None):
        &#34;&#34;&#34;gets all cores &amp; core details from index server

                Args:
                    client_name     (str)       --  name of the client node
                        ***Applicable only for solr cloud mode or multi node Index Server***

                Returns:
                    (list,dict)     -- list containing core names
                                    -- dict containing details about cores

                Raises:

                    SDKException:

                        if input data is not valid

                        if client name is not passed for index server cloud

                        if response is not success

                        if response is empty

        &#34;&#34;&#34;
        server_url = self.server_url[0]
        if self.is_cloud or len(self.client_name) &gt; 1:
            if client_name is None:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;Client name param missing for solr cloud&#39;)
            if client_name not in self.client_name:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;client name not found in this index server cloud&#39;)
            server_url = self.server_url[self.client_name.index(client_name)]
        core_names = []
        baseurl = f&#34;{server_url}/solr/admin/cores&#34;
        flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, baseurl)
        if flag and response.json():
            if &#39;error&#39; in response.json():
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Unable to get core names from index server&#34;)
            if &#39;status&#39; in response.json():
                for core in response.json()[&#39;status&#39;]:
                    core_names.append(core)
                return core_names, response.json()[&#39;status&#39;]
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Something went wrong while getting core names&#34;)

    def _create_solr_query(self, select_dict=None, attr_list=None, op_params=None):
        &#34;&#34;&#34;Method to create the solr query based on the params
            Args:
                select_dict     (dictionary)     --  Dictionary containing search criteria and value
                                                     Acts as &#39;q&#39; field in solr query

                attr_list       (set)            --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in solr query

                op_params       (dictionary)     --  Other params and values for solr query
                                                        (Ex: start, rows)

            Returns:
                The solr url based on params

            Raises:
                SDKException:

                        if failed to form solr query
        &#34;&#34;&#34;
        try:
            search_query = f&#39;q=&#39;
            simple_search = 0
            if select_dict:
                for key, value in select_dict.items():
                    if isinstance(key, tuple):
                        if isinstance(value, list):
                            search_query += f&#39;({key[0]}:{str(value[0])}&#39;
                            for val in value[1:]:
                                search_query += f&#39; OR {key[0]}:{str(val)}&#39;
                        else:
                            search_query += f&#39;({key[0]}:{value}&#39;
                        for key_val in key[1:]:
                            if isinstance(value, list):
                                search_query += f&#39; OR {key_val}:{str(value[0])}&#39;
                                for val in value[1:]:
                                    search_query += f&#39; OR {key_val}:{str(val)}&#39;
                            else:
                                search_query += f&#39; OR {key_val}:{value}&#39;
                        search_query += &#39;) AND &#39;
                    elif isinstance(value, list):
                        search_query += f&#39;({key}:{str(value[0])}&#39;
                        for val in value[1:]:
                            search_query += f&#39; OR {key}:{str(val)}&#39;
                        search_query += &#34;) AND &#34;
                    elif key == &#34;keyword&#34;:
                        search_query += &#34;(&#34; + value + &#34;)&#34;
                        simple_search = 1
                        break
                    else:
                        search_query = search_query + f&#39;{key}:{str(value)} AND &#39;
                if simple_search == 0:
                    search_query = search_query[:-5]
            else:
                search_query += &#34;*:*&#34;

            field_query = &#34;&#34;
            if attr_list:
                field_query = &#34;&amp;fl=&#34;
                for item in attr_list:
                    field_query += f&#39;{str(item)},&#39;
                field_query = field_query[:-1]
            if attr_list and &#39;content&#39; in attr_list:
                field_query = f&#34;{field_query}&amp;exclude=false&#34;

            ex_query = &#34;&#34;
            if not op_params:
                op_params = {&#39;wt&#39;: &#34;json&#34;}
            else:
                op_params[&#39;wt&#39;] = &#34;json&#34;
            for key, values in op_params.items():
                if isinstance(values, list):
                    for value in values:
                        ex_query += self.__form_field_query(key, value)
                else:
                    ex_query += self.__form_field_query(key, values)
            final_url = f&#39;{search_query}{field_query}{ex_query}&#39;
            return final_url
        except Exception as excp:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, f&#34;Something went wrong while creating solr query - {excp}&#34;)

    def execute_solr_query(
            self,
            core_name,
            solr_client=None,
            select_dict=None,
            attr_list=None,
            op_params=None):
        &#34;&#34;&#34;Creates solr url based on input and executes it on solr on given core/collection
            Args:

                core_name               (str)           --  Core name/collection name where we want to query

                solr_client             (str)           --  Index Server client name to execute solr query
                                                                Default : None (picks first client on index server)

                select_dict             (dictionary)    --  Dictionary containing search criteria and
                                                            value. Acts as &#39;q&#39; field in solr query

                        Example :

                            1. General Criteria to filter results              -   {&#34;jid&#34;: 1024, &#34;datatype&#34;: 2,clid: 2}
                            2. Keyword Searches on solr                        -   {&#39;keyword&#39;: &#39;SearchKeyword&#39;}
                            3. For multiple value searches on single field     -   {&#39;cvowner&#39;: [&#39;xxx&#39;,&#39;yyy&#39;]}
                            4. For single value searches on multiple fields    -   {(&#39;cvowner&#39;,&#39;cvreaddisp&#39;) : &#39;xxx&#39;}

                attr_list               (set)           --  Column names to be returned in results.
                                                                Acts as &#39;fl&#39; in solr query

                        Example (For Exchange Mailbox IDA, below fields are there in solr) :
                                    {
                                     &#39;msgclass&#39;,
                                     &#39;ccsmtp&#39;,
                                     &#39;fmsmtp&#39;,
                                     &#39;folder&#39;
                                   }

                op_params               (dictionary)    --  Other params and values for solr query. Do not
                                                            mention &#39;wt&#39; param as it is always json

                                                            Example : {&#34;rows&#34;: 0}

            Returns:
                content of the response

            Raises:
                SDKException:

                        if unable to send request

                        if response is not success
        &#34;&#34;&#34;
        solr_url = f&#34;solr/{core_name}/select?{self._create_solr_query(select_dict, attr_list, op_params)}&#34;
        if solr_client is None:
            solr_url = f&#34;{self.server_url[0]}/{solr_url}&#34;
        else:
            if solr_client not in self.client_name:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;client name not found in this index server&#39;)
            server_url = self.server_url[self.client_name.index(solr_client)]
            solr_url = f&#34;{server_url}/{solr_url}&#34;
        flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, solr_url)
        if flag and response.json():
            return response.json()
        elif response.status_code == httplib.FORBIDDEN:
            cmd = f&#34;(Invoke-WebRequest -UseBasicParsing -uri \&#34;{solr_url}\&#34;).content&#34;
            client_obj = None
            if solr_client:
                client_obj = self._commcell_obj.clients.get(solr_client)
            else:
                # if no client is passed, then take first client in index server cloud
                client_obj = self._commcell_obj.clients.get(self.client_name[0])
            exit_code, output, error_message = client_obj.execute_script(script_type=&#34;PowerShell&#34;,
                                                                         script=cmd)
            if exit_code != 0:
                raise SDKException(
                    &#39;IndexServers&#39;,
                    &#39;104&#39;,
                    f&#34;Something went wrong while querying solr - {exit_code}&#34;)
            elif error_message:
                raise SDKException(
                    &#39;IndexServers&#39;,
                    &#39;104&#39;,
                    f&#34;Something went wrong while querying solr - {error_message}&#34;)
            try:
                return json.loads(output.strip())
            except Exception:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, f&#34;Something went wrong while querying solr - {output}&#34;)
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Something went wrong while querying solr&#34;)

    def get_index_node(self, node_name):
        &#34;&#34;&#34;Returns an Index server node object for given node name
            Args:
                node_name           (str)   --  Index server node name

            Returns:
                IndexNode class object

            Raises:
                SDKException:

                        if node not found for the given node name

        &#34;&#34;&#34;
        node_name = node_name.lower()
        if node_name in self.client_name:
            return IndexNode(self._commcell_obj, self.engine_name, node_name)
        raise SDKException(&#34;IndexServers&#34;, &#39;104&#39;, &#39;Index server node not found&#39;)

    def get_plan_info(self):
        &#34;&#34;&#34;Gets the plan information of the index server
            Returns:
                dict - containing the plan information
        &#34;&#34;&#34;
        client = self._commcell_obj.clients.get(self.engine_name)
        instance_props = client.properties.get(&#34;pseudoClientInfo&#34;, {}).get(&#34;distributedClusterInstanceProperties&#34;, {})
        plan_details = instance_props.get(&#34;clusterConfig&#34;,{}).get(&#34;cloudInfo&#34;, {}).get(&#34;planInfo&#34;, {})
        return plan_details

    def get_os_info(self):
        &#34;&#34;&#34;Returns the OS type for the Index server&#34;&#34;&#34;

        nodes_name = self.client_name
        nodes = [self._commcell_obj.clients.get(node) for node in nodes_name]
        nodes_os_info = [node.os_info for node in nodes]
        if IndexServerOSType.WINDOWS.value.lower() in nodes_os_info[0].lower():
            for node in nodes_os_info[1:]:
                if IndexServerOSType.UNIX.value.lower() in node.lower():
                    return IndexServerOSType.MIXED.value
            return IndexServerOSType.WINDOWS.value
        else:
            for node in nodes_os_info[1:]:
                if IndexServerOSType.WINDOWS.value.lower() in node.lower():
                    return IndexServerOSType.MIXED.value
            return IndexServerOSType.UNIX.value

    def __form_field_query(self, key, value):
        &#34;&#34;&#34;
        Returns the query with the key and value passed
        Args:
                key(str)    -- key for forming the query
                value(str)  -- value for forming the query
            Returns:
                query to be executed against solr
        &#34;&#34;&#34;
        query = None
        if value is None:
            query = f&#39;&amp;{key}&#39;
        else:
            query = f&#39;&amp;{key}={str(value)}&#39;
        return query

    @property
    def plan_name(self):
        &#34;&#34;&#34;Returns the plan name associated with index server
            Returns:
                str - name of the plan
        &#34;&#34;&#34;
        return self.plan_info.get(&#34;planName&#34;)

    @property
    def os_info(self):
        &#34;&#34;&#34;Returns the OS type for the Index server&#34;&#34;&#34;
        return self.os_type

    @property
    def is_cloud(self):
        &#34;&#34;&#34;Returns true if the Index server is cloud and false if not&#34;&#34;&#34;
        return self.server_type == 5

    @property
    def nodes_count(self):
        &#34;&#34;&#34;Returns the count of Index server nodes&#34;&#34;&#34;
        return len(self.client_id)

    @property
    def roles_data(self):
        &#34;&#34;&#34;Returns the cloud roles data&#34;&#34;&#34;
        return self._roles_obj.roles_data

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the index server properties&#34;&#34;&#34;
        return self._properties

    @property
    def host_name(self):
        &#34;&#34;&#34;Returns a list of host names of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.HOST_NAME]

    @property
    def cloud_name(self):
        &#34;&#34;&#34;Returns the internal cloud name of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CLOUD_NAME]

    @property
    def client_name(self):
        &#34;&#34;&#34;Returns a list of client names of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CLIENT_NAME]

    @property
    def server_url(self):
        &#34;&#34;&#34;Returns a list of Solr url of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CI_SERVER_URL]

    @property
    def type(self):
        &#34;&#34;&#34;Returns the type of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.TYPE]

    @property
    def base_port(self):
        &#34;&#34;&#34;Returns a list of base ports of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.BASE_PORT]

    @property
    def client_id(self):
        &#34;&#34;&#34;Returns a list client ids of all index server nodes&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CLIENT_ID]

    @property
    def roles(self):
        &#34;&#34;&#34;Returns a list of roles of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.ROLES]

    @property
    def role_display_name(self):
        &#34;&#34;&#34;Returns the roles display name of index server&#34;&#34;&#34;
        role_disp_name = []
        for role_version in self.roles:
            for role in self.roles_data:
                if role_version == role[&#39;roleVersion&#39;]:
                    role_disp_name.append(role[&#39;roleName&#39;])
                    break
        return role_disp_name

    @property
    def cloud_id(self):
        &#34;&#34;&#34;Returns the cloud id of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.CLOUD_ID]

    @property
    def server_type(self):
        &#34;&#34;&#34;Returns the server type of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.SERVER_TYPE]

    @property
    def engine_name(self):
        &#34;&#34;&#34;Returns the engine name of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.ENGINE_NAME]

    @property
    def index_server_client_id(self):
        &#34;&#34;&#34;Returns the index server client id of index server&#34;&#34;&#34;
        return self._properties[IndexServerConstants.INDEX_SERVER_CLIENT_ID]

    @property
    def fs_collection(self):
        &#34;&#34;&#34;Returns the multinode collection name of File System Index

            Returns:

                str --  File System index multinode collection name

        &#34;&#34;&#34;
        return f&#39;fsindex_{&#34;&#34;.join(letter for letter in self.cloud_name if letter.isalnum())}_multinode&#39;</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.index_server.IndexServer.base_port"><code class="name">var <span class="ident">base_port</span></code></dt>
<dd>
<div class="desc"><p>Returns a list of base ports of all index server nodes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1270-L1273" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def base_port(self):
    &#34;&#34;&#34;Returns a list of base ports of all index server nodes&#34;&#34;&#34;
    return self._properties[IndexServerConstants.BASE_PORT]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.client_id"><code class="name">var <span class="ident">client_id</span></code></dt>
<dd>
<div class="desc"><p>Returns a list client ids of all index server nodes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1275-L1278" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_id(self):
    &#34;&#34;&#34;Returns a list client ids of all index server nodes&#34;&#34;&#34;
    return self._properties[IndexServerConstants.CLIENT_ID]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.client_name"><code class="name">var <span class="ident">client_name</span></code></dt>
<dd>
<div class="desc"><p>Returns a list of client names of all index server nodes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1255-L1258" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_name(self):
    &#34;&#34;&#34;Returns a list of client names of all index server nodes&#34;&#34;&#34;
    return self._properties[IndexServerConstants.CLIENT_NAME]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.cloud_id"><code class="name">var <span class="ident">cloud_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the cloud id of index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1296-L1299" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cloud_id(self):
    &#34;&#34;&#34;Returns the cloud id of index server&#34;&#34;&#34;
    return self._properties[IndexServerConstants.CLOUD_ID]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.cloud_name"><code class="name">var <span class="ident">cloud_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the internal cloud name of index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1250-L1253" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cloud_name(self):
    &#34;&#34;&#34;Returns the internal cloud name of index server&#34;&#34;&#34;
    return self._properties[IndexServerConstants.CLOUD_NAME]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.engine_name"><code class="name">var <span class="ident">engine_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the engine name of index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1306-L1309" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def engine_name(self):
    &#34;&#34;&#34;Returns the engine name of index server&#34;&#34;&#34;
    return self._properties[IndexServerConstants.ENGINE_NAME]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.fs_collection"><code class="name">var <span class="ident">fs_collection</span></code></dt>
<dd>
<div class="desc"><p>Returns the multinode collection name of File System Index</p>
<h2 id="returns">Returns</h2>
<p>str &ndash;
File System index multinode collection name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1316-L1325" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def fs_collection(self):
    &#34;&#34;&#34;Returns the multinode collection name of File System Index

        Returns:

            str --  File System index multinode collection name

    &#34;&#34;&#34;
    return f&#39;fsindex_{&#34;&#34;.join(letter for letter in self.cloud_name if letter.isalnum())}_multinode&#39;</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.host_name"><code class="name">var <span class="ident">host_name</span></code></dt>
<dd>
<div class="desc"><p>Returns a list of host names of all index server nodes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1245-L1248" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def host_name(self):
    &#34;&#34;&#34;Returns a list of host names of all index server nodes&#34;&#34;&#34;
    return self._properties[IndexServerConstants.HOST_NAME]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.index_server_client_id"><code class="name">var <span class="ident">index_server_client_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the index server client id of index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1311-L1314" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_server_client_id(self):
    &#34;&#34;&#34;Returns the index server client id of index server&#34;&#34;&#34;
    return self._properties[IndexServerConstants.INDEX_SERVER_CLIENT_ID]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.is_cloud"><code class="name">var <span class="ident">is_cloud</span></code></dt>
<dd>
<div class="desc"><p>Returns true if the Index server is cloud and false if not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1225-L1228" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_cloud(self):
    &#34;&#34;&#34;Returns true if the Index server is cloud and false if not&#34;&#34;&#34;
    return self.server_type == 5</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.nodes_count"><code class="name">var <span class="ident">nodes_count</span></code></dt>
<dd>
<div class="desc"><p>Returns the count of Index server nodes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1230-L1233" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def nodes_count(self):
    &#34;&#34;&#34;Returns the count of Index server nodes&#34;&#34;&#34;
    return len(self.client_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.os_info"><code class="name">var <span class="ident">os_info</span></code></dt>
<dd>
<div class="desc"><p>Returns the OS type for the Index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1220-L1223" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def os_info(self):
    &#34;&#34;&#34;Returns the OS type for the Index server&#34;&#34;&#34;
    return self.os_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.plan_name"><code class="name">var <span class="ident">plan_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the plan name associated with index server</p>
<h2 id="returns">Returns</h2>
<p>str - name of the plan</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1212-L1218" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def plan_name(self):
    &#34;&#34;&#34;Returns the plan name associated with index server
        Returns:
            str - name of the plan
    &#34;&#34;&#34;
    return self.plan_info.get(&#34;planName&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.properties"><code class="name">var <span class="ident">properties</span></code></dt>
<dd>
<div class="desc"><p>Returns the index server properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1240-L1243" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def properties(self):
    &#34;&#34;&#34;Returns the index server properties&#34;&#34;&#34;
    return self._properties</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.role_display_name"><code class="name">var <span class="ident">role_display_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the roles display name of index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1285-L1294" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def role_display_name(self):
    &#34;&#34;&#34;Returns the roles display name of index server&#34;&#34;&#34;
    role_disp_name = []
    for role_version in self.roles:
        for role in self.roles_data:
            if role_version == role[&#39;roleVersion&#39;]:
                role_disp_name.append(role[&#39;roleName&#39;])
                break
    return role_disp_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.roles"><code class="name">var <span class="ident">roles</span></code></dt>
<dd>
<div class="desc"><p>Returns a list of roles of index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1280-L1283" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def roles(self):
    &#34;&#34;&#34;Returns a list of roles of index server&#34;&#34;&#34;
    return self._properties[IndexServerConstants.ROLES]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.roles_data"><code class="name">var <span class="ident">roles_data</span></code></dt>
<dd>
<div class="desc"><p>Returns the cloud roles data</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1235-L1238" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def roles_data(self):
    &#34;&#34;&#34;Returns the cloud roles data&#34;&#34;&#34;
    return self._roles_obj.roles_data</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.server_type"><code class="name">var <span class="ident">server_type</span></code></dt>
<dd>
<div class="desc"><p>Returns the server type of index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1301-L1304" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_type(self):
    &#34;&#34;&#34;Returns the server type of index server&#34;&#34;&#34;
    return self._properties[IndexServerConstants.SERVER_TYPE]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.server_url"><code class="name">var <span class="ident">server_url</span></code></dt>
<dd>
<div class="desc"><p>Returns a list of Solr url of all index server nodes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1260-L1263" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_url(self):
    &#34;&#34;&#34;Returns a list of Solr url of all index server nodes&#34;&#34;&#34;
    return self._properties[IndexServerConstants.CI_SERVER_URL]</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.type"><code class="name">var <span class="ident">type</span></code></dt>
<dd>
<div class="desc"><p>Returns the type of index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1265-L1268" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def type(self):
    &#34;&#34;&#34;Returns the type of index server&#34;&#34;&#34;
    return self._properties[IndexServerConstants.TYPE]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.index_server.IndexServer.change_plan"><code class="name flex">
<span>def <span class="ident">change_plan</span></span>(<span>self, plan_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies the plan used by an index server</p>
<h2 id="args">Args</h2>
<p>plan_name
(str)
&ndash;
Name of the plan to be used for the index server</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Response was not success.
Response was empty.
if plan with given name doesn't exist</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L749-L781" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def change_plan(self, plan_name):
    &#34;&#34;&#34;Modifies the plan used by an index server

        Args:
            plan_name      (str)       --  Name of the plan to be used for the index server
        Raises:
            SDKException:
                Response was not success.
                Response was empty.
                if plan with given name doesn&#39;t exist
    &#34;&#34;&#34;
    if not self._commcell_obj.plans.has_plan(plan_name):
        raise SDKException(
            &#39;Plan&#39;, &#39;102&#39;, f&#34;Plan with name [{plan_name}] doesn&#39;t exist&#34;)
    request_json = {
        &#34;opType&#34;: IndexServerConstants.OPERATION_EDIT,
        &#34;type&#34;: 1,
        &#34;planInfo&#34;: {
            &#34;planId&#34;: int(self._commcell_obj.plans.get(plan_name).plan_id)
        },
        &#34;cloudInfoEntity&#34;: {
            &#34;cloudId&#34;: self.cloud_id
        }
    }
    flag, response = self._cvpysdk_object.make_request(
        &#34;POST&#34;, self._services[&#39;CLOUD_MODIFY&#39;], request_json)
    if flag:
        if response.json():
            if &#39;cloudId&#39; in response.json():
                self.refresh()
                return
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.delete_docs_from_core"><code class="name flex">
<span>def <span class="ident">delete_docs_from_core</span></span>(<span>self, core_name, select_dict=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the docs from the given core name on index server depending on the select dict passed</p>
<h2 id="args">Args</h2>
<pre><code>core_name               (str)  --  name of the solr core
select_dict             (dict) --  dict with query to delete specific documents
                            default query - "*:*" (Deletes all the docs)
</code></pre>
<p>Returns:
None</p>
<p>Raises:
SDKException:</p>
<pre><code>    if input data is not valid

    if index server is cloud, not implemented error

    if response is empty

    if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L823-L863" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_docs_from_core(self, core_name, select_dict = None):
    &#34;&#34;&#34;Deletes the docs from the given core name on index server depending on the select dict passed

            Args:

                    core_name               (str)  --  name of the solr core
                    select_dict             (dict) --  dict with query to delete specific documents
                                                default query - &#34;*:*&#34; (Deletes all the docs)

                Returns:
                    None

                Raises:
                    SDKException:

                        if input data is not valid

                        if index server is cloud, not implemented error

                        if response is empty

                        if response is not success
    &#34;&#34;&#34;
    if not isinstance(core_name, str):
        raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
    if self.is_cloud:
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Not implemented for solr cloud&#34;)
    json_req = {&#34;delete&#34;: {&#34;query&#34;: self._create_solr_query(select_dict).replace(&#34;q=&#34;, &#34;&#34;).replace(&#34;&amp;wt=json&#34;, &#34;&#34;)}}
    baseurl = f&#34;{self.server_url[0]}/solr/{core_name}/update?commitWithin=1000&amp;overwrite=true&amp;wt=json&#34;
    flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, baseurl, json_req)
    if flag and response.json():
        if &#39;error&#39; in response.json():
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, f&#39; Failed with error message - &#39;
                                                      f&#39;{response.json().get(&#34;error&#34;).get(&#34;msg&#34;)}&#39;)
        if &#39;responseHeader&#39; in response.json():
            commitstatus = str(response.json().get(&#34;responseHeader&#34;).get(&#34;status&#34;))
            if int(commitstatus) != 0:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;,
                                   f&#34;Deleting docs from the core returned bad status - {commitstatus}&#34;)
            return
    raise SDKException(&#39;IndexServers&#39;, &#39;111&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.execute_solr_query"><code class="name flex">
<span>def <span class="ident">execute_solr_query</span></span>(<span>self, core_name, solr_client=None, select_dict=None, attr_list=None, op_params=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates solr url based on input and executes it on solr on given core/collection</p>
<h2 id="args">Args</h2>
<p>core_name
(str)
&ndash;
Core name/collection name where we want to query</p>
<p>solr_client
(str)
&ndash;
Index Server client name to execute solr query
Default : None (picks first client on index server)</p>
<p>select_dict
(dictionary)
&ndash;
Dictionary containing search criteria and
value. Acts as 'q' field in solr query</p>
<pre><code>    Example :

        1. General Criteria to filter results              -   {"jid": 1024, "datatype": 2,clid: 2}
        2. Keyword Searches on solr                        -   {'keyword': 'SearchKeyword'}
        3. For multiple value searches on single field     -   {'cvowner': ['xxx','yyy']}
        4. For single value searches on multiple fields    -   {('cvowner','cvreaddisp') : 'xxx'}
</code></pre>
<p>attr_list
(set)
&ndash;
Column names to be returned in results.
Acts as 'fl' in solr query</p>
<pre><code>    Example (For Exchange Mailbox IDA, below fields are there in solr) :
                {
                 'msgclass',
                 'ccsmtp',
                 'fmsmtp',
                 'folder'
               }
</code></pre>
<p>op_params
(dictionary)
&ndash;
Other params and values for solr query. Do not
mention 'wt' param as it is always json</p>
<pre><code>                                        Example : {"rows": 0}
</code></pre>
<h2 id="returns">Returns</h2>
<p>content of the response</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if unable to send request

    if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1062-L1148" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def execute_solr_query(
        self,
        core_name,
        solr_client=None,
        select_dict=None,
        attr_list=None,
        op_params=None):
    &#34;&#34;&#34;Creates solr url based on input and executes it on solr on given core/collection
        Args:

            core_name               (str)           --  Core name/collection name where we want to query

            solr_client             (str)           --  Index Server client name to execute solr query
                                                            Default : None (picks first client on index server)

            select_dict             (dictionary)    --  Dictionary containing search criteria and
                                                        value. Acts as &#39;q&#39; field in solr query

                    Example :

                        1. General Criteria to filter results              -   {&#34;jid&#34;: 1024, &#34;datatype&#34;: 2,clid: 2}
                        2. Keyword Searches on solr                        -   {&#39;keyword&#39;: &#39;SearchKeyword&#39;}
                        3. For multiple value searches on single field     -   {&#39;cvowner&#39;: [&#39;xxx&#39;,&#39;yyy&#39;]}
                        4. For single value searches on multiple fields    -   {(&#39;cvowner&#39;,&#39;cvreaddisp&#39;) : &#39;xxx&#39;}

            attr_list               (set)           --  Column names to be returned in results.
                                                            Acts as &#39;fl&#39; in solr query

                    Example (For Exchange Mailbox IDA, below fields are there in solr) :
                                {
                                 &#39;msgclass&#39;,
                                 &#39;ccsmtp&#39;,
                                 &#39;fmsmtp&#39;,
                                 &#39;folder&#39;
                               }

            op_params               (dictionary)    --  Other params and values for solr query. Do not
                                                        mention &#39;wt&#39; param as it is always json

                                                        Example : {&#34;rows&#34;: 0}

        Returns:
            content of the response

        Raises:
            SDKException:

                    if unable to send request

                    if response is not success
    &#34;&#34;&#34;
    solr_url = f&#34;solr/{core_name}/select?{self._create_solr_query(select_dict, attr_list, op_params)}&#34;
    if solr_client is None:
        solr_url = f&#34;{self.server_url[0]}/{solr_url}&#34;
    else:
        if solr_client not in self.client_name:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;client name not found in this index server&#39;)
        server_url = self.server_url[self.client_name.index(solr_client)]
        solr_url = f&#34;{server_url}/{solr_url}&#34;
    flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, solr_url)
    if flag and response.json():
        return response.json()
    elif response.status_code == httplib.FORBIDDEN:
        cmd = f&#34;(Invoke-WebRequest -UseBasicParsing -uri \&#34;{solr_url}\&#34;).content&#34;
        client_obj = None
        if solr_client:
            client_obj = self._commcell_obj.clients.get(solr_client)
        else:
            # if no client is passed, then take first client in index server cloud
            client_obj = self._commcell_obj.clients.get(self.client_name[0])
        exit_code, output, error_message = client_obj.execute_script(script_type=&#34;PowerShell&#34;,
                                                                     script=cmd)
        if exit_code != 0:
            raise SDKException(
                &#39;IndexServers&#39;,
                &#39;104&#39;,
                f&#34;Something went wrong while querying solr - {exit_code}&#34;)
        elif error_message:
            raise SDKException(
                &#39;IndexServers&#39;,
                &#39;104&#39;,
                f&#34;Something went wrong while querying solr - {error_message}&#34;)
        try:
            return json.loads(output.strip())
        except Exception:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, f&#34;Something went wrong while querying solr - {output}&#34;)
    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Something went wrong while querying solr&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.get_all_cores"><code class="name flex">
<span>def <span class="ident">get_all_cores</span></span>(<span>self, client_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>gets all cores &amp; core details from index server</p>
<h2 id="args">Args</h2>
<p>client_name
(str)
&ndash;
name of the client node
<strong><em>Applicable only for solr cloud mode or multi node Index Server</em></strong></p>
<h2 id="returns">Returns</h2>
<p>(list,dict)
&ndash; list containing core names
&ndash; dict containing details about cores</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if input data is not valid

if client name is not passed for index server cloud

if response is not success

if response is empty
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L938-L979" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_all_cores(self, client_name=None):
    &#34;&#34;&#34;gets all cores &amp; core details from index server

            Args:
                client_name     (str)       --  name of the client node
                    ***Applicable only for solr cloud mode or multi node Index Server***

            Returns:
                (list,dict)     -- list containing core names
                                -- dict containing details about cores

            Raises:

                SDKException:

                    if input data is not valid

                    if client name is not passed for index server cloud

                    if response is not success

                    if response is empty

    &#34;&#34;&#34;
    server_url = self.server_url[0]
    if self.is_cloud or len(self.client_name) &gt; 1:
        if client_name is None:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;Client name param missing for solr cloud&#39;)
        if client_name not in self.client_name:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;client name not found in this index server cloud&#39;)
        server_url = self.server_url[self.client_name.index(client_name)]
    core_names = []
    baseurl = f&#34;{server_url}/solr/admin/cores&#34;
    flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, baseurl)
    if flag and response.json():
        if &#39;error&#39; in response.json():
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Unable to get core names from index server&#34;)
        if &#39;status&#39; in response.json():
            for core in response.json()[&#39;status&#39;]:
                core_names.append(core)
            return core_names, response.json()[&#39;status&#39;]
    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Something went wrong while getting core names&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.get_health_indicators"><code class="name flex">
<span>def <span class="ident">get_health_indicators</span></span>(<span>self, client_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Get health indicators for index server node by client name</p>
<h2 id="args">Args</h2>
<p>client_name
(str)
&ndash;
name of the client node</p>
<h2 id="returns">Returns</h2>
<p>(response(str)) &ndash; str json object</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if input data is not valid
if client name is not passed for index server cloud
if response is not success
if response is empty</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L903-L936" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_health_indicators(self, client_name=None):
    &#34;&#34;&#34;Get health indicators for index server node by client name

            Args:
                client_name     (str)       --  name of the client node

            Returns:
                (response(str)) -- str json object

            Raises:

                SDKException:
                    if input data is not valid
                    if client name is not passed for index server cloud
                    if response is not success
                    if response is empty

    &#34;&#34;&#34;
    server_url = self.server_url[0]
    response = None
    if self.is_cloud or len(self.client_name) &gt; 1:
        if client_name is None:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;Client name param missing for solr cloud&#39;)
        if client_name not in self.client_name:
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;client name not found in this index server cloud&#39;)
        server_url = self.server_url[self.client_name.index(client_name)]
    baseurl = f&#34;{server_url}/solr/rest/admin/healthsummary&#34;
    headers = {
        &#39;Accept&#39;: &#39;application/xml&#39;
    }
    flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, headers=headers, url=baseurl)
    if flag:
        return response
    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Could not get health summary for [{}]&#34;.format(client_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.get_index_node"><code class="name flex">
<span>def <span class="ident">get_index_node</span></span>(<span>self, node_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns an Index server node object for given node name</p>
<h2 id="args">Args</h2>
<p>node_name
(str)
&ndash;
Index server node name</p>
<h2 id="returns">Returns</h2>
<p>IndexNode class object</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if node not found for the given node name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1150-L1167" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_index_node(self, node_name):
    &#34;&#34;&#34;Returns an Index server node object for given node name
        Args:
            node_name           (str)   --  Index server node name

        Returns:
            IndexNode class object

        Raises:
            SDKException:

                    if node not found for the given node name

    &#34;&#34;&#34;
    node_name = node_name.lower()
    if node_name in self.client_name:
        return IndexNode(self._commcell_obj, self.engine_name, node_name)
    raise SDKException(&#34;IndexServers&#34;, &#39;104&#39;, &#39;Index server node not found&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.get_os_info"><code class="name flex">
<span>def <span class="ident">get_os_info</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the OS type for the Index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1179-L1194" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_os_info(self):
    &#34;&#34;&#34;Returns the OS type for the Index server&#34;&#34;&#34;

    nodes_name = self.client_name
    nodes = [self._commcell_obj.clients.get(node) for node in nodes_name]
    nodes_os_info = [node.os_info for node in nodes]
    if IndexServerOSType.WINDOWS.value.lower() in nodes_os_info[0].lower():
        for node in nodes_os_info[1:]:
            if IndexServerOSType.UNIX.value.lower() in node.lower():
                return IndexServerOSType.MIXED.value
        return IndexServerOSType.WINDOWS.value
    else:
        for node in nodes_os_info[1:]:
            if IndexServerOSType.WINDOWS.value.lower() in node.lower():
                return IndexServerOSType.MIXED.value
        return IndexServerOSType.UNIX.value</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.get_plan_info"><code class="name flex">
<span>def <span class="ident">get_plan_info</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the plan information of the index server</p>
<h2 id="returns">Returns</h2>
<p>dict - containing the plan information</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L1169-L1177" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_plan_info(self):
    &#34;&#34;&#34;Gets the plan information of the index server
        Returns:
            dict - containing the plan information
    &#34;&#34;&#34;
    client = self._commcell_obj.clients.get(self.engine_name)
    instance_props = client.properties.get(&#34;pseudoClientInfo&#34;, {}).get(&#34;distributedClusterInstanceProperties&#34;, {})
    plan_details = instance_props.get(&#34;clusterConfig&#34;,{}).get(&#34;cloudInfo&#34;, {}).get(&#34;planInfo&#34;, {})
    return plan_details</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.hard_commit"><code class="name flex">
<span>def <span class="ident">hard_commit</span></span>(<span>self, core_name)</span>
</code></dt>
<dd>
<div class="desc"><p>do hard commit for the given core name on index server</p>
<h2 id="args">Args</h2>
<p>core_name
(str)
&ndash;
name of the solr core</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if input data is not valid

if index server is cloud, not implemented error

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L865-L900" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def hard_commit(self, core_name):
    &#34;&#34;&#34;do hard commit for the given core name on index server

                Args:

                    core_name               (str)  --  name of the solr core

                Returns:
                    None

                Raises:
                    SDKException:

                        if input data is not valid

                        if index server is cloud, not implemented error

                        if response is empty

                        if response is not success
    &#34;&#34;&#34;
    if not isinstance(core_name, str):
        raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
    if self.is_cloud:
        raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Not implemented for solr cloud&#34;)
    baseurl = f&#34;{self.server_url[0]}/solr/{core_name}/update?commit=true&#34;
    flag, response = self._cvpysdk_object.make_request(&#34;GET&#34;, baseurl)
    if flag and response.json():
        if &#39;error&#39; in response.json():
            raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Hard commit returned error&#34;)
        if &#39;responseHeader&#39; in response.json():
            commitstatus = str(response.json()[&#39;responseHeader&#39;][&#39;status&#39;])
            if int(commitstatus) != 0:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Hard commit returned bad status&#34;)
            return
    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#34;Something went wrong with hard commit&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.modify"><code class="name flex">
<span>def <span class="ident">modify</span></span>(<span>self, index_location, node_name, node_params)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies the properties of an index server</p>
<h2 id="args">Args</h2>
<p>index_location
(str)
&ndash;
index server data directory
node_name
(str)
&ndash;
index server node name
node_params
(dict)
&ndash;
parameters to be passed
[
{
"name" : <property_name>,
"value" : <property_value>
}
]</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Response was not success.
Response was empty.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L704-L747" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify(self, index_location, node_name, node_params):
    &#34;&#34;&#34;Modifies the properties of an index server

        Args:
            index_location      (str)       --  index server data directory
            node_name           (str)       --  index server node name
            node_params         (dict)      --  parameters to be passed
                                                [
                                                    {
                                                        &#34;name&#34; : &lt;property_name&gt;,
                                                        &#34;value&#34; : &lt;property_value&gt;
                                                    }
                                                ]
        Raises:
            SDKException:
                Response was not success.
                Response was empty.
    &#34;&#34;&#34;
    json_req = deepcopy(IndexServerConstants.REQUEST_JSON)
    json_req[&#39;opType&#39;] = IndexServerConstants.OPERATION_EDIT
    json_req[&#39;cloudNodes&#39;] = [{
        &#34;opType&#34;: IndexServerConstants.OPERATION_EDIT,
        &#34;nodeClientEntity&#34;: {
            &#34;clientId&#34;: int(self._commcell_obj.clients.get(node_name).client_id)
        },
        &#34;nodeMetaInfos&#34;: [
            {
                &#34;name&#34;: &#34;INDEXLOCATION&#34;,
                &#34;value&#34;: index_location
            }
        ]
    }]
    json_req[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = self.cloud_id
    for param in node_params:
        json_req[&#39;cloudNodes&#39;][0][&#39;nodeMetaInfos&#39;].append(param)
    flag, response = self._cvpysdk_object.make_request(
        &#34;POST&#34;, self._services[&#39;CLOUD_MODIFY&#39;], json_req)
    if flag:
        if response.json():
            if &#39;cloudId&#39; in response.json():
                self.refresh()
                return
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the index server properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L689-L698" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the index server properties&#34;&#34;&#34;
    self._commcell_obj.index_servers.refresh()
    self._get_properties()
    if self.os_type is None:
        self.os_type = self.get_os_info()
    if not self._roles_obj:
        self._roles_obj = _Roles(self._commcell_obj)
    if self.plan_info is None:
        self.plan_info = self.get_plan_info()</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.update_role"><code class="name flex">
<span>def <span class="ident">update_role</span></span>(<span>self, props=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates a role of an Index Server</p>
<h2 id="args">Args</h2>
<p>props
(list)
&ndash;
array of dictionaries
consisting details of the roles such as role name
and operation type.
[{
"roleName": <name>
(str)
"operationType": 1 or 2
(int)
1 for adding a role
2 for removing a role
}]</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L783-L821" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_role(self, props=None):
    &#34;&#34;&#34;Updates a role of an Index Server

        Args:
            props               (list)  --  array of dictionaries
            consisting details of the roles such as role name
            and operation type.
                                        [{
                                            &#34;roleName&#34;: &lt;name&gt;          (str)
                                            &#34;operationType&#34;: 1 or 2     (int)
                                                1 for adding a role
                                                2 for removing a role
                                        }]

        Raises:
            SDKException:
                if response is empty

                if response is not success

    &#34;&#34;&#34;
    json_req = {&#34;cloudId&#34;: self.cloud_id, &#34;roles&#34;: []}
    if props:
        for prop in props:
            role_id = self._roles_obj.get_role_id(prop[&#39;roleName&#39;])
            if not role_id:
                raise SDKException(&#39;IndexServers&#39;, &#39;103&#39;)
            prop[&#39;roleId&#39;] = role_id
            json_req[&#39;roles&#39;].append(prop)
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;CLOUD_ROLE_UPDATE&#39;], json_req
    )
    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            if response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
                return
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServer.update_roles_data"><code class="name flex">
<span>def <span class="ident">update_roles_data</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Synchronize the cloud roles data with the commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L700-L702" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_roles_data(self):
    &#34;&#34;&#34;Synchronize the cloud roles data with the commcell&#34;&#34;&#34;
    self._roles_obj.update_roles_data()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.index_server.IndexServerOSType"><code class="flex name class">
<span>class <span class="ident">IndexServerOSType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Enum class for Index Server OS Type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L633-L637" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class IndexServerOSType(enum.Enum):
    &#34;&#34;&#34;Enum class for Index Server OS Type&#34;&#34;&#34;
    WINDOWS = &#34;Windows&#34;
    UNIX = &#34;Unix&#34;
    MIXED = &#34;Mixed&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.index_server.IndexServerOSType.MIXED"><code class="name">var <span class="ident">MIXED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.index_server.IndexServerOSType.UNIX"><code class="name">var <span class="ident">UNIX</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.index_server.IndexServerOSType.WINDOWS"><code class="name">var <span class="ident">WINDOWS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.index_server.IndexServers"><code class="flex name class">
<span>class <span class="ident">IndexServers</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the index servers associated with the commcell.</p>
<p>Initialize object of the IndexServers class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the IndexServers class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L218-L630" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class IndexServers(object):
    &#34;&#34;&#34;Class for representing all the index servers associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the IndexServers class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the IndexServers class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._all_index_servers = None
        self._roles_obj = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all index servers of the commcell.

                Returns:
                    str - string of all the index servers with different roles associated
                    with the commcell
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;IS Name&#39;)
        index = 1
        for index_server in self._all_index_servers:
            representation_string += &#39;{:^5}\t{:^20}\n&#39;.format(
                index, index_server[&#39;engineName&#39;])
            index += 1
        return representation_string

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the IndexServers class.&#34;&#34;&#34;
        return &#34;IndexServers class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the index servers associated with the commcell&#34;&#34;&#34;
        return len(self._all_index_servers)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

                Raises:
                    SDKException:
                        Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def _get_index_servers(self):
        &#34;&#34;&#34;Method to retrieve all the index server available on commcell.

            Raises:
                SDKException:
                    Failed to get the list of analytics engines

                    Response was not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[&#39;GET_ALL_INDEX_SERVERS&#39;])
        if flag:
            if response.json() and &#39;listOfCIServer&#39; in response.json():
                for item in response.json()[&#39;listOfCIServer&#39;]:
                    if item[&#39;cloudID&#39;] in self._all_index_servers:
                        # Add only unique roles to list
                        if &#39;version&#39; in item and item[&#39;version&#39;] not in self._all_index_servers[item[&#39;cloudID&#39;]][&#39;version&#39;]:
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;version&#39;].append(item[&#39;version&#39;])
                        # check whether we have populated node details earlier. if not, add it to
                        # exisitng respective fields
                        if item[&#39;clientName&#39;] not in self._all_index_servers[item[&#39;cloudID&#39;]][&#39;clientName&#39;]:

                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;clientId&#39;].append(item[&#39;clientId&#39;])
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;clientName&#39;].append(item[&#39;clientName&#39;])
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;hostName&#39;].append(item[&#39;hostName&#39;])
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;cIServerURL&#39;].append(item[&#39;cIServerURL&#39;])
                            self._all_index_servers[item[&#39;cloudID&#39;]][&#39;basePort&#39;].append(item[&#39;basePort&#39;])

                    else:
                        item[&#39;version&#39;] = [item.get(&#39;version&#39;, &#39;&#39;)]
                        item[&#39;clientId&#39;] = [item[&#39;clientId&#39;]]
                        item[&#39;clientName&#39;] = [item[&#39;clientName&#39;]]
                        item[&#39;hostName&#39;] = [item[&#39;hostName&#39;]]
                        item[&#39;cIServerURL&#39;] = [item[&#39;cIServerURL&#39;]]
                        item[&#39;basePort&#39;] = [item[&#39;basePort&#39;]]
                        self._all_index_servers[item[&#39;cloudID&#39;]] = item
            else:
                self._all_index_servers = {}
        else:
            self._response_not_success(response)

    def _get_all_roles(self):
        &#34;&#34;&#34;Creates an instance of _Roles class and adds it to the IndexServer class&#34;&#34;&#34;
        self._roles_obj = _Roles(self._commcell_object)

    @property
    def all_index_servers(self):
        &#34;&#34;&#34;Returns the details of all the index server for associated commcell.

                Returns:
                    dict - dictionary consisting details of all the index servers
                    associated with commcell
                    Sample - {
                                &lt;cloud_id_1&gt;   :
                                    {
                                        &#34;engineName&#34; : &lt;property_value&gt;,
                                        &#34;internalCloudName&#34; : &lt;property_value&gt;,
                                        ...
                                    },
                                &lt;cloud_id_2&gt;   :
                                    {
                                        &#34;engineName&#34; : &lt;property_value&gt;,
                                        &#34;cloudID&#34; : &lt;property_value&gt;,
                                        ...
                                    }
                            }
        &#34;&#34;&#34;
        return self._all_index_servers

    @property
    def roles_data(self):
        &#34;&#34;&#34;Returns the details of all the cloud roles data

                Returns:
                    list - list of dictionary containing details of the cloud roles
        &#34;&#34;&#34;
        return self._roles_obj.roles_data

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of IndexServers class&#34;&#34;&#34;
        self._all_index_servers = {}
        self._get_index_servers()
        if not self._roles_obj:
            self._get_all_roles()

    def update_roles_data(self):
        &#34;&#34;&#34;Synchronises all the cloud roles details with the commcell&#34;&#34;&#34;
        self._roles_obj.update_roles_data()

    def get_properties(self, cloud_name):
        &#34;&#34;&#34;Returns all details of a index server with the cloud name

                Args:
                    cloud_name     (str)       --  cloud name of index server

                Returns:
                    dict        -   dict consisting details of the index server
        &#34;&#34;&#34;
        for index_server in self._all_index_servers:
            if self._all_index_servers[index_server][&#39;engineName&#39;] == cloud_name:
                return self._all_index_servers[index_server]
        raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)

    def has(self, cloud_name):
        &#34;&#34;&#34;Returns True if the index server with given name is present in commcell.

                Args:
                    cloud_name     (str)       --  the engine name of index server

                Returns:
                    boolean     -   True if index server with given name as is_name
                    is associated with the commcell else returns False

                Raises:
                    SDKExecption:
                        Data type of the input(s) is not valid
        &#34;&#34;&#34;
        if isinstance(cloud_name, str):
            for index_server in self._all_index_servers:
                if self._all_index_servers[index_server][&#34;engineName&#34;].lower() == cloud_name.lower():
                    return True
            return False
        raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)

    def get(self, cloud_data):
        &#34;&#34;&#34;Returns IndexServer object if a index server is found.

                Args:
                    cloud_data        (int/str)       --    cloud name or
                                                            cloud ID of index server

                Returns:
                    object            (IndexServer)   --  Instance on index server with
                    the engine name or cloud id as item

                Raises:
                    SDKException:
                        Index Server not found.

                        Data type of the input(s) is not valid.
        &#34;&#34;&#34;
        if isinstance(cloud_data, int):
            if cloud_data in self._all_index_servers:
                return IndexServer(
                    self._commcell_object,
                    self._all_index_servers[cloud_data][&#39;engineName&#39;],
                    cloud_data)
            SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
        elif isinstance(cloud_data, str):
            name = cloud_data.lower()
            for itter in self._all_index_servers:
                if self._all_index_servers[itter][&#39;engineName&#39;].lower(
                ) == name:
                    return IndexServer(
                        self._commcell_object,
                        self._all_index_servers[itter][&#39;engineName&#39;],
                        self._all_index_servers[itter][&#39;cloudID&#39;])
            raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
        raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)

    def create(
            self,
            index_server_name,
            index_server_node_names,
            index_directory,
            index_server_roles,
            index_pool_name=None,
            is_cloud=False,
            cloud_param=None):
        &#34;&#34;&#34;Creates an index server within the commcell

                Args:
                    index_server_node_names         (list)  --  client names for index server node
                    index_server_name               (str)   --  name for the index server
                    index_directory                 (list)  --  list of index locations for the index server
                                                                nodes respectively
                                                    For example:
                                                            [&lt;path_1&gt;] - same index location for all the nodes
                                                            [&lt;path_1&gt;, &lt;path_2&gt;, &lt;path_3&gt;] - different index
                                                    location for index server with 3 nodes
                    index_server_roles              (list)  --  list of role names to be assigned
                    index_pool_name                 (str)   --  name for the index pool to used by cloud index server
                    cloud_param                     (list)  --  list of custom parameters to be parsed
                                                    into the json for index server meta info
                                                    [
                                                        {
                                                            &#34;name&#34;: &lt;name&gt;,
                                                            &#34;value&#34;: &lt;value&gt;
                                                        }
                                                    ]
                    is_cloud            (bool)  --  if true then creates a cloud mode index server

                Raises:
                    SDKException:
                        Data type of the input(s) is not valid.

                        Response was not success.

                        Response was empty.
        &#34;&#34;&#34;
        if not (isinstance(index_server_roles, list) and isinstance(index_server_node_names, list)
                and isinstance(index_server_name, str)):
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        if isinstance(index_directory, str):
            index_directory = index_directory.split(&#34;,&#34;)
        node_count = len(index_server_node_names)
        index_directories_count = len(index_directory)
        if index_directories_count != 1 and index_directories_count != node_count:
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        cloud_meta_infos = {
            &#39;REPLICATION&#39;: &#39;1&#39;,
            &#39;LANGUAGE&#39;: &#39;0&#39;
        }
        node_meta_infos = {
            &#39;PORTNO&#39;: IndexServerConstants.DEFAULT_SOLR_PORT,
            &#39;JVMMAXMEMORY&#39;: IndexServerConstants.DEFAULT_JVM_MAX_MEMORY
        }
        role_meta_infos = {}
        req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
        req_json[&#39;cloudInfoEntity&#39;] = {
            &#39;cloudName&#39;: index_server_name,
            &#39;cloudDisplayName&#39;: index_server_name
        }
        if is_cloud:
            index_pool_obj = self._commcell_object.index_pools[index_pool_name]
            req_json[&#39;type&#39;] = 5
            req_json[&#39;solrCloudInfo&#39;][&#39;cloudPoolInfo&#39;] = {
                &#39;cloudId&#39;: int(index_pool_obj[&#39;pool_id&#39;])
            }
            cloud_meta_infos[&#39;INDEXLOCATION&#39;] = index_directory[0]
        for node_name_index in range(len(index_server_node_names)):
            node_name = index_server_node_names[node_name_index]
            location_index = node_name_index - (node_name_index//index_directories_count)
            node_obj = self._commcell_object.clients[node_name]
            node_data = {
                &#34;opType&#34;: IndexServerConstants.OPERATION_ADD,
                &#34;nodeClientEntity&#34;: {
                    &#34;hostName&#34;: node_obj[&#39;hostname&#39;],
                    &#34;clientId&#34;: int(node_obj[&#39;id&#39;]),
                    &#34;clientName&#34;: node_name
                },
                &#39;nodeMetaInfos&#39;: [{
                    &#34;name&#34;: &#34;INDEXLOCATION&#34;,
                    &#34;value&#34;: index_directory[location_index]
                }]
            }
            for node_info in node_meta_infos:
                node_data[&#39;nodeMetaInfos&#39;].append({
                    &#39;name&#39;: node_info,
                    &#39;value&#39;: str(node_meta_infos[node_info])
                })
            req_json[&#39;cloudNodes&#39;].append(node_data)
        for role in index_server_roles:
            role_id = self._roles_obj.get_role_id(role)
            if not role_id:
                raise SDKException(&#39;IndexServers&#39;, &#39;103&#39;)
            role_data = {
                &#34;roleId&#34;: role_id,
                &#34;roleName&#34;: role,
                &#34;operationType&#34;: IndexServerConstants.OPERATION_ADD,
                &#39;roleMetaInfos&#39;: []
            }
            for role_info in role_meta_infos:
                role_data[&#39;roleMetaInfos&#39;].append({
                    &#39;name&#39;: role_info,
                    &#39;value&#39;: role_meta_infos[role_info]
                })
            req_json[&#39;solrCloudInfo&#39;][&#39;roles&#39;].append(role_data)
        if cloud_param:
            for param in cloud_param:
                if param[&#39;name&#39;] in cloud_meta_infos:
                    del cloud_meta_infos[param[&#39;name&#39;]]
                req_json[&#39;cloudMetaInfos&#39;].append(param)
        for cloud_info in cloud_meta_infos:
            req_json[&#39;cloudMetaInfos&#39;].append({
                &#39;name&#39;: cloud_info,
                &#39;value&#39;: cloud_meta_infos[cloud_info]
            })
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_CREATE&#39;], req_json)
        if flag:
            if response.json():
                error_code = response.json()[&#39;genericResp&#39;][&#39;errorCode&#39;]
                error_string = response.json()[&#39;genericResp&#39;][&#39;errorMessage&#39;]
                if error_code == 0:
                    self.refresh()
                    self._commcell_object.clients.refresh()
                    self._commcell_object.datacube.refresh_engine()
                else:
                    o_str = &#39;Failed to create Index Server. Error: &#34;{0}&#34;&#39;.format(
                        error_string)
                    raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)

    def delete(self, cloud_name):
        &#34;&#34;&#34;Deletes / removes an index server from the commcell

                Args:
                    cloud_name      (str)   --  cloud name of index server
                    to be removed from the commcell

                Raises:
                    SDKException:
                        Data type of the input(s) is not valid.

                        Response was not success.

                        Response was empty.
        &#34;&#34;&#34;
        if not isinstance(cloud_name, str):
            raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
        cloud_id = self.get(cloud_name).cloud_id
        req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
        req_json[&#34;opType&#34;] = IndexServerConstants.OPERATION_DELETE
        req_json[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = cloud_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CLOUD_DELETE&#39;], req_json
        )
        if flag:
            if response.json() and &#39;genericResp&#39; in response.json() \
                    and &#39;errorCode&#39; not in response.json()[&#39;genericResp&#39;]:
                self.refresh()
                self._commcell_object.clients.refresh()
                self._commcell_object.datacube.refresh_engine()
                return
            if response.json() and &#39;genericResp&#39; in response.json():
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, response.json()[&#39;genericResp&#39;].get(
                        &#39;errorMessage&#39;, &#39;&#39;))
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        self._response_not_success(response)

    def prune_orphan_datasources(self):
        &#34;&#34;&#34;Deletes all the orphan datasources
            Raises:
                SDKException:
                    if failed to prune the orphan datasources

                    If response is empty

                    if response is not success
        &#34;&#34;&#34;
        prune_datasource = self._services[&#39;PRUNE_DATASOURCE&#39;]
        request_json = IndexServerConstants.PRUNE_REQUEST_JSON
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, prune_datasource, request_json)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;Failed to prune orphan datasources&#39;)
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.index_server.IndexServers.all_index_servers"><code class="name">var <span class="ident">all_index_servers</span></code></dt>
<dd>
<div class="desc"><p>Returns the details of all the index server for associated commcell.</p>
<h2 id="returns">Returns</h2>
<p>dict - dictionary consisting details of all the index servers
associated with commcell
Sample - {
<cloud_id_1>
:
{
"engineName" : <property_value>,
"internalCloudName" : <property_value>,
&hellip;
},
<cloud_id_2>
:
{
"engineName" : <property_value>,
"cloudID" : <property_value>,
&hellip;
}
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L319-L341" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_index_servers(self):
    &#34;&#34;&#34;Returns the details of all the index server for associated commcell.

            Returns:
                dict - dictionary consisting details of all the index servers
                associated with commcell
                Sample - {
                            &lt;cloud_id_1&gt;   :
                                {
                                    &#34;engineName&#34; : &lt;property_value&gt;,
                                    &#34;internalCloudName&#34; : &lt;property_value&gt;,
                                    ...
                                },
                            &lt;cloud_id_2&gt;   :
                                {
                                    &#34;engineName&#34; : &lt;property_value&gt;,
                                    &#34;cloudID&#34; : &lt;property_value&gt;,
                                    ...
                                }
                        }
    &#34;&#34;&#34;
    return self._all_index_servers</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServers.roles_data"><code class="name">var <span class="ident">roles_data</span></code></dt>
<dd>
<div class="desc"><p>Returns the details of all the cloud roles data</p>
<h2 id="returns">Returns</h2>
<p>list - list of dictionary containing details of the cloud roles</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L343-L350" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def roles_data(self):
    &#34;&#34;&#34;Returns the details of all the cloud roles data

            Returns:
                list - list of dictionary containing details of the cloud roles
    &#34;&#34;&#34;
    return self._roles_obj.roles_data</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.index_server.IndexServers.create"><code class="name flex">
<span>def <span class="ident">create</span></span>(<span>self, index_server_name, index_server_node_names, index_directory, index_server_roles, index_pool_name=None, is_cloud=False, cloud_param=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates an index server within the commcell</p>
<h2 id="args">Args</h2>
<p>index_server_node_names
(list)
&ndash;
client names for index server node
index_server_name
(str)
&ndash;
name for the index server
index_directory
(list)
&ndash;
list of index locations for the index server
nodes respectively
For example:
[<path_1>] - same index location for all the nodes
[<path_1>, <path_2>, <path_3>] - different index
location for index server with 3 nodes
index_server_roles
(list)
&ndash;
list of role names to be assigned
index_pool_name
(str)
&ndash;
name for the index pool to used by cloud index server
cloud_param
(list)
&ndash;
list of custom parameters to be parsed
into the json for index server meta info
[
{
"name": <name>,
"value": <value>
}
]
is_cloud
(bool)
&ndash;
if true then creates a cloud mode index server</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Data type of the input(s) is not valid.</p>
<pre><code>Response was not success.

Response was empty.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L434-L569" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create(
        self,
        index_server_name,
        index_server_node_names,
        index_directory,
        index_server_roles,
        index_pool_name=None,
        is_cloud=False,
        cloud_param=None):
    &#34;&#34;&#34;Creates an index server within the commcell

            Args:
                index_server_node_names         (list)  --  client names for index server node
                index_server_name               (str)   --  name for the index server
                index_directory                 (list)  --  list of index locations for the index server
                                                            nodes respectively
                                                For example:
                                                        [&lt;path_1&gt;] - same index location for all the nodes
                                                        [&lt;path_1&gt;, &lt;path_2&gt;, &lt;path_3&gt;] - different index
                                                location for index server with 3 nodes
                index_server_roles              (list)  --  list of role names to be assigned
                index_pool_name                 (str)   --  name for the index pool to used by cloud index server
                cloud_param                     (list)  --  list of custom parameters to be parsed
                                                into the json for index server meta info
                                                [
                                                    {
                                                        &#34;name&#34;: &lt;name&gt;,
                                                        &#34;value&#34;: &lt;value&gt;
                                                    }
                                                ]
                is_cloud            (bool)  --  if true then creates a cloud mode index server

            Raises:
                SDKException:
                    Data type of the input(s) is not valid.

                    Response was not success.

                    Response was empty.
    &#34;&#34;&#34;
    if not (isinstance(index_server_roles, list) and isinstance(index_server_node_names, list)
            and isinstance(index_server_name, str)):
        raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
    if isinstance(index_directory, str):
        index_directory = index_directory.split(&#34;,&#34;)
    node_count = len(index_server_node_names)
    index_directories_count = len(index_directory)
    if index_directories_count != 1 and index_directories_count != node_count:
        raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
    cloud_meta_infos = {
        &#39;REPLICATION&#39;: &#39;1&#39;,
        &#39;LANGUAGE&#39;: &#39;0&#39;
    }
    node_meta_infos = {
        &#39;PORTNO&#39;: IndexServerConstants.DEFAULT_SOLR_PORT,
        &#39;JVMMAXMEMORY&#39;: IndexServerConstants.DEFAULT_JVM_MAX_MEMORY
    }
    role_meta_infos = {}
    req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
    req_json[&#39;cloudInfoEntity&#39;] = {
        &#39;cloudName&#39;: index_server_name,
        &#39;cloudDisplayName&#39;: index_server_name
    }
    if is_cloud:
        index_pool_obj = self._commcell_object.index_pools[index_pool_name]
        req_json[&#39;type&#39;] = 5
        req_json[&#39;solrCloudInfo&#39;][&#39;cloudPoolInfo&#39;] = {
            &#39;cloudId&#39;: int(index_pool_obj[&#39;pool_id&#39;])
        }
        cloud_meta_infos[&#39;INDEXLOCATION&#39;] = index_directory[0]
    for node_name_index in range(len(index_server_node_names)):
        node_name = index_server_node_names[node_name_index]
        location_index = node_name_index - (node_name_index//index_directories_count)
        node_obj = self._commcell_object.clients[node_name]
        node_data = {
            &#34;opType&#34;: IndexServerConstants.OPERATION_ADD,
            &#34;nodeClientEntity&#34;: {
                &#34;hostName&#34;: node_obj[&#39;hostname&#39;],
                &#34;clientId&#34;: int(node_obj[&#39;id&#39;]),
                &#34;clientName&#34;: node_name
            },
            &#39;nodeMetaInfos&#39;: [{
                &#34;name&#34;: &#34;INDEXLOCATION&#34;,
                &#34;value&#34;: index_directory[location_index]
            }]
        }
        for node_info in node_meta_infos:
            node_data[&#39;nodeMetaInfos&#39;].append({
                &#39;name&#39;: node_info,
                &#39;value&#39;: str(node_meta_infos[node_info])
            })
        req_json[&#39;cloudNodes&#39;].append(node_data)
    for role in index_server_roles:
        role_id = self._roles_obj.get_role_id(role)
        if not role_id:
            raise SDKException(&#39;IndexServers&#39;, &#39;103&#39;)
        role_data = {
            &#34;roleId&#34;: role_id,
            &#34;roleName&#34;: role,
            &#34;operationType&#34;: IndexServerConstants.OPERATION_ADD,
            &#39;roleMetaInfos&#39;: []
        }
        for role_info in role_meta_infos:
            role_data[&#39;roleMetaInfos&#39;].append({
                &#39;name&#39;: role_info,
                &#39;value&#39;: role_meta_infos[role_info]
            })
        req_json[&#39;solrCloudInfo&#39;][&#39;roles&#39;].append(role_data)
    if cloud_param:
        for param in cloud_param:
            if param[&#39;name&#39;] in cloud_meta_infos:
                del cloud_meta_infos[param[&#39;name&#39;]]
            req_json[&#39;cloudMetaInfos&#39;].append(param)
    for cloud_info in cloud_meta_infos:
        req_json[&#39;cloudMetaInfos&#39;].append({
            &#39;name&#39;: cloud_info,
            &#39;value&#39;: cloud_meta_infos[cloud_info]
        })
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;CLOUD_CREATE&#39;], req_json)
    if flag:
        if response.json():
            error_code = response.json()[&#39;genericResp&#39;][&#39;errorCode&#39;]
            error_string = response.json()[&#39;genericResp&#39;][&#39;errorMessage&#39;]
            if error_code == 0:
                self.refresh()
                self._commcell_object.clients.refresh()
                self._commcell_object.datacube.refresh_engine()
            else:
                o_str = &#39;Failed to create Index Server. Error: &#34;{0}&#34;&#39;.format(
                    error_string)
                raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServers.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, cloud_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes / removes an index server from the commcell</p>
<h2 id="args">Args</h2>
<p>cloud_name
(str)
&ndash;
cloud name of index server
to be removed from the commcell</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Data type of the input(s) is not valid.</p>
<pre><code>Response was not success.

Response was empty.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L571-L607" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, cloud_name):
    &#34;&#34;&#34;Deletes / removes an index server from the commcell

            Args:
                cloud_name      (str)   --  cloud name of index server
                to be removed from the commcell

            Raises:
                SDKException:
                    Data type of the input(s) is not valid.

                    Response was not success.

                    Response was empty.
    &#34;&#34;&#34;
    if not isinstance(cloud_name, str):
        raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)
    cloud_id = self.get(cloud_name).cloud_id
    req_json = deepcopy(IndexServerConstants.REQUEST_JSON)
    req_json[&#34;opType&#34;] = IndexServerConstants.OPERATION_DELETE
    req_json[&#39;cloudInfoEntity&#39;][&#39;cloudId&#39;] = cloud_id
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;CLOUD_DELETE&#39;], req_json
    )
    if flag:
        if response.json() and &#39;genericResp&#39; in response.json() \
                and &#39;errorCode&#39; not in response.json()[&#39;genericResp&#39;]:
            self.refresh()
            self._commcell_object.clients.refresh()
            self._commcell_object.datacube.refresh_engine()
            return
        if response.json() and &#39;genericResp&#39; in response.json():
            raise SDKException(
                &#39;Response&#39;, &#39;102&#39;, response.json()[&#39;genericResp&#39;].get(
                    &#39;errorMessage&#39;, &#39;&#39;))
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServers.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, cloud_data)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns IndexServer object if a index server is found.</p>
<h2 id="args">Args</h2>
<p>cloud_data
(int/str)
&ndash;
cloud name or
cloud ID of index server</p>
<h2 id="returns">Returns</h2>
<p>object
(IndexServer)
&ndash;
Instance on index server with
the engine name or cloud id as item</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Index Server not found.</p>
<pre><code>Data type of the input(s) is not valid.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L398-L432" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, cloud_data):
    &#34;&#34;&#34;Returns IndexServer object if a index server is found.

            Args:
                cloud_data        (int/str)       --    cloud name or
                                                        cloud ID of index server

            Returns:
                object            (IndexServer)   --  Instance on index server with
                the engine name or cloud id as item

            Raises:
                SDKException:
                    Index Server not found.

                    Data type of the input(s) is not valid.
    &#34;&#34;&#34;
    if isinstance(cloud_data, int):
        if cloud_data in self._all_index_servers:
            return IndexServer(
                self._commcell_object,
                self._all_index_servers[cloud_data][&#39;engineName&#39;],
                cloud_data)
        SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
    elif isinstance(cloud_data, str):
        name = cloud_data.lower()
        for itter in self._all_index_servers:
            if self._all_index_servers[itter][&#39;engineName&#39;].lower(
            ) == name:
                return IndexServer(
                    self._commcell_object,
                    self._all_index_servers[itter][&#39;engineName&#39;],
                    self._all_index_servers[itter][&#39;cloudID&#39;])
        raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
    raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServers.get_properties"><code class="name flex">
<span>def <span class="ident">get_properties</span></span>(<span>self, cloud_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns all details of a index server with the cloud name</p>
<h2 id="args">Args</h2>
<p>cloud_name
(str)
&ndash;
cloud name of index server</p>
<h2 id="returns">Returns</h2>
<p>dict
-
dict consisting details of the index server</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L363-L375" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_properties(self, cloud_name):
    &#34;&#34;&#34;Returns all details of a index server with the cloud name

            Args:
                cloud_name     (str)       --  cloud name of index server

            Returns:
                dict        -   dict consisting details of the index server
    &#34;&#34;&#34;
    for index_server in self._all_index_servers:
        if self._all_index_servers[index_server][&#39;engineName&#39;] == cloud_name:
            return self._all_index_servers[index_server]
    raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServers.has"><code class="name flex">
<span>def <span class="ident">has</span></span>(<span>self, cloud_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns True if the index server with given name is present in commcell.</p>
<h2 id="args">Args</h2>
<p>cloud_name
(str)
&ndash;
the engine name of index server</p>
<h2 id="returns">Returns</h2>
<p>boolean
-
True if index server with given name as is_name
is associated with the commcell else returns False</p>
<h2 id="raises">Raises</h2>
<p>SDKExecption:
Data type of the input(s) is not valid</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L377-L396" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has(self, cloud_name):
    &#34;&#34;&#34;Returns True if the index server with given name is present in commcell.

            Args:
                cloud_name     (str)       --  the engine name of index server

            Returns:
                boolean     -   True if index server with given name as is_name
                is associated with the commcell else returns False

            Raises:
                SDKExecption:
                    Data type of the input(s) is not valid
    &#34;&#34;&#34;
    if isinstance(cloud_name, str):
        for index_server in self._all_index_servers:
            if self._all_index_servers[index_server][&#34;engineName&#34;].lower() == cloud_name.lower():
                return True
        return False
    raise SDKException(&#39;IndexServers&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServers.prune_orphan_datasources"><code class="name flex">
<span>def <span class="ident">prune_orphan_datasources</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes all the orphan datasources</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to prune the orphan datasources</p>
<pre><code>If response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L609-L630" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def prune_orphan_datasources(self):
    &#34;&#34;&#34;Deletes all the orphan datasources
        Raises:
            SDKException:
                if failed to prune the orphan datasources

                If response is empty

                if response is not success
    &#34;&#34;&#34;
    prune_datasource = self._services[&#39;PRUNE_DATASOURCE&#39;]
    request_json = IndexServerConstants.PRUNE_REQUEST_JSON
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, prune_datasource, request_json)
    if flag:
        if response.json():
            error_code = response.json().get(&#39;errorCode&#39;, 0)
            if error_code != 0:
                raise SDKException(&#39;IndexServers&#39;, &#39;104&#39;, &#39;Failed to prune orphan datasources&#39;)
            return
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServers.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of IndexServers class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L352-L357" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of IndexServers class&#34;&#34;&#34;
    self._all_index_servers = {}
    self._get_index_servers()
    if not self._roles_obj:
        self._get_all_roles()</code></pre>
</details>
</dd>
<dt id="cvpysdk.index_server.IndexServers.update_roles_data"><code class="name flex">
<span>def <span class="ident">update_roles_data</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Synchronises all the cloud roles details with the commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/index_server.py#L359-L361" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_roles_data(self):
    &#34;&#34;&#34;Synchronises all the cloud roles details with the commcell&#34;&#34;&#34;
    self._roles_obj.update_roles_data()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#indexservers">IndexServers</a><ul>
<li><a href="#indexservers-attributes">IndexServers Attributes</a></li>
</ul>
</li>
<li><a href="#indexserver">IndexServer</a><ul>
<li><a href="#indexserver-attributes">IndexServer Attributes</a></li>
</ul>
</li>
<li><a href="#indexnode">IndexNode</a><ul>
<li><a href="#indexnode-attributes">IndexNode Attributes</a></li>
</ul>
</li>
<li><a href="#_roles">_Roles</a><ul>
<li><a href="#_roles-attributes">_Roles Attributes</a></li>
</ul>
</li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.index_server.IndexNode" href="#cvpysdk.index_server.IndexNode">IndexNode</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.index_server.IndexNode.index_location" href="#cvpysdk.index_server.IndexNode.index_location">index_location</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexNode.jvm_memory" href="#cvpysdk.index_server.IndexNode.jvm_memory">jvm_memory</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexNode.node_id" href="#cvpysdk.index_server.IndexNode.node_id">node_id</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexNode.node_name" href="#cvpysdk.index_server.IndexNode.node_name">node_name</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexNode.refresh" href="#cvpysdk.index_server.IndexNode.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexNode.roles" href="#cvpysdk.index_server.IndexNode.roles">roles</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexNode.solr_port" href="#cvpysdk.index_server.IndexNode.solr_port">solr_port</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexNode.solr_url" href="#cvpysdk.index_server.IndexNode.solr_url">solr_url</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.index_server.IndexServer" href="#cvpysdk.index_server.IndexServer">IndexServer</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.index_server.IndexServer.base_port" href="#cvpysdk.index_server.IndexServer.base_port">base_port</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.change_plan" href="#cvpysdk.index_server.IndexServer.change_plan">change_plan</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.client_id" href="#cvpysdk.index_server.IndexServer.client_id">client_id</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.client_name" href="#cvpysdk.index_server.IndexServer.client_name">client_name</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.cloud_id" href="#cvpysdk.index_server.IndexServer.cloud_id">cloud_id</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.cloud_name" href="#cvpysdk.index_server.IndexServer.cloud_name">cloud_name</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.delete_docs_from_core" href="#cvpysdk.index_server.IndexServer.delete_docs_from_core">delete_docs_from_core</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.engine_name" href="#cvpysdk.index_server.IndexServer.engine_name">engine_name</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.execute_solr_query" href="#cvpysdk.index_server.IndexServer.execute_solr_query">execute_solr_query</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.fs_collection" href="#cvpysdk.index_server.IndexServer.fs_collection">fs_collection</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.get_all_cores" href="#cvpysdk.index_server.IndexServer.get_all_cores">get_all_cores</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.get_health_indicators" href="#cvpysdk.index_server.IndexServer.get_health_indicators">get_health_indicators</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.get_index_node" href="#cvpysdk.index_server.IndexServer.get_index_node">get_index_node</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.get_os_info" href="#cvpysdk.index_server.IndexServer.get_os_info">get_os_info</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.get_plan_info" href="#cvpysdk.index_server.IndexServer.get_plan_info">get_plan_info</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.hard_commit" href="#cvpysdk.index_server.IndexServer.hard_commit">hard_commit</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.host_name" href="#cvpysdk.index_server.IndexServer.host_name">host_name</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.index_server_client_id" href="#cvpysdk.index_server.IndexServer.index_server_client_id">index_server_client_id</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.is_cloud" href="#cvpysdk.index_server.IndexServer.is_cloud">is_cloud</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.modify" href="#cvpysdk.index_server.IndexServer.modify">modify</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.nodes_count" href="#cvpysdk.index_server.IndexServer.nodes_count">nodes_count</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.os_info" href="#cvpysdk.index_server.IndexServer.os_info">os_info</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.plan_name" href="#cvpysdk.index_server.IndexServer.plan_name">plan_name</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.properties" href="#cvpysdk.index_server.IndexServer.properties">properties</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.refresh" href="#cvpysdk.index_server.IndexServer.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.role_display_name" href="#cvpysdk.index_server.IndexServer.role_display_name">role_display_name</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.roles" href="#cvpysdk.index_server.IndexServer.roles">roles</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.roles_data" href="#cvpysdk.index_server.IndexServer.roles_data">roles_data</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.server_type" href="#cvpysdk.index_server.IndexServer.server_type">server_type</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.server_url" href="#cvpysdk.index_server.IndexServer.server_url">server_url</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.type" href="#cvpysdk.index_server.IndexServer.type">type</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.update_role" href="#cvpysdk.index_server.IndexServer.update_role">update_role</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServer.update_roles_data" href="#cvpysdk.index_server.IndexServer.update_roles_data">update_roles_data</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.index_server.IndexServerOSType" href="#cvpysdk.index_server.IndexServerOSType">IndexServerOSType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.index_server.IndexServerOSType.MIXED" href="#cvpysdk.index_server.IndexServerOSType.MIXED">MIXED</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServerOSType.UNIX" href="#cvpysdk.index_server.IndexServerOSType.UNIX">UNIX</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServerOSType.WINDOWS" href="#cvpysdk.index_server.IndexServerOSType.WINDOWS">WINDOWS</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.index_server.IndexServers" href="#cvpysdk.index_server.IndexServers">IndexServers</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.index_server.IndexServers.all_index_servers" href="#cvpysdk.index_server.IndexServers.all_index_servers">all_index_servers</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServers.create" href="#cvpysdk.index_server.IndexServers.create">create</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServers.delete" href="#cvpysdk.index_server.IndexServers.delete">delete</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServers.get" href="#cvpysdk.index_server.IndexServers.get">get</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServers.get_properties" href="#cvpysdk.index_server.IndexServers.get_properties">get_properties</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServers.has" href="#cvpysdk.index_server.IndexServers.has">has</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServers.prune_orphan_datasources" href="#cvpysdk.index_server.IndexServers.prune_orphan_datasources">prune_orphan_datasources</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServers.refresh" href="#cvpysdk.index_server.IndexServers.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServers.roles_data" href="#cvpysdk.index_server.IndexServers.roles_data">roles_data</a></code></li>
<li><code><a title="cvpysdk.index_server.IndexServers.update_roles_data" href="#cvpysdk.index_server.IndexServers.update_roles_data">update_roles_data</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>