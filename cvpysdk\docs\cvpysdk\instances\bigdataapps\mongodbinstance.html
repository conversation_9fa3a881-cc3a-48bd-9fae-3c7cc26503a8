<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.bigdataapps.mongodbinstance API documentation</title>
<meta name="description" content="File for operating on a MongoDB instance.
MongoDBInstance :
Derived class from BigDataAppsInstance Base class, representing a
…" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.bigdataapps.mongodbinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a MongoDB instance.
MongoDBInstance :
Derived class from BigDataAppsInstance Base class, representing a
MongoDBInstance instance and to perform operations on that instance</p>
<h2 id="mongodbinstance">Mongodbinstance</h2>
<p><strong>init</strong>()
&ndash;
Initializes MongoDB instance object with associated
agent_object, instance name and instance id
restore()
&ndash; Submits a restore request based on restore options
restore_collection()
&ndash; collection based restore.</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/bigdataapps/mongodbinstance.py#L1-L223" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------
&#34;&#34;&#34; File for operating on a MongoDB instance.
MongoDBInstance :   Derived class from BigDataAppsInstance Base class, representing a
                        MongoDBInstance instance and to perform operations on that instance
MongoDBInstance:
    __init__()                      --  Initializes MongoDB instance object with associated
    agent_object, instance name and instance id
    restore()                       -- Submits a restore request based on restore options
    restore_collection()            -- collection based restore.

&#34;&#34;&#34;
from __future__ import unicode_literals
from ..bigdataappsinstance import BigDataAppsInstance
from ...exception import SDKException


class MongoDBInstance(BigDataAppsInstance):
    &#34;&#34;&#34;
    Class for representing an Instance of the MongoDB  instance
    &#34;&#34;&#34;
    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the Mongo DB class
            Args:
                agent_object    (object)  --  instance of the Agent class
                instance_name   (str)     --  name of the instance
                instance_id     (str)     --  id of the instance
                    default: None
            Returns:
                object - instance of the Instance class
        &#34;&#34;&#34;
        self._agent_object = agent_object
        self._browse_request = {}
        self._browse_url = None
        super(
            MongoDBInstance,
            self).__init__(
                agent_object,
                instance_name,
                instance_id)

    def restore(self, restore_options):
        &#34;&#34;&#34;
            Restores the content of this instance content
            Args:
                restore_options : dict of keyword arguments needed to submit a MongoDB restore:
                    Example:
                       restore_options = {
                        restore_dict = {}
                            restore_dict[&#34;no_of_streams&#34;] = 2
                            restore_dict[&#34;multinode_restore&#34;] = True
                            restore_dict[&#34;destination_instance&#34;] = self.client_name
                            restore_dict[&#34;destination_instance_id&#34;] = self._instance_object.instance_id
                            restore_dict[&#34;paths&#34;] = [&#34;/&#34;]
                            restore_dict[&#34;mongodb_restore&#34;] = True
                            restore_dict[&#34;destination_client_id&#34;] = self._client_obj.client_id
                            restore_dict[&#34;destination_client_name&#34;] = self._client_obj.client_name
                            restore_dict[&#34;overwrite&#34;] = True
                            restore_dict[&#34;client_type&#34;] = 29
                            restore_dict[&#34;destination_appTypeId&#34;] = 64
                            restore_dict[&#34;backupset_name&#34;] = self.backupsetname
                            restore_dict[&#34;_type_&#34;] = 5
                            restore_dict[&#34;subclient_id&#34;] = -1
                            restore_dict[&#34;source_shard_name&#34;] = self.replicaset
                            restore_dict[&#34;destination_shard_name&#34;] = self.replicaset
                            restore_dict[&#34;hostname&#34;] = self.primary_host
                            restore_dict[&#34;clientName&#34;] = self.master_node
                            restore_dict[&#34;desthostName&#34;] = self.primary_host
                            restore_dict[&#34;destclientName&#34;] = self.master_node
                            restore_dict[&#34;destPortNumber&#34;] = self.port
                            restore_dict[&#34;destDataDir&#34;] = self.bin_path
                            restore_dict[&#34;bkpDataDir&#34;] = self.bkp_dir_path
                            restore_dict[&#34;backupPortNumber&#34;] = self.port
                            restore_dict[&#34;restoreDataDir&#34;] = self.bkp_dir_path
                            restore_dict[&#34;primaryPort&#34;] = self.port
                        }
            Returns:
                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        if not (isinstance(restore_options, dict)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        request_json = self._restore_json(restore_option=restore_options)

        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;subclientId&#34;] = restore_options.get(
            &#34;subclient_id&#34;, -1)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = restore_options.get(
            &#34;backupset_name&#34;)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = restore_options.get(
            &#34;_type_&#34;)

        distributed_restore_json = {
            &#34;distributedRestore&#34;: True,
        }


        client_object_source = self._commcell_object.clients.get(restore_options[&#39;clientName&#39;])
        client_object_destination = self._commcell_object.clients.get(restore_options[&#39;destclientName&#39;])
        distributed_restore_json[&#34;mongoDBRestoreOptions&#34;] = {
                &#34;destShardList&#34;: [
                    {
                        &#34;srcShardName&#34;:  restore_options.get(&#34;source_shard_name&#34;, False),
                        &#34;destShardName&#34;: restore_options.get(&#34;destination_shard_name&#34;, False),
                        &#34;target&#34;: {
                            &#34;hostName&#34;: restore_options.get(&#34;hostname&#34;, False),
                            &#34;clientName&#34;: restore_options.get(&#34;clientName&#34;, False),
                            &#34;clientId&#34;: int(client_object_source.client_id)
                        },
                        &#34;destHostName&#34;: restore_options.get(&#34;desthostName&#34;, False),
                        &#34;destPortNumber&#34;: restore_options.get(&#34;destPortNumber&#34;, False),
                        &#34;destDataDir&#34;:  restore_options.get(&#34;restoreDataDir&#34;, False),
                        &#34;bkpSecondary&#34;: {
                            &#34;clientName&#34;: restore_options.get(&#34;clientName&#34;, False),
                            &#34;hostName&#34;: restore_options.get(&#34;hostname&#34;, False),
                            &#34;clientId&#34;: int(client_object_source.client_id)
                        },
                        &#34;bkpHostName&#34;: restore_options.get(&#34;hostname&#34;, False),
                        &#34;bkpPortNumber&#34;: restore_options.get(&#34;backupPortNumber&#34;, False),
                        &#34;bkpDataDir&#34;: restore_options.get(&#34;bkpDataDir&#34;, False),
                        &#34;useDestAsSecondary&#34;: False,
                        &#34;primaryPortNumber&#34;:restore_options.get(&#34;primaryPort&#34;, False),
                    }
                ],
                &#34;restoreFilesOnly&#34;: False,
                &#34;recover&#34;: True,
                &#34;pointInTimeToEndOfBackup&#34;: True,
                &#34;latestOpLogSync&#34;: True,
                &#34;latestEndOfBackup&#34;: True,
                &#34;isGranularRecovery&#34;: False,
                &#34;autoDBShutDown&#34;: True,
                &#34;isInplaceRestore&#34;: True
            }

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;distributedAppsRestoreOptions&#34;] = distributed_restore_json
        return self._process_restore_response(request_json)

    def restore_collection(self, restore_options):
        &#34;&#34;&#34;
            Restores the content of this instance content for collection restore inplace.
            Args:
                restore_options : dict of keyword arguments needed to submit a MongoDB restore:
                    Example:
                       restore_options = {
                        restore_dict = {}
                            restore_dict[&#34;no_of_streams&#34;] = 2
                            restore_dict[&#34;multinode_restore&#34;] = True
                            restore_dict[&#34;destination_instance&#34;] = self.client_name
                            restore_dict[&#34;destination_instance_id&#34;] = self._instance_object.instance_id
                            restore_dict[&#34;paths&#34;] = [&#34;/&#34;]
                            restore_dict[&#34;mongodb_restore&#34;] = True
                            restore_dict[&#34;destination_client_id&#34;] = self._client_obj.client_id
                            restore_dict[&#34;destination_client_name&#34;] = self._client_obj.client_name
                            restore_dict[&#34;overwrite&#34;] = True
                            restore_dict[&#34;client_type&#34;] = 29
                            restore_dict[&#34;destination_appTypeId&#34;] = 64
                            restore_dict[&#34;backupset_name&#34;] = self.backupsetname
                            restore_dict[&#34;_type_&#34;] = 5
                            restore_dict[&#34;subclient_id&#34;] = subclientid
                            restore_dict[&#34;source_shard_name&#34;] = self.replicaset
                            restore_dict[&#34;destination_shard_name&#34;] = self.replicaset
                            restore_dict[&#34;hostname&#34;] = self.primary_host
                            restore_dict[&#34;clientName&#34;] = self.master_node
                            restore_dict[&#34;desthostName&#34;] = self.primary_host
                            restore_dict[&#34;destclientName&#34;] = self.master_node
                            restore_dict[&#34;destPortNumber&#34;] = self.port
                            restore_dict[&#34;destDataDir&#34;] = self.bin_path
                            restore_dict[&#34;bkpDataDir&#34;] = self.bkp_dir_path
                            restore_dict[&#34;backupPortNumber&#34;] = self.port
                            restore_dict[&#34;restoreDataDir&#34;] = self.bkp_dir_path
                            restore_dict[&#34;primaryPort&#34;] = self.port
                        }
            Returns:
                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        if not (isinstance(restore_options, dict)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        request_json = self._restore_json(restore_option=restore_options)

        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;subclientId&#34;] = restore_options.get(
            &#34;subclient_id&#34;, )
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = restore_options.get(
            &#34;backupset_name&#34;)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = restore_options.get(
            &#34;_type_&#34;)
        distributed_restore_json = {
            &#34;distributedRestore&#34;: True,
        }
        client_object_source = self._commcell_object.clients.get(restore_options[&#39;clientName&#39;])
        client_object_destination = self._commcell_object.clients.get(restore_options[&#39;destclientName&#39;])
        distributed_restore_json[&#34;mongoDBRestoreOptions&#34;] = {
                &#34;destShardList&#34;: [],
                &#34;destGranularEntityList&#34;: [
                    {
                        &#34;srcDbName&#34;: restore_options.get(&#34;source_db_name&#34;, False),
                        &#34;destDbName&#34;: restore_options.get(&#34;restore_db_name&#34;, False),
                        &#34;isDbEntity&#34;: True,
                        &#34;destCollectionName&#34;: &#34;&#34;
                    }
                ],
                &#34;restoreFilesOnly&#34;: False,
                &#34;recover&#34;: True,
                &#34;pointInTimeToEndOfBackup&#34;: True,
                &#34;latestOpLogSync&#34;: True,
                &#34;latestEndOfBackup&#34;: True,
                &#34;isGranularRecovery&#34;: True
            }
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;distributedAppsRestoreOptions&#34;] = distributed_restore_json
        return self._process_restore_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.bigdataapps.mongodbinstance.MongoDBInstance"><code class="flex name class">
<span>class <span class="ident">MongoDBInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of the MongoDB
instance</p>
<p>Initializes the object of the Mongo DB class</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class
instance_name
(str)
&ndash;
name of the instance
instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/bigdataapps/mongodbinstance.py#L32-L223" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class MongoDBInstance(BigDataAppsInstance):
    &#34;&#34;&#34;
    Class for representing an Instance of the MongoDB  instance
    &#34;&#34;&#34;
    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the Mongo DB class
            Args:
                agent_object    (object)  --  instance of the Agent class
                instance_name   (str)     --  name of the instance
                instance_id     (str)     --  id of the instance
                    default: None
            Returns:
                object - instance of the Instance class
        &#34;&#34;&#34;
        self._agent_object = agent_object
        self._browse_request = {}
        self._browse_url = None
        super(
            MongoDBInstance,
            self).__init__(
                agent_object,
                instance_name,
                instance_id)

    def restore(self, restore_options):
        &#34;&#34;&#34;
            Restores the content of this instance content
            Args:
                restore_options : dict of keyword arguments needed to submit a MongoDB restore:
                    Example:
                       restore_options = {
                        restore_dict = {}
                            restore_dict[&#34;no_of_streams&#34;] = 2
                            restore_dict[&#34;multinode_restore&#34;] = True
                            restore_dict[&#34;destination_instance&#34;] = self.client_name
                            restore_dict[&#34;destination_instance_id&#34;] = self._instance_object.instance_id
                            restore_dict[&#34;paths&#34;] = [&#34;/&#34;]
                            restore_dict[&#34;mongodb_restore&#34;] = True
                            restore_dict[&#34;destination_client_id&#34;] = self._client_obj.client_id
                            restore_dict[&#34;destination_client_name&#34;] = self._client_obj.client_name
                            restore_dict[&#34;overwrite&#34;] = True
                            restore_dict[&#34;client_type&#34;] = 29
                            restore_dict[&#34;destination_appTypeId&#34;] = 64
                            restore_dict[&#34;backupset_name&#34;] = self.backupsetname
                            restore_dict[&#34;_type_&#34;] = 5
                            restore_dict[&#34;subclient_id&#34;] = -1
                            restore_dict[&#34;source_shard_name&#34;] = self.replicaset
                            restore_dict[&#34;destination_shard_name&#34;] = self.replicaset
                            restore_dict[&#34;hostname&#34;] = self.primary_host
                            restore_dict[&#34;clientName&#34;] = self.master_node
                            restore_dict[&#34;desthostName&#34;] = self.primary_host
                            restore_dict[&#34;destclientName&#34;] = self.master_node
                            restore_dict[&#34;destPortNumber&#34;] = self.port
                            restore_dict[&#34;destDataDir&#34;] = self.bin_path
                            restore_dict[&#34;bkpDataDir&#34;] = self.bkp_dir_path
                            restore_dict[&#34;backupPortNumber&#34;] = self.port
                            restore_dict[&#34;restoreDataDir&#34;] = self.bkp_dir_path
                            restore_dict[&#34;primaryPort&#34;] = self.port
                        }
            Returns:
                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        if not (isinstance(restore_options, dict)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        request_json = self._restore_json(restore_option=restore_options)

        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;subclientId&#34;] = restore_options.get(
            &#34;subclient_id&#34;, -1)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = restore_options.get(
            &#34;backupset_name&#34;)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = restore_options.get(
            &#34;_type_&#34;)

        distributed_restore_json = {
            &#34;distributedRestore&#34;: True,
        }


        client_object_source = self._commcell_object.clients.get(restore_options[&#39;clientName&#39;])
        client_object_destination = self._commcell_object.clients.get(restore_options[&#39;destclientName&#39;])
        distributed_restore_json[&#34;mongoDBRestoreOptions&#34;] = {
                &#34;destShardList&#34;: [
                    {
                        &#34;srcShardName&#34;:  restore_options.get(&#34;source_shard_name&#34;, False),
                        &#34;destShardName&#34;: restore_options.get(&#34;destination_shard_name&#34;, False),
                        &#34;target&#34;: {
                            &#34;hostName&#34;: restore_options.get(&#34;hostname&#34;, False),
                            &#34;clientName&#34;: restore_options.get(&#34;clientName&#34;, False),
                            &#34;clientId&#34;: int(client_object_source.client_id)
                        },
                        &#34;destHostName&#34;: restore_options.get(&#34;desthostName&#34;, False),
                        &#34;destPortNumber&#34;: restore_options.get(&#34;destPortNumber&#34;, False),
                        &#34;destDataDir&#34;:  restore_options.get(&#34;restoreDataDir&#34;, False),
                        &#34;bkpSecondary&#34;: {
                            &#34;clientName&#34;: restore_options.get(&#34;clientName&#34;, False),
                            &#34;hostName&#34;: restore_options.get(&#34;hostname&#34;, False),
                            &#34;clientId&#34;: int(client_object_source.client_id)
                        },
                        &#34;bkpHostName&#34;: restore_options.get(&#34;hostname&#34;, False),
                        &#34;bkpPortNumber&#34;: restore_options.get(&#34;backupPortNumber&#34;, False),
                        &#34;bkpDataDir&#34;: restore_options.get(&#34;bkpDataDir&#34;, False),
                        &#34;useDestAsSecondary&#34;: False,
                        &#34;primaryPortNumber&#34;:restore_options.get(&#34;primaryPort&#34;, False),
                    }
                ],
                &#34;restoreFilesOnly&#34;: False,
                &#34;recover&#34;: True,
                &#34;pointInTimeToEndOfBackup&#34;: True,
                &#34;latestOpLogSync&#34;: True,
                &#34;latestEndOfBackup&#34;: True,
                &#34;isGranularRecovery&#34;: False,
                &#34;autoDBShutDown&#34;: True,
                &#34;isInplaceRestore&#34;: True
            }

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;distributedAppsRestoreOptions&#34;] = distributed_restore_json
        return self._process_restore_response(request_json)

    def restore_collection(self, restore_options):
        &#34;&#34;&#34;
            Restores the content of this instance content for collection restore inplace.
            Args:
                restore_options : dict of keyword arguments needed to submit a MongoDB restore:
                    Example:
                       restore_options = {
                        restore_dict = {}
                            restore_dict[&#34;no_of_streams&#34;] = 2
                            restore_dict[&#34;multinode_restore&#34;] = True
                            restore_dict[&#34;destination_instance&#34;] = self.client_name
                            restore_dict[&#34;destination_instance_id&#34;] = self._instance_object.instance_id
                            restore_dict[&#34;paths&#34;] = [&#34;/&#34;]
                            restore_dict[&#34;mongodb_restore&#34;] = True
                            restore_dict[&#34;destination_client_id&#34;] = self._client_obj.client_id
                            restore_dict[&#34;destination_client_name&#34;] = self._client_obj.client_name
                            restore_dict[&#34;overwrite&#34;] = True
                            restore_dict[&#34;client_type&#34;] = 29
                            restore_dict[&#34;destination_appTypeId&#34;] = 64
                            restore_dict[&#34;backupset_name&#34;] = self.backupsetname
                            restore_dict[&#34;_type_&#34;] = 5
                            restore_dict[&#34;subclient_id&#34;] = subclientid
                            restore_dict[&#34;source_shard_name&#34;] = self.replicaset
                            restore_dict[&#34;destination_shard_name&#34;] = self.replicaset
                            restore_dict[&#34;hostname&#34;] = self.primary_host
                            restore_dict[&#34;clientName&#34;] = self.master_node
                            restore_dict[&#34;desthostName&#34;] = self.primary_host
                            restore_dict[&#34;destclientName&#34;] = self.master_node
                            restore_dict[&#34;destPortNumber&#34;] = self.port
                            restore_dict[&#34;destDataDir&#34;] = self.bin_path
                            restore_dict[&#34;bkpDataDir&#34;] = self.bkp_dir_path
                            restore_dict[&#34;backupPortNumber&#34;] = self.port
                            restore_dict[&#34;restoreDataDir&#34;] = self.bkp_dir_path
                            restore_dict[&#34;primaryPort&#34;] = self.port
                        }
            Returns:
                object - instance of the Job class for this restore job
        &#34;&#34;&#34;
        if not (isinstance(restore_options, dict)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        request_json = self._restore_json(restore_option=restore_options)

        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;subclientId&#34;] = restore_options.get(
            &#34;subclient_id&#34;, )
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = restore_options.get(
            &#34;backupset_name&#34;)
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = restore_options.get(
            &#34;_type_&#34;)
        distributed_restore_json = {
            &#34;distributedRestore&#34;: True,
        }
        client_object_source = self._commcell_object.clients.get(restore_options[&#39;clientName&#39;])
        client_object_destination = self._commcell_object.clients.get(restore_options[&#39;destclientName&#39;])
        distributed_restore_json[&#34;mongoDBRestoreOptions&#34;] = {
                &#34;destShardList&#34;: [],
                &#34;destGranularEntityList&#34;: [
                    {
                        &#34;srcDbName&#34;: restore_options.get(&#34;source_db_name&#34;, False),
                        &#34;destDbName&#34;: restore_options.get(&#34;restore_db_name&#34;, False),
                        &#34;isDbEntity&#34;: True,
                        &#34;destCollectionName&#34;: &#34;&#34;
                    }
                ],
                &#34;restoreFilesOnly&#34;: False,
                &#34;recover&#34;: True,
                &#34;pointInTimeToEndOfBackup&#34;: True,
                &#34;latestOpLogSync&#34;: True,
                &#34;latestEndOfBackup&#34;: True,
                &#34;isGranularRecovery&#34;: True
            }
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
                &#34;distributedAppsRestoreOptions&#34;] = distributed_restore_json
        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance" href="../bigdataappsinstance.html#cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance">BigDataAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.bigdataapps.mongodbinstance.MongoDBInstance.restore"><code class="name flex">
<span>def <span class="ident">restore</span></span>(<span>self, restore_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the content of this instance content</p>
<h2 id="args">Args</h2>
<p>restore_options : dict of keyword arguments needed to submit a MongoDB restore:
Example:
restore_options = {
restore_dict = {}
restore_dict["no_of_streams"] = 2
restore_dict["multinode_restore"] = True
restore_dict["destination_instance"] = self.client_name
restore_dict["destination_instance_id"] = self.<em>instance_object.instance_id
restore_dict["paths"] = ["/"]
restore_dict["mongodb_restore"] = True
restore_dict["destination_client_id"] = self._client_obj.client_id
restore_dict["destination_client_name"] = self._client_obj.client_name
restore_dict["overwrite"] = True
restore_dict["client_type"] = 29
restore_dict["destination_appTypeId"] = 64
restore_dict["backupset_name"] = self.backupsetname
restore_dict["_type</em>"] = 5
restore_dict["subclient_id"] = -1
restore_dict["source_shard_name"] = self.replicaset
restore_dict["destination_shard_name"] = self.replicaset
restore_dict["hostname"] = self.primary_host
restore_dict["clientName"] = self.master_node
restore_dict["desthostName"] = self.primary_host
restore_dict["destclientName"] = self.master_node
restore_dict["destPortNumber"] = self.port
restore_dict["destDataDir"] = self.bin_path
restore_dict["bkpDataDir"] = self.bkp_dir_path
restore_dict["backupPortNumber"] = self.port
restore_dict["restoreDataDir"] = self.bkp_dir_path
restore_dict["primaryPort"] = self.port
}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/bigdataapps/mongodbinstance.py#L56-L149" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore(self, restore_options):
    &#34;&#34;&#34;
        Restores the content of this instance content
        Args:
            restore_options : dict of keyword arguments needed to submit a MongoDB restore:
                Example:
                   restore_options = {
                    restore_dict = {}
                        restore_dict[&#34;no_of_streams&#34;] = 2
                        restore_dict[&#34;multinode_restore&#34;] = True
                        restore_dict[&#34;destination_instance&#34;] = self.client_name
                        restore_dict[&#34;destination_instance_id&#34;] = self._instance_object.instance_id
                        restore_dict[&#34;paths&#34;] = [&#34;/&#34;]
                        restore_dict[&#34;mongodb_restore&#34;] = True
                        restore_dict[&#34;destination_client_id&#34;] = self._client_obj.client_id
                        restore_dict[&#34;destination_client_name&#34;] = self._client_obj.client_name
                        restore_dict[&#34;overwrite&#34;] = True
                        restore_dict[&#34;client_type&#34;] = 29
                        restore_dict[&#34;destination_appTypeId&#34;] = 64
                        restore_dict[&#34;backupset_name&#34;] = self.backupsetname
                        restore_dict[&#34;_type_&#34;] = 5
                        restore_dict[&#34;subclient_id&#34;] = -1
                        restore_dict[&#34;source_shard_name&#34;] = self.replicaset
                        restore_dict[&#34;destination_shard_name&#34;] = self.replicaset
                        restore_dict[&#34;hostname&#34;] = self.primary_host
                        restore_dict[&#34;clientName&#34;] = self.master_node
                        restore_dict[&#34;desthostName&#34;] = self.primary_host
                        restore_dict[&#34;destclientName&#34;] = self.master_node
                        restore_dict[&#34;destPortNumber&#34;] = self.port
                        restore_dict[&#34;destDataDir&#34;] = self.bin_path
                        restore_dict[&#34;bkpDataDir&#34;] = self.bkp_dir_path
                        restore_dict[&#34;backupPortNumber&#34;] = self.port
                        restore_dict[&#34;restoreDataDir&#34;] = self.bkp_dir_path
                        restore_dict[&#34;primaryPort&#34;] = self.port
                    }
        Returns:
            object - instance of the Job class for this restore job
    &#34;&#34;&#34;
    if not (isinstance(restore_options, dict)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
    request_json = self._restore_json(restore_option=restore_options)

    request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;subclientId&#34;] = restore_options.get(
        &#34;subclient_id&#34;, -1)
    request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = restore_options.get(
        &#34;backupset_name&#34;)
    request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = restore_options.get(
        &#34;_type_&#34;)

    distributed_restore_json = {
        &#34;distributedRestore&#34;: True,
    }


    client_object_source = self._commcell_object.clients.get(restore_options[&#39;clientName&#39;])
    client_object_destination = self._commcell_object.clients.get(restore_options[&#39;destclientName&#39;])
    distributed_restore_json[&#34;mongoDBRestoreOptions&#34;] = {
            &#34;destShardList&#34;: [
                {
                    &#34;srcShardName&#34;:  restore_options.get(&#34;source_shard_name&#34;, False),
                    &#34;destShardName&#34;: restore_options.get(&#34;destination_shard_name&#34;, False),
                    &#34;target&#34;: {
                        &#34;hostName&#34;: restore_options.get(&#34;hostname&#34;, False),
                        &#34;clientName&#34;: restore_options.get(&#34;clientName&#34;, False),
                        &#34;clientId&#34;: int(client_object_source.client_id)
                    },
                    &#34;destHostName&#34;: restore_options.get(&#34;desthostName&#34;, False),
                    &#34;destPortNumber&#34;: restore_options.get(&#34;destPortNumber&#34;, False),
                    &#34;destDataDir&#34;:  restore_options.get(&#34;restoreDataDir&#34;, False),
                    &#34;bkpSecondary&#34;: {
                        &#34;clientName&#34;: restore_options.get(&#34;clientName&#34;, False),
                        &#34;hostName&#34;: restore_options.get(&#34;hostname&#34;, False),
                        &#34;clientId&#34;: int(client_object_source.client_id)
                    },
                    &#34;bkpHostName&#34;: restore_options.get(&#34;hostname&#34;, False),
                    &#34;bkpPortNumber&#34;: restore_options.get(&#34;backupPortNumber&#34;, False),
                    &#34;bkpDataDir&#34;: restore_options.get(&#34;bkpDataDir&#34;, False),
                    &#34;useDestAsSecondary&#34;: False,
                    &#34;primaryPortNumber&#34;:restore_options.get(&#34;primaryPort&#34;, False),
                }
            ],
            &#34;restoreFilesOnly&#34;: False,
            &#34;recover&#34;: True,
            &#34;pointInTimeToEndOfBackup&#34;: True,
            &#34;latestOpLogSync&#34;: True,
            &#34;latestEndOfBackup&#34;: True,
            &#34;isGranularRecovery&#34;: False,
            &#34;autoDBShutDown&#34;: True,
            &#34;isInplaceRestore&#34;: True
        }

    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;distributedAppsRestoreOptions&#34;] = distributed_restore_json
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.bigdataapps.mongodbinstance.MongoDBInstance.restore_collection"><code class="name flex">
<span>def <span class="ident">restore_collection</span></span>(<span>self, restore_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the content of this instance content for collection restore inplace.</p>
<h2 id="args">Args</h2>
<p>restore_options : dict of keyword arguments needed to submit a MongoDB restore:
Example:
restore_options = {
restore_dict = {}
restore_dict["no_of_streams"] = 2
restore_dict["multinode_restore"] = True
restore_dict["destination_instance"] = self.client_name
restore_dict["destination_instance_id"] = self.<em>instance_object.instance_id
restore_dict["paths"] = ["/"]
restore_dict["mongodb_restore"] = True
restore_dict["destination_client_id"] = self._client_obj.client_id
restore_dict["destination_client_name"] = self._client_obj.client_name
restore_dict["overwrite"] = True
restore_dict["client_type"] = 29
restore_dict["destination_appTypeId"] = 64
restore_dict["backupset_name"] = self.backupsetname
restore_dict["_type</em>"] = 5
restore_dict["subclient_id"] = subclientid
restore_dict["source_shard_name"] = self.replicaset
restore_dict["destination_shard_name"] = self.replicaset
restore_dict["hostname"] = self.primary_host
restore_dict["clientName"] = self.master_node
restore_dict["desthostName"] = self.primary_host
restore_dict["destclientName"] = self.master_node
restore_dict["destPortNumber"] = self.port
restore_dict["destDataDir"] = self.bin_path
restore_dict["bkpDataDir"] = self.bkp_dir_path
restore_dict["backupPortNumber"] = self.port
restore_dict["restoreDataDir"] = self.bkp_dir_path
restore_dict["primaryPort"] = self.port
}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/bigdataapps/mongodbinstance.py#L151-L223" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_collection(self, restore_options):
    &#34;&#34;&#34;
        Restores the content of this instance content for collection restore inplace.
        Args:
            restore_options : dict of keyword arguments needed to submit a MongoDB restore:
                Example:
                   restore_options = {
                    restore_dict = {}
                        restore_dict[&#34;no_of_streams&#34;] = 2
                        restore_dict[&#34;multinode_restore&#34;] = True
                        restore_dict[&#34;destination_instance&#34;] = self.client_name
                        restore_dict[&#34;destination_instance_id&#34;] = self._instance_object.instance_id
                        restore_dict[&#34;paths&#34;] = [&#34;/&#34;]
                        restore_dict[&#34;mongodb_restore&#34;] = True
                        restore_dict[&#34;destination_client_id&#34;] = self._client_obj.client_id
                        restore_dict[&#34;destination_client_name&#34;] = self._client_obj.client_name
                        restore_dict[&#34;overwrite&#34;] = True
                        restore_dict[&#34;client_type&#34;] = 29
                        restore_dict[&#34;destination_appTypeId&#34;] = 64
                        restore_dict[&#34;backupset_name&#34;] = self.backupsetname
                        restore_dict[&#34;_type_&#34;] = 5
                        restore_dict[&#34;subclient_id&#34;] = subclientid
                        restore_dict[&#34;source_shard_name&#34;] = self.replicaset
                        restore_dict[&#34;destination_shard_name&#34;] = self.replicaset
                        restore_dict[&#34;hostname&#34;] = self.primary_host
                        restore_dict[&#34;clientName&#34;] = self.master_node
                        restore_dict[&#34;desthostName&#34;] = self.primary_host
                        restore_dict[&#34;destclientName&#34;] = self.master_node
                        restore_dict[&#34;destPortNumber&#34;] = self.port
                        restore_dict[&#34;destDataDir&#34;] = self.bin_path
                        restore_dict[&#34;bkpDataDir&#34;] = self.bkp_dir_path
                        restore_dict[&#34;backupPortNumber&#34;] = self.port
                        restore_dict[&#34;restoreDataDir&#34;] = self.bkp_dir_path
                        restore_dict[&#34;primaryPort&#34;] = self.port
                    }
        Returns:
            object - instance of the Job class for this restore job
    &#34;&#34;&#34;
    if not (isinstance(restore_options, dict)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
    request_json = self._restore_json(restore_option=restore_options)

    request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;subclientId&#34;] = restore_options.get(
        &#34;subclient_id&#34;, )
    request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = restore_options.get(
        &#34;backupset_name&#34;)
    request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = restore_options.get(
        &#34;_type_&#34;)
    distributed_restore_json = {
        &#34;distributedRestore&#34;: True,
    }
    client_object_source = self._commcell_object.clients.get(restore_options[&#39;clientName&#39;])
    client_object_destination = self._commcell_object.clients.get(restore_options[&#39;destclientName&#39;])
    distributed_restore_json[&#34;mongoDBRestoreOptions&#34;] = {
            &#34;destShardList&#34;: [],
            &#34;destGranularEntityList&#34;: [
                {
                    &#34;srcDbName&#34;: restore_options.get(&#34;source_db_name&#34;, False),
                    &#34;destDbName&#34;: restore_options.get(&#34;restore_db_name&#34;, False),
                    &#34;isDbEntity&#34;: True,
                    &#34;destCollectionName&#34;: &#34;&#34;
                }
            ],
            &#34;restoreFilesOnly&#34;: False,
            &#34;recover&#34;: True,
            &#34;pointInTimeToEndOfBackup&#34;: True,
            &#34;latestOpLogSync&#34;: True,
            &#34;latestEndOfBackup&#34;: True,
            &#34;isGranularRecovery&#34;: True
        }
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;distributedAppsRestoreOptions&#34;] = distributed_restore_json
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance" href="../bigdataappsinstance.html#cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance">BigDataAppsInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.browse" href="../../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataappsinstance.BigDataAppsInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances.bigdataapps" href="index.html">cvpysdk.instances.bigdataapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.bigdataapps.mongodbinstance.MongoDBInstance" href="#cvpysdk.instances.bigdataapps.mongodbinstance.MongoDBInstance">MongoDBInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.bigdataapps.mongodbinstance.MongoDBInstance.restore" href="#cvpysdk.instances.bigdataapps.mongodbinstance.MongoDBInstance.restore">restore</a></code></li>
<li><code><a title="cvpysdk.instances.bigdataapps.mongodbinstance.MongoDBInstance.restore_collection" href="#cvpysdk.instances.bigdataapps.mongodbinstance.MongoDBInstance.restore_collection">restore_collection</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>