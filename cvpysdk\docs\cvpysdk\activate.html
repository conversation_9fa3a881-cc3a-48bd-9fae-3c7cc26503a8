<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activate API documentation</title>
<meta name="description" content="Main file for performing operations on Activate apps …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activate</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing operations on Activate apps.</p>
<p>Activate : Base class for all activate apps</p>
<pre><code>    __init__()                  --  initialize instance of the Activate class

    __del__()                   --  destructor class for deleting referenced apps instances
</code></pre>
<h1 id="activate-instance-attributes">Activate instance Attributes</h1>
<pre><code>**entity_manager**              --  returns object of entity_manager class based on entity type specified

**inventory_manager**           --  returns object of Inventories class

**file_storage_optimization**   --  returns object of file_storage_optimization based on FSO type

**sensitive_data_governance**   --  returns object of sensitive data governance app

**request_manager**             --  returns object of Requests class
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activate.py#L1-L199" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing operations on Activate apps.

Activate : Base class for all activate apps

        __init__()                  --  initialize instance of the Activate class

        __del__()                   --  destructor class for deleting referenced apps instances


Activate instance Attributes
============================

    **entity_manager**              --  returns object of entity_manager class based on entity type specified

    **inventory_manager**           --  returns object of Inventories class

    **file_storage_optimization**   --  returns object of file_storage_optimization based on FSO type

    **sensitive_data_governance**   --  returns object of sensitive data governance app

    **request_manager**             --  returns object of Requests class

&#34;&#34;&#34;

from .exception import SDKException

from .activateapps.file_storage_optimization import FsoTypes, FsoServers, FsoServerGroups

from .activateapps.sensitive_data_governance import Projects

from .activateapps.inventory_manager import Inventories

from .activateapps.request_manager import Requests

from .activateapps.entity_manager import EntityManagerTypes, ActivateEntities, Tags, Classifiers

from .activateapps.compliance_utils import ComplianceSearchUtils


class Activate(object):
    &#34;&#34;&#34;Class for representing activate apps in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Activate class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Activate class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._entity = None
        self._tags = None
        self._classifiers = None
        self._inventories = None
        self._fso_servers = None
        self._fso_server_groups = None
        self._sdg_projects = None
        self._req_mgr = None
        self._compliance_search = None

    def __del__(self):
        &#34;&#34;&#34;Destructor method to delete all instances of apps referenced by this class&#34;&#34;&#34;
        del self._entity
        del self._tags
        del self._classifiers
        del self._inventories
        del self._fso_servers
        del self._fso_server_groups
        del self._sdg_projects
        del self._req_mgr

    def compliance_search(self):
        &#34;&#34;&#34;Returns the Compliance Search utility class object from activate apps&#34;&#34;&#34;
        if self._compliance_search is None:
            self._compliance_search = ComplianceSearchUtils(self._commcell_object)
        return self._compliance_search

    def inventory_manager(self):
        &#34;&#34;&#34;Returns the Inventories class object from inventory manager app from activate apps&#34;&#34;&#34;
        if self._inventories is None:
            self._inventories = Inventories(self._commcell_object)
        return self._inventories

    def request_manager(self):
        &#34;&#34;&#34;Returns the Requests class object from request manager app from activate apps&#34;&#34;&#34;
        if self._req_mgr is None:
            self._req_mgr = Requests(self._commcell_object)
        return self._req_mgr

    def file_storage_optimization(self, fso_type=FsoTypes.SERVERS):
        &#34;&#34;&#34;returns object of FsoServers/FsoServerGroups/Projects based on FSO type

                Args:

                    fso_type        (enum)      --  FsoTypes enum (Default : FsoServers)

                Returns:

                    obj --  Instance of FsoServers/FsoServerGroups/Projects based on type

                Raises:

                SDKException:

                        if input data is not valid

                        if entity type is not supported

        &#34;&#34;&#34;
        if not isinstance(fso_type, FsoTypes):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        if fso_type.value == FsoTypes.SERVERS.value:
            if self._fso_servers is None:
                self._fso_servers = FsoServers(self._commcell_object)
            return self._fso_servers
        elif fso_type.value == FsoTypes.SERVER_GROUPS.value:
            if self._fso_server_groups is None:
                self._fso_server_groups = FsoServerGroups(self._commcell_object)
            return self._fso_server_groups
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;102&#39;, &#39;Unsupported FSO type specified&#39;)

    def sensitive_data_governance(self):
        &#34;&#34;&#34;returns object of Sensitive data governance - Projects class

                Args:

                    None

                Returns:

                    obj --  Instance of Projects class from sensitive data governance

                Raises:

                    None

        &#34;&#34;&#34;
        if self._sdg_projects is None:
            self._sdg_projects = Projects(self._commcell_object)
        return self._sdg_projects

    def entity_manager(self, entity_type=EntityManagerTypes.ENTITIES):
        &#34;&#34;&#34;Returns the ActivateEntities or Classifiers or Tagsets object in entity manager based on input type

            Args:

                entity_type     (enum)      --  EntityManagerTypes enum

                                            Default : ENTITIES

            Returns:

                object      --  Instance of ActivateEntities or Classifiers or Tags class based on entity_type

            Raises:

                SDKException:

                        if input data is not valid

                        if entity type is not supported

        &#34;&#34;&#34;
        if not isinstance(entity_type, EntityManagerTypes):
            raise SDKException(&#39;EntityManager&#39;, &#39;101&#39;)
        if entity_type.value == EntityManagerTypes.ENTITIES.value:
            if self._entity is None:
                self._entity = ActivateEntities(self._commcell_object)
            return self._entity
        elif entity_type.value == EntityManagerTypes.TAGS.value:
            if self._tags is None:
                self._tags = Tags(self._commcell_object)
            return self._tags
        elif entity_type.value == EntityManagerTypes.CLASSIFIERS.value:
            if self._classifiers is None:
                self._classifiers = Classifiers(self._commcell_object)
            return self._classifiers
        raise SDKException(&#39;EntityManager&#39;, &#39;102&#39;, &#39;Unsupported entity type specified&#39;)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activate.Activate"><code class="flex name class">
<span>class <span class="ident">Activate</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing activate apps in the commcell.</p>
<p>Initializes an instance of the Activate class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Activate class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activate.py#L58-L199" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Activate(object):
    &#34;&#34;&#34;Class for representing activate apps in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Activate class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Activate class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._entity = None
        self._tags = None
        self._classifiers = None
        self._inventories = None
        self._fso_servers = None
        self._fso_server_groups = None
        self._sdg_projects = None
        self._req_mgr = None
        self._compliance_search = None

    def __del__(self):
        &#34;&#34;&#34;Destructor method to delete all instances of apps referenced by this class&#34;&#34;&#34;
        del self._entity
        del self._tags
        del self._classifiers
        del self._inventories
        del self._fso_servers
        del self._fso_server_groups
        del self._sdg_projects
        del self._req_mgr

    def compliance_search(self):
        &#34;&#34;&#34;Returns the Compliance Search utility class object from activate apps&#34;&#34;&#34;
        if self._compliance_search is None:
            self._compliance_search = ComplianceSearchUtils(self._commcell_object)
        return self._compliance_search

    def inventory_manager(self):
        &#34;&#34;&#34;Returns the Inventories class object from inventory manager app from activate apps&#34;&#34;&#34;
        if self._inventories is None:
            self._inventories = Inventories(self._commcell_object)
        return self._inventories

    def request_manager(self):
        &#34;&#34;&#34;Returns the Requests class object from request manager app from activate apps&#34;&#34;&#34;
        if self._req_mgr is None:
            self._req_mgr = Requests(self._commcell_object)
        return self._req_mgr

    def file_storage_optimization(self, fso_type=FsoTypes.SERVERS):
        &#34;&#34;&#34;returns object of FsoServers/FsoServerGroups/Projects based on FSO type

                Args:

                    fso_type        (enum)      --  FsoTypes enum (Default : FsoServers)

                Returns:

                    obj --  Instance of FsoServers/FsoServerGroups/Projects based on type

                Raises:

                SDKException:

                        if input data is not valid

                        if entity type is not supported

        &#34;&#34;&#34;
        if not isinstance(fso_type, FsoTypes):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        if fso_type.value == FsoTypes.SERVERS.value:
            if self._fso_servers is None:
                self._fso_servers = FsoServers(self._commcell_object)
            return self._fso_servers
        elif fso_type.value == FsoTypes.SERVER_GROUPS.value:
            if self._fso_server_groups is None:
                self._fso_server_groups = FsoServerGroups(self._commcell_object)
            return self._fso_server_groups
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;102&#39;, &#39;Unsupported FSO type specified&#39;)

    def sensitive_data_governance(self):
        &#34;&#34;&#34;returns object of Sensitive data governance - Projects class

                Args:

                    None

                Returns:

                    obj --  Instance of Projects class from sensitive data governance

                Raises:

                    None

        &#34;&#34;&#34;
        if self._sdg_projects is None:
            self._sdg_projects = Projects(self._commcell_object)
        return self._sdg_projects

    def entity_manager(self, entity_type=EntityManagerTypes.ENTITIES):
        &#34;&#34;&#34;Returns the ActivateEntities or Classifiers or Tagsets object in entity manager based on input type

            Args:

                entity_type     (enum)      --  EntityManagerTypes enum

                                            Default : ENTITIES

            Returns:

                object      --  Instance of ActivateEntities or Classifiers or Tags class based on entity_type

            Raises:

                SDKException:

                        if input data is not valid

                        if entity type is not supported

        &#34;&#34;&#34;
        if not isinstance(entity_type, EntityManagerTypes):
            raise SDKException(&#39;EntityManager&#39;, &#39;101&#39;)
        if entity_type.value == EntityManagerTypes.ENTITIES.value:
            if self._entity is None:
                self._entity = ActivateEntities(self._commcell_object)
            return self._entity
        elif entity_type.value == EntityManagerTypes.TAGS.value:
            if self._tags is None:
                self._tags = Tags(self._commcell_object)
            return self._tags
        elif entity_type.value == EntityManagerTypes.CLASSIFIERS.value:
            if self._classifiers is None:
                self._classifiers = Classifiers(self._commcell_object)
            return self._classifiers
        raise SDKException(&#39;EntityManager&#39;, &#39;102&#39;, &#39;Unsupported entity type specified&#39;)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activate.Activate.compliance_search"><code class="name flex">
<span>def <span class="ident">compliance_search</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the Compliance Search utility class object from activate apps</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activate.py#L93-L97" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def compliance_search(self):
    &#34;&#34;&#34;Returns the Compliance Search utility class object from activate apps&#34;&#34;&#34;
    if self._compliance_search is None:
        self._compliance_search = ComplianceSearchUtils(self._commcell_object)
    return self._compliance_search</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate.Activate.entity_manager"><code class="name flex">
<span>def <span class="ident">entity_manager</span></span>(<span>self, entity_type=EntityManagerTypes.ENTITIES)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the ActivateEntities or Classifiers or Tagsets object in entity manager based on input type</p>
<h2 id="args">Args</h2>
<p>entity_type
(enum)
&ndash;
EntityManagerTypes enum</p>
<pre><code>                        Default : ENTITIES
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
Instance of ActivateEntities or Classifiers or Tags class based on entity_type</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if input data is not valid

    if entity type is not supported
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activate.py#L163-L199" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def entity_manager(self, entity_type=EntityManagerTypes.ENTITIES):
    &#34;&#34;&#34;Returns the ActivateEntities or Classifiers or Tagsets object in entity manager based on input type

        Args:

            entity_type     (enum)      --  EntityManagerTypes enum

                                        Default : ENTITIES

        Returns:

            object      --  Instance of ActivateEntities or Classifiers or Tags class based on entity_type

        Raises:

            SDKException:

                    if input data is not valid

                    if entity type is not supported

    &#34;&#34;&#34;
    if not isinstance(entity_type, EntityManagerTypes):
        raise SDKException(&#39;EntityManager&#39;, &#39;101&#39;)
    if entity_type.value == EntityManagerTypes.ENTITIES.value:
        if self._entity is None:
            self._entity = ActivateEntities(self._commcell_object)
        return self._entity
    elif entity_type.value == EntityManagerTypes.TAGS.value:
        if self._tags is None:
            self._tags = Tags(self._commcell_object)
        return self._tags
    elif entity_type.value == EntityManagerTypes.CLASSIFIERS.value:
        if self._classifiers is None:
            self._classifiers = Classifiers(self._commcell_object)
        return self._classifiers
    raise SDKException(&#39;EntityManager&#39;, &#39;102&#39;, &#39;Unsupported entity type specified&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate.Activate.file_storage_optimization"><code class="name flex">
<span>def <span class="ident">file_storage_optimization</span></span>(<span>self, fso_type=FsoTypes.SERVERS)</span>
</code></dt>
<dd>
<div class="desc"><p>returns object of FsoServers/FsoServerGroups/Projects based on FSO type</p>
<h2 id="args">Args</h2>
<p>fso_type
(enum)
&ndash;
FsoTypes enum (Default : FsoServers)</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of FsoServers/FsoServerGroups/Projects based on type
Raises:</p>
<h2 id="sdkexception">Sdkexception</h2>
<p>if input data is not valid</p>
<p>if entity type is not supported</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activate.py#L111-L141" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def file_storage_optimization(self, fso_type=FsoTypes.SERVERS):
    &#34;&#34;&#34;returns object of FsoServers/FsoServerGroups/Projects based on FSO type

            Args:

                fso_type        (enum)      --  FsoTypes enum (Default : FsoServers)

            Returns:

                obj --  Instance of FsoServers/FsoServerGroups/Projects based on type

            Raises:

            SDKException:

                    if input data is not valid

                    if entity type is not supported

    &#34;&#34;&#34;
    if not isinstance(fso_type, FsoTypes):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
    if fso_type.value == FsoTypes.SERVERS.value:
        if self._fso_servers is None:
            self._fso_servers = FsoServers(self._commcell_object)
        return self._fso_servers
    elif fso_type.value == FsoTypes.SERVER_GROUPS.value:
        if self._fso_server_groups is None:
            self._fso_server_groups = FsoServerGroups(self._commcell_object)
        return self._fso_server_groups
    raise SDKException(&#39;FileStorageOptimization&#39;, &#39;102&#39;, &#39;Unsupported FSO type specified&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate.Activate.inventory_manager"><code class="name flex">
<span>def <span class="ident">inventory_manager</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the Inventories class object from inventory manager app from activate apps</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activate.py#L99-L103" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def inventory_manager(self):
    &#34;&#34;&#34;Returns the Inventories class object from inventory manager app from activate apps&#34;&#34;&#34;
    if self._inventories is None:
        self._inventories = Inventories(self._commcell_object)
    return self._inventories</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate.Activate.request_manager"><code class="name flex">
<span>def <span class="ident">request_manager</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the Requests class object from request manager app from activate apps</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activate.py#L105-L109" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def request_manager(self):
    &#34;&#34;&#34;Returns the Requests class object from request manager app from activate apps&#34;&#34;&#34;
    if self._req_mgr is None:
        self._req_mgr = Requests(self._commcell_object)
    return self._req_mgr</code></pre>
</details>
</dd>
<dt id="cvpysdk.activate.Activate.sensitive_data_governance"><code class="name flex">
<span>def <span class="ident">sensitive_data_governance</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>returns object of Sensitive data governance - Projects class</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of Projects class from sensitive data governance</p>
<h2 id="raises">Raises</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activate.py#L143-L161" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def sensitive_data_governance(self):
    &#34;&#34;&#34;returns object of Sensitive data governance - Projects class

            Args:

                None

            Returns:

                obj --  Instance of Projects class from sensitive data governance

            Raises:

                None

    &#34;&#34;&#34;
    if self._sdg_projects is None:
        self._sdg_projects = Projects(self._commcell_object)
    return self._sdg_projects</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#activate-instance-attributes">Activate instance Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activate.Activate" href="#cvpysdk.activate.Activate">Activate</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activate.Activate.compliance_search" href="#cvpysdk.activate.Activate.compliance_search">compliance_search</a></code></li>
<li><code><a title="cvpysdk.activate.Activate.entity_manager" href="#cvpysdk.activate.Activate.entity_manager">entity_manager</a></code></li>
<li><code><a title="cvpysdk.activate.Activate.file_storage_optimization" href="#cvpysdk.activate.Activate.file_storage_optimization">file_storage_optimization</a></code></li>
<li><code><a title="cvpysdk.activate.Activate.inventory_manager" href="#cvpysdk.activate.Activate.inventory_manager">inventory_manager</a></code></li>
<li><code><a title="cvpysdk.activate.Activate.request_manager" href="#cvpysdk.activate.Activate.request_manager">request_manager</a></code></li>
<li><code><a title="cvpysdk.activate.Activate.sensitive_data_governance" href="#cvpysdk.activate.Activate.sensitive_data_governance">sensitive_data_governance</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>