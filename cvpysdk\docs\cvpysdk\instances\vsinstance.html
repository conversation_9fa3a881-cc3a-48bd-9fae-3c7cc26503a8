<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.vsinstance API documentation</title>
<meta name="description" content="File for operating on a Virtual Server Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.vsinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Virtual Server Instance.</p>
<p>VirualServerInstance is the only class defined in this file.</p>
<p>VirtualServerInstance: Derived class from Instance Base class, representing a
virtual server instance, and to perform operations on that instance</p>
<h2 id="virtualserverinstance">Virtualserverinstance</h2>
<p><strong>new</strong>
&ndash;
Decides which instance object needs to be created</p>
<p><strong>init</strong>
&ndash;
initialise object of vsinstance class associated with
the specified agent, instance name and instance id</p>
<p>_get_instance_properties()
&ndash;
Instance class method overwritten to add virtual server
instance properties as well</p>
<p>associated_clients
&ndash;
getter or setter for the associated clients</p>
<p>co_ordinator
&ndash;
getter</p>
<p>frel
&ndash;
setter or getter for the FREL client</p>
<p>To add a new Virtual Instance, create a class in a new module under virtualserver sub package</p>
<p>The new module which is created has to named in the following manner:
1. Name the module with the name of the Virtual Server without special characters
2.Spaces alone must be replaced with underscores('_')</p>
<p>For eg:</p>
<pre><code>The Virtual Server 'Red Hat Virtualization' is named as 'red_hat_virtualization.py'

The Virtual Server 'Hyper-V' is named as 'hyperv.py'
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/vsinstance.py#L1-L359" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Virtual Server Instance.

VirualServerInstance is the only class defined in this file.

VirtualServerInstance: Derived class from Instance Base class, representing a
                            virtual server instance, and to perform operations on that instance

VirtualServerInstance:

     __new__                    --  Decides which instance object needs to be created

    __init__                    --  initialise object of vsinstance class associated with
                                            the specified agent, instance name and instance id

    _get_instance_properties()  --  Instance class method overwritten to add virtual server
                                        instance properties as well

    associated_clients                --  getter or setter for the associated clients

    co_ordinator                    --  getter

    frel                            --  setter or getter for the FREL client

To add a new Virtual Instance, create a class in a new module under virtualserver sub package


The new module which is created has to named in the following manner:
1. Name the module with the name of the Virtual Server without special characters
2.Spaces alone must be replaced with underscores(&#39;_&#39;)

For eg:

    The Virtual Server &#39;Red Hat Virtualization&#39; is named as &#39;red_hat_virtualization.py&#39;

    The Virtual Server &#39;Hyper-V&#39; is named as &#39;hyperv.py&#39;
&#34;&#34;&#34;

from __future__ import unicode_literals

import re
from importlib import import_module
from inspect import getmembers, isclass, isabstract

from ..instance import Instance
from ..constants import VsInstanceType
from ..exception import SDKException


class VirtualServerInstance(Instance):
    &#34;&#34;&#34;Class for representing an Instance of the Virtual Server agent.&#34;&#34;&#34;

    def __new__(cls, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;

        try:
            instance_name = VsInstanceType.VSINSTANCE_TYPE[agent_object.instances._vs_instance_type_dict[instance_id]]
        except KeyError:
            instance_name = re.sub(&#39;[^A-Za-z0-9_]+&#39;, &#39;&#39;, instance_name.replace(&#34; &#34;, &#34;_&#34;))

        try:
            instance_module = import_module(&#34;cvpysdk.instances.virtualserver.{}&#34;.format(instance_name))
        except ImportError:
            instance_module = import_module(&#34;cvpysdk.instances.virtualserver.null&#34;)

        classes = getmembers(instance_module, lambda m: isclass(m) and not isabstract(m))

        for name, _class in classes:
            if issubclass(_class, VirtualServerInstance) and _class.__module__.rsplit(&#34;.&#34;, 1)[-1] == instance_name:
                return object.__new__(_class)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        super(VirtualServerInstance, self)._get_instance_properties()

        self._vsinstancetype = None
        self._asscociatedclients = None
        if &#39;virtualServerInstance&#39; in self._properties:
            self._virtualserverinstance = self._properties[&#34;virtualServerInstance&#34;]
            self._vsinstancetype = self._virtualserverinstance[&#39;vsInstanceType&#39;]
            self._asscociatedclients = self._virtualserverinstance[&#39;associatedClients&#39;]

    def _get_instance_proxies(self):
        &#34;&#34;&#34;
                get the list of all the proxies on a selected instance

                Returns:
                    instance_proxies   (List)  --  returns the proxies list
        &#34;&#34;&#34;
        instance_members = self.associated_clients
        instance_proxies = []
        for member in instance_members:
            if self._commcell_object.client_groups.has_clientgroup(member):
                client_group = self._commcell_object.client_groups.get(member)
                clients_obj = self._commcell_object.clients
                instance_proxies.extend(list(set(clients_obj.virtualization_access_nodes).intersection(
                    set(clients.lower() for clients in client_group.associated_clients))))
            else:
                instance_proxies.append(member)

        return list(dict.fromkeys(instance_proxies))

    def _get_application_properties(self):
        &#34;&#34;&#34;Gets the application properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._APPLICATION = self._services[&#39;APPLICATION_INSTANCE&#39;] % (self._instance_id)
        self._application_properties = None

        # skip GET instance properties api call if instance id is 1
        if not int(self.instance_id) == 1:
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._APPLICATION)

            if flag:
                if response.json() and &#34;virtualServerInfo&#34; in response.json():
                    self._application_properties = response.json()[&#34;virtualServerInfo&#34;]

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))


    def _update_hypervisor_credentials(self, credential_json):
        &#34;&#34;&#34;updates the credential for   this instance.

             Args:
                credentialid (int)  --  Credential ID to update in hypervisor
                credentialname(str) -- Credential name to update in hypervisor

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._credential_service = self._services[&#39;INSTANCE_CREDENTIALS&#39;] % int(
                                                                self._agent_object._client_object.client_id)

        # skip GET instance properties api call if instance id is 1
        if not int(self.instance_id) == 1:
            flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, self._credential_service, credential_json)

            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        if &#39;errorCode&#39; in response.json()[&#39;response&#39;]:
                            error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]
                            if error_code != 0:
                                error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                                o_str = &#39;Failed to update credentials\nError: &#34;{0}&#34;&#39;.format(error_string)
                                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                            if &#39;errorMessage&#39; in response.json():
                                error_string = response.json()[&#39;errorMessage&#39;]
                                if error_string != &#34;&#34;:
                                    o_str = &#39;Failed to update credentials\nError: &#34;{0}&#34;&#39;.format(error_string)
                                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def server_name(self):
        &#34;&#34;&#34;returns the PseudoClient Name of the associated isntance&#34;&#34;&#34;
        return self._agent_object._client_object.client_name

    @property
    def associated_clients(self):
        &#34;&#34;&#34;Treats the clients associated to this instance as a read-only attribute.&#34;&#34;&#34;
        self._associated_clients = []
        if &#34;memberServers&#34; in self._asscociatedclients:
            for client in self._asscociatedclients[&#34;memberServers&#34;]:
                if &#39;clientName&#39; in client[&#39;client&#39;]:
                    self._associated_clients.append(client[&#34;client&#34;][&#34;clientName&#34;])
                elif &#39;clientGroupName&#39; in client[&#39;client&#39;]:

                    self._associated_clients.append(client[&#34;client&#34;][&#34;clientGroupName&#34;])
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;No Client Name or Client Group Name in JSON &#34;)
            return self._associated_clients

    @associated_clients.setter
    def associated_clients(self, clients_list):
        &#34;&#34;&#34;sets the associated clients with Client Dict Provided as input

            it replaces the list of proxies in the GUI

        Args:
                clients_list:    (list/str)       --- list of clients or client groups

        Raises:
            SDKException:
                if response is not success

                if input is not string or list of strings

                if input is not client of CS
        &#34;&#34;&#34;
        if not isinstance(clients_list, list):
            clients_list = [clients_list]
        if not isinstance(clients_list, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        for client_name in clients_list:
            if not isinstance(client_name, str):
                raise SDKException(&#39;Instance&#39;, &#39;105&#39;)

        client_json_list = []

        for client_name in clients_list:
            common_json = {}
            final_json = {}
            if self._commcell_object.clients.has_client(client_name):
                common_json[&#39;clientName&#39;] = client_name
                common_json[&#39;_type_&#39;] = 3
                final_json[&#39;client&#39;] = common_json
            elif self._commcell_object.client_groups.has_clientgroup(client_name):
                common_json[&#39;clientGroupName&#39;] = client_name
                common_json[&#39;_type_&#39;] = 28
                final_json[&#39;client&#39;] = common_json
            else:
                raise SDKException(&#39;Instance&#39;, &#39;105&#39;)

            client_json_list.append(final_json)

        request_json = {
            &#39;App_UpdateInstancePropertiesRequest&#39;: {
                &#39;instanceProperties&#39;: {
                    &#39;virtualServerInstance&#39;: {
                        &#39;associatedClients&#39;: {&#34;memberServers&#34;: client_json_list}
                    }
                },
                &#39;association&#39;: {
                    &#39;entity&#39;: [{
                        &#39;instanceId&#39;: self.instance_id,
                        &#39;_type&#39;: 5
                    }
                    ]
                }
            }
        }
        self._commcell_object.qoperation_execute(request_json)
        self.refresh()

    @property
    def co_ordinator(self):
        &#34;&#34;&#34;Returns the Co_ordinator of this instance it is read-only attribute&#34;&#34;&#34;
        if self.associated_clients is not None:
            _associated_clients = self.associated_clients
            associated_client = _associated_clients[0]
            if self._commcell_object.clients.has_client(associated_client):
                return associated_client
            elif self._commcell_object.client_groups.has_clientgroup(associated_client):
                associated_client_group = self._commcell_object.client_groups.get(associated_client)
                return associated_client_group._associated_clients[0]

    @property
    def frel(self):
        &#34;&#34;&#34;
        Returns the FREL associated at the instance level
            Returns:
                string : frel client name

            Raises:
                SDKException:
                    if failed to fetch properties

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        _application_instance = self._services[&#39;APPLICATION_INSTANCE&#39;] % self._instance_id
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _application_instance)
        if flag:
            if response.json():
                return response.json().get(&#39;virtualServerInfo&#39;, {}).get(&#39;defaultFBRUnixMediaAgent&#39;, {}).get(
                    &#39;mediaAgentName&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @frel.setter
    def frel(self, frel_client):
        &#34;&#34;&#34;sets the FREL in the instance provided as input
        Args:
                frel_client:    (string)       --- FREL client to be set as FREL

        Raises:
            SDKException:
                if response is not success

                if input is not string

                if input is not client of CS
        &#34;&#34;&#34;
        recovery_enablers = self._services[&#39;RECOVERY_ENABLERS&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, recovery_enablers)
        if flag:
            if response.json():
                frel_ready_ma = response.json().get(&#39;mediaAgentList&#39;)
                if list(filter(lambda ma: ma[&#39;mediaAgentName&#39;].lower() == frel_client.lower(), frel_ready_ma)):
                    _application_instance = self._services[&#39;APPLICATION_INSTANCE&#39;] % self._instance_id
                    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _application_instance)
                    if flag:
                        if response.json():
                            _json = response.json()
                            if _json.get(&#39;virtualServerInfo&#39;, {}).get(&#39;defaultFBRUnixMediaAgent&#39;, {}):
                                _json[&#39;virtualServerInfo&#39;][&#39;defaultFBRUnixMediaAgent&#39;][&#39;mediaAgentName&#39;] = frel_client
                            else:
                                raise SDKException(&#39;Instance&#39;, &#39;102&#39;,
                                                   &#39;Not possible to assign/add FREL MA. Please check if the &#39;
                                                   &#39;instance supports FREL&#39;)
                            if _json.get(&#39;virtualServerInfo&#39;, {}).get(&#39;defaultFBRUnixMediaAgent&#39;, {}).get(
                                    &#39;mediaAgentId&#39;):
                                del _json[&#39;virtualServerInfo&#39;][&#39;defaultFBRUnixMediaAgent&#39;][&#39;mediaAgentId&#39;]
                            _json = {&#39;prop&#39;: _json}
                            _application_upate = self._services[&#39;APPLICATION&#39;]
                            flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, _application_upate, _json)
                            if not flag:
                                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                        else:
                            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    else:
                        raise SDKException(&#39;Instance&#39;, &#39;105&#39;)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;108&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.vsinstance.VirtualServerInstance"><code class="flex name class">
<span>class <span class="ident">VirtualServerInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of the Virtual Server agent.</p>
<p>Initialise the instance object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/vsinstance.py#L67-L359" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VirtualServerInstance(Instance):
    &#34;&#34;&#34;Class for representing an Instance of the Virtual Server agent.&#34;&#34;&#34;

    def __new__(cls, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;

        try:
            instance_name = VsInstanceType.VSINSTANCE_TYPE[agent_object.instances._vs_instance_type_dict[instance_id]]
        except KeyError:
            instance_name = re.sub(&#39;[^A-Za-z0-9_]+&#39;, &#39;&#39;, instance_name.replace(&#34; &#34;, &#34;_&#34;))

        try:
            instance_module = import_module(&#34;cvpysdk.instances.virtualserver.{}&#34;.format(instance_name))
        except ImportError:
            instance_module = import_module(&#34;cvpysdk.instances.virtualserver.null&#34;)

        classes = getmembers(instance_module, lambda m: isclass(m) and not isabstract(m))

        for name, _class in classes:
            if issubclass(_class, VirtualServerInstance) and _class.__module__.rsplit(&#34;.&#34;, 1)[-1] == instance_name:
                return object.__new__(_class)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        super(VirtualServerInstance, self)._get_instance_properties()

        self._vsinstancetype = None
        self._asscociatedclients = None
        if &#39;virtualServerInstance&#39; in self._properties:
            self._virtualserverinstance = self._properties[&#34;virtualServerInstance&#34;]
            self._vsinstancetype = self._virtualserverinstance[&#39;vsInstanceType&#39;]
            self._asscociatedclients = self._virtualserverinstance[&#39;associatedClients&#39;]

    def _get_instance_proxies(self):
        &#34;&#34;&#34;
                get the list of all the proxies on a selected instance

                Returns:
                    instance_proxies   (List)  --  returns the proxies list
        &#34;&#34;&#34;
        instance_members = self.associated_clients
        instance_proxies = []
        for member in instance_members:
            if self._commcell_object.client_groups.has_clientgroup(member):
                client_group = self._commcell_object.client_groups.get(member)
                clients_obj = self._commcell_object.clients
                instance_proxies.extend(list(set(clients_obj.virtualization_access_nodes).intersection(
                    set(clients.lower() for clients in client_group.associated_clients))))
            else:
                instance_proxies.append(member)

        return list(dict.fromkeys(instance_proxies))

    def _get_application_properties(self):
        &#34;&#34;&#34;Gets the application properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._APPLICATION = self._services[&#39;APPLICATION_INSTANCE&#39;] % (self._instance_id)
        self._application_properties = None

        # skip GET instance properties api call if instance id is 1
        if not int(self.instance_id) == 1:
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._APPLICATION)

            if flag:
                if response.json() and &#34;virtualServerInfo&#34; in response.json():
                    self._application_properties = response.json()[&#34;virtualServerInfo&#34;]

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))


    def _update_hypervisor_credentials(self, credential_json):
        &#34;&#34;&#34;updates the credential for   this instance.

             Args:
                credentialid (int)  --  Credential ID to update in hypervisor
                credentialname(str) -- Credential name to update in hypervisor

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._credential_service = self._services[&#39;INSTANCE_CREDENTIALS&#39;] % int(
                                                                self._agent_object._client_object.client_id)

        # skip GET instance properties api call if instance id is 1
        if not int(self.instance_id) == 1:
            flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, self._credential_service, credential_json)

            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        if &#39;errorCode&#39; in response.json()[&#39;response&#39;]:
                            error_code = response.json()[&#39;response&#39;][&#39;errorCode&#39;]
                            if error_code != 0:
                                error_string = response.json()[&#39;response&#39;][&#39;errorString&#39;]
                                o_str = &#39;Failed to update credentials\nError: &#34;{0}&#34;&#39;.format(error_string)
                                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                            if &#39;errorMessage&#39; in response.json():
                                error_string = response.json()[&#39;errorMessage&#39;]
                                if error_string != &#34;&#34;:
                                    o_str = &#39;Failed to update credentials\nError: &#34;{0}&#34;&#39;.format(error_string)
                                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def server_name(self):
        &#34;&#34;&#34;returns the PseudoClient Name of the associated isntance&#34;&#34;&#34;
        return self._agent_object._client_object.client_name

    @property
    def associated_clients(self):
        &#34;&#34;&#34;Treats the clients associated to this instance as a read-only attribute.&#34;&#34;&#34;
        self._associated_clients = []
        if &#34;memberServers&#34; in self._asscociatedclients:
            for client in self._asscociatedclients[&#34;memberServers&#34;]:
                if &#39;clientName&#39; in client[&#39;client&#39;]:
                    self._associated_clients.append(client[&#34;client&#34;][&#34;clientName&#34;])
                elif &#39;clientGroupName&#39; in client[&#39;client&#39;]:

                    self._associated_clients.append(client[&#34;client&#34;][&#34;clientGroupName&#34;])
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;No Client Name or Client Group Name in JSON &#34;)
            return self._associated_clients

    @associated_clients.setter
    def associated_clients(self, clients_list):
        &#34;&#34;&#34;sets the associated clients with Client Dict Provided as input

            it replaces the list of proxies in the GUI

        Args:
                clients_list:    (list/str)       --- list of clients or client groups

        Raises:
            SDKException:
                if response is not success

                if input is not string or list of strings

                if input is not client of CS
        &#34;&#34;&#34;
        if not isinstance(clients_list, list):
            clients_list = [clients_list]
        if not isinstance(clients_list, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        for client_name in clients_list:
            if not isinstance(client_name, str):
                raise SDKException(&#39;Instance&#39;, &#39;105&#39;)

        client_json_list = []

        for client_name in clients_list:
            common_json = {}
            final_json = {}
            if self._commcell_object.clients.has_client(client_name):
                common_json[&#39;clientName&#39;] = client_name
                common_json[&#39;_type_&#39;] = 3
                final_json[&#39;client&#39;] = common_json
            elif self._commcell_object.client_groups.has_clientgroup(client_name):
                common_json[&#39;clientGroupName&#39;] = client_name
                common_json[&#39;_type_&#39;] = 28
                final_json[&#39;client&#39;] = common_json
            else:
                raise SDKException(&#39;Instance&#39;, &#39;105&#39;)

            client_json_list.append(final_json)

        request_json = {
            &#39;App_UpdateInstancePropertiesRequest&#39;: {
                &#39;instanceProperties&#39;: {
                    &#39;virtualServerInstance&#39;: {
                        &#39;associatedClients&#39;: {&#34;memberServers&#34;: client_json_list}
                    }
                },
                &#39;association&#39;: {
                    &#39;entity&#39;: [{
                        &#39;instanceId&#39;: self.instance_id,
                        &#39;_type&#39;: 5
                    }
                    ]
                }
            }
        }
        self._commcell_object.qoperation_execute(request_json)
        self.refresh()

    @property
    def co_ordinator(self):
        &#34;&#34;&#34;Returns the Co_ordinator of this instance it is read-only attribute&#34;&#34;&#34;
        if self.associated_clients is not None:
            _associated_clients = self.associated_clients
            associated_client = _associated_clients[0]
            if self._commcell_object.clients.has_client(associated_client):
                return associated_client
            elif self._commcell_object.client_groups.has_clientgroup(associated_client):
                associated_client_group = self._commcell_object.client_groups.get(associated_client)
                return associated_client_group._associated_clients[0]

    @property
    def frel(self):
        &#34;&#34;&#34;
        Returns the FREL associated at the instance level
            Returns:
                string : frel client name

            Raises:
                SDKException:
                    if failed to fetch properties

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        _application_instance = self._services[&#39;APPLICATION_INSTANCE&#39;] % self._instance_id
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _application_instance)
        if flag:
            if response.json():
                return response.json().get(&#39;virtualServerInfo&#39;, {}).get(&#39;defaultFBRUnixMediaAgent&#39;, {}).get(
                    &#39;mediaAgentName&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @frel.setter
    def frel(self, frel_client):
        &#34;&#34;&#34;sets the FREL in the instance provided as input
        Args:
                frel_client:    (string)       --- FREL client to be set as FREL

        Raises:
            SDKException:
                if response is not success

                if input is not string

                if input is not client of CS
        &#34;&#34;&#34;
        recovery_enablers = self._services[&#39;RECOVERY_ENABLERS&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, recovery_enablers)
        if flag:
            if response.json():
                frel_ready_ma = response.json().get(&#39;mediaAgentList&#39;)
                if list(filter(lambda ma: ma[&#39;mediaAgentName&#39;].lower() == frel_client.lower(), frel_ready_ma)):
                    _application_instance = self._services[&#39;APPLICATION_INSTANCE&#39;] % self._instance_id
                    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _application_instance)
                    if flag:
                        if response.json():
                            _json = response.json()
                            if _json.get(&#39;virtualServerInfo&#39;, {}).get(&#39;defaultFBRUnixMediaAgent&#39;, {}):
                                _json[&#39;virtualServerInfo&#39;][&#39;defaultFBRUnixMediaAgent&#39;][&#39;mediaAgentName&#39;] = frel_client
                            else:
                                raise SDKException(&#39;Instance&#39;, &#39;102&#39;,
                                                   &#39;Not possible to assign/add FREL MA. Please check if the &#39;
                                                   &#39;instance supports FREL&#39;)
                            if _json.get(&#39;virtualServerInfo&#39;, {}).get(&#39;defaultFBRUnixMediaAgent&#39;, {}).get(
                                    &#39;mediaAgentId&#39;):
                                del _json[&#39;virtualServerInfo&#39;][&#39;defaultFBRUnixMediaAgent&#39;][&#39;mediaAgentId&#39;]
                            _json = {&#39;prop&#39;: _json}
                            _application_upate = self._services[&#39;APPLICATION&#39;]
                            flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, _application_upate, _json)
                            if not flag:
                                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                        else:
                            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    else:
                        raise SDKException(&#39;Instance&#39;, &#39;105&#39;)
                else:
                    raise SDKException(&#39;Instance&#39;, &#39;108&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.virtualserver.alibaba_cloud.AlibabaCloudInstance" href="virtualserver/alibaba_cloud.html#cvpysdk.instances.virtualserver.alibaba_cloud.AlibabaCloudInstance">AlibabaCloudInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.amazon_web_services.AmazonInstance" href="virtualserver/amazon_web_services.html#cvpysdk.instances.virtualserver.amazon_web_services.AmazonInstance">AmazonInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.azure.AzureInstance" href="virtualserver/azure.html#cvpysdk.instances.virtualserver.azure.AzureInstance">AzureInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.azure_resource_manager.AzureRMInstance" href="virtualserver/azure_resource_manager.html#cvpysdk.instances.virtualserver.azure_resource_manager.AzureRMInstance">AzureRMInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.azure_stack.AzureStackInstance" href="virtualserver/azure_stack.html#cvpysdk.instances.virtualserver.azure_stack.AzureStackInstance">AzureStackInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.fusioncompute.FusionComputeInstance" href="virtualserver/fusioncompute.html#cvpysdk.instances.virtualserver.fusioncompute.FusionComputeInstance">FusionComputeInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.google_cloud_platform.GoogleCloudInstance" href="virtualserver/google_cloud_platform.html#cvpysdk.instances.virtualserver.google_cloud_platform.GoogleCloudInstance">GoogleCloudInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.hyperv.HyperVInstance" href="virtualserver/hyperv.html#cvpysdk.instances.virtualserver.hyperv.HyperVInstance">HyperVInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.kubernetes.KubernetesInstance" href="virtualserver/kubernetes.html#cvpysdk.instances.virtualserver.kubernetes.KubernetesInstance">KubernetesInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.null.NullSubclient" href="virtualserver/null.html#cvpysdk.instances.virtualserver.null.NullSubclient">NullSubclient</a></li>
<li><a title="cvpysdk.instances.virtualserver.nutanix_ahv.nutanixinstance" href="virtualserver/nutanix_ahv.html#cvpysdk.instances.virtualserver.nutanix_ahv.nutanixinstance">nutanixinstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.openstack.OpenStackInstance" href="virtualserver/openstack.html#cvpysdk.instances.virtualserver.openstack.OpenStackInstance">OpenStackInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.oracle_cloud.OracleCloudInstance" href="virtualserver/oracle_cloud.html#cvpysdk.instances.virtualserver.oracle_cloud.OracleCloudInstance">OracleCloudInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.oracle_cloud_infrastructure.OracleCloudInfrastructureInstance" href="virtualserver/oracle_cloud_infrastructure.html#cvpysdk.instances.virtualserver.oracle_cloud_infrastructure.OracleCloudInfrastructureInstance">OracleCloudInfrastructureInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.oraclevm.OracleVMInstance" href="virtualserver/oraclevm.html#cvpysdk.instances.virtualserver.oraclevm.OracleVMInstance">OracleVMInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.proxmox_ve.ProxmoxVEInstance" href="virtualserver/proxmox_ve.html#cvpysdk.instances.virtualserver.proxmox_ve.ProxmoxVEInstance">ProxmoxVEInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.red_hat_virtualization.RhevInstance" href="virtualserver/red_hat_virtualization.html#cvpysdk.instances.virtualserver.red_hat_virtualization.RhevInstance">RhevInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.vcloud_director.vcloudInstance" href="virtualserver/vcloud_director.html#cvpysdk.instances.virtualserver.vcloud_director.vcloudInstance">vcloudInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.vmware.VMwareInstance" href="virtualserver/vmware.html#cvpysdk.instances.virtualserver.vmware.VMwareInstance">VMwareInstance</a></li>
<li><a title="cvpysdk.instances.virtualserver.xen.Xen" href="virtualserver/xen.html#cvpysdk.instances.virtualserver.xen.Xen">Xen</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.vsinstance.VirtualServerInstance.associated_clients"><code class="name">var <span class="ident">associated_clients</span></code></dt>
<dd>
<div class="desc"><p>Treats the clients associated to this instance as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/vsinstance.py#L196-L209" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associated_clients(self):
    &#34;&#34;&#34;Treats the clients associated to this instance as a read-only attribute.&#34;&#34;&#34;
    self._associated_clients = []
    if &#34;memberServers&#34; in self._asscociatedclients:
        for client in self._asscociatedclients[&#34;memberServers&#34;]:
            if &#39;clientName&#39; in client[&#39;client&#39;]:
                self._associated_clients.append(client[&#34;client&#34;][&#34;clientName&#34;])
            elif &#39;clientGroupName&#39; in client[&#39;client&#39;]:

                self._associated_clients.append(client[&#34;client&#34;][&#34;clientGroupName&#34;])
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;No Client Name or Client Group Name in JSON &#34;)
        return self._associated_clients</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.vsinstance.VirtualServerInstance.co_ordinator"><code class="name">var <span class="ident">co_ordinator</span></code></dt>
<dd>
<div class="desc"><p>Returns the Co_ordinator of this instance it is read-only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/vsinstance.py#L273-L283" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def co_ordinator(self):
    &#34;&#34;&#34;Returns the Co_ordinator of this instance it is read-only attribute&#34;&#34;&#34;
    if self.associated_clients is not None:
        _associated_clients = self.associated_clients
        associated_client = _associated_clients[0]
        if self._commcell_object.clients.has_client(associated_client):
            return associated_client
        elif self._commcell_object.client_groups.has_clientgroup(associated_client):
            associated_client_group = self._commcell_object.client_groups.get(associated_client)
            return associated_client_group._associated_clients[0]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.vsinstance.VirtualServerInstance.frel"><code class="name">var <span class="ident">frel</span></code></dt>
<dd>
<div class="desc"><p>Returns the FREL associated at the instance level
Returns:
string : frel client name</p>
<pre><code>Raises:
    SDKException:
        if failed to fetch properties

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/vsinstance.py#L285-L309" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def frel(self):
    &#34;&#34;&#34;
    Returns the FREL associated at the instance level
        Returns:
            string : frel client name

        Raises:
            SDKException:
                if failed to fetch properties

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    _application_instance = self._services[&#39;APPLICATION_INSTANCE&#39;] % self._instance_id
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, _application_instance)
    if flag:
        if response.json():
            return response.json().get(&#39;virtualServerInfo&#39;, {}).get(&#39;defaultFBRUnixMediaAgent&#39;, {}).get(
                &#39;mediaAgentName&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.vsinstance.VirtualServerInstance.server_name"><code class="name">var <span class="ident">server_name</span></code></dt>
<dd>
<div class="desc"><p>returns the PseudoClient Name of the associated isntance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/vsinstance.py#L191-L194" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_name(self):
    &#34;&#34;&#34;returns the PseudoClient Name of the associated isntance&#34;&#34;&#34;
    return self._agent_object._client_object.client_name</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instance.Instance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.browse" href="../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.vsinstance.VirtualServerInstance" href="#cvpysdk.instances.vsinstance.VirtualServerInstance">VirtualServerInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.vsinstance.VirtualServerInstance.associated_clients" href="#cvpysdk.instances.vsinstance.VirtualServerInstance.associated_clients">associated_clients</a></code></li>
<li><code><a title="cvpysdk.instances.vsinstance.VirtualServerInstance.co_ordinator" href="#cvpysdk.instances.vsinstance.VirtualServerInstance.co_ordinator">co_ordinator</a></code></li>
<li><code><a title="cvpysdk.instances.vsinstance.VirtualServerInstance.frel" href="#cvpysdk.instances.vsinstance.VirtualServerInstance.frel">frel</a></code></li>
<li><code><a title="cvpysdk.instances.vsinstance.VirtualServerInstance.server_name" href="#cvpysdk.instances.vsinstance.VirtualServerInstance.server_name">server_name</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>