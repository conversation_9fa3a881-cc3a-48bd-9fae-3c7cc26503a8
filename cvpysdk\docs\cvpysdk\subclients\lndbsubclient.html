<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.lndbsubclient API documentation</title>
<meta name="description" content="File for operating on a Notes Database Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.lndbsubclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Notes Database Subclient.</p>
<p>LNDbSubclient is the only class defined in this file.</p>
<p>LNDbSubclient:
Derived class from Subclient Base class.
Represents a notes database subclient, and performs operations on that subclient</p>
<h2 id="lndbsubclient">Lndbsubclient</h2>
<p>_get_subclient_properties()
&ndash;
gets subclient related properties of
Notes Database subclient.</p>
<p>_get_subclient_properties_json()
&ndash;
gets all the subclient related properties of
Notes Database subclient.</p>
<p>content()
&ndash;
get the content of the subclient</p>
<p>restore_in_place()
&ndash; performs an in place restore of the subclient</p>
<p>restore_out_of_place()
&ndash; performs and out of place restore of the subclient</p>
<p>backup()
&ndash;
run a backup job for the subclient</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/lndbsubclient.py#L1-L440" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Notes Database Subclient.

LNDbSubclient is the only class defined in this file.

LNDbSubclient:  Derived class from Subclient Base class.
Represents a notes database subclient, and performs operations on that subclient

LNDbSubclient:

    _get_subclient_properties()         --  gets subclient related properties of
    Notes Database subclient.

    _get_subclient_properties_json()    --  gets all the subclient related properties of
    Notes Database subclient.

    content()                           --  get the content of the subclient

    restore_in_place()                  -- performs an in place restore of the subclient

    restore_out_of_place()              -- performs and out of place restore of the subclient

    backup()                            --  run a backup job for the subclient
&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

import json

from ..subclient import Subclient
from ..exception import SDKException


class LNDbSubclient(Subclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a LNDB subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of LN DB subclient.&#34;&#34;&#34;
        super(LNDbSubclient, self)._get_subclient_properties()
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]
        if &#39;proxyClient&#39; in self._subclient_properties:
            self._proxyClient = self._subclient_properties[&#39;proxyClient&#39;]
        if &#39;subClientEntity&#39; in self._subclient_properties:
            self._subClientEntity = self._subclient_properties[&#39;subClientEntity&#39;]
        if &#39;commonProperties&#39; in self._subclient_properties:
            self._commonProperties = self._subclient_properties[&#39;commonProperties&#39;]

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;Get the all subclient related properties of this subclient.
           Returns:
                dict - all subclient properties put inside a dict
        &#34;&#34;&#34;

        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return subclient_json

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;
        return self._content

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update content of a
        LNDB Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API

        &#34;&#34;&#34;
        content = []
        try:
            for database in subclient_content:
                if database == {}:
                    continue
                elif &#39;lotusNotesDBContent&#39; in database:
                    content.append(database)
                else:
                    temp_content_dict = {}
                    temp_content_dict = {
                        &#34;lotusNotesDBContent&#34;: {
                            &#34;dbiid1&#34;: database[&#39;dbiid1&#39;],
                            &#34;dbiid2&#34;: database[&#39;dbiid2&#39;],
                            &#34;dbiid3&#34;: database[&#39;dbiid3&#39;],
                            &#34;dbiid4&#34;: database[&#39;dbiid4&#39;],
                            &#34;relativePath&#34;: database[&#39;relativePath&#39;],
                            &#34;databaseTitle&#34;: database[&#39;databaseTitle&#39;]
                        }
                    }
                    if temp_content_dict != {}:
                        content.append(temp_content_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        self._set_subclient_properties(&#34;_content&#34;, content)

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            common_options_dict=None,
            lndb_restore_options=None):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore

                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files

                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy

                    default: None

                from_time           (str)       --  time to retore the contents after

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                common_options_dict (dict)          -- dictionary for all the common options
                    options:
                        unconditionalOverwrite              :   overwrite the files during restore
                        even if they exist

                        recoverWait                         :   Specifies whether this restore
                        operation must wait until resources become available if a database recovery
                        is already taking place

                        recoverZap                          :   Specifies whether the IBM Domino
                        must change the DBIID associated with the restored database

                        recoverZapReplica                   :   Specifies whether the restore
                        operation changes the replica id of the restored database

                        recoverZapIfNecessary               :   Specifies whether the IBM Domino
                        can change the DBIID associated with the restored database if necessary

                        doNotReplayTransactLogs             :   option to skip restoring or
                        replaying logs


                    Disaster Recovery special options:
                        skipErrorsAndContinue               :   enables a data recovery operation
                        to continue despite media errors

                        disasterRecovery                    :   run disaster recovery

                lndb_restore_options    (dict)          -- dictionary for all options specific
                to an lndb restore
                    options:
                        disableReplication      :   disable relpication on database

                        disableBackgroundAgents :   disable background agents


            Returns:
                object  -   instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if common_options_dict is None:
            common_options_dict = {}

        if lndb_restore_options is None:
            lndb_restore_options = {}

        paths = self._filter_paths(paths)

        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        self._backupset_object._instance_object._restore_association = self._subClientEntity

        request_json = self._restore_json(
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            common_options_dict=common_options_dict,
            lndb_restore_options=lndb_restore_options
        )

        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            common_options_dict=None,
            lndb_restore_options=None):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore

                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files

                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy

                    default: None

                from_time           (str)       --  time to retore the contents after

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                common_options_dict (dict)          -- dictionary for all the common options
                    options:
                        unconditionalOverwrite              :   overwrite the files during restore
                        even if they exist

                        recoverWait                         :   Specifies whether this restore
                        operation must wait until resources become available if a database recovery
                        is already taking place

                        recoverZap                          :   Specifies whether the IBM Domino
                        must change the DBIID associated with the restored database

                        recoverZapReplica                   :   Specifies whether the restore
                        operation changes the replica id of the restored database

                        recoverZapIfNecessary               :   Specifies whether the IBM Domino
                        can change the DBIID associated with the restored database if necessary

                        doNotReplayTransactLogs             :   option to skip restoring or
                        replaying logs


                    Disaster Recovery special options:
                        skipErrorsAndContinue               :   enables a data recovery operation
                        to continue despite media errors

                        disasterRecovery                    :   run disaster recovery

                lndb_restore_options    (dict)          -- dictionary for all options specific
                to an lndb restore
                    options:
                        disableReplication      :   disable relpication on database

                        disableBackgroundAgents :   disable background agents

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        if not (isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if common_options_dict is None:
            common_options_dict = {}

        if lndb_restore_options is None:
            lndb_restore_options = {}

        paths = self._filter_paths(paths)

        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        self._backupset_object._instance_object._restore_association = self._subClientEntity

        request_json = self._restore_json(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            common_options_dict=common_options_dict,
            lndb_restore_options=lndb_restore_options)

        return self._process_restore_response(request_json)

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               schedule_pattern=None):

        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

                    Args:
                        backup_level        (str)   --  level of backup the user wish to run

                            Full / Incremental / Differential / Synthetic_full

                        incremental_backup  (bool)  --  run incremental backup

                            only applicable in case of Synthetic_full backup

                        incremental_level   (str)   --  run incremental backup before/after
                        synthetic full

                            BEFORE_SYNTH / AFTER_SYNTH

                            only applicable in case of Synthetic_full backup

                        schedule_pattern (dict) -- scheduling options to be included for the task

                            Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                    Returns:
                        dict    -   JSON request to pass to the API

        &#34;&#34;&#34;

        if schedule_pattern:
            request_json = self._backup_json(
                backup_level,
                incremental_backup,
                incremental_level,
                schedule_pattern=schedule_pattern)

            backup_service = self._services[&#39;CREATE_TASK&#39;]

            flag, response = self._commcell_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )

        else:
            return super(LNDbSubclient, self).backup(
                backup_level=backup_level,
                incremental_backup=incremental_backup,
                incremental_level=incremental_level)

        return self._process_backup_response(flag, response)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.lndbsubclient.LNDbSubclient"><code class="flex name class">
<span>class <span class="ident">LNDbSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Subclient Base class, representing a LNDB subclient,
and to perform operations on that subclient.</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/lndbsubclient.py#L52-L439" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class LNDbSubclient(Subclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a LNDB subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of LN DB subclient.&#34;&#34;&#34;
        super(LNDbSubclient, self)._get_subclient_properties()
        if &#39;content&#39; in self._subclient_properties:
            self._content = self._subclient_properties[&#39;content&#39;]
        if &#39;proxyClient&#39; in self._subclient_properties:
            self._proxyClient = self._subclient_properties[&#39;proxyClient&#39;]
        if &#39;subClientEntity&#39; in self._subclient_properties:
            self._subClientEntity = self._subclient_properties[&#39;subClientEntity&#39;]
        if &#39;commonProperties&#39; in self._subclient_properties:
            self._commonProperties = self._subclient_properties[&#39;commonProperties&#39;]

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;Get the all subclient related properties of this subclient.
           Returns:
                dict - all subclient properties put inside a dict
        &#34;&#34;&#34;

        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return subclient_json

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;
        return self._content

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update content of a
        LNDB Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API

        &#34;&#34;&#34;
        content = []
        try:
            for database in subclient_content:
                if database == {}:
                    continue
                elif &#39;lotusNotesDBContent&#39; in database:
                    content.append(database)
                else:
                    temp_content_dict = {}
                    temp_content_dict = {
                        &#34;lotusNotesDBContent&#34;: {
                            &#34;dbiid1&#34;: database[&#39;dbiid1&#39;],
                            &#34;dbiid2&#34;: database[&#39;dbiid2&#39;],
                            &#34;dbiid3&#34;: database[&#39;dbiid3&#39;],
                            &#34;dbiid4&#34;: database[&#39;dbiid4&#39;],
                            &#34;relativePath&#34;: database[&#39;relativePath&#39;],
                            &#34;databaseTitle&#34;: database[&#39;databaseTitle&#39;]
                        }
                    }
                    if temp_content_dict != {}:
                        content.append(temp_content_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        self._set_subclient_properties(&#34;_content&#34;, content)

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            common_options_dict=None,
            lndb_restore_options=None):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore

                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files

                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy

                    default: None

                from_time           (str)       --  time to retore the contents after

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                common_options_dict (dict)          -- dictionary for all the common options
                    options:
                        unconditionalOverwrite              :   overwrite the files during restore
                        even if they exist

                        recoverWait                         :   Specifies whether this restore
                        operation must wait until resources become available if a database recovery
                        is already taking place

                        recoverZap                          :   Specifies whether the IBM Domino
                        must change the DBIID associated with the restored database

                        recoverZapReplica                   :   Specifies whether the restore
                        operation changes the replica id of the restored database

                        recoverZapIfNecessary               :   Specifies whether the IBM Domino
                        can change the DBIID associated with the restored database if necessary

                        doNotReplayTransactLogs             :   option to skip restoring or
                        replaying logs


                    Disaster Recovery special options:
                        skipErrorsAndContinue               :   enables a data recovery operation
                        to continue despite media errors

                        disasterRecovery                    :   run disaster recovery

                lndb_restore_options    (dict)          -- dictionary for all options specific
                to an lndb restore
                    options:
                        disableReplication      :   disable relpication on database

                        disableBackgroundAgents :   disable background agents


            Returns:
                object  -   instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if common_options_dict is None:
            common_options_dict = {}

        if lndb_restore_options is None:
            lndb_restore_options = {}

        paths = self._filter_paths(paths)

        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        self._backupset_object._instance_object._restore_association = self._subClientEntity

        request_json = self._restore_json(
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            common_options_dict=common_options_dict,
            lndb_restore_options=lndb_restore_options
        )

        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            common_options_dict=None,
            lndb_restore_options=None):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore

                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files

                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy

                    default: None

                from_time           (str)       --  time to retore the contents after

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before

                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                common_options_dict (dict)          -- dictionary for all the common options
                    options:
                        unconditionalOverwrite              :   overwrite the files during restore
                        even if they exist

                        recoverWait                         :   Specifies whether this restore
                        operation must wait until resources become available if a database recovery
                        is already taking place

                        recoverZap                          :   Specifies whether the IBM Domino
                        must change the DBIID associated with the restored database

                        recoverZapReplica                   :   Specifies whether the restore
                        operation changes the replica id of the restored database

                        recoverZapIfNecessary               :   Specifies whether the IBM Domino
                        can change the DBIID associated with the restored database if necessary

                        doNotReplayTransactLogs             :   option to skip restoring or
                        replaying logs


                    Disaster Recovery special options:
                        skipErrorsAndContinue               :   enables a data recovery operation
                        to continue despite media errors

                        disasterRecovery                    :   run disaster recovery

                lndb_restore_options    (dict)          -- dictionary for all options specific
                to an lndb restore
                    options:
                        disableReplication      :   disable relpication on database

                        disableBackgroundAgents :   disable background agents

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        if not (isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if common_options_dict is None:
            common_options_dict = {}

        if lndb_restore_options is None:
            lndb_restore_options = {}

        paths = self._filter_paths(paths)

        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        self._backupset_object._instance_object._restore_association = self._subClientEntity

        request_json = self._restore_json(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            common_options_dict=common_options_dict,
            lndb_restore_options=lndb_restore_options)

        return self._process_restore_response(request_json)

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               schedule_pattern=None):

        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

                    Args:
                        backup_level        (str)   --  level of backup the user wish to run

                            Full / Incremental / Differential / Synthetic_full

                        incremental_backup  (bool)  --  run incremental backup

                            only applicable in case of Synthetic_full backup

                        incremental_level   (str)   --  run incremental backup before/after
                        synthetic full

                            BEFORE_SYNTH / AFTER_SYNTH

                            only applicable in case of Synthetic_full backup

                        schedule_pattern (dict) -- scheduling options to be included for the task

                            Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                    Returns:
                        dict    -   JSON request to pass to the API

        &#34;&#34;&#34;

        if schedule_pattern:
            request_json = self._backup_json(
                backup_level,
                incremental_backup,
                incremental_level,
                schedule_pattern=schedule_pattern)

            backup_service = self._services[&#39;CREATE_TASK&#39;]

            flag, response = self._commcell_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )

        else:
            return super(LNDbSubclient, self).backup(
                backup_level=backup_level,
                incremental_backup=incremental_backup,
                incremental_level=incremental_level)

        return self._process_backup_response(flag, response)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.lndbsubclient.LNDbSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate content from the Subclient relevant to the user.</p>
<h2 id="returns">Returns</h2>
<p>list - list of content associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/lndbsubclient.py#L86-L93" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

        Returns:
            list - list of content associated with the subclient
    &#34;&#34;&#34;
    return self._content</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.lndbsubclient.LNDbSubclient.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, backup_level='Incremental', incremental_backup=False, incremental_level='BEFORE_SYNTH', schedule_pattern=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the JSON request to pass to the API as per the options selected by the user.</p>
<h2 id="args">Args</h2>
<p>backup_level
(str)
&ndash;
level of backup the user wish to run</p>
<pre><code>Full / Incremental / Differential / Synthetic_full
</code></pre>
<p>incremental_backup
(bool)
&ndash;
run incremental backup</p>
<pre><code>only applicable in case of Synthetic_full backup
</code></pre>
<p>incremental_level
(str)
&ndash;
run incremental backup before/after
synthetic full</p>
<pre><code>BEFORE_SYNTH / AFTER_SYNTH

only applicable in case of Synthetic_full backup
</code></pre>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>Please refer schedules.schedulePattern.createSchedule()
                                        doc for the types of Jsons
</code></pre>
<h2 id="returns">Returns</h2>
<p>dict
-
JSON request to pass to the API</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/lndbsubclient.py#L386-L439" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self,
           backup_level=&#34;Incremental&#34;,
           incremental_backup=False,
           incremental_level=&#39;BEFORE_SYNTH&#39;,
           schedule_pattern=None):

    &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

                Args:
                    backup_level        (str)   --  level of backup the user wish to run

                        Full / Incremental / Differential / Synthetic_full

                    incremental_backup  (bool)  --  run incremental backup

                        only applicable in case of Synthetic_full backup

                    incremental_level   (str)   --  run incremental backup before/after
                    synthetic full

                        BEFORE_SYNTH / AFTER_SYNTH

                        only applicable in case of Synthetic_full backup

                    schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

                Returns:
                    dict    -   JSON request to pass to the API

    &#34;&#34;&#34;

    if schedule_pattern:
        request_json = self._backup_json(
            backup_level,
            incremental_backup,
            incremental_level,
            schedule_pattern=schedule_pattern)

        backup_service = self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._commcell_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )

    else:
        return super(LNDbSubclient, self).backup(
            backup_level=backup_level,
            incremental_backup=incremental_backup,
            incremental_level=incremental_level)

    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.lndbsubclient.LNDbSubclient.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, common_options_dict=None, lndb_restore_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of full paths of files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore</p>
<pre><code>default: True
</code></pre>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files</p>
<pre><code>default: True
</code></pre>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy</p>
<pre><code>default: None
</code></pre>
<p>from_time
(str)
&ndash;
time to retore the contents after</p>
<pre><code>    format: YYYY-MM-DD HH:MM:SS

default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before</p>
<pre><code>    format: YYYY-MM-DD HH:MM:SS

default: None
</code></pre>
<p>common_options_dict (dict)
&ndash; dictionary for all the common options
options:
unconditionalOverwrite
:
overwrite the files during restore
even if they exist</p>
<pre><code>    recoverWait                         :   Specifies whether this restore
    operation must wait until resources become available if a database recovery
    is already taking place

    recoverZap                          :   Specifies whether the IBM Domino
    must change the DBIID associated with the restored database

    recoverZapReplica                   :   Specifies whether the restore
    operation changes the replica id of the restored database

    recoverZapIfNecessary               :   Specifies whether the IBM Domino
    can change the DBIID associated with the restored database if necessary

    doNotReplayTransactLogs             :   option to skip restoring or
    replaying logs


Disaster Recovery special options:
    skipErrorsAndContinue               :   enables a data recovery operation
    to continue despite media errors

    disasterRecovery                    :   run disaster recovery
</code></pre>
<p>lndb_restore_options
(dict)
&ndash; dictionary for all options specific
to an lndb restore
options:
disableReplication
:
disable relpication on database</p>
<pre><code>    disableBackgroundAgents :   disable background agents
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/lndbsubclient.py#L133-L251" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        common_options_dict=None,
        lndb_restore_options=None):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

        Args:
            paths                   (list)  --  list of full paths of files/folders to restore

            overwrite               (bool)  --  unconditional overwrite files during restore

                default: True

            restore_data_and_acl    (bool)  --  restore data and ACL files

                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy

                default: None

            from_time           (str)       --  time to retore the contents after

                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to retore the contents before

                    format: YYYY-MM-DD HH:MM:SS

                default: None

            common_options_dict (dict)          -- dictionary for all the common options
                options:
                    unconditionalOverwrite              :   overwrite the files during restore
                    even if they exist

                    recoverWait                         :   Specifies whether this restore
                    operation must wait until resources become available if a database recovery
                    is already taking place

                    recoverZap                          :   Specifies whether the IBM Domino
                    must change the DBIID associated with the restored database

                    recoverZapReplica                   :   Specifies whether the restore
                    operation changes the replica id of the restored database

                    recoverZapIfNecessary               :   Specifies whether the IBM Domino
                    can change the DBIID associated with the restored database if necessary

                    doNotReplayTransactLogs             :   option to skip restoring or
                    replaying logs


                Disaster Recovery special options:
                    skipErrorsAndContinue               :   enables a data recovery operation
                    to continue despite media errors

                    disasterRecovery                    :   run disaster recovery

            lndb_restore_options    (dict)          -- dictionary for all options specific
            to an lndb restore
                options:
                    disableReplication      :   disable relpication on database

                    disableBackgroundAgents :   disable background agents


        Returns:
            object  -   instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not (isinstance(paths, list) and
            isinstance(overwrite, bool) and
            isinstance(restore_data_and_acl, bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if common_options_dict is None:
        common_options_dict = {}

    if lndb_restore_options is None:
        lndb_restore_options = {}

    paths = self._filter_paths(paths)

    if paths == []:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

    self._backupset_object._instance_object._restore_association = self._subClientEntity

    request_json = self._restore_json(
        paths=paths,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        common_options_dict=common_options_dict,
        lndb_restore_options=lndb_restore_options
    )

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.lndbsubclient.LNDbSubclient.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, client, destination_path, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, common_options_dict=None, lndb_restore_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destionation location.</p>
<h2 id="args">Args</h2>
<p>client
(str/object) &ndash;
either the name of the client or
the instance of the Client</p>
<p>destination_path
(str)
&ndash;
full path of the restore location on client</p>
<p>paths
(list)
&ndash;
list of full paths of
files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore</p>
<pre><code>default: True
</code></pre>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files</p>
<pre><code>default: True
</code></pre>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy</p>
<pre><code>default: None
</code></pre>
<p>from_time
(str)
&ndash;
time to retore the contents after</p>
<pre><code>    format: YYYY-MM-DD HH:MM:SS

default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before</p>
<pre><code>    format: YYYY-MM-DD HH:MM:SS

default: None
</code></pre>
<p>common_options_dict (dict)
&ndash; dictionary for all the common options
options:
unconditionalOverwrite
:
overwrite the files during restore
even if they exist</p>
<pre><code>    recoverWait                         :   Specifies whether this restore
    operation must wait until resources become available if a database recovery
    is already taking place

    recoverZap                          :   Specifies whether the IBM Domino
    must change the DBIID associated with the restored database

    recoverZapReplica                   :   Specifies whether the restore
    operation changes the replica id of the restored database

    recoverZapIfNecessary               :   Specifies whether the IBM Domino
    can change the DBIID associated with the restored database if necessary

    doNotReplayTransactLogs             :   option to skip restoring or
    replaying logs


Disaster Recovery special options:
    skipErrorsAndContinue               :   enables a data recovery operation
    to continue despite media errors

    disasterRecovery                    :   run disaster recovery
</code></pre>
<p>lndb_restore_options
(dict)
&ndash; dictionary for all options specific
to an lndb restore
options:
disableReplication
:
disable relpication on database</p>
<pre><code>    disableBackgroundAgents :   disable background agents
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client instance</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/lndbsubclient.py#L253-L384" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        client,
        destination_path,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        common_options_dict=None,
        lndb_restore_options=None):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destionation location.

        Args:
            client                (str/object) --  either the name of the client or
            the instance of the Client

            destination_path      (str)        --  full path of the restore location on client

            paths                 (list)       --  list of full paths of
            files/folders to restore

            overwrite             (bool)       --  unconditional overwrite files during restore

                default: True

            restore_data_and_acl  (bool)       --  restore data and ACL files

                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy

                default: None

            from_time           (str)       --  time to retore the contents after

                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to retore the contents before

                    format: YYYY-MM-DD HH:MM:SS

                default: None

            common_options_dict (dict)          -- dictionary for all the common options
                options:
                    unconditionalOverwrite              :   overwrite the files during restore
                    even if they exist

                    recoverWait                         :   Specifies whether this restore
                    operation must wait until resources become available if a database recovery
                    is already taking place

                    recoverZap                          :   Specifies whether the IBM Domino
                    must change the DBIID associated with the restored database

                    recoverZapReplica                   :   Specifies whether the restore
                    operation changes the replica id of the restored database

                    recoverZapIfNecessary               :   Specifies whether the IBM Domino
                    can change the DBIID associated with the restored database if necessary

                    doNotReplayTransactLogs             :   option to skip restoring or
                    replaying logs


                Disaster Recovery special options:
                    skipErrorsAndContinue               :   enables a data recovery operation
                    to continue despite media errors

                    disasterRecovery                    :   run disaster recovery

            lndb_restore_options    (dict)          -- dictionary for all options specific
            to an lndb restore
                options:
                    disableReplication      :   disable relpication on database

                    disableBackgroundAgents :   disable background agents

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or Client instance

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    if not (isinstance(paths, list) and
            isinstance(overwrite, bool) and
            isinstance(restore_data_and_acl, bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if common_options_dict is None:
        common_options_dict = {}

    if lndb_restore_options is None:
        lndb_restore_options = {}

    paths = self._filter_paths(paths)

    if paths == []:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

    self._backupset_object._instance_object._restore_association = self._subClientEntity

    request_json = self._restore_json(
        client=client,
        destination_path=destination_path,
        paths=paths,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        common_options_dict=common_options_dict,
        lndb_restore_options=lndb_restore_options)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclient.Subclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.lndbsubclient.LNDbSubclient" href="#cvpysdk.subclients.lndbsubclient.LNDbSubclient">LNDbSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.lndbsubclient.LNDbSubclient.backup" href="#cvpysdk.subclients.lndbsubclient.LNDbSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.lndbsubclient.LNDbSubclient.content" href="#cvpysdk.subclients.lndbsubclient.LNDbSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.lndbsubclient.LNDbSubclient.restore_in_place" href="#cvpysdk.subclients.lndbsubclient.LNDbSubclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.lndbsubclient.LNDbSubclient.restore_out_of_place" href="#cvpysdk.subclients.lndbsubclient.LNDbSubclient.restore_out_of_place">restore_out_of_place</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>