from cvpysdk.commcell import Commcell

commcell = Commcell('192.168.100.55', 'admin', 'P@ssw0rd', verify_ssl=False)



# client = commcell.clients.get('alpha') # Get the client object
# agent = client.agents.get('Virtual Server') # Get the agent object
# backupsets = agent.backupsets.get('defaultBackupSet') # Get the backupset object
# print(backupsets) # Print the backupset object


# def main():
#     # print(commcell.auth_token)
    

#     # Content = cvpysdk.subclients.virtualserver.vmware.VMWareVirtualServerSubclient(backupset_object=backupsets,subclient_name='<Subclient Name>')
#     # print(Content.preview_content())

#     # client = (commcell.clients)
#     # print(commcell.plans)


print(commcell.auth_token)
print(commcell.commserv_name)
print(commcell.commserv_version)
