<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.dashboard.ad_dashboard API documentation</title>
<meta name="description" content="File for performing Ad agent dashboard operation …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.dashboard.ad_dashboard</code></h1>
</header>
<section id="section-intro">
<p>File for performing Ad agent dashboard operation.</p>
<p>AdDashboard class is defined in this file.</p>
<p>AdDashboard:
Class for performing Ad agent dashboard operation.</p>
<h2 id="class">Class</h2>
<p>AdDashboard :</p>
<pre><code>__init__(self,commcell_object)         --    initialize object of AdDashboard class associated with the commcell.

get_ad_dashboard_details               --    get the dashboard details from Dashboard API.

get_ad_apps_details                    --    get the app listing details from App Listing API.

configured                          --    return whether both AD and Azure AD are configured or not from Dashboard API and App Listing API.

_get_domains_and_tenants                    --    return number of domains and tenants from Dashboard API and App Listing API.

_get_backup_health                          --    return backup health panel details from Dashboard API and App Listing API.

_get_data_distribution                      --    return data distribution panel details from Dashboard API and App Listing API.

_get_application_panel                      --    return application panel details from Dashboard API and App Listing API.
</code></pre>
<h2 id="addashboard-attributes">Addashboard Attributes</h2>
<pre><code>**configure_dict**                          --      returns a dictionary indicating whether AD and Azure AD are configured with the commcell.

**domains_and_tenants_dict**                --      returns a dictionary containing the number of domain controllers and tenants in the commcell.

**backup_health_dict**                      --      returns a dictionary with information about the backup health panel of the AD Dashboard, including SLA met and SLA not met.

**data_distribution_dict**                  --      returns a dictionary with information about the data distribution panel of the AD Dashboard, such as backup size and backed-up objects.

**application_panel_dict**                  --      returns a dictionary with information about the application panel of the AD Dashboard, including Azure AD backup size and AD backup size.
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/dashboard/ad_dashboard.py#L1-L362" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# pylint: disable=R1705, R0205
# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
File for performing Ad agent dashboard operation.

AdDashboard class is defined in this file.

AdDashboard:    Class for performing Ad agent dashboard operation.

Class:
    AdDashboard :

        __init__(self,commcell_object)         --    initialize object of AdDashboard class associated with the commcell.

        get_ad_dashboard_details               --    get the dashboard details from Dashboard API.

        get_ad_apps_details                    --    get the app listing details from App Listing API.

        configured                          --    return whether both AD and Azure AD are configured or not from Dashboard API and App Listing API.

        _get_domains_and_tenants                    --    return number of domains and tenants from Dashboard API and App Listing API.

        _get_backup_health                          --    return backup health panel details from Dashboard API and App Listing API.

        _get_data_distribution                      --    return data distribution panel details from Dashboard API and App Listing API.

        _get_application_panel                      --    return application panel details from Dashboard API and App Listing API.

AdDashboard Attributes
----------------------
    **configure_dict**                          --      returns a dictionary indicating whether AD and Azure AD are configured with the commcell.

    **domains_and_tenants_dict**                --      returns a dictionary containing the number of domain controllers and tenants in the commcell.

    **backup_health_dict**                      --      returns a dictionary with information about the backup health panel of the AD Dashboard, including SLA met and SLA not met.

    **data_distribution_dict**                  --      returns a dictionary with information about the data distribution panel of the AD Dashboard, such as backup size and backed-up objects.

    **application_panel_dict**                  --      returns a dictionary with information about the application panel of the AD Dashboard, including Azure AD backup size and AD backup size.
&#34;&#34;&#34;

from cvpysdk.exception import SDKException

class AdDashboard(object):
    &#34;&#34;&#34;
    Class for AD Dashboard Details
    &#34;&#34;&#34;

    def __init__(self,commcell_object):
        &#34;&#34;&#34;Initialize object of the Clients class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Clients class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self.dashboard_response = None
        self.azure_ad_response = None
        self.apps_response = None
        self.apps_totalentities = None

    def get_ad_dashboard_details(self):
        &#34;&#34;&#34;
        REST API call to get AD Dashboard details in the commcell
        Raises:
            SDKException:

                if response is empty

                if response is not success
        &#34;&#34;&#34;
        configured = self._services[&#39;ADDASHBOARD&#39;] + &#39;?slaNumberOfDays=1&#39;
        flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=configured)
        if flag and response:
            self.dashboard_response = response.json()
        elif not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, self._commcell_object._update_response_(response.text))

    def get_ad_apps_details(self):
        &#34;&#34;&#34;
        REST API call to get AD APP Listing details in the commcell
        Raises:
            SDKException:

                if response is empty

                if response is not success
        &#34;&#34;&#34;
        configured = self._services[&#39;ADAPPS&#39;]
        flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=configured)

        if flag and response:
            self.apps_response = response.json()
        elif not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, self._commcell_object._update_response_(response.text))

    def _configured(self):
        &#34;&#34;&#34;
        Function check both AD and Azure AD are configured
        Returns:
            configure_dict    --  Configuration value of AD and Azure AD from Dashboard API and Apps Listing API as dict
        &#34;&#34;&#34;
        configure_dict = {&#34;adconfigure&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;isConfigured&#39;, None),
                          &#34;aadconfigure&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;isConfigured&#39;,None),
                          &#34;apps_adconfigure&#34;: False,
                          &#34;apps_aadconfigure&#34;: False}
        for i in range(self.apps_response.get(&#39;totalADClients&#39;, None)):
            if self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;isConfigured&#39;) and \
                    self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;appTypeId&#39;) == 41:
                configure_dict[&#34;apps_adconfigure&#34;] = True
                break

        for i in range(self.apps_response.get(&#39;totalADClients&#39;, None)):
            if self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;isConfigured&#39;) and \
                    self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;appTypeId&#39;) == 139:
                configure_dict[&#34;apps_aadconfigure&#34;] = True
                break

        return configure_dict

    @property
    def is_configured(self):
        &#34;&#34;&#34;
        Returns:
            configure_dict    --  Configuration value of AD and Azure AD from Dashboard API and Apps Listing API as dict
        &#34;&#34;&#34;
        configure_dict=self._configured()
        return configure_dict

    def _get_domains_and_tenants(self):
        &#34;&#34;&#34;
        Function to get number of domains and tenants
        Returns:
            domains_and_tenants_dict    --  Number of domain controllers and tenants from Dashboard API and Apps Listing API as dict
        &#34;&#34;&#34;
        domains_and_tenants_dict = {
            &#34;total_entities&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;totalEntities&#39;),
            &#34;domain_controllers&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;slaSummary&#39;, {}).get(
                &#39;totalEntities&#39;),
            &#34;tenants&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;slaSummary&#39;, {}).get(&#39;totalEntities&#39;),
            &#34;apps_totalentities&#34;: 0,
            &#34;apps_domain_controllers&#34;: 0,
            &#34;apps_tenants&#34;: 0
            }
        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 41 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                domains_and_tenants_dict[&#34;apps_domain_controllers&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 139 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                domains_and_tenants_dict[&#34;apps_tenants&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                domains_and_tenants_dict[&#34;apps_totalentities&#34;] += 1
        return domains_and_tenants_dict

    @property
    def domains_and_tenants(self):
        &#34;&#34;&#34;
        Returns:
            domains_and_tenants_dict    --  Number of domain controllers and tenants from Dashboard API and Apps Listing API as dict
        &#34;&#34;&#34;
        return self._get_domains_and_tenants()

    def _get_backup_health(self):
        &#34;&#34;&#34;
        Function to get backup health panel details
        Returns:
            backup_health_dict    --  Backup health panel details as dict
        &#34;&#34;&#34;
        backup_health_dict = {
            &#34;recently_backedup&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;totalEntities&#39;, 0) - self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;slaNotMetEntities&#39;, 0),
            &#34;recently_backedup_per&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;slaMetPercentage&#39;, 0),
            &#34;recently_not_backedup&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;slaNotMetEntities&#39;, 0) - self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;neverBackedupEntities&#39;, 0),
            &#34;recently_not_backedup_per&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;slaNotMetProcessedAtleastOncePercentage&#39;, 0),
            &#34;never_backedup&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;neverBackedupEntities&#39;, 0),
            &#34;never_backedup_per&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;neverBackedupPercentage&#39;, 0),
            &#34;apps_recently_backedup&#34;: 0,
            &#34;apps_recently_not_backedup&#34;: 0,
            &#34;apps_never_backedup&#34;: 0,
            &#34;apps_totalentities&#34;: 0,
            &#34;apps_recently_backedup_per&#34;: 0,
            &#34;apps_recently_not_backedup_per&#34;: 0,
            &#34;apps_never_backedup_per&#34;: 0
            }
        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MET_SLA&#34;:
                backup_health_dict[&#34;apps_recently_backedup&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MISSED_SLA&#34; and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;numberOfItems&#39;) != 0:
                backup_health_dict[&#34;apps_recently_not_backedup&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MISSED_SLA&#34; and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;numberOfItems&#39;) == 0:
                backup_health_dict[&#34;apps_never_backedup&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                backup_health_dict[&#34;apps_totalentities&#34;] += 1

        backup_health_dict[&#34;apps_recently_backedup_per&#34;] = (
            round(((backup_health_dict[&#34;apps_recently_backedup&#34;] / backup_health_dict[&#34;apps_totalentities&#34;]) * 100), 2))
        backup_health_dict[&#34;apps_recently_not_backedup_per&#34;] = (
            round(((backup_health_dict[&#34;apps_recently_not_backedup&#34;] / backup_health_dict[&#34;apps_totalentities&#34;]) * 100),
                  2))
        backup_health_dict[&#34;apps_never_backedup_per&#34;] = (
            round(((backup_health_dict[&#34;apps_never_backedup&#34;] / backup_health_dict[&#34;apps_totalentities&#34;]) * 100), 2))

        return backup_health_dict

    @property
    def backup_health(self):
        &#34;&#34;&#34;
        Returns:
            backup_health_dict    --  Backup health panel details as dict
        &#34;&#34;&#34;
        return self._get_backup_health()


    def _get_data_distribution(self):
        &#34;&#34;&#34;
        Function to get data distribution panel details
        Returns:
            data_distribution_dict    --  Data distribution details data as dict
        &#34;&#34;&#34;
        data_distribution_dict = {&#34;backup_size&#34;: round((((self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;applicationSize&#39;, 0) +
                                                          self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;applicationSize&#39;,0))
                                                         / 1024) / 1024), 2),
                                  &#34;backup_obj&#34;: (
                                              self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;numberOfItems&#39;, 0) +
                                              self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;numberOfItems&#39;,0)),
                                  &#34;apps_backup_size&#34;: 0,
                                  &#34;apps_backup_obj&#34;: 0,
                                  }

        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            data_distribution_dict[&#34;apps_backup_size&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;applicationSize&#39;, None)

        data_distribution_dict[&#34;apps_backup_size&#34;] = round(((data_distribution_dict[&#34;apps_backup_size&#34;] / 1024) / 1024), 2)

        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            data_distribution_dict[&#34;apps_backup_obj&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;numberOfItems&#39;, None)

        return data_distribution_dict

    @property
    def data_distribution(self):
        &#34;&#34;&#34;
        Returns:
            data_distribution_dict    --  Data distribution details data as dict
        &#34;&#34;&#34;
        return self._get_data_distribution()

    def _get_application_panel(self):
        &#34;&#34;&#34;
        Function to get application panel details
        Returns:
            application_panel_dict    --  Application panel details as dict
        &#34;&#34;&#34;
        application_panel_dict = {
            &#34;aad_tenant&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;slaSummary&#39;, {}).get(&#39;totalEntities&#39;),
            &#34;aad_backup_size&#34;: round((((self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;applicationSize&#39;)) / 1024) / 1024), 2),
            &#34;aad_backup_obj&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;numberOfItems&#39;),
            &#34;aad_sla_per&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;slaSummary&#39;, {}).get(&#39;slaMetPercentage&#39;),
            &#34;aad_not_sla_per&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;slaSummary&#39;, {}).get(&#39;slaNotMetProcessedAtleastOncePercentage&#39;),

            &#34;ad_domains&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;slaSummary&#39;, {}).get(&#39;totalEntities&#39;),
            &#34;ad_backup_size&#34;: round((((self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;applicationSize&#39;)) / 1024) / 1024), 2),
            &#34;ad_backup_obj&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;numberOfItems&#39;),
            &#34;ad_sla_per&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;slaSummary&#39;, {}).get(&#39;slaMetPercentage&#39;),
            &#34;ad_not_sla_per&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;slaSummary&#39;, {}).get(&#39;slaNotMetProcessedAtleastOncePercentage&#39;),

            &#34;apps_aad_tenant&#34;: 0, &#34;apps_aad_backup_size&#34;: 0, &#34;apps_aad_backup_obj&#34;: 0,
            &#34;apps_aad_sla_per&#34;: 0,&#34;apps_aad_not_sla_per&#34;: 0,
            &#34;apps_ad_domains&#34;: 0, &#34;apps_ad_backup_size&#34;: 0, &#34;apps_ad_backup_obj&#34;: 0,
            &#34;apps_ad_sla_per&#34;: 0,&#34;apps_ad_not_sla_per&#34;: 0,
            &#34;apps_aad_sla&#34;: 0, &#34;apps_ad_sla&#34;: 0}

        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 139 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                application_panel_dict[&#34;apps_aad_tenant&#34;] += 1

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 139:
                application_panel_dict[&#34;apps_aad_backup_size&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(
                    &#39;applicationSize&#39;)
                application_panel_dict[&#34;apps_aad_backup_obj&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(
                    &#39;numberOfItems&#39;)

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 41 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                application_panel_dict[&#34;apps_ad_domains&#34;] += 1

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 41:
                application_panel_dict[&#34;apps_ad_backup_size&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(
                    &#39;applicationSize&#39;)
                application_panel_dict[&#34;apps_ad_backup_obj&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(
                    &#39;numberOfItems&#39;)

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 139 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MET_SLA&#34;:
                application_panel_dict[&#34;apps_aad_sla&#34;] += 1

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 41 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MET_SLA&#34;:
                application_panel_dict[&#34;apps_ad_sla&#34;] += 1

        application_panel_dict[&#34;apps_aad_backup_size&#34;] = round(
            ((application_panel_dict[&#34;apps_aad_backup_size&#34;] / 1024) / 1024), 2)
        application_panel_dict[&#34;apps_ad_backup_size&#34;] = round(
            ((application_panel_dict[&#34;apps_ad_backup_size&#34;] / 1024) / 1024), 2)

        application_panel_dict[&#34;apps_aad_sla_per&#34;] = (
            round(((application_panel_dict[&#34;apps_aad_sla&#34;] / application_panel_dict[&#34;apps_aad_tenant&#34;]) * 100), 2))
        application_panel_dict[&#34;apps_ad_sla_per&#34;] = (
            round(((application_panel_dict[&#34;apps_ad_sla&#34;] / application_panel_dict[&#34;apps_ad_domains&#34;]) * 100), 2))

        application_panel_dict[&#34;apps_aad_not_sla_per&#34;] = (round((((application_panel_dict[&#34;apps_aad_tenant&#34;] -
                                                                   application_panel_dict[&#34;apps_aad_sla&#34;]) /
                                                                  application_panel_dict[&#34;apps_aad_tenant&#34;]) * 100), 2))
        application_panel_dict[&#34;apps_ad_not_sla_per&#34;] = (round((((application_panel_dict[&#34;apps_ad_domains&#34;] -
                                                                  application_panel_dict[&#34;apps_ad_sla&#34;]) /
                                                                 application_panel_dict[&#34;apps_ad_domains&#34;]) * 100), 2))

        return application_panel_dict

    @property
    def application_panel(self):
        &#34;&#34;&#34;
        Returns:
            application_panel_dict    --  Application panel details as dict
        &#34;&#34;&#34;
        return self._get_application_panel()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.dashboard.ad_dashboard.AdDashboard"><code class="flex name class">
<span>class <span class="ident">AdDashboard</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for AD Dashboard Details</p>
<p>Initialize object of the Clients class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Clients class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/dashboard/ad_dashboard.py#L60-L362" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class AdDashboard(object):
    &#34;&#34;&#34;
    Class for AD Dashboard Details
    &#34;&#34;&#34;

    def __init__(self,commcell_object):
        &#34;&#34;&#34;Initialize object of the Clients class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Clients class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self.dashboard_response = None
        self.azure_ad_response = None
        self.apps_response = None
        self.apps_totalentities = None

    def get_ad_dashboard_details(self):
        &#34;&#34;&#34;
        REST API call to get AD Dashboard details in the commcell
        Raises:
            SDKException:

                if response is empty

                if response is not success
        &#34;&#34;&#34;
        configured = self._services[&#39;ADDASHBOARD&#39;] + &#39;?slaNumberOfDays=1&#39;
        flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=configured)
        if flag and response:
            self.dashboard_response = response.json()
        elif not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, self._commcell_object._update_response_(response.text))

    def get_ad_apps_details(self):
        &#34;&#34;&#34;
        REST API call to get AD APP Listing details in the commcell
        Raises:
            SDKException:

                if response is empty

                if response is not success
        &#34;&#34;&#34;
        configured = self._services[&#39;ADAPPS&#39;]
        flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=configured)

        if flag and response:
            self.apps_response = response.json()
        elif not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, self._commcell_object._update_response_(response.text))

    def _configured(self):
        &#34;&#34;&#34;
        Function check both AD and Azure AD are configured
        Returns:
            configure_dict    --  Configuration value of AD and Azure AD from Dashboard API and Apps Listing API as dict
        &#34;&#34;&#34;
        configure_dict = {&#34;adconfigure&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;isConfigured&#39;, None),
                          &#34;aadconfigure&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;isConfigured&#39;,None),
                          &#34;apps_adconfigure&#34;: False,
                          &#34;apps_aadconfigure&#34;: False}
        for i in range(self.apps_response.get(&#39;totalADClients&#39;, None)):
            if self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;isConfigured&#39;) and \
                    self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;appTypeId&#39;) == 41:
                configure_dict[&#34;apps_adconfigure&#34;] = True
                break

        for i in range(self.apps_response.get(&#39;totalADClients&#39;, None)):
            if self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;isConfigured&#39;) and \
                    self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;appTypeId&#39;) == 139:
                configure_dict[&#34;apps_aadconfigure&#34;] = True
                break

        return configure_dict

    @property
    def is_configured(self):
        &#34;&#34;&#34;
        Returns:
            configure_dict    --  Configuration value of AD and Azure AD from Dashboard API and Apps Listing API as dict
        &#34;&#34;&#34;
        configure_dict=self._configured()
        return configure_dict

    def _get_domains_and_tenants(self):
        &#34;&#34;&#34;
        Function to get number of domains and tenants
        Returns:
            domains_and_tenants_dict    --  Number of domain controllers and tenants from Dashboard API and Apps Listing API as dict
        &#34;&#34;&#34;
        domains_and_tenants_dict = {
            &#34;total_entities&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;totalEntities&#39;),
            &#34;domain_controllers&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;slaSummary&#39;, {}).get(
                &#39;totalEntities&#39;),
            &#34;tenants&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;slaSummary&#39;, {}).get(&#39;totalEntities&#39;),
            &#34;apps_totalentities&#34;: 0,
            &#34;apps_domain_controllers&#34;: 0,
            &#34;apps_tenants&#34;: 0
            }
        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 41 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                domains_and_tenants_dict[&#34;apps_domain_controllers&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 139 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                domains_and_tenants_dict[&#34;apps_tenants&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                domains_and_tenants_dict[&#34;apps_totalentities&#34;] += 1
        return domains_and_tenants_dict

    @property
    def domains_and_tenants(self):
        &#34;&#34;&#34;
        Returns:
            domains_and_tenants_dict    --  Number of domain controllers and tenants from Dashboard API and Apps Listing API as dict
        &#34;&#34;&#34;
        return self._get_domains_and_tenants()

    def _get_backup_health(self):
        &#34;&#34;&#34;
        Function to get backup health panel details
        Returns:
            backup_health_dict    --  Backup health panel details as dict
        &#34;&#34;&#34;
        backup_health_dict = {
            &#34;recently_backedup&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;totalEntities&#39;, 0) - self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;slaNotMetEntities&#39;, 0),
            &#34;recently_backedup_per&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;slaMetPercentage&#39;, 0),
            &#34;recently_not_backedup&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;slaNotMetEntities&#39;, 0) - self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;neverBackedupEntities&#39;, 0),
            &#34;recently_not_backedup_per&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;slaNotMetProcessedAtleastOncePercentage&#39;, 0),
            &#34;never_backedup&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;neverBackedupEntities&#39;, 0),
            &#34;never_backedup_per&#34;: self.dashboard_response.get(&#39;solutionSummary&#39;, {}).get(&#39;slaSummary&#39;, {}).get(
                &#39;neverBackedupPercentage&#39;, 0),
            &#34;apps_recently_backedup&#34;: 0,
            &#34;apps_recently_not_backedup&#34;: 0,
            &#34;apps_never_backedup&#34;: 0,
            &#34;apps_totalentities&#34;: 0,
            &#34;apps_recently_backedup_per&#34;: 0,
            &#34;apps_recently_not_backedup_per&#34;: 0,
            &#34;apps_never_backedup_per&#34;: 0
            }
        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MET_SLA&#34;:
                backup_health_dict[&#34;apps_recently_backedup&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MISSED_SLA&#34; and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;numberOfItems&#39;) != 0:
                backup_health_dict[&#34;apps_recently_not_backedup&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MISSED_SLA&#34; and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;numberOfItems&#39;) == 0:
                backup_health_dict[&#34;apps_never_backedup&#34;] += 1
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                backup_health_dict[&#34;apps_totalentities&#34;] += 1

        backup_health_dict[&#34;apps_recently_backedup_per&#34;] = (
            round(((backup_health_dict[&#34;apps_recently_backedup&#34;] / backup_health_dict[&#34;apps_totalentities&#34;]) * 100), 2))
        backup_health_dict[&#34;apps_recently_not_backedup_per&#34;] = (
            round(((backup_health_dict[&#34;apps_recently_not_backedup&#34;] / backup_health_dict[&#34;apps_totalentities&#34;]) * 100),
                  2))
        backup_health_dict[&#34;apps_never_backedup_per&#34;] = (
            round(((backup_health_dict[&#34;apps_never_backedup&#34;] / backup_health_dict[&#34;apps_totalentities&#34;]) * 100), 2))

        return backup_health_dict

    @property
    def backup_health(self):
        &#34;&#34;&#34;
        Returns:
            backup_health_dict    --  Backup health panel details as dict
        &#34;&#34;&#34;
        return self._get_backup_health()


    def _get_data_distribution(self):
        &#34;&#34;&#34;
        Function to get data distribution panel details
        Returns:
            data_distribution_dict    --  Data distribution details data as dict
        &#34;&#34;&#34;
        data_distribution_dict = {&#34;backup_size&#34;: round((((self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;applicationSize&#39;, 0) +
                                                          self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;applicationSize&#39;,0))
                                                         / 1024) / 1024), 2),
                                  &#34;backup_obj&#34;: (
                                              self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;numberOfItems&#39;, 0) +
                                              self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;numberOfItems&#39;,0)),
                                  &#34;apps_backup_size&#34;: 0,
                                  &#34;apps_backup_obj&#34;: 0,
                                  }

        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            data_distribution_dict[&#34;apps_backup_size&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;applicationSize&#39;, None)

        data_distribution_dict[&#34;apps_backup_size&#34;] = round(((data_distribution_dict[&#34;apps_backup_size&#34;] / 1024) / 1024), 2)

        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            data_distribution_dict[&#34;apps_backup_obj&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(&#39;numberOfItems&#39;, None)

        return data_distribution_dict

    @property
    def data_distribution(self):
        &#34;&#34;&#34;
        Returns:
            data_distribution_dict    --  Data distribution details data as dict
        &#34;&#34;&#34;
        return self._get_data_distribution()

    def _get_application_panel(self):
        &#34;&#34;&#34;
        Function to get application panel details
        Returns:
            application_panel_dict    --  Application panel details as dict
        &#34;&#34;&#34;
        application_panel_dict = {
            &#34;aad_tenant&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;slaSummary&#39;, {}).get(&#39;totalEntities&#39;),
            &#34;aad_backup_size&#34;: round((((self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;applicationSize&#39;)) / 1024) / 1024), 2),
            &#34;aad_backup_obj&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;numberOfItems&#39;),
            &#34;aad_sla_per&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;slaSummary&#39;, {}).get(&#39;slaMetPercentage&#39;),
            &#34;aad_not_sla_per&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[1].get(&#39;slaSummary&#39;, {}).get(&#39;slaNotMetProcessedAtleastOncePercentage&#39;),

            &#34;ad_domains&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;slaSummary&#39;, {}).get(&#39;totalEntities&#39;),
            &#34;ad_backup_size&#34;: round((((self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;applicationSize&#39;)) / 1024) / 1024), 2),
            &#34;ad_backup_obj&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;numberOfItems&#39;),
            &#34;ad_sla_per&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;slaSummary&#39;, {}).get(&#39;slaMetPercentage&#39;),
            &#34;ad_not_sla_per&#34;: self.dashboard_response.get(&#39;agentSummary&#39;, [{}])[0].get(&#39;slaSummary&#39;, {}).get(&#39;slaNotMetProcessedAtleastOncePercentage&#39;),

            &#34;apps_aad_tenant&#34;: 0, &#34;apps_aad_backup_size&#34;: 0, &#34;apps_aad_backup_obj&#34;: 0,
            &#34;apps_aad_sla_per&#34;: 0,&#34;apps_aad_not_sla_per&#34;: 0,
            &#34;apps_ad_domains&#34;: 0, &#34;apps_ad_backup_size&#34;: 0, &#34;apps_ad_backup_obj&#34;: 0,
            &#34;apps_ad_sla_per&#34;: 0,&#34;apps_ad_not_sla_per&#34;: 0,
            &#34;apps_aad_sla&#34;: 0, &#34;apps_ad_sla&#34;: 0}

        for i in range(len(self.apps_response[&#39;adClients&#39;])):
            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 139 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                application_panel_dict[&#34;apps_aad_tenant&#34;] += 1

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 139:
                application_panel_dict[&#34;apps_aad_backup_size&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(
                    &#39;applicationSize&#39;)
                application_panel_dict[&#34;apps_aad_backup_obj&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(
                    &#39;numberOfItems&#39;)

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 41 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) != &#34;EXCLUDED_SLA&#34;:
                application_panel_dict[&#34;apps_ad_domains&#34;] += 1

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 41:
                application_panel_dict[&#34;apps_ad_backup_size&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(
                    &#39;applicationSize&#39;)
                application_panel_dict[&#34;apps_ad_backup_obj&#34;] += self.apps_response.get(&#39;adClients&#39;, [{}])[i].get(
                    &#39;numberOfItems&#39;)

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 139 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MET_SLA&#34;:
                application_panel_dict[&#34;apps_aad_sla&#34;] += 1

            if self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;appTypeId&#39;) == 41 and \
                    self.apps_response.get(&#39;adClients&#39;, [])[i].get(&#39;slaStatus&#39;) == &#34;MET_SLA&#34;:
                application_panel_dict[&#34;apps_ad_sla&#34;] += 1

        application_panel_dict[&#34;apps_aad_backup_size&#34;] = round(
            ((application_panel_dict[&#34;apps_aad_backup_size&#34;] / 1024) / 1024), 2)
        application_panel_dict[&#34;apps_ad_backup_size&#34;] = round(
            ((application_panel_dict[&#34;apps_ad_backup_size&#34;] / 1024) / 1024), 2)

        application_panel_dict[&#34;apps_aad_sla_per&#34;] = (
            round(((application_panel_dict[&#34;apps_aad_sla&#34;] / application_panel_dict[&#34;apps_aad_tenant&#34;]) * 100), 2))
        application_panel_dict[&#34;apps_ad_sla_per&#34;] = (
            round(((application_panel_dict[&#34;apps_ad_sla&#34;] / application_panel_dict[&#34;apps_ad_domains&#34;]) * 100), 2))

        application_panel_dict[&#34;apps_aad_not_sla_per&#34;] = (round((((application_panel_dict[&#34;apps_aad_tenant&#34;] -
                                                                   application_panel_dict[&#34;apps_aad_sla&#34;]) /
                                                                  application_panel_dict[&#34;apps_aad_tenant&#34;]) * 100), 2))
        application_panel_dict[&#34;apps_ad_not_sla_per&#34;] = (round((((application_panel_dict[&#34;apps_ad_domains&#34;] -
                                                                  application_panel_dict[&#34;apps_ad_sla&#34;]) /
                                                                 application_panel_dict[&#34;apps_ad_domains&#34;]) * 100), 2))

        return application_panel_dict

    @property
    def application_panel(self):
        &#34;&#34;&#34;
        Returns:
            application_panel_dict    --  Application panel details as dict
        &#34;&#34;&#34;
        return self._get_application_panel()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.dashboard.ad_dashboard.AdDashboard.application_panel"><code class="name">var <span class="ident">application_panel</span></code></dt>
<dd>
<div class="desc"><h2 id="returns">Returns</h2>
<p>application_panel_dict
&ndash;
Application panel details as dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/dashboard/ad_dashboard.py#L356-L362" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def application_panel(self):
    &#34;&#34;&#34;
    Returns:
        application_panel_dict    --  Application panel details as dict
    &#34;&#34;&#34;
    return self._get_application_panel()</code></pre>
</details>
</dd>
<dt id="cvpysdk.dashboard.ad_dashboard.AdDashboard.backup_health"><code class="name">var <span class="ident">backup_health</span></code></dt>
<dd>
<div class="desc"><h2 id="returns">Returns</h2>
<p>backup_health_dict
&ndash;
Backup health panel details as dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/dashboard/ad_dashboard.py#L240-L246" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_health(self):
    &#34;&#34;&#34;
    Returns:
        backup_health_dict    --  Backup health panel details as dict
    &#34;&#34;&#34;
    return self._get_backup_health()</code></pre>
</details>
</dd>
<dt id="cvpysdk.dashboard.ad_dashboard.AdDashboard.data_distribution"><code class="name">var <span class="ident">data_distribution</span></code></dt>
<dd>
<div class="desc"><h2 id="returns">Returns</h2>
<p>data_distribution_dict
&ndash;
Data distribution details data as dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/dashboard/ad_dashboard.py#L275-L281" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_distribution(self):
    &#34;&#34;&#34;
    Returns:
        data_distribution_dict    --  Data distribution details data as dict
    &#34;&#34;&#34;
    return self._get_data_distribution()</code></pre>
</details>
</dd>
<dt id="cvpysdk.dashboard.ad_dashboard.AdDashboard.domains_and_tenants"><code class="name">var <span class="ident">domains_and_tenants</span></code></dt>
<dd>
<div class="desc"><h2 id="returns">Returns</h2>
<p>domains_and_tenants_dict
&ndash;
Number of domain controllers and tenants from Dashboard API and Apps Listing API as dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/dashboard/ad_dashboard.py#L181-L187" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def domains_and_tenants(self):
    &#34;&#34;&#34;
    Returns:
        domains_and_tenants_dict    --  Number of domain controllers and tenants from Dashboard API and Apps Listing API as dict
    &#34;&#34;&#34;
    return self._get_domains_and_tenants()</code></pre>
</details>
</dd>
<dt id="cvpysdk.dashboard.ad_dashboard.AdDashboard.is_configured"><code class="name">var <span class="ident">is_configured</span></code></dt>
<dd>
<div class="desc"><h2 id="returns">Returns</h2>
<p>configure_dict
&ndash;
Configuration value of AD and Azure AD from Dashboard API and Apps Listing API as dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/dashboard/ad_dashboard.py#L145-L152" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_configured(self):
    &#34;&#34;&#34;
    Returns:
        configure_dict    --  Configuration value of AD and Azure AD from Dashboard API and Apps Listing API as dict
    &#34;&#34;&#34;
    configure_dict=self._configured()
    return configure_dict</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.dashboard.ad_dashboard.AdDashboard.get_ad_apps_details"><code class="name flex">
<span>def <span class="ident">get_ad_apps_details</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>REST API call to get AD APP Listing details in the commcell</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/dashboard/ad_dashboard.py#L101-L119" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_ad_apps_details(self):
    &#34;&#34;&#34;
    REST API call to get AD APP Listing details in the commcell
    Raises:
        SDKException:

            if response is empty

            if response is not success
    &#34;&#34;&#34;
    configured = self._services[&#39;ADAPPS&#39;]
    flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=configured)

    if flag and response:
        self.apps_response = response.json()
    elif not flag:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
    else:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.dashboard.ad_dashboard.AdDashboard.get_ad_dashboard_details"><code class="name flex">
<span>def <span class="ident">get_ad_dashboard_details</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>REST API call to get AD Dashboard details in the commcell</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/dashboard/ad_dashboard.py#L82-L99" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_ad_dashboard_details(self):
    &#34;&#34;&#34;
    REST API call to get AD Dashboard details in the commcell
    Raises:
        SDKException:

            if response is empty

            if response is not success
    &#34;&#34;&#34;
    configured = self._services[&#39;ADDASHBOARD&#39;] + &#39;?slaNumberOfDays=1&#39;
    flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=configured)
    if flag and response:
        self.dashboard_response = response.json()
    elif not flag:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
    else:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#addashboard-attributes">AdDashboard Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.dashboard" href="index.html">cvpysdk.dashboard</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.dashboard.ad_dashboard.AdDashboard" href="#cvpysdk.dashboard.ad_dashboard.AdDashboard">AdDashboard</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.dashboard.ad_dashboard.AdDashboard.application_panel" href="#cvpysdk.dashboard.ad_dashboard.AdDashboard.application_panel">application_panel</a></code></li>
<li><code><a title="cvpysdk.dashboard.ad_dashboard.AdDashboard.backup_health" href="#cvpysdk.dashboard.ad_dashboard.AdDashboard.backup_health">backup_health</a></code></li>
<li><code><a title="cvpysdk.dashboard.ad_dashboard.AdDashboard.data_distribution" href="#cvpysdk.dashboard.ad_dashboard.AdDashboard.data_distribution">data_distribution</a></code></li>
<li><code><a title="cvpysdk.dashboard.ad_dashboard.AdDashboard.domains_and_tenants" href="#cvpysdk.dashboard.ad_dashboard.AdDashboard.domains_and_tenants">domains_and_tenants</a></code></li>
<li><code><a title="cvpysdk.dashboard.ad_dashboard.AdDashboard.get_ad_apps_details" href="#cvpysdk.dashboard.ad_dashboard.AdDashboard.get_ad_apps_details">get_ad_apps_details</a></code></li>
<li><code><a title="cvpysdk.dashboard.ad_dashboard.AdDashboard.get_ad_dashboard_details" href="#cvpysdk.dashboard.ad_dashboard.AdDashboard.get_ad_dashboard_details">get_ad_dashboard_details</a></code></li>
<li><code><a title="cvpysdk.dashboard.ad_dashboard.AdDashboard.is_configured" href="#cvpysdk.dashboard.ad_dashboard.AdDashboard.is_configured">is_configured</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>