<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activateapps.compliance_utils API documentation</title>
<meta name="description" content="Utilities class for Activate application : Compliance Search …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activateapps.compliance_utils</code></h1>
</header>
<section id="section-intro">
<p>Utilities class for Activate application : Compliance Search</p>
<p>ComplianceSearchUtils:
Class for compliance search related operations support</p>
<p>ExportSets: Class for representing all the export sets associated with the commcell</p>
<p>ExportSet:
Class for an instance of a single Export set of the commcell</p>
<p>Export: Class for an instance of a single Export of the commcell</p>
<h1 id="compliancesearchutils">ComplianceSearchUtils</h1>
<pre><code>__init__()                          --  Initializes the Compliance search utility class

_response_not_success()             --  Helper method to raise exception when response is not 200 (ok)

do_compliance_search()              --  Method to run a compliance search with the search text provided
</code></pre>
<h1 id="exportsets">ExportSets</h1>
<pre><code>__init__()                          --  Initializes the ExportSets class

_response_not_success()             --  Helper method to raise exception when response is not 200 (ok)

_get_export_sets()                  --  Method to call the API and fetch all the export sets
                                        from the commcell environment

refresh()                           --  Method to refresh all the properties of the class ExportSets

add()                               --  Method to create an export set to the commcell environment

has()                               --  Method to check if the export set exists or not

get()                               --  Method to get the export set

delete()                            --  Method to delete the export set
</code></pre>
<h1 id="exportset">ExportSet</h1>
<pre><code>__init__()                          --  Initializes the ExportSet class

_response_not_success()             --  Helper method to raise exception when response is not 200 (ok)

refresh()                           --  Method to refresh all the properties of the class ExportSet

_get_all_exports()                  --  Method to fetch all the exports for the export set

share()                             --  Method to share the export set with the user or user group

has()                               --  Method to check if the export exists or not

get()                               --   Method to get the export

delete()                            --  Method to delete the export

export_items_to_set()               --  Method to export items/documents to the export set

select()                            --  Static Method to randomly pick user input
                                        amount of items from the search result items
</code></pre>
<h2 id="exportset-attributes">Exportset Attributes</h2>
<pre><code>**properties**                      --  return all the properties of the export set

**export_set_full_name**            --  return the export set full name

**export_set_name**                 --  return the export set name

**export_set_comment**              --  return the export set comment

**export_set_id**                   --  return the export set ID

**export_set_guid**                 --  return the export set GUID

**export_set_owner_info**           --  return the export set owner info
</code></pre>
<h1 id="export">Export</h1>
<pre><code>__init__()                          --  Initializes the Export class

_response_not_success()             --  Helper method to raise exception when response is not 200 (ok)

refresh()                           --  Method to refresh all the properties of the class Export

download_export()                   --  Method to download the exported items to a zip file
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L1-L836" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Utilities class for Activate application : Compliance Search

ComplianceSearchUtils:  Class for compliance search related operations support

ExportSets: Class for representing all the export sets associated with the commcell

ExportSet:  Class for an instance of a single Export set of the commcell

Export: Class for an instance of a single Export of the commcell


ComplianceSearchUtils
============

    __init__()                          --  Initializes the Compliance search utility class

    _response_not_success()             --  Helper method to raise exception when response is not 200 (ok)

    do_compliance_search()              --  Method to run a compliance search with the search text provided


ExportSets
============

    __init__()                          --  Initializes the ExportSets class

    _response_not_success()             --  Helper method to raise exception when response is not 200 (ok)

    _get_export_sets()                  --  Method to call the API and fetch all the export sets
                                            from the commcell environment

    refresh()                           --  Method to refresh all the properties of the class ExportSets

    add()                               --  Method to create an export set to the commcell environment

    has()                               --  Method to check if the export set exists or not

    get()                               --  Method to get the export set

    delete()                            --  Method to delete the export set

ExportSet
============

    __init__()                          --  Initializes the ExportSet class

    _response_not_success()             --  Helper method to raise exception when response is not 200 (ok)

    refresh()                           --  Method to refresh all the properties of the class ExportSet

    _get_all_exports()                  --  Method to fetch all the exports for the export set

    share()                             --  Method to share the export set with the user or user group

    has()                               --  Method to check if the export exists or not

    get()                               --   Method to get the export

    delete()                            --  Method to delete the export

    export_items_to_set()               --  Method to export items/documents to the export set

    select()                            --  Static Method to randomly pick user input
                                            amount of items from the search result items

ExportSet Attributes
-----------------------

    **properties**                      --  return all the properties of the export set

    **export_set_full_name**            --  return the export set full name

    **export_set_name**                 --  return the export set name

    **export_set_comment**              --  return the export set comment

    **export_set_id**                   --  return the export set ID

    **export_set_guid**                 --  return the export set GUID

    **export_set_owner_info**           --  return the export set owner info


Export
============

    __init__()                          --  Initializes the Export class

    _response_not_success()             --  Helper method to raise exception when response is not 200 (ok)

    refresh()                           --  Method to refresh all the properties of the class Export

    download_export()                   --  Method to download the exported items to a zip file

&#34;&#34;&#34;
import copy
import base64
import os.path
import random
import requests

from cvpysdk.exception import SDKException
from cvpysdk.activateapps.constants import ComplianceConstants


class ComplianceSearchUtils():
    &#34;&#34;&#34;Class for compliance search related operations support&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes the Compliance search utility class&#34;&#34;&#34;
        self._commcell = commcell
        self._index_servers = commcell.index_servers
        self._cvpysdk_object = commcell._cvpysdk_object
        self._services = commcell._services
        self._update_response_ = commcell._update_response_
        self._users = commcell.users
        self._export_sets = ExportSets(commcell)
        self._do_search_api = self._services[&#34;DO_COMPLIANCE_SEARCH&#34;]

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

            Raises:
                SDKException:
                    Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def do_compliance_search(self, search_text, index_server_name=None,
                             page_size=50, app_type=ComplianceConstants.AppTypes.FILE_SYSTEM):
        &#34;&#34;&#34;Method to run a compliance search with the search text provided

            Args:
                search_text         (str)   -   Search text to be searched on the Compliance search
                index_server_name   (str)   -   Index server name on which the search has to be executed
                page_size           (int)   -   Search result page size value (To Fetch all HITS - 0)
                                                Default: 50
                app_type            (str)   -   ComplianceConstants.AppTypes Enum values
                                                Default: FILE_SYSTEM

            Returns:
                (list)  -   List of all the search result items with the metadata and SOLR fields
                Example :
                    [ {
                        &#34;FileName&#34;: &lt;name&gt;,
                        &#34;SizeKB&#34;: &lt;size&gt;...,
                        &lt;name&gt;: &lt;key&gt;
                    },
                    {
                        &#34;FileName&#34;: &lt;name&gt;,
                        &#34;SizeKB&#34;: &lt;size&gt;...,
                        &lt;name&gt;: &lt;key&gt;
                    }... ]

            Raises:
                    SDKException:
                        Response was not success

        &#34;&#34;&#34;
        self._index_servers.refresh()
        search_request = copy.deepcopy(ComplianceConstants.COMPLIANCE_SEARCH_JSON)
        if index_server_name:
            if not self._index_servers.has(index_server_name):
                raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
            search_request[&#34;listOfCIServer&#34;][0][&#34;cloudID&#34;] = self._index_servers.get(index_server_name).cloud_id
        else:
            del search_request[&#34;listOfCIServer&#34;]
        search_request[&#34;advSearchGrp&#34;][&#34;cvSearchKeyword&#34;][&#34;keyword&#34;] = search_text
        search_request[&#34;userInformation&#34;][&#34;userGuid&#34;] = self._users.get(self._commcell.commcell_username).user_guid
        if app_type in ComplianceConstants.FILE_TYPES:
            search_request[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;applicationType&#34;] = ComplianceConstants.FILE_TYPE
            search_request[&#34;advSearchGrp&#34;][ComplianceConstants.FILE_FILTERS_KEY] = ComplianceConstants.FILE_FILTERS
            custom_facet = copy.deepcopy(ComplianceConstants.FILE_FACET)
            if app_type != ComplianceConstants.AppTypes.FILE_SYSTEM:
                custom_facet = copy.deepcopy(ComplianceConstants.CUSTOM_FACET)
                custom_facet[0][&#34;name&#34;] = ComplianceConstants.CUSTOM_FACETS_NAME[app_type]
                if app_type == ComplianceConstants.AppTypes.TEAMS:
                    search_request[&#34;searchProcessingInfo&#34;][&#34;queryParams&#34;].append(
                        {
                            &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                            &#34;value&#34;: ComplianceConstants.RESPONSE_FIELD_LIST
                        },
                    )
                custom_facet[1][&#34;stringParameter&#34;][0][&#34;name&#34;] = ComplianceConstants.CUSTOM_FACETS[app_type]
            search_request[&#34;facetRequests&#34;][ComplianceConstants.FACET_KEY] = custom_facet
        elif app_type in ComplianceConstants.EMAIL_TYPES:
            search_request[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;applicationType&#34;] = ComplianceConstants.EMAIL_TYPE
            search_request[&#34;advSearchGrp&#34;][ComplianceConstants.EMAIL_FILTERS_KEY] = {
                &#34;usermailbox&#34;: app_type == ComplianceConstants.AppTypes.EXCHANGE,
                &#34;journalmailbox&#34;: app_type == ComplianceConstants.AppTypes.EXCHANGE_JOURNAL,
                &#34;smtpmailbox&#34;: app_type == ComplianceConstants.AppTypes.EXCHANGE_JOURNAL
            }
        else:
            raise SDKException(&#39;ComplianceSearch&#39;, &#39;107&#39;)
        if page_size == 0:
            flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._do_search_api, payload=search_request)
            if flag and response.json() and &#34;totalHits&#34; in response.json().get(&#34;proccessingInfo&#34;):
                page_size = max(response.json()[&#34;proccessingInfo&#34;][&#34;totalHits&#34;], 50)
        search_request[&#34;searchProcessingInfo&#34;][&#34;pageSize&#34;] = page_size
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._do_search_api, payload=search_request)
        if flag:
            if response.json():
                if &#34;searchResult&#34; in response.json():
                    if &#34;resultItem&#34; in response.json()[&#34;searchResult&#34;]:
                        return response.json()[&#34;searchResult&#34;][&#34;resultItem&#34;]
                return []
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)


class ExportSets():
    &#34;&#34;&#34;Class for representing all the export sets associated with the commcell&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes the ExportSets class&#34;&#34;&#34;
        self._commcell = commcell
        self._cvpysdk_object = None
        self._services = None
        self._update_response_ = None
        self._user_guid = None
        self._add_export_set_api = None
        self._add_export_set_json_req = None
        self._get_export_sets_api = None
        self._get_export_sets_json_req = None
        self._delete_export_set_api = None
        self._delete_export_set_json_req = None
        self._all_export_set = None
        self.refresh()

    def _get_export_sets(self):
        &#34;&#34;&#34;Method to call the API and fetch all the export sets from the commcell environment&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#34;POST&#34;, url=self._get_export_sets_api, payload=self._get_export_sets_json_req)
        if flag:
            if response.json() and &#34;containers&#34; in response.json():
                containers = response.json()[&#39;containers&#39;]
                for container in containers:
                    self._all_export_set.update(
                        {container[&#34;containerName&#34;]: container})
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Method to refresh all the properties of the class ExportSets&#34;&#34;&#34;
        self._cvpysdk_object = self._commcell._cvpysdk_object
        self._services = self._commcell._services
        self._update_response_ = self._commcell._update_response_
        self._user_guid = self._commcell.users.get(self._commcell.commcell_username).user_guid
        self._all_export_set = {}
        self._get_export_sets_api = self._services[&#39;GET_EXPORT_SETS&#39;]
        self._get_export_sets_json_req = \
            {
                &#34;getContainerOptions&#34;: 0,
                &#34;entityType&#34;: 9503,
                &#34;userGuid&#34;: self._user_guid,
                &#34;attribute&#34;: {
                    &#34;all&#34;: True
                }
            }
        self._add_export_set_api = self._services[&#39;ADD_EXPORT_SET&#39;]
        self._add_export_set_json_req = \
            {
                &#34;entityType&#34;: 9503,
                &#34;operationType&#34;: 1,
                &#34;userGuid&#34;: self._user_guid,
                &#34;fromSite&#34;: 2,
                &#34;container&#34;: {
                    &#34;containerType&#34;: 9503,
                    &#34;containerName&#34;: None,
                    &#34;containerOwnerType&#34;: 1,
                    &#34;comment&#34;: &#34;Export set created from CvPySDK&#34;
                }
            }
        self._delete_export_set_api = self._services[&#39;DELETE_EXPORT_SET&#39;]
        self._delete_export_set_json_req = \
            {
                &#34;entityType&#34;: 9503,
                &#34;containers&#34;: None
            }
        self._get_export_sets()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

            Raises:
                SDKException:
                    Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def add(self, export_set_name, comment=None):
        &#34;&#34;&#34;Method to create an export set to the commcell environment

            Args:
                export_set_name     (str)   -   Export set name for the newly created export set
                comment             (str)   -   Export set description fot the newly created export set

            Returns:
                ExportSet instance for the newly created export set

        &#34;&#34;&#34;
        if not self.has(export_set_name):
            add_export_set_json_req = copy.deepcopy(self._add_export_set_json_req)
            add_export_set_json_req[&#39;container&#39;][&#39;containerName&#39;] = export_set_name
            if comment:
                add_export_set_json_req[&#39;container&#39;][&#39;comment&#39;] = comment
            flag, response = self._cvpysdk_object.make_request(
                method=&#34;POST&#34;, url=self._add_export_set_api, payload=add_export_set_json_req)
            if flag:
                if response.json() and &#34;container&#34; in response.json():
                    self.refresh()
                    return self.get(export_set_name=export_set_name)
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                self._response_not_success(response)

    def has(self, export_set_name):
        &#34;&#34;&#34;Method to check if the export set exists or not

            Args:
                export_set_name (str)   -   Export set name to be checked

            Returns:
                Returns True if export set exists in the environment or False otherwise

        &#34;&#34;&#34;
        return export_set_name in self._all_export_set

    def get(self, export_set_name):
        &#34;&#34;&#34;Method to get the export set

            Args:
                export_set_name (str)   -   Export set name to get

            Returns:
                ExportSet instance for the export set with the given name if found else raises Exception

        &#34;&#34;&#34;

        if self.has(export_set_name):
            return ExportSet(self._commcell, self._all_export_set[export_set_name])
        raise SDKException(&#39;ComplianceSearch&#39;, &#39;106&#39;)

    def delete(self, export_set_name):
        &#34;&#34;&#34;Method to delete the export set

            Args:
                export_set_name (str)   -   Export set name to be deleted

            Returns:
                Returns True if delete successfully else raises error

            Raises:
                SDKException:
                    Response was not success

        &#34;&#34;&#34;
        if not self.has(export_set_name):
            raise SDKException(&#34;ComplianceSearch&#34;, &#34;105&#34;)
        delete_json = copy.deepcopy(self._delete_export_set_json_req)
        delete_json[&#39;containers&#39;] = [self._all_export_set[export_set_name]]
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, self._delete_export_set_api, delete_json)
        if flag:
            if response.json() and &#34;errList&#34; in response.json() and len(response.json()[&#34;errList&#34;]) == 0:
                self.refresh()
                return True
        self._response_not_success(response)


class ExportSet():
    &#34;&#34;&#34;Class for an instance of a single Export set of the commcell&#34;&#34;&#34;

    def __init__(self, commcell, export_set_properties):
        &#34;&#34;&#34;Initializes the ExportSet class&#34;&#34;&#34;
        self._commcell = commcell
        self._export_set_properties = export_set_properties
        self._cvpysdk_object = None
        self._services = None
        self._update_response_ = None
        self._user_guid = None
        self._get_exports_api = None
        self._get_export_json_req = None
        self._export_items_api = None
        self._export_items_json_req = None
        self._share_export_set_api = None
        self._exports_list = None
        self._all_exports = None
        self._delete_export_api = None
        self._delete_export_json_req = None
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;Method to refresh the properties of the class ExportSet&#34;&#34;&#34;
        self._cvpysdk_object = self._commcell._cvpysdk_object
        self._services = self._commcell._services
        self._update_response_ = self._commcell._update_response_
        self._user_guid = self._commcell.users.get(self._commcell.commcell_username).user_guid
        self._get_exports_api = self._services[&#39;GET_EXPORTS&#39;]
        self._share_export_set_api = self._services[&#39;SECURITY_ASSOCIATION&#39;]
        self._get_export_json_req = \
            {
                &#34;containerOpReq&#34;: {
                    &#34;entityType&#34;: 9503,
                    &#34;operationType&#34;: 1,
                    &#34;userGuid&#34;: self._user_guid,
                    &#34;fromSite&#34;: 0,
                    &#34;container&#34;: self._export_set_properties
                },
                &#34;filter&#34;: {},
                &#34;pagingData&#34;: {
                    &#34;startIndex&#34;: 0,
                    &#34;fetchEmails&#34;: True,
                    &#34;pageSize&#34;: 50,
                    &#34;fetchFiles&#34;: True,
                    &#34;orderByClause&#34;: &#34;CreateTime desc&#34;
                }
            }
        self._export_items_api = self._services[&#39;EXPORT_ITEM_TO_SET&#39;]
        self._export_items_json_req = \
            {
                &#34;complianceData&#34;: {
                    &#34;mode&#34;: 2,
                    &#34;restoreType&#34;: 2,
                    &#34;downLoadDesc&#34;: None,
                    &#34;destContainer&#34;: None,
                    &#34;originatingContainer&#34;: {
                        &#34;containerOwnerType&#34;: 1
                    },
                    &#34;options&#34;: {
                        &#34;zipEML&#34;: True,
                        &#34;retentionInDays&#34;: 30}
                },
                &#34;onlineData&#34;: {
                    &#34;downloadStatus&#34;: 0
                },
                &#34;listOfItems&#34;: {
                    &#34;resultItem&#34;: None
                }
            }
        self._delete_export_api = self._services[&#39;GET_EXPORTS&#39;]
        self._delete_export_json_req = \
            {
                &#34;operationType&#34;: 2,
                &#34;container&#34;: None,
                &#34;downloadItems&#34;: {
                    &#34;items&#34;: None
                },
                &#34;tagModifyRequest&#34;: {
                    &#34;operationType&#34;: 2
                }
            }
        self._all_exports = {}
        self._get_all_exports()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

        Raises:
            SDKException:
                Response was not success

        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def _get_all_exports(self):
        &#34;&#34;&#34;Method to fetch all the exports for the export set&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#34;POST&#34;, url=self._get_exports_api, payload=self._get_export_json_req)
        if flag:
            if (response.json() and &#34;totalHits&#34; in response.json()
                    and int(response.json()[&#39;totalHits&#39;]) != 0):
                items = response.json()[&#39;downloadItems&#39;][&#39;items&#39;]
                for item in items:
                    self._all_exports.update(
                        {item[&#34;description&#34;]: item})
            else:
                self._all_exports = {}
        else:
            self._response_not_success(response)

    def share(self, user_or_user_group_name=None, permissions=None, mode=2):
        &#34;&#34;&#34;Method to share the export set with the user or user group provided

            Args:
                user_or_user_group_name (str)       -   User or user group with which export set has to be shared
                permissions             (list/str)  -   List or comma separated permissions that need
                                                        to be set to the user/ user group
                mode                    (int)       -   to add (2), remove (3) or overwrite (1) the permissions
                                                        Default : Add (2)

            Returns:
                Returns True if share worked fine else raises an Exception

        &#34;&#34;&#34;
        if isinstance(permissions, str):
            permissions = permissions.split(&#34;,&#34;)
        share_json = copy.deepcopy(ComplianceConstants.EXPORT_SET_SHARE_REQUEST_JSON)
        unshare = False
        user_details = {}
        if user_or_user_group_name is None:
            unshare = True
        elif self._commcell.users.has_user(user_or_user_group_name):
            user_details = {
                &#34;userId&#34;: int(self._commcell.users.get(user_or_user_group_name).user_id),
                &#34;userName&#34;: user_or_user_group_name,
                &#34;_type_&#34;: 13
            }
        elif self._commcell.user_groups.has_user_group(user_or_user_group_name):
            user_details = {
                &#34;groupId&#34;: int(self._commcell.user_groups.get(user_or_user_group_name).user_group_id),
                &#34;userGroupName&#34;: user_or_user_group_name,
                &#34;_type_&#34;: 15
            }
        else:
            raise SDKException(&#39;ComplianceSearch&#39;, &#39;101&#39;)
        if unshare:
            share_json[&#39;securityAssociations&#39;][&#39;associations&#39;] = []
            share_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = 1
        else:
            share_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = mode
            share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;] = [user_details]
            if permissions is None:
                share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;] = []
                permissions = [[&#34;View&#34;, &#34;Download&#34;], list(ComplianceConstants.PERMISSIONS.keys())][mode//3]
            if mode == 3:
                share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;] = []
            for permission in permissions:
                try:
                    share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;].append(
                        ComplianceConstants.PERMISSIONS[permission]
                    )
                except KeyError:
                    raise SDKException(&#39;ComplianceSearch&#39;, &#39;102&#39;)
        share_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;downloadSetId&#39;] = self.export_set_id
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, self._share_export_set_api, share_json)
        if flag:
            if response.json() and &#34;response&#34; in response.json():
                if response.json()[&#39;response&#39;][0][&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return True
        self._response_not_success(response)

    def has(self, export_name):
        &#34;&#34;&#34;Method to check if the export exists or not

            Args:
                export_name (str)   -   Export name to be checked

            Returns:
                Returns True if export exists in the environment or False otherwise

        &#34;&#34;&#34;
        return export_name in self._all_exports

    def get(self, export_name):
        &#34;&#34;&#34;Method to get the export

            Args:
                export_name (str)   -   Export name to get

            Returns:
                Export class instance for the export with the given name if found else returns None

        &#34;&#34;&#34;
        if self.has(export_name):
            return Export(self._commcell, self._all_exports[export_name])
        raise SDKException(&#39;ComplianceSearch&#39;, &#39;103&#39;)

    def delete(self, export_name):
        &#34;&#34;&#34;Method to delete the export

            Args:
                export_name (str)   -   Export name to be deleted

            Returns:
                Returns True if delete successfully else raises error

            Raises:
                SDKException:
                    Response was not success

        &#34;&#34;&#34;
        if not self.has(export_name):
            raise SDKException(&#34;ComplianceSearch&#34;, &#34;103&#34;)
        delete_json = copy.deepcopy(self._delete_export_json_req)
        delete_json[&#34;downloadItems&#34;][&#34;items&#34;] = [self._all_exports[export_name]]
        delete_json[&#34;container&#34;] = self.properties
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, self._delete_export_api, delete_json)
        if flag:
            if response.json() and &#34;errList&#34; in response.json() and len(response.json()[&#34;errList&#34;]) != 0:
                if response.json()[&#34;errList&#34;][0][&#34;errorCode&#34;] != 0:
                    raise SDKException(&#34;ComplianceSearch&#34;, &#34;104&#34;,
                                       response.json()[&#34;errList&#34;][0].get(&#34;errLogMessage&#34;))
            return True
        self._response_not_success(response)

    def export_items_to_set(self, export_name, export_items, export_type=ComplianceConstants.ExportTypes.CAB):
        &#34;&#34;&#34;Method to export items/documents to the export set

            Args:
                export_name     (str)   -   Export name for the exported items
                export_items    (list)  -   List of search result items which needs to be export
                export_type     (str)   -   ComplianceConstants.ExportTypes Enum values
                                            Default: CAB

            Returns:
                Returns the restore job ID for the export operation

            Raises:
                SDKException:
                    Response was not success

        &#34;&#34;&#34;
        if export_type not in ComplianceConstants.RESTORE_TYPE:
            raise SDKException(&#39;ComplianceSearch&#39;, &#39;108&#39;)
        export_items_json_req = copy.deepcopy(self._export_items_json_req)
        export_items_json_req[&#39;complianceData&#39;][&#39;restoreType&#39;] = ComplianceConstants.RESTORE_TYPE[export_type]
        export_items_json_req[&#39;complianceData&#39;][&#39;downLoadDesc&#39;] = export_name
        export_items_json_req[&#39;complianceData&#39;][&#39;destContainer&#39;] = self.properties
        export_items_json_req[&#39;listOfItems&#39;][&#39;resultItem&#39;] = export_items
        flag, response = self._cvpysdk_object.make_request(
            method=&#34;POST&#34;, url=self._export_items_api, payload=export_items_json_req)
        if flag:
            if response.json() and &#34;downloadId&#34; in response.json() and &#34;jobId&#34; in response.json():
                self.refresh()
                return response.json()[&#39;jobId&#39;]
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)

    @staticmethod
    def select(result_items, no_of_files=0, export_all=False):
        &#34;&#34;&#34;Static Method to randomly pick user input amount of items from the search result items

            Args:
                result_items    (list)      -   List of all the search result items
                no_of_files     (int)       -   Number of items to be selected
                export_all      (bool)      -   if all the items needs to be selected or not

            Returns:
                List of randomly selected (no_of_files) items from the (result_items)

        &#34;&#34;&#34;
        if export_all or len(result_items) &lt; no_of_files:
            return result_items
        return list(random.sample(result_items, no_of_files))

    @property
    def properties(self):
        &#34;&#34;&#34;Method to return all the properties of the export set

            Returns:
                Dict of the properties of the export set as received from the API (rawdata)

        &#34;&#34;&#34;
        return self._export_set_properties

    @property
    def export_set_full_name(self):
        &#34;&#34;&#34;Method to return the export set full name

            Returns:
                (str) export set full name

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;containerFullName&#39;]

    @property
    def export_set_name(self):
        &#34;&#34;&#34;Method to return the export set name

            Returns:
                (str) export set name

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;containerName&#39;]

    @property
    def export_set_comment(self):
        &#34;&#34;&#34;Method to return the export set comment

            Returns:
                (str) export set comment

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;comment&#39;]

    @property
    def export_set_id(self):
        &#34;&#34;&#34;Method to return the export set ID

            Returns:
                (int) export set ID

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;containerId&#39;]

    @property
    def export_set_guid(self):
        &#34;&#34;&#34;Method to return the export set Guid

            Returns:
                (str) export set Guid

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;containerGuid&#39;]

    @property
    def export_set_owner_info(self):
        &#34;&#34;&#34;Method to return the export set Owner info

            Returns:
                (str) export set Owner info

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;ownerInfo&#39;]


class Export():
    &#34;&#34;&#34;Class for an instance of a single Export of the export set&#34;&#34;&#34;

    def __init__(self, commcell, export_properties):
        &#34;&#34;&#34;Initializes the Export class instance&#34;&#34;&#34;
        self._commcell = commcell
        self._export_set_properties = export_properties
        self._cvpysdk_object = None
        self._services = None
        self._update_response_ = None
        self._user_guid = None
        self._download_file_api = None
        self._download_file_json_req = None
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

        Raises:
            SDKException:
                Response was not success

        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def refresh(self):
        &#34;&#34;&#34;Method to refresh the properties of the class Export&#34;&#34;&#34;
        self._cvpysdk_object = self._commcell._cvpysdk_object
        self._services = self._commcell._services
        self._update_response_ = self._commcell._update_response_
        self._user_guid = self._commcell.users.get(self._commcell.commcell_username).user_guid
        self._download_file_api = self._services[&#39;DOWNLOAD_EXPORT_ITEMS&#39;]
        self._download_file_json_req = \
            {
                &#34;appTypeId&#34;: 200,
                &#34;responseFileName&#34;: self._export_set_properties[&#39;description&#39;],
                &#34;fileParams&#34;: [
                    {
                        &#34;name&#34;: self._export_set_properties[&#39;downLoadID&#39;],
                        &#34;id&#34;: 2
                    },
                    {
                        &#34;name&#34;: &#34;zip&#34;,
                        &#34;id&#34;: 3
                    }
                ]
            }

    def download_export(self, download_folder):
        &#34;&#34;&#34;Method to download the exported items to a zip file

            Args:
                download_folder     (str)   -   Path of the folder in which exported items zip file should be saved

            Returns:
                (str) path of the downloaded zip file

            Raises:
                SDKException:
                    Response was not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#34;POST&#34;, url=self._download_file_api, payload=self._download_file_json_req)
        if flag:
            if response.json() and &#34;fileContent&#34; in response.json():
                file_content = response.json()[&#39;fileContent&#39;]
                download_file = os.path.join(download_folder, file_content[&#39;fileName&#39;])
                if &#39;cloudUrl&#39; in response.json():
                    url = response.json()[&#39;cloudUrl&#39;]
                    cloud_file = requests.get(url)
                    if cloud_file.status_code == 200:
                        file_data = cloud_file.content
                    else:
                        raise SDKException(&#39;ComplianceSearch&#39;, &#39;109&#39;)
                else:
                    file_data = base64.b64decode(file_content[&#39;data&#39;])
                with open(download_file, &#34;wb&#34;) as f:
                    f.write(file_data)
                return download_file
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activateapps.compliance_utils.ComplianceSearchUtils"><code class="flex name class">
<span>class <span class="ident">ComplianceSearchUtils</span></span>
<span>(</span><span>commcell)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for compliance search related operations support</p>
<p>Initializes the Compliance search utility class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L124-L231" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ComplianceSearchUtils():
    &#34;&#34;&#34;Class for compliance search related operations support&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes the Compliance search utility class&#34;&#34;&#34;
        self._commcell = commcell
        self._index_servers = commcell.index_servers
        self._cvpysdk_object = commcell._cvpysdk_object
        self._services = commcell._services
        self._update_response_ = commcell._update_response_
        self._users = commcell.users
        self._export_sets = ExportSets(commcell)
        self._do_search_api = self._services[&#34;DO_COMPLIANCE_SEARCH&#34;]

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

            Raises:
                SDKException:
                    Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def do_compliance_search(self, search_text, index_server_name=None,
                             page_size=50, app_type=ComplianceConstants.AppTypes.FILE_SYSTEM):
        &#34;&#34;&#34;Method to run a compliance search with the search text provided

            Args:
                search_text         (str)   -   Search text to be searched on the Compliance search
                index_server_name   (str)   -   Index server name on which the search has to be executed
                page_size           (int)   -   Search result page size value (To Fetch all HITS - 0)
                                                Default: 50
                app_type            (str)   -   ComplianceConstants.AppTypes Enum values
                                                Default: FILE_SYSTEM

            Returns:
                (list)  -   List of all the search result items with the metadata and SOLR fields
                Example :
                    [ {
                        &#34;FileName&#34;: &lt;name&gt;,
                        &#34;SizeKB&#34;: &lt;size&gt;...,
                        &lt;name&gt;: &lt;key&gt;
                    },
                    {
                        &#34;FileName&#34;: &lt;name&gt;,
                        &#34;SizeKB&#34;: &lt;size&gt;...,
                        &lt;name&gt;: &lt;key&gt;
                    }... ]

            Raises:
                    SDKException:
                        Response was not success

        &#34;&#34;&#34;
        self._index_servers.refresh()
        search_request = copy.deepcopy(ComplianceConstants.COMPLIANCE_SEARCH_JSON)
        if index_server_name:
            if not self._index_servers.has(index_server_name):
                raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
            search_request[&#34;listOfCIServer&#34;][0][&#34;cloudID&#34;] = self._index_servers.get(index_server_name).cloud_id
        else:
            del search_request[&#34;listOfCIServer&#34;]
        search_request[&#34;advSearchGrp&#34;][&#34;cvSearchKeyword&#34;][&#34;keyword&#34;] = search_text
        search_request[&#34;userInformation&#34;][&#34;userGuid&#34;] = self._users.get(self._commcell.commcell_username).user_guid
        if app_type in ComplianceConstants.FILE_TYPES:
            search_request[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;applicationType&#34;] = ComplianceConstants.FILE_TYPE
            search_request[&#34;advSearchGrp&#34;][ComplianceConstants.FILE_FILTERS_KEY] = ComplianceConstants.FILE_FILTERS
            custom_facet = copy.deepcopy(ComplianceConstants.FILE_FACET)
            if app_type != ComplianceConstants.AppTypes.FILE_SYSTEM:
                custom_facet = copy.deepcopy(ComplianceConstants.CUSTOM_FACET)
                custom_facet[0][&#34;name&#34;] = ComplianceConstants.CUSTOM_FACETS_NAME[app_type]
                if app_type == ComplianceConstants.AppTypes.TEAMS:
                    search_request[&#34;searchProcessingInfo&#34;][&#34;queryParams&#34;].append(
                        {
                            &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                            &#34;value&#34;: ComplianceConstants.RESPONSE_FIELD_LIST
                        },
                    )
                custom_facet[1][&#34;stringParameter&#34;][0][&#34;name&#34;] = ComplianceConstants.CUSTOM_FACETS[app_type]
            search_request[&#34;facetRequests&#34;][ComplianceConstants.FACET_KEY] = custom_facet
        elif app_type in ComplianceConstants.EMAIL_TYPES:
            search_request[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;applicationType&#34;] = ComplianceConstants.EMAIL_TYPE
            search_request[&#34;advSearchGrp&#34;][ComplianceConstants.EMAIL_FILTERS_KEY] = {
                &#34;usermailbox&#34;: app_type == ComplianceConstants.AppTypes.EXCHANGE,
                &#34;journalmailbox&#34;: app_type == ComplianceConstants.AppTypes.EXCHANGE_JOURNAL,
                &#34;smtpmailbox&#34;: app_type == ComplianceConstants.AppTypes.EXCHANGE_JOURNAL
            }
        else:
            raise SDKException(&#39;ComplianceSearch&#39;, &#39;107&#39;)
        if page_size == 0:
            flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._do_search_api, payload=search_request)
            if flag and response.json() and &#34;totalHits&#34; in response.json().get(&#34;proccessingInfo&#34;):
                page_size = max(response.json()[&#34;proccessingInfo&#34;][&#34;totalHits&#34;], 50)
        search_request[&#34;searchProcessingInfo&#34;][&#34;pageSize&#34;] = page_size
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._do_search_api, payload=search_request)
        if flag:
            if response.json():
                if &#34;searchResult&#34; in response.json():
                    if &#34;resultItem&#34; in response.json()[&#34;searchResult&#34;]:
                        return response.json()[&#34;searchResult&#34;][&#34;resultItem&#34;]
                return []
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.compliance_utils.ComplianceSearchUtils.do_compliance_search"><code class="name flex">
<span>def <span class="ident">do_compliance_search</span></span>(<span>self, search_text, index_server_name=None, page_size=50, app_type=AppTypes.FILE_SYSTEM)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to run a compliance search with the search text provided</p>
<h2 id="args">Args</h2>
<p>search_text
(str)
-
Search text to be searched on the Compliance search
index_server_name
(str)
-
Index server name on which the search has to be executed
page_size
(int)
-
Search result page size value (To Fetch all HITS - 0)
Default: 50
app_type
(str)
-
ComplianceConstants.AppTypes Enum values
Default: FILE_SYSTEM</p>
<h2 id="returns">Returns</h2>
<p>(list)
-
List of all the search result items with the metadata and SOLR fields
Example :
[ {
"FileName": <name>,
"SizeKB": <size>&hellip;,
<name>: <key>
},
{
"FileName": <name>,
"SizeKB": <size>&hellip;,
<name>: <key>
}&hellip; ]</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Response was not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L151-L231" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def do_compliance_search(self, search_text, index_server_name=None,
                         page_size=50, app_type=ComplianceConstants.AppTypes.FILE_SYSTEM):
    &#34;&#34;&#34;Method to run a compliance search with the search text provided

        Args:
            search_text         (str)   -   Search text to be searched on the Compliance search
            index_server_name   (str)   -   Index server name on which the search has to be executed
            page_size           (int)   -   Search result page size value (To Fetch all HITS - 0)
                                            Default: 50
            app_type            (str)   -   ComplianceConstants.AppTypes Enum values
                                            Default: FILE_SYSTEM

        Returns:
            (list)  -   List of all the search result items with the metadata and SOLR fields
            Example :
                [ {
                    &#34;FileName&#34;: &lt;name&gt;,
                    &#34;SizeKB&#34;: &lt;size&gt;...,
                    &lt;name&gt;: &lt;key&gt;
                },
                {
                    &#34;FileName&#34;: &lt;name&gt;,
                    &#34;SizeKB&#34;: &lt;size&gt;...,
                    &lt;name&gt;: &lt;key&gt;
                }... ]

        Raises:
                SDKException:
                    Response was not success

    &#34;&#34;&#34;
    self._index_servers.refresh()
    search_request = copy.deepcopy(ComplianceConstants.COMPLIANCE_SEARCH_JSON)
    if index_server_name:
        if not self._index_servers.has(index_server_name):
            raise SDKException(&#39;IndexServers&#39;, &#39;102&#39;)
        search_request[&#34;listOfCIServer&#34;][0][&#34;cloudID&#34;] = self._index_servers.get(index_server_name).cloud_id
    else:
        del search_request[&#34;listOfCIServer&#34;]
    search_request[&#34;advSearchGrp&#34;][&#34;cvSearchKeyword&#34;][&#34;keyword&#34;] = search_text
    search_request[&#34;userInformation&#34;][&#34;userGuid&#34;] = self._users.get(self._commcell.commcell_username).user_guid
    if app_type in ComplianceConstants.FILE_TYPES:
        search_request[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;applicationType&#34;] = ComplianceConstants.FILE_TYPE
        search_request[&#34;advSearchGrp&#34;][ComplianceConstants.FILE_FILTERS_KEY] = ComplianceConstants.FILE_FILTERS
        custom_facet = copy.deepcopy(ComplianceConstants.FILE_FACET)
        if app_type != ComplianceConstants.AppTypes.FILE_SYSTEM:
            custom_facet = copy.deepcopy(ComplianceConstants.CUSTOM_FACET)
            custom_facet[0][&#34;name&#34;] = ComplianceConstants.CUSTOM_FACETS_NAME[app_type]
            if app_type == ComplianceConstants.AppTypes.TEAMS:
                search_request[&#34;searchProcessingInfo&#34;][&#34;queryParams&#34;].append(
                    {
                        &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                        &#34;value&#34;: ComplianceConstants.RESPONSE_FIELD_LIST
                    },
                )
            custom_facet[1][&#34;stringParameter&#34;][0][&#34;name&#34;] = ComplianceConstants.CUSTOM_FACETS[app_type]
        search_request[&#34;facetRequests&#34;][ComplianceConstants.FACET_KEY] = custom_facet
    elif app_type in ComplianceConstants.EMAIL_TYPES:
        search_request[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;applicationType&#34;] = ComplianceConstants.EMAIL_TYPE
        search_request[&#34;advSearchGrp&#34;][ComplianceConstants.EMAIL_FILTERS_KEY] = {
            &#34;usermailbox&#34;: app_type == ComplianceConstants.AppTypes.EXCHANGE,
            &#34;journalmailbox&#34;: app_type == ComplianceConstants.AppTypes.EXCHANGE_JOURNAL,
            &#34;smtpmailbox&#34;: app_type == ComplianceConstants.AppTypes.EXCHANGE_JOURNAL
        }
    else:
        raise SDKException(&#39;ComplianceSearch&#39;, &#39;107&#39;)
    if page_size == 0:
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._do_search_api, payload=search_request)
        if flag and response.json() and &#34;totalHits&#34; in response.json().get(&#34;proccessingInfo&#34;):
            page_size = max(response.json()[&#34;proccessingInfo&#34;][&#34;totalHits&#34;], 50)
    search_request[&#34;searchProcessingInfo&#34;][&#34;pageSize&#34;] = page_size
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._do_search_api, payload=search_request)
    if flag:
        if response.json():
            if &#34;searchResult&#34; in response.json():
                if &#34;resultItem&#34; in response.json()[&#34;searchResult&#34;]:
                    return response.json()[&#34;searchResult&#34;][&#34;resultItem&#34;]
            return []
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.Export"><code class="flex name class">
<span>class <span class="ident">Export</span></span>
<span>(</span><span>commcell, export_properties)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for an instance of a single Export of the export set</p>
<p>Initializes the Export class instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L750-L836" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Export():
    &#34;&#34;&#34;Class for an instance of a single Export of the export set&#34;&#34;&#34;

    def __init__(self, commcell, export_properties):
        &#34;&#34;&#34;Initializes the Export class instance&#34;&#34;&#34;
        self._commcell = commcell
        self._export_set_properties = export_properties
        self._cvpysdk_object = None
        self._services = None
        self._update_response_ = None
        self._user_guid = None
        self._download_file_api = None
        self._download_file_json_req = None
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

        Raises:
            SDKException:
                Response was not success

        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def refresh(self):
        &#34;&#34;&#34;Method to refresh the properties of the class Export&#34;&#34;&#34;
        self._cvpysdk_object = self._commcell._cvpysdk_object
        self._services = self._commcell._services
        self._update_response_ = self._commcell._update_response_
        self._user_guid = self._commcell.users.get(self._commcell.commcell_username).user_guid
        self._download_file_api = self._services[&#39;DOWNLOAD_EXPORT_ITEMS&#39;]
        self._download_file_json_req = \
            {
                &#34;appTypeId&#34;: 200,
                &#34;responseFileName&#34;: self._export_set_properties[&#39;description&#39;],
                &#34;fileParams&#34;: [
                    {
                        &#34;name&#34;: self._export_set_properties[&#39;downLoadID&#39;],
                        &#34;id&#34;: 2
                    },
                    {
                        &#34;name&#34;: &#34;zip&#34;,
                        &#34;id&#34;: 3
                    }
                ]
            }

    def download_export(self, download_folder):
        &#34;&#34;&#34;Method to download the exported items to a zip file

            Args:
                download_folder     (str)   -   Path of the folder in which exported items zip file should be saved

            Returns:
                (str) path of the downloaded zip file

            Raises:
                SDKException:
                    Response was not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#34;POST&#34;, url=self._download_file_api, payload=self._download_file_json_req)
        if flag:
            if response.json() and &#34;fileContent&#34; in response.json():
                file_content = response.json()[&#39;fileContent&#39;]
                download_file = os.path.join(download_folder, file_content[&#39;fileName&#39;])
                if &#39;cloudUrl&#39; in response.json():
                    url = response.json()[&#39;cloudUrl&#39;]
                    cloud_file = requests.get(url)
                    if cloud_file.status_code == 200:
                        file_data = cloud_file.content
                    else:
                        raise SDKException(&#39;ComplianceSearch&#39;, &#39;109&#39;)
                else:
                    file_data = base64.b64decode(file_content[&#39;data&#39;])
                with open(download_file, &#34;wb&#34;) as f:
                    f.write(file_data)
                return download_file
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.compliance_utils.Export.download_export"><code class="name flex">
<span>def <span class="ident">download_export</span></span>(<span>self, download_folder)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to download the exported items to a zip file</p>
<h2 id="args">Args</h2>
<p>download_folder
(str)
-
Path of the folder in which exported items zip file should be saved</p>
<h2 id="returns">Returns</h2>
<p>(str) path of the downloaded zip file</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Response was not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L802-L836" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def download_export(self, download_folder):
    &#34;&#34;&#34;Method to download the exported items to a zip file

        Args:
            download_folder     (str)   -   Path of the folder in which exported items zip file should be saved

        Returns:
            (str) path of the downloaded zip file

        Raises:
            SDKException:
                Response was not success

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        method=&#34;POST&#34;, url=self._download_file_api, payload=self._download_file_json_req)
    if flag:
        if response.json() and &#34;fileContent&#34; in response.json():
            file_content = response.json()[&#39;fileContent&#39;]
            download_file = os.path.join(download_folder, file_content[&#39;fileName&#39;])
            if &#39;cloudUrl&#39; in response.json():
                url = response.json()[&#39;cloudUrl&#39;]
                cloud_file = requests.get(url)
                if cloud_file.status_code == 200:
                    file_data = cloud_file.content
                else:
                    raise SDKException(&#39;ComplianceSearch&#39;, &#39;109&#39;)
            else:
                file_data = base64.b64decode(file_content[&#39;data&#39;])
            with open(download_file, &#34;wb&#34;) as f:
                f.write(file_data)
            return download_file
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.Export.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to refresh the properties of the class Export</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L779-L800" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Method to refresh the properties of the class Export&#34;&#34;&#34;
    self._cvpysdk_object = self._commcell._cvpysdk_object
    self._services = self._commcell._services
    self._update_response_ = self._commcell._update_response_
    self._user_guid = self._commcell.users.get(self._commcell.commcell_username).user_guid
    self._download_file_api = self._services[&#39;DOWNLOAD_EXPORT_ITEMS&#39;]
    self._download_file_json_req = \
        {
            &#34;appTypeId&#34;: 200,
            &#34;responseFileName&#34;: self._export_set_properties[&#39;description&#39;],
            &#34;fileParams&#34;: [
                {
                    &#34;name&#34;: self._export_set_properties[&#39;downLoadID&#39;],
                    &#34;id&#34;: 2
                },
                {
                    &#34;name&#34;: &#34;zip&#34;,
                    &#34;id&#34;: 3
                }
            ]
        }</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet"><code class="flex name class">
<span>class <span class="ident">ExportSet</span></span>
<span>(</span><span>commcell, export_set_properties)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for an instance of a single Export set of the commcell</p>
<p>Initializes the ExportSet class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L397-L747" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ExportSet():
    &#34;&#34;&#34;Class for an instance of a single Export set of the commcell&#34;&#34;&#34;

    def __init__(self, commcell, export_set_properties):
        &#34;&#34;&#34;Initializes the ExportSet class&#34;&#34;&#34;
        self._commcell = commcell
        self._export_set_properties = export_set_properties
        self._cvpysdk_object = None
        self._services = None
        self._update_response_ = None
        self._user_guid = None
        self._get_exports_api = None
        self._get_export_json_req = None
        self._export_items_api = None
        self._export_items_json_req = None
        self._share_export_set_api = None
        self._exports_list = None
        self._all_exports = None
        self._delete_export_api = None
        self._delete_export_json_req = None
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;Method to refresh the properties of the class ExportSet&#34;&#34;&#34;
        self._cvpysdk_object = self._commcell._cvpysdk_object
        self._services = self._commcell._services
        self._update_response_ = self._commcell._update_response_
        self._user_guid = self._commcell.users.get(self._commcell.commcell_username).user_guid
        self._get_exports_api = self._services[&#39;GET_EXPORTS&#39;]
        self._share_export_set_api = self._services[&#39;SECURITY_ASSOCIATION&#39;]
        self._get_export_json_req = \
            {
                &#34;containerOpReq&#34;: {
                    &#34;entityType&#34;: 9503,
                    &#34;operationType&#34;: 1,
                    &#34;userGuid&#34;: self._user_guid,
                    &#34;fromSite&#34;: 0,
                    &#34;container&#34;: self._export_set_properties
                },
                &#34;filter&#34;: {},
                &#34;pagingData&#34;: {
                    &#34;startIndex&#34;: 0,
                    &#34;fetchEmails&#34;: True,
                    &#34;pageSize&#34;: 50,
                    &#34;fetchFiles&#34;: True,
                    &#34;orderByClause&#34;: &#34;CreateTime desc&#34;
                }
            }
        self._export_items_api = self._services[&#39;EXPORT_ITEM_TO_SET&#39;]
        self._export_items_json_req = \
            {
                &#34;complianceData&#34;: {
                    &#34;mode&#34;: 2,
                    &#34;restoreType&#34;: 2,
                    &#34;downLoadDesc&#34;: None,
                    &#34;destContainer&#34;: None,
                    &#34;originatingContainer&#34;: {
                        &#34;containerOwnerType&#34;: 1
                    },
                    &#34;options&#34;: {
                        &#34;zipEML&#34;: True,
                        &#34;retentionInDays&#34;: 30}
                },
                &#34;onlineData&#34;: {
                    &#34;downloadStatus&#34;: 0
                },
                &#34;listOfItems&#34;: {
                    &#34;resultItem&#34;: None
                }
            }
        self._delete_export_api = self._services[&#39;GET_EXPORTS&#39;]
        self._delete_export_json_req = \
            {
                &#34;operationType&#34;: 2,
                &#34;container&#34;: None,
                &#34;downloadItems&#34;: {
                    &#34;items&#34;: None
                },
                &#34;tagModifyRequest&#34;: {
                    &#34;operationType&#34;: 2
                }
            }
        self._all_exports = {}
        self._get_all_exports()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

        Raises:
            SDKException:
                Response was not success

        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def _get_all_exports(self):
        &#34;&#34;&#34;Method to fetch all the exports for the export set&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#34;POST&#34;, url=self._get_exports_api, payload=self._get_export_json_req)
        if flag:
            if (response.json() and &#34;totalHits&#34; in response.json()
                    and int(response.json()[&#39;totalHits&#39;]) != 0):
                items = response.json()[&#39;downloadItems&#39;][&#39;items&#39;]
                for item in items:
                    self._all_exports.update(
                        {item[&#34;description&#34;]: item})
            else:
                self._all_exports = {}
        else:
            self._response_not_success(response)

    def share(self, user_or_user_group_name=None, permissions=None, mode=2):
        &#34;&#34;&#34;Method to share the export set with the user or user group provided

            Args:
                user_or_user_group_name (str)       -   User or user group with which export set has to be shared
                permissions             (list/str)  -   List or comma separated permissions that need
                                                        to be set to the user/ user group
                mode                    (int)       -   to add (2), remove (3) or overwrite (1) the permissions
                                                        Default : Add (2)

            Returns:
                Returns True if share worked fine else raises an Exception

        &#34;&#34;&#34;
        if isinstance(permissions, str):
            permissions = permissions.split(&#34;,&#34;)
        share_json = copy.deepcopy(ComplianceConstants.EXPORT_SET_SHARE_REQUEST_JSON)
        unshare = False
        user_details = {}
        if user_or_user_group_name is None:
            unshare = True
        elif self._commcell.users.has_user(user_or_user_group_name):
            user_details = {
                &#34;userId&#34;: int(self._commcell.users.get(user_or_user_group_name).user_id),
                &#34;userName&#34;: user_or_user_group_name,
                &#34;_type_&#34;: 13
            }
        elif self._commcell.user_groups.has_user_group(user_or_user_group_name):
            user_details = {
                &#34;groupId&#34;: int(self._commcell.user_groups.get(user_or_user_group_name).user_group_id),
                &#34;userGroupName&#34;: user_or_user_group_name,
                &#34;_type_&#34;: 15
            }
        else:
            raise SDKException(&#39;ComplianceSearch&#39;, &#39;101&#39;)
        if unshare:
            share_json[&#39;securityAssociations&#39;][&#39;associations&#39;] = []
            share_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = 1
        else:
            share_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = mode
            share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;] = [user_details]
            if permissions is None:
                share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;] = []
                permissions = [[&#34;View&#34;, &#34;Download&#34;], list(ComplianceConstants.PERMISSIONS.keys())][mode//3]
            if mode == 3:
                share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;] = []
            for permission in permissions:
                try:
                    share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;].append(
                        ComplianceConstants.PERMISSIONS[permission]
                    )
                except KeyError:
                    raise SDKException(&#39;ComplianceSearch&#39;, &#39;102&#39;)
        share_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;downloadSetId&#39;] = self.export_set_id
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, self._share_export_set_api, share_json)
        if flag:
            if response.json() and &#34;response&#34; in response.json():
                if response.json()[&#39;response&#39;][0][&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return True
        self._response_not_success(response)

    def has(self, export_name):
        &#34;&#34;&#34;Method to check if the export exists or not

            Args:
                export_name (str)   -   Export name to be checked

            Returns:
                Returns True if export exists in the environment or False otherwise

        &#34;&#34;&#34;
        return export_name in self._all_exports

    def get(self, export_name):
        &#34;&#34;&#34;Method to get the export

            Args:
                export_name (str)   -   Export name to get

            Returns:
                Export class instance for the export with the given name if found else returns None

        &#34;&#34;&#34;
        if self.has(export_name):
            return Export(self._commcell, self._all_exports[export_name])
        raise SDKException(&#39;ComplianceSearch&#39;, &#39;103&#39;)

    def delete(self, export_name):
        &#34;&#34;&#34;Method to delete the export

            Args:
                export_name (str)   -   Export name to be deleted

            Returns:
                Returns True if delete successfully else raises error

            Raises:
                SDKException:
                    Response was not success

        &#34;&#34;&#34;
        if not self.has(export_name):
            raise SDKException(&#34;ComplianceSearch&#34;, &#34;103&#34;)
        delete_json = copy.deepcopy(self._delete_export_json_req)
        delete_json[&#34;downloadItems&#34;][&#34;items&#34;] = [self._all_exports[export_name]]
        delete_json[&#34;container&#34;] = self.properties
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, self._delete_export_api, delete_json)
        if flag:
            if response.json() and &#34;errList&#34; in response.json() and len(response.json()[&#34;errList&#34;]) != 0:
                if response.json()[&#34;errList&#34;][0][&#34;errorCode&#34;] != 0:
                    raise SDKException(&#34;ComplianceSearch&#34;, &#34;104&#34;,
                                       response.json()[&#34;errList&#34;][0].get(&#34;errLogMessage&#34;))
            return True
        self._response_not_success(response)

    def export_items_to_set(self, export_name, export_items, export_type=ComplianceConstants.ExportTypes.CAB):
        &#34;&#34;&#34;Method to export items/documents to the export set

            Args:
                export_name     (str)   -   Export name for the exported items
                export_items    (list)  -   List of search result items which needs to be export
                export_type     (str)   -   ComplianceConstants.ExportTypes Enum values
                                            Default: CAB

            Returns:
                Returns the restore job ID for the export operation

            Raises:
                SDKException:
                    Response was not success

        &#34;&#34;&#34;
        if export_type not in ComplianceConstants.RESTORE_TYPE:
            raise SDKException(&#39;ComplianceSearch&#39;, &#39;108&#39;)
        export_items_json_req = copy.deepcopy(self._export_items_json_req)
        export_items_json_req[&#39;complianceData&#39;][&#39;restoreType&#39;] = ComplianceConstants.RESTORE_TYPE[export_type]
        export_items_json_req[&#39;complianceData&#39;][&#39;downLoadDesc&#39;] = export_name
        export_items_json_req[&#39;complianceData&#39;][&#39;destContainer&#39;] = self.properties
        export_items_json_req[&#39;listOfItems&#39;][&#39;resultItem&#39;] = export_items
        flag, response = self._cvpysdk_object.make_request(
            method=&#34;POST&#34;, url=self._export_items_api, payload=export_items_json_req)
        if flag:
            if response.json() and &#34;downloadId&#34; in response.json() and &#34;jobId&#34; in response.json():
                self.refresh()
                return response.json()[&#39;jobId&#39;]
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)

    @staticmethod
    def select(result_items, no_of_files=0, export_all=False):
        &#34;&#34;&#34;Static Method to randomly pick user input amount of items from the search result items

            Args:
                result_items    (list)      -   List of all the search result items
                no_of_files     (int)       -   Number of items to be selected
                export_all      (bool)      -   if all the items needs to be selected or not

            Returns:
                List of randomly selected (no_of_files) items from the (result_items)

        &#34;&#34;&#34;
        if export_all or len(result_items) &lt; no_of_files:
            return result_items
        return list(random.sample(result_items, no_of_files))

    @property
    def properties(self):
        &#34;&#34;&#34;Method to return all the properties of the export set

            Returns:
                Dict of the properties of the export set as received from the API (rawdata)

        &#34;&#34;&#34;
        return self._export_set_properties

    @property
    def export_set_full_name(self):
        &#34;&#34;&#34;Method to return the export set full name

            Returns:
                (str) export set full name

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;containerFullName&#39;]

    @property
    def export_set_name(self):
        &#34;&#34;&#34;Method to return the export set name

            Returns:
                (str) export set name

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;containerName&#39;]

    @property
    def export_set_comment(self):
        &#34;&#34;&#34;Method to return the export set comment

            Returns:
                (str) export set comment

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;comment&#39;]

    @property
    def export_set_id(self):
        &#34;&#34;&#34;Method to return the export set ID

            Returns:
                (int) export set ID

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;containerId&#39;]

    @property
    def export_set_guid(self):
        &#34;&#34;&#34;Method to return the export set Guid

            Returns:
                (str) export set Guid

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;containerGuid&#39;]

    @property
    def export_set_owner_info(self):
        &#34;&#34;&#34;Method to return the export set Owner info

            Returns:
                (str) export set Owner info

        &#34;&#34;&#34;
        return self._export_set_properties[&#39;ownerInfo&#39;]</code></pre>
</details>
<h3>Static methods</h3>
<dl>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.select"><code class="name flex">
<span>def <span class="ident">select</span></span>(<span>result_items, no_of_files=0, export_all=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Static Method to randomly pick user input amount of items from the search result items</p>
<h2 id="args">Args</h2>
<p>result_items
(list)
-
List of all the search result items
no_of_files
(int)
-
Number of items to be selected
export_all
(bool)
-
if all the items needs to be selected or not</p>
<h2 id="returns">Returns</h2>
<p>List of randomly selected (no_of_files) items from the (result_items)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L662-L677" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@staticmethod
def select(result_items, no_of_files=0, export_all=False):
    &#34;&#34;&#34;Static Method to randomly pick user input amount of items from the search result items

        Args:
            result_items    (list)      -   List of all the search result items
            no_of_files     (int)       -   Number of items to be selected
            export_all      (bool)      -   if all the items needs to be selected or not

        Returns:
            List of randomly selected (no_of_files) items from the (result_items)

    &#34;&#34;&#34;
    if export_all or len(result_items) &lt; no_of_files:
        return result_items
    return list(random.sample(result_items, no_of_files))</code></pre>
</details>
</dd>
</dl>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_comment"><code class="name">var <span class="ident">export_set_comment</span></code></dt>
<dd>
<div class="desc"><p>Method to return the export set comment</p>
<h2 id="returns">Returns</h2>
<p>(str) export set comment</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L709-L717" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def export_set_comment(self):
    &#34;&#34;&#34;Method to return the export set comment

        Returns:
            (str) export set comment

    &#34;&#34;&#34;
    return self._export_set_properties[&#39;comment&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_full_name"><code class="name">var <span class="ident">export_set_full_name</span></code></dt>
<dd>
<div class="desc"><p>Method to return the export set full name</p>
<h2 id="returns">Returns</h2>
<p>(str) export set full name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L689-L697" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def export_set_full_name(self):
    &#34;&#34;&#34;Method to return the export set full name

        Returns:
            (str) export set full name

    &#34;&#34;&#34;
    return self._export_set_properties[&#39;containerFullName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_guid"><code class="name">var <span class="ident">export_set_guid</span></code></dt>
<dd>
<div class="desc"><p>Method to return the export set Guid</p>
<h2 id="returns">Returns</h2>
<p>(str) export set Guid</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L729-L737" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def export_set_guid(self):
    &#34;&#34;&#34;Method to return the export set Guid

        Returns:
            (str) export set Guid

    &#34;&#34;&#34;
    return self._export_set_properties[&#39;containerGuid&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_id"><code class="name">var <span class="ident">export_set_id</span></code></dt>
<dd>
<div class="desc"><p>Method to return the export set ID</p>
<h2 id="returns">Returns</h2>
<p>(int) export set ID</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L719-L727" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def export_set_id(self):
    &#34;&#34;&#34;Method to return the export set ID

        Returns:
            (int) export set ID

    &#34;&#34;&#34;
    return self._export_set_properties[&#39;containerId&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_name"><code class="name">var <span class="ident">export_set_name</span></code></dt>
<dd>
<div class="desc"><p>Method to return the export set name</p>
<h2 id="returns">Returns</h2>
<p>(str) export set name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L699-L707" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def export_set_name(self):
    &#34;&#34;&#34;Method to return the export set name

        Returns:
            (str) export set name

    &#34;&#34;&#34;
    return self._export_set_properties[&#39;containerName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_owner_info"><code class="name">var <span class="ident">export_set_owner_info</span></code></dt>
<dd>
<div class="desc"><p>Method to return the export set Owner info</p>
<h2 id="returns">Returns</h2>
<p>(str) export set Owner info</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L739-L747" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def export_set_owner_info(self):
    &#34;&#34;&#34;Method to return the export set Owner info

        Returns:
            (str) export set Owner info

    &#34;&#34;&#34;
    return self._export_set_properties[&#39;ownerInfo&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.properties"><code class="name">var <span class="ident">properties</span></code></dt>
<dd>
<div class="desc"><p>Method to return all the properties of the export set</p>
<h2 id="returns">Returns</h2>
<p>Dict of the properties of the export set as received from the API (rawdata)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L679-L687" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def properties(self):
    &#34;&#34;&#34;Method to return all the properties of the export set

        Returns:
            Dict of the properties of the export set as received from the API (rawdata)

    &#34;&#34;&#34;
    return self._export_set_properties</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, export_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to delete the export</p>
<h2 id="args">Args</h2>
<p>export_name (str)
-
Export name to be deleted</p>
<h2 id="returns">Returns</h2>
<p>Returns True if delete successfully else raises error</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Response was not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L600-L626" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, export_name):
    &#34;&#34;&#34;Method to delete the export

        Args:
            export_name (str)   -   Export name to be deleted

        Returns:
            Returns True if delete successfully else raises error

        Raises:
            SDKException:
                Response was not success

    &#34;&#34;&#34;
    if not self.has(export_name):
        raise SDKException(&#34;ComplianceSearch&#34;, &#34;103&#34;)
    delete_json = copy.deepcopy(self._delete_export_json_req)
    delete_json[&#34;downloadItems&#34;][&#34;items&#34;] = [self._all_exports[export_name]]
    delete_json[&#34;container&#34;] = self.properties
    flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, self._delete_export_api, delete_json)
    if flag:
        if response.json() and &#34;errList&#34; in response.json() and len(response.json()[&#34;errList&#34;]) != 0:
            if response.json()[&#34;errList&#34;][0][&#34;errorCode&#34;] != 0:
                raise SDKException(&#34;ComplianceSearch&#34;, &#34;104&#34;,
                                   response.json()[&#34;errList&#34;][0].get(&#34;errLogMessage&#34;))
        return True
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.export_items_to_set"><code class="name flex">
<span>def <span class="ident">export_items_to_set</span></span>(<span>self, export_name, export_items, export_type=ExportTypes.CAB)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to export items/documents to the export set</p>
<h2 id="args">Args</h2>
<p>export_name
(str)
-
Export name for the exported items
export_items
(list)
-
List of search result items which needs to be export
export_type
(str)
-
ComplianceConstants.ExportTypes Enum values
Default: CAB</p>
<h2 id="returns">Returns</h2>
<p>Returns the restore job ID for the export operation</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Response was not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L628-L660" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def export_items_to_set(self, export_name, export_items, export_type=ComplianceConstants.ExportTypes.CAB):
    &#34;&#34;&#34;Method to export items/documents to the export set

        Args:
            export_name     (str)   -   Export name for the exported items
            export_items    (list)  -   List of search result items which needs to be export
            export_type     (str)   -   ComplianceConstants.ExportTypes Enum values
                                        Default: CAB

        Returns:
            Returns the restore job ID for the export operation

        Raises:
            SDKException:
                Response was not success

    &#34;&#34;&#34;
    if export_type not in ComplianceConstants.RESTORE_TYPE:
        raise SDKException(&#39;ComplianceSearch&#39;, &#39;108&#39;)
    export_items_json_req = copy.deepcopy(self._export_items_json_req)
    export_items_json_req[&#39;complianceData&#39;][&#39;restoreType&#39;] = ComplianceConstants.RESTORE_TYPE[export_type]
    export_items_json_req[&#39;complianceData&#39;][&#39;downLoadDesc&#39;] = export_name
    export_items_json_req[&#39;complianceData&#39;][&#39;destContainer&#39;] = self.properties
    export_items_json_req[&#39;listOfItems&#39;][&#39;resultItem&#39;] = export_items
    flag, response = self._cvpysdk_object.make_request(
        method=&#34;POST&#34;, url=self._export_items_api, payload=export_items_json_req)
    if flag:
        if response.json() and &#34;downloadId&#34; in response.json() and &#34;jobId&#34; in response.json():
            self.refresh()
            return response.json()[&#39;jobId&#39;]
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, export_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to get the export</p>
<h2 id="args">Args</h2>
<p>export_name (str)
-
Export name to get</p>
<h2 id="returns">Returns</h2>
<p>Export class instance for the export with the given name if found else returns None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L586-L598" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, export_name):
    &#34;&#34;&#34;Method to get the export

        Args:
            export_name (str)   -   Export name to get

        Returns:
            Export class instance for the export with the given name if found else returns None

    &#34;&#34;&#34;
    if self.has(export_name):
        return Export(self._commcell, self._all_exports[export_name])
    raise SDKException(&#39;ComplianceSearch&#39;, &#39;103&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.has"><code class="name flex">
<span>def <span class="ident">has</span></span>(<span>self, export_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to check if the export exists or not</p>
<h2 id="args">Args</h2>
<p>export_name (str)
-
Export name to be checked</p>
<h2 id="returns">Returns</h2>
<p>Returns True if export exists in the environment or False otherwise</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L574-L584" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has(self, export_name):
    &#34;&#34;&#34;Method to check if the export exists or not

        Args:
            export_name (str)   -   Export name to be checked

        Returns:
            Returns True if export exists in the environment or False otherwise

    &#34;&#34;&#34;
    return export_name in self._all_exports</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to refresh the properties of the class ExportSet</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L419-L480" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Method to refresh the properties of the class ExportSet&#34;&#34;&#34;
    self._cvpysdk_object = self._commcell._cvpysdk_object
    self._services = self._commcell._services
    self._update_response_ = self._commcell._update_response_
    self._user_guid = self._commcell.users.get(self._commcell.commcell_username).user_guid
    self._get_exports_api = self._services[&#39;GET_EXPORTS&#39;]
    self._share_export_set_api = self._services[&#39;SECURITY_ASSOCIATION&#39;]
    self._get_export_json_req = \
        {
            &#34;containerOpReq&#34;: {
                &#34;entityType&#34;: 9503,
                &#34;operationType&#34;: 1,
                &#34;userGuid&#34;: self._user_guid,
                &#34;fromSite&#34;: 0,
                &#34;container&#34;: self._export_set_properties
            },
            &#34;filter&#34;: {},
            &#34;pagingData&#34;: {
                &#34;startIndex&#34;: 0,
                &#34;fetchEmails&#34;: True,
                &#34;pageSize&#34;: 50,
                &#34;fetchFiles&#34;: True,
                &#34;orderByClause&#34;: &#34;CreateTime desc&#34;
            }
        }
    self._export_items_api = self._services[&#39;EXPORT_ITEM_TO_SET&#39;]
    self._export_items_json_req = \
        {
            &#34;complianceData&#34;: {
                &#34;mode&#34;: 2,
                &#34;restoreType&#34;: 2,
                &#34;downLoadDesc&#34;: None,
                &#34;destContainer&#34;: None,
                &#34;originatingContainer&#34;: {
                    &#34;containerOwnerType&#34;: 1
                },
                &#34;options&#34;: {
                    &#34;zipEML&#34;: True,
                    &#34;retentionInDays&#34;: 30}
            },
            &#34;onlineData&#34;: {
                &#34;downloadStatus&#34;: 0
            },
            &#34;listOfItems&#34;: {
                &#34;resultItem&#34;: None
            }
        }
    self._delete_export_api = self._services[&#39;GET_EXPORTS&#39;]
    self._delete_export_json_req = \
        {
            &#34;operationType&#34;: 2,
            &#34;container&#34;: None,
            &#34;downloadItems&#34;: {
                &#34;items&#34;: None
            },
            &#34;tagModifyRequest&#34;: {
                &#34;operationType&#34;: 2
            }
        }
    self._all_exports = {}
    self._get_all_exports()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSet.share"><code class="name flex">
<span>def <span class="ident">share</span></span>(<span>self, user_or_user_group_name=None, permissions=None, mode=2)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to share the export set with the user or user group provided</p>
<h2 id="args">Args</h2>
<p>user_or_user_group_name (str)
-
User or user group with which export set has to be shared
permissions
(list/str)
-
List or comma separated permissions that need
to be set to the user/ user group
mode
(int)
-
to add (2), remove (3) or overwrite (1) the permissions
Default : Add (2)</p>
<h2 id="returns">Returns</h2>
<p>Returns True if share worked fine else raises an Exception</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L512-L572" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def share(self, user_or_user_group_name=None, permissions=None, mode=2):
    &#34;&#34;&#34;Method to share the export set with the user or user group provided

        Args:
            user_or_user_group_name (str)       -   User or user group with which export set has to be shared
            permissions             (list/str)  -   List or comma separated permissions that need
                                                    to be set to the user/ user group
            mode                    (int)       -   to add (2), remove (3) or overwrite (1) the permissions
                                                    Default : Add (2)

        Returns:
            Returns True if share worked fine else raises an Exception

    &#34;&#34;&#34;
    if isinstance(permissions, str):
        permissions = permissions.split(&#34;,&#34;)
    share_json = copy.deepcopy(ComplianceConstants.EXPORT_SET_SHARE_REQUEST_JSON)
    unshare = False
    user_details = {}
    if user_or_user_group_name is None:
        unshare = True
    elif self._commcell.users.has_user(user_or_user_group_name):
        user_details = {
            &#34;userId&#34;: int(self._commcell.users.get(user_or_user_group_name).user_id),
            &#34;userName&#34;: user_or_user_group_name,
            &#34;_type_&#34;: 13
        }
    elif self._commcell.user_groups.has_user_group(user_or_user_group_name):
        user_details = {
            &#34;groupId&#34;: int(self._commcell.user_groups.get(user_or_user_group_name).user_group_id),
            &#34;userGroupName&#34;: user_or_user_group_name,
            &#34;_type_&#34;: 15
        }
    else:
        raise SDKException(&#39;ComplianceSearch&#39;, &#39;101&#39;)
    if unshare:
        share_json[&#39;securityAssociations&#39;][&#39;associations&#39;] = []
        share_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = 1
    else:
        share_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = mode
        share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;] = [user_details]
        if permissions is None:
            share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;] = []
            permissions = [[&#34;View&#34;, &#34;Download&#34;], list(ComplianceConstants.PERMISSIONS.keys())][mode//3]
        if mode == 3:
            share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;] = []
        for permission in permissions:
            try:
                share_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;].append(
                    ComplianceConstants.PERMISSIONS[permission]
                )
            except KeyError:
                raise SDKException(&#39;ComplianceSearch&#39;, &#39;102&#39;)
    share_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;downloadSetId&#39;] = self.export_set_id
    flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, self._share_export_set_api, share_json)
    if flag:
        if response.json() and &#34;response&#34; in response.json():
            if response.json()[&#39;response&#39;][0][&#39;errorCode&#39;] == 0:
                self.refresh()
                return True
    self._response_not_success(response)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSets"><code class="flex name class">
<span>class <span class="ident">ExportSets</span></span>
<span>(</span><span>commcell)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the export sets associated with the commcell</p>
<p>Initializes the ExportSets class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L234-L394" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ExportSets():
    &#34;&#34;&#34;Class for representing all the export sets associated with the commcell&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes the ExportSets class&#34;&#34;&#34;
        self._commcell = commcell
        self._cvpysdk_object = None
        self._services = None
        self._update_response_ = None
        self._user_guid = None
        self._add_export_set_api = None
        self._add_export_set_json_req = None
        self._get_export_sets_api = None
        self._get_export_sets_json_req = None
        self._delete_export_set_api = None
        self._delete_export_set_json_req = None
        self._all_export_set = None
        self.refresh()

    def _get_export_sets(self):
        &#34;&#34;&#34;Method to call the API and fetch all the export sets from the commcell environment&#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#34;POST&#34;, url=self._get_export_sets_api, payload=self._get_export_sets_json_req)
        if flag:
            if response.json() and &#34;containers&#34; in response.json():
                containers = response.json()[&#39;containers&#39;]
                for container in containers:
                    self._all_export_set.update(
                        {container[&#34;containerName&#34;]: container})
        else:
            self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Method to refresh all the properties of the class ExportSets&#34;&#34;&#34;
        self._cvpysdk_object = self._commcell._cvpysdk_object
        self._services = self._commcell._services
        self._update_response_ = self._commcell._update_response_
        self._user_guid = self._commcell.users.get(self._commcell.commcell_username).user_guid
        self._all_export_set = {}
        self._get_export_sets_api = self._services[&#39;GET_EXPORT_SETS&#39;]
        self._get_export_sets_json_req = \
            {
                &#34;getContainerOptions&#34;: 0,
                &#34;entityType&#34;: 9503,
                &#34;userGuid&#34;: self._user_guid,
                &#34;attribute&#34;: {
                    &#34;all&#34;: True
                }
            }
        self._add_export_set_api = self._services[&#39;ADD_EXPORT_SET&#39;]
        self._add_export_set_json_req = \
            {
                &#34;entityType&#34;: 9503,
                &#34;operationType&#34;: 1,
                &#34;userGuid&#34;: self._user_guid,
                &#34;fromSite&#34;: 2,
                &#34;container&#34;: {
                    &#34;containerType&#34;: 9503,
                    &#34;containerName&#34;: None,
                    &#34;containerOwnerType&#34;: 1,
                    &#34;comment&#34;: &#34;Export set created from CvPySDK&#34;
                }
            }
        self._delete_export_set_api = self._services[&#39;DELETE_EXPORT_SET&#39;]
        self._delete_export_set_json_req = \
            {
                &#34;entityType&#34;: 9503,
                &#34;containers&#34;: None
            }
        self._get_export_sets()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper method to raise exception when response is not 200 (ok)

            Raises:
                SDKException:
                    Response was not success
        &#34;&#34;&#34;
        raise SDKException(
            &#39;Response&#39;,
            &#39;101&#39;,
            self._update_response_(
                response.text))

    def add(self, export_set_name, comment=None):
        &#34;&#34;&#34;Method to create an export set to the commcell environment

            Args:
                export_set_name     (str)   -   Export set name for the newly created export set
                comment             (str)   -   Export set description fot the newly created export set

            Returns:
                ExportSet instance for the newly created export set

        &#34;&#34;&#34;
        if not self.has(export_set_name):
            add_export_set_json_req = copy.deepcopy(self._add_export_set_json_req)
            add_export_set_json_req[&#39;container&#39;][&#39;containerName&#39;] = export_set_name
            if comment:
                add_export_set_json_req[&#39;container&#39;][&#39;comment&#39;] = comment
            flag, response = self._cvpysdk_object.make_request(
                method=&#34;POST&#34;, url=self._add_export_set_api, payload=add_export_set_json_req)
            if flag:
                if response.json() and &#34;container&#34; in response.json():
                    self.refresh()
                    return self.get(export_set_name=export_set_name)
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                self._response_not_success(response)

    def has(self, export_set_name):
        &#34;&#34;&#34;Method to check if the export set exists or not

            Args:
                export_set_name (str)   -   Export set name to be checked

            Returns:
                Returns True if export set exists in the environment or False otherwise

        &#34;&#34;&#34;
        return export_set_name in self._all_export_set

    def get(self, export_set_name):
        &#34;&#34;&#34;Method to get the export set

            Args:
                export_set_name (str)   -   Export set name to get

            Returns:
                ExportSet instance for the export set with the given name if found else raises Exception

        &#34;&#34;&#34;

        if self.has(export_set_name):
            return ExportSet(self._commcell, self._all_export_set[export_set_name])
        raise SDKException(&#39;ComplianceSearch&#39;, &#39;106&#39;)

    def delete(self, export_set_name):
        &#34;&#34;&#34;Method to delete the export set

            Args:
                export_set_name (str)   -   Export set name to be deleted

            Returns:
                Returns True if delete successfully else raises error

            Raises:
                SDKException:
                    Response was not success

        &#34;&#34;&#34;
        if not self.has(export_set_name):
            raise SDKException(&#34;ComplianceSearch&#34;, &#34;105&#34;)
        delete_json = copy.deepcopy(self._delete_export_set_json_req)
        delete_json[&#39;containers&#39;] = [self._all_export_set[export_set_name]]
        flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, self._delete_export_set_api, delete_json)
        if flag:
            if response.json() and &#34;errList&#34; in response.json() and len(response.json()[&#34;errList&#34;]) == 0:
                self.refresh()
                return True
        self._response_not_success(response)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSets.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, export_set_name, comment=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to create an export set to the commcell environment</p>
<h2 id="args">Args</h2>
<p>export_set_name
(str)
-
Export set name for the newly created export set
comment
(str)
-
Export set description fot the newly created export set</p>
<h2 id="returns">Returns</h2>
<p>ExportSet instance for the newly created export set</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L318-L342" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, export_set_name, comment=None):
    &#34;&#34;&#34;Method to create an export set to the commcell environment

        Args:
            export_set_name     (str)   -   Export set name for the newly created export set
            comment             (str)   -   Export set description fot the newly created export set

        Returns:
            ExportSet instance for the newly created export set

    &#34;&#34;&#34;
    if not self.has(export_set_name):
        add_export_set_json_req = copy.deepcopy(self._add_export_set_json_req)
        add_export_set_json_req[&#39;container&#39;][&#39;containerName&#39;] = export_set_name
        if comment:
            add_export_set_json_req[&#39;container&#39;][&#39;comment&#39;] = comment
        flag, response = self._cvpysdk_object.make_request(
            method=&#34;POST&#34;, url=self._add_export_set_api, payload=add_export_set_json_req)
        if flag:
            if response.json() and &#34;container&#34; in response.json():
                self.refresh()
                return self.get(export_set_name=export_set_name)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSets.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, export_set_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to delete the export set</p>
<h2 id="args">Args</h2>
<p>export_set_name (str)
-
Export set name to be deleted</p>
<h2 id="returns">Returns</h2>
<p>Returns True if delete successfully else raises error</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
Response was not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L371-L394" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, export_set_name):
    &#34;&#34;&#34;Method to delete the export set

        Args:
            export_set_name (str)   -   Export set name to be deleted

        Returns:
            Returns True if delete successfully else raises error

        Raises:
            SDKException:
                Response was not success

    &#34;&#34;&#34;
    if not self.has(export_set_name):
        raise SDKException(&#34;ComplianceSearch&#34;, &#34;105&#34;)
    delete_json = copy.deepcopy(self._delete_export_set_json_req)
    delete_json[&#39;containers&#39;] = [self._all_export_set[export_set_name]]
    flag, response = self._cvpysdk_object.make_request(&#34;POST&#34;, self._delete_export_set_api, delete_json)
    if flag:
        if response.json() and &#34;errList&#34; in response.json() and len(response.json()[&#34;errList&#34;]) == 0:
            self.refresh()
            return True
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSets.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, export_set_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to get the export set</p>
<h2 id="args">Args</h2>
<p>export_set_name (str)
-
Export set name to get</p>
<h2 id="returns">Returns</h2>
<p>ExportSet instance for the export set with the given name if found else raises Exception</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L356-L369" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, export_set_name):
    &#34;&#34;&#34;Method to get the export set

        Args:
            export_set_name (str)   -   Export set name to get

        Returns:
            ExportSet instance for the export set with the given name if found else raises Exception

    &#34;&#34;&#34;

    if self.has(export_set_name):
        return ExportSet(self._commcell, self._all_export_set[export_set_name])
    raise SDKException(&#39;ComplianceSearch&#39;, &#39;106&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSets.has"><code class="name flex">
<span>def <span class="ident">has</span></span>(<span>self, export_set_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to check if the export set exists or not</p>
<h2 id="args">Args</h2>
<p>export_set_name (str)
-
Export set name to be checked</p>
<h2 id="returns">Returns</h2>
<p>Returns True if export set exists in the environment or False otherwise</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L344-L354" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has(self, export_set_name):
    &#34;&#34;&#34;Method to check if the export set exists or not

        Args:
            export_set_name (str)   -   Export set name to be checked

        Returns:
            Returns True if export set exists in the environment or False otherwise

    &#34;&#34;&#34;
    return export_set_name in self._all_export_set</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.compliance_utils.ExportSets.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to refresh all the properties of the class ExportSets</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/compliance_utils.py#L266-L303" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Method to refresh all the properties of the class ExportSets&#34;&#34;&#34;
    self._cvpysdk_object = self._commcell._cvpysdk_object
    self._services = self._commcell._services
    self._update_response_ = self._commcell._update_response_
    self._user_guid = self._commcell.users.get(self._commcell.commcell_username).user_guid
    self._all_export_set = {}
    self._get_export_sets_api = self._services[&#39;GET_EXPORT_SETS&#39;]
    self._get_export_sets_json_req = \
        {
            &#34;getContainerOptions&#34;: 0,
            &#34;entityType&#34;: 9503,
            &#34;userGuid&#34;: self._user_guid,
            &#34;attribute&#34;: {
                &#34;all&#34;: True
            }
        }
    self._add_export_set_api = self._services[&#39;ADD_EXPORT_SET&#39;]
    self._add_export_set_json_req = \
        {
            &#34;entityType&#34;: 9503,
            &#34;operationType&#34;: 1,
            &#34;userGuid&#34;: self._user_guid,
            &#34;fromSite&#34;: 2,
            &#34;container&#34;: {
                &#34;containerType&#34;: 9503,
                &#34;containerName&#34;: None,
                &#34;containerOwnerType&#34;: 1,
                &#34;comment&#34;: &#34;Export set created from CvPySDK&#34;
            }
        }
    self._delete_export_set_api = self._services[&#39;DELETE_EXPORT_SET&#39;]
    self._delete_export_set_json_req = \
        {
            &#34;entityType&#34;: 9503,
            &#34;containers&#34;: None
        }
    self._get_export_sets()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#compliancesearchutils">ComplianceSearchUtils</a></li>
<li><a href="#exportsets">ExportSets</a></li>
<li><a href="#exportset">ExportSet</a><ul>
<li><a href="#exportset-attributes">ExportSet Attributes</a></li>
</ul>
</li>
<li><a href="#export">Export</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.activateapps" href="index.html">cvpysdk.activateapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activateapps.compliance_utils.ComplianceSearchUtils" href="#cvpysdk.activateapps.compliance_utils.ComplianceSearchUtils">ComplianceSearchUtils</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.compliance_utils.ComplianceSearchUtils.do_compliance_search" href="#cvpysdk.activateapps.compliance_utils.ComplianceSearchUtils.do_compliance_search">do_compliance_search</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.compliance_utils.Export" href="#cvpysdk.activateapps.compliance_utils.Export">Export</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.compliance_utils.Export.download_export" href="#cvpysdk.activateapps.compliance_utils.Export.download_export">download_export</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.Export.refresh" href="#cvpysdk.activateapps.compliance_utils.Export.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet" href="#cvpysdk.activateapps.compliance_utils.ExportSet">ExportSet</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.delete" href="#cvpysdk.activateapps.compliance_utils.ExportSet.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.export_items_to_set" href="#cvpysdk.activateapps.compliance_utils.ExportSet.export_items_to_set">export_items_to_set</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_comment" href="#cvpysdk.activateapps.compliance_utils.ExportSet.export_set_comment">export_set_comment</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_full_name" href="#cvpysdk.activateapps.compliance_utils.ExportSet.export_set_full_name">export_set_full_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_guid" href="#cvpysdk.activateapps.compliance_utils.ExportSet.export_set_guid">export_set_guid</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_id" href="#cvpysdk.activateapps.compliance_utils.ExportSet.export_set_id">export_set_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_name" href="#cvpysdk.activateapps.compliance_utils.ExportSet.export_set_name">export_set_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.export_set_owner_info" href="#cvpysdk.activateapps.compliance_utils.ExportSet.export_set_owner_info">export_set_owner_info</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.get" href="#cvpysdk.activateapps.compliance_utils.ExportSet.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.has" href="#cvpysdk.activateapps.compliance_utils.ExportSet.has">has</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.properties" href="#cvpysdk.activateapps.compliance_utils.ExportSet.properties">properties</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.refresh" href="#cvpysdk.activateapps.compliance_utils.ExportSet.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.select" href="#cvpysdk.activateapps.compliance_utils.ExportSet.select">select</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSet.share" href="#cvpysdk.activateapps.compliance_utils.ExportSet.share">share</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.compliance_utils.ExportSets" href="#cvpysdk.activateapps.compliance_utils.ExportSets">ExportSets</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSets.add" href="#cvpysdk.activateapps.compliance_utils.ExportSets.add">add</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSets.delete" href="#cvpysdk.activateapps.compliance_utils.ExportSets.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSets.get" href="#cvpysdk.activateapps.compliance_utils.ExportSets.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSets.has" href="#cvpysdk.activateapps.compliance_utils.ExportSets.has">has</a></code></li>
<li><code><a title="cvpysdk.activateapps.compliance_utils.ExportSets.refresh" href="#cvpysdk.activateapps.compliance_utils.ExportSets.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>